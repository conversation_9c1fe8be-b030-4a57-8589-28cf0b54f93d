import 'excel_models.dart';

// Excel Chart Model
class ExcelChart {
  final String id;
  final String worksheetId;
  final ChartType type;
  final String title;
  final String dataRange;
  final ChartStyle style;
  final Map<String, dynamic> options;
  final ChartPosition position;
  final bool isVisible;
  final DateTime createdAt;

  const ExcelChart({
    required this.id,
    required this.worksheetId,
    required this.type,
    required this.title,
    required this.dataRange,
    required this.style,
    required this.options,
    required this.position,
    required this.isVisible,
    required this.createdAt,
  });

  factory ExcelChart.fromJson(Map<String, dynamic> json) {
    return ExcelChart(
      id: json['id'] as String,
      worksheetId: json['worksheet_id'] as String,
      type: ChartType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ChartType.column,
      ),
      title: json['title'] as String,
      dataRange: json['data_range'] as String,
      style: ChartStyle.fromJson(json['style'] as Map<String, dynamic>),
      options: Map<String, dynamic>.from(json['options'] as Map? ?? {}),
      position: ChartPosition.fromJson(json['position'] as Map<String, dynamic>),
      isVisible: json['is_visible'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'worksheet_id': worksheetId,
      'type': type.name,
      'title': title,
      'data_range': dataRange,
      'style': style.toJson(),
      'options': options,
      'position': position.toJson(),
      'is_visible': isVisible,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Chart Style Model
class ChartStyle {
  final String backgroundColor;
  final String borderColor;
  final double borderWidth;
  final String titleColor;
  final String titleFont;
  final double titleSize;
  final List<String> seriesColors;
  final bool showLegend;
  final String legendPosition;

  const ChartStyle({
    required this.backgroundColor,
    required this.borderColor,
    required this.borderWidth,
    required this.titleColor,
    required this.titleFont,
    required this.titleSize,
    required this.seriesColors,
    required this.showLegend,
    required this.legendPosition,
  });

  factory ChartStyle.fromJson(Map<String, dynamic> json) {
    return ChartStyle(
      backgroundColor: json['background_color'] as String,
      borderColor: json['border_color'] as String,
      borderWidth: (json['border_width'] as num).toDouble(),
      titleColor: json['title_color'] as String,
      titleFont: json['title_font'] as String,
      titleSize: (json['title_size'] as num).toDouble(),
      seriesColors: List<String>.from(json['series_colors'] as List? ?? []),
      showLegend: json['show_legend'] as bool,
      legendPosition: json['legend_position'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'background_color': backgroundColor,
      'border_color': borderColor,
      'border_width': borderWidth,
      'title_color': titleColor,
      'title_font': titleFont,
      'title_size': titleSize,
      'series_colors': seriesColors,
      'show_legend': showLegend,
      'legend_position': legendPosition,
    };
  }
}

// Chart Position Model
class ChartPosition {
  final double x;
  final double y;
  final double width;
  final double height;

  const ChartPosition({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });

  factory ChartPosition.fromJson(Map<String, dynamic> json) {
    return ChartPosition(
      x: (json['x'] as num).toDouble(),
      y: (json['y'] as num).toDouble(),
      width: (json['width'] as num).toDouble(),
      height: (json['height'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
      'width': width,
      'height': height,
    };
  }
}

// Excel Table Model
class ExcelTable {
  final String id;
  final String worksheetId;
  final String name;
  final String range;
  final List<String> headers;
  final TableStyle style;
  final bool hasHeaders;
  final bool hasAutoFilter;
  final bool hasTotalsRow;
  final DateTime createdAt;

  const ExcelTable({
    required this.id,
    required this.worksheetId,
    required this.name,
    required this.range,
    required this.headers,
    required this.style,
    required this.hasHeaders,
    required this.hasAutoFilter,
    required this.hasTotalsRow,
    required this.createdAt,
  });

  factory ExcelTable.fromJson(Map<String, dynamic> json) {
    return ExcelTable(
      id: json['id'] as String,
      worksheetId: json['worksheet_id'] as String,
      name: json['name'] as String,
      range: json['range'] as String,
      headers: List<String>.from(json['headers'] as List? ?? []),
      style: TableStyle.fromJson(json['style'] as Map<String, dynamic>),
      hasHeaders: json['has_headers'] as bool,
      hasAutoFilter: json['has_auto_filter'] as bool,
      hasTotalsRow: json['has_totals_row'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'worksheet_id': worksheetId,
      'name': name,
      'range': range,
      'headers': headers,
      'style': style.toJson(),
      'has_headers': hasHeaders,
      'has_auto_filter': hasAutoFilter,
      'has_totals_row': hasTotalsRow,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Table Style Model
class TableStyle {
  final String name;
  final String headerBackgroundColor;
  final String headerTextColor;
  final String rowBackgroundColor;
  final String alternateRowColor;
  final String borderColor;
  final bool showBorders;

  const TableStyle({
    required this.name,
    required this.headerBackgroundColor,
    required this.headerTextColor,
    required this.rowBackgroundColor,
    required this.alternateRowColor,
    required this.borderColor,
    required this.showBorders,
  });

  factory TableStyle.fromJson(Map<String, dynamic> json) {
    return TableStyle(
      name: json['name'] as String,
      headerBackgroundColor: json['header_background_color'] as String,
      headerTextColor: json['header_text_color'] as String,
      rowBackgroundColor: json['row_background_color'] as String,
      alternateRowColor: json['alternate_row_color'] as String,
      borderColor: json['border_color'] as String,
      showBorders: json['show_borders'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'header_background_color': headerBackgroundColor,
      'header_text_color': headerTextColor,
      'row_background_color': rowBackgroundColor,
      'alternate_row_color': alternateRowColor,
      'border_color': borderColor,
      'show_borders': showBorders,
    };
  }
}

// Excel Pivot Table Model
class ExcelPivotTable {
  final String id;
  final String worksheetId;
  final String name;
  final String sourceRange;
  final List<String> rowFields;
  final List<String> columnFields;
  final List<PivotValueField> valueFields;
  final List<String> filterFields;
  final Map<String, dynamic> options;
  final DateTime refreshDate;
  final bool isAutoRefresh;
  final DateTime createdAt;

  const ExcelPivotTable({
    required this.id,
    required this.worksheetId,
    required this.name,
    required this.sourceRange,
    required this.rowFields,
    required this.columnFields,
    required this.valueFields,
    required this.filterFields,
    required this.options,
    required this.refreshDate,
    required this.isAutoRefresh,
    required this.createdAt,
  });

  factory ExcelPivotTable.fromJson(Map<String, dynamic> json) {
    return ExcelPivotTable(
      id: json['id'] as String,
      worksheetId: json['worksheet_id'] as String,
      name: json['name'] as String,
      sourceRange: json['source_range'] as String,
      rowFields: List<String>.from(json['row_fields'] as List? ?? []),
      columnFields: List<String>.from(json['column_fields'] as List? ?? []),
      valueFields: (json['value_fields'] as List<dynamic>?)
          ?.map((e) => PivotValueField.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      filterFields: List<String>.from(json['filter_fields'] as List? ?? []),
      options: Map<String, dynamic>.from(json['options'] as Map? ?? {}),
      refreshDate: DateTime.parse(json['refresh_date'] as String),
      isAutoRefresh: json['is_auto_refresh'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'worksheet_id': worksheetId,
      'name': name,
      'source_range': sourceRange,
      'row_fields': rowFields,
      'column_fields': columnFields,
      'value_fields': valueFields.map((e) => e.toJson()).toList(),
      'filter_fields': filterFields,
      'options': options,
      'refresh_date': refreshDate.toIso8601String(),
      'is_auto_refresh': isAutoRefresh,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Pivot Value Field Model
class PivotValueField {
  final String fieldName;
  final PivotFunction function;
  final String? customName;

  const PivotValueField({
    required this.fieldName,
    required this.function,
    this.customName,
  });

  factory PivotValueField.fromJson(Map<String, dynamic> json) {
    return PivotValueField(
      fieldName: json['field_name'] as String,
      function: PivotFunction.values.firstWhere(
        (e) => e.name == json['function'],
        orElse: () => PivotFunction.sum,
      ),
      customName: json['custom_name'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'field_name': fieldName,
      'function': function.name,
      'custom_name': customName,
    };
  }
}

// Data Validation Model
class DataValidation {
  final String id;
  final String worksheetId;
  final String cellRange;
  final ValidationType type;
  final ValidationCriteria criteria;
  final String errorMessage;
  final String? inputMessage;
  final bool showErrorAlert;
  final bool showInputMessage;
  final DateTime createdAt;

  const DataValidation({
    required this.id,
    required this.worksheetId,
    required this.cellRange,
    required this.type,
    required this.criteria,
    required this.errorMessage,
    this.inputMessage,
    required this.showErrorAlert,
    required this.showInputMessage,
    required this.createdAt,
  });

  factory DataValidation.fromJson(Map<String, dynamic> json) {
    return DataValidation(
      id: json['id'] as String,
      worksheetId: json['worksheet_id'] as String,
      cellRange: json['cell_range'] as String,
      type: ValidationType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ValidationType.list,
      ),
      criteria: ValidationCriteria.fromJson(json['criteria'] as Map<String, dynamic>),
      errorMessage: json['error_message'] as String,
      inputMessage: json['input_message'] as String?,
      showErrorAlert: json['show_error_alert'] as bool,
      showInputMessage: json['show_input_message'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'worksheet_id': worksheetId,
      'cell_range': cellRange,
      'type': type.name,
      'criteria': criteria.toJson(),
      'error_message': errorMessage,
      'input_message': inputMessage,
      'show_error_alert': showErrorAlert,
      'show_input_message': showInputMessage,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Validation Criteria Model
class ValidationCriteria {
  final String operator;
  final dynamic value1;
  final dynamic value2;
  final List<String>? listValues;

  const ValidationCriteria({
    required this.operator,
    this.value1,
    this.value2,
    this.listValues,
  });

  factory ValidationCriteria.fromJson(Map<String, dynamic> json) {
    return ValidationCriteria(
      operator: json['operator'] as String,
      value1: json['value1'],
      value2: json['value2'],
      listValues: (json['list_values'] as List<dynamic>?)?.cast<String>(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'operator': operator,
      'value1': value1,
      'value2': value2,
      'list_values': listValues,
    };
  }
}

// Excel Formula Model
class ExcelFormula {
  final String id;
  final String name;
  final String expression;
  final List<String> parameters;
  final String description;
  final FormulaCategory category;
  final bool isBuiltIn;
  final DateTime createdAt;

  const ExcelFormula({
    required this.id,
    required this.name,
    required this.expression,
    required this.parameters,
    required this.description,
    required this.category,
    required this.isBuiltIn,
    required this.createdAt,
  });

  factory ExcelFormula.fromJson(Map<String, dynamic> json) {
    return ExcelFormula(
      id: json['id'] as String,
      name: json['name'] as String,
      expression: json['expression'] as String,
      parameters: List<String>.from(json['parameters'] as List? ?? []),
      description: json['description'] as String,
      category: FormulaCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => FormulaCategory.math,
      ),
      isBuiltIn: json['is_built_in'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'expression': expression,
      'parameters': parameters,
      'description': description,
      'category': category.name,
      'is_built_in': isBuiltIn,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Excel Macro Model
class ExcelMacro {
  final String id;
  final String name;
  final String description;
  final List<MacroAction> actions;
  final String? shortcut;
  final bool isEnabled;
  final int executionCount;
  final DateTime createdAt;
  final DateTime lastModified;

  const ExcelMacro({
    required this.id,
    required this.name,
    required this.description,
    required this.actions,
    this.shortcut,
    required this.isEnabled,
    required this.executionCount,
    required this.createdAt,
    required this.lastModified,
  });

  factory ExcelMacro.fromJson(Map<String, dynamic> json) {
    return ExcelMacro(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      actions: (json['actions'] as List<dynamic>?)
          ?.map((e) => MacroAction.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      shortcut: json['shortcut'] as String?,
      isEnabled: json['is_enabled'] as bool,
      executionCount: json['execution_count'] as int,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastModified: DateTime.parse(json['last_modified'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'actions': actions.map((e) => e.toJson()).toList(),
      'shortcut': shortcut,
      'is_enabled': isEnabled,
      'execution_count': executionCount,
      'created_at': createdAt.toIso8601String(),
      'last_modified': lastModified.toIso8601String(),
    };
  }
}

// Macro Action Model
class MacroAction {
  final String id;
  final MacroActionType type;
  final Map<String, dynamic> parameters;
  final int order;

  const MacroAction({
    required this.id,
    required this.type,
    required this.parameters,
    required this.order,
  });

  factory MacroAction.fromJson(Map<String, dynamic> json) {
    return MacroAction(
      id: json['id'] as String,
      type: MacroActionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => MacroActionType.setCellValue,
      ),
      parameters: Map<String, dynamic>.from(json['parameters'] as Map? ?? {}),
      order: json['order'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'parameters': parameters,
      'order': order,
    };
  }
}

// Conditional Formatting Model
class ConditionalFormatting {
  final String id;
  final String worksheetId;
  final String cellRange;
  final ConditionalFormattingRule rule;
  final CellFormat format;
  final int priority;
  final bool isEnabled;
  final DateTime createdAt;

  const ConditionalFormatting({
    required this.id,
    required this.worksheetId,
    required this.cellRange,
    required this.rule,
    required this.format,
    required this.priority,
    required this.isEnabled,
    required this.createdAt,
  });

  factory ConditionalFormatting.fromJson(Map<String, dynamic> json) {
    return ConditionalFormatting(
      id: json['id'] as String,
      worksheetId: json['worksheet_id'] as String,
      cellRange: json['cell_range'] as String,
      rule: ConditionalFormattingRule.fromJson(json['rule'] as Map<String, dynamic>),
      format: CellFormat.fromJson(json['format'] as Map<String, dynamic>),
      priority: json['priority'] as int,
      isEnabled: json['is_enabled'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'worksheet_id': worksheetId,
      'cell_range': cellRange,
      'rule': rule.toJson(),
      'format': format.toJson(),
      'priority': priority,
      'is_enabled': isEnabled,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Conditional Formatting Rule Model
class ConditionalFormattingRule {
  final RuleType type;
  final String? formula;
  final dynamic value1;
  final dynamic value2;
  final String? operator;

  const ConditionalFormattingRule({
    required this.type,
    this.formula,
    this.value1,
    this.value2,
    this.operator,
  });

  factory ConditionalFormattingRule.fromJson(Map<String, dynamic> json) {
    return ConditionalFormattingRule(
      type: RuleType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => RuleType.cellValue,
      ),
      formula: json['formula'] as String?,
      value1: json['value1'],
      value2: json['value2'],
      operator: json['operator'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'formula': formula,
      'value1': value1,
      'value2': value2,
      'operator': operator,
    };
  }
}

// Excel Filter Model
class ExcelFilter {
  final String id;
  final String worksheetId;
  final String dataRange;
  final List<FilterCriteria> criteria;
  final bool isAdvancedFilter;
  final bool isEnabled;
  final DateTime createdAt;

  const ExcelFilter({
    required this.id,
    required this.worksheetId,
    required this.dataRange,
    required this.criteria,
    required this.isAdvancedFilter,
    required this.isEnabled,
    required this.createdAt,
  });

  factory ExcelFilter.fromJson(Map<String, dynamic> json) {
    return ExcelFilter(
      id: json['id'] as String,
      worksheetId: json['worksheet_id'] as String,
      dataRange: json['data_range'] as String,
      criteria: (json['criteria'] as List<dynamic>?)
          ?.map((e) => FilterCriteria.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      isAdvancedFilter: json['is_advanced_filter'] as bool,
      isEnabled: json['is_enabled'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'worksheet_id': worksheetId,
      'data_range': dataRange,
      'criteria': criteria.map((e) => e.toJson()).toList(),
      'is_advanced_filter': isAdvancedFilter,
      'is_enabled': isEnabled,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Filter Criteria Model
class FilterCriteria {
  final String column;
  final FilterOperator operator;
  final dynamic value;
  final bool isCaseSensitive;

  const FilterCriteria({
    required this.column,
    required this.operator,
    required this.value,
    required this.isCaseSensitive,
  });

  factory FilterCriteria.fromJson(Map<String, dynamic> json) {
    return FilterCriteria(
      column: json['column'] as String,
      operator: FilterOperator.values.firstWhere(
        (e) => e.name == json['operator'],
        orElse: () => FilterOperator.equals,
      ),
      value: json['value'],
      isCaseSensitive: json['is_case_sensitive'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'column': column,
      'operator': operator.name,
      'value': value,
      'is_case_sensitive': isCaseSensitive,
    };
  }
}

// Excel Change Event Model
class ExcelChangeEvent {
  final ExcelChangeType type;
  final String? workbookId;
  final String? worksheetId;
  final String? cellRange;
  final DateTime timestamp;

  const ExcelChangeEvent({
    required this.type,
    this.workbookId,
    this.worksheetId,
    this.cellRange,
    required this.timestamp,
  });
}

// Enums
enum ChartType { column, bar, line, pie, area, scatter, bubble, doughnut, radar }
enum PivotFunction { sum, count, average, max, min, product, countNums, stdDev, stdDevP, variance, varP }
enum ValidationType { list, number, date, time, textLength, decimal, whole, custom }
enum FormulaCategory { math, statistical, logical, text, date, lookup, financial, engineering }
enum MacroActionType { setCellValue, formatCell, insertRow, deleteRow, copyRange, pasteRange }
enum RuleType { cellValue, formula, colorScale, dataBar, iconSet }
enum FilterOperator { equals, notEquals, greaterThan, lessThan, contains, startsWith, endsWith }
enum ExcelChangeType { workbookImported, cellsFormatted, chartAdded, pivotTableAdded, macroExecuted }
