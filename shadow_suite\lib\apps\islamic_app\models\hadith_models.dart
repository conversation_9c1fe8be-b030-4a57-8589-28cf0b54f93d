

// Hadith Collection Model
class HadithCollection {
  final String id;
  final String name;
  final String nameArabic;
  final String compiler;
  final int totalHadiths;
  final AuthenticityLevel authenticityLevel;
  final String language;
  final String description;
  final int priority;
  final bool isOffline;
  final int downloadSize;
  final DateTime createdAt;

  const HadithCollection({
    required this.id,
    required this.name,
    required this.nameArabic,
    required this.compiler,
    required this.totalHadiths,
    required this.authenticityLevel,
    required this.language,
    required this.description,
    required this.priority,
    required this.isOffline,
    required this.downloadSize,
    required this.createdAt,
  });

  factory HadithCollection.fromJson(Map<String, dynamic> json) {
    return HadithCollection(
      id: json['id'] as String,
      name: json['name'] as String,
      nameArabic: json['name_arabic'] as String,
      compiler: json['compiler'] as String,
      totalHadiths: json['total_hadiths'] as int,
      authenticityLevel: AuthenticityLevel.values.firstWhere(
        (e) => e.name == json['authenticity_level'],
        orElse: () => AuthenticityLevel.hasan,
      ),
      language: json['language'] as String,
      description: json['description'] as String,
      priority: json['priority'] as int,
      isOffline: json['is_offline'] as bool,
      downloadSize: json['download_size'] as int,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'name_arabic': nameArabic,
      'compiler': compiler,
      'total_hadiths': totalHadiths,
      'authenticity_level': authenticityLevel.name,
      'language': language,
      'description': description,
      'priority': priority,
      'is_offline': isOffline,
      'download_size': downloadSize,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Hadith Chapter Model
class HadithChapter {
  final String id;
  final String collectionId;
  final int chapterNumber;
  final String title;
  final String titleArabic;
  final String description;
  final int hadithCount;

  const HadithChapter({
    required this.id,
    required this.collectionId,
    required this.chapterNumber,
    required this.title,
    required this.titleArabic,
    required this.description,
    required this.hadithCount,
  });

  factory HadithChapter.fromJson(Map<String, dynamic> json) {
    return HadithChapter(
      id: json['id'] as String,
      collectionId: json['collection_id'] as String,
      chapterNumber: json['chapter_number'] as int,
      title: json['title'] as String,
      titleArabic: json['title_arabic'] as String,
      description: json['description'] as String,
      hadithCount: json['hadith_count'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'collection_id': collectionId,
      'chapter_number': chapterNumber,
      'title': title,
      'title_arabic': titleArabic,
      'description': description,
      'hadith_count': hadithCount,
    };
  }
}

// Hadith Entry Model
class HadithEntry {
  final String id;
  final String collectionId;
  final String chapterId;
  final int hadithNumber;
  final String arabicText;
  final String englishTranslation;
  final String narrator;
  final String chain;
  final HadithGrade grade;
  final List<String> keywords;
  final String theme;
  final String reference;
  final DateTime createdAt;

  const HadithEntry({
    required this.id,
    required this.collectionId,
    required this.chapterId,
    required this.hadithNumber,
    required this.arabicText,
    required this.englishTranslation,
    required this.narrator,
    required this.chain,
    required this.grade,
    required this.keywords,
    required this.theme,
    required this.reference,
    required this.createdAt,
  });

  factory HadithEntry.fromJson(Map<String, dynamic> json) {
    return HadithEntry(
      id: json['id'] as String,
      collectionId: json['collection_id'] as String,
      chapterId: json['chapter_id'] as String,
      hadithNumber: json['hadith_number'] as int,
      arabicText: json['arabic_text'] as String,
      englishTranslation: json['english_translation'] as String,
      narrator: json['narrator'] as String,
      chain: json['chain'] as String,
      grade: HadithGrade.values.firstWhere(
        (e) => e.name == json['grade'],
        orElse: () => HadithGrade.hasan,
      ),
      keywords: List<String>.from(json['keywords'] as List? ?? []),
      theme: json['theme'] as String,
      reference: json['reference'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'collection_id': collectionId,
      'chapter_id': chapterId,
      'hadith_number': hadithNumber,
      'arabic_text': arabicText,
      'english_translation': englishTranslation,
      'narrator': narrator,
      'chain': chain,
      'grade': grade.name,
      'keywords': keywords,
      'theme': theme,
      'reference': reference,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Hadith Search Result Model
class HadithSearchResult {
  final String hadithId;
  final String collectionId;
  final int hadithNumber;
  final String arabicText;
  final String englishTranslation;
  final String narrator;
  final HadithGrade grade;
  final String matchedText;
  final double relevanceScore;

  const HadithSearchResult({
    required this.hadithId,
    required this.collectionId,
    required this.hadithNumber,
    required this.arabicText,
    required this.englishTranslation,
    required this.narrator,
    required this.grade,
    required this.matchedText,
    required this.relevanceScore,
  });
}

// Hadith Bookmark Model
class HadithBookmark {
  final String id;
  final String userId;
  final String hadithId;
  final String collectionId;
  final int hadithNumber;
  final String title;
  final String? note;
  final List<String> tags;
  final DateTime createdAt;

  const HadithBookmark({
    required this.id,
    required this.userId,
    required this.hadithId,
    required this.collectionId,
    required this.hadithNumber,
    required this.title,
    this.note,
    required this.tags,
    required this.createdAt,
  });

  factory HadithBookmark.fromJson(Map<String, dynamic> json) {
    return HadithBookmark(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      hadithId: json['hadith_id'] as String,
      collectionId: json['collection_id'] as String,
      hadithNumber: json['hadith_number'] as int,
      title: json['title'] as String,
      note: json['note'] as String?,
      tags: List<String>.from(json['tags'] as List? ?? []),
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'hadith_id': hadithId,
      'collection_id': collectionId,
      'hadith_number': hadithNumber,
      'title': title,
      'note': note,
      'tags': tags,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Hadith Study Progress Model
class HadithStudyProgress {
  final String id;
  final String userId;
  final String collectionId;
  final String? chapterId;
  final int completedHadiths;
  final int totalHadiths;
  final DateTime lastStudiedAt;
  final Map<String, dynamic> notes;

  const HadithStudyProgress({
    required this.id,
    required this.userId,
    required this.collectionId,
    this.chapterId,
    required this.completedHadiths,
    required this.totalHadiths,
    required this.lastStudiedAt,
    required this.notes,
  });

  factory HadithStudyProgress.fromJson(Map<String, dynamic> json) {
    return HadithStudyProgress(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      collectionId: json['collection_id'] as String,
      chapterId: json['chapter_id'] as String?,
      completedHadiths: json['completed_hadiths'] as int,
      totalHadiths: json['total_hadiths'] as int,
      lastStudiedAt: DateTime.parse(json['last_studied_at'] as String),
      notes: Map<String, dynamic>.from(json['notes'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'collection_id': collectionId,
      'chapter_id': chapterId,
      'completed_hadiths': completedHadiths,
      'total_hadiths': totalHadiths,
      'last_studied_at': lastStudiedAt.toIso8601String(),
      'notes': notes,
    };
  }

  double get progressPercentage {
    return totalHadiths > 0 ? (completedHadiths / totalHadiths) * 100 : 0.0;
  }
}

// Hadith Change Event Model
class HadithChangeEvent {
  final HadithChangeType type;
  final String? collectionId;
  final String? hadithId;
  final DateTime timestamp;

  const HadithChangeEvent({
    required this.type,
    this.collectionId,
    this.hadithId,
    required this.timestamp,
  });
}

// Enums
enum AuthenticityLevel {
  sahih,    // Authentic
  hasan,    // Good
  daif,     // Weak
  mawdu,    // Fabricated
}

enum HadithGrade {
  sahih,           // Authentic
  hasan,           // Good
  daif,            // Weak
  sahihLiGhairihi, // Authentic due to external evidence
  hasanLiGhairihi, // Good due to external evidence
  daifJiddan,      // Very weak
  mawdu,           // Fabricated
}

enum HadithChangeType {
  collectionLoaded,
  hadithBookmarked,
  progressUpdated,
  searchPerformed,
}

// Extension methods
extension AuthenticityLevelExtension on AuthenticityLevel {
  String get displayName {
    switch (this) {
      case AuthenticityLevel.sahih:
        return 'Sahih (Authentic)';
      case AuthenticityLevel.hasan:
        return 'Hasan (Good)';
      case AuthenticityLevel.daif:
        return 'Da\'if (Weak)';
      case AuthenticityLevel.mawdu:
        return 'Mawdu\' (Fabricated)';
    }
  }

  String get arabicName {
    switch (this) {
      case AuthenticityLevel.sahih:
        return 'صحيح';
      case AuthenticityLevel.hasan:
        return 'حسن';
      case AuthenticityLevel.daif:
        return 'ضعيف';
      case AuthenticityLevel.mawdu:
        return 'موضوع';
    }
  }
}

extension HadithGradeExtension on HadithGrade {
  String get displayName {
    switch (this) {
      case HadithGrade.sahih:
        return 'Sahih';
      case HadithGrade.hasan:
        return 'Hasan';
      case HadithGrade.daif:
        return 'Da\'if';
      case HadithGrade.sahihLiGhairihi:
        return 'Sahih li-Ghairihi';
      case HadithGrade.hasanLiGhairihi:
        return 'Hasan li-Ghairihi';
      case HadithGrade.daifJiddan:
        return 'Da\'if Jiddan';
      case HadithGrade.mawdu:
        return 'Mawdu\'';
    }
  }

  bool get isAuthentic {
    return this == HadithGrade.sahih || this == HadithGrade.sahihLiGhairihi;
  }

  bool get isAcceptable {
    return isAuthentic || this == HadithGrade.hasan || this == HadithGrade.hasanLiGhairihi;
  }
}
