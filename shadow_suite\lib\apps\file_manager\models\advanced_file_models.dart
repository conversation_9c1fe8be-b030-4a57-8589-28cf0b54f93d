

// Batch Operation Model
class BatchOperation {
  final String id;
  final BatchOperationType type;
  final List<String> sourcePaths;
  final String? destinationPath;
  final BatchOperationStatus status;
  final double progress;
  final int totalItems;
  final int processedItems;
  final int failedItems;
  final DateTime startTime;
  final DateTime? endTime;
  final String? errorMessage;
  final Map<String, dynamic> options;

  const BatchOperation({
    required this.id,
    required this.type,
    required this.sourcePaths,
    this.destinationPath,
    required this.status,
    required this.progress,
    required this.totalItems,
    required this.processedItems,
    required this.failedItems,
    required this.startTime,
    this.endTime,
    this.errorMessage,
    required this.options,
  });

  factory BatchOperation.fromJson(Map<String, dynamic> json) {
    return BatchOperation(
      id: json['id'] as String,
      type: BatchOperationType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => BatchOperationType.copy,
      ),
      sourcePaths: List<String>.from(json['source_paths'] as List? ?? []),
      destinationPath: json['destination_path'] as String?,
      status: BatchOperationStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => BatchOperationStatus.pending,
      ),
      progress: (json['progress'] as num).toDouble(),
      totalItems: json['total_items'] as int,
      processedItems: json['processed_items'] as int,
      failedItems: json['failed_items'] as int,
      startTime: DateTime.parse(json['start_time'] as String),
      endTime: json['end_time'] != null ? DateTime.parse(json['end_time'] as String) : null,
      errorMessage: json['error_message'] as String?,
      options: Map<String, dynamic>.from(json['options'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'source_paths': sourcePaths,
      'destination_path': destinationPath,
      'status': status.name,
      'progress': progress,
      'total_items': totalItems,
      'processed_items': processedItems,
      'failed_items': failedItems,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'error_message': errorMessage,
      'options': options,
    };
  }
}

// File Search Criteria Model
class FileSearchCriteria {
  final String searchPath;
  final String? namePattern;
  final String? contentPattern;
  final bool useRegex;
  final List<String>? fileExtensions;
  final int? minSize;
  final int? maxSize;
  final DateTime? modifiedAfter;
  final DateTime? modifiedBefore;
  final List<String>? tags;
  final bool searchInContent;
  final bool caseSensitive;

  const FileSearchCriteria({
    required this.searchPath,
    this.namePattern,
    this.contentPattern,
    required this.useRegex,
    this.fileExtensions,
    this.minSize,
    this.maxSize,
    this.modifiedAfter,
    this.modifiedBefore,
    this.tags,
    required this.searchInContent,
    required this.caseSensitive,
  });
}

// File Search Result Model
class FileSearchResult {
  final String filePath;
  final String fileName;
  final int fileSize;
  final DateTime lastModified;
  final String? matchedContent;
  final List<int>? matchPositions;
  final double relevanceScore;

  const FileSearchResult({
    required this.filePath,
    required this.fileName,
    required this.fileSize,
    required this.lastModified,
    this.matchedContent,
    this.matchPositions,
    required this.relevanceScore,
  });
}

// File Comparison Result Model
class FileComparisonResult {
  final String file1Path;
  final String file2Path;
  final bool sizeMatch;
  final bool modifiedMatch;
  final bool contentMatch;
  final int file1Size;
  final int file2Size;
  final DateTime file1Modified;
  final DateTime file2Modified;
  final List<FileDifference>? differences;
  final DateTime comparisonDate;

  const FileComparisonResult({
    required this.file1Path,
    required this.file2Path,
    required this.sizeMatch,
    required this.modifiedMatch,
    required this.contentMatch,
    required this.file1Size,
    required this.file2Size,
    required this.file1Modified,
    required this.file2Modified,
    this.differences,
    required this.comparisonDate,
  });
}

// File Difference Model
class FileDifference {
  final int lineNumber;
  final DifferenceType type;
  final String? originalLine;
  final String? modifiedLine;

  const FileDifference({
    required this.lineNumber,
    required this.type,
    this.originalLine,
    this.modifiedLine,
  });
}

// Duplicate File Group Model
class DuplicateFileGroup {
  final String id;
  final String hash;
  final List<String> filePaths;
  final int totalSize;
  final int duplicateCount;
  final DateTime foundAt;

  const DuplicateFileGroup({
    required this.id,
    required this.hash,
    required this.filePaths,
    required this.totalSize,
    required this.duplicateCount,
    required this.foundAt,
  });

  int get wastedSpace => totalSize * duplicateCount;
}

// File Encryption Metadata Model
class FileEncryptionMetadata {
  final String originalPath;
  final String encryptedPath;
  final EncryptionAlgorithm algorithm;
  final DateTime encryptedAt;
  final int originalSize;
  final int encryptedSize;

  const FileEncryptionMetadata({
    required this.originalPath,
    required this.encryptedPath,
    required this.algorithm,
    required this.encryptedAt,
    required this.originalSize,
    required this.encryptedSize,
  });
}

// File Version Model
class FileVersion {
  final String id;
  final String filePath;
  final int versionNumber;
  final String hash;
  final int size;
  final String? comment;
  final DateTime createdAt;

  const FileVersion({
    required this.id,
    required this.filePath,
    required this.versionNumber,
    required this.hash,
    required this.size,
    this.comment,
    required this.createdAt,
  });

  factory FileVersion.fromJson(Map<String, dynamic> json) {
    return FileVersion(
      id: json['id'] as String,
      filePath: json['file_path'] as String,
      versionNumber: json['version_number'] as int,
      hash: json['hash'] as String,
      size: json['size'] as int,
      comment: json['comment'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'file_path': filePath,
      'version_number': versionNumber,
      'hash': hash,
      'size': size,
      'comment': comment,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// File Permissions Model
class FilePermissions {
  final bool ownerRead;
  final bool ownerWrite;
  final bool ownerExecute;
  final bool groupRead;
  final bool groupWrite;
  final bool groupExecute;
  final bool otherRead;
  final bool otherWrite;
  final bool otherExecute;

  const FilePermissions({
    required this.ownerRead,
    required this.ownerWrite,
    required this.ownerExecute,
    required this.groupRead,
    required this.groupWrite,
    required this.groupExecute,
    required this.otherRead,
    required this.otherWrite,
    required this.otherExecute,
  });

  factory FilePermissions.fromOctal(int octal) {
    return FilePermissions(
      ownerRead: (octal & 0x100) != 0,
      ownerWrite: (octal & 0x080) != 0,
      ownerExecute: (octal & 0x040) != 0,
      groupRead: (octal & 0x020) != 0,
      groupWrite: (octal & 0x010) != 0,
      groupExecute: (octal & 0x008) != 0,
      otherRead: (octal & 0x004) != 0,
      otherWrite: (octal & 0x002) != 0,
      otherExecute: (octal & 0x001) != 0,
    );
  }

  int toOctal() {
    int result = 0;
    if (ownerRead) result |= 0x100;
    if (ownerWrite) result |= 0x080;
    if (ownerExecute) result |= 0x040;
    if (groupRead) result |= 0x020;
    if (groupWrite) result |= 0x010;
    if (groupExecute) result |= 0x008;
    if (otherRead) result |= 0x004;
    if (otherWrite) result |= 0x002;
    if (otherExecute) result |= 0x001;
    return result;
  }
}

// File Tag Model
class FileTag {
  final String id;
  final String filePath;
  final String tag;
  final String color;
  final DateTime createdAt;

  const FileTag({
    required this.id,
    required this.filePath,
    required this.tag,
    required this.color,
    required this.createdAt,
  });

  factory FileTag.fromJson(Map<String, dynamic> json) {
    return FileTag(
      id: json['id'] as String,
      filePath: json['file_path'] as String,
      tag: json['tag'] as String,
      color: json['color'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'file_path': filePath,
      'tag': tag,
      'color': color,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// File Category Model
class FileCategory {
  final String name;
  final String description;
  final String icon;
  final String color;
  final List<String> filePaths;
  final CategoryType type;

  FileCategory({
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.filePaths,
    required this.type,
  });
}

// Advanced File Event Model
class AdvancedFileEvent {
  final AdvancedFileEventType type;
  final String? filePath;
  final String message;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  const AdvancedFileEvent({
    required this.type,
    this.filePath,
    required this.message,
    required this.timestamp,
    this.metadata,
  });
}

// Enums
enum BatchOperationType { copy, move, delete, compress, extract, encrypt, decrypt }
enum BatchOperationStatus { pending, running, completed, failed, cancelled }
enum DifferenceType { added, removed, modified }
enum EncryptionAlgorithm { aes128, aes256, rsa2048, rsa4096 }
enum CategoryType { document, media, archive, code, system, other }
enum AdvancedFileEventType {
  batchOperationStarted,
  batchOperationCompleted,
  searchCompleted,
  duplicatesFound,
  fileEncrypted,
  fileDecrypted,
  fileSecurelyDeleted,
  versionCreated,
  permissionsChanged,
  tagAdded,
  tagRemoved,
  filesOrganized,
}
