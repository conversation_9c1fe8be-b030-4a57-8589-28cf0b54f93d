import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:io';
import '../models/media_models.dart';
import '../services/shadow_player_providers.dart';

class VideoFolderView extends ConsumerWidget {
  final List<MediaFile> videos;

  const VideoFolderView({super.key, required this.videos});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final folderGroups = _groupVideosByFolder(videos);

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: folderGroups.length,
      itemBuilder: (context, index) {
        final folderPath = folderGroups.keys.elementAt(index);
        final folderVideos = folderGroups[folderPath]!;
        return VideoFolderGroup(folderPath: folderPath, videos: folderVideos);
      },
    );
  }

  Map<String, List<MediaFile>> _groupVideosByFolder(List<MediaFile> videos) {
    final Map<String, List<MediaFile>> groups = {};

    for (final video in videos) {
      final folderPath = Directory(video.path).parent.path;
      if (!groups.containsKey(folderPath)) {
        groups[folderPath] = [];
      }
      groups[folderPath]!.add(video);
    }

    // Sort folders by name
    final sortedKeys = groups.keys.toList()..sort();
    final sortedGroups = <String, List<MediaFile>>{};
    for (final key in sortedKeys) {
      sortedGroups[key] = groups[key]!;
    }

    return sortedGroups;
  }
}

class VideoFolderGroup extends ConsumerStatefulWidget {
  final String folderPath;
  final List<MediaFile> videos;

  const VideoFolderGroup({
    super.key,
    required this.folderPath,
    required this.videos,
  });

  @override
  ConsumerState<VideoFolderGroup> createState() => _VideoFolderGroupState();
}

class _VideoFolderGroupState extends ConsumerState<VideoFolderGroup> {
  bool _isExpanded = true;

  @override
  Widget build(BuildContext context) {
    final folderName = widget.folderPath.split('/').last;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Folder Header
          InkWell(
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.vertical(
                  top: const Radius.circular(12),
                  bottom: _isExpanded ? Radius.zero : const Radius.circular(12),
                ),
                color: const Color(0xFF34495E),
              ),
              child: Row(
                children: [
                  Icon(
                    _isExpanded ? Icons.folder_open : Icons.folder,
                    color: const Color(0xFFE67E22),
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          folderName.isNotEmpty ? folderName : 'Root',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          '${widget.videos.length} videos',
                          style: const TextStyle(
                            color: Color(0xFFBDC3C7),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF2C3E50),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${widget.videos.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: const Color(0xFFBDC3C7),
                    size: 24,
                  ),
                ],
              ),
            ),
          ),

          // Videos List
          if (_isExpanded)
            Column(
              children: widget.videos.map((video) {
                return VideoFolderItem(video: video);
              }).toList(),
            ),
        ],
      ),
    );
  }
}

class VideoFolderItem extends ConsumerWidget {
  final MediaFile video;

  const VideoFolderItem({super.key, required this.video});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      decoration: const BoxDecoration(
        border: Border(top: BorderSide(color: Color(0xFFF1F2F6), width: 1)),
      ),
      child: InkWell(
        onTap: () => _playVideo(context, ref),
        onLongPress: () => _showVideoOptions(context, ref),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Video Thumbnail
              Container(
                width: 80,
                height: 45,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  color: const Color(0xFF2C3E50),
                ),
                child: Stack(
                  children: [
                    // Thumbnail Image
                    ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: video.thumbnailPath != null
                          ? Image.asset(
                              video.thumbnailPath!,
                              width: double.infinity,
                              height: double.infinity,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  _buildDefaultThumbnail(),
                            )
                          : _buildDefaultThumbnail(),
                    ),

                    // Play Button Overlay
                    Center(
                      child: Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Icon(
                          Icons.play_arrow,
                          color: Colors.white,
                          size: 12,
                        ),
                      ),
                    ),

                    // Duration Badge
                    Positioned(
                      bottom: 2,
                      right: 2,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 3,
                          vertical: 1,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.8),
                          borderRadius: BorderRadius.circular(2),
                        ),
                        child: Text(
                          _formatDuration(video.duration!),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 8,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),

              // Video Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    Text(
                      video.metadata.title?.isNotEmpty == true
                          ? video.metadata.title!
                          : video.path.split('/').last,
                      style: const TextStyle(
                        color: Color(0xFF2C3E50),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),

                    // File Info
                    Row(
                      children: [
                        Text(
                          _getFileInfo(),
                          style: const TextStyle(
                            color: Color(0xFF7F8C8D),
                            fontSize: 11,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _formatFileSize(video.size),
                          style: const TextStyle(
                            color: Color(0xFF95A5A6),
                            fontSize: 11,
                          ),
                        ),
                        if (video.metadata.resolution != null) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 4,
                              vertical: 1,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(
                                0xFF3498DB,
                              ).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(2),
                            ),
                            child: Text(
                              video.metadata.resolution!,
                              style: const TextStyle(
                                color: Color(0xFF3498DB),
                                fontSize: 9,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),

              // Favorite Icon
              if (video.isFavorite)
                const Padding(
                  padding: EdgeInsets.only(left: 8),
                  child: Icon(
                    Icons.favorite,
                    color: Color(0xFFE74C3C),
                    size: 16,
                  ),
                ),

              // Action Button
              IconButton(
                onPressed: () => _showVideoOptions(context, ref),
                icon: const Icon(
                  Icons.more_vert,
                  color: Color(0xFF95A5A6),
                  size: 16,
                ),
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                padding: EdgeInsets.zero,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDefaultThumbnail() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: const Color(0xFF34495E),
      child: const Icon(Icons.video_file, color: Color(0xFFE74C3C), size: 16),
    );
  }

  String _getFileInfo() {
    final extension = video.path.split('.').last.toUpperCase();
    final codec = video.metadata.codec ?? extension;
    return codec;
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  void _playVideo(BuildContext context, WidgetRef ref) {
    // Play the video using the media player service
    ref.read(mediaPlayerServiceProvider).playMedia(video);

    // Navigate to full-screen video player
    Navigator.of(
      context,
    ).pushNamed('/shadow-player/video-player', arguments: video);
  }

  void _showVideoOptions(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFF2C3E50),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Video Title
            Text(
              video.metadata.title ?? video.displayName,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 16),

            // Options
            _buildOption(
              icon: Icons.play_arrow,
              title: 'Play Video',
              onTap: () {
                Navigator.pop(context);
                _playVideo(context, ref);
              },
            ),
            _buildOption(
              icon: video.isFavorite ? Icons.favorite : Icons.favorite_border,
              title: video.isFavorite
                  ? 'Remove from Favorites'
                  : 'Add to Favorites',
              onTap: () {
                Navigator.pop(context);
                ref
                    .read(allMediaFilesProvider.notifier)
                    .toggleFavorite(video.id);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOption({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        child: Row(
          children: [
            Icon(icon, color: const Color(0xFF3498DB), size: 24),
            const SizedBox(width: 16),
            Text(
              title,
              style: const TextStyle(color: Colors.white, fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }
}
