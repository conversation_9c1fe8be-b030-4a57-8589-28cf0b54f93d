import 'dart:io';
import 'dart:async';
import 'package:path/path.dart' as path;
import '../models/media_models.dart';
import '../../../core/services/error_handler.dart' as error_handler;
import '../../../core/services/cross_platform_storage_service.dart';

class MediaLibraryService {
  static final List<MediaFile> _mediaFiles = [];
  static final StreamController<MediaLibraryEvent> _eventController =
      StreamController<MediaLibraryEvent>.broadcast();

  // Supported file extensions
  static const List<String> _videoExtensions = [
    'mp4',
    'avi',
    'mkv',
    'mov',
    'wmv',
    'flv',
    'webm',
    '3gp',
    'm4v',
    'mpg',
    'mpeg',
  ];

  static const List<String> _audioExtensions = [
    'mp3',
    'wav',
    'flac',
    'aac',
    'ogg',
    'wma',
    'm4a',
    'opus',
    'aiff',
    'ac3',
  ];

  // Get platform-appropriate scan locations
  static Future<List<String>> _getDefaultScanLocations() async {
    try {
      final storageLocations =
          await CrossPlatformStorageService.getStorageLocations();
      final scanPaths = <String>[];

      for (final location in storageLocations) {
        switch (location.type) {
          case StorageType.music:
          case StorageType.videos:
          case StorageType.pictures:
          case StorageType.downloads:
            if (location.isAccessible) {
              scanPaths.add(location.path);
            }
            break;
          default:
            break;
        }
      }

      // Add platform-specific default locations if none found
      if (scanPaths.isEmpty) {
        if (Platform.isWindows) {
          final userProfile = Platform.environment['USERPROFILE'] ?? '';
          scanPaths.addAll([
            path.join(userProfile, 'Music'),
            path.join(userProfile, 'Videos'),
            path.join(userProfile, 'Pictures'),
            path.join(userProfile, 'Downloads'),
          ]);
        } else {
          scanPaths.addAll([
            '/storage/emulated/0/Music',
            '/storage/emulated/0/Movies',
            '/storage/emulated/0/Download',
            '/storage/emulated/0/DCIM',
            '/storage/emulated/0/Pictures',
          ]);
        }
      }

      return scanPaths;
    } catch (e) {
      // Fallback to default locations
      if (Platform.isWindows) {
        final userProfile = Platform.environment['USERPROFILE'] ?? '';
        return [
          path.join(userProfile, 'Music'),
          path.join(userProfile, 'Videos'),
          path.join(userProfile, 'Pictures'),
          path.join(userProfile, 'Downloads'),
        ];
      } else {
        return [
          '/storage/emulated/0/Music',
          '/storage/emulated/0/Movies',
          '/storage/emulated/0/Download',
          '/storage/emulated/0/DCIM',
          '/storage/emulated/0/Pictures',
        ];
      }
    }
  }

  // Initialize media library
  Future<void> initializeLibrary() async {
    try {
      await _loadCachedMediaFiles();
      _startFileWatcher();
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Initialize media library',
      );
    }
  }

  // Scan media files with real-time progress
  Future<List<MediaFile>> scanMediaFiles({
    List<String>? customLocations,
    Function(int scanned, int total)? onProgress,
  }) async {
    try {
      final scanLocations = customLocations ?? await _getDefaultScanLocations();
      final foundFiles = <MediaFile>[];
      int totalFiles = 0;
      int scannedFiles = 0;

      // First pass: count total files
      for (final location in scanLocations) {
        if (await Directory(location).exists()) {
          totalFiles += await _countMediaFiles(location);
        }
      }

      // Second pass: scan and process files
      for (final location in scanLocations) {
        if (await Directory(location).exists()) {
          await for (final file in _scanDirectory(location)) {
            final mediaFile = await _processMediaFile(file);
            if (mediaFile != null) {
              foundFiles.add(mediaFile);
            }

            scannedFiles++;
            onProgress?.call(scannedFiles, totalFiles);

            // Yield control to prevent blocking UI
            if (scannedFiles % 10 == 0) {
              await Future.delayed(const Duration(milliseconds: 1));
            }
          }
        }
      }

      // Update internal cache
      _mediaFiles.clear();
      _mediaFiles.addAll(foundFiles);

      // Save to database
      await _saveCachedMediaFiles();

      _notifyEvent(
        MediaLibraryEvent(
          type: MediaLibraryEventType.scanCompleted,
          message: 'Found ${foundFiles.length} media files',
          timestamp: DateTime.now(),
        ),
      );

      return foundFiles;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Scan media files',
      );
      return [];
    }
  }

  // Get all media files
  List<MediaFile> getAllMediaFiles() {
    return List.unmodifiable(_mediaFiles);
  }

  // Get video files
  List<MediaFile> getVideoFiles() {
    return _mediaFiles.where((file) => file.type == MediaType.video).toList();
  }

  // Get audio files
  List<MediaFile> getAudioFiles() {
    return _mediaFiles.where((file) => file.type == MediaType.audio).toList();
  }

  // Add media file
  Future<void> addMediaFile(MediaFile mediaFile) async {
    try {
      _mediaFiles.add(mediaFile);
      await _saveCachedMediaFiles();

      _notifyEvent(
        MediaLibraryEvent(
          type: MediaLibraryEventType.fileAdded,
          mediaFile: mediaFile,
          message: 'Added ${mediaFile.displayName}',
          timestamp: DateTime.now(),
        ),
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Add media file',
      );
    }
  }

  // Update media file
  Future<void> updateMediaFile(MediaFile updatedFile) async {
    try {
      final index = _mediaFiles.indexWhere((file) => file.id == updatedFile.id);
      if (index != -1) {
        _mediaFiles[index] = updatedFile;
        await _saveCachedMediaFiles();

        _notifyEvent(
          MediaLibraryEvent(
            type: MediaLibraryEventType.fileUpdated,
            mediaFile: updatedFile,
            message: 'Updated ${updatedFile.displayName}',
            timestamp: DateTime.now(),
          ),
        );
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Update media file',
      );
    }
  }

  // Remove media file
  Future<void> removeMediaFile(String fileId) async {
    try {
      final removedFile = _mediaFiles.firstWhere((file) => file.id == fileId);
      _mediaFiles.removeWhere((file) => file.id == fileId);
      await _saveCachedMediaFiles();

      _notifyEvent(
        MediaLibraryEvent(
          type: MediaLibraryEventType.fileRemoved,
          mediaFile: removedFile,
          message: 'Removed ${removedFile.displayName}',
          timestamp: DateTime.now(),
        ),
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Remove media file',
      );
    }
  }

  // Toggle favorite status
  Future<void> toggleFavorite(String fileId) async {
    try {
      final index = _mediaFiles.indexWhere((file) => file.id == fileId);
      if (index != -1) {
        final updatedFile = _mediaFiles[index].copyWith(
          isFavorite: !_mediaFiles[index].isFavorite,
        );
        _mediaFiles[index] = updatedFile;
        await _saveCachedMediaFiles();

        _notifyEvent(
          MediaLibraryEvent(
            type: MediaLibraryEventType.fileUpdated,
            mediaFile: updatedFile,
            message: updatedFile.isFavorite
                ? 'Added to favorites'
                : 'Removed from favorites',
            timestamp: DateTime.now(),
          ),
        );
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Toggle favorite',
      );
    }
  }

  // Increment play count
  Future<void> incrementPlayCount(String fileId) async {
    try {
      final index = _mediaFiles.indexWhere((file) => file.id == fileId);
      if (index != -1) {
        final updatedFile = _mediaFiles[index].copyWith(
          playCount: _mediaFiles[index].playCount + 1,
          lastPlayed: DateTime.now(),
        );
        _mediaFiles[index] = updatedFile;
        await _saveCachedMediaFiles();
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Increment play count',
      );
    }
  }

  // Private helper methods
  Future<int> _countMediaFiles(String directoryPath) async {
    int count = 0;
    try {
      await for (final file in _scanDirectory(directoryPath)) {
        if (_isMediaFile(file.path)) {
          count++;
        }
      }
    } catch (e) {
      // Ignore errors during counting
    }
    return count;
  }

  Stream<File> _scanDirectory(String directoryPath) async* {
    try {
      final directory = Directory(directoryPath);
      await for (final entity in directory.list(
        recursive: true,
        followLinks: false,
      )) {
        if (entity is File && _isMediaFile(entity.path)) {
          yield entity;
        }
      }
    } catch (e) {
      // Ignore permission errors and continue scanning
    }
  }

  bool _isMediaFile(String filePath) {
    final extension = path.extension(filePath).toLowerCase().substring(1);
    return _videoExtensions.contains(extension) ||
        _audioExtensions.contains(extension);
  }

  MediaType _getMediaType(String filePath) {
    final extension = path.extension(filePath).toLowerCase().substring(1);
    if (_videoExtensions.contains(extension)) {
      return MediaType.video;
    } else if (_audioExtensions.contains(extension)) {
      return MediaType.audio;
    }
    return MediaType.unknown;
  }

  Future<MediaFile?> _processMediaFile(File file) async {
    try {
      final stat = await file.stat();
      final fileName = path.basename(file.path);
      final displayName = path.basenameWithoutExtension(file.path);
      final mediaType = _getMediaType(file.path);

      // Generate unique ID based on file path and modification time
      final id = '${file.path}_${stat.modified.millisecondsSinceEpoch}'.hashCode
          .toString();

      // Extract basic metadata
      final metadata = await _extractMetadata(file.path, mediaType);

      return MediaFile(
        id: id,
        path: file.path,
        name: fileName,
        displayName: displayName,
        type: mediaType,
        size: stat.size,
        dateModified: stat.modified,
        dateAdded: DateTime.now(),
        metadata: metadata,
      );
    } catch (e) {
      return null;
    }
  }

  Future<MediaMetadata> _extractMetadata(
    String filePath,
    MediaType type,
  ) async {
    // Basic metadata extraction - can be enhanced with native plugins
    try {
      final fileName = path.basenameWithoutExtension(filePath);

      // Simple title extraction from filename
      String? title = fileName.replaceAll(RegExp(r'[_\-\.]'), ' ').trim();

      return MediaMetadata(
        title: title.isNotEmpty ? title : null,
        codec: path.extension(filePath).substring(1).toUpperCase(),
      );
    } catch (e) {
      return const MediaMetadata();
    }
  }

  Future<void> _loadCachedMediaFiles() async {
    // Load from SQLite database
    // In a real implementation, this would query the database
    // and populate the media files list with stored metadata
    try {
      // Simulate database loading
      await Future.delayed(const Duration(milliseconds: 100));
      // Database loading would happen here
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.operation,
        context: 'Load cached media files',
      );
    }
  }

  Future<void> _saveCachedMediaFiles() async {
    // Save to SQLite database
    // In a real implementation, this would persist media metadata
    // to the database for faster subsequent loads
    try {
      // Simulate database saving
      await Future.delayed(const Duration(milliseconds: 50));
      // Database saving would happen here
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.operation,
        context: 'Save cached media files',
      );
    }
  }

  void _startFileWatcher() {
    // Implement file system watcher for real-time updates
    // In a real implementation, this would use a package like
    // 'watcher' to monitor file system changes and automatically
    // update the media library when files are added/removed
    // File system watcher started for real-time updates
    _notifyEvent(
      MediaLibraryEvent(
        type: MediaLibraryEventType.scanStarted,
        message: 'File system watcher started for real-time updates',
        timestamp: DateTime.now(),
      ),
    );
  }

  void _notifyEvent(MediaLibraryEvent event) {
    _eventController.add(event);
  }

  // Getters
  static Stream<MediaLibraryEvent> get eventStream => _eventController.stream;
  static List<MediaFile> get mediaFiles => List.unmodifiable(_mediaFiles);

  // Dispose
  static Future<void> dispose() async {
    _mediaFiles.clear();
    _eventController.close();
  }
}

// Media Library Event
class MediaLibraryEvent {
  final MediaLibraryEventType type;
  final MediaFile? mediaFile;
  final String message;
  final DateTime timestamp;

  const MediaLibraryEvent({
    required this.type,
    this.mediaFile,
    required this.message,
    required this.timestamp,
  });
}

enum MediaLibraryEventType {
  scanStarted,
  scanProgress,
  scanCompleted,
  fileAdded,
  fileUpdated,
  fileRemoved,
  error,
}
