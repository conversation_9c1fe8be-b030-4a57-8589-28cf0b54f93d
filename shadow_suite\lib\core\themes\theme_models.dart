import 'package:flutter/material.dart';

// Main theme model
class ShadowSuiteTheme {
  final String id;
  final String name;
  final String description;
  final ThemeCategory category;
  final ColorScheme colorScheme;
  final TextTheme typography;
  final ThemeCustomizations customizations;
  final DateTime createdAt;
  final bool isCustom;

  ShadowSuiteTheme({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.colorScheme,
    required this.typography,
    required this.customizations,
    DateTime? createdAt,
    this.isCustom = false,
  }) : createdAt = createdAt ?? DateTime.now();

  factory ShadowSuiteTheme.fromJson(Map<String, dynamic> json) {
    return ShadowSuiteTheme(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      category: ThemeCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => ThemeCategory.custom,
      ),
      colorScheme: _colorSchemeFromJson(json['color_scheme'] as Map<String, dynamic>),
      typography: _textThemeFromJson(json['typography'] as Map<String, dynamic>),
      customizations: ThemeCustomizations.fromJson(json['customizations'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['created_at'] as String),
      isCustom: json['is_custom'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category.name,
      'color_scheme': _colorSchemeToJson(colorScheme),
      'typography': _textThemeToJson(typography),
      'customizations': customizations.toJson(),
      'created_at': createdAt.toIso8601String(),
      'is_custom': isCustom,
    };
  }

  static ColorScheme _colorSchemeFromJson(Map<String, dynamic> json) {
    return ColorScheme(
      brightness: json['brightness'] == 'dark' ? Brightness.dark : Brightness.light,
      primary: Color(json['primary'] as int),
      onPrimary: Color(json['on_primary'] as int),
      primaryContainer: Color(json['primary_container'] as int),
      onPrimaryContainer: Color(json['on_primary_container'] as int),
      secondary: Color(json['secondary'] as int),
      onSecondary: Color(json['on_secondary'] as int),
      secondaryContainer: Color(json['secondary_container'] as int),
      onSecondaryContainer: Color(json['on_secondary_container'] as int),
      tertiary: Color(json['tertiary'] as int),
      onTertiary: Color(json['on_tertiary'] as int),
      tertiaryContainer: Color(json['tertiary_container'] as int),
      onTertiaryContainer: Color(json['on_tertiary_container'] as int),
      error: Color(json['error'] as int),
      onError: Color(json['on_error'] as int),
      errorContainer: Color(json['error_container'] as int),
      onErrorContainer: Color(json['on_error_container'] as int),
      surface: Color(json['surface'] as int),
      onSurface: Color(json['on_surface'] as int),
      surfaceContainerHighest: Color(json['surface_variant'] as int),
      onSurfaceVariant: Color(json['on_surface_variant'] as int),
      outline: Color(json['outline'] as int),
      outlineVariant: Color(json['outline_variant'] as int),
      shadow: Color(json['shadow'] as int),
      scrim: Color(json['scrim'] as int),
      inverseSurface: Color(json['inverse_surface'] as int),
      onInverseSurface: Color(json['on_inverse_surface'] as int),
      inversePrimary: Color(json['inverse_primary'] as int),
    );
  }

  static Map<String, dynamic> _colorSchemeToJson(ColorScheme colorScheme) {
    return {
      'brightness': colorScheme.brightness.name,
      'primary': colorScheme.primary.toARGB32(),
      'on_primary': colorScheme.onPrimary.toARGB32(),
      'primary_container': colorScheme.primaryContainer.toARGB32(),
      'on_primary_container': colorScheme.onPrimaryContainer.toARGB32(),
      'secondary': colorScheme.secondary.toARGB32(),
      'on_secondary': colorScheme.onSecondary.toARGB32(),
      'secondary_container': colorScheme.secondaryContainer.toARGB32(),
      'on_secondary_container': colorScheme.onSecondaryContainer.toARGB32(),
      'tertiary': colorScheme.tertiary.toARGB32(),
      'on_tertiary': colorScheme.onTertiary.toARGB32(),
      'tertiary_container': colorScheme.tertiaryContainer.toARGB32(),
      'on_tertiary_container': colorScheme.onTertiaryContainer.toARGB32(),
      'error': colorScheme.error.toARGB32(),
      'on_error': colorScheme.onError.toARGB32(),
      'error_container': colorScheme.errorContainer.toARGB32(),
      'on_error_container': colorScheme.onErrorContainer.toARGB32(),
      'surface': colorScheme.surface.toARGB32(),
      'on_surface': colorScheme.onSurface.toARGB32(),
      'surface_variant': colorScheme.surfaceContainerHighest.toARGB32(),
      'on_surface_variant': colorScheme.onSurfaceVariant.toARGB32(),
      'outline': colorScheme.outline.toARGB32(),
      'outline_variant': colorScheme.outlineVariant.toARGB32(),
      'shadow': colorScheme.shadow.toARGB32(),
      'scrim': colorScheme.scrim.toARGB32(),
      'inverse_surface': colorScheme.inverseSurface.toARGB32(),
      'on_inverse_surface': colorScheme.onInverseSurface.toARGB32(),
      'inverse_primary': colorScheme.inversePrimary.toARGB32(),
    };
  }

  static TextTheme _textThemeFromJson(Map<String, dynamic> json) {
    return TextTheme(
      displayLarge: _textStyleFromJson(json['display_large'] as Map<String, dynamic>?),
      displayMedium: _textStyleFromJson(json['display_medium'] as Map<String, dynamic>?),
      displaySmall: _textStyleFromJson(json['display_small'] as Map<String, dynamic>?),
      headlineLarge: _textStyleFromJson(json['headline_large'] as Map<String, dynamic>?),
      headlineMedium: _textStyleFromJson(json['headline_medium'] as Map<String, dynamic>?),
      headlineSmall: _textStyleFromJson(json['headline_small'] as Map<String, dynamic>?),
      titleLarge: _textStyleFromJson(json['title_large'] as Map<String, dynamic>?),
      titleMedium: _textStyleFromJson(json['title_medium'] as Map<String, dynamic>?),
      titleSmall: _textStyleFromJson(json['title_small'] as Map<String, dynamic>?),
      bodyLarge: _textStyleFromJson(json['body_large'] as Map<String, dynamic>?),
      bodyMedium: _textStyleFromJson(json['body_medium'] as Map<String, dynamic>?),
      bodySmall: _textStyleFromJson(json['body_small'] as Map<String, dynamic>?),
      labelLarge: _textStyleFromJson(json['label_large'] as Map<String, dynamic>?),
      labelMedium: _textStyleFromJson(json['label_medium'] as Map<String, dynamic>?),
      labelSmall: _textStyleFromJson(json['label_small'] as Map<String, dynamic>?),
    );
  }

  static Map<String, dynamic> _textThemeToJson(TextTheme textTheme) {
    return {
      'display_large': _textStyleToJson(textTheme.displayLarge),
      'display_medium': _textStyleToJson(textTheme.displayMedium),
      'display_small': _textStyleToJson(textTheme.displaySmall),
      'headline_large': _textStyleToJson(textTheme.headlineLarge),
      'headline_medium': _textStyleToJson(textTheme.headlineMedium),
      'headline_small': _textStyleToJson(textTheme.headlineSmall),
      'title_large': _textStyleToJson(textTheme.titleLarge),
      'title_medium': _textStyleToJson(textTheme.titleMedium),
      'title_small': _textStyleToJson(textTheme.titleSmall),
      'body_large': _textStyleToJson(textTheme.bodyLarge),
      'body_medium': _textStyleToJson(textTheme.bodyMedium),
      'body_small': _textStyleToJson(textTheme.bodySmall),
      'label_large': _textStyleToJson(textTheme.labelLarge),
      'label_medium': _textStyleToJson(textTheme.labelMedium),
      'label_small': _textStyleToJson(textTheme.labelSmall),
    };
  }

  static TextStyle? _textStyleFromJson(Map<String, dynamic>? json) {
    if (json == null) return null;
    return TextStyle(
      fontSize: json['font_size']?.toDouble(),
      fontWeight: json['font_weight'] != null 
          ? FontWeight.values[json['font_weight'] as int]
          : null,
      fontFamily: json['font_family'] as String?,
      letterSpacing: json['letter_spacing']?.toDouble(),
      height: json['height']?.toDouble(),
    );
  }

  static Map<String, dynamic>? _textStyleToJson(TextStyle? textStyle) {
    if (textStyle == null) return null;
    return {
      'font_size': textStyle.fontSize,
      'font_weight': textStyle.fontWeight?.index,
      'font_family': textStyle.fontFamily,
      'letter_spacing': textStyle.letterSpacing,
      'height': textStyle.height,
    };
  }

  ShadowSuiteTheme copyWith({
    String? name,
    String? description,
    ThemeCategory? category,
    ColorScheme? colorScheme,
    TextTheme? typography,
    ThemeCustomizations? customizations,
  }) {
    return ShadowSuiteTheme(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      colorScheme: colorScheme ?? this.colorScheme,
      typography: typography ?? this.typography,
      customizations: customizations ?? this.customizations,
      createdAt: createdAt,
      isCustom: true,
    );
  }
}

// Theme customizations model
class ThemeCustomizations {
  final double borderRadius;
  final double elevation;
  final Duration animationDuration;
  final double spacing;
  final double iconSize;
  final double buttonHeight;
  final bool enableAnimations;
  final bool enableHapticFeedback;
  final bool enableSounds;

  const ThemeCustomizations({
    required this.borderRadius,
    required this.elevation,
    required this.animationDuration,
    required this.spacing,
    required this.iconSize,
    required this.buttonHeight,
    required this.enableAnimations,
    required this.enableHapticFeedback,
    required this.enableSounds,
  });

  factory ThemeCustomizations.fromJson(Map<String, dynamic> json) {
    return ThemeCustomizations(
      borderRadius: (json['border_radius'] as num).toDouble(),
      elevation: (json['elevation'] as num).toDouble(),
      animationDuration: Duration(milliseconds: json['animation_duration_ms'] as int),
      spacing: (json['spacing'] as num).toDouble(),
      iconSize: (json['icon_size'] as num).toDouble(),
      buttonHeight: (json['button_height'] as num).toDouble(),
      enableAnimations: json['enable_animations'] as bool,
      enableHapticFeedback: json['enable_haptic_feedback'] as bool,
      enableSounds: json['enable_sounds'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'border_radius': borderRadius,
      'elevation': elevation,
      'animation_duration_ms': animationDuration.inMilliseconds,
      'spacing': spacing,
      'icon_size': iconSize,
      'button_height': buttonHeight,
      'enable_animations': enableAnimations,
      'enable_haptic_feedback': enableHapticFeedback,
      'enable_sounds': enableSounds,
    };
  }

  ThemeCustomizations copyWith({
    double? borderRadius,
    double? elevation,
    Duration? animationDuration,
    double? spacing,
    double? iconSize,
    double? buttonHeight,
    bool? enableAnimations,
    bool? enableHapticFeedback,
    bool? enableSounds,
  }) {
    return ThemeCustomizations(
      borderRadius: borderRadius ?? this.borderRadius,
      elevation: elevation ?? this.elevation,
      animationDuration: animationDuration ?? this.animationDuration,
      spacing: spacing ?? this.spacing,
      iconSize: iconSize ?? this.iconSize,
      buttonHeight: buttonHeight ?? this.buttonHeight,
      enableAnimations: enableAnimations ?? this.enableAnimations,
      enableHapticFeedback: enableHapticFeedback ?? this.enableHapticFeedback,
      enableSounds: enableSounds ?? this.enableSounds,
    );
  }
}

// Theme categories
enum ThemeCategory {
  dark,
  light,
  accessibility,
  colorful,
  minimal,
  professional,
  cultural,
  nature,
  custom,
}

// Theme preference model
class ThemePreferences {
  final String selectedThemeId;
  final bool followSystemTheme;
  final bool enableDynamicColors;
  final Map<String, dynamic> customizations;
  final DateTime lastModified;

  const ThemePreferences({
    required this.selectedThemeId,
    required this.followSystemTheme,
    required this.enableDynamicColors,
    required this.customizations,
    required this.lastModified,
  });

  factory ThemePreferences.fromJson(Map<String, dynamic> json) {
    return ThemePreferences(
      selectedThemeId: json['selected_theme_id'] as String,
      followSystemTheme: json['follow_system_theme'] as bool,
      enableDynamicColors: json['enable_dynamic_colors'] as bool,
      customizations: Map<String, dynamic>.from(json['customizations'] as Map),
      lastModified: DateTime.parse(json['last_modified'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'selected_theme_id': selectedThemeId,
      'follow_system_theme': followSystemTheme,
      'enable_dynamic_colors': enableDynamicColors,
      'customizations': customizations,
      'last_modified': lastModified.toIso8601String(),
    };
  }

  ThemePreferences copyWith({
    String? selectedThemeId,
    bool? followSystemTheme,
    bool? enableDynamicColors,
    Map<String, dynamic>? customizations,
  }) {
    return ThemePreferences(
      selectedThemeId: selectedThemeId ?? this.selectedThemeId,
      followSystemTheme: followSystemTheme ?? this.followSystemTheme,
      enableDynamicColors: enableDynamicColors ?? this.enableDynamicColors,
      customizations: customizations ?? this.customizations,
      lastModified: DateTime.now(),
    );
  }
}

// Color palette model for theme creation
class ColorPalette {
  final String name;
  final Color primary;
  final Color secondary;
  final Color accent;
  final Color background;
  final Color surface;
  final Color error;
  final List<Color> variants;

  const ColorPalette({
    required this.name,
    required this.primary,
    required this.secondary,
    required this.accent,
    required this.background,
    required this.surface,
    required this.error,
    required this.variants,
  });

  factory ColorPalette.fromJson(Map<String, dynamic> json) {
    return ColorPalette(
      name: json['name'] as String,
      primary: Color(json['primary'] as int),
      secondary: Color(json['secondary'] as int),
      accent: Color(json['accent'] as int),
      background: Color(json['background'] as int),
      surface: Color(json['surface'] as int),
      error: Color(json['error'] as int),
      variants: (json['variants'] as List<dynamic>)
          .map((e) => Color(e as int))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'primary': primary.toARGB32(),
      'secondary': secondary.toARGB32(),
      'accent': accent.toARGB32(),
      'background': background.toARGB32(),
      'surface': surface.toARGB32(),
      'error': error.toARGB32(),
      'variants': variants.map((e) => e.toARGB32()).toList(),
    };
  }
}

// Shadow Suite Theme Model (Alternative)
class ShadowSuiteThemeAlt {
  final String id;
  final String name;
  final String description;
  final ThemeCategory category;
  final ColorScheme colorScheme;
  final TextTheme typography;
  final ThemeCustomizations customizations;

  const ShadowSuiteThemeAlt({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.colorScheme,
    required this.typography,
    required this.customizations,
  });

  factory ShadowSuiteThemeAlt.fromJson(Map<String, dynamic> json) {
    return ShadowSuiteThemeAlt(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      category: ThemeCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => ThemeCategory.dark,
      ),
      colorScheme: _colorSchemeFromJson(json['color_scheme'] as Map<String, dynamic>),
      typography: _textThemeFromJson(json['typography'] as Map<String, dynamic>),
      customizations: ThemeCustomizations.fromJson(json['customizations'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category.name,
      'color_scheme': ShadowSuiteTheme._colorSchemeToJson(colorScheme),
      'typography': ShadowSuiteTheme._textThemeToJson(typography),
      'customizations': customizations.toJson(),
    };
  }

  // Helper methods for serialization
  static ColorScheme _colorSchemeFromJson(Map<String, dynamic> json) {
    return ColorScheme(
      brightness: json['brightness'] == 'dark' ? Brightness.dark : Brightness.light,
      primary: Color(json['primary'] as int),
      onPrimary: Color(json['on_primary'] as int),
      primaryContainer: Color(json['primary_container'] as int),
      onPrimaryContainer: Color(json['on_primary_container'] as int),
      secondary: Color(json['secondary'] as int),
      onSecondary: Color(json['on_secondary'] as int),
      secondaryContainer: Color(json['secondary_container'] as int),
      onSecondaryContainer: Color(json['on_secondary_container'] as int),
      tertiary: Color(json['tertiary'] as int),
      onTertiary: Color(json['on_tertiary'] as int),
      tertiaryContainer: Color(json['tertiary_container'] as int),
      onTertiaryContainer: Color(json['on_tertiary_container'] as int),
      error: Color(json['error'] as int),
      onError: Color(json['on_error'] as int),
      errorContainer: Color(json['error_container'] as int),
      onErrorContainer: Color(json['on_error_container'] as int),
      surface: Color(json['surface'] as int),
      onSurface: Color(json['on_surface'] as int),
      surfaceContainerHighest: Color(json['surface_variant'] as int),
      onSurfaceVariant: Color(json['on_surface_variant'] as int),
      outline: Color(json['outline'] as int),
      outlineVariant: Color(json['outline_variant'] as int),
      shadow: Color(json['shadow'] as int),
      scrim: Color(json['scrim'] as int),
      inverseSurface: Color(json['inverse_surface'] as int),
      onInverseSurface: Color(json['on_inverse_surface'] as int),
      inversePrimary: Color(json['inverse_primary'] as int),
    );
  }

  // static Map<String, dynamic> _colorSchemeToJson(ColorScheme colorScheme) { // Reserved for future use
  //   return {
  //     'brightness': colorScheme.brightness.name,
  //     'primary': colorScheme.primary.toARGB32(),
  //     'on_primary': colorScheme.onPrimary.toARGB32(),
  //     'primary_container': colorScheme.primaryContainer.toARGB32(),
  //     'on_primary_container': colorScheme.onPrimaryContainer.toARGB32(),
  //     'secondary': colorScheme.secondary.toARGB32(),
  //     'on_secondary': colorScheme.onSecondary.toARGB32(),
  //     'secondary_container': colorScheme.secondaryContainer.toARGB32(),
  //     'on_secondary_container': colorScheme.onSecondaryContainer.toARGB32(),
  //     'tertiary': colorScheme.tertiary.toARGB32(),
  //     'on_tertiary': colorScheme.onTertiary.toARGB32(),
  //     'tertiary_container': colorScheme.tertiaryContainer.toARGB32(),
  //     'on_tertiary_container': colorScheme.onTertiaryContainer.toARGB32(),
  //     'error': colorScheme.error.toARGB32(),
  //     'on_error': colorScheme.onError.toARGB32(),
  //     'error_container': colorScheme.errorContainer.toARGB32(),
  //     'on_error_container': colorScheme.onErrorContainer.toARGB32(),
  //     'surface': colorScheme.surface.toARGB32(),
  //     'on_surface': colorScheme.onSurface.toARGB32(),
  //     'surface_variant': colorScheme.surfaceContainerHighest.toARGB32(),
  //     'on_surface_variant': colorScheme.onSurfaceVariant.toARGB32(),
  //     'outline': colorScheme.outline.toARGB32(),
  //     'outline_variant': colorScheme.outlineVariant.toARGB32(),
  //     'shadow': colorScheme.shadow.toARGB32(),
  //     'scrim': colorScheme.scrim.toARGB32(),
  //     'inverse_surface': colorScheme.inverseSurface.toARGB32(),
  //     'on_inverse_surface': colorScheme.onInverseSurface.toARGB32(),
  //     'inverse_primary': colorScheme.inversePrimary.toARGB32(),
  //   };
  // }

  static TextTheme _textThemeFromJson(Map<String, dynamic> json) {
    return const TextTheme(); // Simplified for now
  }

  // static Map<String, dynamic> _textThemeToJson(TextTheme textTheme) { // Reserved for future use
  //   return {}; // Simplified for now
  // }
}

// Typography preset model
class TypographyPreset {
  final String name;
  final String fontFamily;
  final Map<String, TextStyle> styles;

  const TypographyPreset({
    required this.name,
    required this.fontFamily,
    required this.styles,
  });

  factory TypographyPreset.fromJson(Map<String, dynamic> json) {
    final stylesMap = <String, TextStyle>{};
    final stylesJson = json['styles'] as Map<String, dynamic>;
    
    for (final entry in stylesJson.entries) {
      final styleJson = entry.value as Map<String, dynamic>;
      stylesMap[entry.key] = TextStyle(
        fontSize: styleJson['font_size']?.toDouble(),
        fontWeight: styleJson['font_weight'] != null 
            ? FontWeight.values[styleJson['font_weight'] as int]
            : null,
        fontFamily: styleJson['font_family'] as String?,
        letterSpacing: styleJson['letter_spacing']?.toDouble(),
        height: styleJson['height']?.toDouble(),
      );
    }

    return TypographyPreset(
      name: json['name'] as String,
      fontFamily: json['font_family'] as String,
      styles: stylesMap,
    );
  }

  Map<String, dynamic> toJson() {
    final stylesJson = <String, dynamic>{};
    
    for (final entry in styles.entries) {
      stylesJson[entry.key] = {
        'font_size': entry.value.fontSize,
        'font_weight': entry.value.fontWeight?.index,
        'font_family': entry.value.fontFamily,
        'letter_spacing': entry.value.letterSpacing,
        'height': entry.value.height,
      };
    }

    return {
      'name': name,
      'font_family': fontFamily,
      'styles': stylesJson,
    };
  }
}
