import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'permissions_service.dart';
import 'error_handler.dart' as error_handler;

/// App-wide permissions manager that handles permission requests for all mini-apps
class AppPermissionsManager {
  static bool _isInitialized = false;
  static final Map<String, bool> _permissionCache = {};

  /// Initialize permissions manager on app startup
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await PermissionsService.initialize();
      await _refreshPermissionCache();
      _isInitialized = true;
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.platform,
        context: 'Initialize app permissions manager',
      );
    }
  }

  /// Check and request permissions before opening an app
  static Future<bool> ensureAppPermissions(
    BuildContext context,
    String appName,
  ) async {
    if (!Platform.isAndroid) return true;

    try {
      // Check if permissions are already granted (simplified)
      final hasPermissions = await PermissionsService.hasStoragePermissions();
      if (hasPermissions) {
        _permissionCache[appName] = true;
        return true;
      }

      // Show app-specific permission explanation
      if (!context.mounted) return false;
      final shouldRequest = await _showAppPermissionExplanation(
        context,
        appName,
      );
      if (!shouldRequest) return false;

      // Request permissions (simplified)
      if (!context.mounted) return false;
      final granted = await PermissionsService.requestStoragePermissions(
        context,
      );
      _permissionCache[appName] = granted;

      if (!granted && context.mounted) {
        await _showPermissionDeniedDialog(context, appName);
      }

      return granted;
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.permission,
        context: 'Ensure app permissions for $appName',
      );
      return false;
    }
  }

  /// Request storage permissions specifically (used by multiple apps)
  static Future<bool> ensureStoragePermissions(BuildContext context) async {
    if (!Platform.isAndroid) return true;

    try {
      return await PermissionsService.requestStoragePermissions(context);
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.permission,
        context: 'Ensure storage permissions',
      );
      return false;
    }
  }

  /// Request camera permissions for SmartGallery+
  static Future<bool> ensureCameraPermissions(BuildContext context) async {
    if (!Platform.isAndroid) return true;

    try {
      return await PermissionsService.requestCameraPermissions(context);
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.permission,
        context: 'Ensure camera permissions',
      );
      return false;
    }
  }

  /// Request microphone permissions for Memo Suite
  static Future<bool> ensureMicrophonePermissions(BuildContext context) async {
    if (!Platform.isAndroid) return true;

    try {
      return await PermissionsService.requestMicrophonePermissions(context);
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.permission,
        context: 'Ensure microphone permissions',
      );
      return false;
    }
  }

  /// Request location permissions for Islamic App
  static Future<bool> ensureLocationPermissions(BuildContext context) async {
    if (!Platform.isAndroid) return true;

    try {
      return await PermissionsService.requestLocationPermissions(context);
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.permission,
        context: 'Ensure location permissions',
      );
      return false;
    }
  }

  /// Get cached permission status for an app
  static bool? getCachedPermissionStatus(String appName) {
    return _permissionCache[appName];
  }

  /// Refresh permission cache
  static Future<void> refreshPermissions() async {
    await _refreshPermissionCache();
  }

  /// Show app-specific permission explanation
  static Future<bool> _showAppPermissionExplanation(
    BuildContext context,
    String appName,
  ) async {
    final appInfo = _getAppPermissionInfo(appName);

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(appInfo['icon'] as IconData, color: appInfo['color'] as Color),
            const SizedBox(width: 12),
            Text('${appInfo['name']} Permissions'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${appInfo['name']} needs the following permissions to work properly:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 16),
            ...((appInfo['permissions'] as List<String>).map(
              (permission) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(child: Text(permission)),
                  ],
                ),
              ),
            )),
            const SizedBox(height: 16),
            Text(
              appInfo['explanation'] as String,
              style: TextStyle(color: Colors.grey[600], fontSize: 14),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Not Now'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Grant Permissions'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// Show permission denied dialog with app-specific guidance
  static Future<void> _showPermissionDeniedDialog(
    BuildContext context,
    String appName,
  ) async {
    final appInfo = _getAppPermissionInfo(appName);

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.warning, color: Colors.orange),
            const SizedBox(width: 12),
            Text('${appInfo['name']} Limited'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Some permissions were denied. ${appInfo['name']} may not work properly without these permissions.',
            ),
            const SizedBox(height: 16),
            Text(
              'You can grant permissions later in Settings > Apps > Shadow Suite > Permissions.',
              style: TextStyle(color: Colors.grey[600], fontSize: 14),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Continue Anyway'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              PermissionsService.requestStoragePermissions(context);
            },
            child: const Text('Try Again'),
          ),
        ],
      ),
    );
  }

  /// Get app-specific permission information
  static Map<String, dynamic> _getAppPermissionInfo(String appName) {
    switch (appName.toLowerCase()) {
      case 'file_manager':
        return {
          'name': 'File Manager',
          'icon': Icons.folder,
          'color': Colors.blue,
          'permissions': [
            'Storage access to browse and manage files',
            'Network access for FTP and file sharing',
          ],
          'explanation':
              'File Manager needs storage access to browse your files and network access for sharing features.',
        };
      case 'smartgallery':
        return {
          'name': 'SmartGallery+',
          'icon': Icons.photo_library,
          'color': Colors.purple,
          'permissions': [
            'Photos and videos access for gallery features',
            'Camera access for taking photos',
            'Storage access for file management',
          ],
          'explanation':
              'SmartGallery+ needs media access to organize your photos and videos, plus camera access for taking new photos.',
        };
      case 'shadow_player':
        return {
          'name': 'Shadow Player',
          'icon': Icons.play_circle,
          'color': Colors.red,
          'permissions': [
            'Audio and video access for media playback',
            'Storage access for media files',
          ],
          'explanation':
              'Shadow Player needs access to your media files to provide music and video playback.',
        };
      case 'memo_suite':
        return {
          'name': 'Memo Suite',
          'icon': Icons.note,
          'color': Colors.orange,
          'permissions': [
            'Microphone access for voice recording',
            'Storage access for saving memos',
          ],
          'explanation':
              'Memo Suite needs microphone access for voice memos and storage access for saving your notes.',
        };
      case 'islamic_app':
        return {
          'name': 'Islamic App',
          'icon': Icons.mosque,
          'color': Colors.green,
          'permissions': [
            'Location access for prayer times and Qibla',
            'Notification access for prayer reminders',
          ],
          'explanation':
              'Islamic App needs location access for accurate prayer times and Qibla direction, plus notifications for reminders.',
        };
      default:
        return {
          'name': appName,
          'icon': Icons.apps,
          'color': Colors.grey,
          'permissions': ['Basic app permissions'],
          'explanation':
              'This app needs basic permissions to function properly.',
        };
    }
  }

  /// Refresh the permission cache
  static Future<void> _refreshPermissionCache() async {
    try {
      final apps = [
        'file_manager',
        'smartgallery',
        'shadow_player',
        'memo_suite',
        'islamic_app',
      ];

      for (final app in apps) {
        final hasPermissions = await PermissionsService.hasStoragePermissions();
        _permissionCache[app] = hasPermissions;
      }
    } catch (e) {
      // Continue with empty cache
    }
  }

  /// Get permission status for debugging
  static Future<Map<String, dynamic>> getDebugInfo() async {
    final permissionStatus = {
      'storage': await PermissionsService.hasStoragePermissions(),
      'camera': await PermissionsService.hasCameraPermissions(),
      'microphone': await PermissionsService.hasMicrophonePermissions(),
    };

    return {
      'initialized': _isInitialized,
      'platform': Platform.operatingSystem,
      'permissions': permissionStatus,
      'cache': _permissionCache,
    };
  }
}

/// Riverpod provider for permissions manager
final appPermissionsProvider = Provider<AppPermissionsManager>((ref) {
  return AppPermissionsManager();
});

/// Provider for checking if an app has required permissions
final appPermissionStatusProvider = FutureProvider.family<bool, String>((
  ref,
  appName,
) async {
  return await PermissionsService.hasStoragePermissions();
});
