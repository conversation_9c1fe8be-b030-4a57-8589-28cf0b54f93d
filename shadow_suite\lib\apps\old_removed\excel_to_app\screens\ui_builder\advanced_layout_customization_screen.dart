import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/advanced_layout_service.dart';
import '../../services/settings_persistence_service.dart';
import '../../widgets/settings_management_widget.dart';
import '../../widgets/function_testing_widget.dart';
import '../../widgets/preset_management_widget.dart';
import '../../widgets/quality_assurance_widget.dart';

/// Advanced layout customization screen with 100+ options and real-time preview
class AdvancedLayoutCustomizationScreen extends ConsumerStatefulWidget {
  const AdvancedLayoutCustomizationScreen({super.key});

  @override
  ConsumerState<AdvancedLayoutCustomizationScreen> createState() =>
      _AdvancedLayoutCustomizationScreenState();
}

class _AdvancedLayoutCustomizationScreenState
    extends ConsumerState<AdvancedLayoutCustomizationScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _showPreview = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 8, vsync: this);
    AdvancedLayoutService.initialize();
    SettingsPersistenceService.initialize();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final config = ref.watch(layoutConfigurationProvider);
    final theme = ref.watch(layoutThemeProvider);

    return Theme(
      data: theme,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Advanced Layout Customization'),
          actions: [
            IconButton(
              onPressed: _showPresetDialog,
              icon: const Icon(Icons.bookmark),
              tooltip: 'Presets',
            ),
            IconButton(
              onPressed: _showPresetManagement,
              icon: const Icon(Icons.library_books),
              tooltip: 'Template Library',
            ),
            IconButton(
              onPressed: _exportConfig,
              icon: const Icon(Icons.download),
              tooltip: 'Export',
            ),
            IconButton(
              onPressed: _importConfig,
              icon: const Icon(Icons.upload),
              tooltip: 'Import',
            ),
            IconButton(
              onPressed: () => setState(() => _showPreview = !_showPreview),
              icon: Icon(
                _showPreview ? Icons.visibility_off : Icons.visibility,
              ),
              tooltip: _showPreview ? 'Hide Preview' : 'Show Preview',
            ),
          ],
          bottom: TabBar(
            controller: _tabController,
            isScrollable: true,
            tabs: const [
              Tab(icon: Icon(Icons.navigation), text: 'Navigation'),
              Tab(icon: Icon(Icons.grid_view), text: 'Layout'),
              Tab(icon: Icon(Icons.palette), text: 'Colors'),
              Tab(icon: Icon(Icons.text_fields), text: 'Typography'),
              Tab(icon: Icon(Icons.space_bar), text: 'Spacing'),
              Tab(icon: Icon(Icons.animation), text: 'Animation'),
              Tab(icon: Icon(Icons.settings), text: 'Settings'),
              Tab(icon: Icon(Icons.science), text: 'Testing'),
            ],
          ),
        ),
        body: Row(
          children: [
            // Customization Panel
            Expanded(
              flex: _showPreview ? 1 : 2,
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildNavigationTab(config),
                  _buildLayoutTab(config),
                  _buildColorsTab(config),
                  _buildTypographyTab(config),
                  _buildSpacingTab(config),
                  _buildAnimationTab(config),
                  _buildSettingsTab(),
                  _buildTestingTab(),
                ],
              ),
            ),

            // Real-time Preview
            if (_showPreview)
              Expanded(
                flex: 1,
                child: Container(
                  decoration: BoxDecoration(
                    border: Border(left: BorderSide(color: Colors.grey[300]!)),
                  ),
                  child: _buildPreview(config, theme),
                ),
              ),
          ],
        ),
        floatingActionButton: FloatingActionButton.extended(
          onPressed: _resetToDefault,
          icon: const Icon(Icons.refresh),
          label: const Text('Reset'),
        ),
      ),
    );
  }

  Widget _buildNavigationTab(LayoutConfiguration config) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Navigation System'),
        _buildNavigationSystemSelector(config),
        const SizedBox(height: 24),

        if (config.navigationSystem == NavigationSystem.sidebar) ...[
          _buildSectionHeader('Sidebar Options'),
          _buildSidebarOptions(config),
          const SizedBox(height: 24),
        ],

        if (config.navigationSystem == NavigationSystem.bottomTabs) ...[
          _buildSectionHeader('Bottom Navigation'),
          _buildBottomNavOptions(config),
          const SizedBox(height: 24),
        ],

        if (config.navigationSystem == NavigationSystem.topTabs) ...[
          _buildSectionHeader('Top Navigation'),
          _buildTopNavOptions(config),
          const SizedBox(height: 24),
        ],

        _buildSectionHeader('Navigation Behavior'),
        _buildNavigationBehavior(config),
      ],
    );
  }

  Widget _buildLayoutTab(LayoutConfiguration config) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Grid Layout'),
        _buildGridLayoutOptions(config),
        const SizedBox(height: 24),

        _buildSectionHeader('Responsive Design'),
        _buildResponsiveOptions(config),
        const SizedBox(height: 24),

        _buildSectionHeader('Layout Behavior'),
        _buildLayoutBehavior(config),
      ],
    );
  }

  Widget _buildColorsTab(LayoutConfiguration config) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Color Scheme'),
        _buildColorSchemeSelector(config),
        const SizedBox(height: 24),

        if (config.colorScheme == AppColorScheme.custom) ...[
          _buildSectionHeader('Custom Colors'),
          _buildCustomColorPickers(config),
          const SizedBox(height: 24),
        ],

        _buildSectionHeader('Gradient Options'),
        _buildGradientOptions(config),
      ],
    );
  }

  Widget _buildTypographyTab(LayoutConfiguration config) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Font Settings'),
        _buildFontOptions(config),
        const SizedBox(height: 24),

        _buildSectionHeader('Text Styling'),
        _buildTextStyling(config),
      ],
    );
  }

  Widget _buildSpacingTab(LayoutConfiguration config) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Spacing Configuration'),
        _buildSpacingOptions(config),
        const SizedBox(height: 24),

        _buildSectionHeader('Component Sizing'),
        _buildComponentSizing(config),
        const SizedBox(height: 24),

        _buildSectionHeader('Border & Shadows'),
        _buildBorderShadowOptions(config),
      ],
    );
  }

  Widget _buildAnimationTab(LayoutConfiguration config) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Animation Settings'),
        _buildAnimationOptions(config),
        const SizedBox(height: 24),

        _buildSectionHeader('Transition Effects'),
        _buildTransitionOptions(config),
        const SizedBox(height: 24),

        _buildSectionHeader('Interactive Effects'),
        _buildInteractiveEffects(config),
      ],
    );
  }

  Widget _buildSettingsTab() {
    return const Padding(
      padding: EdgeInsets.all(16),
      child: SettingsManagementWidget(),
    );
  }

  Widget _buildTestingTab() {
    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          const TabBar(
            tabs: [
              Tab(icon: Icon(Icons.functions), text: 'Function Tests'),
              Tab(icon: Icon(Icons.verified), text: 'Quality Assurance'),
            ],
          ),
          const Expanded(
            child: TabBarView(
              children: [
                Padding(
                  padding: EdgeInsets.all(16),
                  child: FunctionTestingWidget(),
                ),
                Padding(
                  padding: EdgeInsets.all(16),
                  child: QualityAssuranceWidget(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreview(LayoutConfiguration config, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Live Preview', style: theme.textTheme.headlineSmall),
          const SizedBox(height: 16),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: _buildMockApp(config, theme),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMockApp(LayoutConfiguration config, ThemeData theme) {
    return Theme(
      data: theme,
      child: Scaffold(
        appBar: config.navigationSystem == NavigationSystem.topTabs
            ? AppBar(
                title: const Text('Preview App'),
                bottom: const TabBar(
                  tabs: [
                    Tab(text: 'Home'),
                    Tab(text: 'Data'),
                    Tab(text: 'Settings'),
                  ],
                ),
              )
            : AppBar(title: const Text('Preview App')),
        drawer: config.navigationSystem == NavigationSystem.sidebar
            ? Drawer(
                width: config.sidebarWidth,
                child: ListView(
                  children: [
                    const DrawerHeader(child: Text('Navigation')),
                    ListTile(
                      leading: const Icon(Icons.home),
                      title: const Text('Home'),
                      selected: true,
                    ),
                    ListTile(
                      leading: const Icon(Icons.data_usage),
                      title: const Text('Data'),
                    ),
                    ListTile(
                      leading: const Icon(Icons.settings),
                      title: const Text('Settings'),
                    ),
                  ],
                ),
              )
            : null,
        body: Padding(
          padding: EdgeInsets.all(config.componentPadding),
          child: GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: config.gridColumns,
              crossAxisSpacing: config.gridSpacing,
              mainAxisSpacing: config.gridSpacing,
              childAspectRatio: config.aspectRatio,
            ),
            itemCount: 6,
            itemBuilder: (context, index) {
              return Card(
                child: Padding(
                  padding: EdgeInsets.all(config.componentPadding),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.widgets,
                        size: 32,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Item ${index + 1}',
                        style: theme.textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
        bottomNavigationBar:
            config.navigationSystem == NavigationSystem.bottomTabs
            ? NavigationBar(
                destinations: const [
                  NavigationDestination(icon: Icon(Icons.home), label: 'Home'),
                  NavigationDestination(
                    icon: Icon(Icons.data_usage),
                    label: 'Data',
                  ),
                  NavigationDestination(
                    icon: Icon(Icons.settings),
                    label: 'Settings',
                  ),
                ],
              )
            : null,
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
    );
  }

  // Helper methods for building specific option sections
  void _updateConfig(
    LayoutConfiguration Function(LayoutConfiguration) updater,
  ) {
    final currentConfig = ref.read(layoutConfigurationProvider);
    final newConfig = updater(currentConfig);
    ref.read(layoutConfigurationProvider.notifier).state = newConfig;
    AdvancedLayoutService.updateConfig(newConfig);

    // Auto-save if enabled
    if (SettingsPersistenceService.isAutoSaveEnabled) {
      SettingsPersistenceService.saveLayoutConfiguration(newConfig);
    }
  }

  void _showPresetDialog() {
    // Implementation for preset dialog
  }

  void _exportConfig() {
    // Implementation for export functionality
  }

  void _importConfig() {
    // Implementation for import functionality
  }

  void _resetToDefault() {
    AdvancedLayoutService.resetToDefault();
    ref.read(layoutConfigurationProvider.notifier).state =
        AdvancedLayoutService.currentConfig;
  }

  // Navigation System Options
  Widget _buildNavigationSystemSelector(LayoutConfiguration config) {
    return Column(
      children: [
        DropdownButtonFormField<NavigationSystem>(
          value: config.navigationSystem,
          decoration: const InputDecoration(
            labelText: 'Navigation Type',
            border: OutlineInputBorder(),
          ),
          items: NavigationSystem.values.map((system) {
            return DropdownMenuItem(
              value: system,
              child: Text(_getNavigationSystemName(system)),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              _updateConfig(
                (config) => config.copyWith(navigationSystem: value),
              );
            }
          },
        ),
      ],
    );
  }

  Widget _buildSidebarOptions(LayoutConfiguration config) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: RadioListTile<SidebarPosition>(
                title: const Text('Left'),
                value: SidebarPosition.left,
                groupValue: config.sidebarPosition,
                onChanged: (value) {
                  _updateConfig(
                    (config) => config.copyWith(sidebarPosition: value),
                  );
                },
              ),
            ),
            Expanded(
              child: RadioListTile<SidebarPosition>(
                title: const Text('Right'),
                value: SidebarPosition.right,
                groupValue: config.sidebarPosition,
                onChanged: (value) {
                  _updateConfig(
                    (config) => config.copyWith(sidebarPosition: value),
                  );
                },
              ),
            ),
          ],
        ),
        SwitchListTile(
          title: const Text('Collapsible'),
          value: config.sidebarCollapsible,
          onChanged: (value) {
            _updateConfig(
              (config) => config.copyWith(sidebarCollapsible: value),
            );
          },
        ),
        SwitchListTile(
          title: const Text('Floating'),
          value: config.sidebarFloating,
          onChanged: (value) {
            _updateConfig((config) => config.copyWith(sidebarFloating: value));
          },
        ),
        ListTile(
          title: const Text('Width'),
          subtitle: Slider(
            value: config.sidebarWidth,
            min: 200,
            max: 400,
            divisions: 20,
            label: '${config.sidebarWidth.round()}px',
            onChanged: (value) {
              _updateConfig((config) => config.copyWith(sidebarWidth: value));
            },
          ),
        ),
      ],
    );
  }

  Widget _buildBottomNavOptions(LayoutConfiguration config) {
    return Column(
      children: [
        DropdownButtonFormField<BottomNavStyle>(
          value: config.bottomNavStyle,
          decoration: const InputDecoration(
            labelText: 'Bottom Nav Style',
            border: OutlineInputBorder(),
          ),
          items: BottomNavStyle.values.map((style) {
            return DropdownMenuItem(
              value: style,
              child: Text(_getBottomNavStyleName(style)),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              _updateConfig((config) => config.copyWith(bottomNavStyle: value));
            }
          },
        ),
        const SizedBox(height: 16),
        SwitchListTile(
          title: const Text('Floating'),
          value: config.bottomNavFloating,
          onChanged: (value) {
            _updateConfig(
              (config) => config.copyWith(bottomNavFloating: value),
            );
          },
        ),
      ],
    );
  }

  Widget _buildTopNavOptions(LayoutConfiguration config) {
    return DropdownButtonFormField<TopNavStyle>(
      value: config.topNavStyle,
      decoration: const InputDecoration(
        labelText: 'Top Nav Style',
        border: OutlineInputBorder(),
      ),
      items: TopNavStyle.values.map((style) {
        return DropdownMenuItem(
          value: style,
          child: Text(_getTopNavStyleName(style)),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          _updateConfig((config) => config.copyWith(topNavStyle: value));
        }
      },
    );
  }

  Widget _buildNavigationBehavior(LayoutConfiguration config) {
    return Column(
      children: [
        SwitchListTile(
          title: const Text('Show Breadcrumbs'),
          value: config.showBreadcrumbs,
          onChanged: (value) {
            _updateConfig((config) => config.copyWith(showBreadcrumbs: value));
          },
        ),
        SwitchListTile(
          title: const Text('Tree Navigation'),
          value: config.treeNavigation,
          onChanged: (value) {
            _updateConfig((config) => config.copyWith(treeNavigation: value));
          },
        ),
        SwitchListTile(
          title: const Text('Auto-hide Navigation'),
          value: config.autoHideNavigation,
          onChanged: (value) {
            _updateConfig(
              (config) => config.copyWith(autoHideNavigation: value),
            );
          },
        ),
      ],
    );
  }

  // Grid Layout Options
  Widget _buildGridLayoutOptions(LayoutConfiguration config) {
    return Column(
      children: [
        DropdownButtonFormField<GridLayoutType>(
          value: config.gridLayout,
          decoration: const InputDecoration(
            labelText: 'Grid Type',
            border: OutlineInputBorder(),
          ),
          items: GridLayoutType.values.map((type) {
            return DropdownMenuItem(
              value: type,
              child: Text(_getGridLayoutTypeName(type)),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              _updateConfig((config) => config.copyWith(gridLayout: value));
            }
          },
        ),
        const SizedBox(height: 16),
        ListTile(
          title: const Text('Grid Columns'),
          subtitle: Slider(
            value: config.gridColumns.toDouble(),
            min: 1,
            max: 6,
            divisions: 5,
            label: '${config.gridColumns}',
            onChanged: (value) {
              _updateConfig(
                (config) => config.copyWith(gridColumns: value.round()),
              );
            },
          ),
        ),
        ListTile(
          title: const Text('Grid Spacing'),
          subtitle: Slider(
            value: config.gridSpacing,
            min: 4,
            max: 32,
            divisions: 14,
            label: '${config.gridSpacing.round()}px',
            onChanged: (value) {
              _updateConfig((config) => config.copyWith(gridSpacing: value));
            },
          ),
        ),
        ListTile(
          title: const Text('Aspect Ratio'),
          subtitle: Slider(
            value: config.aspectRatio,
            min: 0.5,
            max: 2.0,
            divisions: 15,
            label: config.aspectRatio.toStringAsFixed(1),
            onChanged: (value) {
              _updateConfig((config) => config.copyWith(aspectRatio: value));
            },
          ),
        ),
      ],
    );
  }

  Widget _buildResponsiveOptions(LayoutConfiguration config) {
    return Column(
      children: [
        SwitchListTile(
          title: const Text('Responsive Grid'),
          value: config.responsiveGrid,
          onChanged: (value) {
            _updateConfig((config) => config.copyWith(responsiveGrid: value));
          },
        ),
        SwitchListTile(
          title: const Text('Responsive Scaling'),
          value: config.responsiveScaling,
          onChanged: (value) {
            _updateConfig(
              (config) => config.copyWith(responsiveScaling: value),
            );
          },
        ),
        if (config.responsiveScaling)
          ListTile(
            title: const Text('Scale Factor'),
            subtitle: Slider(
              value: config.scaleFactor,
              min: 0.8,
              max: 1.5,
              divisions: 14,
              label: config.scaleFactor.toStringAsFixed(1),
              onChanged: (value) {
                _updateConfig((config) => config.copyWith(scaleFactor: value));
              },
            ),
          ),
      ],
    );
  }

  Widget _buildLayoutBehavior(LayoutConfiguration config) {
    return Column(
      children: [
        SwitchListTile(
          title: const Text('Sticky Header'),
          value: config.stickyHeader,
          onChanged: (value) {
            _updateConfig((config) => config.copyWith(stickyHeader: value));
          },
        ),
        SwitchListTile(
          title: const Text('Infinite Scroll'),
          value: config.infiniteScroll,
          onChanged: (value) {
            _updateConfig((config) => config.copyWith(infiniteScroll: value));
          },
        ),
        SwitchListTile(
          title: const Text('Lazy Loading'),
          value: config.lazyLoading,
          onChanged: (value) {
            _updateConfig((config) => config.copyWith(lazyLoading: value));
          },
        ),
        SwitchListTile(
          title: const Text('Swipe Gestures'),
          value: config.enableSwipeGestures,
          onChanged: (value) {
            _updateConfig(
              (config) => config.copyWith(enableSwipeGestures: value),
            );
          },
        ),
      ],
    );
  }

  // Helper methods for enum names
  String _getNavigationSystemName(NavigationSystem system) {
    switch (system) {
      case NavigationSystem.sidebar:
        return 'Sidebar';
      case NavigationSystem.topTabs:
        return 'Top Tabs';
      case NavigationSystem.bottomTabs:
        return 'Bottom Tabs';
      case NavigationSystem.hamburger:
        return 'Hamburger Menu';
      case NavigationSystem.breadcrumbs:
        return 'Breadcrumbs';
      case NavigationSystem.tree:
        return 'Tree Navigation';
    }
  }

  String _getBottomNavStyleName(BottomNavStyle style) {
    switch (style) {
      case BottomNavStyle.tabs:
        return 'Tabs';
      case BottomNavStyle.floating:
        return 'Floating';
      case BottomNavStyle.dock:
        return 'Dock';
      case BottomNavStyle.custom:
        return 'Custom';
    }
  }

  String _getTopNavStyleName(TopNavStyle style) {
    switch (style) {
      case TopNavStyle.tabs:
        return 'Tabs';
      case TopNavStyle.pills:
        return 'Pills';
      case TopNavStyle.underline:
        return 'Underline';
      case TopNavStyle.custom:
        return 'Custom';
    }
  }

  String _getGridLayoutTypeName(GridLayoutType type) {
    switch (type) {
      case GridLayoutType.responsive:
        return 'Responsive';
      case GridLayoutType.masonry:
        return 'Masonry';
      case GridLayoutType.staggered:
        return 'Staggered';
      case GridLayoutType.grid:
        return 'Grid';
      case GridLayoutType.custom:
        return 'Custom';
    }
  }

  // Color Options
  Widget _buildColorSchemeSelector(LayoutConfiguration config) {
    return DropdownButtonFormField<AppColorScheme>(
      value: config.colorScheme,
      decoration: const InputDecoration(
        labelText: 'Color Scheme',
        border: OutlineInputBorder(),
      ),
      items: AppColorScheme.values.map((scheme) {
        return DropdownMenuItem(
          value: scheme,
          child: Text(_getColorSchemeName(scheme)),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          _updateConfig((config) => config.copyWith(colorScheme: value));
        }
      },
    );
  }

  Widget _buildCustomColorPickers(LayoutConfiguration config) {
    return Column(
      children: [
        _buildColorPicker('Primary Color', config.primaryColor, (color) {
          _updateConfig((config) => config.copyWith(primaryColor: color));
        }),
        _buildColorPicker('Secondary Color', config.secondaryColor, (color) {
          _updateConfig((config) => config.copyWith(secondaryColor: color));
        }),
        _buildColorPicker('Accent Color', config.accentColor, (color) {
          _updateConfig((config) => config.copyWith(accentColor: color));
        }),
        _buildColorPicker('Background Color', config.backgroundColor, (color) {
          _updateConfig((config) => config.copyWith(backgroundColor: color));
        }),
        _buildColorPicker('Surface Color', config.surfaceColor, (color) {
          _updateConfig((config) => config.copyWith(surfaceColor: color));
        }),
      ],
    );
  }

  Widget _buildColorPicker(
    String label,
    Color? color,
    Function(Color) onChanged,
  ) {
    return ListTile(
      title: Text(label),
      trailing: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: color ?? Colors.grey,
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      onTap: () {
        // In a real implementation, this would open a color picker dialog
        // For now, we'll cycle through some preset colors
        final colors = [
          Colors.blue,
          Colors.red,
          Colors.green,
          Colors.orange,
          Colors.purple,
          Colors.teal,
          Colors.pink,
          Colors.indigo,
        ];
        final currentIndex = color != null
            ? colors.indexWhere((c) => c == color)
            : -1;
        final nextIndex = (currentIndex + 1) % colors.length;
        onChanged(colors[nextIndex]);
      },
    );
  }

  Widget _buildGradientOptions(LayoutConfiguration config) {
    return Column(
      children: [
        SwitchListTile(
          title: const Text('Use Gradients'),
          value: config.useGradients,
          onChanged: (value) {
            _updateConfig((config) => config.copyWith(useGradients: value));
          },
        ),
        if (config.useGradients) ...[
          const SizedBox(height: 16),
          const Text('Gradient Colors'),
          const SizedBox(height: 8),
          // Simplified gradient color selection
          Wrap(
            spacing: 8,
            children: [
              _buildGradientColorChip(Colors.blue, Colors.purple),
              _buildGradientColorChip(Colors.red, Colors.orange),
              _buildGradientColorChip(Colors.green, Colors.teal),
              _buildGradientColorChip(Colors.pink, Colors.purple),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildGradientColorChip(Color color1, Color color2) {
    return GestureDetector(
      onTap: () {
        _updateConfig(
          (config) => config.copyWith(gradientColors: [color1, color2]),
        );
      },
      child: Container(
        width: 60,
        height: 30,
        decoration: BoxDecoration(
          gradient: LinearGradient(colors: [color1, color2]),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: Colors.grey),
        ),
      ),
    );
  }

  // Typography Options
  Widget _buildFontOptions(LayoutConfiguration config) {
    return Column(
      children: [
        DropdownButtonFormField<String>(
          value: config.fontFamily,
          decoration: const InputDecoration(
            labelText: 'Font Family',
            border: OutlineInputBorder(),
          ),
          items: ['Roboto', 'Arial', 'Helvetica', 'Times New Roman', 'Georgia']
              .map((font) => DropdownMenuItem(value: font, child: Text(font)))
              .toList(),
          onChanged: (value) {
            if (value != null) {
              _updateConfig((config) => config.copyWith(fontFamily: value));
            }
          },
        ),
        const SizedBox(height: 16),
        ListTile(
          title: const Text('Base Font Size'),
          subtitle: Slider(
            value: config.baseFontSize,
            min: 10,
            max: 24,
            divisions: 14,
            label: '${config.baseFontSize.round()}px',
            onChanged: (value) {
              _updateConfig((config) => config.copyWith(baseFontSize: value));
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTextStyling(LayoutConfiguration config) {
    return Column(
      children: [
        DropdownButtonFormField<FontWeight>(
          value: config.fontWeight,
          decoration: const InputDecoration(
            labelText: 'Font Weight',
            border: OutlineInputBorder(),
          ),
          items:
              [
                    FontWeight.w300,
                    FontWeight.w400,
                    FontWeight.w500,
                    FontWeight.w600,
                    FontWeight.w700,
                  ]
                  .map(
                    (weight) => DropdownMenuItem(
                      value: weight,
                      child: Text(_getFontWeightName(weight)),
                    ),
                  )
                  .toList(),
          onChanged: (value) {
            if (value != null) {
              _updateConfig((config) => config.copyWith(fontWeight: value));
            }
          },
        ),
        const SizedBox(height: 16),
        ListTile(
          title: const Text('Line Height'),
          subtitle: Slider(
            value: config.lineHeight,
            min: 1.0,
            max: 2.0,
            divisions: 10,
            label: config.lineHeight.toStringAsFixed(1),
            onChanged: (value) {
              _updateConfig((config) => config.copyWith(lineHeight: value));
            },
          ),
        ),
        ListTile(
          title: const Text('Letter Spacing'),
          subtitle: Slider(
            value: config.letterSpacing,
            min: -1.0,
            max: 2.0,
            divisions: 30,
            label: config.letterSpacing.toStringAsFixed(1),
            onChanged: (value) {
              _updateConfig((config) => config.copyWith(letterSpacing: value));
            },
          ),
        ),
      ],
    );
  }

  String _getColorSchemeName(AppColorScheme scheme) {
    switch (scheme) {
      case AppColorScheme.light:
        return 'Light';
      case AppColorScheme.dark:
        return 'Dark';
      case AppColorScheme.custom:
        return 'Custom';
      case AppColorScheme.auto:
        return 'Auto';
    }
  }

  String _getFontWeightName(FontWeight weight) {
    switch (weight) {
      case FontWeight.w300:
        return 'Light';
      case FontWeight.w400:
        return 'Normal';
      case FontWeight.w500:
        return 'Medium';
      case FontWeight.w600:
        return 'Semi Bold';
      case FontWeight.w700:
        return 'Bold';
      default:
        return 'Normal';
    }
  }

  // Spacing Options
  Widget _buildSpacingOptions(LayoutConfiguration config) {
    return Column(
      children: [
        DropdownButtonFormField<SpacingConfig>(
          value: config.spacing,
          decoration: const InputDecoration(
            labelText: 'Spacing Configuration',
            border: OutlineInputBorder(),
          ),
          items: SpacingConfig.values.map((spacing) {
            return DropdownMenuItem(
              value: spacing,
              child: Text(_getSpacingConfigName(spacing)),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              _updateConfig((config) => config.copyWith(spacing: value));
            }
          },
        ),
      ],
    );
  }

  Widget _buildComponentSizing(LayoutConfiguration config) {
    return Column(
      children: [
        ListTile(
          title: const Text('Component Padding'),
          subtitle: Slider(
            value: config.componentPadding,
            min: 4,
            max: 32,
            divisions: 14,
            label: '${config.componentPadding.round()}px',
            onChanged: (value) {
              _updateConfig(
                (config) => config.copyWith(componentPadding: value),
              );
            },
          ),
        ),
        ListTile(
          title: const Text('Component Margin'),
          subtitle: Slider(
            value: config.componentMargin,
            min: 0,
            max: 24,
            divisions: 12,
            label: '${config.componentMargin.round()}px',
            onChanged: (value) {
              _updateConfig(
                (config) => config.copyWith(componentMargin: value),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildBorderShadowOptions(LayoutConfiguration config) {
    return Column(
      children: [
        ListTile(
          title: const Text('Border Radius'),
          subtitle: Slider(
            value: config.borderRadius,
            min: 0,
            max: 24,
            divisions: 12,
            label: '${config.borderRadius.round()}px',
            onChanged: (value) {
              _updateConfig((config) => config.copyWith(borderRadius: value));
            },
          ),
        ),
        SwitchListTile(
          title: const Text('Show Shadows'),
          value: config.showShadows,
          onChanged: (value) {
            _updateConfig((config) => config.copyWith(showShadows: value));
          },
        ),
        if (config.showShadows)
          ListTile(
            title: const Text('Shadow Intensity'),
            subtitle: Slider(
              value: config.shadowIntensity,
              min: 0.0,
              max: 1.0,
              divisions: 10,
              label: config.shadowIntensity.toStringAsFixed(1),
              onChanged: (value) {
                _updateConfig(
                  (config) => config.copyWith(shadowIntensity: value),
                );
              },
            ),
          ),
      ],
    );
  }

  // Animation Options
  Widget _buildAnimationOptions(LayoutConfiguration config) {
    return Column(
      children: [
        DropdownButtonFormField<AnimationSpeed>(
          value: config.animationSpeed,
          decoration: const InputDecoration(
            labelText: 'Animation Speed',
            border: OutlineInputBorder(),
          ),
          items: AnimationSpeed.values.map((speed) {
            return DropdownMenuItem(
              value: speed,
              child: Text(_getAnimationSpeedName(speed)),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              _updateConfig((config) => config.copyWith(animationSpeed: value));
            }
          },
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<AnimationCurve>(
          value: config.animationCurve,
          decoration: const InputDecoration(
            labelText: 'Animation Curve',
            border: OutlineInputBorder(),
          ),
          items: AnimationCurve.values.map((curve) {
            return DropdownMenuItem(
              value: curve,
              child: Text(_getAnimationCurveName(curve)),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              _updateConfig((config) => config.copyWith(animationCurve: value));
            }
          },
        ),
      ],
    );
  }

  Widget _buildTransitionOptions(LayoutConfiguration config) {
    return Column(
      children: [
        SwitchListTile(
          title: const Text('Enable Transitions'),
          value: config.enableTransitions,
          onChanged: (value) {
            _updateConfig(
              (config) => config.copyWith(enableTransitions: value),
            );
          },
        ),
        if (config.enableTransitions)
          ListTile(
            title: const Text('Transition Duration'),
            subtitle: Slider(
              value: config.transitionDuration.inMilliseconds.toDouble(),
              min: 100,
              max: 1000,
              divisions: 18,
              label: '${config.transitionDuration.inMilliseconds}ms',
              onChanged: (value) {
                _updateConfig(
                  (config) => config.copyWith(
                    transitionDuration: Duration(milliseconds: value.round()),
                  ),
                );
              },
            ),
          ),
      ],
    );
  }

  Widget _buildInteractiveEffects(LayoutConfiguration config) {
    return Column(
      children: [
        SwitchListTile(
          title: const Text('Hover Effects'),
          value: config.enableHoverEffects,
          onChanged: (value) {
            _updateConfig(
              (config) => config.copyWith(enableHoverEffects: value),
            );
          },
        ),
      ],
    );
  }

  String _getSpacingConfigName(SpacingConfig spacing) {
    switch (spacing) {
      case SpacingConfig.compact:
        return 'Compact';
      case SpacingConfig.normal:
        return 'Normal';
      case SpacingConfig.comfortable:
        return 'Comfortable';
      case SpacingConfig.spacious:
        return 'Spacious';
    }
  }

  String _getAnimationSpeedName(AnimationSpeed speed) {
    switch (speed) {
      case AnimationSpeed.slow:
        return 'Slow';
      case AnimationSpeed.medium:
        return 'Medium';
      case AnimationSpeed.fast:
        return 'Fast';
      case AnimationSpeed.instant:
        return 'Instant';
    }
  }

  String _getAnimationCurveName(AnimationCurve curve) {
    switch (curve) {
      case AnimationCurve.easeInOut:
        return 'Ease In Out';
      case AnimationCurve.easeIn:
        return 'Ease In';
      case AnimationCurve.easeOut:
        return 'Ease Out';
      case AnimationCurve.linear:
        return 'Linear';
      case AnimationCurve.bounce:
        return 'Bounce';
      case AnimationCurve.elastic:
        return 'Elastic';
    }
  }

  void _showPresetManagement() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: SizedBox(
          width: MediaQuery.of(context).size.width * 0.8,
          height: MediaQuery.of(context).size.height * 0.8,
          child: const PresetManagementWidget(),
        ),
      ),
    );
  }
}
