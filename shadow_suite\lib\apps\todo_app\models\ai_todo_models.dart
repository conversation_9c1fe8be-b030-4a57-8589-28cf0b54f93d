import 'package:flutter/material.dart';
import 'task_models.dart';

/// Productivity insight generated by AI analysis
class ProductivityInsight {
  final String id;
  final InsightType type;
  final String title;
  final String description;
  final ImpactLevel impact;
  final double confidence;
  final List<String> recommendations;
  final DateTime createdAt;
  final Map<String, dynamic>? metadata;

  const ProductivityInsight({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.impact,
    required this.confidence,
    required this.recommendations,
    required this.createdAt,
    this.metadata,
  });

  ProductivityInsight copyWith({
    String? id,
    InsightType? type,
    String? title,
    String? description,
    ImpactLevel? impact,
    double? confidence,
    List<String>? recommendations,
    DateTime? createdAt,
    Map<String, dynamic>? metadata,
  }) {
    return ProductivityInsight(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      impact: impact ?? this.impact,
      confidence: confidence ?? this.confidence,
      recommendations: recommendations ?? this.recommendations,
      createdAt: createdAt ?? this.createdAt,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'impact': impact.name,
      'confidence': confidence,
      'recommendations': recommendations,
      'createdAt': createdAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  factory ProductivityInsight.fromJson(Map<String, dynamic> json) {
    return ProductivityInsight(
      id: json['id'],
      type: InsightType.values.firstWhere((e) => e.name == json['type']),
      title: json['title'],
      description: json['description'],
      impact: ImpactLevel.values.firstWhere((e) => e.name == json['impact']),
      confidence: json['confidence'].toDouble(),
      recommendations: List<String>.from(json['recommendations']),
      createdAt: DateTime.parse(json['createdAt']),
      metadata: json['metadata'],
    );
  }
}

/// Types of productivity insights
enum InsightType { productivity, warning, optimization, achievement, pattern }

/// Impact level of insights
enum ImpactLevel { low, medium, high, critical }

/// Task recommendation from AI
class TaskRecommendation {
  final String id;
  final RecommendationType type;
  final String title;
  final String description;
  final List<String> suggestedTasks;
  final Duration estimatedDuration;
  final double confidence;
  final String reasoning;
  final DateTime createdAt;

  const TaskRecommendation({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.suggestedTasks,
    required this.estimatedDuration,
    required this.confidence,
    required this.reasoning,
    required this.createdAt,
  });

  TaskRecommendation copyWith({
    String? id,
    RecommendationType? type,
    String? title,
    String? description,
    List<String>? suggestedTasks,
    Duration? estimatedDuration,
    double? confidence,
    String? reasoning,
    DateTime? createdAt,
  }) {
    return TaskRecommendation(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      suggestedTasks: suggestedTasks ?? this.suggestedTasks,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      confidence: confidence ?? this.confidence,
      reasoning: reasoning ?? this.reasoning,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'suggestedTasks': suggestedTasks,
      'estimatedDuration': estimatedDuration.inMinutes,
      'confidence': confidence,
      'reasoning': reasoning,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory TaskRecommendation.fromJson(Map<String, dynamic> json) {
    return TaskRecommendation(
      id: json['id'],
      type: RecommendationType.values.firstWhere((e) => e.name == json['type']),
      title: json['title'],
      description: json['description'],
      suggestedTasks: List<String>.from(json['suggestedTasks']),
      estimatedDuration: Duration(minutes: json['estimatedDuration']),
      confidence: json['confidence'].toDouble(),
      reasoning: json['reasoning'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Types of task recommendations
enum RecommendationType {
  timeOptimal,
  energyOptimal,
  contextOptimal,
  urgency,
  productivity,
}

/// User preferences for AI recommendations
class UserPreferences {
  final String currentLocation;
  final EnergyLevel energyLevel;
  final List<int> preferredWorkingHours;
  final Map<String, double> categoryPreferences;
  final bool enableSmartNotifications;
  final int focusSessionDuration;
  final int breakDuration;
  final bool enablePomodoroMode;

  const UserPreferences({
    required this.currentLocation,
    required this.energyLevel,
    required this.preferredWorkingHours,
    required this.categoryPreferences,
    required this.enableSmartNotifications,
    required this.focusSessionDuration,
    required this.breakDuration,
    required this.enablePomodoroMode,
  });

  UserPreferences copyWith({
    String? currentLocation,
    EnergyLevel? energyLevel,
    List<int>? preferredWorkingHours,
    Map<String, double>? categoryPreferences,
    bool? enableSmartNotifications,
    int? focusSessionDuration,
    int? breakDuration,
    bool? enablePomodoroMode,
  }) {
    return UserPreferences(
      currentLocation: currentLocation ?? this.currentLocation,
      energyLevel: energyLevel ?? this.energyLevel,
      preferredWorkingHours:
          preferredWorkingHours ?? this.preferredWorkingHours,
      categoryPreferences: categoryPreferences ?? this.categoryPreferences,
      enableSmartNotifications:
          enableSmartNotifications ?? this.enableSmartNotifications,
      focusSessionDuration: focusSessionDuration ?? this.focusSessionDuration,
      breakDuration: breakDuration ?? this.breakDuration,
      enablePomodoroMode: enablePomodoroMode ?? this.enablePomodoroMode,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'currentLocation': currentLocation,
      'energyLevel': energyLevel.name,
      'preferredWorkingHours': preferredWorkingHours,
      'categoryPreferences': categoryPreferences,
      'enableSmartNotifications': enableSmartNotifications,
      'focusSessionDuration': focusSessionDuration,
      'breakDuration': breakDuration,
      'enablePomodoroMode': enablePomodoroMode,
    };
  }

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      currentLocation: json['currentLocation'],
      energyLevel: EnergyLevel.values.firstWhere(
        (e) => e.name == json['energyLevel'],
      ),
      preferredWorkingHours: List<int>.from(json['preferredWorkingHours']),
      categoryPreferences: Map<String, double>.from(
        json['categoryPreferences'],
      ),
      enableSmartNotifications: json['enableSmartNotifications'],
      focusSessionDuration: json['focusSessionDuration'],
      breakDuration: json['breakDuration'],
      enablePomodoroMode: json['enablePomodoroMode'],
    );
  }
}

/// Energy levels for recommendations
enum EnergyLevel { low, medium, high }

/// Comprehensive productivity analytics
class ProductivityAnalytics {
  final String id;
  final Duration period;
  final int totalTasks;
  final int completedTasks;
  final double completionRate;
  final Duration averageTaskDuration;
  final double productivityScore;
  final Duration focusTime;
  final int distractionEvents;
  final List<int> peakProductivityHours;
  final Map<String, int> categoryBreakdown;
  final Map<TaskPriority, int> priorityDistribution;
  final StreakData streakData;
  final List<String> improvementAreas;
  final List<String> achievements;
  final DateTime generatedAt;

  const ProductivityAnalytics({
    required this.id,
    required this.period,
    required this.totalTasks,
    required this.completedTasks,
    required this.completionRate,
    required this.averageTaskDuration,
    required this.productivityScore,
    required this.focusTime,
    required this.distractionEvents,
    required this.peakProductivityHours,
    required this.categoryBreakdown,
    required this.priorityDistribution,
    required this.streakData,
    required this.improvementAreas,
    required this.achievements,
    required this.generatedAt,
  });

  ProductivityAnalytics copyWith({
    String? id,
    Duration? period,
    int? totalTasks,
    int? completedTasks,
    double? completionRate,
    Duration? averageTaskDuration,
    double? productivityScore,
    Duration? focusTime,
    int? distractionEvents,
    List<int>? peakProductivityHours,
    Map<String, int>? categoryBreakdown,
    Map<TaskPriority, int>? priorityDistribution,
    StreakData? streakData,
    List<String>? improvementAreas,
    List<String>? achievements,
    DateTime? generatedAt,
  }) {
    return ProductivityAnalytics(
      id: id ?? this.id,
      period: period ?? this.period,
      totalTasks: totalTasks ?? this.totalTasks,
      completedTasks: completedTasks ?? this.completedTasks,
      completionRate: completionRate ?? this.completionRate,
      averageTaskDuration: averageTaskDuration ?? this.averageTaskDuration,
      productivityScore: productivityScore ?? this.productivityScore,
      focusTime: focusTime ?? this.focusTime,
      distractionEvents: distractionEvents ?? this.distractionEvents,
      peakProductivityHours:
          peakProductivityHours ?? this.peakProductivityHours,
      categoryBreakdown: categoryBreakdown ?? this.categoryBreakdown,
      priorityDistribution: priorityDistribution ?? this.priorityDistribution,
      streakData: streakData ?? this.streakData,
      improvementAreas: improvementAreas ?? this.improvementAreas,
      achievements: achievements ?? this.achievements,
      generatedAt: generatedAt ?? this.generatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'period': period.inDays,
      'totalTasks': totalTasks,
      'completedTasks': completedTasks,
      'completionRate': completionRate,
      'averageTaskDuration': averageTaskDuration.inMinutes,
      'productivityScore': productivityScore,
      'focusTime': focusTime.inMinutes,
      'distractionEvents': distractionEvents,
      'peakProductivityHours': peakProductivityHours,
      'categoryBreakdown': categoryBreakdown,
      'priorityDistribution': priorityDistribution.map(
        (k, v) => MapEntry(k.name, v),
      ),
      'streakData': streakData.toJson(),
      'improvementAreas': improvementAreas,
      'achievements': achievements,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }

  factory ProductivityAnalytics.fromJson(Map<String, dynamic> json) {
    final priorityDistributionMap = Map<String, dynamic>.from(
      json['priorityDistribution'],
    );
    final priorityDistribution = <TaskPriority, int>{};

    priorityDistributionMap.forEach((key, value) {
      final priority = TaskPriority.values.firstWhere((e) => e.name == key);
      priorityDistribution[priority] = value;
    });

    return ProductivityAnalytics(
      id: json['id'],
      period: Duration(days: json['period']),
      totalTasks: json['totalTasks'],
      completedTasks: json['completedTasks'],
      completionRate: json['completionRate'].toDouble(),
      averageTaskDuration: Duration(minutes: json['averageTaskDuration']),
      productivityScore: json['productivityScore'].toDouble(),
      focusTime: Duration(minutes: json['focusTime']),
      distractionEvents: json['distractionEvents'],
      peakProductivityHours: List<int>.from(json['peakProductivityHours']),
      categoryBreakdown: Map<String, int>.from(json['categoryBreakdown']),
      priorityDistribution: priorityDistribution,
      streakData: StreakData.fromJson(json['streakData']),
      improvementAreas: List<String>.from(json['improvementAreas']),
      achievements: List<String>.from(json['achievements']),
      generatedAt: DateTime.parse(json['generatedAt']),
    );
  }
}

/// Streak data for productivity tracking
class StreakData {
  final int currentStreak;
  final int longestStreak;
  final int totalCompletedDays;

  const StreakData({
    required this.currentStreak,
    required this.longestStreak,
    required this.totalCompletedDays,
  });

  StreakData copyWith({
    int? currentStreak,
    int? longestStreak,
    int? totalCompletedDays,
  }) {
    return StreakData(
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      totalCompletedDays: totalCompletedDays ?? this.totalCompletedDays,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'totalCompletedDays': totalCompletedDays,
    };
  }

  factory StreakData.fromJson(Map<String, dynamic> json) {
    return StreakData(
      currentStreak: json['currentStreak'],
      longestStreak: json['longestStreak'],
      totalCompletedDays: json['totalCompletedDays'],
    );
  }
}

/// Task pattern for automation
class TaskPattern {
  final String title;
  final String category;
  final TaskPriority priority;
  final List<String> tags;
  final Duration? estimatedDuration;
  final PatternFrequency frequency;

  const TaskPattern({
    required this.title,
    required this.category,
    required this.priority,
    required this.tags,
    this.estimatedDuration,
    required this.frequency,
  });

  TaskPattern copyWith({
    String? title,
    String? category,
    TaskPriority? priority,
    List<String>? tags,
    Duration? estimatedDuration,
    PatternFrequency? frequency,
  }) {
    return TaskPattern(
      title: title ?? this.title,
      category: category ?? this.category,
      priority: priority ?? this.priority,
      tags: tags ?? this.tags,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      frequency: frequency ?? this.frequency,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'category': category,
      'priority': priority.name,
      'tags': tags,
      'estimatedDuration': estimatedDuration?.inMinutes,
      'frequency': frequency.name,
    };
  }

  factory TaskPattern.fromJson(Map<String, dynamic> json) {
    return TaskPattern(
      title: json['title'],
      category: json['category'],
      priority: TaskPriority.values.firstWhere(
        (e) => e.name == json['priority'],
      ),
      tags: List<String>.from(json['tags']),
      estimatedDuration: json['estimatedDuration'] != null
          ? Duration(minutes: json['estimatedDuration'])
          : null,
      frequency: PatternFrequency.values.firstWhere(
        (e) => e.name == json['frequency'],
      ),
    );
  }
}

/// Pattern frequency options
enum PatternFrequency { daily, weekly, monthly }

/// Habit pattern for automation
class HabitPattern {
  final String title;
  final String description;
  final Duration estimatedDuration;
  final HabitFrequency frequency;

  const HabitPattern({
    required this.title,
    required this.description,
    required this.estimatedDuration,
    required this.frequency,
  });

  HabitPattern copyWith({
    String? title,
    String? description,
    Duration? estimatedDuration,
    HabitFrequency? frequency,
  }) {
    return HabitPattern(
      title: title ?? this.title,
      description: description ?? this.description,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      frequency: frequency ?? this.frequency,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'estimatedDuration': estimatedDuration.inMinutes,
      'frequency': frequency.name,
    };
  }

  factory HabitPattern.fromJson(Map<String, dynamic> json) {
    return HabitPattern(
      title: json['title'],
      description: json['description'],
      estimatedDuration: Duration(minutes: json['estimatedDuration']),
      frequency: HabitFrequency.values.firstWhere(
        (e) => e.name == json['frequency'],
      ),
    );
  }
}

/// Habit frequency options
enum HabitFrequency { daily, weekly, monthly }
