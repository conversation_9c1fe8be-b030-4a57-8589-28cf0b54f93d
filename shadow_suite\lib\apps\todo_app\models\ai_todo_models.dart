import 'package:flutter/material.dart';
import 'task_models.dart';

/// Automation event types
enum AutomationEventType {
  ruleCreated,
  ruleAdded,
  ruleExecuted,
  ruleDeleted,
  ruleRemoved,
  ruleUpdated,
  taskCreated,
  taskCompleted,
  reminderSent,
  scheduleUpdated,
}

/// Schedule frequency for automation
enum ScheduleFrequency { daily, weekly, monthly, yearly }

/// Task automation rule types
enum TaskAutomationRuleType { recurring, conditional, scheduled, trigger }

/// Automation schedule model
class AutomationSchedule {
  final ScheduleFrequency frequency;
  final DateTime? startDate;
  final DateTime? endDate;
  final List<int>? daysOfWeek;
  final int? dayOfMonth;
  final String? timeOfDay;

  const AutomationSchedule({
    required this.frequency,
    this.startDate,
    this.endDate,
    this.daysOfWeek,
    this.dayOfMonth,
    this.timeOfDay,
  });

  AutomationSchedule copyWith({
    ScheduleFrequency? frequency,
    DateTime? startDate,
    DateTime? endDate,
    List<int>? daysOfWeek,
    int? dayOfMonth,
    String? timeOfDay,
  }) {
    return AutomationSchedule(
      frequency: frequency ?? this.frequency,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      daysOfWeek: daysOfWeek ?? this.daysOfWeek,
      dayOfMonth: dayOfMonth ?? this.dayOfMonth,
      timeOfDay: timeOfDay ?? this.timeOfDay,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'frequency': frequency.name,
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'daysOfWeek': daysOfWeek,
      'dayOfMonth': dayOfMonth,
      'timeOfDay': timeOfDay,
    };
  }

  factory AutomationSchedule.fromJson(Map<String, dynamic> json) {
    return AutomationSchedule(
      frequency: ScheduleFrequency.values.firstWhere(
        (e) => e.name == json['frequency'],
      ),
      startDate: json['startDate'] != null
          ? DateTime.parse(json['startDate'])
          : null,
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      daysOfWeek: json['daysOfWeek'] != null
          ? List<int>.from(json['daysOfWeek'])
          : null,
      dayOfMonth: json['dayOfMonth'],
      timeOfDay: json['timeOfDay'],
    );
  }
}

/// Task automation rule model
class TaskAutomationRule {
  final String id;
  final String name;
  final String description;
  final TaskAutomationRuleType type;
  final bool isActive;
  final bool isEnabled;
  final Map<String, dynamic> conditions;
  final Map<String, dynamic> actions;
  final Map<String, dynamic> parameters;
  final ScheduleFrequency? frequency;
  final DateTime? nextExecution;
  final DateTime? lastExecuted;
  final AutomationSchedule? schedule;
  final DateTime createdAt;
  final DateTime updatedAt;

  const TaskAutomationRule({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.isActive,
    required this.isEnabled,
    required this.conditions,
    required this.actions,
    required this.parameters,
    this.frequency,
    this.nextExecution,
    this.lastExecuted,
    this.schedule,
    required this.createdAt,
    required this.updatedAt,
  });

  TaskAutomationRule copyWith({
    String? id,
    String? name,
    String? description,
    TaskAutomationRuleType? type,
    bool? isActive,
    bool? isEnabled,
    Map<String, dynamic>? conditions,
    Map<String, dynamic>? actions,
    Map<String, dynamic>? parameters,
    ScheduleFrequency? frequency,
    DateTime? nextExecution,
    DateTime? lastExecuted,
    AutomationSchedule? schedule,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TaskAutomationRule(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      isActive: isActive ?? this.isActive,
      isEnabled: isEnabled ?? this.isEnabled,
      conditions: conditions ?? this.conditions,
      actions: actions ?? this.actions,
      parameters: parameters ?? this.parameters,
      frequency: frequency ?? this.frequency,
      nextExecution: nextExecution ?? this.nextExecution,
      lastExecuted: lastExecuted ?? this.lastExecuted,
      schedule: schedule ?? this.schedule,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'isActive': isActive,
      'isEnabled': isEnabled,
      'conditions': conditions,
      'actions': actions,
      'parameters': parameters,
      'frequency': frequency?.name,
      'nextExecution': nextExecution?.toIso8601String(),
      'lastExecuted': lastExecuted?.toIso8601String(),
      'schedule': schedule?.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory TaskAutomationRule.fromJson(Map<String, dynamic> json) {
    return TaskAutomationRule(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      type: TaskAutomationRuleType.values.firstWhere(
        (e) => e.name == json['type'],
      ),
      isActive: json['isActive'],
      isEnabled: json['isEnabled'] ?? true,
      conditions: Map<String, dynamic>.from(json['conditions']),
      actions: Map<String, dynamic>.from(json['actions']),
      parameters: Map<String, dynamic>.from(json['parameters']),
      frequency: json['frequency'] != null
          ? ScheduleFrequency.values.firstWhere(
              (e) => e.name == json['frequency'],
            )
          : null,
      nextExecution: json['nextExecution'] != null
          ? DateTime.parse(json['nextExecution'])
          : null,
      lastExecuted: json['lastExecuted'] != null
          ? DateTime.parse(json['lastExecuted'])
          : null,
      schedule: json['schedule'] != null
          ? AutomationSchedule.fromJson(json['schedule'])
          : null,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }
}

/// Automation event model
class AutomationEvent {
  final String id;
  final AutomationEventType type;
  final String message;
  final DateTime timestamp;
  final String? ruleId;
  final String? taskId;
  final Map<String, dynamic>? metadata;

  const AutomationEvent({
    required this.id,
    required this.type,
    required this.message,
    required this.timestamp,
    this.ruleId,
    this.taskId,
    this.metadata,
  });

  AutomationEvent copyWith({
    String? id,
    AutomationEventType? type,
    String? message,
    DateTime? timestamp,
    String? ruleId,
    String? taskId,
    Map<String, dynamic>? metadata,
  }) {
    return AutomationEvent(
      id: id ?? this.id,
      type: type ?? this.type,
      message: message ?? this.message,
      timestamp: timestamp ?? this.timestamp,
      ruleId: ruleId ?? this.ruleId,
      taskId: taskId ?? this.taskId,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'ruleId': ruleId,
      'taskId': taskId,
      'metadata': metadata,
    };
  }

  factory AutomationEvent.fromJson(Map<String, dynamic> json) {
    return AutomationEvent(
      id: json['id'],
      type: AutomationEventType.values.firstWhere(
        (e) => e.name == json['type'],
      ),
      message: json['message'],
      timestamp: DateTime.parse(json['timestamp']),
      ruleId: json['ruleId'],
      taskId: json['taskId'],
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'])
          : null,
    );
  }
}

/// Streak data for tracking task completion patterns
class StreakData {
  final String id;
  final int currentStreak;
  final int longestStreak;
  final DateTime? lastCompletionDate;
  final List<DateTime> completionDates;
  final String taskId;
  final DateTime createdAt;

  const StreakData({
    required this.id,
    required this.currentStreak,
    required this.longestStreak,
    this.lastCompletionDate,
    required this.completionDates,
    required this.taskId,
    required this.createdAt,
  });

  StreakData copyWith({
    String? id,
    int? currentStreak,
    int? longestStreak,
    DateTime? lastCompletionDate,
    List<DateTime>? completionDates,
    String? taskId,
    DateTime? createdAt,
  }) {
    return StreakData(
      id: id ?? this.id,
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      lastCompletionDate: lastCompletionDate ?? this.lastCompletionDate,
      completionDates: completionDates ?? this.completionDates,
      taskId: taskId ?? this.taskId,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'lastCompletionDate': lastCompletionDate?.toIso8601String(),
      'completionDates': completionDates
          .map((d) => d.toIso8601String())
          .toList(),
      'taskId': taskId,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory StreakData.fromJson(Map<String, dynamic> json) {
    return StreakData(
      id: json['id'],
      currentStreak: json['currentStreak'],
      longestStreak: json['longestStreak'],
      lastCompletionDate: json['lastCompletionDate'] != null
          ? DateTime.parse(json['lastCompletionDate'])
          : null,
      completionDates: (json['completionDates'] as List)
          .map((d) => DateTime.parse(d))
          .toList(),
      taskId: json['taskId'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Productivity insight generated by AI analysis
class ProductivityInsight {
  final String id;
  final InsightType type;
  final String title;
  final String description;
  final ImpactLevel impact;
  final double confidence;
  final List<String> recommendations;
  final DateTime createdAt;
  final Map<String, dynamic>? metadata;

  const ProductivityInsight({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.impact,
    required this.confidence,
    required this.recommendations,
    required this.createdAt,
    this.metadata,
  });

  ProductivityInsight copyWith({
    String? id,
    InsightType? type,
    String? title,
    String? description,
    ImpactLevel? impact,
    double? confidence,
    List<String>? recommendations,
    DateTime? createdAt,
    Map<String, dynamic>? metadata,
  }) {
    return ProductivityInsight(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      impact: impact ?? this.impact,
      confidence: confidence ?? this.confidence,
      recommendations: recommendations ?? this.recommendations,
      createdAt: createdAt ?? this.createdAt,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'impact': impact.name,
      'confidence': confidence,
      'recommendations': recommendations,
      'createdAt': createdAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  factory ProductivityInsight.fromJson(Map<String, dynamic> json) {
    return ProductivityInsight(
      id: json['id'],
      type: InsightType.values.firstWhere((e) => e.name == json['type']),
      title: json['title'],
      description: json['description'],
      impact: ImpactLevel.values.firstWhere((e) => e.name == json['impact']),
      confidence: json['confidence'].toDouble(),
      recommendations: List<String>.from(json['recommendations']),
      createdAt: DateTime.parse(json['createdAt']),
      metadata: json['metadata'],
    );
  }
}

/// Types of productivity insights
enum InsightType { productivity, warning, optimization, achievement, pattern }

/// Impact level of insights
enum ImpactLevel { low, medium, high, critical }

/// Task recommendation from AI
class TaskRecommendation {
  final String id;
  final RecommendationType type;
  final String title;
  final String description;
  final List<String> suggestedTasks;
  final Duration estimatedDuration;
  final double confidence;
  final String reasoning;
  final DateTime createdAt;

  const TaskRecommendation({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.suggestedTasks,
    required this.estimatedDuration,
    required this.confidence,
    required this.reasoning,
    required this.createdAt,
  });

  TaskRecommendation copyWith({
    String? id,
    RecommendationType? type,
    String? title,
    String? description,
    List<String>? suggestedTasks,
    Duration? estimatedDuration,
    double? confidence,
    String? reasoning,
    DateTime? createdAt,
  }) {
    return TaskRecommendation(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      suggestedTasks: suggestedTasks ?? this.suggestedTasks,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      confidence: confidence ?? this.confidence,
      reasoning: reasoning ?? this.reasoning,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'suggestedTasks': suggestedTasks,
      'estimatedDuration': estimatedDuration.inMinutes,
      'confidence': confidence,
      'reasoning': reasoning,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory TaskRecommendation.fromJson(Map<String, dynamic> json) {
    return TaskRecommendation(
      id: json['id'],
      type: RecommendationType.values.firstWhere((e) => e.name == json['type']),
      title: json['title'],
      description: json['description'],
      suggestedTasks: List<String>.from(json['suggestedTasks']),
      estimatedDuration: Duration(minutes: json['estimatedDuration']),
      confidence: json['confidence'].toDouble(),
      reasoning: json['reasoning'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Types of task recommendations
enum RecommendationType {
  timeOptimal,
  energyOptimal,
  contextOptimal,
  urgency,
  productivity,
}

/// User preferences for AI recommendations
class UserPreferences {
  final String currentLocation;
  final EnergyLevel energyLevel;
  final List<int> preferredWorkingHours;
  final Map<String, double> categoryPreferences;
  final bool enableSmartNotifications;
  final int focusSessionDuration;
  final int breakDuration;
  final bool enablePomodoroMode;

  const UserPreferences({
    required this.currentLocation,
    required this.energyLevel,
    required this.preferredWorkingHours,
    required this.categoryPreferences,
    required this.enableSmartNotifications,
    required this.focusSessionDuration,
    required this.breakDuration,
    required this.enablePomodoroMode,
  });

  UserPreferences copyWith({
    String? currentLocation,
    EnergyLevel? energyLevel,
    List<int>? preferredWorkingHours,
    Map<String, double>? categoryPreferences,
    bool? enableSmartNotifications,
    int? focusSessionDuration,
    int? breakDuration,
    bool? enablePomodoroMode,
  }) {
    return UserPreferences(
      currentLocation: currentLocation ?? this.currentLocation,
      energyLevel: energyLevel ?? this.energyLevel,
      preferredWorkingHours:
          preferredWorkingHours ?? this.preferredWorkingHours,
      categoryPreferences: categoryPreferences ?? this.categoryPreferences,
      enableSmartNotifications:
          enableSmartNotifications ?? this.enableSmartNotifications,
      focusSessionDuration: focusSessionDuration ?? this.focusSessionDuration,
      breakDuration: breakDuration ?? this.breakDuration,
      enablePomodoroMode: enablePomodoroMode ?? this.enablePomodoroMode,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'currentLocation': currentLocation,
      'energyLevel': energyLevel.name,
      'preferredWorkingHours': preferredWorkingHours,
      'categoryPreferences': categoryPreferences,
      'enableSmartNotifications': enableSmartNotifications,
      'focusSessionDuration': focusSessionDuration,
      'breakDuration': breakDuration,
      'enablePomodoroMode': enablePomodoroMode,
    };
  }

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      currentLocation: json['currentLocation'],
      energyLevel: EnergyLevel.values.firstWhere(
        (e) => e.name == json['energyLevel'],
      ),
      preferredWorkingHours: List<int>.from(json['preferredWorkingHours']),
      categoryPreferences: Map<String, double>.from(
        json['categoryPreferences'],
      ),
      enableSmartNotifications: json['enableSmartNotifications'],
      focusSessionDuration: json['focusSessionDuration'],
      breakDuration: json['breakDuration'],
      enablePomodoroMode: json['enablePomodoroMode'],
    );
  }
}

/// Energy levels for recommendations
enum EnergyLevel { low, medium, high }

/// Comprehensive productivity analytics
class ProductivityAnalytics {
  final String id;
  final Duration period;
  final int totalTasks;
  final int completedTasks;
  final double completionRate;
  final Duration averageTaskDuration;
  final double productivityScore;
  final Duration focusTime;
  final int distractionEvents;
  final List<int> peakProductivityHours;
  final Map<String, int> categoryBreakdown;
  final Map<TaskPriority, int> priorityDistribution;
  final StreakData streakData;
  final List<String> improvementAreas;
  final List<String> achievements;
  final DateTime generatedAt;

  const ProductivityAnalytics({
    required this.id,
    required this.period,
    required this.totalTasks,
    required this.completedTasks,
    required this.completionRate,
    required this.averageTaskDuration,
    required this.productivityScore,
    required this.focusTime,
    required this.distractionEvents,
    required this.peakProductivityHours,
    required this.categoryBreakdown,
    required this.priorityDistribution,
    required this.streakData,
    required this.improvementAreas,
    required this.achievements,
    required this.generatedAt,
  });

  ProductivityAnalytics copyWith({
    String? id,
    Duration? period,
    int? totalTasks,
    int? completedTasks,
    double? completionRate,
    Duration? averageTaskDuration,
    double? productivityScore,
    Duration? focusTime,
    int? distractionEvents,
    List<int>? peakProductivityHours,
    Map<String, int>? categoryBreakdown,
    Map<TaskPriority, int>? priorityDistribution,
    StreakData? streakData,
    List<String>? improvementAreas,
    List<String>? achievements,
    DateTime? generatedAt,
  }) {
    return ProductivityAnalytics(
      id: id ?? this.id,
      period: period ?? this.period,
      totalTasks: totalTasks ?? this.totalTasks,
      completedTasks: completedTasks ?? this.completedTasks,
      completionRate: completionRate ?? this.completionRate,
      averageTaskDuration: averageTaskDuration ?? this.averageTaskDuration,
      productivityScore: productivityScore ?? this.productivityScore,
      focusTime: focusTime ?? this.focusTime,
      distractionEvents: distractionEvents ?? this.distractionEvents,
      peakProductivityHours:
          peakProductivityHours ?? this.peakProductivityHours,
      categoryBreakdown: categoryBreakdown ?? this.categoryBreakdown,
      priorityDistribution: priorityDistribution ?? this.priorityDistribution,
      streakData: streakData ?? this.streakData,
      improvementAreas: improvementAreas ?? this.improvementAreas,
      achievements: achievements ?? this.achievements,
      generatedAt: generatedAt ?? this.generatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'period': period.inDays,
      'totalTasks': totalTasks,
      'completedTasks': completedTasks,
      'completionRate': completionRate,
      'averageTaskDuration': averageTaskDuration.inMinutes,
      'productivityScore': productivityScore,
      'focusTime': focusTime.inMinutes,
      'distractionEvents': distractionEvents,
      'peakProductivityHours': peakProductivityHours,
      'categoryBreakdown': categoryBreakdown,
      'priorityDistribution': priorityDistribution.map(
        (k, v) => MapEntry(k.name, v),
      ),
      'streakData': streakData.toJson(),
      'improvementAreas': improvementAreas,
      'achievements': achievements,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }

  factory ProductivityAnalytics.fromJson(Map<String, dynamic> json) {
    final priorityDistributionMap = Map<String, dynamic>.from(
      json['priorityDistribution'],
    );
    final priorityDistribution = <TaskPriority, int>{};

    priorityDistributionMap.forEach((key, value) {
      final priority = TaskPriority.values.firstWhere((e) => e.name == key);
      priorityDistribution[priority] = value;
    });

    return ProductivityAnalytics(
      id: json['id'],
      period: Duration(days: json['period']),
      totalTasks: json['totalTasks'],
      completedTasks: json['completedTasks'],
      completionRate: json['completionRate'].toDouble(),
      averageTaskDuration: Duration(minutes: json['averageTaskDuration']),
      productivityScore: json['productivityScore'].toDouble(),
      focusTime: Duration(minutes: json['focusTime']),
      distractionEvents: json['distractionEvents'],
      peakProductivityHours: List<int>.from(json['peakProductivityHours']),
      categoryBreakdown: Map<String, int>.from(json['categoryBreakdown']),
      priorityDistribution: priorityDistribution,
      streakData: StreakData.fromJson(json['streakData']),
      improvementAreas: List<String>.from(json['improvementAreas']),
      achievements: List<String>.from(json['achievements']),
      generatedAt: DateTime.parse(json['generatedAt']),
    );
  }
}

/// Energy level enum
enum EnergyLevel { low, medium, high }

/// Recommendation type enum
enum RecommendationType {
  taskOptimization,
  timeBlocking,
  priorityAdjustment,
  breakReminder,
}

/// Task pattern for automation
class TaskPattern {
  final String title;
  final String category;
  final TaskPriority priority;
  final List<String> tags;
  final Duration? estimatedDuration;
  final PatternFrequency frequency;

  const TaskPattern({
    required this.title,
    required this.category,
    required this.priority,
    required this.tags,
    this.estimatedDuration,
    required this.frequency,
  });

  TaskPattern copyWith({
    String? title,
    String? category,
    TaskPriority? priority,
    List<String>? tags,
    Duration? estimatedDuration,
    PatternFrequency? frequency,
  }) {
    return TaskPattern(
      title: title ?? this.title,
      category: category ?? this.category,
      priority: priority ?? this.priority,
      tags: tags ?? this.tags,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      frequency: frequency ?? this.frequency,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'category': category,
      'priority': priority.name,
      'tags': tags,
      'estimatedDuration': estimatedDuration?.inMinutes,
      'frequency': frequency.name,
    };
  }

  factory TaskPattern.fromJson(Map<String, dynamic> json) {
    return TaskPattern(
      title: json['title'],
      category: json['category'],
      priority: TaskPriority.values.firstWhere(
        (e) => e.name == json['priority'],
      ),
      tags: List<String>.from(json['tags']),
      estimatedDuration: json['estimatedDuration'] != null
          ? Duration(minutes: json['estimatedDuration'])
          : null,
      frequency: PatternFrequency.values.firstWhere(
        (e) => e.name == json['frequency'],
      ),
    );
  }
}

/// Pattern frequency options
enum PatternFrequency { daily, weekly, monthly }

/// Habit pattern for automation
class HabitPattern {
  final String title;
  final String description;
  final Duration estimatedDuration;
  final HabitFrequency frequency;

  const HabitPattern({
    required this.title,
    required this.description,
    required this.estimatedDuration,
    required this.frequency,
  });

  HabitPattern copyWith({
    String? title,
    String? description,
    Duration? estimatedDuration,
    HabitFrequency? frequency,
  }) {
    return HabitPattern(
      title: title ?? this.title,
      description: description ?? this.description,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      frequency: frequency ?? this.frequency,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'estimatedDuration': estimatedDuration.inMinutes,
      'frequency': frequency.name,
    };
  }

  factory HabitPattern.fromJson(Map<String, dynamic> json) {
    return HabitPattern(
      title: json['title'],
      description: json['description'],
      estimatedDuration: Duration(minutes: json['estimatedDuration']),
      frequency: HabitFrequency.values.firstWhere(
        (e) => e.name == json['frequency'],
      ),
    );
  }
}

/// Habit frequency options
enum HabitFrequency { daily, weekly, monthly }
