import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sqflite/sqflite.dart';
import '../../../core/database/database_service.dart';
import '../models/note_models.dart';

/// Notes Service with comprehensive CRUD operations
class NotesService {
  static final NotesService _instance = NotesService._internal();
  factory NotesService() => _instance;
  NotesService._internal();

  // In-memory storage with database persistence
  final List<Note> _notes = [];
  final List<NoteFolder> _folders = [];
  
  final ValueNotifier<int> _stateNotifier = ValueNotifier<int>(0);

  // Database instance
  Database? _database;

  /// Initialize notes service
  Future<void> initialize() async {
    await _initializeDatabase();
    await _loadNotesFromDatabase();
    await _loadFoldersFromDatabase();
    if (_notes.isEmpty) {
      _loadSampleNotes();
    }
  }

  Future<void> _initializeDatabase() async {
    _database = await DatabaseService.getDatabase();
    
    // Create notes table
    await _database!.execute('''
      CREATE TABLE IF NOT EXISTS notes (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT,
        type TEXT NOT NULL,
        priority TEXT NOT NULL,
        color INTEGER NOT NULL,
        tags TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_pinned INTEGER DEFAULT 0,
        is_archived INTEGER DEFAULT 0,
        is_favorite INTEGER DEFAULT 0,
        checklist_items TEXT,
        canvas_data TEXT,
        metadata TEXT
      )
    ''');

    // Create note_folders table
    await _database!.execute('''
      CREATE TABLE IF NOT EXISTS note_folders (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        color INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_default INTEGER DEFAULT 0
      )
    ''');
  }

  void _loadSampleNotes() {
    final now = DateTime.now();
    _notes.addAll([
      Note(
        id: '1',
        title: 'Meeting Notes',
        content: 'Discussed project timeline and deliverables for Q4. Key points:\n\n• Launch date: December 15th\n• Team assignments completed\n• Budget approved\n• Next review: Friday',
        type: NoteType.text,
        priority: NotePriority.high,
        color: Colors.yellow[100]!,
        tags: ['work', 'meeting', 'important'],
        createdAt: now.subtract(const Duration(days: 2)),
        updatedAt: now.subtract(const Duration(hours: 3)),
        isPinned: true,
      ),
      Note(
        id: '2',
        title: 'Shopping List',
        type: NoteType.checklist,
        priority: NotePriority.medium,
        color: Colors.green[100]!,
        tags: ['shopping', 'personal'],
        createdAt: now.subtract(const Duration(days: 1)),
        updatedAt: now.subtract(const Duration(hours: 2)),
        checklistItems: [
          ChecklistItem(
            id: '1',
            text: 'Milk',
            isCompleted: true,
            createdAt: now.subtract(const Duration(days: 1)),
            completedAt: now.subtract(const Duration(hours: 4)),
          ),
          ChecklistItem(
            id: '2',
            text: 'Bread',
            isCompleted: false,
            createdAt: now.subtract(const Duration(days: 1)),
          ),
          ChecklistItem(
            id: '3',
            text: 'Eggs',
            isCompleted: false,
            createdAt: now.subtract(const Duration(days: 1)),
          ),
          ChecklistItem(
            id: '4',
            text: 'Apples',
            isCompleted: true,
            createdAt: now.subtract(const Duration(days: 1)),
            completedAt: now.subtract(const Duration(hours: 4)),
          ),
        ],
      ),
      Note(
        id: '3',
        title: 'Design Sketch',
        type: NoteType.canvas,
        priority: NotePriority.medium,
        color: Colors.blue[100]!,
        tags: ['design', 'creative'],
        createdAt: now.subtract(const Duration(hours: 6)),
        updatedAt: now.subtract(const Duration(hours: 1)),
        canvasData: CanvasData(
          strokes: [
            DrawingStroke(
              points: [
                const Offset(50, 50),
                const Offset(100, 100),
                const Offset(150, 50),
                const Offset(200, 100),
              ],
              color: Colors.black,
              strokeWidth: 3.0,
              createdAt: now.subtract(const Duration(hours: 6)),
            ),
          ],
          backgroundColor: Colors.white,
          canvasSize: const Size(400, 600),
        ),
        isFavorite: true,
      ),
      Note(
        id: '4',
        title: 'Ideas for App',
        content: 'Random thoughts and ideas for the new mobile app:\n\n- Dark mode support\n- Offline functionality\n- Push notifications\n- Social sharing\n- Voice commands\n- Gesture controls',
        type: NoteType.text,
        priority: NotePriority.low,
        color: Colors.purple[100]!,
        tags: ['ideas', 'app', 'development'],
        createdAt: now.subtract(const Duration(hours: 12)),
        updatedAt: now.subtract(const Duration(hours: 8)),
      ),
    ]);
    _notifyChange();
  }

  // Note CRUD operations
  List<Note> getNotes() => List.unmodifiable(_notes.where((note) => !note.isArchived));

  List<Note> getPinnedNotes() {
    return _notes.where((note) => note.isPinned && !note.isArchived).toList();
  }

  List<Note> getFavoriteNotes() {
    return _notes.where((note) => note.isFavorite && !note.isArchived).toList();
  }

  List<Note> getArchivedNotes() {
    return _notes.where((note) => note.isArchived).toList();
  }

  List<Note> getNotesByType(NoteType type) {
    return _notes.where((note) => note.type == type && !note.isArchived).toList();
  }

  List<Note> searchNotes(String query) {
    final lowerQuery = query.toLowerCase();
    return _notes.where((note) {
      return !note.isArchived &&
          (note.title.toLowerCase().contains(lowerQuery) ||
           note.content.toLowerCase().contains(lowerQuery) ||
           note.tags.any((tag) => tag.toLowerCase().contains(lowerQuery)));
    }).toList();
  }

  Note? getNote(String id) {
    try {
      return _notes.firstWhere((note) => note.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<void> addNote(Note note) async {
    _notes.add(note);
    await _saveNoteToDatabase(note);
    _notifyChange();
  }

  Future<void> updateNote(Note note) async {
    final index = _notes.indexWhere((n) => n.id == note.id);
    if (index != -1) {
      _notes[index] = note.copyWith(updatedAt: DateTime.now());
      await _saveNoteToDatabase(_notes[index]);
      _notifyChange();
    }
  }

  Future<void> deleteNote(String noteId) async {
    _notes.removeWhere((note) => note.id == noteId);
    await _deleteNoteFromDatabase(noteId);
    _notifyChange();
  }

  Future<void> togglePin(String noteId) async {
    final index = _notes.indexWhere((n) => n.id == noteId);
    if (index != -1) {
      _notes[index] = _notes[index].copyWith(
        isPinned: !_notes[index].isPinned,
        updatedAt: DateTime.now(),
      );
      await _saveNoteToDatabase(_notes[index]);
      _notifyChange();
    }
  }

  Future<void> toggleFavorite(String noteId) async {
    final index = _notes.indexWhere((n) => n.id == noteId);
    if (index != -1) {
      _notes[index] = _notes[index].copyWith(
        isFavorite: !_notes[index].isFavorite,
        updatedAt: DateTime.now(),
      );
      await _saveNoteToDatabase(_notes[index]);
      _notifyChange();
    }
  }

  Future<void> archiveNote(String noteId) async {
    final index = _notes.indexWhere((n) => n.id == noteId);
    if (index != -1) {
      _notes[index] = _notes[index].copyWith(
        isArchived: true,
        updatedAt: DateTime.now(),
      );
      await _saveNoteToDatabase(_notes[index]);
      _notifyChange();
    }
  }

  Future<void> unarchiveNote(String noteId) async {
    final index = _notes.indexWhere((n) => n.id == noteId);
    if (index != -1) {
      _notes[index] = _notes[index].copyWith(
        isArchived: false,
        updatedAt: DateTime.now(),
      );
      await _saveNoteToDatabase(_notes[index]);
      _notifyChange();
    }
  }

  Future<void> updateNoteColor(String noteId, Color color) async {
    final index = _notes.indexWhere((n) => n.id == noteId);
    if (index != -1) {
      _notes[index] = _notes[index].copyWith(
        color: color,
        updatedAt: DateTime.now(),
      );
      await _saveNoteToDatabase(_notes[index]);
      _notifyChange();
    }
  }

  // Checklist operations
  Future<void> addChecklistItem(String noteId, ChecklistItem item) async {
    final index = _notes.indexWhere((n) => n.id == noteId);
    if (index != -1) {
      final note = _notes[index];
      final updatedItems = List<ChecklistItem>.from(note.checklistItems)..add(item);
      _notes[index] = note.copyWith(
        checklistItems: updatedItems,
        updatedAt: DateTime.now(),
      );
      await _saveNoteToDatabase(_notes[index]);
      _notifyChange();
    }
  }

  Future<void> updateChecklistItem(String noteId, ChecklistItem item) async {
    final index = _notes.indexWhere((n) => n.id == noteId);
    if (index != -1) {
      final note = _notes[index];
      final updatedItems = note.checklistItems.map((i) {
        return i.id == item.id ? item : i;
      }).toList();
      _notes[index] = note.copyWith(
        checklistItems: updatedItems,
        updatedAt: DateTime.now(),
      );
      await _saveNoteToDatabase(_notes[index]);
      _notifyChange();
    }
  }

  Future<void> deleteChecklistItem(String noteId, String itemId) async {
    final index = _notes.indexWhere((n) => n.id == noteId);
    if (index != -1) {
      final note = _notes[index];
      final updatedItems = note.checklistItems.where((i) => i.id != itemId).toList();
      _notes[index] = note.copyWith(
        checklistItems: updatedItems,
        updatedAt: DateTime.now(),
      );
      await _saveNoteToDatabase(_notes[index]);
      _notifyChange();
    }
  }

  // Canvas operations
  Future<void> updateCanvasData(String noteId, CanvasData canvasData) async {
    final index = _notes.indexWhere((n) => n.id == noteId);
    if (index != -1) {
      _notes[index] = _notes[index].copyWith(
        canvasData: canvasData,
        updatedAt: DateTime.now(),
      );
      await _saveNoteToDatabase(_notes[index]);
      _notifyChange();
    }
  }

  // Database operations
  Future<void> _saveNoteToDatabase(Note note) async {
    if (_database == null) return;
    
    await _database!.insert(
      'notes',
      {
        'id': note.id,
        'title': note.title,
        'content': note.content,
        'type': note.type.name,
        'priority': note.priority.name,
        'color': note.color.value,
        'tags': note.tags.join(','),
        'created_at': note.createdAt.toIso8601String(),
        'updated_at': note.updatedAt.toIso8601String(),
        'is_pinned': note.isPinned ? 1 : 0,
        'is_archived': note.isArchived ? 1 : 0,
        'is_favorite': note.isFavorite ? 1 : 0,
        'checklist_items': note.checklistItems.map((item) => item.toJson()).toString(),
        'canvas_data': note.canvasData?.toJson().toString(),
        'metadata': note.metadata.toString(),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<void> _loadNotesFromDatabase() async {
    if (_database == null) return;
    
    final List<Map<String, dynamic>> maps = await _database!.query('notes');
    _notes.clear();
    
    for (final map in maps) {
      // Parse checklist items
      List<ChecklistItem> checklistItems = [];
      if (map['checklist_items'] != null && map['checklist_items'].toString().isNotEmpty) {
        try {
          // This would need proper JSON parsing in a real implementation
          checklistItems = [];
        } catch (e) {
          checklistItems = [];
        }
      }

      // Parse canvas data
      CanvasData? canvasData;
      if (map['canvas_data'] != null && map['canvas_data'].toString().isNotEmpty) {
        try {
          // This would need proper JSON parsing in a real implementation
          canvasData = null;
        } catch (e) {
          canvasData = null;
        }
      }

      final note = Note.fromJson({
        'id': map['id'],
        'title': map['title'],
        'content': map['content'],
        'type': map['type'],
        'priority': map['priority'],
        'color': map['color'],
        'tags': map['tags']?.split(',') ?? [],
        'createdAt': map['created_at'],
        'updatedAt': map['updated_at'],
        'isPinned': map['is_pinned'] == 1,
        'isArchived': map['is_archived'] == 1,
        'isFavorite': map['is_favorite'] == 1,
        'checklistItems': checklistItems,
        'canvasData': canvasData,
        'metadata': {},
      });
      
      _notes.add(note);
    }
  }

  Future<void> _deleteNoteFromDatabase(String noteId) async {
    if (_database == null) return;
    
    await _database!.delete(
      'notes',
      where: 'id = ?',
      whereArgs: [noteId],
    );
  }

  Future<void> _loadFoldersFromDatabase() async {
    // Implementation for folders loading
  }

  void _notifyChange() {
    _stateNotifier.value++;
  }

  ValueNotifier<int> get stateNotifier => _stateNotifier;
}

// Providers
final notesServiceProvider = Provider<NotesService>((ref) {
  final service = NotesService();
  service.initialize();
  return service;
});

final notesProvider = Provider<List<Note>>((ref) {
  final service = ref.watch(notesServiceProvider);
  ref.watch(service.stateNotifier);
  return service.getNotes();
});

final pinnedNotesProvider = Provider<List<Note>>((ref) {
  final service = ref.watch(notesServiceProvider);
  ref.watch(service.stateNotifier);
  return service.getPinnedNotes();
});

final favoriteNotesProvider = Provider<List<Note>>((ref) {
  final service = ref.watch(notesServiceProvider);
  ref.watch(service.stateNotifier);
  return service.getFavoriteNotes();
});

final textNotesProvider = Provider<List<Note>>((ref) {
  final service = ref.watch(notesServiceProvider);
  ref.watch(service.stateNotifier);
  return service.getNotesByType(NoteType.text);
});

final checklistNotesProvider = Provider<List<Note>>((ref) {
  final service = ref.watch(notesServiceProvider);
  ref.watch(service.stateNotifier);
  return service.getNotesByType(NoteType.checklist);
});

final canvasNotesProvider = Provider<List<Note>>((ref) {
  final service = ref.watch(notesServiceProvider);
  ref.watch(service.stateNotifier);
  return service.getNotesByType(NoteType.canvas);
});
