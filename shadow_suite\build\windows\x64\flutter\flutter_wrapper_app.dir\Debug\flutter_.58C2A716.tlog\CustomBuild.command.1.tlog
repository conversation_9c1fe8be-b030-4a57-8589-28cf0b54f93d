^D:\PROJECTS\T2 - COPY\SHADOW_SUITE\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SD:/projects/t2 - Copy/shadow_suite/windows" "-BD:/projects/t2 - Copy/shadow_suite/build/windows/x64" --check-stamp-file "D:/projects/t2 - Copy/shadow_suite/build/windows/x64/flutter/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
