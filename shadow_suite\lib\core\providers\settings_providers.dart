import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../theme/app_theme.dart';

// Enums for settings
enum FontSize { small, medium, large }

// Theme Mode Provider
final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, ThemeMode>((
  ref,
) {
  return ThemeModeNotifier();
});

class ThemeModeNotifier extends StateNotifier<ThemeMode> {
  ThemeModeNotifier() : super(ThemeMode.system) {
    _loadThemeMode();
  }

  Future<void> _loadThemeMode() async {
    final prefs = await SharedPreferences.getInstance();
    final themeModeString = prefs.getString('theme_mode') ?? 'system';
    state = ThemeMode.values.firstWhere(
      (mode) => mode.name == themeModeString,
      orElse: () => ThemeMode.system,
    );
  }

  Future<void> setThemeMode(ThemeMode mode) async {
    state = mode;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('theme_mode', mode.name);
  }
}

// Primary Color Provider
final primaryColorProvider = StateNotifierProvider<PrimaryColorNotifier, Color>(
  (ref) {
    return PrimaryColorNotifier();
  },
);

class PrimaryColorNotifier extends StateNotifier<Color> {
  PrimaryColorNotifier() : super(AppTheme.primaryColor) {
    _loadPrimaryColor();
  }

  Future<void> _loadPrimaryColor() async {
    final prefs = await SharedPreferences.getInstance();
    final colorValue =
        prefs.getInt('primary_color') ?? AppTheme.primaryColor.toARGB32();
    state = Color(colorValue);
  }

  Future<void> setPrimaryColor(Color color) async {
    state = color;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('primary_color', color.toARGB32());
  }
}

// Font Size Provider
final fontSizeProvider = StateNotifierProvider<FontSizeNotifier, FontSize>((
  ref,
) {
  return FontSizeNotifier();
});

class FontSizeNotifier extends StateNotifier<FontSize> {
  FontSizeNotifier() : super(FontSize.medium) {
    _loadFontSize();
  }

  Future<void> _loadFontSize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      // Handle both string and double values for backward compatibility
      String fontSizeString;
      if (prefs.containsKey('font_size')) {
        final value = prefs.get('font_size');
        if (value is String) {
          fontSizeString = value;
        } else if (value is double) {
          // Convert old double values to string
          fontSizeString = 'medium';
          await prefs.setString('font_size', fontSizeString);
        } else {
          fontSizeString = 'medium';
        }
      } else {
        fontSizeString = 'medium';
      }

      state = FontSize.values.firstWhere(
        (size) => size.name == fontSizeString,
        orElse: () => FontSize.medium,
      );
    } catch (e) {
      // Fallback to medium if any error occurs
      state = FontSize.medium;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('font_size', 'medium');
    }
  }

  Future<void> setFontSize(FontSize size) async {
    state = size;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('font_size', size.name);
  }
}

// Analytics Provider
final analyticsEnabledProvider = StateNotifierProvider<AnalyticsNotifier, bool>(
  (ref) {
    return AnalyticsNotifier();
  },
);

class AnalyticsNotifier extends StateNotifier<bool> {
  AnalyticsNotifier() : super(true) {
    _loadAnalyticsEnabled();
  }

  Future<void> _loadAnalyticsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    state = prefs.getBool('analytics_enabled') ?? true;
  }

  Future<void> setAnalyticsEnabled(bool enabled) async {
    state = enabled;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('analytics_enabled', enabled);
  }
}

// Crash Reports Provider
final crashReportsEnabledProvider =
    StateNotifierProvider<CrashReportsNotifier, bool>((ref) {
      return CrashReportsNotifier();
    });

class CrashReportsNotifier extends StateNotifier<bool> {
  CrashReportsNotifier() : super(true) {
    _loadCrashReportsEnabled();
  }

  Future<void> _loadCrashReportsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    state = prefs.getBool('crash_reports_enabled') ?? true;
  }

  Future<void> setCrashReportsEnabled(bool enabled) async {
    state = enabled;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('crash_reports_enabled', enabled);
  }
}

// App-specific settings providers
final appSettingsProvider =
    StateNotifierProvider.family<
      AppSettingsNotifier,
      Map<String, dynamic>,
      String
    >((ref, appId) {
      return AppSettingsNotifier(appId);
    });

class AppSettingsNotifier extends StateNotifier<Map<String, dynamic>> {
  final String appId;

  AppSettingsNotifier(this.appId) : super({}) {
    _loadAppSettings();
  }

  Future<void> _loadAppSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final settingsJson = prefs.getString('app_settings_$appId');
    if (settingsJson != null) {
      // In a real implementation, you would parse JSON here
      // For now, we'll use default settings
    }

    // Set default settings based on app
    switch (appId) {
      case 'money_manager':
        state = {
          'default_currency': 'USD',
          'show_decimals': true,
          'enable_notifications': true,
          'auto_backup': false,
          'theme_color': AppTheme.moneyManagerColor.toARGB32(),
        };
        break;
      case 'islamic_app':
        state = {
          'prayer_notifications': true,
          'arabic_font_size': 'medium',
          'location_services': true,
          'calculation_method': 'muslim_world_league',
          'theme_color': AppTheme.islamicAppColor.toARGB32(),
        };
        break;
      case 'memo_suite':
        state = {
          'auto_save': true,
          'voice_quality': 'high',
          'transcription_enabled': false,
          'cloud_sync': false,
          'theme_color': AppTheme.memoSuiteColor.toARGB32(),
        };
        break;
      case 'excel_to_app':
        state = {
          'auto_calculate': true,
          'show_formulas': false,
          'grid_lines': true,
          'auto_save_interval': 5,
          'theme_color': AppTheme.excelToAppColor.toARGB32(),
        };
        break;
      default:
        state = {};
    }
  }

  Future<void> updateSetting(String key, dynamic value) async {
    state = {...state, key: value};
    final prefs = await SharedPreferences.getInstance();
    // In a real implementation, you would serialize to JSON
    await prefs.setString('app_settings_$appId', state.toString());
  }

  T getSetting<T>(String key, T defaultValue) {
    return state[key] as T? ?? defaultValue;
  }
}

// Global settings state provider
final globalSettingsProvider = Provider<GlobalSettings>((ref) {
  final themeMode = ref.watch(themeModeProvider);
  final primaryColor = ref.watch(primaryColorProvider);
  final fontSize = ref.watch(fontSizeProvider);
  final analyticsEnabled = ref.watch(analyticsEnabledProvider);
  final crashReportsEnabled = ref.watch(crashReportsEnabledProvider);

  return GlobalSettings(
    themeMode: themeMode,
    primaryColor: primaryColor,
    fontSize: fontSize,
    analyticsEnabled: analyticsEnabled,
    crashReportsEnabled: crashReportsEnabled,
  );
});

class GlobalSettings {
  final ThemeMode themeMode;
  final Color primaryColor;
  final FontSize fontSize;
  final bool analyticsEnabled;
  final bool crashReportsEnabled;

  const GlobalSettings({
    required this.themeMode,
    required this.primaryColor,
    required this.fontSize,
    required this.analyticsEnabled,
    required this.crashReportsEnabled,
  });

  double get fontSizeMultiplier {
    switch (fontSize) {
      case FontSize.small:
        return 0.9;
      case FontSize.medium:
        return 1.0;
      case FontSize.large:
        return 1.1;
    }
  }

  TextTheme getTextTheme(TextTheme baseTheme) {
    return baseTheme.copyWith(
      displayLarge: baseTheme.displayLarge?.copyWith(
        fontSize: (baseTheme.displayLarge?.fontSize ?? 57) * fontSizeMultiplier,
      ),
      displayMedium: baseTheme.displayMedium?.copyWith(
        fontSize:
            (baseTheme.displayMedium?.fontSize ?? 45) * fontSizeMultiplier,
      ),
      displaySmall: baseTheme.displaySmall?.copyWith(
        fontSize: (baseTheme.displaySmall?.fontSize ?? 36) * fontSizeMultiplier,
      ),
      headlineLarge: baseTheme.headlineLarge?.copyWith(
        fontSize:
            (baseTheme.headlineLarge?.fontSize ?? 32) * fontSizeMultiplier,
      ),
      headlineMedium: baseTheme.headlineMedium?.copyWith(
        fontSize:
            (baseTheme.headlineMedium?.fontSize ?? 28) * fontSizeMultiplier,
      ),
      headlineSmall: baseTheme.headlineSmall?.copyWith(
        fontSize:
            (baseTheme.headlineSmall?.fontSize ?? 24) * fontSizeMultiplier,
      ),
      titleLarge: baseTheme.titleLarge?.copyWith(
        fontSize: (baseTheme.titleLarge?.fontSize ?? 22) * fontSizeMultiplier,
      ),
      titleMedium: baseTheme.titleMedium?.copyWith(
        fontSize: (baseTheme.titleMedium?.fontSize ?? 16) * fontSizeMultiplier,
      ),
      titleSmall: baseTheme.titleSmall?.copyWith(
        fontSize: (baseTheme.titleSmall?.fontSize ?? 14) * fontSizeMultiplier,
      ),
      bodyLarge: baseTheme.bodyLarge?.copyWith(
        fontSize: (baseTheme.bodyLarge?.fontSize ?? 16) * fontSizeMultiplier,
      ),
      bodyMedium: baseTheme.bodyMedium?.copyWith(
        fontSize: (baseTheme.bodyMedium?.fontSize ?? 14) * fontSizeMultiplier,
      ),
      bodySmall: baseTheme.bodySmall?.copyWith(
        fontSize: (baseTheme.bodySmall?.fontSize ?? 12) * fontSizeMultiplier,
      ),
      labelLarge: baseTheme.labelLarge?.copyWith(
        fontSize: (baseTheme.labelLarge?.fontSize ?? 14) * fontSizeMultiplier,
      ),
      labelMedium: baseTheme.labelMedium?.copyWith(
        fontSize: (baseTheme.labelMedium?.fontSize ?? 12) * fontSizeMultiplier,
      ),
      labelSmall: baseTheme.labelSmall?.copyWith(
        fontSize: (baseTheme.labelSmall?.fontSize ?? 11) * fontSizeMultiplier,
      ),
    );
  }
}
