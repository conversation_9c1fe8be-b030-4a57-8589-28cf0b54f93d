import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../file_manager_main.dart';
import 'file_pane.dart';

class DualPaneExplorer extends ConsumerStatefulWidget {
  final bool singlePane;
  
  const DualPaneExplorer({
    super.key,
    this.singlePane = false,
  });

  @override
  ConsumerState<DualPaneExplorer> createState() => _DualPaneExplorerState();
}

class _DualPaneExplorerState extends ConsumerState<DualPaneExplorer> {
  @override
  Widget build(BuildContext context) {
    final activePane = ref.watch(activePaneProvider);
    final leftPath = ref.watch(leftPanePathProvider);
    final rightPath = ref.watch(rightPanePathProvider);

    if (widget.singlePane) {
      return _buildSinglePane(activePane, leftPath, rightPath);
    } else {
      return _buildDualPane(activePane, leftPath, rightPath);
    }
  }

  Widget _buildSinglePane(String activePane, String leftPath, String rightPath) {
    final currentPath = activePane == 'left' ? leftPath : rightPath;
    
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: FilePane(
        path: currentPath,
        isActive: true,
        paneId: activePane,
        onPathChanged: (newPath) => _updatePanePath(activePane, newPath),
        onPaneActivated: () => _setActivePane(activePane),
      ),
    );
  }

  Widget _buildDualPane(String activePane, String leftPath, String rightPath) {
    return Row(
      children: [
        // Left Pane
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                right: BorderSide(
                  color: activePane == 'left' 
                      ? const Color(0xFFE67E22) 
                      : const Color(0xFFE9ECEF),
                  width: activePane == 'left' ? 2 : 1,
                ),
              ),
            ),
            child: FilePane(
              path: leftPath,
              isActive: activePane == 'left',
              paneId: 'left',
              onPathChanged: (newPath) => _updatePanePath('left', newPath),
              onPaneActivated: () => _setActivePane('left'),
            ),
          ),
        ),
        
        // Splitter
        Container(
          width: 4,
          color: const Color(0xFFE9ECEF),
          child: MouseRegion(
            cursor: SystemMouseCursors.resizeColumn,
            child: GestureDetector(
              onPanUpdate: (details) {
                // Implement pane resizing
              },
              child: Container(),
            ),
          ),
        ),
        
        // Right Pane
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                left: BorderSide(
                  color: activePane == 'right' 
                      ? const Color(0xFFE67E22) 
                      : const Color(0xFFE9ECEF),
                  width: activePane == 'right' ? 2 : 1,
                ),
              ),
            ),
            child: FilePane(
              path: rightPath,
              isActive: activePane == 'right',
              paneId: 'right',
              onPathChanged: (newPath) => _updatePanePath('right', newPath),
              onPaneActivated: () => _setActivePane('right'),
            ),
          ),
        ),
      ],
    );
  }

  void _updatePanePath(String paneId, String newPath) {
    if (paneId == 'left') {
      ref.read(leftPanePathProvider.notifier).state = newPath;
    } else {
      ref.read(rightPanePathProvider.notifier).state = newPath;
    }
  }

  void _setActivePane(String paneId) {
    ref.read(activePaneProvider.notifier).state = paneId;
  }
}
