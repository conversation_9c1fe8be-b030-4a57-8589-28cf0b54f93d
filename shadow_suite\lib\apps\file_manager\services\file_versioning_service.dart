import 'dart:io';
import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';

class FileVersioningService {
  static const String _versionDir = '.file_versions';
  static const String _metadataFile = 'versions.json';
  
  static final StreamController<VersioningEvent> _eventController = 
      StreamController<VersioningEvent>.broadcast();
  
  static Stream<VersioningEvent> get eventStream => _eventController.stream;
  
  /// Create a new version of a file
  static Future<FileVersion> createVersion({
    required String filePath,
    String? comment,
    bool autoVersion = false,
  }) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw FileSystemException('File not found', filePath);
      }
      
      final stat = await file.stat();
      final versionId = DateTime.now().millisecondsSinceEpoch.toString();
      
      // Create version directory if it doesn't exist
      final versionDirPath = await _getVersionDirectory(filePath);
      final versionDir = Directory(versionDirPath);
      if (!await versionDir.exists()) {
        await versionDir.create(recursive: true);
      }
      
      // Copy file to version directory
      final versionFileName = '${versionId}_${file.path.split('/').last}';
      final versionFilePath = '$versionDirPath/$versionFileName';
      await file.copy(versionFilePath);
      
      // Create version metadata
      final version = FileVersion(
        id: versionId,
        originalPath: filePath,
        versionPath: versionFilePath,
        timestamp: DateTime.now(),
        size: stat.size,
        comment: comment,
        isAutoVersion: autoVersion,
        checksum: await _calculateChecksum(filePath),
      );
      
      // Save version metadata
      await _saveVersionMetadata(filePath, version);
      
      _eventController.add(VersioningEvent(
        type: VersioningEventType.versionCreated,
        filePath: filePath,
        version: version,
      ));
      
      return version;
      
    } catch (error) {
      debugPrint('Error creating version: $error');
      rethrow;
    }
  }
  
  /// Get all versions of a file
  static Future<List<FileVersion>> getFileVersions(String filePath) async {
    try {
      final metadataPath = await _getMetadataPath(filePath);
      final metadataFile = File(metadataPath);
      
      if (!await metadataFile.exists()) {
        return [];
      }
      
      final jsonContent = await metadataFile.readAsString();
      final data = jsonDecode(jsonContent) as Map<String, dynamic>;
      final versionsData = data['versions'] as List<dynamic>;
      
      return versionsData
          .map((v) => FileVersion.fromJson(v as Map<String, dynamic>))
          .toList()
        ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
      
    } catch (error) {
      debugPrint('Error getting file versions: $error');
      return [];
    }
  }
  
  /// Restore a file from a specific version
  static Future<bool> restoreVersion({
    required String filePath,
    required String versionId,
    bool createBackup = true,
  }) async {
    try {
      final versions = await getFileVersions(filePath);
      final version = versions.firstWhere((v) => v.id == versionId);
      
      // Create backup of current file if requested
      if (createBackup && await File(filePath).exists()) {
        await createVersion(
          filePath: filePath,
          comment: 'Auto-backup before restore',
          autoVersion: true,
        );
      }
      
      // Copy version file back to original location
      final versionFile = File(version.versionPath);
      if (!await versionFile.exists()) {
        throw FileSystemException('Version file not found', version.versionPath);
      }
      
      await versionFile.copy(filePath);
      
      _eventController.add(VersioningEvent(
        type: VersioningEventType.versionRestored,
        filePath: filePath,
        version: version,
      ));
      
      return true;
      
    } catch (error) {
      debugPrint('Error restoring version: $error');
      return false;
    }
  }
  
  /// Delete a specific version
  static Future<bool> deleteVersion({
    required String filePath,
    required String versionId,
  }) async {
    try {
      final versions = await getFileVersions(filePath);
      final versionIndex = versions.indexWhere((v) => v.id == versionId);
      
      if (versionIndex == -1) return false;
      
      final version = versions[versionIndex];
      
      // Delete version file
      final versionFile = File(version.versionPath);
      if (await versionFile.exists()) {
        await versionFile.delete();
      }
      
      // Remove from metadata
      versions.removeAt(versionIndex);
      await _saveAllVersionsMetadata(filePath, versions);
      
      _eventController.add(VersioningEvent(
        type: VersioningEventType.versionDeleted,
        filePath: filePath,
        version: version,
      ));
      
      return true;
      
    } catch (error) {
      debugPrint('Error deleting version: $error');
      return false;
    }
  }
  
  /// Clean up old versions based on retention policy
  static Future<int> cleanupVersions({
    required String filePath,
    int maxVersions = 10,
    Duration? maxAge,
  }) async {
    try {
      final versions = await getFileVersions(filePath);
      final toDelete = <FileVersion>[];
      
      // Remove versions exceeding max count
      if (versions.length > maxVersions) {
        toDelete.addAll(versions.skip(maxVersions));
      }
      
      // Remove versions exceeding max age
      if (maxAge != null) {
        final cutoffDate = DateTime.now().subtract(maxAge);
        toDelete.addAll(
          versions.where((v) => v.timestamp.isBefore(cutoffDate))
        );
      }
      
      // Remove duplicates
      final uniqueToDelete = toDelete.toSet().toList();
      
      // Delete versions
      for (final version in uniqueToDelete) {
        await deleteVersion(filePath: filePath, versionId: version.id);
      }
      
      return uniqueToDelete.length;
      
    } catch (error) {
      debugPrint('Error cleaning up versions: $error');
      return 0;
    }
  }
  
  /// Create backup of entire directory
  static Future<BackupResult> createDirectoryBackup({
    required String sourcePath,
    required String backupPath,
    bool includeSubdirectories = true,
    List<String> excludePatterns = const [],
  }) async {
    final startTime = DateTime.now();
    final backupId = startTime.millisecondsSinceEpoch.toString();
    
    try {
      final sourceDir = Directory(sourcePath);
      final backupDir = Directory('$backupPath/backup_$backupId');
      
      if (!await sourceDir.exists()) {
        throw FileSystemException('Source directory not found', sourcePath);
      }
      
      await backupDir.create(recursive: true);
      
      int filesCopied = 0;
      int totalSize = 0;
      final errors = <String>[];
      
      await for (final entity in sourceDir.list(recursive: includeSubdirectories)) {
        try {
          if (entity is File) {
            final relativePath = entity.path.substring(sourcePath.length);
            
            // Check exclude patterns
            if (_shouldExclude(relativePath, excludePatterns)) continue;
            
            final targetPath = '${backupDir.path}$relativePath';
            final targetDir = Directory(targetPath.substring(0, targetPath.lastIndexOf('/')));
            
            if (!await targetDir.exists()) {
              await targetDir.create(recursive: true);
            }
            
            await entity.copy(targetPath);
            
            final stat = await entity.stat();
            totalSize += stat.size;
            filesCopied++;
          }
        } catch (error) {
          errors.add('${entity.path}: $error');
        }
      }
      
      // Create backup metadata
      final metadata = {
        'id': backupId,
        'sourcePath': sourcePath,
        'backupPath': backupDir.path,
        'timestamp': startTime.toIso8601String(),
        'filesCopied': filesCopied,
        'totalSize': totalSize,
        'duration': DateTime.now().difference(startTime).inMilliseconds,
        'errors': errors,
      };
      
      final metadataFile = File('${backupDir.path}/backup_metadata.json');
      await metadataFile.writeAsString(jsonEncode(metadata));
      
      return BackupResult(
        id: backupId,
        sourcePath: sourcePath,
        backupPath: backupDir.path,
        timestamp: startTime,
        filesCopied: filesCopied,
        totalSize: totalSize,
        duration: DateTime.now().difference(startTime),
        errors: errors,
      );
      
    } catch (error) {
      debugPrint('Error creating directory backup: $error');
      rethrow;
    }
  }
  
  static Future<String> _getVersionDirectory(String filePath) async {
    final directory = filePath.substring(0, filePath.lastIndexOf('/'));
    return '$directory/$_versionDir';
  }
  
  static Future<String> _getMetadataPath(String filePath) async {
    final versionDir = await _getVersionDirectory(filePath);
    return '$versionDir/$_metadataFile';
  }
  
  static Future<void> _saveVersionMetadata(String filePath, FileVersion version) async {
    final versions = await getFileVersions(filePath);
    versions.add(version);
    await _saveAllVersionsMetadata(filePath, versions);
  }
  
  static Future<void> _saveAllVersionsMetadata(String filePath, List<FileVersion> versions) async {
    final metadataPath = await _getMetadataPath(filePath);
    final metadataFile = File(metadataPath);
    
    final data = {
      'filePath': filePath,
      'versions': versions.map((v) => v.toJson()).toList(),
    };
    
    await metadataFile.writeAsString(jsonEncode(data));
  }
  
  static Future<String> _calculateChecksum(String filePath) async {
    final file = File(filePath);
    final bytes = await file.readAsBytes();
    return bytes.hashCode.toString();
  }
  
  static bool _shouldExclude(String path, List<String> excludePatterns) {
    for (final pattern in excludePatterns) {
      if (path.contains(pattern)) return true;
    }
    return false;
  }
  
  static void dispose() {
    _eventController.close();
  }
}

/// File version representation
class FileVersion {
  final String id;
  final String originalPath;
  final String versionPath;
  final DateTime timestamp;
  final int size;
  final String? comment;
  final bool isAutoVersion;
  final String checksum;
  
  const FileVersion({
    required this.id,
    required this.originalPath,
    required this.versionPath,
    required this.timestamp,
    required this.size,
    this.comment,
    required this.isAutoVersion,
    required this.checksum,
  });
  
  Map<String, dynamic> toJson() => {
    'id': id,
    'originalPath': originalPath,
    'versionPath': versionPath,
    'timestamp': timestamp.toIso8601String(),
    'size': size,
    'comment': comment,
    'isAutoVersion': isAutoVersion,
    'checksum': checksum,
  };
  
  factory FileVersion.fromJson(Map<String, dynamic> json) => FileVersion(
    id: json['id'],
    originalPath: json['originalPath'],
    versionPath: json['versionPath'],
    timestamp: DateTime.parse(json['timestamp']),
    size: json['size'],
    comment: json['comment'],
    isAutoVersion: json['isAutoVersion'] ?? false,
    checksum: json['checksum'],
  );
}

/// Backup result
class BackupResult {
  final String id;
  final String sourcePath;
  final String backupPath;
  final DateTime timestamp;
  final int filesCopied;
  final int totalSize;
  final Duration duration;
  final List<String> errors;
  
  const BackupResult({
    required this.id,
    required this.sourcePath,
    required this.backupPath,
    required this.timestamp,
    required this.filesCopied,
    required this.totalSize,
    required this.duration,
    required this.errors,
  });
  
  bool get hasErrors => errors.isNotEmpty;
  double get successRate => filesCopied / (filesCopied + errors.length);
}

/// Versioning event
class VersioningEvent {
  final VersioningEventType type;
  final String filePath;
  final FileVersion version;
  
  const VersioningEvent({
    required this.type,
    required this.filePath,
    required this.version,
  });
}

enum VersioningEventType {
  versionCreated,
  versionRestored,
  versionDeleted,
}
