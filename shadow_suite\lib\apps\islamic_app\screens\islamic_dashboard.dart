import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../../../core/theme/app_theme.dart';
import '../services/islamic_providers.dart';
import '../models/athkar.dart';

class IslamicDashboard extends ConsumerWidget {
  const IslamicDashboard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final statisticsAsync = ref.watch(islamicStatisticsProvider);
    final dailyProgressAsync = ref.watch(dailyProgressProvider);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Islamic App'),
        automaticallyImplyLeading: false,
        backgroundColor: AppTheme.islamicAppColor,
        actions: [
          IconButton(
            onPressed: () {
              ref.invalidate(islamicStatisticsProvider);
              ref.invalidate(dailyProgressProvider);
            },
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWelcomeSection(context),
            const SizedBox(height: 32),
            _buildQuickStats(context, statisticsAsync),
            const SizedBox(height: 32),
            _buildDailyProgress(context, ref, dailyProgressAsync),
            const SizedBox(height: 32),
            _buildQuickActions(context, ref),
            const SizedBox(height: 32),
            _buildFeaturedSections(context, ref),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    final now = DateTime.now();
    final hour = now.hour;
    String greeting;

    if (hour < 12) {
      greeting = 'Good Morning';
    } else if (hour < 17) {
      greeting = 'Good Afternoon';
    } else {
      greeting = 'Good Evening';
    }

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.islamicAppColor,
            AppTheme.islamicAppColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Assalamu Alaikum',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '$greeting • ${_formatDate(now)}',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'May Allah bless your day with peace and guidance',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          const Icon(Icons.mosque, size: 64, color: Colors.white),
        ],
      ),
    );
  }

  Widget _buildQuickStats(
    BuildContext context,
    AsyncValue<Map<String, int>> statisticsAsync,
  ) {
    return statisticsAsync.when(
      data: (stats) => Row(
        children: [
          Expanded(
            child: _buildStatCard(
              context,
              'Bookmarks',
              stats['bookmarks']?.toString() ?? '0',
              Icons.bookmark,
              AppTheme.islamicAppColor,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildStatCard(
              context,
              'Completed Sessions',
              stats['completedSessions']?.toString() ?? '0',
              Icons.check_circle,
              AppTheme.islamicAppColor,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildStatCard(
              context,
              'Custom Athkar',
              stats['customAthkar']?.toString() ?? '0',
              Icons.auto_awesome,
              AppTheme.islamicAppColor,
            ),
          ),
        ],
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Text('Error: $error'),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 12),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDailyProgress(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<Map<AthkarCategory, int>> progressAsync,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Today\'s Athkar Progress',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            TextButton(
              onPressed: () {
                ref.read(islamicAppCurrentScreenProvider.notifier).state =
                    IslamicAppScreen.progressTracking;
              },
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        progressAsync.when(
          data: (progress) => Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  _buildProgressItem(
                    context,
                    'Morning Athkar',
                    progress[AthkarCategory.morning] ?? 0,
                    1,
                  ),
                  const SizedBox(height: 12),
                  _buildProgressItem(
                    context,
                    'Evening Athkar',
                    progress[AthkarCategory.evening] ?? 0,
                    1,
                  ),
                  const SizedBox(height: 12),
                  _buildProgressItem(
                    context,
                    'After Prayer',
                    progress[AthkarCategory.afterPrayer] ?? 0,
                    5,
                  ),
                ],
              ),
            ),
          ),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Text('Error: $error'),
        ),
      ],
    );
  }

  Widget _buildProgressItem(
    BuildContext context,
    String title,
    int completed,
    int target,
  ) {
    final progress = target > 0 ? completed / target : 0.0;

    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
          ),
        ),
        Expanded(
          flex: 3,
          child: LinearProgressIndicator(
            value: progress.clamp(0.0, 1.0),
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.islamicAppColor),
          ),
        ),
        const SizedBox(width: 12),
        Text(
          '$completed/$target',
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildQuickActions(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                context,
                'Start Dhikr',
                Icons.beenhere,
                AppTheme.islamicAppColor,
                () {
                  ref.read(islamicAppCurrentScreenProvider.notifier).state =
                      IslamicAppScreen.athkarCategories;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildActionButton(
                context,
                'Read Quran',
                Icons.menu_book,
                AppTheme.islamicAppColor,
                () {
                  ref.read(islamicAppCurrentScreenProvider.notifier).state =
                      IslamicAppScreen.surahList;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildActionButton(
                context,
                'Bookmarks',
                Icons.bookmark,
                AppTheme.islamicAppColor,
                () {
                  ref.read(islamicAppCurrentScreenProvider.notifier).state =
                      IslamicAppScreen.bookmarks;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.all(20),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      child: Column(
        children: [
          Icon(icon, size: 32),
          const SizedBox(height: 8),
          Text(title),
        ],
      ),
    );
  }

  Widget _buildFeaturedSections(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Explore', style: Theme.of(context).textTheme.headlineMedium),
        const SizedBox(height: 16),
        StaggeredGrid.count(
          crossAxisCount: 2,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          children: [
            _buildFeatureCard(
              context,
              'Athkar Categories',
              'Morning, Evening & Custom',
              Icons.category,
              AppTheme.islamicAppColor,
              () {
                ref.read(islamicAppCurrentScreenProvider.notifier).state =
                    IslamicAppScreen.athkarCategories;
              },
            ),
            _buildFeatureCard(
              context,
              'Quran Search',
              'Find verses & meanings',
              Icons.search,
              AppTheme.islamicAppColor,
              () {
                ref.read(islamicAppCurrentScreenProvider.notifier).state =
                    IslamicAppScreen.quranSearch;
              },
            ),
            _buildFeatureCard(
              context,
              'Progress Tracking',
              'View your spiritual journey',
              Icons.trending_up,
              AppTheme.islamicAppColor,
              () {
                ref.read(islamicAppCurrentScreenProvider.notifier).state =
                    IslamicAppScreen.progressTracking;
              },
            ),
            _buildFeatureCard(
              context,
              'Custom Athkar',
              'Create personal routines',
              Icons.auto_awesome,
              AppTheme.islamicAppColor,
              () {
                ref.read(islamicAppCurrentScreenProvider.notifier).state =
                    IslamicAppScreen.customAthkarCreator;
              },
            ),
            _buildFeatureCard(
              context,
              'Hadith Collections',
              'Authentic sayings of Prophet (ﷺ)',
              Icons.library_books,
              const Color(0xFF27AE60),
              () {
                ref.read(islamicAppCurrentScreenProvider.notifier).state =
                    IslamicAppScreen.hadithCollections;
              },
            ),
            _buildFeatureCard(
              context,
              'Tafseer',
              'Comprehensive Quran commentary',
              Icons.auto_stories,
              const Color(0xFFE67E22),
              () {
                ref.read(islamicAppCurrentScreenProvider.notifier).state =
                    IslamicAppScreen.tafseerList;
              },
            ),
            _buildFeatureCard(
              context,
              'Prayer Times',
              'Accurate prayer times',
              Icons.access_time,
              const Color(0xFF1ABC9C),
              () {
                ref.read(islamicAppCurrentScreenProvider.notifier).state =
                    IslamicAppScreen.prayerTimes;
              },
            ),
            _buildFeatureCard(
              context,
              'Qibla Compass',
              'Find direction to Mecca',
              Icons.explore,
              const Color(0xFFE74C3C),
              () {
                ref.read(islamicAppCurrentScreenProvider.notifier).state =
                    IslamicAppScreen.qiblaCompass;
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFeatureCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(icon, size: 32, color: color),
              const SizedBox(height: 12),
              Text(
                title,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];

    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }
}
