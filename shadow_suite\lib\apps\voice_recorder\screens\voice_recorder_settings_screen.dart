import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/voice_recording_models.dart';
import '../services/voice_recorder_service.dart';

/// Voice Recorder Settings Screen
class VoiceRecorderSettingsScreen extends ConsumerStatefulWidget {
  const VoiceRecorderSettingsScreen({super.key});

  @override
  ConsumerState<VoiceRecorderSettingsScreen> createState() => _VoiceRecorderSettingsScreenState();
}

class _VoiceRecorderSettingsScreenState extends ConsumerState<VoiceRecorderSettingsScreen> {
  late VoiceRecorderSettings _settings;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  void _loadSettings() {
    final service = ref.read(voiceRecorderServiceProvider);
    _settings = service.getSettings();
    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Voice Recorder Settings'),
        backgroundColor: Colors.blueGrey,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveSettings,
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Recording Settings
          _buildSectionHeader('Recording Settings'),
          _buildQualitySelector(),
          _buildFormatSelector(),
          _buildSliderTile(
            'Recording Gain',
            'Adjust microphone sensitivity',
            _settings.recordingGain,
            0.1,
            2.0,
            (value) => setState(() => _settings = _settings.copyWith(recordingGain: value)),
            '${(_settings.recordingGain * 100).toInt()}%',
          ),
          _buildSliderTile(
            'Max Recording Duration',
            'Maximum recording length in minutes',
            _settings.maxRecordingDuration.toDouble(),
            1.0,
            180.0,
            (value) => setState(() => _settings = _settings.copyWith(maxRecordingDuration: value.round())),
            '${_settings.maxRecordingDuration} min',
          ),
          _buildSwitchTile(
            'Noise Reduction',
            'Apply noise reduction during recording',
            _settings.noiseReduction,
            (value) => setState(() => _settings = _settings.copyWith(noiseReduction: value)),
          ),
          
          const SizedBox(height: 24),
          
          // Playback Settings
          _buildSectionHeader('Playback & Display'),
          _buildSwitchTile(
            'Show Waveform',
            'Display audio waveform during recording',
            _settings.showWaveform,
            (value) => setState(() => _settings = _settings.copyWith(showWaveform: value)),
          ),
          _buildSwitchTile(
            'Auto Save',
            'Automatically save recordings when stopped',
            _settings.autoSave,
            (value) => setState(() => _settings = _settings.copyWith(autoSave: value)),
          ),
          
          const SizedBox(height: 24),
          
          // Storage Settings
          _buildSectionHeader('Storage & Organization'),
          _buildStorageLocationSelector(),
          
          const SizedBox(height: 24),
          
          // Advanced Features
          _buildSectionHeader('Advanced Features'),
          _buildSwitchTile(
            'Enable Transcription',
            'Convert speech to text (requires internet)',
            _settings.enableTranscription,
            (value) => setState(() => _settings = _settings.copyWith(enableTranscription: value)),
          ),
          
          const SizedBox(height: 24),
          
          // Data Management
          _buildSectionHeader('Data Management'),
          _buildActionTile(
            'Export All Recordings',
            'Export recordings to external storage',
            Icons.download,
            _exportRecordings,
          ),
          _buildActionTile(
            'Clear Cache',
            'Remove temporary files and thumbnails',
            Icons.cleaning_services,
            _clearCache,
          ),
          _buildActionTile(
            'Delete All Recordings',
            'Permanently delete all recordings',
            Icons.delete_forever,
            _deleteAllRecordings,
            isDestructive: true,
          ),
          
          const SizedBox(height: 24),
          
          // App Info
          _buildSectionHeader('Information'),
          _buildInfoTile('Storage Used', '156 MB'),
          _buildInfoTile('Total Recordings', '24'),
          _buildInfoTile('Total Duration', '2h 45m'),
          
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16, top: 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
          color: Colors.blueGrey,
        ),
      ),
    );
  }

  Widget _buildQualitySelector() {
    return ListTile(
      title: const Text('Recording Quality'),
      subtitle: Text('Current: ${_settings.defaultQuality.name}'),
      trailing: DropdownButton<RecordingQuality>(
        value: _settings.defaultQuality,
        items: RecordingQuality.values.map((quality) {
          return DropdownMenuItem(
            value: quality,
            child: Text(quality.name.toUpperCase()),
          );
        }).toList(),
        onChanged: (value) {
          if (value != null) {
            setState(() => _settings = _settings.copyWith(defaultQuality: value));
          }
        },
      ),
    );
  }

  Widget _buildFormatSelector() {
    return ListTile(
      title: const Text('Audio Format'),
      subtitle: Text('Current: ${_settings.defaultFormat.name.toUpperCase()}'),
      trailing: DropdownButton<AudioFormat>(
        value: _settings.defaultFormat,
        items: AudioFormat.values.map((format) {
          return DropdownMenuItem(
            value: format,
            child: Text(format.name.toUpperCase()),
          );
        }).toList(),
        onChanged: (value) {
          if (value != null) {
            setState(() => _settings = _settings.copyWith(defaultFormat: value));
          }
        },
      ),
    );
  }

  Widget _buildStorageLocationSelector() {
    return ListTile(
      title: const Text('Storage Location'),
      subtitle: Text(_settings.defaultSaveLocation.isEmpty 
          ? 'Default app directory' 
          : _settings.defaultSaveLocation),
      trailing: const Icon(Icons.folder),
      onTap: _selectStorageLocation,
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      activeColor: Colors.blueGrey,
    );
  }

  Widget _buildSliderTile(
    String title,
    String subtitle,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
    String displayValue,
  ) {
    return Column(
      children: [
        ListTile(
          title: Text(title),
          subtitle: Text(subtitle),
          trailing: Text(displayValue),
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          onChanged: onChanged,
          activeColor: Colors.blueGrey,
        ),
      ],
    );
  }

  Widget _buildActionTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap, {
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? Colors.red : Colors.blueGrey,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isDestructive ? Colors.red : null,
        ),
      ),
      subtitle: Text(subtitle),
      onTap: onTap,
    );
  }

  Widget _buildInfoTile(String title, String value) {
    return ListTile(
      title: Text(title),
      trailing: Text(
        value,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
    );
  }

  void _selectStorageLocation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Storage Location'),
        content: const Text('Select where to save recordings'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() => _settings = _settings.copyWith(defaultSaveLocation: '/storage/recordings'));
            },
            child: const Text('Select'),
          ),
        ],
      ),
    );
  }

  void _saveSettings() async {
    await ref.read(voiceRecorderServiceProvider).updateSettings(_settings);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Settings saved successfully'),
        backgroundColor: Colors.blueGrey,
      ),
    );
  }

  void _exportRecordings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Recordings'),
        content: const Text('Export all recordings to external storage?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Recordings exported successfully')),
              );
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  void _clearCache() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cache'),
        content: const Text('Remove temporary files and free up space?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Cache cleared successfully')),
              );
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _deleteAllRecordings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete All Recordings'),
        content: const Text(
          'This will permanently delete all your recordings. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('All recordings deleted'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            child: const Text('Delete All'),
          ),
        ],
      ),
    );
  }
}
