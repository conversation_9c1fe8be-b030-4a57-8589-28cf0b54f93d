import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import '../services/voice_recorder_service.dart';
import '../models/voice_recording_models.dart';

class RecordingScreen extends ConsumerStatefulWidget {
  const RecordingScreen({super.key});

  @override
  ConsumerState<RecordingScreen> createState() => _RecordingScreenState();
}

class _RecordingScreenState extends ConsumerState<RecordingScreen> {
  final VoiceRecorderService _voiceService = VoiceRecorderService();
  RecordingSession? _currentSession;
  Timer? _uiUpdateTimer;
  String _recordingTime = '00:00';

  @override
  void initState() {
    super.initState();
    _voiceService.sessionNotifier.addListener(_onSessionChanged);
    _startUIUpdateTimer();
  }

  @override
  void dispose() {
    _voiceService.sessionNotifier.removeListener(_onSessionChanged);
    _uiUpdateTimer?.cancel();
    super.dispose();
  }

  void _onSessionChanged() {
    if (mounted) {
      setState(() {
        _currentSession = _voiceService.sessionNotifier.value;
      });
    }
  }

  void _startUIUpdateTimer() {
    _uiUpdateTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (_currentSession?.status == RecordingStatus.recording) {
        final elapsed = _currentSession!.currentDuration;
        final minutes = elapsed.inMinutes.toString().padLeft(2, '0');
        final seconds = (elapsed.inSeconds % 60).toString().padLeft(2, '0');
        if (mounted) {
          setState(() {
            _recordingTime = '$minutes:$seconds';
          });
        }
      }
    });
  }

  bool get _isRecording => _currentSession?.status == RecordingStatus.recording;
  bool get _isPaused => _currentSession?.status == RecordingStatus.paused;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Record Audio'),
        backgroundColor: Colors.blueGrey,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Recording Status
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              decoration: BoxDecoration(
                color: _isRecording
                    ? Colors.red.withValues(alpha: 0.1)
                    : Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                _isRecording ? (_isPaused ? 'PAUSED' : 'RECORDING') : 'READY',
                style: TextStyle(
                  color: _isRecording ? Colors.red : Colors.grey,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
            const SizedBox(height: 40),

            // Recording Time
            Text(
              _recordingTime,
              style: const TextStyle(
                fontSize: 48,
                fontWeight: FontWeight.bold,
                color: Colors.blueGrey,
              ),
            ),
            const SizedBox(height: 60),

            // Recording Controls
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Stop Button
                if (_isRecording)
                  _buildControlButton(
                    Icons.stop,
                    Colors.grey,
                    () => _stopRecording(),
                  ),

                // Record/Pause Button
                _buildControlButton(
                  _isRecording
                      ? (_isPaused ? Icons.mic : Icons.pause)
                      : Icons.mic,
                  _isRecording
                      ? (_isPaused ? Colors.red : Colors.orange)
                      : Colors.red,
                  () => _toggleRecording(),
                  size: 80,
                ),
              ],
            ),
            const SizedBox(height: 40),

            // Recording Name Input
            if (_isRecording)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: TextField(
                  decoration: const InputDecoration(
                    labelText: 'Recording Name',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.edit),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlButton(
    IconData icon,
    Color color,
    VoidCallback onPressed, {
    double size = 60,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: color,
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.3),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(size / 2),
          onTap: onPressed,
          child: Center(
            child: Icon(icon, size: size * 0.4, color: Colors.white),
          ),
        ),
      ),
    );
  }

  Future<void> _toggleRecording() async {
    try {
      if (!_isRecording) {
        // Start recording
        await _voiceService.startRecording();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Recording started'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } else if (_isPaused) {
        // Resume recording
        await _voiceService.resumeRecording();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Recording resumed'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } else {
        // Pause recording
        await _voiceService.pauseRecording();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Recording paused'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _stopRecording() async {
    try {
      final recording = await _voiceService.stopRecording();
      if (mounted) {
        setState(() {
          _recordingTime = '00:00';
        });

        if (recording != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Recording saved: ${recording.title}'),
              backgroundColor: Colors.green,
            ),
          );
          // Navigate back to recordings list
          Navigator.pop(context);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Recording stopped'),
              backgroundColor: Colors.grey,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error stopping recording: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
