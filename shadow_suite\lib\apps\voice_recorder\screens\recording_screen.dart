import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class RecordingScreen extends ConsumerStatefulWidget {
  const RecordingScreen({super.key});

  @override
  ConsumerState<RecordingScreen> createState() => _RecordingScreenState();
}

class _RecordingScreenState extends ConsumerState<RecordingScreen> {
  bool _isRecording = false;
  bool _isPaused = false;
  String _recordingTime = '00:00';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Record Audio'),
        backgroundColor: Colors.blueGrey,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Recording Status
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              decoration: BoxDecoration(
                color: _isRecording
                    ? Colors.red.withValues(alpha: 0.1)
                    : Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                _isRecording ? (_isPaused ? 'PAUSED' : 'RECORDING') : 'READY',
                style: TextStyle(
                  color: _isRecording ? Colors.red : Colors.grey,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
            const SizedBox(height: 40),

            // Recording Time
            Text(
              _recordingTime,
              style: const TextStyle(
                fontSize: 48,
                fontWeight: FontWeight.bold,
                color: Colors.blueGrey,
              ),
            ),
            const SizedBox(height: 60),

            // Recording Controls
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Stop Button
                if (_isRecording)
                  _buildControlButton(
                    Icons.stop,
                    Colors.grey,
                    () => _stopRecording(),
                  ),

                // Record/Pause Button
                _buildControlButton(
                  _isRecording
                      ? (_isPaused ? Icons.mic : Icons.pause)
                      : Icons.mic,
                  _isRecording
                      ? (_isPaused ? Colors.red : Colors.orange)
                      : Colors.red,
                  () => _toggleRecording(),
                  size: 80,
                ),

                // Save Button
                if (_isRecording)
                  _buildControlButton(
                    Icons.save,
                    Colors.green,
                    () => _saveRecording(),
                  ),
              ],
            ),
            const SizedBox(height: 40),

            // Recording Name Input
            if (_isRecording)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: TextField(
                  decoration: const InputDecoration(
                    labelText: 'Recording Name',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.edit),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlButton(
    IconData icon,
    Color color,
    VoidCallback onPressed, {
    double size = 60,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: color,
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.3),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(size / 2),
          onTap: onPressed,
          child: Center(
            child: Icon(icon, size: size * 0.4, color: Colors.white),
          ),
        ),
      ),
    );
  }

  void _toggleRecording() {
    setState(() {
      if (!_isRecording) {
        _isRecording = true;
        _isPaused = false;
        _startTimer();
      } else {
        _isPaused = !_isPaused;
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          _isRecording
              ? (_isPaused ? 'Recording paused' : 'Recording started')
              : 'Recording stopped',
        ),
        backgroundColor: _isRecording ? Colors.red : Colors.grey,
      ),
    );
  }

  void _stopRecording() {
    setState(() {
      _isRecording = false;
      _isPaused = false;
      _recordingTime = '00:00';
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Recording stopped'),
        backgroundColor: Colors.grey,
      ),
    );
  }

  void _saveRecording() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Recording saved successfully'),
        backgroundColor: Colors.green,
      ),
    );

    setState(() {
      _isRecording = false;
      _isPaused = false;
      _recordingTime = '00:00';
    });
  }

  void _startTimer() {
    // Simulate timer for demo purposes
    Future.delayed(const Duration(seconds: 1), () {
      if (_isRecording && !_isPaused) {
        setState(() {
          final parts = _recordingTime.split(':');
          int minutes = int.parse(parts[0]);
          int seconds = int.parse(parts[1]);

          seconds++;
          if (seconds >= 60) {
            seconds = 0;
            minutes++;
          }

          _recordingTime =
              '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
        });
        _startTimer();
      }
    });
  }
}
