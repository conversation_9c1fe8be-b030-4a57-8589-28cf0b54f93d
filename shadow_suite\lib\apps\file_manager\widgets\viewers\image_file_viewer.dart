import 'package:flutter/material.dart';
import 'dart:typed_data';
import '../../services/file_viewer_service.dart';

class ImageFileViewer extends StatefulWidget {
  final String filePath;
  final VoidCallback? onClose;

  const ImageFileViewer({super.key, required this.filePath, this.onClose});

  @override
  State<ImageFileViewer> createState() => _ImageFileViewerState();
}

class _ImageFileViewerState extends State<ImageFileViewer> {
  Uint8List? _imageBytes;
  bool _isLoading = true;
  double _scale = 1.0;
  Size? _imageSize;
  FileMetadata? _metadata;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  Future<void> _loadImage() async {
    try {
      final bytes = await FileViewerService.readImageFile(widget.filePath);
      final metadata = await FileViewerService.getFileMetadata(widget.filePath);

      setState(() {
        _imageBytes = bytes;
        _metadata = metadata;
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _isLoading = false;
      });
      _showError('Error loading image: $error');
    }
  }

  @override
  Widget build(BuildContext context) {
    final fileName = widget.filePath.split('/').last;

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Row(
          children: [
            Icon(Icons.image, color: FileViewerService.getFileColor(fileName)),
            const SizedBox(width: 8),
            Expanded(
              child: Text(fileName, style: const TextStyle(fontSize: 16)),
            ),
          ],
        ),
        backgroundColor: Colors.black87,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.zoom_in),
            onPressed: _zoomIn,
            tooltip: 'Zoom In',
          ),
          IconButton(
            icon: const Icon(Icons.zoom_out),
            onPressed: _zoomOut,
            tooltip: 'Zoom Out',
          ),
          IconButton(
            icon: const Icon(Icons.zoom_out_map),
            onPressed: _resetZoom,
            tooltip: 'Fit to Screen',
          ),
          IconButton(
            icon: const Icon(Icons.info),
            onPressed: _showImageInfo,
            tooltip: 'Image Info',
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              widget.onClose?.call();
              Navigator.pop(context);
            },
            tooltip: 'Close',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : _imageBytes == null
          ? const Center(
              child: Text(
                'Failed to load image',
                style: TextStyle(color: Colors.white),
              ),
            )
          : _buildImageViewer(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildImageViewer() {
    return InteractiveViewer(
      minScale: 0.1,
      maxScale: 5.0,
      onInteractionUpdate: (details) {
        setState(() {
          _scale = details.scale;
        });
      },
      child: Center(
        child: Image.memory(
          _imageBytes!,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.broken_image, size: 64, color: Colors.white54),
                  SizedBox(height: 16),
                  Text(
                    'Unable to display image',
                    style: TextStyle(color: Colors.white54),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      height: 60,
      decoration: const BoxDecoration(
        color: Colors.black87,
        border: Border(top: BorderSide(color: Colors.white24)),
      ),
      child: Row(
        children: [
          const SizedBox(width: 16),
          Text(
            'Zoom: ${(_scale * 100).toStringAsFixed(0)}%',
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
          const SizedBox(width: 16),
          if (_metadata != null) ...[
            Text(
              'Size: ${_metadata!.formattedSize}',
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
            const SizedBox(width: 16),
          ],
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.share, color: Colors.white),
            onPressed: _shareImage,
            tooltip: 'Share',
          ),
          IconButton(
            icon: const Icon(Icons.save_alt, color: Colors.white),
            onPressed: _saveImageAs,
            tooltip: 'Save As',
          ),
        ],
      ),
    );
  }

  void _zoomIn() {
    setState(() {
      _scale = (_scale * 1.2).clamp(0.1, 5.0);
    });
  }

  void _zoomOut() {
    setState(() {
      _scale = (_scale / 1.2).clamp(0.1, 5.0);
    });
  }

  void _resetZoom() {
    setState(() {
      _scale = 1.0;
    });
  }

  void _showImageInfo() {
    if (_metadata == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Image Information'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('Name', _metadata!.name),
            _buildInfoRow('Size', _metadata!.formattedSize),
            _buildInfoRow('Type', _metadata!.mimeType),
            _buildInfoRow('Modified', _formatDate(_metadata!.lastModified)),
            _buildInfoRow('Path', _metadata!.path),
            if (_imageSize != null) ...[
              _buildInfoRow(
                'Dimensions',
                '${_imageSize!.width.toInt()} × ${_imageSize!.height.toInt()}',
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _shareImage() {
    // Implement image sharing functionality
    try {
      // In a real implementation, this would use the share package
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Sharing ${widget.filePath.split('/').last}...'),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error sharing image: $e')));
    }
    _showInfo('Share functionality coming soon');
  }

  void _saveImageAs() {
    // Implement save as functionality
    try {
      // In a real implementation, this would use file_picker to save the image
      final fileName = widget.filePath.split('/').last;
      _showInfo('Saving $fileName to Downloads...');
      // Could implement actual file saving here
    } catch (e) {
      _showError('Error saving image: $e');
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  void _showInfo(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF17A2B8),
      ),
    );
  }
}
