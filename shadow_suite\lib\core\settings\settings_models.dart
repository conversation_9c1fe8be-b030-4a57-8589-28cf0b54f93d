

// Settings category model
class SettingsCategory {
  final String id;
  final String name;
  final String description;
  final String icon;
  final int order;
  final List<SettingsItem> settings;

  const SettingsCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.order,
    required this.settings,
  });

  factory SettingsCategory.fromJson(Map<String, dynamic> json) {
    return SettingsCategory(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      icon: json['icon'] as String,
      order: json['order'] as int,
      settings: (json['settings'] as List<dynamic>?)
          ?.map((e) => SettingsItem.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon,
      'order': order,
      'settings': settings.map((e) => e.to<PERSON>son()).toList(),
    };
  }
}

// Individual settings item model
class SettingsItem {
  final String id;
  final String name;
  final String description;
  final SettingsType type;
  final dynamic defaultValue;
  final List<String>? options;
  final List<String>? optionLabels;
  final double? minValue;
  final double? maxValue;
  final String? unit;
  final bool isRequired;
  final bool isVisible;
  final String category;
  final Map<String, dynamic>? validation;
  final Map<String, dynamic>? metadata;

  const SettingsItem({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.defaultValue,
    this.options,
    this.optionLabels,
    this.minValue,
    this.maxValue,
    this.unit,
    this.isRequired = false,
    this.isVisible = true,
    required this.category,
    this.validation,
    this.metadata,
  });

  factory SettingsItem.fromJson(Map<String, dynamic> json) {
    return SettingsItem(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: SettingsType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => SettingsType.text,
      ),
      defaultValue: json['default_value'],
      options: (json['options'] as List<dynamic>?)?.cast<String>(),
      optionLabels: (json['option_labels'] as List<dynamic>?)?.cast<String>(),
      minValue: (json['min_value'] as num?)?.toDouble(),
      maxValue: (json['max_value'] as num?)?.toDouble(),
      unit: json['unit'] as String?,
      isRequired: json['is_required'] as bool? ?? false,
      isVisible: json['is_visible'] as bool? ?? true,
      category: json['category'] as String,
      validation: json['validation'] as Map<String, dynamic>?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'default_value': defaultValue,
      'options': options,
      'option_labels': optionLabels,
      'min_value': minValue,
      'max_value': maxValue,
      'unit': unit,
      'is_required': isRequired,
      'is_visible': isVisible,
      'category': category,
      'validation': validation,
      'metadata': metadata,
    };
  }

  SettingsItem copyWith({
    String? name,
    String? description,
    SettingsType? type,
    dynamic defaultValue,
    List<String>? options,
    List<String>? optionLabels,
    double? minValue,
    double? maxValue,
    String? unit,
    bool? isRequired,
    bool? isVisible,
    Map<String, dynamic>? validation,
    Map<String, dynamic>? metadata,
  }) {
    return SettingsItem(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      defaultValue: defaultValue ?? this.defaultValue,
      options: options ?? this.options,
      optionLabels: optionLabels ?? this.optionLabels,
      minValue: minValue ?? this.minValue,
      maxValue: maxValue ?? this.maxValue,
      unit: unit ?? this.unit,
      isRequired: isRequired ?? this.isRequired,
      isVisible: isVisible ?? this.isVisible,
      category: category,
      validation: validation ?? this.validation,
      metadata: metadata ?? this.metadata,
    );
  }

  bool validate(dynamic value) {
    if (validation == null) return true;

    // Required validation
    if (isRequired && (value == null || value.toString().isEmpty)) {
      return false;
    }

    // Type-specific validation
    switch (type) {
      case SettingsType.number:
      case SettingsType.slider:
        if (value is! num) return false;
        if (minValue != null && value < minValue!) return false;
        if (maxValue != null && value > maxValue!) return false;
        break;
      
      case SettingsType.text:
      case SettingsType.password:
        if (value is! String) return false;
        final minLength = validation!['min_length'] as int?;
        final maxLength = validation!['max_length'] as int?;
        if (minLength != null && value.length < minLength) return false;
        if (maxLength != null && value.length > maxLength) return false;
        break;
      
      case SettingsType.dropdown:
        if (options != null && !options!.contains(value.toString())) return false;
        break;
      
      case SettingsType.email:
        if (value is! String) return false;
        final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
        if (!emailRegex.hasMatch(value)) return false;
        break;
      
      default:
        break;
    }

    return true;
  }
}

// Settings change event model
class SettingsChangeEvent {
  final String settingId;
  final dynamic oldValue;
  final dynamic newValue;
  final DateTime timestamp;

  const SettingsChangeEvent({
    required this.settingId,
    required this.oldValue,
    required this.newValue,
    required this.timestamp,
  });

  factory SettingsChangeEvent.fromJson(Map<String, dynamic> json) {
    return SettingsChangeEvent(
      settingId: json['setting_id'] as String,
      oldValue: json['old_value'],
      newValue: json['new_value'],
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'setting_id': settingId,
      'old_value': oldValue,
      'new_value': newValue,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

// Settings backup model
class SettingsBackup {
  final String id;
  final String name;
  final String description;
  final Map<String, dynamic> settings;
  final DateTime createdAt;
  final String version;
  final Map<String, dynamic> metadata;

  const SettingsBackup({
    required this.id,
    required this.name,
    required this.description,
    required this.settings,
    required this.createdAt,
    required this.version,
    required this.metadata,
  });

  factory SettingsBackup.fromJson(Map<String, dynamic> json) {
    return SettingsBackup(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      settings: Map<String, dynamic>.from(json['settings'] as Map),
      createdAt: DateTime.parse(json['created_at'] as String),
      version: json['version'] as String,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'settings': settings,
      'created_at': createdAt.toIso8601String(),
      'version': version,
      'metadata': metadata,
    };
  }
}

// Settings profile model for different user scenarios
class SettingsProfile {
  final String id;
  final String name;
  final String description;
  final SettingsProfileType type;
  final Map<String, dynamic> settings;
  final bool isActive;
  final DateTime createdAt;
  final DateTime lastModified;

  const SettingsProfile({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.settings,
    required this.isActive,
    required this.createdAt,
    required this.lastModified,
  });

  factory SettingsProfile.fromJson(Map<String, dynamic> json) {
    return SettingsProfile(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: SettingsProfileType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => SettingsProfileType.custom,
      ),
      settings: Map<String, dynamic>.from(json['settings'] as Map),
      isActive: json['is_active'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastModified: DateTime.parse(json['last_modified'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'settings': settings,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'last_modified': lastModified.toIso8601String(),
    };
  }

  SettingsProfile copyWith({
    String? name,
    String? description,
    SettingsProfileType? type,
    Map<String, dynamic>? settings,
    bool? isActive,
  }) {
    return SettingsProfile(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      settings: settings ?? this.settings,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt,
      lastModified: DateTime.now(),
    );
  }
}

// Settings validation result
class SettingsValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const SettingsValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  factory SettingsValidationResult.valid() {
    return const SettingsValidationResult(
      isValid: true,
      errors: [],
      warnings: [],
    );
  }

  factory SettingsValidationResult.invalid(List<String> errors, [List<String>? warnings]) {
    return SettingsValidationResult(
      isValid: false,
      errors: errors,
      warnings: warnings ?? [],
    );
  }
}

// Enums
enum SettingsType {
  text,
  number,
  toggle,
  dropdown,
  slider,
  color,
  date,
  time,
  datetime,
  file,
  folder,
  email,
  url,
  password,
  multiline,
  tags,
  json,
}

enum SettingsProfileType {
  default_,
  accessibility,
  performance,
  minimal,
  powerUser,
  custom,
}

// Settings search and filter models
class SettingsSearchResult {
  final SettingsItem item;
  final SettingsCategory category;
  final double relevanceScore;
  final List<String> matchedTerms;

  const SettingsSearchResult({
    required this.item,
    required this.category,
    required this.relevanceScore,
    required this.matchedTerms,
  });
}

class SettingsFilter {
  final List<SettingsType>? types;
  final List<String>? categories;
  final bool? hasOptions;
  final bool? isRequired;
  final bool? isVisible;
  final String? searchQuery;

  const SettingsFilter({
    this.types,
    this.categories,
    this.hasOptions,
    this.isRequired,
    this.isVisible,
    this.searchQuery,
  });

  bool matches(SettingsItem item, SettingsCategory category) {
    if (types != null && !types!.contains(item.type)) return false;
    if (categories != null && !categories!.contains(category.id)) return false;
    if (hasOptions != null && (item.options != null) != hasOptions!) return false;
    if (isRequired != null && item.isRequired != isRequired!) return false;
    if (isVisible != null && item.isVisible != isVisible!) return false;
    
    if (searchQuery != null && searchQuery!.isNotEmpty) {
      final query = searchQuery!.toLowerCase();
      final nameMatch = item.name.toLowerCase().contains(query);
      final descMatch = item.description.toLowerCase().contains(query);
      final categoryMatch = category.name.toLowerCase().contains(query);
      
      if (!nameMatch && !descMatch && !categoryMatch) return false;
    }
    
    return true;
  }
}

// Settings import/export models
class SettingsExportOptions {
  final List<String>? categories;
  final bool includeDefaults;
  final bool includeMetadata;
  final SettingsExportFormat format;

  const SettingsExportOptions({
    this.categories,
    this.includeDefaults = false,
    this.includeMetadata = true,
    this.format = SettingsExportFormat.json,
  });
}

class SettingsImportResult {
  final bool success;
  final int importedCount;
  final int skippedCount;
  final List<String> errors;
  final List<String> warnings;

  const SettingsImportResult({
    required this.success,
    required this.importedCount,
    required this.skippedCount,
    required this.errors,
    required this.warnings,
  });
}

enum SettingsExportFormat {
  json,
  yaml,
  xml,
  csv,
}
