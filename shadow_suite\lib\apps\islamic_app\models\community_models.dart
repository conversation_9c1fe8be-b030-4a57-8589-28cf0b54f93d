

// Prayer Group Model
class PrayerGroup {
  final String id;
  final String name;
  final String description;
  final String location;
  final List<String> prayerTimes;
  final String contactInfo;
  final int maxMembers;
  final int currentMembers;
  final bool isActive;
  final DateTime createdAt;
  final DateTime lastModified;
  final Map<String, dynamic> metadata;

  const PrayerGroup({
    required this.id,
    required this.name,
    required this.description,
    required this.location,
    required this.prayerTimes,
    required this.contactInfo,
    required this.maxMembers,
    required this.currentMembers,
    required this.isActive,
    required this.createdAt,
    required this.lastModified,
    required this.metadata,
  });

  factory PrayerGroup.fromJson(Map<String, dynamic> json) {
    return PrayerGroup(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      location: json['location'] as String,
      prayerTimes: List<String>.from(json['prayer_times'] as List? ?? []),
      contactInfo: json['contact_info'] as String,
      maxMembers: json['max_members'] as int,
      currentMembers: json['current_members'] as int,
      isActive: json['is_active'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastModified: DateTime.parse(json['last_modified'] as String),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'location': location,
      'prayer_times': prayerTimes,
      'contact_info': contactInfo,
      'max_members': maxMembers,
      'current_members': currentMembers,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'last_modified': lastModified.toIso8601String(),
      'metadata': metadata,
    };
  }
}

// Mosque Information Model
class MosqueInfo {
  final String id;
  final String name;
  final String address;
  final double latitude;
  final double longitude;
  final String? phone;
  final String? website;
  final String? imam;
  final List<String> services;
  final Map<String, String> prayerSchedule;
  final double rating;
  final List<MosqueReview> reviews;
  final bool isVerified;
  final DateTime addedAt;
  final Map<String, dynamic> metadata;

  const MosqueInfo({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    this.phone,
    this.website,
    this.imam,
    required this.services,
    required this.prayerSchedule,
    required this.rating,
    required this.reviews,
    required this.isVerified,
    required this.addedAt,
    required this.metadata,
  });

  factory MosqueInfo.fromJson(Map<String, dynamic> json) {
    return MosqueInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      address: json['address'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      phone: json['phone'] as String?,
      website: json['website'] as String?,
      imam: json['imam'] as String?,
      services: List<String>.from(json['services'] as List? ?? []),
      prayerSchedule: Map<String, String>.from(json['prayer_schedule'] as Map? ?? {}),
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      reviews: (json['reviews'] as List<dynamic>?)
          ?.map((e) => MosqueReview.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      isVerified: json['is_verified'] as bool? ?? false,
      addedAt: DateTime.parse(json['added_at'] as String),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'phone': phone,
      'website': website,
      'imam': imam,
      'services': services,
      'prayer_schedule': prayerSchedule,
      'rating': rating,
      'reviews': reviews.map((e) => e.toJson()).toList(),
      'is_verified': isVerified,
      'added_at': addedAt.toIso8601String(),
      'metadata': metadata,
    };
  }
}

// Mosque Review Model
class MosqueReview {
  final String id;
  final String mosqueId;
  final String reviewerName;
  final double rating;
  final String comment;
  final DateTime createdAt;

  const MosqueReview({
    required this.id,
    required this.mosqueId,
    required this.reviewerName,
    required this.rating,
    required this.comment,
    required this.createdAt,
  });

  factory MosqueReview.fromJson(Map<String, dynamic> json) {
    return MosqueReview(
      id: json['id'] as String,
      mosqueId: json['mosque_id'] as String,
      reviewerName: json['reviewer_name'] as String,
      rating: (json['rating'] as num).toDouble(),
      comment: json['comment'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'mosque_id': mosqueId,
      'reviewer_name': reviewerName,
      'rating': rating,
      'comment': comment,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Islamic Event Model
class IslamicEvent {
  final String id;
  final String title;
  final String description;
  final DateTime startDate;
  final DateTime endDate;
  final String location;
  final IslamicEventCategory category;
  final String? organizer;
  final bool isRecurring;
  final List<String> attendees;
  final int maxAttendees;
  final bool isActive;
  final Map<String, dynamic> details;
  final DateTime createdAt;

  const IslamicEvent({
    required this.id,
    required this.title,
    required this.description,
    required this.startDate,
    required this.endDate,
    required this.location,
    required this.category,
    this.organizer,
    required this.isRecurring,
    required this.attendees,
    required this.maxAttendees,
    required this.isActive,
    required this.details,
    required this.createdAt,
  });

  factory IslamicEvent.fromJson(Map<String, dynamic> json) {
    return IslamicEvent(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      startDate: DateTime.parse(json['start_date'] as String),
      endDate: DateTime.parse(json['end_date'] as String),
      location: json['location'] as String,
      category: IslamicEventCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => IslamicEventCategory.general,
      ),
      organizer: json['organizer'] as String?,
      isRecurring: json['is_recurring'] as bool,
      attendees: List<String>.from(json['attendees'] as List? ?? []),
      maxAttendees: json['max_attendees'] as int,
      isActive: json['is_active'] as bool,
      details: Map<String, dynamic>.from(json['details'] as Map? ?? {}),
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'location': location,
      'category': category.name,
      'organizer': organizer,
      'is_recurring': isRecurring,
      'attendees': attendees,
      'max_attendees': maxAttendees,
      'is_active': isActive,
      'details': details,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Community Member Model
class CommunityMember {
  final String id;
  final String name;
  final String email;
  final String? phone;
  final String? location;
  final List<String> interests;
  final MemberRole role;
  final DateTime joinedAt;
  final bool isActive;
  final int contributions;
  final int reputation;
  final Map<String, dynamic> metadata;

  const CommunityMember({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    this.location,
    required this.interests,
    required this.role,
    required this.joinedAt,
    required this.isActive,
    required this.contributions,
    required this.reputation,
    required this.metadata,
  });

  factory CommunityMember.fromJson(Map<String, dynamic> json) {
    return CommunityMember(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String?,
      location: json['location'] as String?,
      interests: List<String>.from(json['interests'] as List? ?? []),
      role: MemberRole.values.firstWhere(
        (e) => e.name == json['role'],
        orElse: () => MemberRole.member,
      ),
      joinedAt: DateTime.parse(json['joined_at'] as String),
      isActive: json['is_active'] as bool,
      contributions: json['contributions'] as int,
      reputation: json['reputation'] as int,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'location': location,
      'interests': interests,
      'role': role.name,
      'joined_at': joinedAt.toIso8601String(),
      'is_active': isActive,
      'contributions': contributions,
      'reputation': reputation,
      'metadata': metadata,
    };
  }
}

// Dua Collection Model
class DuaCollection {
  final String id;
  final String name;
  final String description;
  final DuaCategory category;
  final String language;
  final List<Dua> duas;
  final bool isOfficial;
  final int downloadCount;
  final double rating;
  final DateTime createdAt;
  final DateTime lastModified;

  const DuaCollection({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.language,
    required this.duas,
    required this.isOfficial,
    required this.downloadCount,
    required this.rating,
    required this.createdAt,
    required this.lastModified,
  });

  factory DuaCollection.fromJson(Map<String, dynamic> json) {
    return DuaCollection(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      category: DuaCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => DuaCategory.general,
      ),
      language: json['language'] as String,
      duas: (json['duas'] as List<dynamic>?)
          ?.map((e) => Dua.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      isOfficial: json['is_official'] as bool,
      downloadCount: json['download_count'] as int,
      rating: (json['rating'] as num).toDouble(),
      createdAt: DateTime.parse(json['created_at'] as String),
      lastModified: DateTime.parse(json['last_modified'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category.name,
      'language': language,
      'duas': duas.map((e) => e.toJson()).toList(),
      'is_official': isOfficial,
      'download_count': downloadCount,
      'rating': rating,
      'created_at': createdAt.toIso8601String(),
      'last_modified': lastModified.toIso8601String(),
    };
  }
}

// Individual Dua Model
class Dua {
  final String id;
  final String title;
  final String arabicText;
  final String transliteration;
  final String translation;
  final DuaCategory category;
  final String source;
  final List<String> benefits;
  final String? audioPath;

  const Dua({
    required this.id,
    required this.title,
    required this.arabicText,
    required this.transliteration,
    required this.translation,
    required this.category,
    required this.source,
    required this.benefits,
    this.audioPath,
  });

  factory Dua.fromJson(Map<String, dynamic> json) {
    return Dua(
      id: json['id'] as String,
      title: json['title'] as String,
      arabicText: json['arabic_text'] as String,
      transliteration: json['transliteration'] as String,
      translation: json['translation'] as String,
      category: DuaCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => DuaCategory.general,
      ),
      source: json['source'] as String,
      benefits: List<String>.from(json['benefits'] as List? ?? []),
      audioPath: json['audio_path'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'arabic_text': arabicText,
      'transliteration': transliteration,
      'translation': translation,
      'category': category.name,
      'source': source,
      'benefits': benefits,
      'audio_path': audioPath,
    };
  }
}

// Enums
enum IslamicEventCategory {
  general,
  educational,
  charity,
  social,
  religious,
  youth,
  family,
  community,
}

enum MemberRole {
  member,
  moderator,
  admin,
  scholar,
  volunteer,
}

enum DuaCategory {
  general,
  daily,
  morning,
  evening,
  prayer,
  travel,
  food,
  sleep,
  protection,
  forgiveness,
  gratitude,
  health,
  family,
  work,
}
