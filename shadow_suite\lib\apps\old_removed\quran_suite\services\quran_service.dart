import '../models/quran_models.dart';
import 'quran_data_service.dart';

/// Main Quran service that provides high-level API for Quran functionality
/// This service acts as a facade for the QuranDataService
class QuranService {
  final QuranDataService _dataService = QuranDataService();

  /// Initialize the Quran service
  Future<void> initialize() async {
    await _dataService.initialize();
  }

  /// Get all Surahs for the current text version
  Future<List<Surah>> getSurahs() async {
    return await _dataService.getSurahs();
  }

  /// Get all Surahs (synchronous version for compatibility)
  List<Surah> getAllSurahs() {
    return _dataService.getChapters();
  }

  /// Get a specific Surah by number
  Future<Surah?> getSurah(int surahNumber) async {
    return await _dataService.getSurah(surahNumber);
  }

  /// Get verses for a specific Surah
  Future<List<Verse>> getVerses(int surahNumber) async {
    return await _dataService.getVerses(surahNumber);
  }

  /// Get a specific verse
  Future<Verse?> getVerse(int surahNumber, int verseNumber) async {
    return _dataService.getVerse(surahNumber, verseNumber);
  }

  /// Search verses by text
  Future<List<SearchResult>> searchVerses(String query) async {
    final verses = _dataService.searchVerses(query);
    // Convert verses to SearchResult objects
    return verses
        .map(
          (verse) => SearchResult(
            surahNumber: 1, // This would be determined from the verse context
            verseNumber: verse.number,
            verse: verse,
            matchType: SearchMatchType.arabic,
            relevanceScore: 1.0, // Default relevance score
          ),
        )
        .toList();
  }

  /// Get Tafseer for a verse
  Future<List<Tafseer>> getTafseer(int surahNumber, int verseNumber) async {
    return _dataService.getTafseer(surahNumber, verseNumber);
  }

  /// Get bookmarks
  Future<List<BookmarkData>> getBookmarks() async {
    return await _dataService.getBookmarks();
  }

  /// Add bookmark
  Future<void> addBookmark(BookmarkData bookmark) async {
    await _dataService.addBookmark(bookmark);
  }

  /// Remove bookmark
  Future<void> removeBookmark(String bookmarkId) async {
    await _dataService.removeBookmark(bookmarkId);
  }

  /// Get reading settings
  ReadingSettings getReadingSettings() {
    return _dataService.getReadingSettings();
  }

  /// Update reading settings
  Future<void> updateReadingSettings(ReadingSettings settings) async {
    await _dataService.updateReadingSettings(settings);
  }

  /// Change text version
  Future<void> changeTextVersion(TextVersion version) async {
    await _dataService.changeTextVersion(version);
  }

  /// Get current text version
  TextVersion getCurrentTextVersion() {
    return _dataService.getCurrentTextVersion();
  }

  /// Get Quran statistics
  Future<QuranStatistics> getStatistics() async {
    final statsMap = _dataService.getStatistics();
    return QuranStatistics(
      totalSurahs: statsMap['totalChapters'] ?? 0,
      totalVerses: statsMap['totalVerses'] ?? 0,
      totalWords: statsMap['totalWords'] ?? 0,
      totalLetters: statsMap['totalLetters'] ?? 0,
      currentVersion: _dataService.getCurrentTextVersion(),
    );
  }

  /// Check if service is initialized
  bool get isInitialized => _dataService.isInitialized;

  /// Get available text versions
  List<TextVersion> getAvailableTextVersions() {
    return TextVersion.values;
  }

  /// Get Surah metadata
  Future<SurahMetadata> getSurahMetadata(int surahNumber) async {
    final surah = await getSurah(surahNumber);
    if (surah == null) {
      throw Exception('Surah $surahNumber not found');
    }

    return SurahMetadata(
      number: surah.number,
      name: surah.name,
      englishName: surah.englishName,
      versesCount: surah.versesCount,
      revelationType: surah.revelationType,
      revelationOrder: surah.revelationOrder,
    );
  }

  /// Get verse context (previous and next verses)
  Future<VerseContext> getVerseContext(int surahNumber, int verseNumber) async {
    final surah = await getSurah(surahNumber);
    if (surah == null) {
      throw Exception('Surah $surahNumber not found');
    }

    Verse? previousVerse;
    Verse? nextVerse;

    if (verseNumber > 1) {
      previousVerse = await getVerse(surahNumber, verseNumber - 1);
    }

    if (verseNumber < surah.versesCount) {
      nextVerse = await getVerse(surahNumber, verseNumber + 1);
    }

    return VerseContext(
      currentVerse: await getVerse(surahNumber, verseNumber),
      previousVerse: previousVerse,
      nextVerse: nextVerse,
    );
  }
}

/// Metadata for a Surah
class SurahMetadata {
  final int number;
  final String name;
  final String englishName;
  final int versesCount;
  final RevelationType revelationType;
  final int revelationOrder;

  const SurahMetadata({
    required this.number,
    required this.name,
    required this.englishName,
    required this.versesCount,
    required this.revelationType,
    required this.revelationOrder,
  });
}

/// Context for a verse (previous and next verses)
class VerseContext {
  final Verse? currentVerse;
  final Verse? previousVerse;
  final Verse? nextVerse;

  const VerseContext({
    required this.currentVerse,
    this.previousVerse,
    this.nextVerse,
  });

  bool get hasPrevious => previousVerse != null;
  bool get hasNext => nextVerse != null;
}
