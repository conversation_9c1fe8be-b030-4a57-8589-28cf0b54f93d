import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/theme/app_theme.dart';
import '../services/file_encryption_service.dart';
import '../models/encryption_models.dart';

/// Advanced file security screen with encryption features
class FileSecurityScreen extends StatefulWidget {
  final String? initialFilePath;

  const FileSecurityScreen({super.key, this.initialFilePath});

  @override
  State<FileSecurityScreen> createState() => _FileSecurityScreenState();
}

class _FileSecurityScreenState extends State<FileSecurityScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _encryptionService = FileEncryptionService();

  // Form controllers
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _filePathController = TextEditingController();
  final _outputPathController = TextEditingController();

  // State variables
  bool _isProcessing = false;
  bool _showPassword = false;
  PasswordStrength? _passwordStrength;
  EncryptionOptions _encryptionOptions = const EncryptionOptions();
  final List<String> _selectedFiles = [];
  EncryptionStatistics? _statistics;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _encryptionService.initialize();

    if (widget.initialFilePath != null) {
      _filePathController.text = widget.initialFilePath!;
      _selectedFiles.add(widget.initialFilePath!);
    }

    _loadStatistics();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _filePathController.dispose();
    _outputPathController.dispose();
    super.dispose();
  }

  void _loadStatistics() {
    setState(() {
      _statistics = _encryptionService.getEncryptionStatistics();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('File Security'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showSecurityInfo,
            icon: const Icon(Icons.info_outline),
            tooltip: 'Security Information',
          ),
          IconButton(
            onPressed: _loadStatistics,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh Statistics',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.lock), text: 'Encrypt'),
            Tab(icon: Icon(Icons.lock_open), text: 'Decrypt'),
            Tab(icon: Icon(Icons.archive), text: 'Archive'),
            Tab(icon: Icon(Icons.analytics), text: 'Statistics'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildEncryptTab(),
          _buildDecryptTab(),
          _buildArchiveTab(),
          _buildStatisticsTab(),
        ],
      ),
    );
  }

  Widget _buildEncryptTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFileSelectionCard(),
          const SizedBox(height: 16),
          _buildPasswordCard(),
          const SizedBox(height: 16),
          _buildEncryptionOptionsCard(),
          const SizedBox(height: 16),
          _buildEncryptionActionsCard(),
        ],
      ),
    );
  }

  Widget _buildFileSelectionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.folder_open, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'File Selection',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _filePathController,
              decoration: InputDecoration(
                labelText: 'File Path',
                hintText: 'Select file to encrypt',
                border: const OutlineInputBorder(),
                suffixIcon: IconButton(
                  onPressed: _selectFile,
                  icon: const Icon(Icons.folder),
                ),
              ),
              readOnly: true,
            ),
            const SizedBox(height: 12),
            TextField(
              controller: _outputPathController,
              decoration: InputDecoration(
                labelText: 'Output Path (Optional)',
                hintText: 'Leave empty for auto-generated path',
                border: const OutlineInputBorder(),
                suffixIcon: IconButton(
                  onPressed: _selectOutputPath,
                  icon: const Icon(Icons.save),
                ),
              ),
            ),
            if (_selectedFiles.isNotEmpty) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Selected Files (${_selectedFiles.length})',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    ...(_selectedFiles
                        .take(3)
                        .map(
                          (file) => Padding(
                            padding: const EdgeInsets.symmetric(vertical: 2),
                            child: Row(
                              children: [
                                const Icon(Icons.insert_drive_file, size: 16),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    file.split('/').last,
                                    style: const TextStyle(fontSize: 12),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )),
                    if (_selectedFiles.length > 3)
                      Text(
                        '... and ${_selectedFiles.length - 3} more files',
                        style: const TextStyle(
                          fontSize: 12,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPasswordCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.security, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Password Security',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _passwordController,
              obscureText: !_showPassword,
              onChanged: _validatePassword,
              decoration: InputDecoration(
                labelText: 'Password',
                hintText: 'Enter a strong password',
                border: const OutlineInputBorder(),
                suffixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      onPressed: _generatePassword,
                      icon: const Icon(Icons.auto_awesome),
                      tooltip: 'Generate Password',
                    ),
                    IconButton(
                      onPressed: () =>
                          setState(() => _showPassword = !_showPassword),
                      icon: Icon(
                        _showPassword ? Icons.visibility_off : Icons.visibility,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: _confirmPasswordController,
              obscureText: !_showPassword,
              decoration: const InputDecoration(
                labelText: 'Confirm Password',
                hintText: 'Re-enter password',
                border: OutlineInputBorder(),
              ),
            ),
            if (_passwordStrength != null) ...[
              const SizedBox(height: 16),
              _buildPasswordStrengthIndicator(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPasswordStrengthIndicator() {
    final strength = _passwordStrength!;
    Color strengthColor;

    switch (strength.level) {
      case PasswordStrengthLevel.weak:
        strengthColor = Colors.red;
        break;
      case PasswordStrengthLevel.medium:
        strengthColor = Colors.orange;
        break;
      case PasswordStrengthLevel.strong:
        strengthColor = Colors.green;
        break;
      case PasswordStrengthLevel.veryStrong:
        strengthColor = Colors.blue;
        break;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Password Strength: ',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(
              strength.level.name.toUpperCase(),
              style: TextStyle(
                color: strengthColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: strength.percentage,
          backgroundColor: Colors.grey.shade300,
          valueColor: AlwaysStoppedAnimation<Color>(strengthColor),
        ),
        const SizedBox(height: 8),
        Text(
          strength.description,
          style: TextStyle(color: strengthColor, fontSize: 12),
        ),
        if (strength.suggestions.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            'Suggestions:',
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
          ),
          ...strength.suggestions.map(
            (suggestion) => Padding(
              padding: const EdgeInsets.only(left: 16, top: 2),
              child: Row(
                children: [
                  const Icon(Icons.arrow_right, size: 12),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      suggestion,
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildEncryptionOptionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.settings, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Encryption Options',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<EncryptionAlgorithm>(
              value: _encryptionOptions.algorithm,
              decoration: const InputDecoration(
                labelText: 'Algorithm',
                border: OutlineInputBorder(),
              ),
              items: EncryptionAlgorithm.values.map((algorithm) {
                return DropdownMenuItem(
                  value: algorithm,
                  child: Text(algorithm.name.toUpperCase()),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _encryptionOptions = EncryptionOptions(
                      algorithm: value,
                      keyDerivation: _encryptionOptions.keyDerivation,
                      iterations: _encryptionOptions.iterations,
                      enableCompression: _encryptionOptions.enableCompression,
                      enableIntegrityCheck:
                          _encryptionOptions.enableIntegrityCheck,
                    );
                  });
                }
              },
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    initialValue: _encryptionOptions.iterations.toString(),
                    decoration: const InputDecoration(
                      labelText: 'Key Iterations',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      final iterations = int.tryParse(value) ?? 100000;
                      setState(() {
                        _encryptionOptions = EncryptionOptions(
                          algorithm: _encryptionOptions.algorithm,
                          keyDerivation: _encryptionOptions.keyDerivation,
                          iterations: iterations,
                          enableCompression:
                              _encryptionOptions.enableCompression,
                          enableIntegrityCheck:
                              _encryptionOptions.enableIntegrityCheck,
                        );
                      });
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButtonFormField<KeyDerivationFunction>(
                    value: _encryptionOptions.keyDerivation,
                    decoration: const InputDecoration(
                      labelText: 'Key Derivation',
                      border: OutlineInputBorder(),
                    ),
                    items: KeyDerivationFunction.values.map((kdf) {
                      return DropdownMenuItem(
                        value: kdf,
                        child: Text(kdf.name.toUpperCase()),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _encryptionOptions = EncryptionOptions(
                            algorithm: _encryptionOptions.algorithm,
                            keyDerivation: value,
                            iterations: _encryptionOptions.iterations,
                            enableCompression:
                                _encryptionOptions.enableCompression,
                            enableIntegrityCheck:
                                _encryptionOptions.enableIntegrityCheck,
                          );
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Enable Compression'),
              subtitle: const Text('Reduce file size before encryption'),
              value: _encryptionOptions.enableCompression,
              onChanged: (value) {
                setState(() {
                  _encryptionOptions = EncryptionOptions(
                    algorithm: _encryptionOptions.algorithm,
                    keyDerivation: _encryptionOptions.keyDerivation,
                    iterations: _encryptionOptions.iterations,
                    enableCompression: value,
                    enableIntegrityCheck:
                        _encryptionOptions.enableIntegrityCheck,
                  );
                });
              },
            ),
            SwitchListTile(
              title: const Text('Enable Integrity Check'),
              subtitle: const Text('Verify file integrity after encryption'),
              value: _encryptionOptions.enableIntegrityCheck,
              onChanged: (value) {
                setState(() {
                  _encryptionOptions = EncryptionOptions(
                    algorithm: _encryptionOptions.algorithm,
                    keyDerivation: _encryptionOptions.keyDerivation,
                    iterations: _encryptionOptions.iterations,
                    enableCompression: _encryptionOptions.enableCompression,
                    enableIntegrityCheck: value,
                  );
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEncryptionActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.play_arrow, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Actions',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _canEncrypt() ? _encryptFile : null,
                    icon: _isProcessing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.lock),
                    label: Text(
                      _isProcessing ? 'Encrypting...' : 'Encrypt File',
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _canEncrypt() ? _encryptFolder : null,
                    icon: const Icon(Icons.folder_zip),
                    label: const Text('Encrypt Folder'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDecryptTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.lock_open, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text('Decryption Interface'),
          Text('Select encrypted files to decrypt'),
        ],
      ),
    );
  }

  Widget _buildArchiveTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.archive, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text('Secure Archive'),
          Text('Create encrypted archives'),
        ],
      ),
    );
  }

  Widget _buildStatisticsTab() {
    if (_statistics == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildStatCard(
            'Active Sessions',
            '${_statistics!.activeSessions}',
            Icons.security,
            Colors.blue,
          ),
          const SizedBox(height: 16),
          _buildStatCard(
            'Cached Keys',
            '${_statistics!.cachedKeys}',
            Icons.key,
            Colors.green,
          ),
          const SizedBox(height: 16),
          _buildStatCard(
            'Total Encryptions',
            '${_statistics!.totalEncryptions}',
            Icons.lock,
            Colors.orange,
          ),
          const SizedBox(height: 16),
          _buildStatCard(
            'Supported Algorithms',
            '${_statistics!.supportedAlgorithms.length}',
            Icons.security,
            Colors.purple,
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(title, style: Theme.of(context).textTheme.bodyMedium),
                  Text(
                    value,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _selectFile() {
    // File picker implementation would go here
    _showSnackBar('File picker not implemented in demo', Colors.orange);
  }

  void _selectOutputPath() {
    // Output path picker implementation would go here
    _showSnackBar('Output path picker not implemented in demo', Colors.orange);
  }

  void _validatePassword(String password) {
    if (password.isNotEmpty) {
      final strength = _encryptionService.validatePasswordStrength(password);
      setState(() {
        _passwordStrength = strength;
      });
    } else {
      setState(() {
        _passwordStrength = null;
      });
    }
  }

  void _generatePassword() {
    final password = _encryptionService.generateSecurePassword(
      length: 16,
      includeUppercase: true,
      includeLowercase: true,
      includeNumbers: true,
      includeSymbols: true,
    );

    _passwordController.text = password;
    _confirmPasswordController.text = password;
    _validatePassword(password);

    // Copy to clipboard
    Clipboard.setData(ClipboardData(text: password));
    _showSnackBar('Password generated and copied to clipboard', Colors.green);
  }

  bool _canEncrypt() {
    return _filePathController.text.isNotEmpty &&
        _passwordController.text.isNotEmpty &&
        _passwordController.text == _confirmPasswordController.text &&
        !_isProcessing;
  }

  void _encryptFile() async {
    if (!_canEncrypt()) return;

    setState(() => _isProcessing = true);

    try {
      final result = await _encryptionService.encryptFile(
        filePath: _filePathController.text,
        password: _passwordController.text,
        outputPath: _outputPathController.text.isNotEmpty
            ? _outputPathController.text
            : null,
        options: _encryptionOptions,
      );

      if (result.success) {
        _showSnackBar(
          'File encrypted successfully!\nOutput: ${result.outputPath}',
          Colors.green,
        );
        _loadStatistics();
      } else {
        _showSnackBar('Encryption failed: ${result.error}', Colors.red);
      }
    } catch (e) {
      _showSnackBar('Encryption error: $e', Colors.red);
    } finally {
      setState(() => _isProcessing = false);
    }
  }

  void _encryptFolder() async {
    if (!_canEncrypt()) return;

    setState(() => _isProcessing = true);

    try {
      final result = await _encryptionService.encryptFolder(
        folderPath: _filePathController.text,
        password: _passwordController.text,
        outputPath: _outputPathController.text.isNotEmpty
            ? _outputPathController.text
            : null,
        options: _encryptionOptions,
      );

      if (result.success) {
        _showSnackBar(
          'Folder encrypted successfully!\n${result.encryptedFiles.length} files encrypted',
          Colors.green,
        );
        _loadStatistics();
      } else {
        _showSnackBar('Folder encryption failed: ${result.error}', Colors.red);
      }
    } catch (e) {
      _showSnackBar('Folder encryption error: $e', Colors.red);
    } finally {
      setState(() => _isProcessing = false);
    }
  }

  void _showSecurityInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Security Information'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Encryption Features:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• AES-256 encryption with secure key derivation'),
              Text('• PBKDF2 with 100,000+ iterations'),
              Text('• Secure random salt and IV generation'),
              Text('• File integrity verification'),
              Text('• Optional compression before encryption'),
              Text('• Secure password generation'),
              SizedBox(height: 16),
              Text(
                'Security Best Practices:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Use strong, unique passwords'),
              Text('• Store passwords securely'),
              Text('• Verify file integrity after encryption'),
              Text('• Keep encrypted files backed up'),
              Text('• Use secure deletion for original files'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 4),
      ),
    );
  }
}
