import 'package:flutter/material.dart';

/// Task priority levels
enum TaskPriority {
  low,
  medium,
  high,
  urgent,
}

/// Task status
enum TaskStatus {
  todo,
  inProgress,
  completed,
  cancelled,
  onHold,
}

/// Task category
enum TaskCategory {
  personal,
  work,
  shopping,
  health,
  education,
  finance,
  travel,
  home,
  social,
  other,
}

/// Recurrence pattern
enum RecurrencePattern {
  none,
  daily,
  weekly,
  monthly,
  yearly,
  custom,
}

/// Task model with comprehensive features
class Task {
  final String id;
  final String title;
  final String description;
  final TaskPriority priority;
  final TaskStatus status;
  final TaskCategory category;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? dueDate;
  final DateTime? startDate;
  final DateTime? completedAt;
  final List<String> tags;
  final List<TaskAttachment> attachments;
  final List<TaskComment> comments;
  final List<Subtask> subtasks;
  final String? assignedTo;
  final String? projectId;
  final int estimatedMinutes;
  final int actualMinutes;
  final RecurrencePattern recurrence;
  final Map<String, dynamic> customFields;
  final Color color;
  final bool isArchived;
  final double progress; // 0.0 to 1.0

  const Task({
    required this.id,
    required this.title,
    this.description = '',
    this.priority = TaskPriority.medium,
    this.status = TaskStatus.todo,
    this.category = TaskCategory.personal,
    required this.createdAt,
    required this.updatedAt,
    this.dueDate,
    this.startDate,
    this.completedAt,
    this.tags = const [],
    this.attachments = const [],
    this.comments = const [],
    this.subtasks = const [],
    this.assignedTo,
    this.projectId,
    this.estimatedMinutes = 0,
    this.actualMinutes = 0,
    this.recurrence = RecurrencePattern.none,
    this.customFields = const {},
    this.color = Colors.blue,
    this.isArchived = false,
    this.progress = 0.0,
  });

  Task copyWith({
    String? id,
    String? title,
    String? description,
    TaskPriority? priority,
    TaskStatus? status,
    TaskCategory? category,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? dueDate,
    DateTime? startDate,
    DateTime? completedAt,
    List<String>? tags,
    List<TaskAttachment>? attachments,
    List<TaskComment>? comments,
    List<Subtask>? subtasks,
    String? assignedTo,
    String? projectId,
    int? estimatedMinutes,
    int? actualMinutes,
    RecurrencePattern? recurrence,
    Map<String, dynamic>? customFields,
    Color? color,
    bool? isArchived,
    double? progress,
  }) {
    return Task(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      category: category ?? this.category,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      dueDate: dueDate ?? this.dueDate,
      startDate: startDate ?? this.startDate,
      completedAt: completedAt ?? this.completedAt,
      tags: tags ?? this.tags,
      attachments: attachments ?? this.attachments,
      comments: comments ?? this.comments,
      subtasks: subtasks ?? this.subtasks,
      assignedTo: assignedTo ?? this.assignedTo,
      projectId: projectId ?? this.projectId,
      estimatedMinutes: estimatedMinutes ?? this.estimatedMinutes,
      actualMinutes: actualMinutes ?? this.actualMinutes,
      recurrence: recurrence ?? this.recurrence,
      customFields: customFields ?? this.customFields,
      color: color ?? this.color,
      isArchived: isArchived ?? this.isArchived,
      progress: progress ?? this.progress,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'priority': priority.name,
      'status': status.name,
      'category': category.name,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'dueDate': dueDate?.toIso8601String(),
      'startDate': startDate?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'tags': tags,
      'attachments': attachments.map((a) => a.toJson()).toList(),
      'comments': comments.map((c) => c.toJson()).toList(),
      'subtasks': subtasks.map((s) => s.toJson()).toList(),
      'assignedTo': assignedTo,
      'projectId': projectId,
      'estimatedMinutes': estimatedMinutes,
      'actualMinutes': actualMinutes,
      'recurrence': recurrence.name,
      'customFields': customFields,
      'color': color.value,
      'isArchived': isArchived,
      'progress': progress,
    };
  }

  factory Task.fromJson(Map<String, dynamic> json) {
    return Task(
      id: json['id'],
      title: json['title'],
      description: json['description'] ?? '',
      priority: TaskPriority.values.firstWhere((e) => e.name == json['priority']),
      status: TaskStatus.values.firstWhere((e) => e.name == json['status']),
      category: TaskCategory.values.firstWhere((e) => e.name == json['category']),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      dueDate: json['dueDate'] != null ? DateTime.parse(json['dueDate']) : null,
      startDate: json['startDate'] != null ? DateTime.parse(json['startDate']) : null,
      completedAt: json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null,
      tags: List<String>.from(json['tags'] ?? []),
      attachments: (json['attachments'] as List?)?.map((a) => TaskAttachment.fromJson(a)).toList() ?? [],
      comments: (json['comments'] as List?)?.map((c) => TaskComment.fromJson(c)).toList() ?? [],
      subtasks: (json['subtasks'] as List?)?.map((s) => Subtask.fromJson(s)).toList() ?? [],
      assignedTo: json['assignedTo'],
      projectId: json['projectId'],
      estimatedMinutes: json['estimatedMinutes'] ?? 0,
      actualMinutes: json['actualMinutes'] ?? 0,
      recurrence: RecurrencePattern.values.firstWhere((e) => e.name == json['recurrence']),
      customFields: Map<String, dynamic>.from(json['customFields'] ?? {}),
      color: Color(json['color'] ?? Colors.blue.value),
      isArchived: json['isArchived'] ?? false,
      progress: json['progress']?.toDouble() ?? 0.0,
    );
  }
}

/// Subtask model
class Subtask {
  final String id;
  final String title;
  final bool isCompleted;
  final DateTime createdAt;
  final DateTime? completedAt;

  const Subtask({
    required this.id,
    required this.title,
    this.isCompleted = false,
    required this.createdAt,
    this.completedAt,
  });

  Subtask copyWith({
    String? id,
    String? title,
    bool? isCompleted,
    DateTime? createdAt,
    DateTime? completedAt,
  }) {
    return Subtask(
      id: id ?? this.id,
      title: title ?? this.title,
      isCompleted: isCompleted ?? this.isCompleted,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'isCompleted': isCompleted,
      'createdAt': createdAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
    };
  }

  factory Subtask.fromJson(Map<String, dynamic> json) {
    return Subtask(
      id: json['id'],
      title: json['title'],
      isCompleted: json['isCompleted'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      completedAt: json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null,
    );
  }
}

/// Task attachment model
class TaskAttachment {
  final String id;
  final String name;
  final String path;
  final String type;
  final int size;
  final DateTime createdAt;

  const TaskAttachment({
    required this.id,
    required this.name,
    required this.path,
    required this.type,
    required this.size,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'path': path,
      'type': type,
      'size': size,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory TaskAttachment.fromJson(Map<String, dynamic> json) {
    return TaskAttachment(
      id: json['id'],
      name: json['name'],
      path: json['path'],
      type: json['type'],
      size: json['size'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Task comment model
class TaskComment {
  final String id;
  final String content;
  final String author;
  final DateTime createdAt;

  const TaskComment({
    required this.id,
    required this.content,
    required this.author,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'author': author,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory TaskComment.fromJson(Map<String, dynamic> json) {
    return TaskComment(
      id: json['id'],
      content: json['content'],
      author: json['author'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}
