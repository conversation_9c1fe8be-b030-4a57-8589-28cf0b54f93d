import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/app_theme.dart';
import '../services/memo_providers.dart';
import '../models/note.dart';
import 'calendar_view_screen.dart';
import 'notes/note_view_screen.dart';
import 'todos/todo_view_screen.dart';
import 'voice_memos/voice_memo_playback_screen.dart';

class MemoSuiteDashboard extends ConsumerWidget {
  const MemoSuiteDashboard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final statisticsAsync = ref.watch(memoStatisticsProvider);
    final recentItemsAsync = ref.watch(recentItemsProvider);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Memo Suite'),
        automaticallyImplyLeading: false,
        backgroundColor: AppTheme.memoSuiteColor,
        actions: [
          IconButton(
            onPressed: () {
              ref.invalidate(memoStatisticsProvider);
              ref.invalidate(recentItemsProvider);
            },
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWelcomeSection(context),
            const SizedBox(height: 32),
            _buildQuickStats(context, statisticsAsync),
            const SizedBox(height: 32),
            _buildQuickActions(context, ref),
            const SizedBox(height: 32),
            _buildSearchSection(context, ref),
            const SizedBox(height: 32),
            _buildCategoryFilters(context, ref),
            const SizedBox(height: 32),
            _buildRecentItems(context, ref, recentItemsAsync),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppTheme.memoSuiteColor, AppTheme.memoSuiteColor.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome to Memo Suite',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Your complete productivity companion for notes, todos, and voice memos',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            Icons.note_alt,
            size: 64,
            color: Colors.white,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context, AsyncValue<Map<String, int>> statisticsAsync) {
    return statisticsAsync.when(
      data: (stats) => Row(
        children: [
          Expanded(
            child: _buildStatCard(
              context,
              'Total Notes',
              stats['notes']?.toString() ?? '0',
              Icons.note_alt,
              AppTheme.memoSuiteColor,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildStatCard(
              context,
              'Pending Todos',
              stats['todos']?.toString() ?? '0',
              Icons.check_box,
              AppTheme.memoSuiteColor,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildStatCard(
              context,
              'Voice Memos',
              stats['voiceMemos']?.toString() ?? '0',
              Icons.mic,
              AppTheme.memoSuiteColor,
            ),
          ),
        ],
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Text('Error: $error'),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: color,
            ),
            const SizedBox(height: 12),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                context,
                'New Note',
                Icons.note_add,
                AppTheme.memoSuiteColor,
                () {
                  ref.read(selectedNoteProvider.notifier).state = null;
                  ref.read(memoSuiteCurrentScreenProvider.notifier).state = MemoSuiteScreen.noteEditor;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildActionButton(
                context,
                'New Todo',
                Icons.add_task,
                AppTheme.memoSuiteColor,
                () {
                  ref.read(selectedTodoProvider.notifier).state = null;
                  ref.read(memoSuiteCurrentScreenProvider.notifier).state = MemoSuiteScreen.todoEditor;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildActionButton(
                context,
                'Record Voice',
                Icons.mic,
                AppTheme.memoSuiteColor,
                () {
                  ref.read(memoSuiteCurrentScreenProvider.notifier).state = MemoSuiteScreen.voiceMemoRecording;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.all(20),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      child: Column(
        children: [
          Icon(icon, size: 32),
          const SizedBox(height: 8),
          Text(title),
        ],
      ),
    );
  }

  Widget _buildSearchSection(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Search',
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        const SizedBox(height: 16),
        TextField(
          decoration: InputDecoration(
            hintText: 'Search notes, todos, and voice memos...',
            prefixIcon: const Icon(Icons.search),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          onChanged: (query) {
            ref.read(notesSearchQueryProvider.notifier).state = query;
            ref.read(voiceMemoSearchQueryProvider.notifier).state = query;
          },
        ),
      ],
    );
  }

  Widget _buildCategoryFilters(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Categories',
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            ...NoteCategory.allCategories.map((category) => FilterChip(
              label: Text(category),
              selected: ref.watch(notesSelectedCategoryProvider) == category,
              onSelected: (selected) {
                ref.read(notesSelectedCategoryProvider.notifier).state = 
                    selected ? category : null;
              },
            )),
          ],
        ),
      ],
    );
  }

  Widget _buildRecentItems(BuildContext context, WidgetRef ref, AsyncValue<List<Map<String, dynamic>>> recentItemsAsync) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Items',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            Row(
              children: [
                TextButton.icon(
                  onPressed: () {
                    ref.read(memoSuiteCurrentScreenProvider.notifier).state = MemoSuiteScreen.notesList;
                  },
                  icon: const Icon(Icons.note_alt),
                  label: const Text('Notes'),
                ),
                TextButton.icon(
                  onPressed: () {
                    ref.read(memoSuiteCurrentScreenProvider.notifier).state = MemoSuiteScreen.todosList;
                  },
                  icon: const Icon(Icons.check_box),
                  label: const Text('Todos'),
                ),
                TextButton.icon(
                  onPressed: () {
                    ref.read(memoSuiteCurrentScreenProvider.notifier).state = MemoSuiteScreen.voiceMemosList;
                  },
                  icon: const Icon(Icons.mic),
                  label: const Text('Voice'),
                ),
                TextButton.icon(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CalendarViewScreen(),
                      ),
                    );
                  },
                  icon: const Icon(Icons.calendar_today),
                  label: const Text('Calendar'),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 16),
        recentItemsAsync.when(
          data: (items) => items.isEmpty
              ? Card(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Center(
                      child: Column(
                        children: [
                          Icon(
                            Icons.inbox,
                            size: 48,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No items yet',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Create your first note, todo, or voice memo to get started',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              : Card(
                  child: ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: items.length,
                    separatorBuilder: (context, index) => const Divider(height: 1),
                    itemBuilder: (context, index) {
                      final item = items[index];
                      return _buildRecentItemTile(context, ref, item);
                    },
                  ),
                ),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Text('Error: $error'),
        ),
      ],
    );
  }

  Widget _buildRecentItemTile(BuildContext context, WidgetRef ref, Map<String, dynamic> item) {
    IconData icon;
    Color color;
    VoidCallback onTap;

    switch (item['type']) {
      case 'note':
        icon = Icons.note_alt;
        color = AppTheme.memoSuiteColor;
        onTap = () async {
          final note = await ref.read(notesProvider.notifier).getNoteById(item['id']);
          if (note != null && context.mounted) {
            ref.read(selectedNoteProvider.notifier).state = note;
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const NoteViewScreen(),
              ),
            );
          }
        };
        break;
      case 'todo':
        icon = Icons.check_box;
        color = AppTheme.memoSuiteColor;
        onTap = () async {
          final todo = await ref.read(todosProvider.notifier).getTodoById(item['id']);
          if (todo != null && context.mounted) {
            ref.read(selectedTodoProvider.notifier).state = todo;
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const TodoViewScreen(),
              ),
            );
          }
        };
        break;
      case 'voiceMemo':
        icon = Icons.mic;
        color = AppTheme.memoSuiteColor;
        onTap = () async {
          final voiceMemo = await ref.read(voiceMemosProvider.notifier).getVoiceMemoById(item['id']);
          if (voiceMemo != null && context.mounted) {
            ref.read(selectedVoiceMemoProvider.notifier).state = voiceMemo;
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const VoiceMemoPlaybackScreen(),
              ),
            );
          }
        };
        break;
      default:
        icon = Icons.help;
        color = Colors.grey;
        onTap = () {};
    }

    final updatedAt = DateTime.fromMillisecondsSinceEpoch(item['updatedAt']);
    final timeAgo = _getTimeAgo(updatedAt);

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: color,
        child: Icon(icon, color: Colors.white, size: 20),
      ),
      title: Text(item['title']),
      subtitle: Text('${item['type']} • $timeAgo'),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
