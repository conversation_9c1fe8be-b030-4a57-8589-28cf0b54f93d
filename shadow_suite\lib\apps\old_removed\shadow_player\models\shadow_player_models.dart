// Export all shadow player models
export 'media_models.dart';

// Additional models specific to ShadowPlayer service
import 'media_models.dart';

/// Playlist for media playback service
class PlaylistForPlayback {
  final String id;
  final String name;
  final String? description;
  final List<MediaItem> items;
  final DateTime createdAt;
  final DateTime modifiedAt;
  final String? artworkPath;

  const PlaylistForPlayback({
    required this.id,
    required this.name,
    this.description,
    required this.items,
    required this.createdAt,
    required this.modifiedAt,
    this.artworkPath,
  });

  PlaylistForPlayback copyWith({
    String? id,
    String? name,
    String? description,
    List<MediaItem>? items,
    DateTime? createdAt,
    DateTime? modifiedAt,
    String? artworkPath,
  }) {
    return PlaylistForPlayback(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      items: items ?? this.items,
      createdAt: createdAt ?? this.createdAt,
      modifiedAt: modifiedAt ?? this.modifiedAt,
      artworkPath: artworkPath ?? this.artworkPath,
    );
  }
}

/// Typedef for backward compatibility
typedef Playlist = PlaylistForPlayback;
