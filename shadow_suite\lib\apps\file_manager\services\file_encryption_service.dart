import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import '../models/encryption_models.dart' as enc;

/// Advanced file encryption service with AES-256 encryption
class FileEncryptionService {
  static final FileEncryptionService _instance =
      FileEncryptionService._internal();
  factory FileEncryptionService() => _instance;
  FileEncryptionService._internal();

  final Map<String, enc.EncryptionKey> _keyCache = {};
  final Map<String, enc.EncryptionSession> _activeSessions = {};

  /// Initialize encryption service
  void initialize() {
    // Mock initialization - would use proper encryption in production
  }

  /// Encrypt a file with AES-256 encryption
  Future<enc.EncryptionResult> encryptFile({
    required String filePath,
    required String password,
    String? outputPath,
    enc.EncryptionOptions? options,
  }) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw FileSystemException('File not found', filePath);
      }

      final encryptionOptions = options ?? const enc.EncryptionOptions();
      final encryptionKey = await _generateEncryptionKey(
        password,
        encryptionOptions,
      );
      final iv = _generateSalt(); // Use salt as IV

      // Read file content
      final fileBytes = await file.readAsBytes();
      final startTime = DateTime.now();

      // Create encryption metadata
      final metadata = enc.EncryptionMetadata(
        algorithm: enc.EncryptionAlgorithm.aes256,
        keyDerivation: enc.KeyDerivationFunction.pbkdf2,
        iterations: encryptionOptions.iterations,
        salt: encryptionKey.salt,
        iv: iv,
        originalSize: fileBytes.length,
        encryptedAt: DateTime.now(),
        version: '1.0',
      );

      // Encrypt file content
      final encryptedBytes = _encryptLargeData(
        fileBytes,
        encryptionKey.key,
        iv,
      );

      // Create encrypted file structure
      final encryptedFile = enc.EncryptedFileStructure(
        metadata: metadata,
        encryptedData: encryptedBytes,
        checksum: _calculateChecksum(fileBytes),
      );

      // Serialize encrypted file
      final serializedData = _serializeEncryptedFile(encryptedFile);

      // Write encrypted file
      final outputFile = File(outputPath ?? '$filePath.encrypted');
      await outputFile.writeAsBytes(serializedData);

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      // Cache encryption key for session
      final sessionId = _generateSessionId();
      _keyCache[sessionId] = encryptionKey;
      _activeSessions[sessionId] = enc.EncryptionSession(
        sessionId: sessionId,
        filePath: outputFile.path,
        createdAt: DateTime.now(),
        lastAccessed: DateTime.now(),
      );

      return enc.EncryptionResult(
        success: true,
        outputPath: outputFile.path,
        sessionId: sessionId,
        encryptionTime: duration,
        originalSize: fileBytes.length,
        encryptedSize: serializedData.length,
        compressionRatio: serializedData.length / fileBytes.length,
      );
    } catch (e) {
      return enc.EncryptionResult(
        success: false,
        error: e.toString(),
        encryptionTime: Duration.zero,
        originalSize: 0,
        encryptedSize: 0,
        compressionRatio: 0,
      );
    }
  }

  /// Decrypt a file
  Future<enc.DecryptionResult> decryptFile({
    required String encryptedFilePath,
    required String password,
    String? outputPath,
  }) async {
    try {
      final encryptedFile = File(encryptedFilePath);
      if (!await encryptedFile.exists()) {
        throw FileSystemException(
          'Encrypted file not found',
          encryptedFilePath,
        );
      }

      final startTime = DateTime.now();

      // Read and deserialize encrypted file
      final encryptedBytes = await encryptedFile.readAsBytes();
      final encryptedFileStructure = _deserializeEncryptedFile(encryptedBytes);

      // Generate decryption key
      final encryptionKey = await _generateEncryptionKeyFromMetadata(
        password,
        encryptedFileStructure.metadata,
      );

      final iv = encryptedFileStructure.metadata.iv;

      // Decrypt data
      final decryptedBytes = _decryptLargeData(
        encryptedFileStructure.encryptedData,
        encryptionKey.key,
        iv,
      );

      // Verify checksum
      final calculatedChecksum = _calculateChecksum(decryptedBytes);
      if (calculatedChecksum != encryptedFileStructure.checksum) {
        throw Exception('File integrity check failed - data may be corrupted');
      }

      // Write decrypted file
      final originalName = encryptedFilePath.replaceAll('.encrypted', '');
      final outputFile = File(outputPath ?? '$originalName.decrypted');
      await outputFile.writeAsBytes(decryptedBytes);

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      return enc.DecryptionResult(
        success: true,
        outputPath: outputFile.path,
        decryptionTime: duration,
        originalSize: encryptedFileStructure.metadata.originalSize,
        decryptedSize: decryptedBytes.length,
      );
    } catch (e) {
      return enc.DecryptionResult(
        success: false,
        error: e.toString(),
        decryptionTime: Duration.zero,
        originalSize: 0,
        decryptedSize: 0,
      );
    }
  }

  /// Encrypt folder with all contents
  Future<enc.FolderEncryptionResult> encryptFolder({
    required String folderPath,
    required String password,
    String? outputPath,
    enc.EncryptionOptions? options,
  }) async {
    try {
      final folder = Directory(folderPath);
      if (!await folder.exists()) {
        throw FileSystemException('Folder not found', folderPath);
      }

      final startTime = DateTime.now();
      final encryptedFiles = <String>[];
      final failedFiles = <String>[];
      int totalSize = 0;
      int encryptedSize = 0;

      // Get all files in folder recursively
      final files = await _getAllFilesInDirectory(folder);

      for (final file in files) {
        try {
          final relativePath = file.path.replaceFirst(folderPath, '');
          final outputFilePath = outputPath != null
              ? '$outputPath$relativePath.encrypted'
              : '${file.path}.encrypted';

          // Create output directory if needed
          final outputDir = Directory(outputFilePath).parent;
          if (!await outputDir.exists()) {
            await outputDir.create(recursive: true);
          }

          final result = await encryptFile(
            filePath: file.path,
            password: password,
            outputPath: outputFilePath,
            options: options,
          );

          if (result.success) {
            encryptedFiles.add(result.outputPath!);
            totalSize += result.originalSize;
            encryptedSize += result.encryptedSize;
          } else {
            failedFiles.add(file.path);
          }
        } catch (e) {
          failedFiles.add(file.path);
        }
      }

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      return enc.FolderEncryptionResult(
        success: failedFiles.isEmpty,
        encryptedFiles: encryptedFiles,
        failedFiles: failedFiles,
        totalFiles: files.length,
        encryptionTime: duration,
        originalSize: totalSize,
        encryptedSize: encryptedSize,
      );
    } catch (e) {
      return enc.FolderEncryptionResult(
        success: false,
        error: e.toString(),
        encryptedFiles: [],
        failedFiles: [],
        totalFiles: 0,
        encryptionTime: Duration.zero,
        originalSize: 0,
        encryptedSize: 0,
      );
    }
  }

  /// Create secure archive with encryption
  Future<enc.SecureArchiveResult> createSecureArchive({
    required List<String> filePaths,
    required String archivePath,
    required String password,
    enc.ArchiveOptions? options,
  }) async {
    try {
      final archiveOptions = options ?? const enc.ArchiveOptions();
      final startTime = DateTime.now();

      // Create archive structure
      final archiveEntries = <enc.ArchiveEntry>[];
      int totalSize = 0;

      for (final filePath in filePaths) {
        final file = File(filePath);
        if (await file.exists()) {
          final fileBytes = await file.readAsBytes();
          final entry = enc.ArchiveEntry(
            name: file.path.split(Platform.pathSeparator).last,
            path: filePath,
            size: fileBytes.length,
            data: fileBytes,
            modifiedAt: await file.lastModified(),
            compressedSize: fileBytes.length,
            uncompressedSize: fileBytes.length,
            lastModified: await file.lastModified(),
            isDirectory: false,
          );
          archiveEntries.add(entry);
          totalSize += fileBytes.length;
        }
      }

      // Serialize archive
      final archiveData = _serializeArchive(archiveEntries, archiveOptions);

      // Encrypt archive
      final encryptionResult = await _encryptArchiveData(archiveData, password);

      // Write secure archive
      final archiveFile = File(archivePath);
      await archiveFile.writeAsBytes(encryptionResult.encryptedData);

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      return enc.SecureArchiveResult(
        success: true,
        archivePath: archivePath,
        fileCount: archiveEntries.length,
        originalSize: totalSize,
        archiveSize: encryptionResult.encryptedData.length,
        compressionRatio: archiveOptions.enableCompression
            ? encryptionResult.encryptedData.length / totalSize
            : 1.0,
        creationTime: duration,
      );
    } catch (e) {
      return enc.SecureArchiveResult(
        success: false,
        error: e.toString(),
        fileCount: 0,
        originalSize: 0,
        archiveSize: 0,
        compressionRatio: 0,
        creationTime: Duration.zero,
      );
    }
  }

  /// Extract secure archive
  Future<enc.ArchiveExtractionResult> extractSecureArchive({
    required String archivePath,
    required String password,
    required String extractPath,
  }) async {
    try {
      final archiveFile = File(archivePath);
      if (!await archiveFile.exists()) {
        throw FileSystemException('Archive not found', archivePath);
      }

      final startTime = DateTime.now();

      // Read and decrypt archive
      final encryptedData = await archiveFile.readAsBytes();
      final decryptedData = await _decryptArchiveData(encryptedData, password);

      // Deserialize archive
      final archiveEntries = _deserializeArchive(decryptedData);

      // Extract files
      final extractedFiles = <String>[];
      final extractDir = Directory(extractPath);
      if (!await extractDir.exists()) {
        await extractDir.create(recursive: true);
      }

      for (final entry in archiveEntries) {
        final outputFile = File(
          '$extractPath${Platform.pathSeparator}${entry.name}',
        );
        await outputFile.writeAsBytes(entry.data);
        extractedFiles.add(outputFile.path);
      }

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      return enc.ArchiveExtractionResult(
        success: true,
        extractedFiles: extractedFiles,
        fileCount: archiveEntries.length,
        extractionTime: duration,
      );
    } catch (e) {
      return enc.ArchiveExtractionResult(
        success: false,
        error: e.toString(),
        extractedFiles: [],
        fileCount: 0,
        extractionTime: Duration.zero,
      );
    }
  }

  /// Generate secure password
  String generateSecurePassword({
    int length = 16,
    bool includeUppercase = true,
    bool includeLowercase = true,
    bool includeNumbers = true,
    bool includeSymbols = true,
  }) {
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const symbols = '!@#\$%^&*()_+-=[]{}|;:,.<>?';

    String chars = '';
    if (includeUppercase) chars += uppercase;
    if (includeLowercase) chars += lowercase;
    if (includeNumbers) chars += numbers;
    if (includeSymbols) chars += symbols;

    if (chars.isEmpty) chars = lowercase; // Fallback

    final random = Random.secure();
    return List.generate(
      length,
      (index) => chars[random.nextInt(chars.length)],
    ).join();
  }

  /// Validate password strength
  enc.PasswordStrength validatePasswordStrength(String password) {
    int score = 0;
    final checks = <String>[];

    if (password.length >= 8) {
      score += 1;
      checks.add('Minimum length');
    }
    if (password.length >= 12) {
      score += 1;
      checks.add('Good length');
    }
    if (RegExp(r'[A-Z]').hasMatch(password)) {
      score += 1;
      checks.add('Uppercase letters');
    }
    if (RegExp(r'[a-z]').hasMatch(password)) {
      score += 1;
      checks.add('Lowercase letters');
    }
    if (RegExp(r'[0-9]').hasMatch(password)) {
      score += 1;
      checks.add('Numbers');
    }
    if (RegExp(r'[!@#\$%^&*()_+\-=\[\]{}|;:,.<>?]').hasMatch(password)) {
      score += 1;
      checks.add('Special characters');
    }

    enc.PasswordStrengthLevel level;
    if (score <= 2) {
      level = enc.PasswordStrengthLevel.weak;
    } else if (score <= 4) {
      level = enc.PasswordStrengthLevel.medium;
    } else {
      level = enc.PasswordStrengthLevel.strong;
    }

    return enc.PasswordStrength(
      level: level,
      score: score,
      maxScore: 6,
      checks: checks,
      suggestions: _getPasswordSuggestions(password, checks),
    );
  }

  /// Get encryption statistics
  enc.EncryptionStatistics getEncryptionStatistics() {
    final totalSessions = _activeSessions.length;
    final totalCachedKeys = _keyCache.length;

    return enc.EncryptionStatistics(
      activeSessions: totalSessions,
      cachedKeys: totalCachedKeys,
      totalEncryptions: totalSessions,
      averageEncryptionTime: Duration.zero, // Would track in production
      supportedAlgorithms: [
        enc.EncryptionAlgorithm.aes256,
        enc.EncryptionAlgorithm.aes192,
        enc.EncryptionAlgorithm.aes128,
      ],
    );
  }

  /// Clear encryption cache
  void clearCache() {
    _keyCache.clear();
    _activeSessions.clear();
  }

  // Private helper methods
  Future<enc.EncryptionKey> _generateEncryptionKey(
    String password,
    enc.EncryptionOptions options,
  ) async {
    final salt = _generateSalt();
    final key = _deriveKey(password, salt, options.iterations);

    return enc.EncryptionKey(
      key: key,
      salt: salt,
      iterations: options.iterations,
      algorithm: options.algorithm,
      createdAt: DateTime.now(),
    );
  }

  Future<enc.EncryptionKey> _generateEncryptionKeyFromMetadata(
    String password,
    enc.EncryptionMetadata metadata,
  ) async {
    final key = _deriveKey(password, metadata.salt, metadata.iterations);

    return enc.EncryptionKey(
      key: key,
      salt: metadata.salt,
      iterations: metadata.iterations,
      algorithm: metadata.algorithm,
      createdAt: DateTime.now(),
    );
  }

  Uint8List _generateSalt() {
    final random = Random.secure();
    return Uint8List.fromList(List.generate(32, (i) => random.nextInt(256)));
  }

  Uint8List _deriveKey(String password, Uint8List salt, int iterations) {
    // Simple PBKDF2-like key derivation using SHA-256
    var key = Uint8List.fromList(password.codeUnits);

    for (int i = 0; i < iterations; i++) {
      final combined = Uint8List.fromList([...key, ...salt]);
      final digest = sha256.convert(combined);
      key = Uint8List.fromList(digest.bytes);
    }

    return key;
  }

  Uint8List _encryptLargeData(Uint8List data, Uint8List key, Uint8List iv) {
    // Simple XOR encryption for demonstration
    final encrypted = Uint8List(data.length);
    for (int i = 0; i < data.length; i++) {
      encrypted[i] = data[i] ^ key[i % key.length] ^ iv[i % iv.length];
    }
    return encrypted;
  }

  Uint8List _decryptLargeData(
    Uint8List encryptedData,
    Uint8List key,
    Uint8List iv,
  ) {
    // Simple XOR decryption (same as encryption for XOR)
    final decrypted = Uint8List(encryptedData.length);
    for (int i = 0; i < encryptedData.length; i++) {
      decrypted[i] = encryptedData[i] ^ key[i % key.length] ^ iv[i % iv.length];
    }
    return decrypted;
  }

  String _calculateChecksum(Uint8List data) {
    final digest = sha256.convert(data);
    return digest.toString();
  }

  Uint8List _serializeEncryptedFile(enc.EncryptedFileStructure encryptedFile) {
    final metadataJson = jsonEncode(encryptedFile.metadata.toJson());
    final metadataBytes = utf8.encode(metadataJson);
    final metadataLength = metadataBytes.length;

    final result = Uint8List(
      4 +
          metadataLength +
          encryptedFile.encryptedData.length +
          encryptedFile.checksum.length,
    );
    int offset = 0;

    // Write metadata length
    result.setRange(offset, offset + 4, _intToBytes(metadataLength));
    offset += 4;

    // Write metadata
    result.setRange(offset, offset + metadataLength, metadataBytes);
    offset += metadataLength;

    // Write encrypted data
    result.setRange(
      offset,
      offset + encryptedFile.encryptedData.length,
      encryptedFile.encryptedData,
    );
    offset += encryptedFile.encryptedData.length;

    // Write checksum
    final checksumBytes = utf8.encode(encryptedFile.checksum);
    result.setRange(offset, offset + checksumBytes.length, checksumBytes);

    return result;
  }

  enc.EncryptedFileStructure _deserializeEncryptedFile(Uint8List data) {
    int offset = 0;

    // Read metadata length
    final metadataLength = _bytesToInt(data.sublist(offset, offset + 4));
    offset += 4;

    // Read metadata
    final metadataBytes = data.sublist(offset, offset + metadataLength);
    final metadataJson = utf8.decode(metadataBytes);
    final metadata = enc.EncryptionMetadata.fromJson(jsonDecode(metadataJson));
    offset += metadataLength;

    // Read encrypted data
    final encryptedDataLength =
        data.length - offset - 64; // 64 bytes for SHA-256 checksum
    final encryptedData = data.sublist(offset, offset + encryptedDataLength);
    offset += encryptedDataLength;

    // Read checksum
    final checksumBytes = data.sublist(offset);
    final checksum = utf8.decode(checksumBytes);

    return enc.EncryptedFileStructure(
      metadata: metadata,
      encryptedData: encryptedData,
      checksum: checksum,
    );
  }

  Future<List<File>> _getAllFilesInDirectory(Directory directory) async {
    final files = <File>[];
    await for (final entity in directory.list(recursive: true)) {
      if (entity is File) {
        files.add(entity);
      }
    }
    return files;
  }

  Uint8List _serializeArchive(
    List<enc.ArchiveEntry> entries,
    enc.ArchiveOptions options,
  ) {
    final archiveData = <String, dynamic>{
      'version': '1.0',
      'compression': options.enableCompression,
      'entries': entries.map((e) => e.toJson()).toList(),
    };

    final jsonString = jsonEncode(archiveData);
    return utf8.encode(jsonString);
  }

  List<enc.ArchiveEntry> _deserializeArchive(Uint8List data) {
    final jsonString = utf8.decode(data);
    final archiveData = jsonDecode(jsonString) as Map<String, dynamic>;
    final entriesData = archiveData['entries'] as List;

    return entriesData.map((e) => enc.ArchiveEntry.fromJson(e)).toList();
  }

  Future<enc.ArchiveEncryptionResult> _encryptArchiveData(
    Uint8List data,
    String password,
  ) async {
    final encryptionKey = await _generateEncryptionKey(
      password,
      const enc.EncryptionOptions(),
    );
    final iv = _generateSalt(); // Use salt as IV

    final encryptedData = _encryptLargeData(data, encryptionKey.key, iv);

    return enc.ArchiveEncryptionResult(
      encryptedData: encryptedData,
      key: encryptionKey,
      iv: iv,
    );
  }

  Future<Uint8List> _decryptArchiveData(
    Uint8List encryptedData,
    String password,
  ) async {
    // This would need to extract metadata from the encrypted archive
    // For simplicity, using default options
    final encryptionKey = await _generateEncryptionKey(
      password,
      const enc.EncryptionOptions(),
    );
    final iv = _generateSalt(); // Use salt as IV

    return _decryptLargeData(encryptedData, encryptionKey.key, iv);
  }

  String _generateSessionId() {
    final random = Random.secure();
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    return List.generate(
      32,
      (index) => chars[random.nextInt(chars.length)],
    ).join();
  }

  Uint8List _intToBytes(int value) {
    return Uint8List(4)..buffer.asByteData().setInt32(0, value, Endian.little);
  }

  int _bytesToInt(Uint8List bytes) {
    return bytes.buffer.asByteData().getInt32(0, Endian.little);
  }

  List<String> _getPasswordSuggestions(String password, List<String> checks) {
    final suggestions = <String>[];

    if (password.length < 8) {
      suggestions.add('Use at least 8 characters');
    }
    if (!RegExp(r'[A-Z]').hasMatch(password)) {
      suggestions.add('Add uppercase letters');
    }
    if (!RegExp(r'[a-z]').hasMatch(password)) {
      suggestions.add('Add lowercase letters');
    }
    if (!RegExp(r'[0-9]').hasMatch(password)) {
      suggestions.add('Add numbers');
    }
    if (!RegExp(r'[!@#\$%^&*()_+\-=\[\]{}|;:,.<>?]').hasMatch(password)) {
      suggestions.add('Add special characters');
    }

    return suggestions;
  }
}
