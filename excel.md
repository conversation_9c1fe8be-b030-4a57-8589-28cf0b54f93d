# Complete Excel Alternative - All Features & 200+ Formulas

## 📊 SHEET MANAGEMENT OPERATIONS

### Row Operations
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **Add Row Above** | Long press row number → Insert Above | Right click row → Insert | Insert new row above selected |
| **Add Row Below** | Long press row number → Insert Below | Right click row → Insert | Insert new row below selected |
| **Add Multiple Rows** | Select range → Long press → Insert | Select rows → Right click → Insert | Insert multiple rows at once |
| **Delete Row** | Long press row → Delete | Right click row → Delete | Remove selected row(s) |
| **Hide Row** | Pinch row vertically | Right click row → Hide | Hide selected row(s) |
| **Unhide Row** | Select around hidden → Unhide | Select around hidden → Unhide | Show hidden rows |
| **Resize Row Height** | Drag row border | Drag row border | Adjust row height |
| **Auto-fit Row Height** | Double tap row border | Double click row border | Auto-size to content |
| **Move Row** | Long press → Drag | Drag row number | Reorder rows |
| **Copy Row** | Triple tap row → Copy | Ctrl+C on selected row | Copy entire row |
| **Group Rows** | Select rows → Group button | Alt+Shift+Right | Create row group |
| **Ungroup Rows** | Select group → Ungroup | Alt+Shift+Left | Remove row grouping |

### Column Operations
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **Add Column Left** | Long press column → Insert Left | Right click column → Insert | Insert column to the left |
| **Add Column Right** | Long press column → Insert Right | Right click column → Insert | Insert column to the right |
| **Add Multiple Columns** | Select range → Insert | Select columns → Right click → Insert | Insert multiple columns |
| **Delete Column** | Long press column → Delete | Right click column → Delete | Remove selected column(s) |
| **Hide Column** | Pinch column horizontally | Right click column → Hide | Hide selected column(s) |
| **Unhide Column** | Select around hidden → Unhide | Select around hidden → Unhide | Show hidden columns |
| **Resize Column Width** | Drag column border | Drag column border | Adjust column width |
| **Auto-fit Column Width** | Double tap column border | Double click column border | Auto-size to content |
| **Move Column** | Long press → Drag | Drag column letter | Reorder columns |
| **Copy Column** | Triple tap column → Copy | Ctrl+C on selected column | Copy entire column |
| **Group Columns** | Select columns → Group | Alt+Shift+Right | Create column group |
| **Ungroup Columns** | Select group → Ungroup | Alt+Shift+Left | Remove column grouping |

### Sheet Operations
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **Add Sheet** | Tap + on sheet tabs | Right click sheet tab → Insert | Add new worksheet |
| **Delete Sheet** | Long press sheet tab → Delete | Right click sheet tab → Delete | Remove worksheet |
| **Rename Sheet** | Double tap sheet tab | Right click sheet tab → Rename | Change sheet name |
| **Move Sheet** | Drag sheet tab | Drag sheet tab | Reorder sheets |
| **Copy Sheet** | Long press → Duplicate | Right click → Move/Copy | Duplicate sheet |
| **Hide Sheet** | Long press → Hide | Right click → Hide | Hide worksheet |
| **Unhide Sheet** | Long press tabs → Unhide | Right click → Unhide | Show hidden sheets |
| **Protect Sheet** | Settings → Protect | Review → Protect Sheet | Add sheet protection |
| **Tab Color** | Long press → Color | Right click → Tab Color | Change sheet tab color |

## 🔢 FORMULA CREATION & INTERACTION

### Formula Input Methods
| Action | Mobile Method | Desktop Method | Description |
|--------|---------------|----------------|-------------|
| **Start Formula** | Type = in cell | Type = in cell | Begin formula entry |
| **Cell Reference** | Tap cell while typing formula | Click cell while typing | Add cell reference to formula |
| **Range Selection** | Tap + drag while in formula | Click + drag while in formula | Select range for formula |
| **Multiple Ranges** | Tap range → Hold Ctrl → Tap another | Click range → Ctrl+Click another | Select multiple ranges |
| **Absolute Reference** | Tap $ button | Press F4 | Toggle $ signs ($A$1) |
| **Sheet Reference** | Tap sheet tab while in formula | Click sheet tab while in formula | Reference other sheet |
| **Named Range** | Type name in formula | Type name or F3 | Use named range in formula |
| **Function Help** | Tap ? next to function | Ctrl+A after function name | Show function syntax |
| **Accept Formula** | Tap ✓ or Enter | Press Enter | Confirm formula |
| **Cancel Formula** | Tap ✗ or Esc | Press Escape | Cancel formula entry |
| **Edit Formula** | Double tap cell with formula | F2 or click formula bar | Edit existing formula |
| **Array Formula** | Tap Array button | Ctrl+Shift+Enter | Create array formula |

### Formula Bar Features
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **Expand Formula Bar** | Tap expand icon | Ctrl+Shift+U | Make formula bar larger |
| **Name Box** | Tap name box | Click name box | Navigate to cell/range |
| **Function List** | Tap fx button | Click fx button | Open function wizard |
| **Formula Auditing** | Tap trace arrows | Formula → Trace Precedents | Show formula dependencies |

## 📋 200+ EXCEL FORMULAS BY CATEGORY

### 🧮 MATHEMATICAL & TRIGONOMETRIC FUNCTIONS (50 functions)

#### Basic Math
- **SUM(range)** - Sum of values
- **AVERAGE(range)** - Average of values  
- **COUNT(range)** - Count of numbers
- **COUNTA(range)** - Count of non-empty cells
- **COUNTBLANK(range)** - Count of empty cells
- **MIN(range)** - Minimum value
- **MAX(range)** - Maximum value
- **MEDIAN(range)** - Middle value
- **MODE(range)** - Most frequent value
- **RANK(value, array)** - Rank of value

#### Advanced Math
- **ABS(number)** - Absolute value
- **SIGN(number)** - Sign of number (-1, 0, 1)
- **SQRT(number)** - Square root
- **POWER(number, power)** - Number raised to power
- **EXP(number)** - e raised to power
- **LN(number)** - Natural logarithm
- **LOG(number, base)** - Logarithm with base
- **LOG10(number)** - Base 10 logarithm
- **FACT(number)** - Factorial
- **COMBIN(n, k)** - Combinations

#### Rounding Functions
- **ROUND(number, digits)** - Round to digits
- **ROUNDUP(number, digits)** - Round up
- **ROUNDDOWN(number, digits)** - Round down
- **CEILING(number, significance)** - Round up to multiple
- **FLOOR(number, significance)** - Round down to multiple
- **INT(number)** - Integer part
- **TRUNC(number, digits)** - Truncate decimals
- **EVEN(number)** - Round to nearest even
- **ODD(number)** - Round to nearest odd
- **MROUND(number, multiple)** - Round to multiple

#### Trigonometric Functions
- **SIN(angle)** - Sine
- **COS(angle)** - Cosine
- **TAN(angle)** - Tangent
- **ASIN(value)** - Arcsine
- **ACOS(value)** - Arccosine
- **ATAN(value)** - Arctangent
- **ATAN2(x, y)** - Arctangent of x/y
- **SINH(value)** - Hyperbolic sine
- **COSH(value)** - Hyperbolic cosine
- **TANH(value)** - Hyperbolic tangent

#### Random & Constants
- **RAND()** - Random number 0-1
- **RANDBETWEEN(min, max)** - Random integer in range
- **PI()** - Pi constant
- **RADIANS(degrees)** - Convert to radians
- **DEGREES(radians)** - Convert to degrees
- **GCD(number1, number2)** - Greatest common divisor
- **LCM(number1, number2)** - Least common multiple
- **MOD(number, divisor)** - Remainder after division
- **QUOTIENT(numerator, denominator)** - Integer quotient
- **SUBTOTAL(function_num, range)** - Subtotal with function

### 📊 STATISTICAL FUNCTIONS (40 functions)

#### Basic Statistics
- **STDEV(range)** - Standard deviation (sample)
- **STDEVP(range)** - Standard deviation (population)
- **VAR(range)** - Variance (sample)
- **VARP(range)** - Variance (population)
- **CORREL(array1, array2)** - Correlation coefficient
- **COVARIANCE.S(array1, array2)** - Sample covariance
- **COVARIANCE.P(array1, array2)** - Population covariance
- **PEARSON(array1, array2)** - Pearson correlation
- **RSQ(known_y, known_x)** - R-squared value
- **SLOPE(known_y, known_x)** - Slope of regression line

#### Percentiles & Quartiles
- **PERCENTILE(array, k)** - Percentile value
- **PERCENTRANK(array, x)** - Percentile rank
- **QUARTILE(array, quart)** - Quartile value
- **LARGE(array, k)** - Kth largest value
- **SMALL(array, k)** - Kth smallest value
- **FREQUENCY(data_array, bins_array)** - Frequency distribution
- **HISTOGRAM(data, bins)** - Histogram data
- **NORM.DIST(x, mean, std, cumulative)** - Normal distribution
- **NORM.INV(probability, mean, std)** - Inverse normal
- **STANDARDIZE(x, mean, std)** - Standardized value

#### Advanced Statistics
- **SKEW(range)** - Skewness of distribution
- **KURT(range)** - Kurtosis of distribution
- **GEOMEAN(range)** - Geometric mean
- **HARMEAN(range)** - Harmonic mean
- **TRIMMEAN(array, percent)** - Trimmed mean
- **CONFIDENCE(alpha, std, size)** - Confidence interval
- **T.DIST(x, deg_freedom, cumulative)** - T-distribution
- **T.INV(probability, deg_freedom)** - Inverse T
- **CHI.DIST(x, deg_freedom, cumulative)** - Chi-square distribution
- **F.DIST(x, deg_freedom1, deg_freedom2, cumulative)** - F-distribution

#### Regression & Forecasting
- **FORECAST(x, known_y, known_x)** - Linear forecast
- **TREND(known_y, known_x, new_x)** - Trend values
- **GROWTH(known_y, known_x, new_x)** - Exponential growth
- **LINEST(known_y, known_x)** - Linear regression statistics
- **LOGEST(known_y, known_x)** - Exponential regression
- **INTERCEPT(known_y, known_x)** - Y-intercept
- **STEYX(known_y, known_x)** - Standard error
- **FISHER(x)** - Fisher transformation
- **FISHERINV(y)** - Inverse Fisher
- **PROB(x_range, prob_range, lower, upper)** - Probability

### 📅 DATE & TIME FUNCTIONS (35 functions)

#### Current Date/Time
- **TODAY()** - Current date
- **NOW()** - Current date and time
- **DATE(year, month, day)** - Create date
- **TIME(hour, minute, second)** - Create time
- **DATETIME(year, month, day, hour, minute, second)** - Create datetime

#### Extract Date Parts
- **YEAR(date)** - Extract year
- **MONTH(date)** - Extract month
- **DAY(date)** - Extract day
- **HOUR(time)** - Extract hour
- **MINUTE(time)** - Extract minute
- **SECOND(time)** - Extract second
- **WEEKDAY(date, type)** - Day of week number
- **WEEKNUM(date, type)** - Week number of year
- **QUARTER(date)** - Quarter number

#### Date Calculations
- **DATEDIF(start_date, end_date, unit)** - Difference between dates
- **NETWORKDAYS(start, end, holidays)** - Working days
- **WORKDAY(start_date, days, holidays)** - Add working days
- **EDATE(start_date, months)** - Add months to date
- **EOMONTH(start_date, months)** - End of month
- **DAYS(end_date, start_date)** - Days between dates
- **DAYS360(start_date, end_date)** - Days in 360-day year

#### Date Formatting & Conversion
- **DATEVALUE(date_text)** - Convert text to date
- **TIMEVALUE(time_text)** - Convert text to time
- **TEXT(value, format)** - Format date as text
- **YEARFRAC(start_date, end_date, basis)** - Fraction of year

#### Advanced Date Functions
- **ISOWEEKNUM(date)** - ISO week number
- **WORKDAY.INTL(start, days, weekend, holidays)** - International workday
- **NETWORKDAYS.INTL(start, end, weekend, holidays)** - International networkdays
- **EASTER(year)** - Easter date for year
- **AGE(birthdate, as_of_date)** - Calculate age
- **WEEKDAYNAME(date)** - Day name (Monday, Tuesday, etc.)
- **MONTHNAME(date)** - Month name (January, February, etc.)
- **ISLEAPYEAR(year)** - Check if leap year
- **DAYSINMONTH(date)** - Days in month
- **DAYSINYEAR(date)** - Days in year

### 📝 TEXT FUNCTIONS (45 functions)

#### Text Manipulation
- **CONCATENATE(text1, text2, ...)** - Join text
- **CONCAT(text1, text2, ...)** - Join text (newer)
- **TEXTJOIN(delimiter, ignore_empty, text1, ...)** - Join with delimiter
- **LEFT(text, num_chars)** - Left characters
- **RIGHT(text, num_chars)** - Right characters
- **MID(text, start, num_chars)** - Middle characters
- **LEN(text)** - Length of text
- **TRIM(text)** - Remove extra spaces
- **CLEAN(text)** - Remove non-printable characters
- **SUBSTITUTE(text, old_text, new_text, instance)** - Replace text

#### Text Case & Formatting
- **UPPER(text)** - Convert to uppercase
- **LOWER(text)** - Convert to lowercase
- **PROPER(text)** - Proper case (Title Case)
- **EXACT(text1, text2)** - Compare exactly
- **COMPARE(text1, text2)** - Compare (case-insensitive)
- **REPT(text, number_times)** - Repeat text
- **REVERSE(text)** - Reverse text
- **CHAR(number)** - Character from code
- **CODE(text)** - Character code
- **UNICODE(text)** - Unicode value

#### Text Search & Position
- **FIND(find_text, within_text, start_num)** - Find position (case-sensitive)
- **SEARCH(find_text, within_text, start_num)** - Find position (case-insensitive)
- **REPLACE(old_text, start_num, num_chars, new_text)** - Replace by position
- **STARTSWITH(text, prefix)** - Check if starts with
- **ENDSWITH(text, suffix)** - Check if ends with
- **CONTAINS(text, substring)** - Check if contains

#### Text Conversion
- **TEXT(value, format_text)** - Format number as text
- **VALUE(text)** - Convert text to number
- **NUMBERVALUE(text, decimal_separator, group_separator)** - Convert localized number text
- **DOLLAR(number, decimals)** - Format as currency
- **FIXED(number, decimals, no_commas)** - Format number with decimals
- **T(value)** - Return text if text, empty if not

#### Advanced Text Functions
- **PHONETIC(range)** - Extract phonetic characters
- **BAHTTEXT(number)** - Thai currency text
- **JIS(text)** - Convert half-width to full-width
- **ASC(text)** - Convert full-width to half-width
- **SPLIT(text, delimiter)** - Split text into array
- **REGEX(text, pattern)** - Regular expression match
- **REGEXREPLACE(text, pattern, replacement)** - Replace with regex
- **REGEXEXTRACT(text, pattern)** - Extract with regex
- **HTMLDECODE(text)** - Decode HTML entities
- **URLENCODE(text)** - URL encode text
- **BASE64ENCODE(text)** - Base64 encode
- **BASE64DECODE(text)** - Base64 decode
- **SOUNDEX(text)** - Soundex code
- **METAPHONE(text)** - Metaphone code

### 🔍 LOOKUP & REFERENCE FUNCTIONS (25 functions)

#### Basic Lookup
- **VLOOKUP(lookup_value, table_array, col_index_num, range_lookup)** - Vertical lookup
- **HLOOKUP(lookup_value, table_array, row_index_num, range_lookup)** - Horizontal lookup
- **XLOOKUP(lookup_value, lookup_array, return_array, if_not_found, match_mode, search_mode)** - Advanced lookup
- **INDEX(array, row_num, col_num)** - Get value by position
- **MATCH(lookup_value, lookup_array, match_type)** - Find position
- **CHOOSE(index_num, value1, value2, ...)** - Choose from list
- **LOOKUP(lookup_value, lookup_vector, result_vector)** - Basic lookup

#### Reference Functions
- **INDIRECT(ref_text, A1_style)** - Reference from text
- **OFFSET(reference, rows, cols, height, width)** - Offset reference
- **ROW(reference)** - Row number
- **COLUMN(reference)** - Column number
- **ROWS(array)** - Number of rows
- **COLUMNS(array)** - Number of columns
- **AREAS(reference)** - Number of areas in reference
- **ADDRESS(row_num, col_num, abs_num, A1_style, sheet_text)** - Create cell address

#### Array Functions
- **TRANSPOSE(array)** - Transpose array
- **SORT(array, sort_index, sort_order, by_col)** - Sort array
- **SORTBY(array, by_array1, sort_order1, ...)** - Sort by criteria
- **FILTER(array, include, if_empty)** - Filter array
- **UNIQUE(array, by_col, exactly_once)** - Unique values
- **SEQUENCE(rows, columns, start, step)** - Generate sequence
- **RANDARRAY(rows, columns, min, max, whole_number)** - Random array

#### Dynamic Arrays
- **SPILL()** - Get spill range
- **ISARRAY(value)** - Check if array
- **FLATTEN(array)** - Flatten multi-dimensional array
- **WRAP(vector, wrap_count, pad_with)** - Wrap array

### 🧠 LOGICAL FUNCTIONS (15 functions)

#### Basic Logic
- **IF(logical_test, value_if_true, value_if_false)** - Basic conditional
- **IFS(logical_test1, value_if_true1, logical_test2, value_if_true2, ...)** - Multiple conditions
- **SWITCH(expression, value1, result1, value2, result2, ..., default)** - Switch case
- **AND(logical1, logical2, ...)** - All true
- **OR(logical1, logical2, ...)** - Any true
- **NOT(logical)** - Opposite
- **XOR(logical1, logical2, ...)** - Exclusive or
- **TRUE()** - True value
- **FALSE()** - False value

#### Error Handling
- **IFERROR(value, value_if_error)** - Handle errors
- **IFNA(value, value_if_na)** - Handle #N/A errors
- **ISERROR(value)** - Check if error
- **ISNA(value)** - Check if #N/A
- **ISERR(value)** - Check if error (except #N/A)
- **ERROR.TYPE(error_val)** - Type of error

### 🏦 FINANCIAL FUNCTIONS (30 functions)

#### Basic Financial
- **PV(rate, nper, pmt, fv, type)** - Present value
- **FV(rate, nper, pmt, pv, type)** - Future value
- **PMT(rate, nper, pv, fv, type)** - Payment amount
- **RATE(nper, pmt, pv, fv, type, guess)** - Interest rate
- **NPER(rate, pmt, pv, fv, type)** - Number of periods
- **IPMT(rate, per, nper, pv, fv, type)** - Interest payment
- **PPMT(rate, per, nper, pv, fv, type)** - Principal payment

#### Investment Analysis
- **NPV(rate, value1, value2, ...)** - Net present value
- **IRR(values, guess)** - Internal rate of return
- **XIRR(values, dates, guess)** - IRR with dates
- **MIRR(values, finance_rate, reinvest_rate)** - Modified IRR
- **XNPV(rate, values, dates)** - NPV with dates

#### Depreciation
- **SLN(cost, salvage, life)** - Straight-line depreciation
- **SYD(cost, salvage, life, per)** - Sum-of-years depreciation
- **DDB(cost, salvage, life, per, factor)** - Double-declining balance
- **DB(cost, salvage, life, per, month)** - Fixed-declining balance
- **VDB(cost, salvage, life, start_per, end_per, factor, no_switch)** - Variable declining balance

#### Bond Functions
- **PRICE(settlement, maturity, rate, yld, redemption, frequency, basis)** - Bond price
- **YIELD(settlement, maturity, coupon, pr, redemption, frequency, basis)** - Bond yield
- **DURATION(settlement, maturity, coupon, yld, frequency, basis)** - Duration
- **MDURATION(settlement, maturity, coupon, yld, frequency, basis)** - Modified duration
- **ACCRINT(issue, first_interest, settlement, rate, par, frequency, basis)** - Accrued interest

#### Currency & Exchange
- **DOLLARDE(fractional_dollar, fraction)** - Decimal from fraction
- **DOLLARFR(decimal_dollar, fraction)** - Fraction from decimal
- **EFFECT(nominal_rate, npery)** - Effective annual rate
- **NOMINAL(effect_rate, npery)** - Nominal annual rate
- **CUMIPMT(rate, nper, pv, start_per, end_per, type)** - Cumulative interest
- **CUMPRINC(rate, nper, pv, start_per, end_per, type)** - Cumulative principal

#### Options & Advanced
- **INTRATE(settlement, maturity, investment, redemption, basis)** - Interest rate for security
- **RECEIVED(settlement, maturity, investment, discount, basis)** - Amount received
- **TBILLEQ(settlement, maturity, discount)** - Treasury bill equivalent yield
- **TBILLPRICE(settlement, maturity, discount)** - Treasury bill price

### 🔍 INFORMATION FUNCTIONS (20 functions)

#### Type Checking
- **ISBLANK(value)** - Check if blank
- **ISNUMBER(value)** - Check if number
- **ISTEXT(value)** - Check if text
- **ISLOGICAL(value)** - Check if logical
- **ISREF(value)** - Check if reference
- **ISFORMULA(reference)** - Check if contains formula
- **ISEVEN(number)** - Check if even number
- **ISODD(number)** - Check if odd number

#### System Information
- **CELL(info_type, reference)** - Cell information
- **INFO(type_text)** - System information
- **TYPE(value)** - Data type number
- **N(value)** - Convert to number
- **NA()** - #N/A error value
- **SHEET(value)** - Sheet number
- **SHEETS(reference)** - Number of sheets

#### Formula Information
- **FORMULATEXT(reference)** - Get formula as text
- **PRECEDENTS(reference)** - Get precedent cells
- **DEPENDENTS(reference)** - Get dependent cells
- **ISCIRCULAR(reference)** - Check for circular reference
- **VOLATILE()** - Mark function as volatile

## 🎯 CONDITIONAL & AGGREGATE FUNCTIONS

### Conditional Functions
- **SUMIF(range, criteria, sum_range)** - Sum with condition
- **SUMIFS(sum_range, criteria_range1, criteria1, ...)** - Sum with multiple conditions
- **AVERAGEIF(range, criteria, average_range)** - Average with condition
- **AVERAGEIFS(average_range, criteria_range1, criteria1, ...)** - Average with multiple conditions
- **COUNTIF(range, criteria)** - Count with condition
- **COUNTIFS(criteria_range1, criteria1, ...)** - Count with multiple conditions
- **MAXIFS(max_range, criteria_range1, criteria1, ...)** - Max with conditions
- **MINIFS(min_range, criteria_range1, criteria1, ...)** - Min with conditions

## 📊 DATA MANIPULATION FEATURES

### Cell Selection During Formula Creation
| Action | Mobile Method | Desktop Method | Result |
|--------|---------------|----------------|---------|
| **Single Cell Reference** | Tap cell | Click cell | Adds A1 to formula |
| **Range Selection** | Tap + drag | Click + drag | Adds A1:B5 to formula |
| **Non-adjacent Ranges** | Tap range → Ctrl+Tap another | Click range → Ctrl+Click | Adds A1:B2,D4:E6 |
| **Entire Column** | Tap column header | Click column header | Adds A:A to formula |
| **Entire Row** | Tap row number | Click row number | Adds 1:1 to formula |
| **Current Sheet Range** | Normal tap/click | Normal click | Sheet1!A1:B5 |
| **Other Sheet Range** | Switch sheet → select | Switch sheet → select | Sheet2!A1:B5 |
| **Named Range** | Type name | Type name or F3 | Uses defined name |
| **3D Reference** | Select across sheets | Ctrl+Shift+Click sheets | Sheet1:Sheet3!A1 |

### Advanced Selection Features
| Feature | Mobile | Desktop | Description |
|---------|---------|---------|-------------|
| **Extend Selection** | Hold Shift + tap | Shift + Click | Extend current selection |
| **Add to Selection** | Hold Ctrl + tap | Ctrl + Click | Add non-adjacent ranges |
| **Select to End** | Swipe to edge | Ctrl + Shift + Arrow | Select to data edge |
| **Select All** | Three-finger tap | Ctrl + A | Select entire sheet |
| **Select Visible** | Filter + select | Alt + ; | Select visible cells only |
| **Go To Special** | Long press → Special | Ctrl + G → Special | Select by criteria |

### Formula Building Interactions
| Action | Mobile Method | Desktop Method | Result |
|--------|---------------|----------------|---------|
| **Switch Between Relative/Absolute** | Tap $ button | F4 key | A1 → $A$1 → A$1 → $A1 |
| **Insert Function** | Tap fx → select function | Type function name | Function with syntax help |
| **Function Arguments** | Tap parameter fields | Tab between arguments | Navigate function parameters |
| **Nested Functions** | Tap inside function | Click inside function | Add function within function |
| **Array Constants** | Use {1,2,3} syntax | Use {1,2,3} syntax | Create array constant |
| **Multiple Criteria** | Use comma separator | Use comma separator | IF(AND(A1>0,B1<10),"Yes","No") |

### Data Validation & Input
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **Dropdown List** | Long press cell → Validation | Data → Validation | Create selection list |
| **Number Range** | Set min/max values | Data → Validation → Number | Restrict number input |
| **Date Range** | Set date constraints | Data → Validation → Date | Restrict date input |
| **Text Length** | Set character limits | Data → Validation → Text Length | Limit text input |
| **Custom Formula** | Create validation rule | Data → Validation → Custom | Custom validation logic |
| **Input Message** | Set help text | Data → Validation → Input Message | Show help when selected |
| **Error Alert** | Set error message | Data → Validation → Error Alert | Custom error message |

### Formatting & Conditional Formatting
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **Conditional Format** | Select range → Format → Conditional | Home → Conditional Formatting | Format based on conditions |
| **Data Bars** | Format → Data Bars | Conditional Formatting → Data Bars | Show bars in cells |
| **Color Scales** | Format → Color Scales | Conditional Formatting → Color Scales | Color gradient based on values |
| **Icon Sets** | Format → Icon Sets | Conditional Formatting → Icon Sets | Icons based on values |
| **Highlight Rules** | Format → Highlight Cells | Conditional Formatting → Highlight Rules | Highlight based on criteria |
| **Top/Bottom Rules** | Format → Top/Bottom | Conditional Formatting → Top/Bottom | Highlight top/bottom values |
| **Formula Rules** | Custom formula condition | New Rule → Use Formula | Custom conditional logic |

## 🔧 ADVANCED FEATURES

### Pivot Tables & Analysis
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **Create Pivot Table** | Select data → Insert → Pivot | Insert → PivotTable | Create pivot table |
| **Add Fields** | Drag fields to areas | Drag fields to areas | Add rows/columns/values |
| **Filter Pivot** | Tap filter dropdown | Click filter arrow | Filter pivot data |
| **Pivot Chart** | Insert → Pivot Chart | Insert → PivotChart | Chart from pivot |
| **Refresh Pivot** | Three-finger swipe down | Right-click → Refresh | Update pivot data |
| **Pivot Table Options** | Long press pivot → Options | Right-click → Options | Configure pivot settings |

### Charts & Graphs
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **Insert Chart** | Select data → Insert → Chart | Insert → Chart | Create chart |
| **Chart Type** | Tap chart → Change Type | Right-click → Change Chart Type | Modify chart type |
| **Chart Elements** | Tap + on chart | Chart Tools → Add Element | Add titles, labels, legends |
| **Chart Styles** | Swipe through styles | Chart Tools → Styles | Apply chart themes |
| **Chart Data** | Tap chart → Edit Data | Right-click → Select Data | Modify chart data range |
| **Chart Formatting** | Double tap chart element | Right-click element → Format | Format individual elements |
| **Move Chart** | Drag chart | Cut/Paste chart | Reposition chart |
| **Resize Chart** | Pinch/drag corners | Drag resize handles | Change chart size |

### Data Import/Export
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **Import CSV** | File → Import → CSV | Data → From Text/CSV | Import comma-separated values |
| **Import Excel** | File → Open → .xlsx | File → Open | Import Excel files |
| **Import Database** | Data → From Database | Data → Get Data → Database | Connect to database |
| **Import Web Data** | Data → From Web | Data → From Web | Import from website |
| **Import JSON** | Data → From JSON | Data → Get Data → JSON | Import JSON data |
| **Import XML** | Data → From XML | Data → From Web → XML | Import XML data |
| **Export PDF** | Share → Export PDF | File → Export → PDF | Export as PDF |
| **Export Image** | Share → Save Image | Right-click → Save as Picture | Export chart/range as image |
| **Export CSV** | File → Export → CSV | File → Save As → CSV | Export as CSV |
| **Print Setup** | File → Print → Setup | File → Page Setup | Configure printing |

### Collaboration Features
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **Add Comments** | Long press cell → Comment | Right-click → New Comment | Add cell comments |
| **Reply to Comments** | Tap comment → Reply | Click comment → Reply | Respond to comments |
| **Resolve Comments** | Tap comment → Resolve | Right-click comment → Resolve | Mark comment complete |
| **Track Changes** | Settings → Track Changes | Review → Track Changes | Enable change tracking |
| **Accept Changes** | Review → Accept | Review → Accept/Reject | Accept tracked changes |
| **Reject Changes** | Review → Reject | Review → Accept/Reject | Reject tracked changes |
| **Share Workbook** | Share button | File → Share | Share for collaboration |
| **Version History** | File → History | File → Version History | View previous versions |
| **Protect Workbook** | Settings → Protection | Review → Protect Workbook | Add workbook protection |
| **Digital Signature** | File → Sign | File → Info → Protect | Add digital signature |

### Macro & Automation
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **Record Macro** | Tools → Macro → Record | Developer → Record Macro | Start macro recording |
| **Stop Recording** | Tools → Macro → Stop | Developer → Stop Recording | End macro recording |
| **Run Macro** | Tools → Macro → Run | Developer → Macros → Run | Execute saved macro |
| **Edit Macro** | Tools → Macro → Edit | Developer → Visual Basic | Edit macro code |
| **Macro Security** | Settings → Security | File → Options → Trust Center | Configure macro security |
| **Personal Macro Workbook** | Save to Personal.xlsb | Developer → Personal Macro Workbook | Save macros globally |
| **Macro Shortcuts** | Assign keyboard shortcut | Developer → Macros → Options | Create macro shortcuts |

### Advanced Formula Features
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **Formula Auditing** | Tap trace arrows | Formulas → Trace Precedents | Show formula dependencies |
| **Error Checking** | Tap error indicator | Formulas → Error Checking | Check for formula errors |
| **Watch Window** | Tools → Watch Window | Formulas → Watch Window | Monitor cell values |
| **Name Manager** | Tools → Names | Formulas → Name Manager | Manage named ranges |
| **Define Name** | Select range → Define Name | Formulas → Define Name | Create named range |
| **Formula Evaluation** | Tools → Evaluate | Formulas → Evaluate Formula | Step through formula |
| **Circular References** | Tools → Circular References | Formulas → Error Checking → Circular | Find circular references |
| **External References** | Tools → Links | Data → Edit Links | Manage external links |

## 🎨 FORMATTING & STYLING

### Cell Formatting Options
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **Number Formats** | Format → Number | Home → Number Format | Apply number formatting |
| **Custom Format** | Format → Custom | Format Cells → Custom | Create custom format |
| **Currency** | Format → Currency | Home → $ | Apply currency format |
| **Percentage** | Format → Percentage | Home → % | Apply percentage format |
| **Date Formats** | Format → Date | Home → Date | Apply date formatting |
| **Time Formats** | Format → Time | Format Cells → Time | Apply time formatting |
| **Scientific Notation** | Format → Scientific | Format Cells → Scientific | Scientific number format |
| **Fraction Format** | Format → Fraction | Format Cells → Fraction | Display as fractions |
| **Text Format** | Format → Text | Format Cells → Text | Treat numbers as text |
| **Accounting Format** | Format → Accounting | Home → Accounting | Accounting number format |

### Font & Text Styling
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **Font Family** | Format → Font | Home → Font | Change font family |
| **Font Size** | Format → Size | Home → Font Size | Change font size |
| **Bold** | Tap B | Ctrl+B or Home → Bold | Apply bold formatting |
| **Italic** | Tap I | Ctrl+I or Home → Italic | Apply italic formatting |
| **Underline** | Tap U | Ctrl+U or Home → Underline | Apply underline |
| **Strikethrough** | Format → Strikethrough | Home → Strikethrough | Strike through text |
| **Superscript** | Format → Superscript | Home → Superscript | Raise text above baseline |
| **Subscript** | Format → Subscript | Home → Subscript | Lower text below baseline |
| **Font Color** | Tap color picker | Home → Font Color | Change text color |
| **Text Effects** | Format → Effects | Home → Text Effects | Add text effects |

### Cell Borders & Shading
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **All Borders** | Format → Borders → All | Home → Borders → All Borders | Add borders to all sides |
| **Outside Borders** | Format → Borders → Outside | Home → Borders → Outside | Border around selection |
| **Inside Borders** | Format → Borders → Inside | Home → Borders → Inside | Borders between cells |
| **Top Border** | Format → Borders → Top | Home → Borders → Top | Top border only |
| **Bottom Border** | Format → Borders → Bottom | Home → Borders → Bottom | Bottom border only |
| **Left Border** | Format → Borders → Left | Home → Borders → Left | Left border only |
| **Right Border** | Format → Borders → Right | Home → Borders → Right | Right border only |
| **Border Style** | Format → Border Style | Format Cells → Border | Choose border line style |
| **Border Color** | Format → Border Color | Format Cells → Border | Choose border color |
| **Fill Color** | Tap fill color | Home → Fill Color | Cell background color |
| **Pattern Fill** | Format → Pattern | Format Cells → Fill → Pattern | Pattern background |
| **Gradient Fill** | Format → Gradient | Format Cells → Fill → Gradient | Gradient background |

### Alignment & Orientation
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **Align Left** | Format → Align Left | Home → Align Left | Left-align text |
| **Align Center** | Format → Align Center | Home → Center | Center-align text |
| **Align Right** | Format → Align Right | Home → Align Right | Right-align text |
| **Justify** | Format → Justify | Format Cells → Alignment → Justify | Justify text |
| **Top Align** | Format → Top | Home → Top Align | Align to top of cell |
| **Middle Align** | Format → Middle | Home → Middle Align | Vertically center |
| **Bottom Align** | Format → Bottom | Home → Bottom Align | Align to bottom |
| **Wrap Text** | Format → Wrap | Home → Wrap Text | Wrap text in cell |
| **Shrink to Fit** | Format → Shrink | Format Cells → Shrink to Fit | Reduce font to fit |
| **Merge Cells** | Select → Merge | Home → Merge & Center | Combine cells |
| **Unmerge Cells** | Select merged → Unmerge | Home → Unmerge Cells | Separate merged cells |
| **Text Orientation** | Format → Rotate | Format Cells → Alignment → Orientation | Rotate text |
| **Indent** | Format → Indent | Home → Increase Indent | Add indentation |

## 🔍 ADVANCED SEARCH & FILTER

### Find & Replace
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **Find** | Search icon → Find | Ctrl+F | Find specific text/values |
| **Find Next** | Tap Next | F3 or Enter | Find next occurrence |
| **Find Previous** | Tap Previous | Shift+F3 | Find previous occurrence |
| **Find All** | Tap Find All | Find All button | Highlight all matches |
| **Replace** | Search → Replace | Ctrl+H | Find and replace |
| **Replace All** | Tap Replace All | Replace All button | Replace all occurrences |
| **Match Case** | Options → Match Case | Options → Match Case | Case-sensitive search |
| **Match Entire Cell** | Options → Whole Cell | Options → Match Entire Cell | Exact cell match only |
| **Search Formulas** | Options → Formulas | Options → Look In → Formulas | Search in formulas |
| **Search Values** | Options → Values | Options → Look In → Values | Search in cell values |
| **Search Comments** | Options → Comments | Options → Look In → Comments | Search in comments |
| **Use Wildcards** | Options → Wildcards | Options → Use Wildcards | Use * and ? wildcards |
| **Regular Expressions** | Options → Regex | Options → Regular Expressions | Use regex patterns |

### AutoFilter & Advanced Filter
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **Enable AutoFilter** | Select data → Filter | Data → Filter | Add filter dropdowns |
| **Filter by Value** | Tap filter → Select values | Click filter → Check values | Filter specific values |
| **Text Filters** | Filter → Text Filters | Filter → Text Filters | Filter text conditions |
| **Number Filters** | Filter → Number Filters | Filter → Number Filters | Filter number conditions |
| **Date Filters** | Filter → Date Filters | Filter → Date Filters | Filter date conditions |
| **Custom Filter** | Filter → Custom | Filter → Custom AutoFilter | Create custom criteria |
| **Top 10 Filter** | Filter → Top 10 | Filter → Top 10 | Filter top/bottom values |
| **Color Filter** | Filter → Filter by Color | Filter → Filter by Color | Filter by cell/font color |
| **Clear Filter** | Filter → Clear | Filter → Clear Filter | Remove filter from column |
| **Advanced Filter** | Data → Advanced Filter | Data → Advanced Filter | Complex filtering options |
| **Filter in Place** | Advanced Filter → Filter | Filter In Place option | Filter current location |
| **Copy to Location** | Advanced Filter → Copy | Copy to Another Location | Filter to new location |
| **Unique Records** | Advanced Filter → Unique | Unique Records Only | Show unique values only |

### Sorting Options
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **Sort A to Z** | Select → Sort A-Z | Data → Sort A to Z | Ascending sort |
| **Sort Z to A** | Select → Sort Z-A | Data → Sort Z to A | Descending sort |
| **Custom Sort** | Data → Sort | Data → Sort | Multi-level sorting |
| **Sort by Color** | Sort → Sort by Color | Sort → Sort by Color | Sort by cell/font color |
| **Add Sort Level** | Sort → Add Level | Sort → Add Level | Additional sort criteria |
| **Sort Left to Right** | Sort → Sort Left to Right | Sort → Options → Left to Right | Sort columns instead of rows |
| **Case Sensitive Sort** | Sort → Options → Case Sensitive | Sort → Options → Case Sensitive | Consider letter case |
| **Sort by Custom List** | Sort → Order → Custom | Sort → Order → Custom List | Sort by predefined order |

## 🎯 DATA ANALYSIS TOOLS

### Goal Seek & Solver
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **Goal Seek** | Tools → Goal Seek | Data → What-If Analysis → Goal Seek | Find input for desired result |
| **Solver** | Tools → Solver | Data → Solver | Optimize with constraints |
| **Scenario Manager** | Tools → Scenarios | Data → What-If Analysis → Scenario Manager | Manage different scenarios |
| **Data Table** | Tools → Data Table | Data → What-If Analysis → Data Table | Show multiple results |

### Subtotals & Grouping
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **Subtotals** | Data → Subtotals | Data → Subtotal | Automatic subtotals |
| **Remove Subtotals** | Data → Remove Subtotals | Data → Subtotal → Remove All | Remove all subtotals |
| **Group Rows** | Select rows → Group | Data → Group → Group | Group selected rows |
| **Group Columns** | Select columns → Group | Data → Group → Group | Group selected columns |
| **Ungroup** | Select group → Ungroup | Data → Group → Ungroup | Remove grouping |
| **Auto Outline** | Data → Auto Outline | Data → Group → Auto Outline | Automatic grouping |
| **Clear Outline** | Data → Clear Outline | Data → Group → Clear Outline | Remove all grouping |
| **Show Detail** | Tap + on group | Click + on group | Expand group |
| **Hide Detail** | Tap - on group | Click - on group | Collapse group |
| **Show Levels** | Tap level numbers | Click level numbers | Show specific outline levels |

### Data Validation Advanced
| Feature | Mobile Method | Desktop Method | Description |
|---------|---------------|----------------|-------------|
| **List Validation** | Validation → List | Data Validation → List | Dropdown from list |
| **Whole Number** | Validation → Whole Number | Data Validation → Whole Number | Integer constraints |
| **Decimal** | Validation → Decimal | Data Validation → Decimal | Decimal constraints |
| **Date** | Validation → Date | Data Validation → Date | Date range constraints |
| **Time** | Validation → Time | Data Validation → Time | Time range constraints |
| **Text Length** | Validation → Text Length | Data Validation → Text Length | Character count limits |
| **Custom** | Validation → Custom | Data Validation → Custom | Formula-based validation |
| **Circle Invalid** | Tools → Circle Invalid | Data → Data Validation → Circle Invalid | Highlight invalid data |
| **Clear Validation** | Validation → Clear All | Data → Data Validation → Clear All | Remove all validation |

## 📱💻 PLATFORM-SPECIFIC ADVANCED FEATURES

### Mobile-Only Features
| Feature | Method | Description |
|---------|---------|-------------|
| **Voice Input** | Tap microphone → Speak | Voice-to-text for cell input |
| **Camera Input** | Tap camera → Capture | Scan documents/tables to import |
| **Handwriting Recognition** | Use stylus/finger to write | Convert handwriting to text/formulas |
| **Shake to Undo** | Shake device | Quick undo last action |
| **Location Services** | Insert → Location | Add GPS coordinates |
| **Offline Sync** | Auto background sync | Work offline, sync when connected |
| **Mobile Sharing** | Share → Other Apps | Share via messaging/email apps |
| **Touch ID/Face ID** | Security settings | Biometric protection |
| **Picture in Picture** | Minimize while editing | View other apps while Excel open |
| **Split Screen** | Drag to split view | Use with other apps simultaneously |

### Desktop-Only Features
| Feature | Method | Description |
|---------|---------|-------------|
| **Multiple Windows** | View → New Window | Multiple views of same workbook |
| **COM Add-ins** | File → Options → Add-ins | Third-party extensions |
| **External Data Connections** | Data → Get Data | Connect to databases/web services |
| **Power Query** | Data → Get Data → From Other Sources | Advanced data transformation |
| **Power Pivot** | Insert → PivotTable → From Data Model | Advanced pivot table modeling |
| **VBA Editor** | Alt+F11 | Visual Basic for Applications |
| **Custom Ribbon** | File → Options → Customize Ribbon | Customize interface |
| **Quick Access Toolbar** | Customize above ribbon | Personal toolbar |
| **Status Bar Customization** | Right-click status bar | Customize status information |
| **Workbook Connections** | Data → Connections | Manage external connections |

This comprehensive list covers all the essential features, formulas, and interactions needed to build a professional Excel alternative. The key is implementing these features with platform-appropriate interaction methods while maintaining consistency across both mobile and desktop versions.