import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/tools_models.dart';

/// Tools settings provider
final toolsSettingsProvider = StateProvider<ToolsSettings>((ref) {
  return ToolsSettings.defaultSettings();
});

/// Spreadsheet provider
final spreadsheetsProvider =
    StateNotifierProvider<SpreadsheetsNotifier, List<Spreadsheet>>((ref) {
      return SpreadsheetsNotifier();
    });

/// Spreadsheets notifier
class SpreadsheetsNotifier extends StateNotifier<List<Spreadsheet>> {
  SpreadsheetsNotifier() : super([]);

  Future<void> loadSpreadsheets() async {
    // Simulate loading spreadsheets
    await Future.delayed(const Duration(milliseconds: 500));

    final mockSpreadsheets = List.generate(
      5,
      (index) => Spreadsheet(
        id: 'sheet_$index',
        name: 'Spreadsheet ${index + 1}',
        description: 'Description for spreadsheet ${index + 1}',
        createdAt: DateTime.now().subtract(Duration(days: index)),
        lastModified: DateTime.now().subtract(Duration(hours: index)),
        sheets: [
          SpreadsheetSheet(
            id: 'ws_${index}_0',
            name: 'Sheet1',
            cells: {},
            rows: 100 + index * 50,
            columns: 10 + index * 2,
          ),
        ],
      ),
    );

    state = mockSpreadsheets;
  }

  Future<void> createSpreadsheet(Spreadsheet spreadsheet) async {
    await Future.delayed(const Duration(milliseconds: 300));
    state = [...state, spreadsheet];
  }

  Future<void> updateSpreadsheet(Spreadsheet spreadsheet) async {
    await Future.delayed(const Duration(milliseconds: 300));
    state = state.map((s) => s.id == spreadsheet.id ? spreadsheet : s).toList();
  }

  Future<void> deleteSpreadsheet(String id) async {
    await Future.delayed(const Duration(milliseconds: 300));
    state = state.where((s) => s.id != id).toList();
  }
}

/// Templates provider
final templatesProvider =
    StateNotifierProvider<TemplatesNotifier, List<Template>>((ref) {
      return TemplatesNotifier();
    });

/// Templates notifier
class TemplatesNotifier extends StateNotifier<List<Template>> {
  TemplatesNotifier() : super([]);

  Future<void> loadTemplates() async {
    // Simulate loading templates
    await Future.delayed(const Duration(milliseconds: 500));

    final mockTemplates = [
      Template(
        id: 'template_1',
        name: 'Budget Tracker',
        description: 'Personal budget tracking template',
        category: 'Finance',
        previewImage: '',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        downloads: 1250,
        rating: 4.5,
      ),
      Template(
        id: 'template_2',
        name: 'Project Timeline',
        description: 'Project management timeline template',
        category: 'Business',
        previewImage: '',
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        downloads: 890,
        rating: 4.2,
      ),
      Template(
        id: 'template_3',
        name: 'Inventory Manager',
        description: 'Inventory tracking and management',
        category: 'Business',
        previewImage: '',
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
        downloads: 567,
        rating: 4.7,
      ),
      Template(
        id: 'template_4',
        name: 'Grade Calculator',
        description: 'Student grade calculation template',
        category: 'Education',
        previewImage: '',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        downloads: 234,
        rating: 4.0,
      ),
      Template(
        id: 'template_5',
        name: 'Workout Planner',
        description: 'Fitness workout planning template',
        category: 'Health',
        previewImage: '',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        downloads: 123,
        rating: 4.3,
      ),
    ];

    state = mockTemplates;
  }

  Future<void> downloadTemplate(String templateId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    state = state.map((t) {
      if (t.id == templateId) {
        return t.copyWith(downloads: t.downloads + 1);
      }
      return t;
    }).toList();
  }

  List<String> getCategories() {
    final categories = state.map((t) => t.category).toSet().toList();
    categories.sort();
    return categories;
  }
}

/// Current spreadsheet provider
final currentSpreadsheetProvider = StateProvider<Spreadsheet?>((ref) => null);

/// Spreadsheet data provider
final spreadsheetDataProvider = StateProvider<List<List<String>>>((ref) {
  // Initialize with empty 10x10 grid
  return List.generate(10, (row) => List.generate(10, (col) => ''));
});

/// Selected cell provider
final selectedCellProvider = StateProvider<CellPosition?>((ref) => null);

/// Formula bar text provider
final formulaBarTextProvider = StateProvider<String>((ref) => '');

/// Cell position model
class CellPosition {
  final int row;
  final int column;

  const CellPosition({required this.row, required this.column});

  /// Convert to cell address string (e.g., "A1", "B2")
  String toAddress() {
    String columnStr = '';
    int tempColumn = column;
    while (tempColumn > 0) {
      tempColumn--;
      columnStr = String.fromCharCode(65 + (tempColumn % 26)) + columnStr;
      tempColumn ~/= 26;
    }
    return '$columnStr$row';
  }

  /// Create CellPosition from address string (e.g., "A1" -> CellPosition(row: 1, column: 1))
  static CellPosition fromAddress(String address) {
    final match = RegExp(r'^([A-Z]+)(\d+)$').firstMatch(address.toUpperCase());
    if (match == null) throw ArgumentError('Invalid cell address: $address');

    final colStr = match.group(1)!;
    final rowStr = match.group(2)!;

    int column = 0;
    for (int i = 0; i < colStr.length; i++) {
      column = column * 26 + (colStr.codeUnitAt(i) - 64);
    }

    return CellPosition(row: int.parse(rowStr), column: column);
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CellPosition &&
          runtimeType == other.runtimeType &&
          row == other.row &&
          column == other.column;

  @override
  int get hashCode => row.hashCode ^ column.hashCode;

  @override
  String toString() => 'CellPosition(row: $row, column: $column)';
}

/// Export format provider
final exportFormatProvider = StateProvider<ExportFormat>(
  (ref) => ExportFormat.xlsx,
);

/// Export format enum
enum ExportFormat { xlsx, csv, pdf, html }

extension ExportFormatExtension on ExportFormat {
  String get name {
    switch (this) {
      case ExportFormat.xlsx:
        return 'Excel (.xlsx)';
      case ExportFormat.csv:
        return 'CSV (.csv)';
      case ExportFormat.pdf:
        return 'PDF (.pdf)';
      case ExportFormat.html:
        return 'HTML (.html)';
    }
  }

  String get extension {
    switch (this) {
      case ExportFormat.xlsx:
        return '.xlsx';
      case ExportFormat.csv:
        return '.csv';
      case ExportFormat.pdf:
        return '.pdf';
      case ExportFormat.html:
        return '.html';
    }
  }
}

/// Recent spreadsheets provider
final recentSpreadsheetsProvider = Provider<List<Spreadsheet>>((ref) {
  final allSpreadsheets = ref.watch(spreadsheetsProvider);
  return allSpreadsheets.take(5).toList();
});

/// Featured templates provider
final featuredTemplatesProvider = Provider<List<Template>>((ref) {
  final allTemplates = ref.watch(templatesProvider);
  return allTemplates.where((t) => t.rating >= 4.5).take(3).toList();
});

/// Template categories provider
final templateCategoriesProvider = Provider<List<String>>((ref) {
  final allTemplates = ref.watch(templatesProvider);
  final categories = allTemplates.map((t) => t.category).toSet().toList();
  categories.sort();
  return categories;
});

/// Filtered templates provider
final filteredTemplatesProvider = Provider.family<List<Template>, String>((
  ref,
  category,
) {
  final allTemplates = ref.watch(templatesProvider);
  if (category.isEmpty || category == 'All') {
    return allTemplates;
  }
  return allTemplates.where((t) => t.category == category).toList();
});

/// Tools statistics provider
final toolsStatisticsProvider = Provider<ToolsStatistics>((ref) {
  final spreadsheets = ref.watch(spreadsheetsProvider);
  final templates = ref.watch(templatesProvider);

  return ToolsStatistics(
    totalSpreadsheets: spreadsheets.length,
    totalTemplates: templates.length,
    totalFormulas: spreadsheets.fold<int>(
      0,
      (sum, s) =>
          sum +
          s.sheets
              .expand((sheet) => sheet.cells.values)
              .where((cell) => cell.formula != null)
              .length,
    ),
    totalCharts: spreadsheets.fold<int>(
      0,
      (sum, s) => sum + s.metadata.length,
    ), // Using metadata count as placeholder for charts
    lastActivity: DateTime.now(),
    favoriteCategory: templates.isNotEmpty
        ? templates.first.category
        : 'Business',
  );
});

/// Tools statistics model
class ToolsStatistics {
  final int totalSpreadsheets;
  final int totalTemplates;
  final int totalFormulas;
  final int totalCharts;
  final DateTime lastActivity;
  final String favoriteCategory;

  const ToolsStatistics({
    required this.totalSpreadsheets,
    required this.totalTemplates,
    required this.totalFormulas,
    required this.totalCharts,
    required this.lastActivity,
    required this.favoriteCategory,
  });
}
