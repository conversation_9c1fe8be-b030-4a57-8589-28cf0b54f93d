import 'package:flutter/material.dart';

/// Note types
enum NoteType {
  text,
  checklist,
  canvas,
}

/// Note priority levels
enum NotePriority {
  low,
  medium,
  high,
}

/// Note model with support for different types
class Note {
  final String id;
  final String title;
  final String content;
  final NoteType type;
  final NotePriority priority;
  final Color color;
  final List<String> tags;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isPinned;
  final bool isArchived;
  final bool isFavorite;
  final List<ChecklistItem> checklistItems;
  final CanvasData? canvasData;
  final Map<String, dynamic> metadata;

  const Note({
    required this.id,
    required this.title,
    this.content = '',
    this.type = NoteType.text,
    this.priority = NotePriority.medium,
    this.color = Colors.white,
    this.tags = const [],
    required this.createdAt,
    required this.updatedAt,
    this.isPinned = false,
    this.isArchived = false,
    this.isFavorite = false,
    this.checklistItems = const [],
    this.canvasData,
    this.metadata = const {},
  });

  Note copyWith({
    String? id,
    String? title,
    String? content,
    NoteType? type,
    NotePriority? priority,
    Color? color,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isPinned,
    bool? isArchived,
    bool? isFavorite,
    List<ChecklistItem>? checklistItems,
    CanvasData? canvasData,
    Map<String, dynamic>? metadata,
  }) {
    return Note(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      color: color ?? this.color,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isPinned: isPinned ?? this.isPinned,
      isArchived: isArchived ?? this.isArchived,
      isFavorite: isFavorite ?? this.isFavorite,
      checklistItems: checklistItems ?? this.checklistItems,
      canvasData: canvasData ?? this.canvasData,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'type': type.name,
      'priority': priority.name,
      'color': color.value,
      'tags': tags,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isPinned': isPinned,
      'isArchived': isArchived,
      'isFavorite': isFavorite,
      'checklistItems': checklistItems.map((item) => item.toJson()).toList(),
      'canvasData': canvasData?.toJson(),
      'metadata': metadata,
    };
  }

  factory Note.fromJson(Map<String, dynamic> json) {
    return Note(
      id: json['id'],
      title: json['title'],
      content: json['content'] ?? '',
      type: NoteType.values.firstWhere((e) => e.name == json['type']),
      priority: NotePriority.values.firstWhere((e) => e.name == json['priority']),
      color: Color(json['color'] ?? Colors.white.value),
      tags: List<String>.from(json['tags'] ?? []),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      isPinned: json['isPinned'] ?? false,
      isArchived: json['isArchived'] ?? false,
      isFavorite: json['isFavorite'] ?? false,
      checklistItems: (json['checklistItems'] as List?)
          ?.map((item) => ChecklistItem.fromJson(item))
          .toList() ?? [],
      canvasData: json['canvasData'] != null 
          ? CanvasData.fromJson(json['canvasData']) 
          : null,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  String get preview {
    switch (type) {
      case NoteType.text:
        return content.length > 100 ? '${content.substring(0, 100)}...' : content;
      case NoteType.checklist:
        final completedCount = checklistItems.where((item) => item.isCompleted).length;
        return '$completedCount/${checklistItems.length} completed';
      case NoteType.canvas:
        return 'Canvas drawing with ${canvasData?.strokes.length ?? 0} strokes';
    }
  }

  double get completionPercentage {
    if (type != NoteType.checklist || checklistItems.isEmpty) return 0.0;
    final completedCount = checklistItems.where((item) => item.isCompleted).length;
    return completedCount / checklistItems.length;
  }
}

/// Checklist item model
class ChecklistItem {
  final String id;
  final String text;
  final bool isCompleted;
  final DateTime createdAt;
  final DateTime? completedAt;

  const ChecklistItem({
    required this.id,
    required this.text,
    this.isCompleted = false,
    required this.createdAt,
    this.completedAt,
  });

  ChecklistItem copyWith({
    String? id,
    String? text,
    bool? isCompleted,
    DateTime? createdAt,
    DateTime? completedAt,
  }) {
    return ChecklistItem(
      id: id ?? this.id,
      text: text ?? this.text,
      isCompleted: isCompleted ?? this.isCompleted,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'isCompleted': isCompleted,
      'createdAt': createdAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
    };
  }

  factory ChecklistItem.fromJson(Map<String, dynamic> json) {
    return ChecklistItem(
      id: json['id'],
      text: json['text'],
      isCompleted: json['isCompleted'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      completedAt: json['completedAt'] != null 
          ? DateTime.parse(json['completedAt']) 
          : null,
    );
  }
}

/// Canvas data model for drawing notes
class CanvasData {
  final List<DrawingStroke> strokes;
  final Color backgroundColor;
  final Size canvasSize;

  const CanvasData({
    this.strokes = const [],
    this.backgroundColor = Colors.white,
    this.canvasSize = const Size(400, 600),
  });

  CanvasData copyWith({
    List<DrawingStroke>? strokes,
    Color? backgroundColor,
    Size? canvasSize,
  }) {
    return CanvasData(
      strokes: strokes ?? this.strokes,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      canvasSize: canvasSize ?? this.canvasSize,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'strokes': strokes.map((stroke) => stroke.toJson()).toList(),
      'backgroundColor': backgroundColor.value,
      'canvasSize': {
        'width': canvasSize.width,
        'height': canvasSize.height,
      },
    };
  }

  factory CanvasData.fromJson(Map<String, dynamic> json) {
    final canvasSizeData = json['canvasSize'] as Map<String, dynamic>;
    return CanvasData(
      strokes: (json['strokes'] as List)
          .map((stroke) => DrawingStroke.fromJson(stroke))
          .toList(),
      backgroundColor: Color(json['backgroundColor'] ?? Colors.white.value),
      canvasSize: Size(
        canvasSizeData['width']?.toDouble() ?? 400,
        canvasSizeData['height']?.toDouble() ?? 600,
      ),
    );
  }
}

/// Drawing stroke model for canvas
class DrawingStroke {
  final List<Offset> points;
  final Color color;
  final double strokeWidth;
  final DateTime createdAt;

  const DrawingStroke({
    required this.points,
    this.color = Colors.black,
    this.strokeWidth = 2.0,
    required this.createdAt,
  });

  DrawingStroke copyWith({
    List<Offset>? points,
    Color? color,
    double? strokeWidth,
    DateTime? createdAt,
  }) {
    return DrawingStroke(
      points: points ?? this.points,
      color: color ?? this.color,
      strokeWidth: strokeWidth ?? this.strokeWidth,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'points': points.map((point) => {
        'dx': point.dx,
        'dy': point.dy,
      }).toList(),
      'color': color.value,
      'strokeWidth': strokeWidth,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory DrawingStroke.fromJson(Map<String, dynamic> json) {
    return DrawingStroke(
      points: (json['points'] as List)
          .map((point) => Offset(
            point['dx']?.toDouble() ?? 0.0,
            point['dy']?.toDouble() ?? 0.0,
          ))
          .toList(),
      color: Color(json['color'] ?? Colors.black.value),
      strokeWidth: json['strokeWidth']?.toDouble() ?? 2.0,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Note folder model for organization
class NoteFolder {
  final String id;
  final String name;
  final String description;
  final Color color;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isDefault;

  const NoteFolder({
    required this.id,
    required this.name,
    this.description = '',
    this.color = Colors.blue,
    required this.createdAt,
    required this.updatedAt,
    this.isDefault = false,
  });

  NoteFolder copyWith({
    String? id,
    String? name,
    String? description,
    Color? color,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isDefault,
  }) {
    return NoteFolder(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      color: color ?? this.color,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDefault: isDefault ?? this.isDefault,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'color': color.value,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isDefault': isDefault,
    };
  }

  factory NoteFolder.fromJson(Map<String, dynamic> json) {
    return NoteFolder(
      id: json['id'],
      name: json['name'],
      description: json['description'] ?? '',
      color: Color(json['color'] ?? Colors.blue.value),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      isDefault: json['isDefault'] ?? false,
    );
  }
}
