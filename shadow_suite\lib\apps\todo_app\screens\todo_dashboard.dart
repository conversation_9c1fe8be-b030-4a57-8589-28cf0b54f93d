import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/task_models.dart';
import '../services/task_service.dart';

class TodoDashboard extends ConsumerWidget {
  const TodoDashboard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final todoTasks = ref.watch(todoTasksProvider);
    final inProgressTasks = ref.watch(inProgressTasksProvider);
    final completedTasks = ref.watch(completedTasksProvider);
    final todayTasks = ref.watch(todayTasksProvider);
    final overdueTasks = ref.watch(overdueTasksProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Task Dashboard'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddTaskDialog(context, ref),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Statistics Cards
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'To Do',
                    todoTasks.length.toString(),
                    Icons.assignment,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'In Progress',
                    inProgressTasks.length.toString(),
                    Icons.work,
                    Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Completed',
                    completedTasks.length.toString(),
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Overdue',
                    overdueTasks.length.toString(),
                    Icons.warning,
                    Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Today's Tasks
            Text(
              'Today\'s Tasks',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (todayTasks.isEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.check_circle_outline,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'No tasks due today',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ],
                ),
              )
            else
              ...todayTasks.take(5).map((task) => _buildTaskCard(task, ref)),

            const SizedBox(height: 24),

            // Overdue Tasks
            if (overdueTasks.isNotEmpty) ...[
              Text(
                'Overdue Tasks',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 16),
              ...overdueTasks.take(3).map((task) => _buildTaskCard(task, ref)),
              const SizedBox(height: 24),
            ],

            // Recent Tasks
            Text(
              'Recent Tasks',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...todoTasks.take(5).map((task) => _buildTaskCard(task, ref)),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(title, style: TextStyle(color: Colors.grey[600], fontSize: 12)),
        ],
      ),
    );
  }

  Widget _buildTaskCard(Task task, WidgetRef ref) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 40,
            decoration: BoxDecoration(
              color: task.color,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  task.title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                if (task.description.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    task.description,
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                const SizedBox(height: 8),
                Row(
                  children: [
                    _buildPriorityChip(task.priority),
                    const SizedBox(width: 8),
                    _buildCategoryChip(task.category),
                    if (task.dueDate != null) ...[
                      const SizedBox(width: 8),
                      _buildDueDateChip(task.dueDate!),
                    ],
                  ],
                ),
              ],
            ),
          ),
          IconButton(
            icon: Icon(
              task.status == TaskStatus.completed
                  ? Icons.check_circle
                  : Icons.radio_button_unchecked,
              color: task.status == TaskStatus.completed
                  ? Colors.green
                  : Colors.grey,
            ),
            onPressed: () {
              if (task.status != TaskStatus.completed) {
                ref.read(taskServiceProvider).completeTask(task.id);
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityChip(TaskPriority priority) {
    Color color;
    switch (priority) {
      case TaskPriority.urgent:
        color = Colors.red;
        break;
      case TaskPriority.high:
        color = Colors.orange;
        break;
      case TaskPriority.medium:
        color = Colors.blue;
        break;
      case TaskPriority.low:
        color = Colors.green;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        priority.name.toUpperCase(),
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildCategoryChip(TaskCategory category) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        category.name.toUpperCase(),
        style: TextStyle(
          color: Colors.grey[700],
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildDueDateChip(DateTime dueDate) {
    final now = DateTime.now();
    final isOverdue = dueDate.isBefore(now);
    final isToday =
        dueDate.year == now.year &&
        dueDate.month == now.month &&
        dueDate.day == now.day;

    Color color = Colors.grey;
    String text = '${dueDate.day}/${dueDate.month}';

    if (isOverdue) {
      color = Colors.red;
      text = 'OVERDUE';
    } else if (isToday) {
      color = Colors.orange;
      text = 'TODAY';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  void _showAddTaskDialog(BuildContext context, WidgetRef ref) {
    final titleController = TextEditingController();
    final descriptionController = TextEditingController();
    final notesController = TextEditingController();

    TaskPriority selectedPriority = TaskPriority.medium;
    TaskCategory selectedCategory = TaskCategory.personal;
    RecurrencePattern selectedRecurrence = RecurrencePattern.none;
    DateTime? selectedDueDate;
    TimeOfDay? selectedDueTime;
    bool hasReminder = false;
    DateTime? reminderDateTime;
    List<String> selectedTags = [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Task'),
        content: SizedBox(
          width: 500,
          child: SingleChildScrollView(
            child: StatefulBuilder(
              builder: (context, setState) => Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Title
                  TextField(
                    controller: titleController,
                    decoration: const InputDecoration(
                      labelText: 'Task Title *',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.title),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Description
                  TextField(
                    controller: descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.description),
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 16),

                  // Priority and Category Row
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<TaskPriority>(
                          value: selectedPriority,
                          decoration: const InputDecoration(
                            labelText: 'Priority',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.flag),
                          ),
                          items: TaskPriority.values.map((priority) {
                            return DropdownMenuItem(
                              value: priority,
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.flag,
                                    color: _getPriorityColor(priority),
                                    size: 16,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(priority.name.toUpperCase()),
                                ],
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              setState(() => selectedPriority = value);
                            }
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: DropdownButtonFormField<TaskCategory>(
                          value: selectedCategory,
                          decoration: const InputDecoration(
                            labelText: 'Category',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.category),
                          ),
                          items: TaskCategory.values.map((category) {
                            return DropdownMenuItem(
                              value: category,
                              child: Text(category.name.toUpperCase()),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              setState(() => selectedCategory = value);
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Due Date and Time
                  Row(
                    children: [
                      Expanded(
                        child: ListTile(
                          title: Text(
                            selectedDueDate == null
                                ? 'No Due Date'
                                : 'Due: ${selectedDueDate!.day}/${selectedDueDate!.month}/${selectedDueDate!.year}',
                          ),
                          leading: const Icon(Icons.calendar_today),
                          onTap: () async {
                            final date = await showDatePicker(
                              context: context,
                              initialDate: DateTime.now(),
                              firstDate: DateTime.now(),
                              lastDate: DateTime.now().add(
                                const Duration(days: 365),
                              ),
                            );
                            if (date != null) {
                              setState(() => selectedDueDate = date);
                            }
                          },
                          trailing: selectedDueDate != null
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () =>
                                      setState(() => selectedDueDate = null),
                                )
                              : null,
                        ),
                      ),
                    ],
                  ),

                  if (selectedDueDate != null) ...[
                    ListTile(
                      title: Text(
                        selectedDueTime == null
                            ? 'No Specific Time'
                            : 'Time: ${selectedDueTime!.format(context)}',
                      ),
                      leading: const Icon(Icons.access_time),
                      onTap: () async {
                        final time = await showTimePicker(
                          context: context,
                          initialTime: TimeOfDay.now(),
                        );
                        if (time != null) {
                          setState(() => selectedDueTime = time);
                        }
                      },
                      trailing: selectedDueTime != null
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () =>
                                  setState(() => selectedDueTime = null),
                            )
                          : null,
                    ),
                  ],

                  const SizedBox(height: 16),

                  // Recurrence
                  DropdownButtonFormField<RecurrencePattern>(
                    value: selectedRecurrence,
                    decoration: const InputDecoration(
                      labelText: 'Recurrence',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.repeat),
                    ),
                    items: RecurrencePattern.values.map((pattern) {
                      return DropdownMenuItem(
                        value: pattern,
                        child: Text(_getRecurrenceDisplayName(pattern)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => selectedRecurrence = value);
                      }
                    },
                  ),
                  const SizedBox(height: 16),

                  // Reminder
                  CheckboxListTile(
                    title: const Text('Set Reminder'),
                    subtitle: hasReminder && reminderDateTime != null
                        ? Text(
                            '${reminderDateTime!.day}/${reminderDateTime!.month} at ${TimeOfDay.fromDateTime(reminderDateTime!).format(context)}',
                          )
                        : const Text('No reminder set'),
                    value: hasReminder,
                    onChanged: (value) async {
                      setState(() => hasReminder = value ?? false);
                      if (hasReminder && selectedDueDate != null) {
                        final reminderDate = await showDatePicker(
                          context: context,
                          initialDate: selectedDueDate!.subtract(
                            const Duration(days: 1),
                          ),
                          firstDate: DateTime.now(),
                          lastDate: selectedDueDate!,
                        );
                        if (reminderDate != null) {
                          final reminderTime = await showTimePicker(
                            context: context,
                            initialTime: const TimeOfDay(hour: 9, minute: 0),
                          );
                          if (reminderTime != null) {
                            setState(() {
                              reminderDateTime = DateTime(
                                reminderDate.year,
                                reminderDate.month,
                                reminderDate.day,
                                reminderTime.hour,
                                reminderTime.minute,
                              );
                            });
                          }
                        }
                      }
                    },
                  ),

                  const SizedBox(height: 16),

                  // Notes
                  TextField(
                    controller: notesController,
                    decoration: const InputDecoration(
                      labelText: 'Additional Notes',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.note),
                    ),
                    maxLines: 2,
                  ),
                ],
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (titleController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Please enter a task title')),
                );
                return;
              }

              // Combine due date and time if both are set
              DateTime? finalDueDate = selectedDueDate;
              if (selectedDueDate != null && selectedDueTime != null) {
                finalDueDate = DateTime(
                  selectedDueDate!.year,
                  selectedDueDate!.month,
                  selectedDueDate!.day,
                  selectedDueTime!.hour,
                  selectedDueTime!.minute,
                );
              }

              final task = Task(
                id: DateTime.now().millisecondsSinceEpoch.toString(),
                title: titleController.text.trim(),
                description: descriptionController.text.trim(),
                priority: selectedPriority,
                status: TaskStatus.todo,
                category: selectedCategory,
                dueDate: finalDueDate,
                recurrence: selectedRecurrence,
                tags: selectedTags,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
                subtasks: [],
                attachments: [],
                customFields: {
                  'hasReminder': hasReminder,
                  'reminderDateTime': reminderDateTime?.toIso8601String(),
                  'notes': notesController.text.trim(),
                },
              );

              ref.read(taskServiceProvider).addTask(task);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Task created successfully'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Create Task'),
          ),
        ],
      ),
    );
  }

  Color _getPriorityColor(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.low:
        return Colors.green;
      case TaskPriority.medium:
        return Colors.orange;
      case TaskPriority.high:
        return Colors.red;
      case TaskPriority.urgent:
        return Colors.purple;
    }
  }

  String _getRecurrenceDisplayName(RecurrencePattern pattern) {
    switch (pattern) {
      case RecurrencePattern.none:
        return 'No Recurrence';
      case RecurrencePattern.daily:
        return 'Daily';
      case RecurrencePattern.weekly:
        return 'Weekly';
      case RecurrencePattern.monthly:
        return 'Monthly';
      case RecurrencePattern.yearly:
        return 'Yearly';
      case RecurrencePattern.custom:
        return 'Custom';
    }
  }
}
