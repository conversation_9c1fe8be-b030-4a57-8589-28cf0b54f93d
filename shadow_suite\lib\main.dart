import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'core/database/database_initializer.dart';

import 'core/services/settings_service.dart';
import 'core/services/error_handler.dart';
import 'core/services/performance_monitor.dart';
import 'core/services/app_permissions_manager.dart';
import 'core/services/storage_scanner_service.dart';
import 'core/services/fullscreen_service.dart';
import 'core/providers/settings_providers.dart' as settings;
import 'core/launcher/new_shadow_suite_launcher.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize error handling system
  ErrorHandler.initialize();

  // Initialize performance monitoring
  PerformanceMonitor().initialize();

  // Monitor app startup performance
  PerformanceMonitor().monitorAppStartup();

  // Initialize database factory for all platforms
  await DatabaseInitializer.initialize();

  // Initialize all app services with database
  await _initializeAllAppServices();

  // Initialize settings service
  await SettingsService.initialize();

  // Initialize permissions manager (skip on web)
  if (!kIsWeb) {
    await AppPermissionsManager.initialize();
  }

  // Initialize storage scanner service (skip on web)
  if (!kIsWeb) {
    await StorageScannerService.initialize();
  }

  // Initialize full-screen service (skip on web)
  if (!kIsWeb) {
    await FullScreenService.initialize();
  }

  runApp(const ProviderScope(child: ShadowSuiteApp()));
}

Future<void> _initializeAllAppServices() async {
  try {
    // Initialize all app services that require database
    print('Initializing app services...');

    // Note: Services will be initialized when first accessed through providers
    // This ensures proper dependency injection and lazy loading

    print('App services initialization completed');
  } catch (e) {
    print('Error initializing app services: $e');
    // Continue app startup even if some services fail
  }
}

class ShadowSuiteApp extends ConsumerWidget {
  const ShadowSuiteApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(settings.themeModeProvider);
    final customColor = ref.watch(customThemeColorProvider);
    final fontSize = ref.watch(settings.fontSizeProvider);

    // Convert FontSize enum to proper scaling factor
    final fontSizeMultiplier = _getFontSizeMultiplier(fontSize);

    return MaterialApp(
      title: 'Shadow Suite 2.0',
      theme: _buildLightTheme(customColor, fontSize),
      darkTheme: _buildDarkTheme(customColor, fontSize),
      themeMode: themeMode,
      debugShowCheckedModeBanner: false,
      home: _buildAppWithShortcuts(),
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(
            context,
          ).copyWith(textScaler: TextScaler.linear(fontSizeMultiplier)),
          child: child!,
        );
      },
    );
  }

  double _getFontSizeMultiplier(settings.FontSize fontSize) {
    switch (fontSize) {
      case settings.FontSize.small:
        return 0.9;
      case settings.FontSize.medium:
        return 1.0;
      case settings.FontSize.large:
        return 1.1;
    }
  }

  ThemeData _buildLightTheme(Color customColor, settings.FontSize fontSize) {
    final baseSize = _getBaseFontSize(fontSize);
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: customColor,
        brightness: Brightness.light,
      ),
      textTheme: TextTheme(
        bodyLarge: TextStyle(fontSize: baseSize),
        bodyMedium: TextStyle(fontSize: baseSize - 2),
        bodySmall: TextStyle(fontSize: baseSize - 4),
        headlineLarge: TextStyle(fontSize: baseSize + 10),
        headlineMedium: TextStyle(fontSize: baseSize + 8),
        headlineSmall: TextStyle(fontSize: baseSize + 6),
        titleLarge: TextStyle(fontSize: baseSize + 4),
        titleMedium: TextStyle(fontSize: baseSize + 2),
        titleSmall: TextStyle(fontSize: baseSize),
      ),
      cardTheme: CardThemeData(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: customColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
    );
  }

  double _getBaseFontSize(settings.FontSize fontSize) {
    switch (fontSize) {
      case settings.FontSize.small:
        return 14.0;
      case settings.FontSize.medium:
        return 16.0;
      case settings.FontSize.large:
        return 18.0;
    }
  }

  ThemeData _buildDarkTheme(Color customColor, settings.FontSize fontSize) {
    final baseSize = _getBaseFontSize(fontSize);
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: customColor,
        brightness: Brightness.dark,
      ),
      textTheme: TextTheme(
        bodyLarge: TextStyle(fontSize: baseSize),
        bodyMedium: TextStyle(fontSize: baseSize - 2),
        bodySmall: TextStyle(fontSize: baseSize - 4),
        headlineLarge: TextStyle(fontSize: baseSize + 10),
        headlineMedium: TextStyle(fontSize: baseSize + 8),
        headlineSmall: TextStyle(fontSize: baseSize + 6),
        titleLarge: TextStyle(fontSize: baseSize + 4),
        titleMedium: TextStyle(fontSize: baseSize + 2),
        titleSmall: TextStyle(fontSize: baseSize),
      ),
      cardTheme: CardThemeData(
        color: const Color(0xFF2C3E50),
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: customColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
    );
  }

  Widget _buildAppWithShortcuts() {
    // Add keyboard shortcuts for desktop platforms
    if (defaultTargetPlatform == TargetPlatform.windows ||
        defaultTargetPlatform == TargetPlatform.macOS ||
        defaultTargetPlatform == TargetPlatform.linux) {
      return Shortcuts(
        shortcuts: <LogicalKeySet, Intent>{
          LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyN):
              const _NewFileIntent(),
          LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyO):
              const _OpenFileIntent(),
          LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyS):
              const _SaveFileIntent(),
          LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyQ):
              const _QuitAppIntent(),
          LogicalKeySet(LogicalKeyboardKey.f11):
              const _ToggleFullscreenIntent(),
        },
        child: Actions(
          actions: <Type, Action<Intent>>{
            _NewFileIntent: _NewFileAction(),
            _OpenFileIntent: _OpenFileAction(),
            _SaveFileIntent: _SaveFileAction(),
            _QuitAppIntent: _QuitAppAction(),
            _ToggleFullscreenIntent: _ToggleFullscreenAction(),
          },
          child: const NewShadowSuiteLauncher(),
        ),
      );
    }

    // For mobile platforms, return the launcher directly
    return const NewShadowSuiteLauncher();
  }
}

// Intent classes for keyboard shortcuts
class _NewFileIntent extends Intent {
  const _NewFileIntent();
}

class _OpenFileIntent extends Intent {
  const _OpenFileIntent();
}

class _SaveFileIntent extends Intent {
  const _SaveFileIntent();
}

class _QuitAppIntent extends Intent {
  const _QuitAppIntent();
}

class _ToggleFullscreenIntent extends Intent {
  const _ToggleFullscreenIntent();
}

// Action classes for keyboard shortcuts
class _NewFileAction extends Action<_NewFileIntent> {
  @override
  Object? invoke(_NewFileIntent intent) {
    // Handle new file action
    print('New file shortcut pressed');
    return null;
  }
}

class _OpenFileAction extends Action<_OpenFileIntent> {
  @override
  Object? invoke(_OpenFileIntent intent) {
    // Handle open file action
    print('Open file shortcut pressed');
    return null;
  }
}

class _SaveFileAction extends Action<_SaveFileIntent> {
  @override
  Object? invoke(_SaveFileIntent intent) {
    // Handle save file action
    print('Save file shortcut pressed');
    return null;
  }
}

class _QuitAppAction extends Action<_QuitAppIntent> {
  @override
  Object? invoke(_QuitAppIntent intent) {
    // Handle quit app action
    print('Quit app shortcut pressed');
    return null;
  }
}

class _ToggleFullscreenAction extends Action<_ToggleFullscreenIntent> {
  @override
  Object? invoke(_ToggleFullscreenIntent intent) {
    // Handle toggle fullscreen action
    FullScreenService.toggleFullScreen();
    return null;
  }
}
