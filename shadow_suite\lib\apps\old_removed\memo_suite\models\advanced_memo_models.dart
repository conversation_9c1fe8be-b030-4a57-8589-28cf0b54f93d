

// Memo Encryption Model
class MemoEncryption {
  final String id;
  final String memoId;
  final EncryptionAlgorithm algorithm;
  final String encryptedContent;
  final String salt;
  final bool isLocked;
  final DateTime createdAt;
  final DateTime lastAccessed;

  const MemoEncryption({
    required this.id,
    required this.memoId,
    required this.algorithm,
    required this.encryptedContent,
    required this.salt,
    required this.isLocked,
    required this.createdAt,
    required this.lastAccessed,
  });

  factory MemoEncryption.fromJson(Map<String, dynamic> json) {
    return MemoEncryption(
      id: json['id'] as String,
      memoId: json['memo_id'] as String,
      algorithm: EncryptionAlgorithm.values.firstWhere(
        (e) => e.name == json['algorithm'],
        orElse: () => EncryptionAlgorithm.aes256,
      ),
      encryptedContent: json['encrypted_content'] as String,
      salt: json['salt'] as String,
      isLocked: json['is_locked'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastAccessed: DateTime.parse(json['last_accessed'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'memo_id': memoId,
      'algorithm': algorithm.name,
      'encrypted_content': encryptedContent,
      'salt': salt,
      'is_locked': isLocked,
      'created_at': createdAt.toIso8601String(),
      'last_accessed': lastAccessed.toIso8601String(),
    };
  }
}

// Memo Reminder Model
class MemoReminder {
  final String id;
  final String memoId;
  final DateTime reminderTime;
  final ReminderType type;
  final String message;
  final bool isRecurring;
  final Duration? recurringInterval;
  final bool isActive;
  final bool isTriggered;
  final DateTime createdAt;

  const MemoReminder({
    required this.id,
    required this.memoId,
    required this.reminderTime,
    required this.type,
    required this.message,
    required this.isRecurring,
    this.recurringInterval,
    required this.isActive,
    required this.isTriggered,
    required this.createdAt,
  });

  factory MemoReminder.fromJson(Map<String, dynamic> json) {
    return MemoReminder(
      id: json['id'] as String,
      memoId: json['memo_id'] as String,
      reminderTime: DateTime.parse(json['reminder_time'] as String),
      type: ReminderType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ReminderType.notification,
      ),
      message: json['message'] as String,
      isRecurring: json['is_recurring'] as bool,
      recurringInterval: json['recurring_interval_ms'] != null 
          ? Duration(milliseconds: json['recurring_interval_ms'] as int) 
          : null,
      isActive: json['is_active'] as bool,
      isTriggered: json['is_triggered'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'memo_id': memoId,
      'reminder_time': reminderTime.toIso8601String(),
      'type': type.name,
      'message': message,
      'is_recurring': isRecurring,
      'recurring_interval_ms': recurringInterval?.inMilliseconds,
      'is_active': isActive,
      'is_triggered': isTriggered,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Memo Attachment Model
class MemoAttachment {
  final String id;
  final String memoId;
  final String fileName;
  final String filePath;
  final AttachmentType type;
  final String mimeType;
  final int fileSize;
  final String? description;
  final bool isEmbedded;
  final DateTime uploadedAt;

  const MemoAttachment({
    required this.id,
    required this.memoId,
    required this.fileName,
    required this.filePath,
    required this.type,
    required this.mimeType,
    required this.fileSize,
    this.description,
    required this.isEmbedded,
    required this.uploadedAt,
  });

  factory MemoAttachment.fromJson(Map<String, dynamic> json) {
    return MemoAttachment(
      id: json['id'] as String,
      memoId: json['memo_id'] as String,
      fileName: json['file_name'] as String,
      filePath: json['file_path'] as String,
      type: AttachmentType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => AttachmentType.file,
      ),
      mimeType: json['mime_type'] as String,
      fileSize: json['file_size'] as int,
      description: json['description'] as String?,
      isEmbedded: json['is_embedded'] as bool,
      uploadedAt: DateTime.parse(json['uploaded_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'memo_id': memoId,
      'file_name': fileName,
      'file_path': filePath,
      'type': type.name,
      'mime_type': mimeType,
      'file_size': fileSize,
      'description': description,
      'is_embedded': isEmbedded,
      'uploaded_at': uploadedAt.toIso8601String(),
    };
  }
}

// Memo Link Model
class MemoLink {
  final String id;
  final String sourceMemoId;
  final String targetMemoId;
  final LinkType type;
  final String? description;
  final double strength;
  final bool isActive;
  final DateTime createdAt;

  const MemoLink({
    required this.id,
    required this.sourceMemoId,
    required this.targetMemoId,
    required this.type,
    this.description,
    required this.strength,
    required this.isActive,
    required this.createdAt,
  });

  factory MemoLink.fromJson(Map<String, dynamic> json) {
    return MemoLink(
      id: json['id'] as String,
      sourceMemoId: json['source_memo_id'] as String,
      targetMemoId: json['target_memo_id'] as String,
      type: LinkType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => LinkType.reference,
      ),
      description: json['description'] as String?,
      strength: (json['strength'] as num).toDouble(),
      isActive: json['is_active'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'source_memo_id': sourceMemoId,
      'target_memo_id': targetMemoId,
      'type': type.name,
      'description': description,
      'strength': strength,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Memo Analytics Model
class MemoAnalytics {
  final String id;
  final DateTime startDate;
  final DateTime endDate;
  final int totalMemos;
  final int totalWords;
  final double averageWordsPerMemo;
  final List<String> mostUsedTags;
  final Map<String, int> categoryDistribution;
  final Map<String, int> dailyActivity;
  final double productivityScore;
  final DateTime generatedAt;

  const MemoAnalytics({
    required this.id,
    required this.startDate,
    required this.endDate,
    required this.totalMemos,
    required this.totalWords,
    required this.averageWordsPerMemo,
    required this.mostUsedTags,
    required this.categoryDistribution,
    required this.dailyActivity,
    required this.productivityScore,
    required this.generatedAt,
  });

  factory MemoAnalytics.fromJson(Map<String, dynamic> json) {
    return MemoAnalytics(
      id: json['id'] as String,
      startDate: DateTime.parse(json['start_date'] as String),
      endDate: DateTime.parse(json['end_date'] as String),
      totalMemos: json['total_memos'] as int,
      totalWords: json['total_words'] as int,
      averageWordsPerMemo: (json['average_words_per_memo'] as num).toDouble(),
      mostUsedTags: List<String>.from(json['most_used_tags'] as List? ?? []),
      categoryDistribution: Map<String, int>.from(json['category_distribution'] as Map? ?? {}),
      dailyActivity: Map<String, int>.from(json['daily_activity'] as Map? ?? {}),
      productivityScore: (json['productivity_score'] as num).toDouble(),
      generatedAt: DateTime.parse(json['generated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'total_memos': totalMemos,
      'total_words': totalWords,
      'average_words_per_memo': averageWordsPerMemo,
      'most_used_tags': mostUsedTags,
      'category_distribution': categoryDistribution,
      'daily_activity': dailyActivity,
      'productivity_score': productivityScore,
      'generated_at': generatedAt.toIso8601String(),
    };
  }
}

// Memo Workspace Model
class MemoWorkspace {
  final String id;
  final String name;
  final String description;
  final String color;
  final String icon;
  final List<String> memoIds;
  final bool isDefault;
  final int sortOrder;
  final DateTime createdAt;
  final DateTime lastModified;

  const MemoWorkspace({
    required this.id,
    required this.name,
    required this.description,
    required this.color,
    required this.icon,
    required this.memoIds,
    required this.isDefault,
    required this.sortOrder,
    required this.createdAt,
    required this.lastModified,
  });

  factory MemoWorkspace.fromJson(Map<String, dynamic> json) {
    return MemoWorkspace(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      color: json['color'] as String,
      icon: json['icon'] as String,
      memoIds: List<String>.from(json['memo_ids'] as List? ?? []),
      isDefault: json['is_default'] as bool,
      sortOrder: json['sort_order'] as int,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastModified: DateTime.parse(json['last_modified'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'color': color,
      'icon': icon,
      'memo_ids': memoIds,
      'is_default': isDefault,
      'sort_order': sortOrder,
      'created_at': createdAt.toIso8601String(),
      'last_modified': lastModified.toIso8601String(),
    };
  }
}

// Memo Shortcut Model
class MemoShortcut {
  final String id;
  final String name;
  final String keyBinding;
  final ShortcutAction action;
  final Map<String, dynamic> parameters;
  final bool isEnabled;
  final int usageCount;
  final DateTime createdAt;

  const MemoShortcut({
    required this.id,
    required this.name,
    required this.keyBinding,
    required this.action,
    required this.parameters,
    required this.isEnabled,
    required this.usageCount,
    required this.createdAt,
  });

  factory MemoShortcut.fromJson(Map<String, dynamic> json) {
    return MemoShortcut(
      id: json['id'] as String,
      name: json['name'] as String,
      keyBinding: json['key_binding'] as String,
      action: ShortcutAction.values.firstWhere(
        (e) => e.name == json['action'],
        orElse: () => ShortcutAction.newMemo,
      ),
      parameters: Map<String, dynamic>.from(json['parameters'] as Map? ?? {}),
      isEnabled: json['is_enabled'] as bool,
      usageCount: json['usage_count'] as int,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'key_binding': keyBinding,
      'action': action.name,
      'parameters': parameters,
      'is_enabled': isEnabled,
      'usage_count': usageCount,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Memo Backup Model
class MemoBackup {
  final String id;
  final String name;
  final String filePath;
  final int memoCount;
  final int fileSize;
  final bool includeAttachments;
  final bool includeVersions;
  final DateTime createdAt;
  final bool isEncrypted;

  const MemoBackup({
    required this.id,
    required this.name,
    required this.filePath,
    required this.memoCount,
    required this.fileSize,
    required this.includeAttachments,
    required this.includeVersions,
    required this.createdAt,
    required this.isEncrypted,
  });

  factory MemoBackup.fromJson(Map<String, dynamic> json) {
    return MemoBackup(
      id: json['id'] as String,
      name: json['name'] as String,
      filePath: json['file_path'] as String,
      memoCount: json['memo_count'] as int,
      fileSize: json['file_size'] as int,
      includeAttachments: json['include_attachments'] as bool,
      includeVersions: json['include_versions'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      isEncrypted: json['is_encrypted'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'file_path': filePath,
      'memo_count': memoCount,
      'file_size': fileSize,
      'include_attachments': includeAttachments,
      'include_versions': includeVersions,
      'created_at': createdAt.toIso8601String(),
      'is_encrypted': isEncrypted,
    };
  }
}

// Mind Map Model
class MindMap {
  final String id;
  final String name;
  final String centralTopic;
  final List<MindMapNode> nodes;
  final List<MindMapConnection> connections;
  final MindMapLayout layout;
  final MindMapStyle style;
  final List<String> linkedMemoIds;
  final DateTime createdAt;
  final DateTime lastModified;

  const MindMap({
    required this.id,
    required this.name,
    required this.centralTopic,
    required this.nodes,
    required this.connections,
    required this.layout,
    required this.style,
    required this.linkedMemoIds,
    required this.createdAt,
    required this.lastModified,
  });

  factory MindMap.fromJson(Map<String, dynamic> json) {
    return MindMap(
      id: json['id'] as String,
      name: json['name'] as String,
      centralTopic: json['central_topic'] as String,
      nodes: (json['nodes'] as List<dynamic>?)
          ?.map((e) => MindMapNode.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      connections: (json['connections'] as List<dynamic>?)
          ?.map((e) => MindMapConnection.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      layout: MindMapLayout.values.firstWhere(
        (e) => e.name == json['layout'],
        orElse: () => MindMapLayout.radial,
      ),
      style: MindMapStyle.fromJson(json['style'] as Map<String, dynamic>),
      linkedMemoIds: List<String>.from(json['linked_memo_ids'] as List? ?? []),
      createdAt: DateTime.parse(json['created_at'] as String),
      lastModified: DateTime.parse(json['last_modified'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'central_topic': centralTopic,
      'nodes': nodes.map((e) => e.toJson()).toList(),
      'connections': connections.map((e) => e.toJson()).toList(),
      'layout': layout.name,
      'style': style.toJson(),
      'linked_memo_ids': linkedMemoIds,
      'created_at': createdAt.toIso8601String(),
      'last_modified': lastModified.toIso8601String(),
    };
  }
}

// Mind Map Node Model
class MindMapNode {
  final String id;
  final String text;
  final double x;
  final double y;
  final String color;
  final String? memoId;

  const MindMapNode({
    required this.id,
    required this.text,
    required this.x,
    required this.y,
    required this.color,
    this.memoId,
  });

  factory MindMapNode.fromJson(Map<String, dynamic> json) {
    return MindMapNode(
      id: json['id'] as String,
      text: json['text'] as String,
      x: (json['x'] as num).toDouble(),
      y: (json['y'] as num).toDouble(),
      color: json['color'] as String,
      memoId: json['memo_id'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'x': x,
      'y': y,
      'color': color,
      'memo_id': memoId,
    };
  }
}

// Mind Map Connection Model
class MindMapConnection {
  final String id;
  final String fromNodeId;
  final String toNodeId;
  final String color;
  final double thickness;

  const MindMapConnection({
    required this.id,
    required this.fromNodeId,
    required this.toNodeId,
    required this.color,
    required this.thickness,
  });

  factory MindMapConnection.fromJson(Map<String, dynamic> json) {
    return MindMapConnection(
      id: json['id'] as String,
      fromNodeId: json['from_node_id'] as String,
      toNodeId: json['to_node_id'] as String,
      color: json['color'] as String,
      thickness: (json['thickness'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'from_node_id': fromNodeId,
      'to_node_id': toNodeId,
      'color': color,
      'thickness': thickness,
    };
  }
}

// Mind Map Style Model
class MindMapStyle {
  final String backgroundColor;
  final String nodeColor;
  final String connectionColor;
  final String textColor;
  final String fontFamily;
  final double fontSize;

  const MindMapStyle({
    required this.backgroundColor,
    required this.nodeColor,
    required this.connectionColor,
    required this.textColor,
    required this.fontFamily,
    required this.fontSize,
  });

  factory MindMapStyle.fromJson(Map<String, dynamic> json) {
    return MindMapStyle(
      backgroundColor: json['background_color'] as String,
      nodeColor: json['node_color'] as String,
      connectionColor: json['connection_color'] as String,
      textColor: json['text_color'] as String,
      fontFamily: json['font_family'] as String,
      fontSize: (json['font_size'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'background_color': backgroundColor,
      'node_color': nodeColor,
      'connection_color': connectionColor,
      'text_color': textColor,
      'font_family': fontFamily,
      'font_size': fontSize,
    };
  }
}

// OCR Result Model
class OCRResult {
  final String id;
  final String imagePath;
  final String extractedText;
  final double confidence;
  final OCRLanguage language;
  final String? memoId;
  final DateTime processedAt;
  final List<BoundingBox> boundingBoxes;

  const OCRResult({
    required this.id,
    required this.imagePath,
    required this.extractedText,
    required this.confidence,
    required this.language,
    this.memoId,
    required this.processedAt,
    required this.boundingBoxes,
  });

  factory OCRResult.fromJson(Map<String, dynamic> json) {
    return OCRResult(
      id: json['id'] as String,
      imagePath: json['image_path'] as String,
      extractedText: json['extracted_text'] as String,
      confidence: (json['confidence'] as num).toDouble(),
      language: OCRLanguage.values.firstWhere(
        (e) => e.name == json['language'],
        orElse: () => OCRLanguage.english,
      ),
      memoId: json['memo_id'] as String?,
      processedAt: DateTime.parse(json['processed_at'] as String),
      boundingBoxes: (json['bounding_boxes'] as List<dynamic>?)
          ?.map((e) => BoundingBox.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'image_path': imagePath,
      'extracted_text': extractedText,
      'confidence': confidence,
      'language': language.name,
      'memo_id': memoId,
      'processed_at': processedAt.toIso8601String(),
      'bounding_boxes': boundingBoxes.map((e) => e.toJson()).toList(),
    };
  }
}

// Bounding Box Model
class BoundingBox {
  final double x;
  final double y;
  final double width;
  final double height;
  final String text;
  final double confidence;

  const BoundingBox({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    required this.text,
    required this.confidence,
  });

  factory BoundingBox.fromJson(Map<String, dynamic> json) {
    return BoundingBox(
      x: (json['x'] as num).toDouble(),
      y: (json['y'] as num).toDouble(),
      width: (json['width'] as num).toDouble(),
      height: (json['height'] as num).toDouble(),
      text: json['text'] as String,
      confidence: (json['confidence'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
      'width': width,
      'height': height,
      'text': text,
      'confidence': confidence,
    };
  }
}

// Drawing Models
class MemoDrawing {
  final String id;
  final String memoId;
  final String title;
  final String drawingData;
  final DrawingFormat format;
  final DrawingSize canvasSize;
  final List<DrawingLayer> layers;
  final List<DrawingTool> tools;
  final DateTime createdAt;
  final DateTime lastModified;

  const MemoDrawing({
    required this.id,
    required this.memoId,
    required this.title,
    required this.drawingData,
    required this.format,
    required this.canvasSize,
    required this.layers,
    required this.tools,
    required this.createdAt,
    required this.lastModified,
  });

  factory MemoDrawing.fromJson(Map<String, dynamic> json) {
    return MemoDrawing(
      id: json['id'] as String,
      memoId: json['memo_id'] as String,
      title: json['title'] as String,
      drawingData: json['drawing_data'] as String,
      format: DrawingFormat.values.firstWhere(
        (e) => e.name == json['format'],
        orElse: () => DrawingFormat.svg,
      ),
      canvasSize: DrawingSize.fromJson(json['canvas_size'] as Map<String, dynamic>),
      layers: (json['layers'] as List<dynamic>?)
          ?.map((e) => DrawingLayer.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      tools: (json['tools'] as List<dynamic>?)
          ?.map((e) => DrawingTool.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      createdAt: DateTime.parse(json['created_at'] as String),
      lastModified: DateTime.parse(json['last_modified'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'memo_id': memoId,
      'title': title,
      'drawing_data': drawingData,
      'format': format.name,
      'canvas_size': canvasSize.toJson(),
      'layers': layers.map((e) => e.toJson()).toList(),
      'tools': tools.map((e) => e.toJson()).toList(),
      'created_at': createdAt.toIso8601String(),
      'last_modified': lastModified.toIso8601String(),
    };
  }
}

class DrawingSize {
  final double width;
  final double height;

  const DrawingSize({required this.width, required this.height});

  factory DrawingSize.fromJson(Map<String, dynamic> json) {
    return DrawingSize(
      width: (json['width'] as num).toDouble(),
      height: (json['height'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {'width': width, 'height': height};
  }
}

class DrawingLayer {
  final String id;
  final String name;
  final bool isVisible;
  final double opacity;

  const DrawingLayer({
    required this.id,
    required this.name,
    required this.isVisible,
    required this.opacity,
  });

  factory DrawingLayer.fromJson(Map<String, dynamic> json) {
    return DrawingLayer(
      id: json['id'] as String,
      name: json['name'] as String,
      isVisible: json['is_visible'] as bool,
      opacity: (json['opacity'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'is_visible': isVisible,
      'opacity': opacity,
    };
  }
}

class DrawingTool {
  final String name;
  final String color;
  final double size;
  final double opacity;

  const DrawingTool({
    required this.name,
    required this.color,
    required this.size,
    required this.opacity,
  });

  factory DrawingTool.fromJson(Map<String, dynamic> json) {
    return DrawingTool(
      name: json['name'] as String,
      color: json['color'] as String,
      size: (json['size'] as num).toDouble(),
      opacity: (json['opacity'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'color': color,
      'size': size,
      'opacity': opacity,
    };
  }
}

// Code Snippet Model
class CodeSnippet {
  final String id;
  final String memoId;
  final String title;
  final String code;
  final ProgrammingLanguage language;
  final String? description;
  final int lineCount;
  final bool isExecutable;
  final DateTime createdAt;
  final DateTime lastModified;

  const CodeSnippet({
    required this.id,
    required this.memoId,
    required this.title,
    required this.code,
    required this.language,
    this.description,
    required this.lineCount,
    required this.isExecutable,
    required this.createdAt,
    required this.lastModified,
  });

  factory CodeSnippet.fromJson(Map<String, dynamic> json) {
    return CodeSnippet(
      id: json['id'] as String,
      memoId: json['memo_id'] as String,
      title: json['title'] as String,
      code: json['code'] as String,
      language: ProgrammingLanguage.values.firstWhere(
        (e) => e.name == json['language'],
        orElse: () => ProgrammingLanguage.plaintext,
      ),
      description: json['description'] as String?,
      lineCount: json['line_count'] as int,
      isExecutable: json['is_executable'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastModified: DateTime.parse(json['last_modified'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'memo_id': memoId,
      'title': title,
      'code': code,
      'language': language.name,
      'description': description,
      'line_count': lineCount,
      'is_executable': isExecutable,
      'created_at': createdAt.toIso8601String(),
      'last_modified': lastModified.toIso8601String(),
    };
  }
}

// Table Model
class MemoTable {
  final String id;
  final String memoId;
  final String title;
  final List<String> headers;
  final List<List<String>> rows;
  final TableStyle style;
  final int columnCount;
  final int rowCount;
  final bool isEditable;
  final DateTime createdAt;
  final DateTime lastModified;

  const MemoTable({
    required this.id,
    required this.memoId,
    required this.title,
    required this.headers,
    required this.rows,
    required this.style,
    required this.columnCount,
    required this.rowCount,
    required this.isEditable,
    required this.createdAt,
    required this.lastModified,
  });

  factory MemoTable.fromJson(Map<String, dynamic> json) {
    return MemoTable(
      id: json['id'] as String,
      memoId: json['memo_id'] as String,
      title: json['title'] as String,
      headers: List<String>.from(json['headers'] as List),
      rows: (json['rows'] as List<dynamic>)
          .map((row) => List<String>.from(row as List))
          .toList(),
      style: TableStyle.fromJson(json['style'] as Map<String, dynamic>),
      columnCount: json['column_count'] as int,
      rowCount: json['row_count'] as int,
      isEditable: json['is_editable'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastModified: DateTime.parse(json['last_modified'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'memo_id': memoId,
      'title': title,
      'headers': headers,
      'rows': rows,
      'style': style.toJson(),
      'column_count': columnCount,
      'row_count': rowCount,
      'is_editable': isEditable,
      'created_at': createdAt.toIso8601String(),
      'last_modified': lastModified.toIso8601String(),
    };
  }
}

// Table Style Model
class TableStyle {
  final String borderColor;
  final double borderWidth;
  final String headerBackgroundColor;
  final String headerTextColor;
  final String rowBackgroundColor;
  final String alternateRowColor;
  final String textColor;

  const TableStyle({
    required this.borderColor,
    required this.borderWidth,
    required this.headerBackgroundColor,
    required this.headerTextColor,
    required this.rowBackgroundColor,
    required this.alternateRowColor,
    required this.textColor,
  });

  factory TableStyle.fromJson(Map<String, dynamic> json) {
    return TableStyle(
      borderColor: json['border_color'] as String,
      borderWidth: (json['border_width'] as num).toDouble(),
      headerBackgroundColor: json['header_background_color'] as String,
      headerTextColor: json['header_text_color'] as String,
      rowBackgroundColor: json['row_background_color'] as String,
      alternateRowColor: json['alternate_row_color'] as String,
      textColor: json['text_color'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'border_color': borderColor,
      'border_width': borderWidth,
      'header_background_color': headerBackgroundColor,
      'header_text_color': headerTextColor,
      'row_background_color': rowBackgroundColor,
      'alternate_row_color': alternateRowColor,
      'text_color': textColor,
    };
  }
}

// Calendar Event Model
class CalendarEvent {
  final String id;
  final String memoId;
  final String title;
  final String? description;
  final DateTime startTime;
  final DateTime endTime;
  final String? location;
  final List<String> attendees;
  final bool isAllDay;
  final int reminderMinutes;
  final DateTime createdAt;

  const CalendarEvent({
    required this.id,
    required this.memoId,
    required this.title,
    this.description,
    required this.startTime,
    required this.endTime,
    this.location,
    required this.attendees,
    required this.isAllDay,
    required this.reminderMinutes,
    required this.createdAt,
  });

  factory CalendarEvent.fromJson(Map<String, dynamic> json) {
    return CalendarEvent(
      id: json['id'] as String,
      memoId: json['memo_id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      startTime: DateTime.parse(json['start_time'] as String),
      endTime: DateTime.parse(json['end_time'] as String),
      location: json['location'] as String?,
      attendees: List<String>.from(json['attendees'] as List? ?? []),
      isAllDay: json['is_all_day'] as bool,
      reminderMinutes: json['reminder_minutes'] as int,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'memo_id': memoId,
      'title': title,
      'description': description,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime.toIso8601String(),
      'location': location,
      'attendees': attendees,
      'is_all_day': isAllDay,
      'reminder_minutes': reminderMinutes,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Presentation Model
class MemoPresentation {
  final String id;
  final String name;
  final List<PresentationSlide> slides;
  final PresentationTheme theme;
  final PresentationTransition transition;
  final int currentSlide;
  final bool isFullscreen;
  final bool autoAdvance;
  final Duration slideInterval;
  final DateTime createdAt;
  final DateTime lastModified;

  const MemoPresentation({
    required this.id,
    required this.name,
    required this.slides,
    required this.theme,
    required this.transition,
    required this.currentSlide,
    required this.isFullscreen,
    required this.autoAdvance,
    required this.slideInterval,
    required this.createdAt,
    required this.lastModified,
  });

  factory MemoPresentation.fromJson(Map<String, dynamic> json) {
    return MemoPresentation(
      id: json['id'] as String,
      name: json['name'] as String,
      slides: (json['slides'] as List<dynamic>)
          .map((e) => PresentationSlide.fromJson(e as Map<String, dynamic>))
          .toList(),
      theme: PresentationTheme.values.firstWhere(
        (e) => e.name == json['theme'],
        orElse: () => PresentationTheme.default_,
      ),
      transition: PresentationTransition.values.firstWhere(
        (e) => e.name == json['transition'],
        orElse: () => PresentationTransition.slide,
      ),
      currentSlide: json['current_slide'] as int,
      isFullscreen: json['is_fullscreen'] as bool,
      autoAdvance: json['auto_advance'] as bool,
      slideInterval: Duration(milliseconds: json['slide_interval_ms'] as int),
      createdAt: DateTime.parse(json['created_at'] as String),
      lastModified: DateTime.parse(json['last_modified'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slides': slides.map((e) => e.toJson()).toList(),
      'theme': theme.name,
      'transition': transition.name,
      'current_slide': currentSlide,
      'is_fullscreen': isFullscreen,
      'auto_advance': autoAdvance,
      'slide_interval_ms': slideInterval.inMilliseconds,
      'created_at': createdAt.toIso8601String(),
      'last_modified': lastModified.toIso8601String(),
    };
  }
}

// Presentation Slide Model
class PresentationSlide {
  final String id;
  final String title;
  final String content;
  final String? backgroundImage;
  final String backgroundColor;
  final int order;

  const PresentationSlide({
    required this.id,
    required this.title,
    required this.content,
    this.backgroundImage,
    required this.backgroundColor,
    required this.order,
  });

  factory PresentationSlide.fromJson(Map<String, dynamic> json) {
    return PresentationSlide(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      backgroundImage: json['background_image'] as String?,
      backgroundColor: json['background_color'] as String,
      order: json['order'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'background_image': backgroundImage,
      'background_color': backgroundColor,
      'order': order,
    };
  }
}

// Enums
enum EncryptionAlgorithm { aes128, aes256, rsa2048, rsa4096 }
enum ReminderType { notification, email, popup, sound }
enum AttachmentType { image, video, audio, document, file, link }
enum LinkType { reference, related, parent, child, duplicate, inspiration }
enum ShortcutAction { newMemo, search, save, export, encrypt, backup, quickNote }
enum MindMapLayout { radial, tree, force, circular }
enum OCRLanguage { english, spanish, french, german, chinese, japanese, arabic }
enum DrawingFormat { svg, png, pdf, json }
enum ProgrammingLanguage { dart, javascript, python, java, cpp, csharp, html, css, sql, json, xml, yaml, markdown, plaintext }
enum PresentationTheme { default_, dark, light, minimal, corporate, creative }
enum PresentationTransition { slide, fade, zoom, flip, cube }
