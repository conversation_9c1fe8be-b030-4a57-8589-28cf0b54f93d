import 'package:flutter/material.dart';
import 'note_models.dart';

/// Note insight generated by AI analysis
class NoteInsight {
  final String id;
  final InsightType type;
  final String title;
  final String description;
  final double confidence;
  final List<String> recommendations;
  final DateTime createdAt;
  final Map<String, dynamic>? metadata;

  const NoteInsight({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.confidence,
    required this.recommendations,
    required this.createdAt,
    this.metadata,
  });

  NoteInsight copyWith({
    String? id,
    InsightType? type,
    String? title,
    String? description,
    double? confidence,
    List<String>? recommendations,
    DateTime? createdAt,
    Map<String, dynamic>? metadata,
  }) {
    return NoteInsight(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      confidence: confidence ?? this.confidence,
      recommendations: recommendations ?? this.recommendations,
      createdAt: createdAt ?? this.createdAt,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'confidence': confidence,
      'recommendations': recommendations,
      'createdAt': createdAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  factory NoteInsight.fromJson(Map<String, dynamic> json) {
    return NoteInsight(
      id: json['id'],
      type: InsightType.values.firstWhere((e) => e.name == json['type']),
      title: json['title'],
      description: json['description'],
      confidence: json['confidence'].toDouble(),
      recommendations: List<String>.from(json['recommendations']),
      createdAt: DateTime.parse(json['createdAt']),
      metadata: json['metadata'],
    );
  }
}

/// Types of note insights
enum InsightType { pattern, organization, style, gap, quality, maintenance }

/// Organization suggestion for notes
class OrganizationSuggestion {
  final String id;
  final SuggestionType type;
  final String title;
  final String description;
  final List<String> affectedNotes;
  final double confidence;
  final String estimatedBenefit;
  final DateTime createdAt;

  const OrganizationSuggestion({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.affectedNotes,
    required this.confidence,
    required this.estimatedBenefit,
    required this.createdAt,
  });

  OrganizationSuggestion copyWith({
    String? id,
    SuggestionType? type,
    String? title,
    String? description,
    List<String>? affectedNotes,
    double? confidence,
    String? estimatedBenefit,
    DateTime? createdAt,
  }) {
    return OrganizationSuggestion(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      affectedNotes: affectedNotes ?? this.affectedNotes,
      confidence: confidence ?? this.confidence,
      estimatedBenefit: estimatedBenefit ?? this.estimatedBenefit,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'affectedNotes': affectedNotes,
      'confidence': confidence,
      'estimatedBenefit': estimatedBenefit,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory OrganizationSuggestion.fromJson(Map<String, dynamic> json) {
    return OrganizationSuggestion(
      id: json['id'],
      type: SuggestionType.values.firstWhere((e) => e.name == json['type']),
      title: json['title'],
      description: json['description'],
      affectedNotes: List<String>.from(json['affectedNotes']),
      confidence: json['confidence'].toDouble(),
      estimatedBenefit: json['estimatedBenefit'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Types of organization suggestions
enum SuggestionType { notebook, tags, link, hierarchy }

/// Content suggestion for note improvement
class ContentSuggestion {
  final String id;
  final ContentSuggestionType type;
  final String title;
  final String description;
  final String originalText;
  final String suggestedText;
  final double confidence;
  final DateTime createdAt;

  const ContentSuggestion({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.originalText,
    required this.suggestedText,
    required this.confidence,
    required this.createdAt,
  });

  ContentSuggestion copyWith({
    String? id,
    ContentSuggestionType? type,
    String? title,
    String? description,
    String? originalText,
    String? suggestedText,
    double? confidence,
    DateTime? createdAt,
  }) {
    return ContentSuggestion(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      originalText: originalText ?? this.originalText,
      suggestedText: suggestedText ?? this.suggestedText,
      confidence: confidence ?? this.confidence,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'originalText': originalText,
      'suggestedText': suggestedText,
      'confidence': confidence,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory ContentSuggestion.fromJson(Map<String, dynamic> json) {
    return ContentSuggestion(
      id: json['id'],
      type: ContentSuggestionType.values.firstWhere(
        (e) => e.name == json['type'],
      ),
      title: json['title'],
      description: json['description'],
      originalText: json['originalText'],
      suggestedText: json['suggestedText'],
      confidence: json['confidence'].toDouble(),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Types of content suggestions
enum ContentSuggestionType { grammar, formatting, expansion, enhancement }

/// Smart search result with AI ranking
class SmartSearchResult {
  final String noteId;
  final String title;
  final String snippet;
  final double relevanceScore;
  final List<String> matchedTerms;
  final SearchResultType type;
  final Map<String, dynamic>? metadata;

  const SmartSearchResult({
    required this.noteId,
    required this.title,
    required this.snippet,
    required this.relevanceScore,
    required this.matchedTerms,
    required this.type,
    this.metadata,
  });

  SmartSearchResult copyWith({
    String? noteId,
    String? title,
    String? snippet,
    double? relevanceScore,
    List<String>? matchedTerms,
    SearchResultType? type,
    Map<String, dynamic>? metadata,
  }) {
    return SmartSearchResult(
      noteId: noteId ?? this.noteId,
      title: title ?? this.title,
      snippet: snippet ?? this.snippet,
      relevanceScore: relevanceScore ?? this.relevanceScore,
      matchedTerms: matchedTerms ?? this.matchedTerms,
      type: type ?? this.type,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'noteId': noteId,
      'title': title,
      'snippet': snippet,
      'relevanceScore': relevanceScore,
      'matchedTerms': matchedTerms,
      'type': type.name,
      'metadata': metadata,
    };
  }

  factory SmartSearchResult.fromJson(Map<String, dynamic> json) {
    return SmartSearchResult(
      noteId: json['noteId'],
      title: json['title'],
      snippet: json['snippet'],
      relevanceScore: json['relevanceScore'].toDouble(),
      matchedTerms: List<String>.from(json['matchedTerms']),
      type: SearchResultType.values.firstWhere((e) => e.name == json['type']),
      metadata: json['metadata'],
    );
  }
}

/// Types of search results
enum SearchResultType { exact, semantic, fuzzy, related }

/// Knowledge graph connection
class KnowledgeConnection {
  final String fromNoteId;
  final String toNoteId;
  final ConnectionType type;
  final double strength;
  final String description;
  final List<String> sharedConcepts;
  final DateTime createdAt;

  const KnowledgeConnection({
    required this.fromNoteId,
    required this.toNoteId,
    required this.type,
    required this.strength,
    required this.description,
    required this.sharedConcepts,
    required this.createdAt,
  });

  KnowledgeConnection copyWith({
    String? fromNoteId,
    String? toNoteId,
    ConnectionType? type,
    double? strength,
    String? description,
    List<String>? sharedConcepts,
    DateTime? createdAt,
  }) {
    return KnowledgeConnection(
      fromNoteId: fromNoteId ?? this.fromNoteId,
      toNoteId: toNoteId ?? this.toNoteId,
      type: type ?? this.type,
      strength: strength ?? this.strength,
      description: description ?? this.description,
      sharedConcepts: sharedConcepts ?? this.sharedConcepts,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fromNoteId': fromNoteId,
      'toNoteId': toNoteId,
      'type': type.name,
      'strength': strength,
      'description': description,
      'sharedConcepts': sharedConcepts,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory KnowledgeConnection.fromJson(Map<String, dynamic> json) {
    return KnowledgeConnection(
      fromNoteId: json['fromNoteId'],
      toNoteId: json['toNoteId'],
      type: ConnectionType.values.firstWhere((e) => e.name == json['type']),
      strength: json['strength'].toDouble(),
      description: json['description'],
      sharedConcepts: List<String>.from(json['sharedConcepts']),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Types of knowledge connections
enum ConnectionType { similar, related, reference, followUp, contradiction }

/// Search analytics data
class SearchAnalytics {
  final int totalSearches;
  final int uniqueQueries;
  final double averageQueryLength;
  final List<QueryFrequency> topQueries;
  final List<SearchPattern> searchPatterns;
  final Map<String, int> queryCategories;
  final List<SearchTrend> searchTrends;
  final DateTime generatedAt;

  const SearchAnalytics({
    required this.totalSearches,
    required this.uniqueQueries,
    required this.averageQueryLength,
    required this.topQueries,
    required this.searchPatterns,
    required this.queryCategories,
    required this.searchTrends,
    required this.generatedAt,
  });

  SearchAnalytics copyWith({
    int? totalSearches,
    int? uniqueQueries,
    double? averageQueryLength,
    List<QueryFrequency>? topQueries,
    List<SearchPattern>? searchPatterns,
    Map<String, int>? queryCategories,
    List<SearchTrend>? searchTrends,
    DateTime? generatedAt,
  }) {
    return SearchAnalytics(
      totalSearches: totalSearches ?? this.totalSearches,
      uniqueQueries: uniqueQueries ?? this.uniqueQueries,
      averageQueryLength: averageQueryLength ?? this.averageQueryLength,
      topQueries: topQueries ?? this.topQueries,
      searchPatterns: searchPatterns ?? this.searchPatterns,
      queryCategories: queryCategories ?? this.queryCategories,
      searchTrends: searchTrends ?? this.searchTrends,
      generatedAt: generatedAt ?? this.generatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalSearches': totalSearches,
      'uniqueQueries': uniqueQueries,
      'averageQueryLength': averageQueryLength,
      'topQueries': topQueries.map((q) => q.toJson()).toList(),
      'searchPatterns': searchPatterns.map((p) => p.toJson()).toList(),
      'queryCategories': queryCategories,
      'searchTrends': searchTrends.map((t) => t.toJson()).toList(),
      'generatedAt': generatedAt.toIso8601String(),
    };
  }

  factory SearchAnalytics.fromJson(Map<String, dynamic> json) {
    return SearchAnalytics(
      totalSearches: json['totalSearches'],
      uniqueQueries: json['uniqueQueries'],
      averageQueryLength: json['averageQueryLength'].toDouble(),
      topQueries: (json['topQueries'] as List)
          .map((q) => QueryFrequency.fromJson(q))
          .toList(),
      searchPatterns: (json['searchPatterns'] as List)
          .map((p) => SearchPattern.fromJson(p))
          .toList(),
      queryCategories: Map<String, int>.from(json['queryCategories']),
      searchTrends: (json['searchTrends'] as List)
          .map((t) => SearchTrend.fromJson(t))
          .toList(),
      generatedAt: DateTime.parse(json['generatedAt']),
    );
  }
}

/// Query frequency data
class QueryFrequency {
  final String query;
  final int count;
  final double percentage;

  const QueryFrequency({
    required this.query,
    required this.count,
    required this.percentage,
  });

  QueryFrequency copyWith({String? query, int? count, double? percentage}) {
    return QueryFrequency(
      query: query ?? this.query,
      count: count ?? this.count,
      percentage: percentage ?? this.percentage,
    );
  }

  Map<String, dynamic> toJson() {
    return {'query': query, 'count': count, 'percentage': percentage};
  }

  factory QueryFrequency.fromJson(Map<String, dynamic> json) {
    return QueryFrequency(
      query: json['query'],
      count: json['count'],
      percentage: json['percentage'].toDouble(),
    );
  }
}

/// Search pattern data
class SearchPattern {
  final String pattern;
  final int frequency;
  final String description;

  const SearchPattern({
    required this.pattern,
    required this.frequency,
    required this.description,
  });

  SearchPattern copyWith({
    String? pattern,
    int? frequency,
    String? description,
  }) {
    return SearchPattern(
      pattern: pattern ?? this.pattern,
      frequency: frequency ?? this.frequency,
      description: description ?? this.description,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pattern': pattern,
      'frequency': frequency,
      'description': description,
    };
  }

  factory SearchPattern.fromJson(Map<String, dynamic> json) {
    return SearchPattern(
      pattern: json['pattern'],
      frequency: json['frequency'],
      description: json['description'],
    );
  }
}

/// Search trend data
class SearchTrend {
  final DateTime date;
  final int searchCount;
  final String topQuery;

  const SearchTrend({
    required this.date,
    required this.searchCount,
    required this.topQuery,
  });

  SearchTrend copyWith({DateTime? date, int? searchCount, String? topQuery}) {
    return SearchTrend(
      date: date ?? this.date,
      searchCount: searchCount ?? this.searchCount,
      topQuery: topQuery ?? this.topQuery,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'searchCount': searchCount,
      'topQuery': topQuery,
    };
  }

  factory SearchTrend.fromJson(Map<String, dynamic> json) {
    return SearchTrend(
      date: DateTime.parse(json['date']),
      searchCount: json['searchCount'],
      topQuery: json['topQuery'],
    );
  }
}
