import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Centralized Settings Service for all mini-apps
class SettingsService {
  static SharedPreferences? _prefs;

  static Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // Global Settings
  static Future<void> setThemeMode(ThemeMode mode) async {
    await _prefs?.setString('theme_mode', mode.name);
  }

  static ThemeMode getThemeMode() {
    final mode = _prefs?.getString('theme_mode') ?? 'system';
    return ThemeMode.values.firstWhere(
      (e) => e.name == mode,
      orElse: () => ThemeMode.system,
    );
  }

  static Future<void> setCustomThemeColor(Color color) async {
    await _prefs?.setInt('custom_theme_color', color.toARGB32());
  }

  static Color getCustomThemeColor() {
    final colorValue = _prefs?.getInt('custom_theme_color');
    return colorValue != null ? Color(colorValue) : Colors.blue;
  }

  static Future<void> setFontSize(double size) async {
    await _prefs?.setDouble('font_size', size);
  }

  static double getFontSize() {
    return _prefs?.getDouble('font_size') ?? 14.0;
  }

  static Future<void> setLanguage(String languageCode) async {
    await _prefs?.setString('language', languageCode);
  }

  static String getLanguage() {
    return _prefs?.getString('language') ?? 'en';
  }

  // Generic settings methods for launcher and other components
  static Future<void> setInt(String key, int value) async {
    await _prefs?.setInt(key, value);
  }

  static int? getInt(String key) {
    return _prefs?.getInt(key);
  }

  static Future<void> setBool(String key, bool value) async {
    await _prefs?.setBool(key, value);
  }

  static bool? getBool(String key) {
    return _prefs?.getBool(key);
  }

  static Future<void> setString(String key, String value) async {
    await _prefs?.setString(key, value);
  }

  static String? getString(String key) {
    return _prefs?.getString(key);
  }

  static Future<void> setDouble(String key, double value) async {
    await _prefs?.setDouble(key, value);
  }

  static double? getDouble(String key) {
    return _prefs?.getDouble(key);
  }

  static Future<void> setRTLEnabled(bool enabled) async {
    await _prefs?.setBool('rtl_enabled', enabled);
  }

  static bool getRTLEnabled() {
    return _prefs?.getBool('rtl_enabled') ?? false;
  }

  // App-specific Settings
  static Future<void> setAppSetting(
    String appId,
    String key,
    dynamic value,
  ) async {
    final settingsKey = '${appId}_$key';
    if (value is String) {
      await _prefs?.setString(settingsKey, value);
    } else if (value is int) {
      await _prefs?.setInt(settingsKey, value);
    } else if (value is double) {
      await _prefs?.setDouble(settingsKey, value);
    } else if (value is bool) {
      await _prefs?.setBool(settingsKey, value);
    } else if (value is List<String>) {
      await _prefs?.setStringList(settingsKey, value);
    } else {
      await _prefs?.setString(settingsKey, jsonEncode(value));
    }
  }

  static T? getAppSetting<T>(String appId, String key, {T? defaultValue}) {
    final settingsKey = '${appId}_$key';

    if (T == String) {
      return _prefs?.getString(settingsKey) as T? ?? defaultValue;
    } else if (T == int) {
      return _prefs?.getInt(settingsKey) as T? ?? defaultValue;
    } else if (T == double) {
      return _prefs?.getDouble(settingsKey) as T? ?? defaultValue;
    } else if (T == bool) {
      return _prefs?.getBool(settingsKey) as T? ?? defaultValue;
    } else if (T == List<String>) {
      return _prefs?.getStringList(settingsKey) as T? ?? defaultValue;
    } else {
      final jsonString = _prefs?.getString(settingsKey);
      if (jsonString != null) {
        try {
          return jsonDecode(jsonString) as T;
        } catch (e) {
          return defaultValue;
        }
      }
      return defaultValue;
    }
  }

  // Keyboard Shortcuts
  static Future<void> setKeyboardShortcut(
    String action,
    String shortcut,
  ) async {
    await _prefs?.setString('shortcut_$action', shortcut);
  }

  static String getKeyboardShortcut(
    String action, {
    String defaultShortcut = '',
  }) {
    return _prefs?.getString('shortcut_$action') ?? defaultShortcut;
  }

  // Backup Settings
  static Future<void> setAutoBackup(bool enabled) async {
    await _prefs?.setBool('auto_backup', enabled);
  }

  static bool getAutoBackup() {
    return _prefs?.getBool('auto_backup') ?? false;
  }

  static Future<void> setBackupInterval(int hours) async {
    await _prefs?.setInt('backup_interval', hours);
  }

  static int getBackupInterval() {
    return _prefs?.getInt('backup_interval') ?? 24;
  }

  static Future<void> setBackupLocation(String path) async {
    await _prefs?.setString('backup_location', path);
  }

  static String getBackupLocation() {
    return _prefs?.getString('backup_location') ?? '';
  }

  // Security Settings
  static Future<void> setPasswordProtection(bool enabled) async {
    await _prefs?.setBool('password_protection', enabled);
  }

  static bool getPasswordProtection() {
    return _prefs?.getBool('password_protection') ?? false;
  }

  static Future<void> setPasswordHash(String hash) async {
    await _prefs?.setString('password_hash', hash);
  }

  static String getPasswordHash() {
    return _prefs?.getString('password_hash') ?? '';
  }

  // Layout Settings
  static Future<void> setSidebarCollapsed(bool collapsed) async {
    await _prefs?.setBool('sidebar_collapsed', collapsed);
  }

  static bool getSidebarCollapsed() {
    return _prefs?.getBool('sidebar_collapsed') ?? false;
  }

  static Future<void> setCompactMode(bool enabled) async {
    await _prefs?.setBool('compact_mode', enabled);
  }

  static bool getCompactMode() {
    return _prefs?.getBool('compact_mode') ?? false;
  }

  // Performance Settings
  static Future<void> setAnimationsEnabled(bool enabled) async {
    await _prefs?.setBool('animations_enabled', enabled);
  }

  static bool getAnimationsEnabled() {
    return _prefs?.getBool('animations_enabled') ?? true;
  }

  static Future<void> setHighPerformanceMode(bool enabled) async {
    await _prefs?.setBool('high_performance_mode', enabled);
  }

  static bool getHighPerformanceMode() {
    return _prefs?.getBool('high_performance_mode') ?? false;
  }

  // Export/Import Settings
  static Future<Map<String, dynamic>> exportSettings() async {
    final keys = _prefs?.getKeys() ?? <String>{};
    final settings = <String, dynamic>{};

    for (final key in keys) {
      final value = _prefs?.get(key);
      if (value != null) {
        settings[key] = value;
      }
    }

    return settings;
  }

  static Future<void> importSettings(Map<String, dynamic> settings) async {
    for (final entry in settings.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value is String) {
        await _prefs?.setString(key, value);
      } else if (value is int) {
        await _prefs?.setInt(key, value);
      } else if (value is double) {
        await _prefs?.setDouble(key, value);
      } else if (value is bool) {
        await _prefs?.setBool(key, value);
      } else if (value is List<String>) {
        await _prefs?.setStringList(key, value);
      }
    }
  }

  static Future<void> resetAllSettings() async {
    await _prefs?.clear();
  }

  static Future<void> resetAppSettings(String appId) async {
    final keys = _prefs?.getKeys() ?? <String>{};
    final appKeys = keys.where((key) => key.startsWith('${appId}_'));

    for (final key in appKeys) {
      await _prefs?.remove(key);
    }
  }
}

// Settings Models
class AppSettings {
  final String appId;
  final String appName;
  final Map<String, SettingItem> settings;

  AppSettings({
    required this.appId,
    required this.appName,
    required this.settings,
  });
}

class SettingItem {
  final String key;
  final String title;
  final String description;
  final SettingType type;
  final dynamic defaultValue;
  final dynamic currentValue;
  final Map<String, dynamic>? options;

  SettingItem({
    required this.key,
    required this.title,
    required this.description,
    required this.type,
    required this.defaultValue,
    required this.currentValue,
    this.options,
  });
}

enum SettingType {
  boolean,
  string,
  number,
  color,
  dropdown,
  slider,
  multiSelect,
  keyboardShortcut,
}

// Providers
final settingsServiceProvider = Provider<SettingsService>((ref) {
  return SettingsService();
});

final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, ThemeMode>((
  ref,
) {
  return ThemeModeNotifier();
});

final customThemeColorProvider =
    StateNotifierProvider<CustomThemeColorNotifier, Color>((ref) {
      return CustomThemeColorNotifier();
    });

final fontSizeProvider = StateNotifierProvider<FontSizeNotifier, double>((ref) {
  return FontSizeNotifier();
});

final languageProvider = StateNotifierProvider<LanguageNotifier, String>((ref) {
  return LanguageNotifier();
});

// State Notifiers
class ThemeModeNotifier extends StateNotifier<ThemeMode> {
  ThemeModeNotifier() : super(SettingsService.getThemeMode());

  void setThemeMode(ThemeMode mode) {
    state = mode;
    SettingsService.setThemeMode(mode);
  }
}

class CustomThemeColorNotifier extends StateNotifier<Color> {
  CustomThemeColorNotifier() : super(SettingsService.getCustomThemeColor());

  void setColor(Color color) {
    state = color;
    SettingsService.setCustomThemeColor(color);
  }
}

class FontSizeNotifier extends StateNotifier<double> {
  FontSizeNotifier() : super(SettingsService.getFontSize());

  void setFontSize(double size) {
    state = size;
    SettingsService.setFontSize(size);
  }
}

class LanguageNotifier extends StateNotifier<String> {
  LanguageNotifier() : super(SettingsService.getLanguage());

  void setLanguage(String languageCode) {
    state = languageCode;
    SettingsService.setLanguage(languageCode);

    // Automatically enable RTL for Arabic
    if (languageCode == 'ar') {
      SettingsService.setRTLEnabled(true);
    } else {
      SettingsService.setRTLEnabled(false);
    }
  }
}

// RTL Provider
final rtlProvider = StateNotifierProvider<RTLNotifier, bool>((ref) {
  return RTLNotifier();
});

class RTLNotifier extends StateNotifier<bool> {
  RTLNotifier() : super(SettingsService.getRTLEnabled());

  void setRTL(bool enabled) {
    state = enabled;
    SettingsService.setRTLEnabled(enabled);
  }

  void toggleRTL() {
    state = !state;
    SettingsService.setRTLEnabled(state);
  }
}
