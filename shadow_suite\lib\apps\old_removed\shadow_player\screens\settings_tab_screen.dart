import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/media_providers.dart';

/// Settings tab screen for Shadow Player
class SettingsTabScreen extends ConsumerStatefulWidget {
  const SettingsTabScreen({super.key});

  @override
  ConsumerState<SettingsTabScreen> createState() => _SettingsTabScreenState();
}

class _SettingsTabScreenState extends ConsumerState<SettingsTabScreen> {
  @override
  Widget build(BuildContext context) {
    final settings = ref.watch(playerSettingsProvider);

    return ListView(
      children: [
        _buildSection('Playback', [
          _buildSwitchTile(
            'Auto-play next',
            'Automatically play the next track',
            settings.autoPlayNext,
            (value) {
              ref
                  .read(playerSettingsProvider.notifier)
                  .updateAutoPlayNext(value);
            },
          ),
          _buildSwitchTile(
            'Shuffle by default',
            'Enable shuffle mode by default',
            settings.shuffleByDefault,
            (value) {
              ref
                  .read(playerSettingsProvider.notifier)
                  .updateShuffleByDefault(value);
            },
          ),
          _buildSwitchTile(
            'Repeat by default',
            'Enable repeat mode by default',
            settings.repeatByDefault,
            (value) {
              ref
                  .read(playerSettingsProvider.notifier)
                  .updateRepeatByDefault(value);
            },
          ),
          _buildSliderTile(
            'Default volume',
            'Set the default playback volume',
            settings.defaultVolume,
            (value) {
              ref
                  .read(playerSettingsProvider.notifier)
                  .updateDefaultVolume(value);
            },
          ),
        ]),
        _buildSection('Library', [
          _buildListTile(
            'Scan folders',
            'Choose folders to scan for media',
            Icons.folder,
            () => _showScanFolders(),
          ),
          _buildListTile(
            'File formats',
            'Manage supported file formats',
            Icons.file_present,
            () => _showFileFormats(),
          ),
          _buildSwitchTile(
            'Auto-scan on startup',
            'Automatically scan for new media on app startup',
            true,
            (value) {
              // TODO: Update setting
            },
          ),
          _buildSwitchTile(
            'Watch folders',
            'Automatically detect new files in scan folders',
            false,
            (value) {
              // TODO: Update setting
            },
          ),
        ]),
        _buildSection('Interface', [
          _buildListTile(
            'Theme',
            'Choose app theme',
            Icons.palette,
            () => _showThemeSelector(),
          ),
          _buildSwitchTile(
            'Show thumbnails',
            'Display video thumbnails in lists',
            true,
            (value) {
              // TODO: Update setting
            },
          ),
          _buildSwitchTile(
            'Show file extensions',
            'Display file extensions in media lists',
            false,
            (value) {
              // TODO: Update setting
            },
          ),
        ]),
        _buildSection('Advanced', [
          _buildListTile(
            'Cache settings',
            'Manage thumbnail and metadata cache',
            Icons.storage,
            () => _showCacheSettings(),
          ),
          _buildListTile(
            'Export library',
            'Export your media library data',
            Icons.download,
            () => _exportLibrary(),
          ),
          _buildListTile(
            'Import library',
            'Import media library data',
            Icons.upload,
            () => _importLibrary(),
          ),
          _buildListTile(
            'Reset settings',
            'Reset all settings to default',
            Icons.restore,
            () => _resetSettings(),
          ),
        ]),
        _buildSection('About', [
          _buildListTile('Version', '1.0.0', Icons.info, null),
          _buildListTile(
            'Licenses',
            'View open source licenses',
            Icons.description,
            () => _showLicenses(),
          ),
          _buildListTile(
            'Privacy policy',
            'View privacy policy',
            Icons.privacy_tip,
            () => _showPrivacyPolicy(),
          ),
        ]),
      ],
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        ...children,
        const Divider(),
      ],
    );
  }

  Widget _buildListTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback? onTap,
  ) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: onTap != null ? const Icon(Icons.chevron_right) : null,
      onTap: onTap,
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
    );
  }

  Widget _buildSliderTile(
    String title,
    String subtitle,
    double value,
    ValueChanged<double> onChanged,
  ) {
    return ListTile(
      title: Text(title),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(subtitle),
          Slider(
            value: value,
            onChanged: onChanged,
            divisions: 10,
            label: '${(value * 100).round()}%',
          ),
        ],
      ),
    );
  }

  void _showScanFolders() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Scan Folders'),
        content: const Text('Folder selection coming soon'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showFileFormats() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supported File Formats'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Video: MP4, AVI, MKV, MOV, WMV'),
            Text('Audio: MP3, WAV, FLAC, AAC, OGG'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showThemeSelector() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Theme'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('System'),
              value: 'system',
              groupValue: 'system',
              onChanged: (value) {
                Navigator.pop(context);
                // TODO: Update theme
              },
            ),
            RadioListTile<String>(
              title: const Text('Light'),
              value: 'light',
              groupValue: 'system',
              onChanged: (value) {
                Navigator.pop(context);
                // TODO: Update theme
              },
            ),
            RadioListTile<String>(
              title: const Text('Dark'),
              value: 'dark',
              groupValue: 'system',
              onChanged: (value) {
                Navigator.pop(context);
                // TODO: Update theme
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showCacheSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cache Settings'),
        content: const Text('Cache management coming soon'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _exportLibrary() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Library export coming soon')));
  }

  void _importLibrary() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Library import coming soon')));
  }

  void _resetSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text(
          'Are you sure you want to reset all settings to default?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Settings reset to default')),
              );
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  void _showLicenses() {
    showLicensePage(context: context);
  }

  void _showPrivacyPolicy() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Privacy Policy'),
        content: const Text('Privacy policy content coming soon'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
