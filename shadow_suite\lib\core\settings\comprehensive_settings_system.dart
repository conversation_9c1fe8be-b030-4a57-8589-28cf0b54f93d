import 'dart:async';
import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'settings_models.dart';
import '../database/database_service.dart';
import '../services/error_handler.dart' as error_handler;

class ComprehensiveSettingsSystem {
  static final Map<String, SettingsCategory> _categories = {};
  static final Map<String, dynamic> _values = {};
  static final StreamController<SettingsChangeEvent> _changeController =
      StreamController<SettingsChangeEvent>.broadcast();

  // Initialize settings system
  static Future<void> initialize() async {
    await _registerAllCategories();
    await _loadSettings();
  }

  // Register all settings categories
  static Future<void> _registerAllCategories() async {
    // 1. General Settings
    _categories['general'] = SettingsCategory(
      id: 'general',
      name: 'General',
      description: 'Basic application settings',
      icon: 'settings',
      order: 1,
      settings: [
        SettingsItem(
          id: 'language',
          name: 'Language',
          description: 'Application language',
          type: SettingsType.dropdown,
          defaultValue: 'en',
          options: ['en', 'ar', 'fr', 'es', 'de', 'zh', 'ja', 'ko'],
          optionLabels: [
            'English',
            'العربية',
            'Français',
            'Español',
            'Deutsch',
            '中文',
            '日本語',
            '한국어',
          ],
          category: 'general',
        ),
        SettingsItem(
          id: 'startup_app',
          name: 'Default Startup App',
          description: 'Which app to open on startup',
          type: SettingsType.dropdown,
          defaultValue: 'dashboard',
          options: [
            'dashboard',
            'money_manager',
            'islamic_app',
            'memo_suite',
            'excel_to_app',
          ],
          optionLabels: [
            'Dashboard',
            'Money Manager',
            'Islamic App',
            'Memo Suite',
            'Excel to App',
          ],
          category: 'general',
        ),
        SettingsItem(
          id: 'auto_save_interval',
          name: 'Auto-save Interval',
          description: 'Automatic save frequency in minutes',
          type: SettingsType.slider,
          defaultValue: 5.0,
          minValue: 1.0,
          maxValue: 60.0,
          category: 'general',
        ),
        SettingsItem(
          id: 'enable_analytics',
          name: 'Enable Analytics',
          description: 'Help improve the app by sharing usage data',
          type: SettingsType.toggle,
          defaultValue: false,
          category: 'general',
        ),
      ],
    );

    // 2. Appearance Settings
    _categories['appearance'] = SettingsCategory(
      id: 'appearance',
      name: 'Appearance',
      description: 'Visual customization options',
      icon: 'palette',
      order: 2,
      settings: [
        SettingsItem(
          id: 'theme',
          name: 'Theme',
          description: 'Application theme',
          type: SettingsType.dropdown,
          defaultValue: 'default_dark',
          options: [
            'default_dark',
            'default_light',
            'high_contrast',
            'colorful',
            'minimal',
            'professional',
          ],
          optionLabels: [
            'Dark',
            'Light',
            'High Contrast',
            'Colorful',
            'Minimal',
            'Professional',
          ],
          category: 'appearance',
        ),
        SettingsItem(
          id: 'follow_system_theme',
          name: 'Follow System Theme',
          description: 'Automatically switch between light and dark themes',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'appearance',
        ),
        SettingsItem(
          id: 'font_size',
          name: 'Font Size',
          description: 'Text size throughout the application',
          type: SettingsType.dropdown,
          defaultValue: 'medium',
          options: ['small', 'medium', 'large', 'extra_large'],
          optionLabels: ['Small', 'Medium', 'Large', 'Extra Large'],
          category: 'appearance',
        ),
        SettingsItem(
          id: 'enable_animations',
          name: 'Enable Animations',
          description: 'Show smooth transitions and animations',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'appearance',
        ),
        SettingsItem(
          id: 'sidebar_width',
          name: 'Sidebar Width',
          description: 'Navigation sidebar width',
          type: SettingsType.slider,
          defaultValue: 280.0,
          minValue: 200.0,
          maxValue: 400.0,
          category: 'appearance',
        ),
      ],
    );

    // 3. Performance Settings
    _categories['performance'] = SettingsCategory(
      id: 'performance',
      name: 'Performance',
      description: 'Optimize app performance',
      icon: 'speed',
      order: 3,
      settings: [
        SettingsItem(
          id: 'enable_hardware_acceleration',
          name: 'Hardware Acceleration',
          description: 'Use GPU for better performance',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'performance',
        ),
        SettingsItem(
          id: 'cache_size_mb',
          name: 'Cache Size (MB)',
          description: 'Maximum cache size in megabytes',
          type: SettingsType.slider,
          defaultValue: 100.0,
          minValue: 50.0,
          maxValue: 500.0,
          category: 'performance',
        ),
        SettingsItem(
          id: 'preload_data',
          name: 'Preload Data',
          description: 'Load data in background for faster access',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'performance',
        ),
        SettingsItem(
          id: 'lazy_loading',
          name: 'Lazy Loading',
          description: 'Load content only when needed',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'performance',
        ),
      ],
    );

    // 4. Accessibility Settings
    _categories['accessibility'] = SettingsCategory(
      id: 'accessibility',
      name: 'Accessibility',
      description: 'Accessibility and usability options',
      icon: 'accessibility',
      order: 4,
      settings: [
        SettingsItem(
          id: 'screen_reader_support',
          name: 'Screen Reader Support',
          description: 'Enhanced support for screen readers',
          type: SettingsType.toggle,
          defaultValue: false,
          category: 'accessibility',
        ),
        SettingsItem(
          id: 'high_contrast_mode',
          name: 'High Contrast Mode',
          description: 'Increase contrast for better visibility',
          type: SettingsType.toggle,
          defaultValue: false,
          category: 'accessibility',
        ),
        SettingsItem(
          id: 'keyboard_navigation',
          name: 'Keyboard Navigation',
          description: 'Enable full keyboard navigation',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'accessibility',
        ),
        SettingsItem(
          id: 'haptic_feedback',
          name: 'Haptic Feedback',
          description: 'Vibration feedback for interactions',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'accessibility',
        ),
        SettingsItem(
          id: 'sound_feedback',
          name: 'Sound Feedback',
          description: 'Audio feedback for interactions',
          type: SettingsType.toggle,
          defaultValue: false,
          category: 'accessibility',
        ),
      ],
    );

    // 5. Data Management Settings
    _categories['data'] = SettingsCategory(
      id: 'data',
      name: 'Data Management',
      description: 'Backup, sync, and data options',
      icon: 'storage',
      order: 5,
      settings: [
        SettingsItem(
          id: 'auto_backup',
          name: 'Auto Backup',
          description: 'Automatically backup data',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'data',
        ),
        SettingsItem(
          id: 'backup_frequency',
          name: 'Backup Frequency',
          description: 'How often to backup data',
          type: SettingsType.dropdown,
          defaultValue: 'daily',
          options: ['hourly', 'daily', 'weekly', 'monthly'],
          optionLabels: ['Hourly', 'Daily', 'Weekly', 'Monthly'],
          category: 'data',
        ),
        SettingsItem(
          id: 'cloud_sync',
          name: 'Cloud Sync',
          description: 'Synchronize data across devices',
          type: SettingsType.toggle,
          defaultValue: false,
          category: 'data',
        ),
        SettingsItem(
          id: 'data_encryption',
          name: 'Data Encryption',
          description: 'Encrypt sensitive data',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'data',
        ),
        SettingsItem(
          id: 'export_format',
          name: 'Default Export Format',
          description: 'Preferred format for data export',
          type: SettingsType.dropdown,
          defaultValue: 'json',
          options: ['json', 'csv', 'xlsx', 'pdf'],
          optionLabels: ['JSON', 'CSV', 'Excel', 'PDF'],
          category: 'data',
        ),
      ],
    );

    // 6. Security Settings
    _categories['security'] = SettingsCategory(
      id: 'security',
      name: 'Security',
      description: 'Privacy and security options',
      icon: 'security',
      order: 6,
      settings: [
        SettingsItem(
          id: 'enable_app_lock',
          name: 'App Lock',
          description: 'Require authentication to open app',
          type: SettingsType.toggle,
          defaultValue: false,
          category: 'security',
        ),
        SettingsItem(
          id: 'lock_timeout',
          name: 'Auto-lock Timeout',
          description: 'Time before app locks automatically',
          type: SettingsType.dropdown,
          defaultValue: '5',
          options: ['1', '5', '10', '30', 'never'],
          optionLabels: [
            '1 minute',
            '5 minutes',
            '10 minutes',
            '30 minutes',
            'Never',
          ],
          category: 'security',
        ),
        SettingsItem(
          id: 'biometric_auth',
          name: 'Biometric Authentication',
          description: 'Use fingerprint or face recognition',
          type: SettingsType.toggle,
          defaultValue: false,
          category: 'security',
        ),
        SettingsItem(
          id: 'session_timeout',
          name: 'Session Timeout',
          description: 'Automatic logout after inactivity',
          type: SettingsType.slider,
          defaultValue: 30.0,
          minValue: 5.0,
          maxValue: 120.0,
          category: 'security',
        ),
      ],
    );

    // 7. Notifications Settings
    _categories['notifications'] = SettingsCategory(
      id: 'notifications',
      name: 'Notifications',
      description: 'Notification preferences',
      icon: 'notifications',
      order: 7,
      settings: [
        SettingsItem(
          id: 'enable_notifications',
          name: 'Enable Notifications',
          description: 'Show app notifications',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'notifications',
        ),
        SettingsItem(
          id: 'notification_sound',
          name: 'Notification Sound',
          description: 'Play sound for notifications',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'notifications',
        ),
        SettingsItem(
          id: 'quiet_hours_start',
          name: 'Quiet Hours Start',
          description: 'Start time for quiet hours',
          type: SettingsType.time,
          defaultValue: '22:00',
          category: 'notifications',
        ),
        SettingsItem(
          id: 'quiet_hours_end',
          name: 'Quiet Hours End',
          description: 'End time for quiet hours',
          type: SettingsType.time,
          defaultValue: '07:00',
          category: 'notifications',
        ),
      ],
    );

    // 8. Tools Builder Settings
    _categories['tools_builder'] = SettingsCategory(
      id: 'tools_builder',
      name: 'Tools Builder',
      description: 'Excel to App conversion and spreadsheet tools',
      icon: 'build',
      order: 8,
      settings: [
        SettingsItem(
          id: 'enable_advanced_formulas',
          name: 'Advanced Formulas',
          description: 'Enable complex Excel formula support',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'tools_builder',
        ),
        SettingsItem(
          id: 'enable_real_time_calculation',
          name: 'Real-time Calculation',
          description: 'Calculate formulas as you type',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'tools_builder',
        ),
        SettingsItem(
          id: 'enable_formula_autocomplete',
          name: 'Formula Auto-complete',
          description: 'Show function suggestions while typing',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'tools_builder',
        ),
        SettingsItem(
          id: 'enable_cell_validation',
          name: 'Cell Validation',
          description: 'Validate cell data types and ranges',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'tools_builder',
        ),
        SettingsItem(
          id: 'enable_chart_generation',
          name: 'Chart Generation',
          description: 'Generate charts from spreadsheet data',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'tools_builder',
        ),
        SettingsItem(
          id: 'enable_macro_support',
          name: 'Macro Support',
          description: 'Enable Excel macro functionality',
          type: SettingsType.toggle,
          defaultValue: false,
          category: 'tools_builder',
        ),
        SettingsItem(
          id: 'enable_pivot_tables',
          name: 'Pivot Tables',
          description: 'Enable pivot table creation and editing',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'tools_builder',
        ),
        SettingsItem(
          id: 'max_cells_per_sheet',
          name: 'Max Cells per Sheet',
          description: 'Maximum number of cells allowed per sheet',
          type: SettingsType.number,
          defaultValue: 100000,
          minValue: 1000,
          maxValue: 1000000,
          category: 'tools_builder',
        ),
        SettingsItem(
          id: 'calculation_timeout',
          name: 'Calculation Timeout',
          description: 'Maximum time for formula calculations (seconds)',
          type: SettingsType.slider,
          defaultValue: 30,
          minValue: 5,
          maxValue: 300,
          unit: 'seconds',
          category: 'tools_builder',
        ),
        SettingsItem(
          id: 'auto_save_interval_tools',
          name: 'Auto-save Interval',
          description: 'How often to auto-save tools (minutes)',
          type: SettingsType.slider,
          defaultValue: 5,
          minValue: 1,
          maxValue: 60,
          unit: 'minutes',
          category: 'tools_builder',
        ),
        SettingsItem(
          id: 'default_tool_theme',
          name: 'Default Tool Theme',
          description: 'Default theme for new tools',
          type: SettingsType.dropdown,
          defaultValue: 'modern',
          options: ['modern', 'classic', 'minimal', 'colorful', 'professional'],
          optionLabels: [
            'Modern',
            'Classic',
            'Minimal',
            'Colorful',
            'Professional',
          ],
          category: 'tools_builder',
        ),
        SettingsItem(
          id: 'enable_touch_gestures',
          name: 'Touch Gestures',
          description: 'Enable touch gestures for mobile devices',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'tools_builder',
        ),
        SettingsItem(
          id: 'enable_keyboard_shortcuts',
          name: 'Keyboard Shortcuts',
          description: 'Enable Excel-style keyboard shortcuts',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'tools_builder',
        ),
      ],
    );

    // 9. Money Manager Settings
    _categories['money_manager'] = SettingsCategory(
      id: 'money_manager',
      name: 'Money Manager',
      description: 'Financial app preferences',
      icon: 'account_balance_wallet',
      order: 9,
      settings: [
        SettingsItem(
          id: 'default_currency',
          name: 'Default Currency',
          description: 'Primary currency for transactions',
          type: SettingsType.dropdown,
          defaultValue: 'USD',
          options: ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF', 'CNY'],
          optionLabels: [
            'US Dollar',
            'Euro',
            'British Pound',
            'Japanese Yen',
            'Canadian Dollar',
            'Australian Dollar',
            'Swiss Franc',
            'Chinese Yuan',
          ],
          category: 'money_manager',
        ),
        SettingsItem(
          id: 'budget_alerts',
          name: 'Budget Alerts',
          description: 'Notify when approaching budget limits',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'money_manager',
        ),
        SettingsItem(
          id: 'investment_updates',
          name: 'Investment Updates',
          description: 'Real-time investment price updates',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'money_manager',
        ),
        SettingsItem(
          id: 'tax_year',
          name: 'Tax Year',
          description: 'Current tax year for calculations',
          type: SettingsType.dropdown,
          defaultValue: '2024',
          options: ['2022', '2023', '2024', '2025'],
          optionLabels: ['2022', '2023', '2024', '2025'],
          category: 'money_manager',
        ),
      ],
    );

    // 10. Islamic App Settings
    _categories['islamic_app'] = SettingsCategory(
      id: 'islamic_app',
      name: 'Islamic App',
      description: 'Islamic app preferences',
      icon: 'mosque',
      order: 10,
      settings: [
        SettingsItem(
          id: 'prayer_calculation_method',
          name: 'Prayer Calculation Method',
          description: 'Method for calculating prayer times',
          type: SettingsType.dropdown,
          defaultValue: 'muslim_world_league',
          options: [
            'muslim_world_league',
            'egyptian',
            'karachi',
            'umm_al_qura',
          ],
          optionLabels: [
            'Muslim World League',
            'Egyptian General Authority',
            'University of Islamic Sciences, Karachi',
            'Umm Al-Qura University, Makkah',
          ],
          category: 'islamic_app',
        ),
        SettingsItem(
          id: 'enable_prayer_notifications',
          name: 'Prayer Notifications',
          description: 'Notify for prayer times',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'islamic_app',
        ),
        SettingsItem(
          id: 'quran_translation',
          name: 'Default Quran Translation',
          description: 'Preferred translation for Quran reading',
          type: SettingsType.dropdown,
          defaultValue: 'sahih_international',
          options: ['sahih_international', 'pickthall', 'yusuf_ali', 'shakir'],
          optionLabels: [
            'Sahih International',
            'Pickthall',
            'Yusuf Ali',
            'Shakir',
          ],
          category: 'islamic_app',
        ),
        SettingsItem(
          id: 'hijri_calendar',
          name: 'Show Hijri Calendar',
          description: 'Display Islamic calendar dates',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'islamic_app',
        ),
      ],
    );

    // 11. ShadowPlayer Settings
    _categories['shadow_player'] = SettingsCategory(
      id: 'shadow_player',
      name: 'ShadowPlayer',
      description: 'Media player and library settings',
      icon: 'play_circle',
      order: 11,
      settings: [
        SettingsItem(
          id: 'auto_scan_enabled',
          name: 'Auto-scan on startup',
          description: 'Automatically scan for new media files when app starts',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'shadow_player',
        ),
        SettingsItem(
          id: 'generate_thumbnails',
          name: 'Generate thumbnails',
          description: 'Create preview thumbnails for video files',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'shadow_player',
        ),
        SettingsItem(
          id: 'background_playback',
          name: 'Background playback',
          description: 'Continue playing audio when app is minimized',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'shadow_player',
        ),
        SettingsItem(
          id: 'video_quality',
          name: 'Default video quality',
          description: 'Preferred video playback quality',
          type: SettingsType.dropdown,
          defaultValue: 'auto',
          options: ['auto', '480p', '720p', '1080p', '4k'],
          optionLabels: ['Auto', '480p', '720p', '1080p', '4K'],
          category: 'shadow_player',
        ),
        SettingsItem(
          id: 'audio_quality',
          name: 'Audio quality',
          description: 'Audio playback quality setting',
          type: SettingsType.dropdown,
          defaultValue: 'high',
          options: ['low', 'medium', 'high', 'lossless'],
          optionLabels: [
            'Low (128kbps)',
            'Medium (256kbps)',
            'High (320kbps)',
            'Lossless',
          ],
          category: 'shadow_player',
        ),
        SettingsItem(
          id: 'equalizer_enabled',
          name: 'Enable equalizer',
          description: 'Use audio equalizer for enhanced sound',
          type: SettingsType.toggle,
          defaultValue: false,
          category: 'shadow_player',
        ),
        SettingsItem(
          id: 'crossfade_duration',
          name: 'Crossfade duration',
          description: 'Duration for crossfading between tracks (seconds)',
          type: SettingsType.slider,
          defaultValue: 3.0,
          minValue: 0.0,
          maxValue: 10.0,
          category: 'shadow_player',
        ),
      ],
    );

    // 12. Advanced Settings
    _categories['advanced'] = SettingsCategory(
      id: 'advanced',
      name: 'Advanced',
      description: 'Advanced configuration options',
      icon: 'settings_applications',
      order: 12,
      settings: [
        SettingsItem(
          id: 'developer_mode',
          name: 'Developer Mode',
          description: 'Enable advanced developer features',
          type: SettingsType.toggle,
          defaultValue: false,
          category: 'advanced',
        ),
        SettingsItem(
          id: 'debug_logging',
          name: 'Debug Logging',
          description: 'Enable detailed logging for troubleshooting',
          type: SettingsType.toggle,
          defaultValue: false,
          category: 'advanced',
        ),
        SettingsItem(
          id: 'experimental_features',
          name: 'Experimental Features',
          description: 'Enable beta and experimental features',
          type: SettingsType.toggle,
          defaultValue: false,
          category: 'advanced',
        ),
        SettingsItem(
          id: 'memory_optimization',
          name: 'Memory Optimization',
          description: 'Optimize memory usage for better performance',
          type: SettingsType.toggle,
          defaultValue: true,
          category: 'advanced',
        ),
      ],
    );
  }

  // Settings management methods
  static T getValue<T>(String settingId, {T? defaultValue}) {
    if (_values.containsKey(settingId)) {
      return _values[settingId] as T;
    }

    // Find setting and return its default value
    for (final category in _categories.values) {
      final setting = category.settings.firstWhere(
        (s) => s.id == settingId,
        orElse: () => throw ArgumentError('Setting not found: $settingId'),
      );
      return (defaultValue ?? setting.defaultValue) as T;
    }

    throw ArgumentError('Setting not found: $settingId');
  }

  static Future<void> setValue(String settingId, dynamic value) async {
    try {
      final oldValue = _values[settingId];
      _values[settingId] = value;

      // Save to database
      await DatabaseService.safeInsert('settings', {
        'id': settingId,
        'value': jsonEncode(value),
        'updated_at': DateTime.now().toIso8601String(),
      }, conflictAlgorithm: ConflictAlgorithm.replace);

      // Notify listeners
      _changeController.add(
        SettingsChangeEvent(
          settingId: settingId,
          oldValue: oldValue,
          newValue: value,
          timestamp: DateTime.now(),
        ),
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.database,
        context: 'Save setting: $settingId',
      );
      rethrow;
    }
  }

  static Future<void> resetToDefault(String settingId) async {
    for (final category in _categories.values) {
      final setting = category.settings.firstWhere(
        (s) => s.id == settingId,
        orElse: () => throw ArgumentError('Setting not found: $settingId'),
      );
      await setValue(settingId, setting.defaultValue);
      return;
    }
  }

  static Future<void> resetCategory(String categoryId) async {
    final category = _categories[categoryId];
    if (category == null) return;

    for (final setting in category.settings) {
      await setValue(setting.id, setting.defaultValue);
    }
  }

  static Future<void> resetAll() async {
    for (final category in _categories.values) {
      for (final setting in category.settings) {
        await setValue(setting.id, setting.defaultValue);
      }
    }
  }

  static Future<Map<String, dynamic>> exportSettings() async {
    return Map<String, dynamic>.from(_values);
  }

  static Future<void> importSettings(Map<String, dynamic> settings) async {
    for (final entry in settings.entries) {
      await setValue(entry.key, entry.value);
    }
  }

  static Future<void> _loadSettings() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM settings');
      for (final row in results) {
        final settingId = row['id'] as String;
        final valueJson = row['value'] as String;
        _values[settingId] = jsonDecode(valueJson);
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Load settings',
      );
    }
  }

  // Getters
  static List<SettingsCategory> get categories =>
      _categories.values.toList()..sort((a, b) => a.order.compareTo(b.order));

  static SettingsCategory? getCategory(String categoryId) =>
      _categories[categoryId];

  static Stream<SettingsChangeEvent> get changeStream =>
      _changeController.stream;

  // Dispose
  static void dispose() {
    _changeController.close();
    _categories.clear();
    _values.clear();
  }
}
