import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/spreadsheet.dart';
import '../models/spreadsheet_cell.dart';
import '../services/tools_builder_providers.dart';

class SpreadsheetEditor extends ConsumerStatefulWidget {
  final Spreadsheet spreadsheet;
  final Function(Spreadsheet) onSpreadsheetChanged;

  const SpreadsheetEditor({
    super.key,
    required this.spreadsheet,
    required this.onSpreadsheetChanged,
  });

  @override
  ConsumerState<SpreadsheetEditor> createState() => _SpreadsheetEditorState();
}

class _SpreadsheetEditorState extends ConsumerState<SpreadsheetEditor> {
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();
  final TextEditingController _formulaController = TextEditingController();

  static const double cellWidth = 100.0;
  static const double cellHeight = 30.0;
  static const double headerHeight = 40.0;
  static const double rowHeaderWidth = 50.0;

  @override
  void initState() {
    super.initState();
    _updateFormulaBar();
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    _formulaController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildToolbar(),
        _buildFormulaBar(),
        Expanded(child: _buildSpreadsheetGrid()),
      ],
    );
  }

  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          // File operations
          IconButton(
            onPressed: () => _addSheet(),
            icon: const Icon(Icons.add),
            tooltip: 'Add Sheet',
          ),
          const VerticalDivider(),

          // Formatting tools
          Consumer(
            builder: (context, ref, child) {
              final selectedCell = ref.watch(selectedCellProvider);
              final activeSheet = widget.spreadsheet.activeSheet;
              final cell = selectedCell != null
                  ? activeSheet?.cells[selectedCell.toAddress()]
                  : null;
              final isBold = cell?.format.isBold ?? false;

              return IconButton(
                onPressed: () => _formatBold(),
                icon: const Icon(Icons.format_bold),
                tooltip: 'Bold',
                style: IconButton.styleFrom(
                  backgroundColor: isBold
                      ? Colors.blue.withValues(alpha: 0.2)
                      : null,
                ),
              );
            },
          ),
          Consumer(
            builder: (context, ref, child) {
              final selectedCell = ref.watch(selectedCellProvider);
              final activeSheet = widget.spreadsheet.activeSheet;
              final cell = selectedCell != null
                  ? activeSheet?.cells[selectedCell.toAddress()]
                  : null;
              final isItalic = cell?.format.isItalic ?? false;

              return IconButton(
                onPressed: () => _formatItalic(),
                icon: const Icon(Icons.format_italic),
                tooltip: 'Italic',
                style: IconButton.styleFrom(
                  backgroundColor: isItalic
                      ? Colors.blue.withValues(alpha: 0.2)
                      : null,
                ),
              );
            },
          ),
          Consumer(
            builder: (context, ref, child) {
              final selectedCell = ref.watch(selectedCellProvider);
              final activeSheet = widget.spreadsheet.activeSheet;
              final cell = selectedCell != null
                  ? activeSheet?.cells[selectedCell.toAddress()]
                  : null;
              final isUnderline = cell?.format.isUnderline ?? false;

              return IconButton(
                onPressed: () => _formatUnderline(),
                icon: const Icon(Icons.format_underlined),
                tooltip: 'Underline',
                style: IconButton.styleFrom(
                  backgroundColor: isUnderline
                      ? Colors.blue.withValues(alpha: 0.2)
                      : null,
                ),
              );
            },
          ),
          const VerticalDivider(),

          // Alignment
          IconButton(
            onPressed: () => _alignLeft(),
            icon: const Icon(Icons.format_align_left),
            tooltip: 'Align Left',
          ),
          IconButton(
            onPressed: () => _alignCenter(),
            icon: const Icon(Icons.format_align_center),
            tooltip: 'Align Center',
          ),
          IconButton(
            onPressed: () => _alignRight(),
            icon: const Icon(Icons.format_align_right),
            tooltip: 'Align Right',
          ),
          const VerticalDivider(),

          // Functions
          IconButton(
            onPressed: () => _insertFunction(),
            icon: const Icon(Icons.functions),
            tooltip: 'Insert Function',
          ),
          const VerticalDivider(),

          // Data tools
          IconButton(
            onPressed: () => _sortData(),
            icon: const Icon(Icons.sort),
            tooltip: 'Sort Data',
          ),
          IconButton(
            onPressed: () => _filterData(),
            icon: const Icon(Icons.filter_list),
            tooltip: 'Filter Data',
          ),
          IconButton(
            onPressed: () => _insertChart(),
            icon: const Icon(Icons.bar_chart),
            tooltip: 'Insert Chart',
          ),
          const VerticalDivider(),

          // Cell operations
          IconButton(
            onPressed: () => _mergeCells(),
            icon: const Icon(Icons.call_merge),
            tooltip: 'Merge Cells',
          ),
          IconButton(
            onPressed: () => _insertRow(),
            icon: const Icon(Icons.table_rows),
            tooltip: 'Insert Row',
          ),
          IconButton(
            onPressed: () => _insertColumn(),
            icon: const Icon(Icons.view_column),
            tooltip: 'Insert Column',
          ),

          const Spacer(),

          // Sheet tabs
          _buildSheetTabs(),
        ],
      ),
    );
  }

  Widget _buildFormulaBar() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          // Cell address
          Consumer(
            builder: (context, ref, child) {
              final selectedCell = ref.watch(selectedCellProvider);
              return Container(
                width: 80,
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[400]!),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  selectedCell?.toAddress() ?? 'A1',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              );
            },
          ),
          const SizedBox(width: 8),

          // Formula bar
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[400]!),
                borderRadius: BorderRadius.circular(4),
                color: Colors.white,
              ),
              child: Row(
                children: [
                  const Text(
                    'fx',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: _formulaController,
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        hintText: 'Enter formula or value...',
                      ),
                      onSubmitted: (value) => _updateCellValue(value),
                      onChanged: (value) {
                        ref.read(formulaBarTextProvider.notifier).state = value;
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSheetTabs() {
    return Row(
      children: widget.spreadsheet.sheets.asMap().entries.map((entry) {
        final index = entry.key;
        final sheet = entry.value;
        final isActive = index == widget.spreadsheet.activeSheetIndex;

        return GestureDetector(
          onTap: () => _switchSheet(index),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            margin: const EdgeInsets.only(right: 4),
            decoration: BoxDecoration(
              color: isActive ? Colors.white : Colors.grey[200],
              border: Border.all(color: Colors.grey[400]!),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(4),
              ),
            ),
            child: Text(
              sheet.name,
              style: TextStyle(
                fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSpreadsheetGrid() {
    final activeSheet = widget.spreadsheet.activeSheet;
    if (activeSheet == null) {
      return const Center(child: Text('No active sheet'));
    }

    return Container(
      decoration: BoxDecoration(border: Border.all(color: Colors.grey[300]!)),
      child: Column(
        children: [
          // Column headers
          SizedBox(
            height: headerHeight,
            child: Row(
              children: [
                // Corner cell
                Container(
                  width: rowHeaderWidth,
                  height: headerHeight,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    border: Border(
                      right: BorderSide(color: Colors.grey[400]!),
                      bottom: BorderSide(color: Colors.grey[400]!),
                    ),
                  ),
                ),
                // Column headers
                Expanded(
                  child: Scrollbar(
                    controller: _horizontalController,
                    scrollbarOrientation: ScrollbarOrientation.bottom,
                    child: SingleChildScrollView(
                      controller: _horizontalController,
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: List.generate(26, (index) {
                          final letter = String.fromCharCode(65 + index);
                          return Container(
                            width: cellWidth,
                            height: headerHeight,
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              border: Border(
                                right: BorderSide(color: Colors.grey[400]!),
                                bottom: BorderSide(color: Colors.grey[400]!),
                              ),
                            ),
                            child: Center(
                              child: Text(
                                letter,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          );
                        }),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Grid rows
          Expanded(
            child: Row(
              children: [
                // Row headers
                SizedBox(
                  width: rowHeaderWidth,
                  child: Scrollbar(
                    controller: _verticalController,
                    child: SingleChildScrollView(
                      controller: _verticalController,
                      child: Column(
                        children: List.generate(100, (index) {
                          final rowNumber = index + 1;
                          return Container(
                            width: rowHeaderWidth,
                            height: cellHeight,
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              border: Border(
                                right: BorderSide(color: Colors.grey[400]!),
                                bottom: BorderSide(color: Colors.grey[400]!),
                              ),
                            ),
                            child: Center(
                              child: Text(
                                rowNumber.toString(),
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          );
                        }),
                      ),
                    ),
                  ),
                ),
                // Grid cells
                Expanded(
                  child: Scrollbar(
                    controller: _horizontalController,
                    scrollbarOrientation: ScrollbarOrientation.bottom,
                    child: Scrollbar(
                      controller: _verticalController,
                      child: SingleChildScrollView(
                        controller: _horizontalController,
                        scrollDirection: Axis.horizontal,
                        child: SingleChildScrollView(
                          controller: _verticalController,
                          child: _buildCellGrid(activeSheet),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCellGrid(SpreadsheetSheet sheet) {
    return Column(
      children: List.generate(100, (rowIndex) {
        final row = rowIndex + 1;
        return Row(
          children: List.generate(26, (colIndex) {
            final col = colIndex + 1;
            final cellAddress = '${SpreadsheetCell.columnToLetter(col)}$row';
            final cell = sheet.getCell(row, col);

            return _buildCell(cellAddress, cell);
          }),
        );
      }),
    );
  }

  Widget _buildCell(String cellAddress, SpreadsheetCell? cell) {
    return Consumer(
      builder: (context, ref, child) {
        final selectedCell = ref.watch(selectedCellProvider);
        final isSelected = selectedCell?.toAddress() == cellAddress;

        return GestureDetector(
          onTap: () => _selectCell(cellAddress),
          child: Container(
            width: cellWidth,
            height: cellHeight,
            decoration: BoxDecoration(
              color: isSelected
                  ? Colors.blue.withValues(alpha: 0.1)
                  : Colors.white,
              border: Border.all(
                color: isSelected ? Colors.blue : Colors.grey[400]!,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: TextField(
              controller: TextEditingController(
                text: cell?.calculatedValue?.toString() ?? cell?.rawValue ?? '',
              ),
              decoration: const InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 4,
                  vertical: 2,
                ),
              ),
              style: TextStyle(
                fontSize: 12,
                fontWeight: cell?.format.isBold == true
                    ? FontWeight.bold
                    : FontWeight.normal,
                fontStyle: cell?.format.isItalic == true
                    ? FontStyle.italic
                    : FontStyle.normal,
              ),
              onSubmitted: (value) => _updateCellValue(value),
              onTap: () => _selectCell(cellAddress),
            ),
          ),
        );
      },
    );
  }

  void _selectCell(String cellAddress) {
    try {
      ref.read(selectedCellProvider.notifier).state = CellPosition.fromAddress(
        cellAddress,
      );
    } catch (e) {
      // Invalid cell address, ignore
    }
    _updateFormulaBar();
  }

  void _updateFormulaBar() {
    final selectedCell = ref.read(selectedCellProvider);
    if (selectedCell != null) {
      final activeSheet = widget.spreadsheet.activeSheet;
      if (activeSheet != null) {
        final cell = activeSheet.cells[selectedCell.toAddress()];
        _formulaController.text = cell?.rawValue ?? '';
      }
    }
  }

  void _updateCellValue(String value) {
    final selectedCell = ref.read(selectedCellProvider);
    if (selectedCell != null) {
      ref
          .read(spreadsheetCalculationProvider.notifier)
          .updateCell(selectedCell.toAddress(), value);

      // Get updated spreadsheet and notify parent
      final updatedSpreadsheet = ref.read(spreadsheetCalculationProvider);
      if (updatedSpreadsheet != null) {
        widget.onSpreadsheetChanged(updatedSpreadsheet);
      }
    }
  }

  void _addSheet() {
    final newSheet = SpreadsheetSheet(
      name: 'Sheet${widget.spreadsheet.sheets.length + 1}',
      cells: {},
    );

    final updatedSheets = [...widget.spreadsheet.sheets, newSheet];
    final updatedSpreadsheet = widget.spreadsheet.copyWith(
      sheets: updatedSheets,
    );
    widget.onSpreadsheetChanged(updatedSpreadsheet);
  }

  void _switchSheet(int index) {
    final updatedSpreadsheet = widget.spreadsheet.copyWith(
      activeSheetIndex: index,
    );
    widget.onSpreadsheetChanged(updatedSpreadsheet);
  }

  void _formatBold() {
    _applyFormatting((format) => format.copyWith(isBold: !format.isBold));
  }

  void _formatItalic() {
    _applyFormatting((format) => format.copyWith(isItalic: !format.isItalic));
  }

  void _formatUnderline() {
    _applyFormatting(
      (format) => format.copyWith(isUnderline: !format.isUnderline),
    );
  }

  void _alignLeft() {
    _applyFormatting(
      (format) => format.copyWith(alignment: CellAlignment.left),
    );
  }

  void _alignCenter() {
    _applyFormatting(
      (format) => format.copyWith(alignment: CellAlignment.center),
    );
  }

  void _alignRight() {
    _applyFormatting(
      (format) => format.copyWith(alignment: CellAlignment.right),
    );
  }

  void _applyFormatting(CellFormat Function(CellFormat) formatFunction) {
    final selectedCell = ref.read(selectedCellProvider);
    if (selectedCell == null) return;

    final activeSheet = widget.spreadsheet.activeSheet;
    if (activeSheet == null) return;

    final cell = activeSheet.cells[selectedCell.toAddress()];
    final currentFormat = cell?.format ?? const CellFormat();
    final newFormat = formatFunction(currentFormat);

    if (cell != null) {
      final updatedCell = cell.copyWith(format: newFormat);
      final updatedSheet = activeSheet.setCell(
        updatedCell.row,
        updatedCell.column,
        updatedCell,
      );
      final updatedSheets = widget.spreadsheet.sheets
          .map((s) => s.id == updatedSheet.id ? updatedSheet : s)
          .toList();
      final updatedSpreadsheet = widget.spreadsheet.copyWith(
        sheets: updatedSheets,
      );
      widget.onSpreadsheetChanged(updatedSpreadsheet);
    }
  }

  void _insertFunction() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Insert Function'),
        content: SizedBox(
          width: 400,
          height: 300,
          child: Column(
            children: [
              TextField(
                decoration: const InputDecoration(
                  hintText: 'Search functions...',
                  prefixIcon: Icon(Icons.search),
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView(
                  children: [
                    _buildFunctionCategory('Mathematical', [
                      'SUM',
                      'AVERAGE',
                      'COUNT',
                      'MAX',
                      'MIN',
                      'ABS',
                      'ROUND',
                    ]),
                    _buildFunctionCategory('Logical', [
                      'IF',
                      'AND',
                      'OR',
                      'NOT',
                    ]),
                    _buildFunctionCategory('Text', [
                      'CONCATENATE',
                      'LEFT',
                      'RIGHT',
                      'LEN',
                      'UPPER',
                      'LOWER',
                    ]),
                    _buildFunctionCategory('Date & Time', [
                      'TODAY',
                      'NOW',
                      'DATE',
                      'YEAR',
                      'MONTH',
                      'DAY',
                    ]),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionCategory(String title, List<String> functions) {
    return ExpansionTile(
      title: Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
      children: functions.map((function) {
        return ListTile(
          dense: true,
          title: Text(
            function,
            style: const TextStyle(fontFamily: 'monospace'),
          ),
          onTap: () {
            _formulaController.text = '=$function()';
            Navigator.of(context).pop();
          },
        );
      }).toList(),
    );
  }

  void _sortData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sort Data'),
        content: const SizedBox(
          width: 300,
          height: 200,
          child: Column(
            children: [
              Text('Sort functionality will be implemented'),
              // Add sort options here
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Sort'),
          ),
        ],
      ),
    );
  }

  void _filterData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Data'),
        content: const SizedBox(
          width: 300,
          height: 200,
          child: Column(
            children: [
              Text('Filter functionality will be implemented'),
              // Add filter options here
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Apply Filter'),
          ),
        ],
      ),
    );
  }

  void _insertChart() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Insert Chart'),
        content: const SizedBox(
          width: 400,
          height: 300,
          child: Column(
            children: [
              Text('Chart insertion will be implemented'),
              // Add chart type selection here
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Insert'),
          ),
        ],
      ),
    );
  }

  void _mergeCells() {
    final selectedCell = ref.read(selectedCellProvider);
    if (selectedCell != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cell merge functionality will be implemented'),
        ),
      );
    }
  }

  void _insertRow() {
    final selectedCell = ref.read(selectedCellProvider);
    if (selectedCell != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Row insertion functionality will be implemented'),
        ),
      );
    }
  }

  void _insertColumn() {
    final selectedCell = ref.read(selectedCellProvider);
    if (selectedCell != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Column insertion functionality will be implemented'),
        ),
      );
    }
  }
}
