import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_theme.dart';
import '../../services/islamic_providers.dart';
import '../../models/surah.dart';
import '../../models/verse.dart' as verse_model;
import '../../models/bookmark.dart' as bookmark_model;

class QuranReadingScreen extends ConsumerStatefulWidget {
  const QuranReadingScreen({super.key});

  @override
  ConsumerState<QuranReadingScreen> createState() => _QuranReadingScreenState();
}

class _QuranReadingScreenState extends ConsumerState<QuranReadingScreen> {
  final PageController _pageController = PageController();
  bool _isNavigating = false;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final selectedSurah = ref.watch(selectedSurahProvider);
    final nightMode = ref.watch(quranNightModeProvider);
    final showTranslation = ref.watch(quranShowTranslationProvider);
    final showTransliteration = ref.watch(quranShowTransliterationProvider);
    final viewMode = ref.watch(quranViewModeProvider);

    if (selectedSurah == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Quran Reader'),
          backgroundColor: AppTheme.islamicAppColor,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.surahList;
            },
          ),
        ),
        body: const Center(
          child: Text('No surah selected'),
        ),
      );
    }

    final versesAsync = ref.watch(versesProvider(selectedSurah.number));

    return Scaffold(
      backgroundColor: nightMode ? Colors.black : AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(selectedSurah.nameEnglish),
        backgroundColor: AppTheme.islamicAppColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.surahList;
          },
        ),
        actions: [
          IconButton(
            icon: Icon(nightMode ? Icons.light_mode : Icons.dark_mode),
            onPressed: () {
              ref.read(quranNightModeProvider.notifier).state = !nightMode;
            },
          ),
          IconButton(
            icon: const Icon(Icons.bookmark_add),
            onPressed: () => _showBookmarkDialog(context, ref, selectedSurah),
          ),
          PopupMenuButton<String>(
            onSelected: (value) => _handleMenuAction(context, ref, value),
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'view_mode',
                child: Row(
                  children: [
                    Icon(viewMode == QuranViewMode.book ? Icons.view_list : Icons.menu_book),
                    const SizedBox(width: 8),
                    Text(viewMode == QuranViewMode.book ? 'Verse View' : 'Book View'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'translation',
                child: Row(
                  children: [
                    Icon(showTranslation ? Icons.visibility_off : Icons.visibility),
                    const SizedBox(width: 8),
                    Text(showTranslation ? 'Hide Translation' : 'Show Translation'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'transliteration',
                child: Row(
                  children: [
                    Icon(showTransliteration ? Icons.visibility_off : Icons.visibility),
                    const SizedBox(width: 8),
                    Text(showTransliteration ? 'Hide Transliteration' : 'Show Transliteration'),
                  ],
                ),
              ),
              const PopupMenuItem(value: 'font_settings', child: Text('Font Settings')),
              const PopupMenuItem(value: 'share_surah', child: Text('Share Surah')),
            ],
          ),
        ],
      ),
      body: GestureDetector(
        onHorizontalDragEnd: (details) => _handleSwipeNavigation(details),
        onDoubleTap: () => _showGoToAyahDialog(context),
        child: versesAsync.when(
          data: (verses) {
            if (verses.isEmpty) {
              return _buildEmptyState(context);
            }
            return _buildQuranContentWithNavigation(context, selectedSurah, verses, nightMode);
          },
          loading: () => _buildLoadingState(),
          error: (error, stack) => _buildErrorState(error, selectedSurah),
        ),
      ),
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          FloatingActionButton(
            heroTag: "goto_ayah",
            mini: true,
            onPressed: () => _showGoToAyahDialog(context),
            backgroundColor: AppTheme.islamicAppColor,
            child: const Icon(Icons.search, color: Colors.white),
          ),
          const SizedBox(height: 8),
          FloatingActionButton(
            heroTag: "navigation",
            mini: true,
            onPressed: () => _showNavigationDialog(context),
            backgroundColor: AppTheme.islamicAppColor,
            child: const Icon(Icons.menu_book, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.menu_book,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Loading verses...',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Please wait while we prepare the Quran verses',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          const CircularProgressIndicator(),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Loading Quran verses...'),
        ],
      ),
    );
  }

  Widget _buildErrorState(dynamic error, Surah selectedSurah) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error, size: 64, color: Colors.red[300]),
          const SizedBox(height: 16),
          Text('Error loading verses: $error'),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => ref.refresh(versesProvider(selectedSurah.number)),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildQuranContentWithNavigation(
    BuildContext context,
    Surah surah,
    List<verse_model.Verse> verses,
    bool nightMode,
  ) {
    return Column(
      children: [
        _buildSurahHeader(context, surah, nightMode),
        _buildNavigationBar(context, surah),
        Expanded(
          child: _buildQuranContent(context, ref, surah, verses, nightMode),
        ),
      ],
    );
  }

  Widget _buildNavigationBar(BuildContext context, Surah surah) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: const BoxDecoration(
        color: Color(0xFFF8F9FA),
        border: Border(
          bottom: BorderSide(color: Color(0xFFE9ECEF)),
        ),
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: _canNavigatePrevious() ? _navigateToPreviousSurah : null,
            icon: const Icon(Icons.arrow_back_ios),
            tooltip: 'Previous Surah',
          ),
          Expanded(
            child: Text(
              'Surah ${surah.number} of 114',
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: Color(0xFF2C3E50),
              ),
            ),
          ),
          IconButton(
            onPressed: _canNavigateNext() ? _navigateToNextSurah : null,
            icon: const Icon(Icons.arrow_forward_ios),
            tooltip: 'Next Surah',
          ),
        ],
      ),
    );
  }

  Widget _buildQuranContent(
    BuildContext context,
    WidgetRef ref,
    Surah surah,
    List<verse_model.Verse> verses,
    bool nightMode,
  ) {
    final fontSize = ref.watch(quranFontSizeProvider);
    final translationFontSize = ref.watch(quranTranslationFontSizeProvider);
    final showTranslation = ref.watch(quranShowTranslationProvider);
    final showTransliteration = ref.watch(quranShowTransliterationProvider);
    final viewMode = ref.watch(quranViewModeProvider);

    return Column(
      children: [
        _buildSurahHeader(context, surah, nightMode),
        Expanded(
          child: viewMode == QuranViewMode.book
              ? _buildBookView(context, ref, verses, fontSize, translationFontSize, showTranslation, showTransliteration, nightMode)
              : _buildVerseView(context, ref, verses, fontSize, translationFontSize, showTranslation, showTransliteration, nightMode),
        ),
      ],
    );
  }

  Widget _buildSurahHeader(BuildContext context, Surah surah, bool nightMode) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: nightMode ? Colors.grey[900] : Colors.white,
        border: Border(
          bottom: BorderSide(
            color: nightMode ? Colors.grey[700]! : Colors.grey[300]!,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    surah.nameEnglish,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: nightMode ? Colors.white : AppTheme.islamicAppColor,
                    ),
                  ),
                  Text(
                    surah.nameTransliteration,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontStyle: FontStyle.italic,
                      color: nightMode ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
              Text(
                surah.nameArabic,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontFamily: 'Arabic',
                  color: nightMode ? Colors.white : AppTheme.islamicAppColor,
                  fontWeight: FontWeight.bold,
                ),
                textDirection: TextDirection.rtl,
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppTheme.islamicAppColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'Surah ${surah.number}',
                  style: TextStyle(
                    color: AppTheme.islamicAppColor,
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getRevelationColor(surah.revelationType).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  surah.revelationType,
                  style: TextStyle(
                    color: _getRevelationColor(surah.revelationType),
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '${surah.verseCount} verses',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: nightMode ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildVerseView(
    BuildContext context,
    WidgetRef ref,
    List<verse_model.Verse> verses,
    double fontSize,
    double translationFontSize,
    bool showTranslation,
    bool showTransliteration,
    bool nightMode,
  ) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: verses.length,
      itemBuilder: (context, index) {
        final verse = verses[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 24),
          child: _buildVerseCard(
            context,
            ref,
            verse,
            fontSize,
            translationFontSize,
            showTranslation,
            showTransliteration,
            nightMode,
          ),
        );
      },
    );
  }

  Widget _buildBookView(
    BuildContext context,
    WidgetRef ref,
    List<verse_model.Verse> verses,
    double fontSize,
    double translationFontSize,
    bool showTranslation,
    bool showTransliteration,
    bool nightMode,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: verses.map((verse) => Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildInlineVerse(
            context,
            ref,
            verse,
            fontSize,
            translationFontSize,
            showTranslation,
            showTransliteration,
            nightMode,
          ),
        )).toList(),
      ),
    );
  }

  Widget _buildVerseCard(
    BuildContext context,
    WidgetRef ref,
    verse_model.Verse verse,
    double fontSize,
    double translationFontSize,
    bool showTranslation,
    bool showTransliteration,
    bool nightMode,
  ) {
    return Card(
      color: nightMode ? Colors.grey[900] : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.islamicAppColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Verse ${verse.verseNumber}',
                    style: TextStyle(
                      color: AppTheme.islamicAppColor,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleVerseAction(context, ref, verse, value),
                  itemBuilder: (context) => [
                    const PopupMenuItem(value: 'bookmark', child: Text('Bookmark')),
                    const PopupMenuItem(value: 'copy', child: Text('Copy')),
                    const PopupMenuItem(value: 'share', child: Text('Share')),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              verse.textArabic,
              style: TextStyle(
                fontFamily: 'Arabic',
                fontSize: fontSize,
                height: 2.0,
                color: nightMode ? Colors.white : AppTheme.islamicAppColor,
              ),
              textAlign: TextAlign.right,
              textDirection: TextDirection.rtl,
            ),
            if (showTransliteration) ...[
              const SizedBox(height: 12),
              Text(
                verse.textTransliteration,
                style: TextStyle(
                  fontSize: translationFontSize,
                  fontStyle: FontStyle.italic,
                  color: nightMode ? Colors.grey[400] : Colors.grey[700],
                ),
              ),
            ],
            if (showTranslation) ...[
              const SizedBox(height: 12),
              Text(
                verse.textEnglish,
                style: TextStyle(
                  fontSize: translationFontSize,
                  height: 1.6,
                  color: nightMode ? Colors.grey[300] : Colors.black87,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInlineVerse(
    BuildContext context,
    WidgetRef ref,
    verse_model.Verse verse,
    double fontSize,
    double translationFontSize,
    bool showTranslation,
    bool showTransliteration,
    bool nightMode,
  ) {
    return GestureDetector(
      onLongPress: () => _showVerseOptions(context, ref, verse),
      child: RichText(
        textAlign: TextAlign.right,
        textDirection: TextDirection.rtl,
        text: TextSpan(
          children: [
            TextSpan(
              text: verse.textArabic,
              style: TextStyle(
                fontFamily: 'Arabic',
                fontSize: fontSize,
                height: 2.0,
                color: nightMode ? Colors.white : AppTheme.islamicAppColor,
              ),
            ),
            TextSpan(
              text: ' ﴿${verse.verseNumber}﴾ ',
              style: TextStyle(
                fontFamily: 'Arabic',
                fontSize: fontSize * 0.8,
                color: nightMode ? Colors.grey[400] : Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleMenuAction(BuildContext context, WidgetRef ref, String action) {
    switch (action) {
      case 'view_mode':
        final currentMode = ref.read(quranViewModeProvider);
        ref.read(quranViewModeProvider.notifier).state = 
            currentMode == QuranViewMode.book ? QuranViewMode.verse : QuranViewMode.book;
        break;
      case 'translation':
        final current = ref.read(quranShowTranslationProvider);
        ref.read(quranShowTranslationProvider.notifier).state = !current;
        break;
      case 'transliteration':
        final current = ref.read(quranShowTransliterationProvider);
        ref.read(quranShowTransliterationProvider.notifier).state = !current;
        break;
      case 'font_settings':
        _showFontSettings(context, ref);
        break;
      case 'share_surah':
        _shareSurah(context, ref);
        break;
    }
  }

  void _handleVerseAction(BuildContext context, WidgetRef ref, verse_model.Verse verse, String action) {
    switch (action) {
      case 'bookmark':
        _showBookmarkDialog(context, ref, null, verse);
        break;
      case 'copy':
        _copyVerse(context, verse);
        break;
      case 'share':
        _shareVerse(context, verse);
        break;
    }
  }

  void _showVerseOptions(BuildContext context, WidgetRef ref, verse_model.Verse verse) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.bookmark_add),
              title: const Text('Bookmark Verse'),
              onTap: () {
                Navigator.pop(context);
                _showBookmarkDialog(context, ref, null, verse);
              },
            ),
            ListTile(
              leading: const Icon(Icons.copy),
              title: const Text('Copy Verse'),
              onTap: () {
                Navigator.pop(context);
                _copyVerse(context, verse);
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('Share Verse'),
              onTap: () {
                Navigator.pop(context);
                _shareVerse(context, verse);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showBookmarkDialog(BuildContext context, WidgetRef ref, Surah? surah, [verse_model.Verse? verse]) {
    final titleController = TextEditingController();
    final notesController = TextEditingController();
    
    if (verse != null) {
      titleController.text = 'Verse ${verse.verseNumber}';
    } else if (surah != null) {
      titleController.text = surah.nameEnglish;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Bookmark'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: titleController,
              decoration: const InputDecoration(
                labelText: 'Title',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: notesController,
              decoration: const InputDecoration(
                labelText: 'Notes (optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (titleController.text.isNotEmpty && verse != null) {
                final bookmark = bookmark_model.Bookmark(
                  surahNumber: verse.surahNumber,
                  verseNumber: verse.verseNumber,
                  title: titleController.text,
                  notes: notesController.text,
                  tags: [],
                );
                ref.read(bookmarksProvider.notifier).addBookmark(bookmark);
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Bookmark added')),
                );
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showFontSettings(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Font Settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Arabic Font Size: ${ref.watch(quranFontSizeProvider).toInt()}'),
            Slider(
              value: ref.watch(quranFontSizeProvider),
              min: 14,
              max: 32,
              divisions: 18,
              onChanged: (value) {
                ref.read(quranFontSizeProvider.notifier).state = value;
              },
            ),
            const SizedBox(height: 16),
            Text('Translation Font Size: ${ref.watch(quranTranslationFontSizeProvider).toInt()}'),
            Slider(
              value: ref.watch(quranTranslationFontSizeProvider),
              min: 12,
              max: 24,
              divisions: 12,
              onChanged: (value) {
                ref.read(quranTranslationFontSizeProvider.notifier).state = value;
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _copyVerse(BuildContext context, verse_model.Verse verse) {
    final text = '${verse.textArabic}\n\n${verse.textEnglish}\n\n(Quran ${verse.surahNumber}:${verse.verseNumber})';
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Verse copied to clipboard')),
    );
  }

  void _shareVerse(BuildContext context, verse_model.Verse verse) {
    final text = '''
${verse.textArabic}

"${verse.textEnglish}"

Quran ${verse.surahNumber}:${verse.verseNumber}
    ''';

    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Verse copied to clipboard')),
    );
  }

  void _shareSurah(BuildContext context, WidgetRef ref) {
    final selectedSurah = ref.read(selectedSurahProvider);
    if (selectedSurah == null) return;

    final text = '''
Surah ${selectedSurah.nameEnglish} (${selectedSurah.nameArabic})
${selectedSurah.nameTransliteration}

Surah ${selectedSurah.number} - ${selectedSurah.verseCount} verses
Revelation: ${selectedSurah.revelationType}

From the Holy Quran
    ''';

    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Surah information copied to clipboard')),
    );
  }

  Color _getRevelationColor(String revelationType) {
    switch (revelationType.toLowerCase()) {
      case 'meccan':
        return Colors.orange;
      case 'medinan':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  // Navigation methods
  bool _canNavigatePrevious() {
    final selectedSurah = ref.read(selectedSurahProvider);
    return selectedSurah != null && selectedSurah.number > 1;
  }

  bool _canNavigateNext() {
    final selectedSurah = ref.read(selectedSurahProvider);
    return selectedSurah != null && selectedSurah.number < 114;
  }

  void _navigateToPreviousSurah() async {
    final selectedSurah = ref.read(selectedSurahProvider);
    if (selectedSurah != null && selectedSurah.number > 1) {
      setState(() => _isNavigating = true);

      final surahsAsync = ref.read(surahsProvider);
      surahsAsync.whenData((surahs) {
        final previousSurah = surahs.firstWhere((s) => s.number == selectedSurah.number - 1);
        ref.read(selectedSurahProvider.notifier).state = previousSurah;
      });

      setState(() => _isNavigating = false);
    }
  }

  void _navigateToNextSurah() async {
    final selectedSurah = ref.read(selectedSurahProvider);
    if (selectedSurah != null && selectedSurah.number < 114) {
      setState(() => _isNavigating = true);

      final surahsAsync = ref.read(surahsProvider);
      surahsAsync.whenData((surahs) {
        final nextSurah = surahs.firstWhere((s) => s.number == selectedSurah.number + 1);
        ref.read(selectedSurahProvider.notifier).state = nextSurah;
      });

      setState(() => _isNavigating = false);
    }
  }

  void _handleSwipeNavigation(DragEndDetails details) {
    if (_isNavigating) return;

    const sensitivity = 100;
    if (details.primaryVelocity != null) {
      if (details.primaryVelocity! > sensitivity) {
        // Swipe right - previous surah
        _navigateToPreviousSurah();
      } else if (details.primaryVelocity! < -sensitivity) {
        // Swipe left - next surah
        _navigateToNextSurah();
      }
    }
  }

  void _showGoToAyahDialog(BuildContext context) {
    final surahController = TextEditingController();
    final ayahController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Go to Ayah'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: surahController,
              decoration: const InputDecoration(
                labelText: 'Surah Number (1-114)',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: ayahController,
              decoration: const InputDecoration(
                labelText: 'Ayah Number',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final surahNum = int.tryParse(surahController.text);
              if (surahNum != null && surahNum >= 1 && surahNum <= 114) {
                _navigateToSurah(surahNum);
                Navigator.of(context).pop();
              }
            },
            child: const Text('Go'),
          ),
        ],
      ),
    );
  }

  void _showNavigationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Quick Navigation'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.bookmark),
              title: const Text('Bookmarks'),
              onTap: () {
                Navigator.of(context).pop();
                ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.bookmarks;
              },
            ),
            ListTile(
              leading: const Icon(Icons.search),
              title: const Text('Search Quran'),
              onTap: () {
                Navigator.of(context).pop();
                ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.quranSearch;
              },
            ),
            ListTile(
              leading: const Icon(Icons.list),
              title: const Text('Surah List'),
              onTap: () {
                Navigator.of(context).pop();
                ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.surahList;
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _navigateToSurah(int surahNumber) async {
    setState(() => _isNavigating = true);

    final surahsAsync = ref.read(surahsProvider);
    surahsAsync.whenData((surahs) {
      final targetSurah = surahs.firstWhere((s) => s.number == surahNumber);
      ref.read(selectedSurahProvider.notifier).state = targetSurah;
    });

    setState(() => _isNavigating = false);
  }
}
