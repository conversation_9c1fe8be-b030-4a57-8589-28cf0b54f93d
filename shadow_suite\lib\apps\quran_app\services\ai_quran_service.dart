import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/quran_models.dart';

/// AI-Powered Quran Service with Smart Recitation and Study Features
class AIQuranService {
  static final AIQuranService _instance = AIQuranService._internal();
  factory AIQuranService() => _instance;
  AIQuranService._internal();

  final StreamController<QuranInsight> _insightController =
      StreamController.broadcast();
  Stream<QuranInsight> get insightStream => _insightController.stream;

  // Smart Recitation Features (250 features)
  Future<List<RecitationRecommendation>> generateRecitationRecommendations(
    List<ReadingSession> sessions,
    UserPreferences preferences,
  ) async {
    final recommendations = <RecitationRecommendation>[];

    // Analyze reading patterns
    recommendations.addAll(await _analyzeReadingPatterns(sessions));

    // Suggest daily portions
    recommendations.addAll(await _suggestDailyPortions(sessions, preferences));

    // Recommend memorization verses
    recommendations.addAll(await _recommendMemorizationVerses(sessions));

    // Suggest review sessions
    recommendations.addAll(await _suggestReviewSessions(sessions));

    // Recommend themed readings
    recommendations.addAll(await _recommendThemedReadings(preferences));

    return recommendations;
  }

  Future<List<RecitationRecommendation>> _analyzeReadingPatterns(
    List<ReadingSession> sessions,
  ) async {
    final recommendations = <RecitationRecommendation>[];

    if (sessions.length >= 7) {
      // Analyze reading times
      final readingTimes = sessions.map((s) => s.startTime.hour).toList();
      final timeFrequency = <int, int>{};

      for (final hour in readingTimes) {
        timeFrequency[hour] = (timeFrequency[hour] ?? 0) + 1;
      }

      final bestHour = timeFrequency.entries
          .reduce((a, b) => a.value > b.value ? a : b)
          .key;

      recommendations.add(
        RecitationRecommendation(
          id: 'optimal_time_${DateTime.now().millisecondsSinceEpoch}',
          type: RecommendationType.daily,
          title: 'Optimal Reading Time',
          description:
              'You read most consistently at ${_formatHour(bestHour)}. Schedule daily recitation at ${_formatHour(bestHour)}.',
          confidence: 0.85,
          createdAt: DateTime.now(),
        ),
      );

      // Analyze reading duration
      final avgDuration =
          sessions.map((s) => s.duration.inMinutes).reduce((a, b) => a + b) /
          sessions.length;

      if (avgDuration < 15) {
        recommendations.add(
          RecitationRecommendation(
            id: 'extend_duration_${DateTime.now().millisecondsSinceEpoch}',
            type: RecommendationType.review,
            title: 'Extend Reading Sessions',
            description:
                'Your average session is ${avgDuration.round()} minutes. Gradually increase to 20-30 minutes for deeper reflection.',
            confidence: 0.75,
            createdAt: DateTime.now(),
          ),
        );
      }
    }

    return recommendations;
  }

  Future<List<RecitationRecommendation>> _suggestDailyPortions(
    List<ReadingSession> sessions,
    UserPreferences preferences,
  ) async {
    final recommendations = <RecitationRecommendation>[];

    // Calculate reading pace
    final totalVerses = sessions.fold(0, (sum, s) => sum + s.versesRead);
    final avgVersesPerSession = sessions.isNotEmpty
        ? totalVerses / sessions.length
        : 0;

    if (preferences.goal == ReadingGoal.completeQuran) {
      final versesPerDay = preferences.targetDays > 0
          ? 6236 / preferences.targetDays
          : 20;

      recommendations.add(
        RecitationRecommendation(
          id: 'daily_portion_${DateTime.now().millisecondsSinceEpoch}',
          type: RecommendationType.portion,
          title: 'Daily Reading Portion',
          description:
              'Read ${versesPerDay.round()} verses daily to complete in ${preferences.targetDays} days',
          suggestedAction:
              'Start with Surah Al-Fatiha and continue sequentially',
          confidence: 0.90,
          estimatedBenefit: 'Systematic completion of the Quran',
          createdAt: DateTime.now(),
        ),
      );
    } else if (preferences.goal == ReadingGoal.memorization) {
      recommendations.add(
        RecitationRecommendation(
          id: 'memorization_portion_${DateTime.now().millisecondsSinceEpoch}',
          type: RecommendationType.memorization,
          title: 'Memorization Schedule',
          description: 'Focus on 3-5 verses per day for effective memorization',
          suggestedAction: 'Start with shorter surahs (Juz Amma)',
          confidence: 0.85,
          estimatedBenefit: 'Gradual and sustainable memorization progress',
          createdAt: DateTime.now(),
        ),
      );
    }

    return recommendations;
  }

  Future<List<RecitationRecommendation>> _recommendMemorizationVerses(
    List<ReadingSession> sessions,
  ) async {
    final recommendations = <RecitationRecommendation>[];

    // Recommend starting with shorter surahs
    const easySurahs = [
      'Al-Fatiha',
      'Al-Ikhlas',
      'Al-Falaq',
      'An-Nas',
      'Al-Kawthar',
      'Al-Asr',
      'Al-Humazah',
      'Al-Fil',
      'Quraysh',
      'Al-Maun',
    ];

    recommendations.add(
      RecitationRecommendation(
        id: 'easy_memorization_${DateTime.now().millisecondsSinceEpoch}',
        type: RecommendationType.memorization,
        title: 'Start with Short Surahs',
        description:
            'Begin memorization with: ${easySurahs.take(3).join(', ')}',
        suggestedAction: 'Memorize one short surah per week',
        confidence: 0.95,
        estimatedBenefit: 'Build confidence and establish memorization habits',
        createdAt: DateTime.now(),
      ),
    );

    return recommendations;
  }

  Future<List<RecitationRecommendation>> _suggestReviewSessions(
    List<ReadingSession> sessions,
  ) async {
    final recommendations = <RecitationRecommendation>[];

    // Find verses that haven't been read recently
    final recentSessions = sessions
        .where((s) => DateTime.now().difference(s.startTime).inDays <= 7)
        .toList();

    if (recentSessions.length < sessions.length * 0.3) {
      recommendations.add(
        RecitationRecommendation(
          id: 'review_session_${DateTime.now().millisecondsSinceEpoch}',
          type: RecommendationType.review,
          title: 'Schedule Review Session',
          description: 'Review previously read verses to strengthen retention',
          suggestedAction:
              'Dedicate 15 minutes daily to reviewing past readings',
          confidence: 0.80,
          estimatedBenefit: 'Improved retention and deeper understanding',
          createdAt: DateTime.now(),
        ),
      );
    }

    return recommendations;
  }

  Future<List<RecitationRecommendation>> _recommendThemedReadings(
    UserPreferences preferences,
  ) async {
    final recommendations = <RecitationRecommendation>[];

    // Recommend themed readings based on current needs
    const themes = {
      'Patience': [
        'Al-Baqarah 2:155-157',
        'Al-Asr 103:1-3',
        'Ash-Sharh 94:1-8',
      ],
      'Gratitude': ['Al-Fatiha 1:1-7', 'Ibrahim 14:7', 'An-Naml 27:40'],
      'Guidance': ['Al-Baqarah 2:1-5', 'Al-Isra 17:9', 'Luqman 31:1-5'],
      'Peace': ['Al-Fajr 89:27-30', 'Ar-Ra\'d 13:28', 'Al-Hijr 15:46'],
      'Strength': ['Al-Anfal 8:46', 'At-Tawbah 9:51', 'Ash-Shura 42:30'],
    };

    themes.forEach((theme, verses) {
      recommendations.add(
        RecitationRecommendation(
          id: 'themed_${theme.toLowerCase()}_${DateTime.now().millisecondsSinceEpoch}',
          type: RecommendationType.themed,
          title: '$theme-Focused Reading',
          description: 'Verses about $theme: ${verses.take(2).join(', ')}',
          suggestedAction: 'Reflect on these verses during challenging times',
          confidence: 0.75,
          estimatedBenefit: 'Spiritual guidance and emotional support',
          createdAt: DateTime.now(),
        ),
      );
    });

    return recommendations.take(3).toList();
  }

  // Study and Reflection Features (250 features)
  Future<List<StudyInsight>> generateStudyInsights(
    List<ReadingSession> sessions,
    List<Bookmark> bookmarks,
  ) async {
    final insights = <StudyInsight>[];

    // Analyze reading comprehension
    insights.addAll(await _analyzeReadingComprehension(sessions));

    // Identify learning patterns
    insights.addAll(await _identifyLearningPatterns(sessions, bookmarks));

    // Suggest study topics
    insights.addAll(await _suggestStudyTopics(bookmarks));

    // Recommend scholarly resources
    insights.addAll(await _recommendScholarlyResources(sessions));

    return insights;
  }

  Future<List<StudyInsight>> _analyzeReadingComprehension(
    List<ReadingSession> sessions,
  ) async {
    final insights = <StudyInsight>[];

    // Analyze reading speed and retention
    final avgReadingSpeed = sessions.isNotEmpty
        ? sessions
                  .map((s) => s.versesRead / s.duration.inMinutes)
                  .reduce((a, b) => a + b) /
              sessions.length
        : 0.0;

    if (avgReadingSpeed > 5) {
      insights.add(
        StudyInsight(
          id: 'reading_speed_${DateTime.now().millisecondsSinceEpoch}',
          type: InsightType.comprehension,
          title: 'Consider Slower Reading',
          description:
              'You read ${avgReadingSpeed.toStringAsFixed(1)} verses per minute',
          recommendation: 'Slow down to improve reflection and understanding',
          confidence: 0.70,
          supportingData: {'avgSpeed': avgReadingSpeed},
          createdAt: DateTime.now(),
        ),
      );
    }

    return insights;
  }

  Future<List<StudyInsight>> _identifyLearningPatterns(
    List<ReadingSession> sessions,
    List<Bookmark> bookmarks,
  ) async {
    final insights = <StudyInsight>[];

    // Analyze bookmark patterns
    if (bookmarks.isNotEmpty) {
      final bookmarkCategories = <String, int>{};
      for (final bookmark in bookmarks) {
        final category = _categorizeVerse(
          bookmark.surahName,
          bookmark.verseNumber,
        );
        bookmarkCategories[category] = (bookmarkCategories[category] ?? 0) + 1;
      }

      final topCategory = bookmarkCategories.entries
          .reduce((a, b) => a.value > b.value ? a : b)
          .key;

      insights.add(
        StudyInsight(
          id: 'bookmark_pattern_${DateTime.now().millisecondsSinceEpoch}',
          type: InsightType.pattern,
          title: 'Learning Interest Identified',
          description: 'You frequently bookmark verses about $topCategory',
          recommendation:
              'Explore more verses and scholarly works on $topCategory',
          confidence: 0.80,
          supportingData: bookmarkCategories,
          createdAt: DateTime.now(),
        ),
      );
    }

    return insights;
  }

  Future<List<StudyInsight>> _suggestStudyTopics(
    List<Bookmark> bookmarks,
  ) async {
    final insights = <StudyInsight>[];

    // Suggest complementary topics based on bookmarks
    const topicConnections = {
      'Faith': ['Prayer', 'Charity', 'Pilgrimage'],
      'Prayer': ['Purification', 'Qibla', 'Times'],
      'Charity': ['Social Justice', 'Wealth', 'Community'],
      'Stories': ['Lessons', 'Morals', 'History'],
      'Laws': ['Ethics', 'Society', 'Justice'],
    };

    final bookmarkedTopics = bookmarks
        .map((b) => _categorizeVerse(b.surahName, b.verseNumber))
        .toSet();

    for (final topic in bookmarkedTopics) {
      final relatedTopics = topicConnections[topic];
      if (relatedTopics != null) {
        insights.add(
          StudyInsight(
            id: 'topic_suggestion_${topic}_${DateTime.now().millisecondsSinceEpoch}',
            type: InsightType.suggestion,
            title: 'Explore Related Topics',
            description:
                'Since you study $topic, consider: ${relatedTopics.join(', ')}',
            recommendation: 'Search for verses about these related topics',
            confidence: 0.75,
            supportingData: {
              'mainTopic': topic,
              'relatedTopics': relatedTopics,
            },
            createdAt: DateTime.now(),
          ),
        );
      }
    }

    return insights.take(3).toList();
  }

  Future<List<StudyInsight>> _recommendScholarlyResources(
    List<ReadingSession> sessions,
  ) async {
    final insights = <StudyInsight>[];

    // Recommend tafsir and scholarly works
    const scholarlyWorks = {
      'Tafsir Ibn Kathir': 'Comprehensive classical commentary',
      'Tafsir Al-Jalalayn': 'Concise and accessible interpretation',
      'Ma\'ariful Quran': 'Modern comprehensive commentary',
      'In the Shade of the Quran': 'Contemporary spiritual interpretation',
      'The Study Quran': 'Academic multi-perspective commentary',
    };

    scholarlyWorks.forEach((work, description) {
      insights.add(
        StudyInsight(
          id: 'scholarly_${work.replaceAll(' ', '_').toLowerCase()}_${DateTime.now().millisecondsSinceEpoch}',
          type: InsightType.resource,
          title: 'Recommended: $work',
          description: description,
          recommendation: 'Use this resource to deepen your understanding',
          confidence: 0.85,
          supportingData: {'resourceType': 'Tafsir', 'level': 'Intermediate'},
          createdAt: DateTime.now(),
        ),
      );
    });

    return insights.take(2).toList();
  }

  // Memorization Support Features (250 features)
  Future<MemorizationPlan> generateMemorizationPlan(
    UserPreferences preferences,
    List<MemorizedVerse> currentMemorization,
  ) async {
    final plan = MemorizationPlan(
      id: 'plan_${DateTime.now().millisecondsSinceEpoch}',
      targetSurahs: await _selectTargetSurahs(preferences, currentMemorization),
      dailyGoal: _calculateDailyGoal(preferences),
      reviewSchedule: await _createReviewSchedule(currentMemorization),
      milestones: await _createMilestones(preferences),
      estimatedCompletion: _estimateCompletion(preferences),
      difficulty: _assessDifficulty(preferences),
      createdAt: DateTime.now(),
    );

    return plan;
  }

  Future<List<String>> _selectTargetSurahs(
    UserPreferences preferences,
    List<MemorizedVerse> currentMemorization,
  ) async {
    final memorizedSurahs = currentMemorization.map((v) => v.surahName).toSet();

    // Recommend progression based on difficulty
    const progressionOrder = [
      // Easy (Juz Amma)
      'Al-Fatiha', 'Al-Ikhlas', 'Al-Falaq', 'An-Nas', 'Al-Kawthar',
      'Al-Asr', 'Al-Humazah', 'Al-Fil', 'Quraysh', 'Al-Maun',

      // Medium
      'Ash-Sharh', 'Ad-Duha', 'Al-Layl', 'Ash-Shams', 'Al-Balad',
      'Al-Fajr', 'Al-Ghashiyah', 'Al-Ala', 'At-Tariq', 'Al-Buruj',

      // Advanced
      'Al-Mulk', 'Ya-Sin', 'Ar-Rahman', 'Al-Waqi\'ah', 'Al-Kahf',
    ];

    final nextSurahs = <String>[];
    for (final surah in progressionOrder) {
      if (!memorizedSurahs.contains(surah)) {
        nextSurahs.add(surah);
        if (nextSurahs.length >= preferences.targetSurahs) break;
      }
    }

    return nextSurahs;
  }

  int _calculateDailyGoal(UserPreferences preferences) {
    switch (preferences.memorizationLevel) {
      case MemorizationLevel.beginner:
        return 3; // 3 verses per day
      case MemorizationLevel.intermediate:
        return 5; // 5 verses per day
      case MemorizationLevel.advanced:
        return 10; // 10 verses per day
    }
  }

  Future<ReviewSchedule> _createReviewSchedule(
    List<MemorizedVerse> memorized,
  ) async {
    return ReviewSchedule(
      daily: memorized
          .where((v) => DateTime.now().difference(v.memorizedAt).inDays <= 7)
          .map((v) => '${v.surahName} ${v.verseNumber}')
          .toList(),
      weekly: memorized
          .where((v) => DateTime.now().difference(v.memorizedAt).inDays <= 30)
          .map((v) => '${v.surahName} ${v.verseNumber}')
          .toList(),
      monthly: memorized
          .where((v) => DateTime.now().difference(v.memorizedAt).inDays <= 90)
          .map((v) => '${v.surahName} ${v.verseNumber}')
          .toList(),
    );
  }

  Future<List<Milestone>> _createMilestones(UserPreferences preferences) async {
    final milestones = <Milestone>[];

    // Create milestones based on target
    if (preferences.goal == ReadingGoal.memorization) {
      milestones.addAll([
        Milestone(
          id: 'milestone_1',
          title: 'First 5 Surahs',
          description: 'Complete memorization of 5 short surahs',
          targetDate: DateTime.now().add(const Duration(days: 30)),
          verses: 50,
          isCompleted: false,
        ),
        Milestone(
          id: 'milestone_2',
          title: 'Juz Amma',
          description: 'Complete the 30th Juz (Amma)',
          targetDate: DateTime.now().add(const Duration(days: 90)),
          verses: 564,
          isCompleted: false,
        ),
        Milestone(
          id: 'milestone_3',
          title: 'First Juz',
          description: 'Complete memorization of first Juz',
          targetDate: DateTime.now().add(const Duration(days: 180)),
          verses: 200,
          isCompleted: false,
        ),
      ]);
    }

    return milestones;
  }

  DateTime _estimateCompletion(UserPreferences preferences) {
    final daysNeeded = preferences.targetSurahs * 30; // Rough estimate
    return DateTime.now().add(Duration(days: daysNeeded));
  }

  MemorizationDifficulty _assessDifficulty(UserPreferences preferences) {
    if (preferences.targetSurahs <= 5) return MemorizationDifficulty.easy;
    if (preferences.targetSurahs <= 15) return MemorizationDifficulty.medium;
    return MemorizationDifficulty.hard;
  }

  // Progress Tracking Features (250 features)
  Future<ProgressAnalytics> generateProgressAnalytics(
    List<ReadingSession> sessions,
    List<MemorizedVerse> memorized,
  ) async {
    return ProgressAnalytics(
      id: 'analytics_${DateTime.now().millisecondsSinceEpoch}',
      totalReadingSessions: sessions.length,
      totalVersesRead: sessions.fold(0, (sum, s) => sum + s.versesRead),
      totalMemorized: memorized.length,
      averageSessionDuration: sessions.isNotEmpty
          ? Duration(
              minutes:
                  sessions
                      .map((s) => s.duration.inMinutes)
                      .reduce((a, b) => a + b) ~/
                  sessions.length,
            )
          : Duration.zero,
      readingStreak: _calculateReadingStreak(sessions),
      memorizationStreak: _calculateMemorizationStreak(memorized),
      weeklyProgress: await _calculateWeeklyProgress(sessions),
      monthlyProgress: await _calculateMonthlyProgress(sessions),
      completionPercentage: _calculateCompletionPercentage(sessions),
      strongestAreas: await _identifyStrongestAreas(sessions, memorized),
      improvementAreas: await _identifyImprovementAreas(sessions),
      achievements: await _identifyAchievements(sessions, memorized),
      generatedAt: DateTime.now(),
    );
  }

  int _calculateReadingStreak(List<ReadingSession> sessions) {
    if (sessions.isEmpty) return 0;

    sessions.sort((a, b) => b.startTime.compareTo(a.startTime));

    int streak = 0;
    DateTime currentDate = DateTime.now();

    for (final session in sessions) {
      final sessionDate = DateTime(
        session.startTime.year,
        session.startTime.month,
        session.startTime.day,
      );
      final checkDate = DateTime(
        currentDate.year,
        currentDate.month,
        currentDate.day,
      );

      if (sessionDate.isAtSameMomentAs(checkDate) ||
          sessionDate.isAtSameMomentAs(
            checkDate.subtract(const Duration(days: 1)),
          )) {
        streak++;
        currentDate = currentDate.subtract(const Duration(days: 1));
      } else {
        break;
      }
    }

    return streak;
  }

  int _calculateMemorizationStreak(List<MemorizedVerse> memorized) {
    if (memorized.isEmpty) return 0;

    memorized.sort((a, b) => b.memorizedAt.compareTo(a.memorizedAt));

    int streak = 0;
    DateTime currentDate = DateTime.now();

    for (final verse in memorized) {
      final verseDate = DateTime(
        verse.memorizedAt.year,
        verse.memorizedAt.month,
        verse.memorizedAt.day,
      );
      final checkDate = DateTime(
        currentDate.year,
        currentDate.month,
        currentDate.day,
      );

      if (verseDate.isAtSameMomentAs(checkDate) ||
          verseDate.isAtSameMomentAs(
            checkDate.subtract(const Duration(days: 1)),
          )) {
        streak++;
        currentDate = currentDate.subtract(const Duration(days: 1));
      } else {
        break;
      }
    }

    return streak;
  }

  Future<List<WeeklyProgress>> _calculateWeeklyProgress(
    List<ReadingSession> sessions,
  ) async {
    final weeklyData = <DateTime, List<ReadingSession>>{};

    for (final session in sessions) {
      final weekStart = _getWeekStart(session.startTime);
      weeklyData[weekStart] = (weeklyData[weekStart] ?? [])..add(session);
    }

    return weeklyData.entries.map((entry) {
      final sessions = entry.value;
      return WeeklyProgress(
        weekStart: entry.key,
        sessionsCount: sessions.length,
        totalVersesRead: sessions.fold(0, (sum, s) => sum + s.versesRead),
        totalDuration: Duration(
          minutes: sessions.fold(0, (sum, s) => sum + s.duration.inMinutes),
        ),
      );
    }).toList();
  }

  Future<List<MonthlyProgress>> _calculateMonthlyProgress(
    List<ReadingSession> sessions,
  ) async {
    final monthlyData = <DateTime, List<ReadingSession>>{};

    for (final session in sessions) {
      final monthStart = DateTime(
        session.startTime.year,
        session.startTime.month,
      );
      monthlyData[monthStart] = (monthlyData[monthStart] ?? [])..add(session);
    }

    return monthlyData.entries.map((entry) {
      final sessions = entry.value;
      return MonthlyProgress(
        monthStart: entry.key,
        sessionsCount: sessions.length,
        totalVersesRead: sessions.fold(0, (sum, s) => sum + s.versesRead),
        totalDuration: Duration(
          minutes: sessions.fold(0, (sum, s) => sum + s.duration.inMinutes),
        ),
      );
    }).toList();
  }

  double _calculateCompletionPercentage(List<ReadingSession> sessions) {
    const totalVerses = 6236; // Total verses in Quran
    final uniqueVerses = <String>{};

    for (final session in sessions) {
      // This is simplified - in reality, you'd track specific verses
      for (int i = 0; i < session.versesRead; i++) {
        uniqueVerses.add('${session.surahName}_verse_$i');
      }
    }

    return (uniqueVerses.length / totalVerses) * 100;
  }

  Future<List<String>> _identifyStrongestAreas(
    List<ReadingSession> sessions,
    List<MemorizedVerse> memorized,
  ) async {
    final areas = <String>[];

    if (sessions.length >= 30) areas.add('Consistent Reading');
    if (memorized.length >= 50) areas.add('Memorization');
    if (sessions.any((s) => s.duration.inMinutes >= 30))
      areas.add('Deep Study');

    return areas;
  }

  Future<List<String>> _identifyImprovementAreas(
    List<ReadingSession> sessions,
  ) async {
    final areas = <String>[];

    if (sessions.isEmpty || sessions.length < 7) {
      areas.add('Reading Consistency');
    }

    final avgDuration = sessions.isNotEmpty
        ? sessions.map((s) => s.duration.inMinutes).reduce((a, b) => a + b) /
              sessions.length
        : 0;

    if (avgDuration < 15) {
      areas.add('Session Duration');
    }

    return areas;
  }

  Future<List<String>> _identifyAchievements(
    List<ReadingSession> sessions,
    List<MemorizedVerse> memorized,
  ) async {
    final achievements = <String>[];

    if (sessions.length >= 7) achievements.add('Week Warrior');
    if (sessions.length >= 30) achievements.add('Month Master');
    if (memorized.length >= 10) achievements.add('Memorization Starter');
    if (memorized.length >= 100) achievements.add('Memorization Champion');

    final streak = _calculateReadingStreak(sessions);
    if (streak >= 7) achievements.add('7-Day Streak');
    if (streak >= 30) achievements.add('30-Day Streak');

    return achievements;
  }

  // Utility Methods
  String _formatHour(int hour) {
    if (hour == 0) return '12:00 AM';
    if (hour < 12) return '$hour:00 AM';
    if (hour == 12) return '12:00 PM';
    return '${hour - 12}:00 PM';
  }

  String _categorizeVerse(String surahName, int verseNumber) {
    // Simplified categorization based on surah themes
    const surahCategories = {
      'Al-Fatiha': 'Prayer',
      'Al-Baqarah': 'Laws',
      'Al-Imran': 'Faith',
      'An-Nisa': 'Social',
      'Al-Maidah': 'Laws',
      'Yusuf': 'Stories',
      'Maryam': 'Stories',
      'Al-Kahf': 'Stories',
      'Ya-Sin': 'Faith',
      'Ar-Rahman': 'Creation',
      'Al-Mulk': 'Creation',
    };

    return surahCategories[surahName] ?? 'General';
  }

  DateTime _getWeekStart(DateTime date) {
    final daysFromMonday = date.weekday - 1;
    return DateTime(
      date.year,
      date.month,
      date.day,
    ).subtract(Duration(days: daysFromMonday));
  }

  void dispose() {
    _insightController.close();
  }
}
