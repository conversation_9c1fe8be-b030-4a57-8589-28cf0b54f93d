import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/tafseer.dart';
import 'islamic_database_service.dart';

// Tafseer Service for Quran commentary integration
class TafseerService {
  static const String _tafseerAssetPath = 'assets/tafseer.txt';
  static bool _isInitialized = false;

  // Initialize tafseer data from assets
  static Future<void> initializeTafseer() async {
    if (_isInitialized) return;

    try {
      // Check if tafseer data already exists in database
      final existingTafseer = await IslamicDatabaseService.getTafseerByVerse(
        1,
        1,
      );
      if (existingTafseer != null) {
        _isInitialized = true;
        return;
      }

      // Load and parse tafseer data from assets
      final content = await rootBundle.loadString(_tafseerAssetPath);
      await _parseTafseerData(content);
      _isInitialized = true;
    } catch (e) {
      // Handle error - tafseer file might not exist or be malformed
      // Use proper logging framework for production
      debugPrint('Error initializing tafseer: $e');
      // In production, this would use a logging service like logger package
    }
  }

  // Parse tafseer data from text content
  static Future<void> _parseTafseerData(String content) async {
    final lines = content.split('\n');

    for (final line in lines) {
      if (line.trim().isEmpty) continue;

      // Expected format: surah_number|verse_number|tafseer_text
      final parts = line.split('|');
      if (parts.length < 3) continue;

      final surahNumber = int.tryParse(parts[0]);
      final verseNumber = int.tryParse(parts[1]);
      final tafseerText = parts
          .sublist(2)
          .join('|'); // In case tafseer contains pipes

      if (surahNumber == null || verseNumber == null) continue;

      final tafseer = Tafseer(
        surahNumber: surahNumber,
        verseNumber: verseNumber,
        text: tafseerText,
        source: 'Ibn Kathir', // Default source
        language: 'en',
      );

      await IslamicDatabaseService.insertTafseer(tafseer);
    }
  }

  // Get tafseer for a specific verse
  static Future<Tafseer?> getTafseerForVerse(
    int surahNumber,
    int verseNumber,
  ) async {
    await initializeTafseer();
    return await IslamicDatabaseService.getTafseerByVerse(
      surahNumber,
      verseNumber,
    );
  }

  // Get all tafseer for a surah
  static Future<List<Tafseer>> getTafseerForSurah(int surahNumber) async {
    await initializeTafseer();
    return await IslamicDatabaseService.getTafseerBySurah(surahNumber);
  }

  // Search tafseer by text
  static Future<List<Tafseer>> searchTafseer(String query) async {
    await initializeTafseer();
    return await IslamicDatabaseService.searchTafseer(query);
  }

  // Add custom tafseer note
  static Future<String> addCustomTafseer({
    required int surahNumber,
    required int verseNumber,
    required String text,
    required String source,
    String language = 'en',
  }) async {
    final tafseer = Tafseer(
      surahNumber: surahNumber,
      verseNumber: verseNumber,
      text: text,
      source: source,
      language: language,
      isCustom: true,
    );

    return await IslamicDatabaseService.insertTafseer(tafseer);
  }

  // Update custom tafseer
  static Future<void> updateCustomTafseer(Tafseer tafseer) async {
    if (tafseer.isCustom) {
      await IslamicDatabaseService.updateTafseer(tafseer);
    }
  }

  // Delete custom tafseer
  static Future<void> deleteCustomTafseer(String id) async {
    final tafseer = await IslamicDatabaseService.getTafseerById(id);
    if (tafseer != null && tafseer.isCustom) {
      await IslamicDatabaseService.deleteTafseer(id);
    }
  }

  // Get tafseer statistics
  static Future<Map<String, int>> getTafseerStatistics() async {
    await initializeTafseer();

    final allTafseer = await IslamicDatabaseService.getAllTafseer();
    final customTafseer = allTafseer.where((t) => t.isCustom).length;
    final defaultTafseer = allTafseer.length - customTafseer;

    return {
      'total': allTafseer.length,
      'default': defaultTafseer,
      'custom': customTafseer,
    };
  }

  // Export custom tafseer
  static Future<String> exportCustomTafseer() async {
    final customTafseer = await IslamicDatabaseService.getCustomTafseer();
    final buffer = StringBuffer();

    buffer.writeln('# Custom Tafseer Export');
    buffer.writeln('# Format: surah_number|verse_number|text|source|language');
    buffer.writeln();

    for (final tafseer in customTafseer) {
      buffer.writeln(
        '${tafseer.surahNumber}|${tafseer.verseNumber}|${tafseer.text}|${tafseer.source}|${tafseer.language}',
      );
    }

    return buffer.toString();
  }

  // Import custom tafseer
  static Future<int> importCustomTafseer(String content) async {
    final lines = content.split('\n');
    int importedCount = 0;

    for (final line in lines) {
      if (line.trim().isEmpty || line.startsWith('#')) continue;

      final parts = line.split('|');
      if (parts.length < 5) continue;

      final surahNumber = int.tryParse(parts[0]);
      final verseNumber = int.tryParse(parts[1]);
      final text = parts[2];
      final source = parts[3];
      final language = parts[4];

      if (surahNumber == null || verseNumber == null) continue;

      try {
        await addCustomTafseer(
          surahNumber: surahNumber,
          verseNumber: verseNumber,
          text: text,
          source: source,
          language: language,
        );
        importedCount++;
      } catch (e) {
        // Continue with other entries if one fails
        continue;
      }
    }

    return importedCount;
  }

  // Get tafseer sources
  static Future<List<String>> getTafseerSources() async {
    await initializeTafseer();
    final allTafseer = await IslamicDatabaseService.getAllTafseer();
    final sources = allTafseer.map((t) => t.source).toSet().toList();
    sources.sort();
    return sources;
  }

  // Get tafseer by source
  static Future<List<Tafseer>> getTafseerBySource(String source) async {
    await initializeTafseer();
    return await IslamicDatabaseService.getTafseerBySource(source);
  }

  // Check if tafseer exists for verse
  static Future<bool> hasTafseerForVerse(
    int surahNumber,
    int verseNumber,
  ) async {
    final tafseer = await getTafseerForVerse(surahNumber, verseNumber);
    return tafseer != null;
  }

  // Get random tafseer for daily reflection
  static Future<Tafseer?> getRandomTafseer() async {
    await initializeTafseer();
    return await IslamicDatabaseService.getRandomTafseer();
  }

  // Mark tafseer as favorite
  static Future<void> toggleTafseerFavorite(String tafseerID) async {
    await IslamicDatabaseService.toggleTafseerFavorite(tafseerID);
  }

  // Get favorite tafseer
  static Future<List<Tafseer>> getFavoriteTafseer() async {
    await initializeTafseer();
    return await IslamicDatabaseService.getFavoriteTafseer();
  }

  // Get tafseer reading progress
  static Future<Map<String, dynamic>> getTafseerProgress() async {
    await initializeTafseer();

    final allTafseer = await IslamicDatabaseService.getAllTafseer();
    final readTafseer = await IslamicDatabaseService.getReadTafseer();

    final totalVerses = 6236; // Total verses in Quran
    final availableTafseer = allTafseer.length;
    final readCount = readTafseer.length;

    return {
      'totalVerses': totalVerses,
      'availableTafseer': availableTafseer,
      'readCount': readCount,
      'readPercentage': availableTafseer > 0
          ? (readCount / availableTafseer * 100).round()
          : 0,
      'coveragePercentage': (availableTafseer / totalVerses * 100).round(),
    };
  }

  // Mark tafseer as read
  static Future<void> markTafseerAsRead(String tafseerID) async {
    await IslamicDatabaseService.markTafseerAsRead(tafseerID);
  }

  // Get recently read tafseer
  static Future<List<Tafseer>> getRecentlyReadTafseer({int limit = 10}) async {
    await initializeTafseer();
    return await IslamicDatabaseService.getRecentlyReadTafseer(limit: limit);
  }
}
