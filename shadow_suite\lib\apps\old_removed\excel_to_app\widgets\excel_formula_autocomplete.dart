import 'package:flutter/material.dart';
import '../services/excel_formula_engine.dart';

class ExcelFormulaAutocomplete extends StatefulWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final Function(String) onSuggestionSelected;
  final VoidCallback? onSubmit;

  const ExcelFormulaAutocomplete({
    super.key,
    required this.controller,
    required this.focusNode,
    required this.onSuggestionSelected,
    this.onSubmit,
  });

  @override
  State<ExcelFormulaAutocomplete> createState() => _ExcelFormulaAutocompleteState();
}

class _ExcelFormulaAutocompleteState extends State<ExcelFormulaAutocomplete> {
  final ExcelFormulaEngine _formulaEngine = ExcelFormulaEngine();
  List<FunctionSuggestion> _suggestions = [];
  bool _showSuggestions = false;
  String _currentInput = '';

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTextChanged);
    widget.focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    widget.focusNode.removeListener(_onFocusChanged);
    super.dispose();
  }

  void _onTextChanged() {
    final text = widget.controller.text;
    final cursorPosition = widget.controller.selection.baseOffset;
    
    setState(() {
      _currentInput = text;
      _updateSuggestions(text, cursorPosition);
    });
  }

  void _onFocusChanged() {
    if (!widget.focusNode.hasFocus) {
      setState(() {
        _showSuggestions = false;
      });
    }
  }

  void _updateSuggestions(String text, int cursorPosition) {
    if (!text.startsWith('=') || cursorPosition <= 1) {
      _showSuggestions = false;
      return;
    }

    // Extract the current function being typed
    final beforeCursor = text.substring(1, cursorPosition);
    final functionMatch = RegExp(r'([A-Z]*)$').firstMatch(beforeCursor.toUpperCase());
    
    if (functionMatch != null) {
      final partialFunction = functionMatch.group(1)!;
      if (partialFunction.isNotEmpty) {
        final functionSuggestions = _formulaEngine.getFunctionSuggestions(partialFunction);
        _suggestions = functionSuggestions.map((func) => _createFunctionSuggestion(func)).toList();
        _showSuggestions = _suggestions.isNotEmpty;
      } else {
        _showSuggestions = false;
      }
    } else {
      _showSuggestions = false;
    }
  }

  FunctionSuggestion _createFunctionSuggestion(String functionName) {
    // Get function details (this would be enhanced with actual function metadata)
    final functionDetails = _getFunctionDetails(functionName);
    return FunctionSuggestion(
      name: functionName,
      description: functionDetails['description'] ?? 'Excel function',
      syntax: functionDetails['syntax'] ?? '$functionName()',
      category: functionDetails['category'] ?? 'General',
    );
  }

  Map<String, String> _getFunctionDetails(String functionName) {
    const functionDetails = {
      'SUM': {
        'description': 'Adds all numbers in a range of cells',
        'syntax': 'SUM(range)',
        'category': 'Math & Trig',
      },
      'AVERAGE': {
        'description': 'Returns the average of numbers in a range',
        'syntax': 'AVERAGE(range)',
        'category': 'Statistical',
      },
      'COUNT': {
        'description': 'Counts the number of cells that contain numbers',
        'syntax': 'COUNT(range)',
        'category': 'Statistical',
      },
      'MAX': {
        'description': 'Returns the largest value in a range',
        'syntax': 'MAX(range)',
        'category': 'Statistical',
      },
      'MIN': {
        'description': 'Returns the smallest value in a range',
        'syntax': 'MIN(range)',
        'category': 'Statistical',
      },
      'IF': {
        'description': 'Returns one value if condition is true, another if false',
        'syntax': 'IF(condition, value_if_true, value_if_false)',
        'category': 'Logical',
      },
      'ROUND': {
        'description': 'Rounds a number to specified decimal places',
        'syntax': 'ROUND(number, digits)',
        'category': 'Math & Trig',
      },
      'CONCATENATE': {
        'description': 'Joins several text strings into one',
        'syntax': 'CONCATENATE(text1, text2, ...)',
        'category': 'Text',
      },
      'TODAY': {
        'description': 'Returns the current date',
        'syntax': 'TODAY()',
        'category': 'Date & Time',
      },
      'NOW': {
        'description': 'Returns the current date and time',
        'syntax': 'NOW()',
        'category': 'Date & Time',
      },
    };

    return functionDetails[functionName] ?? {};
  }

  void _selectSuggestion(FunctionSuggestion suggestion) {
    final text = widget.controller.text;
    final cursorPosition = widget.controller.selection.baseOffset;
    
    // Find the start of the current function being typed
    final beforeCursor = text.substring(1, cursorPosition);
    final functionMatch = RegExp(r'([A-Z]*)$').firstMatch(beforeCursor.toUpperCase());
    
    if (functionMatch != null) {
      final partialFunction = functionMatch.group(1)!;
      final startIndex = cursorPosition - partialFunction.length;
      
      // Replace the partial function with the selected function
      final newText = '${text.substring(0, startIndex)}${suggestion.name}(${text.substring(cursorPosition)}';
      
      widget.controller.text = newText;
      widget.controller.selection = TextSelection.collapsed(
        offset: startIndex + suggestion.name.length + 1,
      );
    }
    
    setState(() {
      _showSuggestions = false;
    });
    
    widget.onSuggestionSelected(suggestion.name);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TextField(
          controller: widget.controller,
          focusNode: widget.focusNode,
          decoration: InputDecoration(
            hintText: 'Enter formula or value...',
            border: const OutlineInputBorder(),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            suffixIcon: _buildValidationIcon(),
          ),
          onSubmitted: (_) => widget.onSubmit?.call(),
        ),
        if (_showSuggestions) _buildSuggestionsPanel(),
      ],
    );
  }

  Widget? _buildValidationIcon() {
    if (_currentInput.isEmpty || !_currentInput.startsWith('=')) {
      return null;
    }

    final isValid = _formulaEngine.isValidFormula(_currentInput);
    return Icon(
      isValid ? Icons.check_circle : Icons.error,
      color: isValid ? const Color(0xFF27AE60) : const Color(0xFFE74C3C),
      size: 20,
    );
  }

  Widget _buildSuggestionsPanel() {
    return Container(
      constraints: const BoxConstraints(maxHeight: 200),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: const Color(0xFFE9ECEF)),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(8),
          bottomRight: Radius.circular(8),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: _suggestions.length,
        itemBuilder: (context, index) {
          final suggestion = _suggestions[index];
          return _buildSuggestionItem(suggestion);
        },
      ),
    );
  }

  Widget _buildSuggestionItem(FunctionSuggestion suggestion) {
    return InkWell(
      onTap: () => _selectSuggestion(suggestion),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(color: Color(0xFFF8F9FA)),
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getCategoryColor(suggestion.category),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                suggestion.name,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    suggestion.syntax,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF2C3E50),
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    suggestion.description,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFF7F8C8D),
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: const Color(0xFFF8F9FA),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                suggestion.category,
                style: const TextStyle(
                  fontSize: 10,
                  color: Color(0xFF7F8C8D),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Math & Trig':
        return const Color(0xFF3498DB);
      case 'Statistical':
        return const Color(0xFF27AE60);
      case 'Logical':
        return const Color(0xFFE74C3C);
      case 'Text':
        return const Color(0xFFF39C12);
      case 'Date & Time':
        return const Color(0xFF9B59B6);
      default:
        return const Color(0xFF95A5A6);
    }
  }
}

class FunctionSuggestion {
  final String name;
  final String description;
  final String syntax;
  final String category;

  const FunctionSuggestion({
    required this.name,
    required this.description,
    required this.syntax,
    required this.category,
  });
}
