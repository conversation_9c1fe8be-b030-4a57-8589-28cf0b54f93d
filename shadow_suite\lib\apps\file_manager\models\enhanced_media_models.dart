import 'dart:ui';

// Video Player Model
class VideoPlayer {
  final String id;
  final String videoPath;
  final String? subtitlePath;
  final VideoPlayerSettings settings;
  final VideoPlayerState state;
  final Duration currentPosition;
  final Duration duration;
  final double playbackSpeed;
  final double volume;
  final double brightness;
  final double contrast;
  final double saturation;
  final DateTime createdAt;

  const VideoPlayer({
    required this.id,
    required this.videoPath,
    this.subtitlePath,
    required this.settings,
    required this.state,
    required this.currentPosition,
    required this.duration,
    required this.playbackSpeed,
    required this.volume,
    required this.brightness,
    required this.contrast,
    required this.saturation,
    required this.createdAt,
  });

  VideoPlayer copyWith({
    VideoPlayerState? state,
    Duration? currentPosition,
    double? playbackSpeed,
    double? volume,
    double? brightness,
    double? contrast,
    double? saturation,
  }) {
    return VideoPlayer(
      id: id,
      videoPath: videoPath,
      subtitlePath: subtitlePath,
      settings: settings,
      state: state ?? this.state,
      currentPosition: currentPosition ?? this.currentPosition,
      duration: duration,
      playbackSpeed: playbackSpeed ?? this.playbackSpeed,
      volume: volume ?? this.volume,
      brightness: brightness ?? this.brightness,
      contrast: contrast ?? this.contrast,
      saturation: saturation ?? this.saturation,
      createdAt: createdAt,
    );
  }
}

// Video Player Settings Model
class VideoPlayerSettings {
  final bool autoPlay;
  final bool loop;
  final bool showControls;
  final bool enableSubtitles;
  final SubtitleStyle subtitleStyle;
  final VideoQuality preferredQuality;
  final bool enableHardwareAcceleration;

  const VideoPlayerSettings({
    required this.autoPlay,
    required this.loop,
    required this.showControls,
    required this.enableSubtitles,
    required this.subtitleStyle,
    required this.preferredQuality,
    required this.enableHardwareAcceleration,
  });

  factory VideoPlayerSettings.defaultSettings() {
    return VideoPlayerSettings(
      autoPlay: false,
      loop: false,
      showControls: true,
      enableSubtitles: true,
      subtitleStyle: SubtitleStyle.defaultStyle(),
      preferredQuality: VideoQuality.auto,
      enableHardwareAcceleration: true,
    );
  }
}

// Subtitle Style Model
class SubtitleStyle {
  final String fontFamily;
  final double fontSize;
  final Color textColor;
  final Color backgroundColor;
  final Color outlineColor;
  final double outlineWidth;
  final SubtitlePosition position;

  const SubtitleStyle({
    required this.fontFamily,
    required this.fontSize,
    required this.textColor,
    required this.backgroundColor,
    required this.outlineColor,
    required this.outlineWidth,
    required this.position,
  });

  factory SubtitleStyle.defaultStyle() {
    return const SubtitleStyle(
      fontFamily: 'Arial',
      fontSize: 16.0,
      textColor: Color(0xFFFFFFFF),
      backgroundColor: Color(0x80000000),
      outlineColor: Color(0xFF000000),
      outlineWidth: 1.0,
      position: SubtitlePosition.bottom,
    );
  }
}

// Audio Equalizer Model
class AudioEqualizer {
  final String id;
  final String audioPath;
  final EqualizerPreset preset;
  final bool isEnabled;
  final List<EqualizerBand> customBands;
  final DateTime createdAt;

  const AudioEqualizer({
    required this.id,
    required this.audioPath,
    required this.preset,
    required this.isEnabled,
    required this.customBands,
    required this.createdAt,
  });
}

// Equalizer Preset Model
class EqualizerPreset {
  final String id;
  final String name;
  final List<double> frequencies;
  final List<double> gains;
  final bool isCustom;

  const EqualizerPreset({
    required this.id,
    required this.name,
    required this.frequencies,
    required this.gains,
    required this.isCustom,
  });

  factory EqualizerPreset.flat() {
    return EqualizerPreset(
      id: 'flat',
      name: 'Flat',
      frequencies: [32, 64, 125, 250, 500, 1000, 2000, 4000, 8000, 16000],
      gains: List.filled(10, 0.0),
      isCustom: false,
    );
  }

  factory EqualizerPreset.rock() {
    return EqualizerPreset(
      id: 'rock',
      name: 'Rock',
      frequencies: [32, 64, 125, 250, 500, 1000, 2000, 4000, 8000, 16000],
      gains: [8.0, 4.0, -5.5, -0.8, -0.4, 1.0, 8.8, 11.2, 11.2, 11.2],
      isCustom: false,
    );
  }
}

// Equalizer Band Model
class EqualizerBand {
  final double frequency;
  final double gain;
  final double quality;

  const EqualizerBand({
    required this.frequency,
    required this.gain,
    required this.quality,
  });
}

// Audio Visualization Model
class AudioVisualization {
  final String id;
  final String audioPath;
  final VisualizationType type;
  final bool isActive;
  final AudioVisualizationSettings settings;
  final DateTime createdAt;

  const AudioVisualization({
    required this.id,
    required this.audioPath,
    required this.type,
    required this.isActive,
    required this.settings,
    required this.createdAt,
  });
}

// Audio Visualization Settings Model
class AudioVisualizationSettings {
  final int barCount;
  final Color primaryColor;
  final Color secondaryColor;
  final bool enableGradient;
  final double sensitivity;
  final bool enableSmoothing;

  const AudioVisualizationSettings({
    required this.barCount,
    required this.primaryColor,
    required this.secondaryColor,
    required this.enableGradient,
    required this.sensitivity,
    required this.enableSmoothing,
  });

  factory AudioVisualizationSettings.defaultSettings() {
    return const AudioVisualizationSettings(
      barCount: 64,
      primaryColor: Color(0xFF2196F3),
      secondaryColor: Color(0xFF03DAC6),
      enableGradient: true,
      sensitivity: 1.0,
      enableSmoothing: true,
    );
  }
}

// Image Editor Model
class ImageEditor {
  final String id;
  final String imagePath;
  final String originalImagePath;
  final List<ImageEditAction> editHistory;
  final ImageFilter? currentFilter;
  final ImageAdjustments adjustments;
  final CropArea? cropArea;
  final double rotationAngle;
  final bool isModified;
  final DateTime createdAt;

  const ImageEditor({
    required this.id,
    required this.imagePath,
    required this.originalImagePath,
    required this.editHistory,
    this.currentFilter,
    required this.adjustments,
    this.cropArea,
    required this.rotationAngle,
    required this.isModified,
    required this.createdAt,
  });

  ImageEditor copyWith({
    ImageFilter? currentFilter,
    ImageAdjustments? adjustments,
    CropArea? cropArea,
    double? rotationAngle,
    bool? isModified,
  }) {
    return ImageEditor(
      id: id,
      imagePath: imagePath,
      originalImagePath: originalImagePath,
      editHistory: editHistory,
      currentFilter: currentFilter ?? this.currentFilter,
      adjustments: adjustments ?? this.adjustments,
      cropArea: cropArea ?? this.cropArea,
      rotationAngle: rotationAngle ?? this.rotationAngle,
      isModified: isModified ?? this.isModified,
      createdAt: createdAt,
    );
  }
}

// Image Filter Model
class ImageFilter {
  final String id;
  final String name;
  final FilterType type;
  final Map<String, double> parameters;

  const ImageFilter({
    required this.id,
    required this.name,
    required this.type,
    required this.parameters,
  });

  factory ImageFilter.sepia() {
    return const ImageFilter(
      id: 'sepia',
      name: 'Sepia',
      type: FilterType.colorAdjustment,
      parameters: {'intensity': 0.8},
    );
  }

  factory ImageFilter.blackAndWhite() {
    return const ImageFilter(
      id: 'bw',
      name: 'Black & White',
      type: FilterType.colorAdjustment,
      parameters: {'intensity': 1.0},
    );
  }
}

// Image Adjustments Model
class ImageAdjustments {
  final double brightness;
  final double contrast;
  final double saturation;
  final double hue;
  final double gamma;
  final double exposure;
  final double highlights;
  final double shadows;

  const ImageAdjustments({
    required this.brightness,
    required this.contrast,
    required this.saturation,
    required this.hue,
    required this.gamma,
    required this.exposure,
    required this.highlights,
    required this.shadows,
  });

  factory ImageAdjustments.neutral() {
    return const ImageAdjustments(
      brightness: 0.0,
      contrast: 0.0,
      saturation: 0.0,
      hue: 0.0,
      gamma: 1.0,
      exposure: 0.0,
      highlights: 0.0,
      shadows: 0.0,
    );
  }
}

// Crop Area Model
class CropArea {
  final double x;
  final double y;
  final double width;
  final double height;
  final double aspectRatio;

  const CropArea({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    required this.aspectRatio,
  });
}

// Image Edit Action Model
class ImageEditAction {
  final ImageEditType type;
  final ImageFilter? filter;
  final ImageAdjustments? adjustments;
  final CropArea? cropArea;
  final double? rotationAngle;
  final DateTime timestamp;

  const ImageEditAction({
    required this.type,
    this.filter,
    this.adjustments,
    this.cropArea,
    this.rotationAngle,
    required this.timestamp,
  });
}

// Media Converter Model
class MediaConverter {
  final String id;
  final String inputPath;
  final String outputPath;
  final MediaFormat inputFormat;
  final MediaFormat outputFormat;
  final ConversionSettings settings;
  final ConversionStatus status;
  final double progress;
  final DateTime startTime;
  final DateTime? endTime;
  final String? errorMessage;

  const MediaConverter({
    required this.id,
    required this.inputPath,
    required this.outputPath,
    required this.inputFormat,
    required this.outputFormat,
    required this.settings,
    required this.status,
    required this.progress,
    required this.startTime,
    this.endTime,
    this.errorMessage,
  });
}

// Media Format Model
class MediaFormat {
  final String name;
  final String extension;
  final String mimeType;
  final MediaFormatType type;
  final List<String> supportedCodecs;

  const MediaFormat({
    required this.name,
    required this.extension,
    required this.mimeType,
    required this.type,
    required this.supportedCodecs,
  });

  factory MediaFormat.mp4() {
    return const MediaFormat(
      name: 'MP4',
      extension: 'mp4',
      mimeType: 'video/mp4',
      type: MediaFormatType.video,
      supportedCodecs: ['h264', 'h265', 'aac'],
    );
  }
}

// Conversion Settings Model
class ConversionSettings {
  final VideoCodec? videoCodec;
  final AudioCodec? audioCodec;
  final int? videoBitrate;
  final int? audioBitrate;
  final Size? resolution;
  final double? frameRate;
  final int? audioSampleRate;
  final int? audioChannels;

  const ConversionSettings({
    this.videoCodec,
    this.audioCodec,
    this.videoBitrate,
    this.audioBitrate,
    this.resolution,
    this.frameRate,
    this.audioSampleRate,
    this.audioChannels,
  });

  factory ConversionSettings.defaultSettings() {
    return const ConversionSettings(
      videoCodec: VideoCodec.h264,
      audioCodec: AudioCodec.aac,
      videoBitrate: 2000000, // 2 Mbps
      audioBitrate: 128000,  // 128 kbps
      frameRate: 30.0,
      audioSampleRate: 44100,
      audioChannels: 2,
    );
  }
}

// Slideshow Model
class Slideshow {
  final String id;
  final String name;
  final List<String> imagePaths;
  final SlideshowSettings settings;
  final int currentIndex;
  final bool isPlaying;
  final DateTime createdAt;
  final DateTime lastModified;

  const Slideshow({
    required this.id,
    required this.name,
    required this.imagePaths,
    required this.settings,
    required this.currentIndex,
    required this.isPlaying,
    required this.createdAt,
    required this.lastModified,
  });
}

// Slideshow Settings Model
class SlideshowSettings {
  final Duration slideDuration;
  final TransitionType transitionType;
  final Duration transitionDuration;
  final bool enableMusic;
  final String? musicPath;
  final bool loop;
  final bool randomOrder;

  const SlideshowSettings({
    required this.slideDuration,
    required this.transitionType,
    required this.transitionDuration,
    required this.enableMusic,
    this.musicPath,
    required this.loop,
    required this.randomOrder,
  });

  factory SlideshowSettings.defaultSettings() {
    return const SlideshowSettings(
      slideDuration: Duration(seconds: 3),
      transitionType: TransitionType.fade,
      transitionDuration: Duration(milliseconds: 500),
      enableMusic: false,
      loop: true,
      randomOrder: false,
    );
  }
}

// Audio Recorder Model
class AudioRecorder {
  final String id;
  final String outputPath;
  final AudioRecordingSettings settings;
  final RecordingStatus status;
  final Duration duration;
  final int fileSize;
  final DateTime startTime;

  const AudioRecorder({
    required this.id,
    required this.outputPath,
    required this.settings,
    required this.status,
    required this.duration,
    required this.fileSize,
    required this.startTime,
  });
}

// Audio Recording Settings Model
class AudioRecordingSettings {
  final AudioFormat format;
  final int sampleRate;
  final int bitRate;
  final int channels;
  final bool enableNoiseReduction;
  final double inputGain;

  const AudioRecordingSettings({
    required this.format,
    required this.sampleRate,
    required this.bitRate,
    required this.channels,
    required this.enableNoiseReduction,
    required this.inputGain,
  });

  factory AudioRecordingSettings.defaultSettings() {
    return const AudioRecordingSettings(
      format: AudioFormat.mp3,
      sampleRate: 44100,
      bitRate: 128000,
      channels: 2,
      enableNoiseReduction: true,
      inputGain: 1.0,
    );
  }
}

// Screen Recorder Model
class ScreenRecorder {
  final String id;
  final String outputPath;
  final ScreenRecordingSettings settings;
  final RecordingStatus status;
  final Duration duration;
  final int fileSize;
  final DateTime startTime;

  const ScreenRecorder({
    required this.id,
    required this.outputPath,
    required this.settings,
    required this.status,
    required this.duration,
    required this.fileSize,
    required this.startTime,
  });
}

// Screen Recording Settings Model
class ScreenRecordingSettings {
  final Size resolution;
  final double frameRate;
  final int bitRate;
  final bool recordAudio;
  final bool showCursor;
  final bool recordTouches;

  const ScreenRecordingSettings({
    required this.resolution,
    required this.frameRate,
    required this.bitRate,
    required this.recordAudio,
    required this.showCursor,
    required this.recordTouches,
  });

  factory ScreenRecordingSettings.defaultSettings() {
    return const ScreenRecordingSettings(
      resolution: Size(1920, 1080),
      frameRate: 30.0,
      bitRate: 5000000, // 5 Mbps
      recordAudio: true,
      showCursor: true,
      recordTouches: true,
    );
  }
}

// Enhanced Media Event Model
class EnhancedMediaEvent {
  final EnhancedMediaEventType type;
  final String? playerId;
  final String? editorId;
  final String? filePath;
  final dynamic value;
  final DateTime timestamp;

  const EnhancedMediaEvent({
    required this.type,
    this.playerId,
    this.editorId,
    this.filePath,
    this.value,
    required this.timestamp,
  });
}

// Enums
enum VideoPlayerState { stopped, playing, paused, buffering, error }
enum VideoQuality { auto, low, medium, high, ultra }
enum SubtitlePosition { top, center, bottom }
enum VisualizationType { spectrum, waveform, bars, circle }
enum FilterType { colorAdjustment, blur, sharpen, artistic }
enum ImageEditType { filter, adjustment, crop, rotate, resize }
enum MediaFormatType { video, audio, image }
enum VideoCodec { h264, h265, vp8, vp9, av1 }
enum AudioCodec { aac, mp3, flac, ogg, opus }
enum AudioFormat { mp3, wav, flac, aac, ogg }
enum TransitionType { none, fade, slide, zoom, flip }
enum ConversionStatus { pending, converting, completed, failed }
enum RecordingStatus { recording, paused, stopped, error }
enum EnhancedMediaEventType {
  playbackSpeedChanged,
  frameStep,
  equalizerChanged,
  imageFilterApplied,
  imageCropped,
  imageRotated,
  conversionStarted,
  conversionCompleted,
  metadataUpdated,
  recordingStarted,
  recordingStopped,
}
