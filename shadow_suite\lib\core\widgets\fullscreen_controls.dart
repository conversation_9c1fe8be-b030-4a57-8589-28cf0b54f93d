import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/fullscreen_service.dart';

/// Full-screen controls overlay with auto-hide functionality
class FullScreenControls extends ConsumerStatefulWidget {
  final Widget child;
  final List<Widget>? topControls;
  final List<Widget>? bottomControls;
  final List<Widget>? centerControls;
  final VoidCallback? onExitFullScreen;
  final VoidCallback? onToggleFullScreen;
  final bool showDefaultControls;
  final Color overlayColor;
  final Duration fadeAnimationDuration;
  final bool enableGestures;

  const FullScreenControls({
    super.key,
    required this.child,
    this.topControls,
    this.bottomControls,
    this.centerControls,
    this.onExitFullScreen,
    this.onToggleFullScreen,
    this.showDefaultControls = true,
    this.overlayColor = Colors.black54,
    this.fadeAnimationDuration = const Duration(milliseconds: 300),
    this.enableGestures = true,
  });

  @override
  ConsumerState<FullScreenControls> createState() => _FullScreenControlsState();
}

class _FullScreenControlsState extends ConsumerState<FullScreenControls>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  Timer? _autoHideTimer;
  bool _controlsVisible = true;

  @override
  void initState() {
    super.initState();
    
    _fadeController = AnimationController(
      duration: widget.fadeAnimationDuration,
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _fadeController.forward();
    _startAutoHideTimer();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _autoHideTimer?.cancel();
    super.dispose();
  }

  void _startAutoHideTimer() {
    _autoHideTimer?.cancel();
    
    if (FullScreenService.autoHideEnabled && _controlsVisible) {
      _autoHideTimer = Timer(FullScreenService.autoHideDelay, () {
        if (mounted) {
          _hideControls();
        }
      });
    }
  }

  void _showControls() {
    if (!_controlsVisible) {
      setState(() {
        _controlsVisible = true;
      });
      _fadeController.forward();
    }
    _startAutoHideTimer();
  }

  void _hideControls() {
    if (_controlsVisible) {
      setState(() {
        _controlsVisible = false;
      });
      _fadeController.reverse();
    }
    _autoHideTimer?.cancel();
  }

  void _toggleControls() {
    if (_controlsVisible) {
      _hideControls();
    } else {
      _showControls();
    }
  }

  void _handleTap() {
    if (widget.enableGestures) {
      _toggleControls();
    }
  }

  void _handleDoubleTap() {
    if (widget.enableGestures) {
      widget.onToggleFullScreen?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isFullScreen = ref.watch(fullScreenProvider);
    
    return GestureDetector(
      onTap: _handleTap,
      onDoubleTap: _handleDoubleTap,
      child: Stack(
        children: [
          // Main content
          widget.child,
          
          // Controls overlay
          if (isFullScreen)
            AnimatedBuilder(
              animation: _fadeAnimation,
              builder: (context, child) {
                return Opacity(
                  opacity: _fadeAnimation.value,
                  child: _buildControlsOverlay(context),
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildControlsOverlay(BuildContext context) {
    return Container(
      color: widget.overlayColor.withValues(alpha: 0.3),
      child: SafeArea(
        child: Column(
          children: [
            // Top controls
            _buildTopControls(context),
            
            // Center controls
            Expanded(
              child: _buildCenterControls(context),
            ),
            
            // Bottom controls
            _buildBottomControls(context),
          ],
        ),
      ),
    );
  }

  Widget _buildTopControls(BuildContext context) {
    final topControls = widget.topControls ?? [];
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withValues(alpha: 0.7),
            Colors.transparent,
          ],
        ),
      ),
      child: Row(
        children: [
          // Default back button
          if (widget.showDefaultControls)
            IconButton(
              onPressed: widget.onExitFullScreen ?? () {
                FullScreenService.exitFullScreen();
                ref.read(fullScreenProvider.notifier).state = false;
              },
              icon: const Icon(Icons.arrow_back, color: Colors.white),
              tooltip: 'Exit Full Screen',
            ),
          
          // Custom top controls
          ...topControls,
          
          const Spacer(),
          
          // Default settings button
          if (widget.showDefaultControls)
            IconButton(
              onPressed: () => _showFullScreenSettings(context),
              icon: const Icon(Icons.settings, color: Colors.white),
              tooltip: 'Full Screen Settings',
            ),
        ],
      ),
    );
  }

  Widget _buildCenterControls(BuildContext context) {
    final centerControls = widget.centerControls ?? [];
    
    if (centerControls.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Center(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.5),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: centerControls,
        ),
      ),
    );
  }

  Widget _buildBottomControls(BuildContext context) {
    final bottomControls = widget.bottomControls ?? [];
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.bottomCenter,
          end: Alignment.topCenter,
          colors: [
            Colors.black.withValues(alpha: 0.7),
            Colors.transparent,
          ],
        ),
      ),
      child: Row(
        children: [
          // Custom bottom controls
          ...bottomControls,
          
          const Spacer(),
          
          // Default full-screen toggle
          if (widget.showDefaultControls)
            IconButton(
              onPressed: widget.onToggleFullScreen ?? () {
                FullScreenService.toggleFullScreen();
                ref.read(fullScreenProvider.notifier).state = 
                    !ref.read(fullScreenProvider);
              },
              icon: Icon(
                ref.watch(fullScreenProvider) 
                    ? Icons.fullscreen_exit 
                    : Icons.fullscreen,
                color: Colors.white,
              ),
              tooltip: ref.watch(fullScreenProvider) 
                  ? 'Exit Full Screen' 
                  : 'Enter Full Screen',
            ),
        ],
      ),
    );
  }

  void _showFullScreenSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const FullScreenSettingsDialog(),
    );
  }
}

/// Full-screen settings dialog
class FullScreenSettingsDialog extends ConsumerStatefulWidget {
  const FullScreenSettingsDialog({super.key});

  @override
  ConsumerState<FullScreenSettingsDialog> createState() => 
      _FullScreenSettingsDialogState();
}

class _FullScreenSettingsDialogState 
    extends ConsumerState<FullScreenSettingsDialog> {
  @override
  Widget build(BuildContext context) {
    final autoHideConfig = ref.watch(autoHideConfigProvider);
    
    return AlertDialog(
      title: const Text('Full Screen Settings'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SwitchListTile(
            title: const Text('Auto-hide Controls'),
            subtitle: const Text('Automatically hide controls after inactivity'),
            value: autoHideConfig.enabled,
            onChanged: (value) {
              ref.read(autoHideConfigProvider.notifier).state = 
                  autoHideConfig.copyWith(enabled: value);
              FullScreenService.setAutoHideConfig(enabled: value);
            },
          ),
          
          if (autoHideConfig.enabled) ...[
            const SizedBox(height: 16),
            Text('Auto-hide Delay: ${autoHideConfig.delay.inSeconds}s'),
            Slider(
              value: autoHideConfig.delay.inSeconds.toDouble(),
              min: 1,
              max: 10,
              divisions: 9,
              label: '${autoHideConfig.delay.inSeconds}s',
              onChanged: (value) {
                final newDelay = Duration(seconds: value.round());
                ref.read(autoHideConfigProvider.notifier).state = 
                    autoHideConfig.copyWith(delay: newDelay);
                FullScreenService.setAutoHideConfig(delay: newDelay);
              },
            ),
          ],
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }
}

/// Simple full-screen toggle button
class FullScreenToggleButton extends ConsumerWidget {
  final VoidCallback? onPressed;
  final Color? iconColor;
  final double? iconSize;
  final String? tooltip;

  const FullScreenToggleButton({
    super.key,
    this.onPressed,
    this.iconColor,
    this.iconSize,
    this.tooltip,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isFullScreen = ref.watch(fullScreenProvider);
    
    return IconButton(
      onPressed: onPressed ?? () async {
        await FullScreenService.toggleFullScreen();
        ref.read(fullScreenProvider.notifier).state = !isFullScreen;
      },
      icon: Icon(
        isFullScreen ? Icons.fullscreen_exit : Icons.fullscreen,
        color: iconColor,
        size: iconSize,
      ),
      tooltip: tooltip ?? (isFullScreen ? 'Exit Full Screen' : 'Enter Full Screen'),
    );
  }
}

/// Full-screen wrapper widget
class FullScreenWrapper extends ConsumerWidget {
  final Widget child;
  final bool enableFullScreen;
  final FullScreenConfig? config;

  const FullScreenWrapper({
    super.key,
    required this.child,
    this.enableFullScreen = true,
    this.config,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (!enableFullScreen) {
      return child;
    }

    return FullScreenControls(
      onExitFullScreen: () async {
        await FullScreenService.exitFullScreen();
        ref.read(fullScreenProvider.notifier).state = false;
      },
      onToggleFullScreen: () async {
        await FullScreenService.toggleFullScreen();
        ref.read(fullScreenProvider.notifier).state = 
            !ref.read(fullScreenProvider);
      },
      child: child,
    );
  }
}
