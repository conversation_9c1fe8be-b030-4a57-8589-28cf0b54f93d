import 'dart:io';
import 'dart:convert';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import 'settings_service.dart';
import 'database_error_handler.dart';

// Comprehensive Backup and Recovery Service
class BackupService {
  static const String _backupExtension = '.shadowsuite_backup';
  // static const String _settingsBackupName = 'settings_backup.json'; // Reserved for future use

  // Create a complete backup of all app data
  static Future<DatabaseResult<String>> createFullBackup({
    String? customPath,
    String? customName,
  }) async {
    try {
      final backupDir = await _getBackupDirectory(customPath);
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      final backupName = customName ?? 'shadowsuite_backup_$timestamp';
      final backupPath = path.join(backupDir.path, '$backupName$_backupExtension');

      // Create backup directory if it doesn't exist
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }

      final backupData = <String, dynamic>{};

      // Backup all databases
      await _backupDatabases(backupData);

      // Backup settings
      await _backupSettings(backupData);

      // Backup user files (if any)
      await _backupUserFiles(backupData);

      // Write backup file
      final backupFile = File(backupPath);
      await backupFile.writeAsString(jsonEncode(backupData));

      return DatabaseResult.success(backupPath);
    } catch (e, stackTrace) {
      final error = DatabaseErrorHandler.handleException(
        e,
        stackTrace,
        operation: 'createFullBackup',
      );
      return DatabaseResult.failure(error);
    }
  }

  // Restore from a backup file
  static Future<DatabaseResult<bool>> restoreFromBackup(String backupPath) async {
    try {
      final backupFile = File(backupPath);
      if (!await backupFile.exists()) {
        return DatabaseResult.failure(DatabaseError(
          type: DatabaseErrorType.unknown,
          message: 'Backup file not found',
          userMessage: 'The selected backup file does not exist',
        ));
      }

      final backupContent = await backupFile.readAsString();
      final backupData = jsonDecode(backupContent) as Map<String, dynamic>;

      // Validate backup format
      if (!_isValidBackupFormat(backupData)) {
        return DatabaseResult.failure(DatabaseError(
          type: DatabaseErrorType.unknown,
          message: 'Invalid backup format',
          userMessage: 'The selected file is not a valid ShadowSuite backup',
        ));
      }

      // Restore databases
      await _restoreDatabases(backupData);

      // Restore settings
      await _restoreSettings(backupData);

      // Restore user files
      await _restoreUserFiles(backupData);

      return const DatabaseResult.success(true);
    } catch (e, stackTrace) {
      final error = DatabaseErrorHandler.handleException(
        e,
        stackTrace,
        operation: 'restoreFromBackup',
      );
      return DatabaseResult.failure(error);
    }
  }

  // Get list of available backups
  static Future<List<BackupInfo>> getAvailableBackups([String? customPath]) async {
    try {
      final backupDir = await _getBackupDirectory(customPath);
      if (!await backupDir.exists()) {
        return [];
      }

      final backups = <BackupInfo>[];
      await for (final entity in backupDir.list()) {
        if (entity is File && entity.path.endsWith(_backupExtension)) {
          final stat = await entity.stat();
          final name = path.basenameWithoutExtension(entity.path);
          
          backups.add(BackupInfo(
            name: name,
            path: entity.path,
            size: stat.size,
            createdAt: stat.modified,
          ));
        }
      }

      // Sort by creation date (newest first)
      backups.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      return backups;
    } catch (e) {
      return [];
    }
  }

  // Delete a backup file
  static Future<bool> deleteBackup(String backupPath) async {
    try {
      final backupFile = File(backupPath);
      if (await backupFile.exists()) {
        await backupFile.delete();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  // Auto backup if enabled
  static Future<void> performAutoBackupIfNeeded() async {
    if (!SettingsService.getAutoBackup()) {
      return;
    }

    final lastBackupKey = 'last_auto_backup';
    final lastBackupTime = SettingsService.getAppSetting<int>('system', lastBackupKey, defaultValue: 0);
    final now = DateTime.now().millisecondsSinceEpoch;
    final intervalMs = SettingsService.getBackupInterval() * 60 * 60 * 1000; // Convert hours to milliseconds

    if (now - lastBackupTime! >= intervalMs) {
      final result = await createFullBackup();
      if (result.isSuccess) {
        await SettingsService.setAppSetting('system', lastBackupKey, now);
      }
    }
  }

  // Private helper methods
  static Future<Directory> _getBackupDirectory([String? customPath]) async {
    if (customPath != null && customPath.isNotEmpty) {
      return Directory(customPath);
    }

    final documentsDir = await getApplicationDocumentsDirectory();
    return Directory(path.join(documentsDir.path, 'ShadowSuite', 'Backups'));
  }

  static Future<void> _backupDatabases(Map<String, dynamic> backupData) async {
    final databases = <String, dynamic>{};

    // Get all database files
    final dbPaths = await _getAllDatabasePaths();
    
    for (final dbPath in dbPaths) {
      final dbFile = File(dbPath);
      if (await dbFile.exists()) {
        final dbName = path.basename(dbPath);
        final dbContent = await dbFile.readAsBytes();
        databases[dbName] = base64Encode(dbContent);
      }
    }

    backupData['databases'] = databases;
  }

  static Future<void> _backupSettings(Map<String, dynamic> backupData) async {
    final settings = await SettingsService.exportSettings();
    backupData['settings'] = settings;
  }

  static Future<void> _backupUserFiles(Map<String, dynamic> backupData) async {
    // Backup any user-created files (voice memos, attachments, etc.)
    final userFiles = <String, dynamic>{};
    
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final userFilesDir = Directory(path.join(appDir.path, 'ShadowSuite', 'UserFiles'));
      
      if (await userFilesDir.exists()) {
        await for (final entity in userFilesDir.list(recursive: true)) {
          if (entity is File) {
            final relativePath = path.relative(entity.path, from: userFilesDir.path);
            final content = await entity.readAsBytes();
            userFiles[relativePath] = base64Encode(content);
          }
        }
      }
    } catch (e) {
      // User files backup is optional, continue if it fails
    }

    backupData['userFiles'] = userFiles;
  }

  static Future<void> _restoreDatabases(Map<String, dynamic> backupData) async {
    final databases = backupData['databases'] as Map<String, dynamic>?;
    if (databases == null) return;

    final dbDir = await getDatabasesPath();
    
    for (final entry in databases.entries) {
      final dbName = entry.key;
      final dbContent = base64Decode(entry.value as String);
      
      final dbPath = path.join(dbDir, dbName);
      final dbFile = File(dbPath);
      
      // Close any open connections to this database
      await _closeDatabaseConnections(dbPath);
      
      // Write the restored database
      await dbFile.writeAsBytes(dbContent);
    }
  }

  static Future<void> _restoreSettings(Map<String, dynamic> backupData) async {
    final settings = backupData['settings'] as Map<String, dynamic>?;
    if (settings != null) {
      await SettingsService.importSettings(settings);
    }
  }

  static Future<void> _restoreUserFiles(Map<String, dynamic> backupData) async {
    final userFiles = backupData['userFiles'] as Map<String, dynamic>?;
    if (userFiles == null) return;

    try {
      final appDir = await getApplicationDocumentsDirectory();
      final userFilesDir = Directory(path.join(appDir.path, 'ShadowSuite', 'UserFiles'));
      
      // Create directory if it doesn't exist
      if (!await userFilesDir.exists()) {
        await userFilesDir.create(recursive: true);
      }

      for (final entry in userFiles.entries) {
        final relativePath = entry.key;
        final content = base64Decode(entry.value as String);
        
        final filePath = path.join(userFilesDir.path, relativePath);
        final file = File(filePath);
        
        // Create parent directories if needed
        await file.parent.create(recursive: true);
        await file.writeAsBytes(content);
      }
    } catch (e) {
      // User files restore is optional, continue if it fails
    }
  }

  static Future<List<String>> _getAllDatabasePaths() async {
    final dbDir = await getDatabasesPath();
    final paths = <String>[];
    
    // Add known database files
    paths.add(path.join(dbDir, 'memo_suite.db'));
    paths.add(path.join(dbDir, 'islamic_app.db'));
    paths.add(path.join(dbDir, 'money_flow.db'));
    paths.add(path.join(dbDir, 'tools_builder.db'));
    
    return paths;
  }

  static Future<void> _closeDatabaseConnections(String dbPath) async {
    // This would close any open database connections
    // Implementation depends on how databases are managed in the app
  }

  static bool _isValidBackupFormat(Map<String, dynamic> backupData) {
    return backupData.containsKey('databases') || 
           backupData.containsKey('settings') || 
           backupData.containsKey('userFiles');
  }
}

// Backup Information Model
class BackupInfo {
  final String name;
  final String path;
  final int size;
  final DateTime createdAt;

  BackupInfo({
    required this.name,
    required this.path,
    required this.size,
    required this.createdAt,
  });

  String get formattedSize {
    if (size < 1024) return '$size B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)} KB';
    if (size < 1024 * 1024 * 1024) return '${(size / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  String get formattedDate {
    return '${createdAt.day}/${createdAt.month}/${createdAt.year} ${createdAt.hour.toString().padLeft(2, '0')}:${createdAt.minute.toString().padLeft(2, '0')}';
  }
}
