import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class NoteEditorScreen extends ConsumerStatefulWidget {
  const NoteEditorScreen({super.key});

  @override
  ConsumerState<NoteEditorScreen> createState() => _NoteEditorScreenState();
}

class _NoteEditorScreenState extends ConsumerState<NoteEditorScreen> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _contentController = TextEditingController();
  String _selectedCategory = 'Personal';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('New Note'),
        backgroundColor: Colors.amber,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () => _saveNote(),
            icon: const Icon(Icons.save),
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'share', child: Text('Share')),
              const PopupMenuItem(value: 'export', child: Text('Export')),
              const PopupMenuItem(value: 'delete', child: Text('Delete')),
            ],
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Title Field
            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                hintText: 'Note title...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.title),
              ),
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Category Selection
            DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                labelText: 'Category',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.category),
              ),
              items: const [
                DropdownMenuItem(value: 'Personal', child: Text('Personal')),
                DropdownMenuItem(value: 'Work', child: Text('Work')),
                DropdownMenuItem(value: 'Learning', child: Text('Learning')),
                DropdownMenuItem(value: 'Travel', child: Text('Travel')),
                DropdownMenuItem(value: 'Food', child: Text('Food')),
                DropdownMenuItem(value: 'Ideas', child: Text('Ideas')),
              ],
              onChanged: (value) {
                setState(() => _selectedCategory = value!);
              },
            ),
            const SizedBox(height: 16),

            // Content Field
            Expanded(
              child: TextField(
                controller: _contentController,
                decoration: const InputDecoration(
                  hintText: 'Start writing your note...',
                  border: OutlineInputBorder(),
                  alignLabelWithHint: true,
                ),
                maxLines: null,
                expands: true,
                textAlignVertical: TextAlignVertical.top,
              ),
            ),
            const SizedBox(height: 16),

            // Formatting Toolbar
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                children: [
                  IconButton(
                    onPressed: () => _formatText('bold'),
                    icon: const Icon(Icons.format_bold),
                  ),
                  IconButton(
                    onPressed: () => _formatText('italic'),
                    icon: const Icon(Icons.format_italic),
                  ),
                  IconButton(
                    onPressed: () => _formatText('underline'),
                    icon: const Icon(Icons.format_underlined),
                  ),
                  const VerticalDivider(),
                  IconButton(
                    onPressed: () => _insertList(),
                    icon: const Icon(Icons.format_list_bulleted),
                  ),
                  IconButton(
                    onPressed: () => _insertCheckbox(),
                    icon: const Icon(Icons.check_box),
                  ),
                  const Spacer(),
                  Text(
                    '${_contentController.text.length} characters',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _saveNote() {
    if (_titleController.text.isEmpty && _contentController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add some content to save the note'),
        ),
      );
      return;
    }

    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Note saved successfully')));
    Navigator.pop(context);
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'share':
        _shareNote();
        break;
      case 'export':
        _exportNote();
        break;
      case 'delete':
        _deleteNote();
        break;
    }
  }

  void _shareNote() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Note shared successfully')));
  }

  void _exportNote() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Note exported successfully')));
  }

  void _deleteNote() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Note'),
        content: const Text('Are you sure you want to delete this note?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(const SnackBar(content: Text('Note deleted')));
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _formatText(String format) {
    final text = _contentController.text;
    final selection = _contentController.selection;

    if (selection.isValid && !selection.isCollapsed) {
      final selectedText = text.substring(selection.start, selection.end);
      String formattedText;

      switch (format) {
        case 'bold':
          formattedText = '**$selectedText**';
          break;
        case 'italic':
          formattedText = '*$selectedText*';
          break;
        case 'underline':
          formattedText = '_${selectedText}_';
          break;
        default:
          formattedText = selectedText;
      }

      final newText = text.replaceRange(
        selection.start,
        selection.end,
        formattedText,
      );
      _contentController.text = newText;
      _contentController.selection = TextSelection.collapsed(
        offset: selection.start + formattedText.length,
      );
    }
  }

  void _insertList() {
    final cursorPosition = _contentController.selection.baseOffset;
    final text = _contentController.text;
    final newText =
        '${text.substring(0, cursorPosition)}\n• ${text.substring(cursorPosition)}';

    _contentController.text = newText;
    _contentController.selection = TextSelection.collapsed(
      offset: cursorPosition + 3,
    );
  }

  void _insertCheckbox() {
    final cursorPosition = _contentController.selection.baseOffset;
    final text = _contentController.text;
    final newText =
        '${text.substring(0, cursorPosition)}\n☐ ${text.substring(cursorPosition)}';

    _contentController.text = newText;
    _contentController.selection = TextSelection.collapsed(
      offset: cursorPosition + 3,
    );
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }
}
