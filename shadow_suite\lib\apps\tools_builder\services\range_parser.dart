import '../models/spreadsheet.dart';
import '../models/spreadsheet_cell.dart';
import '../utils/cell_address_parser.dart';

// Advanced Range Parser for Excel-style range operations
class RangeParser {
  // Parse range string like "A1:C10" and return list of cell addresses
  static List<String> parseRange(String range) {
    final addresses = <String>[];
    
    if (range.contains(':')) {
      final parts = range.split(':');
      if (parts.length == 2) {
        final startAddress = parts[0].trim();
        final endAddress = parts[1].trim();
        
        final startCoords = CellAddressParser.parseAddress(startAddress);
        final endCoords = CellAddressParser.parseAddress(endAddress);
        
        final startRow = startCoords['row']!;
        final startCol = startCoords['column']!;
        final endRow = endCoords['row']!;
        final endCol = endCoords['column']!;
        
        for (int row = startRow; row <= endRow; row++) {
          for (int col = startCol; col <= endCol; col++) {
            addresses.add(CellAddressParser.indicesToAddress(row, col));
          }
        }
      }
    } else {
      // Single cell
      addresses.add(range.trim());
    }
    
    return addresses;
  }
  
  // Get values from range in spreadsheet
  static List<dynamic> getRangeValues(String range, Spreadsheet spreadsheet) {
    final addresses = parseRange(range);
    final values = <dynamic>[];
    
    final activeSheet = spreadsheet.activeSheet;
    if (activeSheet == null) return values;
    
    for (final address in addresses) {
      final coords = CellAddressParser.parseAddress(address);
      final cell = activeSheet.getCell(coords['row']!, coords['column']!);
      
      if (cell != null) {
        values.add(cell.calculatedValue ?? cell.rawValue);
      } else {
        values.add(0); // Empty cells default to 0 for calculations
      }
    }
    
    return values;
  }
  
  // Get numeric values only from range
  static List<double> getRangeNumericValues(String range, Spreadsheet spreadsheet) {
    final values = getRangeValues(range, spreadsheet);
    final numericValues = <double>[];
    
    for (final value in values) {
      if (value is num) {
        numericValues.add(value.toDouble());
      } else if (value is String) {
        final parsed = double.tryParse(value);
        if (parsed != null) {
          numericValues.add(parsed);
        }
      }
    }
    
    return numericValues;
  }
  
  // Check if string is a valid range
  static bool isValidRange(String range) {
    if (range.contains(':')) {
      final parts = range.split(':');
      if (parts.length == 2) {
        return isValidCellAddress(parts[0].trim()) && isValidCellAddress(parts[1].trim());
      }
    } else {
      return isValidCellAddress(range.trim());
    }
    return false;
  }
  
  // Check if string is a valid cell address
  static bool isValidCellAddress(String address) {
    final regex = RegExp(r'^[A-Z]+\d+$');
    return regex.hasMatch(address.toUpperCase());
  }
  
  // Get range dimensions
  static Map<String, int> getRangeDimensions(String range) {
    final addresses = parseRange(range);
    if (addresses.isEmpty) return {'rows': 0, 'columns': 0};
    
    final firstCoords = CellAddressParser.parseAddress(addresses.first);
    final lastCoords = CellAddressParser.parseAddress(addresses.last);
    
    final rows = lastCoords['row']! - firstCoords['row']! + 1;
    final columns = lastCoords['column']! - firstCoords['column']! + 1;
    
    return {'rows': rows, 'columns': columns};
  }
  
  // Expand relative ranges (like A:A for entire column)
  static String expandRange(String range, Spreadsheet spreadsheet) {
    final activeSheet = spreadsheet.activeSheet;
    if (activeSheet == null) return range;
    
    // Handle entire column (A:A)
    if (range.contains(':') && range.split(':').length == 2) {
      final parts = range.split(':');
      final start = parts[0].trim();
      final end = parts[1].trim();
      
      // Check if it's a column range (A:A)
      if (start.length == 1 && end.length == 1 && start == end) {
        return '${start}1:${end}1000'; // Expand to reasonable range
      }
      
      // Check if it's a row range (1:1)
      if (RegExp(r'^\d+$').hasMatch(start) && start == end) {
        return 'A$start:Z$end'; // Expand to reasonable range
      }
    }
    
    return range;
  }
  
  // Get all cells in range with their addresses
  static Map<String, SpreadsheetCell?> getRangeCells(String range, Spreadsheet spreadsheet) {
    final addresses = parseRange(range);
    final cells = <String, SpreadsheetCell?>{};
    
    final activeSheet = spreadsheet.activeSheet;
    if (activeSheet == null) return cells;
    
    for (final address in addresses) {
      final coords = CellAddressParser.parseAddress(address);
      final cell = activeSheet.getCell(coords['row']!, coords['column']!);
      cells[address] = cell;
    }
    
    return cells;
  }
  
  // Calculate range statistics
  static Map<String, dynamic> getRangeStatistics(String range, Spreadsheet spreadsheet) {
    final values = getRangeNumericValues(range, spreadsheet);
    
    if (values.isEmpty) {
      return {
        'count': 0,
        'sum': 0.0,
        'average': 0.0,
        'min': 0.0,
        'max': 0.0,
      };
    }
    
    final sum = values.reduce((a, b) => a + b);
    final average = sum / values.length;
    final min = values.reduce((a, b) => a < b ? a : b);
    final max = values.reduce((a, b) => a > b ? a : b);
    
    return {
      'count': values.length,
      'sum': sum,
      'average': average,
      'min': min,
      'max': max,
    };
  }
}
