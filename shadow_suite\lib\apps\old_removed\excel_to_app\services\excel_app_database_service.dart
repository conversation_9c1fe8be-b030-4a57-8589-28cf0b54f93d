import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/excel_app_tool.dart';

class ExcelAppDatabaseService {
  static const String _toolsKey = 'excel_app_tools';
  static const String _settingsKey = 'excel_app_settings';

  // Singleton pattern
  static final ExcelAppDatabaseService _instance = ExcelAppDatabaseService._internal();
  factory ExcelAppDatabaseService() => _instance;
  ExcelAppDatabaseService._internal();

  SharedPreferences? _prefs;

  Future<void> initialize() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  // Tool CRUD Operations
  Future<List<ExcelAppTool>> getAllTools() async {
    await initialize();
    final toolsJson = _prefs?.getStringList(_toolsKey) ?? [];
    return toolsJson.map((json) => ExcelAppTool.fromJson(json)).toList();
  }

  Future<ExcelAppTool?> getToolById(String id) async {
    final tools = await getAllTools();
    try {
      return tools.firstWhere((tool) => tool.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<bool> saveTool(ExcelAppTool tool) async {
    try {
      await initialize();
      final tools = await getAllTools();
      
      // Remove existing tool with same ID
      tools.removeWhere((t) => t.id == tool.id);
      
      // Add updated tool
      tools.add(tool);
      
      // Save to preferences
      final toolsJson = tools.map((t) => t.toJson()).toList();
      await _prefs?.setStringList(_toolsKey, toolsJson);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> deleteTool(String id) async {
    try {
      await initialize();
      final tools = await getAllTools();
      
      // Remove tool with matching ID
      tools.removeWhere((t) => t.id == id);
      
      // Save updated list
      final toolsJson = tools.map((t) => t.toJson()).toList();
      await _prefs?.setStringList(_toolsKey, toolsJson);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<ExcelAppTool?> duplicateTool(String id, String newName) async {
    try {
      final originalTool = await getToolById(id);
      if (originalTool == null) return null;

      final now = DateTime.now();
      final duplicatedTool = originalTool.copyWith(
        id: _generateId(),
        name: newName,
        createdAt: now,
        lastModified: now,
        thumbnailPath: null, // Reset thumbnail for new tool
      );

      final success = await saveTool(duplicatedTool);
      return success ? duplicatedTool : null;
    } catch (e) {
      return null;
    }
  }

  // Auto-save functionality
  Future<bool> autoSaveTool(ExcelAppTool tool) async {
    final updatedTool = tool.copyWith(lastModified: DateTime.now());
    return await saveTool(updatedTool);
  }

  // Search and filter
  Future<List<ExcelAppTool>> searchTools(String query) async {
    final tools = await getAllTools();
    if (query.isEmpty) return tools;

    return tools.where((tool) {
      return tool.name.toLowerCase().contains(query.toLowerCase()) ||
             tool.description.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  Future<List<ExcelAppTool>> getRecentTools({int limit = 5}) async {
    final tools = await getAllTools();
    tools.sort((a, b) => b.lastModified.compareTo(a.lastModified));
    return tools.take(limit).toList();
  }

  // Import/Export functionality
  Future<String> exportTool(String id) async {
    final tool = await getToolById(id);
    if (tool == null) throw Exception('Tool not found');
    
    return tool.toJson();
  }

  Future<ExcelAppTool?> importTool(String jsonData, {String? newName}) async {
    try {
      final tool = ExcelAppTool.fromJson(jsonData);
      final now = DateTime.now();
      
      final importedTool = tool.copyWith(
        id: _generateId(),
        name: newName ?? '${tool.name} (Imported)',
        createdAt: now,
        lastModified: now,
        thumbnailPath: null,
      );

      final success = await saveTool(importedTool);
      return success ? importedTool : null;
    } catch (e) {
      return null;
    }
  }

  // Settings management
  Future<Map<String, dynamic>> getSettings() async {
    await initialize();
    final settingsJson = _prefs?.getString(_settingsKey);
    if (settingsJson == null) return _getDefaultSettings();
    
    try {
      return jsonDecode(settingsJson);
    } catch (e) {
      return _getDefaultSettings();
    }
  }

  Future<bool> saveSettings(Map<String, dynamic> settings) async {
    try {
      await initialize();
      await _prefs?.setString(_settingsKey, jsonEncode(settings));
      return true;
    } catch (e) {
      return false;
    }
  }

  Map<String, dynamic> _getDefaultSettings() {
    return {
      'autoSave': true,
      'autoSaveInterval': 30, // seconds
      'defaultColumns': 10,
      'defaultRows': 20,
      'showGridLines': true,
      'enableFormulas': true,
      'touchOptimized': true,
      'theme': 'system',
    };
  }

  // Utility methods
  String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  // Statistics
  Future<Map<String, dynamic>> getStatistics() async {
    final tools = await getAllTools();
    final now = DateTime.now();
    
    return {
      'totalTools': tools.length,
      'toolsCreatedThisWeek': tools.where((tool) {
        final weekAgo = now.subtract(const Duration(days: 7));
        return tool.createdAt.isAfter(weekAgo);
      }).length,
      'toolsModifiedToday': tools.where((tool) {
        final today = DateTime(now.year, now.month, now.day);
        return tool.lastModified.isAfter(today);
      }).length,
      'averageComponentsPerTool': tools.isEmpty 
          ? 0.0 
          : tools.map((t) => t.uiComponents.length).reduce((a, b) => a + b) / tools.length,
    };
  }

  // Cleanup and maintenance
  Future<bool> clearAllData() async {
    try {
      await initialize();
      await _prefs?.remove(_toolsKey);
      await _prefs?.remove(_settingsKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<int> getDataSize() async {
    await initialize();
    final toolsJson = _prefs?.getStringList(_toolsKey) ?? [];
    final settingsJson = _prefs?.getString(_settingsKey) ?? '';
    
    int totalSize = 0;
    for (final json in toolsJson) {
      totalSize += json.length;
    }
    totalSize += settingsJson.length;
    
    return totalSize; // Size in characters (approximate bytes)
  }
}
