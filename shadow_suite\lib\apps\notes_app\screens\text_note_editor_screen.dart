import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/note_models.dart';
import '../services/notes_service.dart';

/// Text Note Editor with rich formatting and color support
class TextNoteEditorScreen extends ConsumerStatefulWidget {
  final Note? note;
  final bool isNewNote;

  const TextNoteEditorScreen({
    super.key,
    this.note,
    this.isNewNote = false,
  });

  @override
  ConsumerState<TextNoteEditorScreen> createState() => _TextNoteEditorScreenState();
}

class _TextNoteEditorScreenState extends ConsumerState<TextNoteEditorScreen> {
  late TextEditingController _titleController;
  late TextEditingController _contentController;
  late Color _selectedColor;
  late NotePriority _selectedPriority;
  List<String> _tags = [];
  bool _isPinned = false;
  bool _hasUnsavedChanges = false;

  final List<Color> _availableColors = [
    Colors.white,
    Colors.red.shade100,
    Colors.orange.shade100,
    Colors.yellow.shade100,
    Colors.green.shade100,
    Colors.blue.shade100,
    Colors.purple.shade100,
    Colors.pink.shade100,
    Colors.grey.shade100,
  ];

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.note?.title ?? '');
    _contentController = TextEditingController(text: widget.note?.content ?? '');
    _selectedColor = widget.note?.color ?? Colors.white;
    _selectedPriority = widget.note?.priority ?? NotePriority.medium;
    _tags = List.from(widget.note?.tags ?? []);
    _isPinned = widget.note?.isPinned ?? false;

    _titleController.addListener(_onContentChanged);
    _contentController.addListener(_onContentChanged);
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  void _onContentChanged() {
    if (!_hasUnsavedChanges) {
      setState(() => _hasUnsavedChanges = true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _selectedColor,
      appBar: AppBar(
        title: Text(widget.isNewNote ? 'New Text Note' : 'Edit Note'),
        backgroundColor: _selectedColor,
        foregroundColor: _getContrastColor(_selectedColor),
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(_isPinned ? Icons.push_pin : Icons.push_pin_outlined),
            onPressed: () => setState(() => _isPinned = !_isPinned),
          ),
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _hasUnsavedChanges ? _saveNote : null,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildColorPicker(),
          _buildPrioritySelector(),
          Expanded(child: _buildNoteEditor()),
          _buildTagsSection(),
        ],
      ),
    );
  }

  Widget _buildColorPicker() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          const Text('Color: ', style: TextStyle(fontWeight: FontWeight.bold)),
          Expanded(
            child: SizedBox(
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _availableColors.length,
                itemBuilder: (context, index) {
                  final color = _availableColors[index];
                  final isSelected = color == _selectedColor;
                  return GestureDetector(
                    onTap: () => setState(() => _selectedColor = color),
                    child: Container(
                      width: 40,
                      height: 40,
                      margin: const EdgeInsets.only(right: 8),
                      decoration: BoxDecoration(
                        color: color,
                        border: Border.all(
                          color: isSelected ? Colors.black : Colors.grey,
                          width: isSelected ? 3 : 1,
                        ),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: isSelected
                          ? const Icon(Icons.check, color: Colors.black)
                          : null,
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrioritySelector() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          const Text('Priority: ', style: TextStyle(fontWeight: FontWeight.bold)),
          DropdownButton<NotePriority>(
            value: _selectedPriority,
            onChanged: (priority) {
              if (priority != null) {
                setState(() => _selectedPriority = priority);
                _onContentChanged();
              }
            },
            items: NotePriority.values.map((priority) {
              return DropdownMenuItem(
                value: priority,
                child: Row(
                  children: [
                    Icon(
                      _getPriorityIcon(priority),
                      color: _getPriorityColor(priority),
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(priority.name.toUpperCase()),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildNoteEditor() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          TextField(
            controller: _titleController,
            style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            decoration: const InputDecoration(
              hintText: 'Note title...',
              border: InputBorder.none,
            ),
            maxLines: null,
          ),
          const Divider(),
          Expanded(
            child: TextField(
              controller: _contentController,
              style: const TextStyle(fontSize: 16, height: 1.5),
              decoration: const InputDecoration(
                hintText: 'Start writing your note...',
                border: InputBorder.none,
              ),
              maxLines: null,
              expands: true,
              textAlignVertical: TextAlignVertical.top,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTagsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text('Tags: ', style: TextStyle(fontWeight: FontWeight.bold)),
              IconButton(
                icon: const Icon(Icons.add),
                onPressed: _showAddTagDialog,
                iconSize: 20,
              ),
            ],
          ),
          if (_tags.isNotEmpty)
            Wrap(
              spacing: 8,
              children: _tags.map((tag) {
                return Chip(
                  label: Text(tag),
                  onDeleted: () {
                    setState(() => _tags.remove(tag));
                    _onContentChanged();
                  },
                  backgroundColor: _selectedColor.withValues(alpha: 0.3),
                );
              }).toList(),
            ),
        ],
      ),
    );
  }

  IconData _getPriorityIcon(NotePriority priority) {
    switch (priority) {
      case NotePriority.low:
        return Icons.keyboard_arrow_down;
      case NotePriority.medium:
        return Icons.remove;
      case NotePriority.high:
        return Icons.keyboard_arrow_up;
    }
  }

  Color _getPriorityColor(NotePriority priority) {
    switch (priority) {
      case NotePriority.low:
        return Colors.green;
      case NotePriority.medium:
        return Colors.orange;
      case NotePriority.high:
        return Colors.red;
    }
  }

  Color _getContrastColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  void _showAddTagDialog() {
    String newTag = '';
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Tag'),
        content: TextField(
          onChanged: (value) => newTag = value,
          decoration: const InputDecoration(
            hintText: 'Enter tag name',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (newTag.isNotEmpty && !_tags.contains(newTag)) {
                setState(() => _tags.add(newTag));
                _onContentChanged();
              }
              Navigator.pop(context);
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  Future<void> _saveNote() async {
    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a title')),
      );
      return;
    }

    try {
      final notesService = ref.read(notesServiceProvider);
      final now = DateTime.now();

      final note = Note(
        id: widget.note?.id ?? now.millisecondsSinceEpoch.toString(),
        title: _titleController.text.trim(),
        content: _contentController.text,
        type: NoteType.text,
        priority: _selectedPriority,
        color: _selectedColor,
        tags: _tags,
        createdAt: widget.note?.createdAt ?? now,
        updatedAt: now,
        isPinned: _isPinned,
        isArchived: widget.note?.isArchived ?? false,
        isFavorite: widget.note?.isFavorite ?? false,
      );

      if (widget.isNewNote) {
        await notesService.addNote(note);
      } else {
        await notesService.updateNote(note);
      }

      setState(() => _hasUnsavedChanges = false);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Note saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving note: $e')),
        );
      }
    }
  }
}
