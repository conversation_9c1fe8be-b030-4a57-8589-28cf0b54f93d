import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/media_models.dart';
import '../services/shadow_player_providers.dart';
import '../widgets/media_search_bar.dart';
import '../widgets/music_filter_panel.dart';

class MusicTabScreen extends ConsumerStatefulWidget {
  const MusicTabScreen({super.key});

  @override
  ConsumerState<MusicTabScreen> createState() => _MusicTabScreenState();
}

class _MusicTabScreenState extends ConsumerState<MusicTabScreen>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);

    final audios = ref.watch(filteredAudioFilesProvider);
    final viewMode = ref.watch(musicViewModeProvider);
    final isScanning = ref.watch(mediaScanningProvider);
    final searchQuery = ref.watch(musicSearchQueryProvider);

    return Column(
      children: [
        // Search and Filter Bar
        Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: Color(0xFFF8F9FA),
            border: Border(
              bottom: BorderSide(color: Color(0xFFE9ECEF), width: 1),
            ),
          ),
          child: Column(
            children: [
              // Search Bar
              MediaSearchBar(
                hintText: 'Search music...',
                searchQuery: searchQuery,
                onSearchChanged: (query) {
                  ref.read(musicSearchQueryProvider.notifier).state = query;
                },
                onClearSearch: () {
                  ref.read(musicSearchQueryProvider.notifier).state = '';
                },
              ),
              const SizedBox(height: 12),

              // View Mode and Sort Controls
              Row(
                children: [
                  // View Mode Selector
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: const Color(0xFFE9ECEF)),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildViewModeButton(
                          icon: Icons.list,
                          mode: ViewMode.list,
                          currentMode: viewMode,
                          onTap: () =>
                              ref.read(musicViewModeProvider.notifier).state =
                                  ViewMode.list,
                        ),
                        _buildViewModeButton(
                          icon: Icons.grid_view,
                          mode: ViewMode.grid,
                          currentMode: viewMode,
                          onTap: () =>
                              ref.read(musicViewModeProvider.notifier).state =
                                  ViewMode.grid,
                        ),
                        _buildViewModeButton(
                          icon: Icons.folder,
                          mode: ViewMode.folder,
                          currentMode: viewMode,
                          onTap: () =>
                              ref.read(musicViewModeProvider.notifier).state =
                                  ViewMode.folder,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Sort Selector
                  Expanded(child: _buildSortSelector()),
                  const SizedBox(width: 12),

                  // Filter Button
                  IconButton(
                    onPressed: () => _showFilterPanel(context),
                    icon: const Icon(
                      Icons.filter_list,
                      color: Color(0xFF3498DB),
                    ),
                    tooltip: 'Filter Music',
                  ),
                ],
              ),
            ],
          ),
        ),

        // Music Count and Status
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: const BoxDecoration(
            color: Color(0xFFF1F2F6),
            border: Border(
              bottom: BorderSide(color: Color(0xFFE9ECEF), width: 1),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.library_music,
                color: const Color(0xFF3498DB),
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                '${audios.length} songs',
                style: const TextStyle(
                  color: Color(0xFF2C3E50),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (searchQuery.isNotEmpty) ...[
                const SizedBox(width: 8),
                Text(
                  '(filtered)',
                  style: const TextStyle(
                    color: Color(0xFF7F8C8D),
                    fontSize: 12,
                  ),
                ),
              ],
              const Spacer(),
              if (isScanning)
                const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: 12,
                      height: 12,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Color(0xFFE67E22),
                        ),
                      ),
                    ),
                    SizedBox(width: 8),
                    Text(
                      'Scanning...',
                      style: TextStyle(
                        color: Color(0xFFE67E22),
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),

        // Music Content
        Expanded(child: _buildMusicContent(audios, viewMode, isScanning)),
      ],
    );
  }

  Widget _buildMusicContent(
    List<MediaFile> audios,
    ViewMode viewMode,
    bool isScanning,
  ) {
    if (isScanning && audios.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF3498DB)),
            ),
            SizedBox(height: 16),
            Text(
              'Scanning for music...',
              style: TextStyle(color: Color(0xFF7F8C8D), fontSize: 16),
            ),
          ],
        ),
      );
    }

    if (audios.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.library_music_outlined,
              size: 64,
              color: const Color(0xFFBDC3C7),
            ),
            const SizedBox(height: 16),
            const Text(
              'No music found',
              style: TextStyle(
                color: Color(0xFF7F8C8D),
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Try refreshing or check your scan locations',
              style: TextStyle(color: Color(0xFF95A5A6), fontSize: 14),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _refreshMusic(),
              icon: const Icon(Icons.refresh),
              label: const Text('Refresh Library'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF3498DB),
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    // For now, show a simple list view
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: audios.length,
      itemBuilder: (context, index) {
        final audio = audios[index];
        return _buildMusicListItem(audio);
      },
    );
  }

  Widget _buildMusicListItem(MediaFile audio) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            color: const Color(0xFF3498DB),
          ),
          child: const Icon(Icons.music_note, color: Colors.white, size: 24),
        ),
        title: Text(
          audio.metadata.title?.isNotEmpty == true
              ? audio.metadata.title!
              : audio.displayName,
          style: const TextStyle(
            color: Color(0xFF2C3E50),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(
          '${audio.metadata.artist?.isNotEmpty == true ? audio.metadata.artist! : 'Unknown Artist'} • ${audio.metadata.album?.isNotEmpty == true ? audio.metadata.album! : 'Unknown Album'}',
          style: const TextStyle(color: Color(0xFF7F8C8D), fontSize: 12),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (audio.duration != null)
              Text(
                _formatDuration(audio.duration!),
                style: const TextStyle(color: Color(0xFF95A5A6), fontSize: 12),
              ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: () => _playAudio(audio),
              icon: const Icon(
                Icons.play_arrow,
                color: Color(0xFF3498DB),
                size: 20,
              ),
            ),
          ],
        ),
        onTap: () => _playAudio(audio),
      ),
    );
  }

  Widget _buildViewModeButton({
    required IconData icon,
    required ViewMode mode,
    required ViewMode currentMode,
    required VoidCallback onTap,
  }) {
    final isSelected = mode == currentMode;

    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF3498DB) : Colors.transparent,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Icon(
          icon,
          color: isSelected ? Colors.white : const Color(0xFF7F8C8D),
          size: 20,
        ),
      ),
    );
  }

  Widget _buildSortSelector() {
    final sortBy = ref.watch(musicSortByProvider);
    final sortOrder = ref.watch(musicSortOrderProvider);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE9ECEF)),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<SortBy>(
          value: sortBy,
          isDense: true,
          icon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(width: 4),
              Icon(
                sortOrder == SortOrder.ascending
                    ? Icons.arrow_upward
                    : Icons.arrow_downward,
                size: 16,
                color: const Color(0xFF7F8C8D),
              ),
            ],
          ),
          style: const TextStyle(color: Color(0xFF2C3E50), fontSize: 14),
          onChanged: (SortBy? newValue) {
            if (newValue != null) {
              if (newValue == sortBy) {
                // Toggle sort order
                final newOrder = sortOrder == SortOrder.ascending
                    ? SortOrder.descending
                    : SortOrder.ascending;
                ref.read(musicSortOrderProvider.notifier).state = newOrder;
              } else {
                ref.read(musicSortByProvider.notifier).state = newValue;
              }
            }
          },
          items: const [
            DropdownMenuItem(value: SortBy.name, child: Text('Name')),
            DropdownMenuItem(value: SortBy.artist, child: Text('Artist')),
            DropdownMenuItem(value: SortBy.album, child: Text('Album')),
            DropdownMenuItem(
              value: SortBy.dateAdded,
              child: Text('Date Added'),
            ),
            DropdownMenuItem(value: SortBy.duration, child: Text('Duration')),
          ],
        ),
      ),
    );
  }

  void _showFilterPanel(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        builder: (context, scrollController) =>
            MusicFilterPanel(scrollController: scrollController),
      ),
    );
  }

  void _refreshMusic() {
    ref.read(mediaLibraryServiceProvider).scanMediaFiles();
  }

  void _playAudio(MediaFile audio) {
    ref.read(mediaPlayerServiceProvider).playMedia(audio);
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }
}
