import 'package:uuid/uuid.dart';
import 'spreadsheet.dart';
import 'ui_component.dart';

enum ToolType {
  calculator,
  converter,
  form,
  dashboard,
  analyzer,
  custom,
}

enum ToolCategory {
  finance,
  engineering,
  education,
  business,
  personal,
  utilities,
  other,
}

enum ToolStatus {
  draft,
  published,
  archived,
}

class ToolTemplate {
  final String id;
  final String name;
  final String description;
  final ToolType type;
  final ToolCategory category;
  final String thumbnailUrl;
  final List<UIComponent> components;
  final Spreadsheet? spreadsheet;
  final Map<String, dynamic> configuration;
  final List<String> tags;
  final bool isPremium;

  ToolTemplate({
    String? id,
    required this.name,
    required this.description,
    required this.type,
    required this.category,
    this.thumbnailUrl = '',
    this.components = const [],
    this.spreadsheet,
    this.configuration = const {},
    this.tags = const [],
    this.isPremium = false,
  }) : id = id ?? const Uuid().v4();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'category': category.name,
      'thumbnailUrl': thumbnailUrl,
      'components': components.map((e) => e.toMap()).toList(),
      'spreadsheet': spreadsheet?.toMap(),
      'configuration': configuration,
      'tags': tags,
      'isPremium': isPremium,
    };
  }

  factory ToolTemplate.fromMap(Map<String, dynamic> map) {
    return ToolTemplate(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      type: ToolType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => ToolType.custom,
      ),
      category: ToolCategory.values.firstWhere(
        (e) => e.name == map['category'],
        orElse: () => ToolCategory.other,
      ),
      thumbnailUrl: map['thumbnailUrl'] ?? '',
      components: (map['components'] as List<dynamic>? ?? [])
          .map((e) => UIComponent.fromMap(e))
          .toList(),
      spreadsheet: map['spreadsheet'] != null 
          ? Spreadsheet.fromMap(map['spreadsheet']) 
          : null,
      configuration: Map<String, dynamic>.from(map['configuration'] ?? {}),
      tags: List<String>.from(map['tags'] ?? []),
      isPremium: map['isPremium'] ?? false,
    );
  }
}

class Tool {
  final String id;
  final String name;
  final String description;
  final ToolType type;
  final ToolCategory category;
  final ToolStatus status;
  final String thumbnailUrl;
  final List<UIComponent> components;
  final Spreadsheet? spreadsheet;
  final Map<String, dynamic> configuration;
  final List<String> tags;
  final String? templateId;
  final String creatorId;
  final DateTime createdAt;
  final DateTime lastModified;
  final int version;
  final bool isPublic;
  final bool requiresAuth;
  final Map<String, dynamic> permissions;
  final Map<String, dynamic> analytics;

  Tool({
    String? id,
    required this.name,
    required this.description,
    required this.type,
    required this.category,
    this.status = ToolStatus.draft,
    this.thumbnailUrl = '',
    this.components = const [],
    this.spreadsheet,
    this.configuration = const {},
    this.tags = const [],
    this.templateId,
    required this.creatorId,
    DateTime? createdAt,
    DateTime? lastModified,
    this.version = 1,
    this.isPublic = false,
    this.requiresAuth = false,
    this.permissions = const {},
    this.analytics = const {},
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       lastModified = lastModified ?? DateTime.now();

  Tool copyWith({
    String? name,
    String? description,
    ToolType? type,
    ToolCategory? category,
    ToolStatus? status,
    String? thumbnailUrl,
    List<UIComponent>? components,
    Spreadsheet? spreadsheet,
    Map<String, dynamic>? configuration,
    List<String>? tags,
    String? templateId,
    DateTime? lastModified,
    int? version,
    bool? isPublic,
    bool? requiresAuth,
    Map<String, dynamic>? permissions,
    Map<String, dynamic>? analytics,
  }) {
    return Tool(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      category: category ?? this.category,
      status: status ?? this.status,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      components: components ?? this.components,
      spreadsheet: spreadsheet ?? this.spreadsheet,
      configuration: configuration ?? this.configuration,
      tags: tags ?? this.tags,
      templateId: templateId ?? this.templateId,
      creatorId: creatorId,
      createdAt: createdAt,
      lastModified: lastModified ?? DateTime.now(),
      version: version ?? this.version,
      isPublic: isPublic ?? this.isPublic,
      requiresAuth: requiresAuth ?? this.requiresAuth,
      permissions: permissions ?? this.permissions,
      analytics: analytics ?? this.analytics,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'category': category.name,
      'status': status.name,
      'thumbnailUrl': thumbnailUrl,
      'components': components.map((e) => e.toMap()).toList(),
      'spreadsheet': spreadsheet?.toMap(),
      'configuration': configuration,
      'tags': tags,
      'templateId': templateId,
      'creatorId': creatorId,
      'createdAt': createdAt.toIso8601String(),
      'lastModified': lastModified.toIso8601String(),
      'version': version,
      'isPublic': isPublic,
      'requiresAuth': requiresAuth,
      'permissions': permissions,
      'analytics': analytics,
    };
  }

  factory Tool.fromMap(Map<String, dynamic> map) {
    return Tool(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      type: ToolType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => ToolType.custom,
      ),
      category: ToolCategory.values.firstWhere(
        (e) => e.name == map['category'],
        orElse: () => ToolCategory.other,
      ),
      status: ToolStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => ToolStatus.draft,
      ),
      thumbnailUrl: map['thumbnailUrl'] ?? '',
      components: (map['components'] as List<dynamic>? ?? [])
          .map((e) => UIComponent.fromMap(e))
          .toList(),
      spreadsheet: map['spreadsheet'] != null
          ? Spreadsheet.fromMap(map['spreadsheet'])
          : null,
      configuration: Map<String, dynamic>.from(map['configuration'] ?? {}),
      tags: List<String>.from(map['tags'] ?? []),
      templateId: map['templateId'],
      creatorId: map['creatorId'],
      createdAt: DateTime.parse(map['createdAt']),
      lastModified: DateTime.parse(map['lastModified']),
      version: map['version'] ?? 1,
      isPublic: map['isPublic'] ?? false,
      requiresAuth: map['requiresAuth'] ?? false,
      permissions: Map<String, dynamic>.from(map['permissions'] ?? {}),
      analytics: Map<String, dynamic>.from(map['analytics'] ?? {}),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Tool && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Predefined tool templates
class DefaultToolTemplates {
  static List<ToolTemplate> getDefaultTemplates() {
    return [
      // Loan Calculator Template
      ToolTemplate(
        name: 'Loan Calculator',
        description: 'Calculate loan payments, interest, and amortization schedules',
        type: ToolType.calculator,
        category: ToolCategory.finance,
        thumbnailUrl: 'assets/templates/loan_calculator.png',
        tags: ['finance', 'loan', 'calculator', 'mortgage'],
        components: [
          UIComponent(
            type: ComponentType.numberInput,
            label: 'Loan Amount',
            placeholder: 'Enter loan amount',
            x: 20,
            y: 20,
            dataBinding: const DataBinding(cellAddress: 'B2'),
          ),
          UIComponent(
            type: ComponentType.numberInput,
            label: 'Interest Rate (%)',
            placeholder: 'Enter annual interest rate',
            x: 20,
            y: 80,
            dataBinding: const DataBinding(cellAddress: 'B3'),
          ),
          UIComponent(
            type: ComponentType.numberInput,
            label: 'Loan Term (years)',
            placeholder: 'Enter loan term',
            x: 20,
            y: 140,
            dataBinding: const DataBinding(cellAddress: 'B4'),
          ),
          UIComponent(
            type: ComponentType.label,
            label: 'Monthly Payment:',
            x: 20,
            y: 220,
          ),
          UIComponent(
            type: ComponentType.label,
            label: '\$0.00',
            x: 150,
            y: 220,
            dataBinding: const DataBinding(
              cellAddress: 'B6',
              bindingType: 'output',
            ),
          ),
        ],
      ),
      
      // Unit Converter Template
      ToolTemplate(
        name: 'Unit Converter',
        description: 'Convert between different units of measurement',
        type: ToolType.converter,
        category: ToolCategory.utilities,
        thumbnailUrl: 'assets/templates/unit_converter.png',
        tags: ['converter', 'units', 'measurement'],
        components: [
          UIComponent(
            type: ComponentType.dropdown,
            label: 'From Unit',
            x: 20,
            y: 20,
            properties: const {
              'options': ['meters', 'feet', 'inches', 'kilometers', 'miles']
            },
            dataBinding: const DataBinding(cellAddress: 'B2'),
          ),
          UIComponent(
            type: ComponentType.numberInput,
            label: 'Value',
            placeholder: 'Enter value to convert',
            x: 20,
            y: 80,
            dataBinding: const DataBinding(cellAddress: 'B3'),
          ),
          UIComponent(
            type: ComponentType.dropdown,
            label: 'To Unit',
            x: 20,
            y: 140,
            properties: const {
              'options': ['meters', 'feet', 'inches', 'kilometers', 'miles']
            },
            dataBinding: const DataBinding(cellAddress: 'B4'),
          ),
          UIComponent(
            type: ComponentType.label,
            label: 'Result:',
            x: 20,
            y: 220,
          ),
          UIComponent(
            type: ComponentType.label,
            label: '0',
            x: 80,
            y: 220,
            dataBinding: const DataBinding(
              cellAddress: 'B6',
              bindingType: 'output',
            ),
          ),
        ],
      ),
      
      // BMI Calculator Template
      ToolTemplate(
        name: 'BMI Calculator',
        description: 'Calculate Body Mass Index and health category',
        type: ToolType.calculator,
        category: ToolCategory.personal,
        thumbnailUrl: 'assets/templates/bmi_calculator.png',
        tags: ['health', 'bmi', 'calculator', 'fitness'],
        components: [
          UIComponent(
            type: ComponentType.numberInput,
            label: 'Weight (kg)',
            placeholder: 'Enter your weight',
            x: 20,
            y: 20,
            dataBinding: const DataBinding(cellAddress: 'B2'),
          ),
          UIComponent(
            type: ComponentType.numberInput,
            label: 'Height (cm)',
            placeholder: 'Enter your height',
            x: 20,
            y: 80,
            dataBinding: const DataBinding(cellAddress: 'B3'),
          ),
          UIComponent(
            type: ComponentType.label,
            label: 'BMI:',
            x: 20,
            y: 160,
          ),
          UIComponent(
            type: ComponentType.label,
            label: '0.0',
            x: 60,
            y: 160,
            dataBinding: const DataBinding(
              cellAddress: 'B5',
              bindingType: 'output',
            ),
          ),
          UIComponent(
            type: ComponentType.label,
            label: 'Category:',
            x: 20,
            y: 200,
          ),
          UIComponent(
            type: ComponentType.label,
            label: 'Normal',
            x: 90,
            y: 200,
            dataBinding: const DataBinding(
              cellAddress: 'B6',
              bindingType: 'output',
            ),
          ),
        ],
      ),
    ];
  }
}
