import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Comprehensive font customization service
class FontCustomizationService {
  static SharedPreferences? _prefs;

  // Font families available in the system
  static const Map<String, FontFamily> _availableFonts = {
    'roboto': FontFamily(
      name: '<PERSON><PERSON>',
      displayName: 'Roboto',
      category: FontCategory.sansSerif,
      description: 'Modern and clean sans-serif font',
      isSystemFont: true,
    ),
    'open_sans': FontFamily(
      name: 'Open Sans',
      displayName: 'Open Sans',
      category: FontCategory.sansSerif,
      description: 'Friendly and readable sans-serif font',
      isSystemFont: false,
    ),
    'lato': FontFamily(
      name: 'Lato',
      displayName: 'Lato',
      category: FontCategory.sansSerif,
      description: 'Elegant and professional sans-serif font',
      isSystemFont: false,
    ),
    'source_sans_pro': FontFamily(
      name: 'Source Sans Pro',
      displayName: 'Source Sans Pro',
      category: FontCategory.sansSerif,
      description: 'Adobe\'s clean and versatile sans-serif font',
      isSystemFont: false,
    ),
    'montserrat': FontFamily(
      name: 'Montserrat',
      displayName: 'Montserrat',
      category: FontCategory.sansSerif,
      description: 'Geometric and modern sans-serif font',
      isSystemFont: false,
    ),
    'poppins': FontFamily(
      name: 'Poppins',
      displayName: 'Poppins',
      category: FontCategory.sansSerif,
      description: 'Rounded and friendly sans-serif font',
      isSystemFont: false,
    ),
    'inter': FontFamily(
      name: 'Inter',
      displayName: 'Inter',
      category: FontCategory.sansSerif,
      description: 'Optimized for UI and screen reading',
      isSystemFont: false,
    ),
    'nunito': FontFamily(
      name: 'Nunito',
      displayName: 'Nunito',
      category: FontCategory.sansSerif,
      description: 'Well-balanced and highly readable',
      isSystemFont: false,
    ),
    'merriweather': FontFamily(
      name: 'Merriweather',
      displayName: 'Merriweather',
      category: FontCategory.serif,
      description: 'Elegant serif font for reading',
      isSystemFont: false,
    ),
    'playfair_display': FontFamily(
      name: 'Playfair Display',
      displayName: 'Playfair Display',
      category: FontCategory.serif,
      description: 'Distinctive serif font with high contrast',
      isSystemFont: false,
    ),
    'source_code_pro': FontFamily(
      name: 'Source Code Pro',
      displayName: 'Source Code Pro',
      category: FontCategory.monospace,
      description: 'Monospace font for code and technical content',
      isSystemFont: false,
    ),
    'fira_code': FontFamily(
      name: 'Fira Code',
      displayName: 'Fira Code',
      category: FontCategory.monospace,
      description: 'Monospace font with programming ligatures',
      isSystemFont: false,
    ),
    'amiri': FontFamily(
      name: 'Amiri',
      displayName: 'أميري',
      category: FontCategory.arabic,
      description: 'Classical Arabic typeface',
      isSystemFont: false,
      supportsArabic: true,
    ),
    'noto_sans_arabic': FontFamily(
      name: 'Noto Sans Arabic',
      displayName: 'Noto Sans Arabic',
      category: FontCategory.arabic,
      description: 'Modern Arabic sans-serif font',
      isSystemFont: false,
      supportsArabic: true,
    ),
    'cairo': FontFamily(
      name: 'Cairo',
      displayName: 'Cairo',
      category: FontCategory.arabic,
      description: 'Contemporary Arabic font family',
      isSystemFont: false,
      supportsArabic: true,
    ),
  };

  /// Initialize font service
  static Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// Get all available fonts
  static List<FontFamily> get availableFonts => _availableFonts.values.toList();

  /// Get fonts by category
  static List<FontFamily> getFontsByCategory(FontCategory category) {
    return _availableFonts.values
        .where((font) => font.category == category)
        .toList();
  }

  /// Get current font settings
  static FontSettings getCurrentFontSettings() {
    return FontSettings(
      primaryFont: _prefs?.getString('primary_font') ?? 'roboto',
      secondaryFont: _prefs?.getString('secondary_font') ?? 'roboto',
      arabicFont: _prefs?.getString('arabic_font') ?? 'amiri',
      codeFont: _prefs?.getString('code_font') ?? 'source_code_pro',
      baseFontSize: _prefs?.getDouble('base_font_size') ?? 16.0,
      fontScale: _prefs?.getDouble('font_scale') ?? 1.0,
      lineHeight: _prefs?.getDouble('line_height') ?? 1.4,
      letterSpacing: _prefs?.getDouble('letter_spacing') ?? 0.0,
      enableDynamicType: _prefs?.getBool('enable_dynamic_type') ?? true,
      enableFontSmoothing: _prefs?.getBool('enable_font_smoothing') ?? true,
    );
  }

  /// Save font settings
  static Future<void> saveFontSettings(FontSettings settings) async {
    await _prefs?.setString('primary_font', settings.primaryFont);
    await _prefs?.setString('secondary_font', settings.secondaryFont);
    await _prefs?.setString('arabic_font', settings.arabicFont);
    await _prefs?.setString('code_font', settings.codeFont);
    await _prefs?.setDouble('base_font_size', settings.baseFontSize);
    await _prefs?.setDouble('font_scale', settings.fontScale);
    await _prefs?.setDouble('line_height', settings.lineHeight);
    await _prefs?.setDouble('letter_spacing', settings.letterSpacing);
    await _prefs?.setBool('enable_dynamic_type', settings.enableDynamicType);
    await _prefs?.setBool(
      'enable_font_smoothing',
      settings.enableFontSmoothing,
    );
  }

  /// Create text theme from font settings
  static TextTheme createTextTheme(
    FontSettings settings, {
    bool isDark = false,
  }) {
    final primaryFont = _availableFonts[settings.primaryFont];
    final baseSize = settings.baseFontSize * settings.fontScale;

    return TextTheme(
      displayLarge: TextStyle(
        fontFamily: primaryFont?.name,
        fontSize: baseSize * 3.5,
        fontWeight: FontWeight.w300,
        letterSpacing: settings.letterSpacing,
        height: settings.lineHeight,
      ),
      displayMedium: TextStyle(
        fontFamily: primaryFont?.name,
        fontSize: baseSize * 2.8,
        fontWeight: FontWeight.w400,
        letterSpacing: settings.letterSpacing,
        height: settings.lineHeight,
      ),
      displaySmall: TextStyle(
        fontFamily: primaryFont?.name,
        fontSize: baseSize * 2.2,
        fontWeight: FontWeight.w400,
        letterSpacing: settings.letterSpacing,
        height: settings.lineHeight,
      ),
      headlineLarge: TextStyle(
        fontFamily: primaryFont?.name,
        fontSize: baseSize * 2.0,
        fontWeight: FontWeight.w600,
        letterSpacing: settings.letterSpacing,
        height: settings.lineHeight,
      ),
      headlineMedium: TextStyle(
        fontFamily: primaryFont?.name,
        fontSize: baseSize * 1.75,
        fontWeight: FontWeight.w600,
        letterSpacing: settings.letterSpacing,
        height: settings.lineHeight,
      ),
      headlineSmall: TextStyle(
        fontFamily: primaryFont?.name,
        fontSize: baseSize * 1.5,
        fontWeight: FontWeight.w600,
        letterSpacing: settings.letterSpacing,
        height: settings.lineHeight,
      ),
      titleLarge: TextStyle(
        fontFamily: primaryFont?.name,
        fontSize: baseSize * 1.375,
        fontWeight: FontWeight.w600,
        letterSpacing: settings.letterSpacing,
        height: settings.lineHeight,
      ),
      titleMedium: TextStyle(
        fontFamily: primaryFont?.name,
        fontSize: baseSize * 1.125,
        fontWeight: FontWeight.w500,
        letterSpacing: settings.letterSpacing,
        height: settings.lineHeight,
      ),
      titleSmall: TextStyle(
        fontFamily: primaryFont?.name,
        fontSize: baseSize,
        fontWeight: FontWeight.w500,
        letterSpacing: settings.letterSpacing,
        height: settings.lineHeight,
      ),
      bodyLarge: TextStyle(
        fontFamily: primaryFont?.name,
        fontSize: baseSize,
        fontWeight: FontWeight.w400,
        letterSpacing: settings.letterSpacing,
        height: settings.lineHeight,
      ),
      bodyMedium: TextStyle(
        fontFamily: primaryFont?.name,
        fontSize: baseSize * 0.875,
        fontWeight: FontWeight.w400,
        letterSpacing: settings.letterSpacing,
        height: settings.lineHeight,
      ),
      bodySmall: TextStyle(
        fontFamily: primaryFont?.name,
        fontSize: baseSize * 0.75,
        fontWeight: FontWeight.w400,
        letterSpacing: settings.letterSpacing,
        height: settings.lineHeight,
      ),
      labelLarge: TextStyle(
        fontFamily: primaryFont?.name,
        fontSize: baseSize * 0.875,
        fontWeight: FontWeight.w500,
        letterSpacing: settings.letterSpacing,
        height: settings.lineHeight,
      ),
      labelMedium: TextStyle(
        fontFamily: primaryFont?.name,
        fontSize: baseSize * 0.75,
        fontWeight: FontWeight.w500,
        letterSpacing: settings.letterSpacing,
        height: settings.lineHeight,
      ),
      labelSmall: TextStyle(
        fontFamily: primaryFont?.name,
        fontSize: baseSize * 0.6875,
        fontWeight: FontWeight.w500,
        letterSpacing: settings.letterSpacing,
        height: settings.lineHeight,
      ),
    );
  }

  /// Get Arabic text style
  static TextStyle getArabicTextStyle(
    FontSettings settings, {
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
  }) {
    final arabicFont = _availableFonts[settings.arabicFont];
    final baseSize = fontSize ?? (settings.baseFontSize * settings.fontScale);

    return TextStyle(
      fontFamily: arabicFont?.name,
      fontSize: baseSize,
      fontWeight: fontWeight ?? FontWeight.w400,
      letterSpacing: settings.letterSpacing,
      height: settings.lineHeight * 1.2, // Arabic needs more line height
      color: color,
    );
  }

  /// Get code text style
  static TextStyle getCodeTextStyle(
    FontSettings settings, {
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
  }) {
    final codeFont = _availableFonts[settings.codeFont];
    final baseSize =
        fontSize ?? (settings.baseFontSize * settings.fontScale * 0.9);

    return TextStyle(
      fontFamily: codeFont?.name,
      fontSize: baseSize,
      fontWeight: fontWeight ?? FontWeight.w400,
      letterSpacing: settings.letterSpacing,
      height: settings.lineHeight,
      color: color,
    );
  }

  /// Reset to default font settings
  static Future<void> resetToDefaults() async {
    final defaultSettings = FontSettings(
      primaryFont: 'roboto',
      secondaryFont: 'roboto',
      arabicFont: 'amiri',
      codeFont: 'source_code_pro',
      baseFontSize: 16.0,
      fontScale: 1.0,
      lineHeight: 1.4,
      letterSpacing: 0.0,
      enableDynamicType: true,
      enableFontSmoothing: true,
    );

    await saveFontSettings(defaultSettings);
  }
}

/// Font family model
class FontFamily {
  final String name;
  final String displayName;
  final FontCategory category;
  final String description;
  final bool isSystemFont;
  final bool supportsArabic;

  const FontFamily({
    required this.name,
    required this.displayName,
    required this.category,
    required this.description,
    this.isSystemFont = false,
    this.supportsArabic = false,
  });
}

/// Font settings model
class FontSettings {
  final String primaryFont;
  final String secondaryFont;
  final String arabicFont;
  final String codeFont;
  final double baseFontSize;
  final double fontScale;
  final double lineHeight;
  final double letterSpacing;
  final bool enableDynamicType;
  final bool enableFontSmoothing;

  const FontSettings({
    required this.primaryFont,
    required this.secondaryFont,
    required this.arabicFont,
    required this.codeFont,
    required this.baseFontSize,
    required this.fontScale,
    required this.lineHeight,
    required this.letterSpacing,
    required this.enableDynamicType,
    required this.enableFontSmoothing,
  });

  FontSettings copyWith({
    String? primaryFont,
    String? secondaryFont,
    String? arabicFont,
    String? codeFont,
    double? baseFontSize,
    double? fontScale,
    double? lineHeight,
    double? letterSpacing,
    bool? enableDynamicType,
    bool? enableFontSmoothing,
  }) {
    return FontSettings(
      primaryFont: primaryFont ?? this.primaryFont,
      secondaryFont: secondaryFont ?? this.secondaryFont,
      arabicFont: arabicFont ?? this.arabicFont,
      codeFont: codeFont ?? this.codeFont,
      baseFontSize: baseFontSize ?? this.baseFontSize,
      fontScale: fontScale ?? this.fontScale,
      lineHeight: lineHeight ?? this.lineHeight,
      letterSpacing: letterSpacing ?? this.letterSpacing,
      enableDynamicType: enableDynamicType ?? this.enableDynamicType,
      enableFontSmoothing: enableFontSmoothing ?? this.enableFontSmoothing,
    );
  }
}

/// Font category enumeration
enum FontCategory { sansSerif, serif, monospace, arabic, decorative }

/// Font settings provider
final fontSettingsProvider =
    StateNotifierProvider<FontSettingsNotifier, FontSettings>((ref) {
      return FontSettingsNotifier();
    });

class FontSettingsNotifier extends StateNotifier<FontSettings> {
  FontSettingsNotifier()
    : super(
        const FontSettings(
          primaryFont: 'roboto',
          secondaryFont: 'roboto',
          arabicFont: 'amiri',
          codeFont: 'source_code_pro',
          baseFontSize: 16.0,
          fontScale: 1.0,
          lineHeight: 1.4,
          letterSpacing: 0.0,
          enableDynamicType: true,
          enableFontSmoothing: true,
        ),
      ) {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    await FontCustomizationService.initialize();
    state = FontCustomizationService.getCurrentFontSettings();
  }

  Future<void> updateSettings(FontSettings settings) async {
    state = settings;
    await FontCustomizationService.saveFontSettings(settings);
  }

  Future<void> resetToDefaults() async {
    await FontCustomizationService.resetToDefaults();
    state = FontCustomizationService.getCurrentFontSettings();
  }

  void updatePrimaryFont(String fontName) {
    final newSettings = state.copyWith(primaryFont: fontName);
    updateSettings(newSettings);
  }

  void updateArabicFont(String fontName) {
    final newSettings = state.copyWith(arabicFont: fontName);
    updateSettings(newSettings);
  }

  void updateBaseFontSize(double size) {
    final newSettings = state.copyWith(baseFontSize: size);
    updateSettings(newSettings);
  }

  void updateFontScale(double scale) {
    final newSettings = state.copyWith(fontScale: scale);
    updateSettings(newSettings);
  }

  void updateLineHeight(double height) {
    final newSettings = state.copyWith(lineHeight: height);
    updateSettings(newSettings);
  }

  void updateLetterSpacing(double spacing) {
    final newSettings = state.copyWith(letterSpacing: spacing);
    updateSettings(newSettings);
  }
}
