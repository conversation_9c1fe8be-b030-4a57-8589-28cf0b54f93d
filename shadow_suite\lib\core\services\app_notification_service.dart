import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'android_platform_service.dart';

// App Notification Service Provider
final appNotificationServiceProvider = Provider<AppNotificationService>((ref) {
  final notificationService = ref.read(notificationServiceProvider);
  return AppNotificationService(notificationService);
});

// Notification Settings Provider
final notificationSettingsProvider =
    StateNotifierProvider<NotificationSettingsNotifier, NotificationSettings>((
      ref,
    ) {
      return NotificationSettingsNotifier();
    });

// App Notification Service
class AppNotificationService {
  final NotificationService _notificationService;

  AppNotificationService(this._notificationService);

  // Initialize notification channels
  Future<void> initialize() async {
    await _createNotificationChannels();
  }

  Future<void> _createNotificationChannels() async {
    // Money Manager notifications
    await _notificationService.createNotificationChannel(
      channelId: 'money_manager',
      channelName: 'Money Manager',
      description: 'Budget alerts, bill reminders, and financial notifications',
      priority: NotificationPriority.normal,
    );

    // Islamic App notifications
    await _notificationService.createNotificationChannel(
      channelId: 'islamic_app',
      channelName: 'Islamic App',
      description: 'Prayer time notifications and daily dhikr reminders',
      priority: NotificationPriority.high,
    );

    // Memo Suite notifications
    await _notificationService.createNotificationChannel(
      channelId: 'memo_suite',
      channelName: 'Memo Suite',
      description: 'Task deadlines and note reminders',
      priority: NotificationPriority.normal,
    );

    // Excel to App notifications
    await _notificationService.createNotificationChannel(
      channelId: 'excel_to_app',
      channelName: 'Excel to App',
      description: 'Calculation updates and data refresh alerts',
      priority: NotificationPriority.low,
    );

    // File Manager notifications
    await _notificationService.createNotificationChannel(
      channelId: 'file_manager',
      channelName: 'File Manager',
      description: 'File operation completion and storage warnings',
      priority: NotificationPriority.normal,
    );
  }

  // Money Manager Notifications
  Future<void> showBudgetAlert({
    required String categoryName,
    required double spentAmount,
    required double budgetLimit,
    required double percentage,
  }) async {
    final title = 'Budget Alert: $categoryName';
    final body =
        'You\'ve spent \$${spentAmount.toStringAsFixed(2)} (${percentage.toStringAsFixed(1)}%) of your \$${budgetLimit.toStringAsFixed(2)} budget.';

    await _notificationService.showNotification(
      title: title,
      body: body,
      channelId: 'money_manager',
      channelName: 'Money Manager',
      priority: percentage > 90
          ? NotificationPriority.high
          : NotificationPriority.normal,
      data: {
        'type': 'budget_alert',
        'category': categoryName,
        'percentage': percentage.toString(),
      },
    );
  }

  Future<void> scheduleBillReminder({
    required String billName,
    required double amount,
    required DateTime dueDate,
  }) async {
    final title = 'Bill Reminder: $billName';
    final body =
        '\$${amount.toStringAsFixed(2)} due on ${_formatDate(dueDate)}';

    // Schedule notification 1 day before due date
    final reminderTime = dueDate.subtract(const Duration(days: 1));

    await _notificationService.scheduleNotification(
      title: title,
      body: body,
      scheduledTime: reminderTime,
      channelId: 'money_manager',
      channelName: 'Money Manager',
      priority: NotificationPriority.high,
      data: {
        'type': 'bill_reminder',
        'bill_name': billName,
        'amount': amount.toString(),
        'due_date': dueDate.toIso8601String(),
      },
    );
  }

  // Islamic App Notifications
  Future<void> showPrayerTimeNotification({
    required String prayerName,
    required DateTime prayerTime,
    bool isReminder = false,
  }) async {
    final title = isReminder
        ? 'Prayer Reminder: $prayerName'
        : 'Prayer Time: $prayerName';
    final body = isReminder
        ? '$prayerName prayer time is in 15 minutes (${_formatTime(prayerTime)})'
        : 'It\'s time for $prayerName prayer (${_formatTime(prayerTime)})';

    await _notificationService.showNotification(
      title: title,
      body: body,
      channelId: 'islamic_app',
      channelName: 'Islamic App',
      priority: NotificationPriority.high,
      data: {
        'type': 'prayer_time',
        'prayer_name': prayerName,
        'prayer_time': prayerTime.toIso8601String(),
        'is_reminder': isReminder.toString(),
      },
    );
  }

  Future<void> scheduleDailyDhikrReminder({
    required String dhikrText,
    required DateTime reminderTime,
  }) async {
    final title = 'Daily Dhikr Reminder';
    final body = dhikrText;

    await _notificationService.scheduleNotification(
      title: title,
      body: body,
      scheduledTime: reminderTime,
      channelId: 'islamic_app',
      channelName: 'Islamic App',
      priority: NotificationPriority.normal,
      data: {'type': 'dhikr_reminder', 'dhikr_text': dhikrText},
    );
  }

  // Memo Suite Notifications
  Future<void> showTaskDeadlineNotification({
    required String taskTitle,
    required DateTime deadline,
    required String priority,
  }) async {
    final title = 'Task Deadline: $taskTitle';
    final body = 'Due ${_formatDateTime(deadline)} - Priority: $priority';

    await _notificationService.showNotification(
      title: title,
      body: body,
      channelId: 'memo_suite',
      channelName: 'Memo Suite',
      priority: priority.toLowerCase() == 'high'
          ? NotificationPriority.high
          : NotificationPriority.normal,
      data: {
        'type': 'task_deadline',
        'task_title': taskTitle,
        'deadline': deadline.toIso8601String(),
        'priority': priority,
      },
    );
  }

  Future<void> scheduleNoteReminder({
    required String noteTitle,
    required String noteContent,
    required DateTime reminderTime,
  }) async {
    final title = 'Note Reminder: $noteTitle';
    final body = noteContent.length > 100
        ? '${noteContent.substring(0, 100)}...'
        : noteContent;

    await _notificationService.scheduleNotification(
      title: title,
      body: body,
      scheduledTime: reminderTime,
      channelId: 'memo_suite',
      channelName: 'Memo Suite',
      priority: NotificationPriority.normal,
      data: {
        'type': 'note_reminder',
        'note_title': noteTitle,
        'note_content': noteContent,
      },
    );
  }

  // Excel to App Notifications
  Future<void> showCalculationUpdateNotification({
    required String toolName,
    required String cellAddress,
    required String newValue,
  }) async {
    final title = 'Calculation Updated: $toolName';
    final body = 'Cell $cellAddress updated to: $newValue';

    await _notificationService.showNotification(
      title: title,
      body: body,
      channelId: 'excel_to_app',
      channelName: 'Excel to App',
      priority: NotificationPriority.low,
      data: {
        'type': 'calculation_update',
        'tool_name': toolName,
        'cell_address': cellAddress,
        'new_value': newValue,
      },
    );
  }

  Future<void> showDataRefreshAlert({
    required String toolName,
    required int updatedCells,
  }) async {
    final title = 'Data Refreshed: $toolName';
    final body = '$updatedCells cells updated with new data';

    await _notificationService.showNotification(
      title: title,
      body: body,
      channelId: 'excel_to_app',
      channelName: 'Excel to App',
      priority: NotificationPriority.normal,
      data: {
        'type': 'data_refresh',
        'tool_name': toolName,
        'updated_cells': updatedCells.toString(),
      },
    );
  }

  // File Manager Notifications
  Future<void> showFileOperationComplete({
    required String operation,
    required int fileCount,
    required bool success,
  }) async {
    final title = success ? 'Operation Complete' : 'Operation Failed';
    final body = success
        ? '$operation completed successfully for $fileCount files'
        : '$operation failed for $fileCount files';

    await _notificationService.showNotification(
      title: title,
      body: body,
      channelId: 'file_manager',
      channelName: 'File Manager',
      priority: success
          ? NotificationPriority.normal
          : NotificationPriority.high,
      data: {
        'type': 'file_operation',
        'operation': operation,
        'file_count': fileCount.toString(),
        'success': success.toString(),
      },
    );
  }

  Future<void> showStorageWarning({
    required double usedPercentage,
    required String availableSpace,
  }) async {
    final title = 'Storage Warning';
    final body =
        'Storage ${usedPercentage.toStringAsFixed(1)}% full. $availableSpace remaining.';

    await _notificationService.showNotification(
      title: title,
      body: body,
      channelId: 'file_manager',
      channelName: 'File Manager',
      priority: usedPercentage > 90
          ? NotificationPriority.high
          : NotificationPriority.normal,
      data: {
        'type': 'storage_warning',
        'used_percentage': usedPercentage.toString(),
        'available_space': availableSpace,
      },
    );
  }

  // Utility methods
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatTime(DateTime time) {
    final hour = time.hour > 12 ? time.hour - 12 : time.hour;
    final period = time.hour >= 12 ? 'PM' : 'AM';
    return '${hour == 0 ? 12 : hour}:${time.minute.toString().padLeft(2, '0')} $period';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${_formatDate(dateTime)} at ${_formatTime(dateTime)}';
  }
}

// Notification Settings
class NotificationSettings {
  final bool enableMoneyManagerNotifications;
  final bool enableIslamicAppNotifications;
  final bool enableMemoSuiteNotifications;
  final bool enableExcelToAppNotifications;
  final bool enableFileManagerNotifications;
  final bool enablePrayerTimeNotifications;
  final bool enableBudgetAlerts;
  final bool enableTaskReminders;
  final bool enableBillReminders;
  final bool enableDhikrReminders;

  const NotificationSettings({
    this.enableMoneyManagerNotifications = true,
    this.enableIslamicAppNotifications = true,
    this.enableMemoSuiteNotifications = true,
    this.enableExcelToAppNotifications = false,
    this.enableFileManagerNotifications = true,
    this.enablePrayerTimeNotifications = true,
    this.enableBudgetAlerts = true,
    this.enableTaskReminders = true,
    this.enableBillReminders = true,
    this.enableDhikrReminders = true,
  });

  NotificationSettings copyWith({
    bool? enableMoneyManagerNotifications,
    bool? enableIslamicAppNotifications,
    bool? enableMemoSuiteNotifications,
    bool? enableExcelToAppNotifications,
    bool? enableFileManagerNotifications,
    bool? enablePrayerTimeNotifications,
    bool? enableBudgetAlerts,
    bool? enableTaskReminders,
    bool? enableBillReminders,
    bool? enableDhikrReminders,
  }) {
    return NotificationSettings(
      enableMoneyManagerNotifications:
          enableMoneyManagerNotifications ??
          this.enableMoneyManagerNotifications,
      enableIslamicAppNotifications:
          enableIslamicAppNotifications ?? this.enableIslamicAppNotifications,
      enableMemoSuiteNotifications:
          enableMemoSuiteNotifications ?? this.enableMemoSuiteNotifications,
      enableExcelToAppNotifications:
          enableExcelToAppNotifications ?? this.enableExcelToAppNotifications,
      enableFileManagerNotifications:
          enableFileManagerNotifications ?? this.enableFileManagerNotifications,
      enablePrayerTimeNotifications:
          enablePrayerTimeNotifications ?? this.enablePrayerTimeNotifications,
      enableBudgetAlerts: enableBudgetAlerts ?? this.enableBudgetAlerts,
      enableTaskReminders: enableTaskReminders ?? this.enableTaskReminders,
      enableBillReminders: enableBillReminders ?? this.enableBillReminders,
      enableDhikrReminders: enableDhikrReminders ?? this.enableDhikrReminders,
    );
  }
}

// Notification Settings Notifier
class NotificationSettingsNotifier extends StateNotifier<NotificationSettings> {
  NotificationSettingsNotifier() : super(const NotificationSettings());

  void updateSettings(NotificationSettings newSettings) {
    state = newSettings;
  }

  void toggleMoneyManagerNotifications() {
    state = state.copyWith(
      enableMoneyManagerNotifications: !state.enableMoneyManagerNotifications,
    );
  }

  void toggleIslamicAppNotifications() {
    state = state.copyWith(
      enableIslamicAppNotifications: !state.enableIslamicAppNotifications,
    );
  }

  void toggleMemoSuiteNotifications() {
    state = state.copyWith(
      enableMemoSuiteNotifications: !state.enableMemoSuiteNotifications,
    );
  }

  void toggleExcelToAppNotifications() {
    state = state.copyWith(
      enableExcelToAppNotifications: !state.enableExcelToAppNotifications,
    );
  }

  void toggleFileManagerNotifications() {
    state = state.copyWith(
      enableFileManagerNotifications: !state.enableFileManagerNotifications,
    );
  }
}
