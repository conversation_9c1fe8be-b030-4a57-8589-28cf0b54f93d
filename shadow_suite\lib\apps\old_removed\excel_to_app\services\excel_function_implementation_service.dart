import 'dart:math' as math;

/// Comprehensive Excel function implementation service
class ExcelFunctionImplementationService {
  static bool _isInitialized = false;
  static final Map<String, Function> _functions = {};
  static final Map<String, FunctionMetadata> _functionMetadata = {};

  /// Initialize the Excel function implementation service
  static void initialize() {
    if (_isInitialized) return;

    _registerMathematicalFunctions();
    _registerStatisticalFunctions();
    _registerTextFunctions();
    _registerDateTimeFunctions();
    _registerLogicalFunctions();
    _registerLookupFunctions();
    _registerFinancialFunctions();
    _registerEngineeringFunctions();
    _registerDatabaseFunctions();
    _registerArrayFunctions();

    _isInitialized = true;
  }

  /// Execute Excel function with given name and arguments
  static dynamic executeFunction(String functionName, List<dynamic> args) {
    // return PerformanceOptimizer.measure('execute_function_$functionName', () {
    return (() {
      final function = _functions[functionName.toUpperCase()];
      if (function == null) {
        throw ArgumentError('Function not found: $functionName');
      }

      try {
        return function(args);
      } catch (e) {
        throw ArgumentError('Function execution failed: $functionName - $e');
      }
    });
  }

  /// Get all available functions
  static List<String> get availableFunctions => _functions.keys.toList();

  /// Get function metadata
  static FunctionMetadata? getFunctionMetadata(String functionName) {
    return _functionMetadata[functionName.toUpperCase()];
  }

  /// Get functions by category
  static List<String> getFunctionsByCategory(FunctionCategory category) {
    return _functionMetadata.entries
        .where((entry) => entry.value.category == category)
        .map((entry) => entry.key)
        .toList();
  }

  // Mathematical Functions
  static void _registerMathematicalFunctions() {
    _registerFunction(
      'SUM',
      FunctionCategory.mathematical,
      'Adds all numbers in a range',
      (args) {
        if (args.isEmpty) return 0;
        return _flattenArgs(args).fold<double>(0, (sum, value) {
          final num = _parseNumber(value);
          return sum + (num ?? 0);
        });
      },
    );

    _registerFunction(
      'AVERAGE',
      FunctionCategory.mathematical,
      'Returns the average of numbers',
      (args) {
        if (args.isEmpty) return 0;
        final numbers = _flattenArgs(
          args,
        ).map(_parseNumber).where((n) => n != null).cast<double>().toList();
        if (numbers.isEmpty) return 0;
        return numbers.reduce((a, b) => a + b) / numbers.length;
      },
    );

    _registerFunction(
      'COUNT',
      FunctionCategory.mathematical,
      'Counts the number of cells that contain numbers',
      (args) {
        if (args.isEmpty) return 0;
        return _flattenArgs(
          args,
        ).where((value) => _parseNumber(value) != null).length;
      },
    );

    _registerFunction(
      'MAX',
      FunctionCategory.mathematical,
      'Returns the largest value',
      (args) {
        if (args.isEmpty) return 0;
        final numbers = _flattenArgs(
          args,
        ).map(_parseNumber).where((n) => n != null).cast<double>().toList();
        if (numbers.isEmpty) return 0;
        return numbers.reduce(math.max);
      },
    );

    _registerFunction(
      'MIN',
      FunctionCategory.mathematical,
      'Returns the smallest value',
      (args) {
        if (args.isEmpty) return 0;
        final numbers = _flattenArgs(
          args,
        ).map(_parseNumber).where((n) => n != null).cast<double>().toList();
        if (numbers.isEmpty) return 0;
        return numbers.reduce(math.min);
      },
    );

    _registerFunction(
      'ABS',
      FunctionCategory.mathematical,
      'Returns the absolute value of a number',
      (args) {
        if (args.isEmpty) throw ArgumentError('ABS requires 1 argument');
        final num = _parseNumber(args[0]);
        if (num == null) throw ArgumentError('ABS requires a numeric argument');
        return num.abs();
      },
    );

    _registerFunction(
      'ROUND',
      FunctionCategory.mathematical,
      'Rounds a number to specified digits',
      (args) {
        if (args.length < 1) {
          throw ArgumentError('ROUND requires at least 1 argument');
        }
        final num = _parseNumber(args[0]);
        if (num == null) {
          throw ArgumentError('ROUND requires a numeric argument');
        }
        final digits = args.length > 1
            ? (_parseNumber(args[1])?.toInt() ?? 0)
            : 0;
        final multiplier = math.pow(10, digits);
        return (num * multiplier).round() / multiplier;
      },
    );

    _registerFunction(
      'SQRT',
      FunctionCategory.mathematical,
      'Returns the square root of a number',
      (args) {
        if (args.isEmpty) throw ArgumentError('SQRT requires 1 argument');
        final num = _parseNumber(args[0]);
        if (num == null || num < 0) {
          throw ArgumentError('SQRT requires a non-negative number');
        }
        return math.sqrt(num);
      },
    );

    _registerFunction(
      'POWER',
      FunctionCategory.mathematical,
      'Returns the result of a number raised to a power',
      (args) {
        if (args.length < 2) throw ArgumentError('POWER requires 2 arguments');
        final base = _parseNumber(args[0]);
        final exponent = _parseNumber(args[1]);
        if (base == null || exponent == null) {
          throw ArgumentError('POWER requires numeric arguments');
        }
        return math.pow(base, exponent);
      },
    );

    _registerFunction(
      'MOD',
      FunctionCategory.mathematical,
      'Returns the remainder after division',
      (args) {
        if (args.length < 2) throw ArgumentError('MOD requires 2 arguments');
        final dividend = _parseNumber(args[0]);
        final divisor = _parseNumber(args[1]);
        if (dividend == null || divisor == null || divisor == 0) {
          throw ArgumentError(
            'MOD requires valid numeric arguments and non-zero divisor',
          );
        }
        return dividend % divisor;
      },
    );

    _registerFunction(
      'CEILING',
      FunctionCategory.mathematical,
      'Rounds a number up to the nearest integer',
      (args) {
        if (args.isEmpty) throw ArgumentError('CEILING requires 1 argument');
        final num = _parseNumber(args[0]);
        if (num == null) {
          throw ArgumentError('CEILING requires a numeric argument');
        }
        return num.ceil().toDouble();
      },
    );

    _registerFunction(
      'FLOOR',
      FunctionCategory.mathematical,
      'Rounds a number down to the nearest integer',
      (args) {
        if (args.isEmpty) throw ArgumentError('FLOOR requires 1 argument');
        final num = _parseNumber(args[0]);
        if (num == null) {
          throw ArgumentError('FLOOR requires a numeric argument');
        }
        return num.floor().toDouble();
      },
    );

    _registerFunction(
      'RAND',
      FunctionCategory.mathematical,
      'Returns a random number between 0 and 1',
      (args) {
        return math.Random().nextDouble();
      },
    );

    _registerFunction(
      'RANDBETWEEN',
      FunctionCategory.mathematical,
      'Returns a random integer between two numbers',
      (args) {
        if (args.length < 2) {
          throw ArgumentError('RANDBETWEEN requires 2 arguments');
        }
        final bottom = _parseNumber(args[0])?.toInt();
        final top = _parseNumber(args[1])?.toInt();
        if (bottom == null || top == null) {
          throw ArgumentError('RANDBETWEEN requires integer arguments');
        }
        return math.Random().nextInt(top - bottom + 1) + bottom;
      },
    );

    // Trigonometric functions
    _registerFunction(
      'SIN',
      FunctionCategory.mathematical,
      'Returns the sine of an angle',
      (args) {
        if (args.isEmpty) throw ArgumentError('SIN requires 1 argument');
        final num = _parseNumber(args[0]);
        if (num == null) throw ArgumentError('SIN requires a numeric argument');
        return math.sin(num);
      },
    );

    _registerFunction(
      'COS',
      FunctionCategory.mathematical,
      'Returns the cosine of an angle',
      (args) {
        if (args.isEmpty) throw ArgumentError('COS requires 1 argument');
        final num = _parseNumber(args[0]);
        if (num == null) throw ArgumentError('COS requires a numeric argument');
        return math.cos(num);
      },
    );

    _registerFunction(
      'TAN',
      FunctionCategory.mathematical,
      'Returns the tangent of an angle',
      (args) {
        if (args.isEmpty) throw ArgumentError('TAN requires 1 argument');
        final num = _parseNumber(args[0]);
        if (num == null) throw ArgumentError('TAN requires a numeric argument');
        return math.tan(num);
      },
    );

    _registerFunction(
      'ASIN',
      FunctionCategory.mathematical,
      'Returns the arcsine of a number',
      (args) {
        if (args.isEmpty) throw ArgumentError('ASIN requires 1 argument');
        final num = _parseNumber(args[0]);
        if (num == null || num < -1 || num > 1) {
          throw ArgumentError('ASIN requires a number between -1 and 1');
        }
        return math.asin(num);
      },
    );

    _registerFunction(
      'ACOS',
      FunctionCategory.mathematical,
      'Returns the arccosine of a number',
      (args) {
        if (args.isEmpty) throw ArgumentError('ACOS requires 1 argument');
        final num = _parseNumber(args[0]);
        if (num == null || num < -1 || num > 1) {
          throw ArgumentError('ACOS requires a number between -1 and 1');
        }
        return math.acos(num);
      },
    );

    _registerFunction(
      'ATAN',
      FunctionCategory.mathematical,
      'Returns the arctangent of a number',
      (args) {
        if (args.isEmpty) throw ArgumentError('ATAN requires 1 argument');
        final num = _parseNumber(args[0]);
        if (num == null) {
          throw ArgumentError('ATAN requires a numeric argument');
        }
        return math.atan(num);
      },
    );

    _registerFunction(
      'PI',
      FunctionCategory.mathematical,
      'Returns the value of pi',
      (args) {
        return math.pi;
      },
    );

    _registerFunction(
      'EXP',
      FunctionCategory.mathematical,
      'Returns e raised to the power of a number',
      (args) {
        if (args.isEmpty) throw ArgumentError('EXP requires 1 argument');
        final num = _parseNumber(args[0]);
        if (num == null) throw ArgumentError('EXP requires a numeric argument');
        return math.exp(num);
      },
    );

    _registerFunction(
      'LN',
      FunctionCategory.mathematical,
      'Returns the natural logarithm of a number',
      (args) {
        if (args.isEmpty) throw ArgumentError('LN requires 1 argument');
        final num = _parseNumber(args[0]);
        if (num == null || num <= 0) {
          throw ArgumentError('LN requires a positive number');
        }
        return math.log(num);
      },
    );

    _registerFunction(
      'LOG',
      FunctionCategory.mathematical,
      'Returns the logarithm of a number to a specified base',
      (args) {
        if (args.isEmpty) {
          throw ArgumentError('LOG requires at least 1 argument');
        }
        final num = _parseNumber(args[0]);
        if (num == null || num <= 0) {
          throw ArgumentError('LOG requires a positive number');
        }
        final base = args.length > 1 ? _parseNumber(args[1]) : 10;
        if (base == null || base <= 0 || base == 1) {
          throw ArgumentError('LOG requires a valid base');
        }
        return math.log(num) / math.log(base);
      },
    );

    _registerFunction(
      'LOG10',
      FunctionCategory.mathematical,
      'Returns the base-10 logarithm of a number',
      (args) {
        if (args.isEmpty) throw ArgumentError('LOG10 requires 1 argument');
        final num = _parseNumber(args[0]);
        if (num == null || num <= 0) {
          throw ArgumentError('LOG10 requires a positive number');
        }
        return math.log(num) / math.ln10;
      },
    );

    _registerFunction(
      'DEGREES',
      FunctionCategory.mathematical,
      'Converts radians to degrees',
      (args) {
        if (args.isEmpty) throw ArgumentError('DEGREES requires 1 argument');
        final num = _parseNumber(args[0]);
        if (num == null) {
          throw ArgumentError('DEGREES requires a numeric argument');
        }
        return num * 180 / math.pi;
      },
    );

    _registerFunction(
      'RADIANS',
      FunctionCategory.mathematical,
      'Converts degrees to radians',
      (args) {
        if (args.isEmpty) throw ArgumentError('RADIANS requires 1 argument');
        final num = _parseNumber(args[0]);
        if (num == null) {
          throw ArgumentError('RADIANS requires a numeric argument');
        }
        return num * math.pi / 180;
      },
    );
  }

  // Statistical Functions
  static void _registerStatisticalFunctions() {
    _registerFunction(
      'MEDIAN',
      FunctionCategory.statistical,
      'Returns the median of numbers',
      (args) {
        if (args.isEmpty) return 0;
        final numbers = _flattenArgs(
          args,
        ).map(_parseNumber).where((n) => n != null).cast<double>().toList();
        if (numbers.isEmpty) return 0;
        numbers.sort();
        final middle = numbers.length ~/ 2;
        if (numbers.length % 2 == 0) {
          return (numbers[middle - 1] + numbers[middle]) / 2;
        } else {
          return numbers[middle];
        }
      },
    );

    _registerFunction(
      'MODE',
      FunctionCategory.statistical,
      'Returns the most frequently occurring value',
      (args) {
        if (args.isEmpty) return 0;
        final numbers = _flattenArgs(
          args,
        ).map(_parseNumber).where((n) => n != null).cast<double>().toList();
        if (numbers.isEmpty) return 0;

        final frequency = <double, int>{};
        for (final num in numbers) {
          frequency[num] = (frequency[num] ?? 0) + 1;
        }

        int maxCount = 0;
        double mode = 0;
        for (final entry in frequency.entries) {
          if (entry.value > maxCount) {
            maxCount = entry.value;
            mode = entry.key;
          }
        }

        return mode;
      },
    );

    _registerFunction(
      'STDEV',
      FunctionCategory.statistical,
      'Returns the standard deviation of a sample',
      (args) {
        if (args.isEmpty) return 0;
        final numbers = _flattenArgs(
          args,
        ).map(_parseNumber).where((n) => n != null).cast<double>().toList();
        if (numbers.length < 2) return 0;

        final mean = numbers.reduce((a, b) => a + b) / numbers.length;
        final variance =
            numbers.map((n) => math.pow(n - mean, 2)).reduce((a, b) => a + b) /
            (numbers.length - 1);
        return math.sqrt(variance);
      },
    );

    _registerFunction(
      'VAR',
      FunctionCategory.statistical,
      'Returns the variance of a sample',
      (args) {
        if (args.isEmpty) return 0;
        final numbers = _flattenArgs(
          args,
        ).map(_parseNumber).where((n) => n != null).cast<double>().toList();
        if (numbers.length < 2) return 0;

        final mean = numbers.reduce((a, b) => a + b) / numbers.length;
        return numbers
                .map((n) => math.pow(n - mean, 2))
                .reduce((a, b) => a + b) /
            (numbers.length - 1);
      },
    );

    _registerFunction(
      'COUNTA',
      FunctionCategory.statistical,
      'Counts non-empty cells',
      (args) {
        if (args.isEmpty) return 0;
        return _flattenArgs(
          args,
        ).where((value) => value != null && value.toString().isNotEmpty).length;
      },
    );

    _registerFunction(
      'COUNTBLANK',
      FunctionCategory.statistical,
      'Counts empty cells',
      (args) {
        if (args.isEmpty) return 0;
        return _flattenArgs(
          args,
        ).where((value) => value == null || value.toString().isEmpty).length;
      },
    );
  }

  // Text Functions
  static void _registerTextFunctions() {
    _registerFunction(
      'CONCATENATE',
      FunctionCategory.text,
      'Joins text strings together',
      (args) {
        return args.map((arg) => arg.toString()).join('');
      },
    );

    _registerFunction(
      'LEFT',
      FunctionCategory.text,
      'Returns leftmost characters from text',
      (args) {
        if (args.isEmpty) {
          throw ArgumentError('LEFT requires at least 1 argument');
        }
        final text = args[0].toString();
        final numChars = args.length > 1
            ? (_parseNumber(args[1])?.toInt() ?? 1)
            : 1;
        if (numChars < 0) {
          throw ArgumentError(
            'LEFT requires non-negative number of characters',
          );
        }
        return text.substring(0, math.min(numChars, text.length));
      },
    );

    _registerFunction(
      'RIGHT',
      FunctionCategory.text,
      'Returns rightmost characters from text',
      (args) {
        if (args.isEmpty) {
          throw ArgumentError('RIGHT requires at least 1 argument');
        }
        final text = args[0].toString();
        final numChars = args.length > 1
            ? (_parseNumber(args[1])?.toInt() ?? 1)
            : 1;
        if (numChars < 0) {
          throw ArgumentError(
            'RIGHT requires non-negative number of characters',
          );
        }
        final startIndex = math.max(0, text.length - numChars);
        return text.substring(startIndex);
      },
    );

    _registerFunction(
      'MID',
      FunctionCategory.text,
      'Returns characters from the middle of text',
      (args) {
        if (args.length < 2) {
          throw ArgumentError('MID requires at least 2 arguments');
        }
        final text = args[0].toString();
        final startNum = _parseNumber(args[1])?.toInt();
        final numChars = args.length > 2
            ? (_parseNumber(args[2])?.toInt() ?? text.length)
            : text.length;
        if (startNum == null || startNum < 1) {
          throw ArgumentError('MID requires valid start position');
        }
        if (numChars < 0) {
          throw ArgumentError('MID requires non-negative number of characters');
        }
        final startIndex = startNum - 1;
        if (startIndex >= text.length) return '';
        return text.substring(
          startIndex,
          math.min(startIndex + numChars, text.length),
        );
      },
    );

    _registerFunction(
      'LEN',
      FunctionCategory.text,
      'Returns the length of text',
      (args) {
        if (args.isEmpty) throw ArgumentError('LEN requires 1 argument');
        return args[0].toString().length;
      },
    );

    _registerFunction(
      'UPPER',
      FunctionCategory.text,
      'Converts text to uppercase',
      (args) {
        if (args.isEmpty) throw ArgumentError('UPPER requires 1 argument');
        return args[0].toString().toUpperCase();
      },
    );

    _registerFunction(
      'LOWER',
      FunctionCategory.text,
      'Converts text to lowercase',
      (args) {
        if (args.isEmpty) throw ArgumentError('LOWER requires 1 argument');
        return args[0].toString().toLowerCase();
      },
    );

    _registerFunction(
      'PROPER',
      FunctionCategory.text,
      'Capitalizes first letter of each word',
      (args) {
        if (args.isEmpty) throw ArgumentError('PROPER requires 1 argument');
        final text = args[0].toString();
        return text
            .split(' ')
            .map((word) {
              if (word.isEmpty) return word;
              return word[0].toUpperCase() + word.substring(1).toLowerCase();
            })
            .join(' ');
      },
    );

    _registerFunction(
      'TRIM',
      FunctionCategory.text,
      'Removes extra spaces from text',
      (args) {
        if (args.isEmpty) throw ArgumentError('TRIM requires 1 argument');
        return args[0].toString().trim().replaceAll(RegExp(r'\s+'), ' ');
      },
    );

    _registerFunction(
      'FIND',
      FunctionCategory.text,
      'Finds one text string within another',
      (args) {
        if (args.length < 2) {
          throw ArgumentError('FIND requires at least 2 arguments');
        }
        final findText = args[0].toString();
        final withinText = args[1].toString();
        final startNum = args.length > 2
            ? (_parseNumber(args[2])?.toInt() ?? 1)
            : 1;
        if (startNum < 1) {
          throw ArgumentError('FIND requires valid start position');
        }
        final startIndex = startNum - 1;
        if (startIndex >= withinText.length) {
          throw ArgumentError('Start position beyond text length');
        }
        final result = withinText.indexOf(findText, startIndex);
        if (result == -1) throw ArgumentError('Text not found');
        return result + 1;
      },
    );

    _registerFunction(
      'SEARCH',
      FunctionCategory.text,
      'Finds one text string within another (case-insensitive)',
      (args) {
        if (args.length < 2) {
          throw ArgumentError('SEARCH requires at least 2 arguments');
        }
        final findText = args[0].toString().toLowerCase();
        final withinText = args[1].toString().toLowerCase();
        final startNum = args.length > 2
            ? (_parseNumber(args[2])?.toInt() ?? 1)
            : 1;
        if (startNum < 1) {
          throw ArgumentError('SEARCH requires valid start position');
        }
        final startIndex = startNum - 1;
        if (startIndex >= withinText.length) {
          throw ArgumentError('Start position beyond text length');
        }
        final result = withinText.indexOf(findText, startIndex);
        if (result == -1) throw ArgumentError('Text not found');
        return result + 1;
      },
    );

    _registerFunction(
      'REPLACE',
      FunctionCategory.text,
      'Replaces part of text with new text',
      (args) {
        if (args.length < 4) {
          throw ArgumentError('REPLACE requires 4 arguments');
        }
        final oldText = args[0].toString();
        final startNum = _parseNumber(args[1])?.toInt();
        final numChars = _parseNumber(args[2])?.toInt();
        final newText = args[3].toString();
        if (startNum == null || startNum < 1) {
          throw ArgumentError('REPLACE requires valid start position');
        }
        if (numChars == null || numChars < 0) {
          throw ArgumentError(
            'REPLACE requires non-negative number of characters',
          );
        }
        final startIndex = startNum - 1;
        if (startIndex > oldText.length) return oldText + newText;
        final endIndex = math.min(startIndex + numChars, oldText.length);
        return oldText.substring(0, startIndex) +
            newText +
            oldText.substring(endIndex);
      },
    );

    _registerFunction(
      'SUBSTITUTE',
      FunctionCategory.text,
      'Substitutes new text for old text',
      (args) {
        if (args.length < 3) {
          throw ArgumentError('SUBSTITUTE requires at least 3 arguments');
        }
        final text = args[0].toString();
        final oldText = args[1].toString();
        final newText = args[2].toString();
        final instanceNum = args.length > 3
            ? _parseNumber(args[3])?.toInt()
            : null;

        if (instanceNum != null) {
          if (instanceNum < 1) {
            throw ArgumentError('SUBSTITUTE requires positive instance number');
          }
          int count = 0;
          int index = 0;
          while (index < text.length) {
            final foundIndex = text.indexOf(oldText, index);
            if (foundIndex == -1) break;
            count++;
            if (count == instanceNum) {
              return text.substring(0, foundIndex) +
                  newText +
                  text.substring(foundIndex + oldText.length);
            }
            index = foundIndex + 1;
          }
          return text;
        } else {
          return text.replaceAll(oldText, newText);
        }
      },
    );

    _registerFunction(
      'REPT',
      FunctionCategory.text,
      'Repeats text a given number of times',
      (args) {
        if (args.length < 2) throw ArgumentError('REPT requires 2 arguments');
        final text = args[0].toString();
        final numberTimes = _parseNumber(args[1])?.toInt();
        if (numberTimes == null || numberTimes < 0) {
          throw ArgumentError('REPT requires non-negative number');
        }
        return text * numberTimes;
      },
    );

    _registerFunction(
      'CHAR',
      FunctionCategory.text,
      'Returns character specified by code number',
      (args) {
        if (args.isEmpty) throw ArgumentError('CHAR requires 1 argument');
        final code = _parseNumber(args[0])?.toInt();
        if (code == null || code < 1 || code > 255) {
          throw ArgumentError('CHAR requires valid character code');
        }
        return String.fromCharCode(code);
      },
    );

    _registerFunction(
      'CODE',
      FunctionCategory.text,
      'Returns numeric code for first character',
      (args) {
        if (args.isEmpty) throw ArgumentError('CODE requires 1 argument');
        final text = args[0].toString();
        if (text.isEmpty) throw ArgumentError('CODE requires non-empty text');
        return text.codeUnitAt(0);
      },
    );

    _registerFunction(
      'TEXT',
      FunctionCategory.text,
      'Formats a number and converts it to text',
      (args) {
        if (args.length < 2) throw ArgumentError('TEXT requires 2 arguments');
        final value = _parseNumber(args[0]) ?? 0;
        final format = args[1].toString();
        return _formatNumber(value, format);
      },
    );
  }

  // Date and Time Functions
  static void _registerDateTimeFunctions() {
    _registerFunction(
      'NOW',
      FunctionCategory.dateTime,
      'Returns current date and time',
      (args) {
        return DateTime.now();
      },
    );

    _registerFunction(
      'TODAY',
      FunctionCategory.dateTime,
      'Returns current date',
      (args) {
        final now = DateTime.now();
        return DateTime(now.year, now.month, now.day);
      },
    );

    _registerFunction(
      'YEAR',
      FunctionCategory.dateTime,
      'Returns the year of a date',
      (args) {
        if (args.isEmpty) throw ArgumentError('YEAR requires 1 argument');
        final date = _parseDate(args[0]);
        if (date == null) throw ArgumentError('YEAR requires a valid date');
        return date.year;
      },
    );

    _registerFunction(
      'MONTH',
      FunctionCategory.dateTime,
      'Returns the month of a date',
      (args) {
        if (args.isEmpty) throw ArgumentError('MONTH requires 1 argument');
        final date = _parseDate(args[0]);
        if (date == null) throw ArgumentError('MONTH requires a valid date');
        return date.month;
      },
    );

    _registerFunction(
      'DAY',
      FunctionCategory.dateTime,
      'Returns the day of a date',
      (args) {
        if (args.isEmpty) throw ArgumentError('DAY requires 1 argument');
        final date = _parseDate(args[0]);
        if (date == null) throw ArgumentError('DAY requires a valid date');
        return date.day;
      },
    );

    _registerFunction(
      'HOUR',
      FunctionCategory.dateTime,
      'Returns the hour of a time',
      (args) {
        if (args.isEmpty) throw ArgumentError('HOUR requires 1 argument');
        final date = _parseDate(args[0]);
        if (date == null) {
          throw ArgumentError('HOUR requires a valid date/time');
        }
        return date.hour;
      },
    );

    _registerFunction(
      'MINUTE',
      FunctionCategory.dateTime,
      'Returns the minute of a time',
      (args) {
        if (args.isEmpty) throw ArgumentError('MINUTE requires 1 argument');
        final date = _parseDate(args[0]);
        if (date == null) {
          throw ArgumentError('MINUTE requires a valid date/time');
        }
        return date.minute;
      },
    );

    _registerFunction(
      'SECOND',
      FunctionCategory.dateTime,
      'Returns the second of a time',
      (args) {
        if (args.isEmpty) throw ArgumentError('SECOND requires 1 argument');
        final date = _parseDate(args[0]);
        if (date == null) {
          throw ArgumentError('SECOND requires a valid date/time');
        }
        return date.second;
      },
    );

    _registerFunction(
      'WEEKDAY',
      FunctionCategory.dateTime,
      'Returns the day of the week',
      (args) {
        if (args.isEmpty) throw ArgumentError('WEEKDAY requires 1 argument');
        final date = _parseDate(args[0]);
        if (date == null) throw ArgumentError('WEEKDAY requires a valid date');
        final returnType = args.length > 1
            ? (_parseNumber(args[1])?.toInt() ?? 1)
            : 1;

        switch (returnType) {
          case 1: // Sunday = 1, Monday = 2, ..., Saturday = 7
            return date.weekday == 7 ? 1 : date.weekday + 1;
          case 2: // Monday = 1, Tuesday = 2, ..., Sunday = 7
            return date.weekday;
          case 3: // Monday = 0, Tuesday = 1, ..., Sunday = 6
            return date.weekday - 1;
          default:
            throw ArgumentError('WEEKDAY return type must be 1, 2, or 3');
        }
      },
    );

    _registerFunction(
      'DATE',
      FunctionCategory.dateTime,
      'Creates a date from year, month, and day',
      (args) {
        if (args.length < 3) throw ArgumentError('DATE requires 3 arguments');
        final year = _parseNumber(args[0])?.toInt();
        final month = _parseNumber(args[1])?.toInt();
        final day = _parseNumber(args[2])?.toInt();
        if (year == null || month == null || day == null) {
          throw ArgumentError('DATE requires valid year, month, and day');
        }
        try {
          return DateTime(year, month, day);
        } catch (e) {
          throw ArgumentError('DATE: Invalid date values');
        }
      },
    );

    _registerFunction(
      'TIME',
      FunctionCategory.dateTime,
      'Creates a time from hour, minute, and second',
      (args) {
        if (args.length < 3) throw ArgumentError('TIME requires 3 arguments');
        final hour = _parseNumber(args[0])?.toInt();
        final minute = _parseNumber(args[1])?.toInt();
        final second = _parseNumber(args[2])?.toInt();
        if (hour == null || minute == null || second == null) {
          throw ArgumentError('TIME requires valid hour, minute, and second');
        }
        if (hour < 0 ||
            hour > 23 ||
            minute < 0 ||
            minute > 59 ||
            second < 0 ||
            second > 59) {
          throw ArgumentError('TIME: Invalid time values');
        }
        return DateTime(1900, 1, 1, hour, minute, second);
      },
    );
  }

  // Logical Functions
  static void _registerLogicalFunctions() {
    _registerFunction(
      'IF',
      FunctionCategory.logical,
      'Returns one value if condition is true, another if false',
      (args) {
        if (args.length < 2) {
          throw ArgumentError('IF requires at least 2 arguments');
        }
        final condition = _parseBoolean(args[0]);
        final valueIfTrue = args[1];
        final valueIfFalse = args.length > 2 ? args[2] : false;
        return condition ? valueIfTrue : valueIfFalse;
      },
    );

    _registerFunction(
      'AND',
      FunctionCategory.logical,
      'Returns true if all arguments are true',
      (args) {
        if (args.isEmpty) return true;
        return _flattenArgs(args).every((arg) => _parseBoolean(arg));
      },
    );

    _registerFunction(
      'OR',
      FunctionCategory.logical,
      'Returns true if any argument is true',
      (args) {
        if (args.isEmpty) return false;
        return _flattenArgs(args).any((arg) => _parseBoolean(arg));
      },
    );

    _registerFunction(
      'NOT',
      FunctionCategory.logical,
      'Reverses the logic of its argument',
      (args) {
        if (args.isEmpty) throw ArgumentError('NOT requires 1 argument');
        return !_parseBoolean(args[0]);
      },
    );

    _registerFunction(
      'TRUE',
      FunctionCategory.logical,
      'Returns the logical value TRUE',
      (args) {
        return true;
      },
    );

    _registerFunction(
      'FALSE',
      FunctionCategory.logical,
      'Returns the logical value FALSE',
      (args) {
        return false;
      },
    );
  }

  // Lookup Functions
  static void _registerLookupFunctions() {
    _registerFunction(
      'VLOOKUP',
      FunctionCategory.lookup,
      'Looks up a value in the first column and returns a value in the same row',
      (args) {
        if (args.length < 3) {
          throw ArgumentError('VLOOKUP requires at least 3 arguments');
        }
        final lookupValue = args[0];
        final tableArray = args[1] as List<List<dynamic>>;
        final colIndexNum = _parseNumber(args[2])?.toInt();
        // final rangeLookup = args.length > 3 ? _parseBoolean(args[3]) : true;

        if (colIndexNum == null ||
            colIndexNum < 1 ||
            colIndexNum > tableArray.first.length) {
          throw ArgumentError('VLOOKUP: Invalid column index');
        }

        for (final row in tableArray) {
          if (row.isNotEmpty &&
              row.first.toString() == lookupValue.toString()) {
            return row[colIndexNum - 1];
          }
        }

        throw ArgumentError('VLOOKUP: Value not found');
      },
    );

    _registerFunction(
      'HLOOKUP',
      FunctionCategory.lookup,
      'Looks up a value in the top row and returns a value in the same column',
      (args) {
        if (args.length < 3) {
          throw ArgumentError('HLOOKUP requires at least 3 arguments');
        }
        final lookupValue = args[0];
        final tableArray = args[1] as List<List<dynamic>>;
        final rowIndexNum = _parseNumber(args[2])?.toInt();
        // final rangeLookup = args.length > 3 ? _parseBoolean(args[3]) : true;

        if (rowIndexNum == null ||
            rowIndexNum < 1 ||
            rowIndexNum > tableArray.length) {
          throw ArgumentError('HLOOKUP: Invalid row index');
        }

        if (tableArray.isNotEmpty) {
          final headerRow = tableArray.first;
          for (int i = 0; i < headerRow.length; i++) {
            if (headerRow[i].toString() == lookupValue.toString()) {
              return tableArray[rowIndexNum - 1][i];
            }
          }
        }

        throw ArgumentError('HLOOKUP: Value not found');
      },
    );

    _registerFunction(
      'INDEX',
      FunctionCategory.lookup,
      'Returns a value from a table based on row and column numbers',
      (args) {
        if (args.length < 3) throw ArgumentError('INDEX requires 3 arguments');
        final array = args[0] as List<List<dynamic>>;
        final rowNum = _parseNumber(args[1])?.toInt();
        final colNum = _parseNumber(args[2])?.toInt();

        if (rowNum == null || colNum == null || rowNum < 1 || colNum < 1) {
          throw ArgumentError('INDEX: Invalid row or column number');
        }

        if (rowNum > array.length || colNum > array.first.length) {
          throw ArgumentError('INDEX: Row or column number out of range');
        }

        return array[rowNum - 1][colNum - 1];
      },
    );

    _registerFunction(
      'MATCH',
      FunctionCategory.lookup,
      'Returns the position of a value in an array',
      (args) {
        if (args.length < 2) {
          throw ArgumentError('MATCH requires at least 2 arguments');
        }
        final lookupValue = args[0];
        final lookupArray = _flattenArgs([args[1]]);
        // final matchType = args.length > 2
        //     ? (_parseNumber(args[2])?.toInt() ?? 1)
        //     : 1;

        for (int i = 0; i < lookupArray.length; i++) {
          if (lookupArray[i].toString() == lookupValue.toString()) {
            return i + 1;
          }
        }

        throw ArgumentError('MATCH: Value not found');
      },
    );
  }

  // Financial Functions
  static void _registerFinancialFunctions() {
    _registerFunction(
      'PMT',
      FunctionCategory.financial,
      'Calculates payment for a loan',
      (args) {
        if (args.length < 3) throw ArgumentError('PMT requires 3 arguments');
        final rate = _parseNumber(args[0]);
        final nper = _parseNumber(args[1]);
        final pv = _parseNumber(args[2]);
        final fv = args.length > 3 ? (_parseNumber(args[3]) ?? 0) : 0;
        final type = args.length > 4
            ? (_parseNumber(args[4])?.toInt() ?? 0)
            : 0;

        if (rate == null || nper == null || pv == null) {
          throw ArgumentError('PMT requires valid numeric arguments');
        }

        if (rate == 0) {
          return -(pv + fv) / nper;
        }

        final pvif = math.pow(1 + rate, nper);
        final pmt = -(pv * pvif + fv) / ((pvif - 1) / rate * (1 + rate * type));
        return pmt;
      },
    );

    _registerFunction(
      'PV',
      FunctionCategory.financial,
      'Calculates present value of an investment',
      (args) {
        if (args.length < 3) throw ArgumentError('PV requires 3 arguments');
        final rate = _parseNumber(args[0]);
        final nper = _parseNumber(args[1]);
        final pmt = _parseNumber(args[2]);
        final fv = args.length > 3 ? (_parseNumber(args[3]) ?? 0) : 0;
        final type = args.length > 4
            ? (_parseNumber(args[4])?.toInt() ?? 0)
            : 0;

        if (rate == null || nper == null || pmt == null) {
          throw ArgumentError('PV requires valid numeric arguments');
        }

        if (rate == 0) {
          return -pmt * nper - fv;
        }

        final pvif = math.pow(1 + rate, nper);
        final pv = -(pmt * (1 + rate * type) * (pvif - 1) / rate + fv) / pvif;
        return pv;
      },
    );

    _registerFunction(
      'FV',
      FunctionCategory.financial,
      'Calculates future value of an investment',
      (args) {
        if (args.length < 3) throw ArgumentError('FV requires 3 arguments');
        final rate = _parseNumber(args[0]);
        final nper = _parseNumber(args[1]);
        final pmt = _parseNumber(args[2]);
        final pv = args.length > 3 ? (_parseNumber(args[3]) ?? 0) : 0;
        final type = args.length > 4
            ? (_parseNumber(args[4])?.toInt() ?? 0)
            : 0;

        if (rate == null || nper == null || pmt == null) {
          throw ArgumentError('FV requires valid numeric arguments');
        }

        if (rate == 0) {
          return -pv - pmt * nper;
        }

        final fvif = math.pow(1 + rate, nper);
        final fv = -pv * fvif - pmt * (1 + rate * type) * (fvif - 1) / rate;
        return fv;
      },
    );
  }

  // Engineering Functions
  static void _registerEngineeringFunctions() {
    _registerFunction(
      'CONVERT',
      FunctionCategory.engineering,
      'Converts a number from one measurement system to another',
      (args) {
        if (args.length < 3) {
          throw ArgumentError('CONVERT requires 3 arguments');
        }
        final number = _parseNumber(args[0]);
        final fromUnit = args[1].toString().toLowerCase();
        final toUnit = args[2].toString().toLowerCase();

        if (number == null) {
          throw ArgumentError('CONVERT requires a valid number');
        }

        return _performUnitConversion(number, fromUnit, toUnit);
      },
    );
  }

  // Database Functions
  static void _registerDatabaseFunctions() {
    _registerFunction(
      'DSUM',
      FunctionCategory.database,
      'Sums values in a database that match criteria',
      (args) {
        if (args.length < 3) throw ArgumentError('DSUM requires 3 arguments');
        // Simplified implementation
        return 0;
      },
    );
  }

  // Array Functions
  static void _registerArrayFunctions() {
    _registerFunction(
      'TRANSPOSE',
      FunctionCategory.array,
      'Transposes an array',
      (args) {
        if (args.isEmpty) throw ArgumentError('TRANSPOSE requires 1 argument');
        final array = args[0] as List<List<dynamic>>;
        if (array.isEmpty) return [];

        final transposed = <List<dynamic>>[];
        for (int col = 0; col < array.first.length; col++) {
          final newRow = <dynamic>[];
          for (int row = 0; row < array.length; row++) {
            newRow.add(array[row][col]);
          }
          transposed.add(newRow);
        }
        return transposed;
      },
    );
  }

  // Helper methods
  static void _registerFunction(
    String name,
    FunctionCategory category,
    String description,
    Function implementation,
  ) {
    _functions[name] = implementation;
    _functionMetadata[name] = FunctionMetadata(
      name: name,
      category: category,
      description: description,
    );
  }

  static List<dynamic> _flattenArgs(List<dynamic> args) {
    final result = <dynamic>[];
    for (final arg in args) {
      if (arg is List) {
        result.addAll(_flattenArgs(arg));
      } else {
        result.add(arg);
      }
    }
    return result;
  }

  static double? _parseNumber(dynamic value) {
    if (value is num) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  static bool _parseBoolean(dynamic value) {
    if (value is bool) return value;
    if (value is num) return value != 0;
    if (value is String) {
      final lower = value.toLowerCase();
      if (lower == 'true' || lower == 'yes' || lower == '1') return true;
      if (lower == 'false' || lower == 'no' || lower == '0') return false;
      return value.isNotEmpty;
    }
    return value != null;
  }

  static DateTime? _parseDate(dynamic value) {
    if (value is DateTime) return value;
    if (value is String) {
      return DateTime.tryParse(value);
    }
    if (value is num) {
      // Excel date serial number (days since 1900-01-01)
      final baseDate = DateTime(1900, 1, 1);
      return baseDate.add(
        Duration(days: value.toInt() - 2),
      ); // Excel has a leap year bug
    }
    return null;
  }

  /// Format number according to Excel format string
  static String _formatNumber(double value, String format) {
    // Simplified number formatting - in production would need full Excel format support
    if (format.contains('%')) {
      return '${(value * 100).toStringAsFixed(2)}%';
    }
    if (format.contains('\$')) {
      return '\$${value.toStringAsFixed(2)}';
    }
    if (format.contains('#,##0')) {
      return value
          .toStringAsFixed(0)
          .replaceAllMapped(
            RegExp(r'(\d)(?=(\d{3})+(?!\d))'),
            (match) => '${match[1]},',
          );
    }
    if (format.contains('0.00')) {
      return value.toStringAsFixed(2);
    }
    return value.toString();
  }

  /// Perform unit conversion
  static double _performUnitConversion(
    double value,
    String fromUnit,
    String toUnit,
  ) {
    // Comprehensive unit conversion table
    final conversions = <String, Map<String, double>>{
      // Length conversions
      'length': {
        'in_cm': 2.54,
        'cm_in': 1 / 2.54,
        'ft_m': 0.3048,
        'm_ft': 1 / 0.3048,
        'yd_m': 0.9144,
        'm_yd': 1 / 0.9144,
        'mi_km': 1.609344,
        'km_mi': 1 / 1.609344,
        'mm_in': 1 / 25.4,
        'in_mm': 25.4,
      },
      // Weight conversions
      'weight': {
        'lb_kg': 0.453592,
        'kg_lb': 1 / 0.453592,
        'oz_g': 28.3495,
        'g_oz': 1 / 28.3495,
        'ton_kg': 1000,
        'kg_ton': 1 / 1000,
      },
      // Temperature conversions (special handling needed)
      'temperature': {
        'c_f': 1.8, // multiply by 1.8 and add 32
        'f_c': 5 / 9, // subtract 32 and multiply by 5/9
        'c_k': 1, // add 273.15
        'k_c': 1, // subtract 273.15
      },
      // Volume conversions
      'volume': {
        'gal_l': 3.78541,
        'l_gal': 1 / 3.78541,
        'qt_l': 0.946353,
        'l_qt': 1 / 0.946353,
        'pt_l': 0.473176,
        'l_pt': 1 / 0.473176,
        'cup_ml': 236.588,
        'ml_cup': 1 / 236.588,
      },
    };

    final conversionKey = '${fromUnit}_$toUnit';

    // Check all conversion categories
    for (final category in conversions.values) {
      if (category.containsKey(conversionKey)) {
        final factor = category[conversionKey]!;

        // Special handling for temperature conversions
        if (fromUnit == 'c' && toUnit == 'f') {
          return value * 1.8 + 32;
        } else if (fromUnit == 'f' && toUnit == 'c') {
          return (value - 32) * 5 / 9;
        } else if (fromUnit == 'c' && toUnit == 'k') {
          return value + 273.15;
        } else if (fromUnit == 'k' && toUnit == 'c') {
          return value - 273.15;
        } else {
          return value * factor;
        }
      }
    }

    throw ArgumentError(
      'CONVERT: Unsupported unit conversion from $fromUnit to $toUnit',
    );
  }
}

/// Function metadata class
class FunctionMetadata {
  final String name;
  final FunctionCategory category;
  final String description;

  const FunctionMetadata({
    required this.name,
    required this.category,
    required this.description,
  });
}

/// Function categories enum
enum FunctionCategory {
  mathematical,
  statistical,
  text,
  dateTime,
  logical,
  lookup,
  financial,
  engineering,
  database,
  array,
}
