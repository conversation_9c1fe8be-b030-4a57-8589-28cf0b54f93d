import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'performance_optimizer.dart';

/// Advanced chart and visualization service
class ChartVisualizationService {
  static bool _isInitialized = false;
  static final Map<String, ChartTemplate> _templates = {};
  static final List<ChartConfiguration> _savedCharts = [];

  /// Initialize the chart visualization service
  static void initialize() {
    if (_isInitialized) return;
    
    _createChartTemplates();
    _isInitialized = true;
  }

  /// Get available chart types
  static List<ChartType> get availableChartTypes => ChartType.values;

  /// Get chart templates
  static List<ChartTemplate> get templates => _templates.values.toList();

  /// Get template by type
  static ChartTemplate? getTemplate(ChartType type) {
    return _templates[type.name];
  }

  /// Create chart configuration from data
  static Future<ChartConfiguration> createChart({
    required ChartType type,
    required List<List<dynamic>> data,
    required ChartOptions options,
  }) async {
    return PerformanceOptimizer.measureAsync('create_chart', () async {
      final template = getTemplate(type);
      if (template == null) {
        throw ArgumentError('Unsupported chart type: $type');
      }

      final processedData = _processDataForChart(data, type, options);
      final config = ChartConfiguration(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: type,
        title: options.title,
        data: processedData,
        options: options,
        createdAt: DateTime.now(),
      );

      _savedCharts.add(config);
      return config;
    });
  }

  /// Generate chart data from formulas
  static Future<ChartData> generateChartData({
    required List<String> formulas,
    required Map<String, dynamic> variables,
    required ChartType type,
  }) async {
    return PerformanceOptimizer.measureAsync('generate_chart_data', () async {
      final dataPoints = <DataPoint>[];
      
      // Generate data based on formulas
      for (int i = 0; i < 100; i++) {
        final x = i.toDouble();
        variables['x'] = x;
        
        final values = <double>[];
        for (final formula in formulas) {
          final value = _evaluateFormula(formula, variables);
          values.add(value);
        }
        
        dataPoints.add(DataPoint(x: x, values: values));
      }

      return ChartData(
        points: dataPoints,
        series: formulas.asMap().entries.map((entry) {
          return DataSeries(
            name: 'Series ${entry.key + 1}',
            color: _getSeriesColor(entry.key),
            formula: entry.value,
          );
        }).toList(),
      );
    });
  }

  /// Create dashboard with multiple charts
  static Future<Dashboard> createDashboard({
    required String name,
    required List<ChartConfiguration> charts,
    required DashboardLayout layout,
  }) async {
    return PerformanceOptimizer.measureAsync('create_dashboard', () async {
      return Dashboard(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        charts: charts,
        layout: layout,
        createdAt: DateTime.now(),
      );
    });
  }

  /// Export chart as image data
  static Future<ChartExportResult> exportChart({
    required ChartConfiguration config,
    required ExportFormat format,
    required Size size,
  }) async {
    return PerformanceOptimizer.measureAsync('export_chart', () async {
      // Simulate chart export
      final data = _generateMockImageData(size);
      
      return ChartExportResult(
        success: true,
        data: data,
        format: format,
        size: size,
        fileName: '${config.title}_${DateTime.now().millisecondsSinceEpoch}.${format.extension}',
      );
    });
  }

  /// Get chart statistics
  static ChartStatistics getStatistics(ChartConfiguration config) {
    final data = config.data;
    if (data.points.isEmpty) {
      return const ChartStatistics(
        totalPoints: 0,
        seriesCount: 0,
        minValue: 0,
        maxValue: 0,
        averageValue: 0,
        standardDeviation: 0,
      );
    }

    final allValues = data.points
        .expand((point) => point.values)
        .where((value) => value.isFinite)
        .toList();

    if (allValues.isEmpty) {
      return const ChartStatistics(
        totalPoints: 0,
        seriesCount: 0,
        minValue: 0,
        maxValue: 0,
        averageValue: 0,
        standardDeviation: 0,
      );
    }

    final minValue = allValues.reduce(math.min);
    final maxValue = allValues.reduce(math.max);
    final averageValue = allValues.reduce((a, b) => a + b) / allValues.length;
    
    final variance = allValues
        .map((value) => math.pow(value - averageValue, 2))
        .reduce((a, b) => a + b) / allValues.length;
    final standardDeviation = math.sqrt(variance);

    return ChartStatistics(
      totalPoints: data.points.length,
      seriesCount: data.series.length,
      minValue: minValue,
      maxValue: maxValue,
      averageValue: averageValue,
      standardDeviation: standardDeviation,
    );
  }

  /// Get chart recommendations based on data
  static List<ChartRecommendation> getChartRecommendations(List<List<dynamic>> data) {
    final recommendations = <ChartRecommendation>[];
    
    if (data.isEmpty) return recommendations;
    
    final rowCount = data.length;
    final columnCount = data.first.length;
    
    // Analyze data characteristics
    final hasNumericData = _hasNumericData(data);
    final hasCategoricalData = _hasCategoricalData(data);
    final hasTimeData = _hasTimeData(data);
    
    // Line chart recommendations
    if (hasNumericData && rowCount > 5) {
      recommendations.add(ChartRecommendation(
        type: ChartType.line,
        confidence: 0.8,
        reason: 'Good for showing trends over time with numeric data',
        suitability: hasTimeData ? 0.9 : 0.7,
      ));
    }
    
    // Bar chart recommendations
    if (hasCategoricalData && hasNumericData) {
      recommendations.add(ChartRecommendation(
        type: ChartType.bar,
        confidence: 0.9,
        reason: 'Excellent for comparing categories',
        suitability: 0.8,
      ));
    }
    
    // Pie chart recommendations
    if (hasCategoricalData && columnCount == 2 && rowCount <= 10) {
      recommendations.add(ChartRecommendation(
        type: ChartType.pie,
        confidence: 0.7,
        reason: 'Good for showing proportions of a whole',
        suitability: 0.6,
      ));
    }
    
    // Scatter plot recommendations
    if (hasNumericData && columnCount >= 2) {
      recommendations.add(ChartRecommendation(
        type: ChartType.scatter,
        confidence: 0.6,
        reason: 'Useful for showing correlation between variables',
        suitability: 0.7,
      ));
    }
    
    // Area chart recommendations
    if (hasNumericData && hasTimeData) {
      recommendations.add(ChartRecommendation(
        type: ChartType.area,
        confidence: 0.7,
        reason: 'Good for showing cumulative values over time',
        suitability: 0.6,
      ));
    }
    
    // Sort by confidence
    recommendations.sort((a, b) => b.confidence.compareTo(a.confidence));
    
    return recommendations;
  }

  /// Get saved charts
  static List<ChartConfiguration> get savedCharts => List.unmodifiable(_savedCharts);

  /// Delete chart
  static void deleteChart(String chartId) {
    _savedCharts.removeWhere((chart) => chart.id == chartId);
  }

  /// Clear all charts
  static void clearCharts() {
    _savedCharts.clear();
  }

  // Private methods
  static void _createChartTemplates() {
    _templates[ChartType.line.name] = ChartTemplate(
      type: ChartType.line,
      name: 'Line Chart',
      description: 'Shows trends and changes over time',
      icon: Icons.show_chart,
      color: Colors.blue,
      defaultOptions: ChartOptions(
        title: 'Line Chart',
        showLegend: true,
        showGrid: true,
        animationDuration: 1000,
      ),
    );

    _templates[ChartType.bar.name] = ChartTemplate(
      type: ChartType.bar,
      name: 'Bar Chart',
      description: 'Compares different categories',
      icon: Icons.bar_chart,
      color: Colors.green,
      defaultOptions: ChartOptions(
        title: 'Bar Chart',
        showLegend: true,
        showGrid: true,
        animationDuration: 800,
      ),
    );

    _templates[ChartType.pie.name] = ChartTemplate(
      type: ChartType.pie,
      name: 'Pie Chart',
      description: 'Shows proportions of a whole',
      icon: Icons.pie_chart,
      color: Colors.orange,
      defaultOptions: ChartOptions(
        title: 'Pie Chart',
        showLegend: true,
        showGrid: false,
        animationDuration: 1200,
      ),
    );

    _templates[ChartType.scatter.name] = ChartTemplate(
      type: ChartType.scatter,
      name: 'Scatter Plot',
      description: 'Shows correlation between variables',
      icon: Icons.scatter_plot,
      color: Colors.purple,
      defaultOptions: ChartOptions(
        title: 'Scatter Plot',
        showLegend: true,
        showGrid: true,
        animationDuration: 600,
      ),
    );

    _templates[ChartType.area.name] = ChartTemplate(
      type: ChartType.area,
      name: 'Area Chart',
      description: 'Shows cumulative values over time',
      icon: Icons.area_chart,
      color: Colors.teal,
      defaultOptions: ChartOptions(
        title: 'Area Chart',
        showLegend: true,
        showGrid: true,
        animationDuration: 1000,
      ),
    );

    _templates[ChartType.histogram.name] = ChartTemplate(
      type: ChartType.histogram,
      name: 'Histogram',
      description: 'Shows distribution of data',
      icon: Icons.equalizer,
      color: Colors.indigo,
      defaultOptions: ChartOptions(
        title: 'Histogram',
        showLegend: false,
        showGrid: true,
        animationDuration: 800,
      ),
    );
  }

  static ChartData _processDataForChart(List<List<dynamic>> data, ChartType type, ChartOptions options) {
    final points = <DataPoint>[];
    final series = <DataSeries>[];
    
    if (data.isEmpty) {
      return ChartData(points: points, series: series);
    }
    
    // Process based on chart type
    switch (type) {
      case ChartType.line:
      case ChartType.area:
        _processTimeSeriesData(data, points, series);
        break;
      case ChartType.bar:
        _processCategoricalData(data, points, series);
        break;
      case ChartType.pie:
        _processPieData(data, points, series);
        break;
      case ChartType.scatter:
        _processScatterData(data, points, series);
        break;
      case ChartType.histogram:
        _processHistogramData(data, points, series);
        break;
    }
    
    return ChartData(points: points, series: series);
  }

  static void _processTimeSeriesData(List<List<dynamic>> data, List<DataPoint> points, List<DataSeries> series) {
    for (int i = 1; i < data.length; i++) {
      final row = data[i];
      if (row.isNotEmpty) {
        final x = i.toDouble();
        final values = <double>[];
        
        for (int j = 1; j < row.length; j++) {
          final value = double.tryParse(row[j].toString()) ?? 0.0;
          values.add(value);
        }
        
        points.add(DataPoint(x: x, values: values));
      }
    }
    
    // Create series
    if (data.isNotEmpty && data.first.length > 1) {
      for (int j = 1; j < data.first.length; j++) {
        series.add(DataSeries(
          name: data.first[j].toString(),
          color: _getSeriesColor(j - 1),
          formula: '',
        ));
      }
    }
  }

  static void _processCategoricalData(List<List<dynamic>> data, List<DataPoint> points, List<DataSeries> series) {
    for (int i = 1; i < data.length; i++) {
      final row = data[i];
      if (row.length >= 2) {
        final x = i.toDouble();
        final value = double.tryParse(row[1].toString()) ?? 0.0;
        points.add(DataPoint(x: x, values: [value], category: row[0].toString()));
      }
    }
    
    series.add(DataSeries(
      name: 'Values',
      color: Colors.blue,
      formula: '',
    ));
  }

  static void _processPieData(List<List<dynamic>> data, List<DataPoint> points, List<DataSeries> series) {
    for (int i = 1; i < data.length; i++) {
      final row = data[i];
      if (row.length >= 2) {
        final value = double.tryParse(row[1].toString()) ?? 0.0;
        points.add(DataPoint(x: i.toDouble(), values: [value], category: row[0].toString()));
      }
    }
    
    series.add(DataSeries(
      name: 'Segments',
      color: Colors.blue,
      formula: '',
    ));
  }

  static void _processScatterData(List<List<dynamic>> data, List<DataPoint> points, List<DataSeries> series) {
    for (int i = 1; i < data.length; i++) {
      final row = data[i];
      if (row.length >= 2) {
        final x = double.tryParse(row[0].toString()) ?? 0.0;
        final y = double.tryParse(row[1].toString()) ?? 0.0;
        points.add(DataPoint(x: x, values: [y]));
      }
    }
    
    series.add(DataSeries(
      name: 'Data Points',
      color: Colors.blue,
      formula: '',
    ));
  }

  static void _processHistogramData(List<List<dynamic>> data, List<DataPoint> points, List<DataSeries> series) {
    final values = <double>[];
    
    // Collect all numeric values
    for (int i = 1; i < data.length; i++) {
      for (int j = 0; j < data[i].length; j++) {
        final value = double.tryParse(data[i][j].toString());
        if (value != null) {
          values.add(value);
        }
      }
    }
    
    // Create histogram bins
    if (values.isNotEmpty) {
      final minValue = values.reduce(math.min);
      final maxValue = values.reduce(math.max);
      const binCount = 10;
      final binWidth = (maxValue - minValue) / binCount;
      
      final bins = List.filled(binCount, 0);
      
      for (final value in values) {
        final binIndex = ((value - minValue) / binWidth).floor().clamp(0, binCount - 1);
        bins[binIndex]++;
      }
      
      for (int i = 0; i < binCount; i++) {
        final binStart = minValue + i * binWidth;
        points.add(DataPoint(x: binStart, values: [bins[i].toDouble()]));
      }
    }
    
    series.add(DataSeries(
      name: 'Frequency',
      color: Colors.blue,
      formula: '',
    ));
  }

  static double _evaluateFormula(String formula, Map<String, dynamic> variables) {
    // Simple formula evaluation - in production would use proper parser
    try {
      var result = formula;
      variables.forEach((key, value) {
        result = result.replaceAll(key, value.toString());
      });
      
      // Basic math operations
      if (result.contains('sin(')) {
        final match = RegExp(r'sin\(([^)]+)\)').firstMatch(result);
        if (match != null) {
          final value = double.tryParse(match.group(1)!) ?? 0;
          result = result.replaceAll(match.group(0)!, math.sin(value).toString());
        }
      }
      
      if (result.contains('cos(')) {
        final match = RegExp(r'cos\(([^)]+)\)').firstMatch(result);
        if (match != null) {
          final value = double.tryParse(match.group(1)!) ?? 0;
          result = result.replaceAll(match.group(0)!, math.cos(value).toString());
        }
      }
      
      return double.tryParse(result) ?? 0.0;
    } catch (e) {
      return 0.0;
    }
  }

  static Color _getSeriesColor(int index) {
    final colors = [
      Colors.blue,
      Colors.red,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.pink,
      Colors.indigo,
    ];
    return colors[index % colors.length];
  }

  static bool _hasNumericData(List<List<dynamic>> data) {
    for (final row in data.skip(1)) {
      for (final cell in row) {
        if (double.tryParse(cell.toString()) != null) {
          return true;
        }
      }
    }
    return false;
  }

  static bool _hasCategoricalData(List<List<dynamic>> data) {
    for (final row in data.skip(1)) {
      for (final cell in row) {
        if (double.tryParse(cell.toString()) == null && cell.toString().isNotEmpty) {
          return true;
        }
      }
    }
    return false;
  }

  static bool _hasTimeData(List<List<dynamic>> data) {
    for (final row in data.skip(1)) {
      for (final cell in row) {
        if (DateTime.tryParse(cell.toString()) != null) {
          return true;
        }
      }
    }
    return false;
  }

  static List<int> _generateMockImageData(Size size) {
    // Generate mock image data
    final width = size.width.toInt();
    final height = size.height.toInt();
    return List.generate(width * height * 4, (index) => 255);
  }
}

/// Data classes and enums
enum ChartType { line, bar, pie, scatter, area, histogram }

enum ExportFormat {
  png('png'),
  jpg('jpg'),
  svg('svg'),
  pdf('pdf');

  const ExportFormat(this.extension);
  final String extension;
}

enum DashboardLayout { grid, stack, tabs, carousel }

class ChartTemplate {
  final ChartType type;
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final ChartOptions defaultOptions;

  const ChartTemplate({
    required this.type,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.defaultOptions,
  });
}

class ChartConfiguration {
  final String id;
  final ChartType type;
  final String title;
  final ChartData data;
  final ChartOptions options;
  final DateTime createdAt;

  const ChartConfiguration({
    required this.id,
    required this.type,
    required this.title,
    required this.data,
    required this.options,
    required this.createdAt,
  });
}

class ChartData {
  final List<DataPoint> points;
  final List<DataSeries> series;

  const ChartData({
    required this.points,
    required this.series,
  });
}

class DataPoint {
  final double x;
  final List<double> values;
  final String? category;

  const DataPoint({
    required this.x,
    required this.values,
    this.category,
  });
}

class DataSeries {
  final String name;
  final Color color;
  final String formula;

  const DataSeries({
    required this.name,
    required this.color,
    required this.formula,
  });
}

class ChartOptions {
  final String title;
  final bool showLegend;
  final bool showGrid;
  final int animationDuration;
  final Color? backgroundColor;
  final String? xAxisLabel;
  final String? yAxisLabel;

  const ChartOptions({
    required this.title,
    required this.showLegend,
    required this.showGrid,
    required this.animationDuration,
    this.backgroundColor,
    this.xAxisLabel,
    this.yAxisLabel,
  });
}

class Dashboard {
  final String id;
  final String name;
  final List<ChartConfiguration> charts;
  final DashboardLayout layout;
  final DateTime createdAt;

  const Dashboard({
    required this.id,
    required this.name,
    required this.charts,
    required this.layout,
    required this.createdAt,
  });
}

class ChartExportResult {
  final bool success;
  final List<int> data;
  final ExportFormat format;
  final Size size;
  final String fileName;
  final String? error;

  const ChartExportResult({
    required this.success,
    required this.data,
    required this.format,
    required this.size,
    required this.fileName,
    this.error,
  });
}

class ChartStatistics {
  final int totalPoints;
  final int seriesCount;
  final double minValue;
  final double maxValue;
  final double averageValue;
  final double standardDeviation;

  const ChartStatistics({
    required this.totalPoints,
    required this.seriesCount,
    required this.minValue,
    required this.maxValue,
    required this.averageValue,
    required this.standardDeviation,
  });
}

class ChartRecommendation {
  final ChartType type;
  final double confidence;
  final String reason;
  final double suitability;

  const ChartRecommendation({
    required this.type,
    required this.confidence,
    required this.reason,
    required this.suitability,
  });
}
