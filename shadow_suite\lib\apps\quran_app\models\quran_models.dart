import 'package:flutter/material.dart';

/// Reading session model
class ReadingSession {
  final String id;
  final DateTime startTime;
  final Duration duration;
  final String surahName;
  final int versesRead;
  final double focusScore;
  final List<String> bookmarkedVerses;
  final DateTime createdAt;

  const ReadingSession({
    required this.id,
    required this.startTime,
    required this.duration,
    required this.surahName,
    required this.versesRead,
    required this.focusScore,
    required this.bookmarkedVerses,
    required this.createdAt,
  });

  ReadingSession copyWith({
    String? id,
    DateTime? startTime,
    Duration? duration,
    String? surahName,
    int? versesRead,
    double? focusScore,
    List<String>? bookmarkedVerses,
    DateTime? createdAt,
  }) {
    return ReadingSession(
      id: id ?? this.id,
      startTime: startTime ?? this.startTime,
      duration: duration ?? this.duration,
      surahName: surahName ?? this.surahName,
      versesRead: versesRead ?? this.versesRead,
      focusScore: focusScore ?? this.focusScore,
      bookmarkedVerses: bookmarkedVerses ?? this.bookmarkedVerses,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'startTime': startTime.toIso8601String(),
      'duration': duration.inMinutes,
      'surahName': surahName,
      'versesRead': versesRead,
      'focusScore': focusScore,
      'bookmarkedVerses': bookmarkedVerses,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory ReadingSession.fromJson(Map<String, dynamic> json) {
    return ReadingSession(
      id: json['id'],
      startTime: DateTime.parse(json['startTime']),
      duration: Duration(minutes: json['duration']),
      surahName: json['surahName'],
      versesRead: json['versesRead'],
      focusScore: json['focusScore'].toDouble(),
      bookmarkedVerses: List<String>.from(json['bookmarkedVerses']),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Bookmark model
class Bookmark {
  final String id;
  final String surahName;
  final int verseNumber;
  final String verseText;
  final String note;
  final List<String> tags;
  final DateTime createdAt;

  const Bookmark({
    required this.id,
    required this.surahName,
    required this.verseNumber,
    required this.verseText,
    this.note = '',
    this.tags = const [],
    required this.createdAt,
  });

  Bookmark copyWith({
    String? id,
    String? surahName,
    int? verseNumber,
    String? verseText,
    String? note,
    List<String>? tags,
    DateTime? createdAt,
  }) {
    return Bookmark(
      id: id ?? this.id,
      surahName: surahName ?? this.surahName,
      verseNumber: verseNumber ?? this.verseNumber,
      verseText: verseText ?? this.verseText,
      note: note ?? this.note,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'surahName': surahName,
      'verseNumber': verseNumber,
      'verseText': verseText,
      'note': note,
      'tags': tags,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory Bookmark.fromJson(Map<String, dynamic> json) {
    return Bookmark(
      id: json['id'],
      surahName: json['surahName'],
      verseNumber: json['verseNumber'],
      verseText: json['verseText'],
      note: json['note'] ?? '',
      tags: List<String>.from(json['tags'] ?? []),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Memorized verse model
class MemorizedVerse {
  final String id;
  final String surahName;
  final int verseNumber;
  final String verseText;
  final DateTime memorizedAt;
  final double confidenceLevel;
  final int reviewCount;
  final DateTime lastReviewed;

  const MemorizedVerse({
    required this.id,
    required this.surahName,
    required this.verseNumber,
    required this.verseText,
    required this.memorizedAt,
    required this.confidenceLevel,
    required this.reviewCount,
    required this.lastReviewed,
  });

  MemorizedVerse copyWith({
    String? id,
    String? surahName,
    int? verseNumber,
    String? verseText,
    DateTime? memorizedAt,
    double? confidenceLevel,
    int? reviewCount,
    DateTime? lastReviewed,
  }) {
    return MemorizedVerse(
      id: id ?? this.id,
      surahName: surahName ?? this.surahName,
      verseNumber: verseNumber ?? this.verseNumber,
      verseText: verseText ?? this.verseText,
      memorizedAt: memorizedAt ?? this.memorizedAt,
      confidenceLevel: confidenceLevel ?? this.confidenceLevel,
      reviewCount: reviewCount ?? this.reviewCount,
      lastReviewed: lastReviewed ?? this.lastReviewed,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'surahName': surahName,
      'verseNumber': verseNumber,
      'verseText': verseText,
      'memorizedAt': memorizedAt.toIso8601String(),
      'confidenceLevel': confidenceLevel,
      'reviewCount': reviewCount,
      'lastReviewed': lastReviewed.toIso8601String(),
    };
  }

  factory MemorizedVerse.fromJson(Map<String, dynamic> json) {
    return MemorizedVerse(
      id: json['id'],
      surahName: json['surahName'],
      verseNumber: json['verseNumber'],
      verseText: json['verseText'],
      memorizedAt: DateTime.parse(json['memorizedAt']),
      confidenceLevel: json['confidenceLevel'].toDouble(),
      reviewCount: json['reviewCount'],
      lastReviewed: DateTime.parse(json['lastReviewed']),
    );
  }
}

/// User preferences for Quran reading
class UserPreferences {
  final ReadingGoal goal;
  final int targetDays;
  final int targetSurahs;
  final MemorizationLevel memorizationLevel;
  final String preferredTranslation;
  final bool enableArabicText;
  final bool enableTransliteration;
  final double fontSize;
  final String fontFamily;

  const UserPreferences({
    required this.goal,
    required this.targetDays,
    required this.targetSurahs,
    required this.memorizationLevel,
    required this.preferredTranslation,
    required this.enableArabicText,
    required this.enableTransliteration,
    required this.fontSize,
    required this.fontFamily,
  });

  UserPreferences copyWith({
    ReadingGoal? goal,
    int? targetDays,
    int? targetSurahs,
    MemorizationLevel? memorizationLevel,
    String? preferredTranslation,
    bool? enableArabicText,
    bool? enableTransliteration,
    double? fontSize,
    String? fontFamily,
  }) {
    return UserPreferences(
      goal: goal ?? this.goal,
      targetDays: targetDays ?? this.targetDays,
      targetSurahs: targetSurahs ?? this.targetSurahs,
      memorizationLevel: memorizationLevel ?? this.memorizationLevel,
      preferredTranslation: preferredTranslation ?? this.preferredTranslation,
      enableArabicText: enableArabicText ?? this.enableArabicText,
      enableTransliteration:
          enableTransliteration ?? this.enableTransliteration,
      fontSize: fontSize ?? this.fontSize,
      fontFamily: fontFamily ?? this.fontFamily,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'goal': goal.name,
      'targetDays': targetDays,
      'targetSurahs': targetSurahs,
      'memorizationLevel': memorizationLevel.name,
      'preferredTranslation': preferredTranslation,
      'enableArabicText': enableArabicText,
      'enableTransliteration': enableTransliteration,
      'fontSize': fontSize,
      'fontFamily': fontFamily,
    };
  }

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      goal: ReadingGoal.values.firstWhere((e) => e.name == json['goal']),
      targetDays: json['targetDays'],
      targetSurahs: json['targetSurahs'],
      memorizationLevel: MemorizationLevel.values.firstWhere(
        (e) => e.name == json['memorizationLevel'],
      ),
      preferredTranslation: json['preferredTranslation'],
      enableArabicText: json['enableArabicText'],
      enableTransliteration: json['enableTransliteration'],
      fontSize: json['fontSize'].toDouble(),
      fontFamily: json['fontFamily'],
    );
  }
}

/// Enums
enum ReadingGoal { completeQuran, memorization, understanding, reflection }

enum MemorizationLevel { beginner, intermediate, advanced }

/// Quran insight model for AI analysis
class QuranInsight {
  final String id;
  final InsightType type;
  final String title;
  final String description;
  final double confidence;
  final String? verseReference;
  final DateTime createdAt;

  const QuranInsight({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.confidence,
    this.verseReference,
    required this.createdAt,
  });

  QuranInsight copyWith({
    String? id,
    InsightType? type,
    String? title,
    String? description,
    double? confidence,
    String? verseReference,
    DateTime? createdAt,
  }) {
    return QuranInsight(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      confidence: confidence ?? this.confidence,
      verseReference: verseReference ?? this.verseReference,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'confidence': confidence,
      'verseReference': verseReference,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory QuranInsight.fromJson(Map<String, dynamic> json) {
    return QuranInsight(
      id: json['id'],
      type: InsightType.values.firstWhere((e) => e.name == json['type']),
      title: json['title'],
      description: json['description'],
      confidence: json['confidence'].toDouble(),
      verseReference: json['verseReference'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Insight type enum
enum InsightType { pattern, progress, recommendation, reflection }

/// Recitation recommendation model
class RecitationRecommendation {
  final String id;
  final RecommendationType type;
  final String title;
  final String description;
  final String? surahName;
  final int? verseNumber;
  final double confidence;
  final DateTime createdAt;

  const RecitationRecommendation({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    this.surahName,
    this.verseNumber,
    required this.confidence,
    required this.createdAt,
  });

  RecitationRecommendation copyWith({
    String? id,
    RecommendationType? type,
    String? title,
    String? description,
    String? surahName,
    int? verseNumber,
    double? confidence,
    DateTime? createdAt,
  }) {
    return RecitationRecommendation(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      surahName: surahName ?? this.surahName,
      verseNumber: verseNumber ?? this.verseNumber,
      confidence: confidence ?? this.confidence,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'surahName': surahName,
      'verseNumber': verseNumber,
      'confidence': confidence,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory RecitationRecommendation.fromJson(Map<String, dynamic> json) {
    return RecitationRecommendation(
      id: json['id'],
      type: RecommendationType.values.firstWhere((e) => e.name == json['type']),
      title: json['title'],
      description: json['description'],
      surahName: json['surahName'],
      verseNumber: json['verseNumber'],
      confidence: json['confidence'].toDouble(),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Recommendation type enum
enum RecommendationType { daily, review, memorization, reflection }

/// Study insight model
class StudyInsight {
  final String id;
  final InsightType type;
  final String title;
  final String description;
  final double confidence;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;

  const StudyInsight({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.confidence,
    this.metadata,
    required this.createdAt,
  });

  StudyInsight copyWith({
    String? id,
    InsightType? type,
    String? title,
    String? description,
    double? confidence,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
  }) {
    return StudyInsight(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      confidence: confidence ?? this.confidence,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'confidence': confidence,
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory StudyInsight.fromJson(Map<String, dynamic> json) {
    return StudyInsight(
      id: json['id'],
      type: InsightType.values.firstWhere((e) => e.name == json['type']),
      title: json['title'],
      description: json['description'],
      confidence: json['confidence'].toDouble(),
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'])
          : null,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}
