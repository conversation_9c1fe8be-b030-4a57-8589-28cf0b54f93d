import 'package:flutter/material.dart';

/// Visual formula builder service with drag-and-drop interface
class VisualFormulaBuilderService {
  static final List<FormulaComponent> _components = [];
  static final List<FormulaCategory> _categories = [];
  static bool _isInitialized = false;

  /// Initialize the visual formula builder
  static void initialize() {
    if (_isInitialized) return;

    _createFormulaCategories();
    _createFormulaComponents();
    _isInitialized = true;
  }

  /// Get all formula categories
  static List<FormulaCategory> get categories => List.unmodifiable(_categories);

  /// Get components by category
  static List<FormulaComponent> getComponentsByCategory(String categoryId) {
    return _components.where((c) => c.categoryId == categoryId).toList();
  }

  /// Get all components
  static List<FormulaComponent> get components =>
      List.unmodifiable(_components);

  /// Build formula from visual components
  static String buildFormula(List<FormulaNode> nodes) {
    if (nodes.isEmpty) return '';

    final buffer = StringBuffer();
    for (int i = 0; i < nodes.length; i++) {
      final node = nodes[i];
      buffer.write(_nodeToFormula(node));

      // Add operators between nodes
      if (i < nodes.length - 1 && node.operator != null) {
        buffer.write(' ${node.operator} ');
      }
    }

    return buffer.toString();
  }

  /// Parse formula into visual components
  static List<FormulaNode> parseFormula(String formula) {
    final nodes = <FormulaNode>[];

    // Simple parsing - in production would use proper parser
    final parts = formula.split(RegExp(r'[\+\-\*\/\(\)]'));
    for (final part in parts) {
      final trimmed = part.trim();
      if (trimmed.isNotEmpty) {
        nodes.add(
          FormulaNode(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            type: _detectNodeType(trimmed),
            value: trimmed,
            position: Offset(nodes.length * 120.0, 50),
          ),
        );
      }
    }

    return nodes;
  }

  /// Validate formula structure
  static FormulaValidationResult validateFormula(List<FormulaNode> nodes) {
    final errors = <String>[];
    final warnings = <String>[];

    if (nodes.isEmpty) {
      errors.add('Formula cannot be empty');
      return FormulaValidationResult(
        isValid: false,
        errors: errors,
        warnings: warnings,
      );
    }

    // Check for balanced parentheses
    int parenthesesCount = 0;
    for (final node in nodes) {
      if (node.value.contains('(')) {
        parenthesesCount += node.value.split('(').length - 1;
      }
      if (node.value.contains(')')) {
        parenthesesCount -= node.value.split(')').length - 1;
      }
    }

    if (parenthesesCount != 0) {
      errors.add('Unbalanced parentheses in formula');
    }

    // Check for valid function names
    for (final node in nodes) {
      if (node.type == FormulaNodeType.function) {
        final functionName = node.value.split('(')[0].toUpperCase();
        // Check if function exists in our implementation
        final supportedFunctions = [
          'SUM',
          'AVERAGE',
          'COUNT',
          'MAX',
          'MIN',
          'IF',
          'VLOOKUP',
          'HLOOKUP',
          'INDEX',
          'MATCH',
          'CONCATENATE',
          'LEFT',
          'RIGHT',
          'MID',
          'LEN',
          'UPPER',
          'LOWER',
          'TRIM',
          'SUBSTITUTE',
          'FIND',
          'SEARCH',
          'DATE',
          'TIME',
          'NOW',
          'TODAY',
          'YEAR',
          'MONTH',
          'DAY',
          'HOUR',
          'MINUTE',
          'SECOND',
          'WEEKDAY',
          'NETWORKDAYS',
          'ABS',
          'ROUND',
          'ROUNDUP',
          'ROUNDDOWN',
          'CEILING',
          'FLOOR',
          'MOD',
          'POWER',
          'SQRT',
          'EXP',
          'LN',
          'LOG',
          'LOG10',
          'SIN',
          'COS',
          'TAN',
          'ASIN',
          'ACOS',
          'ATAN',
          'PI',
          'RADIANS',
          'DEGREES',
        ];
        if (!supportedFunctions.contains(functionName)) {
          errors.add('Unknown function: $functionName');
        }
      }
    }

    // Check for proper operator usage
    for (int i = 0; i < nodes.length - 1; i++) {
      final current = nodes[i];
      final next = nodes[i + 1];

      if (current.type == FormulaNodeType.operator &&
          next.type == FormulaNodeType.operator) {
        warnings.add('Consecutive operators detected');
      }
    }

    return FormulaValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Get function suggestions based on input
  static List<FunctionSuggestion> getFunctionSuggestions(String input) {
    final suggestions = <FunctionSuggestion>[];
    final lowerInput = input.toLowerCase();

    for (final component in _components) {
      if (component.type == FormulaComponentType.function) {
        if (component.name.toLowerCase().contains(lowerInput) ||
            component.description.toLowerCase().contains(lowerInput)) {
          suggestions.add(
            FunctionSuggestion(
              name: component.name,
              description: component.description,
              syntax: component.syntax,
              example: component.example,
              category: component.categoryId,
            ),
          );
        }
      }
    }

    // Sort by relevance
    suggestions.sort((a, b) {
      final aStartsWith = a.name.toLowerCase().startsWith(lowerInput);
      final bStartsWith = b.name.toLowerCase().startsWith(lowerInput);

      if (aStartsWith && !bStartsWith) return -1;
      if (!aStartsWith && bStartsWith) return 1;

      return a.name.compareTo(b.name);
    });

    return suggestions.take(10).toList();
  }

  /// Create formula template
  static FormulaTemplate createTemplate(
    String name,
    String description,
    List<FormulaNode> nodes,
  ) {
    return FormulaTemplate(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      description: description,
      nodes: nodes,
      formula: buildFormula(nodes),
      createdAt: DateTime.now(),
    );
  }

  // Private methods
  static void _createFormulaCategories() {
    _categories.addAll([
      FormulaCategory(
        id: 'math',
        name: 'Mathematical',
        description: 'Basic mathematical operations and functions',
        icon: Icons.calculate,
        color: Colors.blue,
      ),
      FormulaCategory(
        id: 'statistical',
        name: 'Statistical',
        description: 'Statistical analysis and data functions',
        icon: Icons.analytics,
        color: Colors.green,
      ),
      FormulaCategory(
        id: 'logical',
        name: 'Logical',
        description: 'Logical operations and conditional functions',
        icon: Icons.psychology,
        color: Colors.purple,
      ),
      FormulaCategory(
        id: 'text',
        name: 'Text',
        description: 'Text manipulation and string functions',
        icon: Icons.text_fields,
        color: Colors.orange,
      ),
      FormulaCategory(
        id: 'datetime',
        name: 'Date & Time',
        description: 'Date and time manipulation functions',
        icon: Icons.access_time,
        color: Colors.teal,
      ),
      FormulaCategory(
        id: 'lookup',
        name: 'Lookup & Reference',
        description: 'Data lookup and reference functions',
        icon: Icons.search,
        color: Colors.indigo,
      ),
    ]);
  }

  static void _createFormulaComponents() {
    // Mathematical components
    _components.addAll([
      FormulaComponent(
        id: 'sum',
        name: 'SUM',
        description: 'Adds all numbers in a range',
        categoryId: 'math',
        type: FormulaComponentType.function,
        syntax: 'SUM(number1, [number2], ...)',
        example: 'SUM(A1:A10)',
        parameters: ['range'],
        returnType: 'number',
      ),
      FormulaComponent(
        id: 'average',
        name: 'AVERAGE',
        description: 'Returns the average of numbers',
        categoryId: 'math',
        type: FormulaComponentType.function,
        syntax: 'AVERAGE(number1, [number2], ...)',
        example: 'AVERAGE(A1:A10)',
        parameters: ['range'],
        returnType: 'number',
      ),
      FormulaComponent(
        id: 'count',
        name: 'COUNT',
        description: 'Counts the number of cells with numbers',
        categoryId: 'math',
        type: FormulaComponentType.function,
        syntax: 'COUNT(value1, [value2], ...)',
        example: 'COUNT(A1:A10)',
        parameters: ['range'],
        returnType: 'number',
      ),
      FormulaComponent(
        id: 'max',
        name: 'MAX',
        description: 'Returns the largest value',
        categoryId: 'math',
        type: FormulaComponentType.function,
        syntax: 'MAX(number1, [number2], ...)',
        example: 'MAX(A1:A10)',
        parameters: ['range'],
        returnType: 'number',
      ),
      FormulaComponent(
        id: 'min',
        name: 'MIN',
        description: 'Returns the smallest value',
        categoryId: 'math',
        type: FormulaComponentType.function,
        syntax: 'MIN(number1, [number2], ...)',
        example: 'MIN(A1:A10)',
        parameters: ['range'],
        returnType: 'number',
      ),

      // Statistical components
      FormulaComponent(
        id: 'median',
        name: 'MEDIAN',
        description: 'Returns the median value',
        categoryId: 'statistical',
        type: FormulaComponentType.function,
        syntax: 'MEDIAN(number1, [number2], ...)',
        example: 'MEDIAN(A1:A10)',
        parameters: ['range'],
        returnType: 'number',
      ),
      FormulaComponent(
        id: 'stdev',
        name: 'STDEV',
        description: 'Returns the standard deviation',
        categoryId: 'statistical',
        type: FormulaComponentType.function,
        syntax: 'STDEV(number1, [number2], ...)',
        example: 'STDEV(A1:A10)',
        parameters: ['range'],
        returnType: 'number',
      ),

      // Logical components
      FormulaComponent(
        id: 'if',
        name: 'IF',
        description: 'Returns one value if true, another if false',
        categoryId: 'logical',
        type: FormulaComponentType.function,
        syntax: 'IF(logical_test, value_if_true, value_if_false)',
        example: 'IF(A1>10, "High", "Low")',
        parameters: ['condition', 'true_value', 'false_value'],
        returnType: 'any',
      ),
      FormulaComponent(
        id: 'and',
        name: 'AND',
        description: 'Returns TRUE if all arguments are TRUE',
        categoryId: 'logical',
        type: FormulaComponentType.function,
        syntax: 'AND(logical1, [logical2], ...)',
        example: 'AND(A1>0, B1<100)',
        parameters: ['condition1', 'condition2'],
        returnType: 'boolean',
      ),
      FormulaComponent(
        id: 'or',
        name: 'OR',
        description: 'Returns TRUE if any argument is TRUE',
        categoryId: 'logical',
        type: FormulaComponentType.function,
        syntax: 'OR(logical1, [logical2], ...)',
        example: 'OR(A1>0, B1<100)',
        parameters: ['condition1', 'condition2'],
        returnType: 'boolean',
      ),

      // Text components
      FormulaComponent(
        id: 'concatenate',
        name: 'CONCATENATE',
        description: 'Joins text strings together',
        categoryId: 'text',
        type: FormulaComponentType.function,
        syntax: 'CONCATENATE(text1, [text2], ...)',
        example: 'CONCATENATE("Hello", " ", "World")',
        parameters: ['text1', 'text2'],
        returnType: 'text',
      ),
      FormulaComponent(
        id: 'left',
        name: 'LEFT',
        description: 'Returns leftmost characters',
        categoryId: 'text',
        type: FormulaComponentType.function,
        syntax: 'LEFT(text, [num_chars])',
        example: 'LEFT("Hello", 3)',
        parameters: ['text', 'length'],
        returnType: 'text',
      ),
      FormulaComponent(
        id: 'right',
        name: 'RIGHT',
        description: 'Returns rightmost characters',
        categoryId: 'text',
        type: FormulaComponentType.function,
        syntax: 'RIGHT(text, [num_chars])',
        example: 'RIGHT("Hello", 3)',
        parameters: ['text', 'length'],
        returnType: 'text',
      ),

      // Date & Time components
      FormulaComponent(
        id: 'now',
        name: 'NOW',
        description: 'Returns current date and time',
        categoryId: 'datetime',
        type: FormulaComponentType.function,
        syntax: 'NOW()',
        example: 'NOW()',
        parameters: [],
        returnType: 'datetime',
      ),
      FormulaComponent(
        id: 'today',
        name: 'TODAY',
        description: 'Returns current date',
        categoryId: 'datetime',
        type: FormulaComponentType.function,
        syntax: 'TODAY()',
        example: 'TODAY()',
        parameters: [],
        returnType: 'date',
      ),

      // Lookup components
      FormulaComponent(
        id: 'vlookup',
        name: 'VLOOKUP',
        description: 'Looks up a value in a table',
        categoryId: 'lookup',
        type: FormulaComponentType.function,
        syntax:
            'VLOOKUP(lookup_value, table_array, col_index_num, [range_lookup])',
        example: 'VLOOKUP(A1, B1:D10, 3, FALSE)',
        parameters: [
          'lookup_value',
          'table_array',
          'column_index',
          'exact_match',
        ],
        returnType: 'any',
      ),

      // Operators
      FormulaComponent(
        id: 'add',
        name: '+',
        description: 'Addition operator',
        categoryId: 'math',
        type: FormulaComponentType.operator,
        syntax: 'value1 + value2',
        example: 'A1 + B1',
        parameters: ['left', 'right'],
        returnType: 'number',
      ),
      FormulaComponent(
        id: 'subtract',
        name: '-',
        description: 'Subtraction operator',
        categoryId: 'math',
        type: FormulaComponentType.operator,
        syntax: 'value1 - value2',
        example: 'A1 - B1',
        parameters: ['left', 'right'],
        returnType: 'number',
      ),
      FormulaComponent(
        id: 'multiply',
        name: '*',
        description: 'Multiplication operator',
        categoryId: 'math',
        type: FormulaComponentType.operator,
        syntax: 'value1 * value2',
        example: 'A1 * B1',
        parameters: ['left', 'right'],
        returnType: 'number',
      ),
      FormulaComponent(
        id: 'divide',
        name: '/',
        description: 'Division operator',
        categoryId: 'math',
        type: FormulaComponentType.operator,
        syntax: 'value1 / value2',
        example: 'A1 / B1',
        parameters: ['left', 'right'],
        returnType: 'number',
      ),
    ]);
  }

  static String _nodeToFormula(FormulaNode node) {
    switch (node.type) {
      case FormulaNodeType.function:
        return node.value;
      case FormulaNodeType.operator:
        return node.value;
      case FormulaNodeType.value:
        return node.value;
      case FormulaNodeType.reference:
        return node.value;
    }
  }

  static FormulaNodeType _detectNodeType(String value) {
    if (value.contains('(') && value.contains(')')) {
      return FormulaNodeType.function;
    } else if ([
      '+',
      '-',
      '*',
      '/',
      '=',
      '>',
      '<',
      '>=',
      '<=',
      '<>',
    ].contains(value)) {
      return FormulaNodeType.operator;
    } else if (RegExp(r'^[A-Z]+\d+$').hasMatch(value)) {
      return FormulaNodeType.reference;
    } else {
      return FormulaNodeType.value;
    }
  }
}

/// Formula component data class
class FormulaComponent {
  final String id;
  final String name;
  final String description;
  final String categoryId;
  final FormulaComponentType type;
  final String syntax;
  final String example;
  final List<String> parameters;
  final String returnType;

  const FormulaComponent({
    required this.id,
    required this.name,
    required this.description,
    required this.categoryId,
    required this.type,
    required this.syntax,
    required this.example,
    required this.parameters,
    required this.returnType,
  });
}

/// Formula category data class
class FormulaCategory {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color color;

  const FormulaCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
  });
}

/// Formula node for visual builder
class FormulaNode {
  final String id;
  final FormulaNodeType type;
  final String value;
  final Offset position;
  final String? operator;
  final List<String> connections;

  FormulaNode({
    required this.id,
    required this.type,
    required this.value,
    required this.position,
    this.operator,
    this.connections = const [],
  });

  FormulaNode copyWith({
    String? id,
    FormulaNodeType? type,
    String? value,
    Offset? position,
    String? operator,
    List<String>? connections,
  }) {
    return FormulaNode(
      id: id ?? this.id,
      type: type ?? this.type,
      value: value ?? this.value,
      position: position ?? this.position,
      operator: operator ?? this.operator,
      connections: connections ?? this.connections,
    );
  }
}

/// Formula validation result
class FormulaValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const FormulaValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });
}

/// Function suggestion
class FunctionSuggestion {
  final String name;
  final String description;
  final String syntax;
  final String example;
  final String category;

  const FunctionSuggestion({
    required this.name,
    required this.description,
    required this.syntax,
    required this.example,
    required this.category,
  });
}

/// Formula template
class FormulaTemplate {
  final String id;
  final String name;
  final String description;
  final List<FormulaNode> nodes;
  final String formula;
  final DateTime createdAt;

  const FormulaTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.nodes,
    required this.formula,
    required this.createdAt,
  });
}

/// Enums
enum FormulaComponentType { function, operator, value, reference }

enum FormulaNodeType { function, operator, value, reference }
