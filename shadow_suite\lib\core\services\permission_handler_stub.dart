/// Stub implementation for permission_handler when not available
/// This allows the app to compile on platforms where permission_handler is not supported
library;

enum Permission {
  storage,
  camera,
  microphone,
  location,
  notification,
  photos,
  videos,
  audio,
  manageExternalStorage,
}

enum PermissionStatus {
  denied,
  granted,
  restricted,
  limited,
  permanentlyDenied,
}

extension PermissionStatusExtension on PermissionStatus {
  bool get isGranted => this == PermissionStatus.granted;
  bool get isDenied => this == PermissionStatus.denied;
  bool get isPermanentlyDenied => this == PermissionStatus.permanentlyDenied;
}

extension PermissionExtension on Permission {
  Future<PermissionStatus> get status async {
    // On desktop platforms, always return granted
    return PermissionStatus.granted;
  }

  Future<bool> get isGranted async {
    // On desktop platforms, always return true
    return true;
  }

  Future<PermissionStatus> request() async {
    // On desktop platforms, always return granted
    return PermissionStatus.granted;
  }
}

extension PermissionListExtension on List<Permission> {
  Future<Map<Permission, PermissionStatus>> request() async {
    // On desktop platforms, always return granted for all permissions
    final result = <Permission, PermissionStatus>{};
    for (final permission in this) {
      result[permission] = PermissionStatus.granted;
    }
    return result;
  }
}

Future<void> openAppSettings() async {
  // Stub implementation - no-op on desktop
}

class PermissionHandlerPlatform {
  static Future<void> openAppSettings() async {
    // Stub implementation - no-op on desktop
  }
}
