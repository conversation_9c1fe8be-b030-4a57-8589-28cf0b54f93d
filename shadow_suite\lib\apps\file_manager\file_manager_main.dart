import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'services/file_manager_service.dart';
import 'widgets/dual_pane_explorer.dart';
import 'widgets/file_manager_toolbar.dart';
import 'widgets/file_manager_navigation_bar.dart';
import 'widgets/file_manager_status_bar.dart';
import 'widgets/file_manager_dashboard_view.dart';
import 'screens/cloud_storage_screen.dart';
import 'screens/network_shares_screen.dart';
import 'screens/media_player_screen.dart';
import 'screens/file_operations_screen.dart';

// File Manager View Modes
enum FileManagerViewMode {
  dualPane,
  singlePane,
  dashboard,
  cloudStorage,
  networkShares,
  mediaPlayer,
  fileOperations,
}

// Current view mode provider
final currentFileManagerViewModeProvider = StateProvider<FileManagerViewMode>(
  (ref) => FileManagerViewMode.dualPane,
);

// Left and right pane paths
final leftPanePathProvider = StateProvider<String>(
  (ref) => '/storage/emulated/0',
);
final rightPanePathProvider = StateProvider<String>(
  (ref) => '/storage/emulated/0/Documents',
);

// Active pane (left or right)
final activePaneProvider = StateProvider<String>((ref) => 'left');

class FileManagerMain extends ConsumerStatefulWidget {
  const FileManagerMain({super.key});

  @override
  ConsumerState<FileManagerMain> createState() => _FileManagerMainState();
}

class _FileManagerMainState extends ConsumerState<FileManagerMain> {
  @override
  void initState() {
    super.initState();
    // Initialize File Manager service
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FileManagerService.initialize();
      _updateViewModeFromRoute();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateViewModeFromRoute();
  }

  void _updateViewModeFromRoute() {
    try {
      // Try to get the router state, but handle cases where it's not available
      final routerState = GoRouterState.of(context);
      final location = routerState.uri.toString();
      FileManagerViewMode newMode = FileManagerViewMode.dualPane;

      if (location.contains('/file-manager/browser')) {
        newMode = FileManagerViewMode.dualPane;
      } else if (location.contains('/file-manager/network')) {
        newMode = FileManagerViewMode.networkShares;
      } else if (location.contains('/file-manager/ftp-server')) {
        newMode = FileManagerViewMode.networkShares;
      } else if (location.contains('/file-manager/ftp-client')) {
        newMode = FileManagerViewMode.networkShares;
      } else if (location.contains('/file-manager/lan-discovery')) {
        newMode = FileManagerViewMode.networkShares;
      } else if (location.contains('/file-manager/wifi-share')) {
        newMode = FileManagerViewMode.networkShares;
      } else if (location == '/file-manager') {
        newMode = FileManagerViewMode.dashboard;
      }

      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(currentFileManagerViewModeProvider.notifier).state = newMode;
      });
    } catch (e) {
      // If GoRouter context is not available, use default mode
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(currentFileManagerViewModeProvider.notifier).state =
            FileManagerViewMode.dualPane;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final viewMode = ref.watch(currentFileManagerViewModeProvider);

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: Column(
        children: [
          // Navigation Bar (breadcrumb, back/forward buttons, address bar)
          const FileManagerNavigationBar(),
          // Toolbar (file operations)
          const FileManagerToolbar(),
          // Main Content Area
          Expanded(child: _buildMainContent(viewMode, ref)),
          // Status Bar
          const FileManagerStatusBar(),
        ],
      ),
    );
  }

  Widget _buildMainContent(FileManagerViewMode viewMode, WidgetRef ref) {
    switch (viewMode) {
      case FileManagerViewMode.dualPane:
        return const DualPaneExplorer();
      case FileManagerViewMode.singlePane:
        return const DualPaneExplorer(singlePane: true);
      case FileManagerViewMode.dashboard:
        return const FileManagerDashboardView();
      case FileManagerViewMode.cloudStorage:
        return const CloudStorageScreen();
      case FileManagerViewMode.networkShares:
        return const NetworkSharesScreen();
      case FileManagerViewMode.mediaPlayer:
        return const MediaPlayerScreen();
      case FileManagerViewMode.fileOperations:
        return const FileOperationsScreen();
    }
  }
}

// Navigation helper widget for consistent header
class FileManagerHeader extends StatelessWidget {
  final String title;
  final String subtitle;
  final List<Widget>? actions;
  final Widget? leading;

  const FileManagerHeader({
    super.key,
    required this.title,
    required this.subtitle,
    this.actions,
    this.leading,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(color: Colors.black12, blurRadius: 4, offset: Offset(0, 2)),
        ],
      ),
      child: Row(
        children: [
          if (leading != null) ...[leading!, const SizedBox(width: 16)],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF7F8C8D),
                  ),
                ),
              ],
            ),
          ),
          if (actions != null) ...actions!,
        ],
      ),
    );
  }
}

// Quick action button widget
class FileManagerQuickAction extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onTap;
  final Color? color;

  const FileManagerQuickAction({
    super.key,
    required this.icon,
    required this.label,
    required this.onTap,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: const [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 4,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 32, color: color ?? const Color(0xFFE67E22)),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Color(0xFF2C3E50),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

// File item widget for lists
class FileItemWidget extends StatelessWidget {
  final String name;
  final String? subtitle;
  final IconData icon;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final List<Widget>? actions;
  final bool isSelected;
  final bool showCheckbox;

  const FileItemWidget({
    super.key,
    required this.name,
    this.subtitle,
    required this.icon,
    this.onTap,
    this.onLongPress,
    this.actions,
    this.isSelected = false,
    this.showCheckbox = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: isSelected
            ? const Color(0xFFE67E22).withValues(alpha: 0.1)
            : Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: isSelected
            ? Border.all(color: const Color(0xFFE67E22), width: 2)
            : null,
        boxShadow: const [
          BoxShadow(color: Colors.black12, blurRadius: 2, offset: Offset(0, 1)),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              if (showCheckbox)
                Checkbox(
                  value: isSelected,
                  onChanged: (_) => onTap?.call(),
                  activeColor: const Color(0xFFE67E22),
                )
              else
                Icon(icon, color: const Color(0xFFE67E22)),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF2C3E50),
                      ),
                    ),
                    if (subtitle != null)
                      Text(
                        subtitle!,
                        style: const TextStyle(
                          color: Color(0xFF7F8C8D),
                          fontSize: 12,
                        ),
                      ),
                  ],
                ),
              ),
              if (actions != null && !showCheckbox)
                Row(mainAxisSize: MainAxisSize.min, children: actions!),
            ],
          ),
        ),
      ),
    );
  }
}
