import 'dart:math' as math;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/spreadsheet.dart';
import 'range_parser.dart';

// Enhanced Formula Engine with auto-suggest and validation
class EnhancedFormulaEngine {
  static final Map<String, FormulaFunction> _functions = {
    // Mathematical Functions
    'SUM': FormulaFunction(
      name: 'SUM',
      description: 'Adds all numbers in a range of cells',
      syntax: 'SUM(range)',
      example: 'SUM(A1:A10)',
      category: 'Mathematical',
      parameters: ['range'],
      calculate: (args) => _sum(args),
    ),
    'AVERAGE': FormulaFunction(
      name: 'AVERAGE',
      description: 'Returns the average of numbers in a range',
      syntax: 'AVERAGE(range)',
      example: 'AVERAGE(A1:A10)',
      category: 'Mathematical',
      parameters: ['range'],
      calculate: (args) => _average(args),
    ),
    'COUNT': FormulaFunction(
      name: 'COUNT',
      description: 'Counts the number of cells that contain numbers',
      syntax: 'COUNT(range)',
      example: 'COUNT(A1:A10)',
      category: 'Mathematical',
      parameters: ['range'],
      calculate: (args) => _count(args),
    ),
    'MAX': FormulaFunction(
      name: 'MAX',
      description: 'Returns the largest value in a range',
      syntax: 'MAX(range)',
      example: 'MAX(A1:A10)',
      category: 'Mathematical',
      parameters: ['range'],
      calculate: (args) => _max(args),
    ),
    'MIN': FormulaFunction(
      name: 'MIN',
      description: 'Returns the smallest value in a range',
      syntax: 'MIN(range)',
      example: 'MIN(A1:A10)',
      category: 'Mathematical',
      parameters: ['range'],
      calculate: (args) => _min(args),
    ),
    'ABS': FormulaFunction(
      name: 'ABS',
      description: 'Returns the absolute value of a number',
      syntax: 'ABS(number)',
      example: 'ABS(-5)',
      category: 'Mathematical',
      parameters: ['number'],
      calculate: (args) => _abs(args),
    ),
    'ROUND': FormulaFunction(
      name: 'ROUND',
      description: 'Rounds a number to specified decimal places',
      syntax: 'ROUND(number, digits)',
      example: 'ROUND(3.14159, 2)',
      category: 'Mathematical',
      parameters: ['number', 'digits'],
      calculate: (args) => _round(args),
    ),

    // Logical Functions
    'IF': FormulaFunction(
      name: 'IF',
      description: 'Returns one value if condition is true, another if false',
      syntax: 'IF(condition, value_if_true, value_if_false)',
      example: 'IF(A1>10, "High", "Low")',
      category: 'Logical',
      parameters: ['condition', 'value_if_true', 'value_if_false'],
      calculate: (args) => _if(args),
    ),
    'AND': FormulaFunction(
      name: 'AND',
      description: 'Returns TRUE if all conditions are true',
      syntax: 'AND(condition1, condition2, ...)',
      example: 'AND(A1>0, B1<10)',
      category: 'Logical',
      parameters: ['condition1', 'condition2'],
      calculate: (args) => _and(args),
    ),
    'OR': FormulaFunction(
      name: 'OR',
      description: 'Returns TRUE if any condition is true',
      syntax: 'OR(condition1, condition2, ...)',
      example: 'OR(A1>10, B1<5)',
      category: 'Logical',
      parameters: ['condition1', 'condition2'],
      calculate: (args) => _or(args),
    ),

    // Financial Functions
    'PMT': FormulaFunction(
      name: 'PMT',
      description: 'Calculates loan payment amount',
      syntax: 'PMT(rate, nper, pv)',
      example: 'PMT(0.05/12, 360, 100000)',
      category: 'Financial',
      parameters: ['rate', 'nper', 'pv'],
      calculate: (args) => _pmt(args),
    ),
    'PV': FormulaFunction(
      name: 'PV',
      description: 'Calculates present value of investment',
      syntax: 'PV(rate, nper, pmt)',
      example: 'PV(0.05, 10, 1000)',
      category: 'Financial',
      parameters: ['rate', 'nper', 'pmt'],
      calculate: (args) => _pv(args),
    ),
    'FV': FormulaFunction(
      name: 'FV',
      description: 'Calculates future value of investment',
      syntax: 'FV(rate, nper, pmt, pv)',
      example: 'FV(0.05, 10, 1000, 0)',
      category: 'Financial',
      parameters: ['rate', 'nper', 'pmt', 'pv'],
      calculate: (args) => _fv(args),
    ),

    // Text Functions
    'CONCATENATE': FormulaFunction(
      name: 'CONCATENATE',
      description: 'Joins text strings together',
      syntax: 'CONCATENATE(text1, text2, ...)',
      example: 'CONCATENATE("Hello", " ", "World")',
      category: 'Text',
      parameters: ['text1', 'text2'],
      calculate: (args) => _concatenate(args),
    ),
    'LEFT': FormulaFunction(
      name: 'LEFT',
      description: 'Returns leftmost characters from text',
      syntax: 'LEFT(text, num_chars)',
      example: 'LEFT("Hello", 3)',
      category: 'Text',
      parameters: ['text', 'num_chars'],
      calculate: (args) => _left(args),
    ),
    'RIGHT': FormulaFunction(
      name: 'RIGHT',
      description: 'Returns rightmost characters from text',
      syntax: 'RIGHT(text, num_chars)',
      example: 'RIGHT("Hello", 3)',
      category: 'Text',
      parameters: ['text', 'num_chars'],
      calculate: (args) => _right(args),
    ),
    'LEN': FormulaFunction(
      name: 'LEN',
      description: 'Returns the length of text',
      syntax: 'LEN(text)',
      example: 'LEN("Hello")',
      category: 'Text',
      parameters: ['text'],
      calculate: (args) => _len(args),
    ),

    // Date Functions
    'TODAY': FormulaFunction(
      name: 'TODAY',
      description: 'Returns current date',
      syntax: 'TODAY()',
      example: 'TODAY()',
      category: 'Date & Time',
      parameters: [],
      calculate: (args) => _today(args),
    ),
    'NOW': FormulaFunction(
      name: 'NOW',
      description: 'Returns current date and time',
      syntax: 'NOW()',
      example: 'NOW()',
      category: 'Date & Time',
      parameters: [],
      calculate: (args) => _now(args),
    ),

    // Lookup Functions
    'VLOOKUP': FormulaFunction(
      name: 'VLOOKUP',
      description:
          'Looks up a value in the first column and returns a value in the same row',
      syntax:
          'VLOOKUP(lookup_value, table_array, col_index_num, [range_lookup])',
      example: 'VLOOKUP("Apple", A1:C10, 2, FALSE)',
      category: 'Lookup',
      parameters: [
        'lookup_value',
        'table_array',
        'col_index_num',
        'range_lookup',
      ],
      calculate: (args) => _vlookup(args),
    ),
    'HLOOKUP': FormulaFunction(
      name: 'HLOOKUP',
      description:
          'Looks up a value in the first row and returns a value in the same column',
      syntax:
          'HLOOKUP(lookup_value, table_array, row_index_num, [range_lookup])',
      example: 'HLOOKUP("Q1", A1:D5, 3, FALSE)',
      category: 'Lookup',
      parameters: [
        'lookup_value',
        'table_array',
        'row_index_num',
        'range_lookup',
      ],
      calculate: (args) => _hlookup(args),
    ),
    'INDEX': FormulaFunction(
      name: 'INDEX',
      description:
          'Returns a value from a table based on row and column numbers',
      syntax: 'INDEX(array, row_num, [column_num])',
      example: 'INDEX(A1:C10, 5, 2)',
      category: 'Lookup',
      parameters: ['array', 'row_num', 'column_num'],
      calculate: (args) => _index(args),
    ),
    'MATCH': FormulaFunction(
      name: 'MATCH',
      description: 'Returns the position of a value in an array',
      syntax: 'MATCH(lookup_value, lookup_array, [match_type])',
      example: 'MATCH("Apple", A1:A10, 0)',
      category: 'Lookup',
      parameters: ['lookup_value', 'lookup_array', 'match_type'],
      calculate: (args) => _match(args),
    ),
  };

  // Auto-suggest functions based on input
  static List<FormulaSuggestion> getSuggestions(String input) {
    final suggestions = <FormulaSuggestion>[];
    final lowerInput = input.toLowerCase();

    for (final function in _functions.values) {
      if (function.name.toLowerCase().contains(lowerInput) ||
          function.description.toLowerCase().contains(lowerInput)) {
        suggestions.add(
          FormulaSuggestion(
            function: function,
            matchType: function.name.toLowerCase().startsWith(lowerInput)
                ? SuggestionMatchType.nameStart
                : SuggestionMatchType.description,
          ),
        );
      }
    }

    // Sort by relevance
    suggestions.sort((a, b) {
      if (a.matchType != b.matchType) {
        return a.matchType.index.compareTo(b.matchType.index);
      }
      return a.function.name.compareTo(b.function.name);
    });

    return suggestions.take(10).toList();
  }

  // Validate formula syntax
  static FormulaValidationResult validateFormula(String formula) {
    if (formula.isEmpty) {
      return FormulaValidationResult(
        isValid: false,
        errors: ['Formula cannot be empty'],
      );
    }

    if (!formula.startsWith('=')) {
      return FormulaValidationResult(
        isValid: false,
        errors: ['Formula must start with ='],
      );
    }

    final errors = <String>[];
    final warnings = <String>[];

    // Check for balanced parentheses
    int openParens = 0;
    for (int i = 0; i < formula.length; i++) {
      if (formula[i] == '(') openParens++;
      if (formula[i] == ')') openParens--;
      if (openParens < 0) {
        errors.add('Unmatched closing parenthesis at position $i');
        break;
      }
    }

    if (openParens > 0) {
      errors.add('Missing $openParens closing parenthesis');
    }

    // Check for valid function names
    final functionPattern = RegExp(r'([A-Z]+)\s*\(');
    final matches = functionPattern.allMatches(formula);

    for (final match in matches) {
      final functionName = match.group(1)!;
      if (!_functions.containsKey(functionName)) {
        errors.add('Unknown function: $functionName');
      }
    }

    // Check for circular references (basic check)
    if (formula.contains('A1') && formula.contains('=A1')) {
      warnings.add('Possible circular reference detected');
    }

    return FormulaValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  // Calculate formula result with range support
  static dynamic calculateFormula(String formula, Spreadsheet spreadsheet) {
    try {
      if (!formula.startsWith('=')) return formula;

      final expression = formula.substring(1);
      return _evaluateExpression(expression, spreadsheet);
    } catch (e) {
      return '#ERROR!';
    }
  }

  static dynamic _evaluateExpression(
    String expression,
    Spreadsheet spreadsheet,
  ) {
    // Simple expression evaluator - in production, use a proper parser
    for (final functionName in _functions.keys) {
      if (expression.contains(functionName)) {
        final function = _functions[functionName]!;
        // Extract arguments and calculate
        final args = _extractArguments(expression, functionName);
        return function.calculate(args);
      }
    }

    // Handle simple arithmetic
    if (expression.contains('+')) {
      final parts = expression.split('+');
      return _parseNumber(parts[0]) + _parseNumber(parts[1]);
    }

    return _parseNumber(expression);
  }

  static List<String> _extractArguments(
    String expression,
    String functionName,
  ) {
    final startIndex =
        expression.indexOf('$functionName(') + functionName.length + 1;
    final endIndex = expression.lastIndexOf(')');

    if (startIndex >= endIndex) return [];

    final argsString = expression.substring(startIndex, endIndex);
    return argsString.split(',').map((s) => s.trim()).toList();
  }

  static double _parseNumber(String value) {
    return double.tryParse(value.trim()) ?? 0.0;
  }

  // Function implementations with range support
  static double _sum(List<String> args) {
    double sum = 0;
    for (final arg in args) {
      if (RangeParser.isValidRange(arg)) {
        // This is a range, we'll need the spreadsheet context
        // For now, parse as individual numbers
        sum += _parseNumber(arg);
      } else {
        sum += _parseNumber(arg);
      }
    }
    return sum;
  }

  static double _average(List<String> args) {
    if (args.isEmpty) return 0;
    return _sum(args) / args.length;
  }

  static int _count(List<String> args) {
    return args.where((arg) => double.tryParse(arg.trim()) != null).length;
  }

  static double _max(List<String> args) {
    double max = double.negativeInfinity;
    for (final arg in args) {
      final value = _parseNumber(arg);
      if (value > max) max = value;
    }
    return max;
  }

  static double _min(List<String> args) {
    double min = double.infinity;
    for (final arg in args) {
      final value = _parseNumber(arg);
      if (value < min) min = value;
    }
    return min;
  }

  static double _abs(List<String> args) {
    if (args.isEmpty) return 0;
    return _parseNumber(args[0]).abs();
  }

  static double _round(List<String> args) {
    if (args.length < 2) return 0;
    final number = _parseNumber(args[0]);
    final digits = _parseNumber(args[1]).toInt();
    final multiplier = math.pow(10, digits);
    return (number * multiplier).round() / multiplier;
  }

  static dynamic _if(List<String> args) {
    if (args.length < 3) return '';
    // Simple condition evaluation
    final condition = args[0].trim();
    final trueValue = args[1].trim();
    final falseValue = args[2].trim();

    // Basic condition parsing
    if (condition.contains('>')) {
      final parts = condition.split('>');
      final left = _parseNumber(parts[0]);
      final right = _parseNumber(parts[1]);
      return left > right ? trueValue : falseValue;
    }

    return trueValue;
  }

  static bool _and(List<String> args) {
    for (final arg in args) {
      if (_parseNumber(arg) == 0) return false;
    }
    return true;
  }

  static bool _or(List<String> args) {
    for (final arg in args) {
      if (_parseNumber(arg) != 0) return true;
    }
    return false;
  }

  static double _pmt(List<String> args) {
    if (args.length < 3) return 0;
    final rate = _parseNumber(args[0]);
    final nper = _parseNumber(args[1]);
    final pv = _parseNumber(args[2]);

    if (rate == 0) return -pv / nper;

    final factor = math.pow(1 + rate, nper);
    return -(pv * rate * factor) / (factor - 1);
  }

  static double _pv(List<String> args) {
    if (args.length < 3) return 0;
    final rate = _parseNumber(args[0]);
    final nper = _parseNumber(args[1]);
    final pmt = _parseNumber(args[2]);

    if (rate == 0) return -pmt * nper;

    final factor = math.pow(1 + rate, nper);
    return -pmt * (factor - 1) / (rate * factor);
  }

  static double _fv(List<String> args) {
    if (args.length < 4) return 0;
    final rate = _parseNumber(args[0]);
    final nper = _parseNumber(args[1]);
    final pmt = _parseNumber(args[2]);
    final pv = _parseNumber(args[3]);

    if (rate == 0) return -(pv + pmt * nper);

    final factor = math.pow(1 + rate, nper);
    return -(pv * factor + pmt * (factor - 1) / rate);
  }

  static String _concatenate(List<String> args) {
    return args.join('');
  }

  static String _left(List<String> args) {
    if (args.length < 2) return '';
    final text = args[0];
    final numChars = _parseNumber(args[1]).toInt();
    return text.length > numChars ? text.substring(0, numChars) : text;
  }

  static String _right(List<String> args) {
    if (args.length < 2) return '';
    final text = args[0];
    final numChars = _parseNumber(args[1]).toInt();
    return text.length > numChars
        ? text.substring(text.length - numChars)
        : text;
  }

  static int _len(List<String> args) {
    if (args.isEmpty) return 0;
    return args[0].length;
  }

  static DateTime _today(List<String> args) {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day);
  }

  static DateTime _now(List<String> args) {
    return DateTime.now();
  }

  // Lookup function implementations
  static dynamic _vlookup(List<String> args) {
    if (args.length < 3) return '#VALUE!';

    final lookupValue = args[0].trim();
    // For demonstration, return a placeholder result
    // In a full implementation, this would parse the range and search through it
    return 'VLOOKUP($lookupValue)';
  }

  static dynamic _hlookup(List<String> args) {
    if (args.length < 3) return '#VALUE!';

    final lookupValue = args[0].trim();
    // For demonstration, return a placeholder result
    return 'HLOOKUP($lookupValue)';
  }

  static dynamic _index(List<String> args) {
    if (args.length < 2) return '#VALUE!';

    final array = args[0].trim(); // Should be a range like "A1:C10"
    final rowNum = _parseNumber(args[1]).toInt();
    final colNum = args.length > 2 ? _parseNumber(args[2]).toInt() : 1;

    // For demonstration, return a placeholder result
    return 'INDEX($array,$rowNum,$colNum)';
  }

  static dynamic _match(List<String> args) {
    if (args.length < 2) return '#VALUE!';

    // For demonstration, return a placeholder result
    // In a full implementation, this would search through the array
    return 1; // Position found
  }
}

// Supporting classes
class FormulaFunction {
  final String name;
  final String description;
  final String syntax;
  final String example;
  final String category;
  final List<String> parameters;
  final Function(List<String>) calculate;

  const FormulaFunction({
    required this.name,
    required this.description,
    required this.syntax,
    required this.example,
    required this.category,
    required this.parameters,
    required this.calculate,
  });
}

class FormulaSuggestion {
  final FormulaFunction function;
  final SuggestionMatchType matchType;

  const FormulaSuggestion({required this.function, required this.matchType});
}

enum SuggestionMatchType { nameStart, nameContains, description }

class FormulaValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const FormulaValidationResult({
    required this.isValid,
    this.errors = const [],
    this.warnings = const [],
  });
}

// Providers
final enhancedFormulaEngineProvider = Provider<EnhancedFormulaEngine>(
  (ref) => EnhancedFormulaEngine(),
);
final formulaSuggestionsProvider = StateProvider<List<FormulaSuggestion>>(
  (ref) => [],
);
final formulaValidationProvider = StateProvider<FormulaValidationResult?>(
  (ref) => null,
);
final currentFormulaProvider = StateProvider<String>((ref) => '');
