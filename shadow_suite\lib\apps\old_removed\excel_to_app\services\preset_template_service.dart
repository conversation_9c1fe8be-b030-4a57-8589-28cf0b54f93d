import 'dart:convert';
import 'package:flutter/material.dart';
import 'advanced_layout_service.dart';
import 'settings_persistence_service.dart';

/// Advanced preset template service with comprehensive templates and management
class PresetTemplateService {
  static final List<PresetTemplate> _templates = [];
  static final List<PresetCategory> _categories = [];
  static bool _isInitialized = false;

  /// Initialize the preset template service
  static void initialize() {
    if (_isInitialized) return;

    _createCategories();
    _createBuiltInTemplates();
    _isInitialized = true;
  }

  /// Get all preset templates
  static List<PresetTemplate> get templates => List.unmodifiable(_templates);

  /// Get templates by category
  static List<PresetTemplate> getTemplatesByCategory(String categoryId) {
    return _templates.where((t) => t.categoryId == categoryId).toList();
  }

  /// Get all categories
  static List<PresetCategory> get categories => List.unmodifiable(_categories);

  /// Create a new template from current configuration
  static PresetTemplate createTemplate({
    required String name,
    required String description,
    required String categoryId,
    required LayoutConfiguration configuration,
    List<String> tags = const [],
    String? author,
  }) {
    final template = PresetTemplate(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      description: description,
      categoryId: categoryId,
      configuration: configuration,
      tags: tags,
      author: author ?? 'User',
      createdAt: DateTime.now(),
      isBuiltIn: false,
    );

    _templates.add(template);
    return template;
  }

  /// Export template to JSON
  static String exportTemplate(PresetTemplate template) {
    return jsonEncode(template.toJson());
  }

  /// Export multiple templates to JSON
  static String exportTemplates(List<PresetTemplate> templates) {
    final data = {
      'version': '1.0',
      'timestamp': DateTime.now().toIso8601String(),
      'templates': templates.map((t) => t.toJson()).toList(),
    };
    return jsonEncode(data);
  }

  /// Import template from JSON
  static PresetTemplate? importTemplate(String jsonString) {
    try {
      final data = jsonDecode(jsonString) as Map<String, dynamic>;
      final template = PresetTemplate.fromJson(data);

      // Check if template already exists
      final existingIndex = _templates.indexWhere((t) => t.id == template.id);
      if (existingIndex != -1) {
        _templates[existingIndex] = template;
      } else {
        _templates.add(template);
      }

      return template;
    } catch (e) {
      return null;
    }
  }

  /// Import multiple templates from JSON
  static List<PresetTemplate> importTemplates(String jsonString) {
    try {
      final data = jsonDecode(jsonString) as Map<String, dynamic>;
      final templatesList = data['templates'] as List<dynamic>;

      final importedTemplates = <PresetTemplate>[];

      for (final templateData in templatesList) {
        final template = PresetTemplate.fromJson(
          templateData as Map<String, dynamic>,
        );

        // Check if template already exists
        final existingIndex = _templates.indexWhere((t) => t.id == template.id);
        if (existingIndex != -1) {
          _templates[existingIndex] = template;
        } else {
          _templates.add(template);
        }

        importedTemplates.add(template);
      }

      return importedTemplates;
    } catch (e) {
      return [];
    }
  }

  /// Delete a template
  static bool deleteTemplate(String templateId) {
    final index = _templates.indexWhere((t) => t.id == templateId);
    if (index != -1 && !_templates[index].isBuiltIn) {
      _templates.removeAt(index);
      return true;
    }
    return false;
  }

  /// Search templates by name or tags
  static List<PresetTemplate> searchTemplates(String query) {
    final lowerQuery = query.toLowerCase();
    return _templates.where((template) {
      return template.name.toLowerCase().contains(lowerQuery) ||
          template.description.toLowerCase().contains(lowerQuery) ||
          template.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  /// Get featured templates
  static List<PresetTemplate> getFeaturedTemplates() {
    return _templates.where((t) => t.isFeatured).toList();
  }

  /// Apply template to current configuration
  static void applyTemplate(PresetTemplate template) {
    AdvancedLayoutService.updateConfig(template.configuration);
    SettingsPersistenceService.saveLayoutConfiguration(template.configuration);
  }

  /// Create categories
  static void _createCategories() {
    _categories.addAll([
      PresetCategory(
        id: 'business',
        name: 'Business & Professional',
        description:
            'Templates for business applications and professional tools',
        icon: Icons.business,
        color: Colors.blue,
      ),
      PresetCategory(
        id: 'creative',
        name: 'Creative & Design',
        description: 'Templates for creative applications and design tools',
        icon: Icons.palette,
        color: Colors.purple,
      ),
      PresetCategory(
        id: 'data',
        name: 'Data & Analytics',
        description: 'Templates for data visualization and analytics',
        icon: Icons.analytics,
        color: Colors.green,
      ),
      PresetCategory(
        id: 'mobile',
        name: 'Mobile Optimized',
        description: 'Templates optimized for mobile devices',
        icon: Icons.phone_android,
        color: Colors.orange,
      ),
      PresetCategory(
        id: 'desktop',
        name: 'Desktop Applications',
        description: 'Templates for desktop applications',
        icon: Icons.desktop_windows,
        color: Colors.indigo,
      ),
      PresetCategory(
        id: 'minimal',
        name: 'Minimal & Clean',
        description: 'Clean, minimal design templates',
        icon: Icons.minimize,
        color: Colors.grey,
      ),
    ]);
  }

  /// Create built-in templates
  static void _createBuiltInTemplates() {
    // Business & Professional Templates
    _templates.addAll([
      PresetTemplate(
        id: 'business_dashboard',
        name: 'Executive Dashboard',
        description:
            'Professional dashboard for business executives with sidebar navigation and data cards',
        categoryId: 'business',
        configuration: const LayoutConfiguration(
          navigationSystem: NavigationSystem.sidebar,
          sidebarPosition: SidebarPosition.left,
          sidebarCollapsible: true,
          colorScheme: AppColorScheme.light,
          gridLayout: GridLayoutType.responsive,
          gridColumns: 3,
          fontFamily: 'Roboto',
          baseFontSize: 14,
          showShadows: true,
          borderRadius: 8,
        ),
        tags: ['business', 'dashboard', 'professional', 'sidebar'],
        author: 'Shadow Suite',
        isBuiltIn: true,
        isFeatured: true,
      ),

      PresetTemplate(
        id: 'financial_app',
        name: 'Financial Application',
        description:
            'Clean layout for financial applications with top navigation and data tables',
        categoryId: 'business',
        configuration: const LayoutConfiguration(
          navigationSystem: NavigationSystem.topTabs,
          colorScheme: AppColorScheme.light,
          gridLayout: GridLayoutType.grid,
          gridColumns: 2,
          fontFamily: 'Arial',
          baseFontSize: 13,
          showShadows: false,
          borderRadius: 4,
          componentPadding: 12,
        ),
        tags: ['financial', 'business', 'tables', 'top-nav'],
        author: 'Shadow Suite',
        isBuiltIn: true,
      ),

      // Creative & Design Templates
      PresetTemplate(
        id: 'creative_portfolio',
        name: 'Creative Portfolio',
        description:
            'Modern portfolio layout with masonry grid and floating navigation',
        categoryId: 'creative',
        configuration: const LayoutConfiguration(
          navigationSystem: NavigationSystem.hamburger,
          colorScheme: AppColorScheme.dark,
          gridLayout: GridLayoutType.masonry,
          gridColumns: 3,
          fontFamily: 'Helvetica',
          baseFontSize: 16,
          useGradients: true,
          borderRadius: 12,
          animationSpeed: AnimationSpeed.fast,
        ),
        tags: ['creative', 'portfolio', 'masonry', 'dark'],
        author: 'Shadow Suite',
        isBuiltIn: true,
        isFeatured: true,
      ),

      PresetTemplate(
        id: 'design_studio',
        name: 'Design Studio',
        description:
            'Vibrant layout for design studios with custom colors and animations',
        categoryId: 'creative',
        configuration: const LayoutConfiguration(
          navigationSystem: NavigationSystem.sidebar,
          sidebarPosition: SidebarPosition.right,
          colorScheme: AppColorScheme.custom,
          gridLayout: GridLayoutType.staggered,
          gridColumns: 4,
          fontFamily: 'Georgia',
          baseFontSize: 15,
          useGradients: true,
          borderRadius: 16,
          animationSpeed: AnimationSpeed.medium,
          enableHoverEffects: true,
        ),
        tags: ['design', 'creative', 'colorful', 'animations'],
        author: 'Shadow Suite',
        isBuiltIn: true,
      ),

      // Data & Analytics Templates
      PresetTemplate(
        id: 'analytics_dashboard',
        name: 'Analytics Dashboard',
        description:
            'Data-focused layout with charts and metrics in responsive grid',
        categoryId: 'data',
        configuration: const LayoutConfiguration(
          navigationSystem: NavigationSystem.topTabs,
          colorScheme: AppColorScheme.light,
          gridLayout: GridLayoutType.responsive,
          gridColumns: 4,
          fontFamily: 'Roboto',
          baseFontSize: 12,
          showShadows: true,
          borderRadius: 6,
          componentPadding: 8,
          spacing: SpacingConfig.compact,
        ),
        tags: ['analytics', 'data', 'charts', 'metrics'],
        author: 'Shadow Suite',
        isBuiltIn: true,
        isFeatured: true,
      ),

      // Mobile Optimized Templates
      PresetTemplate(
        id: 'mobile_first',
        name: 'Mobile First',
        description:
            'Optimized for mobile devices with bottom navigation and touch-friendly controls',
        categoryId: 'mobile',
        configuration: const LayoutConfiguration(
          navigationSystem: NavigationSystem.bottomTabs,
          bottomNavStyle: BottomNavStyle.floating,
          colorScheme: AppColorScheme.auto,
          gridLayout: GridLayoutType.responsive,
          gridColumns: 1,
          fontFamily: 'Roboto',
          baseFontSize: 16,
          componentPadding: 20,
          borderRadius: 12,
          enableSwipeGestures: true,
        ),
        tags: ['mobile', 'touch', 'bottom-nav', 'responsive'],
        author: 'Shadow Suite',
        isBuiltIn: true,
        isFeatured: true,
      ),

      // Desktop Applications Templates
      PresetTemplate(
        id: 'desktop_pro',
        name: 'Desktop Professional',
        description:
            'Traditional desktop application layout with menu bar and toolbars',
        categoryId: 'desktop',
        configuration: const LayoutConfiguration(
          navigationSystem: NavigationSystem.topTabs,
          showBreadcrumbs: true,
          colorScheme: AppColorScheme.light,
          gridLayout: GridLayoutType.grid,
          gridColumns: 3,
          fontFamily: 'Arial',
          baseFontSize: 11,
          componentPadding: 8,
          borderRadius: 4,
          animationSpeed: AnimationSpeed.slow,
        ),
        tags: ['desktop', 'professional', 'traditional', 'breadcrumbs'],
        author: 'Shadow Suite',
        isBuiltIn: true,
      ),

      // Minimal & Clean Templates
      PresetTemplate(
        id: 'minimal_clean',
        name: 'Minimal Clean',
        description: 'Ultra-clean minimal design with maximum white space',
        categoryId: 'minimal',
        configuration: const LayoutConfiguration(
          navigationSystem: NavigationSystem.hamburger,
          colorScheme: AppColorScheme.light,
          gridLayout: GridLayoutType.grid,
          gridColumns: 2,
          fontFamily: 'Helvetica',
          baseFontSize: 14,
          showShadows: false,
          borderRadius: 0,
          componentPadding: 24,
          spacing: SpacingConfig.spacious,
          animationSpeed: AnimationSpeed.instant,
        ),
        tags: ['minimal', 'clean', 'white-space', 'simple'],
        author: 'Shadow Suite',
        isBuiltIn: true,
      ),
    ]);
  }
}

/// Preset template data class
class PresetTemplate {
  final String id;
  final String name;
  final String description;
  final String categoryId;
  final LayoutConfiguration configuration;
  final List<String> tags;
  final String author;
  final DateTime createdAt;
  final bool isBuiltIn;
  final bool isFeatured;

  PresetTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.categoryId,
    required this.configuration,
    this.tags = const [],
    required this.author,
    DateTime? createdAt,
    this.isBuiltIn = false,
    this.isFeatured = false,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'categoryId': categoryId,
      'configuration': configuration.toJson(),
      'tags': tags,
      'author': author,
      'createdAt': createdAt.toIso8601String(),
      'isBuiltIn': isBuiltIn,
      'isFeatured': isFeatured,
    };
  }

  factory PresetTemplate.fromJson(Map<String, dynamic> json) {
    return PresetTemplate(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      categoryId: json['categoryId'],
      configuration: LayoutConfiguration.fromJson(json['configuration']),
      tags: List<String>.from(json['tags'] ?? []),
      author: json['author'],
      createdAt: DateTime.parse(json['createdAt']),
      isBuiltIn: json['isBuiltIn'] ?? false,
      isFeatured: json['isFeatured'] ?? false,
    );
  }
}

/// Preset category data class
class PresetCategory {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color color;

  const PresetCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
  });
}
