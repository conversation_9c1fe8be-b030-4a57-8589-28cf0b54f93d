import 'package:flutter/material.dart';
import '../models/note.dart';

class ChecklistEditor extends StatefulWidget {
  final List<ChecklistItem> items;
  final Function(List<ChecklistItem>) onItemsChanged;

  const ChecklistEditor({
    super.key,
    required this.items,
    required this.onItemsChanged,
  });

  @override
  State<ChecklistEditor> createState() => _ChecklistEditorState();
}

class _ChecklistEditorState extends State<ChecklistEditor> {
  late List<ChecklistItem> _items;
  final TextEditingController _newItemController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _items = List.from(widget.items);
  }

  @override
  void dispose() {
    _newItemController.dispose();
    super.dispose();
  }

  void _addItem() {
    if (_newItemController.text.trim().isNotEmpty) {
      setState(() {
        _items.add(ChecklistItem(text: _newItemController.text.trim()));
        _newItemController.clear();
      });
      widget.onItemsChanged(_items);
    }
  }

  void _removeItem(int index) {
    setState(() {
      _items.removeAt(index);
    });
    widget.onItemsChanged(_items);
  }

  void _toggleItem(int index) {
    setState(() {
      _items[index] = _items[index].copyWith(
        isCompleted: !_items[index].isCompleted,
      );
    });
    widget.onItemsChanged(_items);
  }

  void _updateItemText(int index, String text) {
    setState(() {
      _items[index] = _items[index].copyWith(text: text);
    });
    widget.onItemsChanged(_items);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Add new item
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _newItemController,
                  decoration: const InputDecoration(
                    hintText: 'Add new checklist item...',
                    border: OutlineInputBorder(),
                  ),
                  onSubmitted: (_) => _addItem(),
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: _addItem,
                icon: const Icon(Icons.add),
                style: IconButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
        // Checklist items
        Expanded(
          child: _items.isEmpty
              ? const Center(
                  child: Text(
                    'No checklist items yet.\nAdd one above to get started!',
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.grey),
                  ),
                )
              : ListView.builder(
                  itemCount: _items.length,
                  itemBuilder: (context, index) {
                    final item = _items[index];
                    return Card(
                      margin: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 4,
                      ),
                      child: ListTile(
                        leading: Checkbox(
                          value: item.isCompleted,
                          onChanged: (_) => _toggleItem(index),
                        ),
                        title: TextFormField(
                          initialValue: item.text,
                          decoration: const InputDecoration(
                            border: InputBorder.none,
                            hintText: 'Enter item text...',
                          ),
                          style: TextStyle(
                            decoration: item.isCompleted
                                ? TextDecoration.lineThrough
                                : null,
                            color: item.isCompleted
                                ? Colors.grey
                                : null,
                          ),
                          onChanged: (text) => _updateItemText(index, text),
                        ),
                        trailing: IconButton(
                          icon: const Icon(Icons.delete, color: Colors.red),
                          onPressed: () => _removeItem(index),
                        ),
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }
}
