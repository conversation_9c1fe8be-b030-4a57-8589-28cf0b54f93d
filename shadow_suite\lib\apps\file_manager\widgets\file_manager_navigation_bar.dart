import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../file_manager_main.dart';
import '../services/file_manager_service.dart';

class FileManagerNavigationBar extends ConsumerStatefulWidget {
  const FileManagerNavigationBar({super.key});

  @override
  ConsumerState<FileManagerNavigationBar> createState() => _FileManagerNavigationBarState();
}

class _FileManagerNavigationBarState extends ConsumerState<FileManagerNavigationBar> {
  final TextEditingController _addressController = TextEditingController();
  bool _isEditingAddress = false;

  @override
  void initState() {
    super.initState();
    _updateAddressBar();
  }

  void _updateAddressBar() {
    final activePane = ref.read(activePaneProvider);
    final currentPath = activePane == 'left' 
        ? ref.read(leftPanePathProvider) 
        : ref.read(rightPanePathProvider);
    _addressController.text = currentPath;
  }

  @override
  Widget build(BuildContext context) {
    final activePane = ref.watch(activePaneProvider);
    final currentPath = activePane == 'left' 
        ? ref.watch(leftPanePathProvider) 
        : ref.watch(rightPanePathProvider);

    return Container(
      height: 50,
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE9ECEF)),
        ),
      ),
      child: Row(
        children: [
          // Back/Forward buttons
          IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: FileManagerService.canNavigateBack() 
                ? () => _navigateBack()
                : null,
            tooltip: 'Back',
          ),
          IconButton(
            icon: const Icon(Icons.arrow_forward),
            onPressed: FileManagerService.canNavigateForward()
                ? () => _navigateForward()
                : null,
            tooltip: 'Forward',
          ),
          IconButton(
            icon: const Icon(Icons.arrow_upward),
            onPressed: () => _navigateUp(),
            tooltip: 'Up',
          ),
          const SizedBox(width: 8),
          
          // Breadcrumb/Address bar
          Expanded(
            child: Container(
              height: 32,
              decoration: BoxDecoration(
                color: const Color(0xFFF8F9FA),
                border: Border.all(color: const Color(0xFFDEE2E6)),
                borderRadius: BorderRadius.circular(4),
              ),
              child: _isEditingAddress
                  ? TextField(
                      controller: _addressController,
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      ),
                      onSubmitted: (value) => _navigateToPath(value),
                      onEditingComplete: () => setState(() => _isEditingAddress = false),
                    )
                  : InkWell(
                      onTap: () => setState(() => _isEditingAddress = true),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        alignment: Alignment.centerLeft,
                        child: Row(
                          children: [
                            const Icon(Icons.folder, size: 16, color: Color(0xFFE67E22)),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                currentPath,
                                style: const TextStyle(fontSize: 13),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
            ),
          ),
          
          const SizedBox(width: 8),
          
          // View mode toggle
          IconButton(
            icon: const Icon(Icons.view_column),
            onPressed: () => _toggleViewMode(),
            tooltip: 'Toggle View Mode',
          ),
          
          // Refresh button
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _refresh(),
            tooltip: 'Refresh',
          ),
          
          // Search button
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearch(),
            tooltip: 'Search',
          ),
        ],
      ),
    );
  }

  void _navigateBack() async {
    await FileManagerService.navigateBack();
    _updateCurrentPath();
  }

  void _navigateForward() async {
    await FileManagerService.navigateForward();
    _updateCurrentPath();
  }

  void _navigateUp() async {
    await FileManagerService.navigateToParent();
    _updateCurrentPath();
  }

  void _navigateToPath(String path) async {
    try {
      await FileManagerService.navigateToDirectory(path);
      _updateCurrentPath();
    } catch (e) {
      _showError('Invalid path: $path');
    }
    setState(() => _isEditingAddress = false);
  }

  void _updateCurrentPath() {
    final newPath = FileManagerService.getCurrentDirectory();
    final activePane = ref.read(activePaneProvider);
    
    if (activePane == 'left') {
      ref.read(leftPanePathProvider.notifier).state = newPath;
    } else {
      ref.read(rightPanePathProvider.notifier).state = newPath;
    }
    
    _updateAddressBar();
  }

  void _toggleViewMode() {
    final currentMode = ref.read(currentFileManagerViewModeProvider);
    final newMode = currentMode == FileManagerViewMode.dualPane 
        ? FileManagerViewMode.singlePane 
        : FileManagerViewMode.dualPane;
    ref.read(currentFileManagerViewModeProvider.notifier).state = newMode;
  }

  void _refresh() {
    // Trigger refresh of current directory
    FileManagerService.refreshCurrentDirectory();
  }

  void _showSearch() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Files'),
        content: const TextField(
          decoration: InputDecoration(
            hintText: 'Enter search term...',
            prefixIcon: Icon(Icons.search),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  void dispose() {
    _addressController.dispose();
    super.dispose();
  }
}
