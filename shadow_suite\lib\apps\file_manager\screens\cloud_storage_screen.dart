import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../file_manager_main.dart';

class CloudStorageScreen extends ConsumerStatefulWidget {
  const CloudStorageScreen({super.key});

  @override
  ConsumerState<CloudStorageScreen> createState() => _CloudStorageScreenState();
}

class _CloudStorageScreenState extends ConsumerState<CloudStorageScreen> {
  final List<CloudProvider> _connectedProviders = [];
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          FileManagerHeader(
            title: 'Cloud Storage',
            subtitle: 'Access your cloud files and folders',
            actions: [
              IconButton(
                onPressed: _refreshProviders,
                icon: const Icon(Icons.sync, color: Color(0xFFE67E22)),
                tooltip: 'Refresh',
              ),
              IconButton(
                onPressed: _showAddProviderDialog,
                icon: const Icon(Icons.add, color: Color(0xFFE67E22)),
                tooltip: 'Add Provider',
              ),
            ],
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _connectedProviders.isEmpty
                    ? _buildEmptyState()
                    : _buildProvidersList(),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.cloud_off,
            size: 64,
            color: Color(0xFF7F8C8D),
          ),
          const SizedBox(height: 16),
          const Text(
            'No Cloud Providers Connected',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Connect to your cloud storage providers to access your files',
            style: TextStyle(color: Color(0xFF7F8C8D)),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: _showAddProviderDialog,
            icon: const Icon(Icons.add),
            label: const Text('Add Cloud Provider'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFE67E22),
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProvidersList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _connectedProviders.length,
      itemBuilder: (context, index) {
        final provider = _connectedProviders[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: Icon(
              _getProviderIcon(provider.type),
              color: const Color(0xFFE67E22),
              size: 32,
            ),
            title: Text(
              provider.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('${provider.usedSpace} / ${provider.totalSpace}'),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: provider.usagePercentage,
                  backgroundColor: Colors.grey[300],
                  valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFFE67E22)),
                ),
              ],
            ),
            trailing: PopupMenuButton(
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'browse',
                  child: Row(
                    children: [
                      Icon(Icons.folder_open),
                      SizedBox(width: 8),
                      Text('Browse Files'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'sync',
                  child: Row(
                    children: [
                      Icon(Icons.sync),
                      SizedBox(width: 8),
                      Text('Sync Now'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'disconnect',
                  child: Row(
                    children: [
                      Icon(Icons.link_off, color: Colors.red),
                      SizedBox(width: 8),
                      Text('Disconnect', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
              onSelected: (value) => _handleProviderAction(provider, value.toString()),
            ),
            onTap: () => _browseProvider(provider),
          ),
        );
      },
    );
  }

  IconData _getProviderIcon(CloudProviderType type) {
    switch (type) {
      case CloudProviderType.googleDrive:
        return Icons.cloud;
      case CloudProviderType.dropbox:
        return Icons.cloud_upload;
      case CloudProviderType.oneDrive:
        return Icons.cloud_download;
      case CloudProviderType.iCloud:
        return Icons.cloud_circle;
    }
  }

  void _refreshProviders() {
    setState(() => _isLoading = true);

    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    });
  }

  void _showAddProviderDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Cloud Provider'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildProviderOption('Google Drive', Icons.cloud, CloudProviderType.googleDrive),
            _buildProviderOption('Dropbox', Icons.cloud_upload, CloudProviderType.dropbox),
            _buildProviderOption('OneDrive', Icons.cloud_download, CloudProviderType.oneDrive),
            _buildProviderOption('iCloud', Icons.cloud_circle, CloudProviderType.iCloud),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildProviderOption(String name, IconData icon, CloudProviderType type) {
    return ListTile(
      leading: Icon(icon, color: const Color(0xFFE67E22)),
      title: Text(name),
      onTap: () {
        Navigator.pop(context);
        _connectProvider(type);
      },
    );
  }

  void _connectProvider(CloudProviderType type) {
    setState(() => _isLoading = true);

    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        final provider = CloudProvider(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: _getProviderName(type),
          type: type,
          usedSpace: '2.5 GB',
          totalSpace: '15 GB',
          usagePercentage: 0.17,
          isConnected: true,
        );

        setState(() {
          _connectedProviders.add(provider);
          _isLoading = false;
        });

        _showSuccess('Connected to ${provider.name} successfully');
      }
    });
  }

  String _getProviderName(CloudProviderType type) {
    switch (type) {
      case CloudProviderType.googleDrive:
        return 'Google Drive';
      case CloudProviderType.dropbox:
        return 'Dropbox';
      case CloudProviderType.oneDrive:
        return 'OneDrive';
      case CloudProviderType.iCloud:
        return 'iCloud';
    }
  }

  void _handleProviderAction(CloudProvider provider, String action) {
    switch (action) {
      case 'browse':
        _browseProvider(provider);
        break;
      case 'sync':
        _syncProvider(provider);
        break;
      case 'disconnect':
        _disconnectProvider(provider);
        break;
    }
  }

  void _browseProvider(CloudProvider provider) {
    _showInfo('Opening ${provider.name} file browser...');
  }

  void _syncProvider(CloudProvider provider) {
    _showInfo('Syncing ${provider.name}...');
  }

  void _disconnectProvider(CloudProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Disconnect Provider'),
        content: Text('Are you sure you want to disconnect from ${provider.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _connectedProviders.remove(provider);
              });
              _showSuccess('Disconnected from ${provider.name}');
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Disconnect'),
          ),
        ],
      ),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF27AE60),
      ),
    );
  }

  void _showInfo(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF3498DB),
      ),
    );
  }
}

class CloudProvider {
  final String id;
  final String name;
  final CloudProviderType type;
  final String usedSpace;
  final String totalSpace;
  final double usagePercentage;
  final bool isConnected;

  const CloudProvider({
    required this.id,
    required this.name,
    required this.type,
    required this.usedSpace,
    required this.totalSpace,
    required this.usagePercentage,
    required this.isConnected,
  });
}

enum CloudProviderType {
  googleDrive,
  dropbox,
  oneDrive,
  iCloud,
}
