import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/app_theme.dart';
import '../services/tools_builder_providers.dart';
import '../models/tools_models.dart';
import '../services/excel_service.dart';
import '../services/performance_optimizer.dart';
import 'tool_editor_screen.dart';
import 'tool_runtime_screen.dart';
import 'settings_screen.dart';
import 'templates_screen.dart';

class ToolsBuilderDashboard extends ConsumerStatefulWidget {
  const ToolsBuilderDashboard({super.key});

  @override
  ConsumerState<ToolsBuilderDashboard> createState() =>
      _ToolsBuilderDashboardState();
}

class _ToolsBuilderDashboardState extends ConsumerState<ToolsBuilderDashboard>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildMyToolsTab(),
                _buildTemplatesTab(),
                _buildRecentToolsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showCreateToolDialog(),
        backgroundColor: AppTheme.toolsBuilderColor,
        icon: const Icon(Icons.add, color: Colors.white),
        label: const Text('New Tool', style: TextStyle(color: Colors.white)),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(Icons.build_circle, size: 32, color: AppTheme.toolsBuilderColor),
          const SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Tools Builder',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.toolsBuilderColor,
                ),
              ),
              Text(
                'Create powerful tools with spreadsheet logic and custom UI',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.black87),
              ),
            ],
          ),
          const Spacer(),
          _buildQuickActions(),
          const SizedBox(width: 16),
          IconButton(
            onPressed: () => _openSettings(),
            icon: const Icon(Icons.settings),
            tooltip: 'Settings',
            iconSize: 28,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Row(
      children: [
        ElevatedButton.icon(
          onPressed: () => _importExcelFile(),
          icon: const Icon(Icons.upload_file, size: 16),
          label: const Text('Import Excel'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
          ),
        ),
        const SizedBox(width: 12),
        ElevatedButton.icon(
          onPressed: () => _showTemplatesScreen(),
          icon: const Icon(Icons.library_books, size: 16),
          label: const Text('Browse Templates'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
        ),
        const SizedBox(width: 12),
        Consumer(
          builder: (context, ref, child) {
            // Search query for future use
            ref.watch(toolsSearchProvider);
            return SizedBox(
              width: 250,
              child: TextField(
                decoration: InputDecoration(
                  hintText: 'Search tools...',
                  prefixIcon: const Icon(Icons.search, size: 20),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                onChanged: (value) {
                  ref.read(toolsSearchProvider.notifier).state = value;
                },
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: AppTheme.toolsBuilderColor,
        unselectedLabelColor: Colors.black54,
        indicatorColor: AppTheme.toolsBuilderColor,
        tabs: const [
          Tab(icon: Icon(Icons.folder), text: 'My Tools'),
          Tab(icon: Icon(Icons.library_books), text: 'Templates'),
          Tab(icon: Icon(Icons.history), text: 'Recent'),
        ],
      ),
    );
  }

  Widget _buildMyToolsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final tools = ref.watch(toolsProvider);
        final searchQuery = ref.watch(toolsSearchProvider);
        final categoryFilter = ref.watch(toolsFilterProvider);

        var filteredTools = tools;

        // Optimized filtering for better performance with caching
        if (searchQuery.isNotEmpty) {
          final lowerQuery = searchQuery.toLowerCase();
          filteredTools = filteredTools.where((tool) {
            return tool.name.toLowerCase().contains(lowerQuery) ||
                tool.description.toLowerCase().contains(lowerQuery) ||
                tool.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));
          }).toList();
        }

        if (categoryFilter != null) {
          filteredTools = filteredTools
              .where((tool) => tool.category == categoryFilter)
              .toList();
        }

        if (filteredTools.isEmpty) {
          return _buildEmptyState();
        }

        return Column(
          children: [
            _buildPerformanceMetrics(),
            _buildFilterBar(),
            Expanded(child: _buildToolsGrid(filteredTools)),
          ],
        );
      },
    );
  }

  Widget _buildTemplatesTab() {
    return Consumer(
      builder: (context, ref, child) {
        final templates = ref.watch(templatesProvider);

        if (templates.isEmpty) {
          return _buildEmptyTemplatesState();
        }

        return Padding(
          padding: const EdgeInsets.all(16),
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 0.8,
            ),
            itemCount: templates.length,
            itemBuilder: (context, index) {
              final template = templates[index];
              return _buildTemplateCard(template);
            },
          ),
        );
      },
    );
  }

  Widget _buildRecentToolsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final recentTools = ref.watch(recentToolsProvider);

        if (recentTools.isEmpty) {
          return _buildEmptyRecentState();
        }

        return Padding(
          padding: const EdgeInsets.all(16),
          child: ListView.builder(
            itemCount: recentTools.length,
            itemBuilder: (context, index) {
              final tool = recentTools[index];
              return _buildRecentToolCard(tool);
            },
          ),
        );
      },
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Consumer(
            builder: (context, ref, child) {
              final categoryFilter = ref.watch(toolsFilterProvider);
              return DropdownButton<ToolCategory?>(
                value: categoryFilter,
                hint: const Text('All Categories'),
                items: [
                  const DropdownMenuItem(
                    value: null,
                    child: Text('All Categories'),
                  ),
                  ...ToolCategory.values.map(
                    (category) => DropdownMenuItem(
                      value: category,
                      child: Text(category.name.toUpperCase()),
                    ),
                  ),
                ],
                onChanged: (value) {
                  ref.read(toolsFilterProvider.notifier).state = value;
                },
              );
            },
          ),
          const SizedBox(width: 16),
          Consumer(
            builder: (context, ref, child) {
              final sortBy = ref.watch(toolsSortProvider);
              return DropdownButton<String>(
                value: sortBy,
                items: const [
                  DropdownMenuItem(
                    value: 'lastModified',
                    child: Text('Last Modified'),
                  ),
                  DropdownMenuItem(value: 'name', child: Text('Name')),
                  DropdownMenuItem(
                    value: 'created',
                    child: Text('Date Created'),
                  ),
                  DropdownMenuItem(value: 'category', child: Text('Category')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    ref.read(toolsSortProvider.notifier).state = value;
                  }
                },
              );
            },
          ),
          const Spacer(),
          Text('View:', style: Theme.of(context).textTheme.bodyMedium),
          const SizedBox(width: 8),
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.grid_view),
            color: AppTheme.toolsBuilderColor,
          ),
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.list),
            color: Colors.grey,
          ),
        ],
      ),
    );
  }

  Widget _buildToolsGrid(List<Tool> tools) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 0.8,
        ),
        itemCount: tools.length,
        cacheExtent: 1000, // Cache more items for better performance
        addAutomaticKeepAlives: true, // Keep items alive when scrolling
        itemBuilder: (context, index) {
          final tool = tools[index];
          return _buildToolCard(tool);
        },
      ),
    );
  }

  Widget _buildToolCard(Tool tool) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => _openTool(tool),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: _getCategoryColor(
                        tool.category.name,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getCategoryIcon(tool.category.name),
                      color: _getCategoryColor(tool.category.name),
                      size: 20,
                    ),
                  ),
                  const Spacer(),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleToolAction(value, tool),
                    itemBuilder: (context) => [
                      const PopupMenuItem(value: 'edit', child: Text('Edit')),
                      const PopupMenuItem(
                        value: 'duplicate',
                        child: Text('Duplicate'),
                      ),
                      const PopupMenuItem(
                        value: 'export',
                        child: Text('Export'),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Text('Delete'),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                tool.name,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Text(
                tool.description,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.black87),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              const Spacer(),
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(
                        tool.status,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      tool.status.name.toUpperCase(),
                      style: TextStyle(
                        color: _getStatusColor(tool.status),
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    _formatDate(tool.lastModified),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.black54,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTemplateCard(Template template) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => _createFromTemplate(template),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _getCategoryColor(
                    template.category,
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getCategoryIcon(template.category),
                  color: _getCategoryColor(template.category),
                  size: 20,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                template.name,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Text(
                template.description,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.black87),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              const Spacer(),
              // Premium badge removed as Template model doesn't have isPremium property
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentToolCard(Tool tool) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: _getCategoryColor(tool.category.name).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getCategoryIcon(tool.category.name),
            color: _getCategoryColor(tool.category.name),
            size: 20,
          ),
        ),
        title: Text(tool.name),
        subtitle: Text(
          '${tool.category.name.toUpperCase()} • ${_formatDate(tool.lastModified)}',
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.black54),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => _openTool(tool),
              icon: const Icon(Icons.play_arrow),
              tooltip: 'Run Tool',
            ),
            IconButton(
              onPressed: () => _editTool(tool),
              icon: const Icon(Icons.edit),
              tooltip: 'Edit Tool',
            ),
          ],
        ),
        onTap: () => _openTool(tool),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.build_circle_outlined, size: 120, color: Colors.black26),
          const SizedBox(height: 24),
          Text(
            'No Tools Yet',
            style: Theme.of(
              context,
            ).textTheme.headlineMedium?.copyWith(color: Colors.black54),
          ),
          const SizedBox(height: 16),
          Text(
            'Create your first tool to get started',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.black45),
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () => _showCreateToolDialog(),
            icon: const Icon(Icons.add),
            label: const Text('Create New Tool'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.toolsBuilderColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyTemplatesState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.library_books, size: 120, color: Colors.black26),
          const SizedBox(height: 24),
          Text(
            'No Templates Available',
            style: Theme.of(
              context,
            ).textTheme.headlineMedium?.copyWith(color: Colors.black54),
          ),
          const SizedBox(height: 16),
          Text(
            'Templates will be loaded automatically',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.black45),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyRecentState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.history, size: 120, color: Colors.black26),
          const SizedBox(height: 24),
          Text(
            'No Recent Tools',
            style: Theme.of(
              context,
            ).textTheme.headlineMedium?.copyWith(color: Colors.black54),
          ),
          const SizedBox(height: 16),
          Text(
            'Tools you work on will appear here',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.black45),
          ),
        ],
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'finance':
        return Colors.green;
      case 'engineering':
        return Colors.blue;
      case 'education':
        return Colors.purple;
      case 'business':
        return Colors.orange;
      case 'personal':
        return Colors.pink;
      case 'utilities':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'finance':
        return Icons.attach_money;
      case 'engineering':
        return Icons.engineering;
      case 'education':
        return Icons.school;
      case 'business':
        return Icons.business;
      case 'personal':
        return Icons.person;
      case 'utilities':
        return Icons.build;
      default:
        return Icons.category;
    }
  }

  Color _getStatusColor(ToolStatus status) {
    switch (status) {
      case ToolStatus.draft:
        return Colors.orange;
      case ToolStatus.published:
        return Colors.green;
      case ToolStatus.archived:
        return Colors.grey;
    }
  }

  Widget _buildPerformanceMetrics() {
    final metrics = PerformanceOptimizer.getMetrics();

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Icon(Icons.speed, color: Colors.blue, size: 20),
          const SizedBox(width: 8),
          Text(
            'Performance Metrics',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue[700],
            ),
          ),
          const SizedBox(width: 16),
          _buildMetricChip('Cache', '${metrics.cacheSize}', Icons.memory),
          const SizedBox(width: 8),
          _buildMetricChip('Undo', '${metrics.undoStackSize}', Icons.undo),
          const SizedBox(width: 8),
          _buildMetricChip(
            'Components',
            '${metrics.componentCacheSize}',
            Icons.widgets,
          ),
          const Spacer(),
          TextButton.icon(
            onPressed: () {
              PerformanceOptimizer.cleanup();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Performance cache cleared')),
              );
            },
            icon: const Icon(Icons.cleaning_services, size: 16),
            label: const Text('Cleanup'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.blue[700],
              textStyle: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricChip(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: Colors.blue[600]),
          const SizedBox(width: 4),
          Text(
            '$label: $value',
            style: TextStyle(
              fontSize: 12,
              color: Colors.blue[700],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _showCreateToolDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create New Tool'),
        content: const Text('Choose how you want to create your tool:'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _createBlankTool();
            },
            child: const Text('Start from Scratch'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showTemplatesScreen();
            },
            child: const Text('Use Template'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _importExcelFile();
            },
            child: const Text('Import Excel'),
          ),
        ],
      ),
    );
  }

  void _createBlankTool() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const ToolEditorScreen()));
  }

  void _createFromTemplate(Template template) {
    // Implementation for creating tool from template
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Creating tool from ${template.name} template...'),
      ),
    );
  }

  Future<void> _importExcelFile() async {
    try {
      final result = await ExcelService.importFromExcel();
      if (result.isSuccess && result.spreadsheet != null && mounted) {
        // Create a new tool from the imported spreadsheet
        final tool = Tool(
          name: result.spreadsheet!.name,
          description: 'Tool created from imported Excel file',
          type: ToolType.custom,
          category: ToolCategory.utilities,
          spreadsheet: result.spreadsheet!,
          components: [],
          creatorId: 'current-user',
        );

        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => ToolEditorScreen(tool: tool)),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to import Excel file: $e')),
        );
      }
    }
  }

  void _showTemplatesScreen() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const TemplatesScreen()));
  }

  void _openTool(Tool tool) {
    ref.read(recentToolsProvider.notifier).addRecentTool(tool);
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => ToolRuntimeScreen(tool: tool)),
    );
  }

  void _editTool(Tool tool) {
    ref.read(recentToolsProvider.notifier).addRecentTool(tool);
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => ToolEditorScreen(tool: tool)),
    );
  }

  void _handleToolAction(String action, Tool tool) {
    switch (action) {
      case 'edit':
        _editTool(tool);
        break;
      case 'duplicate':
        _duplicateTool(tool);
        break;
      case 'export':
        _exportTool(tool);
        break;
      case 'delete':
        _deleteTool(tool);
        break;
    }
  }

  void _duplicateTool(Tool tool) {
    // Implementation for duplicating tool
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Duplicating ${tool.name}...')));
  }

  void _exportTool(Tool tool) {
    // Implementation for exporting tool
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Exporting ${tool.name}...')));
  }

  void _deleteTool(Tool tool) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Tool'),
        content: Text('Are you sure you want to delete "${tool.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(toolsProvider.notifier).removeTool(tool.id);
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(SnackBar(content: Text('${tool.name} deleted')));
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _openSettings() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ToolsBuilderSettingsScreen(),
      ),
    );
  }
}
