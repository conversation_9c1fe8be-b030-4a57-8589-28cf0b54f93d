import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_component.dart' as ui_comp;
import '../models/excel_app_tool.dart';
import 'excel_formula_engine.dart';

// UI Component providers
final uiComponentsProvider = StateNotifierProvider<UIComponentsNotifier, List<ui_comp.UIComponent>>((ref) {
  return UIComponentsNotifier();
});

final selectedComponentProvider = StateProvider<ui_comp.UIComponent?>((ref) => null);

final canvasScaleProvider = StateProvider<double>((ref) => 1.0);

final canvasOffsetProvider = StateProvider<Offset>((ref) => Offset.zero);

final isPreviewModeProvider = StateProvider<bool>((ref) => false);

// Tool providers
final currentToolProvider = StateProvider<ExcelAppTool?>((ref) => null);

final toolsListProvider = StateNotifierProvider<ToolsListNotifier, List<ExcelAppTool>>((ref) {
  return ToolsListNotifier();
});

// Formula engine provider
final excelFormulaEngineProvider = Provider<ExcelFormulaEngine>((ref) {
  return ExcelFormulaEngine();
});

// UI Components State Notifier
class UIComponentsNotifier extends StateNotifier<List<ui_comp.UIComponent>> {
  UIComponentsNotifier() : super([]);

  void addComponent(ui_comp.UIComponent component) {
    state = [...state, component];
  }

  void updateComponent(ui_comp.UIComponent updatedComponent) {
    state = [
      for (final component in state)
        if (component.id == updatedComponent.id)
          updatedComponent
        else
          component,
    ];
  }

  void removeComponent(String componentId) {
    state = state.where((component) => component.id != componentId).toList();
  }

  void clearComponents() {
    state = [];
  }

  void reorderComponents(int oldIndex, int newIndex) {
    final components = List<ui_comp.UIComponent>.from(state);
    final component = components.removeAt(oldIndex);
    components.insert(newIndex, component);
    state = components;
  }
}

// Tools List State Notifier
class ToolsListNotifier extends StateNotifier<List<ExcelAppTool>> {
  ToolsListNotifier() : super([]);

  void addTool(ExcelAppTool tool) {
    state = [...state, tool];
  }

  void updateTool(ExcelAppTool updatedTool) {
    state = [
      for (final tool in state)
        if (tool.id == updatedTool.id)
          updatedTool
        else
          tool,
    ];
  }

  void removeTool(String toolId) {
    state = state.where((tool) => tool.id != toolId).toList();
  }

  void clearTools() {
    state = [];
  }
}

// Canvas interaction providers
final isDraggingProvider = StateProvider<bool>((ref) => false);

final dragOffsetProvider = StateProvider<Offset>((ref) => Offset.zero);

final snapToGridProvider = StateProvider<bool>((ref) => true);

final gridSizeProvider = StateProvider<double>((ref) => 20.0);

// Property editor providers
final showPropertyEditorProvider = StateProvider<bool>((ref) => true);

final propertyEditorWidthProvider = StateProvider<double>((ref) => 300.0);

// Component palette providers
final componentPaletteExpandedProvider = StateProvider<bool>((ref) => true);

final selectedComponentTypeProvider = StateProvider<ui_comp.UIComponentType?>((ref) => null);
