import 'dart:async';
import 'dart:convert';
import 'performance_optimizer.dart';

/// Real-time collaboration service for Excel-to-App builder
class CollaborationService {
  static bool _isInitialized = false;
  static final Map<String, CollaborationSession> _sessions = {};
  static final Map<String, User> _users = {};
  static final StreamController<CollaborationEvent> _eventController =
      StreamController.broadcast();
  static final List<CollaborationEvent> _eventHistory = [];
  static String? _currentUserId;
  static String? _currentSessionId;

  /// Initialize the collaboration service
  static void initialize() {
    if (_isInitialized) return;

    _createDefaultUsers();
    _isInitialized = true;
  }

  /// Get collaboration events stream
  static Stream<CollaborationEvent> get events => _eventController.stream;

  /// Create a new collaboration session
  static Future<CollaborationSession> createSession({
    required String name,
    required String description,
    required String ownerId,
  }) async {
    return PerformanceOptimizer.measureAsync('create_session', () async {
      final session = CollaborationSession(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        description: description,
        ownerId: ownerId,
        participants: [ownerId],
        createdAt: DateTime.now(),
        lastActivity: DateTime.now(),
        isActive: true,
        settings: const SessionSettings(),
      );

      _sessions[session.id] = session;

      final event = CollaborationEvent(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: EventType.sessionCreated,
        sessionId: session.id,
        userId: ownerId,
        timestamp: DateTime.now(),
        data: {'sessionName': name},
      );

      _addEvent(event);
      return session;
    });
  }

  /// Join an existing session
  static Future<bool> joinSession(String sessionId, String userId) async {
    return PerformanceOptimizer.measureAsync('join_session', () async {
      final session = _sessions[sessionId];
      if (session == null || !session.isActive) return false;

      if (!session.participants.contains(userId)) {
        session.participants.add(userId);
        session.lastActivity = DateTime.now();

        final event = CollaborationEvent(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          type: EventType.userJoined,
          sessionId: sessionId,
          userId: userId,
          timestamp: DateTime.now(),
          data: {'userName': _users[userId]?.name ?? 'Unknown User'},
        );

        _addEvent(event);
      }

      _currentSessionId = sessionId;
      return true;
    });
  }

  /// Leave a session
  static Future<void> leaveSession(String sessionId, String userId) async {
    return PerformanceOptimizer.measureAsync('leave_session', () async {
      final session = _sessions[sessionId];
      if (session == null) return;

      session.participants.remove(userId);
      session.lastActivity = DateTime.now();

      final event = CollaborationEvent(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: EventType.userLeft,
        sessionId: sessionId,
        userId: userId,
        timestamp: DateTime.now(),
        data: {'userName': _users[userId]?.name ?? 'Unknown User'},
      );

      _addEvent(event);

      if (_currentSessionId == sessionId) {
        _currentSessionId = null;
      }
    });
  }

  /// Share a formula with collaborators
  static Future<void> shareFormula({
    required String sessionId,
    required String userId,
    required String formula,
    required String description,
  }) async {
    return PerformanceOptimizer.measureAsync('share_formula', () async {
      final session = _sessions[sessionId];
      if (session == null || !session.participants.contains(userId)) return;

      final sharedItem = SharedItem(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: SharedItemType.formula,
        title:
            'Formula: ${formula.substring(0, formula.length > 20 ? 20 : formula.length)}...',
        content: formula,
        description: description,
        sharedBy: userId,
        sharedAt: DateTime.now(),
        sessionId: sessionId,
      );

      session.sharedItems.add(sharedItem);
      session.lastActivity = DateTime.now();

      final event = CollaborationEvent(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: EventType.formulaShared,
        sessionId: sessionId,
        userId: userId,
        timestamp: DateTime.now(),
        data: {
          'formula': formula,
          'description': description,
          'itemId': sharedItem.id,
        },
      );

      _addEvent(event);
    });
  }

  /// Share a layout configuration
  static Future<void> shareLayout({
    required String sessionId,
    required String userId,
    required Map<String, dynamic> layoutConfig,
    required String name,
  }) async {
    return PerformanceOptimizer.measureAsync('share_layout', () async {
      final session = _sessions[sessionId];
      if (session == null || !session.participants.contains(userId)) return;

      final sharedItem = SharedItem(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: SharedItemType.layout,
        title: 'Layout: $name',
        content: jsonEncode(layoutConfig),
        description: 'Shared layout configuration',
        sharedBy: userId,
        sharedAt: DateTime.now(),
        sessionId: sessionId,
      );

      session.sharedItems.add(sharedItem);
      session.lastActivity = DateTime.now();

      final event = CollaborationEvent(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: EventType.layoutShared,
        sessionId: sessionId,
        userId: userId,
        timestamp: DateTime.now(),
        data: {'layoutName': name, 'itemId': sharedItem.id},
      );

      _addEvent(event);
    });
  }

  /// Send a chat message
  static Future<void> sendMessage({
    required String sessionId,
    required String userId,
    required String message,
  }) async {
    return PerformanceOptimizer.measureAsync('send_message', () async {
      final session = _sessions[sessionId];
      if (session == null || !session.participants.contains(userId)) return;

      final chatMessage = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        senderId: userId,
        message: message,
        timestamp: DateTime.now(),
        sessionId: sessionId,
      );

      session.chatMessages.add(chatMessage);
      session.lastActivity = DateTime.now();

      final event = CollaborationEvent(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: EventType.messageReceived,
        sessionId: sessionId,
        userId: userId,
        timestamp: DateTime.now(),
        data: {'message': message, 'messageId': chatMessage.id},
      );

      _addEvent(event);
    });
  }

  /// Add a comment to a shared item
  static Future<void> addComment({
    required String sessionId,
    required String userId,
    required String itemId,
    required String comment,
  }) async {
    return PerformanceOptimizer.measureAsync('add_comment', () async {
      final session = _sessions[sessionId];
      if (session == null || !session.participants.contains(userId)) return;

      SharedItem? item;
      try {
        item = session.sharedItems.firstWhere((item) => item.id == itemId);
      } catch (e) {
        return; // Item not found, exit silently
      }

      final commentObj = Comment(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        authorId: userId,
        content: comment,
        timestamp: DateTime.now(),
        itemId: itemId,
      );

      item.comments.add(commentObj);
      session.lastActivity = DateTime.now();

      final event = CollaborationEvent(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: EventType.commentAdded,
        sessionId: sessionId,
        userId: userId,
        timestamp: DateTime.now(),
        data: {
          'comment': comment,
          'itemId': itemId,
          'commentId': commentObj.id,
        },
      );

      _addEvent(event);
    });
  }

  /// Get active sessions
  static List<CollaborationSession> getActiveSessions() {
    return _sessions.values.where((session) => session.isActive).toList()
      ..sort((a, b) => b.lastActivity.compareTo(a.lastActivity));
  }

  /// Get session by ID
  static CollaborationSession? getSession(String sessionId) {
    return _sessions[sessionId];
  }

  /// Get current session
  static CollaborationSession? get currentSession {
    return _currentSessionId != null ? _sessions[_currentSessionId] : null;
  }

  /// Get user by ID
  static User? getUser(String userId) {
    return _users[userId];
  }

  /// Get all users
  static List<User> get users => _users.values.toList();

  /// Set current user
  static void setCurrentUser(String userId) {
    _currentUserId = userId;
  }

  /// Get current user
  static User? get currentUser {
    return _currentUserId != null ? _users[_currentUserId] : null;
  }

  /// Get collaboration statistics
  static CollaborationStatistics getStatistics() {
    final totalSessions = _sessions.length;
    final activeSessions = _sessions.values.where((s) => s.isActive).length;
    final totalUsers = _users.length;
    final totalEvents = _eventHistory.length;

    final eventsByType = <EventType, int>{};
    for (final event in _eventHistory) {
      eventsByType[event.type] = (eventsByType[event.type] ?? 0) + 1;
    }

    final averageSessionDuration = _sessions.values.isNotEmpty
        ? _sessions.values
                  .map((s) => s.lastActivity.difference(s.createdAt))
                  .reduce(
                    (a, b) => Duration(
                      microseconds: a.inMicroseconds + b.inMicroseconds,
                    ),
                  )
                  .inMinutes /
              _sessions.length
        : 0.0;

    return CollaborationStatistics(
      totalSessions: totalSessions,
      activeSessions: activeSessions,
      totalUsers: totalUsers,
      totalEvents: totalEvents,
      eventsByType: eventsByType,
      averageSessionDuration: averageSessionDuration,
    );
  }

  /// Get recent activity
  static List<CollaborationEvent> getRecentActivity({int limit = 20}) {
    return _eventHistory.reversed.take(limit).toList();
  }

  /// Search sessions
  static List<CollaborationSession> searchSessions(String query) {
    final lowerQuery = query.toLowerCase();
    return _sessions.values.where((session) {
      return session.name.toLowerCase().contains(lowerQuery) ||
          session.description.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// Export session data
  static Map<String, dynamic> exportSession(String sessionId) {
    final session = _sessions[sessionId];
    if (session == null) return {};

    return {
      'session': session.toJson(),
      'participants': session.participants
          .map((id) => _users[id]?.toJson())
          .where((user) => user != null)
          .toList(),
      'events': _eventHistory
          .where((e) => e.sessionId == sessionId)
          .map((e) => e.toJson())
          .toList(),
    };
  }

  /// Clear all data
  static void clear() {
    _sessions.clear();
    _eventHistory.clear();
    _currentSessionId = null;
  }

  // Private methods
  static void _addEvent(CollaborationEvent event) {
    _eventHistory.add(event);
    _eventController.add(event);

    // Keep only last 1000 events
    if (_eventHistory.length > 1000) {
      _eventHistory.removeAt(0);
    }
  }

  static void _createDefaultUsers() {
    _users['user1'] = const User(
      id: 'user1',
      name: 'John Doe',
      email: '<EMAIL>',
      avatar: '👨‍💼',
      role: UserRole.admin,
      isOnline: true,
    );

    _users['user2'] = const User(
      id: 'user2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      avatar: '👩‍💻',
      role: UserRole.editor,
      isOnline: true,
    );

    _users['user3'] = const User(
      id: 'user3',
      name: 'Bob Johnson',
      email: '<EMAIL>',
      avatar: '👨‍🔬',
      role: UserRole.viewer,
      isOnline: false,
    );

    // Set default current user
    _currentUserId = 'user1';
  }

  /// Dispose resources
  static void dispose() {
    _eventController.close();
    clear();
    _isInitialized = false;
  }
}

/// Data classes
class CollaborationSession {
  final String id;
  final String name;
  final String description;
  final String ownerId;
  final List<String> participants;
  final DateTime createdAt;
  DateTime lastActivity;
  bool isActive;
  final SessionSettings settings;
  final List<SharedItem> sharedItems;
  final List<ChatMessage> chatMessages;

  CollaborationSession({
    required this.id,
    required this.name,
    required this.description,
    required this.ownerId,
    required this.participants,
    required this.createdAt,
    required this.lastActivity,
    required this.isActive,
    required this.settings,
    List<SharedItem>? sharedItems,
    List<ChatMessage>? chatMessages,
  }) : sharedItems = sharedItems ?? [],
       chatMessages = chatMessages ?? [];

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'description': description,
    'ownerId': ownerId,
    'participants': participants,
    'createdAt': createdAt.toIso8601String(),
    'lastActivity': lastActivity.toIso8601String(),
    'isActive': isActive,
    'settings': settings.toJson(),
    'sharedItems': sharedItems.map((item) => item.toJson()).toList(),
    'chatMessages': chatMessages.map((msg) => msg.toJson()).toList(),
  };
}

class User {
  final String id;
  final String name;
  final String email;
  final String avatar;
  final UserRole role;
  final bool isOnline;

  const User({
    required this.id,
    required this.name,
    required this.email,
    required this.avatar,
    required this.role,
    required this.isOnline,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'email': email,
    'avatar': avatar,
    'role': role.name,
    'isOnline': isOnline,
  };
}

class SharedItem {
  final String id;
  final SharedItemType type;
  final String title;
  final String content;
  final String description;
  final String sharedBy;
  final DateTime sharedAt;
  final String sessionId;
  final List<Comment> comments;

  SharedItem({
    required this.id,
    required this.type,
    required this.title,
    required this.content,
    required this.description,
    required this.sharedBy,
    required this.sharedAt,
    required this.sessionId,
    List<Comment>? comments,
  }) : comments = comments ?? [];

  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type.name,
    'title': title,
    'content': content,
    'description': description,
    'sharedBy': sharedBy,
    'sharedAt': sharedAt.toIso8601String(),
    'sessionId': sessionId,
    'comments': comments.map((comment) => comment.toJson()).toList(),
  };
}

class ChatMessage {
  final String id;
  final String senderId;
  final String message;
  final DateTime timestamp;
  final String sessionId;

  const ChatMessage({
    required this.id,
    required this.senderId,
    required this.message,
    required this.timestamp,
    required this.sessionId,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'senderId': senderId,
    'message': message,
    'timestamp': timestamp.toIso8601String(),
    'sessionId': sessionId,
  };
}

class Comment {
  final String id;
  final String authorId;
  final String content;
  final DateTime timestamp;
  final String itemId;

  const Comment({
    required this.id,
    required this.authorId,
    required this.content,
    required this.timestamp,
    required this.itemId,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'authorId': authorId,
    'content': content,
    'timestamp': timestamp.toIso8601String(),
    'itemId': itemId,
  };
}

class CollaborationEvent {
  final String id;
  final EventType type;
  final String sessionId;
  final String userId;
  final DateTime timestamp;
  final Map<String, dynamic> data;

  const CollaborationEvent({
    required this.id,
    required this.type,
    required this.sessionId,
    required this.userId,
    required this.timestamp,
    required this.data,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type.name,
    'sessionId': sessionId,
    'userId': userId,
    'timestamp': timestamp.toIso8601String(),
    'data': data,
  };
}

class SessionSettings {
  final bool allowChat;
  final bool allowSharing;
  final bool allowComments;
  final bool isPublic;
  final int maxParticipants;

  const SessionSettings({
    this.allowChat = true,
    this.allowSharing = true,
    this.allowComments = true,
    this.isPublic = false,
    this.maxParticipants = 10,
  });

  Map<String, dynamic> toJson() => {
    'allowChat': allowChat,
    'allowSharing': allowSharing,
    'allowComments': allowComments,
    'isPublic': isPublic,
    'maxParticipants': maxParticipants,
  };
}

class CollaborationStatistics {
  final int totalSessions;
  final int activeSessions;
  final int totalUsers;
  final int totalEvents;
  final Map<EventType, int> eventsByType;
  final double averageSessionDuration;

  const CollaborationStatistics({
    required this.totalSessions,
    required this.activeSessions,
    required this.totalUsers,
    required this.totalEvents,
    required this.eventsByType,
    required this.averageSessionDuration,
  });
}

/// Enums
enum UserRole { admin, editor, viewer }

enum SharedItemType { formula, layout, data, template }

enum EventType {
  sessionCreated,
  userJoined,
  userLeft,
  formulaShared,
  layoutShared,
  messageReceived,
  commentAdded,
}
