import 'package:uuid/uuid.dart';

enum GoalCategory {
  emergencyFund,
  vacation,
  purchase,
  investment,
  education,
  home,
  car,
  retirement,
  debt,
  other;

  String get displayName {
    switch (this) {
      case GoalCategory.emergencyFund:
        return 'Emergency Fund';
      case GoalCategory.vacation:
        return 'Vacation';
      case GoalCategory.purchase:
        return 'Purchase';
      case GoalCategory.investment:
        return 'Investment';
      case GoalCategory.education:
        return 'Education';
      case GoalCategory.home:
        return 'Home';
      case GoalCategory.car:
        return 'Car';
      case GoalCategory.retirement:
        return 'Retirement';
      case GoalCategory.debt:
        return 'Debt Payoff';
      case GoalCategory.other:
        return 'Other';
    }
  }

  String get icon {
    switch (this) {
      case GoalCategory.emergencyFund:
        return '🛡️';
      case GoalCategory.vacation:
        return '✈️';
      case GoalCategory.purchase:
        return '🛍️';
      case GoalCategory.investment:
        return '📈';
      case GoalCategory.education:
        return '🎓';
      case GoalCategory.home:
        return '🏠';
      case GoalCategory.car:
        return '🚗';
      case GoalCategory.retirement:
        return '🏖️';
      case GoalCategory.debt:
        return '💳';
      case GoalCategory.other:
        return '🎯';
    }
  }
}

enum GoalStatus {
  active,
  completed,
  paused,
  cancelled;

  String get displayName {
    switch (this) {
      case GoalStatus.active:
        return 'Active';
      case GoalStatus.completed:
        return 'Completed';
      case GoalStatus.paused:
        return 'Paused';
      case GoalStatus.cancelled:
        return 'Cancelled';
    }
  }
}

class GoalMilestone {
  final String id;
  final String goalId;
  final String title;
  final double targetAmount;
  final DateTime targetDate;
  final bool isCompleted;
  final DateTime? completedAt;
  final DateTime createdAt;

  GoalMilestone({
    String? id,
    required this.goalId,
    required this.title,
    required this.targetAmount,
    required this.targetDate,
    this.isCompleted = false,
    this.completedAt,
    DateTime? createdAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'goalId': goalId,
      'title': title,
      'targetAmount': targetAmount,
      'targetDate': targetDate.toIso8601String(),
      'isCompleted': isCompleted ? 1 : 0,
      'completedAt': completedAt?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory GoalMilestone.fromMap(Map<String, dynamic> map) {
    return GoalMilestone(
      id: map['id'],
      goalId: map['goalId'],
      title: map['title'],
      targetAmount: map['targetAmount']?.toDouble() ?? 0.0,
      targetDate: DateTime.parse(map['targetDate']),
      isCompleted: map['isCompleted'] == 1,
      completedAt: map['completedAt'] != null ? DateTime.parse(map['completedAt']) : null,
      createdAt: DateTime.parse(map['createdAt']),
    );
  }

  GoalMilestone copyWith({
    String? goalId,
    String? title,
    double? targetAmount,
    DateTime? targetDate,
    bool? isCompleted,
    DateTime? completedAt,
  }) {
    return GoalMilestone(
      id: id,
      goalId: goalId ?? this.goalId,
      title: title ?? this.title,
      targetAmount: targetAmount ?? this.targetAmount,
      targetDate: targetDate ?? this.targetDate,
      isCompleted: isCompleted ?? this.isCompleted,
      completedAt: completedAt ?? this.completedAt,
      createdAt: createdAt,
    );
  }
}

class Goal {
  final String id;
  final String name;
  final String description;
  final double targetAmount;
  final double currentAmount;
  final DateTime targetDate;
  final GoalCategory category;
  final GoalStatus status;
  final String color;
  final List<GoalMilestone> milestones;
  final DateTime createdAt;
  final DateTime updatedAt;

  Goal({
    String? id,
    required this.name,
    required this.description,
    required this.targetAmount,
    this.currentAmount = 0.0,
    required this.targetDate,
    required this.category,
    this.status = GoalStatus.active,
    this.color = '#4CAF50',
    this.milestones = const [],
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  double get progressPercentage {
    if (targetAmount <= 0) return 0.0;
    return (currentAmount / targetAmount * 100).clamp(0.0, 100.0);
  }

  bool get isCompleted => status == GoalStatus.completed || currentAmount >= targetAmount;

  bool get isOverdue => DateTime.now().isAfter(targetDate) && !isCompleted;

  Duration get timeRemaining {
    if (isCompleted) return Duration.zero;
    final now = DateTime.now();
    if (now.isAfter(targetDate)) return Duration.zero;
    return targetDate.difference(now);
  }

  String get formattedTimeRemaining {
    final remaining = timeRemaining;
    if (remaining == Duration.zero) return isCompleted ? 'Completed' : 'Overdue';
    
    final days = remaining.inDays;
    if (days > 365) {
      final years = (days / 365).floor();
      return '$years year${years > 1 ? 's' : ''} remaining';
    } else if (days > 30) {
      final months = (days / 30).floor();
      return '$months month${months > 1 ? 's' : ''} remaining';
    } else if (days > 0) {
      return '$days day${days > 1 ? 's' : ''} remaining';
    } else {
      final hours = remaining.inHours;
      return '$hours hour${hours > 1 ? 's' : ''} remaining';
    }
  }

  double get amountRemaining => (targetAmount - currentAmount).clamp(0.0, double.infinity);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'targetAmount': targetAmount,
      'currentAmount': currentAmount,
      'targetDate': targetDate.toIso8601String(),
      'category': category.name,
      'status': status.name,
      'color': color,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory Goal.fromMap(Map<String, dynamic> map) {
    return Goal(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      targetAmount: map['targetAmount']?.toDouble() ?? 0.0,
      currentAmount: map['currentAmount']?.toDouble() ?? 0.0,
      targetDate: DateTime.parse(map['targetDate']),
      category: GoalCategory.values.firstWhere(
        (e) => e.name == map['category'],
        orElse: () => GoalCategory.other,
      ),
      status: GoalStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => GoalStatus.active,
      ),
      color: map['color'] ?? '#4CAF50',
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }

  Goal copyWith({
    String? name,
    String? description,
    double? targetAmount,
    double? currentAmount,
    DateTime? targetDate,
    GoalCategory? category,
    GoalStatus? status,
    String? color,
    List<GoalMilestone>? milestones,
    DateTime? updatedAt,
  }) {
    return Goal(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      targetAmount: targetAmount ?? this.targetAmount,
      currentAmount: currentAmount ?? this.currentAmount,
      targetDate: targetDate ?? this.targetDate,
      category: category ?? this.category,
      status: status ?? this.status,
      color: color ?? this.color,
      milestones: milestones ?? this.milestones,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }
}
