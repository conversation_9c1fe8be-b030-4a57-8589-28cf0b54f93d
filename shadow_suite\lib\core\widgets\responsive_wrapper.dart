import 'package:flutter/material.dart';
import '../utils/responsive_utils.dart';

class ResponsiveWrapper extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final bool applyResponsivePadding;

  const ResponsiveWrapper({
    super.key,
    required this.child,
    this.padding,
    this.applyResponsivePadding = true,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          padding ??
          (applyResponsivePadding
              ? ResponsiveUtils.getScreenPadding(context)
              : EdgeInsets.zero),
      child: child,
    );
  }
}

class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final double? responsiveScaleFactor;

  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.responsiveScaleFactor,
  });

  @override
  Widget build(BuildContext context) {
    final baseStyle = style ?? Theme.of(context).textTheme.bodyMedium!;
    final scaleFactor =
        responsiveScaleFactor ??
        (ResponsiveUtils.isMobile(context)
            ? 0.9
            : ResponsiveUtils.isTablet(context)
            ? 1.0
            : 1.1);

    final responsiveStyle = baseStyle.copyWith(
      fontSize: (baseStyle.fontSize ?? 14) * scaleFactor,
    );

    return Text(
      text,
      style: responsiveStyle,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? margin;
  final EdgeInsets? padding;
  final double? elevation;
  final Color? color;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.margin,
    this.padding,
    this.elevation,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: margin ?? ResponsiveUtils.getResponsiveMargin(context),
      elevation: elevation ?? ResponsiveUtils.getElevation(context),
      color: color,
      shape: RoundedRectangleBorder(
        borderRadius: ResponsiveUtils.getResponsiveBorderRadius(context),
      ),
      child: Padding(
        padding: padding ?? ResponsiveUtils.getResponsivePadding(context),
        child: child,
      ),
    );
  }
}

class ResponsiveButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final ButtonStyle? style;
  final bool isPrimary;

  const ResponsiveButton({
    super.key,
    required this.child,
    this.onPressed,
    this.style,
    this.isPrimary = false,
  });

  @override
  Widget build(BuildContext context) {
    final buttonHeight = ResponsiveUtils.getButtonHeight(context);

    final defaultStyle = isPrimary
        ? ElevatedButton.styleFrom(
            minimumSize: Size(double.infinity, buttonHeight),
            padding: ResponsiveUtils.getResponsivePadding(context),
          )
        : OutlinedButton.styleFrom(
            minimumSize: Size(double.infinity, buttonHeight),
            padding: ResponsiveUtils.getResponsivePadding(context),
          );

    if (isPrimary) {
      return ElevatedButton(
        onPressed: onPressed,
        style: style ?? defaultStyle,
        child: child,
      );
    } else {
      return OutlinedButton(
        onPressed: onPressed,
        style: style ?? defaultStyle,
        child: child,
      );
    }
  }
}

class ResponsiveGridView extends StatelessWidget {
  final List<Widget> children;
  final double? childAspectRatio;
  final EdgeInsets? padding;
  final double? mainAxisSpacing;
  final double? crossAxisSpacing;

  const ResponsiveGridView({
    super.key,
    required this.children,
    this.childAspectRatio,
    this.padding,
    this.mainAxisSpacing,
    this.crossAxisSpacing,
  });

  @override
  Widget build(BuildContext context) {
    final crossAxisCount = ResponsiveUtils.getCrossAxisCount(context);

    return GridView.count(
      crossAxisCount: crossAxisCount,
      childAspectRatio: childAspectRatio ?? 1.0,
      padding: padding ?? ResponsiveUtils.getScreenPadding(context),
      mainAxisSpacing: mainAxisSpacing ?? 16,
      crossAxisSpacing: crossAxisSpacing ?? 16,
      children: children,
    );
  }
}

class ResponsiveDialog extends StatelessWidget {
  final Widget child;
  final String? title;
  final List<Widget>? actions;

  const ResponsiveDialog({
    super.key,
    required this.child,
    this.title,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    final dialogWidth = ResponsiveUtils.getDialogWidth(context);
    final maxWidth = ResponsiveUtils.getMaxDialogWidth(context);

    return Dialog(
      child: Container(
        width: dialogWidth,
        constraints: BoxConstraints(maxWidth: maxWidth),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (title != null)
              Container(
                width: double.infinity,
                padding: ResponsiveUtils.getResponsivePadding(context),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: ResponsiveText(
                  title!,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            Flexible(
              child: Padding(
                padding: ResponsiveUtils.getResponsivePadding(context),
                child: child,
              ),
            ),
            if (actions != null)
              Padding(
                padding: ResponsiveUtils.getResponsivePadding(context),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: actions!,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class ResponsiveListTile extends StatelessWidget {
  final Widget? leading;
  final Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;

  const ResponsiveListTile({
    super.key,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final height = ResponsiveUtils.getListTileHeight(context);

    return SizedBox(
      height: height,
      child: ListTile(
        leading: leading,
        title: title,
        subtitle: subtitle,
        trailing: trailing,
        onTap: onTap,
        contentPadding: ResponsiveUtils.getResponsivePadding(context),
      ),
    );
  }
}

class ResponsiveAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget? title;
  final List<Widget>? actions;
  final Widget? leading;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const ResponsiveAppBar({
    super.key,
    this.title,
    this.actions,
    this.leading,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: title,
      actions: actions,
      leading: leading,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      toolbarHeight: ResponsiveUtils.getAppBarHeight(context),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
