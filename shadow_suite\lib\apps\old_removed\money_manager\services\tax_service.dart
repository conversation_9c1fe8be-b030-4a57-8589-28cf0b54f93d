import 'dart:async';
import 'dart:math' as math;
import '../models/money_manager_models.dart';
import '../../../core/database/database_service.dart';
import '../../../core/services/error_handler.dart' as error_handler;
import '../models/tax_models.dart';

class TaxService {
  static final List<TaxCalculation> _taxCalculations = [];
  static final List<TaxDeduction> _deductions = [];
  static final List<TaxDocument> _documents = [];

  // Initialize tax service
  static Future<void> initialize() async {
    await _loadTaxCalculations();
    await _loadDeductions();
    await _loadDocuments();
  }

  // FEATURE 21: Tax Calculations
  static Future<TaxCalculation> calculateTaxes({
    required int taxYear,
    required double grossIncome,
    required FilingStatus filingStatus,
    required List<TaxDeduction> deductions,
    required List<TaxCredit> credits,
    String? state,
  }) async {
    try {
      // Federal tax calculation
      final federalTax = _calculateFederalTax(
        grossIncome,
        filingStatus,
        taxYear,
      );

      // State tax calculation
      final stateTax = state != null
          ? _calculateStateTax(grossIncome, state, taxYear)
          : 0.0;

      // Calculate deductions
      final totalDeductions = deductions.fold<double>(
        0,
        (sum, d) => sum + d.amount,
      );
      final standardDeduction = _getStandardDeduction(filingStatus, taxYear);
      final itemizedDeductions = totalDeductions;
      final finalDeductions = math.max(standardDeduction, itemizedDeductions);

      // Calculate credits
      final totalCredits = credits.fold<double>(0, (sum, c) => sum + c.amount);

      // Adjusted gross income
      final adjustedGrossIncome = grossIncome - finalDeductions;

      // Final tax calculation
      final federalTaxAfterCredits = math.max(0, federalTax - totalCredits);
      final totalTax = federalTaxAfterCredits + stateTax;

      // Effective tax rate
      final effectiveTaxRate = grossIncome > 0
          ? (totalTax / grossIncome) * 100
          : 0;

      // Marginal tax rate
      final marginalTaxRate = _getMarginalTaxRate(
        adjustedGrossIncome,
        filingStatus,
        taxYear,
      );

      final calculation = TaxCalculation(
        id: 'tax_calc_${DateTime.now().millisecondsSinceEpoch}',
        taxYear: taxYear,
        grossIncome: grossIncome,
        adjustedGrossIncome: adjustedGrossIncome,
        filingStatus: filingStatus,
        standardDeduction: standardDeduction,
        itemizedDeductions: itemizedDeductions,
        totalDeductions: finalDeductions,
        federalTax: federalTax,
        stateTax: stateTax,
        totalCredits: totalCredits,
        totalTax: totalTax,
        effectiveTaxRate: effectiveTaxRate.toDouble(),
        marginalTaxRate: marginalTaxRate,
        refundOwed: 0, // Would be calculated based on withholdings
        deductions: deductions,
        credits: credits,
        state: state,
        calculatedAt: DateTime.now(),
      );

      await DatabaseService.safeInsert(
        'tax_calculations',
        calculation.toJson(),
      );
      _taxCalculations.add(calculation);

      return calculation;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Calculate taxes',
      );
      rethrow;
    }
  }

  // FEATURE 22: Tax Deduction Tracking
  static Future<TaxDeduction> addDeduction({
    required String category,
    required String description,
    required double amount,
    required DateTime date,
    required int taxYear,
    String? receiptPath,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final deduction = TaxDeduction(
        id: 'deduction_${DateTime.now().millisecondsSinceEpoch}',
        category: category,
        description: description,
        amount: amount,
        date: date,
        taxYear: taxYear,
        receiptPath: receiptPath,
        isVerified: receiptPath != null,
        metadata: metadata ?? {},
        createdAt: DateTime.now(),
      );

      await DatabaseService.safeInsert('tax_deductions', deduction.toJson());
      _deductions.add(deduction);

      return deduction;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.database,
        context: 'Add tax deduction',
      );
      rethrow;
    }
  }

  // FEATURE 23: Tax Document Management
  static Future<TaxDocument> uploadTaxDocument({
    required String name,
    required TaxDocumentType type,
    required String filePath,
    required int taxYear,
    String? description,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final document = TaxDocument(
        id: 'tax_doc_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        type: type,
        filePath: filePath,
        taxYear: taxYear,
        description: description ?? '',
        fileSize: await _getFileSize(filePath),
        uploadedAt: DateTime.now(),
        metadata: metadata ?? {},
      );

      await DatabaseService.safeInsert('tax_documents', document.toJson());
      _documents.add(document);

      return document;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.database,
        context: 'Upload tax document',
      );
      rethrow;
    }
  }

  // FEATURE 24: Tax Planning and Projections
  static Future<TaxProjection> projectTaxes({
    required double projectedIncome,
    required FilingStatus filingStatus,
    required List<TaxDeduction> plannedDeductions,
    required List<TaxCredit> plannedCredits,
    String? state,
  }) async {
    try {
      final currentYear = DateTime.now().year;

      // Calculate current year projection
      final currentYearTax = await calculateTaxes(
        taxYear: currentYear,
        grossIncome: projectedIncome,
        filingStatus: filingStatus,
        deductions: plannedDeductions,
        credits: plannedCredits,
        state: state,
      );

      // Calculate optimization strategies
      final strategies = _generateTaxStrategies(
        projectedIncome,
        filingStatus,
        plannedDeductions,
        plannedCredits,
      );

      // Calculate quarterly estimates
      final quarterlyEstimates = _calculateQuarterlyEstimates(
        currentYearTax.totalTax,
      );

      return TaxProjection(
        projectedIncome: projectedIncome,
        projectedTax: currentYearTax.totalTax,
        effectiveTaxRate: currentYearTax.effectiveTaxRate,
        marginalTaxRate: currentYearTax.marginalTaxRate,
        quarterlyEstimates: quarterlyEstimates,
        optimizationStrategies: strategies,
        projectedAt: DateTime.now(),
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Project taxes',
      );
      rethrow;
    }
  }

  // FEATURE 25: Expense Analytics and Categorization
  static Future<ExpenseAnalytics> analyzeExpenses({
    required DateTime startDate,
    required DateTime endDate,
    List<String>? categoryFilter,
    bool includeTaxDeductible = false,
  }) async {
    try {
      // Get transactions from database
      final transactions = await _getTransactionsForPeriod(startDate, endDate);

      // Filter by categories if specified
      var filteredTransactions = transactions;
      if (categoryFilter != null && categoryFilter.isNotEmpty) {
        filteredTransactions = transactions
            .where((t) => categoryFilter.contains(t.categoryId))
            .toList();
      }

      // Calculate analytics
      final totalExpenses = filteredTransactions
          .where((t) => t.type == TransactionType.expense)
          .fold<double>(0, (sum, t) => sum + t.amount);

      final averageDaily =
          totalExpenses / startDate.difference(endDate).inDays.abs();
      final averageMonthly = averageDaily * 30;

      // Category breakdown
      final categoryBreakdown = <String, double>{};
      for (final transaction in filteredTransactions) {
        final categoryId = transaction.categoryId;
        categoryBreakdown[categoryId] =
            (categoryBreakdown[categoryId] ?? 0.0) + transaction.amount;
      }

      // Trend analysis
      final trends = _calculateExpenseTrends(
        filteredTransactions,
        startDate,
        endDate,
      );

      // Tax deductible expenses
      final taxDeductibleExpenses = includeTaxDeductible
          ? _identifyTaxDeductibleExpenses(filteredTransactions)
          : <dynamic>[];

      // Spending patterns
      final patterns = _analyzeSpendingPatterns(filteredTransactions);

      // Recommendations
      final recommendations = _generateExpenseRecommendations(
        categoryBreakdown,
        trends,
        patterns,
      );

      return ExpenseAnalytics(
        startDate: startDate,
        endDate: endDate,
        totalExpenses: totalExpenses,
        averageDaily: averageDaily,
        averageMonthly: averageMonthly,
        categoryBreakdown: categoryBreakdown,
        trends: trends,
        taxDeductibleExpenses: taxDeductibleExpenses,
        spendingPatterns: patterns,
        recommendations: recommendations,
        analyzedAt: DateTime.now(),
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Analyze expenses',
      );
      rethrow;
    }
  }

  // Helper methods
  static double _calculateFederalTax(
    double income,
    FilingStatus status,
    int year,
  ) {
    // Simplified federal tax calculation using 2023 tax brackets
    final brackets = _getFederalTaxBrackets(status, year);
    var tax = 0.0;
    var remainingIncome = income;

    for (final bracket in brackets) {
      if (remainingIncome <= 0) break;

      final taxableAtThisBracket = math.min(
        remainingIncome,
        bracket.upperLimit - bracket.lowerLimit,
      );
      tax += taxableAtThisBracket * (bracket.rate / 100);
      remainingIncome -= taxableAtThisBracket;
    }

    return tax;
  }

  static double _calculateStateTax(double income, String state, int year) {
    // Simplified state tax calculation
    final stateRates = {
      'CA': 9.3,
      'NY': 8.82,
      'NJ': 10.75,
      'CT': 6.99,
      'TX': 0.0,
      'FL': 0.0,
      'WA': 0.0,
      'NV': 0.0,
    };

    final rate = stateRates[state] ?? 5.0; // Default 5% for other states
    return income * (rate / 100);
  }

  static double _getStandardDeduction(FilingStatus status, int year) {
    // 2023 standard deductions
    switch (status) {
      case FilingStatus.single:
        return 13850;
      case FilingStatus.marriedFilingJointly:
        return 27700;
      case FilingStatus.marriedFilingSeparately:
        return 13850;
      case FilingStatus.headOfHousehold:
        return 20800;
    }
  }

  static double _getMarginalTaxRate(
    double income,
    FilingStatus status,
    int year,
  ) {
    final brackets = _getFederalTaxBrackets(status, year);

    for (final bracket in brackets.reversed) {
      if (income >= bracket.lowerLimit) {
        return bracket.rate;
      }
    }

    return brackets.first.rate;
  }

  static List<TaxBracket> _getFederalTaxBrackets(
    FilingStatus status,
    int year,
  ) {
    // 2023 federal tax brackets for single filers
    if (status == FilingStatus.single) {
      return [
        TaxBracket(0, 11000, 10),
        TaxBracket(11000, 44725, 12),
        TaxBracket(44725, 95375, 22),
        TaxBracket(95375, 182050, 24),
        TaxBracket(182050, 231250, 32),
        TaxBracket(231250, 578125, 35),
        TaxBracket(578125, double.infinity, 37),
      ];
    }

    // Add other filing statuses as needed
    return [TaxBracket(0, double.infinity, 22)]; // Default
  }

  static List<TaxStrategy> _generateTaxStrategies(
    double income,
    FilingStatus status,
    List<TaxDeduction> deductions,
    List<TaxCredit> credits,
  ) {
    final strategies = <TaxStrategy>[];

    // Strategy 1: Maximize retirement contributions
    strategies.add(
      TaxStrategy(
        name: 'Maximize 401(k) Contributions',
        description:
            'Contribute the maximum amount to your 401(k) to reduce taxable income',
        potentialSavings:
            income * 0.22 * 0.15, // Assume 22% tax rate, 15% contribution
        category: 'Retirement',
        priority: TaxStrategyPriority.high,
      ),
    );

    // Strategy 2: Health Savings Account
    if (income > 50000) {
      strategies.add(
        TaxStrategy(
          name: 'Maximize HSA Contributions',
          description: 'Contribute to HSA for triple tax advantage',
          potentialSavings: 3650 * 0.22, // Max HSA contribution * tax rate
          category: 'Healthcare',
          priority: TaxStrategyPriority.medium,
        ),
      );
    }

    // Strategy 3: Tax-loss harvesting
    strategies.add(
      TaxStrategy(
        name: 'Tax-Loss Harvesting',
        description: 'Realize investment losses to offset gains',
        potentialSavings: 3000 * 0.22, // Max capital loss deduction
        category: 'Investment',
        priority: TaxStrategyPriority.medium,
      ),
    );

    return strategies;
  }

  static List<double> _calculateQuarterlyEstimates(double annualTax) {
    final quarterly = annualTax / 4;
    return [quarterly, quarterly, quarterly, quarterly];
  }

  static Future<List<dynamic>> _getTransactionsForPeriod(
    DateTime start,
    DateTime end,
  ) async {
    // This would query the actual transactions table
    // For now, return empty list
    return [];
  }

  static Map<String, double> _calculateExpenseTrends(
    List<dynamic> transactions,
    DateTime start,
    DateTime end,
  ) {
    // Calculate month-over-month trends
    final trends = <String, double>{};

    // Group transactions by month
    final monthlyTotals = <String, double>{};
    for (final transaction in transactions) {
      final monthKey =
          '${transaction.date.year}-${transaction.date.month.toString().padLeft(2, '0')}';
      monthlyTotals[monthKey] =
          (monthlyTotals[monthKey] ?? 0) + transaction.amount;
    }

    // Calculate trends
    final months = monthlyTotals.keys.toList()..sort();
    for (int i = 1; i < months.length; i++) {
      final currentMonth = monthlyTotals[months[i]]!;
      final previousMonth = monthlyTotals[months[i - 1]]!;
      final trend = previousMonth > 0
          ? ((currentMonth - previousMonth) / previousMonth) * 100
          : 0;
      trends[months[i]] = trend.toDouble();
    }

    return trends;
  }

  static List<dynamic> _identifyTaxDeductibleExpenses(
    List<dynamic> transactions,
  ) {
    // Identify potentially tax-deductible expenses based on categories
    final deductibleCategories = {
      'Business Meals',
      'Office Supplies',
      'Professional Development',
      'Charitable Donations',
      'Medical Expenses',
      'Home Office',
    };

    return transactions
        .where((t) => deductibleCategories.contains(t.categoryId))
        .toList();
  }

  static Map<String, dynamic> _analyzeSpendingPatterns(
    List<dynamic> transactions,
  ) {
    final patterns = <String, dynamic>{};

    // Day of week analysis
    final dayOfWeekSpending = <int, double>{};
    for (final transaction in transactions) {
      final dayOfWeek = transaction.date.weekday;
      dayOfWeekSpending[dayOfWeek] =
          (dayOfWeekSpending[dayOfWeek] ?? 0) + transaction.amount;
    }
    patterns['dayOfWeek'] = dayOfWeekSpending;

    // Time of day analysis (if available)
    patterns['timeOfDay'] = <String, double>{
      'morning': 0,
      'afternoon': 0,
      'evening': 0,
      'night': 0,
    };

    return patterns;
  }

  static List<String> _generateExpenseRecommendations(
    Map<String, double> categoryBreakdown,
    Map<String, double> trends,
    Map<String, dynamic> patterns,
  ) {
    final recommendations = <String>[];

    // High spending categories
    final sortedCategories = categoryBreakdown.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    if (sortedCategories.isNotEmpty) {
      final topCategory = sortedCategories.first;
      recommendations.add(
        'Consider reducing spending in ${topCategory.key} - your highest expense category',
      );
    }

    // Increasing trends
    final increasingTrends = trends.entries.where((e) => e.value > 10).toList();
    if (increasingTrends.isNotEmpty) {
      recommendations.add(
        'Monitor increasing spending trends in recent months',
      );
    }

    // General recommendations
    recommendations.addAll([
      'Set up automatic savings to reduce discretionary spending',
      'Review subscriptions and recurring charges monthly',
      'Use the 24-hour rule for non-essential purchases over \$100',
    ]);

    return recommendations;
  }

  static Future<int> _getFileSize(String filePath) async {
    try {
      // In a real implementation, get actual file size
      // In a real implementation, get actual file size from file system
      return 1024; // Simulated file size
    } catch (e) {
      return 0;
    }
  }

  static Future<void> _loadTaxCalculations() async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM tax_calculations',
      );
      _taxCalculations.clear();
      for (final row in results) {
        _taxCalculations.add(TaxCalculation.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Load tax calculations',
      );
    }
  }

  static Future<void> _loadDeductions() async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM tax_deductions',
      );
      _deductions.clear();
      for (final row in results) {
        _deductions.add(TaxDeduction.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Load deductions',
      );
    }
  }

  static Future<void> _loadDocuments() async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM tax_documents',
      );
      _documents.clear();
      for (final row in results) {
        _documents.add(TaxDocument.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Load documents',
      );
    }
  }

  // Getters
  static List<TaxCalculation> get taxCalculations =>
      List.unmodifiable(_taxCalculations);
  static List<TaxDeduction> get deductions => List.unmodifiable(_deductions);
  static List<TaxDocument> get documents => List.unmodifiable(_documents);

  // Dispose
  static void dispose() {
    _taxCalculations.clear();
    _deductions.clear();
    _documents.clear();
  }
}
