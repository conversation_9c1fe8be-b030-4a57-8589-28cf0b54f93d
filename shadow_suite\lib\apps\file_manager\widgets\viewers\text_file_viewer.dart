import 'package:flutter/material.dart';
import '../../services/file_viewer_service.dart';

class TextFileViewer extends StatefulWidget {
  final String filePath;
  final bool isEditable;
  final VoidCallback? onSave;
  final VoidCallback? onClose;

  const TextFileViewer({
    super.key,
    required this.filePath,
    this.isEditable = true,
    this.onSave,
    this.onClose,
  });

  @override
  State<TextFileViewer> createState() => _TextFileViewerState();
}

class _TextFileViewerState extends State<TextFileViewer> {
  late TextEditingController _controller;
  bool _isLoading = true;
  bool _hasChanges = false;
  String _originalContent = '';
  int _lineCount = 0;
  int _currentLine = 1;
  int _currentColumn = 1;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _controller.addListener(_onTextChanged);
    _loadFile();
  }

  Future<void> _loadFile() async {
    try {
      final content = await FileViewerService.readTextFile(widget.filePath);
      setState(() {
        _originalContent = content;
        _controller.text = content;
        _lineCount = content.split('\n').length;
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _isLoading = false;
      });
      _showError('Error loading file: $error');
    }
  }

  void _onTextChanged() {
    final hasChanges = _controller.text != _originalContent;
    if (hasChanges != _hasChanges) {
      setState(() {
        _hasChanges = hasChanges;
        _lineCount = _controller.text.split('\n').length;
      });
    }

    // Update cursor position
    final selection = _controller.selection;
    if (selection.isValid) {
      final textBeforeCursor = _controller.text.substring(
        0,
        selection.baseOffset,
      );
      final lines = textBeforeCursor.split('\n');
      setState(() {
        _currentLine = lines.length;
        _currentColumn = lines.last.length + 1;
      });
    }
  }

  Future<void> _saveFile() async {
    if (!widget.isEditable || !_hasChanges) return;

    try {
      await FileViewerService.writeTextFile(widget.filePath, _controller.text);
      setState(() {
        _originalContent = _controller.text;
        _hasChanges = false;
      });
      _showSuccess('File saved successfully');
      widget.onSave?.call();
    } catch (error) {
      _showError('Error saving file: $error');
    }
  }

  @override
  Widget build(BuildContext context) {
    final fileName = widget.filePath.split('/').last;

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Icon(
              FileViewerService.getFileIcon(fileName),
              color: FileViewerService.getFileColor(fileName),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                fileName + (_hasChanges ? ' *' : ''),
                style: const TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF2C3E50),
        elevation: 1,
        actions: [
          if (widget.isEditable) ...[
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _hasChanges ? _saveFile : null,
              tooltip: 'Save (Ctrl+S)',
            ),
            IconButton(
              icon: const Icon(Icons.undo),
              onPressed: _hasChanges ? _revertChanges : null,
              tooltip: 'Revert Changes',
            ),
          ],
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
            tooltip: 'Find (Ctrl+F)',
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: _closeFile,
            tooltip: 'Close',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Toolbar
                Container(
                  height: 40,
                  decoration: const BoxDecoration(
                    color: Color(0xFFF8F9FA),
                    border: Border(
                      bottom: BorderSide(color: Color(0xFFE9ECEF)),
                    ),
                  ),
                  child: Row(
                    children: [
                      const SizedBox(width: 16),
                      Text(
                        'Lines: $_lineCount',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF6C757D),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Text(
                        'Ln $_currentLine, Col $_currentColumn',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF6C757D),
                        ),
                      ),
                      const Spacer(),
                      if (widget.isEditable) ...[
                        Text(
                          _hasChanges ? 'Modified' : 'Saved',
                          style: TextStyle(
                            fontSize: 12,
                            color: _hasChanges
                                ? Colors.orange
                                : const Color(0xFF28A745),
                          ),
                        ),
                        const SizedBox(width: 16),
                      ],
                    ],
                  ),
                ),

                // Text editor
                Expanded(
                  child: Container(
                    decoration: const BoxDecoration(color: Colors.white),
                    child: TextField(
                      controller: _controller,
                      readOnly: !widget.isEditable,
                      maxLines: null,
                      expands: true,
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 14,
                        height: 1.4,
                      ),
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.all(16),
                      ),
                      onChanged: (_) => _onTextChanged(),
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  void _revertChanges() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Revert Changes'),
        content: const Text(
          'Are you sure you want to revert all changes? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _controller.text = _originalContent;
                _hasChanges = false;
              });
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('Revert'),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    String searchTerm = '';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Find Text'),
        content: TextField(
          decoration: const InputDecoration(
            hintText: 'Enter search term...',
            border: OutlineInputBorder(),
          ),
          onChanged: (value) => searchTerm = value,
          onSubmitted: (value) {
            Navigator.pop(context);
            _findText(value);
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _findText(searchTerm);
            },
            child: const Text('Find'),
          ),
        ],
      ),
    );
  }

  void _findText(String searchTerm) {
    if (searchTerm.isEmpty) return;

    final text = _controller.text;
    final index = text.indexOf(searchTerm);

    if (index != -1) {
      _controller.selection = TextSelection(
        baseOffset: index,
        extentOffset: index + searchTerm.length,
      );
    } else {
      _showInfo('Text not found');
    }
  }

  void _closeFile() {
    if (_hasChanges) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Unsaved Changes'),
          content: const Text(
            'You have unsaved changes. Do you want to save before closing?',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                widget.onClose?.call();
                Navigator.pop(context);
              },
              child: const Text('Discard'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                final navigator = Navigator.of(context);
                await _saveFile();
                widget.onClose?.call();
                if (mounted) {
                  navigator.pop();
                }
              },
              child: const Text('Save & Close'),
            ),
          ],
        ),
      );
    } else {
      widget.onClose?.call();
      Navigator.pop(context);
    }
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF28A745),
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  void _showInfo(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF17A2B8),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
