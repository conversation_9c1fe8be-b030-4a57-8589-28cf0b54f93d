import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../../core/theme/app_theme.dart';
import '../../services/memo_providers.dart';
import '../../models/voice_memo.dart';

enum RecordingState { idle, recording, paused, stopped }

class VoiceMemoRecordingScreen extends ConsumerStatefulWidget {
  const VoiceMemoRecordingScreen({super.key});

  @override
  ConsumerState<VoiceMemoRecordingScreen> createState() =>
      _VoiceMemoRecordingScreenState();
}

class _VoiceMemoRecordingScreenState
    extends ConsumerState<VoiceMemoRecordingScreen>
    with TickerProviderStateMixin {
  RecordingState _recordingState = RecordingState.idle;
  Duration _recordingDuration = Duration.zero;
  Timer? _timer;
  String? _recordingPath;
  late AnimationController _waveformController;
  late AnimationController _pulseController;

  AudioQuality _selectedQuality = AudioQuality.medium;
  String _selectedCategory = VoiceMemoCategory.personal.displayName;
  final TextEditingController _titleController = TextEditingController();
  final List<String> _tags = [];
  final TextEditingController _tagController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _waveformController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    _waveformController.dispose();
    _pulseController.dispose();
    _titleController.dispose();
    _tagController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Record Voice Memo'),
        backgroundColor: AppTheme.memoSuiteColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            if (_recordingState == RecordingState.recording) {
              _showExitDialog();
            } else {
              ref.read(memoSuiteCurrentScreenProvider.notifier).state =
                  MemoSuiteScreen.voiceMemosList;
            }
          },
        ),
        actions: [
          if (_recordingState == RecordingState.stopped)
            IconButton(icon: const Icon(Icons.save), onPressed: _saveRecording),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  _buildRecordingVisualizer(),
                  const SizedBox(height: 32),
                  _buildRecordingInfo(),
                  const SizedBox(height: 32),
                  _buildRecordingSettings(),
                  if (_recordingState == RecordingState.stopped) ...[
                    const SizedBox(height: 32),
                    _buildSaveForm(),
                  ],
                ],
              ),
            ),
          ),
          _buildRecordingControls(),
        ],
      ),
    );
  }

  Widget _buildRecordingVisualizer() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedBuilder(
            animation: _pulseController,
            builder: (context, child) {
              return Container(
                width:
                    80 +
                    (_recordingState == RecordingState.recording
                        ? _pulseController.value * 20
                        : 0),
                height:
                    80 +
                    (_recordingState == RecordingState.recording
                        ? _pulseController.value * 20
                        : 0),
                decoration: BoxDecoration(
                  color: _getRecordingColor().withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: _getRecordingColor(),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      _getRecordingIcon(),
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 24),
          Text(
            _formatDuration(_recordingDuration),
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: _getRecordingColor(),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _getRecordingStatusText(),
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildRecordingInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recording Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.memoSuiteColor,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Duration', _formatDuration(_recordingDuration)),
            const SizedBox(height: 8),
            _buildInfoRow('Quality', _selectedQuality.displayName),
            const SizedBox(height: 8),
            _buildInfoRow('Category', _selectedCategory),
            const SizedBox(height: 8),
            _buildInfoRow('Status', _getRecordingStatusText()),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordingSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recording Settings',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.memoSuiteColor,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<AudioQuality>(
                    value: _selectedQuality,
                    decoration: const InputDecoration(
                      labelText: 'Audio Quality',
                      border: OutlineInputBorder(),
                    ),
                    items: AudioQuality.values.map((quality) {
                      return DropdownMenuItem(
                        value: quality,
                        child: Text(quality.displayName),
                      );
                    }).toList(),
                    onChanged: _recordingState == RecordingState.idle
                        ? (value) {
                            if (value != null) {
                              setState(() {
                                _selectedQuality = value;
                              });
                            }
                          }
                        : null,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedCategory,
                    decoration: const InputDecoration(
                      labelText: 'Category',
                      border: OutlineInputBorder(),
                    ),
                    items: VoiceMemoCategory.allCategories.map((category) {
                      return DropdownMenuItem(
                        value: category,
                        child: Text(category),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedCategory = value;
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveForm() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Save Recording',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.memoSuiteColor,
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Title *',
                border: OutlineInputBorder(),
                hintText: 'Enter a title for your voice memo',
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _tagController,
                    decoration: InputDecoration(
                      labelText: 'Add tags',
                      border: const OutlineInputBorder(),
                      suffixIcon: IconButton(
                        icon: const Icon(Icons.add),
                        onPressed: _addTag,
                      ),
                    ),
                    onSubmitted: (_) => _addTag(),
                  ),
                ),
              ],
            ),
            if (_tags.isNotEmpty) ...[
              const SizedBox(height: 16),
              Wrap(
                spacing: 8,
                children: _tags
                    .map(
                      (tag) => Chip(
                        label: Text(tag),
                        deleteIcon: const Icon(Icons.close, size: 16),
                        onDeleted: () => _removeTag(tag),
                      ),
                    )
                    .toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRecordingControls() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey, width: 0.2)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          if (_recordingState != RecordingState.idle)
            _buildControlButton(
              icon: Icons.stop,
              label: 'Stop',
              color: Colors.red,
              onPressed: _stopRecording,
            ),
          _buildControlButton(
            icon: _getMainControlIcon(),
            label: _getMainControlLabel(),
            color: _getRecordingColor(),
            onPressed: _handleMainControl,
            isMain: true,
          ),
          if (_recordingState == RecordingState.stopped)
            _buildControlButton(
              icon: Icons.delete,
              label: 'Delete',
              color: Colors.grey,
              onPressed: _deleteRecording,
            ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
    bool isMain = false,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: isMain ? 80 : 60,
          height: isMain ? 80 : 60,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: color.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: IconButton(
            onPressed: onPressed,
            icon: Icon(icon, color: Colors.white, size: isMain ? 40 : 30),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: color,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
      ],
    );
  }

  void _handleMainControl() {
    switch (_recordingState) {
      case RecordingState.idle:
        _startRecording();
        break;
      case RecordingState.recording:
        _pauseRecording();
        break;
      case RecordingState.paused:
        _resumeRecording();
        break;
      case RecordingState.stopped:
        _playRecording();
        break;
    }
  }

  Future<void> _startRecording() async {
    // Check microphone permission
    final permission = await Permission.microphone.request();
    if (permission != PermissionStatus.granted) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Microphone permission is required to record audio'),
          ),
        );
      }
      return;
    }

    setState(() {
      _recordingState = RecordingState.recording;
      _recordingDuration = Duration.zero;
    });

    _pulseController.repeat();
    _waveformController.repeat();

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _recordingDuration = Duration(
          seconds: _recordingDuration.inSeconds + 1,
        );
      });
    });

    // Start actual audio recording
    await _startAudioRecording();
  }

  void _pauseRecording() {
    setState(() {
      _recordingState = RecordingState.paused;
    });

    _pulseController.stop();
    _waveformController.stop();
    _timer?.cancel();

    // Simulate recording pause - in a real app, you would pause the audio recording
  }

  void _resumeRecording() {
    setState(() {
      _recordingState = RecordingState.recording;
    });

    _pulseController.repeat();
    _waveformController.repeat();

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _recordingDuration = Duration(
          seconds: _recordingDuration.inSeconds + 1,
        );
      });
    });

    // Simulate recording resume - in a real app, you would resume the audio recording
  }

  void _stopRecording() {
    setState(() {
      _recordingState = RecordingState.stopped;
    });

    _pulseController.stop();
    _waveformController.stop();
    _timer?.cancel();

    // Set default title if empty
    if (_titleController.text.isEmpty) {
      _titleController.text =
          'Voice Memo ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}';
    }

    // Stop actual recording implementation would go here
  }

  void _playRecording() {
    if (_recordingPath == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No recording available to play')),
      );
      return;
    }

    // In a real implementation, you would use just_audio or similar package
    // For now, provide feedback that playback would start
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Playing recording: $_recordingPath')),
    );

    // Simulate playback state
    setState(() {
      // Could add playback state management here
    });
  }

  Future<void> _startAudioRecording() async {
    try {
      // Get the documents directory for storing recordings
      final directory = await getApplicationDocumentsDirectory();
      final recordingsDir = Directory('${directory.path}/voice_recordings');

      // Create recordings directory if it doesn't exist
      if (!await recordingsDir.exists()) {
        await recordingsDir.create(recursive: true);
      }

      // Generate a unique filename with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'voice_memo_$timestamp.m4a';
      _recordingPath = '${recordingsDir.path}/$fileName';

      // For cross-platform compatibility, we'll implement a basic recording simulation
      // In a production app, you would use packages like:
      // - record: for audio recording
      // - just_audio: for playback
      // - flutter_sound: for advanced audio features

      print('Recording started: $_recordingPath');

      // Create a placeholder file to simulate recording
      final file = File(_recordingPath!);
      await file.writeAsString(
        'Audio recording placeholder - ${DateTime.now()}',
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to start recording: $e')),
        );
      }
      setState(() {
        _recordingState = RecordingState.idle;
      });
    }
  }

  void _deleteRecording() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Recording'),
        content: const Text('Are you sure you want to delete this recording?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _recordingState = RecordingState.idle;
                _recordingDuration = Duration.zero;
                _titleController.clear();
                _tags.clear();
              });
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _saveRecording() async {
    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Please enter a title')));
      return;
    }

    try {
      // Generate file path for the recording
      final directory = await getApplicationDocumentsDirectory();
      final filePath =
          '${directory.path}/voice_memo_${DateTime.now().millisecondsSinceEpoch}.m4a';

      // Calculate estimated file size based on duration and quality
      final estimatedFileSize = _calculateFileSize(
        _recordingDuration,
        _selectedQuality,
      );

      final voiceMemo = VoiceMemo(
        title: _titleController.text.trim(),
        filePath: filePath,
        duration: _recordingDuration,
        category: _selectedCategory,
        tags: _tags,
        fileSize: estimatedFileSize,
      );

      await ref.read(voiceMemosProvider.notifier).addVoiceMemo(voiceMemo);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Voice memo saved successfully')),
        );

        ref.read(memoSuiteCurrentScreenProvider.notifier).state =
            MemoSuiteScreen.voiceMemosList;
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error saving voice memo: $e')));
      }
    }
  }

  void _addTag() {
    final tag = _tagController.text.trim();
    if (tag.isNotEmpty && !_tags.contains(tag)) {
      setState(() {
        _tags.add(tag);
        _tagController.clear();
      });
    }
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
    });
  }

  void _showExitDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Stop Recording'),
        content: const Text(
          'Are you sure you want to stop recording and go back?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Continue Recording'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _stopRecording();
              ref.read(memoSuiteCurrentScreenProvider.notifier).state =
                  MemoSuiteScreen.voiceMemosList;
            },
            child: const Text('Stop & Exit'),
          ),
        ],
      ),
    );
  }

  Color _getRecordingColor() {
    switch (_recordingState) {
      case RecordingState.idle:
        return Colors.grey;
      case RecordingState.recording:
        return Colors.red;
      case RecordingState.paused:
        return Colors.orange;
      case RecordingState.stopped:
        return AppTheme.memoSuiteColor;
    }
  }

  IconData _getRecordingIcon() {
    switch (_recordingState) {
      case RecordingState.idle:
        return Icons.mic;
      case RecordingState.recording:
        return Icons.mic;
      case RecordingState.paused:
        return Icons.pause;
      case RecordingState.stopped:
        return Icons.stop;
    }
  }

  IconData _getMainControlIcon() {
    switch (_recordingState) {
      case RecordingState.idle:
        return Icons.mic;
      case RecordingState.recording:
        return Icons.pause;
      case RecordingState.paused:
        return Icons.mic;
      case RecordingState.stopped:
        return Icons.play_arrow;
    }
  }

  String _getMainControlLabel() {
    switch (_recordingState) {
      case RecordingState.idle:
        return 'Record';
      case RecordingState.recording:
        return 'Pause';
      case RecordingState.paused:
        return 'Resume';
      case RecordingState.stopped:
        return 'Play';
    }
  }

  String _getRecordingStatusText() {
    switch (_recordingState) {
      case RecordingState.idle:
        return 'Ready to record';
      case RecordingState.recording:
        return 'Recording...';
      case RecordingState.paused:
        return 'Recording paused';
      case RecordingState.stopped:
        return 'Recording completed';
    }
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  double _calculateFileSize(Duration duration, AudioQuality quality) {
    // Estimate file size based on duration and quality
    // These are approximate values for M4A format
    final durationInMinutes = duration.inSeconds / 60.0;

    switch (quality) {
      case AudioQuality.low:
        return durationInMinutes * 0.5; // ~0.5 MB per minute
      case AudioQuality.medium:
        return durationInMinutes * 1.0; // ~1.0 MB per minute
      case AudioQuality.high:
        return durationInMinutes * 2.0; // ~2.0 MB per minute
      case AudioQuality.veryHigh:
        return durationInMinutes * 3.0; // ~3.0 MB per minute
    }
  }
}
