import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/app_theme.dart';
import '../models/tool.dart';
import '../models/ui_component.dart';
import '../services/tools_builder_providers.dart';
import '../services/excel_service.dart';

class ToolRuntimeScreen extends ConsumerStatefulWidget {
  final Tool tool;

  const ToolRuntimeScreen({super.key, required this.tool});

  @override
  ConsumerState<ToolRuntimeScreen> createState() => _ToolRuntimeScreenState();
}

class _ToolRuntimeScreenState extends ConsumerState<ToolRuntimeScreen> {
  bool _isFullscreen = false;
  double _zoomLevel = 1.0;

  @override
  void initState() {
    super.initState();
    // Initialize the tool runtime
    ref.read(currentToolProvider.notifier).setTool(widget.tool);
    if (widget.tool.spreadsheet != null) {
      ref
          .read(spreadsheetCalculationProvider.notifier)
          .setSpreadsheet(widget.tool.spreadsheet!);
    }
    ref.read(toolRuntimeProvider.notifier).clearValues();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: _isFullscreen ? null : _buildAppBar(),
      body: Column(
        children: [
          if (!_isFullscreen) _buildToolbar(),
          Expanded(child: _buildToolInterface()),
        ],
      ),
      floatingActionButton: _buildFloatingActions(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(widget.tool.name),
      backgroundColor: AppTheme.toolsBuilderColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          onPressed: () => _shareResults(),
          icon: const Icon(Icons.share),
          tooltip: 'Share Results',
        ),
        IconButton(
          onPressed: () => _exportResults(),
          icon: const Icon(Icons.download),
          tooltip: 'Export Results',
        ),
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(value: 'reset', child: Text('Reset Values')),
            const PopupMenuItem(
              value: 'save_template',
              child: Text('Save as Template'),
            ),
            const PopupMenuItem(value: 'edit', child: Text('Edit Tool')),
            const PopupMenuItem(
              value: 'fullscreen',
              child: Text('Toggle Fullscreen'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          // Zoom controls
          IconButton(
            onPressed: () => _adjustZoom(-0.1),
            icon: const Icon(Icons.zoom_out),
            tooltip: 'Zoom Out',
          ),
          Text('${(_zoomLevel * 100).toInt()}%'),
          IconButton(
            onPressed: () => _adjustZoom(0.1),
            icon: const Icon(Icons.zoom_in),
            tooltip: 'Zoom In',
          ),
          IconButton(
            onPressed: () => _resetZoom(),
            icon: const Icon(Icons.center_focus_strong),
            tooltip: 'Reset Zoom',
          ),
          const VerticalDivider(),

          // Tool info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.tool.name,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  widget.tool.description,
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          // Runtime controls
          ElevatedButton.icon(
            onPressed: () => _resetTool(),
            icon: const Icon(Icons.refresh, size: 16),
            label: const Text('Reset'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
          ),
          const SizedBox(width: 8),
          ElevatedButton.icon(
            onPressed: () => _calculateAll(),
            icon: const Icon(Icons.calculate, size: 16),
            label: const Text('Calculate'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToolInterface() {
    return Transform.scale(
      scale: _zoomLevel,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Stack(
          children: [
            // Background pattern
            CustomPaint(
              size: Size.infinite,
              painter: BackgroundPatternPainter(),
            ),

            // Tool components
            ...widget.tool.components.map(
              (component) => _buildRuntimeComponent(component),
            ),

            // Overlay for calculations
            Consumer(
              builder: (context, ref, child) {
                final isCalculating = ref.watch(isCalculatingProvider);
                if (isCalculating) {
                  return Container(
                    color: Colors.black.withValues(alpha: 0.3),
                    child: const Center(
                      child: Card(
                        child: Padding(
                          padding: EdgeInsets.all(24),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              CircularProgressIndicator(),
                              SizedBox(height: 16),
                              Text('Calculating...'),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRuntimeComponent(UIComponent component) {
    return Positioned(
      left: component.x,
      top: component.y,
      child: _buildComponentWidget(component),
    );
  }

  Widget _buildComponentWidget(UIComponent component) {
    if (!component.isVisible) return const SizedBox.shrink();

    switch (component.type) {
      case ComponentType.textInput:
        return _buildTextInput(component);
      case ComponentType.numberInput:
        return _buildNumberInput(component);
      case ComponentType.dropdown:
        return _buildDropdown(component);
      case ComponentType.checkbox:
        return _buildCheckbox(component);
      case ComponentType.radioButton:
        return _buildRadioButton(component);
      case ComponentType.button:
        return _buildButton(component);
      case ComponentType.label:
        return _buildLabel(component);
      case ComponentType.slider:
        return _buildSlider(component);
      case ComponentType.dateInput:
        return _buildDateInput(component);
      case ComponentType.textArea:
        return _buildTextArea(component);
      default:
        return _buildDefaultComponent(component);
    }
  }

  Widget _buildTextInput(UIComponent component) {
    return SizedBox(
      width: component.style.width ?? 200,
      child: Consumer(
        builder: (context, ref, child) {
          final value =
              ref.watch(toolRuntimeProvider)[component.id]?.toString() ?? '';
          return TextField(
            controller: TextEditingController(text: value),
            decoration: InputDecoration(
              labelText: component.label,
              hintText: component.placeholder,
              border: const OutlineInputBorder(),
              enabled: component.isEnabled,
            ),
            onChanged: (value) {
              ref
                  .read(toolRuntimeProvider.notifier)
                  .updateComponentValue(component.id, value);
            },
          );
        },
      ),
    );
  }

  Widget _buildNumberInput(UIComponent component) {
    return SizedBox(
      width: component.style.width ?? 200,
      child: Consumer(
        builder: (context, ref, child) {
          final value =
              ref.watch(toolRuntimeProvider)[component.id]?.toString() ?? '';
          return TextField(
            controller: TextEditingController(text: value),
            decoration: InputDecoration(
              labelText: component.label,
              hintText: component.placeholder,
              border: const OutlineInputBorder(),
              enabled: component.isEnabled,
            ),
            keyboardType: TextInputType.number,
            onChanged: (value) {
              final numValue = double.tryParse(value) ?? 0;
              ref
                  .read(toolRuntimeProvider.notifier)
                  .updateComponentValue(component.id, numValue);
            },
          );
        },
      ),
    );
  }

  Widget _buildDropdown(UIComponent component) {
    final options =
        component.properties['options'] as List<String>? ??
        ['Option 1', 'Option 2'];

    return SizedBox(
      width: component.style.width ?? 200,
      child: Consumer(
        builder: (context, ref, child) {
          final value = ref
              .watch(toolRuntimeProvider)[component.id]
              ?.toString();
          return DropdownButtonFormField<String>(
            value: options.contains(value) ? value : null,
            decoration: InputDecoration(
              labelText: component.label,
              border: const OutlineInputBorder(),
              enabled: component.isEnabled,
            ),
            items: options
                .map(
                  (option) =>
                      DropdownMenuItem(value: option, child: Text(option)),
                )
                .toList(),
            onChanged: component.isEnabled
                ? (value) {
                    if (value != null) {
                      ref
                          .read(toolRuntimeProvider.notifier)
                          .updateComponentValue(component.id, value);
                    }
                  }
                : null,
          );
        },
      ),
    );
  }

  Widget _buildCheckbox(UIComponent component) {
    return SizedBox(
      width: component.style.width ?? 150,
      child: Consumer(
        builder: (context, ref, child) {
          final value =
              ref.watch(toolRuntimeProvider)[component.id] as bool? ?? false;
          return CheckboxListTile(
            title: Text(component.label),
            value: value,
            onChanged: component.isEnabled
                ? (value) {
                    ref
                        .read(toolRuntimeProvider.notifier)
                        .updateComponentValue(component.id, value ?? false);
                  }
                : null,
            dense: true,
          );
        },
      ),
    );
  }

  Widget _buildRadioButton(UIComponent component) {
    return SizedBox(
      width: component.style.width ?? 150,
      child: Consumer(
        builder: (context, ref, child) {
          final value =
              ref.watch(toolRuntimeProvider)[component.id] as bool? ?? false;
          return RadioListTile<bool>(
            title: Text(component.label),
            value: true,
            groupValue: value,
            onChanged: component.isEnabled
                ? (value) {
                    ref
                        .read(toolRuntimeProvider.notifier)
                        .updateComponentValue(component.id, value ?? false);
                  }
                : null,
            dense: true,
          );
        },
      ),
    );
  }

  Widget _buildButton(UIComponent component) {
    return SizedBox(
      width: component.style.width ?? 120,
      height: component.style.height ?? 40,
      child: ElevatedButton(
        onPressed: component.isEnabled
            ? () => _handleButtonPress(component)
            : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: component.style.backgroundColor != null
              ? Color(
                  int.parse(
                    component.style.backgroundColor!.replaceFirst('#', '0xFF'),
                  ),
                )
              : null,
        ),
        child: Text(component.label),
      ),
    );
  }

  Widget _buildLabel(UIComponent component) {
    return Container(
      width: component.style.width ?? 100,
      height: component.style.height ?? 30,
      alignment: _getAlignment(component.style.alignment),
      child: Consumer(
        builder: (context, ref, child) {
          // Check if this label is bound to a cell for output
          String displayText = component.label;

          if (component.dataBinding?.bindingType == 'output' &&
              component.dataBinding?.cellAddress != null) {
            final spreadsheet = ref.watch(spreadsheetCalculationProvider);
            final activeSheet = spreadsheet?.activeSheet;
            final cell = activeSheet?.cells[component.dataBinding!.cellAddress];
            displayText =
                cell?.calculatedValue?.toString() ??
                cell?.rawValue ??
                component.label;
          }

          return Text(
            displayText,
            style: TextStyle(
              fontSize: component.style.fontSize ?? 16,
              fontWeight: component.style.isBold
                  ? FontWeight.bold
                  : FontWeight.normal,
              fontStyle: component.style.isItalic
                  ? FontStyle.italic
                  : FontStyle.normal,
              color: component.style.textColor != null
                  ? Color(
                      int.parse(
                        component.style.textColor!.replaceFirst('#', '0xFF'),
                      ),
                    )
                  : null,
            ),
          );
        },
      ),
    );
  }

  Widget _buildSlider(UIComponent component) {
    return SizedBox(
      width: component.style.width ?? 200,
      height: component.style.height ?? 60,
      child: Consumer(
        builder: (context, ref, child) {
          final value =
              (ref.watch(toolRuntimeProvider)[component.id] as num?)
                  ?.toDouble() ??
              0.5;
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(component.label),
              Slider(
                value: value.clamp(0.0, 1.0),
                onChanged: component.isEnabled
                    ? (value) {
                        ref
                            .read(toolRuntimeProvider.notifier)
                            .updateComponentValue(component.id, value);
                      }
                    : null,
              ),
              Text('Value: ${value.toStringAsFixed(2)}'),
            ],
          );
        },
      ),
    );
  }

  Widget _buildDateInput(UIComponent component) {
    return SizedBox(
      width: component.style.width ?? 200,
      child: Consumer(
        builder: (context, ref, child) {
          final value =
              ref.watch(toolRuntimeProvider)[component.id] as DateTime?;
          return TextField(
            controller: TextEditingController(
              text: value?.toString().split(' ')[0] ?? '',
            ),
            decoration: InputDecoration(
              labelText: component.label,
              hintText: 'Select date',
              border: const OutlineInputBorder(),
              suffixIcon: const Icon(Icons.calendar_today),
              enabled: component.isEnabled,
            ),
            readOnly: true,
            onTap: component.isEnabled
                ? () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: value ?? DateTime.now(),
                      firstDate: DateTime(1900),
                      lastDate: DateTime(2100),
                    );
                    if (date != null) {
                      ref
                          .read(toolRuntimeProvider.notifier)
                          .updateComponentValue(component.id, date);
                    }
                  }
                : null,
          );
        },
      ),
    );
  }

  Widget _buildTextArea(UIComponent component) {
    return SizedBox(
      width: component.style.width ?? 300,
      height: component.style.height ?? 100,
      child: Consumer(
        builder: (context, ref, child) {
          final value =
              ref.watch(toolRuntimeProvider)[component.id]?.toString() ?? '';
          return TextField(
            controller: TextEditingController(text: value),
            decoration: InputDecoration(
              labelText: component.label,
              hintText: component.placeholder,
              border: const OutlineInputBorder(),
              enabled: component.isEnabled,
            ),
            maxLines: 4,
            onChanged: (value) {
              ref
                  .read(toolRuntimeProvider.notifier)
                  .updateComponentValue(component.id, value);
            },
          );
        },
      ),
    );
  }

  Widget _buildDefaultComponent(UIComponent component) {
    return Container(
      width: component.style.width ?? 100,
      height: component.style.height ?? 40,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Center(
        child: Text(
          component.type.name.toUpperCase(),
          style: const TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ),
    );
  }

  Widget _buildFloatingActions() {
    if (_isFullscreen) {
      return FloatingActionButton(
        onPressed: () => _toggleFullscreen(),
        backgroundColor: AppTheme.toolsBuilderColor,
        child: const Icon(Icons.fullscreen_exit, color: Colors.white),
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        FloatingActionButton.small(
          onPressed: () => _toggleFullscreen(),
          backgroundColor: AppTheme.toolsBuilderColor,
          heroTag: 'fullscreen',
          child: const Icon(Icons.fullscreen, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton.small(
          onPressed: () => _showResults(),
          backgroundColor: Colors.green,
          heroTag: 'results',
          child: const Icon(Icons.assessment, color: Colors.white),
        ),
      ],
    );
  }

  Alignment _getAlignment(ComponentAlignment? alignment) {
    switch (alignment) {
      case ComponentAlignment.left:
        return Alignment.centerLeft;
      case ComponentAlignment.center:
        return Alignment.center;
      case ComponentAlignment.right:
        return Alignment.centerRight;
      default:
        return Alignment.centerLeft;
    }
  }

  void _adjustZoom(double delta) {
    setState(() {
      _zoomLevel = (_zoomLevel + delta).clamp(0.5, 3.0);
    });
  }

  void _resetZoom() {
    setState(() {
      _zoomLevel = 1.0;
    });
  }

  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
    });
  }

  void _resetTool() {
    ref.read(toolRuntimeProvider.notifier).clearValues();
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Tool values reset')));
  }

  void _calculateAll() {
    if (widget.tool.spreadsheet != null) {
      ref
          .read(spreadsheetCalculationProvider.notifier)
          .setSpreadsheet(widget.tool.spreadsheet!);
    }
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Calculations updated')));
  }

  void _handleButtonPress(UIComponent component) {
    final action = component.events['click'];
    if (action != null && action.isNotEmpty) {
      // Execute button action
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Button action: $action')));
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'reset':
        _resetTool();
        break;
      case 'save_template':
        _saveAsTemplate();
        break;
      case 'edit':
        _editTool();
        break;
      case 'fullscreen':
        _toggleFullscreen();
        break;
    }
  }

  void _shareResults() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share functionality will be implemented')),
    );
  }

  Future<void> _exportResults() async {
    if (widget.tool.spreadsheet == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No spreadsheet data to export')),
      );
      return;
    }
    final success = await ExcelService.exportToExcel(
      widget.tool.spreadsheet!,
      '${widget.tool.name}_results',
    );
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success ? 'Results exported successfully' : 'Export failed',
          ),
        ),
      );
    }
  }

  void _saveAsTemplate() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Save as template functionality will be implemented'),
      ),
    );
  }

  void _editTool() {
    Navigator.of(context).pop();
  }

  void _showResults() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Tool Results'),
        content: SizedBox(
          width: 400,
          height: 300,
          child: Consumer(
            builder: (context, ref, child) {
              final runtimeValues = ref.watch(toolRuntimeProvider);
              final spreadsheet = ref.watch(spreadsheetCalculationProvider);

              return SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Component Values:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    ...runtimeValues.entries.map((entry) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2),
                        child: Text('${entry.key}: ${entry.value}'),
                      );
                    }),
                    const SizedBox(height: 16),
                    const Text(
                      'Calculated Values:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    if (spreadsheet?.activeSheet != null)
                      ...spreadsheet!.activeSheet!.cells.entries.map((entry) {
                        final cell = entry.value;
                        if (cell.calculatedValue != null) {
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 2),
                            child: Text(
                              '${entry.key}: ${cell.calculatedValue}',
                            ),
                          );
                        }
                        return const SizedBox.shrink();
                      }),
                  ],
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _exportResults();
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }
}

class BackgroundPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey[100]!
      ..strokeWidth = 0.5;

    const spacing = 50.0;

    // Draw subtle grid pattern
    for (double x = 0; x < size.width; x += spacing) {
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }

    for (double y = 0; y < size.height; y += spacing) {
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Additional provider for calculation state
final isCalculatingProvider = StateProvider<bool>((ref) => false);
