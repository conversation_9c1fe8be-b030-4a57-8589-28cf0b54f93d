import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'screens/hadith_dashboard.dart';
import 'screens/hadith_collections_screen.dart';
import 'screens/hadith_search_screen.dart';
import 'screens/hadith_bookmarks_screen.dart';

/// Main Hadith Application
class HadithAppMain extends ConsumerStatefulWidget {
  const HadithAppMain({super.key});

  @override
  ConsumerState<HadithAppMain> createState() => _HadithAppMainState();
}

class _HadithAppMainState extends ConsumerState<HadithAppMain> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const HadithDashboard(),
    const HadithCollectionsScreen(),
    const HadithSearchScreen(),
    const HadithBookmarksScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        selectedItemColor: Colors.brown,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.library_books),
            label: 'Collections',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.search),
            label: 'Search',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.bookmark),
            label: 'Bookmarks',
          ),
        ],
      ),
    );
  }
}
