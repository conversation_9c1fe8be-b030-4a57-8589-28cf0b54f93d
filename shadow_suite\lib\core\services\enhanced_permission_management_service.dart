import 'dart:async';
import 'package:flutter/material.dart';
import 'comprehensive_permission_detection_service.dart';
import 'permissions_service.dart';

/// Enhanced permission management service with fallback functionality
class EnhancedPermissionManagementService {
  static final EnhancedPermissionManagementService _instance =
      EnhancedPermissionManagementService._internal();
  factory EnhancedPermissionManagementService() => _instance;
  EnhancedPermissionManagementService._internal();

  final ComprehensivePermissionDetectionService _detectionService =
      ComprehensivePermissionDetectionService();

  // Permission state cache
  final Map<String, PermissionState> _permissionStates = {};
  final Map<String, DateTime> _lastRequestTimes = {};

  // Fallback functionality registry
  final Map<String, FallbackFunction> _fallbackFunctions = {};

  // Event streams
  final StreamController<PermissionStateChangeEvent> _stateController =
      StreamController<PermissionStateChangeEvent>.broadcast();
  Stream<PermissionStateChangeEvent> get stateChanges =>
      _stateController.stream;

  /// Initialize the enhanced permission management service
  Future<void> initialize() async {
    await _detectionService.initialize();
    _registerFallbackFunctions();
    _setupPermissionStateMonitoring();
  }

  /// Get comprehensive permission state for an app
  Future<AppPermissionState> getAppPermissionState(String appName) async {
    final detectionStatus = await _detectionService.getAppPermissionStatus(
      appName,
    );
    final fallbackAvailable = _checkFallbackAvailability(
      appName,
      detectionStatus.missingPermissions,
    );

    return AppPermissionState(
      appName: appName,
      detectionStatus: detectionStatus,
      fallbackAvailable: fallbackAvailable,
      canRequestMissing: await _canRequestMissingPermissions(
        detectionStatus.missingPermissions,
      ),
      lastStateCheck: DateTime.now(),
    );
  }

  /// Request permissions with enhanced user experience
  Future<PermissionRequestResult> requestPermissionsWithFallback(
    BuildContext context,
    String appName,
  ) async {
    final appState = await getAppPermissionState(appName);

    if (appState.detectionStatus.hasAllPermissions) {
      return PermissionRequestResult(
        success: true,
        grantedPermissions: appState.detectionStatus.requiredPermissions,
        deniedPermissions: [],
        fallbacksActivated: [],
      );
    }

    // Show permission explanation dialog
    if (!context.mounted) {
      return PermissionRequestResult(
        success: false,
        grantedPermissions: [],
        deniedPermissions: appState.detectionStatus.missingPermissions,
        fallbacksActivated: [],
      );
    }
    final shouldProceed = await _showPermissionExplanationDialog(
      context,
      appName,
      appState.detectionStatus.missingPermissions,
    );

    if (!shouldProceed) {
      return PermissionRequestResult(
        success: false,
        grantedPermissions: [],
        deniedPermissions: appState.detectionStatus.missingPermissions,
        fallbacksActivated: [],
        userCancelled: true,
      );
    }

    // Request permissions
    final granted = <String>[];
    final denied = <String>[];
    final fallbacksActivated = <String>[];

    for (final permission in appState.detectionStatus.missingPermissions) {
      if (!context.mounted) break;
      final permissionGranted = await _requestSinglePermission(
        context,
        permission,
      );

      if (permissionGranted) {
        granted.add(permission);
      } else {
        denied.add(permission);

        // Activate fallback if available
        if (appState.fallbackAvailable.containsKey(permission)) {
          await _activateFallback(permission, appName);
          fallbacksActivated.add(permission);
        }
      }
    }

    // Update permission states
    await _updatePermissionStates(appName, granted, denied);

    return PermissionRequestResult(
      success: denied.isEmpty || fallbacksActivated.isNotEmpty,
      grantedPermissions: granted,
      deniedPermissions: denied,
      fallbacksActivated: fallbacksActivated,
    );
  }

  /// Check if app can function with current permissions and fallbacks
  Future<bool> canAppFunction(String appName) async {
    final appState = await getAppPermissionState(appName);

    if (appState.detectionStatus.hasAllPermissions) {
      return true;
    }

    // Check if fallbacks cover all missing permissions
    for (final missingPermission
        in appState.detectionStatus.missingPermissions) {
      if (!appState.fallbackAvailable.containsKey(missingPermission)) {
        return false;
      }
    }

    return true;
  }

  /// Get fallback functionality description for missing permissions
  Future<Map<String, String>> getFallbackDescriptions(String appName) async {
    final appState = await getAppPermissionState(appName);
    final descriptions = <String, String>{};

    for (final permission in appState.detectionStatus.missingPermissions) {
      if (appState.fallbackAvailable.containsKey(permission)) {
        descriptions[permission] = _getFallbackDescription(permission, appName);
      }
    }

    return descriptions;
  }

  /// Handle permission permanently denied scenario
  Future<void> handlePermanentlyDeniedPermissions(
    BuildContext context,
    String appName,
    List<String> permanentlyDeniedPermissions,
  ) async {
    await _showPermanentlyDeniedDialog(
      context,
      appName,
      permanentlyDeniedPermissions,
    );
  }

  /// Register a custom fallback function for a permission
  void registerFallbackFunction(String permission, FallbackFunction fallback) {
    _fallbackFunctions[permission] = fallback;
  }

  /// Get permission usage statistics
  Future<PermissionUsageStats> getPermissionUsageStats() async {
    final allAppPermissions = await _detectionService.getAllAppPermissions();

    final totalPermissions = allAppPermissions.values
        .expand((status) => status.requiredPermissions)
        .toSet()
        .length;

    final grantedPermissions = allAppPermissions.values
        .expand(
          (status) => status.permissionStatuses.entries
              .where((entry) => entry.value == PermissionStatus.granted)
              .map((entry) => entry.key),
        )
        .toSet()
        .length;

    return PermissionUsageStats(
      totalPermissions: totalPermissions,
      grantedPermissions: grantedPermissions,
      deniedPermissions: totalPermissions - grantedPermissions,
      appsWithAllPermissions: allAppPermissions.values
          .where((status) => status.hasAllPermissions)
          .length,
      totalApps: allAppPermissions.length,
    );
  }

  // Private methods
  void _registerFallbackFunctions() {
    // Register built-in fallback functions
    _fallbackFunctions['storage'] = (appName) async {
      // Use app-specific storage directory
      return true;
    };

    _fallbackFunctions['camera'] = (appName) async {
      // Use file picker for image selection
      return true;
    };

    _fallbackFunctions['location'] = (appName) async {
      // Use manual location input
      return true;
    };
  }

  void _setupPermissionStateMonitoring() {
    // Monitor permission changes
    _detectionService.permissionChanges.listen((event) {
      _stateController.add(
        PermissionStateChangeEvent(
          appName: event.appName,
          changedPermissions: event.changedPermissions,
          newState: event.newStatus,
          timestamp: DateTime.now(),
        ),
      );
    });
  }

  Map<String, bool> _checkFallbackAvailability(
    String appName,
    List<String> missingPermissions,
  ) {
    final availability = <String, bool>{};

    for (final permission in missingPermissions) {
      availability[permission] = _fallbackFunctions.containsKey(permission);
    }

    return availability;
  }

  Future<bool> _canRequestMissingPermissions(List<String> permissions) async {
    // Check if any permissions can still be requested
    for (final permission in permissions) {
      final info = await _detectionService.getPermissionInfo(permission);
      if (info.canRequest && !info.isPermanentlyDenied) {
        return true;
      }
    }
    return false;
  }

  Future<bool> _showPermissionExplanationDialog(
    BuildContext context,
    String appName,
    List<String> missingPermissions,
  ) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => PermissionExplanationDialog(
            appName: appName,
            missingPermissions: missingPermissions,
            fallbackDescriptions: _getFallbackDescriptionsSync(
              appName,
              missingPermissions,
            ),
          ),
        ) ??
        false;
  }

  Future<bool> _requestSinglePermission(
    BuildContext context,
    String permission,
  ) async {
    switch (permission) {
      case 'storage':
        return await PermissionsService.requestStoragePermissions(context);
      case 'camera':
        return await PermissionsService.requestCameraPermissions(context);
      case 'location':
        return await PermissionsService.requestLocationPermissions(context);
      case 'notifications':
        return true; // Notifications not implemented in simplified service
      default:
        return false;
    }
  }

  Future<void> _activateFallback(String permission, String appName) async {
    final fallbackFunction = _fallbackFunctions[permission];
    if (fallbackFunction != null) {
      await fallbackFunction(appName);
    }
  }

  Future<void> _updatePermissionStates(
    String appName,
    List<String> granted,
    List<String> denied,
  ) async {
    for (final permission in granted) {
      _permissionStates['${appName}_$permission'] = PermissionState.granted;
    }

    for (final permission in denied) {
      _permissionStates['${appName}_$permission'] = PermissionState.denied;
      _lastRequestTimes['${appName}_$permission'] = DateTime.now();
    }
  }

  String _getFallbackDescription(String permission, String appName) {
    switch (permission) {
      case 'storage':
        return 'Files will be saved to app-specific storage';
      case 'camera':
        return 'You can select images from gallery instead';
      case 'location':
        return 'You can manually enter your location';
      case 'notifications':
        return 'Reminders will be shown within the app';
      default:
        return 'Alternative functionality available';
    }
  }

  Map<String, String> _getFallbackDescriptionsSync(
    String appName,
    List<String> permissions,
  ) {
    final descriptions = <String, String>{};
    for (final permission in permissions) {
      if (_fallbackFunctions.containsKey(permission)) {
        descriptions[permission] = _getFallbackDescription(permission, appName);
      }
    }
    return descriptions;
  }

  Future<void> _showPermanentlyDeniedDialog(
    BuildContext context,
    String appName,
    List<String> permissions,
  ) async {
    await showDialog(
      context: context,
      builder: (context) =>
          PermanentlyDeniedDialog(appName: appName, permissions: permissions),
    );
  }

  void dispose() {
    _stateController.close();
    _detectionService.dispose();
  }
}

/// Fallback function type
typedef FallbackFunction = Future<bool> Function(String appName);

/// Permission state enumeration
enum PermissionState { granted, denied, permanentlyDenied, notRequested }

/// App permission state model
class AppPermissionState {
  final String appName;
  final AppPermissionStatus detectionStatus;
  final Map<String, bool> fallbackAvailable;
  final bool canRequestMissing;
  final DateTime lastStateCheck;

  const AppPermissionState({
    required this.appName,
    required this.detectionStatus,
    required this.fallbackAvailable,
    required this.canRequestMissing,
    required this.lastStateCheck,
  });

  bool get canFunction =>
      detectionStatus.hasAllPermissions ||
      detectionStatus.missingPermissions.every(
        (p) => fallbackAvailable[p] == true,
      );
}

/// Permission request result
class PermissionRequestResult {
  final bool success;
  final List<String> grantedPermissions;
  final List<String> deniedPermissions;
  final List<String> fallbacksActivated;
  final bool userCancelled;

  const PermissionRequestResult({
    required this.success,
    required this.grantedPermissions,
    required this.deniedPermissions,
    required this.fallbacksActivated,
    this.userCancelled = false,
  });
}

/// Permission state change event
class PermissionStateChangeEvent {
  final String appName;
  final List<String> changedPermissions;
  final AppPermissionStatus newState;
  final DateTime timestamp;

  const PermissionStateChangeEvent({
    required this.appName,
    required this.changedPermissions,
    required this.newState,
    required this.timestamp,
  });
}

/// Permission usage statistics
class PermissionUsageStats {
  final int totalPermissions;
  final int grantedPermissions;
  final int deniedPermissions;
  final int appsWithAllPermissions;
  final int totalApps;

  const PermissionUsageStats({
    required this.totalPermissions,
    required this.grantedPermissions,
    required this.deniedPermissions,
    required this.appsWithAllPermissions,
    required this.totalApps,
  });

  double get grantedPercentage =>
      totalPermissions > 0 ? (grantedPermissions / totalPermissions) * 100 : 0;

  double get appsWithAllPermissionsPercentage =>
      totalApps > 0 ? (appsWithAllPermissions / totalApps) * 100 : 0;
}

/// Permission explanation dialog
class PermissionExplanationDialog extends StatelessWidget {
  final String appName;
  final List<String> missingPermissions;
  final Map<String, String> fallbackDescriptions;

  const PermissionExplanationDialog({
    super.key,
    required this.appName,
    required this.missingPermissions,
    required this.fallbackDescriptions,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('$appName Permissions'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$appName needs the following permissions to function properly:',
          ),
          const SizedBox(height: 16),
          ...missingPermissions.map(
            (permission) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(Icons.security, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _getPermissionDisplayName(permission),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        if (fallbackDescriptions.containsKey(permission))
                          Text(
                            'Fallback: ${fallbackDescriptions[permission]}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(true),
          child: const Text('Grant Permissions'),
        ),
      ],
    );
  }

  String _getPermissionDisplayName(String permission) {
    switch (permission) {
      case 'storage':
        return 'Storage Access';
      case 'camera':
        return 'Camera Access';
      case 'location':
        return 'Location Access';
      case 'notifications':
        return 'Notifications';
      default:
        return permission;
    }
  }
}

/// Permanently denied permissions dialog
class PermanentlyDeniedDialog extends StatelessWidget {
  final String appName;
  final List<String> permissions;

  const PermanentlyDeniedDialog({
    super.key,
    required this.appName,
    required this.permissions,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Permissions Required'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '$appName requires certain permissions that have been permanently denied. '
            'Please enable them in Settings to use full functionality.',
          ),
          const SizedBox(height: 16),
          ...permissions.map(
            (permission) => ListTile(
              leading: const Icon(Icons.warning, color: Colors.orange),
              title: Text(_getPermissionDisplayName(permission)),
              dense: true,
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop();
            // Open app settings
            // AppSettings.openAppSettings();
          },
          child: const Text('Open Settings'),
        ),
      ],
    );
  }

  String _getPermissionDisplayName(String permission) {
    switch (permission) {
      case 'storage':
        return 'Storage Access';
      case 'camera':
        return 'Camera Access';
      case 'location':
        return 'Location Access';
      case 'notifications':
        return 'Notifications';
      default:
        return permission;
    }
  }
}
