import 'dart:typed_data';

/// Encryption algorithm types
enum EncryptionAlgorithm { aes128, aes192, aes256, chacha20, salsa20 }

/// Key derivation function types
enum KeyDerivationFunction { pbkdf2, scrypt, argon2 }

/// Password strength levels
enum PasswordStrengthLevel { weak, medium, strong, veryStrong }

/// Encryption options configuration
class EncryptionOptions {
  final EncryptionAlgorithm algorithm;
  final KeyDerivationFunction keyDerivation;
  final int iterations;
  final bool enableCompression;
  final bool enableIntegrityCheck;

  const EncryptionOptions({
    this.algorithm = EncryptionAlgorithm.aes256,
    this.keyDerivation = KeyDerivationFunction.pbkdf2,
    this.iterations = 100000,
    this.enableCompression = true,
    this.enableIntegrityCheck = true,
  });

  Map<String, dynamic> toJson() => {
    'algorithm': algorithm.name,
    'keyDerivation': keyDerivation.name,
    'iterations': iterations,
    'enableCompression': enableCompression,
    'enableIntegrityCheck': enableIntegrityCheck,
  };

  factory EncryptionOptions.fromJson(Map<String, dynamic> json) =>
      EncryptionOptions(
        algorithm: EncryptionAlgorithm.values.firstWhere(
          (e) => e.name == json['algorithm'],
          orElse: () => EncryptionAlgorithm.aes256,
        ),
        keyDerivation: KeyDerivationFunction.values.firstWhere(
          (e) => e.name == json['keyDerivation'],
          orElse: () => KeyDerivationFunction.pbkdf2,
        ),
        iterations: json['iterations'] ?? 100000,
        enableCompression: json['enableCompression'] ?? true,
        enableIntegrityCheck: json['enableIntegrityCheck'] ?? true,
      );
}

/// Encryption key with metadata
class EncryptionKey {
  final Uint8List key;
  final Uint8List salt;
  final int iterations;
  final EncryptionAlgorithm algorithm;
  final DateTime createdAt;

  const EncryptionKey({
    required this.key,
    required this.salt,
    required this.iterations,
    required this.algorithm,
    required this.createdAt,
  });
}

/// Encryption metadata stored with encrypted files
class EncryptionMetadata {
  final EncryptionAlgorithm algorithm;
  final KeyDerivationFunction keyDerivation;
  final int iterations;
  final Uint8List salt;
  final Uint8List iv;
  final int originalSize;
  final DateTime encryptedAt;
  final String version;

  const EncryptionMetadata({
    required this.algorithm,
    required this.keyDerivation,
    required this.iterations,
    required this.salt,
    required this.iv,
    required this.originalSize,
    required this.encryptedAt,
    required this.version,
  });

  Map<String, dynamic> toJson() => {
    'algorithm': algorithm.name,
    'keyDerivation': keyDerivation.name,
    'iterations': iterations,
    'salt': salt.toList(),
    'iv': iv.toList(),
    'originalSize': originalSize,
    'encryptedAt': encryptedAt.toIso8601String(),
    'version': version,
  };

  factory EncryptionMetadata.fromJson(Map<String, dynamic> json) =>
      EncryptionMetadata(
        algorithm: EncryptionAlgorithm.values.firstWhere(
          (e) => e.name == json['algorithm'],
          orElse: () => EncryptionAlgorithm.aes256,
        ),
        keyDerivation: KeyDerivationFunction.values.firstWhere(
          (e) => e.name == json['keyDerivation'],
          orElse: () => KeyDerivationFunction.pbkdf2,
        ),
        iterations: json['iterations'],
        salt: Uint8List.fromList(List<int>.from(json['salt'])),
        iv: Uint8List.fromList(List<int>.from(json['iv'])),
        originalSize: json['originalSize'],
        encryptedAt: DateTime.parse(json['encryptedAt']),
        version: json['version'],
      );
}

/// Encrypted file structure
class EncryptedFileStructure {
  final EncryptionMetadata metadata;
  final Uint8List encryptedData;
  final String checksum;

  const EncryptedFileStructure({
    required this.metadata,
    required this.encryptedData,
    required this.checksum,
  });
}

/// Encryption session for tracking active encryptions
class EncryptionSession {
  final String sessionId;
  final String filePath;
  final DateTime createdAt;
  final DateTime lastAccessed;

  const EncryptionSession({
    required this.sessionId,
    required this.filePath,
    required this.createdAt,
    required this.lastAccessed,
  });
}

/// Encryption operation result
class EncryptionResult {
  final bool success;
  final String? outputPath;
  final String? sessionId;
  final String? error;
  final Duration encryptionTime;
  final int originalSize;
  final int encryptedSize;
  final double compressionRatio;

  const EncryptionResult({
    required this.success,
    this.outputPath,
    this.sessionId,
    this.error,
    required this.encryptionTime,
    required this.originalSize,
    required this.encryptedSize,
    required this.compressionRatio,
  });
}

/// Decryption operation result
class DecryptionResult {
  final bool success;
  final String? outputPath;
  final String? error;
  final Duration decryptionTime;
  final int originalSize;
  final int decryptedSize;

  const DecryptionResult({
    required this.success,
    this.outputPath,
    this.error,
    required this.decryptionTime,
    required this.originalSize,
    required this.decryptedSize,
  });
}

/// Folder encryption result
class FolderEncryptionResult {
  final bool success;
  final List<String> encryptedFiles;
  final List<String> failedFiles;
  final int totalFiles;
  final String? error;
  final Duration encryptionTime;
  final int originalSize;
  final int encryptedSize;

  const FolderEncryptionResult({
    required this.success,
    required this.encryptedFiles,
    required this.failedFiles,
    required this.totalFiles,
    this.error,
    required this.encryptionTime,
    required this.originalSize,
    required this.encryptedSize,
  });
}

/// Archive options for secure archives
class ArchiveOptions {
  final bool enableCompression;
  final int compressionLevel;
  final bool preservePermissions;
  final bool preserveTimestamps;

  const ArchiveOptions({
    this.enableCompression = true,
    this.compressionLevel = 6,
    this.preservePermissions = true,
    this.preserveTimestamps = true,
  });

  Map<String, dynamic> toJson() => {
    'enableCompression': enableCompression,
    'compressionLevel': compressionLevel,
    'preservePermissions': preservePermissions,
    'preserveTimestamps': preserveTimestamps,
  };

  factory ArchiveOptions.fromJson(Map<String, dynamic> json) => ArchiveOptions(
    enableCompression: json['enableCompression'] ?? true,
    compressionLevel: json['compressionLevel'] ?? 6,
    preservePermissions: json['preservePermissions'] ?? true,
    preserveTimestamps: json['preserveTimestamps'] ?? true,
  );
}

/// Archive entry for secure archives
class ArchiveEntry {
  final String name;
  final String path;
  final int size;
  final Uint8List data;
  final DateTime modifiedAt;
  final int compressedSize;
  final int uncompressedSize;
  final DateTime lastModified;
  final bool isDirectory;

  const ArchiveEntry({
    required this.name,
    required this.path,
    required this.size,
    required this.data,
    required this.modifiedAt,
    required this.compressedSize,
    required this.uncompressedSize,
    required this.lastModified,
    required this.isDirectory,
  });

  Map<String, dynamic> toJson() => {
    'name': name,
    'path': path,
    'size': size,
    'data': data.toList(),
    'modifiedAt': modifiedAt.toIso8601String(),
    'compressedSize': compressedSize,
    'uncompressedSize': uncompressedSize,
    'lastModified': lastModified.toIso8601String(),
    'isDirectory': isDirectory,
  };

  factory ArchiveEntry.fromJson(Map<String, dynamic> json) => ArchiveEntry(
    name: json['name'],
    path: json['path'],
    size: json['size'],
    data: Uint8List.fromList(List<int>.from(json['data'])),
    modifiedAt: DateTime.parse(json['modifiedAt']),
    compressedSize: json['compressedSize'] ?? 0,
    uncompressedSize: json['uncompressedSize'] ?? 0,
    lastModified: DateTime.parse(json['lastModified']),
    isDirectory: json['isDirectory'] ?? false,
  );
}

/// Secure archive creation result
class SecureArchiveResult {
  final bool success;
  final String? archivePath;
  final String? error;
  final int fileCount;
  final int originalSize;
  final int archiveSize;
  final double compressionRatio;
  final Duration creationTime;

  const SecureArchiveResult({
    required this.success,
    this.archivePath,
    this.error,
    required this.fileCount,
    required this.originalSize,
    required this.archiveSize,
    required this.compressionRatio,
    required this.creationTime,
  });
}

/// Archive extraction result
class ArchiveExtractionResult {
  final bool success;
  final List<String> extractedFiles;
  final String? error;
  final int fileCount;
  final Duration extractionTime;

  const ArchiveExtractionResult({
    required this.success,
    required this.extractedFiles,
    this.error,
    required this.fileCount,
    required this.extractionTime,
  });
}

/// Archive encryption result (internal)
class ArchiveEncryptionResult {
  final Uint8List encryptedData;
  final EncryptionKey key;
  final Uint8List iv;

  const ArchiveEncryptionResult({
    required this.encryptedData,
    required this.key,
    required this.iv,
  });
}

/// Password strength assessment
class PasswordStrength {
  final PasswordStrengthLevel level;
  final int score;
  final int maxScore;
  final List<String> checks;
  final List<String> suggestions;

  const PasswordStrength({
    required this.level,
    required this.score,
    required this.maxScore,
    required this.checks,
    required this.suggestions,
  });

  double get percentage => score / maxScore;

  String get description {
    switch (level) {
      case PasswordStrengthLevel.weak:
        return 'Weak - Consider using a stronger password';
      case PasswordStrengthLevel.medium:
        return 'Medium - Good but could be stronger';
      case PasswordStrengthLevel.strong:
        return 'Strong - Good password security';
      case PasswordStrengthLevel.veryStrong:
        return 'Very Strong - Excellent password security';
    }
  }
}

/// Encryption statistics
class EncryptionStatistics {
  final int activeSessions;
  final int cachedKeys;
  final int totalEncryptions;
  final Duration averageEncryptionTime;
  final List<EncryptionAlgorithm> supportedAlgorithms;

  const EncryptionStatistics({
    required this.activeSessions,
    required this.cachedKeys,
    required this.totalEncryptions,
    required this.averageEncryptionTime,
    required this.supportedAlgorithms,
  });
}

/// File integrity check result
class IntegrityCheckResult {
  final bool isValid;
  final String? error;
  final String originalChecksum;
  final String calculatedChecksum;
  final DateTime checkedAt;

  const IntegrityCheckResult({
    required this.isValid,
    this.error,
    required this.originalChecksum,
    required this.calculatedChecksum,
    required this.checkedAt,
  });
}

/// Encryption progress callback
typedef EncryptionProgressCallback =
    void Function(double progress, String status);

/// Encryption event types
enum EncryptionEventType { started, progress, completed, failed, cancelled }

/// Encryption event
class EncryptionEvent {
  final EncryptionEventType type;
  final String? message;
  final double? progress;
  final DateTime timestamp;

  const EncryptionEvent({
    required this.type,
    this.message,
    this.progress,
    required this.timestamp,
  });
}

/// Secure deletion options
class SecureDeletionOptions {
  final int passes;
  final bool randomizeData;
  final bool deleteMetadata;
  final bool verifyDeletion;

  const SecureDeletionOptions({
    this.passes = 3,
    this.randomizeData = true,
    this.deleteMetadata = true,
    this.verifyDeletion = true,
  });
}

/// Secure deletion result
class SecureDeletionResult {
  final bool success;
  final String? error;
  final int passesCompleted;
  final Duration deletionTime;
  final bool verificationPassed;

  const SecureDeletionResult({
    required this.success,
    this.error,
    required this.passesCompleted,
    required this.deletionTime,
    required this.verificationPassed,
  });
}
