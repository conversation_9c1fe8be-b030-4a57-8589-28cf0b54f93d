import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/app_theme.dart';
import '../services/money_flow_providers.dart';
import '../models/account.dart';
import '../models/transaction.dart';
import '../models/budget.dart';
import '../models/goal.dart';
import '../models/category.dart';
import 'money_flow_dashboard.dart';
import 'accounts/accounts_tab.dart';
import 'transactions/transactions_tab.dart';
import 'budgets/budgets_tab.dart';
import 'categories/categories_tab.dart';
import 'reports/reports_tab.dart';

class MoneyFlowPlaceholder extends ConsumerStatefulWidget {
  const MoneyFlowPlaceholder({super.key});

  @override
  ConsumerState<MoneyFlowPlaceholder> createState() => _MoneyFlowPlaceholderState();
}

class _MoneyFlowPlaceholderState extends ConsumerState<MoneyFlowPlaceholder> with TickerProviderStateMixin {
  late TabController _tabController;
  int _currentTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {
          _currentTabIndex = _tabController.index;
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Eagerly initialize providers
    ref.watch(accountsProvider);
    ref.watch(transactionsProvider);
    ref.watch(budgetsProvider);
    ref.watch(moneyFlowStatisticsProvider);
    ref.watch(accountBalancesProvider);
    ref.watch(monthlySpendingProvider);
    ref.watch(recentTransactionsProvider);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Money Flow'),
        automaticallyImplyLeading: false,
        backgroundColor: AppTheme.moneyFlowColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () {
              // Refresh all data
              ref.invalidate(accountsProvider);
              ref.invalidate(transactionsProvider);
              ref.invalidate(budgetsProvider);
              ref.invalidate(moneyFlowStatisticsProvider);
              ref.invalidate(accountBalancesProvider);
              ref.invalidate(monthlySpendingProvider);
              ref.invalidate(recentTransactionsProvider);
            },
            icon: const Icon(Icons.refresh),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(
              icon: Icon(Icons.dashboard),
              text: 'Dashboard',
            ),
            Tab(
              icon: Icon(Icons.account_balance),
              text: 'Accounts',
            ),
            Tab(
              icon: Icon(Icons.receipt_long),
              text: 'Transactions',
            ),
            Tab(
              icon: Icon(Icons.pie_chart),
              text: 'Budget & Goals',
            ),
            Tab(
              icon: Icon(Icons.category),
              text: 'Categories',
            ),
            Tab(
              icon: Icon(Icons.analytics),
              text: 'Reports',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          const MoneyFlowDashboard(),
          const AccountsTab(),
          const TransactionsTab(),
          const BudgetsTab(),
          const CategoriesTab(),
          const ReportsTab(),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget? _buildFloatingActionButton() {
    switch (_currentTabIndex) {
      case 1: // Accounts tab
        return FloatingActionButton(
          onPressed: () => _showAddAccountDialog(),
          backgroundColor: AppTheme.moneyFlowColor,
          child: const Icon(Icons.add, color: Colors.white),
        );
      case 2: // Transactions tab
        return FloatingActionButton(
          onPressed: () => _showAddTransactionDialog(),
          backgroundColor: AppTheme.moneyFlowColor,
          child: const Icon(Icons.add, color: Colors.white),
        );
      case 3: // Budget & Goals tab
        return FloatingActionButton(
          onPressed: () => _showBudgetGoalMenu(),
          backgroundColor: AppTheme.moneyFlowColor,
          child: const Icon(Icons.add, color: Colors.white),
        );
      case 4: // Categories tab
        return FloatingActionButton(
          onPressed: () => _showAddCategoryMenu(),
          backgroundColor: AppTheme.moneyFlowColor,
          child: const Icon(Icons.add, color: Colors.white),
        );
      default:
        return null;
    }
  }

  void _showAddAccountDialog() {
    final nameController = TextEditingController();
    final bankNameController = TextEditingController();
    final accountNumberController = TextEditingController();
    final balanceController = TextEditingController();
    final descriptionController = TextEditingController();
    final routingNumberController = TextEditingController();
    final swiftCodeController = TextEditingController();
    final creditLimitController = TextEditingController();
    final interestRateController = TextEditingController();
    final overdraftLimitController = TextEditingController();
    final notesController = TextEditingController();

    AccountType selectedType = AccountType.checking;
    String selectedCurrency = 'USD';
    String selectedColor = '#2196F3';
    DateTime? openDate;
    bool includeInNetWorth = true;
    bool allowOverdraft = false;
    bool showAdvancedOptions = false;

    final currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF', 'CNY', 'INR', 'BRL'];
    final colors = [
      '#2196F3', '#4CAF50', '#FF9800', '#9C27B0', '#F44336',
      '#795548', '#607D8B', '#E91E63', '#3F51B5', '#009688',
      '#FF5722', '#8BC34A', '#03DAC6', '#FFC107', '#673AB7',
    ];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Add New Account'),
          content: SizedBox(
            width: 600,
            height: 600,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Basic Information Section
                  Text(
                    'Basic Information',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.moneyFlowColor,
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'Account Name *',
                      border: OutlineInputBorder(),
                      hintText: 'e.g., Main Checking',
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description',
                      border: OutlineInputBorder(),
                      hintText: 'Brief description of this account',
                    ),
                    maxLines: 2,
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<AccountType>(
                    value: selectedType,
                    decoration: const InputDecoration(
                      labelText: 'Account Type *',
                      border: OutlineInputBorder(),
                    ),
                    items: AccountType.values.map((type) => DropdownMenuItem(
                      value: type,
                      child: Text(type.displayName),
                    )).toList(),
                    onChanged: (value) {
                      setDialogState(() {
                        selectedType = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: bankNameController,
                    decoration: const InputDecoration(
                      labelText: 'Bank/Institution Name *',
                      border: OutlineInputBorder(),
                      hintText: 'e.g., Chase Bank',
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: accountNumberController,
                          decoration: const InputDecoration(
                            labelText: 'Account Number *',
                            border: OutlineInputBorder(),
                            hintText: 'e.g., **********',
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextField(
                          controller: routingNumberController,
                          decoration: const InputDecoration(
                            labelText: 'Routing Number',
                            border: OutlineInputBorder(),
                            hintText: 'e.g., *********',
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: TextField(
                          controller: balanceController,
                          decoration: const InputDecoration(
                            labelText: 'Initial Balance *',
                            border: OutlineInputBorder(),
                            hintText: '0.00',
                          ),
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: selectedCurrency,
                          decoration: const InputDecoration(
                            labelText: 'Currency',
                            border: OutlineInputBorder(),
                          ),
                          items: currencies.map((currency) => DropdownMenuItem(
                            value: currency,
                            child: Text(currency),
                          )).toList(),
                          onChanged: (value) {
                            setDialogState(() {
                              selectedCurrency = value!;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // Advanced Options Toggle
                  Row(
                    children: [
                      Checkbox(
                        value: showAdvancedOptions,
                        onChanged: (value) {
                          setDialogState(() {
                            showAdvancedOptions = value!;
                          });
                        },
                      ),
                      Text(
                        'Show Advanced Options',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),

                  if (showAdvancedOptions) ...[
                    const SizedBox(height: 16),
                    const Divider(),
                    const SizedBox(height: 16),

                    // Advanced Information Section
                    Text(
                      'Advanced Information',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.moneyFlowColor,
                      ),
                    ),
                    const SizedBox(height: 16),

                    if (selectedType == AccountType.checking || selectedType == AccountType.savings) ...[
                      TextField(
                        controller: swiftCodeController,
                        decoration: const InputDecoration(
                          labelText: 'SWIFT/BIC Code',
                          border: OutlineInputBorder(),
                          hintText: 'e.g., CHASUS33',
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],

                    if (selectedType == AccountType.credit) ...[
                      Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: creditLimitController,
                              decoration: const InputDecoration(
                                labelText: 'Credit Limit',
                                border: OutlineInputBorder(),
                                hintText: '0.00',
                              ),
                              keyboardType: const TextInputType.numberWithOptions(decimal: true),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: TextField(
                              controller: interestRateController,
                              decoration: const InputDecoration(
                                labelText: 'Interest Rate (%)',
                                border: OutlineInputBorder(),
                                hintText: '0.00',
                              ),
                              keyboardType: const TextInputType.numberWithOptions(decimal: true),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                    ],

                    if (selectedType == AccountType.savings || selectedType == AccountType.investment) ...[
                      TextField(
                        controller: interestRateController,
                        decoration: const InputDecoration(
                          labelText: 'Interest Rate (%)',
                          border: OutlineInputBorder(),
                          hintText: '0.00',
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      ),
                      const SizedBox(height: 16),
                    ],

                    ListTile(
                      title: const Text('Account Opening Date'),
                      subtitle: Text(openDate != null
                          ? '${openDate!.day}/${openDate!.month}/${openDate!.year}'
                          : 'Not set'),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: openDate ?? DateTime.now(),
                          firstDate: DateTime(1900),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          setDialogState(() {
                            openDate = date;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 16),

                    // Settings Section
                    Text(
                      'Account Settings',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.moneyFlowColor,
                      ),
                    ),
                    const SizedBox(height: 16),

                    CheckboxListTile(
                      title: const Text('Include in Net Worth Calculation'),
                      subtitle: const Text('Include this account when calculating total net worth'),
                      value: includeInNetWorth,
                      onChanged: (value) {
                        setDialogState(() {
                          includeInNetWorth = value!;
                        });
                      },
                    ),

                    if (selectedType == AccountType.checking) ...[
                      CheckboxListTile(
                        title: const Text('Allow Overdraft'),
                        subtitle: const Text('Allow negative balance for this account'),
                        value: allowOverdraft,
                        onChanged: (value) {
                          setDialogState(() {
                            allowOverdraft = value!;
                          });
                        },
                      ),

                      if (allowOverdraft) ...[
                        const SizedBox(height: 16),
                        TextField(
                          controller: overdraftLimitController,
                          decoration: const InputDecoration(
                            labelText: 'Overdraft Limit',
                            border: OutlineInputBorder(),
                            hintText: '0.00',
                            prefixText: '-\$ ',
                          ),
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        ),
                      ],
                    ],

                    const SizedBox(height: 16),
                    TextField(
                      controller: notesController,
                      decoration: const InputDecoration(
                        labelText: 'Notes',
                        border: OutlineInputBorder(),
                        hintText: 'Additional notes about this account...',
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Color Selection
                  const SizedBox(height: 16),
                  Text(
                    'Account Color',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 12,
                    runSpacing: 12,
                    children: colors.map((color) {
                      final colorValue = Color(int.parse(color.replaceFirst('#', '0xFF')));
                      final isSelected = selectedColor == color;
                      return GestureDetector(
                        onTap: () {
                          setDialogState(() {
                            selectedColor = color;
                          });
                        },
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: colorValue,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: isSelected ? Colors.black : Colors.grey[300]!,
                              width: isSelected ? 3 : 1,
                            ),
                          ),
                          child: isSelected ? const Icon(Icons.check, color: Colors.white, size: 20) : null,
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                if (nameController.text.trim().isEmpty ||
                    bankNameController.text.trim().isEmpty ||
                    accountNumberController.text.trim().isEmpty ||
                    balanceController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Please fill in all required fields')),
                  );
                  return;
                }

                final balance = double.tryParse(balanceController.text.trim());
                if (balance == null) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Please enter a valid balance')),
                  );
                  return;
                }

                final account = Account(
                  name: nameController.text.trim(),
                  type: selectedType,
                  bankName: bankNameController.text.trim(),
                  accountNumber: accountNumberController.text.trim(),
                  balance: balance,
                  currency: selectedCurrency,
                  color: selectedColor,
                  description: descriptionController.text.trim().isEmpty
                      ? null
                      : descriptionController.text.trim(),
                  routingNumber: routingNumberController.text.trim().isEmpty
                      ? null
                      : routingNumberController.text.trim(),
                  swiftCode: swiftCodeController.text.trim().isEmpty
                      ? null
                      : swiftCodeController.text.trim(),
                  creditLimit: creditLimitController.text.trim().isEmpty
                      ? null
                      : double.tryParse(creditLimitController.text.trim()),
                  interestRate: interestRateController.text.trim().isEmpty
                      ? null
                      : double.tryParse(interestRateController.text.trim()),
                  openDate: openDate,
                  notes: notesController.text.trim().isEmpty
                      ? null
                      : notesController.text.trim(),
                  includeInNetWorth: includeInNetWorth,
                  allowOverdraft: allowOverdraft,
                  overdraftLimit: overdraftLimitController.text.trim().isEmpty
                      ? null
                      : double.tryParse(overdraftLimitController.text.trim()),
                );

                final navigator = Navigator.of(context);
                final messenger = ScaffoldMessenger.of(context);

                try {
                  await ref.read(accountsProvider.notifier).addAccount(account);
                  navigator.pop();
                  messenger.showSnackBar(
                    const SnackBar(content: Text('Account added successfully')),
                  );
                } catch (e) {
                  messenger.showSnackBar(
                    SnackBar(content: Text('Error adding account: $e')),
                  );
                }
              },
              child: const Text('Add Account'),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddTransactionDialog() {
    final accountsAsync = ref.read(accountsProvider);

    accountsAsync.when(
      data: (accounts) {
        if (accounts.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Please add an account first')),
          );
          return;
        }
        _showTransactionForm(accounts);
      },
      loading: () => ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Loading accounts...')),
      ),
      error: (error, stack) => ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading accounts: $error')),
      ),
    );
  }

  void _showTransactionForm(List<Account> accounts) {
    final descriptionController = TextEditingController();
    final amountController = TextEditingController();
    final notesController = TextEditingController();
    Account selectedAccount = accounts.first;
    Account? selectedToAccount;
    TransactionType selectedType = TransactionType.expense;
    DateTime selectedDate = DateTime.now();
    TransactionCategory? selectedCategory;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Add Transaction'),
          content: SizedBox(
            width: 500,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  DropdownButtonFormField<TransactionType>(
                    value: selectedType,
                    decoration: const InputDecoration(
                      labelText: 'Transaction Type *',
                      border: OutlineInputBorder(),
                    ),
                    items: TransactionType.values.map((type) => DropdownMenuItem(
                      value: type,
                      child: Text(type.name.toUpperCase()),
                    )).toList(),
                    onChanged: (value) {
                      setDialogState(() {
                        selectedType = value!;
                        if (selectedType != TransactionType.transfer) {
                          selectedToAccount = null;
                        }
                        // Reset category when type changes
                        selectedCategory = null;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<Account>(
                    value: selectedAccount,
                    decoration: const InputDecoration(
                      labelText: 'From Account *',
                      border: OutlineInputBorder(),
                    ),
                    items: accounts.map((account) => DropdownMenuItem(
                      value: account,
                      child: Text('${account.name} (${account.bankName})'),
                    )).toList(),
                    onChanged: (value) {
                      setDialogState(() {
                        selectedAccount = value!;
                      });
                    },
                  ),
                  if (selectedType == TransactionType.transfer) ...[
                    const SizedBox(height: 16),
                    DropdownButtonFormField<Account?>(
                      value: selectedToAccount,
                      decoration: const InputDecoration(
                        labelText: 'To Account *',
                        border: OutlineInputBorder(),
                      ),
                      items: accounts
                          .where((account) => account.id != selectedAccount.id)
                          .map((account) => DropdownMenuItem(
                            value: account,
                            child: Text('${account.name} (${account.bankName})'),
                          )).toList(),
                      onChanged: (value) {
                        setDialogState(() {
                          selectedToAccount = value;
                        });
                      },
                    ),
                  ],
                  const SizedBox(height: 16),
                  TextField(
                    controller: amountController,
                    decoration: const InputDecoration(
                      labelText: 'Amount *',
                      border: OutlineInputBorder(),
                      hintText: '0.00',
                      prefixText: '\$ ',
                    ),
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description *',
                      border: OutlineInputBorder(),
                      hintText: 'e.g., Grocery shopping',
                    ),
                  ),
                  const SizedBox(height: 16),
                  Consumer(
                    builder: (context, ref, child) {
                      final categoriesAsync = ref.watch(categoriesProvider);
                      return categoriesAsync.when(
                        data: (allCategories) {
                          final categoryType = selectedType == TransactionType.income
                              ? CategoryType.income
                              : selectedType == TransactionType.expense
                                  ? CategoryType.expense
                                  : CategoryType.transfer;

                          final filteredCategories = allCategories
                              .where((c) => c.type == categoryType)
                              .toList();

                          if (filteredCategories.isEmpty) {
                            return Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey[300]!),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'No ${categoryType.displayName.toLowerCase()} categories available. Create one first.',
                                style: TextStyle(color: Colors.grey[600]),
                              ),
                            );
                          }

                          if (selectedCategory == null ||
                              selectedCategory!.type != categoryType) {
                            selectedCategory = filteredCategories.first;
                          }

                          return DropdownButtonFormField<TransactionCategory>(
                            value: selectedCategory,
                            decoration: const InputDecoration(
                              labelText: 'Category *',
                              border: OutlineInputBorder(),
                            ),
                            items: filteredCategories.map((category) => DropdownMenuItem(
                              value: category,
                              child: Row(
                                children: [
                                  Text(
                                    CategoryIcons.getIcon(category.icon),
                                    style: const TextStyle(fontSize: 16),
                                  ),
                                  const SizedBox(width: 8),
                                  Text(category.name),
                                ],
                              ),
                            )).toList(),
                            onChanged: (value) {
                              setDialogState(() {
                                selectedCategory = value;
                              });
                            },
                          );
                        },
                        loading: () => const LinearProgressIndicator(),
                        error: (error, stack) => Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.red[300]!),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Error loading categories: $error',
                            style: TextStyle(color: Colors.red[600]),
                          ),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: notesController,
                    decoration: const InputDecoration(
                      labelText: 'Notes (Optional)',
                      border: OutlineInputBorder(),
                      hintText: 'Additional notes...',
                    ),
                    maxLines: 2,
                  ),
                  const SizedBox(height: 16),
                  ListTile(
                    title: const Text('Date'),
                    subtitle: Text('${selectedDate.day}/${selectedDate.month}/${selectedDate.year}'),
                    trailing: const Icon(Icons.calendar_today),
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: selectedDate,
                        firstDate: DateTime(2020),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                      );
                      if (date != null) {
                        setDialogState(() {
                          selectedDate = date;
                        });
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                if (descriptionController.text.trim().isEmpty ||
                    amountController.text.trim().isEmpty ||
                    selectedCategory == null ||
                    (selectedType == TransactionType.transfer && selectedToAccount == null)) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Please fill in all required fields')),
                  );
                  return;
                }

                final amount = double.tryParse(amountController.text.trim());
                if (amount == null || amount <= 0) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Please enter a valid amount')),
                  );
                  return;
                }

                final transaction = MoneyTransaction(
                  accountId: selectedAccount.id,
                  toAccountId: selectedToAccount?.id,
                  type: selectedType,
                  amount: amount,
                  category: selectedCategory!.name,
                  description: descriptionController.text.trim(),
                  notes: notesController.text.trim().isEmpty ? null : notesController.text.trim(),
                  date: selectedDate,
                );

                final navigator = Navigator.of(context);
                final messenger = ScaffoldMessenger.of(context);

                try {
                  await ref.read(transactionsProvider.notifier).addTransaction(transaction);
                  navigator.pop();
                  messenger.showSnackBar(
                    const SnackBar(content: Text('Transaction added successfully')),
                  );
                } catch (e) {
                  messenger.showSnackBar(
                    SnackBar(content: Text('Error adding transaction: $e')),
                  );
                }
              },
              child: const Text('Add Transaction'),
            ),
          ],
        ),
      ),
    );
  }

  void _showBudgetGoalMenu() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'What would you like to create?',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: Card(
                    child: InkWell(
                      onTap: () {
                        Navigator.of(context).pop();
                        _showAddBudgetDialog();
                      },
                      borderRadius: BorderRadius.circular(12),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          children: [
                            Icon(
                              Icons.pie_chart,
                              size: 48,
                              color: AppTheme.moneyFlowColor,
                            ),
                            const SizedBox(height: 12),
                            Text(
                              'Budget',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Track spending limits',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Colors.grey[600],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Card(
                    child: InkWell(
                      onTap: () {
                        Navigator.of(context).pop();
                        _showAddGoalDialog();
                      },
                      borderRadius: BorderRadius.circular(12),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          children: [
                            Icon(
                              Icons.flag,
                              size: 48,
                              color: AppTheme.moneyFlowColor,
                            ),
                            const SizedBox(height: 12),
                            Text(
                              'Goal',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Save for something special',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Colors.grey[600],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _showAddBudgetDialog() {
    final nameController = TextEditingController();
    final amountController = TextEditingController();
    String selectedCategory = 'Food & Dining';
    BudgetPeriod selectedPeriod = BudgetPeriod.monthly;
    String selectedColor = '#4CAF50';
    DateTime startDate = DateTime.now();
    DateTime endDate = DateTime(DateTime.now().year, DateTime.now().month + 1, 0);

    final categories = [
      'Food & Dining', 'Transportation', 'Shopping', 'Entertainment',
      'Bills & Utilities', 'Healthcare', 'Education', 'Travel',
      'Groceries', 'Gas', 'Other'
    ];

    final colors = [
      '#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336',
      '#795548', '#607D8B', '#E91E63', '#3F51B5', '#009688',
    ];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Create Budget'),
          content: SizedBox(
            width: 500,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'Budget Name *',
                      border: OutlineInputBorder(),
                      hintText: 'e.g., Monthly Food Budget',
                    ),
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    value: selectedCategory,
                    decoration: const InputDecoration(
                      labelText: 'Category *',
                      border: OutlineInputBorder(),
                    ),
                    items: categories.map((category) => DropdownMenuItem(
                      value: category,
                      child: Text(category),
                    )).toList(),
                    onChanged: (value) {
                      setDialogState(() {
                        selectedCategory = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: amountController,
                    decoration: const InputDecoration(
                      labelText: 'Budget Amount *',
                      border: OutlineInputBorder(),
                      hintText: '0.00',
                      prefixText: '\$ ',
                    ),
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<BudgetPeriod>(
                    value: selectedPeriod,
                    decoration: const InputDecoration(
                      labelText: 'Budget Period *',
                      border: OutlineInputBorder(),
                    ),
                    items: BudgetPeriod.values.map((period) => DropdownMenuItem(
                      value: period,
                      child: Text(period.displayName),
                    )).toList(),
                    onChanged: (value) {
                      setDialogState(() {
                        selectedPeriod = value!;
                        // Update end date based on period
                        switch (selectedPeriod) {
                          case BudgetPeriod.weekly:
                            endDate = startDate.add(const Duration(days: 7));
                            break;
                          case BudgetPeriod.monthly:
                            endDate = DateTime(startDate.year, startDate.month + 1, 0);
                            break;
                          case BudgetPeriod.quarterly:
                            endDate = DateTime(startDate.year, startDate.month + 3, 0);
                            break;
                          case BudgetPeriod.yearly:
                            endDate = DateTime(startDate.year + 1, startDate.month, startDate.day);
                            break;
                        }
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ListTile(
                          title: const Text('Start Date'),
                          subtitle: Text('${startDate.day}/${startDate.month}/${startDate.year}'),
                          onTap: () async {
                            final date = await showDatePicker(
                              context: context,
                              initialDate: startDate,
                              firstDate: DateTime(2020),
                              lastDate: DateTime(2030),
                            );
                            if (date != null) {
                              setDialogState(() {
                                startDate = date;
                              });
                            }
                          },
                        ),
                      ),
                      Expanded(
                        child: ListTile(
                          title: const Text('End Date'),
                          subtitle: Text('${endDate.day}/${endDate.month}/${endDate.year}'),
                          onTap: () async {
                            final date = await showDatePicker(
                              context: context,
                              initialDate: endDate,
                              firstDate: startDate,
                              lastDate: DateTime(2030),
                            );
                            if (date != null) {
                              setDialogState(() {
                                endDate = date;
                              });
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Budget Color'),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        children: colors.map((color) {
                          final colorValue = Color(int.parse(color.replaceFirst('#', '0xFF')));
                          final isSelected = selectedColor == color;
                          return GestureDetector(
                            onTap: () {
                              setDialogState(() {
                                selectedColor = color;
                              });
                            },
                            child: Container(
                              width: 32,
                              height: 32,
                              decoration: BoxDecoration(
                                color: colorValue,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: isSelected ? Colors.black : Colors.transparent,
                                  width: 2,
                                ),
                              ),
                              child: isSelected ? const Icon(Icons.check, color: Colors.white, size: 16) : null,
                            ),
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                if (nameController.text.trim().isEmpty ||
                    amountController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Please fill in all required fields')),
                  );
                  return;
                }

                final amount = double.tryParse(amountController.text.trim());
                if (amount == null || amount <= 0) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Please enter a valid amount')),
                  );
                  return;
                }

                final budget = Budget(
                  name: nameController.text.trim(),
                  category: selectedCategory,
                  amount: amount,
                  period: selectedPeriod,
                  startDate: startDate,
                  endDate: endDate,
                  color: selectedColor,
                );

                final navigator = Navigator.of(context);
                final messenger = ScaffoldMessenger.of(context);

                try {
                  await ref.read(budgetsProvider.notifier).addBudget(budget);
                  navigator.pop();
                  messenger.showSnackBar(
                    const SnackBar(content: Text('Budget created successfully')),
                  );
                } catch (e) {
                  messenger.showSnackBar(
                    SnackBar(content: Text('Error creating budget: $e')),
                  );
                }
              },
              child: const Text('Create Budget'),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddGoalDialog() {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    final targetAmountController = TextEditingController();
    GoalCategory selectedCategory = GoalCategory.emergencyFund;
    String selectedColor = '#4CAF50';
    DateTime targetDate = DateTime.now().add(const Duration(days: 365));

    final colors = [
      '#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336',
      '#795548', '#607D8B', '#E91E63', '#3F51B5', '#009688',
    ];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Create Goal'),
          content: SizedBox(
            width: 500,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'Goal Name *',
                      border: OutlineInputBorder(),
                      hintText: 'e.g., Emergency Fund',
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description *',
                      border: OutlineInputBorder(),
                      hintText: 'e.g., Save for unexpected expenses',
                    ),
                    maxLines: 2,
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<GoalCategory>(
                    value: selectedCategory,
                    decoration: const InputDecoration(
                      labelText: 'Category *',
                      border: OutlineInputBorder(),
                    ),
                    items: GoalCategory.values.map((category) => DropdownMenuItem(
                      value: category,
                      child: Row(
                        children: [
                          Text(category.icon, style: const TextStyle(fontSize: 20)),
                          const SizedBox(width: 8),
                          Text(category.displayName),
                        ],
                      ),
                    )).toList(),
                    onChanged: (value) {
                      setDialogState(() {
                        selectedCategory = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: targetAmountController,
                    decoration: const InputDecoration(
                      labelText: 'Target Amount *',
                      border: OutlineInputBorder(),
                      hintText: '0.00',
                      prefixText: '\$ ',
                    ),
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  ),
                  const SizedBox(height: 16),
                  ListTile(
                    title: const Text('Target Date'),
                    subtitle: Text('${targetDate.day}/${targetDate.month}/${targetDate.year}'),
                    trailing: const Icon(Icons.calendar_today),
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: targetDate,
                        firstDate: DateTime.now(),
                        lastDate: DateTime(2030),
                      );
                      if (date != null) {
                        setDialogState(() {
                          targetDate = date;
                        });
                      }
                    },
                  ),
                  const SizedBox(height: 16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Goal Color'),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        children: colors.map((color) {
                          final colorValue = Color(int.parse(color.replaceFirst('#', '0xFF')));
                          final isSelected = selectedColor == color;
                          return GestureDetector(
                            onTap: () {
                              setDialogState(() {
                                selectedColor = color;
                              });
                            },
                            child: Container(
                              width: 32,
                              height: 32,
                              decoration: BoxDecoration(
                                color: colorValue,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: isSelected ? Colors.black : Colors.transparent,
                                  width: 2,
                                ),
                              ),
                              child: isSelected ? const Icon(Icons.check, color: Colors.white, size: 16) : null,
                            ),
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                if (nameController.text.trim().isEmpty ||
                    descriptionController.text.trim().isEmpty ||
                    targetAmountController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Please fill in all required fields')),
                  );
                  return;
                }

                final targetAmount = double.tryParse(targetAmountController.text.trim());
                if (targetAmount == null || targetAmount <= 0) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Please enter a valid target amount')),
                  );
                  return;
                }

                final goal = Goal(
                  name: nameController.text.trim(),
                  description: descriptionController.text.trim(),
                  targetAmount: targetAmount,
                  targetDate: targetDate,
                  category: selectedCategory,
                  color: selectedColor,
                );

                final navigator = Navigator.of(context);
                final messenger = ScaffoldMessenger.of(context);

                try {
                  await ref.read(goalsProvider.notifier).addGoal(goal);
                  navigator.pop();
                  messenger.showSnackBar(
                    const SnackBar(content: Text('Goal created successfully')),
                  );
                } catch (e) {
                  messenger.showSnackBar(
                    SnackBar(content: Text('Error creating goal: $e')),
                  );
                }
              },
              child: const Text('Create Goal'),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddCategoryMenu() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'What type of category would you like to create?',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: Card(
                    child: InkWell(
                      onTap: () {
                        Navigator.of(context).pop();
                        _showAddCategoryDialog(CategoryType.income);
                      },
                      borderRadius: BorderRadius.circular(12),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          children: [
                            Icon(
                              Icons.trending_up,
                              size: 48,
                              color: Colors.green,
                            ),
                            const SizedBox(height: 12),
                            Text(
                              'Income',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Money coming in',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Colors.grey[600],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Card(
                    child: InkWell(
                      onTap: () {
                        Navigator.of(context).pop();
                        _showAddCategoryDialog(CategoryType.expense);
                      },
                      borderRadius: BorderRadius.circular(12),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          children: [
                            Icon(
                              Icons.trending_down,
                              size: 48,
                              color: Colors.red,
                            ),
                            const SizedBox(height: 12),
                            Text(
                              'Expense',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Money going out',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Colors.grey[600],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Card(
                    child: InkWell(
                      onTap: () {
                        Navigator.of(context).pop();
                        _showAddCategoryDialog(CategoryType.transfer);
                      },
                      borderRadius: BorderRadius.circular(12),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          children: [
                            Icon(
                              Icons.swap_horiz,
                              size: 48,
                              color: Colors.blue,
                            ),
                            const SizedBox(height: 12),
                            Text(
                              'Transfer',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Between accounts',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Colors.grey[600],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _showAddCategoryDialog(CategoryType type) {
    final nameController = TextEditingController();
    String selectedIcon = CategoryIcons.getIconsByType(type).first;
    String selectedColor = '#2196F3';

    final availableIcons = CategoryIcons.getIconsByType(type);
    final colors = [
      '#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336',
      '#795548', '#607D8B', '#E91E63', '#3F51B5', '#009688',
      '#FF5722', '#8BC34A', '#03DAC6', '#FFC107', '#673AB7',
    ];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text('Create ${type.displayName} Category'),
          content: SizedBox(
            width: 500,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'Category Name *',
                      border: OutlineInputBorder(),
                      hintText: 'e.g., Groceries',
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Select Icon',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Container(
                    height: 150,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: GridView.builder(
                      padding: const EdgeInsets.all(8),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 6,
                        crossAxisSpacing: 8,
                        mainAxisSpacing: 8,
                      ),
                      itemCount: availableIcons.length,
                      itemBuilder: (context, index) {
                        final iconKey = availableIcons[index];
                        final iconEmoji = CategoryIcons.getIcon(iconKey);
                        final isSelected = selectedIcon == iconKey;

                        return GestureDetector(
                          onTap: () {
                            setDialogState(() {
                              selectedIcon = iconKey;
                            });
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: isSelected ? AppTheme.moneyFlowColor.withValues(alpha: 0.1) : null,
                              border: Border.all(
                                color: isSelected ? AppTheme.moneyFlowColor : Colors.grey[300]!,
                                width: isSelected ? 2 : 1,
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Center(
                              child: Text(
                                iconEmoji,
                                style: const TextStyle(fontSize: 20),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Select Color',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 12,
                    runSpacing: 12,
                    children: colors.map((color) {
                      final colorValue = Color(int.parse(color.replaceFirst('#', '0xFF')));
                      final isSelected = selectedColor == color;
                      return GestureDetector(
                        onTap: () {
                          setDialogState(() {
                            selectedColor = color;
                          });
                        },
                        child: Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: colorValue,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: isSelected ? Colors.black : Colors.grey[300]!,
                              width: isSelected ? 2 : 1,
                            ),
                          ),
                          child: isSelected
                              ? const Icon(Icons.check, color: Colors.white, size: 16)
                              : null,
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                if (nameController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Please enter a category name')),
                  );
                  return;
                }

                final navigator = Navigator.of(context);
                final messenger = ScaffoldMessenger.of(context);

                try {
                  final newCategory = TransactionCategory(
                    name: nameController.text.trim(),
                    type: type,
                    icon: selectedIcon,
                    color: selectedColor,
                  );
                  await ref.read(categoriesProvider.notifier).addCategory(newCategory);
                  navigator.pop();
                  messenger.showSnackBar(
                    const SnackBar(content: Text('Category created successfully')),
                  );
                } catch (e) {
                  messenger.showSnackBar(
                    SnackBar(content: Text('Error creating category: $e')),
                  );
                }
              },
              child: const Text('Create'),
            ),
          ],
        ),
      ),
    );
  }
}
