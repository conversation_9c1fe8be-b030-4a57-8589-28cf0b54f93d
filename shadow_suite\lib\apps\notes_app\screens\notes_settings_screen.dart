import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class NotesSettingsScreen extends ConsumerWidget {
  const NotesSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: Colors.amber,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSettingsSection(
            context,
            'Appearance',
            [
              _buildSettingsTile(
                context,
                'Theme',
                'Light',
                Icons.palette,
                () {},
              ),
              _buildSettingsTile(
                context,
                'Font Size',
                'Medium',
                Icons.text_fields,
                () {},
              ),
              _buildSwitchTile(
                context,
                'Dark Mode',
                'Use dark theme for better readability',
                Icons.dark_mode,
                false,
                (value) {},
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSettingsSection(
            context,
            'Editor',
            [
              _buildSwitchTile(
                context,
                'Auto-save',
                'Automatically save notes while typing',
                Icons.save,
                true,
                (value) {},
              ),
              _buildSwitchTile(
                context,
                'Word Wrap',
                'Wrap long lines in editor',
                Icons.wrap_text,
                true,
                (value) {},
              ),
              _buildSettingsTile(
                context,
                'Default Category',
                'Personal',
                Icons.category,
                () {},
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSettingsSection(
            context,
            'Backup & Sync',
            [
              _buildSettingsTile(
                context,
                'Backup Location',
                'Local Storage',
                Icons.backup,
                () {},
              ),
              _buildSwitchTile(
                context,
                'Auto Backup',
                'Automatically backup notes daily',
                Icons.cloud_upload,
                false,
                (value) {},
              ),
              _buildSettingsTile(
                context,
                'Export Notes',
                'Export all notes to file',
                Icons.download,
                () {},
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSettingsSection(
            context,
            'Privacy',
            [
              _buildSwitchTile(
                context,
                'Password Protection',
                'Require password to access notes',
                Icons.lock,
                false,
                (value) {},
              ),
              _buildSettingsTile(
                context,
                'Clear Search History',
                'Remove all search history',
                Icons.clear_all,
                () => _showClearHistoryDialog(context),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(
    BuildContext context,
    String title,
    List<Widget> children,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.amber,
          ),
        ),
        const SizedBox(height: 8),
        Card(
          elevation: 2,
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildSettingsTile(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: Colors.amber),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  Widget _buildSwitchTile(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return SwitchListTile(
      secondary: Icon(icon, color: Colors.amber),
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      activeColor: Colors.amber,
    );
  }

  void _showClearHistoryDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Search History'),
        content: const Text(
          'Are you sure you want to clear all search history? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Search history cleared')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }
}
