import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Notes App Settings Screen
class NotesSettingsScreen extends ConsumerStatefulWidget {
  const NotesSettingsScreen({super.key});

  @override
  ConsumerState<NotesSettingsScreen> createState() =>
      _NotesSettingsScreenState();
}

class _NotesSettingsScreenState extends ConsumerState<NotesSettingsScreen> {
  // Display Settings
  bool _showPreview = true;
  bool _enableMarkdown = true;
  bool _showLineNumbers = false;
  double _fontSize = 16.0;
  String _fontFamily = 'System';
  bool _darkMode = false;

  // Editor Settings
  bool _autoSave = true;
  int _autoSaveInterval = 30; // seconds
  bool _enableSpellCheck = true;
  bool _enableWordWrap = true;
  bool _showWordCount = true;

  // Organization Settings
  String _defaultNoteType = 'text';
  Color _defaultNoteColor = Colors.white;
  bool _enableTags = true;
  bool _enableFolders = true;
  String _sortBy = 'updated';
  bool _sortAscending = false;

  // Sync & Backup
  bool _enableCloudSync = false;
  bool _autoBackup = true;
  int _backupFrequency = 24; // hours
  bool _enableVersionHistory = true;
  int _maxVersions = 10;

  // Security
  bool _enableEncryption = false;
  bool _enablePinLock = false;
  bool _enableBiometric = false;

  final List<String> _fontFamilies = [
    'System',
    'Roboto',
    'Open Sans',
    'Lato',
    'Montserrat',
    'Source Sans Pro',
  ];

  final List<String> _sortOptions = [
    'created',
    'updated',
    'title',
    'type',
    'priority',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notes Settings'),
        backgroundColor: Colors.amber,
        foregroundColor: Colors.white,
        actions: [
          IconButton(icon: const Icon(Icons.save), onPressed: _saveSettings),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Display Settings
          _buildSectionHeader('Display Settings'),
          _buildSwitchTile(
            'Show Note Preview',
            'Display content preview in note list',
            _showPreview,
            (value) => setState(() => _showPreview = value),
          ),
          _buildSwitchTile(
            'Enable Markdown',
            'Support markdown formatting in text notes',
            _enableMarkdown,
            (value) => setState(() => _enableMarkdown = value),
          ),
          _buildSwitchTile(
            'Show Line Numbers',
            'Display line numbers in text editor',
            _showLineNumbers,
            (value) => setState(() => _showLineNumbers = value),
          ),
          _buildSliderTile(
            'Font Size',
            'Adjust text size for better readability',
            _fontSize,
            12.0,
            24.0,
            (value) => setState(() => _fontSize = value),
            '${_fontSize.toInt()}pt',
          ),
          _buildDropdownTile(
            'Font Family',
            _fontFamily,
            _fontFamilies,
            (value) => setState(() => _fontFamily = value!),
          ),
          _buildSwitchTile(
            'Dark Mode',
            'Use dark theme for notes editor',
            _darkMode,
            (value) => setState(() => _darkMode = value),
          ),

          const SizedBox(height: 24),

          // Editor Settings
          _buildSectionHeader('Editor Settings'),
          _buildSwitchTile(
            'Auto Save',
            'Automatically save changes while typing',
            _autoSave,
            (value) => setState(() => _autoSave = value),
          ),
          _buildSliderTile(
            'Auto Save Interval',
            'Seconds between automatic saves',
            _autoSaveInterval.toDouble(),
            5.0,
            120.0,
            (value) => setState(() => _autoSaveInterval = value.round()),
            '${_autoSaveInterval}s',
          ),
          _buildSwitchTile(
            'Spell Check',
            'Check spelling while typing',
            _enableSpellCheck,
            (value) => setState(() => _enableSpellCheck = value),
          ),
          _buildSwitchTile(
            'Word Wrap',
            'Wrap long lines in editor',
            _enableWordWrap,
            (value) => setState(() => _enableWordWrap = value),
          ),
          _buildSwitchTile(
            'Show Word Count',
            'Display word count in editor',
            _showWordCount,
            (value) => setState(() => _showWordCount = value),
          ),

          const SizedBox(height: 24),

          // Organization Settings
          _buildSectionHeader('Organization'),
          _buildNoteTypeSelector(),
          _buildColorSelector(),
          _buildSwitchTile(
            'Enable Tags',
            'Use tags to organize notes',
            _enableTags,
            (value) => setState(() => _enableTags = value),
          ),
          _buildSwitchTile(
            'Enable Folders',
            'Organize notes in folders',
            _enableFolders,
            (value) => setState(() => _enableFolders = value),
          ),
          _buildDropdownTile(
            'Sort By',
            _sortBy,
            _sortOptions,
            (value) => setState(() => _sortBy = value!),
          ),
          _buildSwitchTile(
            'Sort Ascending',
            'Sort in ascending order',
            _sortAscending,
            (value) => setState(() => _sortAscending = value),
          ),

          const SizedBox(height: 24),

          // Sync & Backup
          _buildSectionHeader('Sync & Backup'),
          _buildSwitchTile(
            'Cloud Sync',
            'Sync notes across devices',
            _enableCloudSync,
            (value) => setState(() => _enableCloudSync = value),
          ),
          _buildSwitchTile(
            'Auto Backup',
            'Automatically backup notes',
            _autoBackup,
            (value) => setState(() => _autoBackup = value),
          ),
          _buildSliderTile(
            'Backup Frequency',
            'Hours between automatic backups',
            _backupFrequency.toDouble(),
            1.0,
            168.0,
            (value) => setState(() => _backupFrequency = value.round()),
            '${_backupFrequency}h',
          ),
          _buildSwitchTile(
            'Version History',
            'Keep history of note changes',
            _enableVersionHistory,
            (value) => setState(() => _enableVersionHistory = value),
          ),
          _buildSliderTile(
            'Max Versions',
            'Maximum versions to keep per note',
            _maxVersions.toDouble(),
            5.0,
            50.0,
            (value) => setState(() => _maxVersions = value.round()),
            '${_maxVersions} versions',
          ),

          const SizedBox(height: 24),

          // Security
          _buildSectionHeader('Security'),
          _buildSwitchTile(
            'Enable Encryption',
            'Encrypt notes for security',
            _enableEncryption,
            (value) => setState(() => _enableEncryption = value),
          ),
          _buildSwitchTile(
            'PIN Lock',
            'Require PIN to access notes',
            _enablePinLock,
            (value) => setState(() => _enablePinLock = value),
          ),
          _buildSwitchTile(
            'Biometric Lock',
            'Use fingerprint/face unlock',
            _enableBiometric,
            (value) => setState(() => _enableBiometric = value),
          ),

          const SizedBox(height: 24),

          // Data Management
          _buildSectionHeader('Data Management'),
          _buildActionTile(
            'Export All Notes',
            'Export notes to external file',
            Icons.download,
            _exportNotes,
          ),
          _buildActionTile(
            'Import Notes',
            'Import notes from file',
            Icons.upload,
            _importNotes,
          ),
          _buildActionTile(
            'Clear Cache',
            'Remove temporary files',
            Icons.cleaning_services,
            _clearCache,
          ),
          _buildActionTile(
            'Reset Settings',
            'Reset all settings to default',
            Icons.restore,
            _resetSettings,
          ),
          _buildActionTile(
            'Delete All Notes',
            'Permanently delete all notes',
            Icons.delete_forever,
            _deleteAllNotes,
            isDestructive: true,
          ),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16, top: 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
          color: Colors.amber,
        ),
      ),
    );
  }

  Widget _buildNoteTypeSelector() {
    return ListTile(
      title: const Text('Default Note Type'),
      subtitle: Text('New notes will be created as $_defaultNoteType'),
      trailing: DropdownButton<String>(
        value: _defaultNoteType,
        items: const [
          DropdownMenuItem(value: 'text', child: Text('Text')),
          DropdownMenuItem(value: 'checklist', child: Text('Checklist')),
          DropdownMenuItem(value: 'canvas', child: Text('Canvas')),
        ],
        onChanged: (value) {
          if (value != null) {
            setState(() => _defaultNoteType = value);
          }
        },
      ),
    );
  }

  Widget _buildColorSelector() {
    final colors = [
      Colors.white,
      Colors.yellow[100]!,
      Colors.green[100]!,
      Colors.blue[100]!,
      Colors.purple[100]!,
      Colors.pink[100]!,
    ];

    return ListTile(
      title: const Text('Default Note Color'),
      subtitle: const Text('Color for new notes'),
      trailing: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: _defaultNoteColor,
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      onTap: () {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Select Color'),
            content: Wrap(
              children: colors.map((color) {
                return GestureDetector(
                  onTap: () {
                    setState(() => _defaultNoteColor = color);
                    Navigator.pop(context);
                  },
                  child: Container(
                    width: 40,
                    height: 40,
                    margin: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: color,
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      activeColor: Colors.amber,
    );
  }

  Widget _buildDropdownTile(
    String title,
    String value,
    List<String> options,
    ValueChanged<String?> onChanged,
  ) {
    return ListTile(
      title: Text(title),
      trailing: DropdownButton<String>(
        value: value,
        items: options.map((option) {
          return DropdownMenuItem(value: option, child: Text(option));
        }).toList(),
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildSliderTile(
    String title,
    String subtitle,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
    String displayValue,
  ) {
    return Column(
      children: [
        ListTile(
          title: Text(title),
          subtitle: Text(subtitle),
          trailing: Text(displayValue),
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          onChanged: onChanged,
          activeColor: Colors.amber,
        ),
      ],
    );
  }

  Widget _buildActionTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap, {
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(icon, color: isDestructive ? Colors.red : Colors.amber),
      title: Text(
        title,
        style: TextStyle(color: isDestructive ? Colors.red : null),
      ),
      subtitle: Text(subtitle),
      onTap: onTap,
    );
  }

  void _saveSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Settings saved successfully'),
        backgroundColor: Colors.amber,
      ),
    );
  }

  void _exportNotes() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Notes'),
        content: const Text('Export all notes to external file?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Notes exported successfully')),
              );
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  void _importNotes() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import Notes'),
        content: const Text('Import notes from external file?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Notes imported successfully')),
              );
            },
            child: const Text('Import'),
          ),
        ],
      ),
    );
  }

  void _clearCache() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Cache cleared successfully')));
  }

  void _resetSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text('Reset all settings to default values?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Settings reset to default')),
              );
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  void _deleteAllNotes() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete All Notes'),
        content: const Text(
          'This will permanently delete all your notes. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('All notes deleted'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            child: const Text('Delete All'),
          ),
        ],
      ),
    );
  }
}
