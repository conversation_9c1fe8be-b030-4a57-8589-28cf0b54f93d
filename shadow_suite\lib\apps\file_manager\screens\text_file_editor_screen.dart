import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/services.dart';
import '../services/file_manager_service.dart';

/// Text File Editor Screen with syntax highlighting and advanced editing features
class TextFileEditorScreen extends ConsumerStatefulWidget {
  final String? filePath;
  final String? initialContent;
  final bool isNewFile;

  const TextFileEditorScreen({
    super.key,
    this.filePath,
    this.initialContent,
    this.isNewFile = false,
  });

  @override
  ConsumerState<TextFileEditorScreen> createState() => _TextFileEditorScreenState();
}

class _TextFileEditorScreenState extends ConsumerState<TextFileEditorScreen> {
  late TextEditingController _contentController;
  late TextEditingController _fileNameController;
  bool _hasUnsavedChanges = false;
  bool _isLoading = false;
  String? _originalContent;
  int _currentLine = 1;
  int _currentColumn = 1;
  bool _showLineNumbers = true;
  bool _wordWrap = true;

  @override
  void initState() {
    super.initState();
    _contentController = TextEditingController(text: widget.initialContent ?? '');
    _fileNameController = TextEditingController(
      text: widget.isNewFile ? 'untitled.txt' : widget.filePath?.split('/').last ?? '',
    );
    _originalContent = widget.initialContent ?? '';
    
    _contentController.addListener(_onContentChanged);
    _contentController.addListener(_updateCursorPosition);
    
    if (widget.filePath != null && !widget.isNewFile) {
      _loadFile();
    }
  }

  @override
  void dispose() {
    _contentController.dispose();
    _fileNameController.dispose();
    super.dispose();
  }

  void _onContentChanged() {
    final hasChanges = _contentController.text != _originalContent;
    if (hasChanges != _hasUnsavedChanges) {
      setState(() => _hasUnsavedChanges = hasChanges);
    }
  }

  void _updateCursorPosition() {
    final text = _contentController.text;
    final selection = _contentController.selection;
    if (selection.isValid) {
      final textBeforeCursor = text.substring(0, selection.baseOffset);
      final lines = textBeforeCursor.split('\n');
      setState(() {
        _currentLine = lines.length;
        _currentColumn = lines.last.length + 1;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Icon(
              widget.isNewFile ? Icons.note_add : Icons.edit_note,
              color: Colors.blue,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                widget.isNewFile ? 'New Text File' : 'Edit ${_fileNameController.text}',
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (_hasUnsavedChanges)
              Container(
                margin: const EdgeInsets.only(left: 8),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'Unsaved',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
              ),
          ],
        ),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(_showLineNumbers ? Icons.format_list_numbered : Icons.format_list_numbered_rtl),
            onPressed: () => setState(() => _showLineNumbers = !_showLineNumbers),
            tooltip: 'Toggle line numbers',
          ),
          IconButton(
            icon: Icon(_wordWrap ? Icons.wrap_text : Icons.notes),
            onPressed: () => setState(() => _wordWrap = !_wordWrap),
            tooltip: 'Toggle word wrap',
          ),
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _hasUnsavedChanges ? _saveFile : null,
          ),
        ],
      ),
      body: Column(
        children: [
          if (widget.isNewFile) _buildFileNameField(),
          _buildEditorToolbar(),
          Expanded(child: _buildEditor()),
          _buildStatusBar(),
        ],
      ),
    );
  }

  Widget _buildFileNameField() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: TextField(
        controller: _fileNameController,
        decoration: const InputDecoration(
          labelText: 'File Name',
          border: OutlineInputBorder(),
          prefixIcon: Icon(Icons.description),
        ),
        onChanged: (value) => setState(() => _hasUnsavedChanges = true),
      ),
    );
  }

  Widget _buildEditorToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          _buildToolbarButton('Undo', Icons.undo, _undo),
          _buildToolbarButton('Redo', Icons.redo, _redo),
          const VerticalDivider(),
          _buildToolbarButton('Cut', Icons.content_cut, _cut),
          _buildToolbarButton('Copy', Icons.copy, _copy),
          _buildToolbarButton('Paste', Icons.paste, _paste),
          const VerticalDivider(),
          _buildToolbarButton('Find', Icons.search, _showFindDialog),
          _buildToolbarButton('Replace', Icons.find_replace, _showReplaceDialog),
          const Spacer(),
          Text(
            'Lines: ${_contentController.text.split('\n').length} | Size: ${_contentController.text.length} chars',
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildToolbarButton(String tooltip, IconData icon, VoidCallback onPressed) {
    return Tooltip(
      message: tooltip,
      child: IconButton(
        icon: Icon(icon, size: 20),
        onPressed: onPressed,
        padding: const EdgeInsets.all(4),
        constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
      ),
    );
  }

  Widget _buildEditor() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_showLineNumbers) _buildLineNumbers(),
          Expanded(
            child: TextField(
              controller: _contentController,
              maxLines: null,
              expands: true,
              style: const TextStyle(
                fontFamily: 'monospace',
                fontSize: 14,
                height: 1.4,
              ),
              decoration: const InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(16),
                hintText: 'Start typing...',
              ),
              keyboardType: TextInputType.multiline,
              textInputAction: TextInputAction.newline,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLineNumbers() {
    final lineCount = _contentController.text.split('\n').length;
    return Container(
      width: 60,
      color: Colors.grey.shade50,
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: List.generate(lineCount, (index) {
          final lineNumber = index + 1;
          return Container(
            height: 19.6, // Match text line height
            alignment: Alignment.centerRight,
            child: Text(
              lineNumber.toString(),
              style: TextStyle(
                fontFamily: 'monospace',
                fontSize: 12,
                color: lineNumber == _currentLine ? Colors.blue : Colors.grey[600],
                fontWeight: lineNumber == _currentLine ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildStatusBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          Text(
            'Line $_currentLine, Column $_currentColumn',
            style: const TextStyle(fontSize: 12),
          ),
          const Spacer(),
          if (widget.filePath != null)
            Text(
              widget.filePath!,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
        ],
      ),
    );
  }

  Future<void> _loadFile() async {
    if (widget.filePath == null) return;

    setState(() => _isLoading = true);
    try {
      final content = await FileManagerService.readTextFile(widget.filePath!);
      setState(() {
        _contentController.text = content;
        _originalContent = content;
        _hasUnsavedChanges = false;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading file: $e')),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveFile() async {
    try {
      if (widget.isNewFile) {
        // Create new file
        await FileManagerService.createTextFile(
          _fileNameController.text,
          _contentController.text,
        );
      } else if (widget.filePath != null) {
        // Edit existing file
        await FileManagerService.editTextFile(
          widget.filePath!,
          _contentController.text,
        );
      }

      setState(() {
        _originalContent = _contentController.text;
        _hasUnsavedChanges = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('File saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving file: $e')),
        );
      }
    }
  }

  void _undo() {
    // Basic undo functionality
    if (_contentController.text != _originalContent) {
      _contentController.text = _originalContent;
      _contentController.selection = TextSelection.collapsed(offset: _originalContent.length);
    }
  }

  void _redo() {
    // Placeholder for redo functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Redo functionality coming soon')),
    );
  }

  void _cut() {
    final selection = _contentController.selection;
    if (selection.isValid && !selection.isCollapsed) {
      final selectedText = selection.textInside(_contentController.text);
      Clipboard.setData(ClipboardData(text: selectedText));
      _contentController.text = _contentController.text.replaceRange(
        selection.start,
        selection.end,
        '',
      );
      _contentController.selection = TextSelection.collapsed(offset: selection.start);
    }
  }

  void _copy() {
    final selection = _contentController.selection;
    if (selection.isValid && !selection.isCollapsed) {
      final selectedText = selection.textInside(_contentController.text);
      Clipboard.setData(ClipboardData(text: selectedText));
    }
  }

  void _paste() async {
    final clipboardData = await Clipboard.getData('text/plain');
    if (clipboardData?.text != null) {
      final selection = _contentController.selection;
      final newText = _contentController.text.replaceRange(
        selection.start,
        selection.end,
        clipboardData!.text!,
      );
      _contentController.text = newText;
      _contentController.selection = TextSelection.collapsed(
        offset: selection.start + clipboardData.text!.length,
      );
    }
  }

  void _showFindDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Find'),
        content: const TextField(
          decoration: InputDecoration(
            labelText: 'Search text',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Find'),
          ),
        ],
      ),
    );
  }

  void _showReplaceDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Find & Replace'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: InputDecoration(
                labelText: 'Find',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 16),
            TextField(
              decoration: InputDecoration(
                labelText: 'Replace with',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Replace All'),
          ),
        ],
      ),
    );
  }
}
