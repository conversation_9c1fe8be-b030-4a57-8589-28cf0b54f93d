/// Comprehensive Hadith models for authentic Islamic content
library;

/// Hadith authenticity grading
enum HadithAuthenticity {
  sahih, // Authentic
  hasan, // Good
  daif, // Weak
  mawdu, // Fabricated
}

/// Hadith categories for organization
enum HadithCategory {
  faith, // Iman and <PERSON><PERSON><PERSON><PERSON>
  worship, // Salah, Zakat, Hajj, etc.
  transactions, // Business, trade, contracts
  family, // Marriage, divorce, children
  ethics, // Akhlaq and moral conduct
  knowledge, // Seeking knowledge, teaching
  jihad, // Struggle in the path of Allah
  government, // Leadership and governance
  food, // Halal, haram foods
  dress, // Clothing and appearance
  medicine, // Prophetic medicine
  dreams, // Interpretation of dreams
  endTimes, // Signs of the Hour
  paradise, // Description of Jannah
  hellfire, // Description of Jahannam
  prophets, // Stories of previous prophets
  companions, // Stories of Sahabah
  general, // General guidance
}

/// Complete Hadith model with all authentic elements
class Hadith {
  final String id;
  final String collection;
  final String book;
  final String chapter;
  final int hadithNumber;
  final String arabicText;
  final String englishText;
  final String narrator;
  final String isnad; // Chain of narration
  final HadithAuthenticity authenticity;
  final HadithCategory category;
  final String grade;
  final String reference;
  final String commentary;
  final List<String> keywords;
  final Map<String, String> translations; // Additional language translations

  const Hadith({
    required this.id,
    required this.collection,
    required this.book,
    required this.chapter,
    required this.hadithNumber,
    required this.arabicText,
    required this.englishText,
    required this.narrator,
    required this.isnad,
    required this.authenticity,
    required this.category,
    required this.grade,
    required this.reference,
    required this.commentary,
    this.keywords = const [],
    this.translations = const {},
  });

  /// Get authenticity description
  String get authenticityDescription {
    switch (authenticity) {
      case HadithAuthenticity.sahih:
        return 'Sahih (Authentic) - The highest level of authenticity';
      case HadithAuthenticity.hasan:
        return 'Hasan (Good) - Acceptable for Islamic rulings';
      case HadithAuthenticity.daif:
        return 'Da\'if (Weak) - Not reliable for Islamic rulings';
      case HadithAuthenticity.mawdu:
        return 'Mawdu\' (Fabricated) - Not authentic';
    }
  }

  /// Get category description
  String get categoryDescription {
    switch (category) {
      case HadithCategory.faith:
        return 'Faith and Belief (Iman wa Aqeedah)';
      case HadithCategory.worship:
        return 'Acts of Worship (Ibadat)';
      case HadithCategory.transactions:
        return 'Business and Transactions (Muamalat)';
      case HadithCategory.family:
        return 'Family and Marriage (Nikah wa Usrah)';
      case HadithCategory.ethics:
        return 'Ethics and Morals (Akhlaq)';
      case HadithCategory.knowledge:
        return 'Knowledge and Learning (Ilm)';
      case HadithCategory.jihad:
        return 'Struggle in Allah\'s Path (Jihad)';
      case HadithCategory.government:
        return 'Leadership and Governance (Imamah)';
      case HadithCategory.food:
        return 'Food and Drink (Ta\'am wa Sharab)';
      case HadithCategory.dress:
        return 'Clothing and Appearance (Libas)';
      case HadithCategory.medicine:
        return 'Prophetic Medicine (Tibb Nabawi)';
      case HadithCategory.dreams:
        return 'Dreams and Visions (Ru\'ya)';
      case HadithCategory.endTimes:
        return 'End Times (Ashrat as-Sa\'ah)';
      case HadithCategory.paradise:
        return 'Paradise (Jannah)';
      case HadithCategory.hellfire:
        return 'Hellfire (Jahannam)';
      case HadithCategory.prophets:
        return 'Stories of Prophets (Qasas al-Anbiya)';
      case HadithCategory.companions:
        return 'Companions (Sahabah)';
      case HadithCategory.general:
        return 'General Guidance';
    }
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'collection': collection,
    'book': book,
    'chapter': chapter,
    'hadithNumber': hadithNumber,
    'arabicText': arabicText,
    'englishText': englishText,
    'narrator': narrator,
    'isnad': isnad,
    'authenticity': authenticity.name,
    'category': category.name,
    'grade': grade,
    'reference': reference,
    'commentary': commentary,
    'keywords': keywords,
    'translations': translations,
  };

  factory Hadith.fromJson(Map<String, dynamic> json) => Hadith(
    id: json['id'],
    collection: json['collection'],
    book: json['book'],
    chapter: json['chapter'],
    hadithNumber: json['hadithNumber'],
    arabicText: json['arabicText'],
    englishText: json['englishText'],
    narrator: json['narrator'],
    isnad: json['isnad'],
    authenticity: HadithAuthenticity.values.firstWhere(
      (e) => e.name == json['authenticity'],
    ),
    category: HadithCategory.values.firstWhere(
      (e) => e.name == json['category'],
    ),
    grade: json['grade'],
    reference: json['reference'],
    commentary: json['commentary'],
    keywords: List<String>.from(json['keywords'] ?? []),
    translations: Map<String, String>.from(json['translations'] ?? {}),
  );
}

/// Hadith collection information
class HadithCollection {
  final String name;
  final String author;
  final String description;
  final int totalHadiths;
  final String timeperiod;
  final String language;
  final bool isAuthentic;

  const HadithCollection({
    required this.name,
    required this.author,
    required this.description,
    required this.totalHadiths,
    required this.timeperiod,
    required this.language,
    required this.isAuthentic,
  });

  Map<String, dynamic> toJson() => {
    'name': name,
    'author': author,
    'description': description,
    'totalHadiths': totalHadiths,
    'timeperiod': timeperiod,
    'language': language,
    'isAuthentic': isAuthentic,
  };

  factory HadithCollection.fromJson(Map<String, dynamic> json) =>
      HadithCollection(
        name: json['name'],
        author: json['author'],
        description: json['description'],
        totalHadiths: json['totalHadiths'],
        timeperiod: json['timeperiod'],
        language: json['language'],
        isAuthentic: json['isAuthentic'],
      );
}

/// Hadith search result with relevance scoring
class HadithSearchResult {
  final Hadith hadith;
  final double relevanceScore;
  final List<String> matchedTerms;
  final String matchType; // 'text', 'narrator', 'commentary', etc.

  const HadithSearchResult({
    required this.hadith,
    required this.relevanceScore,
    required this.matchedTerms,
    required this.matchType,
  });

  Map<String, dynamic> toJson() => {
    'hadith': hadith.toJson(),
    'relevanceScore': relevanceScore,
    'matchedTerms': matchedTerms,
    'matchType': matchType,
  };

  factory HadithSearchResult.fromJson(Map<String, dynamic> json) =>
      HadithSearchResult(
        hadith: Hadith.fromJson(json['hadith']),
        relevanceScore: json['relevanceScore'],
        matchedTerms: List<String>.from(json['matchedTerms']),
        matchType: json['matchType'],
      );
}

/// Hadith bookmark for saving favorite hadiths
class HadithBookmark {
  final String id;
  final String hadithId;
  final String title;
  final String? notes;
  final DateTime createdAt;
  final List<String> tags;

  const HadithBookmark({
    required this.id,
    required this.hadithId,
    required this.title,
    this.notes,
    required this.createdAt,
    required this.tags,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'hadithId': hadithId,
    'title': title,
    'notes': notes,
    'createdAt': createdAt.toIso8601String(),
    'tags': tags,
  };

  factory HadithBookmark.fromJson(Map<String, dynamic> json) => HadithBookmark(
    id: json['id'],
    hadithId: json['hadithId'],
    title: json['title'],
    notes: json['notes'],
    createdAt: DateTime.parse(json['createdAt']),
    tags: List<String>.from(json['tags'] ?? []),
  );
}

/// Hadith study session for tracking reading progress
class HadithStudySession {
  final String id;
  final DateTime startTime;
  DateTime? endTime;
  final List<String> hadithsRead;
  final String collection;
  final HadithCategory? category;
  final Duration? duration;
  final Map<String, dynamic> notes;

  HadithStudySession({
    required this.id,
    required this.startTime,
    this.endTime,
    required this.hadithsRead,
    required this.collection,
    this.category,
    this.duration,
    this.notes = const {},
  });

  /// Mark session as completed
  void complete() {
    endTime = DateTime.now();
  }

  /// Get session duration
  Duration get sessionDuration {
    if (endTime != null) {
      return endTime!.difference(startTime);
    }
    return DateTime.now().difference(startTime);
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'startTime': startTime.toIso8601String(),
    'endTime': endTime?.toIso8601String(),
    'hadithsRead': hadithsRead,
    'collection': collection,
    'category': category?.name,
    'duration': duration?.inSeconds,
    'notes': notes,
  };

  factory HadithStudySession.fromJson(
    Map<String, dynamic> json,
  ) => HadithStudySession(
    id: json['id'],
    startTime: DateTime.parse(json['startTime']),
    endTime: json['endTime'] != null ? DateTime.parse(json['endTime']) : null,
    hadithsRead: List<String>.from(json['hadithsRead']),
    collection: json['collection'],
    category: json['category'] != null
        ? HadithCategory.values.firstWhere((e) => e.name == json['category'])
        : null,
    duration: json['duration'] != null
        ? Duration(seconds: json['duration'])
        : null,
    notes: Map<String, dynamic>.from(json['notes'] ?? {}),
  );
}

/// Hadith statistics for tracking study progress
class HadithStatistics {
  final int totalHadithsRead;
  final int totalStudySessions;
  final Duration totalStudyTime;
  final Map<String, int> collectionProgress;
  final Map<HadithCategory, int> categoryProgress;
  final int bookmarkedHadiths;
  final DateTime lastStudyDate;
  final int currentStreak;

  const HadithStatistics({
    required this.totalHadithsRead,
    required this.totalStudySessions,
    required this.totalStudyTime,
    required this.collectionProgress,
    required this.categoryProgress,
    required this.bookmarkedHadiths,
    required this.lastStudyDate,
    required this.currentStreak,
  });

  Map<String, dynamic> toJson() => {
    'totalHadithsRead': totalHadithsRead,
    'totalStudySessions': totalStudySessions,
    'totalStudyTime': totalStudyTime.inSeconds,
    'collectionProgress': collectionProgress,
    'categoryProgress': categoryProgress.map((k, v) => MapEntry(k.name, v)),
    'bookmarkedHadiths': bookmarkedHadiths,
    'lastStudyDate': lastStudyDate.toIso8601String(),
    'currentStreak': currentStreak,
  };

  factory HadithStatistics.fromJson(Map<String, dynamic> json) =>
      HadithStatistics(
        totalHadithsRead: json['totalHadithsRead'],
        totalStudySessions: json['totalStudySessions'],
        totalStudyTime: Duration(seconds: json['totalStudyTime']),
        collectionProgress: Map<String, int>.from(json['collectionProgress']),
        categoryProgress: (json['categoryProgress'] as Map<String, dynamic>)
            .map(
              (k, v) => MapEntry(
                HadithCategory.values.firstWhere((e) => e.name == k),
                v as int,
              ),
            ),
        bookmarkedHadiths: json['bookmarkedHadiths'],
        lastStudyDate: DateTime.parse(json['lastStudyDate']),
        currentStreak: json['currentStreak'],
      );
}
