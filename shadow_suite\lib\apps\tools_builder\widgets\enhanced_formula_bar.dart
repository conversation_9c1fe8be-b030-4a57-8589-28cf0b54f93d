import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/app_theme.dart';
import '../services/enhanced_formula_engine.dart';
import '../services/tools_providers.dart';

class EnhancedFormulaBar extends ConsumerStatefulWidget {
  final Function(String) onFormulaChanged;
  final String? initialFormula;

  const EnhancedFormulaBar({
    super.key,
    required this.onFormulaChanged,
    this.initialFormula,
  });

  @override
  ConsumerState<EnhancedFormulaBar> createState() => _EnhancedFormulaBarState();
}

class _EnhancedFormulaBarState extends ConsumerState<EnhancedFormulaBar> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialFormula ?? '');
    _focusNode = FocusNode();

    _controller.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _focusNode.removeListener(_onFocusChanged);
    _controller.dispose();
    _focusNode.dispose();
    _removeOverlay();
    super.dispose();
  }

  void _onTextChanged() {
    final text = _controller.text;
    ref.read(currentFormulaProvider.notifier).state = text;

    // Validate formula
    if (text.startsWith('=')) {
      final validation = EnhancedFormulaEngine.validateFormula(text);
      ref.read(formulaValidationProvider.notifier).state = validation;
    } else {
      ref.read(formulaValidationProvider.notifier).state = null;
    }

    // Get suggestions
    if (text.startsWith('=') && text.length > 1) {
      final query = _extractCurrentFunction(text);
      if (query.isNotEmpty) {
        final suggestions = EnhancedFormulaEngine.getSuggestions(query);
        ref.read(formulaSuggestionsProvider.notifier).state = suggestions;
        _showSuggestionsOverlay();
      } else {
        _hideSuggestionsOverlay();
      }
    } else {
      _hideSuggestionsOverlay();
    }

    widget.onFormulaChanged(text);
  }

  void _onFocusChanged() {
    if (!_focusNode.hasFocus) {
      _hideSuggestionsOverlay();
    }
  }

  String _extractCurrentFunction(String formula) {
    final cursorPosition = _controller.selection.baseOffset;
    if (cursorPosition <= 1) return '';

    // Find the current function being typed
    final beforeCursor = formula.substring(1, cursorPosition);
    final match = RegExp(r'([A-Z]*)$').firstMatch(beforeCursor);
    return match?.group(1) ?? '';
  }

  void _showSuggestionsOverlay() {
    if (_overlayEntry != null) return;

    _overlayEntry = OverlayEntry(
      builder: (context) => _buildSuggestionsOverlay(),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideSuggestionsOverlay() {
    _removeOverlay();
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  Widget _buildSuggestionsOverlay() {
    return Positioned(
      left: 0,
      right: 0,
      top: 120, // Adjust based on formula bar position
      child: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          constraints: const BoxConstraints(maxHeight: 300),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Consumer(
            builder: (context, ref, child) {
              final suggestions = ref.watch(formulaSuggestionsProvider);

              if (suggestions.isEmpty) {
                return const SizedBox.shrink();
              }

              return ListView.builder(
                shrinkWrap: true,
                itemCount: suggestions.length,
                itemBuilder: (context, index) {
                  final suggestion = suggestions[index];
                  return _buildSuggestionItem(suggestion);
                },
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildSuggestionItem(FormulaSuggestion suggestion) {
    return InkWell(
      onTap: () => _insertSuggestion(suggestion),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.toolsBuilderColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    suggestion.function.name,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.toolsBuilderColor,
                      fontFamily: 'monospace',
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    suggestion.function.description,
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              'Syntax: ${suggestion.function.syntax}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontFamily: 'monospace',
              ),
            ),
            Text(
              'Example: ${suggestion.function.example}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
                fontFamily: 'monospace',
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _insertSuggestion(FormulaSuggestion suggestion) {
    final currentText = _controller.text;
    final cursorPosition = _controller.selection.baseOffset;

    // Find the start of the current function
    final beforeCursor = currentText.substring(0, cursorPosition);
    final match = RegExp(r'([A-Z]*)$').firstMatch(beforeCursor);

    if (match != null) {
      final startPos = match.start;
      final beforeFunction = currentText.substring(0, startPos);
      final afterCursor = currentText.substring(cursorPosition);

      final newText = '$beforeFunction${suggestion.function.name}($afterCursor';
      _controller.text = newText;
      _controller.selection = TextSelection.collapsed(
        offset: startPos + suggestion.function.name.length + 1,
      );
    }

    _hideSuggestionsOverlay();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Formula bar header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppTheme.toolsBuilderColor,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  'fx',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Consumer(
                  builder: (context, ref, child) {
                    final selectedCell = ref.watch(selectedCellProvider);
                    return Text(
                      selectedCell?.toAddress() ?? 'A1',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    );
                  },
                ),
              ),
              // Function library button
              IconButton(
                onPressed: () => _showFunctionLibrary(),
                icon: const Icon(Icons.functions),
                tooltip: 'Function Library',
              ),
              // Formula validation indicator
              Consumer(
                builder: (context, ref, child) {
                  final validation = ref.watch(formulaValidationProvider);
                  if (validation == null) return const SizedBox.shrink();

                  return IconButton(
                    onPressed: () => _showValidationDetails(validation),
                    icon: Icon(
                      validation.isValid ? Icons.check_circle : Icons.error,
                      color: validation.isValid ? Colors.green : Colors.red,
                    ),
                    tooltip: validation.isValid
                        ? 'Formula is valid'
                        : 'Formula has errors',
                  );
                },
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Formula input field
          TextField(
            controller: _controller,
            focusNode: _focusNode,
            decoration: InputDecoration(
              hintText: 'Enter formula or value...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
              suffixIcon: _controller.text.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _controller.clear();
                        widget.onFormulaChanged('');
                      },
                      icon: const Icon(Icons.clear),
                    )
                  : null,
            ),
            style: const TextStyle(fontFamily: 'monospace', fontSize: 14),
            maxLines: 1,
            onSubmitted: (value) {
              widget.onFormulaChanged(value);
              _focusNode.unfocus();
            },
          ),

          // Validation messages
          Consumer(
            builder: (context, ref, child) {
              final validation = ref.watch(formulaValidationProvider);
              if (validation == null ||
                  (validation.errors.isEmpty && validation.warnings.isEmpty)) {
                return const SizedBox.shrink();
              }

              return Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ...validation.errors.map(
                      (error) => _buildValidationMessage(
                        error,
                        Colors.red,
                        Icons.error,
                      ),
                    ),
                    ...validation.warnings.map(
                      (warning) => _buildValidationMessage(
                        warning,
                        Colors.orange,
                        Icons.warning,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildValidationMessage(String message, Color color, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 8),
          Expanded(
            child: Text(message, style: TextStyle(color: color, fontSize: 12)),
          ),
        ],
      ),
    );
  }

  void _showFunctionLibrary() {
    showDialog(
      context: context,
      builder: (context) => const FunctionLibraryDialog(),
    );
  }

  void _showValidationDetails(FormulaValidationResult validation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Formula Validation'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (validation.errors.isNotEmpty) ...[
              const Text(
                'Errors:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              ...validation.errors.map((error) => Text('• $error')),
              const SizedBox(height: 8),
            ],
            if (validation.warnings.isNotEmpty) ...[
              const Text(
                'Warnings:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
              ),
              ...validation.warnings.map((warning) => Text('• $warning')),
            ],
            if (validation.isValid && validation.warnings.isEmpty)
              const Text(
                'Formula is valid!',
                style: TextStyle(color: Colors.green),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

class FunctionLibraryDialog extends StatefulWidget {
  const FunctionLibraryDialog({super.key});

  @override
  State<FunctionLibraryDialog> createState() => _FunctionLibraryDialogState();
}

class _FunctionLibraryDialogState extends State<FunctionLibraryDialog> {
  String _selectedCategory = 'All';

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 600,
        height: 500,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Function Library',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Search and filter
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      hintText: 'Search functions...',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      // Search functionality can be implemented here
                    },
                  ),
                ),
                const SizedBox(width: 16),
                DropdownButton<String>(
                  value: _selectedCategory,
                  items: const [
                    DropdownMenuItem(
                      value: 'All',
                      child: Text('All Categories'),
                    ),
                    DropdownMenuItem(
                      value: 'Mathematical',
                      child: Text('Mathematical'),
                    ),
                    DropdownMenuItem(value: 'Logical', child: Text('Logical')),
                    DropdownMenuItem(
                      value: 'Financial',
                      child: Text('Financial'),
                    ),
                    DropdownMenuItem(value: 'Text', child: Text('Text')),
                    DropdownMenuItem(
                      value: 'Date & Time',
                      child: Text('Date & Time'),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedCategory = value!;
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Function list
            Expanded(
              child: ListView(
                children: [
                  // This would be populated with actual functions
                  const Text(
                    'Function library content will be implemented here',
                  ),
                ],
              ),
            ),

            // Actions
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Close'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
