import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/quran_models.dart';

/// Widget for displaying a single verse with RTL support and interactive features
class VerseWidget extends StatelessWidget {
  final Verse verse;
  final int surahNumber;
  final ReadingSettings settings;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onTafseerTap;
  final VoidCallback? onBookmarkTap;
  final VoidCallback? onShareTap;
  final bool isBookmarked;
  final bool showVerseNumber;

  const VerseWidget({
    super.key,
    required this.verse,
    required this.surahNumber,
    required this.settings,
    this.onTap,
    this.onLongPress,
    this.onTafseerTap,
    this.onBookmarkTap,
    this.onShareTap,
    this.isBookmarked = false,
    this.showVerseNumber = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              if (showVerseNumber) _buildVerseHeader(context),
              const SizedBox(height: 12),
              _buildArabicText(context),
              if (settings.showTransliteration && verse.transliteration != null) ...[
                const SizedBox(height: 8),
                _buildTransliteration(context),
              ],
              if (settings.showTranslation && verse.translation != null) ...[
                const SizedBox(height: 8),
                _buildTranslation(context),
              ],
              const SizedBox(height: 12),
              _buildActionButtons(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVerseHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            '$surahNumber:${verse.number}',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
              fontSize: 14,
            ),
          ),
        ),
        const Spacer(),
        if (verse.hasSajdah)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.keyboard_arrow_down,
                  size: 16,
                  color: Colors.green.shade700,
                ),
                const SizedBox(width: 4),
                Text(
                  'Sajdah',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.green.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        if (isBookmarked) ...[
          const SizedBox(width: 8),
          Icon(
            Icons.bookmark,
            color: Theme.of(context).primaryColor,
            size: 20,
          ),
        ],
      ],
    );
  }

  Widget _buildArabicText(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).dividerColor.withValues(alpha: 0.3),
        ),
      ),
      child: Text(
        verse.arabicText,
        textAlign: TextAlign.right,
        textDirection: TextDirection.rtl,
        style: TextStyle(
          fontSize: settings.arabicFontSize,
          fontFamily: settings.arabicFontFamily,
          height: settings.lineSpacing,
          color: Theme.of(context).colorScheme.onSurface,
        ),
      ),
    );
  }

  Widget _buildTransliteration(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        verse.transliteration!,
        style: TextStyle(
          fontSize: settings.transliterationFontSize,
          fontStyle: FontStyle.italic,
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
        ),
      ),
    );
  }

  Widget _buildTranslation(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        verse.translation!,
        style: TextStyle(
          fontSize: settings.translationFontSize,
          color: Theme.of(context).colorScheme.onSurface,
          height: 1.4,
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildActionButton(
          context,
          icon: Icons.content_copy,
          label: 'Copy',
          onTap: () => _copyVerse(context),
        ),
        _buildActionButton(
          context,
          icon: Icons.share,
          label: 'Share',
          onTap: onShareTap,
        ),
        _buildActionButton(
          context,
          icon: Icons.book,
          label: 'Tafseer',
          onTap: onTafseerTap,
        ),
        _buildActionButton(
          context,
          icon: isBookmarked ? Icons.bookmark : Icons.bookmark_border,
          label: isBookmarked ? 'Saved' : 'Save',
          onTap: onBookmarkTap,
        ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 20,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _copyVerse(BuildContext context) {
    final text = '${verse.arabicText}\n\n'
        '${verse.transliteration ?? ''}\n\n'
        '${verse.translation ?? ''}\n\n'
        'Quran $surahNumber:${verse.number}';

    Clipboard.setData(ClipboardData(text: text));
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Verse copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
