import 'dart:math' as math;
import '../models/money_manager_models.dart';
import 'money_manager_database.dart';

class BudgetService {
  // Create a new budget
  static Future<String> createBudget({
    required String name,
    required double amount,
    required String categoryId,
    required BudgetPeriod period,
    required DateTime startDate,
    DateTime? endDate,
    bool isRecurring = false,
    List<String> accountIds = const [],
  }) async {
    final budget = Budget(
      id: _generateId(),
      name: name,
      amount: amount,
      categoryId: categoryId,
      period: period,
      startDate: startDate,
      endDate: endDate ?? _calculateEndDate(startDate, period),
      createdAt: DateTime.now(),
    );

    await MoneyManagerDatabase.saveBudget(budget);
    return budget.id;
  }

  // Get all active budgets
  static Future<List<Budget>> getActiveBudgets() async {
    final budgets = await MoneyManagerDatabase.getBudgets();
    final now = DateTime.now();
    
    return budgets.where((budget) {
      return budget.startDate.isBefore(now) &&
             budget.endDate.isAfter(now);
    }).toList();
  }

  // Get budget analytics
  static Future<BudgetAnalytics> getBudgetAnalytics(String budgetId) async {
    // Mock budget for now - replace with actual database implementation
    final budgets = await MoneyManagerDatabase.getBudgets();
    final budget = budgets.where((b) => b.id == budgetId).firstOrNull;
    if (budget == null) {
      throw Exception('Budget not found');
    }

    final transactions = await _getBudgetTransactions(budget);
    final spent = _calculateSpentAmount(transactions);
    final remaining = budget.amount - spent;
    final percentage = budget.amount > 0 ? (spent / budget.amount) * 100 : 0;
    
    final dailyAverage = _calculateDailyAverage(transactions, budget);
    final projectedSpending = _calculateProjectedSpending(budget, dailyAverage);
    
    final status = _determineBudgetStatus(spent.toDouble(), budget.amount, percentage.toDouble());
    final recommendations = _generateRecommendations(budget, spent, projectedSpending);

    return BudgetAnalytics(
      budgetId: budgetId,
      budgetName: budget.name,
      budgetAmount: budget.amount,
      spentAmount: spent,
      remainingAmount: remaining,
      spentPercentage: percentage.toDouble(),
      dailyAverage: dailyAverage,
      projectedSpending: projectedSpending,
      status: status,
      recommendations: recommendations,
      transactionCount: transactions.length,
      daysRemaining: budget.endDate.difference(DateTime.now()).inDays,
      isOnTrack: projectedSpending <= budget.amount,
    );
  }

  // Get spending trends for a budget
  static Future<List<SpendingTrend>> getSpendingTrends(String budgetId) async {
    // Mock budget for now - replace with actual database implementation
    final budgets = await MoneyManagerDatabase.getBudgets();
    final budget = budgets.where((b) => b.id == budgetId).firstOrNull;
    if (budget == null) return [];

    final transactions = await _getBudgetTransactions(budget);
    final trends = <SpendingTrend>[];
    
    // Group transactions by day
    final dailySpending = <DateTime, double>{};
    for (final transaction in transactions) {
      final date = DateTime(transaction.date.year, transaction.date.month, transaction.date.day);
      dailySpending[date] = (dailySpending[date] ?? 0) + transaction.amount;
    }

    // Create trend data
    var cumulativeSpending = 0.0;
    final sortedDates = dailySpending.keys.toList()..sort();
    
    for (final date in sortedDates) {
      cumulativeSpending += dailySpending[date]!;
      trends.add(SpendingTrend(
        date: date,
        dailySpending: dailySpending[date]!,
        cumulativeSpending: cumulativeSpending,
        budgetPercentage: (cumulativeSpending / budget.amount) * 100,
      ));
    }

    return trends;
  }

  // Get budget comparison data
  static Future<BudgetComparison> compareBudgets(List<String> budgetIds) async {
    final comparisons = <BudgetComparisonItem>[];
    
    for (final budgetId in budgetIds) {
      final analytics = await getBudgetAnalytics(budgetId);
      comparisons.add(BudgetComparisonItem(
        budgetId: budgetId,
        budgetName: analytics.budgetName,
        budgetAmount: analytics.budgetAmount,
        spentAmount: analytics.spentAmount,
        spentPercentage: analytics.spentPercentage,
        status: analytics.status,
        efficiency: _calculateBudgetEfficiency(analytics),
      ));
    }

    return BudgetComparison(
      comparisons: comparisons,
      bestPerforming: _findBestPerformingBudget(comparisons),
      worstPerforming: _findWorstPerformingBudget(comparisons),
      averageSpentPercentage: _calculateAverageSpentPercentage(comparisons),
    );
  }

  // Get budget alerts
  static Future<List<BudgetAlert>> getBudgetAlerts() async {
    final budgets = await getActiveBudgets();
    final alerts = <BudgetAlert>[];

    for (final budget in budgets) {
      final analytics = await getBudgetAnalytics(budget.id);
      
      // Check for various alert conditions
      if (analytics.spentPercentage >= 90) {
        alerts.add(BudgetAlert(
          budgetId: budget.id,
          budgetName: budget.name,
          type: BudgetAlertType.overspending,
          severity: AlertSeverity.high,
          message: 'You have spent ${analytics.spentPercentage.toStringAsFixed(1)}% of your budget',
          amount: analytics.spentAmount,
          percentage: analytics.spentPercentage,
        ));
      } else if (analytics.spentPercentage >= 75) {
        alerts.add(BudgetAlert(
          budgetId: budget.id,
          budgetName: budget.name,
          type: BudgetAlertType.warning,
          severity: AlertSeverity.medium,
          message: 'You are approaching your budget limit (${analytics.spentPercentage.toStringAsFixed(1)}%)',
          amount: analytics.spentAmount,
          percentage: analytics.spentPercentage,
        ));
      }

      if (!analytics.isOnTrack && analytics.daysRemaining > 0) {
        alerts.add(BudgetAlert(
          budgetId: budget.id,
          budgetName: budget.name,
          type: BudgetAlertType.projection,
          severity: AlertSeverity.medium,
          message: 'At current spending rate, you may exceed budget by \$${(analytics.projectedSpending - budget.amount).toStringAsFixed(2)}',
          amount: analytics.projectedSpending,
          percentage: (analytics.projectedSpending / budget.amount) * 100,
        ));
      }
    }

    return alerts;
  }

  // Helper methods
  static String _generateId() {
    return 'budget_${DateTime.now().millisecondsSinceEpoch}_${math.Random().nextInt(1000)}';
  }

  static DateTime _calculateEndDate(DateTime startDate, BudgetPeriod period) {
    switch (period) {
      case BudgetPeriod.weekly:
        return startDate.add(const Duration(days: 7));
      case BudgetPeriod.monthly:
        return DateTime(startDate.year, startDate.month + 1, startDate.day);
      case BudgetPeriod.quarterly:
        return DateTime(startDate.year, startDate.month + 3, startDate.day);
      case BudgetPeriod.yearly:
        return DateTime(startDate.year + 1, startDate.month, startDate.day);
    }
  }

  static Future<List<Transaction>> _getBudgetTransactions(Budget budget) async {
    final allTransactions = await MoneyManagerDatabase.getTransactions();
    
    return allTransactions.where((transaction) {
      // Filter by date range
      if (transaction.date.isBefore(budget.startDate) || 
          transaction.date.isAfter(budget.endDate)) {
        return false;
      }
      
      // Filter by category
      if (transaction.categoryId != budget.categoryId) {
        return false;
      }
      
      // Filter by accounts if specified
      // Note: accountIds property not available in current Budget model
      
      // Only include expense transactions
      return transaction.type == TransactionType.expense;
    }).toList();
  }

  static double _calculateSpentAmount(List<Transaction> transactions) {
    return transactions.fold<double>(0.0, (sum, transaction) => sum + transaction.amount);
  }

  static double _calculateDailyAverage(List<Transaction> transactions, Budget budget) {
    if (transactions.isEmpty) return 0.0;
    
    final daysPassed = DateTime.now().difference(budget.startDate).inDays + 1;
    final totalSpent = _calculateSpentAmount(transactions);
    
    return totalSpent / daysPassed;
  }

  static double _calculateProjectedSpending(Budget budget, double dailyAverage) {
    final totalDays = budget.endDate.difference(budget.startDate).inDays + 1;
    return dailyAverage * totalDays;
  }

  static BudgetStatus _determineBudgetStatus(double spent, double budgetAmount, double percentage) {
    if (percentage >= 100) {
      return BudgetStatus.exceeded;
    } else if (percentage >= 90) {
      return BudgetStatus.critical;
    } else if (percentage >= 75) {
      return BudgetStatus.warning;
    } else if (percentage >= 50) {
      return BudgetStatus.onTrack;
    } else {
      return BudgetStatus.underSpent;
    }
  }

  static List<String> _generateRecommendations(Budget budget, double spent, double projectedSpending) {
    final recommendations = <String>[];
    final percentage = (spent / budget.amount) * 100;
    
    if (percentage >= 90) {
      recommendations.add('Consider reducing spending in this category for the remainder of the period');
      recommendations.add('Review recent transactions to identify unnecessary expenses');
    } else if (projectedSpending > budget.amount) {
      recommendations.add('Your current spending pace may exceed the budget');
      recommendations.add('Try to reduce daily spending by \$${((projectedSpending - budget.amount) / budget.endDate.difference(DateTime.now()).inDays).toStringAsFixed(2)} per day');
    } else if (percentage < 25) {
      recommendations.add('You\'re well under budget - consider if the budget amount is realistic');
      recommendations.add('You might be able to allocate some funds to other categories or savings');
    }
    
    return recommendations;
  }

  static double _calculateBudgetEfficiency(BudgetAnalytics analytics) {
    // Efficiency score based on staying within budget and consistent spending
    if (analytics.spentPercentage > 100) {
      return 0.0; // Poor efficiency if over budget
    }
    
    final targetPercentage = 85.0; // Ideal spending percentage
    final deviation = (analytics.spentPercentage - targetPercentage).abs();
    
    return math.max(0.0, 100.0 - deviation);
  }

  static String? _findBestPerformingBudget(List<BudgetComparisonItem> comparisons) {
    if (comparisons.isEmpty) return null;
    
    var best = comparisons.first;
    for (final comparison in comparisons) {
      if (comparison.efficiency > best.efficiency) {
        best = comparison;
      }
    }
    
    return best.budgetId;
  }

  static String? _findWorstPerformingBudget(List<BudgetComparisonItem> comparisons) {
    if (comparisons.isEmpty) return null;
    
    var worst = comparisons.first;
    for (final comparison in comparisons) {
      if (comparison.efficiency < worst.efficiency) {
        worst = comparison;
      }
    }
    
    return worst.budgetId;
  }

  static double _calculateAverageSpentPercentage(List<BudgetComparisonItem> comparisons) {
    if (comparisons.isEmpty) return 0.0;
    
    final total = comparisons.fold<double>(0.0, (sum, item) => sum + item.spentPercentage);
    return total / comparisons.length;
  }
}

// Data classes for budget analytics
class BudgetAnalytics {
  final String budgetId;
  final String budgetName;
  final double budgetAmount;
  final double spentAmount;
  final double remainingAmount;
  final double spentPercentage;
  final double dailyAverage;
  final double projectedSpending;
  final BudgetStatus status;
  final List<String> recommendations;
  final int transactionCount;
  final int daysRemaining;
  final bool isOnTrack;

  const BudgetAnalytics({
    required this.budgetId,
    required this.budgetName,
    required this.budgetAmount,
    required this.spentAmount,
    required this.remainingAmount,
    required this.spentPercentage,
    required this.dailyAverage,
    required this.projectedSpending,
    required this.status,
    required this.recommendations,
    required this.transactionCount,
    required this.daysRemaining,
    required this.isOnTrack,
  });
}

class SpendingTrend {
  final DateTime date;
  final double dailySpending;
  final double cumulativeSpending;
  final double budgetPercentage;

  const SpendingTrend({
    required this.date,
    required this.dailySpending,
    required this.cumulativeSpending,
    required this.budgetPercentage,
  });
}

class BudgetComparison {
  final List<BudgetComparisonItem> comparisons;
  final String? bestPerforming;
  final String? worstPerforming;
  final double averageSpentPercentage;

  const BudgetComparison({
    required this.comparisons,
    required this.bestPerforming,
    required this.worstPerforming,
    required this.averageSpentPercentage,
  });
}

class BudgetComparisonItem {
  final String budgetId;
  final String budgetName;
  final double budgetAmount;
  final double spentAmount;
  final double spentPercentage;
  final BudgetStatus status;
  final double efficiency;

  const BudgetComparisonItem({
    required this.budgetId,
    required this.budgetName,
    required this.budgetAmount,
    required this.spentAmount,
    required this.spentPercentage,
    required this.status,
    required this.efficiency,
  });
}

class BudgetAlert {
  final String budgetId;
  final String budgetName;
  final BudgetAlertType type;
  final AlertSeverity severity;
  final String message;
  final double amount;
  final double percentage;

  const BudgetAlert({
    required this.budgetId,
    required this.budgetName,
    required this.type,
    required this.severity,
    required this.message,
    required this.amount,
    required this.percentage,
  });
}

enum BudgetStatus { underSpent, onTrack, warning, critical, exceeded }
enum BudgetAlertType { warning, overspending, projection, reminder }
enum AlertSeverity { low, medium, high }
