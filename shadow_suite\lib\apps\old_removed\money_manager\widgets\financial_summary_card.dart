import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class FinancialSummaryCard extends ConsumerWidget {
  final String title;
  final FutureProvider<double> provider;
  final IconData icon;
  final Color color;
  final String format;

  const FinancialSummaryCard({
    super.key,
    required this.title,
    required this.provider,
    required this.icon,
    required this.color,
    this.format = 'currency',
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final valueAsync = ref.watch(provider);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
              const Spacer(),
              Icon(
                Icons.more_vert,
                color: Colors.grey.shade400,
                size: 16,
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF7F8C8D),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          valueAsync.when(
            data: (value) => Text(
              _formatValue(value),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2C3E50),
              ),
            ),
            loading: () => const Text(
              'Loading...',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF95A5A6),
              ),
            ),
            error: (error, stack) => const Text(
              'Error',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFFE74C3C),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatValue(double value) {
    switch (format) {
      case 'currency':
        return '\$${value.toStringAsFixed(2)}';
      case 'percentage':
        return '${value.toStringAsFixed(1)}%';
      case 'number':
        return value.toStringAsFixed(0);
      default:
        return value.toString();
    }
  }
}
