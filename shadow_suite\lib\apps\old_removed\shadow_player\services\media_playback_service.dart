import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import '../models/shadow_player_models.dart';

/// Comprehensive media playback service with advanced features
class MediaPlaybackService {
  static final MediaPlaybackService _instance =
      MediaPlaybackService._internal();
  factory MediaPlaybackService() => _instance;
  MediaPlaybackService._internal();

  // State management
  PlaybackState _playbackState = PlaybackState.stopped;
  MediaItem? _currentMedia;
  Playlist? _currentPlaylist;
  int _currentIndex = 0;
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;
  double _volume = 1.0;
  double _playbackSpeed = 1.0;
  RepeatMode _repeatMode = RepeatMode.none;
  bool _shuffleEnabled = false;

  // Audio processing
  // EqualizerSettings _equalizerSettings = EqualizerSettings.flat(); // Unused
  // AudioEffects _audioEffects = const AudioEffects(); // Unused

  // Event streams
  final StreamController<PlaybackState> _stateController =
      StreamController<PlaybackState>.broadcast();
  final StreamController<MediaItem?> _mediaController =
      StreamController<MediaItem?>.broadcast();
  final StreamController<Duration> _positionController =
      StreamController<Duration>.broadcast();
  final StreamController<PlaybackEvent> _eventController =
      StreamController<PlaybackEvent>.broadcast();

  // Timers
  Timer? _positionTimer;
  Timer? _sleepTimer;

  /// Stream of playback state changes
  Stream<PlaybackState> get stateStream => _stateController.stream;

  /// Stream of current media changes
  Stream<MediaItem?> get mediaStream => _mediaController.stream;

  /// Stream of position updates
  Stream<Duration> get positionStream => _positionController.stream;

  /// Stream of playback events
  Stream<PlaybackEvent> get eventStream => _eventController.stream;

  /// Current playback state
  PlaybackState get playbackState => _playbackState;

  /// Current media item
  MediaItem? get currentMedia => _currentMedia;

  /// Current playlist
  Playlist? get currentPlaylist => _currentPlaylist;

  /// Current position
  Duration get position => _position;

  /// Current duration
  Duration get duration => _duration;

  /// Current volume (0.0 to 1.0)
  double get volume => _volume;

  /// Current playback speed
  double get playbackSpeed => _playbackSpeed;

  /// Current repeat mode
  RepeatMode get repeatMode => _repeatMode;

  /// Whether shuffle is enabled
  bool get shuffleEnabled => _shuffleEnabled;

  /// Initialize the media playback service
  Future<void> initialize() async {
    try {
      // Initialize audio engine
      await _initializeAudioEngine();

      // Setup background playback
      await _setupBackgroundPlayback();

      // Start position timer
      _startPositionTimer();

      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.initialized,
          message: 'Media playback service initialized',
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.error,
          message: 'Failed to initialize: $e',
          timestamp: DateTime.now(),
        ),
      );
      rethrow;
    }
  }

  /// Load and play a media item
  Future<void> playMedia(MediaItem media) async {
    try {
      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.loading,
          message: 'Loading ${media.title}',
          timestamp: DateTime.now(),
        ),
      );

      // Stop current playback
      if (_playbackState != PlaybackState.stopped) {
        await stop();
      }

      // Validate media file
      final file = File(media.filePath);
      if (!await file.exists()) {
        throw FileSystemException('Media file not found', media.filePath);
      }

      // Load media
      _currentMedia = media;
      _duration = media.duration;
      _position = Duration.zero;

      // Update state
      _updatePlaybackState(PlaybackState.loading);

      // Start playback
      await _startPlayback();

      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.mediaChanged,
          message: 'Now playing: ${media.title}',
          timestamp: DateTime.now(),
        ),
      );

      _mediaController.add(_currentMedia);
    } catch (e) {
      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.error,
          message: 'Failed to play media: $e',
          timestamp: DateTime.now(),
        ),
      );
      rethrow;
    }
  }

  /// Play a playlist
  Future<void> playPlaylist(Playlist playlist, {int startIndex = 0}) async {
    if (playlist.items.isEmpty) {
      throw ArgumentError('Playlist is empty');
    }

    _currentPlaylist = playlist;
    _currentIndex = startIndex.clamp(0, playlist.items.length - 1);

    await playMedia(playlist.items[_currentIndex]);

    _emitEvent(
      PlaybackEvent(
        type: PlaybackEventType.playlistChanged,
        message: 'Playing playlist: ${playlist.name}',
        timestamp: DateTime.now(),
      ),
    );
  }

  /// Resume playback
  Future<void> play() async {
    if (_currentMedia == null) return;

    try {
      await _resumePlayback();
      _updatePlaybackState(PlaybackState.playing);

      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.resumed,
          message: 'Playback resumed',
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.error,
          message: 'Failed to resume: $e',
          timestamp: DateTime.now(),
        ),
      );
    }
  }

  /// Pause playback
  Future<void> pause() async {
    if (_playbackState != PlaybackState.playing) return;

    try {
      await _pausePlayback();
      _updatePlaybackState(PlaybackState.paused);

      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.paused,
          message: 'Playback paused',
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.error,
          message: 'Failed to pause: $e',
          timestamp: DateTime.now(),
        ),
      );
    }
  }

  /// Stop playback
  Future<void> stop() async {
    try {
      await _stopPlayback();
      _updatePlaybackState(PlaybackState.stopped);
      _position = Duration.zero;
      _positionController.add(_position);

      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.stopped,
          message: 'Playback stopped',
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.error,
          message: 'Failed to stop: $e',
          timestamp: DateTime.now(),
        ),
      );
    }
  }

  /// Skip to next track
  Future<void> skipToNext() async {
    if (_currentPlaylist == null || _currentPlaylist!.items.isEmpty) return;

    int nextIndex;
    if (_shuffleEnabled) {
      nextIndex = _getRandomIndex();
    } else {
      nextIndex = _currentIndex + 1;
      if (nextIndex >= _currentPlaylist!.items.length) {
        if (_repeatMode == RepeatMode.all) {
          nextIndex = 0;
        } else {
          return; // End of playlist
        }
      }
    }

    _currentIndex = nextIndex;
    await playMedia(_currentPlaylist!.items[_currentIndex]);

    _emitEvent(
      PlaybackEvent(
        type: PlaybackEventType.skippedToNext,
        message: 'Skipped to next track',
        timestamp: DateTime.now(),
      ),
    );
  }

  /// Skip to previous track
  Future<void> skipToPrevious() async {
    if (_currentPlaylist == null || _currentPlaylist!.items.isEmpty) return;

    // If more than 3 seconds into track, restart current track
    if (_position.inSeconds > 3) {
      await seekTo(Duration.zero);
      return;
    }

    int previousIndex;
    if (_shuffleEnabled) {
      previousIndex = _getRandomIndex();
    } else {
      previousIndex = _currentIndex - 1;
      if (previousIndex < 0) {
        if (_repeatMode == RepeatMode.all) {
          previousIndex = _currentPlaylist!.items.length - 1;
        } else {
          return; // Beginning of playlist
        }
      }
    }

    _currentIndex = previousIndex;
    await playMedia(_currentPlaylist!.items[_currentIndex]);

    _emitEvent(
      PlaybackEvent(
        type: PlaybackEventType.skippedToPrevious,
        message: 'Skipped to previous track',
        timestamp: DateTime.now(),
      ),
    );
  }

  /// Seek to specific position
  Future<void> seekTo(Duration position) async {
    if (_currentMedia == null) return;

    try {
      // Clamp position manually since Duration doesn't have clamp method
      final clampedPosition = Duration(
        milliseconds: position.inMilliseconds.clamp(
          0,
          _duration.inMilliseconds,
        ),
      );
      await _seekToPosition(clampedPosition);
      _position = clampedPosition;
      _positionController.add(_position);

      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.seeked,
          message: 'Seeked to ${_formatDuration(clampedPosition)}',
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.error,
          message: 'Failed to seek: $e',
          timestamp: DateTime.now(),
        ),
      );
    }
  }

  /// Set volume (0.0 to 1.0)
  Future<void> setVolume(double volume) async {
    final clampedVolume = volume.clamp(0.0, 1.0);

    try {
      await _setAudioVolume(clampedVolume);
      _volume = clampedVolume;

      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.volumeChanged,
          message: 'Volume set to ${(clampedVolume * 100).round()}%',
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.error,
          message: 'Failed to set volume: $e',
          timestamp: DateTime.now(),
        ),
      );
    }
  }

  /// Set playback speed (0.5 to 2.0)
  Future<void> setPlaybackSpeed(double speed) async {
    final clampedSpeed = speed.clamp(0.5, 2.0);

    try {
      await _setAudioSpeed(clampedSpeed);
      _playbackSpeed = clampedSpeed;

      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.speedChanged,
          message: 'Playback speed set to ${clampedSpeed}x',
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.error,
          message: 'Failed to set speed: $e',
          timestamp: DateTime.now(),
        ),
      );
    }
  }

  /// Set repeat mode
  void setRepeatMode(RepeatMode mode) {
    _repeatMode = mode;

    _emitEvent(
      PlaybackEvent(
        type: PlaybackEventType.repeatModeChanged,
        message: 'Repeat mode: ${mode.name}',
        timestamp: DateTime.now(),
      ),
    );
  }

  /// Toggle shuffle
  void toggleShuffle() {
    _shuffleEnabled = !_shuffleEnabled;

    _emitEvent(
      PlaybackEvent(
        type: PlaybackEventType.shuffleToggled,
        message: 'Shuffle ${_shuffleEnabled ? 'enabled' : 'disabled'}',
        timestamp: DateTime.now(),
      ),
    );
  }

  /// Apply equalizer settings
  Future<void> applyEqualizer(EqualizerSettings settings) async {
    try {
      await _applyEqualizerSettings(settings);
      // _equalizerSettings = settings;

      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.equalizerChanged,
          message: 'Equalizer preset: ${settings.presetName}',
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.error,
          message: 'Failed to apply equalizer: $e',
          timestamp: DateTime.now(),
        ),
      );
    }
  }

  /// Apply audio effects
  Future<void> applyAudioEffects(AudioEffects effects) async {
    try {
      await _applyAudioEffects(effects);
      // _audioEffects = effects;

      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.effectsChanged,
          message: 'Audio effects applied',
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.error,
          message: 'Failed to apply effects: $e',
          timestamp: DateTime.now(),
        ),
      );
    }
  }

  /// Set sleep timer
  void setSleepTimer(Duration duration) {
    _sleepTimer?.cancel();

    _sleepTimer = Timer(duration, () async {
      await stop();
      _emitEvent(
        PlaybackEvent(
          type: PlaybackEventType.sleepTimerExpired,
          message: 'Sleep timer expired - playback stopped',
          timestamp: DateTime.now(),
        ),
      );
    });

    _emitEvent(
      PlaybackEvent(
        type: PlaybackEventType.sleepTimerSet,
        message: 'Sleep timer set for ${_formatDuration(duration)}',
        timestamp: DateTime.now(),
      ),
    );
  }

  /// Cancel sleep timer
  void cancelSleepTimer() {
    _sleepTimer?.cancel();
    _sleepTimer = null;

    _emitEvent(
      PlaybackEvent(
        type: PlaybackEventType.sleepTimerCancelled,
        message: 'Sleep timer cancelled',
        timestamp: DateTime.now(),
      ),
    );
  }

  /// Get playback statistics
  PlaybackStatistics getStatistics() {
    return PlaybackStatistics(
      totalPlayTime: Duration.zero, // Would track in production
      tracksPlayed: 0, // Would track in production
      favoriteGenre: '', // Would analyze in production
      averageSessionLength: Duration.zero, // Would track in production
      mostPlayedArtist: '', // Would analyze in production
      playbackQuality: PlaybackQuality.high,
    );
  }

  // Private methods
  Future<void> _initializeAudioEngine() async {
    // Initialize platform-specific audio engine
    // This would use platform channels in a real implementation
  }

  Future<void> _setupBackgroundPlayback() async {
    // Setup background audio session
    // This would configure audio session for background playback
  }

  Future<void> _startPlayback() async {
    // Start actual audio playback
    // This would use platform-specific audio APIs
    _updatePlaybackState(PlaybackState.playing);
  }

  Future<void> _resumePlayback() async {
    // Resume audio playback
    // This would use platform-specific audio APIs
  }

  Future<void> _pausePlayback() async {
    // Pause audio playback
    // This would use platform-specific audio APIs
  }

  Future<void> _stopPlayback() async {
    // Stop audio playback
    // This would use platform-specific audio APIs
  }

  Future<void> _seekToPosition(Duration position) async {
    // Seek to specific position
    // This would use platform-specific audio APIs
  }

  Future<void> _setAudioVolume(double volume) async {
    // Set audio volume
    // This would use platform-specific audio APIs
  }

  Future<void> _setAudioSpeed(double speed) async {
    // Set playback speed
    // This would use platform-specific audio APIs
  }

  Future<void> _applyEqualizerSettings(EqualizerSettings settings) async {
    // Apply equalizer settings
    // This would use platform-specific audio processing
  }

  Future<void> _applyAudioEffects(AudioEffects effects) async {
    // Apply audio effects
    // This would use platform-specific audio processing
  }

  void _startPositionTimer() {
    _positionTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (_playbackState == PlaybackState.playing) {
        _position = _position + const Duration(milliseconds: 100);

        // Check if track ended
        if (_position >= _duration) {
          _onTrackEnded();
        } else {
          _positionController.add(_position);
        }
      }
    });
  }

  void _onTrackEnded() async {
    _emitEvent(
      PlaybackEvent(
        type: PlaybackEventType.trackEnded,
        message: 'Track ended',
        timestamp: DateTime.now(),
      ),
    );

    if (_repeatMode == RepeatMode.one) {
      await seekTo(Duration.zero);
      await play();
    } else if (_currentPlaylist != null && _currentPlaylist!.items.length > 1) {
      await skipToNext();
    } else {
      await stop();
    }
  }

  int _getRandomIndex() {
    if (_currentPlaylist == null || _currentPlaylist!.items.isEmpty) return 0;

    final random = math.Random();
    int randomIndex;
    do {
      randomIndex = random.nextInt(_currentPlaylist!.items.length);
    } while (randomIndex == _currentIndex &&
        _currentPlaylist!.items.length > 1);

    return randomIndex;
  }

  void _updatePlaybackState(PlaybackState state) {
    _playbackState = state;
    _stateController.add(state);
  }

  void _emitEvent(PlaybackEvent event) {
    _eventController.add(event);
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// Dispose resources
  void dispose() {
    _positionTimer?.cancel();
    _sleepTimer?.cancel();
    _stateController.close();
    _mediaController.close();
    _positionController.close();
    _eventController.close();
  }
}
