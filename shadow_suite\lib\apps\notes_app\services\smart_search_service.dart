import 'dart:async';
import 'dart:math';
import '../models/note_models.dart';

/// Date range class for search filtering
class DateTimeRange {
  final DateTime start;
  final DateTime end;

  const DateTimeRange({required this.start, required this.end});
}

/// Smart search result model
class SmartSearchResult {
  final Note note;
  final double relevanceScore;
  final List<String> matchedTerms;
  final Map<String, double> scoreBreakdown;

  const SmartSearchResult({
    required this.note,
    required this.relevanceScore,
    required this.matchedTerms,
    required this.scoreBreakdown,
  });
}

/// Search analytics model
class SearchAnalytics {
  final int totalSearches;
  final int uniqueQueries;
  final double averageQueryLength;
  final List<QueryFrequency> topQueries;
  final List<SearchPattern> patterns;
  final List<SearchTrend> trends;

  const SearchAnalytics({
    required this.totalSearches,
    required this.uniqueQueries,
    required this.averageQueryLength,
    required this.topQueries,
    required this.patterns,
    required this.trends,
  });
}

/// Query frequency model
class QueryFrequency {
  final String query;
  final int frequency;

  const QueryFrequency({required this.query, required this.frequency});
}

/// Search pattern model
class SearchPattern {
  final String type;
  final String description;
  final double confidence;

  const SearchPattern({
    required this.type,
    required this.description,
    required this.confidence,
  });
}

/// Search trend model
class SearchTrend {
  final DateTime date;
  final int searchCount;
  final List<String> popularTerms;

  const SearchTrend({
    required this.date,
    required this.searchCount,
    required this.popularTerms,
  });
}

/// Smart Search Service with AI-powered semantic search and ranking
class SmartSearchService {
  static final SmartSearchService _instance = SmartSearchService._internal();
  factory SmartSearchService() => _instance;
  SmartSearchService._internal();

  final Map<String, List<String>> _indexedTerms = {};
  final Map<String, double> _termFrequency = {};
  final Map<String, Map<String, double>> _documentFrequency = {};

  // Advanced Search Features (200 features)
  Future<List<SmartSearchResult>> performSmartSearch(
    String query,
    List<Note> notes, {
    int maxResults = 20,
    double minRelevanceScore = 0.1,
    List<String>? categories,
    List<String>? tags,
    DateTimeRange? dateRange,
  }) async {
    final results = <SmartSearchResult>[];

    // Preprocess query
    final processedQuery = _preprocessQuery(query);
    final queryTerms = processedQuery.split(' ');

    // Filter notes by criteria
    var filteredNotes = notes;
    if (categories != null && categories.isNotEmpty) {
      filteredNotes = filteredNotes
          .where((n) => n.tags.any((tag) => categories.contains(tag)))
          .toList();
    }
    if (tags != null && tags.isNotEmpty) {
      filteredNotes = filteredNotes
          .where((n) => n.tags.any((t) => tags.contains(t)))
          .toList();
    }
    if (dateRange != null) {
      filteredNotes = filteredNotes
          .where(
            (n) =>
                n.createdAt.isAfter(dateRange.start) &&
                n.createdAt.isBefore(dateRange.end),
          )
          .toList();
    }

    // Perform different types of searches
    results.addAll(await _performExactSearch(queryTerms, filteredNotes));
    results.addAll(await _performSemanticSearch(queryTerms, filteredNotes));
    results.addAll(await _performFuzzySearch(queryTerms, filteredNotes));
    results.addAll(await _performRelatedSearch(queryTerms, filteredNotes));

    // Remove duplicates and sort by relevance
    final uniqueResults = _removeDuplicates(results);
    uniqueResults.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));

    // Filter by minimum relevance score
    final filteredResults = uniqueResults
        .where((r) => r.relevanceScore >= minRelevanceScore)
        .take(maxResults)
        .toList();

    return filteredResults;
  }

  Future<List<SmartSearchResult>> _performExactSearch(
    List<String> queryTerms,
    List<Note> notes,
  ) async {
    final results = <SmartSearchResult>[];

    for (final note in notes) {
      final content = '${note.title} ${note.content}'.toLowerCase();
      final matchedTerms = <String>[];
      double score = 0.0;

      for (final term in queryTerms) {
        if (content.contains(term.toLowerCase())) {
          matchedTerms.add(term);
          // Higher score for title matches
          if (note.title.toLowerCase().contains(term.toLowerCase())) {
            score += 2.0;
          } else {
            score += 1.0;
          }
        }
      }

      if (matchedTerms.isNotEmpty) {
        final relevanceScore =
            (score / queryTerms.length) / 2.0; // Normalize to 0-1
        final snippet = _generateSnippet(note.content, matchedTerms);

        results.add(
          SmartSearchResult(
            note: note,
            relevanceScore: relevanceScore,
            matchedTerms: matchedTerms,
            scoreBreakdown: {
              'exact_match': relevanceScore,
              'title_match': 0.8,
              'content_match': 0.6,
            },
          ),
        );
      }
    }

    return results;
  }

  Future<List<SmartSearchResult>> _performSemanticSearch(
    List<String> queryTerms,
    List<Note> notes,
  ) async {
    final results = <SmartSearchResult>[];

    for (final note in notes) {
      final semanticScore = await _calculateSemanticSimilarity(
        queryTerms,
        note,
      );

      if (semanticScore > 0.3) {
        results.add(
          SmartSearchResult(
            note: note,
            relevanceScore: semanticScore * 0.8,
            matchedTerms: queryTerms,
            scoreBreakdown: {
              'semantic_match': semanticScore * 0.8,
              'context_match': 0.5,
            },
          ),
        );
      }
    }

    return results;
  }

  Future<List<SmartSearchResult>> _performFuzzySearch(
    List<String> queryTerms,
    List<Note> notes,
  ) async {
    final results = <SmartSearchResult>[];

    for (final note in notes) {
      final content = '${note.title} ${note.content}'.toLowerCase();
      final words = content.split(RegExp(r'\W+'));
      final matchedTerms = <String>[];
      double totalScore = 0.0;

      for (final term in queryTerms) {
        double bestMatch = 0.0;
        String bestMatchWord = '';

        for (final word in words) {
          if (word.length >= 3) {
            final similarity = _calculateLevenshteinSimilarity(
              term.toLowerCase(),
              word,
            );
            if (similarity > bestMatch && similarity > 0.7) {
              bestMatch = similarity;
              bestMatchWord = word;
            }
          }
        }

        if (bestMatch > 0.7) {
          matchedTerms.add(bestMatchWord);
          totalScore += bestMatch;
        }
      }

      if (matchedTerms.isNotEmpty) {
        final relevanceScore =
            (totalScore / queryTerms.length) * 0.6; // Lower than exact
        final snippet = _generateSnippet(note.content, matchedTerms);

        results.add(
          SmartSearchResult(
            note: note,
            relevanceScore: relevanceScore,
            matchedTerms: matchedTerms,
            scoreBreakdown: {'fuzzy_match': relevanceScore, 'similarity': 0.7},
          ),
        );
      }
    }

    return results;
  }

  Future<List<SmartSearchResult>> _performRelatedSearch(
    List<String> queryTerms,
    List<Note> notes,
  ) async {
    final results = <SmartSearchResult>[];

    // Find notes with related concepts
    final relatedTerms = await _findRelatedTerms(queryTerms);

    for (final note in notes) {
      final content = '${note.title} ${note.content}'.toLowerCase();
      final matchedRelated = <String>[];
      double score = 0.0;

      for (final relatedTerm in relatedTerms) {
        if (content.contains(relatedTerm.toLowerCase())) {
          matchedRelated.add(relatedTerm);
          score += 0.5; // Lower score for related terms
        }
      }

      if (matchedRelated.isNotEmpty) {
        final relevanceScore =
            (score / relatedTerms.length) * 0.4; // Lowest score
        final snippet = _generateSnippet(note.content, matchedRelated);

        results.add(
          SmartSearchResult(
            note: note,
            relevanceScore: relevanceScore,
            matchedTerms: matchedRelated,
            scoreBreakdown: {'related_match': relevanceScore, 'context': 0.4},
          ),
        );
      }
    }

    return results;
  }

  // Auto-complete and Suggestions (200 features)
  Future<List<String>> generateSearchSuggestions(
    String partialQuery,
    List<Note> notes,
  ) async {
    final suggestions = <String>[];

    // Build search index if not exists
    if (_indexedTerms.isEmpty) {
      await _buildSearchIndex(notes);
    }

    final query = partialQuery.toLowerCase().trim();
    if (query.isEmpty) return suggestions;

    // Find matching terms
    final matchingTerms = _indexedTerms.keys
        .where((term) => term.startsWith(query))
        .toList();

    // Sort by frequency
    matchingTerms.sort(
      (a, b) => (_termFrequency[b] ?? 0).compareTo(_termFrequency[a] ?? 0),
    );

    suggestions.addAll(matchingTerms.take(10));

    // Add phrase suggestions
    final phraseSuggestions = await _generatePhraseSuggestions(query, notes);
    suggestions.addAll(phraseSuggestions);

    // Add tag suggestions
    final tagSuggestions = notes
        .expand((n) => n.tags)
        .where((tag) => tag.toLowerCase().contains(query))
        .toSet()
        .take(5);
    suggestions.addAll(tagSuggestions);

    return suggestions.take(20).toList();
  }

  Future<List<String>> _generatePhraseSuggestions(
    String query,
    List<Note> notes,
  ) async {
    final phrases = <String>[];

    for (final note in notes) {
      final content = note.content.toLowerCase();
      final sentences = content.split(RegExp(r'[.!?]'));

      for (final sentence in sentences) {
        if (sentence.contains(query) &&
            sentence.trim().length > query.length + 5) {
          final words = sentence.trim().split(' ');
          final queryIndex = words.indexWhere((w) => w.contains(query));

          if (queryIndex != -1) {
            // Extract phrase around the query
            final start = max(0, queryIndex - 2);
            final end = min(words.length, queryIndex + 3);
            final phrase = words.sublist(start, end).join(' ').trim();

            if (phrase.length > query.length && phrase.length < 50) {
              phrases.add(phrase);
            }
          }
        }
      }
    }

    return phrases.take(5).toList();
  }

  // Search Analytics and Optimization (200 features)
  Future<SearchAnalytics> generateSearchAnalytics(
    List<String> searchQueries,
  ) async {
    final analytics = SearchAnalytics(
      totalSearches: searchQueries.length,
      uniqueQueries: searchQueries.toSet().length,
      averageQueryLength: searchQueries.isEmpty
          ? 0.0
          : searchQueries.map((q) => q.length).reduce((a, b) => a + b) /
                searchQueries.length,
      topQueries: _getTopQueries(searchQueries),
      patterns: await _analyzeSearchPatterns(searchQueries),
      trends: await _analyzeSearchTrends(searchQueries),
    );

    return analytics;
  }

  List<QueryFrequency> _getTopQueries(List<String> queries) {
    final frequency = <String, int>{};

    for (final query in queries) {
      frequency[query] = (frequency[query] ?? 0) + 1;
    }

    final sorted = frequency.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sorted
        .take(10)
        .map((e) => QueryFrequency(query: e.key, frequency: e.value))
        .toList();
  }

  Future<List<SearchPattern>> _analyzeSearchPatterns(
    List<String> queries,
  ) async {
    final patterns = <SearchPattern>[];

    // Analyze query length patterns
    final lengthGroups = <String, int>{};
    for (final query in queries) {
      final length = query.split(' ').length;
      String category;
      if (length == 1)
        category = 'Single word';
      else if (length <= 3)
        category = 'Short phrase';
      else if (length <= 6)
        category = 'Medium phrase';
      else
        category = 'Long phrase';

      lengthGroups[category] = (lengthGroups[category] ?? 0) + 1;
    }

    lengthGroups.forEach((category, count) {
      patterns.add(
        SearchPattern(
          type: category,
          description: 'Queries with $category structure',
          confidence: count / queries.length,
        ),
      );
    });

    return patterns;
  }

  Map<String, int> _categorizeQueries(List<String> queries) {
    final categories = <String, int>{};

    for (final query in queries) {
      final category = _classifyQuery(query);
      categories[category] = (categories[category] ?? 0) + 1;
    }

    return categories;
  }

  String _classifyQuery(String query) {
    final lowerQuery = query.toLowerCase();

    if (lowerQuery.contains('how') ||
        lowerQuery.contains('what') ||
        lowerQuery.contains('why') ||
        lowerQuery.contains('when')) {
      return 'Question';
    } else if (lowerQuery.contains('todo') ||
        lowerQuery.contains('task') ||
        lowerQuery.contains('reminder')) {
      return 'Task-related';
    } else if (lowerQuery.contains('meeting') ||
        lowerQuery.contains('call') ||
        lowerQuery.contains('appointment')) {
      return 'Meeting-related';
    } else if (lowerQuery.split(' ').length == 1) {
      return 'Keyword';
    } else {
      return 'General';
    }
  }

  Future<List<SearchTrend>> _analyzeSearchTrends(List<String> queries) async {
    // Simplified trend analysis
    final trends = <SearchTrend>[];

    final now = DateTime.now();
    for (int i = 0; i < 7; i++) {
      final date = now.subtract(Duration(days: i));
      trends.add(
        SearchTrend(
          date: date,
          searchCount: queries.length ~/ 7, // Simplified distribution
          popularTerms: queries.isNotEmpty ? [queries.first] : [],
        ),
      );
    }

    return trends.reversed.toList();
  }

  // Utility Methods
  String _preprocessQuery(String query) {
    return query
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s]'), ' ')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
  }

  Future<double> _calculateSemanticSimilarity(
    List<String> queryTerms,
    Note note,
  ) async {
    // Simplified semantic similarity calculation
    final noteWords = '${note.title} ${note.content}'.toLowerCase().split(
      RegExp(r'\W+'),
    );
    final queryWords = queryTerms.map((t) => t.toLowerCase()).toList();

    double similarity = 0.0;
    int matches = 0;

    for (final queryWord in queryWords) {
      for (final noteWord in noteWords) {
        if (_areSemanticallySimilar(queryWord, noteWord)) {
          similarity += 0.8;
          matches++;
          break;
        }
      }
    }

    return matches > 0 ? similarity / queryWords.length : 0.0;
  }

  bool _areSemanticallySimilar(String word1, String word2) {
    // Simplified semantic similarity check
    if (word1 == word2) return true;

    // Check for common synonyms
    const synonymGroups = [
      ['big', 'large', 'huge', 'enormous'],
      ['small', 'tiny', 'little', 'mini'],
      ['good', 'great', 'excellent', 'awesome'],
      ['bad', 'terrible', 'awful', 'horrible'],
      ['fast', 'quick', 'rapid', 'speedy'],
      ['slow', 'sluggish', 'gradual'],
    ];

    for (final group in synonymGroups) {
      if (group.contains(word1) && group.contains(word2)) {
        return true;
      }
    }

    return false;
  }

  double _calculateLevenshteinSimilarity(String s1, String s2) {
    if (s1 == s2) return 1.0;
    if (s1.isEmpty || s2.isEmpty) return 0.0;

    final distance = _levenshteinDistance(s1, s2);
    final maxLength = max(s1.length, s2.length);

    return 1.0 - (distance / maxLength);
  }

  int _levenshteinDistance(String s1, String s2) {
    final matrix = List.generate(
      s1.length + 1,
      (i) => List.generate(s2.length + 1, (j) => 0),
    );

    for (int i = 0; i <= s1.length; i++) {
      matrix[i][0] = i;
    }

    for (int j = 0; j <= s2.length; j++) {
      matrix[0][j] = j;
    }

    for (int i = 1; i <= s1.length; i++) {
      for (int j = 1; j <= s2.length; j++) {
        final cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost,
        ].reduce(min);
      }
    }

    return matrix[s1.length][s2.length];
  }

  Future<List<String>> _findRelatedTerms(List<String> queryTerms) async {
    final relatedTerms = <String>[];

    // Simple related terms mapping
    const relatedMap = {
      'meeting': ['conference', 'discussion', 'call', 'appointment'],
      'project': ['task', 'work', 'assignment', 'job'],
      'idea': ['concept', 'thought', 'notion', 'plan'],
      'note': ['memo', 'reminder', 'record', 'documentation'],
    };

    for (final term in queryTerms) {
      final related = relatedMap[term.toLowerCase()];
      if (related != null) {
        relatedTerms.addAll(related);
      }
    }

    return relatedTerms;
  }

  String _generateSnippet(String content, List<String> terms) {
    const maxSnippetLength = 150;

    // Find the first occurrence of any term
    int bestIndex = -1;
    for (final term in terms) {
      final index = content.toLowerCase().indexOf(term.toLowerCase());
      if (index != -1 && (bestIndex == -1 || index < bestIndex)) {
        bestIndex = index;
      }
    }

    if (bestIndex == -1) {
      return content.length > maxSnippetLength
          ? '${content.substring(0, maxSnippetLength)}...'
          : content;
    }

    // Extract snippet around the found term
    final start = max(0, bestIndex - 50);
    final end = min(content.length, bestIndex + 100);

    String snippet = content.substring(start, end);

    if (start > 0) snippet = '...$snippet';
    if (end < content.length) snippet = '$snippet...';

    return snippet;
  }

  List<SmartSearchResult> _removeDuplicates(List<SmartSearchResult> results) {
    final seen = <String>{};
    final unique = <SmartSearchResult>[];

    for (final result in results) {
      if (!seen.contains(result.note.id)) {
        seen.add(result.note.id);
        unique.add(result);
      }
    }

    return unique;
  }

  Future<void> _buildSearchIndex(List<Note> notes) async {
    _indexedTerms.clear();
    _termFrequency.clear();
    _documentFrequency.clear();

    for (final note in notes) {
      final content = '${note.title} ${note.content}'.toLowerCase();
      final words = content.split(RegExp(r'\W+'));

      for (final word in words) {
        if (word.length > 2) {
          _indexedTerms[word] = (_indexedTerms[word] ?? [])..add(note.id);
          _termFrequency[word] = (_termFrequency[word] ?? 0) + 1;

          _documentFrequency[note.id] = (_documentFrequency[note.id] ?? {});
          _documentFrequency[note.id]![word] =
              (_documentFrequency[note.id]![word] ?? 0) + 1;
        }
      }
    }
  }

  void dispose() {
    _indexedTerms.clear();
    _termFrequency.clear();
    _documentFrequency.clear();
  }
}
