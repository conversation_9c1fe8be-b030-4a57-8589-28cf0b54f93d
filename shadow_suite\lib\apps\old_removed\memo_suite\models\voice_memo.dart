import 'package:uuid/uuid.dart';

class VoiceMemo {
  final String id;
  final String title;
  final String filePath;
  final Duration duration;
  final String category;
  final List<String> tags;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? transcription;
  final double fileSize; // in MB

  VoiceMemo({
    String? id,
    required this.title,
    required this.filePath,
    required this.duration,
    required this.category,
    required this.tags,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.transcription,
    this.fileSize = 0.0,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  VoiceMemo copyWith({
    String? title,
    String? filePath,
    Duration? duration,
    String? category,
    List<String>? tags,
    DateTime? updatedAt,
    String? transcription,
    double? fileSize,
  }) {
    return VoiceMemo(
      id: id,
      title: title ?? this.title,
      filePath: filePath ?? this.filePath,
      duration: duration ?? this.duration,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      transcription: transcription ?? this.transcription,
      fileSize: fileSize ?? this.fileSize,
    );
  }

  String get formattedDuration {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  String get formattedFileSize {
    if (fileSize < 1) {
      return '${(fileSize * 1024).toStringAsFixed(1)} KB';
    }
    return '${fileSize.toStringAsFixed(1)} MB';
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'filePath': filePath,
      'duration': duration.inMilliseconds,
      'category': category,
      'tags': tags.join(','),
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'transcription': transcription,
      'fileSize': fileSize,
    };
  }

  factory VoiceMemo.fromMap(Map<String, dynamic> map) {
    return VoiceMemo(
      id: map['id'],
      title: map['title'],
      filePath: map['filePath'],
      duration: Duration(milliseconds: map['duration']),
      category: map['category'],
      tags: map['tags'].toString().split(',').where((tag) => tag.isNotEmpty).toList(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
      transcription: map['transcription'],
      fileSize: map['fileSize']?.toDouble() ?? 0.0,
    );
  }

  @override
  String toString() {
    return 'VoiceMemo(id: $id, title: $title, duration: $formattedDuration, category: $category, size: $formattedFileSize)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VoiceMemo && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum VoiceMemoCategory {
  personal('Personal'),
  work('Work'),
  meetings('Meetings'),
  ideas('Ideas'),
  lectures('Lectures'),
  interviews('Interviews'),
  other('Other');

  const VoiceMemoCategory(this.displayName);
  final String displayName;

  static List<String> get allCategories => VoiceMemoCategory.values.map((e) => e.displayName).toList();
}

enum AudioQuality {
  low('Low (32 kbps)'),
  medium('Medium (64 kbps)'),
  high('High (128 kbps)'),
  veryHigh('Very High (256 kbps)');

  const AudioQuality(this.displayName);
  final String displayName;

  static List<String> get allQualities => AudioQuality.values.map((e) => e.displayName).toList();
}
