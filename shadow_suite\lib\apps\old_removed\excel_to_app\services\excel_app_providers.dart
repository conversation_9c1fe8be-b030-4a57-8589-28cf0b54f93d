import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/excel_app_tool.dart';
import 'excel_app_database_service.dart';
import 'excel_formula_engine.dart';

// Database service provider
final excelAppDatabaseProvider = Provider<ExcelAppDatabaseService>((ref) {
  return ExcelAppDatabaseService();
});

// Formula engine provider
final excelFormulaEngineProvider = Provider<ExcelFormulaEngine>((ref) {
  return ExcelFormulaEngine();
});

// Tools list provider
final excelAppToolsProvider =
    StateNotifierProvider<
      ExcelAppToolsNotifier,
      AsyncValue<List<ExcelAppTool>>
    >((ref) {
      return ExcelAppToolsNotifier(ref);
    });

// Favorite tools provider
final favoriteToolsProvider =
    StateNotifierProvider<FavoriteToolsNotifier, Set<String>>((ref) {
      return FavoriteToolsNotifier();
    });

class ExcelAppToolsNotifier
    extends StateNotifier<AsyncValue<List<ExcelAppTool>>> {
  final Ref ref;

  ExcelAppToolsNotifier(this.ref) : super(const AsyncValue.loading()) {
    loadTools();
  }

  Future<void> loadTools() async {
    try {
      state = const AsyncValue.loading();
      final database = ref.read(excelAppDatabaseProvider);
      final tools = await database.getAllTools();
      state = AsyncValue.data(tools);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> saveTool(ExcelAppTool tool) async {
    try {
      final database = ref.read(excelAppDatabaseProvider);
      await database.saveTool(tool);
      await loadTools(); // Refresh the list
    } catch (error) {
      // Handle error
    }
  }

  Future<void> deleteTool(String id) async {
    try {
      final database = ref.read(excelAppDatabaseProvider);
      await database.deleteTool(id);
      await loadTools(); // Refresh the list
    } catch (error) {
      // Handle error
    }
  }

  Future<void> duplicateTool(String id, String newName) async {
    try {
      final database = ref.read(excelAppDatabaseProvider);
      await database.duplicateTool(id, newName);
      await loadTools(); // Refresh the list
    } catch (error) {
      // Handle error
    }
  }

  Future<void> importTool(String jsonData, {String? newName}) async {
    try {
      final database = ref.read(excelAppDatabaseProvider);
      await database.importTool(jsonData, newName: newName);
      await loadTools(); // Refresh the list
    } catch (error) {
      // Handle error
    }
  }
}

// Current tool provider (for editing)
final currentExcelAppToolProvider =
    StateNotifierProvider<CurrentExcelAppToolNotifier, ExcelAppTool?>((ref) {
      return CurrentExcelAppToolNotifier(ref);
    });

class CurrentExcelAppToolNotifier extends StateNotifier<ExcelAppTool?> {
  final Ref ref;

  CurrentExcelAppToolNotifier(this.ref) : super(null);

  void setTool(ExcelAppTool tool) {
    state = tool;
  }

  void clearTool() {
    state = null;
  }

  void updateTool(ExcelAppTool tool) {
    state = tool;
    // Auto-save
    _autoSave();
  }

  void updateSpreadsheet(ExcelSpreadsheet spreadsheet) {
    if (state != null) {
      state = state!.copyWith(
        spreadsheet: spreadsheet,
        lastModified: DateTime.now(),
      );
      _autoSave();
    }
  }

  void updateUIComponents(List<UIComponent> components) {
    if (state != null) {
      state = state!.copyWith(
        uiComponents: components,
        lastModified: DateTime.now(),
      );
      _autoSave();
    }
  }

  void updateCell(
    String address,
    dynamic value, {
    String? formula,
    bool isFormula = false,
  }) {
    if (state != null) {
      final cells = Map<String, ExcelCell>.from(state!.spreadsheet.cells);
      cells[address] = ExcelCell(
        address: address,
        value: value,
        formula: formula,
        isFormula: isFormula,
      );

      final updatedSpreadsheet = state!.spreadsheet.copyWith(
        cells: cells,
        lastModified: DateTime.now(),
      );

      state = state!.copyWith(
        spreadsheet: updatedSpreadsheet,
        lastModified: DateTime.now(),
      );

      _autoSave();
    }
  }

  Future<void> _autoSave() async {
    if (state != null) {
      final database = ref.read(excelAppDatabaseProvider);
      await database.autoSaveTool(state!);
    }
  }
}

// Spreadsheet editor state providers
final selectedCellProvider = StateProvider<String?>((ref) => null);
final selectedRangeProvider = StateProvider<List<String>>((ref) => []);
final isEditingCellProvider = StateProvider<bool>((ref) => false);
final cellInputProvider = StateProvider<String>((ref) => '');
final showFormulaBarProvider = StateProvider<bool>((ref) => true);

// Formula auto-complete providers
final formulaInputProvider = StateProvider<String>((ref) => '');
final showAutoCompleteProvider = StateProvider<bool>((ref) => false);
final autoCompleteSuggestionsProvider = Provider<List<String>>((ref) {
  final input = ref.watch(formulaInputProvider);
  if (!input.startsWith('=')) return [];

  final formulaEngine = ref.read(excelFormulaEngineProvider);
  final functionPart = input.substring(1); // Remove =
  return formulaEngine.getFunctionSuggestions(functionPart);
});

// UI Builder state providers
final selectedComponentProvider = StateProvider<String?>((ref) => null);
final draggedComponentProvider = StateProvider<UIComponent?>((ref) => null);
final isDesignModeProvider = StateProvider<bool>((ref) => true);
final showComponentPaletteProvider = StateProvider<bool>((ref) => true);
final showPropertiesPanelProvider = StateProvider<bool>((ref) => true);

// Component palette provider
final componentPaletteProvider = Provider<List<ComponentType>>((ref) {
  return [
    ComponentType.textInput,
    ComponentType.numberInput,
    ComponentType.dropdown,
    ComponentType.label,
    ComponentType.calculatedDisplay,
    ComponentType.chart,
    ComponentType.container,
    ComponentType.spacer,
  ];
});

// Cell binding providers
final showCellBindingPopupProvider = StateProvider<bool>((ref) => false);
final bindingTargetComponentProvider = StateProvider<String?>((ref) => null);

// Tool runtime providers
final isRunningToolProvider = StateProvider<bool>((ref) => false);
final runtimeDataProvider = StateProvider<Map<String, dynamic>>((ref) => {});

// Search and filter providers
final toolsSearchQueryProvider = StateProvider<String>((ref) => '');
final toolsFilterProvider = StateProvider<String>(
  (ref) => 'all',
); // all, recent, favorites

final filteredToolsProvider = Provider<AsyncValue<List<ExcelAppTool>>>((ref) {
  final toolsAsync = ref.watch(excelAppToolsProvider);
  final searchQuery = ref.watch(toolsSearchQueryProvider);
  final filter = ref.watch(toolsFilterProvider);

  return toolsAsync.when(
    data: (tools) {
      var filteredTools = tools;

      // Apply search filter
      if (searchQuery.isNotEmpty) {
        filteredTools = filteredTools.where((tool) {
          return tool.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
              tool.description.toLowerCase().contains(
                searchQuery.toLowerCase(),
              );
        }).toList();
      }

      // Apply category filter
      switch (filter) {
        case 'recent':
          filteredTools.sort(
            (a, b) => b.lastModified.compareTo(a.lastModified),
          );
          filteredTools = filteredTools.take(10).toList();
          break;
        case 'favorites':
          // Filter by favorites
          final favoriteIds = ref.watch(favoriteToolsProvider);
          filteredTools = filteredTools
              .where((tool) => favoriteIds.contains(tool.id))
              .toList();
          break;
        default:
          filteredTools.sort(
            (a, b) => b.lastModified.compareTo(a.lastModified),
          );
      }

      return AsyncValue.data(filteredTools);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

// Settings providers
final excelAppSettingsProvider =
    StateNotifierProvider<ExcelAppSettingsNotifier, Map<String, dynamic>>((
      ref,
    ) {
      return ExcelAppSettingsNotifier(ref);
    });

class ExcelAppSettingsNotifier extends StateNotifier<Map<String, dynamic>> {
  final Ref ref;

  ExcelAppSettingsNotifier(this.ref) : super({}) {
    loadSettings();
  }

  Future<void> loadSettings() async {
    try {
      final database = ref.read(excelAppDatabaseProvider);
      final settings = await database.getSettings();
      state = settings;
    } catch (error) {
      // Use default settings
      state = {
        'autoSave': true,
        'autoSaveInterval': 30,
        'defaultColumns': 10,
        'defaultRows': 20,
        'showGridLines': true,
        'enableFormulas': true,
        'touchOptimized': true,
        'theme': 'system',
      };
    }
  }

  Future<void> updateSetting(String key, dynamic value) async {
    state = {...state, key: value};

    try {
      final database = ref.read(excelAppDatabaseProvider);
      await database.saveSettings(state);
    } catch (error) {
      // Handle error
    }
  }

  Future<void> resetToDefaults() async {
    state = {
      'autoSave': true,
      'autoSaveInterval': 30,
      'defaultColumns': 10,
      'defaultRows': 20,
      'showGridLines': true,
      'enableFormulas': true,
      'touchOptimized': true,
      'theme': 'system',
    };

    try {
      final database = ref.read(excelAppDatabaseProvider);
      await database.saveSettings(state);
    } catch (error) {
      // Handle error
    }
  }
}

// Statistics provider
final excelAppStatisticsProvider = FutureProvider<Map<String, dynamic>>((
  ref,
) async {
  final database = ref.read(excelAppDatabaseProvider);
  return await database.getStatistics();
});

// Validation providers
final toolNameValidationProvider = Provider.family<String?, String>((
  ref,
  name,
) {
  if (name.isEmpty) return 'Tool name is required';
  if (name.length < 3) return 'Tool name must be at least 3 characters';
  if (name.length > 50) return 'Tool name must be less than 50 characters';
  return null;
});

final formulaValidationProvider = Provider.family<bool, String>((ref, formula) {
  if (!formula.startsWith('=')) return true; // Not a formula

  final formulaEngine = ref.read(excelFormulaEngineProvider);
  return formulaEngine.isValidFormula(formula);
});

// Favorite tools notifier
class FavoriteToolsNotifier extends StateNotifier<Set<String>> {
  FavoriteToolsNotifier() : super(<String>{});

  void toggleFavorite(String toolId) {
    if (state.contains(toolId)) {
      state = Set.from(state)..remove(toolId);
    } else {
      state = Set.from(state)..add(toolId);
    }
  }

  void addFavorite(String toolId) {
    if (!state.contains(toolId)) {
      state = Set.from(state)..add(toolId);
    }
  }

  void removeFavorite(String toolId) {
    if (state.contains(toolId)) {
      state = Set.from(state)..remove(toolId);
    }
  }

  bool isFavorite(String toolId) {
    return state.contains(toolId);
  }

  void clearFavorites() {
    state = <String>{};
  }
}
