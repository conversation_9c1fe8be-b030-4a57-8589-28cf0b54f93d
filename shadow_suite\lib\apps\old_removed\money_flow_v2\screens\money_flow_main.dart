import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/app_theme.dart';
import '../services/money_flow_providers.dart';
import 'money_flow_dashboard.dart';
import 'money_flow_accounts.dart';
import 'money_flow_transactions.dart';
import 'money_flow_budgets.dart';
import 'money_flow_reports.dart';

class MoneyFlowMain extends ConsumerWidget {
  const MoneyFlowMain({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentScreen = ref.watch(moneyFlowCurrentScreenProvider);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Column(
        children: [
          _buildHeader(context, ref, currentScreen),
          Expanded(child: _buildCurrentScreen(currentScreen)),
        ],
      ),
    );
  }

  Widget _buildHeader(
    BuildContext context,
    WidgetRef ref,
    MoneyFlowScreen currentScreen,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.moneyFlowColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Title bar
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.account_balance_wallet,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Money Flow',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        'Personal Finance Management',
                        style: TextStyle(fontSize: 14, color: Colors.white70),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => _refreshAllData(ref),
                  icon: const Icon(Icons.refresh, color: Colors.white),
                  tooltip: 'Refresh Data',
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () => _showQuickActions(context, ref),
                  icon: const Icon(Icons.add, color: Colors.white),
                  tooltip: 'Quick Actions',
                ),
              ],
            ),
          ),
          // Navigation tabs
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              children: [
                _buildNavTab(
                  'Dashboard',
                  Icons.dashboard,
                  MoneyFlowScreen.dashboard,
                  currentScreen,
                  ref,
                ),
                _buildNavTab(
                  'Accounts',
                  Icons.account_balance,
                  MoneyFlowScreen.accounts,
                  currentScreen,
                  ref,
                ),
                _buildNavTab(
                  'Transactions',
                  Icons.receipt_long,
                  MoneyFlowScreen.transactions,
                  currentScreen,
                  ref,
                ),
                _buildNavTab(
                  'Budgets',
                  Icons.pie_chart,
                  MoneyFlowScreen.budgets,
                  currentScreen,
                  ref,
                ),
                _buildNavTab(
                  'Reports',
                  Icons.analytics,
                  MoneyFlowScreen.reports,
                  currentScreen,
                  ref,
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildNavTab(
    String title,
    IconData icon,
    MoneyFlowScreen screen,
    MoneyFlowScreen currentScreen,
    WidgetRef ref,
  ) {
    final isSelected = currentScreen == screen;

    return Expanded(
      child: InkWell(
        onTap: () =>
            ref.read(moneyFlowCurrentScreenProvider.notifier).state = screen,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected
                ? Colors.white.withValues(alpha: 0.2)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Icon(
                icon,
                color: isSelected ? Colors.white : Colors.white70,
                size: 20,
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  color: isSelected ? Colors.white : Colors.white70,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentScreen(MoneyFlowScreen currentScreen) {
    switch (currentScreen) {
      case MoneyFlowScreen.dashboard:
        return const MoneyFlowDashboard();
      case MoneyFlowScreen.accounts:
        return const MoneyFlowAccounts();
      case MoneyFlowScreen.transactions:
        return const MoneyFlowTransactions();
      case MoneyFlowScreen.budgets:
        return const MoneyFlowBudgets();
      case MoneyFlowScreen.reports:
        return const MoneyFlowReports();
    }
  }

  void _refreshAllData(WidgetRef ref) {
    ref.read(moneyFlowAccountsProvider.notifier).loadAccounts();
    ref.read(moneyFlowTransactionsProvider.notifier).loadTransactions();
    ref.read(moneyFlowBudgetsProvider.notifier).loadBudgets();
    ref.read(moneyFlowStatisticsProvider.notifier).loadStatistics();
    ref.read(moneyFlowMonthlySpendingProvider.notifier).loadSpending();
    ref
        .read(moneyFlowRecentTransactionsProvider.notifier)
        .loadRecentTransactions();
  }

  void _showQuickActions(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              child: const Text(
                'Quick Actions',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                ),
              ),
            ),
            _buildQuickActionItem(
              'Add Transaction',
              Icons.add_circle,
              const Color(0xFF27AE60),
              () {
                Navigator.pop(context);
                ref.read(moneyFlowCurrentScreenProvider.notifier).state =
                    MoneyFlowScreen.transactions;
                // Show add transaction dialog
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text(
                      'Add Transaction feature available in Unified Finance Manager',
                    ),
                    action: SnackBarAction(
                      label: 'Open',
                      onPressed: () {
                        // Navigate to Unified Finance Manager
                      },
                    ),
                  ),
                );
              },
            ),
            _buildQuickActionItem(
              'Create Account',
              Icons.account_balance,
              const Color(0xFF3498DB),
              () {
                Navigator.pop(context);
                ref.read(moneyFlowCurrentScreenProvider.notifier).state =
                    MoneyFlowScreen.accounts;
                showDialog(
                  context: context,
                  builder: (context) => AddAccountDialog(),
                );
              },
            ),
            _buildQuickActionItem(
              'Set Budget',
              Icons.pie_chart,
              const Color(0xFF9B59B6),
              () {
                Navigator.pop(context);
                ref.read(moneyFlowCurrentScreenProvider.notifier).state =
                    MoneyFlowScreen.budgets;
                // Show add budget dialog
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text(
                      'Budget creation available in Unified Finance Manager',
                    ),
                  ),
                );
              },
            ),
            _buildQuickActionItem(
              'View Reports',
              Icons.analytics,
              const Color(0xFFE67E22),
              () {
                Navigator.pop(context);
                ref.read(moneyFlowCurrentScreenProvider.notifier).state =
                    MoneyFlowScreen.reports;
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionItem(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF2C3E50),
              ),
            ),
            const Spacer(),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Color(0xFF95A5A6),
            ),
          ],
        ),
      ),
    );
  }
}

// Header widget for consistency
class MoneyFlowHeader extends StatelessWidget {
  final String title;
  final String subtitle;
  final List<Widget>? actions;

  const MoneyFlowHeader({
    super.key,
    required this.title,
    required this.subtitle,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Color(0xFFF8F9FA),
        border: Border(bottom: BorderSide(color: Color(0xFFE9ECEF))),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF7F8C8D),
                  ),
                ),
              ],
            ),
          ),
          if (actions != null) ...actions!,
        ],
      ),
    );
  }
}

/// Dialog for adding a new account
class AddAccountDialog extends StatefulWidget {
  const AddAccountDialog({super.key});

  @override
  State<AddAccountDialog> createState() => _AddAccountDialogState();
}

class _AddAccountDialogState extends State<AddAccountDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _balanceController = TextEditingController();
  String _selectedType = 'Checking';

  final List<String> _accountTypes = [
    'Checking',
    'Savings',
    'Credit Card',
    'Cash',
    'Investment',
    'Loan',
    'Other',
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _balanceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Account'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Account Name',
                hintText: 'Enter account name',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter an account name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedType,
              decoration: const InputDecoration(labelText: 'Account Type'),
              items: _accountTypes.map((type) {
                return DropdownMenuItem(value: type, child: Text(type));
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedType = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _balanceController,
              decoration: const InputDecoration(
                labelText: 'Initial Balance',
                hintText: 'Enter initial balance',
                prefixText: '\$',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter an initial balance';
                }
                if (double.tryParse(value) == null) {
                  return 'Please enter a valid number';
                }
                return null;
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(onPressed: _createAccount, child: const Text('Add')),
      ],
    );
  }

  void _createAccount() {
    if (_formKey.currentState!.validate()) {
      final balance = double.parse(_balanceController.text);

      // Create account with the provided data
      // Note: This is a demo implementation - real implementation would save to database
      print(
        'Creating account: ${_nameController.text}, Balance: \$${balance.toStringAsFixed(2)}',
      );

      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Account "${_nameController.text}" created successfully',
          ),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}
