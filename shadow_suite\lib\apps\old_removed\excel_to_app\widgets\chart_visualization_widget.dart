import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../services/chart_visualization_service.dart';

/// Advanced chart visualization widget
class ChartVisualizationWidget extends StatefulWidget {
  final List<List<dynamic>>? data;
  final Function(ChartConfiguration)? onChartCreated;

  const ChartVisualizationWidget({super.key, this.data, this.onChartCreated});

  @override
  State<ChartVisualizationWidget> createState() =>
      _ChartVisualizationWidgetState();
}

class _ChartVisualizationWidgetState extends State<ChartVisualizationWidget>
    with TickerProviderStateMixin {
  late TabController _tabController;
  ChartType _selectedChartType = ChartType.line;
  ChartConfiguration? _currentChart;
  ChartOptions _chartOptions = const ChartOptions(
    title: 'My Chart',
    showLegend: true,
    showGrid: true,
    animationDuration: 1000,
  );
  bool _isGenerating = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    ChartVisualizationService.initialize();

    if (widget.data != null) {
      _generateChart();
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildChartTypesTab(),
                _buildChartPreviewTab(),
                _buildChartOptionsTab(),
                _buildSavedChartsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              const Icon(Icons.bar_chart, size: 24),
              const SizedBox(width: 8),
              Text(
                'Chart Visualization',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const Spacer(),
              if (_isGenerating)
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
            ],
          ),
          const SizedBox(height: 16),
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(icon: Icon(Icons.category), text: 'Types'),
              Tab(icon: Icon(Icons.preview), text: 'Preview'),
              Tab(icon: Icon(Icons.settings), text: 'Options'),
              Tab(icon: Icon(Icons.save), text: 'Saved'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildChartTypesTab() {
    final templates = ChartVisualizationService.templates;
    final recommendations = widget.data != null
        ? ChartVisualizationService.getChartRecommendations(widget.data!)
        : <ChartRecommendation>[];

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (recommendations.isNotEmpty) ...[
            Text(
              'Recommended Charts',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: recommendations.length,
                itemBuilder: (context, index) {
                  return _buildRecommendationCard(recommendations[index]);
                },
              ),
            ),
            const SizedBox(height: 24),
          ],
          Text(
            'All Chart Types',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 1.2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: templates.length,
              itemBuilder: (context, index) {
                return _buildChartTypeCard(templates[index]);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationCard(ChartRecommendation recommendation) {
    final template = ChartVisualizationService.getTemplate(recommendation.type);
    if (template == null) return const SizedBox.shrink();

    return Container(
      width: 200,
      margin: const EdgeInsets.only(right: 16),
      child: Card(
        color: Colors.green[50],
        child: InkWell(
          onTap: () => _selectChartType(recommendation.type),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(template.icon, color: template.color),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        template.name,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${(recommendation.confidence * 100).toInt()}%',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  recommendation.reason,
                  style: const TextStyle(fontSize: 12),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildChartTypeCard(ChartTemplate template) {
    final isSelected = _selectedChartType == template.type;

    return Card(
      color: isSelected ? template.color.withValues(alpha: 0.1) : null,
      child: InkWell(
        onTap: () => _selectChartType(template.type),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                template.icon,
                size: 48,
                color: isSelected ? template.color : Colors.grey[600],
              ),
              const SizedBox(height: 12),
              Text(
                template.name,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: isSelected ? template.color : null,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                template.description,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChartPreviewTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: widget.data != null ? _generateChart : null,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Generate Chart'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _currentChart != null ? _saveChart : null,
                  icon: const Icon(Icons.save),
                  label: const Text('Save Chart'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: _currentChart != null
                ? _buildChartPreview(_currentChart!)
                : _buildEmptyState(),
          ),
        ],
      ),
    );
  }

  Widget _buildChartPreview(ChartConfiguration config) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(config.title, style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 16),
            Expanded(child: _buildChart(config)),
            const SizedBox(height: 16),
            _buildChartStatistics(config),
          ],
        ),
      ),
    );
  }

  Widget _buildChart(ChartConfiguration config) {
    // Simple chart visualization using CustomPainter
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: CustomPaint(painter: ChartPainter(config), size: Size.infinite),
    );
  }

  Widget _buildChartStatistics(ChartConfiguration config) {
    final stats = ChartVisualizationService.getStatistics(config);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildStatItem('Points', '${stats.totalPoints}'),
        _buildStatItem('Series', '${stats.seriesCount}'),
        _buildStatItem('Min', stats.minValue.toStringAsFixed(1)),
        _buildStatItem('Max', stats.maxValue.toStringAsFixed(1)),
        _buildStatItem('Avg', stats.averageValue.toStringAsFixed(1)),
      ],
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        Text(label, style: const TextStyle(fontSize: 12)),
      ],
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.bar_chart_outlined, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text('No chart generated yet'),
          Text('Import data and select a chart type to get started'),
        ],
      ),
    );
  }

  Widget _buildChartOptionsTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Chart Options',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    decoration: const InputDecoration(
                      labelText: 'Chart Title',
                      border: OutlineInputBorder(),
                    ),
                    controller: TextEditingController(
                      text: _chartOptions.title,
                    ),
                    onChanged: (value) {
                      setState(() {
                        _chartOptions = ChartOptions(
                          title: value,
                          showLegend: _chartOptions.showLegend,
                          showGrid: _chartOptions.showGrid,
                          animationDuration: _chartOptions.animationDuration,
                        );
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  CheckboxListTile(
                    title: const Text('Show Legend'),
                    value: _chartOptions.showLegend,
                    onChanged: (value) {
                      setState(() {
                        _chartOptions = ChartOptions(
                          title: _chartOptions.title,
                          showLegend: value ?? true,
                          showGrid: _chartOptions.showGrid,
                          animationDuration: _chartOptions.animationDuration,
                        );
                      });
                    },
                  ),
                  CheckboxListTile(
                    title: const Text('Show Grid'),
                    value: _chartOptions.showGrid,
                    onChanged: (value) {
                      setState(() {
                        _chartOptions = ChartOptions(
                          title: _chartOptions.title,
                          showLegend: _chartOptions.showLegend,
                          showGrid: value ?? true,
                          animationDuration: _chartOptions.animationDuration,
                        );
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Animation Duration: ${_chartOptions.animationDuration}ms',
                  ),
                  Slider(
                    value: _chartOptions.animationDuration.toDouble(),
                    min: 0,
                    max: 2000,
                    divisions: 20,
                    onChanged: (value) {
                      setState(() {
                        _chartOptions = ChartOptions(
                          title: _chartOptions.title,
                          showLegend: _chartOptions.showLegend,
                          showGrid: _chartOptions.showGrid,
                          animationDuration: value.toInt(),
                        );
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _applyOptions,
                  icon: const Icon(Icons.check),
                  label: const Text('Apply Options'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _resetOptions,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Reset'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSavedChartsTab() {
    final savedCharts = ChartVisualizationService.savedCharts;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _exportChart,
                  icon: const Icon(Icons.download),
                  label: const Text('Export Chart'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _clearSavedCharts,
                  icon: const Icon(Icons.clear),
                  label: const Text('Clear All'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: savedCharts.isEmpty
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.save_outlined, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text('No saved charts'),
                        Text('Create and save charts to see them here'),
                      ],
                    ),
                  )
                : ListView.builder(
                    itemCount: savedCharts.length,
                    itemBuilder: (context, index) {
                      return _buildSavedChartTile(savedCharts[index]);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildSavedChartTile(ChartConfiguration chart) {
    final template = ChartVisualizationService.getTemplate(chart.type);

    return Card(
      child: ListTile(
        leading: Icon(
          template?.icon ?? Icons.bar_chart,
          color: template?.color ?? Colors.blue,
        ),
        title: Text(chart.title),
        subtitle: Text(
          '${chart.type.name.toUpperCase()} • ${chart.createdAt.toString().substring(0, 19)}',
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => _loadChart(chart),
              icon: const Icon(Icons.visibility),
              tooltip: 'View',
            ),
            IconButton(
              onPressed: () => _deleteChart(chart.id),
              icon: const Icon(Icons.delete),
              tooltip: 'Delete',
            ),
          ],
        ),
      ),
    );
  }

  void _selectChartType(ChartType type) {
    setState(() {
      _selectedChartType = type;
    });

    if (widget.data != null) {
      _generateChart();
    }
  }

  void _generateChart() async {
    if (widget.data == null) return;

    setState(() => _isGenerating = true);

    try {
      final chart = await ChartVisualizationService.createChart(
        type: _selectedChartType,
        data: widget.data!,
        options: _chartOptions,
      );

      setState(() {
        _currentChart = chart;
      });

      widget.onChartCreated?.call(chart);
    } catch (e) {
      _showSnackBar('Failed to generate chart: $e', Colors.red);
    } finally {
      setState(() => _isGenerating = false);
    }
  }

  void _saveChart() {
    if (_currentChart == null) return;

    _showSnackBar('Chart saved successfully', Colors.green);
  }

  void _loadChart(ChartConfiguration chart) {
    setState(() {
      _currentChart = chart;
      _selectedChartType = chart.type;
      _chartOptions = chart.options;
    });

    _tabController.animateTo(1); // Switch to preview tab
  }

  void _deleteChart(String chartId) {
    ChartVisualizationService.deleteChart(chartId);
    setState(() {});
    _showSnackBar('Chart deleted', Colors.orange);
  }

  void _applyOptions() {
    if (widget.data != null) {
      _generateChart();
    }
    _showSnackBar('Options applied', Colors.blue);
  }

  void _resetOptions() {
    final template = ChartVisualizationService.getTemplate(_selectedChartType);
    setState(() {
      _chartOptions =
          template?.defaultOptions ??
          const ChartOptions(
            title: 'My Chart',
            showLegend: true,
            showGrid: true,
            animationDuration: 1000,
          );
    });
  }

  void _exportChart() async {
    if (_currentChart == null) return;

    try {
      final result = await ChartVisualizationService.exportChart(
        config: _currentChart!,
        format: ExportFormat.png,
        size: const Size(800, 600),
      );

      if (result.success) {
        _showSnackBar('Chart exported as ${result.fileName}', Colors.green);
      } else {
        _showSnackBar('Export failed: ${result.error}', Colors.red);
      }
    } catch (e) {
      _showSnackBar('Export failed: $e', Colors.red);
    }
  }

  void _clearSavedCharts() {
    ChartVisualizationService.clearCharts();
    setState(() {});
    _showSnackBar('All charts cleared', Colors.orange);
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

/// Custom painter for chart visualization
class ChartPainter extends CustomPainter {
  final ChartConfiguration config;

  ChartPainter(this.config);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    // Draw grid if enabled
    if (config.options.showGrid) {
      _drawGrid(canvas, size);
    }

    // Draw chart based on type
    switch (config.type) {
      case ChartType.line:
        _drawLineChart(canvas, size, paint);
        break;
      case ChartType.bar:
        _drawBarChart(canvas, size, paint);
        break;
      case ChartType.pie:
        _drawPieChart(canvas, size, paint);
        break;
      case ChartType.scatter:
        _drawScatterChart(canvas, size, paint);
        break;
      case ChartType.area:
        _drawAreaChart(canvas, size, paint);
        break;
      case ChartType.histogram:
        _drawHistogramChart(canvas, size, paint);
        break;
    }
  }

  void _drawGrid(Canvas canvas, Size size) {
    final gridPaint = Paint()
      ..color = Colors.grey[300]!
      ..strokeWidth = 0.5;

    const gridLines = 10;
    final stepX = size.width / gridLines;
    final stepY = size.height / gridLines;

    // Vertical lines
    for (int i = 0; i <= gridLines; i++) {
      final x = i * stepX;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), gridPaint);
    }

    // Horizontal lines
    for (int i = 0; i <= gridLines; i++) {
      final y = i * stepY;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), gridPaint);
    }
  }

  void _drawLineChart(Canvas canvas, Size size, Paint paint) {
    if (config.data.points.isEmpty) return;

    final points = config.data.points;
    final maxX = points.map((p) => p.x).reduce(math.max);
    final minX = points.map((p) => p.x).reduce(math.min);
    final allValues = points.expand((p) => p.values).toList();
    final maxY = allValues.isNotEmpty ? allValues.reduce(math.max) : 1.0;
    final minY = allValues.isNotEmpty ? allValues.reduce(math.min) : 0.0;

    paint.color = Colors.blue;

    final path = Path();
    bool isFirst = true;

    for (final point in points) {
      if (point.values.isNotEmpty) {
        final x = (point.x - minX) / (maxX - minX) * size.width;
        final y =
            size.height -
            (point.values.first - minY) / (maxY - minY) * size.height;

        if (isFirst) {
          path.moveTo(x, y);
          isFirst = false;
        } else {
          path.lineTo(x, y);
        }
      }
    }

    canvas.drawPath(path, paint);
  }

  void _drawBarChart(Canvas canvas, Size size, Paint paint) {
    if (config.data.points.isEmpty) return;

    final points = config.data.points;
    final allValues = points.expand((p) => p.values).toList();
    final maxY = allValues.isNotEmpty ? allValues.reduce(math.max) : 1.0;

    final barWidth = size.width / points.length * 0.8;
    final spacing = size.width / points.length * 0.2;

    paint.style = PaintingStyle.fill;
    paint.color = Colors.blue;

    for (int i = 0; i < points.length; i++) {
      final point = points[i];
      if (point.values.isNotEmpty) {
        final x = i * (barWidth + spacing) + spacing / 2;
        final height = point.values.first / maxY * size.height;
        final y = size.height - height;

        canvas.drawRect(Rect.fromLTWH(x, y, barWidth, height), paint);
      }
    }
  }

  void _drawPieChart(Canvas canvas, Size size, Paint paint) {
    if (config.data.points.isEmpty) return;

    final points = config.data.points;
    final total = points.fold<double>(
      0,
      (sum, point) => sum + (point.values.isNotEmpty ? point.values.first : 0),
    );

    if (total == 0) return;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2 * 0.8;

    paint.style = PaintingStyle.fill;

    double startAngle = -math.pi / 2;
    final colors = [
      Colors.blue,
      Colors.red,
      Colors.green,
      Colors.orange,
      Colors.purple,
    ];

    for (int i = 0; i < points.length; i++) {
      final point = points[i];
      if (point.values.isNotEmpty) {
        final value = point.values.first;
        final sweepAngle = (value / total) * 2 * math.pi;

        paint.color = colors[i % colors.length];

        canvas.drawArc(
          Rect.fromCircle(center: center, radius: radius),
          startAngle,
          sweepAngle,
          true,
          paint,
        );

        startAngle += sweepAngle;
      }
    }
  }

  void _drawScatterChart(Canvas canvas, Size size, Paint paint) {
    if (config.data.points.isEmpty) return;

    final points = config.data.points;
    final maxX = points.map((p) => p.x).reduce(math.max);
    final minX = points.map((p) => p.x).reduce(math.min);
    final allValues = points.expand((p) => p.values).toList();
    final maxY = allValues.isNotEmpty ? allValues.reduce(math.max) : 1.0;
    final minY = allValues.isNotEmpty ? allValues.reduce(math.min) : 0.0;

    paint.style = PaintingStyle.fill;
    paint.color = Colors.blue;

    for (final point in points) {
      if (point.values.isNotEmpty) {
        final x = (point.x - minX) / (maxX - minX) * size.width;
        final y =
            size.height -
            (point.values.first - minY) / (maxY - minY) * size.height;

        canvas.drawCircle(Offset(x, y), 4, paint);
      }
    }
  }

  void _drawAreaChart(Canvas canvas, Size size, Paint paint) {
    if (config.data.points.isEmpty) return;

    final points = config.data.points;
    final maxX = points.map((p) => p.x).reduce(math.max);
    final minX = points.map((p) => p.x).reduce(math.min);
    final allValues = points.expand((p) => p.values).toList();
    final maxY = allValues.isNotEmpty ? allValues.reduce(math.max) : 1.0;
    final minY = allValues.isNotEmpty ? allValues.reduce(math.min) : 0.0;

    final path = Path();
    path.moveTo(0, size.height);

    for (final point in points) {
      if (point.values.isNotEmpty) {
        final x = (point.x - minX) / (maxX - minX) * size.width;
        final y =
            size.height -
            (point.values.first - minY) / (maxY - minY) * size.height;
        path.lineTo(x, y);
      }
    }

    path.lineTo(size.width, size.height);
    path.close();

    paint.style = PaintingStyle.fill;
    paint.color = Colors.blue.withValues(alpha: 0.3);
    canvas.drawPath(path, paint);

    // Draw line on top
    paint.style = PaintingStyle.stroke;
    paint.color = Colors.blue;
    _drawLineChart(canvas, size, paint);
  }

  void _drawHistogramChart(Canvas canvas, Size size, Paint paint) {
    _drawBarChart(canvas, size, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
