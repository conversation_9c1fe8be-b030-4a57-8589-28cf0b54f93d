import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../../../core/database/database_initializer.dart';
import '../models/tool.dart';
import '../models/spreadsheet.dart';
import '../models/ui_component.dart';
import '../models/spreadsheet_cell.dart';

class ToolsDatabaseService {
  static Database? _database;
  static const String _dbName = 'tools_builder.db';
  static const int _dbVersion = 1;

  // Table names
  static const String _toolsTable = 'tools';
  static const String _templatesTable = 'tool_templates';
  static const String _spreadsheetsTable = 'spreadsheets';
  static const String _sheetsTable = 'sheets';
  static const String _cellsTable = 'cells';
  static const String _componentsTable = 'ui_components';
  static const String _namedRangesTable = 'named_ranges';
  static const String _conditionalFormatsTable = 'conditional_formats';
  static const String _dataValidationsTable = 'data_validations';

  static Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  static Future<Database> _initDatabase() async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, _dbName);

    // Use safe database opening with proper initialization
    final db = await DatabaseInitializer.safeOpenDatabase(
      path,
      version: _dbVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );

    if (db == null) {
      throw Exception('Failed to initialize Tools Builder database');
    }

    return db;
  }

  static Future<void> _onCreate(Database db, int version) async {
    // Create tools table
    await db.execute('''
      CREATE TABLE $_toolsTable (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        type TEXT NOT NULL,
        category TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'draft',
        thumbnailUrl TEXT,
        configuration TEXT,
        tags TEXT,
        templateId TEXT,
        creatorId TEXT NOT NULL,
        createdAt TEXT NOT NULL,
        lastModified TEXT NOT NULL,
        version INTEGER NOT NULL DEFAULT 1,
        isPublic INTEGER NOT NULL DEFAULT 0,
        requiresAuth INTEGER NOT NULL DEFAULT 0,
        permissions TEXT,
        analytics TEXT
      )
    ''');

    // Create tool templates table
    await db.execute('''
      CREATE TABLE $_templatesTable (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        type TEXT NOT NULL,
        category TEXT NOT NULL,
        thumbnailUrl TEXT,
        configuration TEXT,
        tags TEXT,
        isPremium INTEGER NOT NULL DEFAULT 0
      )
    ''');

    // Create spreadsheets table
    await db.execute('''
      CREATE TABLE $_spreadsheetsTable (
        id TEXT PRIMARY KEY,
        toolId TEXT NOT NULL,
        name TEXT NOT NULL,
        activeSheetIndex INTEGER NOT NULL DEFAULT 0,
        description TEXT,
        metadata TEXT,
        createdAt TEXT NOT NULL,
        lastModified TEXT NOT NULL,
        filePath TEXT,
        isReadOnly INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (toolId) REFERENCES $_toolsTable (id) ON DELETE CASCADE
      )
    ''');

    // Create sheets table
    await db.execute('''
      CREATE TABLE $_sheetsTable (
        id TEXT PRIMARY KEY,
        spreadsheetId TEXT NOT NULL,
        name TEXT NOT NULL,
        isHidden INTEGER NOT NULL DEFAULT 0,
        isProtected INTEGER NOT NULL DEFAULT 0,
        protectionPassword TEXT,
        tabColor TEXT NOT NULL DEFAULT '#FFFFFF',
        sheetIndex INTEGER NOT NULL DEFAULT 0,
        defaultRowHeight REAL NOT NULL DEFAULT 20.0,
        defaultColumnWidth REAL NOT NULL DEFAULT 80.0,
        rowHeights TEXT,
        columnWidths TEXT,
        hiddenRows TEXT,
        hiddenColumns TEXT,
        printSettings TEXT,
        FOREIGN KEY (spreadsheetId) REFERENCES $_spreadsheetsTable (id) ON DELETE CASCADE
      )
    ''');

    // Create cells table
    await db.execute('''
      CREATE TABLE $_cellsTable (
        id TEXT PRIMARY KEY,
        sheetId TEXT NOT NULL,
        row INTEGER NOT NULL,
        column INTEGER NOT NULL,
        cellAddress TEXT NOT NULL,
        rawValue TEXT,
        calculatedValue TEXT,
        formula TEXT,
        dataType TEXT NOT NULL DEFAULT 'text',
        format TEXT,
        comment TEXT,
        isLocked INTEGER NOT NULL DEFAULT 0,
        isHidden INTEGER NOT NULL DEFAULT 0,
        dependentCells TEXT,
        precedentCells TEXT,
        lastModified TEXT NOT NULL,
        FOREIGN KEY (sheetId) REFERENCES $_sheetsTable (id) ON DELETE CASCADE,
        UNIQUE(sheetId, cellAddress)
      )
    ''');

    // Create UI components table
    await db.execute('''
      CREATE TABLE $_componentsTable (
        id TEXT PRIMARY KEY,
        toolId TEXT NOT NULL,
        type TEXT NOT NULL,
        label TEXT,
        placeholder TEXT,
        helpText TEXT,
        x REAL NOT NULL DEFAULT 0,
        y REAL NOT NULL DEFAULT 0,
        style TEXT,
        properties TEXT,
        dataBinding TEXT,
        children TEXT,
        parentId TEXT,
        isVisible INTEGER NOT NULL DEFAULT 1,
        isEnabled INTEGER NOT NULL DEFAULT 1,
        isRequired INTEGER NOT NULL DEFAULT 0,
        zIndex INTEGER NOT NULL DEFAULT 0,
        events TEXT,
        FOREIGN KEY (toolId) REFERENCES $_toolsTable (id) ON DELETE CASCADE
      )
    ''');

    // Create named ranges table
    await db.execute('''
      CREATE TABLE $_namedRangesTable (
        id TEXT PRIMARY KEY,
        sheetId TEXT NOT NULL,
        name TEXT NOT NULL,
        range TEXT NOT NULL,
        comment TEXT,
        FOREIGN KEY (sheetId) REFERENCES $_sheetsTable (id) ON DELETE CASCADE
      )
    ''');

    // Create conditional formats table
    await db.execute('''
      CREATE TABLE $_conditionalFormatsTable (
        id TEXT PRIMARY KEY,
        sheetId TEXT NOT NULL,
        range TEXT NOT NULL,
        condition TEXT NOT NULL,
        format TEXT NOT NULL,
        priority INTEGER NOT NULL DEFAULT 1,
        FOREIGN KEY (sheetId) REFERENCES $_sheetsTable (id) ON DELETE CASCADE
      )
    ''');

    // Create data validations table
    await db.execute('''
      CREATE TABLE $_dataValidationsTable (
        id TEXT PRIMARY KEY,
        sheetId TEXT NOT NULL,
        range TEXT NOT NULL,
        validationType TEXT NOT NULL,
        criteria TEXT,
        allowedValues TEXT,
        inputMessage TEXT,
        errorMessage TEXT,
        showInputMessage INTEGER NOT NULL DEFAULT 1,
        showErrorMessage INTEGER NOT NULL DEFAULT 1,
        FOREIGN KEY (sheetId) REFERENCES $_sheetsTable (id) ON DELETE CASCADE
      )
    ''');

    // Create indexes for better performance
    await db.execute(
      'CREATE INDEX idx_tools_creator ON $_toolsTable(creatorId)',
    );
    await db.execute(
      'CREATE INDEX idx_tools_category ON $_toolsTable(category)',
    );
    await db.execute('CREATE INDEX idx_tools_status ON $_toolsTable(status)');
    await db.execute('CREATE INDEX idx_cells_sheet ON $_cellsTable(sheetId)');
    await db.execute(
      'CREATE INDEX idx_cells_address ON $_cellsTable(cellAddress)',
    );
    await db.execute(
      'CREATE INDEX idx_components_tool ON $_componentsTable(toolId)',
    );
    await db.execute(
      'CREATE INDEX idx_components_parent ON $_componentsTable(parentId)',
    );

    // Insert default templates
    await _insertDefaultTemplates(db);
  }

  static Future<void> _onUpgrade(
    Database db,
    int oldVersion,
    int newVersion,
  ) async {
    // Handle database upgrades here
  }

  static Future<void> _insertDefaultTemplates(Database db) async {
    final templates = DefaultToolTemplates.getDefaultTemplates();

    for (final template in templates) {
      await db.insert(_templatesTable, {
        'id': template.id,
        'name': template.name,
        'description': template.description,
        'type': template.type.name,
        'category': template.category.name,
        'thumbnailUrl': template.thumbnailUrl,
        'configuration': _encodeJson(template.configuration),
        'tags': _encodeJson(template.tags),
        'isPremium': template.isPremium ? 1 : 0,
      });
    }
  }

  // Tool operations
  static Future<List<Tool>> getAllTools({String? creatorId}) async {
    final db = await database;
    final whereClause = creatorId != null ? 'creatorId = ?' : null;
    final whereArgs = creatorId != null ? [creatorId] : null;

    final List<Map<String, dynamic>> maps = await db.query(
      _toolsTable,
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'lastModified DESC',
    );

    final tools = <Tool>[];
    for (final map in maps) {
      final tool = await _toolFromMap(map);
      tools.add(tool);
    }

    return tools;
  }

  static Future<Tool?> getToolById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _toolsTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;
    return await _toolFromMap(maps.first);
  }

  static Future<void> insertTool(Tool tool) async {
    final db = await database;

    await db.transaction((txn) async {
      // Insert tool
      await txn.insert(_toolsTable, _toolToMap(tool));

      // Insert spreadsheet
      if (tool.spreadsheet != null) {
        await _insertSpreadsheet(txn, tool.spreadsheet!, tool.id);
      }

      // Insert components
      for (final component in tool.components) {
        await _insertComponent(txn, component, tool.id);
      }
    });
  }

  static Future<void> updateTool(Tool tool) async {
    final db = await database;

    await db.transaction((txn) async {
      // Update tool
      await txn.update(
        _toolsTable,
        _toolToMap(tool),
        where: 'id = ?',
        whereArgs: [tool.id],
      );

      // Delete and recreate spreadsheet and components
      await txn.delete(
        _spreadsheetsTable,
        where: 'toolId = ?',
        whereArgs: [tool.id],
      );
      await txn.delete(
        _componentsTable,
        where: 'toolId = ?',
        whereArgs: [tool.id],
      );

      // Insert updated data
      if (tool.spreadsheet != null) {
        await _insertSpreadsheet(txn, tool.spreadsheet!, tool.id);
      }
      for (final component in tool.components) {
        await _insertComponent(txn, component, tool.id);
      }
    });
  }

  static Future<void> deleteTool(String id) async {
    final db = await database;
    await db.delete(_toolsTable, where: 'id = ?', whereArgs: [id]);
  }

  // Template operations
  static Future<List<ToolTemplate>> getAllTemplates() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _templatesTable,
      orderBy: 'category, name',
    );

    return maps.map((map) => _templateFromMap(map)).toList();
  }

  static Future<ToolTemplate?> getTemplateById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _templatesTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;
    return _templateFromMap(maps.first);
  }

  // Helper methods
  static Future<Tool> _toolFromMap(Map<String, dynamic> map) async {
    final db = await database;

    // Get spreadsheet
    final spreadsheetMaps = await db.query(
      _spreadsheetsTable,
      where: 'toolId = ?',
      whereArgs: [map['id']],
    );

    Spreadsheet? spreadsheet;
    if (spreadsheetMaps.isNotEmpty) {
      spreadsheet = await _spreadsheetFromMap(spreadsheetMaps.first);
    }

    // Get components
    final componentMaps = await db.query(
      _componentsTable,
      where: 'toolId = ?',
      whereArgs: [map['id']],
      orderBy: 'zIndex, y, x',
    );

    final components = componentMaps.map((m) => _componentFromMap(m)).toList();

    return Tool(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      type: ToolType.values.firstWhere((e) => e.name == map['type']),
      category: ToolCategory.values.firstWhere(
        (e) => e.name == map['category'],
      ),
      status: ToolStatus.values.firstWhere((e) => e.name == map['status']),
      thumbnailUrl: map['thumbnailUrl'] ?? '',
      components: components,
      spreadsheet: spreadsheet ?? Spreadsheet(name: 'Default', sheets: []),
      configuration: Map<String, dynamic>.from(
        _decodeJson(map['configuration']) ?? {},
      ),
      tags: List<String>.from(_decodeJson(map['tags']) ?? []),
      templateId: map['templateId'],
      creatorId: map['creatorId'],
      createdAt: DateTime.parse(map['createdAt']),
      lastModified: DateTime.parse(map['lastModified']),
      version: map['version'],
      isPublic: map['isPublic'] == 1,
      requiresAuth: map['requiresAuth'] == 1,
      permissions: Map<String, dynamic>.from(
        _decodeJson(map['permissions']) ?? {},
      ),
      analytics: Map<String, dynamic>.from(_decodeJson(map['analytics']) ?? {}),
    );
  }

  static Map<String, dynamic> _toolToMap(Tool tool) {
    return {
      'id': tool.id,
      'name': tool.name,
      'description': tool.description,
      'type': tool.type.name,
      'category': tool.category.name,
      'status': tool.status.name,
      'thumbnailUrl': tool.thumbnailUrl,
      'configuration': _encodeJson(tool.configuration),
      'tags': _encodeJson(tool.tags),
      'templateId': tool.templateId,
      'creatorId': tool.creatorId,
      'createdAt': tool.createdAt.toIso8601String(),
      'lastModified': tool.lastModified.toIso8601String(),
      'version': tool.version,
      'isPublic': tool.isPublic ? 1 : 0,
      'requiresAuth': tool.requiresAuth ? 1 : 0,
      'permissions': _encodeJson(tool.permissions),
      'analytics': _encodeJson(tool.analytics),
    };
  }

  static ToolTemplate _templateFromMap(Map<String, dynamic> map) {
    return ToolTemplate(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      type: ToolType.values.firstWhere((e) => e.name == map['type']),
      category: ToolCategory.values.firstWhere(
        (e) => e.name == map['category'],
      ),
      thumbnailUrl: map['thumbnailUrl'] ?? '',
      configuration: Map<String, dynamic>.from(
        _decodeJson(map['configuration']) ?? {},
      ),
      tags: List<String>.from(_decodeJson(map['tags']) ?? []),
      isPremium: map['isPremium'] == 1,
    );
  }

  static UIComponent _componentFromMap(Map<String, dynamic> map) {
    return UIComponent(
      id: map['id'],
      type: ComponentType.values.firstWhere((e) => e.name == map['type']),
      label: map['label'] ?? '',
      placeholder: map['placeholder'],
      helpText: map['helpText'],
      x: map['x']?.toDouble() ?? 0,
      y: map['y']?.toDouble() ?? 0,
      style: ComponentStyle.fromMap(
        Map<String, dynamic>.from(_decodeJson(map['style']) ?? {}),
      ),
      properties: Map<String, dynamic>.from(
        _decodeJson(map['properties']) ?? {},
      ),
      dataBinding: map['dataBinding'] != null
          ? DataBinding.fromMap(
              Map<String, dynamic>.from(_decodeJson(map['dataBinding']) ?? {}),
            )
          : null,
      children: List<String>.from(_decodeJson(map['children']) ?? []),
      parentId: map['parentId'],
      isVisible: map['isVisible'] == 1,
      isEnabled: map['isEnabled'] == 1,
      isRequired: map['isRequired'] == 1,
      zIndex: map['zIndex'] ?? 0,
      events: Map<String, String>.from(_decodeJson(map['events']) ?? {}),
    );
  }

  static Future<void> _insertSpreadsheet(
    DatabaseExecutor db,
    Spreadsheet spreadsheet,
    String toolId,
  ) async {
    await db.insert(_spreadsheetsTable, {
      'id': spreadsheet.id,
      'toolId': toolId,
      'name': spreadsheet.name,
      'activeSheetIndex': spreadsheet.activeSheetIndex,
      'description': spreadsheet.description,
      'metadata': _encodeJson(spreadsheet.metadata),
      'createdAt': spreadsheet.createdAt.toIso8601String(),
      'lastModified': spreadsheet.lastModified.toIso8601String(),
      'filePath': spreadsheet.filePath,
      'isReadOnly': spreadsheet.isReadOnly ? 1 : 0,
    });

    for (final sheet in spreadsheet.sheets) {
      await _insertSheet(db, sheet, spreadsheet.id);
    }
  }

  static Future<void> _insertSheet(
    DatabaseExecutor db,
    SpreadsheetSheet sheet,
    String spreadsheetId,
  ) async {
    await db.insert(_sheetsTable, {
      'id': sheet.id,
      'spreadsheetId': spreadsheetId,
      'name': sheet.name,
      'isHidden': sheet.isHidden ? 1 : 0,
      'isProtected': sheet.isProtected ? 1 : 0,
      'protectionPassword': sheet.protectionPassword,
      'tabColor': sheet.tabColor,
      'sheetIndex': sheet.index,
      'defaultRowHeight': sheet.defaultRowHeight,
      'defaultColumnWidth': sheet.defaultColumnWidth,
      'rowHeights': _encodeJson(sheet.rowHeights),
      'columnWidths': _encodeJson(sheet.columnWidths),
      'hiddenRows': _encodeJson(sheet.hiddenRows.toList()),
      'hiddenColumns': _encodeJson(sheet.hiddenColumns.toList()),
      'printSettings': _encodeJson(sheet.printSettings),
    });

    for (final cell in sheet.cells.values) {
      await _insertCell(db, cell, sheet.id);
    }
  }

  static Future<void> _insertCell(
    DatabaseExecutor db,
    SpreadsheetCell cell,
    String sheetId,
  ) async {
    await db.insert(_cellsTable, {
      'id': cell.id,
      'sheetId': sheetId,
      'row': cell.row,
      'column': cell.column,
      'cellAddress': cell.cellAddress,
      'rawValue': cell.rawValue,
      'calculatedValue': _encodeJson(cell.calculatedValue),
      'formula': cell.formula,
      'dataType': cell.dataType.name,
      'format': _encodeJson(cell.format.toMap()),
      'comment': cell.comment,
      'isLocked': cell.isLocked ? 1 : 0,
      'isHidden': cell.isHidden ? 1 : 0,
      'dependentCells': _encodeJson(cell.dependentCells),
      'precedentCells': _encodeJson(cell.precedentCells),
      'lastModified': cell.lastModified.toIso8601String(),
    });
  }

  static Future<void> _insertComponent(
    DatabaseExecutor db,
    UIComponent component,
    String toolId,
  ) async {
    await db.insert(_componentsTable, {
      'id': component.id,
      'toolId': toolId,
      'type': component.type.name,
      'label': component.label,
      'placeholder': component.placeholder,
      'helpText': component.helpText,
      'x': component.x,
      'y': component.y,
      'style': _encodeJson(component.style.toMap()),
      'properties': _encodeJson(component.properties),
      'dataBinding': component.dataBinding != null
          ? _encodeJson(component.dataBinding!.toMap())
          : null,
      'children': _encodeJson(component.children),
      'parentId': component.parentId,
      'isVisible': component.isVisible ? 1 : 0,
      'isEnabled': component.isEnabled ? 1 : 0,
      'isRequired': component.isRequired ? 1 : 0,
      'zIndex': component.zIndex,
      'events': _encodeJson(component.events),
    });
  }

  static Future<Spreadsheet> _spreadsheetFromMap(
    Map<String, dynamic> map,
  ) async {
    final db = await database;

    // Get sheets
    final sheetMaps = await db.query(
      _sheetsTable,
      where: 'spreadsheetId = ?',
      whereArgs: [map['id']],
      orderBy: 'sheetIndex',
    );

    final sheets = <SpreadsheetSheet>[];
    for (final sheetMap in sheetMaps) {
      final sheet = await _sheetFromMap(sheetMap);
      sheets.add(sheet);
    }

    return Spreadsheet(
      id: map['id'],
      name: map['name'],
      sheets: sheets,
      activeSheetIndex: map['activeSheetIndex'],
      description: map['description'],
      metadata: Map<String, dynamic>.from(_decodeJson(map['metadata']) ?? {}),
      createdAt: DateTime.parse(map['createdAt']),
      lastModified: DateTime.parse(map['lastModified']),
      filePath: map['filePath'],
      isReadOnly: map['isReadOnly'] == 1,
    );
  }

  static Future<SpreadsheetSheet> _sheetFromMap(
    Map<String, dynamic> map,
  ) async {
    final db = await database;

    // Get cells
    final cellMaps = await db.query(
      _cellsTable,
      where: 'sheetId = ?',
      whereArgs: [map['id']],
    );

    final cells = <String, SpreadsheetCell>{};
    for (final cellMap in cellMaps) {
      final cell = _cellFromMap(cellMap);
      cells[cell.cellAddress] = cell;
    }

    return SpreadsheetSheet(
      id: map['id'],
      name: map['name'],
      cells: cells,
      isHidden: map['isHidden'] == 1,
      isProtected: map['isProtected'] == 1,
      protectionPassword: map['protectionPassword'],
      tabColor: map['tabColor'],
      index: map['sheetIndex'],
      defaultRowHeight: map['defaultRowHeight'],
      defaultColumnWidth: map['defaultColumnWidth'],
      rowHeights: Map<int, double>.from(
        (_decodeJson(map['rowHeights']) as Map<String, dynamic>? ?? {}).map(
          (k, v) => MapEntry(int.parse(k), v.toDouble()),
        ),
      ),
      columnWidths: Map<int, double>.from(
        (_decodeJson(map['columnWidths']) as Map<String, dynamic>? ?? {}).map(
          (k, v) => MapEntry(int.parse(k), v.toDouble()),
        ),
      ),
      hiddenRows: Set<int>.from(_decodeJson(map['hiddenRows']) ?? []),
      hiddenColumns: Set<int>.from(_decodeJson(map['hiddenColumns']) ?? []),
      printSettings: Map<String, dynamic>.from(
        _decodeJson(map['printSettings']) ?? {},
      ),
    );
  }

  static SpreadsheetCell _cellFromMap(Map<String, dynamic> map) {
    return SpreadsheetCell(
      id: map['id'],
      row: map['row'],
      column: map['column'],
      rawValue: map['rawValue'] ?? '',
      calculatedValue: _decodeJson(map['calculatedValue']),
      formula: map['formula'],
      dataType: CellDataType.values.firstWhere(
        (e) => e.name == map['dataType'],
      ),
      format: CellFormat.fromMap(
        Map<String, dynamic>.from(_decodeJson(map['format']) ?? {}),
      ),
      comment: map['comment'],
      isLocked: map['isLocked'] == 1,
      isHidden: map['isHidden'] == 1,
      dependentCells: List<String>.from(
        _decodeJson(map['dependentCells']) ?? [],
      ),
      precedentCells: List<String>.from(
        _decodeJson(map['precedentCells']) ?? [],
      ),
      lastModified: DateTime.parse(map['lastModified']),
    );
  }

  static String _encodeJson(dynamic value) {
    if (value == null) return 'null';
    try {
      return jsonEncode(value);
    } catch (e) {
      debugPrint('Error encoding JSON: $e');
      return 'null';
    }
  }

  static dynamic _decodeJson(String? value) {
    if (value == null || value.isEmpty || value == 'null') return null;
    try {
      return jsonDecode(value);
    } catch (e) {
      debugPrint('Error decoding JSON: $e, value: $value');
      return null;
    }
  }
}
