import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_theme.dart';
import '../../services/memo_providers.dart';
import '../../models/todo.dart';

class TodosListScreen extends ConsumerWidget {
  const TodosListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final todosAsync = ref.watch(todosProvider);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Todos'),
        backgroundColor: AppTheme.memoSuiteColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            ref.read(memoSuiteCurrentScreenProvider.notifier).state = MemoSuiteScreen.dashboard;
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              ref.read(selectedTodoProvider.notifier).state = null;
              ref.read(memoSuiteCurrentScreenProvider.notifier).state = MemoSuiteScreen.todoEditor;
            },
          ),
        ],
      ),
      body: todosAsync.when(
        data: (todos) => _buildKanbanBoard(context, ref, todos),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: Colors.red[300]),
              const SizedBox(height: 16),
              Text('Error loading todos: $error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.refresh(todosProvider),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildKanbanBoard(BuildContext context, WidgetRef ref, List<Todo> todos) {
    final todoTodos = todos.where((todo) => todo.status == TodoStatus.todo).toList();
    final inProgressTodos = todos.where((todo) => todo.status == TodoStatus.inProgress).toList();
    final doneTodos = todos.where((todo) => todo.status == TodoStatus.done).toList();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: _buildKanbanColumn(
              context,
              ref,
              'To Do',
              todoTodos,
              TodoStatus.todo,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildKanbanColumn(
              context,
              ref,
              'In Progress',
              inProgressTodos,
              TodoStatus.inProgress,
              Colors.orange,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildKanbanColumn(
              context,
              ref,
              'Done',
              doneTodos,
              TodoStatus.done,
              Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKanbanColumn(
    BuildContext context,
    WidgetRef ref,
    String title,
    List<Todo> todos,
    TodoStatus status,
    Color color,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  _getStatusIcon(status),
                  color: color,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    todos.length.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: todos.isEmpty
                ? _buildEmptyColumn(context, status)
                : ListView.builder(
                    padding: const EdgeInsets.all(8),
                    itemCount: todos.length,
                    itemBuilder: (context, index) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: _buildTodoCard(context, ref, todos[index]),
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyColumn(BuildContext context, TodoStatus status) {
    String message;
    IconData icon;

    switch (status) {
      case TodoStatus.todo:
        message = 'No pending tasks';
        icon = Icons.inbox;
        break;
      case TodoStatus.inProgress:
        message = 'No tasks in progress';
        icon = Icons.work_outline;
        break;
      case TodoStatus.done:
        message = 'No completed tasks';
        icon = Icons.check_circle_outline;
        break;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTodoCard(BuildContext context, WidgetRef ref, Todo todo) {
    final priorityColor = _getPriorityColor(todo.priority);
    final isOverdue = todo.isOverdue;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: isOverdue ? Colors.red : priorityColor.withValues(alpha: 0.3),
          width: isOverdue ? 2 : 1,
        ),
      ),
      child: InkWell(
        onTap: () {
          ref.read(selectedTodoProvider.notifier).state = todo;
          ref.read(memoSuiteCurrentScreenProvider.notifier).state = MemoSuiteScreen.todoView;
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Checkbox(
                    value: todo.isCompleted,
                    onChanged: (value) {
                      final updatedTodo = todo.copyWith(
                        status: value == true ? TodoStatus.done : TodoStatus.todo,
                      );
                      ref.read(todosProvider.notifier).updateTodo(updatedTodo);
                    },
                    activeColor: priorityColor,
                  ),
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: priorityColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      todo.title,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isOverdue ? Colors.red[700] : null,
                        decoration: todo.isCompleted ? TextDecoration.lineThrough : null,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleTodoAction(context, ref, todo, value),
                    itemBuilder: (context) => [
                      const PopupMenuItem(value: 'edit', child: Text('Edit')),
                      const PopupMenuItem(value: 'move', child: Text('Move')),
                      const PopupMenuItem(value: 'delete', child: Text('Delete')),
                    ],
                  ),
                ],
              ),
              if (todo.description.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  todo.description,
                  style: Theme.of(context).textTheme.bodySmall,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              const SizedBox(height: 12),
              if (todo.subTasks.isNotEmpty) ...[
                LinearProgressIndicator(
                  value: todo.progressPercentage,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(priorityColor),
                ),
                const SizedBox(height: 4),
                Text(
                  '${(todo.progressPercentage * 100).toInt()}% complete (${todo.subTasks.where((t) => t.isCompleted).length}/${todo.subTasks.length})',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
              ],
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: priorityColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      todo.priority.displayName,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: priorityColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const Spacer(),
                  if (todo.dueDate != null) ...[
                    Icon(
                      isOverdue ? Icons.warning : Icons.schedule,
                      size: 12,
                      color: isOverdue ? Colors.red : Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _formatDueDate(todo.dueDate!),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: isOverdue ? Colors.red : Colors.grey[600],
                        fontWeight: isOverdue ? FontWeight.w600 : null,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getStatusIcon(TodoStatus status) {
    switch (status) {
      case TodoStatus.todo:
        return Icons.radio_button_unchecked;
      case TodoStatus.inProgress:
        return Icons.work_outline;
      case TodoStatus.done:
        return Icons.check_circle;
    }
  }

  Color _getPriorityColor(TodoPriority priority) {
    switch (priority) {
      case TodoPriority.low:
        return Colors.green;
      case TodoPriority.medium:
        return Colors.orange;
      case TodoPriority.high:
        return Colors.red;
    }
  }

  String _formatDueDate(DateTime dueDate) {
    final now = DateTime.now();
    final difference = dueDate.difference(now);

    if (difference.isNegative) {
      return 'Overdue';
    } else if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Tomorrow';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d';
    } else {
      return '${dueDate.day}/${dueDate.month}';
    }
  }

  void _handleTodoAction(BuildContext context, WidgetRef ref, Todo todo, String action) {
    switch (action) {
      case 'edit':
        ref.read(selectedTodoProvider.notifier).state = todo;
        ref.read(memoSuiteCurrentScreenProvider.notifier).state = MemoSuiteScreen.todoEditor;
        break;
      case 'move':
        _showMoveDialog(context, ref, todo);
        break;
      case 'delete':
        _showDeleteDialog(context, ref, todo);
        break;
    }
  }

  void _showMoveDialog(BuildContext context, WidgetRef ref, Todo todo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Move Todo'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: TodoStatus.values.map((status) => ListTile(
            title: Text(status.displayName),
            leading: Icon(_getStatusIcon(status)),
            selected: todo.status == status,
            onTap: () {
              if (todo.status != status) {
                final updatedTodo = todo.copyWith(status: status);
                ref.read(todosProvider.notifier).updateTodo(updatedTodo);
              }
              Navigator.of(context).pop();
            },
          )).toList(),
        ),
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, WidgetRef ref, Todo todo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Todo'),
        content: Text('Are you sure you want to delete "${todo.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              ref.read(todosProvider.notifier).deleteTodo(todo.id);
              Navigator.of(context).pop();
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
