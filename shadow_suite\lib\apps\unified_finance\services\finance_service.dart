import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../../../core/database/database_initializer.dart';
import '../models/finance_models.dart';

/// Finance Service for managing financial data
class FinanceService {
  static final FinanceService _instance = FinanceService._internal();
  factory FinanceService() => _instance;
  FinanceService._internal();

  // In-memory storage with database persistence
  final List<FinanceAccount> _accounts = [];
  final List<FinanceTransaction> _transactions = [];
  final List<Budget> _budgets = [];
  final List<FinancialGoal> _goals = [];

  // Database instance
  Database? _database;

  // State change notifier
  final ValueNotifier<int> _stateNotifier = ValueNotifier<int>(0);

  // Initialize with sample data and load from database
  Future<void> initialize() async {
    await _loadAccountsFromDatabase();
    await _loadTransactionsFromDatabase();
    await _loadGoalsFromDatabase();
    if (_accounts.isEmpty) {
      _loadSampleData();
    }
  }

  void _loadSampleData() {
    final now = DateTime.now();

    // Sample accounts
    _accounts.addAll([
      FinanceAccount(
        id: '1',
        name: 'Main Checking',
        institution: 'Chase Bank',
        type: AccountType.checking,
        balance: 8450.75,
        color: Colors.blue,
        icon: Icons.account_balance,
        createdAt: now.subtract(const Duration(days: 30)),
        updatedAt: now,
      ),
      FinanceAccount(
        id: '2',
        name: 'Savings Account',
        institution: 'Wells Fargo',
        type: AccountType.savings,
        balance: 15230.50,
        color: Colors.green,
        icon: Icons.savings,
        createdAt: now.subtract(const Duration(days: 25)),
        updatedAt: now,
      ),
      FinanceAccount(
        id: '3',
        name: 'Credit Card',
        institution: 'Capital One',
        type: AccountType.credit,
        balance: -1250.25,
        color: Colors.red,
        icon: Icons.credit_card,
        createdAt: now.subtract(const Duration(days: 20)),
        updatedAt: now,
      ),
    ]);

    // Sample transactions
    _transactions.addAll([
      FinanceTransaction(
        id: '1',
        accountId: '1',
        type: TransactionType.expense,
        category: TransactionCategory.food,
        amount: 45.67,
        description: 'Grocery Shopping',
        date: now.subtract(const Duration(days: 1)),
        createdAt: now.subtract(const Duration(days: 1)),
        updatedAt: now.subtract(const Duration(days: 1)),
      ),
      FinanceTransaction(
        id: '2',
        accountId: '1',
        type: TransactionType.income,
        category: TransactionCategory.salary,
        amount: 3500.00,
        description: 'Monthly Salary',
        date: now.subtract(const Duration(days: 5)),
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now.subtract(const Duration(days: 5)),
      ),
      FinanceTransaction(
        id: '3',
        accountId: '3',
        type: TransactionType.expense,
        category: TransactionCategory.entertainment,
        amount: 89.99,
        description: 'Netflix Subscription',
        date: now.subtract(const Duration(days: 3)),
        createdAt: now.subtract(const Duration(days: 3)),
        updatedAt: now.subtract(const Duration(days: 3)),
      ),
    ]);

    // Sample budgets
    _budgets.addAll([
      Budget(
        id: '1',
        name: 'Food Budget',
        category: TransactionCategory.food,
        budgetAmount: 500.00,
        spentAmount: 245.67,
        startDate: DateTime(now.year, now.month, 1),
        endDate: DateTime(now.year, now.month + 1, 0),
        createdAt: now.subtract(const Duration(days: 10)),
        updatedAt: now,
      ),
      Budget(
        id: '2',
        name: 'Entertainment Budget',
        category: TransactionCategory.entertainment,
        budgetAmount: 200.00,
        spentAmount: 89.99,
        startDate: DateTime(now.year, now.month, 1),
        endDate: DateTime(now.year, now.month + 1, 0),
        createdAt: now.subtract(const Duration(days: 10)),
        updatedAt: now,
      ),
    ]);

    // Sample goals
    _goals.addAll([
      FinancialGoal(
        id: '1',
        name: 'Emergency Fund',
        description: 'Build emergency fund for 6 months expenses',
        targetAmount: 20000.00,
        currentAmount: 15230.50,
        targetDate: DateTime(now.year + 1, now.month, now.day),
        createdAt: now.subtract(const Duration(days: 60)),
        updatedAt: now,
      ),
      FinancialGoal(
        id: '2',
        name: 'Vacation Fund',
        description: 'Save for summer vacation',
        targetAmount: 5000.00,
        currentAmount: 1200.00,
        targetDate: DateTime(now.year, 6, 1),
        createdAt: now.subtract(const Duration(days: 30)),
        updatedAt: now,
      ),
    ]);
  }

  // Account operations
  List<FinanceAccount> getAccounts() => List.unmodifiable(_accounts);

  FinanceAccount? getAccount(String id) {
    try {
      return _accounts.firstWhere((account) => account.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<void> addAccount(FinanceAccount account) async {
    try {
      // Add to in-memory list
      _accounts.add(account);

      // Persist to database
      await _saveAccountToDatabase(account);

      // Notify listeners of state change
      _notifyStateChange();
    } catch (e) {
      // Remove from memory if database save failed
      _accounts.removeWhere((a) => a.id == account.id);
      rethrow;
    }
  }

  Future<void> updateAccount(FinanceAccount account) async {
    final index = _accounts.indexWhere((a) => a.id == account.id);
    if (index != -1) {
      _accounts[index] = account;
    }
  }

  Future<void> deleteAccount(String id) async {
    _accounts.removeWhere((account) => account.id == id);
    // Also remove related transactions
    _transactions.removeWhere((transaction) => transaction.accountId == id);
  }

  // Transaction operations
  List<FinanceTransaction> getTransactions() =>
      List.unmodifiable(_transactions);

  List<FinanceTransaction> getTransactionsByAccount(String accountId) {
    return _transactions.where((t) => t.accountId == accountId).toList();
  }

  Future<void> addTransaction(FinanceTransaction transaction) async {
    try {
      // Add to in-memory list
      _transactions.add(transaction);

      // Update account balance
      _updateAccountBalance(transaction);

      // Persist to database
      await _saveTransactionToDatabase(transaction);

      // Notify listeners of state change
      _notifyStateChange();
    } catch (e) {
      // Remove from memory if database save failed
      _transactions.removeWhere((t) => t.id == transaction.id);
      rethrow;
    }
  }

  Future<void> updateTransaction(FinanceTransaction transaction) async {
    final index = _transactions.indexWhere((t) => t.id == transaction.id);
    if (index != -1) {
      final oldTransaction = _transactions[index];
      _transactions[index] = transaction;
      // Revert old transaction effect and apply new one
      _revertAccountBalance(oldTransaction);
      _updateAccountBalance(transaction);
    }
  }

  Future<void> deleteTransaction(String id) async {
    final transaction = _transactions.firstWhere((t) => t.id == id);
    _transactions.removeWhere((t) => t.id == id);
    _revertAccountBalance(transaction);
  }

  void _updateAccountBalance(FinanceTransaction transaction) {
    final accountIndex = _accounts.indexWhere(
      (a) => a.id == transaction.accountId,
    );
    if (accountIndex != -1) {
      final account = _accounts[accountIndex];
      double newBalance = account.balance;

      switch (transaction.type) {
        case TransactionType.income:
          newBalance += transaction.amount;
          break;
        case TransactionType.expense:
          newBalance -= transaction.amount;
          break;
        case TransactionType.transfer:
          newBalance -= transaction.amount;
          // Handle transfer to account if specified
          if (transaction.toAccountId != null) {
            final toAccountIndex = _accounts.indexWhere(
              (a) => a.id == transaction.toAccountId,
            );
            if (toAccountIndex != -1) {
              final toAccount = _accounts[toAccountIndex];
              _accounts[toAccountIndex] = toAccount.copyWith(
                balance: toAccount.balance + transaction.amount,
                updatedAt: DateTime.now(),
              );
            }
          }
          break;
      }

      _accounts[accountIndex] = account.copyWith(
        balance: newBalance,
        updatedAt: DateTime.now(),
      );
    }
  }

  void _revertAccountBalance(FinanceTransaction transaction) {
    final accountIndex = _accounts.indexWhere(
      (a) => a.id == transaction.accountId,
    );
    if (accountIndex != -1) {
      final account = _accounts[accountIndex];
      double newBalance = account.balance;

      switch (transaction.type) {
        case TransactionType.income:
          newBalance -= transaction.amount;
          break;
        case TransactionType.expense:
          newBalance += transaction.amount;
          break;
        case TransactionType.transfer:
          newBalance += transaction.amount;
          if (transaction.toAccountId != null) {
            final toAccountIndex = _accounts.indexWhere(
              (a) => a.id == transaction.toAccountId,
            );
            if (toAccountIndex != -1) {
              final toAccount = _accounts[toAccountIndex];
              _accounts[toAccountIndex] = toAccount.copyWith(
                balance: toAccount.balance - transaction.amount,
                updatedAt: DateTime.now(),
              );
            }
          }
          break;
      }

      _accounts[accountIndex] = account.copyWith(
        balance: newBalance,
        updatedAt: DateTime.now(),
      );
    }
  }

  // Budget operations
  List<Budget> getBudgets() => List.unmodifiable(_budgets);

  Future<void> addBudget(Budget budget) async {
    _budgets.add(budget);
  }

  Future<void> updateBudget(Budget budget) async {
    final index = _budgets.indexWhere((b) => b.id == budget.id);
    if (index != -1) {
      _budgets[index] = budget;
    }
  }

  Future<void> deleteBudget(String id) async {
    _budgets.removeWhere((budget) => budget.id == id);
  }

  // Goal operations
  List<FinancialGoal> getGoals() => List.unmodifiable(_goals);

  Future<void> addGoal(FinancialGoal goal) async {
    try {
      // Add to in-memory list
      _goals.add(goal);

      // Persist to database
      await _saveGoalToDatabase(goal);

      // Notify listeners of state change
      _notifyStateChange();
    } catch (e) {
      // Remove from memory if database save failed
      _goals.removeWhere((g) => g.id == goal.id);
      rethrow;
    }
  }

  Future<void> updateGoal(FinancialGoal goal) async {
    final index = _goals.indexWhere((g) => g.id == goal.id);
    if (index != -1) {
      _goals[index] = goal;
    }
  }

  Future<void> deleteGoal(String id) async {
    _goals.removeWhere((goal) => goal.id == id);
  }

  // Analytics
  double getTotalBalance() {
    return _accounts.fold(0.0, (sum, account) => sum + account.balance);
  }

  double getMonthlyIncome() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    return _transactions
        .where(
          (t) =>
              t.type == TransactionType.income && t.date.isAfter(startOfMonth),
        )
        .fold(0.0, (sum, t) => sum + t.amount);
  }

  double getMonthlyExpenses() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    return _transactions
        .where(
          (t) =>
              t.type == TransactionType.expense && t.date.isAfter(startOfMonth),
        )
        .fold(0.0, (sum, t) => sum + t.amount);
  }

  // Database methods
  Future<Database> _getDatabase() async {
    if (_database == null) {
      await DatabaseInitializer.ensureInitialized();
      final dbPath = await getDatabasesPath();
      final path = join(dbPath, 'finance.db');

      _database = await DatabaseInitializer.safeOpenDatabase(
        path,
        version: 1,
        onCreate: _createTables,
      );
    }
    return _database!;
  }

  Future<void> _createTables(Database db, int version) async {
    await db.execute('''
      CREATE TABLE accounts (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        institution TEXT NOT NULL,
        type TEXT NOT NULL,
        balance REAL NOT NULL,
        currency TEXT NOT NULL,
        color INTEGER NOT NULL,
        icon INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE transactions (
        id TEXT PRIMARY KEY,
        account_id TEXT NOT NULL,
        amount REAL NOT NULL,
        type TEXT NOT NULL,
        category TEXT NOT NULL,
        description TEXT NOT NULL,
        date TEXT NOT NULL,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (account_id) REFERENCES accounts (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE goals (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        target_amount REAL NOT NULL,
        current_amount REAL NOT NULL DEFAULT 0,
        target_date TEXT NOT NULL,
        category TEXT NOT NULL,
        description TEXT,
        color INTEGER NOT NULL,
        icon INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');
  }

  Future<void> _saveAccountToDatabase(FinanceAccount account) async {
    final db = await _getDatabase();
    await db.insert('accounts', {
      'id': account.id,
      'name': account.name,
      'institution': account.institution,
      'type': account.type.name,
      'balance': account.balance,
      'currency': account.currency,
      'color': account.color.toARGB32(),
      'icon': account.icon.codePoint,
      'created_at': account.createdAt.toIso8601String(),
      'updated_at': account.updatedAt.toIso8601String(),
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<void> _saveTransactionToDatabase(
    FinanceTransaction transaction,
  ) async {
    final db = await _getDatabase();
    await db.insert('transactions', {
      'id': transaction.id,
      'account_id': transaction.accountId,
      'amount': transaction.amount,
      'type': transaction.type.name,
      'category': transaction.category.name,
      'description': transaction.description,
      'date': transaction.date.toIso8601String(),
      'notes': transaction.notes,
      'created_at': transaction.createdAt.toIso8601String(),
      'updated_at': transaction.updatedAt.toIso8601String(),
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<void> _saveGoalToDatabase(FinancialGoal goal) async {
    final db = await _getDatabase();
    await db.insert('goals', {
      'id': goal.id,
      'name': goal.name,
      'target_amount': goal.targetAmount,
      'current_amount': goal.currentAmount,
      'target_date': goal.targetDate.toIso8601String(),
      'category': 'savings', // Default category
      'description': goal.description,
      'color': Colors.blue.toARGB32(), // Default color
      'icon': Icons.flag.codePoint, // Default icon
      'created_at': goal.createdAt.toIso8601String(),
      'updated_at': goal.updatedAt.toIso8601String(),
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<void> _loadAccountsFromDatabase() async {
    try {
      final db = await _getDatabase();
      final List<Map<String, dynamic>> maps = await db.query('accounts');

      _accounts.clear();
      for (final map in maps) {
        final account = FinanceAccount(
          id: map['id'],
          name: map['name'],
          institution: map['institution'],
          type: AccountType.values.firstWhere((t) => t.name == map['type']),
          balance: map['balance'],
          currency: map['currency'],
          icon: Icons.account_balance,
          createdAt: DateTime.parse(map['created_at']),
          updatedAt: DateTime.parse(map['updated_at']),
          isActive: map['is_active'] == 1,
        );
        _accounts.add(account);
      }

      _notifyStateChange();
    } catch (e) {
      debugPrint('Error loading accounts from database: $e');
    }
  }

  Future<void> _loadTransactionsFromDatabase() async {
    try {
      final db = await _getDatabase();
      final List<Map<String, dynamic>> maps = await db.query('transactions');

      _transactions.clear();
      for (final map in maps) {
        final transaction = FinanceTransaction(
          id: map['id'],
          accountId: map['account_id'],
          amount: map['amount'],
          type: TransactionType.values.firstWhere((t) => t.name == map['type']),
          category: TransactionCategory.values.firstWhere(
            (c) => c.name == map['category'],
          ),
          description: map['description'],
          date: DateTime.parse(map['date']),
          notes: map['notes'],
          createdAt: DateTime.parse(map['created_at']),
          updatedAt: DateTime.parse(map['updated_at']),
        );
        _transactions.add(transaction);
      }

      _notifyStateChange();
    } catch (e) {
      debugPrint('Error loading transactions from database: $e');
    }
  }

  Future<void> _loadGoalsFromDatabase() async {
    try {
      final db = await _getDatabase();
      final List<Map<String, dynamic>> maps = await db.query('goals');

      _goals.clear();
      for (final map in maps) {
        final goal = FinancialGoal(
          id: map['id'],
          name: map['name'],
          targetAmount: map['target_amount'],
          currentAmount: map['current_amount'],
          targetDate: DateTime.parse(map['target_date']),
          description: map['description'],
          createdAt: DateTime.parse(map['created_at']),
          updatedAt: DateTime.parse(map['updated_at']),
        );
        _goals.add(goal);
      }

      _notifyStateChange();
    } catch (e) {
      debugPrint('Error loading goals from database: $e');
    }
  }

  void _notifyStateChange() {
    _stateNotifier.value++;
  }

  ValueNotifier<int> get stateNotifier => _stateNotifier;
}

// Providers
final financeServiceProvider = Provider<FinanceService>((ref) {
  final service = FinanceService();
  service.initialize();
  return service;
});

final accountsProvider = Provider<List<FinanceAccount>>((ref) {
  final service = ref.watch(financeServiceProvider);
  return service.getAccounts();
});

final transactionsProvider = Provider<List<FinanceTransaction>>((ref) {
  final service = ref.watch(financeServiceProvider);
  return service.getTransactions();
});

final budgetsProvider = Provider<List<Budget>>((ref) {
  final service = ref.watch(financeServiceProvider);
  return service.getBudgets();
});

final goalsProvider = Provider<List<FinancialGoal>>((ref) {
  final service = ref.watch(financeServiceProvider);
  return service.getGoals();
});
