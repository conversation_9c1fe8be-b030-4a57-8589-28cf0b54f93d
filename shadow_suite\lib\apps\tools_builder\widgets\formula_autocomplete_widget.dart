import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/formula_autocomplete_service.dart';

// Formula Auto-Complete Widget with dropdown suggestions
class FormulaAutoCompleteWidget extends StatefulWidget {
  final TextEditingController controller;
  final Function(String) onFormulaChanged;
  final Function(String) onFormulaSubmitted;
  final Function()? onCellReferenceMode;
  final bool isInFormulaMode;
  final String? currentCellAddress;

  const FormulaAutoCompleteWidget({
    super.key,
    required this.controller,
    required this.onFormulaChanged,
    required this.onFormulaSubmitted,
    this.onCellReferenceMode,
    this.isInFormulaMode = false,
    this.currentCellAddress,
  });

  @override
  State<FormulaAutoCompleteWidget> createState() => _FormulaAutoCompleteWidgetState();
}

class _FormulaAutoCompleteWidgetState extends State<FormulaAutoCompleteWidget> {
  final FocusNode _focusNode = FocusNode();
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  
  List<FunctionDefinition> _suggestions = [];
  int _selectedIndex = 0;
  bool _showSuggestions = false;
  String _currentInput = '';
  int _cursorPosition = 0;
  FunctionSignatureInfo? _currentSignature;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    _focusNode.removeListener(_onFocusChanged);
    _focusNode.dispose();
    _hideOverlay();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: widget.isInFormulaMode ? Colors.blue : Colors.grey[300]!,
            width: widget.isInFormulaMode ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(4),
          color: widget.isInFormulaMode ? Colors.blue[50] : Colors.white,
        ),
        child: Row(
          children: [
            // Formula indicator
            if (widget.isInFormulaMode)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.blue[100],
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(4),
                    bottomLeft: Radius.circular(4),
                  ),
                ),
                child: const Icon(
                  Icons.functions,
                  size: 16,
                  color: Colors.blue,
                ),
              ),
            
            // Cell address indicator
            if (widget.currentCellAddress != null)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                child: Text(
                  widget.currentCellAddress!,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            
            // Formula input field
            Expanded(
              child: TextField(
                controller: widget.controller,
                focusNode: _focusNode,
                decoration: InputDecoration(
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  hintText: widget.isInFormulaMode 
                      ? 'Enter formula (e.g., =SUM(A1:A10))'
                      : 'Enter value or formula',
                  hintStyle: TextStyle(
                    color: Colors.grey[500],
                    fontSize: 12,
                  ),
                ),
                style: TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 14,
                  color: widget.isInFormulaMode ? Colors.blue[800] : Colors.black,
                ),
                onChanged: (value) {
                  _cursorPosition = widget.controller.selection.baseOffset;
                  widget.onFormulaChanged(value);
                },
                onSubmitted: widget.onFormulaSubmitted,
                onTap: () {
                  _cursorPosition = widget.controller.selection.baseOffset;
                  _updateSuggestions();
                },
              ),
            ),
            
            // Cell reference mode button
            if (widget.isInFormulaMode && widget.onCellReferenceMode != null)
              IconButton(
                onPressed: widget.onCellReferenceMode,
                icon: const Icon(Icons.grid_on, size: 16),
                tooltip: 'Click cells to add references',
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
              ),
          ],
        ),
      ),
    );
  }

  void _onTextChanged() {
    final text = widget.controller.text;
    _currentInput = text;
    _cursorPosition = widget.controller.selection.baseOffset;
    
    // Update function signature if we're inside a function
    _updateFunctionSignature();
    
    // Update suggestions if in formula mode
    if (widget.isInFormulaMode && text.startsWith('=')) {
      _updateSuggestions();
    } else {
      _hideSuggestions();
    }
  }

  void _onFocusChanged() {
    if (!_focusNode.hasFocus) {
      _hideSuggestions();
    }
  }

  void _updateSuggestions() {
    if (!widget.isInFormulaMode || !_currentInput.startsWith('=')) {
      _hideSuggestions();
      return;
    }

    // Extract the current function being typed
    final formulaContent = _currentInput.substring(1);
    final currentWord = _getCurrentWord(formulaContent, _cursorPosition - 1);
    
    if (currentWord.isEmpty) {
      _hideSuggestions();
      return;
    }

    final suggestions = FormulaAutoCompleteService.getSuggestions(currentWord);
    
    setState(() {
      _suggestions = suggestions;
      _selectedIndex = 0;
      _showSuggestions = suggestions.isNotEmpty;
    });

    if (_showSuggestions) {
      _showOverlay();
    } else {
      _hideOverlay();
    }
  }

  void _updateFunctionSignature() {
    if (!widget.isInFormulaMode || !_currentInput.startsWith('=')) {
      setState(() {
        _currentSignature = null;
      });
      return;
    }

    final signature = FormulaAutoCompleteService.getFunctionSignature(
      _currentInput, 
      _cursorPosition
    );
    
    setState(() {
      _currentSignature = signature;
    });
  }

  String _getCurrentWord(String text, int position) {
    if (position < 0 || position >= text.length) return '';
    
    int start = position;
    while (start > 0 && RegExp(r'[A-Z]').hasMatch(text[start - 1])) {
      start--;
    }
    
    int end = position;
    while (end < text.length && RegExp(r'[A-Z]').hasMatch(text[end])) {
      end++;
    }
    
    return text.substring(start, end);
  }

  void _showOverlay() {
    _hideOverlay();
    
    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        width: 400,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: const Offset(0, 40),
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              constraints: const BoxConstraints(maxHeight: 300),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Function signature display
                  if (_currentSignature != null)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue[50],
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(8),
                          topRight: Radius.circular(8),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _currentSignature!.function.syntax,
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _currentSignature!.function.description,
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.grey[600],
                            ),
                          ),
                          if (_currentSignature!.currentParameterName.isNotEmpty) ...[
                            const SizedBox(height: 4),
                            Text(
                              'Current parameter: ${_currentSignature!.currentParameterName}',
                              style: const TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  
                  // Suggestions list
                  Flexible(
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: _suggestions.length,
                      itemBuilder: (context, index) {
                        final suggestion = _suggestions[index];
                        final isSelected = index == _selectedIndex;
                        
                        return Container(
                          color: isSelected ? Colors.blue[100] : null,
                          child: ListTile(
                            dense: true,
                            leading: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: _getCategoryColor(suggestion.category),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                suggestion.category.substring(0, 1),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            title: Text(
                              suggestion.name,
                              style: const TextStyle(
                                fontFamily: 'monospace',
                                fontWeight: FontWeight.bold,
                                fontSize: 13,
                              ),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  suggestion.syntax,
                                  style: const TextStyle(
                                    fontFamily: 'monospace',
                                    fontSize: 11,
                                    color: Colors.blue,
                                  ),
                                ),
                                Text(
                                  suggestion.description,
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.grey[600],
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                            onTap: () => _selectSuggestion(suggestion),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
    
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _hideSuggestions() {
    setState(() {
      _showSuggestions = false;
      _suggestions.clear();
    });
    _hideOverlay();
  }

  void _selectSuggestion(FunctionDefinition suggestion) {
    final currentText = widget.controller.text;
    final cursorPos = _cursorPosition;
    
    // Find the start of the current word
    int wordStart = cursorPos - 1;
    while (wordStart > 0 && RegExp(r'[A-Z]').hasMatch(currentText[wordStart - 1])) {
      wordStart--;
    }
    
    // Replace the current word with the selected function
    final beforeWord = currentText.substring(0, wordStart);
    final afterWord = currentText.substring(cursorPos);
    final functionText = '${suggestion.name}(';
    
    final newText = beforeWord + functionText + afterWord;
    final newCursorPos = wordStart + functionText.length;
    
    widget.controller.text = newText;
    widget.controller.selection = TextSelection.collapsed(offset: newCursorPos);
    
    _hideSuggestions();
    widget.onFormulaChanged(newText);
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Math':
        return Colors.green;
      case 'Statistical':
        return Colors.blue;
      case 'Logical':
        return Colors.orange;
      case 'Text':
        return Colors.purple;
      case 'Date':
        return Colors.red;
      case 'Lookup':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  // Handle keyboard navigation
  bool _handleKeyEvent(KeyEvent event) {
    if (!_showSuggestions || _suggestions.isEmpty) return false;
    
    if (event is KeyDownEvent) {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.arrowDown:
          setState(() {
            _selectedIndex = (_selectedIndex + 1) % _suggestions.length;
          });
          return true;
        case LogicalKeyboardKey.arrowUp:
          setState(() {
            _selectedIndex = (_selectedIndex - 1 + _suggestions.length) % _suggestions.length;
          });
          return true;
        case LogicalKeyboardKey.enter:
        case LogicalKeyboardKey.tab:
          if (_selectedIndex < _suggestions.length) {
            _selectSuggestion(_suggestions[_selectedIndex]);
          }
          return true;
        case LogicalKeyboardKey.escape:
          _hideSuggestions();
          return true;
      }
    }
    
    return false;
  }
}
