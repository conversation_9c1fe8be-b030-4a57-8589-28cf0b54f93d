import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/excel_app_tool.dart';
import 'excel_to_app_main.dart';

class ExcelAppToolCard extends StatelessWidget {
  final ExcelAppTool tool;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDuplicate;
  final VoidCallback? onDelete;
  final VoidCallback? onShare;

  const ExcelAppToolCard({
    super.key,
    required this.tool,
    this.onTap,
    this.onEdit,
    this.onDuplicate,
    this.onDelete,
    this.onShare,
  });

  @override
  Widget build(BuildContext context) {
    return ExcelToAppCard(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context),
          const SizedBox(height: 12),
          _buildThumbnail(context),
          const SizedBox(height: 12),
          _buildTitle(context),
          const SizedBox(height: 4),
          _buildDescription(context),
          const Spacer(),
          _buildFooter(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        _buildSecurityIcon(),
        const Spacer(),
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'run',
              child: Row(
                children: [
                  Icon(Icons.play_arrow, size: 18),
                  SizedBox(width: 8),
                  Text('Run'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 18),
                  SizedBox(width: 8),
                  Text('Edit'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'duplicate',
              child: Row(
                children: [
                  Icon(Icons.copy, size: 18),
                  SizedBox(width: 8),
                  Text('Duplicate'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share, size: 18),
                  SizedBox(width: 8),
                  Text('Share'),
                ],
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 18, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Delete', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
          child: const Icon(
            Icons.more_vert,
            color: Color(0xFF7F8C8D),
          ),
        ),
      ],
    );
  }

  Widget _buildSecurityIcon() {
    IconData icon;
    Color color;
    String tooltip;

    switch (tool.securityType) {
      case SecurityType.pin:
        icon = Icons.lock;
        color = const Color(0xFFF39C12);
        tooltip = 'PIN Protected';
        break;
      case SecurityType.biometric:
        icon = Icons.fingerprint;
        color = const Color(0xFF27AE60);
        tooltip = 'Biometric Protected';
        break;
      case SecurityType.none:
        icon = Icons.lock_open;
        color = const Color(0xFF95A5A6);
        tooltip = 'No Security';
        break;
    }

    return Tooltip(
      message: tooltip,
      child: Icon(
        icon,
        size: 18,
        color: color,
      ),
    );
  }

  Widget _buildThumbnail(BuildContext context) {
    return Container(
      height: 120,
      width: double.infinity,
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE9ECEF)),
      ),
      child: tool.thumbnailPath != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.asset(
                tool.thumbnailPath!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildDefaultThumbnail(),
              ),
            )
          : _buildDefaultThumbnail(),
    );
  }

  Widget _buildDefaultThumbnail() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.table_view,
          size: 32,
          color: const Color(0xFF3498DB).withValues(alpha: 0.5),
        ),
        const SizedBox(height: 8),
        Text(
          '${tool.spreadsheet.columns}×${tool.spreadsheet.rows}',
          style: const TextStyle(
            color: Color(0xFF7F8C8D),
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '${tool.uiComponents.length} components',
          style: const TextStyle(
            color: Color(0xFF7F8C8D),
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Text(
      tool.name,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.bold,
        color: const Color(0xFF2C3E50),
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildDescription(BuildContext context) {
    if (tool.description.isEmpty) {
      return Text(
        'No description',
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: const Color(0xFFBDC3C7),
          fontStyle: FontStyle.italic,
        ),
      );
    }

    return Text(
      tool.description,
      style: Theme.of(context).textTheme.bodySmall?.copyWith(
        color: const Color(0xFF7F8C8D),
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Divider(height: 16),
        Row(
          children: [
            Icon(
              Icons.access_time,
              size: 14,
              color: const Color(0xFF95A5A6),
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                'Modified ${_formatDate(tool.lastModified)}',
                style: const TextStyle(
                  color: Color(0xFF95A5A6),
                  fontSize: 11,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Icon(
              Icons.calendar_today,
              size: 14,
              color: const Color(0xFF95A5A6),
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                'Created ${_formatDate(tool.createdAt)}',
                style: const TextStyle(
                  color: Color(0xFF95A5A6),
                  fontSize: 11,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        if (difference.inMinutes == 0) {
          return 'just now';
        }
        return '${difference.inMinutes}m ago';
      }
      return '${difference.inHours}h ago';
    } else if (difference.inDays == 1) {
      return 'yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return DateFormat('MMM d, y').format(date);
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'run':
        onTap?.call();
        break;
      case 'edit':
        onEdit?.call();
        break;
      case 'duplicate':
        onDuplicate?.call();
        break;
      case 'share':
        onShare?.call();
        break;
      case 'delete':
        onDelete?.call();
        break;
    }
  }
}

// Statistics card for dashboard
class ExcelAppStatsCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final String? subtitle;

  const ExcelAppStatsCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return ExcelToAppCard(
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF2C3E50),
                  ),
                ),
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: const Color(0xFF7F8C8D),
                  ),
                ),
                if (subtitle != null)
                  Text(
                    subtitle!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: const Color(0xFF95A5A6),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
