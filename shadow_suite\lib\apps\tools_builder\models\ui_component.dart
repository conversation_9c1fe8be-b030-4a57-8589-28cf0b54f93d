import 'package:uuid/uuid.dart';

enum ComponentType {
  // Basic Input Components
  textInput,
  numberInput,
  dropdown,
  checkbox,
  radioButton,
  button,
  label,
  slider,
  dateInput,
  timeInput,
  textArea,

  // Display Components
  image,
  chart,
  table,
  container,
  divider,
  spacer,

  // Advanced Components
  progressBar,
  colorPicker,
  fileUpload,
  richTextEditor,
  codeEditor,
  map,
  video,
  audio,
  qrCode,
  barcode,
  signature,
  drawing,
  calendar,
  dataTable,
}

enum ComponentAlignment {
  left,
  center,
  right,
  justify,
}

enum ComponentSize {
  small,
  medium,
  large,
  custom,
}

class ComponentStyle {
  final String? backgroundColor;
  final String? textColor;
  final String? borderColor;
  final double? borderWidth;
  final double? borderRadius;
  final String? fontFamily;
  final double? fontSize;
  final bool isBold;
  final bool isItalic;
  final double? padding;
  final double? margin;
  final double? width;
  final double? height;
  final ComponentAlignment alignment;
  final String? boxShadow;

  const ComponentStyle({
    this.backgroundColor,
    this.textColor,
    this.borderColor,
    this.borderWidth,
    this.borderRadius,
    this.fontFamily,
    this.fontSize,
    this.isBold = false,
    this.isItalic = false,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.alignment = ComponentAlignment.left,
    this.boxShadow,
  });

  ComponentStyle copyWith({
    String? backgroundColor,
    String? textColor,
    String? borderColor,
    double? borderWidth,
    double? borderRadius,
    String? fontFamily,
    double? fontSize,
    bool? isBold,
    bool? isItalic,
    double? padding,
    double? margin,
    double? width,
    double? height,
    ComponentAlignment? alignment,
    String? boxShadow,
  }) {
    return ComponentStyle(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      textColor: textColor ?? this.textColor,
      borderColor: borderColor ?? this.borderColor,
      borderWidth: borderWidth ?? this.borderWidth,
      borderRadius: borderRadius ?? this.borderRadius,
      fontFamily: fontFamily ?? this.fontFamily,
      fontSize: fontSize ?? this.fontSize,
      isBold: isBold ?? this.isBold,
      isItalic: isItalic ?? this.isItalic,
      padding: padding ?? this.padding,
      margin: margin ?? this.margin,
      width: width ?? this.width,
      height: height ?? this.height,
      alignment: alignment ?? this.alignment,
      boxShadow: boxShadow ?? this.boxShadow,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'backgroundColor': backgroundColor,
      'textColor': textColor,
      'borderColor': borderColor,
      'borderWidth': borderWidth,
      'borderRadius': borderRadius,
      'fontFamily': fontFamily,
      'fontSize': fontSize,
      'isBold': isBold,
      'isItalic': isItalic,
      'padding': padding,
      'margin': margin,
      'width': width,
      'height': height,
      'alignment': alignment.name,
      'boxShadow': boxShadow,
    };
  }

  factory ComponentStyle.fromMap(Map<String, dynamic> map) {
    return ComponentStyle(
      backgroundColor: map['backgroundColor'],
      textColor: map['textColor'],
      borderColor: map['borderColor'],
      borderWidth: map['borderWidth']?.toDouble(),
      borderRadius: map['borderRadius']?.toDouble(),
      fontFamily: map['fontFamily'],
      fontSize: map['fontSize']?.toDouble(),
      isBold: map['isBold'] ?? false,
      isItalic: map['isItalic'] ?? false,
      padding: map['padding']?.toDouble(),
      margin: map['margin']?.toDouble(),
      width: map['width']?.toDouble(),
      height: map['height']?.toDouble(),
      alignment: ComponentAlignment.values.firstWhere(
        (e) => e.name == map['alignment'],
        orElse: () => ComponentAlignment.left,
      ),
      boxShadow: map['boxShadow'],
    );
  }
}

class DataBinding {
  final String cellAddress;
  final String bindingType; // input, output, bidirectional
  final String? formula;
  final String? validationRule;
  final dynamic defaultValue;

  const DataBinding({
    required this.cellAddress,
    this.bindingType = 'bidirectional',
    this.formula,
    this.validationRule,
    this.defaultValue,
  });

  DataBinding copyWith({
    String? cellAddress,
    String? bindingType,
    String? formula,
    String? validationRule,
    dynamic defaultValue,
  }) {
    return DataBinding(
      cellAddress: cellAddress ?? this.cellAddress,
      bindingType: bindingType ?? this.bindingType,
      formula: formula ?? this.formula,
      validationRule: validationRule ?? this.validationRule,
      defaultValue: defaultValue ?? this.defaultValue,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'cellAddress': cellAddress,
      'bindingType': bindingType,
      'formula': formula,
      'validationRule': validationRule,
      'defaultValue': defaultValue,
    };
  }

  factory DataBinding.fromMap(Map<String, dynamic> map) {
    return DataBinding(
      cellAddress: map['cellAddress'],
      bindingType: map['bindingType'] ?? 'bidirectional',
      formula: map['formula'],
      validationRule: map['validationRule'],
      defaultValue: map['defaultValue'],
    );
  }
}

// Enhanced Cell Binding System for isolated component connections
class CellBinding {
  final String id;
  final String componentId;
  final String cellAddress;
  final String property; // which property of the component to bind (value, text, enabled, etc.)
  final BindingDirection direction;
  final String? transformer; // optional data transformation function
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  CellBinding({
    String? id,
    required this.componentId,
    required this.cellAddress,
    required this.property,
    this.direction = BindingDirection.bidirectional,
    this.transformer,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  CellBinding copyWith({
    String? componentId,
    String? cellAddress,
    String? property,
    BindingDirection? direction,
    String? transformer,
    bool? isActive,
    DateTime? updatedAt,
  }) {
    return CellBinding(
      id: id,
      componentId: componentId ?? this.componentId,
      cellAddress: cellAddress ?? this.cellAddress,
      property: property ?? this.property,
      direction: direction ?? this.direction,
      transformer: transformer ?? this.transformer,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'componentId': componentId,
      'cellAddress': cellAddress,
      'property': property,
      'direction': direction.name,
      'transformer': transformer,
      'isActive': isActive,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory CellBinding.fromMap(Map<String, dynamic> map) {
    return CellBinding(
      id: map['id'],
      componentId: map['componentId'],
      cellAddress: map['cellAddress'],
      property: map['property'],
      direction: BindingDirection.values.firstWhere(
        (e) => e.name == map['direction'],
        orElse: () => BindingDirection.bidirectional,
      ),
      transformer: map['transformer'],
      isActive: map['isActive'] ?? true,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CellBinding && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum BindingDirection {
  input,    // Cell -> Component (read-only)
  output,   // Component -> Cell (write-only)
  bidirectional, // Both directions
}

// Component Isolation System
class ComponentIsolation {
  final String componentId;
  final Set<String> isolatedProperties;
  final Map<String, dynamic> isolatedState;
  final bool preventCascading;
  final DateTime createdAt;

  ComponentIsolation({
    required this.componentId,
    this.isolatedProperties = const {},
    this.isolatedState = const {},
    this.preventCascading = true,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  ComponentIsolation copyWith({
    Set<String>? isolatedProperties,
    Map<String, dynamic>? isolatedState,
    bool? preventCascading,
  }) {
    return ComponentIsolation(
      componentId: componentId,
      isolatedProperties: isolatedProperties ?? this.isolatedProperties,
      isolatedState: isolatedState ?? this.isolatedState,
      preventCascading: preventCascading ?? this.preventCascading,
      createdAt: createdAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'componentId': componentId,
      'isolatedProperties': isolatedProperties.toList(),
      'isolatedState': isolatedState,
      'preventCascading': preventCascading,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  factory ComponentIsolation.fromMap(Map<String, dynamic> map) {
    return ComponentIsolation(
      componentId: map['componentId'],
      isolatedProperties: Set<String>.from(map['isolatedProperties'] ?? []),
      isolatedState: Map<String, dynamic>.from(map['isolatedState'] ?? {}),
      preventCascading: map['preventCascading'] ?? true,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
    );
  }
}

class UIComponent {
  final String id;
  final ComponentType type;
  final String label;
  final String? placeholder;
  final String? helpText;
  final double x;
  final double y;
  final ComponentStyle style;
  final Map<String, dynamic> properties;
  final DataBinding? dataBinding;
  final List<String> children;
  final String? parentId;
  final bool isVisible;
  final bool isEnabled;
  final bool isRequired;
  final int zIndex;
  final Map<String, String> events;

  UIComponent({
    String? id,
    required this.type,
    this.label = '',
    this.placeholder,
    this.helpText,
    this.x = 0,
    this.y = 0,
    this.style = const ComponentStyle(),
    this.properties = const {},
    this.dataBinding,
    this.children = const [],
    this.parentId,
    this.isVisible = true,
    this.isEnabled = true,
    this.isRequired = false,
    this.zIndex = 0,
    this.events = const {},
  }) : id = id ?? const Uuid().v4();

  UIComponent copyWith({
    ComponentType? type,
    String? label,
    String? placeholder,
    String? helpText,
    double? x,
    double? y,
    ComponentStyle? style,
    Map<String, dynamic>? properties,
    DataBinding? dataBinding,
    List<String>? children,
    String? parentId,
    bool? isVisible,
    bool? isEnabled,
    bool? isRequired,
    int? zIndex,
    Map<String, String>? events,
  }) {
    return UIComponent(
      id: id,
      type: type ?? this.type,
      label: label ?? this.label,
      placeholder: placeholder ?? this.placeholder,
      helpText: helpText ?? this.helpText,
      x: x ?? this.x,
      y: y ?? this.y,
      style: style ?? this.style,
      properties: properties ?? this.properties,
      dataBinding: dataBinding ?? this.dataBinding,
      children: children ?? this.children,
      parentId: parentId ?? this.parentId,
      isVisible: isVisible ?? this.isVisible,
      isEnabled: isEnabled ?? this.isEnabled,
      isRequired: isRequired ?? this.isRequired,
      zIndex: zIndex ?? this.zIndex,
      events: events ?? this.events,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.name,
      'label': label,
      'placeholder': placeholder,
      'helpText': helpText,
      'x': x,
      'y': y,
      'style': style.toMap(),
      'properties': properties,
      'dataBinding': dataBinding?.toMap(),
      'children': children,
      'parentId': parentId,
      'isVisible': isVisible,
      'isEnabled': isEnabled,
      'isRequired': isRequired,
      'zIndex': zIndex,
      'events': events,
    };
  }

  factory UIComponent.fromMap(Map<String, dynamic> map) {
    return UIComponent(
      id: map['id'],
      type: ComponentType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => ComponentType.label,
      ),
      label: map['label'] ?? '',
      placeholder: map['placeholder'],
      helpText: map['helpText'],
      x: map['x']?.toDouble() ?? 0,
      y: map['y']?.toDouble() ?? 0,
      style: ComponentStyle.fromMap(map['style'] ?? {}),
      properties: Map<String, dynamic>.from(map['properties'] ?? {}),
      dataBinding: map['dataBinding'] != null 
          ? DataBinding.fromMap(map['dataBinding']) 
          : null,
      children: List<String>.from(map['children'] ?? []),
      parentId: map['parentId'],
      isVisible: map['isVisible'] ?? true,
      isEnabled: map['isEnabled'] ?? true,
      isRequired: map['isRequired'] ?? false,
      zIndex: map['zIndex'] ?? 0,
      events: Map<String, String>.from(map['events'] ?? {}),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UIComponent && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
