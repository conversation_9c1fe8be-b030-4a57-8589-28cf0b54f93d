import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sqflite/sqflite.dart';
import '../models/account.dart';
import '../models/transaction.dart';
import '../models/budget.dart';
import '../models/goal.dart';
import '../models/category.dart';
import 'money_flow_database_service.dart';
import 'goals_service.dart';
import 'categories_service.dart';

// Database initialization provider
final databaseProvider = FutureProvider<Database>((ref) async {
  return await MoneyFlowDatabaseService.database;
});

// Account Providers
final accountsProvider = StateNotifierProvider<AccountsNotifier, AsyncValue<List<Account>>>((ref) {
  return AccountsNotifier(ref);
});

class AccountsNotifier extends StateNotifier<AsyncValue<List<Account>>> {
  final Ref ref;

  AccountsNotifier(this.ref) : super(const AsyncValue.loading()) {
    _initializeAndLoad();
  }

  Future<void> _initializeAndLoad() async {
    try {
      // Wait for database to be initialized
      await ref.read(databaseProvider.future);
      await loadAccounts();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> loadAccounts() async {
    try {
      state = const AsyncValue.loading();
      final accounts = await MoneyFlowDatabaseService.getAllAccounts();
      state = AsyncValue.data(accounts);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addAccount(Account account) async {
    try {
      await MoneyFlowDatabaseService.insertAccount(account);
      await loadAccounts();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateAccount(Account account) async {
    try {
      await MoneyFlowDatabaseService.updateAccount(account);
      await loadAccounts();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteAccount(String id) async {
    try {
      await MoneyFlowDatabaseService.deleteAccount(id);
      await loadAccounts();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<Account?> getAccountById(String id) async {
    try {
      return await MoneyFlowDatabaseService.getAccountById(id);
    } catch (error) {
      return null;
    }
  }
}

// Transaction Providers
final transactionsProvider = StateNotifierProvider<TransactionsNotifier, AsyncValue<List<MoneyTransaction>>>((ref) {
  return TransactionsNotifier(ref);
});

class TransactionsNotifier extends StateNotifier<AsyncValue<List<MoneyTransaction>>> {
  final Ref ref;

  TransactionsNotifier(this.ref) : super(const AsyncValue.loading()) {
    _initializeAndLoad();
  }

  Future<void> _initializeAndLoad() async {
    try {
      // Ensure database is initialized
      await MoneyFlowDatabaseService.database;
      await loadTransactions();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> loadTransactions() async {
    try {
      state = const AsyncValue.loading();
      final transactions = await MoneyFlowDatabaseService.getAllTransactions();
      state = AsyncValue.data(transactions);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addTransaction(MoneyTransaction transaction) async {
    try {
      await MoneyFlowDatabaseService.insertTransaction(transaction);
      await loadTransactions();
      // Refresh accounts to update balances
      ref.read(accountsProvider.notifier).loadAccounts();
      // Refresh budgets to update spending
      ref.read(budgetsProvider.notifier).loadBudgets();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateTransaction(MoneyTransaction transaction) async {
    try {
      await MoneyFlowDatabaseService.updateTransaction(transaction);
      await loadTransactions();
      ref.read(accountsProvider.notifier).loadAccounts();
      ref.read(budgetsProvider.notifier).loadBudgets();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteTransaction(String id) async {
    try {
      await MoneyFlowDatabaseService.deleteTransaction(id);
      await loadTransactions();
      ref.read(accountsProvider.notifier).loadAccounts();
      ref.read(budgetsProvider.notifier).loadBudgets();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<List<MoneyTransaction>> getTransactionsByAccount(String accountId) async {
    try {
      return await MoneyFlowDatabaseService.getTransactionsByAccount(accountId);
    } catch (error) {
      return [];
    }
  }
}

// Budget Providers
final budgetsProvider = StateNotifierProvider<BudgetsNotifier, AsyncValue<List<Budget>>>((ref) {
  return BudgetsNotifier(ref);
});

class BudgetsNotifier extends StateNotifier<AsyncValue<List<Budget>>> {
  final Ref ref;

  BudgetsNotifier(this.ref) : super(const AsyncValue.loading()) {
    _initializeAndLoad();
  }

  Future<void> _initializeAndLoad() async {
    try {
      // Wait for database to be initialized
      await ref.read(databaseProvider.future);
      await loadBudgets();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> loadBudgets() async {
    try {
      state = const AsyncValue.loading();
      final budgets = await MoneyFlowDatabaseService.getAllBudgets();
      state = AsyncValue.data(budgets);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addBudget(Budget budget) async {
    try {
      await MoneyFlowDatabaseService.insertBudget(budget);
      await loadBudgets();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateBudget(Budget budget) async {
    try {
      await MoneyFlowDatabaseService.updateBudget(budget);
      await loadBudgets();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteBudget(String id) async {
    try {
      await MoneyFlowDatabaseService.deleteBudget(id);
      await loadBudgets();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<List<Budget>> getActiveBudgets() async {
    try {
      return await MoneyFlowDatabaseService.getActiveBudgets();
    } catch (error) {
      return [];
    }
  }
}

// Statistics Providers
final moneyFlowStatisticsProvider = StateNotifierProvider<MoneyFlowStatisticsNotifier, AsyncValue<Map<String, int>>>((ref) {
  return MoneyFlowStatisticsNotifier(ref);
});

class MoneyFlowStatisticsNotifier extends StateNotifier<AsyncValue<Map<String, int>>> {
  final Ref ref;

  MoneyFlowStatisticsNotifier(this.ref) : super(const AsyncValue.loading()) {
    _initializeAndLoad();

    // Listen to data changes and refresh statistics
    ref.listen(accountsProvider, (previous, next) {
      if (next.hasValue) loadStatistics();
    });
    ref.listen(transactionsProvider, (previous, next) {
      if (next.hasValue) loadStatistics();
    });
    ref.listen(budgetsProvider, (previous, next) {
      if (next.hasValue) loadStatistics();
    });
  }

  Future<void> _initializeAndLoad() async {
    try {
      // Wait for database to be initialized
      await ref.read(databaseProvider.future);
      await loadStatistics();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> loadStatistics() async {
    try {
      final stats = await MoneyFlowDatabaseService.getStatistics();
      state = AsyncValue.data(stats);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

final accountBalancesProvider = StateNotifierProvider<AccountBalancesNotifier, AsyncValue<Map<String, double>>>((ref) {
  return AccountBalancesNotifier(ref);
});

class AccountBalancesNotifier extends StateNotifier<AsyncValue<Map<String, double>>> {
  final Ref ref;

  AccountBalancesNotifier(this.ref) : super(const AsyncValue.loading()) {
    _initializeAndLoad();

    // Listen to account changes and refresh balances
    ref.listen(accountsProvider, (previous, next) {
      if (next.hasValue) loadBalances();
    });
    ref.listen(transactionsProvider, (previous, next) {
      if (next.hasValue) loadBalances();
    });
  }

  Future<void> _initializeAndLoad() async {
    try {
      // Wait for database to be initialized
      await ref.read(databaseProvider.future);
      await loadBalances();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> loadBalances() async {
    try {
      final balances = await MoneyFlowDatabaseService.getAccountBalances();
      state = AsyncValue.data(balances);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

final monthlySpendingProvider = StateNotifierProvider<MonthlySpendingNotifier, AsyncValue<Map<String, double>>>((ref) {
  return MonthlySpendingNotifier(ref);
});

class MonthlySpendingNotifier extends StateNotifier<AsyncValue<Map<String, double>>> {
  final Ref ref;

  MonthlySpendingNotifier(this.ref) : super(const AsyncValue.loading()) {
    _initializeAndLoad();

    // Listen to transaction changes and refresh spending
    ref.listen(transactionsProvider, (previous, next) {
      if (next.hasValue) loadSpending();
    });
  }

  Future<void> _initializeAndLoad() async {
    try {
      // Wait for database to be initialized
      await ref.read(databaseProvider.future);
      await loadSpending();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> loadSpending() async {
    try {
      final spending = await MoneyFlowDatabaseService.getMonthlySpending();
      state = AsyncValue.data(spending);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

final recentTransactionsProvider = StateNotifierProvider<RecentTransactionsNotifier, AsyncValue<List<MoneyTransaction>>>((ref) {
  return RecentTransactionsNotifier(ref);
});

class RecentTransactionsNotifier extends StateNotifier<AsyncValue<List<MoneyTransaction>>> {
  final Ref ref;

  RecentTransactionsNotifier(this.ref) : super(const AsyncValue.loading()) {
    _initializeAndLoad();

    // Listen to transaction changes and refresh recent transactions
    ref.listen(transactionsProvider, (previous, next) {
      if (next.hasValue) loadRecentTransactions();
    });
  }

  Future<void> _initializeAndLoad() async {
    try {
      // Wait for database to be initialized
      await ref.read(databaseProvider.future);
      await loadRecentTransactions();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> loadRecentTransactions() async {
    try {
      final transactions = await MoneyFlowDatabaseService.getRecentTransactions(limit: 10);
      state = AsyncValue.data(transactions);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Navigation Providers
final moneyFlowCurrentScreenProvider = StateProvider<MoneyFlowScreen>((ref) => MoneyFlowScreen.dashboard);

enum MoneyFlowScreen {
  dashboard,
  accounts,
  accountDetail,
  accountForm,
  transactions,
  transactionDetail,
  transactionForm,
  budgets,
  budgetDetail,
  budgetForm,
  reports,
}

// Goals Providers
final goalsProvider = StateNotifierProvider<GoalsNotifier, AsyncValue<List<Goal>>>((ref) {
  return GoalsNotifier(ref);
});

class GoalsNotifier extends StateNotifier<AsyncValue<List<Goal>>> {
  final Ref ref;

  GoalsNotifier(this.ref) : super(const AsyncValue.loading()) {
    _initializeAndLoad();
  }

  Future<void> _initializeAndLoad() async {
    try {
      // Wait for database to be initialized
      await ref.read(databaseProvider.future);
      await loadGoals();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> loadGoals() async {
    try {
      state = const AsyncValue.loading();
      final goals = await GoalsService.getAllGoals();
      state = AsyncValue.data(goals);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addGoal(Goal goal) async {
    try {
      await GoalsService.insertGoal(goal);
      await loadGoals();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateGoal(Goal goal) async {
    try {
      await GoalsService.updateGoal(goal);
      await loadGoals();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteGoal(String id) async {
    try {
      await GoalsService.deleteGoal(id);
      await loadGoals();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateGoalProgress(String goalId, double amount) async {
    try {
      await GoalsService.updateGoalProgress(goalId, amount);
      await loadGoals();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addToGoalProgress(String goalId, double amount) async {
    try {
      await GoalsService.addToGoalProgress(goalId, amount);
      await loadGoals();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

final goalStatisticsProvider = StateNotifierProvider<GoalStatisticsNotifier, AsyncValue<Map<String, dynamic>>>((ref) {
  return GoalStatisticsNotifier(ref);
});

class GoalStatisticsNotifier extends StateNotifier<AsyncValue<Map<String, dynamic>>> {
  final Ref ref;

  GoalStatisticsNotifier(this.ref) : super(const AsyncValue.loading()) {
    _initializeAndLoad();

    // Listen to goal changes and refresh statistics
    ref.listen(goalsProvider, (previous, next) {
      if (next.hasValue) loadStatistics();
    });
  }

  Future<void> _initializeAndLoad() async {
    try {
      // Wait for database to be initialized
      await ref.read(databaseProvider.future);
      await loadStatistics();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> loadStatistics() async {
    try {
      final stats = await GoalsService.getGoalStatistics();
      state = AsyncValue.data(stats);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Categories Providers
final categoriesProvider = StateNotifierProvider<CategoriesNotifier, AsyncValue<List<TransactionCategory>>>((ref) {
  return CategoriesNotifier(ref);
});

class CategoriesNotifier extends StateNotifier<AsyncValue<List<TransactionCategory>>> {
  final Ref ref;

  CategoriesNotifier(this.ref) : super(const AsyncValue.loading()) {
    _initializeAndLoad();
  }

  Future<void> _initializeAndLoad() async {
    try {
      // Wait for database to be initialized
      await ref.read(databaseProvider.future);
      await loadCategories();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> loadCategories() async {
    try {
      state = const AsyncValue.loading();
      final categories = await CategoriesService.getAllCategories();
      state = AsyncValue.data(categories);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addCategory(TransactionCategory category) async {
    try {
      await CategoriesService.insertCategory(category);
      await loadCategories();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateCategory(TransactionCategory category) async {
    try {
      await CategoriesService.updateCategory(category);
      await loadCategories();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteCategory(String id) async {
    try {
      await CategoriesService.deleteCategory(id);
      await loadCategories();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> restoreCategory(String id) async {
    try {
      await CategoriesService.restoreCategory(id);
      await loadCategories();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

final categoryStatisticsProvider = StateNotifierProvider<CategoryStatisticsNotifier, AsyncValue<Map<String, dynamic>>>((ref) {
  return CategoryStatisticsNotifier(ref);
});

class CategoryStatisticsNotifier extends StateNotifier<AsyncValue<Map<String, dynamic>>> {
  final Ref ref;

  CategoryStatisticsNotifier(this.ref) : super(const AsyncValue.loading()) {
    loadStatistics();

    // Listen to category changes and refresh statistics
    ref.listen(categoriesProvider, (previous, next) {
      loadStatistics();
    });
  }

  Future<void> loadStatistics() async {
    try {
      final stats = await CategoriesService.getCategoryStatistics();
      state = AsyncValue.data(stats);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Selected Item Providers
final selectedAccountProvider = StateProvider<Account?>((ref) => null);
final selectedTransactionProvider = StateProvider<MoneyTransaction?>((ref) => null);
final selectedBudgetProvider = StateProvider<Budget?>((ref) => null);
final selectedGoalProvider = StateProvider<Goal?>((ref) => null);
final selectedCategoryProvider = StateProvider<TransactionCategory?>((ref) => null);

// Filter Providers
final transactionFilterTypeProvider = StateProvider<TransactionType?>((ref) => null);
final transactionFilterCategoryProvider = StateProvider<String?>((ref) => null);
final transactionFilterAccountProvider = StateProvider<String?>((ref) => null);
final transactionSearchQueryProvider = StateProvider<String?>((ref) => null);
final transactionDateRangeProvider = StateProvider<DateTimeRange?>((ref) => null);
final accountFilterTypeProvider = StateProvider<AccountType?>((ref) => null);
