import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/money_flow_models.dart';
import 'money_flow_database_service.dart';

// Database provider
final moneyFlowDatabaseProvider = Provider<MoneyFlowDatabaseService>((ref) {
  return MoneyFlowDatabaseService();
});

// Accounts provider
final moneyFlowAccountsProvider = StateNotifierProvider<MoneyFlowAccountsNotifier, AsyncValue<List<MoneyAccount>>>((ref) {
  return MoneyFlowAccountsNotifier(ref);
});

class MoneyFlowAccountsNotifier extends StateNotifier<AsyncValue<List<MoneyAccount>>> {
  final Ref ref;

  MoneyFlowAccountsNotifier(this.ref) : super(const AsyncValue.loading()) {
    loadAccounts();
  }

  Future<void> loadAccounts() async {
    try {
      state = const AsyncValue.loading();
      final database = ref.read(moneyFlowDatabaseProvider);
      final accounts = await database.getAllAccounts();
      state = AsyncValue.data(accounts);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> saveAccount(MoneyAccount account) async {
    try {
      final database = ref.read(moneyFlowDatabaseProvider);
      await database.saveAccount(account);
      await loadAccounts(); // Refresh the list
    } catch (error) {
      // Handle error
    }
  }

  Future<void> deleteAccount(String id) async {
    try {
      final database = ref.read(moneyFlowDatabaseProvider);
      await database.deleteAccount(id);
      await loadAccounts(); // Refresh the list
    } catch (error) {
      // Handle error
    }
  }
}

// Transactions provider
final moneyFlowTransactionsProvider = StateNotifierProvider<MoneyFlowTransactionsNotifier, AsyncValue<List<MoneyTransactionV2>>>((ref) {
  return MoneyFlowTransactionsNotifier(ref);
});

class MoneyFlowTransactionsNotifier extends StateNotifier<AsyncValue<List<MoneyTransactionV2>>> {
  final Ref ref;

  MoneyFlowTransactionsNotifier(this.ref) : super(const AsyncValue.loading()) {
    loadTransactions();
  }

  Future<void> loadTransactions() async {
    try {
      state = const AsyncValue.loading();
      final database = ref.read(moneyFlowDatabaseProvider);
      final transactions = await database.getAllTransactions();
      state = AsyncValue.data(transactions);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> saveTransaction(MoneyTransactionV2 transaction) async {
    try {
      final database = ref.read(moneyFlowDatabaseProvider);
      await database.saveTransaction(transaction);
      await loadTransactions(); // Refresh the list
      // Also refresh accounts to update balances
      ref.read(moneyFlowAccountsProvider.notifier).loadAccounts();
    } catch (error) {
      // Handle error
    }
  }

  Future<void> deleteTransaction(String id) async {
    try {
      final database = ref.read(moneyFlowDatabaseProvider);
      await database.deleteTransaction(id);
      await loadTransactions(); // Refresh the list
      // Also refresh accounts to update balances
      ref.read(moneyFlowAccountsProvider.notifier).loadAccounts();
    } catch (error) {
      // Handle error
    }
  }
}

// Budgets provider
final moneyFlowBudgetsProvider = StateNotifierProvider<MoneyFlowBudgetsNotifier, AsyncValue<List<MoneyBudget>>>((ref) {
  return MoneyFlowBudgetsNotifier(ref);
});

class MoneyFlowBudgetsNotifier extends StateNotifier<AsyncValue<List<MoneyBudget>>> {
  final Ref ref;

  MoneyFlowBudgetsNotifier(this.ref) : super(const AsyncValue.loading()) {
    loadBudgets();
  }

  Future<void> loadBudgets() async {
    try {
      state = const AsyncValue.loading();
      final database = ref.read(moneyFlowDatabaseProvider);
      final budgets = await database.getAllBudgets();
      state = AsyncValue.data(budgets);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> saveBudget(MoneyBudget budget) async {
    try {
      final database = ref.read(moneyFlowDatabaseProvider);
      await database.saveBudget(budget);
      await loadBudgets(); // Refresh the list
    } catch (error) {
      // Handle error
    }
  }

  Future<void> deleteBudget(String id) async {
    try {
      final database = ref.read(moneyFlowDatabaseProvider);
      await database.deleteBudget(id);
      await loadBudgets(); // Refresh the list
    } catch (error) {
      // Handle error
    }
  }
}

// Statistics provider
final moneyFlowStatisticsProvider = StateNotifierProvider<MoneyFlowStatisticsNotifier, AsyncValue<Map<String, dynamic>>>((ref) {
  return MoneyFlowStatisticsNotifier(ref);
});

class MoneyFlowStatisticsNotifier extends StateNotifier<AsyncValue<Map<String, dynamic>>> {
  final Ref ref;

  MoneyFlowStatisticsNotifier(this.ref) : super(const AsyncValue.loading()) {
    loadStatistics();

    // Listen to data changes and refresh statistics
    ref.listen(moneyFlowAccountsProvider, (previous, next) {
      if (next.hasValue) loadStatistics();
    });
    ref.listen(moneyFlowTransactionsProvider, (previous, next) {
      if (next.hasValue) loadStatistics();
    });
    ref.listen(moneyFlowBudgetsProvider, (previous, next) {
      if (next.hasValue) loadStatistics();
    });
  }

  Future<void> loadStatistics() async {
    try {
      final database = ref.read(moneyFlowDatabaseProvider);
      final stats = await database.getStatistics();
      state = AsyncValue.data(stats);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Monthly spending provider
final moneyFlowMonthlySpendingProvider = StateNotifierProvider<MoneyFlowMonthlySpendingNotifier, AsyncValue<Map<String, double>>>((ref) {
  return MoneyFlowMonthlySpendingNotifier(ref);
});

class MoneyFlowMonthlySpendingNotifier extends StateNotifier<AsyncValue<Map<String, double>>> {
  final Ref ref;

  MoneyFlowMonthlySpendingNotifier(this.ref) : super(const AsyncValue.loading()) {
    loadSpending();

    // Listen to transaction changes and refresh spending
    ref.listen(moneyFlowTransactionsProvider, (previous, next) {
      if (next.hasValue) loadSpending();
    });
  }

  Future<void> loadSpending() async {
    try {
      final database = ref.read(moneyFlowDatabaseProvider);
      final spending = await database.getMonthlySpending();
      state = AsyncValue.data(spending);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Recent transactions provider
final moneyFlowRecentTransactionsProvider = StateNotifierProvider<MoneyFlowRecentTransactionsNotifier, AsyncValue<List<MoneyTransactionV2>>>((ref) {
  return MoneyFlowRecentTransactionsNotifier(ref);
});

class MoneyFlowRecentTransactionsNotifier extends StateNotifier<AsyncValue<List<MoneyTransactionV2>>> {
  final Ref ref;

  MoneyFlowRecentTransactionsNotifier(this.ref) : super(const AsyncValue.loading()) {
    loadRecentTransactions();

    // Listen to transaction changes and refresh recent transactions
    ref.listen(moneyFlowTransactionsProvider, (previous, next) {
      if (next.hasValue) loadRecentTransactions();
    });
  }

  Future<void> loadRecentTransactions() async {
    try {
      final database = ref.read(moneyFlowDatabaseProvider);
      final transactions = await database.getRecentTransactions(limit: 10);
      state = AsyncValue.data(transactions);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Navigation provider
final moneyFlowCurrentScreenProvider = StateProvider<MoneyFlowScreen>((ref) => MoneyFlowScreen.dashboard);

enum MoneyFlowScreen {
  dashboard,
  accounts,
  transactions,
  budgets,
  reports,
}

// Selected items providers
final selectedMoneyAccountProvider = StateProvider<MoneyAccount?>((ref) => null);
final selectedMoneyTransactionProvider = StateProvider<MoneyTransactionV2?>((ref) => null);
final selectedMoneyBudgetProvider = StateProvider<MoneyBudget?>((ref) => null);

// Search and filter providers
final moneyFlowSearchQueryProvider = StateProvider<String>((ref) => '');
final moneyFlowFilterCategoryProvider = StateProvider<String?>((ref) => null);
final moneyFlowFilterAccountProvider = StateProvider<String?>((ref) => null);
final moneyFlowFilterDateRangeProvider = StateProvider<DateTimeRange?>((ref) => null);

// Settings provider
final moneyFlowSettingsProvider = StateNotifierProvider<MoneyFlowSettingsNotifier, AsyncValue<Map<String, dynamic>>>((ref) {
  return MoneyFlowSettingsNotifier(ref);
});

class MoneyFlowSettingsNotifier extends StateNotifier<AsyncValue<Map<String, dynamic>>> {
  final Ref ref;

  MoneyFlowSettingsNotifier(this.ref) : super(const AsyncValue.loading()) {
    loadSettings();
  }

  Future<void> loadSettings() async {
    try {
      final database = ref.read(moneyFlowDatabaseProvider);
      final settings = await database.getSettings();
      state = AsyncValue.data(settings);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> saveSettings(Map<String, dynamic> settings) async {
    try {
      final database = ref.read(moneyFlowDatabaseProvider);
      await database.saveSettings(settings);
      await loadSettings(); // Refresh the settings
    } catch (error) {
      // Handle error
    }
  }
}
