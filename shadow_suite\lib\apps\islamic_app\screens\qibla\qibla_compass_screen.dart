import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import 'dart:math' as math;
import '../../services/prayer_times_service.dart';
import '../../../../core/theme/app_theme.dart';

class QiblaCompassScreen extends ConsumerStatefulWidget {
  const QiblaCompassScreen({super.key});

  @override
  ConsumerState<QiblaCompassScreen> createState() => _QiblaCompassScreenState();
}

class _QiblaCompassScreenState extends ConsumerState<QiblaCompassScreen>
    with TickerProviderStateMixin {
  late AnimationController _compassController;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  
  double _deviceHeading = 0.0;
  double _qiblaDirection = 0.0;
  bool _isCalibrated = false;
  bool _isLocationAvailable = false;
  
  // Default location (can be updated with GPS)
  final double _latitude = 40.7128; // New York
  final double _longitude = -74.0060;
  final String _locationName = 'New York, USA';
  
  Timer? _calibrationTimer;
  int _calibrationSteps = 0;
  final int _maxCalibrationSteps = 8;

  @override
  void initState() {
    super.initState();
    _compassController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    
    _calculateQiblaDirection();
    _startCompassSimulation();
  }

  @override
  void dispose() {
    _compassController.dispose();
    _pulseController.dispose();
    _calibrationTimer?.cancel();
    super.dispose();
  }

  void _calculateQiblaDirection() {
    _qiblaDirection = PrayerTimesService.calculateQiblaDirection(
      latitude: _latitude,
      longitude: _longitude,
    );
    setState(() {
      _isLocationAvailable = true;
    });
  }

  void _startCompassSimulation() {
    // Simulate compass readings for demonstration
    Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (mounted) {
        setState(() {
          // Simulate device rotation with some randomness
          _deviceHeading = (_deviceHeading + (math.Random().nextDouble() - 0.5) * 2) % 360;
          if (_deviceHeading < 0) _deviceHeading += 360;
        });
      } else {
        timer.cancel();
      }
    });
  }

  void _startCalibration() {
    setState(() {
      _isCalibrated = false;
      _calibrationSteps = 0;
    });

    _calibrationTimer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      setState(() {
        _calibrationSteps++;
      });

      if (_calibrationSteps >= _maxCalibrationSteps) {
        timer.cancel();
        setState(() {
          _isCalibrated = true;
        });
        _pulseController.repeat(reverse: true);
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Compass calibrated successfully!'),
            backgroundColor: AppTheme.islamicAppColor,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Qibla Compass'),
        backgroundColor: AppTheme.islamicAppColor,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.my_location),
            onPressed: _updateLocation,
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettings,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildLocationCard(),
          Expanded(
            child: _buildCompassView(),
          ),
          _buildControlPanel(),
        ],
      ),
    );
  }

  Widget _buildLocationCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.islamicAppColor.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(
            color: AppTheme.islamicAppColor.withValues(alpha: 0.3),
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: AppTheme.islamicAppColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _locationName,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              if (_isLocationAvailable)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'GPS',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                'Qibla Direction: ${_qiblaDirection.toStringAsFixed(1)}°',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.islamicAppColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              Text(
                'Distance: ${_calculateDistanceToKaaba().toStringAsFixed(0)} km',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCompassView() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Center(
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Compass background
            _buildCompassBackground(),
            
            // Compass needle
            _buildCompassNeedle(),
            
            // Qibla indicator
            _buildQiblaIndicator(),
            
            // Center dot
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: AppTheme.islamicAppColor,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.islamicAppColor.withValues(alpha: 0.3),
                    blurRadius: 8,
                    spreadRadius: 2,
                  ),
                ],
              ),
            ),
            
            // Calibration overlay
            if (!_isCalibrated) _buildCalibrationOverlay(),
          ],
        ),
      ),
    );
  }

  Widget _buildCompassBackground() {
    return Container(
      width: 300,
      height: 300,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: CustomPaint(
        painter: CompassBackgroundPainter(),
      ),
    );
  }

  Widget _buildCompassNeedle() {
    return Transform.rotate(
      angle: -_deviceHeading * math.pi / 180,
      child: SizedBox(
        width: 200,
        height: 200,
        child: CustomPaint(
          painter: CompassNeedlePainter(),
        ),
      ),
    );
  }

  Widget _buildQiblaIndicator() {
    final qiblaAngle = (_qiblaDirection - _deviceHeading) * math.pi / 180;
    final isAligned = ((_qiblaDirection - _deviceHeading).abs() % 360) < 5;
    
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.rotate(
          angle: qiblaAngle,
          child: Transform.scale(
            scale: isAligned && _isCalibrated ? _pulseAnimation.value : 1.0,
            child: SizedBox(
              width: 250,
              height: 250,
              child: CustomPaint(
                painter: QiblaIndicatorPainter(
                  isAligned: isAligned,
                  isCalibrated: _isCalibrated,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCalibrationOverlay() {
    return Container(
      width: 300,
      height: 300,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.black.withValues(alpha: 0.7),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.screen_rotation,
            color: Colors.white,
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            'Calibrating Compass',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Rotate your device in a figure-8 pattern',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          LinearProgressIndicator(
            value: _calibrationSteps / _maxCalibrationSteps,
            backgroundColor: Colors.white30,
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.islamicAppColor),
          ),
        ],
      ),
    );
  }

  Widget _buildControlPanel() {
    final isAligned = ((_qiblaDirection - _deviceHeading).abs() % 360) < 5;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          if (isAligned && _isCalibrated)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green),
                  const SizedBox(width: 8),
                  Text(
                    'Aligned with Qibla',
                    style: TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            )
          else
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange),
              ),
              child: Row(
                children: [
                  Icon(Icons.navigation, color: Colors.orange),
                  const SizedBox(width: 8),
                  Text(
                    'Turn ${_getDirectionInstruction()}',
                    style: TextStyle(
                      color: Colors.orange,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _isCalibrated ? null : _startCalibration,
                  icon: Icon(_isCalibrated ? Icons.check : Icons.tune),
                  label: Text(_isCalibrated ? 'Calibrated' : 'Calibrate'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isCalibrated ? Colors.green : AppTheme.islamicAppColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _updateLocation,
                  icon: const Icon(Icons.my_location),
                  label: const Text('Update Location'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppTheme.islamicAppColor,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getDirectionInstruction() {
    final diff = (_qiblaDirection - _deviceHeading) % 360;
    final normalizedDiff = diff > 180 ? diff - 360 : diff;
    
    if (normalizedDiff.abs() < 5) {
      return 'Perfect alignment!';
    } else if (normalizedDiff > 0) {
      return '${normalizedDiff.abs().toStringAsFixed(0)}° clockwise';
    } else {
      return '${normalizedDiff.abs().toStringAsFixed(0)}° counter-clockwise';
    }
  }

  double _calculateDistanceToKaaba() {
    // Simplified distance calculation to Kaaba
    const kaabaLat = 21.4225;
    const kaabaLng = 39.8262;
    
    final lat1Rad = _latitude * math.pi / 180;
    final lat2Rad = kaabaLat * math.pi / 180;
    final deltaLatRad = (kaabaLat - _latitude) * math.pi / 180;
    final deltaLngRad = (kaabaLng - _longitude) * math.pi / 180;
    
    final a = math.sin(deltaLatRad / 2) * math.sin(deltaLatRad / 2) +
        math.cos(lat1Rad) * math.cos(lat2Rad) *
        math.sin(deltaLngRad / 2) * math.sin(deltaLngRad / 2);
    final c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    
    return 6371 * c; // Earth's radius in km
  }

  void _updateLocation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Location'),
        content: const Text('GPS location update will be implemented with device location services.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSettings() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Compass Settings',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.vibration),
              title: const Text('Vibration Feedback'),
              trailing: Switch(
                value: true,
                onChanged: (value) {},
              ),
            ),
            ListTile(
              leading: const Icon(Icons.volume_up),
              title: const Text('Audio Feedback'),
              trailing: Switch(
                value: false,
                onChanged: (value) {},
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Close'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Custom painters for compass components
class CompassBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    
    final paint = Paint()
      ..color = Colors.grey[300]!
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;
    
    // Draw compass markings
    for (int i = 0; i < 360; i += 30) {
      final angle = i * math.pi / 180;
      final startRadius = radius - 20;
      final endRadius = radius - 10;
      
      final start = Offset(
        center.dx + startRadius * math.cos(angle - math.pi / 2),
        center.dy + startRadius * math.sin(angle - math.pi / 2),
      );
      final end = Offset(
        center.dx + endRadius * math.cos(angle - math.pi / 2),
        center.dy + endRadius * math.sin(angle - math.pi / 2),
      );
      
      canvas.drawLine(start, end, paint);
    }
    
    // Draw cardinal directions
    final textPainter = TextPainter(
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    );
    
    final directions = ['N', 'E', 'S', 'W'];
    for (int i = 0; i < 4; i++) {
      final angle = i * math.pi / 2;
      textPainter.text = TextSpan(
        text: directions[i],
        style: TextStyle(
          color: AppTheme.islamicAppColor,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      );
      textPainter.layout();
      
      final textCenter = Offset(
        center.dx + (radius - 35) * math.cos(angle - math.pi / 2) - textPainter.width / 2,
        center.dy + (radius - 35) * math.sin(angle - math.pi / 2) - textPainter.height / 2,
      );
      
      textPainter.paint(canvas, textCenter);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class CompassNeedlePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final paint = Paint()..style = PaintingStyle.fill;
    
    // North needle (red)
    paint.color = Colors.red;
    final northPath = Path();
    northPath.moveTo(center.dx, center.dy - 80);
    northPath.lineTo(center.dx - 8, center.dy);
    northPath.lineTo(center.dx + 8, center.dy);
    northPath.close();
    canvas.drawPath(northPath, paint);
    
    // South needle (white with black border)
    paint.color = Colors.white;
    final southPath = Path();
    southPath.moveTo(center.dx, center.dy + 80);
    southPath.lineTo(center.dx - 8, center.dy);
    southPath.lineTo(center.dx + 8, center.dy);
    southPath.close();
    canvas.drawPath(southPath, paint);
    
    paint
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;
    canvas.drawPath(southPath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class QiblaIndicatorPainter extends CustomPainter {
  final bool isAligned;
  final bool isCalibrated;

  QiblaIndicatorPainter({required this.isAligned, required this.isCalibrated});

  @override
  void paint(Canvas canvas, Size size) {
    if (!isCalibrated) return;
    
    final center = Offset(size.width / 2, size.height / 2);
    final paint = Paint()
      ..color = isAligned ? Colors.green : AppTheme.islamicAppColor
      ..style = PaintingStyle.fill;
    
    // Draw Kaaba symbol
    final kaabaPath = Path();
    kaabaPath.moveTo(center.dx, center.dy - 100);
    kaabaPath.lineTo(center.dx - 12, center.dy - 85);
    kaabaPath.lineTo(center.dx + 12, center.dy - 85);
    kaabaPath.close();
    canvas.drawPath(kaabaPath, paint);
    
    // Draw direction line
    paint
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;
    canvas.drawLine(
      center,
      Offset(center.dx, center.dy - 85),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => 
      oldDelegate is QiblaIndicatorPainter && 
      (oldDelegate.isAligned != isAligned || oldDelegate.isCalibrated != isCalibrated);
}
