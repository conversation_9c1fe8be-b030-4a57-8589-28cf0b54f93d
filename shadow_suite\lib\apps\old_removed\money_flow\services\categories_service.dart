import 'package:sqflite/sqflite.dart';
import '../models/category.dart';
import 'money_flow_database_service.dart';

class CategoriesService {
  static const String _tableName = 'transaction_categories';

  static Future<void> createTable(Database db) async {
    await db.execute('''
      CREATE TABLE $_tableName (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        icon TEXT NOT NULL DEFAULT 'category',
        color TEXT NOT NULL DEFAULT '#2196F3',
        isDefault INTEGER NOT NULL DEFAULT 0,
        isActive INTEGER NOT NULL DEFAULT 1,
        usageCount INTEGER NOT NULL DEFAULT 0,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_categories_type ON $_tableName (type)');
    await db.execute('CREATE INDEX idx_categories_active ON $_tableName (isActive)');
    await db.execute('CREATE INDEX idx_categories_usage ON $_tableName (usageCount DESC)');
  }

  static Future<List<TransactionCategory>> getAllCategories() async {
    final db = await MoneyFlowDatabaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      where: 'isActive = ?',
      whereArgs: [1],
      orderBy: 'type ASC, usageCount DESC, name ASC',
    );

    return maps.map((map) => TransactionCategory.fromMap(map)).toList();
  }

  static Future<List<TransactionCategory>> getCategoriesByType(CategoryType type) async {
    final db = await MoneyFlowDatabaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      where: 'type = ? AND isActive = ?',
      whereArgs: [type.name, 1],
      orderBy: 'usageCount DESC, name ASC',
    );

    return maps.map((map) => TransactionCategory.fromMap(map)).toList();
  }

  static Future<TransactionCategory?> getCategoryById(String id) async {
    final db = await MoneyFlowDatabaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;
    return TransactionCategory.fromMap(maps.first);
  }

  static Future<TransactionCategory?> getCategoryByName(String name, CategoryType type) async {
    final db = await MoneyFlowDatabaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      where: 'name = ? AND type = ? AND isActive = ?',
      whereArgs: [name, type.name, 1],
    );

    if (maps.isEmpty) return null;
    return TransactionCategory.fromMap(maps.first);
  }

  static Future<void> insertCategory(TransactionCategory category) async {
    final db = await MoneyFlowDatabaseService.database;
    await db.insert(_tableName, category.toMap());
  }

  static Future<void> updateCategory(TransactionCategory category) async {
    final db = await MoneyFlowDatabaseService.database;
    await db.update(
      _tableName,
      category.copyWith(updatedAt: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [category.id],
    );
  }

  static Future<void> deleteCategory(String id) async {
    final db = await MoneyFlowDatabaseService.database;
    // Soft delete - mark as inactive instead of actually deleting
    await db.update(
      _tableName,
      {
        'isActive': 0,
        'updatedAt': DateTime.now().toIso8601String(),
      },
      where: 'id = ? AND isDefault = ?',
      whereArgs: [id, 0], // Only allow deletion of non-default categories
    );
  }

  static Future<void> incrementUsageCount(String categoryId) async {
    final db = await MoneyFlowDatabaseService.database;
    await db.rawUpdate(
      'UPDATE $_tableName SET usageCount = usageCount + 1, updatedAt = ? WHERE id = ?',
      [DateTime.now().toIso8601String(), categoryId],
    );
  }

  static Future<List<TransactionCategory>> getMostUsedCategories({int limit = 10}) async {
    final db = await MoneyFlowDatabaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      where: 'isActive = ? AND usageCount > ?',
      whereArgs: [1, 0],
      orderBy: 'usageCount DESC',
      limit: limit,
    );

    return maps.map((map) => TransactionCategory.fromMap(map)).toList();
  }

  static Future<Map<String, dynamic>> getCategoryStatistics() async {
    final db = await MoneyFlowDatabaseService.database;
    
    final totalCategoriesResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $_tableName WHERE isActive = ?',
      [1],
    );
    final totalCategories = totalCategoriesResult.first['count'] as int;

    final incomeCategoriesResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $_tableName WHERE type = ? AND isActive = ?',
      [CategoryType.income.name, 1],
    );
    final incomeCategories = incomeCategoriesResult.first['count'] as int;

    final expenseCategoriesResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $_tableName WHERE type = ? AND isActive = ?',
      [CategoryType.expense.name, 1],
    );
    final expenseCategories = expenseCategoriesResult.first['count'] as int;

    final transferCategoriesResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $_tableName WHERE type = ? AND isActive = ?',
      [CategoryType.transfer.name, 1],
    );
    final transferCategories = transferCategoriesResult.first['count'] as int;

    final customCategoriesResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $_tableName WHERE isDefault = ? AND isActive = ?',
      [0, 1],
    );
    final customCategories = customCategoriesResult.first['count'] as int;

    final totalUsageResult = await db.rawQuery(
      'SELECT SUM(usageCount) as total FROM $_tableName WHERE isActive = ?',
      [1],
    );
    final totalUsage = (totalUsageResult.first['total'] as int?) ?? 0;

    return {
      'totalCategories': totalCategories,
      'incomeCategories': incomeCategories,
      'expenseCategories': expenseCategories,
      'transferCategories': transferCategories,
      'customCategories': customCategories,
      'totalUsage': totalUsage,
    };
  }

  static Future<void> resetUsageStatistics() async {
    final db = await MoneyFlowDatabaseService.database;
    await db.update(
      _tableName,
      {
        'usageCount': 0,
        'updatedAt': DateTime.now().toIso8601String(),
      },
      where: 'isActive = ?',
      whereArgs: [1],
    );
  }

  static Future<void> populateDefaultCategories() async {
    final db = await MoneyFlowDatabaseService.database;
    
    // Check if default categories already exist
    final existingCount = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM $_tableName WHERE isDefault = ?', [1]),
    ) ?? 0;

    if (existingCount == 0) {
      // Insert default categories
      final defaultCategories = DefaultCategories.getDefaultCategories();
      for (final category in defaultCategories) {
        await db.insert(_tableName, category.toMap());
      }
    }
  }

  static Future<List<TransactionCategory>> searchCategories(String query) async {
    final db = await MoneyFlowDatabaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      where: 'name LIKE ? AND isActive = ?',
      whereArgs: ['%$query%', 1],
      orderBy: 'usageCount DESC, name ASC',
    );

    return maps.map((map) => TransactionCategory.fromMap(map)).toList();
  }

  static Future<bool> categoryNameExists(String name, CategoryType type, {String? excludeId}) async {
    final db = await MoneyFlowDatabaseService.database;
    
    String whereClause = 'name = ? AND type = ? AND isActive = ?';
    List<dynamic> whereArgs = [name, type.name, 1];
    
    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }
    
    final count = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM $_tableName WHERE $whereClause', whereArgs),
    ) ?? 0;

    return count > 0;
  }

  static Future<void> restoreCategory(String id) async {
    final db = await MoneyFlowDatabaseService.database;
    await db.update(
      _tableName,
      {
        'isActive': 1,
        'updatedAt': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  static Future<List<TransactionCategory>> getDeletedCategories() async {
    final db = await MoneyFlowDatabaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      where: 'isActive = ?',
      whereArgs: [0],
      orderBy: 'updatedAt DESC',
    );

    return maps.map((map) => TransactionCategory.fromMap(map)).toList();
  }
}
