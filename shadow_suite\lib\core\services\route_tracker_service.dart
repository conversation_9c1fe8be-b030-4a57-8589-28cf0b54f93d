import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/app_navigation.dart';

/// Service to track current route and provide route-aware functionality
class RouteTrackerService {
  static String _currentRoute = '/dashboard';
  static AppSection _currentSection = AppSection.dashboard;
  static String? _currentSubRoute;

  /// Get current route
  static String get currentRoute => _currentRoute;

  /// Get current app section
  static AppSection get currentSection => _currentSection;

  /// Get current sub-route
  static String? get currentSubRoute => _currentSubRoute;

  /// Update current route and determine section
  static void updateRoute(String route) {
    _currentRoute = route;
    _updateSectionFromRoute(route);
  }

  /// Determine app section from route
  static void _updateSectionFromRoute(String route) {
    if (route.startsWith('/dashboard')) {
      _currentSection = AppSection.dashboard;
      _currentSubRoute = null;
    } else if (route.startsWith('/money-manager')) {
      _currentSection = AppSection.moneyManager;
      _currentSubRoute = _extractSubRoute(route, '/money-manager');
    } else if (route.startsWith('/file-manager')) {
      _currentSection = AppSection.fileManager;
      _currentSubRoute = _extractSubRoute(route, '/file-manager');
    } else if (route.startsWith('/excel-to-app')) {
      _currentSection = AppSection.excelToApp;
      _currentSubRoute = _extractSubRoute(route, '/excel-to-app');
    } else if (route.startsWith('/islamic-app')) {
      _currentSection = AppSection.islamicApp;
      _currentSubRoute = _extractSubRoute(route, '/islamic-app');
    } else if (route.startsWith('/memo-suite')) {
      _currentSection = AppSection.memoSuite;
      _currentSubRoute = _extractSubRoute(route, '/memo-suite');
    } else if (route.startsWith('/shadow-player')) {
      _currentSection = AppSection.shadowPlayer;
      _currentSubRoute = _extractSubRoute(route, '/shadow-player');
    } else if (route.startsWith('/smart-gallery')) {
      _currentSection = AppSection.smartGallery;
      _currentSubRoute = _extractSubRoute(route, '/smart-gallery');
    } else if (route.startsWith('/settings')) {
      _currentSection = AppSection.settings;
      _currentSubRoute = _extractSubRoute(route, '/settings');
    } else if (route.startsWith('/profile')) {
      _currentSection = AppSection.profile;
      _currentSubRoute = null;
    } else {
      _currentSection = AppSection.dashboard;
      _currentSubRoute = null;
    }
  }

  /// Extract sub-route from full route
  static String? _extractSubRoute(String fullRoute, String baseRoute) {
    if (fullRoute.length <= baseRoute.length) return null;
    
    final subRoute = fullRoute.substring(baseRoute.length);
    if (subRoute.startsWith('/')) {
      return subRoute.substring(1);
    }
    return subRoute.isEmpty ? null : subRoute;
  }

  /// Check if a route is currently active
  static bool isRouteActive(String route) {
    return _currentRoute == route;
  }

  /// Check if a section is currently active
  static bool isSectionActive(AppSection section) {
    return _currentSection == section;
  }

  /// Check if a sub-route is currently active
  static bool isSubRouteActive(String subRoute) {
    return _currentSubRoute == subRoute;
  }

  /// Get the main route for a section
  static String getMainRouteForSection(AppSection section) {
    switch (section) {
      case AppSection.dashboard:
        return '/dashboard';
      case AppSection.moneyManager:
        return '/money-manager';
      case AppSection.fileManager:
        return '/file-manager';
      case AppSection.excelToApp:
        return '/excel-to-app';
      case AppSection.islamicApp:
        return '/islamic-app';
      case AppSection.memoSuite:
        return '/memo-suite';
      case AppSection.shadowPlayer:
        return '/shadow-player';
      case AppSection.smartGallery:
        return '/smart-gallery';
      case AppSection.settings:
        return '/settings';
      case AppSection.profile:
        return '/profile';
    }
  }

  /// Navigate to section with permission check
  static Future<void> navigateToSection(
    BuildContext context,
    AppSection section, {
    String? subRoute,
  }) async {
    String route = getMainRouteForSection(section);
    if (subRoute != null) {
      route = '$route/$subRoute';
    }

    if (context.mounted) {
      context.go(route);
      updateRoute(route);
    }
  }

  /// Get breadcrumb for current route
  static List<String> getCurrentBreadcrumb() {
    final breadcrumb = <String>[];
    
    // Add main section
    breadcrumb.add(_getSectionDisplayName(_currentSection));
    
    // Add sub-route if exists
    if (_currentSubRoute != null) {
      breadcrumb.add(_getSubRouteDisplayName(_currentSubRoute!));
    }
    
    return breadcrumb;
  }

  /// Get display name for section
  static String _getSectionDisplayName(AppSection section) {
    switch (section) {
      case AppSection.dashboard:
        return 'Dashboard';
      case AppSection.moneyManager:
        return 'Money Manager';
      case AppSection.fileManager:
        return 'File Manager';
      case AppSection.excelToApp:
        return 'Excel to App';
      case AppSection.islamicApp:
        return 'Islamic App';
      case AppSection.memoSuite:
        return 'Memo Suite';
      case AppSection.shadowPlayer:
        return 'Shadow Player';
      case AppSection.smartGallery:
        return 'SmartGallery+';
      case AppSection.settings:
        return 'Settings';
      case AppSection.profile:
        return 'Profile';
    }
  }

  /// Get display name for sub-route
  static String _getSubRouteDisplayName(String subRoute) {
    switch (subRoute) {
      case 'dashboard':
        return 'Dashboard';
      case 'accounts':
        return 'Accounts';
      case 'transactions':
        return 'Transactions';
      case 'categories':
        return 'Categories';
      case 'budgets':
        return 'Budgets';
      case 'goals':
        return 'Goals';
      case 'reports':
        return 'Reports';
      case 'browse':
        return 'Browse Files';
      case 'cloud':
        return 'Cloud Storage';
      case 'network':
        return 'Network Shares';
      case 'media':
        return 'Media Player';
      case 'operations':
        return 'File Operations';
      case 'create':
        return 'Create Tool';
      case 'my-tools':
        return 'My Tools';
      case 'import':
        return 'Import Tools';
      case 'quran':
        return 'Quran';
      case 'hadith':
        return 'Hadith';
      case 'tafseer':
        return 'Tafseer';
      case 'athkar':
        return 'Athkar';
      case 'prayer-times':
        return 'Prayer Times';
      case 'qibla':
        return 'Qibla Compass';
      case 'bookmarks':
        return 'Bookmarks';
      case 'notes':
        return 'Notes';
      case 'todos':
        return 'Todos';
      case 'voice-memos':
        return 'Voice Memos';
      case 'calendar':
        return 'Calendar';
      case 'video':
        return 'Video Library';
      case 'music':
        return 'Music Library';
      case 'all':
        return 'All Media';
      case 'favorites':
        return 'Favorites';
      case 'people':
        return 'People';
      case 'insights':
        return 'AI Insights';
      default:
        return subRoute.replaceAll('-', ' ').split(' ')
            .map((word) => word.isEmpty ? '' : word[0].toUpperCase() + word.substring(1))
            .join(' ');
    }
  }
}

/// Riverpod providers for route tracking
final currentRouteProvider = StateProvider<String>((ref) => '/dashboard');

final currentSectionProvider = StateProvider<AppSection>((ref) => AppSection.dashboard);

final currentSubRouteProvider = StateProvider<String?>((ref) => null);

/// Provider that combines route information
final routeInfoProvider = Provider<RouteInfo>((ref) {
  final route = ref.watch(currentRouteProvider);
  final section = ref.watch(currentSectionProvider);
  final subRoute = ref.watch(currentSubRouteProvider);
  
  return RouteInfo(
    route: route,
    section: section,
    subRoute: subRoute,
    breadcrumb: RouteTrackerService.getCurrentBreadcrumb(),
  );
});

/// Route information model
class RouteInfo {
  final String route;
  final AppSection section;
  final String? subRoute;
  final List<String> breadcrumb;

  const RouteInfo({
    required this.route,
    required this.section,
    required this.subRoute,
    required this.breadcrumb,
  });

  bool isSectionActive(AppSection section) {
    return this.section == section;
  }

  bool isRouteActive(String route) {
    return this.route == route;
  }

  bool isSubRouteActive(String subRoute) {
    return this.subRoute == subRoute;
  }
}

/// Router listener to update route tracking
class RouteTrackerListener extends ChangeNotifier {
  final WidgetRef ref;

  RouteTrackerListener(this.ref);

  void onRouteChanged(String route) {
    RouteTrackerService.updateRoute(route);
    
    // Update providers
    ref.read(currentRouteProvider.notifier).state = route;
    ref.read(currentSectionProvider.notifier).state = RouteTrackerService.currentSection;
    ref.read(currentSubRouteProvider.notifier).state = RouteTrackerService.currentSubRoute;
  }
}
