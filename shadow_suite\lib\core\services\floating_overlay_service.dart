import 'dart:async';
import 'package:flutter/material.dart';

/// Floating overlay service for all Shadow Suite mini-apps
/// Provides floating window functionality and Android widget integration
class FloatingOverlayService {
  static final FloatingOverlayService _instance =
      FloatingOverlayService._internal();
  factory FloatingOverlayService() => _instance;
  FloatingOverlayService._internal();

  // Overlay management
  final Map<String, OverlayEntry> _activeOverlays = {};
  final Map<String, FloatingWindowConfig> _windowConfigs = {};
  bool _isInitialized = false;
  bool _hasOverlayPermission = false;

  /// Initialize floating overlay service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Request overlay permission for Android
      await _requestOverlayPermission();

      // Initialize default window configurations
      _initializeDefaultConfigs();

      _isInitialized = true;
    } catch (e) {
      throw Exception('Failed to initialize floating overlay service: $e');
    }
  }

  /// Request overlay permission for Android
  Future<void> _requestOverlayPermission() async {
    try {
      // In a real implementation, this would use system_alert_window package
      // For now, simulating permission grant
      _hasOverlayPermission = true;
    } catch (e) {
      _hasOverlayPermission = false;
      throw Exception('Failed to request overlay permission: $e');
    }
  }

  /// Initialize default floating window configurations
  void _initializeDefaultConfigs() {
    // Prayer times floating widget
    _windowConfigs['prayer_times'] = FloatingWindowConfig(
      id: 'prayer_times',
      title: 'Prayer Times',
      size: const Size(300, 200),
      position: const Offset(50, 100),
      resizable: true,
      draggable: true,
      alwaysOnTop: true,
      showCloseButton: true,
      backgroundColor: Colors.green.shade50,
      borderRadius: 12.0,
    );

    // Quick file access floating widget
    _windowConfigs['quick_files'] = FloatingWindowConfig(
      id: 'quick_files',
      title: 'Quick Files',
      size: const Size(250, 300),
      position: const Offset(100, 150),
      resizable: true,
      draggable: true,
      alwaysOnTop: false,
      showCloseButton: true,
      backgroundColor: Colors.blue.shade50,
      borderRadius: 8.0,
    );

    // Budget summary floating widget
    _windowConfigs['budget_summary'] = FloatingWindowConfig(
      id: 'budget_summary',
      title: 'Budget Summary',
      size: const Size(280, 180),
      position: const Offset(150, 200),
      resizable: false,
      draggable: true,
      alwaysOnTop: false,
      showCloseButton: true,
      backgroundColor: Colors.orange.shade50,
      borderRadius: 10.0,
    );

    // Media player controls floating widget
    _windowConfigs['media_controls'] = FloatingWindowConfig(
      id: 'media_controls',
      title: 'Media Player',
      size: const Size(320, 120),
      position: const Offset(200, 250),
      resizable: false,
      draggable: true,
      alwaysOnTop: true,
      showCloseButton: true,
      backgroundColor: Colors.purple.shade50,
      borderRadius: 15.0,
    );

    // Quran verse floating widget
    _windowConfigs['quran_verse'] = FloatingWindowConfig(
      id: 'quran_verse',
      title: 'Daily Verse',
      size: const Size(350, 250),
      position: const Offset(75, 125),
      resizable: true,
      draggable: true,
      alwaysOnTop: false,
      showCloseButton: true,
      backgroundColor: Colors.teal.shade50,
      borderRadius: 12.0,
    );
  }

  /// Show floating window for specific mini-app
  Future<void> showFloatingWindow({
    required String windowId,
    required Widget content,
    FloatingWindowConfig? customConfig,
  }) async {
    _ensureInitialized();
    _ensurePermission();

    // Close existing window if open
    if (_activeOverlays.containsKey(windowId)) {
      await closeFloatingWindow(windowId);
    }

    final config = customConfig ?? _windowConfigs[windowId];
    if (config == null) {
      throw ArgumentError('No configuration found for window: $windowId');
    }

    // Create overlay entry
    final overlayEntry = OverlayEntry(
      builder: (context) => FloatingWindow(
        config: config,
        content: content,
        onClose: () => closeFloatingWindow(windowId),
        onMove: (offset) => _updateWindowPosition(windowId, offset),
        onResize: (size) => _updateWindowSize(windowId, size),
      ),
    );

    // Add to overlay
    final overlay = Overlay.of(NavigationService.navigatorKey.currentContext!);
    overlay.insert(overlayEntry);

    _activeOverlays[windowId] = overlayEntry;
  }

  /// Close floating window
  Future<void> closeFloatingWindow(String windowId) async {
    final overlayEntry = _activeOverlays[windowId];
    if (overlayEntry != null) {
      overlayEntry.remove();
      _activeOverlays.remove(windowId);
    }
  }

  /// Close all floating windows
  Future<void> closeAllFloatingWindows() async {
    final windowIds = List.from(_activeOverlays.keys);
    for (final windowId in windowIds) {
      await closeFloatingWindow(windowId);
    }
  }

  /// Update window position
  void _updateWindowPosition(String windowId, Offset position) {
    final config = _windowConfigs[windowId];
    if (config != null) {
      _windowConfigs[windowId] = config.copyWith(position: position);
    }
  }

  /// Update window size
  void _updateWindowSize(String windowId, Size size) {
    final config = _windowConfigs[windowId];
    if (config != null && config.resizable) {
      _windowConfigs[windowId] = config.copyWith(size: size);
    }
  }

  /// Check if window is currently open
  bool isWindowOpen(String windowId) {
    return _activeOverlays.containsKey(windowId);
  }

  /// Get list of open windows
  List<String> getOpenWindows() {
    return _activeOverlays.keys.toList();
  }

  /// Update window configuration
  void updateWindowConfig(String windowId, FloatingWindowConfig config) {
    _windowConfigs[windowId] = config;
  }

  /// Get window configuration
  FloatingWindowConfig? getWindowConfig(String windowId) {
    return _windowConfigs[windowId];
  }

  /// Ensure service is initialized
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError(
        'FloatingOverlayService not initialized. Call initialize() first.',
      );
    }
  }

  /// Ensure overlay permission is granted
  void _ensurePermission() {
    if (!_hasOverlayPermission) {
      throw StateError('Overlay permission not granted.');
    }
  }

  // Getters
  bool get isInitialized => _isInitialized;
  bool get hasOverlayPermission => _hasOverlayPermission;
  Map<String, FloatingWindowConfig> get windowConfigs =>
      Map.from(_windowConfigs);
}

/// Floating window configuration
class FloatingWindowConfig {
  final String id;
  final String title;
  final Size size;
  final Offset position;
  final bool resizable;
  final bool draggable;
  final bool alwaysOnTop;
  final bool showCloseButton;
  final Color backgroundColor;
  final double borderRadius;

  const FloatingWindowConfig({
    required this.id,
    required this.title,
    required this.size,
    required this.position,
    required this.resizable,
    required this.draggable,
    required this.alwaysOnTop,
    required this.showCloseButton,
    required this.backgroundColor,
    required this.borderRadius,
  });

  FloatingWindowConfig copyWith({
    String? id,
    String? title,
    Size? size,
    Offset? position,
    bool? resizable,
    bool? draggable,
    bool? alwaysOnTop,
    bool? showCloseButton,
    Color? backgroundColor,
    double? borderRadius,
  }) => FloatingWindowConfig(
    id: id ?? this.id,
    title: title ?? this.title,
    size: size ?? this.size,
    position: position ?? this.position,
    resizable: resizable ?? this.resizable,
    draggable: draggable ?? this.draggable,
    alwaysOnTop: alwaysOnTop ?? this.alwaysOnTop,
    showCloseButton: showCloseButton ?? this.showCloseButton,
    backgroundColor: backgroundColor ?? this.backgroundColor,
    borderRadius: borderRadius ?? this.borderRadius,
  );
}

/// Floating window widget
class FloatingWindow extends StatefulWidget {
  final FloatingWindowConfig config;
  final Widget content;
  final VoidCallback onClose;
  final Function(Offset) onMove;
  final Function(Size) onResize;

  const FloatingWindow({
    super.key,
    required this.config,
    required this.content,
    required this.onClose,
    required this.onMove,
    required this.onResize,
  });

  @override
  State<FloatingWindow> createState() => _FloatingWindowState();
}

class _FloatingWindowState extends State<FloatingWindow> {
  late Offset _position;
  late Size _size;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    _position = widget.config.position;
    _size = widget.config.size;
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: _position.dx,
      top: _position.dy,
      child: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(widget.config.borderRadius),
        child: Container(
          width: _size.width,
          height: _size.height,
          decoration: BoxDecoration(
            color: widget.config.backgroundColor,
            borderRadius: BorderRadius.circular(widget.config.borderRadius),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: Column(
            children: [
              // Title bar
              if (widget.config.draggable || widget.config.showCloseButton)
                GestureDetector(
                  onPanStart: widget.config.draggable ? _onDragStart : null,
                  onPanUpdate: widget.config.draggable ? _onDragUpdate : null,
                  onPanEnd: widget.config.draggable ? _onDragEnd : null,
                  child: Container(
                    height: 40,
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(widget.config.borderRadius),
                        topRight: Radius.circular(widget.config.borderRadius),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            widget.config.title,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        if (widget.config.showCloseButton)
                          IconButton(
                            icon: const Icon(Icons.close, size: 18),
                            onPressed: widget.onClose,
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(
                              minWidth: 24,
                              minHeight: 24,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),

              // Content area
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(widget.config.borderRadius),
                    bottomRight: Radius.circular(widget.config.borderRadius),
                    topLeft:
                        widget.config.draggable || widget.config.showCloseButton
                        ? Radius.zero
                        : Radius.circular(widget.config.borderRadius),
                    topRight:
                        widget.config.draggable || widget.config.showCloseButton
                        ? Radius.zero
                        : Radius.circular(widget.config.borderRadius),
                  ),
                  child: widget.content,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onDragStart(DragStartDetails details) {
    _isDragging = true;
  }

  void _onDragUpdate(DragUpdateDetails details) {
    if (_isDragging) {
      setState(() {
        _position += details.delta;
      });
      widget.onMove(_position);
    }
  }

  void _onDragEnd(DragEndDetails details) {
    _isDragging = false;
  }
}

/// Navigation service for global context access
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();
}
