import 'package:uuid/uuid.dart';

enum AthkarCategory {
  morning('Morning Athkar'),
  evening('Evening Athkar'),
  afterPrayer('After Prayer'),
  sleeping('Before Sleeping'),
  eating('Before/After Eating'),
  travel('Travel'),
  custom('Custom');

  const AthkarCategory(this.displayName);
  final String displayName;

  static List<String> get allCategories => AthkarCategory.values.map((e) => e.displayName).toList();
}

class Dhikr {
  final String id;
  final String textArabic;
  final String textEnglish;
  final String textTransliteration;
  final String meaning;
  final int recommendedCount;
  final String source; // Hadith reference
  final AthkarCategory category;
  final List<String> benefits;

  Dhikr({
    String? id,
    required this.textArabic,
    required this.textEnglish,
    required this.textTransliteration,
    required this.meaning,
    required this.recommendedCount,
    required this.source,
    required this.category,
    required this.benefits,
  }) : id = id ?? const Uuid().v4();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'textArabic': textArabic,
      'textEnglish': textEnglish,
      'textTransliteration': textTransliteration,
      'meaning': meaning,
      'recommendedCount': recommendedCount,
      'source': source,
      'category': category.name,
      'benefits': benefits.join('|'),
    };
  }

  factory Dhikr.fromMap(Map<String, dynamic> map) {
    return Dhikr(
      id: map['id'],
      textArabic: map['textArabic'],
      textEnglish: map['textEnglish'],
      textTransliteration: map['textTransliteration'],
      meaning: map['meaning'],
      recommendedCount: map['recommendedCount'],
      source: map['source'],
      category: AthkarCategory.values.firstWhere((c) => c.name == map['category']),
      benefits: map['benefits'].toString().split('|').where((b) => b.isNotEmpty).toList(),
    );
  }

  @override
  String toString() {
    return 'Dhikr(id: $id, textEnglish: $textEnglish, count: $recommendedCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Dhikr && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class DhikrSession {
  final String id;
  final String dhikrId;
  final int targetCount;
  final int currentCount;
  final DateTime startTime;
  final DateTime? endTime;
  final bool isCompleted;
  final AthkarCategory category;

  DhikrSession({
    String? id,
    required this.dhikrId,
    required this.targetCount,
    required this.currentCount,
    DateTime? startTime,
    this.endTime,
    required this.isCompleted,
    required this.category,
  })  : id = id ?? const Uuid().v4(),
        startTime = startTime ?? DateTime.now();

  Duration? get duration {
    if (endTime != null) {
      return endTime!.difference(startTime);
    }
    return null;
  }

  double get progress => targetCount > 0 ? currentCount / targetCount : 0.0;

  DhikrSession copyWith({
    int? currentCount,
    DateTime? endTime,
    bool? isCompleted,
  }) {
    return DhikrSession(
      id: id,
      dhikrId: dhikrId,
      targetCount: targetCount,
      currentCount: currentCount ?? this.currentCount,
      startTime: startTime,
      endTime: endTime ?? this.endTime,
      isCompleted: isCompleted ?? this.isCompleted,
      category: category,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'dhikrId': dhikrId,
      'targetCount': targetCount,
      'currentCount': currentCount,
      'startTime': startTime.millisecondsSinceEpoch,
      'endTime': endTime?.millisecondsSinceEpoch,
      'isCompleted': isCompleted ? 1 : 0,
      'category': category.name,
    };
  }

  factory DhikrSession.fromMap(Map<String, dynamic> map) {
    return DhikrSession(
      id: map['id'],
      dhikrId: map['dhikrId'],
      targetCount: map['targetCount'],
      currentCount: map['currentCount'],
      startTime: DateTime.fromMillisecondsSinceEpoch(map['startTime']),
      endTime: map['endTime'] != null ? DateTime.fromMillisecondsSinceEpoch(map['endTime']) : null,
      isCompleted: map['isCompleted'] == 1,
      category: AthkarCategory.values.firstWhere((c) => c.name == map['category']),
    );
  }

  @override
  String toString() {
    return 'DhikrSession(id: $id, progress: ${(progress * 100).toInt()}%, completed: $isCompleted)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DhikrSession && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class CustomAthkarRoutine {
  final String id;
  final String name;
  final String description;
  final List<CustomDhikrItem> dhikrItems;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String color; // Hex color code
  final bool isActive;

  CustomAthkarRoutine({
    String? id,
    required this.name,
    required this.description,
    required this.dhikrItems,
    DateTime? createdAt,
    DateTime? updatedAt,
    required this.color,
    this.isActive = true,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  int get totalDhikrCount => dhikrItems.fold(0, (sum, item) => sum + item.count);

  CustomAthkarRoutine copyWith({
    String? name,
    String? description,
    List<CustomDhikrItem>? dhikrItems,
    DateTime? updatedAt,
    String? color,
    bool? isActive,
  }) {
    return CustomAthkarRoutine(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      dhikrItems: dhikrItems ?? this.dhikrItems,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      color: color ?? this.color,
      isActive: isActive ?? this.isActive,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'dhikrItems': dhikrItems.map((item) => item.toMap()).toList(),
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'color': color,
      'isActive': isActive ? 1 : 0,
    };
  }

  factory CustomAthkarRoutine.fromMap(Map<String, dynamic> map) {
    return CustomAthkarRoutine(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      dhikrItems: (map['dhikrItems'] as List<dynamic>)
          .map((item) => CustomDhikrItem.fromMap(item))
          .toList(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
      color: map['color'],
      isActive: map['isActive'] == 1,
    );
  }

  @override
  String toString() {
    return 'CustomAthkarRoutine(id: $id, name: $name, dhikrCount: ${dhikrItems.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CustomAthkarRoutine && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class CustomDhikrItem {
  final String dhikrId;
  final int count;
  final int order;

  const CustomDhikrItem({
    required this.dhikrId,
    required this.count,
    required this.order,
  });

  Map<String, dynamic> toMap() {
    return {
      'dhikrId': dhikrId,
      'count': count,
      'order': order,
    };
  }

  factory CustomDhikrItem.fromMap(Map<String, dynamic> map) {
    return CustomDhikrItem(
      dhikrId: map['dhikrId'],
      count: map['count'],
      order: map['order'],
    );
  }

  @override
  String toString() {
    return 'CustomDhikrItem(dhikrId: $dhikrId, count: $count, order: $order)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CustomDhikrItem && 
           other.dhikrId == dhikrId && 
           other.order == order;
  }

  @override
  int get hashCode => '$dhikrId-$order'.hashCode;
}

// Pre-defined Athkar data
class AthkarData {
  static const List<Map<String, dynamic>> morningAthkar = [
    {
      'textArabic': 'أَعُوذُ بِاللَّهِ مِنَ الشَّيْطَانِ الرَّجِيمِ',
      'textEnglish': 'A\'udhu billahi min ash-shaytani\'r-rajim',
      'textTransliteration': 'A\'udhu billahi min ash-shaytani\'r-rajim',
      'meaning': 'I seek refuge in Allah from Satan, the accursed.',
      'recommendedCount': 1,
      'source': 'Quran',
      'benefits': ['Protection from Satan', 'Spiritual purification'],
    },
    {
      'textArabic': 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
      'textEnglish': 'Bismillahi\'r-rahmani\'r-rahim',
      'textTransliteration': 'Bismillahi\'r-rahmani\'r-rahim',
      'meaning': 'In the name of Allah, the Most Gracious, the Most Merciful.',
      'recommendedCount': 1,
      'source': 'Quran',
      'benefits': ['Blessing in actions', 'Divine protection'],
    },
    {
      'textArabic': 'اللَّهُمَّ أَنْتَ رَبِّي لَا إِلَٰهَ إِلَّا أَنْتَ خَلَقْتَنِي وَأَنَا عَبْدُكَ',
      'textEnglish': 'Allahumma anta rabbi la ilaha illa anta khalaqtani wa ana \'abduka',
      'textTransliteration': 'Allahumma anta rabbi la ilaha illa anta khalaqtani wa ana \'abduka',
      'meaning': 'O Allah, You are my Lord, none has the right to be worshipped except You, You created me and I am Your servant.',
      'recommendedCount': 1,
      'source': 'Sahih Bukhari',
      'benefits': ['Forgiveness of sins', 'Protection throughout the day'],
    },
  ];

  static const List<Map<String, dynamic>> eveningAthkar = [
    {
      'textArabic': 'أَمْسَيْنَا وَأَمْسَى الْمُلْكُ لِلَّهِ',
      'textEnglish': 'Amsayna wa amsa\'l-mulku lillah',
      'textTransliteration': 'Amsayna wa amsa\'l-mulku lillah',
      'meaning': 'We have reached the evening and at this very time unto Allah belongs all sovereignty.',
      'recommendedCount': 1,
      'source': 'Sahih Muslim',
      'benefits': ['Acknowledgment of Allah\'s sovereignty', 'Evening protection'],
    },
    {
      'textArabic': 'اللَّهُمَّ بِكَ أَمْسَيْنَا وَبِكَ أَصْبَحْنَا',
      'textEnglish': 'Allahumma bika amsayna wa bika asbahna',
      'textTransliteration': 'Allahumma bika amsayna wa bika asbahna',
      'meaning': 'O Allah, by Your leave we have reached the evening and by Your leave we have reached the morning.',
      'recommendedCount': 1,
      'source': 'Abu Dawud',
      'benefits': ['Gratitude to Allah', 'Recognition of dependence on Allah'],
    },
  ];

  static const List<Map<String, dynamic>> afterPrayerAthkar = [
    {
      'textArabic': 'سُبْحَانَ اللَّهِ',
      'textEnglish': 'Subhan Allah',
      'textTransliteration': 'Subhan Allah',
      'meaning': 'Glory be to Allah.',
      'recommendedCount': 33,
      'source': 'Sahih Muslim',
      'benefits': ['Spiritual purification', 'Reward equivalent to charity'],
    },
    {
      'textArabic': 'الْحَمْدُ لِلَّهِ',
      'textEnglish': 'Alhamdulillah',
      'textTransliteration': 'Alhamdulillah',
      'meaning': 'All praise is due to Allah.',
      'recommendedCount': 33,
      'source': 'Sahih Muslim',
      'benefits': ['Gratitude expression', 'Spiritual reward'],
    },
    {
      'textArabic': 'اللَّهُ أَكْبَرُ',
      'textEnglish': 'Allahu Akbar',
      'textTransliteration': 'Allahu Akbar',
      'meaning': 'Allah is the Greatest.',
      'recommendedCount': 34,
      'source': 'Sahih Muslim',
      'benefits': ['Magnification of Allah', 'Spiritual elevation'],
    },
  ];

  static const List<Map<String, dynamic>> sleepingAthkar = [
    {
      'textArabic': 'اللَّهُمَّ بِاسْمِكَ أَمُوتُ وَأَحْيَا',
      'textEnglish': 'Allahumma bismika amutu wa ahya',
      'textTransliteration': 'Allahumma bismika amutu wa ahya',
      'meaning': 'O Allah, in Your name I die and I live.',
      'recommendedCount': 1,
      'source': 'Sahih Bukhari',
      'benefits': ['Protection during sleep', 'Peaceful rest'],
    },
    {
      'textArabic': 'أَسْتَغْفِرُ اللَّهَ الَّذِي لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ وَأَتُوبُ إِلَيْهِ',
      'textEnglish': 'Astaghfirullaha\'l-ladhi la ilaha illa huwa\'l-hayyu\'l-qayyumu wa atubu ilayh',
      'textTransliteration': 'Astaghfirullaha\'l-ladhi la ilaha illa huwa\'l-hayyu\'l-qayyumu wa atubu ilayh',
      'meaning': 'I seek forgiveness from Allah, besides whom there is no god, the Ever-Living, the Sustainer, and I repent to Him.',
      'recommendedCount': 3,
      'source': 'Abu Dawud',
      'benefits': ['Forgiveness of sins', 'Spiritual purification'],
    },
    {
      'textArabic': 'سُبْحَانَ اللَّهِ وَالْحَمْدُ لِلَّهِ وَاللَّهُ أَكْبَرُ',
      'textEnglish': 'Subhan Allah wa\'l-hamdu lillahi wa Allahu akbar',
      'textTransliteration': 'Subhan Allah wa\'l-hamdu lillahi wa Allahu akbar',
      'meaning': 'Glory be to Allah, praise be to Allah, and Allah is the Greatest.',
      'recommendedCount': 1,
      'source': 'Sahih Bukhari',
      'benefits': ['Comprehensive praise', 'Spiritual reward'],
    },
  ];

  static const List<Map<String, dynamic>> eatingAthkar = [
    {
      'textArabic': 'بِسْمِ اللَّهِ',
      'textEnglish': 'Bismillah',
      'textTransliteration': 'Bismillah',
      'meaning': 'In the name of Allah.',
      'recommendedCount': 1,
      'source': 'Sahih Bukhari',
      'benefits': ['Blessing in food', 'Protection from harm'],
    },
    {
      'textArabic': 'الْحَمْدُ لِلَّهِ الَّذِي أَطْعَمَنِي هَٰذَا وَرَزَقَنِيهِ مِنْ غَيْرِ حَوْلٍ مِنِّي وَلَا قُوَّةٍ',
      'textEnglish': 'Alhamdu lillahi\'l-ladhi at\'amani hadha wa razaqanihi min ghayri hawlin minni wa la quwwah',
      'textTransliteration': 'Alhamdu lillahi\'l-ladhi at\'amani hadha wa razaqanihi min ghayri hawlin minni wa la quwwah',
      'meaning': 'Praise be to Allah who has fed me this and provided it for me without any might or power on my part.',
      'recommendedCount': 1,
      'source': 'Abu Dawud',
      'benefits': ['Gratitude for sustenance', 'Recognition of Allah\'s provision'],
    },
  ];

  static const List<Map<String, dynamic>> travelAthkar = [
    {
      'textArabic': 'سُبْحَانَ الَّذِي سَخَّرَ لَنَا هَٰذَا وَمَا كُنَّا لَهُ مُقْرِنِينَ',
      'textEnglish': 'Subhana\'l-ladhi sakhkhara lana hadha wa ma kunna lahu muqrinin',
      'textTransliteration': 'Subhana\'l-ladhi sakhkhara lana hadha wa ma kunna lahu muqrinin',
      'meaning': 'Glory be to Him who has subjected this to us, and we could never have it (by our efforts).',
      'recommendedCount': 1,
      'source': 'Abu Dawud',
      'benefits': ['Safe journey', 'Protection during travel'],
    },
    {
      'textArabic': 'اللَّهُمَّ إِنَّا نَسْأَلُكَ فِي سَفَرِنَا هَٰذَا الْبِرَّ وَالتَّقْوَىٰ',
      'textEnglish': 'Allahumma inna nas\'aluka fi safarina hadha\'l-birra wa\'t-taqwa',
      'textTransliteration': 'Allahumma inna nas\'aluka fi safarina hadha\'l-birra wa\'t-taqwa',
      'meaning': 'O Allah, we ask You on this our journey for goodness and piety.',
      'recommendedCount': 1,
      'source': 'Sahih Muslim',
      'benefits': ['Blessed journey', 'Spiritual guidance'],
    },
    {
      'textArabic': 'اللَّهُ أَكْبَرُ اللَّهُ أَكْبَرُ اللَّهُ أَكْبَرُ',
      'textEnglish': 'Allahu akbar, Allahu akbar, Allahu akbar',
      'textTransliteration': 'Allahu akbar, Allahu akbar, Allahu akbar',
      'meaning': 'Allah is the Greatest, Allah is the Greatest, Allah is the Greatest.',
      'recommendedCount': 3,
      'source': 'Sahih Bukhari',
      'benefits': ['Magnification of Allah', 'Travel protection'],
    },
  ];
}
