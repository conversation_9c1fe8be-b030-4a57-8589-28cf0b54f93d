import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/media_models.dart';
import '../services/shadow_player_providers.dart';

class VideoListView extends ConsumerWidget {
  final List<MediaFile> videos;

  const VideoListView({super.key, required this.videos});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: videos.length,
      itemBuilder: (context, index) {
        final video = videos[index];
        return VideoListItem(video: video);
      },
    );
  }
}

class VideoListItem extends ConsumerWidget {
  final MediaFile video;

  const VideoListItem({super.key, required this.video});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _playVideo(context, ref),
        onLongPress: () => _showVideoOptions(context, ref),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Video Thumbnail
              Container(
                width: 120,
                height: 68,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: const Color(0xFF2C3E50),
                ),
                child: Stack(
                  children: [
                    // Thumbnail Image
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: video.thumbnailPath != null
                          ? Image.asset(
                              video.thumbnailPath!,
                              width: double.infinity,
                              height: double.infinity,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  _buildDefaultThumbnail(),
                            )
                          : _buildDefaultThumbnail(),
                    ),

                    // Play Button Overlay
                    Center(
                      child: Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: const Icon(
                          Icons.play_arrow,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),

                    // Duration Badge
                    Positioned(
                      bottom: 4,
                      right: 4,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 4,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.8),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          _formatDuration(video.duration!),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),

              // Video Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    Text(
                      video.metadata.title?.isNotEmpty == true
                          ? video.metadata.title!
                          : video.path.split('/').last,
                      style: const TextStyle(
                        color: Color(0xFF2C3E50),
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),

                    // File Info
                    Row(
                      children: [
                        Icon(
                          Icons.video_file,
                          color: const Color(0xFF7F8C8D),
                          size: 14,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _getFileInfo(),
                          style: const TextStyle(
                            color: Color(0xFF7F8C8D),
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _formatFileSize(video.size),
                          style: const TextStyle(
                            color: Color(0xFF95A5A6),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),

                    // Resolution and Additional Info
                    Row(
                      children: [
                        if (video.metadata.resolution != null) ...[
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(
                                0xFF3498DB,
                              ).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              video.metadata.resolution!,
                              style: const TextStyle(
                                color: Color(0xFF3498DB),
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                        ],
                        // Play count removed - not available in player_models MediaFile
                        if (video.isFavorite)
                          const Icon(
                            Icons.favorite,
                            color: Color(0xFFE74C3C),
                            size: 12,
                          ),
                      ],
                    ),
                  ],
                ),
              ),

              // Action Button
              IconButton(
                onPressed: () => _showVideoOptions(context, ref),
                icon: const Icon(
                  Icons.more_vert,
                  color: Color(0xFF95A5A6),
                  size: 20,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDefaultThumbnail() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: const Color(0xFF34495E),
      child: const Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.video_file, color: Color(0xFFE74C3C), size: 24),
          SizedBox(height: 4),
          Text(
            'No Preview',
            style: TextStyle(color: Color(0xFFBDC3C7), fontSize: 8),
          ),
        ],
      ),
    );
  }

  String _getFileInfo() {
    final extension = video.path.split('.').last.toUpperCase();
    final codec = video.metadata.codec ?? extension;
    return codec;
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;

    if (hours > 0) {
      return '$hours:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '$minutes:${seconds.toString().padLeft(2, '0')}';
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  void _playVideo(BuildContext context, WidgetRef ref) {
    // Play the video using the media player service
    ref.read(mediaPlayerServiceProvider).playMedia(video);

    // Navigate to full-screen video player
    Navigator.of(
      context,
    ).pushNamed('/shadow-player/video-player', arguments: video);
  }

  void _showVideoOptions(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFF2C3E50),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Video Title
            Text(
              video.metadata.title?.isNotEmpty == true
                  ? video.metadata.title!
                  : video.path.split('/').last,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 16),

            // Options
            _buildOption(
              icon: Icons.play_arrow,
              title: 'Play Video',
              onTap: () {
                Navigator.pop(context);
                _playVideo(context, ref);
              },
            ),
            _buildOption(
              icon: video.isFavorite ? Icons.favorite : Icons.favorite_border,
              title: video.isFavorite
                  ? 'Remove from Favorites'
                  : 'Add to Favorites',
              onTap: () {
                Navigator.pop(context);
                ref
                    .read(allMediaFilesProvider.notifier)
                    .toggleFavorite(video.id);
              },
            ),
            _buildOption(
              icon: Icons.info,
              title: 'Video Information',
              onTap: () {
                Navigator.pop(context);
                // Show video info dialog
                _showVideoInfoDialog(context, video);
              },
            ),
            _buildOption(
              icon: Icons.share,
              title: 'Share Video',
              onTap: () {
                Navigator.pop(context);
                // Implement share functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Sharing ${video.metadata.title ?? video.name}...',
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOption({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        child: Row(
          children: [
            Icon(icon, color: const Color(0xFF3498DB), size: 24),
            const SizedBox(width: 16),
            Text(
              title,
              style: const TextStyle(color: Colors.white, fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  void _showVideoInfoDialog(BuildContext context, MediaFile video) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          video.metadata.title?.isNotEmpty == true
              ? video.metadata.title!
              : video.path.split('/').last,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('File Size', _formatFileSize(video.size)),
            _buildInfoRow(
              'Duration',
              _formatDuration(video.duration ?? Duration.zero),
            ),
            if (video.metadata.resolution != null)
              _buildInfoRow('Resolution', video.metadata.resolution!),
            if (video.metadata.codec != null)
              _buildInfoRow('Codec', video.metadata.codec!),
            _buildInfoRow('Path', video.path),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}
