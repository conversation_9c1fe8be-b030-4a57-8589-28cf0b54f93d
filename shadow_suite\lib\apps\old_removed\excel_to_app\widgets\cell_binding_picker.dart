import 'package:flutter/material.dart';
import '../models/excel_app_tool.dart';

class CellBindingPicker extends StatefulWidget {
  final ExcelAppTool tool;
  final String? initialCell;
  final Function(String?) onCellSelected;

  const CellBindingPicker({
    super.key,
    required this.tool,
    this.initialCell,
    required this.onCellSelected,
  });

  @override
  State<CellBindingPicker> createState() => _CellBindingPickerState();
}

class _CellBindingPickerState extends State<CellBindingPicker> {
  String? _selectedCell;
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();

  @override
  void initState() {
    super.initState();
    _selectedCell = widget.initialCell;
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 600,
        height: 500,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            _buildSelectedCellInfo(),
            const SizedBox(height: 16),
            Expanded(child: _buildSpreadsheetGrid()),
            const SizedBox(height: 16),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Icon(
          Icons.grid_on,
          color: Color(0xFF3498DB),
          size: 24,
        ),
        const SizedBox(width: 12),
        const Text(
          'Select Cell for Binding',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2C3E50),
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  Widget _buildSelectedCellInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE9ECEF)),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.info_outline,
            color: Color(0xFF3498DB),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Selected Cell: ${_selectedCell ?? 'None'}',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                if (_selectedCell != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    'Value: ${_getCellValue(_selectedCell!)}',
                    style: const TextStyle(
                      color: Color(0xFF7F8C8D),
                      fontSize: 12,
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (_selectedCell != null)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: const Color(0xFF27AE60),
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Text(
                'BOUND',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSpreadsheetGrid() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFE9ECEF)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Column headers
          Container(
            height: 40,
            decoration: const BoxDecoration(
              color: Color(0xFFF8F9FA),
              border: Border(
                bottom: BorderSide(color: Color(0xFFE9ECEF)),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 50,
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(color: Color(0xFFE9ECEF)),
                    ),
                  ),
                ),
                Expanded(
                  child: Scrollbar(
                    controller: _horizontalController,
                    scrollbarOrientation: ScrollbarOrientation.bottom,
                    child: SingleChildScrollView(
                      controller: _horizontalController,
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: List.generate(
                          widget.tool.spreadsheet.columns,
                          (index) => _buildColumnHeader(index + 1),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Grid content
          Expanded(
            child: Row(
              children: [
                // Row headers
                Container(
                  width: 50,
                  decoration: const BoxDecoration(
                    color: Color(0xFFF8F9FA),
                    border: Border(
                      right: BorderSide(color: Color(0xFFE9ECEF)),
                    ),
                  ),
                  child: Scrollbar(
                    controller: _verticalController,
                    child: SingleChildScrollView(
                      controller: _verticalController,
                      child: Column(
                        children: List.generate(
                          widget.tool.spreadsheet.rows,
                          (index) => _buildRowHeader(index + 1),
                        ),
                      ),
                    ),
                  ),
                ),
                // Cells
                Expanded(
                  child: Scrollbar(
                    controller: _horizontalController,
                    scrollbarOrientation: ScrollbarOrientation.bottom,
                    child: Scrollbar(
                      controller: _verticalController,
                      child: SingleChildScrollView(
                        controller: _horizontalController,
                        scrollDirection: Axis.horizontal,
                        child: SingleChildScrollView(
                          controller: _verticalController,
                          child: _buildCellGrid(),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColumnHeader(int columnIndex) {
    final columnName = _getColumnName(columnIndex);
    return Container(
      width: 80,
      height: 40,
      decoration: const BoxDecoration(
        border: Border(
          right: BorderSide(color: Color(0xFFE9ECEF)),
        ),
      ),
      child: Center(
        child: Text(
          columnName,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
          ),
        ),
      ),
    );
  }

  Widget _buildRowHeader(int rowIndex) {
    return Container(
      width: 50,
      height: 30,
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE9ECEF)),
        ),
      ),
      child: Center(
        child: Text(
          rowIndex.toString(),
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
          ),
        ),
      ),
    );
  }

  Widget _buildCellGrid() {
    return Column(
      children: List.generate(
        widget.tool.spreadsheet.rows,
        (rowIndex) => Row(
          children: List.generate(
            widget.tool.spreadsheet.columns,
            (colIndex) => _buildCell(rowIndex + 1, colIndex + 1),
          ),
        ),
      ),
    );
  }

  Widget _buildCell(int row, int column) {
    final cellAddress = '${_getColumnName(column)}$row';
    final cell = widget.tool.spreadsheet.cells[cellAddress];
    final isSelected = _selectedCell == cellAddress;
    final hasValue = cell?.value != null && cell!.value.toString().isNotEmpty;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedCell = cellAddress;
        });
      },
      child: Container(
        width: 80,
        height: 30,
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF3498DB).withValues(alpha: 0.2)
              : hasValue
                  ? const Color(0xFF27AE60).withValues(alpha: 0.1)
                  : Colors.white,
          border: Border.all(
            color: isSelected
                ? const Color(0xFF3498DB)
                : const Color(0xFFE9ECEF),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Center(
          child: Text(
            _getCellDisplayValue(cell),
            style: TextStyle(
              fontSize: 11,
              color: isSelected
                  ? const Color(0xFF3498DB)
                  : hasValue
                      ? const Color(0xFF27AE60)
                      : const Color(0xFF7F8C8D),
              fontWeight: isSelected || hasValue ? FontWeight.w600 : FontWeight.normal,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        TextButton(
          onPressed: () {
            setState(() {
              _selectedCell = null;
            });
          },
          child: const Text('Clear Selection'),
        ),
        const Spacer(),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        const SizedBox(width: 12),
        ElevatedButton(
          onPressed: _selectedCell != null
              ? () {
                  widget.onCellSelected(_selectedCell);
                  Navigator.of(context).pop();
                }
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF3498DB),
            foregroundColor: Colors.white,
          ),
          child: const Text('Bind Cell'),
        ),
      ],
    );
  }

  String _getColumnName(int columnIndex) {
    String result = '';
    while (columnIndex > 0) {
      columnIndex--;
      result = String.fromCharCode(65 + (columnIndex % 26)) + result;
      columnIndex ~/= 26;
    }
    return result;
  }

  String _getCellValue(String cellAddress) {
    final cell = widget.tool.spreadsheet.cells[cellAddress];
    if (cell == null) return '';
    
    if (cell.isFormula && cell.formula != null) {
      return cell.formula!;
    }
    
    return cell.value?.toString() ?? '';
  }

  String _getCellDisplayValue(ExcelCell? cell) {
    if (cell == null) return '';
    
    final value = cell.value?.toString() ?? '';
    if (value.length > 8) {
      return '${value.substring(0, 8)}...';
    }
    return value;
  }
}
