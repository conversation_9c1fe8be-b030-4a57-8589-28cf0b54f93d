import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// Removed unused imports - AppLayout is deprecated

final sidebarCollapsedProvider = StateProvider<bool>((ref) => false);

class AppLayout extends ConsumerWidget {
  final Widget child;

  const AppLayout({super.key, required this.child});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // AppLayout is deprecated - use ResponsiveLayout directly in router
    // This prevents duplicate sidebar rendering
    return child;
  }
}
