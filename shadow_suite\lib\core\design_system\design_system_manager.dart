import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class DesignSystemManager {
  // Material Design 3 Color Schemes
  static const Map<String, ColorScheme> materialColorSchemes = {
    'material_blue': ColorScheme(
      brightness: Brightness.light,
      primary: Color(0xFF1976D2),
      onPrimary: Color(0xFFFFFFFF),
      secondary: Color(0xFF03DAC6),
      onSecondary: Color(0xFF000000),
      error: Color(0xFFB00020),
      onError: Color(0xFFFFFFFF),
      surface: Color(0xFFFAFAFA),
      onSurface: Color(0xFF000000),
    ),
    'material_green': ColorScheme(
      brightness: Brightness.light,
      primary: Color(0xFF4CAF50),
      onPrimary: Color(0xFFFFFFFF),
      secondary: Color(0xFF81C784),
      onSecondary: Color(0xFF000000),
      error: Color(0xFFB00020),
      onError: Color(0xFFFFFFFF),
      surface: Color(0xFFFFFFFF),
      onSurface: Color(0xFF000000),
      surfaceContainerHighest: Color(0xFFF1F8E9),
      onSurfaceVariant: Color(0xFF000000),
    ),
    'material_purple': ColorScheme(
      brightness: Brightness.light,
      primary: Color(0xFF9C27B0),
      onPrimary: Color(0xFFFFFFFF),
      secondary: Color(0xFFBA68C8),
      onSecondary: Color(0xFF000000),
      error: Color(0xFFB00020),
      onError: Color(0xFFFFFFFF),
      surface: Color(0xFFFFFFFF),
      onSurface: Color(0xFF000000),
      surfaceContainerHighest: Color(0xFFF3E5F5),
      onSurfaceVariant: Color(0xFF000000),
    ),
    'material_orange': ColorScheme(
      brightness: Brightness.light,
      primary: Color(0xFFFF9800),
      onPrimary: Color(0xFFFFFFFF),
      secondary: Color(0xFFFFB74D),
      onSecondary: Color(0xFF000000),
      error: Color(0xFFB00020),
      onError: Color(0xFFFFFFFF),
      surface: Color(0xFFFFFFFF),
      onSurface: Color(0xFF000000),
      surfaceContainerHighest: Color(0xFFFFF3E0),
      onSurfaceVariant: Color(0xFF000000),
    ),
  };

  // Dark theme variants
  static const Map<String, ColorScheme> materialDarkColorSchemes = {
    'material_blue_dark': ColorScheme(
      brightness: Brightness.dark,
      primary: Color(0xFF90CAF9),
      onPrimary: Color(0xFF000000),
      secondary: Color(0xFF03DAC6),
      onSecondary: Color(0xFF000000),
      error: Color(0xFFCF6679),
      onError: Color(0xFF000000),
      surface: Color(0xFF121212),
      onSurface: Color(0xFFFFFFFF),
      surfaceContainerHighest: Color(0xFF121212),
      onSurfaceVariant: Color(0xFFFFFFFF),
    ),
    'material_green_dark': ColorScheme(
      brightness: Brightness.dark,
      primary: Color(0xFF81C784),
      onPrimary: Color(0xFF000000),
      secondary: Color(0xFFA5D6A7),
      onSecondary: Color(0xFF000000),
      error: Color(0xFFCF6679),
      onError: Color(0xFF000000),
      surface: Color(0xFF1B5E20),
      onSurface: Color(0xFFFFFFFF),
      surfaceContainerHighest: Color(0xFF0D2818),
      onSurfaceVariant: Color(0xFFFFFFFF),
    ),
  };

  // Typography presets
  static const Map<String, TextTheme> typographyPresets = {
    'default': TextTheme(
      displayLarge: TextStyle(fontSize: 57, fontWeight: FontWeight.w400),
      displayMedium: TextStyle(fontSize: 45, fontWeight: FontWeight.w400),
      displaySmall: TextStyle(fontSize: 36, fontWeight: FontWeight.w400),
      headlineLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.w400),
      headlineMedium: TextStyle(fontSize: 28, fontWeight: FontWeight.w400),
      headlineSmall: TextStyle(fontSize: 24, fontWeight: FontWeight.w400),
      titleLarge: TextStyle(fontSize: 22, fontWeight: FontWeight.w500),
      titleMedium: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
      titleSmall: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
      bodyLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.w400),
      bodyMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
      bodySmall: TextStyle(fontSize: 12, fontWeight: FontWeight.w400),
      labelLarge: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
      labelMedium: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
      labelSmall: TextStyle(fontSize: 11, fontWeight: FontWeight.w500),
    ),
    'compact': TextTheme(
      displayLarge: TextStyle(fontSize: 48, fontWeight: FontWeight.w400),
      displayMedium: TextStyle(fontSize: 38, fontWeight: FontWeight.w400),
      displaySmall: TextStyle(fontSize: 30, fontWeight: FontWeight.w400),
      headlineLarge: TextStyle(fontSize: 28, fontWeight: FontWeight.w400),
      headlineMedium: TextStyle(fontSize: 24, fontWeight: FontWeight.w400),
      headlineSmall: TextStyle(fontSize: 20, fontWeight: FontWeight.w400),
      titleLarge: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
      titleMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
      titleSmall: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
      bodyLarge: TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
      bodyMedium: TextStyle(fontSize: 12, fontWeight: FontWeight.w400),
      bodySmall: TextStyle(fontSize: 10, fontWeight: FontWeight.w400),
      labelLarge: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
      labelMedium: TextStyle(fontSize: 10, fontWeight: FontWeight.w500),
      labelSmall: TextStyle(fontSize: 9, fontWeight: FontWeight.w500),
    ),
    'comfortable': TextTheme(
      displayLarge: TextStyle(fontSize: 64, fontWeight: FontWeight.w400),
      displayMedium: TextStyle(fontSize: 52, fontWeight: FontWeight.w400),
      displaySmall: TextStyle(fontSize: 42, fontWeight: FontWeight.w400),
      headlineLarge: TextStyle(fontSize: 36, fontWeight: FontWeight.w400),
      headlineMedium: TextStyle(fontSize: 32, fontWeight: FontWeight.w400),
      headlineSmall: TextStyle(fontSize: 28, fontWeight: FontWeight.w400),
      titleLarge: TextStyle(fontSize: 26, fontWeight: FontWeight.w500),
      titleMedium: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
      titleSmall: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
      bodyLarge: TextStyle(fontSize: 18, fontWeight: FontWeight.w400),
      bodyMedium: TextStyle(fontSize: 16, fontWeight: FontWeight.w400),
      bodySmall: TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
      labelLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
      labelMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
      labelSmall: TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
    ),
  };

  // Layout presets
  static const Map<String, LayoutConfig> layoutPresets = {
    'compact': LayoutConfig(
      padding: EdgeInsets.all(8.0),
      spacing: 8.0,
      borderRadius: 4.0,
      elevation: 1.0,
    ),
    'default': LayoutConfig(
      padding: EdgeInsets.all(16.0),
      spacing: 16.0,
      borderRadius: 8.0,
      elevation: 2.0,
    ),
    'comfortable': LayoutConfig(
      padding: EdgeInsets.all(24.0),
      spacing: 24.0,
      borderRadius: 12.0,
      elevation: 4.0,
    ),
  };

  // Accessibility configurations
  static const Map<String, AccessibilityConfig> accessibilityPresets = {
    'standard': AccessibilityConfig(
      minimumTouchTargetSize: Size(44, 44),
      focusHighlightColor: Color(0xFF2196F3),
      highContrast: false,
      reduceMotion: false,
    ),
    'high_contrast': AccessibilityConfig(
      minimumTouchTargetSize: Size(48, 48),
      focusHighlightColor: Color(0xFFFFFF00),
      highContrast: true,
      reduceMotion: false,
    ),
    'reduced_motion': AccessibilityConfig(
      minimumTouchTargetSize: Size(48, 48),
      focusHighlightColor: Color(0xFF2196F3),
      highContrast: false,
      reduceMotion: true,
    ),
  };

  // Generate custom color palette from a seed color
  static ColorScheme generateColorScheme(Color seedColor, {bool isDark = false}) {
    return ColorScheme.fromSeed(
      seedColor: seedColor,
      brightness: isDark ? Brightness.dark : Brightness.light,
    );
  }

  // Create a complete theme from presets
  static ThemeData createTheme({
    required String colorSchemeKey,
    required String typographyKey,
    required String layoutKey,
    required String accessibilityKey,
    bool isDark = false,
  }) {
    final colorSchemes = isDark ? materialDarkColorSchemes : materialColorSchemes;
    final colorScheme = colorSchemes[colorSchemeKey] ?? materialColorSchemes['material_blue']!;
    final textTheme = typographyPresets[typographyKey] ?? typographyPresets['default']!;
    final layout = layoutPresets[layoutKey] ?? layoutPresets['default']!;
    final accessibility = accessibilityPresets[accessibilityKey] ?? accessibilityPresets['standard']!;

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      textTheme: textTheme,
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        elevation: layout.elevation,
      ),
      cardTheme: CardThemeData(
        elevation: layout.elevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(layout.borderRadius),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          minimumSize: accessibility.minimumTouchTargetSize,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(layout.borderRadius),
          ),
          padding: layout.padding,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(layout.borderRadius),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(layout.borderRadius),
          borderSide: BorderSide(color: accessibility.focusHighlightColor, width: 2),
        ),
        contentPadding: layout.padding,
      ),
      focusColor: accessibility.focusHighlightColor,
    );
  }

  // Validate color contrast for accessibility
  static bool validateColorContrast(Color foreground, Color background) {
    final luminance1 = foreground.computeLuminance();
    final luminance2 = background.computeLuminance();
    final ratio = (luminance1 > luminance2)
        ? (luminance1 + 0.05) / (luminance2 + 0.05)
        : (luminance2 + 0.05) / (luminance1 + 0.05);
    
    return ratio >= 4.5; // WCAG AA standard
  }

  // Generate haptic feedback patterns
  static void triggerHapticFeedback(HapticFeedbackType type) {
    switch (type) {
      case HapticFeedbackType.light:
        HapticFeedback.lightImpact();
        break;
      case HapticFeedbackType.medium:
        HapticFeedback.mediumImpact();
        break;
      case HapticFeedbackType.heavy:
        HapticFeedback.heavyImpact();
        break;
      case HapticFeedbackType.selection:
        HapticFeedback.selectionClick();
        break;
    }
  }
}

// Configuration classes
class LayoutConfig {
  final EdgeInsets padding;
  final double spacing;
  final double borderRadius;
  final double elevation;

  const LayoutConfig({
    required this.padding,
    required this.spacing,
    required this.borderRadius,
    required this.elevation,
  });
}

class AccessibilityConfig {
  final Size minimumTouchTargetSize;
  final Color focusHighlightColor;
  final bool highContrast;
  final bool reduceMotion;

  const AccessibilityConfig({
    required this.minimumTouchTargetSize,
    required this.focusHighlightColor,
    required this.highContrast,
    required this.reduceMotion,
  });
}

enum HapticFeedbackType { light, medium, heavy, selection }
