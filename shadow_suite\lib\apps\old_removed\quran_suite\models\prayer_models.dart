/// Comprehensive Prayer models for Islamic prayer times and Qibla
library;

/// Five daily prayers in Islam
enum PrayerType {
  fajr, // Dawn prayer
  sunrise, // Sunrise (not a prayer, but important for timing)
  dhuhr, // Noon prayer
  asr, // Afternoon prayer
  maghrib, // Sunset prayer
  isha, // Night prayer
}

/// Prayer calculation methods used by different Islamic organizations
enum PrayerCalculationMethod {
  isna, // Islamic Society of North America
  muslimWorldLeague, // Muslim World League
  ummAlQura, // Umm Al-Qura University, Mecca
  egyptian, // Egyptian General Authority of Survey
  karachi, // University of Islamic Sciences, Karachi
}

/// Asr calculation methods
enum AsrCalculationMethod {
  standard, // Shafi, Maliki, Hanbali schools
  hanafi, // Hanafi school
}

/// Location data for prayer time calculations
class LocationData {
  final double latitude;
  final double longitude;
  final String city;
  final String country;
  final String timezone;
  final double? elevation; // Optional elevation in meters

  const LocationData({
    required this.latitude,
    required this.longitude,
    required this.city,
    required this.country,
    required this.timezone,
    this.elevation,
  });

  /// Get location description
  String get description => '$city, $country';

  /// Get coordinates as string
  String get coordinates =>
      '${latitude.toStringAsFixed(4)}, ${longitude.toStringAsFixed(4)}';

  Map<String, dynamic> toJson() => {
    'latitude': latitude,
    'longitude': longitude,
    'city': city,
    'country': country,
    'timezone': timezone,
    'elevation': elevation,
  };

  factory LocationData.fromJson(Map<String, dynamic> json) => LocationData(
    latitude: json['latitude'],
    longitude: json['longitude'],
    city: json['city'],
    country: json['country'],
    timezone: json['timezone'],
    elevation: json['elevation'],
  );
}

/// Prayer calculation parameters
class CalculationParameters {
  final double fajrAngle; // Angle below horizon for Fajr
  final double ishaAngle; // Angle below horizon for Isha
  final double maghribAngle; // Angle below horizon for Maghrib
  final AsrCalculationMethod asrMethod;

  const CalculationParameters({
    required this.fajrAngle,
    required this.ishaAngle,
    required this.maghribAngle,
    required this.asrMethod,
  });

  Map<String, dynamic> toJson() => {
    'fajrAngle': fajrAngle,
    'ishaAngle': ishaAngle,
    'maghribAngle': maghribAngle,
    'asrMethod': asrMethod.name,
  };

  factory CalculationParameters.fromJson(Map<String, dynamic> json) =>
      CalculationParameters(
        fajrAngle: json['fajrAngle'],
        ishaAngle: json['ishaAngle'],
        maghribAngle: json['maghribAngle'],
        asrMethod: AsrCalculationMethod.values.firstWhere(
          (e) => e.name == json['asrMethod'],
        ),
      );
}

/// Prayer time information
class PrayerTimeInfo {
  final PrayerType prayer;
  final DateTime time;
  final Duration timeRemaining;

  const PrayerTimeInfo({
    required this.prayer,
    required this.time,
    required this.timeRemaining,
  });

  /// Get prayer name in English
  String get prayerName {
    switch (prayer) {
      case PrayerType.fajr:
        return 'Fajr';
      case PrayerType.sunrise:
        return 'Sunrise';
      case PrayerType.dhuhr:
        return 'Dhuhr';
      case PrayerType.asr:
        return 'Asr';
      case PrayerType.maghrib:
        return 'Maghrib';
      case PrayerType.isha:
        return 'Isha';
    }
  }

  /// Get prayer name in Arabic
  String get prayerNameArabic {
    switch (prayer) {
      case PrayerType.fajr:
        return 'الفجر';
      case PrayerType.sunrise:
        return 'الشروق';
      case PrayerType.dhuhr:
        return 'الظهر';
      case PrayerType.asr:
        return 'العصر';
      case PrayerType.maghrib:
        return 'المغرب';
      case PrayerType.isha:
        return 'العشاء';
    }
  }

  /// Get time remaining as formatted string
  String get timeRemainingFormatted {
    if (timeRemaining.isNegative) return 'Passed';

    final hours = timeRemaining.inHours;
    final minutes = timeRemaining.inMinutes % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  /// Check if prayer time has passed
  bool get hasPassed => timeRemaining.isNegative;

  Map<String, dynamic> toJson() => {
    'prayer': prayer.name,
    'time': time.toIso8601String(),
    'timeRemaining': timeRemaining.inSeconds,
  };

  factory PrayerTimeInfo.fromJson(Map<String, dynamic> json) => PrayerTimeInfo(
    prayer: PrayerType.values.firstWhere((e) => e.name == json['prayer']),
    time: DateTime.parse(json['time']),
    timeRemaining: Duration(seconds: json['timeRemaining']),
  );
}

/// Qibla direction information
class QiblaInfo {
  final double direction; // Direction in degrees from North
  final double distance; // Distance to Kaaba in kilometers
  final LocationData userLocation;

  const QiblaInfo({
    required this.direction,
    required this.distance,
    required this.userLocation,
  });

  /// Get direction as compass bearing
  String get compassBearing {
    const directions = [
      'N',
      'NNE',
      'NE',
      'ENE',
      'E',
      'ESE',
      'SE',
      'SSE',
      'S',
      'SSW',
      'SW',
      'WSW',
      'W',
      'WNW',
      'NW',
      'NNW',
    ];
    final index = ((direction + 11.25) / 22.5).floor() % 16;
    return directions[index];
  }

  /// Get formatted direction
  String get formattedDirection => '${direction.toStringAsFixed(1)}°';

  /// Get formatted distance
  String get formattedDistance => '${distance.toStringAsFixed(0)} km';

  Map<String, dynamic> toJson() => {
    'direction': direction,
    'distance': distance,
    'userLocation': userLocation.toJson(),
  };

  factory QiblaInfo.fromJson(Map<String, dynamic> json) => QiblaInfo(
    direction: json['direction'],
    distance: json['distance'],
    userLocation: LocationData.fromJson(json['userLocation']),
  );
}

/// Prayer notification settings
class PrayerNotificationSettings {
  final bool enabled;
  final Map<PrayerType, bool> prayerNotifications;
  final Map<PrayerType, int> reminderMinutes; // Minutes before prayer
  final bool playAdhan;
  final String adhanSound;
  final double volume;
  final bool vibrate;

  const PrayerNotificationSettings({
    required this.enabled,
    required this.prayerNotifications,
    required this.reminderMinutes,
    required this.playAdhan,
    required this.adhanSound,
    required this.volume,
    required this.vibrate,
  });

  /// Default notification settings
  factory PrayerNotificationSettings.defaultSettings() =>
      PrayerNotificationSettings(
        enabled: true,
        prayerNotifications: {
          for (final prayer in PrayerType.values) prayer: true,
        },
        reminderMinutes: {for (final prayer in PrayerType.values) prayer: 5},
        playAdhan: true,
        adhanSound: 'default_adhan.mp3',
        volume: 0.8,
        vibrate: true,
      );

  Map<String, dynamic> toJson() => {
    'enabled': enabled,
    'prayerNotifications': prayerNotifications.map(
      (k, v) => MapEntry(k.name, v),
    ),
    'reminderMinutes': reminderMinutes.map((k, v) => MapEntry(k.name, v)),
    'playAdhan': playAdhan,
    'adhanSound': adhanSound,
    'volume': volume,
    'vibrate': vibrate,
  };

  factory PrayerNotificationSettings.fromJson(Map<String, dynamic> json) =>
      PrayerNotificationSettings(
        enabled: json['enabled'],
        prayerNotifications:
            (json['prayerNotifications'] as Map<String, dynamic>).map(
              (k, v) => MapEntry(
                PrayerType.values.firstWhere((e) => e.name == k),
                v as bool,
              ),
            ),
        reminderMinutes: (json['reminderMinutes'] as Map<String, dynamic>).map(
          (k, v) => MapEntry(
            PrayerType.values.firstWhere((e) => e.name == k),
            v as int,
          ),
        ),
        playAdhan: json['playAdhan'],
        adhanSound: json['adhanSound'],
        volume: json['volume'],
        vibrate: json['vibrate'],
      );
}

/// Prayer tracking for spiritual progress
class PrayerTracking {
  final DateTime date;
  final Map<PrayerType, bool> prayersCompleted;
  final Map<PrayerType, DateTime?> prayerTimes;
  final Map<PrayerType, bool> prayedOnTime;
  final Map<PrayerType, bool> prayedInCongregation;

  const PrayerTracking({
    required this.date,
    required this.prayersCompleted,
    required this.prayerTimes,
    required this.prayedOnTime,
    required this.prayedInCongregation,
  });

  /// Get completion percentage for the day
  double get completionPercentage {
    final totalPrayers = PrayerType.values
        .where((p) => p != PrayerType.sunrise)
        .length;
    final completedCount = prayersCompleted.values
        .where((completed) => completed)
        .length;
    return (completedCount / totalPrayers) * 100;
  }

  /// Check if all prayers are completed
  bool get allPrayersCompleted {
    return PrayerType.values
        .where((p) => p != PrayerType.sunrise)
        .every((prayer) => prayersCompleted[prayer] == true);
  }

  /// Get count of prayers prayed on time
  int get onTimePrayerCount {
    return prayedOnTime.values.where((onTime) => onTime).length;
  }

  /// Get count of prayers prayed in congregation
  int get congregationPrayerCount {
    return prayedInCongregation.values
        .where((congregation) => congregation)
        .length;
  }

  Map<String, dynamic> toJson() => {
    'date': date.toIso8601String(),
    'prayersCompleted': prayersCompleted.map((k, v) => MapEntry(k.name, v)),
    'prayerTimes': prayerTimes.map(
      (k, v) => MapEntry(k.name, v?.toIso8601String()),
    ),
    'prayedOnTime': prayedOnTime.map((k, v) => MapEntry(k.name, v)),
    'prayedInCongregation': prayedInCongregation.map(
      (k, v) => MapEntry(k.name, v),
    ),
  };

  factory PrayerTracking.fromJson(Map<String, dynamic> json) => PrayerTracking(
    date: DateTime.parse(json['date']),
    prayersCompleted: (json['prayersCompleted'] as Map<String, dynamic>).map(
      (k, v) =>
          MapEntry(PrayerType.values.firstWhere((e) => e.name == k), v as bool),
    ),
    prayerTimes: (json['prayerTimes'] as Map<String, dynamic>).map(
      (k, v) => MapEntry(
        PrayerType.values.firstWhere((e) => e.name == k),
        v != null ? DateTime.parse(v) : null,
      ),
    ),
    prayedOnTime: (json['prayedOnTime'] as Map<String, dynamic>).map(
      (k, v) =>
          MapEntry(PrayerType.values.firstWhere((e) => e.name == k), v as bool),
    ),
    prayedInCongregation: (json['prayedInCongregation'] as Map<String, dynamic>)
        .map(
          (k, v) => MapEntry(
            PrayerType.values.firstWhere((e) => e.name == k),
            v as bool,
          ),
        ),
  );
}
