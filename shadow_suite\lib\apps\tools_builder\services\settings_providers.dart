import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Enums for settings
enum AppThemeMode { light, dark, system }

// Settings State Class
class ToolsBuilderSettings {
  // General Settings
  final bool autoSaveEnabled;
  final bool confirmDelete;
  final bool showTips;
  final AppThemeMode theme;
  final String language;
  final int maxUndoSteps;
  final int cacheSize;

  // Spreadsheet Settings
  final bool showGridLines;
  final bool showRowNumbers;
  final bool showColumnHeaders;
  final bool autoComplete;
  final bool autoFill;
  final double defaultCellWidth;
  final double defaultCellHeight;

  // UI Builder Settings
  final bool showGrid;
  final bool snapToGrid;
  final double gridSize;
  final bool showComponentOutlines;
  final bool showComponentLabels;
  final bool autoSelectOnDrop;

  // Formula Settings
  final bool autoCalculate;
  final bool showFormulaBar;
  final bool highlightDependencies;
  final bool showFormulaErrors;
  final bool errorNotifications;

  // Import/Export Settings
  final bool preserveFormatting;
  final bool preserveFormulas;
  final bool autoDetectDataTypes;
  final String defaultExportFormat;
  final int compressionLevel;

  // Advanced Settings
  final bool enableBackups;
  final bool encryptData;
  final bool debugMode;
  final bool showPerformanceMetrics;

  const ToolsBuilderSettings({
    // General defaults
    this.autoSaveEnabled = true,
    this.confirmDelete = true,
    this.showTips = true,
    this.theme = AppThemeMode.system,
    this.language = 'English',
    this.maxUndoSteps = 50,
    this.cacheSize = 100,

    // Spreadsheet defaults
    this.showGridLines = true,
    this.showRowNumbers = true,
    this.showColumnHeaders = true,
    this.autoComplete = true,
    this.autoFill = true,
    this.defaultCellWidth = 100.0,
    this.defaultCellHeight = 30.0,

    // UI Builder defaults
    this.showGrid = true,
    this.snapToGrid = true,
    this.gridSize = 10.0,
    this.showComponentOutlines = true,
    this.showComponentLabels = false,
    this.autoSelectOnDrop = true,

    // Formula defaults
    this.autoCalculate = true,
    this.showFormulaBar = true,
    this.highlightDependencies = true,
    this.showFormulaErrors = true,
    this.errorNotifications = true,

    // Import/Export defaults
    this.preserveFormatting = true,
    this.preserveFormulas = true,
    this.autoDetectDataTypes = true,
    this.defaultExportFormat = 'xlsx',
    this.compressionLevel = 6,

    // Advanced defaults
    this.enableBackups = true,
    this.encryptData = false,
    this.debugMode = false,
    this.showPerformanceMetrics = false,
  });

  ToolsBuilderSettings copyWith({
    bool? autoSaveEnabled,
    bool? confirmDelete,
    bool? showTips,
    AppThemeMode? theme,
    String? language,
    int? maxUndoSteps,
    int? cacheSize,
    bool? showGridLines,
    bool? showRowNumbers,
    bool? showColumnHeaders,
    bool? autoComplete,
    bool? autoFill,
    double? defaultCellWidth,
    double? defaultCellHeight,
    bool? showGrid,
    bool? snapToGrid,
    double? gridSize,
    bool? showComponentOutlines,
    bool? showComponentLabels,
    bool? autoSelectOnDrop,
    bool? autoCalculate,
    bool? showFormulaBar,
    bool? highlightDependencies,
    bool? showFormulaErrors,
    bool? errorNotifications,
    bool? preserveFormatting,
    bool? preserveFormulas,
    bool? autoDetectDataTypes,
    String? defaultExportFormat,
    int? compressionLevel,
    bool? enableBackups,
    bool? encryptData,
    bool? debugMode,
    bool? showPerformanceMetrics,
  }) {
    return ToolsBuilderSettings(
      autoSaveEnabled: autoSaveEnabled ?? this.autoSaveEnabled,
      confirmDelete: confirmDelete ?? this.confirmDelete,
      showTips: showTips ?? this.showTips,
      theme: theme ?? this.theme,
      language: language ?? this.language,
      maxUndoSteps: maxUndoSteps ?? this.maxUndoSteps,
      cacheSize: cacheSize ?? this.cacheSize,
      showGridLines: showGridLines ?? this.showGridLines,
      showRowNumbers: showRowNumbers ?? this.showRowNumbers,
      showColumnHeaders: showColumnHeaders ?? this.showColumnHeaders,
      autoComplete: autoComplete ?? this.autoComplete,
      autoFill: autoFill ?? this.autoFill,
      defaultCellWidth: defaultCellWidth ?? this.defaultCellWidth,
      defaultCellHeight: defaultCellHeight ?? this.defaultCellHeight,
      showGrid: showGrid ?? this.showGrid,
      snapToGrid: snapToGrid ?? this.snapToGrid,
      gridSize: gridSize ?? this.gridSize,
      showComponentOutlines: showComponentOutlines ?? this.showComponentOutlines,
      showComponentLabels: showComponentLabels ?? this.showComponentLabels,
      autoSelectOnDrop: autoSelectOnDrop ?? this.autoSelectOnDrop,
      autoCalculate: autoCalculate ?? this.autoCalculate,
      showFormulaBar: showFormulaBar ?? this.showFormulaBar,
      highlightDependencies: highlightDependencies ?? this.highlightDependencies,
      showFormulaErrors: showFormulaErrors ?? this.showFormulaErrors,
      errorNotifications: errorNotifications ?? this.errorNotifications,
      preserveFormatting: preserveFormatting ?? this.preserveFormatting,
      preserveFormulas: preserveFormulas ?? this.preserveFormulas,
      autoDetectDataTypes: autoDetectDataTypes ?? this.autoDetectDataTypes,
      defaultExportFormat: defaultExportFormat ?? this.defaultExportFormat,
      compressionLevel: compressionLevel ?? this.compressionLevel,
      enableBackups: enableBackups ?? this.enableBackups,
      encryptData: encryptData ?? this.encryptData,
      debugMode: debugMode ?? this.debugMode,
      showPerformanceMetrics: showPerformanceMetrics ?? this.showPerformanceMetrics,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'autoSaveEnabled': autoSaveEnabled,
      'confirmDelete': confirmDelete,
      'showTips': showTips,
      'theme': theme.name,
      'language': language,
      'maxUndoSteps': maxUndoSteps,
      'cacheSize': cacheSize,
      'showGridLines': showGridLines,
      'showRowNumbers': showRowNumbers,
      'showColumnHeaders': showColumnHeaders,
      'autoComplete': autoComplete,
      'autoFill': autoFill,
      'defaultCellWidth': defaultCellWidth,
      'defaultCellHeight': defaultCellHeight,
      'showGrid': showGrid,
      'snapToGrid': snapToGrid,
      'gridSize': gridSize,
      'showComponentOutlines': showComponentOutlines,
      'showComponentLabels': showComponentLabels,
      'autoSelectOnDrop': autoSelectOnDrop,
      'autoCalculate': autoCalculate,
      'showFormulaBar': showFormulaBar,
      'highlightDependencies': highlightDependencies,
      'showFormulaErrors': showFormulaErrors,
      'errorNotifications': errorNotifications,
      'preserveFormatting': preserveFormatting,
      'preserveFormulas': preserveFormulas,
      'autoDetectDataTypes': autoDetectDataTypes,
      'defaultExportFormat': defaultExportFormat,
      'compressionLevel': compressionLevel,
      'enableBackups': enableBackups,
      'encryptData': encryptData,
      'debugMode': debugMode,
      'showPerformanceMetrics': showPerformanceMetrics,
    };
  }

  factory ToolsBuilderSettings.fromMap(Map<String, dynamic> map) {
    return ToolsBuilderSettings(
      autoSaveEnabled: map['autoSaveEnabled'] ?? true,
      confirmDelete: map['confirmDelete'] ?? true,
      showTips: map['showTips'] ?? true,
      theme: AppThemeMode.values.firstWhere(
        (e) => e.name == map['theme'],
        orElse: () => AppThemeMode.system,
      ),
      language: map['language'] ?? 'English',
      maxUndoSteps: map['maxUndoSteps'] ?? 50,
      cacheSize: map['cacheSize'] ?? 100,
      showGridLines: map['showGridLines'] ?? true,
      showRowNumbers: map['showRowNumbers'] ?? true,
      showColumnHeaders: map['showColumnHeaders'] ?? true,
      autoComplete: map['autoComplete'] ?? true,
      autoFill: map['autoFill'] ?? true,
      defaultCellWidth: map['defaultCellWidth']?.toDouble() ?? 100.0,
      defaultCellHeight: map['defaultCellHeight']?.toDouble() ?? 30.0,
      showGrid: map['showGrid'] ?? true,
      snapToGrid: map['snapToGrid'] ?? true,
      gridSize: map['gridSize']?.toDouble() ?? 10.0,
      showComponentOutlines: map['showComponentOutlines'] ?? true,
      showComponentLabels: map['showComponentLabels'] ?? false,
      autoSelectOnDrop: map['autoSelectOnDrop'] ?? true,
      autoCalculate: map['autoCalculate'] ?? true,
      showFormulaBar: map['showFormulaBar'] ?? true,
      highlightDependencies: map['highlightDependencies'] ?? true,
      showFormulaErrors: map['showFormulaErrors'] ?? true,
      errorNotifications: map['errorNotifications'] ?? true,
      preserveFormatting: map['preserveFormatting'] ?? true,
      preserveFormulas: map['preserveFormulas'] ?? true,
      autoDetectDataTypes: map['autoDetectDataTypes'] ?? true,
      defaultExportFormat: map['defaultExportFormat'] ?? 'xlsx',
      compressionLevel: map['compressionLevel'] ?? 6,
      enableBackups: map['enableBackups'] ?? true,
      encryptData: map['encryptData'] ?? false,
      debugMode: map['debugMode'] ?? false,
      showPerformanceMetrics: map['showPerformanceMetrics'] ?? false,
    );
  }
}

// Settings Notifier
class SettingsNotifier extends StateNotifier<ToolsBuilderSettings> {
  SettingsNotifier() : super(const ToolsBuilderSettings()) {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString('tools_builder_settings');

      if (settingsJson != null) {
        final settingsMap = jsonDecode(settingsJson) as Map<String, dynamic>;
        state = ToolsBuilderSettings.fromMap(settingsMap);
      }
    } catch (e) {
      // Use default settings if loading fails
      state = const ToolsBuilderSettings();
    }
  }

  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = jsonEncode(state.toMap());
      await prefs.setString('tools_builder_settings', settingsJson);
    } catch (e) {
      // Handle save error silently
    }
  }

  void updateSettings(ToolsBuilderSettings settings) {
    state = settings;
    _saveSettings();
  }

  void resetToDefaults() {
    state = const ToolsBuilderSettings();
    _saveSettings();
  }
}

// Helper function to update settings
void updateSetting(WidgetRef ref, ToolsBuilderSettings Function(ToolsBuilderSettings) updater) {
  final currentSettings = ref.read(settingsProvider);
  final newSettings = updater(currentSettings);
  ref.read(settingsProvider.notifier).updateSettings(newSettings);
}

// Main Settings Provider
final settingsProvider = StateNotifierProvider<SettingsNotifier, ToolsBuilderSettings>((ref) {
  return SettingsNotifier();
});

// Individual Setting Providers for easier access
final autoSaveEnabledProvider = Provider<bool>((ref) {
  return ref.watch(settingsProvider).autoSaveEnabled;
});

final confirmDeleteProvider = Provider<bool>((ref) {
  return ref.watch(settingsProvider).confirmDelete;
});

final showTipsProvider = Provider<bool>((ref) {
  return ref.watch(settingsProvider).showTips;
});

final themeProvider = Provider<AppThemeMode>((ref) {
  return ref.watch(settingsProvider).theme;
});

final languageProvider = Provider<String>((ref) {
  return ref.watch(settingsProvider).language;
});

final maxUndoStepsProvider = Provider<int>((ref) {
  return ref.watch(settingsProvider).maxUndoSteps;
});

final cacheSizeProvider = Provider<int>((ref) {
  return ref.watch(settingsProvider).cacheSize;
});

// Spreadsheet Settings
final showGridLinesProvider = Provider<bool>((ref) {
  return ref.watch(settingsProvider).showGridLines;
});

final showRowNumbersProvider = Provider<bool>((ref) {
  return ref.watch(settingsProvider).showRowNumbers;
});

final showColumnHeadersProvider = Provider<bool>((ref) {
  return ref.watch(settingsProvider).showColumnHeaders;
});

final autoCompleteProvider = Provider<bool>((ref) {
  return ref.watch(settingsProvider).autoComplete;
});

final autoFillProvider = Provider<bool>((ref) {
  return ref.watch(settingsProvider).autoFill;
});

final defaultCellWidthProvider = Provider<double>((ref) {
  return ref.watch(settingsProvider).defaultCellWidth;
});

final defaultCellHeightProvider = Provider<double>((ref) {
  return ref.watch(settingsProvider).defaultCellHeight;
});

// Additional Settings (UI Builder settings are in tools_providers.dart)
// Formula Settings
final autoCalculateProvider = Provider<bool>((ref) => true);
final showFormulaBarProvider = Provider<bool>((ref) => true);
final highlightDependenciesProvider = Provider<bool>((ref) => true);
final showFormulaErrorsProvider = Provider<bool>((ref) => true);
final errorNotificationsProvider = Provider<bool>((ref) => true);

// Import/Export Settings
final preserveFormattingProvider = Provider<bool>((ref) => true);
final preserveFormulasProvider = Provider<bool>((ref) => true);
final autoDetectDataTypesProvider = Provider<bool>((ref) => true);
final defaultExportFormatProvider = Provider<String>((ref) => 'xlsx');
final compressionLevelProvider = Provider<int>((ref) => 5);

// Security Settings
final enableBackupsProvider = Provider<bool>((ref) => true);
final encryptDataProvider = Provider<bool>((ref) => false);

// Advanced Settings
final debugModeProvider = Provider<bool>((ref) => false);
final showPerformanceMetricsProvider = Provider<bool>((ref) => false);

// Note: UI Builder settings (showGrid, snapToGrid, etc.) are defined in tools_providers.dart
