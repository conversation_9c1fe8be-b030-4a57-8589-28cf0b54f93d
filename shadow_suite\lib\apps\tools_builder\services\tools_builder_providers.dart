import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/tool.dart';
import '../models/spreadsheet.dart';

// Re-export existing providers for backward compatibility
export 'tools_providers.dart';

// Template-related providers
final templateSearchQueryProvider = StateProvider<String>((ref) => '');

final selectedTemplateCategoryProvider = StateProvider<String?>((ref) => null);

// Tool editing providers
final currentToolProvider = StateNotifierProvider<CurrentToolNotifier, Tool?>((
  ref,
) {
  return CurrentToolNotifier();
});

final selectedComponentProvider = StateProvider<String?>((ref) => null);

final spreadsheetCalculationProvider =
    StateNotifierProvider<SpreadsheetCalculationNotifier, Spreadsheet?>((ref) {
      return SpreadsheetCalculationNotifier();
    });

final toolRuntimeProvider =
    StateNotifierProvider<ToolRuntimeNotifier, ToolRuntimeState>((ref) {
      return ToolRuntimeNotifier();
    });

final toolsProvider = StateNotifierProvider<ToolsNotifier, List<Tool>>((ref) {
  return ToolsNotifier();
});

// UI Builder settings providers
final showGridProvider = StateProvider<bool>((ref) => true);

final snapToGridProvider = StateProvider<bool>((ref) => true);

final gridSizeProvider = StateProvider<double>((ref) => 20.0);

final showComponentOutlinesProvider = StateProvider<bool>((ref) => true);

final showComponentLabelsProvider = StateProvider<bool>((ref) => true);

final autoSelectOnDropProvider = StateProvider<bool>((ref) => true);

// Search and filter providers
final toolsSearchProvider = StateProvider<String>((ref) => '');

final toolsFilterProvider = StateProvider<ToolCategory?>((ref) => null);

final toolsSortProvider = StateProvider<String>((ref) => 'name');

final recentToolsProvider =
    StateNotifierProvider<RecentToolsNotifier, List<Tool>>((ref) {
      return RecentToolsNotifier();
    });

// State Notifiers
class CurrentToolNotifier extends StateNotifier<Tool?> {
  CurrentToolNotifier() : super(null);

  void setTool(Tool? tool) {
    state = tool;
  }

  void updateTool(Tool tool) {
    state = tool;
  }

  void clearTool() {
    state = null;
  }
}

class SpreadsheetCalculationNotifier extends StateNotifier<Spreadsheet?> {
  SpreadsheetCalculationNotifier() : super(null);

  void setSpreadsheet(Spreadsheet spreadsheet) {
    state = spreadsheet;
  }

  void updateCell(String cellAddress, String value) {
    if (state == null) return;

    // Update the cell value and recalculate
    // This is a simplified implementation
    state = state!.copyWith(lastModified: DateTime.now());
  }

  void recalculate() {
    if (state == null) return;

    // Trigger recalculation of all formulas
    state = state!.copyWith(lastModified: DateTime.now());
  }
}

class ToolRuntimeState {
  final bool isRunning;
  final Map<String, dynamic> variables;
  final List<String> errors;
  final DateTime? lastExecution;

  const ToolRuntimeState({
    this.isRunning = false,
    this.variables = const {},
    this.errors = const [],
    this.lastExecution,
  });

  ToolRuntimeState copyWith({
    bool? isRunning,
    Map<String, dynamic>? variables,
    List<String>? errors,
    DateTime? lastExecution,
  }) {
    return ToolRuntimeState(
      isRunning: isRunning ?? this.isRunning,
      variables: variables ?? this.variables,
      errors: errors ?? this.errors,
      lastExecution: lastExecution ?? this.lastExecution,
    );
  }

  dynamic operator [](String key) => variables[key];

  Iterable<MapEntry<String, dynamic>> get entries => variables.entries;
}

class ToolRuntimeNotifier extends StateNotifier<ToolRuntimeState> {
  ToolRuntimeNotifier() : super(const ToolRuntimeState());

  void startExecution() {
    state = state.copyWith(
      isRunning: true,
      errors: [],
      lastExecution: DateTime.now(),
    );
  }

  void stopExecution() {
    state = state.copyWith(isRunning: false);
  }

  void setVariable(String name, dynamic value) {
    final newVariables = Map<String, dynamic>.from(state.variables);
    newVariables[name] = value;
    state = state.copyWith(variables: newVariables);
  }

  void addError(String error) {
    final newErrors = [...state.errors, error];
    state = state.copyWith(errors: newErrors);
  }

  void clearErrors() {
    state = state.copyWith(errors: []);
  }

  void updateComponentValue(String componentId, dynamic value) {
    setVariable(componentId, value);
  }

  void clearValues() {
    state = state.copyWith(variables: {});
  }
}

class RecentToolsNotifier extends StateNotifier<List<Tool>> {
  RecentToolsNotifier() : super([]);

  void addRecentTool(Tool tool) {
    final currentTools = [...state];
    currentTools.removeWhere((t) => t.id == tool.id);
    currentTools.insert(0, tool);

    // Keep only the 10 most recent tools
    if (currentTools.length > 10) {
      currentTools.removeRange(10, currentTools.length);
    }

    state = currentTools;
  }

  void clearRecentTools() {
    state = [];
  }
}

class ToolsNotifier extends StateNotifier<List<Tool>> {
  ToolsNotifier() : super([]);

  void addTool(Tool tool) {
    state = [...state, tool];
  }

  Tool createTool({
    required String name,
    required String description,
    required ToolType type,
    required ToolCategory category,
    String? creatorId,
  }) {
    final tool = Tool(
      name: name,
      description: description,
      type: type,
      category: category,
      components: [],
      creatorId: creatorId ?? 'user',
    );
    addTool(tool);
    return tool;
  }

  void updateTool(Tool tool) {
    state = state.map((t) => t.id == tool.id ? tool : t).toList();
  }

  void removeTool(String toolId) {
    state = state.where((t) => t.id != toolId).toList();
  }

  void setTools(List<Tool> tools) {
    state = tools;
  }

  Future<void> loadTools() async {
    // Simulate loading tools from database
    await Future.delayed(const Duration(milliseconds: 500));

    // Load mock tools
    final mockTools = <Tool>[
      Tool(
        name: 'Sample Calculator',
        description: 'A simple calculator tool',
        type: ToolType.calculator,
        category: ToolCategory.utilities,
        components: [],
        creatorId: 'system',
      ),
      Tool(
        name: 'Data Analyzer',
        description: 'Analyze data with charts',
        type: ToolType.analyzer,
        category: ToolCategory.business,
        components: [],
        creatorId: 'system',
      ),
    ];

    state = mockTools;
  }
}

// Computed providers
final filteredToolsProvider = Provider.family<List<Tool>, String>((ref, query) {
  final tools = ref.watch(toolsProvider);
  if (query.isEmpty) return tools;

  return tools
      .where(
        (tool) =>
            tool.name.toLowerCase().contains(query.toLowerCase()) ||
            tool.description.toLowerCase().contains(query.toLowerCase()),
      )
      .toList();
});

final toolsByTypeProvider = Provider.family<List<Tool>, ToolType>((ref, type) {
  final tools = ref.watch(toolsProvider);
  return tools.where((tool) => tool.type == type).toList();
});

final toolsByCategoryProvider = Provider.family<List<Tool>, ToolCategory>((
  ref,
  category,
) {
  final tools = ref.watch(toolsProvider);
  return tools.where((tool) => tool.category == category).toList();
});

// Statistics providers
final toolsStatsProvider = Provider<Map<String, int>>((ref) {
  final tools = ref.watch(toolsProvider);

  return {
    'total': tools.length,
    'calculators': tools.where((t) => t.type == ToolType.calculator).length,
    'analyzer': tools.where((t) => t.type == ToolType.analyzer).length,
    'custom': tools.where((t) => t.type == ToolType.custom).length,
  };
});
