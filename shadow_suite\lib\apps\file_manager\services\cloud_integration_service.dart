import 'dart:async';
import 'dart:io';
import '../models/cloud_models.dart';
import '../../../core/database/database_service.dart';
import '../../../core/services/error_handler.dart' as error_handler;

class CloudIntegrationService {
  static final List<CloudAccount> _cloudAccounts = [];
  static final List<CloudSyncTask> _syncTasks = [];
  static final List<CloudFile> _cloudFiles = [];
  static final Map<String, CloudProvider> _providers = {};
  
  static final StreamController<CloudEvent> _eventController = 
      StreamController<CloudEvent>.broadcast();
  
  // Initialize cloud integration service
  static Future<void> initialize() async {
    try {
      await _initializeCloudProviders();
      await _loadCloudAccounts();
      await _loadSyncTasks();
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Initialize cloud integration service');
    }
  }

  // FEATURE 26: MULTIPLE CLOUD SERVICE INTEGRATION
  static Future<CloudAccount> addCloudAccount({
    required CloudProviderType providerType,
    required String accountName,
    required Map<String, String> credentials,
  }) async {
    try {
      final provider = _providers[providerType.name];
      if (provider == null) {
        throw Exception('Cloud provider not supported: ${providerType.name}');
      }
      
      // Authenticate with cloud provider
      final authResult = await _authenticateWithProvider(provider, credentials);
      if (!authResult.isSuccess) {
        throw Exception('Authentication failed: ${authResult.errorMessage}');
      }
      
      final account = CloudAccount(
        id: 'account_${DateTime.now().millisecondsSinceEpoch}',
        providerType: providerType,
        accountName: accountName,
        displayName: accountName,
        isActive: true,
        totalStorage: authResult.totalStorage ?? 0,
        usedStorage: authResult.usedStorage ?? 0,
        lastSyncTime: null,
        credentials: credentials,
        createdAt: DateTime.now(),
      );
      
      _cloudAccounts.add(account);
      await _saveCloudAccount(account);
      
      _notifyEvent(CloudEvent(
        type: CloudEventType.accountAdded,
        accountId: account.id,
        message: 'Cloud account added successfully',
        timestamp: DateTime.now(),
      ));
      
      return account;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Add cloud account');
      rethrow;
    }
  }

  // FEATURE 27: REAL-TIME CLOUD SYNCHRONIZATION
  static Future<CloudSyncTask> startRealTimeSync({
    required String accountId,
    required String localPath,
    required String remotePath,
    SyncDirection direction = SyncDirection.bidirectional,
    SyncSettings? settings,
  }) async {
    try {
      // final account = _cloudAccounts.firstWhere((a) => a.id == accountId); // Reserved for future use
      
      final syncTask = CloudSyncTask(
        id: 'sync_${DateTime.now().millisecondsSinceEpoch}',
        accountId: accountId,
        localPath: localPath,
        remotePath: remotePath,
        direction: direction,
        settings: settings ?? SyncSettings.defaultSettings(),
        status: SyncStatus.active,
        lastSyncTime: null,
        filesUploaded: 0,
        filesDownloaded: 0,
        bytesTransferred: 0,
        conflictsResolved: 0,
        createdAt: DateTime.now(),
      );
      
      _syncTasks.add(syncTask);
      await _saveSyncTask(syncTask);
      
      // Start real-time monitoring
      _startRealTimeMonitoring(syncTask);
      
      _notifyEvent(CloudEvent(
        type: CloudEventType.syncStarted,
        accountId: accountId,
        syncTaskId: syncTask.id,
        message: 'Real-time sync started',
        timestamp: DateTime.now(),
      ));
      
      return syncTask;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Start real-time sync');
      rethrow;
    }
  }

  // FEATURE 28: OFFLINE FILE CACHING WITH SMART SYNC
  static Future<void> enableOfflineCaching({
    required String accountId,
    required List<String> filePaths,
    CacheStrategy strategy = CacheStrategy.intelligent,
  }) async {
    try {
      final account = _cloudAccounts.firstWhere((a) => a.id == accountId);
      
      for (final filePath in filePaths) {
        final cacheEntry = CloudCacheEntry(
          id: 'cache_${DateTime.now().millisecondsSinceEpoch}',
          accountId: accountId,
          remotePath: filePath,
          localCachePath: await _generateCachePath(filePath),
          strategy: strategy,
          priority: _calculateCachePriority(filePath, strategy),
          lastAccessed: DateTime.now(),
          expiresAt: _calculateCacheExpiry(strategy),
          isDownloaded: false,
          fileSize: 0,
          createdAt: DateTime.now(),
        );
        
        await _downloadFileToCache(account, cacheEntry);
      }
      
      _notifyEvent(CloudEvent(
        type: CloudEventType.cachingEnabled,
        accountId: accountId,
        message: 'Offline caching enabled for ${filePaths.length} files',
        timestamp: DateTime.now(),
      ));
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Enable offline caching');
    }
  }

  // FEATURE 29: CLOUD STORAGE ANALYTICS AND USAGE MONITORING
  static Future<CloudStorageAnalytics> getStorageAnalytics(String accountId) async {
    try {
      final account = _cloudAccounts.firstWhere((a) => a.id == accountId);
      final provider = _providers[account.providerType.name]!;
      
      // Fetch storage analytics from cloud provider
      final analytics = await _fetchStorageAnalytics(provider, account);
      
      return analytics;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Get storage analytics');
      rethrow;
    }
  }

  // FEATURE 30: SELECTIVE SYNC WITH FOLDER EXCLUSIONS
  static Future<void> configureSelectiveSync({
    required String syncTaskId,
    required List<String> includePaths,
    required List<String> excludePaths,
  }) async {
    try {
      final taskIndex = _syncTasks.indexWhere((t) => t.id == syncTaskId);
      if (taskIndex == -1) throw Exception('Sync task not found');
      
      final task = _syncTasks[taskIndex];
      final updatedSettings = task.settings.copyWith(
        includePaths: includePaths,
        excludePaths: excludePaths,
      );
      
      final updatedTask = task.copyWith(settings: updatedSettings);
      _syncTasks[taskIndex] = updatedTask;
      
      await _updateSyncTask(updatedTask);
      
      _notifyEvent(CloudEvent(
        type: CloudEventType.selectiveSyncConfigured,
        syncTaskId: syncTaskId,
        message: 'Selective sync configured',
        timestamp: DateTime.now(),
      ));
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Configure selective sync');
    }
  }

  // FEATURE 31: CONFLICT RESOLUTION FOR SYNC CONFLICTS
  static Future<void> resolveConflict({
    required String conflictId,
    required ConflictResolution resolution,
  }) async {
    try {
      final conflict = await _getConflictById(conflictId);
      if (conflict == null) throw Exception('Conflict not found');
      
      switch (resolution) {
        case ConflictResolution.useLocal:
          await _uploadLocalFile(conflict.localPath, conflict.remotePath, conflict.accountId);
          break;
        case ConflictResolution.useRemote:
          await _downloadRemoteFile(conflict.remotePath, conflict.localPath, conflict.accountId);
          break;
        case ConflictResolution.createCopy:
          await _createConflictCopy(conflict);
          break;
        case ConflictResolution.merge:
          await _mergeConflictedFiles(conflict);
          break;
        case ConflictResolution.skip:
          // Skip this conflict and continue
          break;
      }
      
      await _markConflictResolved(conflictId);
      
      _notifyEvent(CloudEvent(
        type: CloudEventType.conflictResolved,
        message: 'Conflict resolved: ${resolution.name}',
        timestamp: DateTime.now(),
      ));
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Resolve conflict');
    }
  }

  // FEATURE 32: CLOUD BACKUP SCHEDULING
  static Future<CloudBackupSchedule> createBackupSchedule({
    required String accountId,
    required String localPath,
    required String remotePath,
    required BackupFrequency frequency,
    BackupSettings? settings,
  }) async {
    try {
      final schedule = CloudBackupSchedule(
        id: 'backup_${DateTime.now().millisecondsSinceEpoch}',
        accountId: accountId,
        localPath: localPath,
        remotePath: remotePath,
        frequency: frequency,
        settings: settings ?? BackupSettings.defaultSettings(),
        isActive: true,
        lastBackupTime: null,
        nextBackupTime: _calculateNextBackupTime(frequency),
        totalBackups: 0,
        totalSize: 0,
        createdAt: DateTime.now(),
      );
      
      await _saveBackupSchedule(schedule);
      
      _notifyEvent(CloudEvent(
        type: CloudEventType.backupScheduleCreated,
        accountId: accountId,
        message: 'Backup schedule created',
        timestamp: DateTime.now(),
      ));
      
      return schedule;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Create backup schedule');
      rethrow;
    }
  }

  // FEATURE 33: MULTI-ACCOUNT CLOUD MANAGEMENT
  static Future<void> syncAcrossAccounts({
    required String sourceAccountId,
    required String targetAccountId,
    required String sourcePath,
    required String targetPath,
  }) async {
    try {
      final sourceAccount = _cloudAccounts.firstWhere((a) => a.id == sourceAccountId);
      final targetAccount = _cloudAccounts.firstWhere((a) => a.id == targetAccountId);
      
      // Download from source
      final tempPath = await _createTempFile();
      await _downloadFromCloud(sourceAccount, sourcePath, tempPath);
      
      // Upload to target
      await _uploadToCloud(targetAccount, tempPath, targetPath);
      
      // Clean up temp file
      await File(tempPath).delete();
      
      _notifyEvent(CloudEvent(
        type: CloudEventType.crossAccountSyncCompleted,
        message: 'File synced across accounts',
        timestamp: DateTime.now(),
      ));
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Sync across accounts');
    }
  }

  // FEATURE 34: BANDWIDTH THROTTLING FOR UPLOADS/DOWNLOADS
  static Future<void> setBandwidthThrottling({
    required String accountId,
    int? uploadLimitKbps,
    int? downloadLimitKbps,
  }) async {
    try {
      final accountIndex = _cloudAccounts.indexWhere((a) => a.id == accountId);
      if (accountIndex == -1) throw Exception('Account not found');
      
      // final account = _cloudAccounts[accountIndex]; // Reserved for future use
      // Update bandwidth settings (implementation would depend on HTTP client configuration)
      
      _notifyEvent(CloudEvent(
        type: CloudEventType.bandwidthThrottlingSet,
        accountId: accountId,
        message: 'Bandwidth throttling configured',
        timestamp: DateTime.now(),
      ));
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Set bandwidth throttling');
    }
  }

  // FEATURE 35: CLOUD FILE SHARING WITH EXPIRATION DATES
  static Future<CloudShareLink> createShareLink({
    required String accountId,
    required String filePath,
    SharePermissions permissions = SharePermissions.read,
    DateTime? expirationDate,
    String? password,
  }) async {
    try {
      final account = _cloudAccounts.firstWhere((a) => a.id == accountId);
      final provider = _providers[account.providerType.name]!;
      
      final shareLink = await _createProviderShareLink(
        provider,
        account,
        filePath,
        permissions,
        expirationDate,
        password,
      );
      
      await _saveShareLink(shareLink);
      
      _notifyEvent(CloudEvent(
        type: CloudEventType.shareLinkCreated,
        accountId: accountId,
        message: 'Share link created',
        timestamp: DateTime.now(),
      ));
      
      return shareLink;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Create share link');
      rethrow;
    }
  }

  // HELPER METHODS

  static Future<void> _initializeCloudProviders() async {
    _providers[CloudProviderType.googleDrive.name] = const CloudProvider(
      type: CloudProviderType.googleDrive,
      name: 'Google Drive',
      baseUrl: 'https://www.googleapis.com/drive/v3',
      endpoints: {
        'files': '/files',
        'upload': '/upload/drive/v3/files',
        'download': '/files/{fileId}',
      },
      supportedFeatures: ['sync', 'share', 'backup', 'versioning'],
    );

    _providers[CloudProviderType.dropbox.name] = const CloudProvider(
      type: CloudProviderType.dropbox,
      name: 'Dropbox',
      baseUrl: 'https://api.dropboxapi.com/2',
      endpoints: {
        'files': '/files/list_folder',
        'upload': '/files/upload',
        'download': '/files/download',
      },
      supportedFeatures: ['sync', 'share', 'backup'],
    );

    _providers[CloudProviderType.oneDrive.name] = const CloudProvider(
      type: CloudProviderType.oneDrive,
      name: 'OneDrive',
      baseUrl: 'https://graph.microsoft.com/v1.0/me/drive',
      endpoints: {
        'files': '/items',
        'upload': '/items/{parent-id}:/{filename}:/content',
        'download': '/items/{item-id}/content',
      },
      supportedFeatures: ['sync', 'share', 'backup', 'versioning'],
    );
  }

  static Future<void> _loadCloudAccounts() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM cloud_accounts');
      _cloudAccounts.clear();
      for (final row in results) {
        _cloudAccounts.add(CloudAccount.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load cloud accounts');
    }
  }

  static Future<void> _loadSyncTasks() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM cloud_sync_tasks');
      _syncTasks.clear();
      for (final _ in results) {
        // Load sync tasks from database (implementation would parse row data)
        // _syncTasks.add(CloudSyncTask.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load sync tasks');
    }
  }

  static Future<CloudAuthResult> _authenticateWithProvider(
    CloudProvider provider,
    Map<String, String> credentials,
  ) async {
    try {
      // Simplified authentication - in production, use proper OAuth flows
      return const CloudAuthResult(
        isSuccess: true,
        accessToken: 'mock_access_token',
        totalStorage: 15 * 1024 * 1024 * 1024, // 15 GB
        usedStorage: 5 * 1024 * 1024 * 1024,   // 5 GB
      );
    } catch (error) {
      return CloudAuthResult(
        isSuccess: false,
        errorMessage: error.toString(),
      );
    }
  }

  static Future<void> _startRealTimeMonitoring(CloudSyncTask syncTask) async {
    // Start file system watcher for real-time sync
    Timer.periodic(const Duration(seconds: 30), (timer) async {
      if (syncTask.status != SyncStatus.active) {
        timer.cancel();
        return;
      }

      await _performSyncCheck(syncTask);
    });
  }

  static Future<void> _performSyncCheck(CloudSyncTask syncTask) async {
    try {
      // Check for local changes
      final localChanges = await _detectLocalChanges(syncTask.localPath, syncTask.lastSyncTime);

      // Check for remote changes
      final remoteChanges = await _detectRemoteChanges(syncTask.accountId, syncTask.remotePath, syncTask.lastSyncTime);

      // Process changes based on sync direction
      if (syncTask.direction == SyncDirection.upload || syncTask.direction == SyncDirection.bidirectional) {
        await _processLocalChanges(syncTask, localChanges);
      }

      if (syncTask.direction == SyncDirection.download || syncTask.direction == SyncDirection.bidirectional) {
        await _processRemoteChanges(syncTask, remoteChanges);
      }

      // Update last sync time
      await _updateSyncTaskLastSyncTime(syncTask.id, DateTime.now());
    } catch (error) {
      error_handler.ErrorHandler.handleError(error, null, error_handler.ErrorType.operation,
        context: 'Perform sync check');
    }
  }

  static Future<String> _generateCachePath(String remotePath) async {
    final cacheDir = '/cache/cloud_files';
    final fileName = remotePath.split('/').last;
    return '$cacheDir/$fileName';
  }

  static int _calculateCachePriority(String filePath, CacheStrategy strategy) {
    switch (strategy) {
      case CacheStrategy.intelligent:
        // Calculate priority based on file type, size, and access patterns
        return 50;
      case CacheStrategy.aggressive:
        return 100;
      case CacheStrategy.conservative:
        return 25;
      case CacheStrategy.manual:
        return 0;
    }
  }

  static DateTime _calculateCacheExpiry(CacheStrategy strategy) {
    switch (strategy) {
      case CacheStrategy.intelligent:
        return DateTime.now().add(const Duration(days: 7));
      case CacheStrategy.aggressive:
        return DateTime.now().add(const Duration(days: 30));
      case CacheStrategy.conservative:
        return DateTime.now().add(const Duration(days: 1));
      case CacheStrategy.manual:
        return DateTime.now().add(const Duration(days: 365));
    }
  }

  static Future<void> _downloadFileToCache(CloudAccount account, CloudCacheEntry cacheEntry) async {
    try {
      // Download file from cloud to cache
      await _downloadFromCloud(account, cacheEntry.remotePath, cacheEntry.localCachePath);
    } catch (error) {
      error_handler.ErrorHandler.handleError(error, null, error_handler.ErrorType.operation,
        context: 'Download file to cache');
    }
  }

  static Future<CloudStorageAnalytics> _fetchStorageAnalytics(CloudProvider provider, CloudAccount account) async {
    // Fetch analytics from cloud provider API
    return CloudStorageAnalytics(
      accountId: account.id,
      totalStorage: account.totalStorage,
      usedStorage: account.usedStorage,
      availableStorage: account.availableStorage,
      storageByType: {
        'Documents': 1024 * 1024 * 100,
        'Images': 1024 * 1024 * 500,
        'Videos': 1024 * 1024 * 1000,
        'Other': 1024 * 1024 * 50,
      },
      usageHistory: [],
      largestFiles: [],
      generatedAt: DateTime.now(),
    );
  }

  static DateTime _calculateNextBackupTime(BackupFrequency frequency) {
    final now = DateTime.now();
    switch (frequency) {
      case BackupFrequency.hourly:
        return now.add(const Duration(hours: 1));
      case BackupFrequency.daily:
        return now.add(const Duration(days: 1));
      case BackupFrequency.weekly:
        return now.add(const Duration(days: 7));
      case BackupFrequency.monthly:
        return now.add(const Duration(days: 30));
    }
  }

  static Future<CloudConflict?> _getConflictById(String conflictId) async {
    // Get conflict from database
    return null;
  }

  static Future<void> _uploadLocalFile(String localPath, String remotePath, String accountId) async {
    // Upload local file to cloud
  }

  static Future<void> _downloadRemoteFile(String remotePath, String localPath, String accountId) async {
    // Download remote file to local
  }

  static Future<void> _createConflictCopy(CloudConflict conflict) async {
    // Create a copy of the conflicted file
  }

  static Future<void> _mergeConflictedFiles(CloudConflict conflict) async {
    // Merge conflicted files (for text files)
  }

  static Future<void> _markConflictResolved(String conflictId) async {
    // Mark conflict as resolved in database
  }

  static Future<String> _createTempFile() async {
    return '/tmp/cloud_sync_${DateTime.now().millisecondsSinceEpoch}';
  }

  static Future<void> _downloadFromCloud(CloudAccount account, String remotePath, String localPath) async {
    // Download file from cloud provider
  }

  static Future<void> _uploadToCloud(CloudAccount account, String localPath, String remotePath) async {
    // Upload file to cloud provider
  }

  static Future<CloudShareLink> _createProviderShareLink(
    CloudProvider provider,
    CloudAccount account,
    String filePath,
    SharePermissions permissions,
    DateTime? expirationDate,
    String? password,
  ) async {
    // Create share link using provider API
    return CloudShareLink(
      id: 'share_${DateTime.now().millisecondsSinceEpoch}',
      accountId: account.id,
      filePath: filePath,
      shareUrl: 'https://example.com/share/mock_link',
      permissions: permissions,
      expirationDate: expirationDate,
      password: password,
      accessCount: 0,
      createdAt: DateTime.now(),
    );
  }

  static Future<List<String>> _detectLocalChanges(String localPath, DateTime? lastSyncTime) async {
    // Detect local file changes since last sync
    return [];
  }

  static Future<List<String>> _detectRemoteChanges(String accountId, String remotePath, DateTime? lastSyncTime) async {
    // Detect remote file changes since last sync
    return [];
  }

  static Future<void> _processLocalChanges(CloudSyncTask syncTask, List<String> changes) async {
    // Process local changes for upload
  }

  static Future<void> _processRemoteChanges(CloudSyncTask syncTask, List<String> changes) async {
    // Process remote changes for download
  }

  // Database operations
  static Future<void> _saveCloudAccount(CloudAccount account) async {
    try {
      await DatabaseService.safeInsert('cloud_accounts', account.toJson());
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Save cloud account');
    }
  }

  static Future<void> _saveSyncTask(CloudSyncTask syncTask) async {
    try {
      await DatabaseService.safeInsert('cloud_sync_tasks', {
        'id': syncTask.id,
        'account_id': syncTask.accountId,
        'local_path': syncTask.localPath,
        'remote_path': syncTask.remotePath,
        'direction': syncTask.direction.name,
        'status': syncTask.status.name,
        'files_uploaded': syncTask.filesUploaded,
        'files_downloaded': syncTask.filesDownloaded,
        'bytes_transferred': syncTask.bytesTransferred,
        'conflicts_resolved': syncTask.conflictsResolved,
        'created_at': syncTask.createdAt.toIso8601String(),
      });
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Save sync task');
    }
  }

  static Future<void> _updateSyncTask(CloudSyncTask syncTask) async {
    try {
      await DatabaseService.safeUpdate('cloud_sync_tasks', {
        'status': syncTask.status.name,
        'files_uploaded': syncTask.filesUploaded,
        'files_downloaded': syncTask.filesDownloaded,
        'bytes_transferred': syncTask.bytesTransferred,
        'conflicts_resolved': syncTask.conflictsResolved,
      }, where: 'id = ?', whereArgs: [syncTask.id]);
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Update sync task');
    }
  }

  static Future<void> _updateSyncTaskLastSyncTime(String syncTaskId, DateTime lastSyncTime) async {
    try {
      await DatabaseService.safeUpdate('cloud_sync_tasks', {
        'last_sync_time': lastSyncTime.toIso8601String(),
      }, where: 'id = ?', whereArgs: [syncTaskId]);
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Update sync task last sync time');
    }
  }

  static Future<void> _saveBackupSchedule(CloudBackupSchedule schedule) async {
    try {
      await DatabaseService.safeInsert('cloud_backup_schedules', {
        'id': schedule.id,
        'account_id': schedule.accountId,
        'local_path': schedule.localPath,
        'remote_path': schedule.remotePath,
        'frequency': schedule.frequency.name,
        'is_active': schedule.isActive,
        'next_backup_time': schedule.nextBackupTime?.toIso8601String(),
        'total_backups': schedule.totalBackups,
        'total_size': schedule.totalSize,
        'created_at': schedule.createdAt.toIso8601String(),
      });
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Save backup schedule');
    }
  }

  static Future<void> _saveShareLink(CloudShareLink shareLink) async {
    try {
      await DatabaseService.safeInsert('cloud_share_links', {
        'id': shareLink.id,
        'account_id': shareLink.accountId,
        'file_path': shareLink.filePath,
        'share_url': shareLink.shareUrl,
        'permissions': shareLink.permissions.name,
        'expiration_date': shareLink.expirationDate?.toIso8601String(),
        'password': shareLink.password,
        'access_count': shareLink.accessCount,
        'created_at': shareLink.createdAt.toIso8601String(),
      });
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Save share link');
    }
  }

  static void _notifyEvent(CloudEvent event) {
    _eventController.add(event);
  }

  // Getters
  static List<CloudAccount> get cloudAccounts => List.unmodifiable(_cloudAccounts);
  static List<CloudSyncTask> get syncTasks => List.unmodifiable(_syncTasks);
  static List<CloudFile> get cloudFiles => List.unmodifiable(_cloudFiles);
  static Stream<CloudEvent> get eventStream => _eventController.stream;

  // Dispose
  static void dispose() {
    _cloudAccounts.clear();
    _syncTasks.clear();
    _cloudFiles.clear();
    _providers.clear();
    _eventController.close();
  }
}
