import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../../../../core/theme/app_theme.dart';
import '../../services/memo_providers.dart';
import '../../models/voice_memo.dart';

class VoiceMemosListScreen extends ConsumerStatefulWidget {
  const VoiceMemosListScreen({super.key});

  @override
  ConsumerState<VoiceMemosListScreen> createState() => _VoiceMemosListScreenState();
}

class _VoiceMemosListScreenState extends ConsumerState<VoiceMemosListScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      ref.read(voiceMemoSearchQueryProvider.notifier).state = _searchController.text;
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final voiceMemosAsync = ref.watch(voiceMemosProvider);
    final searchQuery = ref.watch(voiceMemoSearchQueryProvider);
    final selectedCategory = ref.watch(voiceMemoSelectedCategoryProvider);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Voice Memos'),
        backgroundColor: AppTheme.memoSuiteColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            ref.read(memoSuiteCurrentScreenProvider.notifier).state = MemoSuiteScreen.dashboard;
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.mic),
            onPressed: () {
              ref.read(memoSuiteCurrentScreenProvider.notifier).state = MemoSuiteScreen.voiceMemoRecording;
            },
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilters(context, ref),
          Expanded(
            child: voiceMemosAsync.when(
              data: (voiceMemos) {
                final filteredMemos = _filterVoiceMemos(voiceMemos, searchQuery, selectedCategory);
                if (filteredMemos.isEmpty) {
                  return _buildEmptyState(context, ref);
                }
                return _buildVoiceMemosList(context, ref, filteredMemos);
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error, size: 64, color: Colors.red[300]),
                    const SizedBox(height: 16),
                    Text('Error loading voice memos: $error'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => ref.refresh(voiceMemosProvider),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          ref.read(memoSuiteCurrentScreenProvider.notifier).state = MemoSuiteScreen.voiceMemoRecording;
        },
        backgroundColor: AppTheme.memoSuiteColor,
        child: const Icon(Icons.mic),
      ),
    );
  }

  Widget _buildSearchAndFilters(BuildContext context, WidgetRef ref) {
    final selectedCategory = ref.watch(voiceMemoSelectedCategoryProvider);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Colors.grey, width: 0.2)),
      ),
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search voice memos...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          const SizedBox(height: 16),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                FilterChip(
                  label: const Text('All'),
                  selected: selectedCategory == null,
                  onSelected: (selected) {
                    ref.read(voiceMemoSelectedCategoryProvider.notifier).state = null;
                  },
                ),
                const SizedBox(width: 8),
                ...VoiceMemoCategory.allCategories.map((category) => Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(category),
                    selected: selectedCategory == category,
                    onSelected: (selected) {
                      ref.read(voiceMemoSelectedCategoryProvider.notifier).state = 
                          selected ? category : null;
                    },
                  ),
                )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<VoiceMemo> _filterVoiceMemos(List<VoiceMemo> voiceMemos, String searchQuery, String? selectedCategory) {
    var filteredMemos = voiceMemos;

    if (searchQuery.isNotEmpty) {
      filteredMemos = filteredMemos.where((memo) =>
          memo.title.toLowerCase().contains(searchQuery.toLowerCase()) ||
          memo.tags.any((tag) => tag.toLowerCase().contains(searchQuery.toLowerCase())) ||
          (memo.transcription?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false)).toList();
    }

    if (selectedCategory != null) {
      filteredMemos = filteredMemos.where((memo) => memo.category == selectedCategory).toList();
    }

    return filteredMemos;
  }

  Widget _buildEmptyState(BuildContext context, WidgetRef ref) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.mic,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No voice memos found',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Record your first voice memo to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              ref.read(memoSuiteCurrentScreenProvider.notifier).state = MemoSuiteScreen.voiceMemoRecording;
            },
            icon: const Icon(Icons.mic),
            label: const Text('Record Voice Memo'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.memoSuiteColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVoiceMemosList(BuildContext context, WidgetRef ref, List<VoiceMemo> voiceMemos) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: StaggeredGrid.count(
        crossAxisCount: 2,
        mainAxisSpacing: 16,
        crossAxisSpacing: 16,
        children: voiceMemos.map((memo) => _buildVoiceMemoCard(context, ref, memo)).toList(),
      ),
    );
  }

  Widget _buildVoiceMemoCard(BuildContext context, WidgetRef ref, VoiceMemo memo) {
    return Card(
      child: InkWell(
        onTap: () {
          ref.read(selectedVoiceMemoProvider.notifier).state = memo;
          ref.read(memoSuiteCurrentScreenProvider.notifier).state = MemoSuiteScreen.voiceMemoPlayback;
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppTheme.memoSuiteColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.mic,
                      color: AppTheme.memoSuiteColor,
                      size: 20,
                    ),
                  ),
                  const Spacer(),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleVoiceMemoAction(context, ref, memo, value),
                    itemBuilder: (context) => [
                      const PopupMenuItem(value: 'play', child: Text('Play')),
                      const PopupMenuItem(value: 'rename', child: Text('Rename')),
                      const PopupMenuItem(value: 'delete', child: Text('Delete')),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                memo.title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    memo.formattedDuration,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    memo.formattedFileSize,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppTheme.memoSuiteColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      memo.category,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.memoSuiteColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    _formatDate(memo.updatedAt),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              if (memo.tags.isNotEmpty) ...[
                const SizedBox(height: 8),
                Wrap(
                  spacing: 4,
                  children: memo.tags.take(2).map((tag) => Chip(
                    label: Text(tag),
                    labelStyle: const TextStyle(fontSize: 10),
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    visualDensity: VisualDensity.compact,
                  )).toList(),
                ),
              ],
              if (memo.transcription != null && memo.transcription!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.text_fields,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          memo.transcription!,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[700],
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _handleVoiceMemoAction(BuildContext context, WidgetRef ref, VoiceMemo memo, String action) {
    switch (action) {
      case 'play':
        ref.read(selectedVoiceMemoProvider.notifier).state = memo;
        ref.read(memoSuiteCurrentScreenProvider.notifier).state = MemoSuiteScreen.voiceMemoPlayback;
        break;
      case 'rename':
        _showRenameDialog(context, ref, memo);
        break;
      case 'delete':
        _showDeleteDialog(context, ref, memo);
        break;
    }
  }

  void _showRenameDialog(BuildContext context, WidgetRef ref, VoiceMemo memo) {
    final controller = TextEditingController(text: memo.title);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Rename Voice Memo'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Title',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (controller.text.trim().isNotEmpty) {
                final updatedMemo = memo.copyWith(title: controller.text.trim());
                ref.read(voiceMemosProvider.notifier).updateVoiceMemo(updatedMemo);
              }
              Navigator.of(context).pop();
            },
            child: const Text('Rename'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, WidgetRef ref, VoiceMemo memo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Voice Memo'),
        content: Text('Are you sure you want to delete "${memo.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              ref.read(voiceMemosProvider.notifier).deleteVoiceMemo(memo.id);
              Navigator.of(context).pop();
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
