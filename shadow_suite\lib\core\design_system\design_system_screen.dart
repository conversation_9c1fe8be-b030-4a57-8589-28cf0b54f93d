import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'design_system_manager.dart';
import '../theme/app_theme.dart';

class DesignSystemScreen extends ConsumerStatefulWidget {
  const DesignSystemScreen({super.key});

  @override
  ConsumerState<DesignSystemScreen> createState() => _DesignSystemScreenState();
}

class _DesignSystemScreenState extends ConsumerState<DesignSystemScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _selectedColorScheme = 'material_blue';
  String _selectedTypography = 'default';
  String _selectedLayout = 'default';
  String _selectedAccessibility = 'standard';
  bool _isDarkMode = false;
  Color _customSeedColor = const Color(0xFF3498DB);

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Design System'),
        backgroundColor: AppTheme.primaryColor,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.palette), text: 'Colors'),
            Tab(icon: Icon(Icons.text_fields), text: 'Typography'),
            Tab(icon: Icon(Icons.view_quilt), text: 'Layout'),
            Tab(icon: Icon(Icons.accessibility), text: 'Accessibility'),
            Tab(icon: Icon(Icons.preview), text: 'Preview'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildColorTab(),
          _buildTypographyTab(),
          _buildLayoutTab(),
          _buildAccessibilityTab(),
          _buildPreviewTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _applyDesignSystem,
        icon: const Icon(Icons.check),
        label: const Text('Apply Theme'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  Widget _buildColorTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Material Design Presets'),
          const SizedBox(height: 16),
          _buildColorSchemeGrid(),
          const SizedBox(height: 32),
          _buildSectionHeader('Custom Color Palette'),
          const SizedBox(height: 16),
          _buildCustomColorPicker(),
          const SizedBox(height: 32),
          _buildSectionHeader('Dark Mode'),
          const SizedBox(height: 16),
          SwitchListTile(
            title: const Text('Enable Dark Mode'),
            subtitle: const Text('Switch between light and dark themes'),
            value: _isDarkMode,
            onChanged: (value) {
              setState(() {
                _isDarkMode = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTypographyTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Typography Presets'),
          const SizedBox(height: 16),
          ...DesignSystemManager.typographyPresets.keys.map((key) {
            return Card(
              child: RadioListTile<String>(
                title: Text(key.replaceAll('_', ' ').toUpperCase()),
                subtitle: _buildTypographyPreview(key),
                value: key,
                groupValue: _selectedTypography,
                onChanged: (value) {
                  setState(() {
                    _selectedTypography = value!;
                  });
                },
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildLayoutTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Layout Presets'),
          const SizedBox(height: 16),
          ...DesignSystemManager.layoutPresets.keys.map((key) {
            final layout = DesignSystemManager.layoutPresets[key]!;
            return Card(
              child: RadioListTile<String>(
                title: Text(key.replaceAll('_', ' ').toUpperCase()),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Padding: ${layout.padding.top}px'),
                    Text('Spacing: ${layout.spacing}px'),
                    Text('Border Radius: ${layout.borderRadius}px'),
                    Text('Elevation: ${layout.elevation}px'),
                  ],
                ),
                value: key,
                groupValue: _selectedLayout,
                onChanged: (value) {
                  setState(() {
                    _selectedLayout = value!;
                  });
                },
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildAccessibilityTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Accessibility Presets'),
          const SizedBox(height: 16),
          ...DesignSystemManager.accessibilityPresets.keys.map((key) {
            final accessibility = DesignSystemManager.accessibilityPresets[key]!;
            return Card(
              child: RadioListTile<String>(
                title: Text(key.replaceAll('_', ' ').toUpperCase()),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Touch Target: ${accessibility.minimumTouchTargetSize.width}x${accessibility.minimumTouchTargetSize.height}'),
                    Text('High Contrast: ${accessibility.highContrast ? 'Yes' : 'No'}'),
                    Text('Reduce Motion: ${accessibility.reduceMotion ? 'Yes' : 'No'}'),
                  ],
                ),
                value: key,
                groupValue: _selectedAccessibility,
                onChanged: (value) {
                  setState(() {
                    _selectedAccessibility = value!;
                  });
                },
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildPreviewTab() {
    final theme = DesignSystemManager.createTheme(
      colorSchemeKey: _selectedColorScheme,
      typographyKey: _selectedTypography,
      layoutKey: _selectedLayout,
      accessibilityKey: _selectedAccessibility,
      isDark: _isDarkMode,
    );

    return Theme(
      data: theme,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader('Theme Preview'),
            const SizedBox(height: 16),
            _buildPreviewCard(theme),
            const SizedBox(height: 16),
            _buildPreviewButtons(theme),
            const SizedBox(height: 16),
            _buildPreviewInputs(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
        fontWeight: FontWeight.bold,
        color: AppTheme.primaryColor,
      ),
    );
  }

  Widget _buildColorSchemeGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 2.5,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: DesignSystemManager.materialColorSchemes.length,
      itemBuilder: (context, index) {
        final key = DesignSystemManager.materialColorSchemes.keys.elementAt(index);
        final colorScheme = DesignSystemManager.materialColorSchemes[key]!;
        final isSelected = _selectedColorScheme == key;

        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedColorScheme = key;
            });
          },
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected ? AppTheme.primaryColor : Colors.grey[300]!,
                width: isSelected ? 3 : 1,
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: colorScheme.primary,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        bottomLeft: Radius.circular(8),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: colorScheme.secondary,
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(8),
                        bottomRight: Radius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCustomColorPicker() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Create from seed color:'),
            const SizedBox(height: 16),
            Row(
              children: [
                GestureDetector(
                  onTap: _showColorPicker,
                  child: Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: _customSeedColor,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Seed Color: #${_customSeedColor.toARGB32().toRadixString(16).substring(2).toUpperCase()}'),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            _selectedColorScheme = 'custom';
                          });
                        },
                        child: const Text('Use Custom Palette'),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypographyPreview(String key) {
    final textTheme = DesignSystemManager.typographyPresets[key]!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Headline', style: textTheme.headlineSmall),
        Text('Body text example', style: textTheme.bodyMedium),
      ],
    );
  }

  Widget _buildPreviewCard(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Card Title', style: theme.textTheme.titleLarge),
            const SizedBox(height: 8),
            Text('This is a preview of how cards will look with the selected theme.', 
                 style: theme.textTheme.bodyMedium),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewButtons(ThemeData theme) {
    return Row(
      children: [
        ElevatedButton(
          onPressed: () {},
          child: const Text('Primary Button'),
        ),
        const SizedBox(width: 16),
        OutlinedButton(
          onPressed: () {},
          child: const Text('Secondary Button'),
        ),
      ],
    );
  }

  Widget _buildPreviewInputs(ThemeData theme) {
    return Column(
      children: [
        TextField(
          decoration: const InputDecoration(
            labelText: 'Text Input',
            hintText: 'Enter some text...',
          ),
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<String>(
          decoration: const InputDecoration(
            labelText: 'Dropdown',
          ),
          items: const [
            DropdownMenuItem(value: 'option1', child: Text('Option 1')),
            DropdownMenuItem(value: 'option2', child: Text('Option 2')),
          ],
          onChanged: (value) {},
        ),
      ],
    );
  }

  void _showColorPicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Pick a Color'),
        content: SingleChildScrollView(
          child: BlockPicker(
            pickerColor: _customSeedColor,
            onColorChanged: (color) {
              setState(() {
                _customSeedColor = color;
              });
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  void _applyDesignSystem() {
    final theme = DesignSystemManager.createTheme(
      colorSchemeKey: _selectedColorScheme,
      typographyKey: _selectedTypography,
      layoutKey: _selectedLayout,
      accessibilityKey: _selectedAccessibility,
      isDark: _isDarkMode,
    );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Design system applied successfully!'),
        backgroundColor: theme.colorScheme.primary,
      ),
    );

    // In a real implementation, you would save these settings
    // and apply them globally to the app
  }
}

// Simple color picker widget
class BlockPicker extends StatelessWidget {
  final Color pickerColor;
  final ValueChanged<Color> onColorChanged;

  const BlockPicker({
    super.key,
    required this.pickerColor,
    required this.onColorChanged,
  });

  @override
  Widget build(BuildContext context) {
    final colors = [
      Colors.red, Colors.pink, Colors.purple, Colors.deepPurple,
      Colors.indigo, Colors.blue, Colors.lightBlue, Colors.cyan,
      Colors.teal, Colors.green, Colors.lightGreen, Colors.lime,
      Colors.yellow, Colors.amber, Colors.orange, Colors.deepOrange,
      Colors.brown, Colors.grey, Colors.blueGrey, Colors.black,
    ];

    return Wrap(
      children: colors.map((color) {
        return GestureDetector(
          onTap: () => onColorChanged(color),
          child: Container(
            width: 40,
            height: 40,
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                color: pickerColor == color ? Colors.black : Colors.transparent,
                width: 2,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}
