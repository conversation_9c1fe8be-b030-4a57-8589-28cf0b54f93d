import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/note.dart';
import '../models/todo.dart';
import '../models/voice_memo.dart';
import '../models/text_note.dart';
import 'database_service.dart';

// Notes Providers
final notesProvider = StateNotifierProvider<NotesNotifier, AsyncValue<List<Note>>>((ref) {
  return NotesNotifier();
});

class NotesNotifier extends StateNotifier<AsyncValue<List<Note>>> {
  NotesNotifier() : super(const AsyncValue.loading()) {
    loadNotes();
  }

  Future<void> loadNotes() async {
    try {
      state = const AsyncValue.loading();
      final notes = await MemoSuiteDatabaseService.getAllNotes();
      state = AsyncValue.data(notes);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addNote(Note note) async {
    try {
      await MemoSuiteDatabaseService.insertNote(note);
      await loadNotes();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateNote(Note note) async {
    try {
      await MemoSuiteDatabaseService.updateNote(note);
      await loadNotes();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteNote(String id) async {
    try {
      await MemoSuiteDatabaseService.deleteNote(id);
      await loadNotes();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<List<Note>> searchNotes(String query) async {
    if (query.isEmpty) {
      return state.value ?? [];
    }
    return await MemoSuiteDatabaseService.searchNotes(query);
  }

  Future<List<Note>> getNotesByCategory(String category) async {
    return await MemoSuiteDatabaseService.getNotesByCategory(category);
  }

  Future<Note?> getNoteById(String id) async {
    try {
      return await MemoSuiteDatabaseService.getNoteById(id);
    } catch (error) {
      return null;
    }
  }
}

// Todos Providers
final todosProvider = StateNotifierProvider<TodosNotifier, AsyncValue<List<Todo>>>((ref) {
  return TodosNotifier();
});

class TodosNotifier extends StateNotifier<AsyncValue<List<Todo>>> {
  TodosNotifier() : super(const AsyncValue.loading()) {
    loadTodos();
  }

  Future<void> loadTodos() async {
    try {
      state = const AsyncValue.loading();
      final todos = await MemoSuiteDatabaseService.getAllTodos();
      state = AsyncValue.data(todos);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addTodo(Todo todo) async {
    try {
      await MemoSuiteDatabaseService.insertTodo(todo);
      await loadTodos();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateTodo(Todo todo) async {
    try {
      await MemoSuiteDatabaseService.updateTodo(todo);
      await loadTodos();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteTodo(String id) async {
    try {
      await MemoSuiteDatabaseService.deleteTodo(id);
      await loadTodos();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  List<Todo> getTodosByStatus(TodoStatus status) {
    final todos = state.value ?? [];
    return todos.where((todo) => todo.status == status).toList();
  }

  Future<Todo?> getTodoById(String id) async {
    try {
      return await MemoSuiteDatabaseService.getTodoById(id);
    } catch (error) {
      return null;
    }
  }
}

// Voice Memos Providers
final voiceMemosProvider = StateNotifierProvider<VoiceMemosNotifier, AsyncValue<List<VoiceMemo>>>((ref) {
  return VoiceMemosNotifier();
});

class VoiceMemosNotifier extends StateNotifier<AsyncValue<List<VoiceMemo>>> {
  VoiceMemosNotifier() : super(const AsyncValue.loading()) {
    loadVoiceMemos();
  }

  Future<void> loadVoiceMemos() async {
    try {
      state = const AsyncValue.loading();
      final voiceMemos = await MemoSuiteDatabaseService.getAllVoiceMemos();
      state = AsyncValue.data(voiceMemos);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addVoiceMemo(VoiceMemo voiceMemo) async {
    try {
      await MemoSuiteDatabaseService.insertVoiceMemo(voiceMemo);
      await loadVoiceMemos();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateVoiceMemo(VoiceMemo voiceMemo) async {
    try {
      await MemoSuiteDatabaseService.updateVoiceMemo(voiceMemo);
      await loadVoiceMemos();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteVoiceMemo(String id) async {
    try {
      await MemoSuiteDatabaseService.deleteVoiceMemo(id);
      await loadVoiceMemos();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<VoiceMemo?> getVoiceMemoById(String id) async {
    try {
      return await MemoSuiteDatabaseService.getVoiceMemoById(id);
    } catch (error) {
      return null;
    }
  }
}

// Statistics Provider with auto-refresh
final memoStatisticsProvider = StateNotifierProvider<MemoStatisticsNotifier, AsyncValue<Map<String, int>>>((ref) {
  return MemoStatisticsNotifier(ref);
});

class MemoStatisticsNotifier extends StateNotifier<AsyncValue<Map<String, int>>> {
  final Ref ref;

  MemoStatisticsNotifier(this.ref) : super(const AsyncValue.loading()) {
    loadStatistics();

    // Listen to data changes and refresh statistics
    ref.listen(notesProvider, (previous, next) {
      loadStatistics();
    });
    ref.listen(todosProvider, (previous, next) {
      loadStatistics();
    });
    ref.listen(voiceMemosProvider, (previous, next) {
      loadStatistics();
    });
  }

  Future<void> loadStatistics() async {
    try {
      final stats = await MemoSuiteDatabaseService.getStatistics();
      state = AsyncValue.data(stats);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Recent Items Provider with auto-refresh
final recentItemsProvider = StateNotifierProvider<RecentItemsNotifier, AsyncValue<List<Map<String, dynamic>>>>((ref) {
  return RecentItemsNotifier(ref);
});

class RecentItemsNotifier extends StateNotifier<AsyncValue<List<Map<String, dynamic>>>> {
  final Ref ref;

  RecentItemsNotifier(this.ref) : super(const AsyncValue.loading()) {
    loadRecentItems();

    // Listen to data changes and refresh recent items
    ref.listen(notesProvider, (previous, next) {
      loadRecentItems();
    });
    ref.listen(todosProvider, (previous, next) {
      loadRecentItems();
    });
    ref.listen(voiceMemosProvider, (previous, next) {
      loadRecentItems();
    });
  }

  Future<void> loadRecentItems() async {
    try {
      final items = await MemoSuiteDatabaseService.getRecentItems(limit: 10);
      state = AsyncValue.data(items);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Search and Filter Providers
final notesSearchQueryProvider = StateProvider<String>((ref) => '');
final notesSelectedCategoryProvider = StateProvider<String?>((ref) => null);
final notesViewModeProvider = StateProvider<NotesViewMode>((ref) => NotesViewMode.grid);

enum NotesViewMode { grid, list }

// Todo Filter Providers
final todoFilterStatusProvider = StateProvider<TodoStatus?>((ref) => null);
final todoFilterPriorityProvider = StateProvider<TodoPriority?>((ref) => null);
final todoFilterCategoryProvider = StateProvider<String?>((ref) => null);

// Voice Memo Filter Providers
final voiceMemoSearchQueryProvider = StateProvider<String>((ref) => '');
final voiceMemoSelectedCategoryProvider = StateProvider<String?>((ref) => null);

// Navigation Providers
final memoSuiteCurrentScreenProvider = StateProvider<MemoSuiteScreen>((ref) => MemoSuiteScreen.dashboard);

enum MemoSuiteScreen {
  dashboard,
  notesList,
  noteView,
  noteEditor,
  todosList,
  todoView,
  todoEditor,
  voiceMemosList,
  voiceMemoRecording,
  voiceMemoPlayback,
  calendar,
}

// Selected Item Providers
final selectedNoteProvider = StateProvider<Note?>((ref) => null);
final selectedTodoProvider = StateProvider<Todo?>((ref) => null);
final selectedVoiceMemoProvider = StateProvider<VoiceMemo?>((ref) => null);

// Text Notes Provider (alias for compatibility)
final textNotesProvider = StateNotifierProvider<TextNotesNotifier, List<TextNote>>((ref) {
  return TextNotesNotifier();
});

class TextNotesNotifier extends StateNotifier<List<TextNote>> {
  TextNotesNotifier() : super([]);

  void addNote(TextNote note) {
    state = [...state, note];
  }

  void updateNote(TextNote updatedNote) {
    state = [
      for (final note in state)
        if (note.id == updatedNote.id)
          updatedNote
        else
          note,
    ];
  }

  void removeNote(String noteId) {
    state = state.where((note) => note.id != noteId).toList();
  }

  void clearNotes() {
    state = [];
  }
}
