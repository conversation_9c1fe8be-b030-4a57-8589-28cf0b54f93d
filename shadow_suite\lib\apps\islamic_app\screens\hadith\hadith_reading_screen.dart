import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/islamic_providers.dart';
import '../../services/offline_hadith_service.dart';
import '../../models/hadith_models.dart';

class HadithReadingScreen extends ConsumerStatefulWidget {
  const HadithReadingScreen({super.key});

  @override
  ConsumerState<HadithReadingScreen> createState() =>
      _HadithReadingScreenState();
}

class _HadithReadingScreenState extends ConsumerState<HadithReadingScreen> {
  final PageController _pageController = PageController();
  int _currentIndex = 0;
  List<HadithEntry> _hadiths = [];
  bool _isLoading = true;
  String _selectedCollection = 'sahih_bukhari';
  int _currentPage = 0;
  final int _pageSize = 50; // Load 50 hadiths at a time
  bool _hasMoreHadiths = true;

  @override
  void initState() {
    super.initState();
    _loadHadiths();
  }

  Future<void> _loadHadiths({bool loadMore = false}) async {
    if (!loadMore) {
      setState(() {
        _isLoading = true;
        _hadiths.clear();
        _currentPage = 0;
      });
    }

    try {
      await OfflineHadithService.initialize();
      final collectionHadiths = OfflineHadithService.getCollectionHadiths(
        _selectedCollection,
      );

      final startIndex = _currentPage * _pageSize;
      final endIndex = (startIndex + _pageSize).clamp(
        0,
        collectionHadiths.length,
      );

      if (startIndex < collectionHadiths.length) {
        final newHadiths = collectionHadiths.sublist(startIndex, endIndex);
        setState(() {
          if (loadMore) {
            _hadiths.addAll(newHadiths);
          } else {
            _hadiths = newHadiths;
          }
          _hasMoreHadiths = endIndex < collectionHadiths.length;
          _currentPage++;
          _isLoading = false;
        });
      } else {
        setState(() {
          _hasMoreHadiths = false;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        // Fallback to sample data if service fails
        _hadiths = _getSampleHadiths();
      });
    }
  }

  List<HadithEntry> _getSampleHadiths() {
    return [
      HadithEntry(
        id: '1',
        collectionId: 'bukhari',
        chapterId: 'faith',
        hadithNumber: 1,
        arabicText:
            'إِنَّمَا الْأَعْمَالُ بِالنِّيَّاتِ، وَإِنَّمَا لِكُلِّ امْرِئٍ مَا نَوَى',
        englishTranslation:
            'Actions are but by intention and every man shall have only that which he intended.',
        narrator: 'Umar ibn al-Khattab',
        chain: 'Complete chain of narrators',
        grade: HadithGrade.sahih,
        keywords: ['intention', 'actions', 'niyyah'],
        theme: 'Faith and Intention',
        reference: 'Bukhari 1',
        createdAt: DateTime.now(),
      ),
      HadithEntry(
        id: '2',
        collectionId: 'bukhari',
        chapterId: 'faith',
        hadithNumber: 2,
        arabicText:
            'بُنِيَ الْإِسْلَامُ عَلَى خَمْسٍ: شَهَادَةِ أَنْ لَا إِلَهَ إِلَّا اللَّهُ وَأَنَّ مُحَمَّدًا رَسُولُ اللَّهِ',
        englishTranslation:
            'Islam is built upon five pillars: testifying that there is no god but Allah and that Muhammad is His messenger...',
        narrator: 'Abdullah ibn Umar',
        chain: 'Complete chain of narrators',
        grade: HadithGrade.sahih,
        keywords: ['pillars', 'islam', 'shahada'],
        theme: 'Pillars of Islam',
        reference: 'Bukhari 8',
        createdAt: DateTime.now(),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text('Hadith Reading'),
        backgroundColor: const Color(0xFF27AE60),
        foregroundColor: Colors.white,
        leading: IconButton(
          onPressed: () {
            ref.read(islamicAppCurrentScreenProvider.notifier).state =
                IslamicAppScreen.hadithCollections;
          },
          icon: const Icon(Icons.arrow_back),
        ),
        actions: [
          IconButton(
            onPressed: _showSearchDialog,
            icon: const Icon(Icons.search),
            tooltip: 'Search Hadith',
          ),
          IconButton(
            onPressed: _showBookmarkDialog,
            icon: const Icon(Icons.bookmark_add),
            tooltip: 'Bookmark',
          ),
          PopupMenuButton(
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings),
                    SizedBox(width: 8),
                    Text('Reading Settings'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'share',
                child: Row(
                  children: [
                    Icon(Icons.share),
                    SizedBox(width: 8),
                    Text('Share Hadith'),
                  ],
                ),
              ),
            ],
            onSelected: _handleMenuAction,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildCollectionSelector(),
          _buildNavigationHeader(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _hadiths.isEmpty
                ? _buildEmptyState()
                : PageView.builder(
                    controller: _pageController,
                    itemCount: _hadiths.length,
                    onPageChanged: (index) {
                      setState(() {
                        _currentIndex = index;
                      });

                      // Load more hadiths when approaching the end
                      if (index >= _hadiths.length - 5 &&
                          _hasMoreHadiths &&
                          !_isLoading) {
                        _loadHadiths(loadMore: true);
                      }
                    },
                    itemBuilder: (context, index) {
                      return _buildHadithCard(_hadiths[index]);
                    },
                  ),
          ),
          _buildBottomNavigation(),
        ],
      ),
    );
  }

  Widget _buildNavigationHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Color(0xFFE9ECEF))),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Sahih al-Bukhari', // Collection name
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF27AE60),
                  ),
                ),
                Text(
                  _hadiths.isNotEmpty ? _hadiths[_currentIndex].theme : '',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: const Color(0xFF7F8C8D),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: const Color(0xFF27AE60).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              '${_currentIndex + 1} of ${_hadiths.length}',
              style: const TextStyle(
                color: Color(0xFF27AE60),
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHadithCard(HadithEntry hadith) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHadithHeader(hadith),
              const SizedBox(height: 24),
              _buildArabicText(hadith.arabicText),
              const SizedBox(height: 24),
              _buildEnglishText(hadith.englishTranslation),
              const SizedBox(height: 24),
              _buildHadithFooter(hadith),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHadithHeader(HadithEntry hadith) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: const Color(0xFF27AE60).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            'Hadith ${hadith.hadithNumber}',
            style: const TextStyle(
              color: Color(0xFF27AE60),
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
        const Spacer(),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getGradeColor(
              hadith.grade.displayName,
            ).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            hadith.grade.displayName,
            style: TextStyle(
              color: _getGradeColor(hadith.grade.displayName),
              fontWeight: FontWeight.w600,
              fontSize: 11,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildArabicText(String arabicText) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE9ECEF)),
      ),
      child: Text(
        arabicText,
        style: const TextStyle(
          fontSize: 20,
          height: 2.0,
          fontWeight: FontWeight.w500,
          color: Color(0xFF2C3E50),
        ),
        textAlign: TextAlign.right,
        textDirection: TextDirection.rtl,
      ),
    );
  }

  Widget _buildEnglishText(String englishText) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade100),
      ),
      child: Text(
        englishText,
        style: const TextStyle(
          fontSize: 16,
          height: 1.6,
          color: Color(0xFF2C3E50),
        ),
        textAlign: TextAlign.left,
      ),
    );
  }

  Widget _buildHadithFooter(HadithEntry hadith) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Divider(),
        const SizedBox(height: 12),
        Row(
          children: [
            const Icon(Icons.person, size: 16, color: Color(0xFF7F8C8D)),
            const SizedBox(width: 8),
            Text(
              'Narrated by: ${hadith.narrator}',
              style: const TextStyle(
                color: Color(0xFF7F8C8D),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            const Icon(Icons.library_books, size: 16, color: Color(0xFF7F8C8D)),
            const SizedBox(width: 8),
            Text(
              'Reference: ${hadith.reference}',
              style: const TextStyle(
                color: Color(0xFF7F8C8D),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Color(0xFFE9ECEF))),
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: _currentIndex > 0 ? _previousHadith : null,
            icon: const Icon(Icons.arrow_back_ios),
            tooltip: 'Previous',
          ),
          Expanded(
            child: LinearProgressIndicator(
              value: _hadiths.isNotEmpty
                  ? (_currentIndex + 1) / _hadiths.length
                  : 0.0,
              backgroundColor: Colors.grey[300],
              valueColor: const AlwaysStoppedAnimation<Color>(
                Color(0xFF27AE60),
              ),
            ),
          ),
          IconButton(
            onPressed: _currentIndex < _hadiths.length - 1 ? _nextHadith : null,
            icon: const Icon(Icons.arrow_forward_ios),
            tooltip: 'Next',
          ),
        ],
      ),
    );
  }

  Color _getGradeColor(String grade) {
    switch (grade.toLowerCase()) {
      case 'sahih':
        return const Color(0xFF27AE60);
      case 'hasan':
        return const Color(0xFF3498DB);
      case 'daif':
        return const Color(0xFFE67E22);
      default:
        return const Color(0xFF7F8C8D);
    }
  }

  void _previousHadith() {
    if (_currentIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _nextHadith() {
    if (_currentIndex < _hadiths.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _showSearchDialog() {
    // Implement search functionality
  }

  void _showBookmarkDialog() {
    // Implement bookmark functionality
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'settings':
        // Show reading settings
        break;
      case 'share':
        // Share current hadith
        break;
    }
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.menu_book_outlined, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No Hadiths Found',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'Try selecting a different collection',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => _loadHadiths(),
            child: const Text('Reload'),
          ),
        ],
      ),
    );
  }

  Widget _buildCollectionSelector() {
    final collections = [
      {'id': 'sahih_bukhari', 'name': 'Sahih Bukhari'},
      {'id': 'sahih_muslim', 'name': 'Sahih Muslim'},
      {'id': 'sunan_abu_dawud', 'name': 'Sunan Abu Dawud'},
      {'id': 'jami_tirmidhi', 'name': 'Jami\' at-Tirmidhi'},
      {'id': 'sunan_nasai', 'name': 'Sunan an-Nasa\'i'},
      {'id': 'sunan_ibn_majah', 'name': 'Sunan Ibn Majah'},
    ];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: DropdownButtonFormField<String>(
        value: _selectedCollection,
        decoration: const InputDecoration(
          labelText: 'Hadith Collection',
          border: OutlineInputBorder(),
          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        ),
        items: collections.map((collection) {
          return DropdownMenuItem(
            value: collection['id'],
            child: Text(collection['name']!),
          );
        }).toList(),
        onChanged: (value) {
          if (value != null && value != _selectedCollection) {
            setState(() {
              _selectedCollection = value;
              _currentIndex = 0;
            });
            _loadHadiths();
          }
        },
      ),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
}
