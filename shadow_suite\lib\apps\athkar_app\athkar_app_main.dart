import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'screens/athkar_dashboard.dart';
import 'screens/morning_athkar_screen.dart';
import 'screens/evening_athkar_screen.dart';
import 'screens/prayer_athkar_screen.dart';
import 'screens/general_athkar_screen.dart';
import 'screens/athkar_settings_screen.dart';

/// Main Athkar Application
class AthkarAppMain extends ConsumerStatefulWidget {
  const AthkarAppMain({super.key});

  @override
  ConsumerState<AthkarAppMain> createState() => _AthkarAppMainState();
}

class _AthkarAppMainState extends ConsumerState<AthkarAppMain> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const AthkarDashboard(),
    const MorningAthkarScreen(),
    const EveningAthkarScreen(),
    const PrayerAthkarScreen(),
    const GeneralAthkarScreen(),
    const AthkarSettingsScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        selectedItemColor: Colors.green,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.wb_sunny),
            label: 'Morning',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.nights_stay),
            label: 'Evening',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.mosque),
            label: 'Prayer',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.menu_book),
            label: 'General',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'Settings',
          ),
        ],
      ),
    );
  }
}
