import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../../../core/database/database_initializer.dart';
import '../models/account.dart';
import '../models/transaction.dart';
import '../models/budget.dart';
import '../models/goal.dart';
import 'goals_service.dart';
import 'categories_service.dart';

class MoneyFlowDatabaseService {
  static Database? _database;
  static const String _databaseName = 'money_flow.db';
  static const int _databaseVersion = 1;

  // Table names
  static const String _accountsTable = 'accounts';
  static const String _transactionsTable = 'transactions';
  static const String _budgetsTable = 'budgets';

  static Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  static Future<Database> _initDatabase() async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, _databaseName);

    // Use safe database opening with proper initialization
    final db = await DatabaseInitializer.safeOpenDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
    );

    if (db == null) {
      throw Exception('Failed to initialize Money Flow database');
    }

    return db;
  }

  static Future<void> _onCreate(Database db, int version) async {
    // Create accounts table
    await db.execute('''
      CREATE TABLE $_accountsTable (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        bankName TEXT NOT NULL,
        accountNumber TEXT NOT NULL,
        balance REAL NOT NULL DEFAULT 0.0,
        currency TEXT NOT NULL DEFAULT 'USD',
        color TEXT NOT NULL DEFAULT '#2196F3',
        isActive INTEGER NOT NULL DEFAULT 1,
        description TEXT,
        routingNumber TEXT,
        swiftCode TEXT,
        creditLimit REAL,
        interestRate REAL,
        openDate INTEGER,
        notes TEXT,
        includeInNetWorth INTEGER NOT NULL DEFAULT 1,
        allowOverdraft INTEGER NOT NULL DEFAULT 0,
        overdraftLimit REAL,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL
      )
    ''');

    // Create transactions table
    await db.execute('''
      CREATE TABLE $_transactionsTable (
        id TEXT PRIMARY KEY,
        accountId TEXT NOT NULL,
        toAccountId TEXT,
        type TEXT NOT NULL,
        amount REAL NOT NULL,
        category TEXT NOT NULL,
        description TEXT NOT NULL,
        notes TEXT,
        tags TEXT,
        receiptPath TEXT,
        date INTEGER NOT NULL,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        FOREIGN KEY (accountId) REFERENCES $_accountsTable (id) ON DELETE CASCADE,
        FOREIGN KEY (toAccountId) REFERENCES $_accountsTable (id) ON DELETE SET NULL
      )
    ''');

    // Create budgets table
    await db.execute('''
      CREATE TABLE $_budgetsTable (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        category TEXT NOT NULL,
        amount REAL NOT NULL,
        spent REAL NOT NULL DEFAULT 0.0,
        period TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'active',
        startDate INTEGER NOT NULL,
        endDate INTEGER NOT NULL,
        color TEXT NOT NULL DEFAULT '#4CAF50',
        alertsEnabled INTEGER NOT NULL DEFAULT 1,
        alertThreshold REAL NOT NULL DEFAULT 0.8,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL
      )
    ''');

    // Create goals tables
    await GoalsService.createTables(db);

    // Create categories table
    await CategoriesService.createTable(db);

    // Create indexes for better performance
    await db.execute(
      'CREATE INDEX idx_transactions_account ON $_transactionsTable(accountId)',
    );
    await db.execute(
      'CREATE INDEX idx_transactions_date ON $_transactionsTable(date)',
    );
    await db.execute(
      'CREATE INDEX idx_transactions_category ON $_transactionsTable(category)',
    );
    await db.execute(
      'CREATE INDEX idx_budgets_category ON $_budgetsTable(category)',
    );
    await db.execute(
      'CREATE INDEX idx_budgets_period ON $_budgetsTable(startDate, endDate)',
    );

    // Insert sample data
    await _insertSampleData(db);
  }

  static Future<void> _insertSampleData(Database db) async {
    // Sample accounts
    final sampleAccounts = [
      Account(
        name: 'Main Checking',
        type: AccountType.checking,
        bankName: 'Chase Bank',
        accountNumber: '**********',
        balance: 2500.00,
        color: '#2196F3',
      ),
      Account(
        name: 'Savings Account',
        type: AccountType.savings,
        bankName: 'Chase Bank',
        accountNumber: '**********',
        balance: 15000.00,
        color: '#4CAF50',
      ),
      Account(
        name: 'Credit Card',
        type: AccountType.credit,
        bankName: 'Capital One',
        accountNumber: '****************',
        balance: -850.00,
        color: '#FF5722',
      ),
    ];

    for (final account in sampleAccounts) {
      await db.insert(_accountsTable, account.toMap());
    }

    // Sample transactions
    final now = DateTime.now();
    final sampleTransactions = [
      MoneyTransaction(
        accountId: sampleAccounts[0].id,
        type: TransactionType.income,
        amount: 3500.00,
        category: 'Salary',
        description: 'Monthly Salary',
        date: now.subtract(const Duration(days: 5)),
      ),
      MoneyTransaction(
        accountId: sampleAccounts[0].id,
        type: TransactionType.expense,
        amount: 120.50,
        category: 'Food & Dining',
        description: 'Grocery Shopping',
        date: now.subtract(const Duration(days: 2)),
      ),
      MoneyTransaction(
        accountId: sampleAccounts[0].id,
        type: TransactionType.expense,
        amount: 45.00,
        category: 'Transportation',
        description: 'Gas Station',
        date: now.subtract(const Duration(days: 1)),
      ),
    ];

    for (final transaction in sampleTransactions) {
      await db.insert(_transactionsTable, transaction.toMap());
    }

    // Sample budgets
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    final sampleBudgets = [
      Budget(
        name: 'Monthly Food Budget',
        category: 'Food & Dining',
        amount: 500.00,
        spent: 120.50,
        period: BudgetPeriod.monthly,
        startDate: startOfMonth,
        endDate: endOfMonth,
        color: '#FF9800',
      ),
      Budget(
        name: 'Transportation Budget',
        category: 'Transportation',
        amount: 200.00,
        spent: 45.00,
        period: BudgetPeriod.monthly,
        startDate: startOfMonth,
        endDate: endOfMonth,
        color: '#9C27B0',
      ),
    ];

    for (final budget in sampleBudgets) {
      await db.insert(_budgetsTable, budget.toMap());
    }

    // Sample goals
    final sampleGoals = [
      Goal(
        name: 'Emergency Fund',
        description: 'Build an emergency fund for unexpected expenses',
        targetAmount: 10000.00,
        currentAmount: 2500.00,
        targetDate: DateTime(now.year + 1, now.month, now.day),
        category: GoalCategory.emergencyFund,
        color: '#4CAF50',
      ),
      Goal(
        name: 'Vacation to Europe',
        description: 'Save for a 2-week vacation to Europe',
        targetAmount: 5000.00,
        currentAmount: 1200.00,
        targetDate: DateTime(now.year, now.month + 8, now.day),
        category: GoalCategory.vacation,
        color: '#2196F3',
      ),
      Goal(
        name: 'New Car',
        description: 'Save for a down payment on a new car',
        targetAmount: 8000.00,
        currentAmount: 3500.00,
        targetDate: DateTime(now.year, now.month + 6, now.day),
        category: GoalCategory.car,
        color: '#FF9800',
      ),
    ];

    for (final goal in sampleGoals) {
      await GoalsService.insertGoal(goal);
    }

    // Populate default categories
    await CategoriesService.populateDefaultCategories();
  }

  // Account CRUD operations
  static Future<String> insertAccount(Account account) async {
    final db = await database;
    await db.insert(_accountsTable, account.toMap());
    return account.id;
  }

  static Future<List<Account>> getAllAccounts() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _accountsTable,
      where: 'isActive = ?',
      whereArgs: [1],
      orderBy: 'name ASC',
    );
    return List.generate(maps.length, (i) => Account.fromMap(maps[i]));
  }

  static Future<Account?> getAccountById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _accountsTable,
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Account.fromMap(maps.first);
    }
    return null;
  }

  static Future<void> updateAccount(Account account) async {
    final db = await database;
    await db.update(
      _accountsTable,
      account.toMap(),
      where: 'id = ?',
      whereArgs: [account.id],
    );
  }

  static Future<void> deleteAccount(String id) async {
    final db = await database;
    await db.update(
      _accountsTable,
      {'isActive': 0},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  static Future<void> updateAccountBalance(
    String accountId,
    double newBalance,
  ) async {
    final db = await database;
    await db.update(
      _accountsTable,
      {
        'balance': newBalance,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      },
      where: 'id = ?',
      whereArgs: [accountId],
    );
  }

  // Transaction CRUD operations
  static Future<String> insertTransaction(MoneyTransaction transaction) async {
    final db = await database;
    await db.insert(_transactionsTable, transaction.toMap());

    // Update account balance
    final account = await getAccountById(transaction.accountId);
    if (account != null) {
      double newBalance = account.balance;
      if (transaction.type == TransactionType.income) {
        newBalance += transaction.amount;
      } else if (transaction.type == TransactionType.expense) {
        newBalance -= transaction.amount;
      } else if (transaction.type == TransactionType.transfer &&
          transaction.toAccountId != null) {
        // Handle transfer
        newBalance -= transaction.amount;
        final toAccount = await getAccountById(transaction.toAccountId!);
        if (toAccount != null) {
          await updateAccountBalance(
            transaction.toAccountId!,
            toAccount.balance + transaction.amount,
          );
        }
      }
      await updateAccountBalance(transaction.accountId, newBalance);
    }

    return transaction.id;
  }

  static Future<List<MoneyTransaction>> getAllTransactions() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _transactionsTable,
      orderBy: 'date DESC',
    );
    return List.generate(maps.length, (i) => MoneyTransaction.fromMap(maps[i]));
  }

  static Future<List<MoneyTransaction>> getTransactionsByAccount(
    String accountId,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _transactionsTable,
      where: 'accountId = ? OR toAccountId = ?',
      whereArgs: [accountId, accountId],
      orderBy: 'date DESC',
    );
    return List.generate(maps.length, (i) => MoneyTransaction.fromMap(maps[i]));
  }

  static Future<List<MoneyTransaction>> getRecentTransactions({
    int limit = 10,
  }) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _transactionsTable,
      orderBy: 'date DESC',
      limit: limit,
    );
    return List.generate(maps.length, (i) => MoneyTransaction.fromMap(maps[i]));
  }

  static Future<void> updateTransaction(MoneyTransaction transaction) async {
    final db = await database;
    await db.update(
      _transactionsTable,
      transaction.toMap(),
      where: 'id = ?',
      whereArgs: [transaction.id],
    );
  }

  static Future<void> deleteTransaction(String id) async {
    final db = await database;
    await db.delete(_transactionsTable, where: 'id = ?', whereArgs: [id]);
  }

  // Budget CRUD operations
  static Future<String> insertBudget(Budget budget) async {
    final db = await database;
    await db.insert(_budgetsTable, budget.toMap());
    return budget.id;
  }

  static Future<List<Budget>> getAllBudgets() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _budgetsTable,
      orderBy: 'createdAt DESC',
    );
    return List.generate(maps.length, (i) => Budget.fromMap(maps[i]));
  }

  static Future<List<Budget>> getActiveBudgets() async {
    final db = await database;
    final now = DateTime.now().millisecondsSinceEpoch;
    final List<Map<String, dynamic>> maps = await db.query(
      _budgetsTable,
      where: 'status = ? AND startDate <= ? AND endDate >= ?',
      whereArgs: ['active', now, now],
      orderBy: 'createdAt DESC',
    );
    return List.generate(maps.length, (i) => Budget.fromMap(maps[i]));
  }

  static Future<void> updateBudget(Budget budget) async {
    final db = await database;
    await db.update(
      _budgetsTable,
      budget.toMap(),
      where: 'id = ?',
      whereArgs: [budget.id],
    );
  }

  static Future<void> deleteBudget(String id) async {
    final db = await database;
    await db.delete(_budgetsTable, where: 'id = ?', whereArgs: [id]);
  }

  // Statistics and Reports
  static Future<Map<String, double>> getAccountBalances() async {
    final accounts = await getAllAccounts();
    double totalBalance = 0.0;
    double totalAssets = 0.0;
    double totalLiabilities = 0.0;

    for (final account in accounts) {
      totalBalance += account.balance;
      if (account.balance >= 0) {
        totalAssets += account.balance;
      } else {
        totalLiabilities += account.balance.abs();
      }
    }

    return {
      'totalBalance': totalBalance,
      'totalAssets': totalAssets,
      'totalLiabilities': totalLiabilities,
    };
  }

  static Future<Map<String, double>> getMonthlySpending() async {
    final db = await database;
    final now = DateTime.now();
    final startOfMonth = DateTime(
      now.year,
      now.month,
      1,
    ).millisecondsSinceEpoch;
    final endOfMonth = DateTime(
      now.year,
      now.month + 1,
      0,
    ).millisecondsSinceEpoch;

    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT category, SUM(amount) as total
      FROM $_transactionsTable
      WHERE type = 'expense' AND date >= ? AND date <= ?
      GROUP BY category
      ORDER BY total DESC
    ''',
      [startOfMonth, endOfMonth],
    );

    final Map<String, double> spending = {};
    for (final map in maps) {
      spending[map['category']] = map['total'].toDouble();
    }

    return spending;
  }

  static Future<Map<String, int>> getStatistics() async {
    final db = await database;

    final accountsCount =
        Sqflite.firstIntValue(
          await db.rawQuery(
            'SELECT COUNT(*) FROM $_accountsTable WHERE isActive = 1',
          ),
        ) ??
        0;

    final transactionsCount =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM $_transactionsTable'),
        ) ??
        0;

    final budgetsCount =
        Sqflite.firstIntValue(
          await db.rawQuery(
            'SELECT COUNT(*) FROM $_budgetsTable WHERE status = ?',
            ['active'],
          ),
        ) ??
        0;

    return {
      'accounts': accountsCount,
      'transactions': transactionsCount,
      'budgets': budgetsCount,
    };
  }
}
