import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path/path.dart' as path;
import 'error_handler.dart' as error_handler;

/// Comprehensive storage scanning service for discovering media and files
class StorageScannerService {
  static bool _isScanning = false;
  static bool _isInitialized = false;
  static final Map<String, List<FileSystemEntity>> _scanCache = {};
  static final StreamController<ScanProgress> _progressController =
      StreamController.broadcast();
  static Timer? _backgroundScanTimer;

  /// Initialize the storage scanner service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Start background scanning timer (every 5 minutes)
      _backgroundScanTimer = Timer.periodic(
        const Duration(minutes: 5),
        (_) => _performBackgroundScan(),
      );

      _isInitialized = true;
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.platform,
        context: 'Initialize storage scanner service',
      );
    }
  }

  /// Dispose of the service
  static void dispose() {
    _backgroundScanTimer?.cancel();
    _progressController.close();
    _isInitialized = false;
  }

  /// Get scan progress stream
  static Stream<ScanProgress> get progressStream => _progressController.stream;

  /// Check if currently scanning
  static bool get isScanning => _isScanning;

  /// Perform full storage scan
  static Future<StorageScanResult> performFullScan({
    bool includeImages = true,
    bool includeVideos = true,
    bool includeAudio = true,
    bool includeDocuments = true,
    bool includeOthers = false,
  }) async {
    if (_isScanning) {
      throw Exception('Scan already in progress');
    }

    _isScanning = true;
    final result = StorageScanResult();

    try {
      _progressController.add(
        ScanProgress(
          phase: ScanPhase.starting,
          message: 'Initializing storage scan...',
          progress: 0.0,
        ),
      );

      // Get all accessible directories
      final directories = await _getAccessibleDirectories();

      _progressController.add(
        ScanProgress(
          phase: ScanPhase.scanning,
          message: 'Scanning directories...',
          progress: 0.1,
        ),
      );

      int processedDirs = 0;
      for (final directory in directories) {
        try {
          await _scanDirectory(
            directory,
            result,
            includeImages: includeImages,
            includeVideos: includeVideos,
            includeAudio: includeAudio,
            includeDocuments: includeDocuments,
            includeOthers: includeOthers,
          );

          processedDirs++;
          final progress = 0.1 + (0.8 * processedDirs / directories.length);

          _progressController.add(
            ScanProgress(
              phase: ScanPhase.scanning,
              message: 'Scanned ${directory.path}',
              progress: progress,
            ),
          );
        } catch (e) {
          // Continue scanning other directories even if one fails
          debugPrint('Failed to scan directory ${directory.path}: $e');
        }
      }

      _progressController.add(
        ScanProgress(
          phase: ScanPhase.organizing,
          message: 'Organizing results...',
          progress: 0.9,
        ),
      );

      // Sort results by modification date (newest first)
      result.sortByDate();

      _progressController.add(
        ScanProgress(
          phase: ScanPhase.completed,
          message: 'Scan completed successfully',
          progress: 1.0,
        ),
      );

      // Cache results
      _cacheResults(result);

      return result;
    } catch (e) {
      _progressController.add(
        ScanProgress(
          phase: ScanPhase.error,
          message: 'Scan failed: ${e.toString()}',
          progress: 0.0,
        ),
      );

      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.file,
        context: 'Perform full storage scan',
      );

      rethrow;
    } finally {
      _isScanning = false;
    }
  }

  /// Perform quick scan for specific file types
  static Future<List<File>> quickScan(FileType fileType) async {
    try {
      final cacheKey = fileType.toString();
      if (_scanCache.containsKey(cacheKey)) {
        return _scanCache[cacheKey]!.cast<File>();
      }

      final directories = await _getAccessibleDirectories();
      final files = <File>[];

      for (final directory in directories) {
        try {
          await for (final entity in directory.list(recursive: true)) {
            if (entity is File && _matchesFileType(entity, fileType)) {
              files.add(entity);
              if (files.length >= 1000) break; // Limit for quick scan
            }
          }
        } catch (e) {
          // Continue with other directories
        }
      }

      // Cache results
      _scanCache[cacheKey] = files;

      return files;
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.file,
        context: 'Quick scan for ${fileType.toString()}',
      );
      return [];
    }
  }

  /// Get accessible directories for scanning
  static Future<List<Directory>> _getAccessibleDirectories() async {
    final directories = <Directory>[];

    try {
      // Add common directories based on platform
      if (Platform.isAndroid) {
        // Try to access common Android directories
        final commonPaths = [
          '/storage/emulated/0/DCIM',
          '/storage/emulated/0/Pictures',
          '/storage/emulated/0/Movies',
          '/storage/emulated/0/Music',
          '/storage/emulated/0/Download',
          '/storage/emulated/0/Documents',
          '/sdcard/DCIM',
          '/sdcard/Pictures',
          '/sdcard/Movies',
          '/sdcard/Music',
        ];

        for (final pathStr in commonPaths) {
          try {
            final dir = Directory(pathStr);
            if (await dir.exists()) {
              directories.add(dir);
            }
          } catch (e) {
            // Directory not accessible, continue
          }
        }
      } else if (Platform.isWindows) {
        // Try to access common Windows directories
        final userProfile = Platform.environment['USERPROFILE'];
        if (userProfile != null) {
          final commonPaths = [
            '$userProfile\\Pictures',
            '$userProfile\\Videos',
            '$userProfile\\Music',
            '$userProfile\\Documents',
            '$userProfile\\Downloads',
            '$userProfile\\Desktop',
          ];

          for (final pathStr in commonPaths) {
            try {
              final dir = Directory(pathStr);
              if (await dir.exists()) {
                directories.add(dir);
              }
            } catch (e) {
              // Directory not accessible, continue
            }
          }
        }

        // Also try common system paths
        final systemPaths = [
          'C:\\Users\\<USER>\\Pictures',
          'C:\\Users\\<USER>\\Videos',
          'C:\\Users\\<USER>\\Music',
          'C:\\Users\\<USER>\\Documents',
        ];

        for (final pathStr in systemPaths) {
          try {
            final dir = Directory(pathStr);
            if (await dir.exists()) {
              directories.add(dir);
            }
          } catch (e) {
            // Directory not accessible, continue
          }
        }
      } else {
        // For other platforms, try current directory and common paths
        try {
          final currentDir = Directory.current;
          directories.add(currentDir);
        } catch (e) {
          // Continue
        }
      }

      // Remove duplicates
      final uniquePaths = <String>{};
      return directories.where((dir) => uniquePaths.add(dir.path)).toList();
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.platform,
        context: 'Get accessible directories',
      );
      return [];
    }
  }

  /// Scan a specific directory
  static Future<void> _scanDirectory(
    Directory directory,
    StorageScanResult result, {
    required bool includeImages,
    required bool includeVideos,
    required bool includeAudio,
    required bool includeDocuments,
    required bool includeOthers,
  }) async {
    try {
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          final fileType = _getFileType(entity);

          switch (fileType) {
            case FileType.image:
              if (includeImages) result.images.add(entity);
              break;
            case FileType.video:
              if (includeVideos) result.videos.add(entity);
              break;
            case FileType.audio:
              if (includeAudio) result.audio.add(entity);
              break;
            case FileType.document:
              if (includeDocuments) result.documents.add(entity);
              break;
            case FileType.other:
              if (includeOthers) result.others.add(entity);
              break;
          }
        }
      }
    } catch (e) {
      // Directory access denied or other error, continue
    }
  }

  /// Determine file type based on extension
  static FileType _getFileType(File file) {
    final extension = path.extension(file.path).toLowerCase();

    if (_imageExtensions.contains(extension)) return FileType.image;
    if (_videoExtensions.contains(extension)) return FileType.video;
    if (_audioExtensions.contains(extension)) return FileType.audio;
    if (_documentExtensions.contains(extension)) return FileType.document;

    return FileType.other;
  }

  /// Check if file matches specific type
  static bool _matchesFileType(File file, FileType type) {
    return _getFileType(file) == type;
  }

  /// Perform background scan (lightweight)
  static Future<void> _performBackgroundScan() async {
    if (_isScanning) return;

    try {
      // Perform lightweight scan to update cache
      await quickScan(FileType.image);
      await quickScan(FileType.video);
      await quickScan(FileType.audio);
    } catch (e) {
      // Background scan failure is not critical
      debugPrint('Background scan failed: $e');
    }
  }

  /// Cache scan results
  static void _cacheResults(StorageScanResult result) {
    _scanCache['images'] = result.images;
    _scanCache['videos'] = result.videos;
    _scanCache['audio'] = result.audio;
    _scanCache['documents'] = result.documents;
    _scanCache['others'] = result.others;
  }

  /// Clear scan cache
  static void clearCache() {
    _scanCache.clear();
  }

  /// Get cached results
  static Map<String, List<FileSystemEntity>> getCachedResults() {
    return Map.from(_scanCache);
  }

  // File extension definitions
  static const _imageExtensions = {
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.bmp',
    '.webp',
    '.svg',
    '.tiff',
    '.ico',
  };

  static const _videoExtensions = {
    '.mp4',
    '.avi',
    '.mkv',
    '.mov',
    '.wmv',
    '.flv',
    '.webm',
    '.m4v',
    '.3gp',
  };

  static const _audioExtensions = {
    '.mp3',
    '.wav',
    '.flac',
    '.aac',
    '.ogg',
    '.wma',
    '.m4a',
    '.opus',
  };

  static const _documentExtensions = {
    '.pdf',
    '.doc',
    '.docx',
    '.txt',
    '.rtf',
    '.xls',
    '.xlsx',
    '.ppt',
    '.pptx',
  };
}

/// File type enumeration
enum FileType { image, video, audio, document, other }

/// Scan phase enumeration
enum ScanPhase { starting, scanning, organizing, completed, error }

/// Scan progress information
class ScanProgress {
  final ScanPhase phase;
  final String message;
  final double progress; // 0.0 to 1.0

  const ScanProgress({
    required this.phase,
    required this.message,
    required this.progress,
  });
}

/// Storage scan result container
class StorageScanResult {
  final List<File> images = [];
  final List<File> videos = [];
  final List<File> audio = [];
  final List<File> documents = [];
  final List<File> others = [];

  int get totalFiles =>
      images.length +
      videos.length +
      audio.length +
      documents.length +
      others.length;

  void sortByDate() {
    images.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));
    videos.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));
    audio.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));
    documents.sort(
      (a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()),
    );
    others.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));
  }
}

/// Riverpod providers for storage scanning
final storageScannerProvider = Provider<StorageScannerService>((ref) {
  return StorageScannerService();
});

final scanProgressProvider = StreamProvider<ScanProgress>((ref) {
  return StorageScannerService.progressStream;
});

final isScanningProvider = Provider<bool>((ref) {
  return StorageScannerService.isScanning;
});
