import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'error_handler.dart';
import 'package:http/http.dart' as http;
// import 'error_handler.dart' as error_handler; // Duplicate import removed

class NetworkService {
  static const Duration _defaultTimeout = Duration(seconds: 30);
  static const int _maxRetries = 3;
  static const Duration _retryDelay = Duration(seconds: 2);
  
  // Check internet connectivity
  static Future<bool> hasInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com')
          .timeout(const Duration(seconds: 5));
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
  
  // GET request with error handling
  static Future<NetworkResponse<T>> get<T>(
    String url, {
    Map<String, String>? headers,
    Duration? timeout,
    T Function(Map<String, dynamic>)? fromJson,
    int maxRetries = _maxRetries,
  }) async {
    return _executeWithRetry(
      () => _performGet<T>(url, headers: headers, timeout: timeout, fromJson: fromJson),
      maxRetries: maxRetries,
      operationName: 'GET $url',
    );
  }
  
  // POST request with error handling
  static Future<NetworkResponse<T>> post<T>(
    String url, {
    Map<String, String>? headers,
    dynamic body,
    Duration? timeout,
    T Function(Map<String, dynamic>)? fromJson,
    int maxRetries = _maxRetries,
  }) async {
    return _executeWithRetry(
      () => _performPost<T>(url, headers: headers, body: body, timeout: timeout, fromJson: fromJson),
      maxRetries: maxRetries,
      operationName: 'POST $url',
    );
  }
  
  // PUT request with error handling
  static Future<NetworkResponse<T>> put<T>(
    String url, {
    Map<String, String>? headers,
    dynamic body,
    Duration? timeout,
    T Function(Map<String, dynamic>)? fromJson,
    int maxRetries = _maxRetries,
  }) async {
    return _executeWithRetry(
      () => _performPut<T>(url, headers: headers, body: body, timeout: timeout, fromJson: fromJson),
      maxRetries: maxRetries,
      operationName: 'PUT $url',
    );
  }
  
  // DELETE request with error handling
  static Future<NetworkResponse<T>> delete<T>(
    String url, {
    Map<String, String>? headers,
    Duration? timeout,
    T Function(Map<String, dynamic>)? fromJson,
    int maxRetries = _maxRetries,
  }) async {
    return _executeWithRetry(
      () => _performDelete<T>(url, headers: headers, timeout: timeout, fromJson: fromJson),
      maxRetries: maxRetries,
      operationName: 'DELETE $url',
    );
  }
  
  // Download file with progress tracking
  static Future<NetworkResponse<String>> downloadFile(
    String url,
    String savePath, {
    Map<String, String>? headers,
    Function(int received, int total)? onProgress,
    Duration? timeout,
  }) async {
    try {
      if (!await hasInternetConnection()) {
        return NetworkResponse.error(
          NetworkError.noConnection('No internet connection available'),
        );
      }
      
      final request = http.Request('GET', Uri.parse(url));
      if (headers != null) {
        request.headers.addAll(headers);
      }
      
      final streamedResponse = await request.send()
          .timeout(timeout ?? _defaultTimeout);
      
      if (streamedResponse.statusCode != 200) {
        return NetworkResponse.error(
          NetworkError.httpError(streamedResponse.statusCode, 'Download failed'),
        );
      }
      
      final file = File(savePath);
      final sink = file.openWrite();
      
      var received = 0;
      final total = streamedResponse.contentLength ?? 0;
      
      await for (final chunk in streamedResponse.stream) {
        sink.add(chunk);
        received += chunk.length;
        onProgress?.call(received, total);
      }
      
      await sink.close();
      
      return NetworkResponse.success(savePath);
      
    } catch (error) {
      ErrorHandler.handleNetworkError(error, operation: 'Download file: $url');
      return NetworkResponse.error(_mapError(error));
    }
  }
  
  // Upload file with progress tracking
  static Future<NetworkResponse<T>> uploadFile<T>(
    String url,
    String filePath, {
    Map<String, String>? headers,
    Map<String, String>? fields,
    String fieldName = 'file',
    Function(int sent, int total)? onProgress,
    Duration? timeout,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      if (!await hasInternetConnection()) {
        return NetworkResponse.error(
          NetworkError.noConnection('No internet connection available'),
        );
      }

      final file = File(filePath);
      if (!await file.exists()) {
        return NetworkResponse.error(
          NetworkError.fileNotFound('File not found: $filePath'),
        );
      }

      final request = http.MultipartRequest('POST', Uri.parse(url));

      if (headers != null) {
        request.headers.addAll(headers);
      }

      if (fields != null) {
        request.fields.addAll(fields);
      }

      final multipartFile = await http.MultipartFile.fromPath(fieldName, filePath);
      request.files.add(multipartFile);

      final streamedResponse = await request.send()
          .timeout(timeout ?? _defaultTimeout);

      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        if (fromJson != null && response.body.isNotEmpty) {
          final jsonData = json.decode(response.body) as Map<String, dynamic>;
          final data = fromJson(jsonData);
          return NetworkResponse.success(data);
        } else {
          return NetworkResponse.success(null);
        }
      } else {
        return NetworkResponse.error(
          NetworkError.httpError(response.statusCode, response.body),
        );
      }

    } catch (error) {
      ErrorHandler.handleNetworkError(error, operation: 'Upload file: $filePath');
      return NetworkResponse.error(_mapError(error));
    }
  }
  
  // Private implementation methods
  static Future<NetworkResponse<T>> _executeWithRetry<T>(
    Future<NetworkResponse<T>> Function() operation, {
    required int maxRetries,
    required String operationName,
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      attempts++;
      
      final response = await operation();
      
      if (response.isSuccess || !_shouldRetry(response.error)) {
        return response;
      }
      
      if (attempts < maxRetries) {
        await Future.delayed(_retryDelay * attempts);
      }
    }
    
    // If we get here, all retries failed
    return NetworkResponse.error(
      NetworkError.maxRetriesExceeded('Max retries exceeded for $operation'),
    );
  }
  
  static Future<NetworkResponse<T>> _performGet<T>(
    String url, {
    Map<String, String>? headers,
    Duration? timeout,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      if (!await hasInternetConnection()) {
        return NetworkResponse.error(
          NetworkError.noConnection('No internet connection available'),
        );
      }
      
      final response = await http.get(
        Uri.parse(url),
        headers: headers,
      ).timeout(timeout ?? _defaultTimeout);
      
      return _processResponse<T>(response, fromJson);
      
    } catch (error) {
      ErrorHandler.handleNetworkError(error, operation: 'GET $url');
      return NetworkResponse.error(_mapError(error));
    }
  }
  
  static Future<NetworkResponse<T>> _performPost<T>(
    String url, {
    Map<String, String>? headers,
    dynamic body,
    Duration? timeout,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      if (!await hasInternetConnection()) {
        return NetworkResponse.error(
          NetworkError.noConnection('No internet connection available'),
        );
      }
      
      final response = await http.post(
        Uri.parse(url),
        headers: headers,
        body: body is Map ? json.encode(body) : body,
      ).timeout(timeout ?? _defaultTimeout);
      
      return _processResponse<T>(response, fromJson);
      
    } catch (error) {
      ErrorHandler.handleNetworkError(error, operation: 'POST $url');
      return NetworkResponse.error(_mapError(error));
    }
  }
  
  static Future<NetworkResponse<T>> _performPut<T>(
    String url, {
    Map<String, String>? headers,
    dynamic body,
    Duration? timeout,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      if (!await hasInternetConnection()) {
        return NetworkResponse.error(
          NetworkError.noConnection('No internet connection available'),
        );
      }
      
      final response = await http.put(
        Uri.parse(url),
        headers: headers,
        body: body is Map ? json.encode(body) : body,
      ).timeout(timeout ?? _defaultTimeout);
      
      return _processResponse<T>(response, fromJson);
      
    } catch (error) {
      ErrorHandler.handleNetworkError(error, operation: 'PUT $url');
      return NetworkResponse.error(_mapError(error));
    }
  }
  
  static Future<NetworkResponse<T>> _performDelete<T>(
    String url, {
    Map<String, String>? headers,
    Duration? timeout,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      if (!await hasInternetConnection()) {
        return NetworkResponse.error(
          NetworkError.noConnection('No internet connection available'),
        );
      }
      
      final response = await http.delete(
        Uri.parse(url),
        headers: headers,
      ).timeout(timeout ?? _defaultTimeout);
      
      return _processResponse<T>(response, fromJson);
      
    } catch (error) {
      ErrorHandler.handleNetworkError(error, operation: 'DELETE $url');
      return NetworkResponse.error(_mapError(error));
    }
  }
  
  static NetworkResponse<T> _processResponse<T>(
    http.Response response,
    T Function(Map<String, dynamic>)? fromJson,
  ) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      if (fromJson != null && response.body.isNotEmpty) {
        try {
          final jsonData = json.decode(response.body) as Map<String, dynamic>;
          final data = fromJson(jsonData);
          return NetworkResponse.success(data);
        } catch (e) {
          return NetworkResponse.error(
            NetworkError.parseError('Failed to parse response: $e'),
          );
        }
      } else {
        return NetworkResponse.success(null);
      }
    } else {
      return NetworkResponse.error(
        NetworkError.httpError(response.statusCode, response.body),
      );
    }
  }
  
  static NetworkError _mapError(dynamic error) {
    if (error is SocketException) {
      return NetworkError.noConnection('No internet connection');
    } else if (error is TimeoutException) {
      return NetworkError.timeout('Request timed out');
    } else if (error is HttpException) {
      return NetworkError.httpError(0, error.message);
    } else if (error is FormatException) {
      return NetworkError.parseError('Invalid response format');
    } else {
      return NetworkError.unknown('Unknown network error: $error');
    }
  }
  
  static bool _shouldRetry(NetworkError? error) {
    if (error == null) return false;
    
    return error.type == NetworkErrorType.timeout ||
           error.type == NetworkErrorType.noConnection ||
           (error.type == NetworkErrorType.httpError && 
            error.statusCode != null && 
            error.statusCode! >= 500);
  }
}

// Network response wrapper
class NetworkResponse<T> {
  final bool isSuccess;
  final T? data;
  final NetworkError? error;
  
  const NetworkResponse._(this.isSuccess, this.data, this.error);
  
  factory NetworkResponse.success(T? data) {
    return NetworkResponse._(true, data, null);
  }
  
  factory NetworkResponse.error(NetworkError error) {
    return NetworkResponse._(false, null, error);
  }
  
  bool get isError => !isSuccess;
}

// Network error class
class NetworkError {
  final NetworkErrorType type;
  final String message;
  final int? statusCode;
  final dynamic originalError;
  
  const NetworkError._(this.type, this.message, this.statusCode, this.originalError);
  
  factory NetworkError.noConnection(String message) {
    return NetworkError._(NetworkErrorType.noConnection, message, null, null);
  }
  
  factory NetworkError.timeout(String message) {
    return NetworkError._(NetworkErrorType.timeout, message, null, null);
  }
  
  factory NetworkError.httpError(int statusCode, String message) {
    return NetworkError._(NetworkErrorType.httpError, message, statusCode, null);
  }
  
  factory NetworkError.parseError(String message) {
    return NetworkError._(NetworkErrorType.parseError, message, null, null);
  }
  
  factory NetworkError.fileNotFound(String message) {
    return NetworkError._(NetworkErrorType.fileNotFound, message, null, null);
  }
  
  factory NetworkError.maxRetriesExceeded(String message) {
    return NetworkError._(NetworkErrorType.maxRetriesExceeded, message, null, null);
  }
  
  factory NetworkError.unknown(String message) {
    return NetworkError._(NetworkErrorType.unknown, message, null, null);
  }
  
  @override
  String toString() {
    return 'NetworkError(${type.name}): $message${statusCode != null ? ' (Status: $statusCode)' : ''}';
  }
}

enum NetworkErrorType {
  noConnection,
  timeout,
  httpError,
  parseError,
  fileNotFound,
  maxRetriesExceeded,
  unknown,
}
