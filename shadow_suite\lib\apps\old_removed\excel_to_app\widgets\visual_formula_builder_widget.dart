import 'package:flutter/material.dart';
import '../services/visual_formula_builder_service.dart';
import '../services/advanced_formula_engine.dart' as engine;

/// Visual formula builder widget with drag-and-drop interface
class VisualFormulaBuilderWidget extends StatefulWidget {
  final Function(String)? onFormulaChanged;
  final String? initialFormula;

  const VisualFormulaBuilderWidget({
    super.key,
    this.onFormulaChanged,
    this.initialFormula,
  });

  @override
  State<VisualFormulaBuilderWidget> createState() =>
      _VisualFormulaBuilderWidgetState();
}

class _VisualFormulaBuilderWidgetState extends State<VisualFormulaBuilderWidget>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final List<FormulaNode> _nodes = [];
  final TextEditingController _formulaController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  String _selectedCategoryId = 'math';
  FormulaValidationResult? _validationResult;
  List<FunctionSuggestion> _suggestions = [];
  bool _showSuggestions = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    VisualFormulaBuilderService.initialize();

    if (widget.initialFormula != null) {
      _formulaController.text = widget.initialFormula!;
      _nodes.addAll(
        VisualFormulaBuilderService.parseFormula(widget.initialFormula!),
      );
    }

    _searchController.addListener(_onSearchChanged);
    _formulaController.addListener(_onFormulaChanged);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _formulaController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text;
    if (query.isNotEmpty) {
      setState(() {
        _suggestions = VisualFormulaBuilderService.getFunctionSuggestions(
          query,
        );
        _showSuggestions = true;
      });
    } else {
      setState(() {
        _showSuggestions = false;
      });
    }
  }

  void _onFormulaChanged() {
    final formula = _formulaController.text;
    _validateFormula();
    widget.onFormulaChanged?.call(formula);
  }

  void _validateFormula() {
    setState(() {
      _validationResult = VisualFormulaBuilderService.validateFormula(_nodes);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        children: [
          _buildHeader(),
          _buildFormulaInput(),
          if (_validationResult != null) _buildValidationResults(),
          Expanded(
            child: Row(
              children: [
                Expanded(flex: 1, child: _buildComponentLibrary()),
                Expanded(flex: 2, child: _buildVisualCanvas()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
      ),
      child: Row(
        children: [
          const Icon(Icons.functions, size: 24),
          const SizedBox(width: 8),
          Text(
            'Visual Formula Builder',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const Spacer(),
          IconButton(
            onPressed: _clearFormula,
            icon: const Icon(Icons.clear),
            tooltip: 'Clear Formula',
          ),
          IconButton(
            onPressed: _testFormula,
            icon: const Icon(Icons.play_arrow),
            tooltip: 'Test Formula',
          ),
        ],
      ),
    );
  }

  Widget _buildFormulaInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Formula:', style: Theme.of(context).textTheme.titleMedium),
          const SizedBox(height: 8),
          Stack(
            children: [
              TextField(
                controller: _formulaController,
                decoration: const InputDecoration(
                  hintText: 'Enter formula or build visually below...',
                  border: OutlineInputBorder(),
                  prefixText: '= ',
                ),
                maxLines: 2,
              ),
              if (_showSuggestions) _buildSuggestionsList(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestionsList() {
    return Positioned(
      top: 60,
      left: 0,
      right: 0,
      child: Material(
        elevation: 4,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          constraints: const BoxConstraints(maxHeight: 200),
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _suggestions.length,
            itemBuilder: (context, index) {
              final suggestion = _suggestions[index];
              return ListTile(
                title: Text(suggestion.name),
                subtitle: Text(suggestion.description),
                trailing: Text(suggestion.category),
                onTap: () => _insertSuggestion(suggestion),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildValidationResults() {
    final result = _validationResult!;

    if (result.isValid && result.warnings.isEmpty) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.green, size: 16),
            const SizedBox(width: 8),
            Text(
              'Formula is valid',
              style: TextStyle(color: Colors.green[700]),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (result.errors.isNotEmpty) ...[
            Row(
              children: [
                const Icon(Icons.error, color: Colors.red, size: 16),
                const SizedBox(width: 8),
                Text(
                  'Errors:',
                  style: TextStyle(
                    color: Colors.red[700],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            ...result.errors.map(
              (error) => Padding(
                padding: const EdgeInsets.only(left: 24, top: 4),
                child: Text(error, style: TextStyle(color: Colors.red[700])),
              ),
            ),
          ],
          if (result.warnings.isNotEmpty) ...[
            if (result.errors.isNotEmpty) const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.warning, color: Colors.orange, size: 16),
                const SizedBox(width: 8),
                Text(
                  'Warnings:',
                  style: TextStyle(
                    color: Colors.orange[700],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            ...result.warnings.map(
              (warning) => Padding(
                padding: const EdgeInsets.only(left: 24, top: 4),
                child: Text(
                  warning,
                  style: TextStyle(color: Colors.orange[700]),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildComponentLibrary() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              TextField(
                controller: _searchController,
                decoration: const InputDecoration(
                  labelText: 'Search Functions',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TabBar(
                controller: _tabController,
                isScrollable: true,
                tabs: const [
                  Tab(text: 'Functions'),
                  Tab(text: 'Operators'),
                  Tab(text: 'Templates'),
                ],
              ),
            ],
          ),
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildFunctionsList(),
              _buildOperatorsList(),
              _buildTemplatesList(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFunctionsList() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: DropdownButtonFormField<String>(
            value: _selectedCategoryId,
            decoration: const InputDecoration(
              labelText: 'Category',
              border: OutlineInputBorder(),
            ),
            items: VisualFormulaBuilderService.categories.map((category) {
              return DropdownMenuItem(
                value: category.id,
                child: Row(
                  children: [
                    Icon(category.icon, size: 16, color: category.color),
                    const SizedBox(width: 8),
                    Text(category.name),
                  ],
                ),
              );
            }).toList(),
            onChanged: (value) {
              setState(() => _selectedCategoryId = value!);
            },
          ),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: ListView.builder(
            itemCount: VisualFormulaBuilderService.getComponentsByCategory(
              _selectedCategoryId,
            ).where((c) => c.type == FormulaComponentType.function).length,
            itemBuilder: (context, index) {
              final components =
                  VisualFormulaBuilderService.getComponentsByCategory(
                        _selectedCategoryId,
                      )
                      .where((c) => c.type == FormulaComponentType.function)
                      .toList();
              final component = components[index];
              return _buildComponentTile(component);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildOperatorsList() {
    final operators = VisualFormulaBuilderService.components
        .where((c) => c.type == FormulaComponentType.operator)
        .toList();

    return ListView.builder(
      itemCount: operators.length,
      itemBuilder: (context, index) {
        return _buildComponentTile(operators[index]);
      },
    );
  }

  Widget _buildTemplatesList() {
    return const Center(
      child: Text('Formula templates will be displayed here'),
    );
  }

  Widget _buildComponentTile(FormulaComponent component) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        title: Text(component.name),
        subtitle: Text(component.description),
        trailing: IconButton(
          onPressed: () => _addComponent(component),
          icon: const Icon(Icons.add),
        ),
        onTap: () => _showComponentDetails(component),
      ),
    );
  }

  Widget _buildVisualCanvas() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Stack(
        children: [
          // Grid background
          CustomPaint(painter: GridPainter(), size: Size.infinite),
          // Formula nodes
          ..._nodes.map((node) => _buildFormulaNode(node)),
          // Drop zone
          if (_nodes.isEmpty)
            const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.functions, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    'Drag functions here to build your formula',
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFormulaNode(FormulaNode node) {
    return Positioned(
      left: node.position.dx,
      top: node.position.dy,
      child: Draggable<FormulaNode>(
        data: node,
        feedback: Material(
          elevation: 4,
          borderRadius: BorderRadius.circular(8),
          child: _buildNodeWidget(node, isDragging: true),
        ),
        childWhenDragging: Opacity(opacity: 0.5, child: _buildNodeWidget(node)),
        child: _buildNodeWidget(node),
      ),
    );
  }

  Widget _buildNodeWidget(FormulaNode node, {bool isDragging = false}) {
    Color color;
    IconData icon;

    switch (node.type) {
      case FormulaNodeType.function:
        color = Colors.blue;
        icon = Icons.functions;
        break;
      case FormulaNodeType.operator:
        color = Colors.orange;
        icon = Icons.calculate;
        break;
      case FormulaNodeType.value:
        color = Colors.green;
        icon = Icons.numbers;
        break;
      case FormulaNodeType.reference:
        color = Colors.purple;
        icon = Icons.grid_on;
        break;
    }

    return Container(
      width: 120,
      height: 60,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        border: Border.all(color: color),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            node.value,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  void _addComponent(FormulaComponent component) {
    final node = FormulaNode(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: _componentTypeToNodeType(component.type),
      value: component.name,
      position: Offset(50 + _nodes.length * 130.0, 100),
    );

    setState(() {
      _nodes.add(node);
      _updateFormula();
    });
  }

  void _insertSuggestion(FunctionSuggestion suggestion) {
    final currentText = _formulaController.text;
    final selection = _formulaController.selection;

    final newText = currentText.replaceRange(
      selection.start,
      selection.end,
      suggestion.name,
    );

    _formulaController.text = newText;
    _formulaController.selection = TextSelection.collapsed(
      offset: selection.start + suggestion.name.length,
    );

    setState(() {
      _showSuggestions = false;
    });
  }

  void _updateFormula() {
    final formula = VisualFormulaBuilderService.buildFormula(_nodes);
    _formulaController.text = formula;
    _validateFormula();
  }

  void _clearFormula() {
    setState(() {
      _nodes.clear();
      _formulaController.clear();
      _validationResult = null;
    });
  }

  void _testFormula() {
    final formula = _formulaController.text;
    if (formula.isNotEmpty) {
      try {
        final result = engine.AdvancedFormulaEngine.evaluate(formula);
        _showTestResult(result);
      } catch (e) {
        _showTestResult('Error: $e');
      }
    }
  }

  void _showTestResult(dynamic result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Formula Result'),
        content: Text('Result: $result'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showComponentDetails(FormulaComponent component) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(component.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Description: ${component.description}'),
            const SizedBox(height: 8),
            Text('Syntax: ${component.syntax}'),
            const SizedBox(height: 8),
            Text('Example: ${component.example}'),
            const SizedBox(height: 8),
            Text('Returns: ${component.returnType}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _addComponent(component);
            },
            child: const Text('Add to Formula'),
          ),
        ],
      ),
    );
  }

  FormulaNodeType _componentTypeToNodeType(FormulaComponentType type) {
    switch (type) {
      case FormulaComponentType.function:
        return FormulaNodeType.function;
      case FormulaComponentType.operator:
        return FormulaNodeType.operator;
      case FormulaComponentType.value:
        return FormulaNodeType.value;
      case FormulaComponentType.reference:
        return FormulaNodeType.reference;
    }
  }
}

/// Grid painter for visual canvas
class GridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey[300]!
      ..strokeWidth = 0.5;

    const gridSize = 20.0;

    // Draw vertical lines
    for (double x = 0; x < size.width; x += gridSize) {
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }

    // Draw horizontal lines
    for (double y = 0; y < size.height; y += gridSize) {
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
