// WebDAV Connection Model
class WebDAVConnection {
  final String id;
  final String serverUrl;
  final String username;
  final String password;
  final bool useSSL;
  final bool isConnected;
  final DateTime? lastConnected;
  final List<String> supportedMethods;
  final DateTime createdAt;

  const WebDAVConnection({
    required this.id,
    required this.serverUrl,
    required this.username,
    required this.password,
    required this.useSSL,
    required this.isConnected,
    this.lastConnected,
    required this.supportedMethods,
    required this.createdAt,
  });

  WebDAVConnection copyWith({
    bool? isConnected,
    DateTime? lastConnected,
    List<String>? supportedMethods,
  }) {
    return WebDAVConnection(
      id: id,
      serverUrl: serverUrl,
      username: username,
      password: password,
      useSSL: useSSL,
      isConnected: isConnected ?? this.isConnected,
      lastConnected: lastConnected ?? this.lastConnected,
      supportedMethods: supportedMethods ?? this.supportedMethods,
      createdAt: createdAt,
    );
  }
}

// SFTP Connection Model
class SFTPConnection {
  final String id;
  final String hostname;
  final int port;
  final String username;
  final String? password;
  final String? privateKeyPath;
  final String? passphrase;
  final bool isConnected;
  final DateTime? lastConnected;
  final String? serverFingerprint;
  final DateTime createdAt;

  const SFTPConnection({
    required this.id,
    required this.hostname,
    required this.port,
    required this.username,
    this.password,
    this.privateKeyPath,
    this.passphrase,
    required this.isConnected,
    this.lastConnected,
    this.serverFingerprint,
    required this.createdAt,
  });

  SFTPConnection copyWith({
    bool? isConnected,
    DateTime? lastConnected,
    String? serverFingerprint,
  }) {
    return SFTPConnection(
      id: id,
      hostname: hostname,
      port: port,
      username: username,
      password: password,
      privateKeyPath: privateKeyPath,
      passphrase: passphrase,
      isConnected: isConnected ?? this.isConnected,
      lastConnected: lastConnected ?? this.lastConnected,
      serverFingerprint: serverFingerprint ?? this.serverFingerprint,
      createdAt: createdAt,
    );
  }
}

// Network Drive Model
class NetworkDrive {
  final String id;
  final String drivePath;
  final String mountPoint;
  final NetworkDriveType type;
  final String? username;
  final String? password;
  final Map<String, String> options;
  final bool isMounted;
  final int totalSpace;
  final int freeSpace;
  final DateTime? lastAccessed;
  final DateTime createdAt;

  const NetworkDrive({
    required this.id,
    required this.drivePath,
    required this.mountPoint,
    required this.type,
    this.username,
    this.password,
    required this.options,
    required this.isMounted,
    required this.totalSpace,
    required this.freeSpace,
    this.lastAccessed,
    required this.createdAt,
  });

  NetworkDrive copyWith({
    bool? isMounted,
    int? totalSpace,
    int? freeSpace,
    DateTime? lastAccessed,
  }) {
    return NetworkDrive(
      id: id,
      drivePath: drivePath,
      mountPoint: mountPoint,
      type: type,
      username: username,
      password: password,
      options: options,
      isMounted: isMounted ?? this.isMounted,
      totalSpace: totalSpace ?? this.totalSpace,
      freeSpace: freeSpace ?? this.freeSpace,
      lastAccessed: lastAccessed ?? this.lastAccessed,
      createdAt: createdAt,
    );
  }

  double get usagePercentage {
    if (totalSpace == 0) return 0.0;
    final usedSpace = totalSpace - freeSpace;
    return (usedSpace / totalSpace) * 100.0;
  }
}

// Remote Desktop Session Model
class RemoteDesktopSession {
  final String id;
  final String hostname;
  final int port;
  final String username;
  final String password;
  final RemoteDesktopProtocol protocol;
  final bool isConnected;
  final DateTime? sessionStartTime;
  final DateTime? lastActivity;
  final DateTime createdAt;

  const RemoteDesktopSession({
    required this.id,
    required this.hostname,
    required this.port,
    required this.username,
    required this.password,
    required this.protocol,
    required this.isConnected,
    this.sessionStartTime,
    this.lastActivity,
    required this.createdAt,
  });

  RemoteDesktopSession copyWith({
    bool? isConnected,
    DateTime? sessionStartTime,
    DateTime? lastActivity,
  }) {
    return RemoteDesktopSession(
      id: id,
      hostname: hostname,
      port: port,
      username: username,
      password: password,
      protocol: protocol,
      isConnected: isConnected ?? this.isConnected,
      sessionStartTime: sessionStartTime ?? this.sessionStartTime,
      lastActivity: lastActivity ?? this.lastActivity,
      createdAt: createdAt,
    );
  }

  Duration? get sessionDuration {
    if (sessionStartTime == null) return null;
    return DateTime.now().difference(sessionStartTime!);
  }
}

// P2P Connection Model
class P2PConnection {
  final String id;
  final String localPath;
  final List<String> allowedPeers;
  final List<String> connectedPeers;
  final bool enableEncryption;
  final String encryptionKey;
  final bool isActive;
  final int port;
  final int bytesShared;
  final int peersConnected;
  final DateTime createdAt;

  const P2PConnection({
    required this.id,
    required this.localPath,
    required this.allowedPeers,
    required this.connectedPeers,
    required this.enableEncryption,
    required this.encryptionKey,
    required this.isActive,
    required this.port,
    required this.bytesShared,
    required this.peersConnected,
    required this.createdAt,
  });

  P2PConnection copyWith({
    List<String>? connectedPeers,
    bool? isActive,
    int? bytesShared,
    int? peersConnected,
  }) {
    return P2PConnection(
      id: id,
      localPath: localPath,
      allowedPeers: allowedPeers,
      connectedPeers: connectedPeers ?? this.connectedPeers,
      enableEncryption: enableEncryption,
      encryptionKey: encryptionKey,
      isActive: isActive ?? this.isActive,
      port: port,
      bytesShared: bytesShared ?? this.bytesShared,
      peersConnected: peersConnected ?? this.peersConnected,
      createdAt: createdAt,
    );
  }
}

// Torrent Download Model
class TorrentDownload {
  final String id;
  final String torrentPath;
  final String downloadPath;
  final TorrentSettings settings;
  final TorrentStatus status;
  final double progress;
  final int downloadSpeed;
  final int uploadSpeed;
  final int seeders;
  final int leechers;
  final int totalSize;
  final int downloadedSize;
  final int uploadedSize;
  final Duration? eta;
  final DateTime createdAt;

  const TorrentDownload({
    required this.id,
    required this.torrentPath,
    required this.downloadPath,
    required this.settings,
    required this.status,
    required this.progress,
    required this.downloadSpeed,
    required this.uploadSpeed,
    required this.seeders,
    required this.leechers,
    required this.totalSize,
    required this.downloadedSize,
    required this.uploadedSize,
    this.eta,
    required this.createdAt,
  });

  double get ratio {
    if (downloadedSize == 0) return 0.0;
    return uploadedSize / downloadedSize;
  }
}

// Torrent Settings Model
class TorrentSettings {
  final int maxDownloadSpeed;
  final int maxUploadSpeed;
  final int maxConnections;
  final bool enableDHT;
  final bool enablePEX;
  final bool enableLSD;
  final double seedRatioLimit;
  final Duration seedTimeLimit;

  const TorrentSettings({
    required this.maxDownloadSpeed,
    required this.maxUploadSpeed,
    required this.maxConnections,
    required this.enableDHT,
    required this.enablePEX,
    required this.enableLSD,
    required this.seedRatioLimit,
    required this.seedTimeLimit,
  });

  factory TorrentSettings.defaultSettings() {
    return const TorrentSettings(
      maxDownloadSpeed: 0, // Unlimited
      maxUploadSpeed: 0,   // Unlimited
      maxConnections: 200,
      enableDHT: true,
      enablePEX: true,
      enableLSD: true,
      seedRatioLimit: 2.0,
      seedTimeLimit: Duration(hours: 24),
    );
  }
}

// Network Speed Test Model
class NetworkSpeedTest {
  final String id;
  final String testServerUrl;
  final int testDurationSeconds;
  final SpeedTestStatus status;
  final double downloadSpeed;
  final double uploadSpeed;
  final int latency;
  final double jitter;
  final double packetLoss;
  final DateTime startTime;
  final DateTime? endTime;

  const NetworkSpeedTest({
    required this.id,
    required this.testServerUrl,
    required this.testDurationSeconds,
    required this.status,
    required this.downloadSpeed,
    required this.uploadSpeed,
    required this.latency,
    required this.jitter,
    required this.packetLoss,
    required this.startTime,
    this.endTime,
  });

  NetworkSpeedTest copyWith({
    SpeedTestStatus? status,
    double? downloadSpeed,
    double? uploadSpeed,
    int? latency,
    double? jitter,
    double? packetLoss,
    DateTime? endTime,
  }) {
    return NetworkSpeedTest(
      id: id,
      testServerUrl: testServerUrl,
      testDurationSeconds: testDurationSeconds,
      status: status ?? this.status,
      downloadSpeed: downloadSpeed ?? this.downloadSpeed,
      uploadSpeed: uploadSpeed ?? this.uploadSpeed,
      latency: latency ?? this.latency,
      jitter: jitter ?? this.jitter,
      packetLoss: packetLoss ?? this.packetLoss,
      startTime: startTime,
      endTime: endTime ?? this.endTime,
    );
  }

  Duration? get testDuration {
    if (endTime == null) return null;
    return endTime!.difference(startTime);
  }
}

// VPN Connection Model
class VPNConnection {
  final String id;
  final String serverAddress;
  final String username;
  final String password;
  final VPNProtocol protocol;
  final String? configFile;
  final bool isConnected;
  final DateTime? connectionTime;
  final String? publicIP;
  final String? vpnIP;
  final DateTime createdAt;

  const VPNConnection({
    required this.id,
    required this.serverAddress,
    required this.username,
    required this.password,
    required this.protocol,
    this.configFile,
    required this.isConnected,
    this.connectionTime,
    this.publicIP,
    this.vpnIP,
    required this.createdAt,
  });

  VPNConnection copyWith({
    bool? isConnected,
    DateTime? connectionTime,
    String? publicIP,
    String? vpnIP,
  }) {
    return VPNConnection(
      id: id,
      serverAddress: serverAddress,
      username: username,
      password: password,
      protocol: protocol,
      configFile: configFile,
      isConnected: isConnected ?? this.isConnected,
      connectionTime: connectionTime ?? this.connectionTime,
      publicIP: publicIP ?? this.publicIP,
      vpnIP: vpnIP ?? this.vpnIP,
      createdAt: createdAt,
    );
  }
}

// Network Cache Config Model
class NetworkCacheConfig {
  final String id;
  final String networkPath;
  final String cachePath;
  final CachePolicy policy;
  final int maxCacheSize;
  final int currentCacheSize;
  final double hitRate;
  final bool isEnabled;
  final DateTime createdAt;

  const NetworkCacheConfig({
    required this.id,
    required this.networkPath,
    required this.cachePath,
    required this.policy,
    required this.maxCacheSize,
    required this.currentCacheSize,
    required this.hitRate,
    required this.isEnabled,
    required this.createdAt,
  });

  double get cacheUsagePercentage {
    if (maxCacheSize == 0) return 0.0;
    return (currentCacheSize / maxCacheSize) * 100.0;
  }
}

// Firewall Rule Model
class FirewallRule {
  final String id;
  final String name;
  final FirewallAction action;
  final FirewallDirection direction;
  final String? sourceIP;
  final String? destinationIP;
  final int? sourcePort;
  final int? destinationPort;
  final NetworkProtocol? protocol;
  final bool isEnabled;
  final DateTime createdAt;

  const FirewallRule({
    required this.id,
    required this.name,
    required this.action,
    required this.direction,
    this.sourceIP,
    this.destinationIP,
    this.sourcePort,
    this.destinationPort,
    this.protocol,
    required this.isEnabled,
    required this.createdAt,
  });
}

// Network Transfer Model
class NetworkTransfer {
  final String id;
  final String sourcePath;
  final String destinationPath;
  final NetworkTransferType type;
  final TransferStatus status;
  final int totalSize;
  final int transferredSize;
  final double progress;
  final int transferSpeed;
  final Duration? eta;
  final DateTime startTime;
  final DateTime? endTime;

  const NetworkTransfer({
    required this.id,
    required this.sourcePath,
    required this.destinationPath,
    required this.type,
    required this.status,
    required this.totalSize,
    required this.transferredSize,
    required this.progress,
    required this.transferSpeed,
    this.eta,
    required this.startTime,
    this.endTime,
  });
}

// Network Event Model
class NetworkEvent {
  final NetworkEventType type;
  final String? connectionId;
  final String? driveId;
  final String message;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  const NetworkEvent({
    required this.type,
    this.connectionId,
    this.driveId,
    required this.message,
    required this.timestamp,
    this.metadata,
  });
}

// Connection Result Models
class ConnectionResult {
  final bool isSuccess;
  final String? errorMessage;
  final String? serverFingerprint;
  final int? totalSpace;
  final int? freeSpace;
  final String? publicIP;
  final String? vpnIP;

  const ConnectionResult({
    required this.isSuccess,
    this.errorMessage,
    this.serverFingerprint,
    this.totalSpace,
    this.freeSpace,
    this.publicIP,
    this.vpnIP,
  });
}

class SpeedTestResult {
  final double downloadSpeed;
  final double uploadSpeed;
  final int latency;
  final double jitter;
  final double packetLoss;

  const SpeedTestResult({
    required this.downloadSpeed,
    required this.uploadSpeed,
    required this.latency,
    required this.jitter,
    required this.packetLoss,
  });
}

// Enums
enum NetworkDriveType { smb, nfs, ftp, webdav }
enum RemoteDesktopProtocol { rdp, vnc, ssh }
enum TorrentStatus { pending, downloading, seeding, paused, completed, error }
enum SpeedTestStatus { pending, running, completed, error }
enum VPNProtocol { openVPN, ipsec, wireguard, pptp, l2tp }
enum CachePolicy { writeThrough, writeBack, writeAround }
enum FirewallAction { allow, deny, drop }
enum FirewallDirection { inbound, outbound, both }
enum NetworkProtocol { tcp, udp, icmp, any }
enum NetworkTransferType { upload, download, sync }
enum TransferStatus { pending, transferring, completed, failed, cancelled }
enum NetworkEventType {
  webdavConnected,
  webdavDisconnected,
  sftpConnected,
  sftpDisconnected,
  networkDriveMounted,
  networkDriveUnmounted,
  p2pSharingStarted,
  p2pSharingStopped,
  torrentAdded,
  torrentCompleted,
  speedTestCompleted,
  vpnConnected,
  vpnDisconnected,
  networkCacheEnabled,
  networkCacheDisabled,
  firewallConfigured,
  transferStarted,
  transferCompleted,
}
