import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/services.dart';

/// Audio Transcription Screen with speech-to-text functionality
class AudioTranscriptionScreen extends ConsumerStatefulWidget {
  final String recordingId;
  final String recordingTitle;
  final String filePath;

  const AudioTranscriptionScreen({
    super.key,
    required this.recordingId,
    required this.recordingTitle,
    required this.filePath,
  });

  @override
  ConsumerState<AudioTranscriptionScreen> createState() => _AudioTranscriptionScreenState();
}

class _AudioTranscriptionScreenState extends ConsumerState<AudioTranscriptionScreen> {
  bool _isTranscribing = false;
  String _transcriptionText = '';
  double _transcriptionProgress = 0.0;
  String _selectedLanguage = 'en-US';
  bool _showTimestamps = true;
  bool _showSpeakerLabels = false;
  List<TranscriptionSegment> _segments = [];

  final List<Map<String, String>> _supportedLanguages = [
    {'code': 'en-US', 'name': 'English (US)'},
    {'code': 'en-GB', 'name': 'English (UK)'},
    {'code': 'es-ES', 'name': 'Spanish'},
    {'code': 'fr-FR', 'name': 'French'},
    {'code': 'de-DE', 'name': 'German'},
    {'code': 'it-IT', 'name': 'Italian'},
    {'code': 'pt-BR', 'name': 'Portuguese (Brazil)'},
    {'code': 'ja-JP', 'name': 'Japanese'},
    {'code': 'ko-KR', 'name': 'Korean'},
    {'code': 'zh-CN', 'name': 'Chinese (Simplified)'},
  ];

  @override
  void initState() {
    super.initState();
    _loadExistingTranscription();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Audio Transcription'),
        backgroundColor: Colors.blueGrey,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showTranscriptionSettings,
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareTranscription,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildRecordingInfo(),
          _buildTranscriptionControls(),
          if (_isTranscribing) _buildProgressIndicator(),
          Expanded(child: _buildTranscriptionContent()),
        ],
      ),
    );
  }

  Widget _buildRecordingInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blueGrey.withValues(alpha: 0.1),
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.recordingTitle,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'File: ${widget.filePath.split('/').last}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTranscriptionControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedLanguage,
                  decoration: const InputDecoration(
                    labelText: 'Language',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.language),
                  ),
                  items: _supportedLanguages.map((lang) {
                    return DropdownMenuItem(
                      value: lang['code'],
                      child: Text(lang['name']!),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() => _selectedLanguage = value);
                    }
                  },
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                onPressed: _isTranscribing ? null : _startTranscription,
                icon: Icon(_isTranscribing ? Icons.hourglass_empty : Icons.transcribe),
                label: Text(_isTranscribing ? 'Transcribing...' : 'Start Transcription'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blueGrey,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: CheckboxListTile(
                  title: const Text('Show Timestamps'),
                  value: _showTimestamps,
                  onChanged: (value) => setState(() => _showTimestamps = value ?? false),
                  dense: true,
                ),
              ),
              Expanded(
                child: CheckboxListTile(
                  title: const Text('Speaker Labels'),
                  value: _showSpeakerLabels,
                  onChanged: (value) => setState(() => _showSpeakerLabels = value ?? false),
                  dense: true,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          LinearProgressIndicator(
            value: _transcriptionProgress,
            backgroundColor: Colors.grey.shade300,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.blueGrey),
          ),
          const SizedBox(height: 8),
          Text(
            'Transcribing audio... ${(_transcriptionProgress * 100).toStringAsFixed(0)}%',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTranscriptionContent() {
    if (_transcriptionText.isEmpty && _segments.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.transcribe, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No transcription available',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Start transcription to convert speech to text',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Transcription',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.copy),
                    onPressed: _copyTranscription,
                    tooltip: 'Copy to clipboard',
                  ),
                  IconButton(
                    icon: const Icon(Icons.edit),
                    onPressed: _editTranscription,
                    tooltip: 'Edit transcription',
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: SingleChildScrollView(
                child: _showTimestamps && _segments.isNotEmpty
                  ? _buildSegmentedTranscription()
                  : _buildPlainTranscription(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSegmentedTranscription() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: _segments.map((segment) {
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.blueGrey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      _formatTimestamp(segment.startTime),
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.blueGrey,
                      ),
                    ),
                  ),
                  if (_showSpeakerLabels && segment.speaker.isNotEmpty) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.purple.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        segment.speaker,
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.purple,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 8),
              Text(
                segment.text,
                style: const TextStyle(fontSize: 14, height: 1.5),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildPlainTranscription() {
    return SelectableText(
      _transcriptionText,
      style: const TextStyle(fontSize: 14, height: 1.5),
    );
  }

  void _loadExistingTranscription() {
    // Simulate loading existing transcription
    // In a real app, this would load from database or cache
    setState(() {
      _transcriptionText = 'Sample transcription text would appear here...';
      _segments = [
        TranscriptionSegment(
          startTime: 0.0,
          endTime: 3.5,
          text: 'Hello, this is a sample transcription.',
          speaker: 'Speaker 1',
          confidence: 0.95,
        ),
        TranscriptionSegment(
          startTime: 3.5,
          endTime: 7.2,
          text: 'The audio transcription feature converts speech to text.',
          speaker: 'Speaker 1',
          confidence: 0.92,
        ),
        TranscriptionSegment(
          startTime: 7.2,
          endTime: 11.0,
          text: 'You can edit and share the transcription results.',
          speaker: 'Speaker 1',
          confidence: 0.88,
        ),
      ];
    });
  }

  Future<void> _startTranscription() async {
    setState(() {
      _isTranscribing = true;
      _transcriptionProgress = 0.0;
    });

    // Simulate transcription progress
    for (int i = 0; i <= 100; i += 10) {
      await Future.delayed(const Duration(milliseconds: 200));
      if (mounted) {
        setState(() => _transcriptionProgress = i / 100);
      }
    }

    // Simulate transcription completion
    setState(() {
      _isTranscribing = false;
      _transcriptionText = '''
This is a simulated transcription of your audio recording. In a real implementation, this would use speech-to-text services like Google Cloud Speech-to-Text, Azure Speech Services, or AWS Transcribe.

The transcription would include:
- Accurate speech recognition
- Punctuation and formatting
- Speaker identification (if enabled)
- Timestamps for each segment
- Confidence scores for accuracy

You can edit this text, copy it to clipboard, or share it with others.
''';
      
      _segments = [
        TranscriptionSegment(
          startTime: 0.0,
          endTime: 5.2,
          text: 'This is a simulated transcription of your audio recording.',
          speaker: 'Speaker 1',
          confidence: 0.95,
        ),
        TranscriptionSegment(
          startTime: 5.2,
          endTime: 12.8,
          text: 'In a real implementation, this would use speech-to-text services like Google Cloud Speech-to-Text, Azure Speech Services, or AWS Transcribe.',
          speaker: 'Speaker 1',
          confidence: 0.92,
        ),
        TranscriptionSegment(
          startTime: 12.8,
          endTime: 18.5,
          text: 'The transcription would include accurate speech recognition, punctuation and formatting.',
          speaker: 'Speaker 1',
          confidence: 0.89,
        ),
      ];
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Transcription completed successfully'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _copyTranscription() {
    if (_transcriptionText.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: _transcriptionText));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Transcription copied to clipboard')),
      );
    }
  }

  void _editTranscription() {
    showDialog(
      context: context,
      builder: (context) {
        final controller = TextEditingController(text: _transcriptionText);
        return AlertDialog(
          title: const Text('Edit Transcription'),
          content: SizedBox(
            width: 400,
            height: 300,
            child: TextField(
              controller: controller,
              maxLines: null,
              expands: true,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Edit your transcription...',
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() => _transcriptionText = controller.text);
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Transcription updated')),
                );
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }

  void _shareTranscription() {
    if (_transcriptionText.isNotEmpty) {
      // In a real app, this would use the share plugin
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Share functionality would be implemented here')),
      );
    }
  }

  void _showTranscriptionSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Transcription Settings'),
        content: const Text('Advanced transcription settings will be available here.'),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  String _formatTimestamp(double seconds) {
    final minutes = (seconds / 60).floor();
    final remainingSeconds = (seconds % 60).floor();
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }
}

/// Model for transcription segments
class TranscriptionSegment {
  final double startTime;
  final double endTime;
  final String text;
  final String speaker;
  final double confidence;

  TranscriptionSegment({
    required this.startTime,
    required this.endTime,
    required this.text,
    required this.speaker,
    required this.confidence,
  });
}
