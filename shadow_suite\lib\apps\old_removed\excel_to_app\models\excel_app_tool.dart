import 'dart:convert';

enum SecurityType { none, pin, biometric }

enum ComponentType {
  // Input Components
  textInput,
  numberInput,
  passwordInput,
  emailInput,
  multilineText,

  // Selection Components
  dropdown,
  radioButton,
  checkbox,
  multiSelect,

  // Date & Time Components
  dateInput,
  timeInput,
  dateTimeInput,

  // Action Components
  button,
  submitButton,
  resetButton,

  // Display Components
  label,
  heading,
  paragraph,
  calculatedDisplay,

  // Interactive Components
  slider,
  stepper,
  rating,

  // Data Components
  chart,
  table,
  dataGrid,

  // Layout Components
  container,
  row,
  column,
  spacer,

  // Advanced Components
  fileUpload,
  colorPicker,
  qrCode,
}

class ExcelCell {
  final String address; // A1, B2, etc.
  final dynamic value;
  final String? formula;
  final bool isFormula;
  final Map<String, dynamic> formatting;

  const ExcelCell({
    required this.address,
    this.value,
    this.formula,
    this.isFormula = false,
    this.formatting = const {},
  });

  ExcelCell copyWith({
    String? address,
    dynamic value,
    String? formula,
    bool? isFormula,
    Map<String, dynamic>? formatting,
  }) {
    return ExcelCell(
      address: address ?? this.address,
      value: value ?? this.value,
      formula: formula ?? this.formula,
      isFormula: isFormula ?? this.isFormula,
      formatting: formatting ?? this.formatting,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'address': address,
      'value': value,
      'formula': formula,
      'isFormula': isFormula,
      'formatting': formatting,
    };
  }

  factory ExcelCell.fromMap(Map<String, dynamic> map) {
    return ExcelCell(
      address: map['address'] ?? '',
      value: map['value'],
      formula: map['formula'],
      isFormula: map['isFormula'] ?? false,
      formatting: Map<String, dynamic>.from(map['formatting'] ?? {}),
    );
  }

  String toJson() => json.encode(toMap());
  factory ExcelCell.fromJson(String source) => ExcelCell.fromMap(json.decode(source));
}

class ExcelAppTemplate {
  final String id;
  final String name;
  final String description;
  final ExcelAppTool tool;
  final DateTime createdAt;
  final String author;

  const ExcelAppTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.tool,
    required this.createdAt,
    required this.author,
  });

  ExcelAppTemplate copyWith({
    String? id,
    String? name,
    String? description,
    ExcelAppTool? tool,
    DateTime? createdAt,
    String? author,
  }) {
    return ExcelAppTemplate(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      tool: tool ?? this.tool,
      createdAt: createdAt ?? this.createdAt,
      author: author ?? this.author,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'tool': tool.toMap(),
      'createdAt': createdAt.millisecondsSinceEpoch,
      'author': author,
    };
  }

  factory ExcelAppTemplate.fromMap(Map<String, dynamic> map) {
    return ExcelAppTemplate(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      tool: ExcelAppTool.fromMap(map['tool']),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      author: map['author'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory ExcelAppTemplate.fromJson(String source) => ExcelAppTemplate.fromMap(json.decode(source));
}

class ExcelSpreadsheet {
  final String name;
  final int columns;
  final int rows;
  final Map<String, ExcelCell> cells;
  final DateTime lastModified;

  const ExcelSpreadsheet({
    required this.name,
    required this.columns,
    required this.rows,
    this.cells = const {},
    required this.lastModified,
  });

  ExcelSpreadsheet copyWith({
    String? name,
    int? columns,
    int? rows,
    Map<String, ExcelCell>? cells,
    DateTime? lastModified,
  }) {
    return ExcelSpreadsheet(
      name: name ?? this.name,
      columns: columns ?? this.columns,
      rows: rows ?? this.rows,
      cells: cells ?? this.cells,
      lastModified: lastModified ?? this.lastModified,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'columns': columns,
      'rows': rows,
      'cells': cells.map((key, value) => MapEntry(key, value.toMap())),
      'lastModified': lastModified.millisecondsSinceEpoch,
    };
  }

  factory ExcelSpreadsheet.fromMap(Map<String, dynamic> map) {
    return ExcelSpreadsheet(
      name: map['name'] ?? '',
      columns: map['columns']?.toInt() ?? 10,
      rows: map['rows']?.toInt() ?? 20,
      cells: Map<String, ExcelCell>.from(
        (map['cells'] ?? {}).map(
          (key, value) => MapEntry(key, ExcelCell.fromMap(value)),
        ),
      ),
      lastModified: DateTime.fromMillisecondsSinceEpoch(map['lastModified'] ?? 0),
    );
  }

  String toJson() => json.encode(toMap());
  factory ExcelSpreadsheet.fromJson(String source) => ExcelSpreadsheet.fromMap(json.decode(source));
}

class UIComponent {
  final String id;
  final ComponentType type;
  final String label;
  final double x;
  final double y;
  final double width;
  final double height;
  final String? boundCell;
  final Map<String, dynamic> properties;

  const UIComponent({
    required this.id,
    required this.type,
    required this.label,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    this.boundCell,
    this.properties = const {},
  });

  UIComponent copyWith({
    String? id,
    ComponentType? type,
    String? label,
    double? x,
    double? y,
    double? width,
    double? height,
    String? boundCell,
    Map<String, dynamic>? properties,
  }) {
    return UIComponent(
      id: id ?? this.id,
      type: type ?? this.type,
      label: label ?? this.label,
      x: x ?? this.x,
      y: y ?? this.y,
      width: width ?? this.width,
      height: height ?? this.height,
      boundCell: boundCell ?? this.boundCell,
      properties: properties ?? this.properties,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.name,
      'label': label,
      'x': x,
      'y': y,
      'width': width,
      'height': height,
      'boundCell': boundCell,
      'properties': properties,
    };
  }

  factory UIComponent.fromMap(Map<String, dynamic> map) {
    return UIComponent(
      id: map['id'] ?? '',
      type: ComponentType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => ComponentType.label,
      ),
      label: map['label'] ?? '',
      x: map['x']?.toDouble() ?? 0.0,
      y: map['y']?.toDouble() ?? 0.0,
      width: map['width']?.toDouble() ?? 100.0,
      height: map['height']?.toDouble() ?? 40.0,
      boundCell: map['boundCell'],
      properties: Map<String, dynamic>.from(map['properties'] ?? {}),
    );
  }

  String toJson() => json.encode(toMap());
  factory UIComponent.fromJson(String source) => UIComponent.fromMap(json.decode(source));
}

class ExcelAppTool {
  final String id;
  final String name;
  final String description;
  final SecurityType securityType;
  final String? securityPin;
  final ExcelSpreadsheet spreadsheet;
  final List<UIComponent> uiComponents;
  final DateTime createdAt;
  final DateTime lastModified;
  final String? thumbnailPath;

  const ExcelAppTool({
    required this.id,
    required this.name,
    this.description = '',
    this.securityType = SecurityType.none,
    this.securityPin,
    required this.spreadsheet,
    this.uiComponents = const [],
    required this.createdAt,
    required this.lastModified,
    this.thumbnailPath,
  });

  ExcelAppTool copyWith({
    String? id,
    String? name,
    String? description,
    SecurityType? securityType,
    String? securityPin,
    ExcelSpreadsheet? spreadsheet,
    List<UIComponent>? uiComponents,
    DateTime? createdAt,
    DateTime? lastModified,
    String? thumbnailPath,
  }) {
    return ExcelAppTool(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      securityType: securityType ?? this.securityType,
      securityPin: securityPin ?? this.securityPin,
      spreadsheet: spreadsheet ?? this.spreadsheet,
      uiComponents: uiComponents ?? this.uiComponents,
      createdAt: createdAt ?? this.createdAt,
      lastModified: lastModified ?? this.lastModified,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'securityType': securityType.name,
      'securityPin': securityPin,
      'spreadsheet': spreadsheet.toMap(),
      'uiComponents': uiComponents.map((x) => x.toMap()).toList(),
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastModified': lastModified.millisecondsSinceEpoch,
      'thumbnailPath': thumbnailPath,
    };
  }

  factory ExcelAppTool.fromMap(Map<String, dynamic> map) {
    return ExcelAppTool(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      securityType: SecurityType.values.firstWhere(
        (e) => e.name == map['securityType'],
        orElse: () => SecurityType.none,
      ),
      securityPin: map['securityPin'],
      spreadsheet: ExcelSpreadsheet.fromMap(map['spreadsheet'] ?? {}),
      uiComponents: List<UIComponent>.from(
        (map['uiComponents'] ?? []).map((x) => UIComponent.fromMap(x)),
      ),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      lastModified: DateTime.fromMillisecondsSinceEpoch(map['lastModified'] ?? 0),
      thumbnailPath: map['thumbnailPath'],
    );
  }

  String toJson() => json.encode(toMap());
  factory ExcelAppTool.fromJson(String source) => ExcelAppTool.fromMap(json.decode(source));
}
