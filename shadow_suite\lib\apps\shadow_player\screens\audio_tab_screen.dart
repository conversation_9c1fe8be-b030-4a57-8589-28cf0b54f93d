import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/media_providers.dart';
import '../models/media_models.dart';
import '../widgets/audio_player_sheet.dart';

/// Audio tab screen showing music library
class AudioTabScreen extends ConsumerStatefulWidget {
  const AudioTabScreen({super.key});

  @override
  ConsumerState<AudioTabScreen> createState() => _AudioTabScreenState();
}

class _AudioTabScreenState extends ConsumerState<AudioTabScreen> {
  ViewMode _viewMode = ViewMode.list;
  String _sortBy = 'title';
  bool _ascending = true;

  @override
  Widget build(BuildContext context) {
    final audioFiles = ref.watch(audioFilesProvider);
    final isLoading = ref.watch(mediaLibraryLoadingProvider);

    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (audioFiles.isEmpty) {
      return _buildEmptyState();
    }

    final sortedFiles = _sortAudioFiles(audioFiles);

    return Column(
      children: [
        _buildToolbar(),
        Expanded(child: _buildAudioList(sortedFiles)),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.music_note, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          const Text(
            'No audio files found',
            style: TextStyle(fontSize: 18, color: Colors.grey),
          ),
          const SizedBox(height: 8),
          const Text(
            'Add some music to your library',
            style: TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _scanForAudio(),
            icon: const Icon(Icons.refresh),
            label: const Text('Scan for Music'),
          ),
        ],
      ),
    );
  }

  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        children: [
          // View mode toggle
          ToggleButtons(
            isSelected: [
              _viewMode == ViewMode.list,
              _viewMode == ViewMode.grid,
            ],
            onPressed: (index) {
              setState(() {
                _viewMode = index == 0 ? ViewMode.list : ViewMode.grid;
              });
            },
            children: const [Icon(Icons.list), Icon(Icons.grid_view)],
          ),
          const SizedBox(width: 16),

          // Sort options
          Expanded(
            child: Row(
              children: [
                const Text('Sort by: '),
                DropdownButton<String>(
                  value: _sortBy,
                  onChanged: (value) {
                    setState(() {
                      _sortBy = value!;
                    });
                  },
                  items: const [
                    DropdownMenuItem(value: 'title', child: Text('Title')),
                    DropdownMenuItem(value: 'artist', child: Text('Artist')),
                    DropdownMenuItem(value: 'album', child: Text('Album')),
                    DropdownMenuItem(
                      value: 'duration',
                      child: Text('Duration'),
                    ),
                    DropdownMenuItem(
                      value: 'dateAdded',
                      child: Text('Date Added'),
                    ),
                  ],
                ),
                IconButton(
                  icon: Icon(
                    _ascending ? Icons.arrow_upward : Icons.arrow_downward,
                  ),
                  onPressed: () {
                    setState(() {
                      _ascending = !_ascending;
                    });
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAudioList(List<MediaFile> audioFiles) {
    if (_viewMode == ViewMode.grid) {
      return GridView.builder(
        padding: const EdgeInsets.all(8.0),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          childAspectRatio: 1.0,
        ),
        itemCount: audioFiles.length,
        itemBuilder: (context, index) {
          return _buildAudioGridItem(audioFiles[index]);
        },
      );
    } else {
      return ListView.builder(
        itemCount: audioFiles.length,
        itemBuilder: (context, index) {
          return _buildAudioListItem(audioFiles[index]);
        },
      );
    }
  }

  Widget _buildAudioListItem(MediaFile audioFile) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Theme.of(context).primaryColor,
        child: const Icon(Icons.music_note, color: Colors.white),
      ),
      title: Text(
        audioFile.metadata.title ?? audioFile.displayName,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Text(
        '${audioFile.metadata.artist ?? 'Unknown Artist'} • ${audioFile.metadata.album ?? 'Unknown Album'}',
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(_formatDuration(audioFile.duration)),
          PopupMenuButton<String>(
            onSelected: (action) => _handleAudioAction(action, audioFile),
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'play', child: Text('Play')),
              const PopupMenuItem(
                value: 'addToPlaylist',
                child: Text('Add to Playlist'),
              ),
              const PopupMenuItem(value: 'info', child: Text('Info')),
              const PopupMenuItem(value: 'delete', child: Text('Delete')),
            ],
          ),
        ],
      ),
      onTap: () => _playAudio(audioFile),
    );
  }

  Widget _buildAudioGridItem(MediaFile audioFile) {
    return Card(
      child: InkWell(
        onTap: () => _playAudio(audioFile),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(4),
                  ),
                ),
                child: Icon(
                  Icons.music_note,
                  size: 48,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    audioFile.metadata.title ?? audioFile.displayName,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    audioFile.metadata.artist ?? 'Unknown Artist',
                    style: TextStyle(color: Colors.grey[600]),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    _formatDuration(audioFile.duration),
                    style: TextStyle(color: Colors.grey[500], fontSize: 12),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<MediaFile> _sortAudioFiles(List<MediaFile> files) {
    final sortedFiles = [...files];

    sortedFiles.sort((a, b) {
      int comparison = 0;

      switch (_sortBy) {
        case 'title':
          comparison = (a.metadata.title ?? a.displayName).compareTo(
            b.metadata.title ?? b.displayName,
          );
          break;
        case 'artist':
          comparison = (a.metadata.artist ?? '').compareTo(
            b.metadata.artist ?? '',
          );
          break;
        case 'album':
          comparison = (a.metadata.album ?? '').compareTo(
            b.metadata.album ?? '',
          );
          break;
        case 'duration':
          comparison = (a.duration?.inSeconds ?? 0).compareTo(
            b.duration?.inSeconds ?? 0,
          );
          break;
        case 'dateAdded':
          comparison = a.dateAdded.compareTo(b.dateAdded);
          break;
      }

      return _ascending ? comparison : -comparison;
    });

    return sortedFiles;
  }

  String _formatDuration(Duration? duration) {
    if (duration == null) return '--:--';

    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  void _scanForAudio() {
    ref.read(mediaLibraryProvider.notifier).scanForMedia();
  }

  void _playAudio(MediaFile audioFile) {
    // Set the current playing audio
    ref.read(currentPlayingMediaProvider.notifier).state = audioFile;

    // Show audio player
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AudioPlayerSheet(audioFile: audioFile),
    );
  }

  void _handleAudioAction(String action, MediaFile audioFile) {
    switch (action) {
      case 'play':
        _playAudio(audioFile);
        break;
      case 'addToPlaylist':
        // TODO: Implement add to playlist
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Add to playlist coming soon')),
        );
        break;
      case 'info':
        _showAudioInfo(audioFile);
        break;
      case 'delete':
        _deleteAudio(audioFile);
        break;
    }
  }

  void _showAudioInfo(MediaFile audioFile) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(audioFile.displayName),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Title: ${audioFile.metadata.title ?? 'Unknown'}'),
            Text('Artist: ${audioFile.metadata.artist ?? 'Unknown'}'),
            Text('Album: ${audioFile.metadata.album ?? 'Unknown'}'),
            Text('Duration: ${_formatDuration(audioFile.duration)}'),
            Text('File Size: ${_formatFileSize(audioFile.size)}'),
            Text('Path: ${audioFile.path}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _deleteAudio(MediaFile audioFile) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Audio'),
        content: Text(
          'Are you sure you want to delete "${audioFile.displayName}"?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ref
                  .read(mediaLibraryProvider.notifier)
                  .removeMediaFile(audioFile.id);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Audio file deleted')),
              );
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
