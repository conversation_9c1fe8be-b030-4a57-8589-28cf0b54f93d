import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/money_flow_models.dart';

class MoneyFlowDatabaseService {
  static const String _accountsKey = 'money_flow_v2_accounts';
  static const String _transactionsKey = 'money_flow_v2_transactions';
  static const String _budgetsKey = 'money_flow_v2_budgets';
  static const String _settingsKey = 'money_flow_v2_settings';

  // Singleton pattern
  static final MoneyFlowDatabaseService _instance = MoneyFlowDatabaseService._internal();
  factory MoneyFlowDatabaseService() => _instance;
  MoneyFlowDatabaseService._internal();

  SharedPreferences? _prefs;

  Future<void> initialize() async {
    _prefs ??= await SharedPreferences.getInstance();
    await _initializeSampleData();
  }

  Future<void> _initializeSampleData() async {
    final accounts = await getAllAccounts();
    if (accounts.isEmpty) {
      await _createSampleData();
    }
  }

  Future<void> _createSampleData() async {
    final now = DateTime.now();
    
    // Sample accounts
    final sampleAccounts = [
      MoneyAccount(
        id: _generateId(),
        name: 'Main Checking',
        type: AccountType.checking,
        bankName: 'Chase Bank',
        accountNumber: '****1234',
        balance: 2500.00,
        color: '#2196F3',
        createdAt: now,
        updatedAt: now,
      ),
      MoneyAccount(
        id: _generateId(),
        name: 'Savings Account',
        type: AccountType.savings,
        bankName: 'Chase Bank',
        accountNumber: '****5678',
        balance: 15000.00,
        color: '#4CAF50',
        createdAt: now,
        updatedAt: now,
      ),
      MoneyAccount(
        id: _generateId(),
        name: 'Credit Card',
        type: AccountType.credit,
        bankName: 'Capital One',
        accountNumber: '****9012',
        balance: -850.00,
        color: '#FF5722',
        createdAt: now,
        updatedAt: now,
      ),
    ];

    for (final account in sampleAccounts) {
      await saveAccount(account);
    }

    // Sample transactions
    final sampleTransactions = [
      MoneyTransactionV2(
        id: _generateId(),
        accountId: sampleAccounts[0].id,
        type: TransactionType.income,
        amount: 3500.00,
        category: 'Salary',
        description: 'Monthly Salary',
        date: now.subtract(const Duration(days: 5)),
        createdAt: now,
        updatedAt: now,
      ),
      MoneyTransactionV2(
        id: _generateId(),
        accountId: sampleAccounts[0].id,
        type: TransactionType.expense,
        amount: 120.50,
        category: 'Food & Dining',
        description: 'Grocery Shopping',
        date: now.subtract(const Duration(days: 2)),
        createdAt: now,
        updatedAt: now,
      ),
      MoneyTransactionV2(
        id: _generateId(),
        accountId: sampleAccounts[0].id,
        type: TransactionType.expense,
        amount: 45.00,
        category: 'Transportation',
        description: 'Gas Station',
        date: now.subtract(const Duration(days: 1)),
        createdAt: now,
        updatedAt: now,
      ),
    ];

    for (final transaction in sampleTransactions) {
      await saveTransaction(transaction);
    }

    // Sample budgets
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);
    
    final sampleBudgets = [
      MoneyBudget(
        id: _generateId(),
        name: 'Monthly Food Budget',
        category: 'Food & Dining',
        amount: 500.00,
        spent: 120.50,
        period: BudgetPeriod.monthly,
        startDate: startOfMonth,
        endDate: endOfMonth,
        color: '#FF9800',
        createdAt: now,
        updatedAt: now,
      ),
      MoneyBudget(
        id: _generateId(),
        name: 'Transportation Budget',
        category: 'Transportation',
        amount: 200.00,
        spent: 45.00,
        period: BudgetPeriod.monthly,
        startDate: startOfMonth,
        endDate: endOfMonth,
        color: '#9C27B0',
        createdAt: now,
        updatedAt: now,
      ),
    ];

    for (final budget in sampleBudgets) {
      await saveBudget(budget);
    }
  }

  // Account CRUD Operations
  Future<List<MoneyAccount>> getAllAccounts() async {
    await initialize();
    final accountsJson = _prefs?.getStringList(_accountsKey) ?? [];
    return accountsJson.map((json) => MoneyAccount.fromJson(json)).toList();
  }

  Future<MoneyAccount?> getAccountById(String id) async {
    final accounts = await getAllAccounts();
    try {
      return accounts.firstWhere((account) => account.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<bool> saveAccount(MoneyAccount account) async {
    try {
      await initialize();
      final accounts = await getAllAccounts();
      
      // Remove existing account with same ID
      accounts.removeWhere((a) => a.id == account.id);
      
      // Add updated account
      accounts.add(account);
      
      // Save to preferences
      final accountsJson = accounts.map((a) => a.toJson()).toList();
      await _prefs?.setStringList(_accountsKey, accountsJson);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> deleteAccount(String id) async {
    try {
      await initialize();
      final accounts = await getAllAccounts();
      
      // Remove account with matching ID
      accounts.removeWhere((a) => a.id == id);
      
      // Save updated list
      final accountsJson = accounts.map((a) => a.toJson()).toList();
      await _prefs?.setStringList(_accountsKey, accountsJson);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  // Transaction CRUD Operations
  Future<List<MoneyTransactionV2>> getAllTransactions() async {
    await initialize();
    final transactionsJson = _prefs?.getStringList(_transactionsKey) ?? [];
    return transactionsJson.map((json) => MoneyTransactionV2.fromJson(json)).toList();
  }

  Future<List<MoneyTransactionV2>> getTransactionsByAccount(String accountId) async {
    final transactions = await getAllTransactions();
    return transactions.where((t) => t.accountId == accountId || t.toAccountId == accountId).toList();
  }

  Future<List<MoneyTransactionV2>> getRecentTransactions({int limit = 10}) async {
    final transactions = await getAllTransactions();
    transactions.sort((a, b) => b.date.compareTo(a.date));
    return transactions.take(limit).toList();
  }

  Future<bool> saveTransaction(MoneyTransactionV2 transaction) async {
    try {
      await initialize();
      final transactions = await getAllTransactions();
      
      // Remove existing transaction with same ID
      transactions.removeWhere((t) => t.id == transaction.id);
      
      // Add updated transaction
      transactions.add(transaction);
      
      // Save to preferences
      final transactionsJson = transactions.map((t) => t.toJson()).toList();
      await _prefs?.setStringList(_transactionsKey, transactionsJson);
      
      // Update account balances
      await _updateAccountBalances(transaction);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> deleteTransaction(String id) async {
    try {
      await initialize();
      final transactions = await getAllTransactions();
      
      // Remove transaction with matching ID
      transactions.removeWhere((t) => t.id == id);
      
      // Save updated list
      final transactionsJson = transactions.map((t) => t.toJson()).toList();
      await _prefs?.setStringList(_transactionsKey, transactionsJson);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  // Budget CRUD Operations
  Future<List<MoneyBudget>> getAllBudgets() async {
    await initialize();
    final budgetsJson = _prefs?.getStringList(_budgetsKey) ?? [];
    return budgetsJson.map((json) => MoneyBudget.fromJson(json)).toList();
  }

  Future<bool> saveBudget(MoneyBudget budget) async {
    try {
      await initialize();
      final budgets = await getAllBudgets();
      
      // Remove existing budget with same ID
      budgets.removeWhere((b) => b.id == budget.id);
      
      // Add updated budget
      budgets.add(budget);
      
      // Save to preferences
      final budgetsJson = budgets.map((b) => b.toJson()).toList();
      await _prefs?.setStringList(_budgetsKey, budgetsJson);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> deleteBudget(String id) async {
    try {
      await initialize();
      final budgets = await getAllBudgets();
      
      // Remove budget with matching ID
      budgets.removeWhere((b) => b.id == id);
      
      // Save updated list
      final budgetsJson = budgets.map((b) => b.toJson()).toList();
      await _prefs?.setStringList(_budgetsKey, budgetsJson);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  // Statistics and Analytics
  Future<Map<String, dynamic>> getStatistics() async {
    final accounts = await getAllAccounts();
    final transactions = await getAllTransactions();
    final budgets = await getAllBudgets();
    
    double totalBalance = 0.0;
    double totalAssets = 0.0;
    double totalLiabilities = 0.0;
    
    for (final account in accounts) {
      totalBalance += account.balance;
      if (account.balance >= 0) {
        totalAssets += account.balance;
      } else {
        totalLiabilities += account.balance.abs();
      }
    }
    
    return {
      'totalAccounts': accounts.length,
      'totalTransactions': transactions.length,
      'totalBudgets': budgets.length,
      'totalBalance': totalBalance,
      'totalAssets': totalAssets,
      'totalLiabilities': totalLiabilities,
    };
  }

  Future<Map<String, double>> getMonthlySpending() async {
    final transactions = await getAllTransactions();
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);
    
    final monthlyTransactions = transactions.where((t) =>
        t.type == TransactionType.expense &&
        t.date.isAfter(startOfMonth) &&
        t.date.isBefore(endOfMonth)).toList();
    
    final Map<String, double> spending = {};
    for (final transaction in monthlyTransactions) {
      spending[transaction.category] = (spending[transaction.category] ?? 0) + transaction.amount;
    }
    
    return spending;
  }

  Future<void> _updateAccountBalances(MoneyTransactionV2 transaction) async {
    // This is a simplified version - in a real app, you'd want more sophisticated balance tracking
    // For now, we'll just recalculate balances based on all transactions
  }

  String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  // Settings management
  Future<Map<String, dynamic>> getSettings() async {
    await initialize();
    final settingsJson = _prefs?.getString(_settingsKey);
    if (settingsJson == null) return _getDefaultSettings();
    
    try {
      return jsonDecode(settingsJson);
    } catch (e) {
      return _getDefaultSettings();
    }
  }

  Future<bool> saveSettings(Map<String, dynamic> settings) async {
    try {
      await initialize();
      await _prefs?.setString(_settingsKey, jsonEncode(settings));
      return true;
    } catch (e) {
      return false;
    }
  }

  Map<String, dynamic> _getDefaultSettings() {
    return {
      'autoSave': true,
      'autoSaveInterval': 30,
      'defaultCurrency': 'USD',
      'hideBalances': false,
      'requirePinForTransactions': false,
      'showTransactionCategories': true,
    };
  }
}
