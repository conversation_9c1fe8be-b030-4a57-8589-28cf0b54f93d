import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:path/path.dart' as path;
import '../models/file_manager_models.dart';
import '../../../core/database/database_service.dart';
import '../../../core/services/error_handler.dart' as error_handler;
import '../../../core/services/cross_platform_storage_service.dart';

class FileManagerService {
  static String _currentDirectory = '/storage/emulated/0';
  static final List<FileOperation> _operations = [];
  static final List<FileSystemItem> _currentItems = [];
  static final List<String> _navigationHistory = [];
  static int _historyIndex = -1;

  static final StreamController<FileManagerChangeEvent> _changeController =
      StreamController<FileManagerChangeEvent>.broadcast();

  // Initialize file manager service
  static Future<void> initialize() async {
    try {
      // Initialize cross-platform storage
      await CrossPlatformStorageService.initialize();

      // Check and request permissions
      final permissions = await CrossPlatformStorageService.checkPermissions();
      if (!permissions.hasAllPermissions) {
        await CrossPlatformStorageService.requestPermissions();
      }

      // Set appropriate default directory based on platform
      if (Platform.isWindows) {
        _currentDirectory = Platform.environment['USERPROFILE'] ?? 'C:\\';
      } else {
        _currentDirectory = '/storage/emulated/0';
      }

      await _loadOperationHistory();
      await navigateToDirectory(_currentDirectory);
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Initialize file manager',
      );
    }
  }

  // STORAGE ACCESS METHODS

  /// Get available storage locations
  static Future<List<StorageLocation>> getStorageLocations() async {
    try {
      return await CrossPlatformStorageService.getStorageLocations();
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Get storage locations',
      );
      return [];
    }
  }

  /// Get network drives (Windows only)
  static Future<List<NetworkDrive>> getNetworkDrives() async {
    try {
      return await CrossPlatformStorageService.getNetworkDrives();
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Get network drives',
      );
      return [];
    }
  }

  /// Check if path is accessible
  static Future<bool> isPathAccessible(String path) async {
    try {
      return await CrossPlatformStorageService.isPathAccessible(path);
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Check path accessibility: $path',
      );
      return false;
    }
  }

  // CORE FILE OPERATIONS

  // Navigate to directory
  static Future<void> navigateToDirectory(String directoryPath) async {
    try {
      final directory = Directory(directoryPath);
      if (!await directory.exists()) {
        throw Exception('Directory does not exist: $directoryPath');
      }

      _currentDirectory = directoryPath;
      await _loadDirectoryContents();

      // Update navigation history
      if (_historyIndex == -1 ||
          _navigationHistory[_historyIndex] != directoryPath) {
        // Remove any forward history
        if (_historyIndex < _navigationHistory.length - 1) {
          _navigationHistory.removeRange(
            _historyIndex + 1,
            _navigationHistory.length,
          );
        }
        _navigationHistory.add(directoryPath);
        _historyIndex = _navigationHistory.length - 1;
      }

      _notifyChange(
        FileManagerChangeEvent(
          type: FileManagerChangeType.directoryChanged,
          path: directoryPath,
          timestamp: DateTime.now(),
        ),
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Navigate to directory: $directoryPath',
      );
      rethrow;
    }
  }

  // Create new folder
  static Future<FileSystemItem> createFolder(String name) async {
    try {
      final folderPath = path.join(_currentDirectory, name);
      final directory = Directory(folderPath);

      if (await directory.exists()) {
        throw Exception('Folder already exists: $name');
      }

      await directory.create(recursive: true);
      final item = FileSystemItem.fromFileSystemEntity(directory);

      await _refreshCurrentDirectory();

      _notifyChange(
        FileManagerChangeEvent(
          type: FileManagerChangeType.fileCreated,
          path: folderPath,
          item: item,
          timestamp: DateTime.now(),
        ),
      );

      return item;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Create folder: $name',
      );
      rethrow;
    }
  }

  // Create new file
  static Future<FileSystemItem> createFile(
    String name, [
    Uint8List? content,
  ]) async {
    try {
      final filePath = path.join(_currentDirectory, name);
      final file = File(filePath);

      if (await file.exists()) {
        throw Exception('File already exists: $name');
      }

      await file.writeAsBytes(content ?? Uint8List(0));
      final item = FileSystemItem.fromFileSystemEntity(file);

      await _refreshCurrentDirectory();

      _notifyChange(
        FileManagerChangeEvent(
          type: FileManagerChangeType.fileCreated,
          path: filePath,
          item: item,
          timestamp: DateTime.now(),
        ),
      );

      return item;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Create file: $name',
      );
      rethrow;
    }
  }

  // Delete file or folder
  static Future<void> deleteItem(String itemPath) async {
    try {
      final entity = await FileSystemEntity.type(itemPath);

      if (entity == FileSystemEntityType.notFound) {
        throw Exception('Item not found: $itemPath');
      }

      final operation = FileOperation(
        id: 'delete_${DateTime.now().millisecondsSinceEpoch}',
        type: FileOperationType.delete,
        sourcePath: itemPath,
        status: FileOperationStatus.inProgress,
        progress: 0.0,
        totalBytes: 0,
        processedBytes: 0,
        startTime: DateTime.now(),
      );

      _operations.add(operation);
      await _saveOperation(operation);

      if (entity == FileSystemEntityType.directory) {
        await Directory(itemPath).delete(recursive: true);
      } else {
        await File(itemPath).delete();
      }

      await _updateOperationStatus(
        operation.id,
        FileOperationStatus.completed,
        100.0,
      );
      await _refreshCurrentDirectory();

      _notifyChange(
        FileManagerChangeEvent(
          type: FileManagerChangeType.fileDeleted,
          path: itemPath,
          timestamp: DateTime.now(),
        ),
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Delete item: $itemPath',
      );
      rethrow;
    }
  }

  // Rename file or folder
  static Future<FileSystemItem> renameItem(
    String oldPath,
    String newName,
  ) async {
    try {
      final parentDir = path.dirname(oldPath);
      final newPath = path.join(parentDir, newName);

      final entity = await FileSystemEntity.type(oldPath);
      if (entity == FileSystemEntityType.notFound) {
        throw Exception('Item not found: $oldPath');
      }

      if (entity == FileSystemEntityType.directory) {
        await Directory(oldPath).rename(newPath);
      } else {
        await File(oldPath).rename(newPath);
      }

      final newItem = FileSystemItem.fromFileSystemEntity(
        entity == FileSystemEntityType.directory
            ? Directory(newPath)
            : File(newPath),
      );

      await _refreshCurrentDirectory();

      _notifyChange(
        FileManagerChangeEvent(
          type: FileManagerChangeType.fileModified,
          path: newPath,
          item: newItem,
          timestamp: DateTime.now(),
        ),
      );

      return newItem;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Rename item: $oldPath to $newName',
      );
      rethrow;
    }
  }

  // Copy file or folder
  static Future<void> copyItem(
    String sourcePath,
    String destinationPath,
  ) async {
    try {
      final operation = FileOperation(
        id: 'copy_${DateTime.now().millisecondsSinceEpoch}',
        type: FileOperationType.copy,
        sourcePath: sourcePath,
        destinationPath: destinationPath,
        status: FileOperationStatus.inProgress,
        progress: 0.0,
        totalBytes: await _calculateSize(sourcePath),
        processedBytes: 0,
        startTime: DateTime.now(),
      );

      _operations.add(operation);
      await _saveOperation(operation);

      await _performCopyOperation(sourcePath, destinationPath, operation.id);

      await _updateOperationStatus(
        operation.id,
        FileOperationStatus.completed,
        100.0,
      );
      await _refreshCurrentDirectory();

      _notifyChange(
        FileManagerChangeEvent(
          type: FileManagerChangeType.operationCompleted,
          path: destinationPath,
          timestamp: DateTime.now(),
        ),
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Copy item: $sourcePath to $destinationPath',
      );
      rethrow;
    }
  }

  // Move file or folder
  static Future<void> moveItem(
    String sourcePath,
    String destinationPath,
  ) async {
    try {
      final operation = FileOperation(
        id: 'move_${DateTime.now().millisecondsSinceEpoch}',
        type: FileOperationType.move,
        sourcePath: sourcePath,
        destinationPath: destinationPath,
        status: FileOperationStatus.inProgress,
        progress: 0.0,
        totalBytes: await _calculateSize(sourcePath),
        processedBytes: 0,
        startTime: DateTime.now(),
      );

      _operations.add(operation);
      await _saveOperation(operation);

      // Try rename first (faster for same filesystem)
      try {
        final entity = await FileSystemEntity.type(sourcePath);
        if (entity == FileSystemEntityType.directory) {
          await Directory(sourcePath).rename(destinationPath);
        } else {
          await File(sourcePath).rename(destinationPath);
        }
      } catch (e) {
        // If rename fails, fall back to copy + delete
        await _performCopyOperation(sourcePath, destinationPath, operation.id);
        await deleteItem(sourcePath);
      }

      await _updateOperationStatus(
        operation.id,
        FileOperationStatus.completed,
        100.0,
      );
      await _refreshCurrentDirectory();

      _notifyChange(
        FileManagerChangeEvent(
          type: FileManagerChangeType.operationCompleted,
          path: destinationPath,
          timestamp: DateTime.now(),
        ),
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Move item: $sourcePath to $destinationPath',
      );
      rethrow;
    }
  }

  // NAVIGATION METHODS

  // Go back in navigation history
  static Future<void> navigateBack() async {
    if (canNavigateBack()) {
      _historyIndex--;
      await navigateToDirectory(_navigationHistory[_historyIndex]);
    }
  }

  // Go forward in navigation history
  static Future<void> navigateForward() async {
    if (canNavigateForward()) {
      _historyIndex++;
      await navigateToDirectory(_navigationHistory[_historyIndex]);
    }
  }

  // Go to parent directory
  static Future<void> navigateToParent() async {
    final parentPath = path.dirname(_currentDirectory);
    if (parentPath != _currentDirectory) {
      await navigateToDirectory(parentPath);
    }
  }

  // Go to home directory
  static Future<void> navigateToHome() async {
    await navigateToDirectory('/storage/emulated/0');
  }

  // Go to root directory
  static Future<void> navigateToRoot() async {
    await navigateToDirectory('/');
  }

  // GETTER METHODS

  // Get current directory path
  static String getCurrentDirectory() {
    return _currentDirectory;
  }

  // Get current directory items
  static List<FileSystemItem> getCurrentItems() {
    return List.unmodifiable(_currentItems);
  }

  // Get operations list
  static List<FileOperation> getOperations() {
    return List.unmodifiable(_operations);
  }

  // Alias for createFolder method
  static Future<FileSystemItem> createDirectory(String name) async {
    return await createFolder(name);
  }

  // Refresh current directory
  static Future<void> refreshCurrentDirectory() async {
    final currentPath = getCurrentDirectory();
    await navigateToDirectory(currentPath);
  }

  // HELPER METHODS

  static Future<void> _loadDirectoryContents() async {
    try {
      final directory = Directory(_currentDirectory);
      final entities = await directory.list().toList();

      _currentItems.clear();
      for (final entity in entities) {
        try {
          final item = FileSystemItem.fromFileSystemEntity(entity);
          _currentItems.add(item);
        } catch (e) {
          // Skip items that can't be accessed
          continue;
        }
      }

      // Sort items: directories first, then files, alphabetically
      _currentItems.sort((a, b) {
        if (a.type == FileSystemItemType.directory &&
            b.type != FileSystemItemType.directory) {
          return -1;
        } else if (a.type != FileSystemItemType.directory &&
            b.type == FileSystemItemType.directory) {
          return 1;
        }
        return a.name.toLowerCase().compareTo(b.name.toLowerCase());
      });
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Load directory contents',
      );
    }
  }

  static Future<void> _refreshCurrentDirectory() async {
    await _loadDirectoryContents();
  }

  static Future<int> _calculateSize(String itemPath) async {
    try {
      final entity = await FileSystemEntity.type(itemPath);

      if (entity == FileSystemEntityType.file) {
        final file = File(itemPath);
        return await file.length();
      } else if (entity == FileSystemEntityType.directory) {
        int totalSize = 0;
        final directory = Directory(itemPath);
        await for (final entity in directory.list(recursive: true)) {
          if (entity is File) {
            try {
              totalSize += await entity.length();
            } catch (e) {
              // Skip files that can't be accessed
            }
          }
        }
        return totalSize;
      }

      return 0;
    } catch (error) {
      return 0;
    }
  }

  static Future<void> _performCopyOperation(
    String sourcePath,
    String destinationPath,
    String operationId,
  ) async {
    final entity = await FileSystemEntity.type(sourcePath);

    if (entity == FileSystemEntityType.file) {
      await _copyFile(sourcePath, destinationPath, operationId);
    } else if (entity == FileSystemEntityType.directory) {
      await _copyDirectory(sourcePath, destinationPath, operationId);
    }
  }

  static Future<void> _copyFile(
    String sourcePath,
    String destinationPath,
    String operationId,
  ) async {
    final sourceFile = File(sourcePath);
    final destinationFile = File(destinationPath);

    // Ensure destination directory exists
    await destinationFile.parent.create(recursive: true);

    // Copy file with progress tracking
    final sourceStream = sourceFile.openRead();
    final destinationSink = destinationFile.openWrite();

    int copiedBytes = 0;
    final totalBytes = await sourceFile.length();

    await for (final chunk in sourceStream) {
      destinationSink.add(chunk);
      copiedBytes += chunk.length;

      final progress = totalBytes > 0
          ? (copiedBytes / totalBytes) * 100
          : 100.0;
      await _updateOperationProgress(operationId, progress, copiedBytes);
    }

    await destinationSink.close();
  }

  static Future<void> _copyDirectory(
    String sourcePath,
    String destinationPath,
    String operationId,
  ) async {
    final sourceDirectory = Directory(sourcePath);
    final destinationDirectory = Directory(destinationPath);

    await destinationDirectory.create(recursive: true);

    await for (final entity in sourceDirectory.list(recursive: false)) {
      final relativePath = path.relative(entity.path, from: sourcePath);
      final newPath = path.join(destinationPath, relativePath);

      if (entity is File) {
        await _copyFile(entity.path, newPath, operationId);
      } else if (entity is Directory) {
        await _copyDirectory(entity.path, newPath, operationId);
      }
    }
  }

  static Future<void> _saveOperation(FileOperation operation) async {
    try {
      await DatabaseService.safeInsert('file_operations', operation.toJson());
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Save file operation',
      );
    }
  }

  static Future<void> _updateOperationStatus(
    String operationId,
    FileOperationStatus status,
    double progress,
  ) async {
    try {
      final operationIndex = _operations.indexWhere(
        (op) => op.id == operationId,
      );
      if (operationIndex != -1) {
        final operation = _operations[operationIndex];
        final updatedOperation = FileOperation(
          id: operation.id,
          type: operation.type,
          sourcePath: operation.sourcePath,
          destinationPath: operation.destinationPath,
          status: status,
          progress: progress,
          totalBytes: operation.totalBytes,
          processedBytes: operation.processedBytes,
          startTime: operation.startTime,
          endTime: status == FileOperationStatus.completed
              ? DateTime.now()
              : null,
        );

        _operations[operationIndex] = updatedOperation;

        await DatabaseService.safeUpdate(
          'file_operations',
          updatedOperation.toJson(),
          where: 'id = ?',
          whereArgs: [operationId],
        );
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Update operation status',
      );
    }
  }

  static Future<void> _updateOperationProgress(
    String operationId,
    double progress,
    int processedBytes,
  ) async {
    try {
      final operationIndex = _operations.indexWhere(
        (op) => op.id == operationId,
      );
      if (operationIndex != -1) {
        final operation = _operations[operationIndex];
        final updatedOperation = FileOperation(
          id: operation.id,
          type: operation.type,
          sourcePath: operation.sourcePath,
          destinationPath: operation.destinationPath,
          status: operation.status,
          progress: progress,
          totalBytes: operation.totalBytes,
          processedBytes: processedBytes,
          startTime: operation.startTime,
          endTime: operation.endTime,
        );

        _operations[operationIndex] = updatedOperation;
      }
    } catch (error) {
      // Ignore progress update errors
    }
  }

  static Future<void> _loadOperationHistory() async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM file_operations ORDER BY start_time DESC LIMIT 100',
      );
      _operations.clear();
      for (final row in results) {
        _operations.add(FileOperation.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Load operation history',
      );
    }
  }

  static void _notifyChange(FileManagerChangeEvent event) {
    _changeController.add(event);
  }

  // GETTERS
  static String get currentDirectory => _currentDirectory;
  static List<FileSystemItem> get currentItems =>
      List.unmodifiable(_currentItems);
  static List<FileOperation> get operations => List.unmodifiable(_operations);
  static bool canNavigateBack() => _historyIndex > 0;
  static bool canNavigateForward() =>
      _historyIndex < _navigationHistory.length - 1;
  static Stream<FileManagerChangeEvent> get changeStream =>
      _changeController.stream;

  // Text file operations
  static Future<String> readTextFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('File not found: $filePath');
      }

      return await file.readAsString();
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Read text file: $filePath',
      );
      rethrow;
    }
  }

  static Future<void> writeTextFile(String filePath, String content) async {
    try {
      final file = File(filePath);
      await file.writeAsString(content);

      // Record operation
      final operation = FileOperation(
        id: 'write_${DateTime.now().millisecondsSinceEpoch}',
        type: FileOperationType
            .copy, // Using copy as closest equivalent to create
        sourcePath: filePath,
        status: FileOperationStatus.completed,
        progress: 1.0,
        totalBytes: content.length,
        processedBytes: content.length,
        startTime: DateTime.now(),
        endTime: DateTime.now(),
      );
      _operations.add(operation);

      // Refresh current directory if the file is in current directory
      if (path.dirname(filePath) == _currentDirectory) {
        await refreshCurrentDirectory();
      }

      _notifyChange(
        FileManagerChangeEvent(
          type: FileManagerChangeType.fileCreated,
          path: filePath,
          timestamp: DateTime.now(),
        ),
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Write text file: $filePath',
      );
      rethrow;
    }
  }

  static Future<void> editTextFile(String filePath, String newContent) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('File not found: $filePath');
      }

      await file.writeAsString(newContent);

      // Record operation
      final operation = FileOperation(
        id: 'edit_${DateTime.now().millisecondsSinceEpoch}',
        type: FileOperationType
            .copy, // Using copy as closest equivalent to modify
        sourcePath: filePath,
        status: FileOperationStatus.completed,
        progress: 1.0,
        totalBytes: newContent.length,
        processedBytes: newContent.length,
        startTime: DateTime.now(),
        endTime: DateTime.now(),
      );
      _operations.add(operation);

      // Refresh current directory if the file is in current directory
      if (path.dirname(filePath) == _currentDirectory) {
        await refreshCurrentDirectory();
      }

      _notifyChange(
        FileManagerChangeEvent(
          type: FileManagerChangeType.fileModified,
          path: filePath,
          timestamp: DateTime.now(),
        ),
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Edit text file: $filePath',
      );
      rethrow;
    }
  }

  static Future<FileSystemItem> createTextFile(
    String name,
    String content,
  ) async {
    try {
      final filePath = path.join(_currentDirectory, name);
      await writeTextFile(filePath, content);

      final file = File(filePath);
      final stat = await file.stat();

      return FileSystemItem(
        name: name,
        path: filePath,
        type: FileSystemItemType.file,
        size: stat.size,
        lastModified: stat.modified,
        lastAccessed: stat.accessed,
        isHidden: name.startsWith('.'),
        isReadOnly: false,
        extension: path.extension(filePath),
        mimeType: _getMimeType(filePath),
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Create text file: $name',
      );
      rethrow;
    }
  }

  // Check if file is text-based
  static bool isTextFile(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    const textExtensions = [
      '.txt',
      '.md',
      '.json',
      '.xml',
      '.csv',
      '.log',
      '.yaml',
      '.yml',
      '.dart',
      '.js',
      '.html',
      '.css',
      '.py',
      '.java',
      '.cpp',
      '.c',
      '.h',
      '.php',
      '.rb',
      '.go',
      '.rs',
      '.swift',
      '.kt',
      '.ts',
      '.sql',
      '.sh',
      '.bat',
      '.ps1',
      '.ini',
      '.conf',
      '.cfg',
    ];
    return textExtensions.contains(extension);
  }

  // Get MIME type for file
  static String? _getMimeType(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    const mimeTypes = {
      '.txt': 'text/plain',
      '.md': 'text/markdown',
      '.json': 'application/json',
      '.xml': 'application/xml',
      '.csv': 'text/csv',
      '.html': 'text/html',
      '.css': 'text/css',
      '.js': 'application/javascript',
      '.dart': 'application/dart',
      '.py': 'text/x-python',
      '.java': 'text/x-java-source',
      '.cpp': 'text/x-c++src',
      '.c': 'text/x-csrc',
      '.h': 'text/x-chdr',
      '.php': 'application/x-httpd-php',
      '.rb': 'application/x-ruby',
      '.go': 'text/x-go',
      '.rs': 'text/rust',
      '.swift': 'text/x-swift',
      '.kt': 'text/x-kotlin',
      '.ts': 'application/typescript',
      '.sql': 'application/sql',
      '.sh': 'application/x-sh',
      '.bat': 'application/x-msdos-program',
      '.ps1': 'application/x-powershell',
      '.ini': 'text/plain',
      '.conf': 'text/plain',
      '.cfg': 'text/plain',
      '.log': 'text/plain',
      '.yaml': 'application/x-yaml',
      '.yml': 'application/x-yaml',
    };
    return mimeTypes[extension];
  }

  // DISPOSE
  static void dispose() {
    _currentItems.clear();
    _operations.clear();
    _navigationHistory.clear();
    _changeController.close();
  }
}
