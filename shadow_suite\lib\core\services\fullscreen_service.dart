import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'error_handler.dart' as error_handler;

/// Comprehensive full-screen service for immersive media experiences
class FullScreenService {
  static bool _isFullScreen = false;
  static bool _isInitialized = false;
  static SystemUiMode? _previousSystemUiMode;
  static List<SystemUiOverlay>? _previousOverlays;
  static bool _autoHideEnabled = true;
  static Duration _autoHideDelay = const Duration(seconds: 3);

  /// Initialize full-screen service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Store current system UI state for restoration
      _previousSystemUiMode = SystemUiMode.edgeToEdge;
      _previousOverlays = SystemUiOverlay.values;

      _isInitialized = true;
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.platform,
        context: 'Initialize full-screen service',
      );
    }
  }

  /// Check if currently in full-screen mode
  static bool get isFullScreen => _isFullScreen;

  /// Check if auto-hide is enabled
  static bool get autoHideEnabled => _autoHideEnabled;

  /// Get auto-hide delay
  static Duration get autoHideDelay => _autoHideDelay;

  /// Enter full-screen mode
  static Future<void> enterFullScreen({
    bool hideSystemUI = true,
    bool enableAutoHide = true,
    Duration? autoHideDelay,
  }) async {
    if (_isFullScreen) return;

    try {
      _isFullScreen = true;
      _autoHideEnabled = enableAutoHide;
      if (autoHideDelay != null) {
        _autoHideDelay = autoHideDelay;
      }

      if (hideSystemUI) {
        await _hideSystemUI();
      }

      // Set preferred orientations for full-screen
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);

      // Enable immersive mode on Android
      if (Platform.isAndroid) {
        await SystemChrome.setEnabledSystemUIMode(
          SystemUiMode.immersiveSticky,
          overlays: [],
        );
      }
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.platform,
        context: 'Enter full-screen mode',
      );
    }
  }

  /// Exit full-screen mode
  static Future<void> exitFullScreen() async {
    if (!_isFullScreen) return;

    try {
      _isFullScreen = false;

      await _showSystemUI();

      // Reset preferred orientations
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);

      // Restore normal system UI mode
      await SystemChrome.setEnabledSystemUIMode(
        _previousSystemUiMode ?? SystemUiMode.edgeToEdge,
        overlays: _previousOverlays ?? SystemUiOverlay.values,
      );
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.platform,
        context: 'Exit full-screen mode',
      );
    }
  }

  /// Toggle full-screen mode
  static Future<void> toggleFullScreen({
    bool hideSystemUI = true,
    bool enableAutoHide = true,
    Duration? autoHideDelay,
  }) async {
    if (_isFullScreen) {
      await exitFullScreen();
    } else {
      await enterFullScreen(
        hideSystemUI: hideSystemUI,
        enableAutoHide: enableAutoHide,
        autoHideDelay: autoHideDelay,
      );
    }
  }

  /// Hide system UI elements
  static Future<void> _hideSystemUI() async {
    try {
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.immersive,
        overlays: [],
      );
    } catch (e) {
      // Fallback for older Android versions
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.manual,
        overlays: [],
      );
    }
  }

  /// Show system UI elements
  static Future<void> _showSystemUI() async {
    await SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.edgeToEdge,
      overlays: SystemUiOverlay.values,
    );
  }

  /// Set auto-hide configuration
  static void setAutoHideConfig({bool? enabled, Duration? delay}) {
    if (enabled != null) {
      _autoHideEnabled = enabled;
    }
    if (delay != null) {
      _autoHideDelay = delay;
    }
  }

  /// Force landscape orientation for video playback
  static Future<void> setLandscapeOrientation() async {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  /// Force portrait orientation
  static Future<void> setPortraitOrientation() async {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  /// Reset to all orientations
  static Future<void> resetOrientation() async {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  /// Get current orientation
  static DeviceOrientation getCurrentOrientation(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    if (mediaQuery.orientation == Orientation.landscape) {
      return DeviceOrientation.landscapeLeft;
    } else {
      return DeviceOrientation.portraitUp;
    }
  }

  /// Check if device is in landscape mode
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  /// Check if device is in portrait mode
  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  /// Get safe area insets for full-screen mode
  static EdgeInsets getSafeAreaInsets(BuildContext context) {
    if (_isFullScreen) {
      return EdgeInsets.zero;
    }
    return MediaQuery.of(context).padding;
  }

  /// Get screen dimensions
  static Size getScreenSize(BuildContext context) {
    return MediaQuery.of(context).size;
  }

  /// Calculate aspect ratio
  static double getAspectRatio(BuildContext context) {
    final size = getScreenSize(context);
    return size.width / size.height;
  }

  /// Dispose of the service
  static Future<void> dispose() async {
    if (_isFullScreen) {
      await exitFullScreen();
    }
    _isInitialized = false;
  }
}

/// Full-screen state provider
final fullScreenProvider = StateProvider<bool>((ref) => false);

/// Auto-hide configuration provider
final autoHideConfigProvider = StateProvider<AutoHideConfig>(
  (ref) => const AutoHideConfig(enabled: true, delay: Duration(seconds: 3)),
);

/// Orientation provider
final orientationProvider = StateProvider<DeviceOrientation>(
  (ref) => DeviceOrientation.portraitUp,
);

/// Auto-hide configuration model
class AutoHideConfig {
  final bool enabled;
  final Duration delay;

  const AutoHideConfig({required this.enabled, required this.delay});

  AutoHideConfig copyWith({bool? enabled, Duration? delay}) {
    return AutoHideConfig(
      enabled: enabled ?? this.enabled,
      delay: delay ?? this.delay,
    );
  }
}

/// Full-screen mode types
enum FullScreenMode { normal, immersive, immersiveSticky, lean }

/// Full-screen configuration
class FullScreenConfig {
  final FullScreenMode mode;
  final bool hideSystemUI;
  final bool enableAutoHide;
  final Duration autoHideDelay;
  final List<DeviceOrientation> allowedOrientations;

  const FullScreenConfig({
    this.mode = FullScreenMode.immersive,
    this.hideSystemUI = true,
    this.enableAutoHide = true,
    this.autoHideDelay = const Duration(seconds: 3),
    this.allowedOrientations = const [
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ],
  });

  FullScreenConfig copyWith({
    FullScreenMode? mode,
    bool? hideSystemUI,
    bool? enableAutoHide,
    Duration? autoHideDelay,
    List<DeviceOrientation>? allowedOrientations,
  }) {
    return FullScreenConfig(
      mode: mode ?? this.mode,
      hideSystemUI: hideSystemUI ?? this.hideSystemUI,
      enableAutoHide: enableAutoHide ?? this.enableAutoHide,
      autoHideDelay: autoHideDelay ?? this.autoHideDelay,
      allowedOrientations: allowedOrientations ?? this.allowedOrientations,
    );
  }
}
