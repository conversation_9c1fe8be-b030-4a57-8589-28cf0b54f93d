import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_component.dart';
import '../models/spreadsheet.dart';

class PropertiesPanel extends ConsumerStatefulWidget {
  final String? selectedComponentId;
  final List<UIComponent> components;
  final Spreadsheet spreadsheet;
  final Function(UIComponent) onComponentChanged;

  const PropertiesPanel({
    super.key,
    required this.selectedComponentId,
    required this.components,
    required this.spreadsheet,
    required this.onComponentChanged,
  });

  @override
  ConsumerState<PropertiesPanel> createState() => _PropertiesPanelState();
}

class _PropertiesPanelState extends ConsumerState<PropertiesPanel> {
  final Map<String, TextEditingController> _controllers = {};

  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  UIComponent? get selectedComponent {
    if (widget.selectedComponentId == null) return null;
    try {
      return widget.components.firstWhere((c) => c.id == widget.selectedComponentId);
    } catch (e) {
      return null;
    }
  }

  TextEditingController _getController(String key, String initialValue) {
    if (!_controllers.containsKey(key)) {
      _controllers[key] = TextEditingController(text: initialValue);
    }
    return _controllers[key]!;
  }

  @override
  Widget build(BuildContext context) {
    final component = selectedComponent;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(left: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Properties',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          if (component == null)
            _buildNoSelectionState()
          else
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildComponentInfo(component),
                    const SizedBox(height: 16),
                    _buildBasicProperties(component),
                    const SizedBox(height: 16),
                    _buildStyleProperties(component),
                    const SizedBox(height: 16),
                    _buildDataBinding(component),
                    const SizedBox(height: 16),
                    _buildAdvancedProperties(component),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildNoSelectionState() {
    return Expanded(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.touch_app,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No Component Selected',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Select a component on the canvas to edit its properties',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComponentInfo(UIComponent component) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(_getComponentIcon(component.type)),
                const SizedBox(width: 8),
                Text(
                  component.type.name.toUpperCase(),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'ID: ${component.id}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontFamily: 'monospace',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicProperties(UIComponent component) {
    return _buildPropertySection(
      'Basic Properties',
      [
        _buildTextField(
          'Label',
          component.label,
          (value) => _updateComponent(component.copyWith(label: value)),
        ),
        if (component.type == ComponentType.textInput || 
            component.type == ComponentType.numberInput ||
            component.type == ComponentType.textArea)
          _buildTextField(
            'Placeholder',
            component.placeholder ?? '',
            (value) => _updateComponent(component.copyWith(placeholder: value)),
          ),
        _buildTextField(
          'Help Text',
          component.helpText ?? '',
          (value) => _updateComponent(component.copyWith(helpText: value)),
        ),
        _buildPositionFields(component),
        _buildSizeFields(component),
        _buildVisibilityToggles(component),
      ],
    );
  }

  Widget _buildStyleProperties(UIComponent component) {
    return _buildPropertySection(
      'Style Properties',
      [
        _buildColorField(
          'Background Color',
          component.style.backgroundColor,
          (value) => _updateComponentStyle(component, component.style.copyWith(backgroundColor: value)),
        ),
        _buildColorField(
          'Text Color',
          component.style.textColor,
          (value) => _updateComponentStyle(component, component.style.copyWith(textColor: value)),
        ),
        _buildNumberField(
          'Font Size',
          component.style.fontSize?.toString() ?? '',
          (value) => _updateComponentStyle(component, component.style.copyWith(fontSize: double.tryParse(value))),
        ),
        _buildFontStyleToggles(component),
        _buildAlignmentDropdown(component),
        _buildBorderFields(component),
      ],
    );
  }

  Widget _buildDataBinding(UIComponent component) {
    return _buildPropertySection(
      'Data Binding',
      [
        _buildCellAddressField(component),
        _buildBindingTypeDropdown(component),
        if (component.dataBinding?.bindingType == 'input' || component.dataBinding?.bindingType == 'bidirectional')
          _buildTextField(
            'Validation Rule',
            component.dataBinding?.validationRule ?? '',
            (value) => _updateDataBinding(component, component.dataBinding?.copyWith(validationRule: value)),
          ),
        _buildTextField(
          'Default Value',
          component.dataBinding?.defaultValue?.toString() ?? '',
          (value) => _updateDataBinding(component, component.dataBinding?.copyWith(defaultValue: value)),
        ),
      ],
    );
  }

  Widget _buildAdvancedProperties(UIComponent component) {
    return _buildPropertySection(
      'Advanced Properties',
      [
        _buildNumberField(
          'Z-Index',
          component.zIndex.toString(),
          (value) => _updateComponent(component.copyWith(zIndex: int.tryParse(value) ?? 0)),
        ),
        if (component.type == ComponentType.dropdown)
          _buildDropdownOptions(component),
        if (component.type == ComponentType.button)
          _buildButtonActions(component),
      ],
    );
  }

  Widget _buildPropertySection(String title, List<Widget> children) {
    return ExpansionTile(
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      initiallyExpanded: true,
      children: children,
    );
  }

  Widget _buildTextField(String label, String value, Function(String) onChanged) {
    final controller = _getController('${label}_$value', value);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: TextField(
        controller: controller,
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        ),
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildNumberField(String label, String value, Function(String) onChanged) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: TextField(
        controller: _getController('${label}_$value', value),
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        ),
        keyboardType: TextInputType.number,
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildColorField(String label, String? value, Function(String?) onChanged) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _getController('${label}_${value ?? ''}', value ?? ''),
              decoration: InputDecoration(
                labelText: label,
                border: const OutlineInputBorder(),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                hintText: '#FFFFFF',
              ),
              onChanged: onChanged,
            ),
          ),
          const SizedBox(width: 8),
          GestureDetector(
            onTap: () => _showColorPicker(value, onChanged),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: value != null ? Color(int.parse(value.replaceFirst('#', '0xFF'))) : Colors.grey[300],
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPositionFields(UIComponent component) {
    return Row(
      children: [
        Expanded(
          child: _buildNumberField(
            'X Position',
            component.x.toString(),
            (value) => _updateComponent(component.copyWith(x: double.tryParse(value) ?? 0)),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildNumberField(
            'Y Position',
            component.y.toString(),
            (value) => _updateComponent(component.copyWith(y: double.tryParse(value) ?? 0)),
          ),
        ),
      ],
    );
  }

  Widget _buildSizeFields(UIComponent component) {
    return Row(
      children: [
        Expanded(
          child: _buildNumberField(
            'Width',
            component.style.width?.toString() ?? '',
            (value) => _updateComponentStyle(component, component.style.copyWith(width: double.tryParse(value))),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildNumberField(
            'Height',
            component.style.height?.toString() ?? '',
            (value) => _updateComponentStyle(component, component.style.copyWith(height: double.tryParse(value))),
          ),
        ),
      ],
    );
  }

  Widget _buildVisibilityToggles(UIComponent component) {
    return Column(
      children: [
        CheckboxListTile(
          dense: true,
          title: const Text('Visible'),
          value: component.isVisible,
          onChanged: (value) => _updateComponent(component.copyWith(isVisible: value ?? true)),
        ),
        CheckboxListTile(
          dense: true,
          title: const Text('Enabled'),
          value: component.isEnabled,
          onChanged: (value) => _updateComponent(component.copyWith(isEnabled: value ?? true)),
        ),
        CheckboxListTile(
          dense: true,
          title: const Text('Required'),
          value: component.isRequired,
          onChanged: (value) => _updateComponent(component.copyWith(isRequired: value ?? false)),
        ),
      ],
    );
  }

  Widget _buildFontStyleToggles(UIComponent component) {
    return Column(
      children: [
        CheckboxListTile(
          dense: true,
          title: const Text('Bold'),
          value: component.style.isBold,
          onChanged: (value) => _updateComponentStyle(component, component.style.copyWith(isBold: value ?? false)),
        ),
        CheckboxListTile(
          dense: true,
          title: const Text('Italic'),
          value: component.style.isItalic,
          onChanged: (value) => _updateComponentStyle(component, component.style.copyWith(isItalic: value ?? false)),
        ),
      ],
    );
  }

  Widget _buildAlignmentDropdown(UIComponent component) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: DropdownButtonFormField<ComponentAlignment>(
        value: component.style.alignment,
        decoration: const InputDecoration(
          labelText: 'Text Alignment',
          border: OutlineInputBorder(),
        ),
        items: ComponentAlignment.values.map((alignment) {
          return DropdownMenuItem(
            value: alignment,
            child: Text(alignment.name.toUpperCase()),
          );
        }).toList(),
        onChanged: (value) {
          if (value != null) {
            _updateComponentStyle(component, component.style.copyWith(alignment: value));
          }
        },
      ),
    );
  }

  Widget _buildBorderFields(UIComponent component) {
    return Column(
      children: [
        _buildColorField(
          'Border Color',
          component.style.borderColor,
          (value) => _updateComponentStyle(component, component.style.copyWith(borderColor: value)),
        ),
        _buildNumberField(
          'Border Width',
          component.style.borderWidth?.toString() ?? '',
          (value) => _updateComponentStyle(component, component.style.copyWith(borderWidth: double.tryParse(value))),
        ),
        _buildNumberField(
          'Border Radius',
          component.style.borderRadius?.toString() ?? '',
          (value) => _updateComponentStyle(component, component.style.copyWith(borderRadius: double.tryParse(value))),
        ),
      ],
    );
  }

  Widget _buildCellAddressField(UIComponent component) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _getController('cellAddress', component.dataBinding?.cellAddress ?? ''),
              decoration: const InputDecoration(
                labelText: 'Cell Address',
                border: OutlineInputBorder(),
                hintText: 'A1',
              ),
              onChanged: (value) => _updateDataBinding(
                component,
                (component.dataBinding ?? const DataBinding(cellAddress: '')).copyWith(cellAddress: value),
              ),
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: () => _showCellPicker(component),
            icon: const Icon(Icons.table_chart),
            tooltip: 'Pick Cell',
          ),
        ],
      ),
    );
  }

  Widget _buildBindingTypeDropdown(UIComponent component) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: DropdownButtonFormField<String>(
        value: component.dataBinding?.bindingType ?? 'bidirectional',
        decoration: const InputDecoration(
          labelText: 'Binding Type',
          border: OutlineInputBorder(),
        ),
        items: const [
          DropdownMenuItem(value: 'input', child: Text('Input (Component → Cell)')),
          DropdownMenuItem(value: 'output', child: Text('Output (Cell → Component)')),
          DropdownMenuItem(value: 'bidirectional', child: Text('Bidirectional')),
        ],
        onChanged: (value) {
          if (value != null) {
            _updateDataBinding(
              component,
              (component.dataBinding ?? const DataBinding(cellAddress: '')).copyWith(bindingType: value),
            );
          }
        },
      ),
    );
  }

  Widget _buildDropdownOptions(UIComponent component) {
    final options = component.properties['options'] as List<String>? ?? [];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Dropdown Options:', style: TextStyle(fontWeight: FontWeight.bold)),
        ...options.asMap().entries.map((entry) {
          final index = entry.key;
          final option = entry.value;
          
          return Row(
            children: [
              Expanded(
                child: TextField(
                  controller: TextEditingController(text: option),
                  decoration: InputDecoration(
                    labelText: 'Option ${index + 1}',
                    border: const OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    final newOptions = [...options];
                    newOptions[index] = value;
                    _updateComponentProperties(component, {'options': newOptions});
                  },
                ),
              ),
              IconButton(
                onPressed: () {
                  final newOptions = [...options];
                  newOptions.removeAt(index);
                  _updateComponentProperties(component, {'options': newOptions});
                },
                icon: const Icon(Icons.delete),
              ),
            ],
          );
        }),
        ElevatedButton.icon(
          onPressed: () {
            final newOptions = [...options, 'New Option'];
            _updateComponentProperties(component, {'options': newOptions});
          },
          icon: const Icon(Icons.add),
          label: const Text('Add Option'),
        ),
      ],
    );
  }

  Widget _buildButtonActions(UIComponent component) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Button Actions:', style: TextStyle(fontWeight: FontWeight.bold)),
        _buildTextField(
          'Click Action',
          component.events['click'] ?? '',
          (value) => _updateComponentEvents(component, {'click': value}),
        ),
      ],
    );
  }

  void _updateComponent(UIComponent component) {
    widget.onComponentChanged(component);
  }

  void _updateComponentStyle(UIComponent component, ComponentStyle style) {
    widget.onComponentChanged(component.copyWith(style: style));
  }

  void _updateComponentProperties(UIComponent component, Map<String, dynamic> properties) {
    final newProperties = {...component.properties, ...properties};
    widget.onComponentChanged(component.copyWith(properties: newProperties));
  }

  void _updateComponentEvents(UIComponent component, Map<String, String> events) {
    final newEvents = {...component.events, ...events};
    widget.onComponentChanged(component.copyWith(events: newEvents));
  }

  void _updateDataBinding(UIComponent component, DataBinding? dataBinding) {
    widget.onComponentChanged(component.copyWith(dataBinding: dataBinding));
  }

  void _showColorPicker(String? currentColor, Function(String?) onChanged) {
    // Simple color picker implementation
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Pick Color'),
        content: SizedBox(
          width: 300,
          height: 200,
          child: GridView.count(
            crossAxisCount: 6,
            children: [
              '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF',
              '#800000', '#008000', '#000080', '#808000', '#800080', '#008080',
              '#C0C0C0', '#808080', '#000000', '#FFFFFF', '#FFA500', '#A52A2A',
            ].map((color) {
              return GestureDetector(
                onTap: () {
                  onChanged(color);
                  Navigator.of(context).pop();
                },
                child: Container(
                  margin: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Color(int.parse(color.replaceFirst('#', '0xFF'))),
                    border: Border.all(color: Colors.grey),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showCellPicker(UIComponent component) {
    // Cell picker implementation
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Pick Cell'),
        content: const Text('Cell picker will be implemented'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  IconData _getComponentIcon(ComponentType type) {
    switch (type) {
      case ComponentType.textInput:
        return Icons.text_fields;
      case ComponentType.numberInput:
        return Icons.numbers;
      case ComponentType.dropdown:
        return Icons.arrow_drop_down;
      case ComponentType.checkbox:
        return Icons.check_box;
      case ComponentType.radioButton:
        return Icons.radio_button_checked;
      case ComponentType.button:
        return Icons.smart_button;
      case ComponentType.label:
        return Icons.label;
      case ComponentType.slider:
        return Icons.linear_scale;
      case ComponentType.dateInput:
        return Icons.date_range;
      case ComponentType.timeInput:
        return Icons.access_time;
      case ComponentType.textArea:
        return Icons.notes;
      case ComponentType.image:
        return Icons.image;
      case ComponentType.chart:
        return Icons.bar_chart;
      case ComponentType.table:
        return Icons.table_chart;
      case ComponentType.container:
        return Icons.crop_square;
      case ComponentType.divider:
        return Icons.horizontal_rule;
      case ComponentType.spacer:
        return Icons.space_bar;
      case ComponentType.progressBar:
        return Icons.linear_scale;
      case ComponentType.colorPicker:
        return Icons.palette;
      case ComponentType.fileUpload:
        return Icons.cloud_upload;
      case ComponentType.richTextEditor:
        return Icons.text_format;
      case ComponentType.codeEditor:
        return Icons.code;
      case ComponentType.map:
        return Icons.map;
      case ComponentType.video:
        return Icons.video_library;
      case ComponentType.audio:
        return Icons.audio_file;
      case ComponentType.qrCode:
        return Icons.qr_code;
      case ComponentType.barcode:
        return Icons.view_week;
      case ComponentType.signature:
        return Icons.draw;
      case ComponentType.drawing:
        return Icons.brush;
      case ComponentType.calendar:
        return Icons.calendar_today;
      case ComponentType.dataTable:
        return Icons.table_chart;
    }
  }
}
