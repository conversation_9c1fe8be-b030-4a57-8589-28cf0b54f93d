import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/app_theme.dart';
import '../services/app_generation_service.dart';
import '../services/data_import_export_service.dart';
import '../models/app_generation_models.dart' as app_models;

/// App generation screen for creating complete apps from Excel data
class AppGenerationScreen extends ConsumerStatefulWidget {
  const AppGenerationScreen({super.key});

  @override
  ConsumerState<AppGenerationScreen> createState() =>
      _AppGenerationScreenState();
}

class _AppGenerationScreenState extends ConsumerState<AppGenerationScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Form controllers
  final _appNameController = TextEditingController();
  final _appDescriptionController = TextEditingController();

  // State variables
  List<List<dynamic>> _importedData = [];
  AppType _selectedAppType = AppType.dataTable;
  AppTemplate? _selectedTemplate;
  app_models.LayoutConfiguration? _layoutConfig;
  bool _isGenerating = false;
  GeneratedApp? _generatedApp;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    AppGenerationService.initialize();
    _initializeDefaults();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _appNameController.dispose();
    _appDescriptionController.dispose();
    super.dispose();
  }

  void _initializeDefaults() {
    _appNameController.text = 'My Excel App';
    _appDescriptionController.text = 'Generated app from Excel data';
    // Mock layout configuration
    _layoutConfig = const app_models.LayoutConfiguration(
      navigationSystem: app_models.NavigationSystem.sidebar,
      gridLayout: app_models.GridLayoutType.responsive,
      colorScheme: app_models.AppColorScheme.light,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('App Generation'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _generateApp,
            icon: const Icon(Icons.build),
            tooltip: 'Generate App',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.upload_file), text: 'Data Import'),
            Tab(icon: Icon(Icons.apps), text: 'App Type'),
            Tab(icon: Icon(Icons.palette), text: 'Layout'),
            Tab(icon: Icon(Icons.settings), text: 'Configuration'),
            Tab(icon: Icon(Icons.preview), text: 'Preview'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildDataImportTab(),
          _buildAppTypeTab(),
          _buildLayoutTab(),
          _buildConfigurationTab(),
          _buildPreviewTab(),
        ],
      ),
      floatingActionButton: _isGenerating
          ? const FloatingActionButton(
              onPressed: null,
              child: CircularProgressIndicator(color: Colors.white),
            )
          : FloatingActionButton.extended(
              onPressed: _canGenerateApp() ? _generateApp : null,
              icon: const Icon(Icons.build),
              label: const Text('Generate App'),
              backgroundColor: _canGenerateApp()
                  ? AppTheme.primaryColor
                  : Colors.grey,
            ),
    );
  }

  Widget _buildDataImportTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Import Excel Data',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _importExcelFile,
                          icon: const Icon(Icons.file_upload),
                          label: const Text('Import Excel File'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: _importSampleData,
                          icon: const Icon(Icons.data_usage),
                          label: const Text('Use Sample Data'),
                        ),
                      ),
                    ],
                  ),
                  if (_importedData.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.green),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.check_circle, color: Colors.green),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Data imported: ${_importedData.length} rows, ${_importedData.isNotEmpty ? _importedData.first.length : 0} columns',
                              style: const TextStyle(color: Colors.green),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          if (_importedData.isNotEmpty) _buildDataPreview(),
        ],
      ),
    );
  }

  Widget _buildDataPreview() {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Data Preview',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 16),
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: SingleChildScrollView(
                    child: DataTable(
                      columns: _importedData.isNotEmpty
                          ? _importedData.first
                                .asMap()
                                .entries
                                .map(
                                  (entry) => DataColumn(
                                    label: Text('Column ${entry.key + 1}'),
                                  ),
                                )
                                .toList()
                          : [],
                      rows: _importedData
                          .take(10)
                          .map(
                            (row) => DataRow(
                              cells: row
                                  .map(
                                    (cell) => DataCell(Text(cell.toString())),
                                  )
                                  .toList(),
                            ),
                          )
                          .toList(),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppTypeTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Choose App Type',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 1.2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: AppGenerationService.appTemplates.length,
              itemBuilder: (context, index) {
                final template = AppGenerationService.appTemplates[index];
                final isSelected = _selectedTemplate?.id == template.id;

                return Card(
                  elevation: isSelected ? 8 : 2,
                  color: isSelected
                      ? AppTheme.primaryColor.withValues(alpha: 0.1)
                      : null,
                  child: InkWell(
                    onTap: () => _selectTemplate(template),
                    borderRadius: BorderRadius.circular(8),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            template.icon,
                            size: 48,
                            color: isSelected
                                ? AppTheme.primaryColor
                                : Colors.grey[600],
                          ),
                          const SizedBox(height: 12),
                          Text(
                            template.name,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: isSelected ? AppTheme.primaryColor : null,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            template.description,
                            style: Theme.of(context).textTheme.bodySmall,
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLayoutTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Layout Configuration',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildLayoutSection('Navigation Style', [
                    _buildLayoutOption(
                      'Sidebar',
                      Icons.menu,
                      _layoutConfig?.navigationSystem ==
                          app_models.NavigationSystem.sidebar,
                      () => _updateNavigationStyle(
                        app_models.NavigationSystem.sidebar,
                      ),
                    ),
                    _buildLayoutOption(
                      'Top Tabs',
                      Icons.tab,
                      _layoutConfig?.navigationSystem ==
                          app_models.NavigationSystem.topTabs,
                      () => _updateNavigationStyle(
                        app_models.NavigationSystem.topTabs,
                      ),
                    ),
                    _buildLayoutOption(
                      'Bottom Tabs',
                      Icons.tab_unselected,
                      _layoutConfig?.navigationSystem ==
                          app_models.NavigationSystem.bottomTabs,
                      () => _updateNavigationStyle(
                        app_models.NavigationSystem.bottomTabs,
                      ),
                    ),
                  ]),
                  const SizedBox(height: 24),
                  _buildLayoutSection('Color Scheme', [
                    _buildLayoutOption(
                      'Light',
                      Icons.light_mode,
                      _layoutConfig?.colorScheme ==
                          app_models.AppColorScheme.light,
                      () => _updateColorScheme(app_models.AppColorScheme.light),
                    ),
                    _buildLayoutOption(
                      'Dark',
                      Icons.dark_mode,
                      _layoutConfig?.colorScheme ==
                          app_models.AppColorScheme.dark,
                      () => _updateColorScheme(app_models.AppColorScheme.dark),
                    ),
                    _buildLayoutOption(
                      'Auto',
                      Icons.brightness_auto,
                      _layoutConfig?.colorScheme ==
                          app_models.AppColorScheme.auto,
                      () => _updateColorScheme(app_models.AppColorScheme.auto),
                    ),
                  ]),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLayoutSection(String title, List<Widget> options) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 12),
        Wrap(spacing: 12, runSpacing: 12, children: options),
      ],
    );
  }

  Widget _buildLayoutOption(
    String label,
    IconData icon,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? AppTheme.primaryColor : Colors.grey[300]!,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(8),
          color: isSelected
              ? AppTheme.primaryColor.withValues(alpha: 0.1)
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? AppTheme.primaryColor : Colors.grey[600],
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? AppTheme.primaryColor : null,
                fontWeight: isSelected ? FontWeight.bold : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigurationTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'App Configuration',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  TextField(
                    controller: _appNameController,
                    decoration: const InputDecoration(
                      labelText: 'App Name',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.apps),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _appDescriptionController,
                    decoration: const InputDecoration(
                      labelText: 'App Description',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.description),
                    ),
                    maxLines: 3,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewTab() {
    if (_generatedApp == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.preview, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No app generated yet'),
            Text('Complete the configuration and generate your app'),
          ],
        ),
      );
    }

    return AppGenerationService.previewApp(_generatedApp!.id);
  }

  void _importExcelFile() async {
    try {
      final result = await DataImportExportService.importData(
        format: DataFormat.excel,
        fileName: 'imported_data.xlsx',
        data: Uint8List(0),
      );
      if (result.success) {
        setState(() {
          _importedData = result.data;
        });
        _showSnackBar('Excel file imported successfully', Colors.green);
      } else {
        _showSnackBar('Failed to import Excel file', Colors.red);
      }
    } catch (e) {
      _showSnackBar('Import failed: $e', Colors.red);
    }
  }

  void _importSampleData() {
    setState(() {
      _importedData = [
        ['Product', 'Sales', 'Region', 'Date'],
        ['Widget A', 1000, 'North', '2024-01-01'],
        ['Widget B', 1500, 'South', '2024-01-02'],
        ['Widget C', 800, 'East', '2024-01-03'],
        ['Widget D', 1200, 'West', '2024-01-04'],
        ['Widget E', 900, 'North', '2024-01-05'],
      ];
    });
    _showSnackBar('Sample data loaded', Colors.blue);
  }

  void _selectTemplate(AppTemplate template) {
    setState(() {
      _selectedTemplate = template;
      _selectedAppType = template.appType;
    });
  }

  void _updateNavigationStyle(app_models.NavigationSystem style) {
    setState(() {
      _layoutConfig =
          _layoutConfig?.copyWith(navigationSystem: style) ??
          app_models.LayoutConfiguration(navigationSystem: style);
    });
  }

  void _updateColorScheme(app_models.AppColorScheme colorScheme) {
    setState(() {
      _layoutConfig =
          _layoutConfig?.copyWith(colorScheme: colorScheme) ??
          app_models.LayoutConfiguration(colorScheme: colorScheme);
    });
  }

  bool _canGenerateApp() {
    return _importedData.isNotEmpty &&
        _selectedTemplate != null &&
        _layoutConfig != null &&
        _appNameController.text.isNotEmpty &&
        !_isGenerating;
  }

  void _generateApp() async {
    if (!_canGenerateApp()) return;

    setState(() => _isGenerating = true);

    try {
      final config = AppConfiguration(
        appType: _selectedAppType,
        layoutConfig: _layoutConfig!,
        customSettings: {
          'template': _selectedTemplate!.id,
          'features': _selectedTemplate!.features.map((f) => f.name).toList(),
        },
      );

      final app = await AppGenerationService.generateApp(
        appName: _appNameController.text,
        data: _importedData,
        configuration: config,
      );

      setState(() {
        _generatedApp = app;
      });

      _showSnackBar('App generated successfully!', Colors.green);
      _tabController.animateTo(4); // Switch to preview tab
    } catch (e) {
      _showSnackBar('Failed to generate app: $e', Colors.red);
    } finally {
      setState(() => _isGenerating = false);
    }
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
