import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:io';

// Android Platform Service Provider
final androidPlatformServiceProvider = Provider<AndroidPlatformService>((ref) {
  return AndroidPlatformService();
});

// Notification Service Provider
final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService();
});

// File System Permissions Provider
final fileSystemPermissionsProvider =
    StateNotifierProvider<FileSystemPermissionsNotifier, FileSystemPermissions>(
      (ref) {
        return FileSystemPermissionsNotifier();
      },
    );

// Android Platform Service
class AndroidPlatformService {
  static const MethodChannel _channel = MethodChannel('shadow_suite/platform');

  // Check if running on Android
  bool get isAndroid => Platform.isAndroid;

  // Request storage permissions
  Future<bool> requestStoragePermissions() async {
    if (!isAndroid) return true;

    try {
      final bool granted = await _channel.invokeMethod(
        'requestStoragePermissions',
      );
      return granted;
    } catch (e) {
      debugPrint('Error requesting storage permissions: $e');
      return false;
    }
  }

  // Check storage permissions
  Future<bool> hasStoragePermissions() async {
    if (!isAndroid) return true;

    try {
      final bool hasPermissions = await _channel.invokeMethod(
        'hasStoragePermissions',
      );
      return hasPermissions;
    } catch (e) {
      debugPrint('Error checking storage permissions: $e');
      return false;
    }
  }

  // Request notification permissions
  Future<bool> requestNotificationPermissions() async {
    if (!isAndroid) return true;

    try {
      final bool granted = await _channel.invokeMethod(
        'requestNotificationPermissions',
      );
      return granted;
    } catch (e) {
      debugPrint('Error requesting notification permissions: $e');
      return false;
    }
  }

  // Enable haptic feedback
  Future<void> enableHapticFeedback() async {
    if (!isAndroid) return;

    try {
      await HapticFeedback.lightImpact();
    } catch (e) {
      debugPrint('Error enabling haptic feedback: $e');
    }
  }

  // Get device info
  Future<AndroidDeviceInfo> getDeviceInfo() async {
    if (!isAndroid) {
      return AndroidDeviceInfo(
        model: 'Desktop',
        version: 'Unknown',
        sdkVersion: 0,
        manufacturer: 'Unknown',
        screenDensity: 1.0,
        screenSize: const Size(1024, 768),
      );
    }

    try {
      final Map<String, dynamic> deviceInfo = await _channel.invokeMethod(
        'getDeviceInfo',
      );
      return AndroidDeviceInfo.fromMap(deviceInfo);
    } catch (e) {
      debugPrint('Error getting device info: $e');
      return AndroidDeviceInfo(
        model: 'Unknown',
        version: 'Unknown',
        sdkVersion: 0,
        manufacturer: 'Unknown',
        screenDensity: 1.0,
        screenSize: const Size(360, 640),
      );
    }
  }

  // Open app settings
  Future<void> openAppSettings() async {
    if (!isAndroid) return;

    try {
      await _channel.invokeMethod('openAppSettings');
    } catch (e) {
      debugPrint('Error opening app settings: $e');
    }
  }

  // Set navigation bar color
  Future<void> setNavigationBarColor(Color color) async {
    if (!isAndroid) return;

    try {
      await _channel.invokeMethod('setNavigationBarColor', {
        'color': color.toARGB32(),
      });
    } catch (e) {
      debugPrint('Error setting navigation bar color: $e');
    }
  }

  // Set status bar color
  Future<void> setStatusBarColor(
    Color color, {
    bool lightContent = false,
  }) async {
    if (!isAndroid) return;

    try {
      await _channel.invokeMethod('setStatusBarColor', {
        'color': color.toARGB32(),
        'lightContent': lightContent,
      });
    } catch (e) {
      debugPrint('Error setting status bar color: $e');
    }
  }
}

// Notification Service
class NotificationService {
  static const MethodChannel _channel = MethodChannel(
    'shadow_suite/notifications',
  );

  // Show notification
  Future<void> showNotification({
    required String title,
    required String body,
    String? channelId,
    String? channelName,
    NotificationPriority priority = NotificationPriority.normal,
    Map<String, String>? data,
  }) async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('showNotification', {
        'title': title,
        'body': body,
        'channelId': channelId ?? 'default',
        'channelName': channelName ?? 'Default',
        'priority': priority.index,
        'data': data ?? {},
      });
    } catch (e) {
      debugPrint('Error showing notification: $e');
    }
  }

  // Schedule notification
  Future<void> scheduleNotification({
    required String title,
    required String body,
    required DateTime scheduledTime,
    String? channelId,
    String? channelName,
    NotificationPriority priority = NotificationPriority.normal,
    Map<String, String>? data,
  }) async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('scheduleNotification', {
        'title': title,
        'body': body,
        'scheduledTime': scheduledTime.millisecondsSinceEpoch,
        'channelId': channelId ?? 'scheduled',
        'channelName': channelName ?? 'Scheduled',
        'priority': priority.index,
        'data': data ?? {},
      });
    } catch (e) {
      debugPrint('Error scheduling notification: $e');
    }
  }

  // Cancel notification
  Future<void> cancelNotification(int notificationId) async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('cancelNotification', {
        'notificationId': notificationId,
      });
    } catch (e) {
      debugPrint('Error canceling notification: $e');
    }
  }

  // Cancel all notifications
  Future<void> cancelAllNotifications() async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('cancelAllNotifications');
    } catch (e) {
      debugPrint('Error canceling all notifications: $e');
    }
  }

  // Create notification channel
  Future<void> createNotificationChannel({
    required String channelId,
    required String channelName,
    String? description,
    NotificationPriority priority = NotificationPriority.normal,
  }) async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('createNotificationChannel', {
        'channelId': channelId,
        'channelName': channelName,
        'description': description ?? '',
        'priority': priority.index,
      });
    } catch (e) {
      debugPrint('Error creating notification channel: $e');
    }
  }
}

// File System Permissions State
class FileSystemPermissions {
  final bool hasStoragePermission;
  final bool hasExternalStoragePermission;
  final bool hasManageExternalStoragePermission;
  final DateTime lastChecked;

  const FileSystemPermissions({
    this.hasStoragePermission = false,
    this.hasExternalStoragePermission = false,
    this.hasManageExternalStoragePermission = false,
    required this.lastChecked,
  });

  bool get hasAllPermissions =>
      hasStoragePermission &&
      hasExternalStoragePermission &&
      hasManageExternalStoragePermission;

  FileSystemPermissions copyWith({
    bool? hasStoragePermission,
    bool? hasExternalStoragePermission,
    bool? hasManageExternalStoragePermission,
    DateTime? lastChecked,
  }) {
    return FileSystemPermissions(
      hasStoragePermission: hasStoragePermission ?? this.hasStoragePermission,
      hasExternalStoragePermission:
          hasExternalStoragePermission ?? this.hasExternalStoragePermission,
      hasManageExternalStoragePermission:
          hasManageExternalStoragePermission ??
          this.hasManageExternalStoragePermission,
      lastChecked: lastChecked ?? this.lastChecked,
    );
  }
}

// File System Permissions Notifier
class FileSystemPermissionsNotifier
    extends StateNotifier<FileSystemPermissions> {
  FileSystemPermissionsNotifier()
    : super(FileSystemPermissions(lastChecked: DateTime.now()));

  Future<void> checkPermissions() async {
    final androidService = AndroidPlatformService();

    final hasStorage = await androidService.hasStoragePermissions();

    state = state.copyWith(
      hasStoragePermission: hasStorage,
      hasExternalStoragePermission: hasStorage,
      hasManageExternalStoragePermission: hasStorage,
      lastChecked: DateTime.now(),
    );
  }

  Future<bool> requestPermissions() async {
    final androidService = AndroidPlatformService();

    final granted = await androidService.requestStoragePermissions();

    if (granted) {
      await checkPermissions();
    }

    return granted;
  }
}

// Android Device Info
class AndroidDeviceInfo {
  final String model;
  final String version;
  final int sdkVersion;
  final String manufacturer;
  final double screenDensity;
  final Size screenSize;

  const AndroidDeviceInfo({
    required this.model,
    required this.version,
    required this.sdkVersion,
    required this.manufacturer,
    required this.screenDensity,
    required this.screenSize,
  });

  factory AndroidDeviceInfo.fromMap(Map<String, dynamic> map) {
    return AndroidDeviceInfo(
      model: map['model'] as String? ?? 'Unknown',
      version: map['version'] as String? ?? 'Unknown',
      sdkVersion: map['sdkVersion'] as int? ?? 0,
      manufacturer: map['manufacturer'] as String? ?? 'Unknown',
      screenDensity: (map['screenDensity'] as num?)?.toDouble() ?? 1.0,
      screenSize: Size(
        (map['screenWidth'] as num?)?.toDouble() ?? 360,
        (map['screenHeight'] as num?)?.toDouble() ?? 640,
      ),
    );
  }
}

// Notification Priority Enum
enum NotificationPriority { low, normal, high, urgent }
