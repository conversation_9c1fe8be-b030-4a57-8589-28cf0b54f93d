import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/network_sharing_service.dart';
import '../models/file_manager_models.dart';
import '../file_manager_main.dart';

class FTPServerScreen extends ConsumerStatefulWidget {
  const FTPServerScreen({super.key});

  @override
  ConsumerState<FTPServerScreen> createState() => _FTPServerScreenState();
}

class _FTPServerScreenState extends ConsumerState<FTPServerScreen> {
  bool _isServerRunning = false;
  NetworkShare? _activeServer;
  final _nameController = TextEditingController(text: 'Shadow Suite FTP');
  final _portController = TextEditingController(text: '21');
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final List<String> _sharedPaths = [];
  bool _requireAuth = false;
  bool _allowAnonymous = true;

  @override
  void initState() {
    super.initState();
    _loadServerStatus();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          FileManagerHeader(
            title: 'FTP Server',
            subtitle: _isServerRunning
                ? 'Server running on port ${_activeServer?.port ?? 21}'
                : 'Configure and start FTP server',
            actions: [
              IconButton(
                onPressed: _isServerRunning ? _stopServer : _startServer,
                icon: Icon(
                  _isServerRunning ? Icons.stop : Icons.play_arrow,
                  color: _isServerRunning
                      ? const Color(0xFFE74C3C)
                      : const Color(0xFF27AE60),
                ),
                tooltip: _isServerRunning ? 'Stop Server' : 'Start Server',
              ),
              IconButton(
                onPressed: _showServerInfo,
                icon: const Icon(Icons.info, color: Color(0xFF3498DB)),
                tooltip: 'Server Information',
              ),
            ],
          ),
          Expanded(
            child: _isServerRunning
                ? _buildServerStatus()
                : _buildServerConfiguration(),
          ),
        ],
      ),
    );
  }

  Widget _buildServerConfiguration() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Server Settings
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Server Configuration',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2C3E50),
                    ),
                  ),
                  const SizedBox(height: 16),

                  TextField(
                    controller: _nameController,
                    decoration: const InputDecoration(
                      labelText: 'Server Name',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.label),
                    ),
                  ),
                  const SizedBox(height: 16),

                  TextField(
                    controller: _portController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'Port',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.settings_ethernet),
                      helperText: 'Default FTP port is 21',
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Authentication Settings
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Authentication',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2C3E50),
                    ),
                  ),
                  const SizedBox(height: 16),

                  SwitchListTile(
                    title: const Text('Allow Anonymous Access'),
                    subtitle: const Text(
                      'Allow users to connect without credentials',
                    ),
                    value: _allowAnonymous,
                    onChanged: (value) {
                      setState(() {
                        _allowAnonymous = value;
                        if (value) _requireAuth = false;
                      });
                    },
                    activeColor: const Color(0xFF3498DB),
                  ),

                  SwitchListTile(
                    title: const Text('Require Authentication'),
                    subtitle: const Text('Require username and password'),
                    value: _requireAuth,
                    onChanged: (value) {
                      setState(() {
                        _requireAuth = value;
                        if (value) _allowAnonymous = false;
                      });
                    },
                    activeColor: const Color(0xFF3498DB),
                  ),

                  if (_requireAuth) ...[
                    const SizedBox(height: 16),
                    TextField(
                      controller: _usernameController,
                      decoration: const InputDecoration(
                        labelText: 'Username',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.person),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _passwordController,
                      obscureText: true,
                      decoration: const InputDecoration(
                        labelText: 'Password',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.lock),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Shared Folders
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Text(
                        'Shared Folders',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2C3E50),
                        ),
                      ),
                      const Spacer(),
                      ElevatedButton.icon(
                        onPressed: _addSharedFolder,
                        icon: const Icon(Icons.add),
                        label: const Text('Add Folder'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF3498DB),
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  if (_sharedPaths.isEmpty)
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF8F9FA),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: const Color(0xFFE9ECEF)),
                      ),
                      child: const Center(
                        child: Column(
                          children: [
                            Icon(
                              Icons.folder_open,
                              size: 48,
                              color: Color(0xFFBDC3C7),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'No folders shared',
                              style: TextStyle(
                                color: Color(0xFF7F8C8D),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              'Add folders to share via FTP',
                              style: TextStyle(color: Color(0xFF95A5A6)),
                            ),
                          ],
                        ),
                      ),
                    )
                  else
                    ...(_sharedPaths.map((path) => _buildSharedPathItem(path))),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Start Server Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _canStartServer() ? _startServer : null,
              icon: const Icon(Icons.play_arrow),
              label: const Text('Start FTP Server'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF27AE60),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildServerStatus() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // Server Status Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const Icon(
                    Icons.cloud_upload,
                    size: 64,
                    color: Color(0xFF27AE60),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'FTP Server Running',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF27AE60),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Server: ${_activeServer?.name ?? 'Unknown'}',
                    style: const TextStyle(color: Color(0xFF7F8C8D)),
                  ),
                  Text(
                    'Port: ${_activeServer?.port ?? 21}',
                    style: const TextStyle(color: Color(0xFF7F8C8D)),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton.icon(
                        onPressed: _stopServer,
                        icon: const Icon(Icons.stop),
                        label: const Text('Stop Server'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFE74C3C),
                          foregroundColor: Colors.white,
                        ),
                      ),
                      OutlinedButton.icon(
                        onPressed: _showServerInfo,
                        icon: const Icon(Icons.info),
                        label: const Text('Server Info'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Connection Information
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Connection Information',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2C3E50),
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildInfoRow(
                    'Server Address',
                    _activeServer?.ipAddress ?? 'Unknown',
                  ),
                  _buildInfoRow('Port', '${_activeServer?.port ?? 21}'),
                  _buildInfoRow('Protocol', 'FTP'),
                  _buildInfoRow(
                    'Connected Clients',
                    '${_activeServer?.connectedClients ?? 0}',
                  ),
                  _buildInfoRow('Shared Folders', '${_sharedPaths.length}'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSharedPathItem(String path) {
    return ListTile(
      leading: const Icon(Icons.folder, color: Color(0xFF3498DB)),
      title: Text(path.split('/').last),
      subtitle: Text(path),
      trailing: IconButton(
        icon: const Icon(Icons.remove_circle, color: Color(0xFFE74C3C)),
        onPressed: () => _removeSharedPath(path),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              color: Color(0xFF7F8C8D),
            ),
          ),
          Text(value, style: const TextStyle(color: Color(0xFF2C3E50))),
        ],
      ),
    );
  }

  bool _canStartServer() {
    return _sharedPaths.isNotEmpty &&
        _nameController.text.isNotEmpty &&
        _portController.text.isNotEmpty &&
        (!_requireAuth ||
            (_usernameController.text.isNotEmpty &&
                _passwordController.text.isNotEmpty));
  }

  Future<void> _loadServerStatus() async {
    try {
      final shares = await NetworkSharingService.getActiveShares();
      final ftpServer = shares
          .where((s) => s.protocol == ShareProtocol.ftp)
          .firstOrNull;

      if (mounted) {
        setState(() {
          _isServerRunning = ftpServer != null;
          _activeServer = ftpServer;
        });
      }
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _startServer() async {
    try {
      final port = int.tryParse(_portController.text) ?? 21;

      final server = await NetworkSharingService.startFTPServer(
        name: _nameController.text,
        sharedPaths: _sharedPaths,
        port: port,
        username: _requireAuth ? _usernameController.text : null,
        password: _requireAuth ? _passwordController.text : null,
      );

      setState(() {
        _isServerRunning = true;
        _activeServer = server;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('FTP Server started successfully'),
            backgroundColor: Color(0xFF27AE60),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to start FTP server: $e'),
            backgroundColor: const Color(0xFFE74C3C),
          ),
        );
      }
    }
  }

  Future<void> _stopServer() async {
    if (_activeServer != null) {
      try {
        await NetworkSharingService.stopShare(_activeServer!.id);

        setState(() {
          _isServerRunning = false;
          _activeServer = null;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('FTP Server stopped'),
              backgroundColor: Color(0xFF27AE60),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to stop FTP server: $e'),
              backgroundColor: const Color(0xFFE74C3C),
            ),
          );
        }
      }
    }
  }

  void _addSharedFolder() {
    // Implement folder picker functionality
    try {
      // In a real implementation, this would use file_picker package
      // For now, add a default shared path
      final newPath = '/storage/emulated/0/Documents';
      if (!_sharedPaths.contains(newPath)) {
        setState(() {
          _sharedPaths.add(newPath);
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Added shared path: $newPath')));
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error adding shared path: $e')));
    }
  }

  void _removeSharedPath(String path) {
    setState(() {
      _sharedPaths.remove(path);
    });
  }

  void _showServerInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('FTP Server Information'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Server Name: ${_activeServer?.name ?? _nameController.text}'),
            Text('Port: ${_activeServer?.port ?? _portController.text}'),
            Text('Status: ${_isServerRunning ? 'Running' : 'Stopped'}'),
            if (_isServerRunning) ...[
              Text('IP Address: ${_activeServer?.ipAddress ?? 'Unknown'}'),
              Text(
                'Connected Clients: ${_activeServer?.connectedClients ?? 0}',
              ),
            ],
            Text('Shared Folders: ${_sharedPaths.length}'),
            Text(
              'Authentication: ${_requireAuth ? 'Required' : (_allowAnonymous ? 'Anonymous' : 'None')}',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
