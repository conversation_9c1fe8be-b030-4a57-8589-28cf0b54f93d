import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'excel_to_app_main.dart';
import '../services/excel_app_providers.dart';
import '../models/excel_app_tool.dart';

class ExcelAppMyTools extends ConsumerStatefulWidget {
  const ExcelAppMyTools({super.key});

  @override
  ConsumerState<ExcelAppMyTools> createState() => _ExcelAppMyToolsState();
}

class _ExcelAppMyToolsState extends ConsumerState<ExcelAppMyTools> {
  String _searchQuery = '';

  // final List<String> _categories = [
  //   'All',
  //   'Business',
  //   'Finance',
  //   'Productivity',
  //   'Education',
  //   'Personal',
  //   'Other',
  // ];

  @override
  Widget build(BuildContext context) {
    final toolsAsync = ref.watch(excelAppToolsProvider);

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Column(
        children: [
          ExcelToAppHeader(
            title: 'My Tools',
            subtitle: 'Manage all your Excel-to-App tools',
            actions: [
              IconButton(
                onPressed: () => _showCreateToolDialog(),
                icon: const Icon(Icons.add, color: Colors.white),
                tooltip: 'Create New Tool',
              ),
            ],
          ),
          _buildFilterBar(),
          Expanded(
            child: toolsAsync.when(
              data: (tools) => _buildToolsContent(tools),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _buildErrorState(error),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Color(0xFFE9ECEF), width: 1)),
      ),
      child: TextField(
        onChanged: (value) => setState(() => _searchQuery = value),
        decoration: InputDecoration(
          hintText: 'Search tools...',
          prefixIcon: const Icon(Icons.search, color: Color(0xFF7F8C8D)),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Color(0xFFE9ECEF)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Color(0xFF3498DB)),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
      ),
    );
  }

  Widget _buildToolsContent(List<ExcelAppTool> tools) {
    final filteredTools = _filterTools(tools);

    if (filteredTools.isEmpty) {
      return _buildEmptyState();
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.2,
      ),
      itemCount: filteredTools.length,
      itemBuilder: (context, index) {
        return _buildToolCard(filteredTools[index]);
      },
    );
  }

  Widget _buildToolCard(ExcelAppTool tool) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _openTool(tool),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF3498DB).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.apps,
                      color: Color(0xFF3498DB),
                      size: 24,
                    ),
                  ),
                  const Spacer(),
                  PopupMenuButton<String>(
                    onSelected: (action) => _handleToolAction(action, tool),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 16),
                            SizedBox(width: 8),
                            Text('Edit'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 16, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Delete', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                    child: const Icon(
                      Icons.more_vert,
                      color: Color(0xFF7F8C8D),
                      size: 20,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Tool Name
              Text(
                tool.name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),

              // Tool Description
              Text(
                tool.description,
                style: const TextStyle(fontSize: 12, color: Color(0xFF7F8C8D)),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const Spacer(),

              // Footer Info
              Text(
                _formatDate(tool.lastModified),
                style: const TextStyle(fontSize: 10, color: Color(0xFF95A5A6)),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.folder_open, size: 64, color: Color(0xFFBDC3C7)),
          const SizedBox(height: 16),
          const Text(
            'No tools found',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Create your first Excel-to-App tool to get started',
            style: TextStyle(color: Color(0xFF7F8C8D)),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showCreateToolDialog(),
            icon: const Icon(Icons.add),
            label: const Text('Create Tool'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF3498DB),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Color(0xFFE74C3C)),
          const SizedBox(height: 16),
          const Text(
            'Error loading tools',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: const TextStyle(color: Color(0xFF7F8C8D)),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => ref.invalidate(excelAppToolsProvider),
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF3498DB),
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  List<ExcelAppTool> _filterTools(List<ExcelAppTool> tools) {
    return tools.where((tool) {
      final matchesSearch =
          _searchQuery.isEmpty ||
          tool.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          tool.description.toLowerCase().contains(_searchQuery.toLowerCase());

      return matchesSearch;
    }).toList();
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _openTool(ExcelAppTool tool) {
    ref.read(currentExcelAppToolProvider.notifier).setTool(tool);
    ref.read(excelToAppCurrentScreenProvider.notifier).state =
        ExcelToAppScreen.createTool;
  }

  void _handleToolAction(String action, ExcelAppTool tool) {
    switch (action) {
      case 'edit':
        _openTool(tool);
        break;
      case 'delete':
        _deleteTool(tool);
        break;
    }
  }

  void _deleteTool(ExcelAppTool tool) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Tool'),
        content: Text('Are you sure you want to delete "${tool.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ref.read(excelAppToolsProvider.notifier).deleteTool(tool.id);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showCreateToolDialog() {
    ref.read(excelToAppCurrentScreenProvider.notifier).state =
        ExcelToAppScreen.createTool;
  }
}
