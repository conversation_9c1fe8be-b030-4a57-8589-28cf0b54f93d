import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_component.dart';
import '../services/tools_builder_providers.dart';
import 'advanced_component_library.dart';

class ComponentPalette extends ConsumerWidget {
  final Function(ComponentType) onComponentSelected;

  const ComponentPalette({super.key, required this.onComponentSelected});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(right: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Components',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // Search bar
          TextField(
            decoration: InputDecoration(
              hintText: 'Search components...',
              prefixIcon: const Icon(Icons.search, size: 20),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Component categories
          Expanded(
            child: ListView(
              children: [
                _buildComponentCategory('Input Components', [
                  _ComponentInfo(
                    ComponentType.textInput,
                    Icons.text_fields,
                    'Text Input',
                  ),
                  _ComponentInfo(
                    ComponentType.numberInput,
                    Icons.numbers,
                    'Number Input',
                  ),
                  _ComponentInfo(
                    ComponentType.dropdown,
                    Icons.arrow_drop_down,
                    'Dropdown',
                  ),
                  _ComponentInfo(
                    ComponentType.checkbox,
                    Icons.check_box,
                    'Checkbox',
                  ),
                  _ComponentInfo(
                    ComponentType.radioButton,
                    Icons.radio_button_checked,
                    'Radio Button',
                  ),
                  _ComponentInfo(
                    ComponentType.slider,
                    Icons.linear_scale,
                    'Slider',
                  ),
                  _ComponentInfo(
                    ComponentType.dateInput,
                    Icons.date_range,
                    'Date Input',
                  ),
                  _ComponentInfo(
                    ComponentType.timeInput,
                    Icons.access_time,
                    'Time Input',
                  ),
                  _ComponentInfo(
                    ComponentType.textArea,
                    Icons.notes,
                    'Text Area',
                  ),
                ]),
                _buildComponentCategory('Display Components', [
                  _ComponentInfo(ComponentType.label, Icons.label, 'Label'),
                  _ComponentInfo(ComponentType.image, Icons.image, 'Image'),
                  _ComponentInfo(ComponentType.chart, Icons.bar_chart, 'Chart'),
                  _ComponentInfo(
                    ComponentType.table,
                    Icons.table_chart,
                    'Table',
                  ),
                ]),
                _buildComponentCategory('Layout Components', [
                  _ComponentInfo(
                    ComponentType.container,
                    Icons.crop_square,
                    'Container',
                  ),
                  _ComponentInfo(
                    ComponentType.divider,
                    Icons.horizontal_rule,
                    'Divider',
                  ),
                  _ComponentInfo(
                    ComponentType.spacer,
                    Icons.space_bar,
                    'Spacer',
                  ),
                ]),
                _buildComponentCategory('Action Components', [
                  _ComponentInfo(
                    ComponentType.button,
                    Icons.smart_button,
                    'Button',
                  ),
                ]),
                _buildComponentCategory('Advanced Components', [
                  _ComponentInfo(
                    ComponentType.progressBar,
                    Icons.linear_scale,
                    'Progress Bar',
                  ),
                  _ComponentInfo(
                    ComponentType.colorPicker,
                    Icons.palette,
                    'Color Picker',
                  ),
                  _ComponentInfo(
                    ComponentType.fileUpload,
                    Icons.cloud_upload,
                    'File Upload',
                  ),
                  _ComponentInfo(
                    ComponentType.richTextEditor,
                    Icons.text_format,
                    'Rich Text Editor',
                  ),
                  _ComponentInfo(
                    ComponentType.codeEditor,
                    Icons.code,
                    'Code Editor',
                  ),
                  _ComponentInfo(
                    ComponentType.calendar,
                    Icons.calendar_today,
                    'Calendar',
                  ),
                  _ComponentInfo(
                    ComponentType.dataTable,
                    Icons.table_chart,
                    'Data Table',
                  ),
                ]),
                _buildComponentCategory('Media Components', [
                  _ComponentInfo(ComponentType.map, Icons.map, 'Map'),
                  _ComponentInfo(
                    ComponentType.video,
                    Icons.video_library,
                    'Video Player',
                  ),
                  _ComponentInfo(
                    ComponentType.audio,
                    Icons.audio_file,
                    'Audio Player',
                  ),
                  _ComponentInfo(
                    ComponentType.qrCode,
                    Icons.qr_code,
                    'QR Code',
                  ),
                  _ComponentInfo(
                    ComponentType.barcode,
                    Icons.view_week,
                    'Barcode',
                  ),
                ]),
                _buildComponentCategory('Drawing Components', [
                  _ComponentInfo(
                    ComponentType.signature,
                    Icons.draw,
                    'Signature Pad',
                  ),
                  _ComponentInfo(
                    ComponentType.drawing,
                    Icons.brush,
                    'Drawing Canvas',
                  ),
                ]),
              ],
            ),
          ),

          const Divider(),

          // Quick actions
          _buildQuickActions(),
        ],
      ),
    );
  }

  Widget _buildComponentCategory(
    String title,
    List<_ComponentInfo> components,
  ) {
    return ExpansionTile(
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
      ),
      initiallyExpanded: true,
      children: components
          .map((component) => _buildComponentItem(component))
          .toList(),
    );
  }

  Widget _buildComponentItem(_ComponentInfo component) {
    return Draggable<ComponentType>(
      data: component.type,
      feedback: Material(
        elevation: 4,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(component.icon, size: 16, color: Colors.blue),
              const SizedBox(width: 8),
              Text(
                component.name,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
        ),
      ),
      childWhenDragging: Opacity(
        opacity: 0.5,
        child: _buildComponentTile(component),
      ),
      child: _buildComponentTile(component),
    );
  }

  Widget _buildComponentTile(_ComponentInfo component) {
    return ListTile(
      dense: true,
      leading: Icon(component.icon, size: 20),
      title: Text(component.name, style: const TextStyle(fontSize: 13)),
      onTap: () => onComponentSelected(component.type),
      hoverColor: Colors.grey[100],
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
        ),
        const SizedBox(height: 8),

        // Grid toggle
        Consumer(
          builder: (context, ref, child) {
            final showGrid = ref.watch(showGridProvider);
            return CheckboxListTile(
              dense: true,
              title: const Text('Show Grid', style: TextStyle(fontSize: 12)),
              value: showGrid,
              onChanged: (value) {
                ref.read(showGridProvider.notifier).state = value ?? false;
              },
            );
          },
        ),

        // Snap to grid toggle
        Consumer(
          builder: (context, ref, child) {
            final snapToGrid = ref.watch(snapToGridProvider);
            return CheckboxListTile(
              dense: true,
              title: const Text('Snap to Grid', style: TextStyle(fontSize: 12)),
              value: snapToGrid,
              onChanged: (value) {
                ref.read(snapToGridProvider.notifier).state = value ?? false;
              },
            );
          },
        ),

        // Grid size slider
        Consumer(
          builder: (context, ref, child) {
            final gridSize = ref.watch(gridSizeProvider);
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Grid Size: ${gridSize.toInt()}px',
                  style: const TextStyle(fontSize: 12),
                ),
                Slider(
                  value: gridSize,
                  min: 5,
                  max: 50,
                  divisions: 9,
                  onChanged: (value) {
                    ref.read(gridSizeProvider.notifier).state = value;
                  },
                ),
              ],
            );
          },
        ),

        const SizedBox(height: 8),

        // Clear all button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () {
              // Clear all components
              // This would need to be implemented in the parent widget
            },
            icon: const Icon(Icons.clear_all, size: 16),
            label: const Text('Clear All', style: TextStyle(fontSize: 12)),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[50],
              foregroundColor: Colors.red,
              elevation: 0,
            ),
          ),
        ),

        const SizedBox(height: 8),

        // Template buttons
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () {
              // Load template
            },
            icon: const Icon(Icons.library_books, size: 16),
            label: const Text('Load Template', style: TextStyle(fontSize: 12)),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[50],
              foregroundColor: Colors.blue,
              elevation: 0,
            ),
          ),
        ),

        const SizedBox(height: 4),

        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () {
              // Save as template
            },
            icon: const Icon(Icons.save, size: 16),
            label: const Text('Save Template', style: TextStyle(fontSize: 12)),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green[50],
              foregroundColor: Colors.green,
              elevation: 0,
            ),
          ),
        ),
      ],
    );
  }
}

class _ComponentInfo {
  final ComponentType type;
  final IconData icon;
  final String name;

  const _ComponentInfo(this.type, this.icon, this.name);
}

// Component preview dialog
class ComponentPreviewDialog extends StatelessWidget {
  final ComponentType componentType;

  const ComponentPreviewDialog({super.key, required this.componentType});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('${componentType.name.toUpperCase()} Preview'),
      content: SizedBox(
        width: 300,
        height: 200,
        child: Column(
          children: [
            Text(
              'This is how the ${componentType.name} component will look:',
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            Expanded(child: Center(child: _buildPreviewWidget(componentType))),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop();
            // Add component to canvas
          },
          child: const Text('Add to Canvas'),
        ),
      ],
    );
  }

  Widget _buildPreviewWidget(ComponentType type) {
    return AdvancedComponentLibrary.buildComponentPreview(type);
  }
}
