import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'screens/tools_builder_home.dart';
import '../../core/theme/app_theme.dart';

/// Standalone main entry point for Tools Builder app
void main() {
  runApp(const ProviderScope(child: ToolsBuilderStandaloneApp()));
}

/// Standalone Tools Builder application
class ToolsBuilderStandaloneApp extends StatelessWidget {
  const ToolsBuilderStandaloneApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Tools Builder',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      home: const ToolsBuilderStandaloneHome(),
      debugShowCheckedModeBanner: false,
    );
  }
}

/// Standalone home screen for Tools Builder
class ToolsBuilderStandaloneHome extends ConsumerStatefulWidget {
  const ToolsBuilderStandaloneHome({super.key});

  @override
  ConsumerState<ToolsBuilderStandaloneHome> createState() =>
      _ToolsBuilderStandaloneHomeState();
}

class _ToolsBuilderStandaloneHomeState
    extends ConsumerState<ToolsBuilderStandaloneHome> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tools Builder'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: const ToolsBuilderHome(),
    );
  }
}

/// Standalone launcher for Tools Builder with additional features
class ToolsBuilderLauncher extends ConsumerWidget {
  const ToolsBuilderLauncher({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 40),
                Text(
                  'Tools Builder',
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Create powerful tools and spreadsheets',
                  style: Theme.of(
                    context,
                  ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
                ),
                const SizedBox(height: 40),
                Expanded(
                  child: GridView.count(
                    crossAxisCount: 2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    children: [
                      _buildFeatureCard(
                        context,
                        'Spreadsheets',
                        'Create and edit spreadsheets with formulas',
                        Icons.table_chart,
                        Colors.green,
                        () => _navigateToSpreadsheets(context),
                      ),
                      _buildFeatureCard(
                        context,
                        'Templates',
                        'Browse and use pre-built templates',
                        Icons.dashboard_outlined,
                        Colors.blue,
                        () => _navigateToTemplates(context),
                      ),
                      _buildFeatureCard(
                        context,
                        'Tool Editor',
                        'Build custom tools and calculators',
                        Icons.build,
                        Colors.orange,
                        () => _navigateToToolEditor(context),
                      ),
                      _buildFeatureCard(
                        context,
                        'Settings',
                        'Configure app preferences',
                        Icons.settings,
                        Colors.grey,
                        () => _navigateToSettings(context),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => _navigateToMainApp(context),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Open Tools Builder',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureCard(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withValues(alpha: 0.1),
                color.withValues(alpha: 0.05),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 40, color: color),
              const SizedBox(height: 12),
              Text(
                title,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToMainApp(BuildContext context) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const ToolsBuilderStandaloneHome(),
      ),
    );
  }

  void _navigateToSpreadsheets(BuildContext context) {
    // Navigate to spreadsheets section
    _navigateToMainApp(context);
  }

  void _navigateToTemplates(BuildContext context) {
    // Navigate to templates section
    _navigateToMainApp(context);
  }

  void _navigateToToolEditor(BuildContext context) {
    // Navigate to tool editor section
    _navigateToMainApp(context);
  }

  void _navigateToSettings(BuildContext context) {
    // Navigate to settings section
    _navigateToMainApp(context);
  }
}
