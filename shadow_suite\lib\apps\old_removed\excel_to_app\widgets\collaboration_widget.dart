import 'package:flutter/material.dart';
import 'dart:async';
import '../services/collaboration_service.dart';

/// Real-time collaboration widget
class CollaborationWidget extends StatefulWidget {
  final String? currentFormula;
  final Map<String, dynamic>? currentLayout;
  final Function(String)? onFormulaReceived;
  final Function(Map<String, dynamic>)? onLayoutReceived;

  const CollaborationWidget({
    super.key,
    this.currentFormula,
    this.currentLayout,
    this.onFormulaReceived,
    this.onLayoutReceived,
  });

  @override
  State<CollaborationWidget> createState() => _CollaborationWidgetState();
}

class _CollaborationWidgetState extends State<CollaborationWidget>
    with TickerProviderStateMixin {
  late TabController _tabController;
  StreamSubscription<CollaborationEvent>? _eventSubscription;
  final TextEditingController _messageController = TextEditingController();
  final TextEditingController _sessionNameController = TextEditingController();
  final ScrollController _chatScrollController = ScrollController();

  CollaborationSession? _currentSession;
  List<CollaborationSession> _availableSessions = [];
  bool _isConnected = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    CollaborationService.initialize();
    _loadSessions();
    _subscribeToEvents();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _messageController.dispose();
    _sessionNameController.dispose();
    _chatScrollController.dispose();
    _eventSubscription?.cancel();
    super.dispose();
  }

  void _loadSessions() {
    setState(() {
      _availableSessions = CollaborationService.getActiveSessions();
      _currentSession = CollaborationService.currentSession;
      _isConnected = _currentSession != null;
    });
  }

  void _subscribeToEvents() {
    _eventSubscription = CollaborationService.events.listen((event) {
      setState(() {
        _loadSessions();
      });

      // Handle specific events
      switch (event.type) {
        case EventType.formulaShared:
          final formula = event.data['formula'] as String?;
          if (formula != null) {
            widget.onFormulaReceived?.call(formula);
            _showNotification('Formula shared: $formula');
          }
          break;
        case EventType.layoutShared:
          final layoutName = event.data['layoutName'] as String?;
          if (layoutName != null) {
            _showNotification('Layout shared: $layoutName');
          }
          break;
        case EventType.messageReceived:
          _scrollChatToBottom();
          break;
        case EventType.userJoined:
          final userName = event.data['userName'] as String?;
          if (userName != null) {
            _showNotification('$userName joined the session');
          }
          break;
        case EventType.userLeft:
          final userName = event.data['userName'] as String?;
          if (userName != null) {
            _showNotification('$userName left the session');
          }
          break;
        default:
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildSessionsTab(),
                _buildChatTab(),
                _buildSharedItemsTab(),
                _buildActivityTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                _isConnected ? Icons.people : Icons.people_outline,
                size: 24,
                color: _isConnected ? Colors.green : Colors.grey,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Collaboration',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    Text(
                      _isConnected
                          ? 'Connected to: ${_currentSession?.name}'
                          : 'Not connected',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
              if (_isConnected) ...[
                _buildParticipantAvatars(),
                IconButton(
                  onPressed: _leaveSession,
                  icon: const Icon(Icons.exit_to_app),
                  tooltip: 'Leave Session',
                ),
              ],
            ],
          ),
          const SizedBox(height: 16),
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(icon: Icon(Icons.group), text: 'Sessions'),
              Tab(icon: Icon(Icons.chat), text: 'Chat'),
              Tab(icon: Icon(Icons.share), text: 'Shared'),
              Tab(icon: Icon(Icons.timeline), text: 'Activity'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildParticipantAvatars() {
    if (_currentSession == null) return const SizedBox.shrink();

    final participants = _currentSession!.participants
        .map((id) => CollaborationService.getUser(id))
        .where((user) => user != null)
        .take(3)
        .toList();

    return Row(
      children: [
        ...participants.map(
          (user) => Padding(
            padding: const EdgeInsets.only(left: 4),
            child: CircleAvatar(
              radius: 16,
              backgroundColor: user?.isOnline == true
                  ? Colors.green[100]
                  : Colors.grey[300],
              child: Text(user!.avatar),
            ),
          ),
        ),
        if (_currentSession!.participants.length > 3)
          Padding(
            padding: const EdgeInsets.only(left: 4),
            child: CircleAvatar(
              radius: 16,
              backgroundColor: Colors.grey[300],
              child: Text('+${_currentSession!.participants.length - 3}'),
            ),
          ),
      ],
    );
  }

  Widget _buildSessionsTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _showCreateSessionDialog,
                  icon: const Icon(Icons.add),
                  label: const Text('Create Session'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _refreshSessions,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Refresh'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: _availableSessions.isEmpty
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.group_off, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text('No active sessions'),
                        Text('Create a new session to start collaborating'),
                      ],
                    ),
                  )
                : ListView.builder(
                    itemCount: _availableSessions.length,
                    itemBuilder: (context, index) {
                      return _buildSessionTile(_availableSessions[index]);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildSessionTile(CollaborationSession session) {
    final isCurrentSession = _currentSession?.id == session.id;
    final owner = CollaborationService.getUser(session.ownerId);

    return Card(
      color: isCurrentSession
          ? Theme.of(context).colorScheme.primaryContainer
          : null,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: isCurrentSession ? Colors.green : Colors.blue,
          child: Text('${session.participants.length}'),
        ),
        title: Text(session.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(session.description),
            const SizedBox(height: 4),
            Text(
              'Owner: ${owner?.name ?? 'Unknown'} • ${session.participants.length} participants',
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
        trailing: isCurrentSession
            ? const Icon(Icons.check_circle, color: Colors.green)
            : IconButton(
                onPressed: () => _joinSession(session.id),
                icon: const Icon(Icons.login),
              ),
        isThreeLine: true,
      ),
    );
  }

  Widget _buildChatTab() {
    if (!_isConnected) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('Join a session to start chatting'),
          ],
        ),
      );
    }

    final messages = _currentSession?.chatMessages ?? [];

    return Column(
      children: [
        Expanded(
          child: messages.isEmpty
              ? const Center(
                  child: Text('No messages yet. Start the conversation!'),
                )
              : ListView.builder(
                  controller: _chatScrollController,
                  itemCount: messages.length,
                  itemBuilder: (context, index) {
                    return _buildChatMessage(messages[index]);
                  },
                ),
        ),
        _buildChatInput(),
      ],
    );
  }

  Widget _buildChatMessage(ChatMessage message) {
    final sender = CollaborationService.getUser(message.senderId);
    final isCurrentUser =
        message.senderId == CollaborationService.currentUser?.id;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isCurrentUser) ...[
            CircleAvatar(radius: 16, child: Text(sender?.avatar ?? '?')),
            const SizedBox(width: 8),
          ],
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isCurrentUser
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (!isCurrentUser)
                    Text(
                      sender?.name ?? 'Unknown',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  Text(
                    message.message,
                    style: TextStyle(
                      color: isCurrentUser
                          ? Theme.of(context).colorScheme.onPrimary
                          : Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatTime(message.timestamp),
                    style: TextStyle(
                      fontSize: 10,
                      color: isCurrentUser
                          ? Theme.of(
                              context,
                            ).colorScheme.onPrimary.withValues(alpha: 0.7)
                          : Theme.of(context).colorScheme.onSurfaceVariant
                                .withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isCurrentUser) ...[
            const SizedBox(width: 8),
            CircleAvatar(radius: 16, child: Text(sender?.avatar ?? '?')),
          ],
        ],
      ),
    );
  }

  Widget _buildChatInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: const InputDecoration(
                hintText: 'Type a message...',
                border: OutlineInputBorder(),
              ),
              onSubmitted: (_) => _sendMessage(),
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: _sendMessage,
            icon: const Icon(Icons.send),
            style: IconButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSharedItemsTab() {
    if (!_isConnected) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.share_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('Join a session to view shared items'),
          ],
        ),
      );
    }

    final sharedItems = _currentSession?.sharedItems ?? [];

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: widget.currentFormula != null
                      ? _shareCurrentFormula
                      : null,
                  icon: const Icon(Icons.functions),
                  label: const Text('Share Formula'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: widget.currentLayout != null
                      ? _shareCurrentLayout
                      : null,
                  icon: const Icon(Icons.dashboard),
                  label: const Text('Share Layout'),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: sharedItems.isEmpty
              ? const Center(child: Text('No shared items yet'))
              : ListView.builder(
                  itemCount: sharedItems.length,
                  itemBuilder: (context, index) {
                    return _buildSharedItemTile(sharedItems[index]);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildSharedItemTile(SharedItem item) {
    final sharedBy = CollaborationService.getUser(item.sharedBy);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ExpansionTile(
        leading: Icon(_getSharedItemIcon(item.type)),
        title: Text(item.title),
        subtitle: Text(
          'Shared by ${sharedBy?.name ?? 'Unknown'} • ${_formatTime(item.sharedAt)}',
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Description:',
                  style: Theme.of(context).textTheme.titleSmall,
                ),
                const SizedBox(height: 4),
                Text(item.description),
                const SizedBox(height: 16),
                Text('Content:', style: Theme.of(context).textTheme.titleSmall),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    item.content,
                    style: const TextStyle(fontFamily: 'monospace'),
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    ElevatedButton.icon(
                      onPressed: () => _useSharedItem(item),
                      icon: const Icon(Icons.download),
                      label: const Text('Use'),
                    ),
                    const SizedBox(width: 16),
                    OutlinedButton.icon(
                      onPressed: () => _showCommentDialog(item),
                      icon: const Icon(Icons.comment),
                      label: Text('Comments (${item.comments.length})'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityTab() {
    final recentActivity = CollaborationService.getRecentActivity(limit: 50);

    return recentActivity.isEmpty
        ? const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.timeline, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text('No recent activity'),
              ],
            ),
          )
        : ListView.builder(
            itemCount: recentActivity.length,
            itemBuilder: (context, index) {
              return _buildActivityTile(recentActivity[index]);
            },
          );
  }

  Widget _buildActivityTile(CollaborationEvent event) {
    final user = CollaborationService.getUser(event.userId);

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: _getEventColor(event.type),
        child: Icon(_getEventIcon(event.type)),
      ),
      title: Text(_getEventDescription(event)),
      subtitle: Text(
        '${user?.name ?? 'Unknown'} • ${_formatTime(event.timestamp)}',
      ),
      dense: true,
    );
  }

  IconData _getSharedItemIcon(SharedItemType type) {
    switch (type) {
      case SharedItemType.formula:
        return Icons.functions;
      case SharedItemType.layout:
        return Icons.dashboard;
      case SharedItemType.data:
        return Icons.table_chart;
      case SharedItemType.template:
        return Icons.description_outlined;
    }
  }

  IconData _getEventIcon(EventType type) {
    switch (type) {
      case EventType.sessionCreated:
        return Icons.add_circle;
      case EventType.userJoined:
        return Icons.person_add;
      case EventType.userLeft:
        return Icons.person_remove;
      case EventType.formulaShared:
        return Icons.functions;
      case EventType.layoutShared:
        return Icons.dashboard;
      case EventType.messageReceived:
        return Icons.message;
      case EventType.commentAdded:
        return Icons.comment;
    }
  }

  Color _getEventColor(EventType type) {
    switch (type) {
      case EventType.sessionCreated:
        return Colors.green;
      case EventType.userJoined:
        return Colors.blue;
      case EventType.userLeft:
        return Colors.orange;
      case EventType.formulaShared:
        return Colors.purple;
      case EventType.layoutShared:
        return Colors.teal;
      case EventType.messageReceived:
        return Colors.grey;
      case EventType.commentAdded:
        return Colors.indigo;
    }
  }

  String _getEventDescription(CollaborationEvent event) {
    switch (event.type) {
      case EventType.sessionCreated:
        return 'Created session "${event.data['sessionName']}"';
      case EventType.userJoined:
        return 'Joined the session';
      case EventType.userLeft:
        return 'Left the session';
      case EventType.formulaShared:
        return 'Shared a formula';
      case EventType.layoutShared:
        return 'Shared layout "${event.data['layoutName']}"';
      case EventType.messageReceived:
        return 'Sent a message';
      case EventType.commentAdded:
        return 'Added a comment';
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  void _showCreateSessionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create Collaboration Session'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _sessionNameController,
              decoration: const InputDecoration(
                labelText: 'Session Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            const TextField(
              decoration: InputDecoration(
                labelText: 'Description',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: _createSession,
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  void _createSession() async {
    final name = _sessionNameController.text.trim();
    if (name.isEmpty) return;

    final currentUser = CollaborationService.currentUser;
    if (currentUser == null) return;

    try {
      final session = await CollaborationService.createSession(
        name: name,
        description: 'Collaboration session for Excel-to-App builder',
        ownerId: currentUser.id,
      );

      await CollaborationService.joinSession(session.id, currentUser.id);

      if (mounted) {
        Navigator.of(context).pop();
        _sessionNameController.clear();
        _loadSessions();
      }

      _showSnackBar('Session created successfully', Colors.green);
    } catch (e) {
      _showSnackBar('Failed to create session: $e', Colors.red);
    }
  }

  void _joinSession(String sessionId) async {
    final currentUser = CollaborationService.currentUser;
    if (currentUser == null) return;

    try {
      final success = await CollaborationService.joinSession(
        sessionId,
        currentUser.id,
      );
      if (success) {
        _loadSessions();
        _showSnackBar('Joined session successfully', Colors.green);
      } else {
        _showSnackBar('Failed to join session', Colors.red);
      }
    } catch (e) {
      _showSnackBar('Failed to join session: $e', Colors.red);
    }
  }

  void _leaveSession() async {
    if (_currentSession == null) return;

    final currentUser = CollaborationService.currentUser;
    if (currentUser == null) return;

    try {
      await CollaborationService.leaveSession(
        _currentSession!.id,
        currentUser.id,
      );
      _loadSessions();
      _showSnackBar('Left session', Colors.orange);
    } catch (e) {
      _showSnackBar('Failed to leave session: $e', Colors.red);
    }
  }

  void _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty || _currentSession == null) return;

    final currentUser = CollaborationService.currentUser;
    if (currentUser == null) return;

    try {
      await CollaborationService.sendMessage(
        sessionId: _currentSession!.id,
        userId: currentUser.id,
        message: message,
      );

      _messageController.clear();
      _scrollChatToBottom();
    } catch (e) {
      _showSnackBar('Failed to send message: $e', Colors.red);
    }
  }

  void _shareCurrentFormula() async {
    if (widget.currentFormula == null || _currentSession == null) return;

    final currentUser = CollaborationService.currentUser;
    if (currentUser == null) return;

    try {
      await CollaborationService.shareFormula(
        sessionId: _currentSession!.id,
        userId: currentUser.id,
        formula: widget.currentFormula!,
        description: 'Shared formula from Excel-to-App builder',
      );

      _showSnackBar('Formula shared successfully', Colors.green);
    } catch (e) {
      _showSnackBar('Failed to share formula: $e', Colors.red);
    }
  }

  void _shareCurrentLayout() async {
    if (widget.currentLayout == null || _currentSession == null) return;

    final currentUser = CollaborationService.currentUser;
    if (currentUser == null) return;

    try {
      await CollaborationService.shareLayout(
        sessionId: _currentSession!.id,
        userId: currentUser.id,
        layoutConfig: widget.currentLayout!,
        name: 'Custom Layout',
      );

      _showSnackBar('Layout shared successfully', Colors.green);
    } catch (e) {
      _showSnackBar('Failed to share layout: $e', Colors.red);
    }
  }

  void _useSharedItem(SharedItem item) {
    switch (item.type) {
      case SharedItemType.formula:
        widget.onFormulaReceived?.call(item.content);
        _showSnackBar('Formula applied', Colors.green);
        break;
      case SharedItemType.layout:
        try {
          final layoutData = Map<String, dynamic>.from(
            item.content as Map? ?? {},
          );
          widget.onLayoutReceived?.call(layoutData);
          _showSnackBar('Layout applied', Colors.green);
        } catch (e) {
          _showSnackBar('Failed to apply layout: $e', Colors.red);
        }
        break;
      default:
        _showSnackBar('Item type not supported yet', Colors.orange);
    }
  }

  void _showCommentDialog(SharedItem item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Comments for ${item.title}'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: Column(
            children: [
              Expanded(
                child: item.comments.isEmpty
                    ? const Center(child: Text('No comments yet'))
                    : ListView.builder(
                        itemCount: item.comments.length,
                        itemBuilder: (context, index) {
                          final comment = item.comments[index];
                          final author = CollaborationService.getUser(
                            comment.authorId,
                          );
                          return ListTile(
                            title: Text(comment.content),
                            subtitle: Text(
                              '${author?.name ?? 'Unknown'} • ${_formatTime(comment.timestamp)}',
                            ),
                          );
                        },
                      ),
              ),
              const Divider(),
              TextField(
                decoration: InputDecoration(
                  hintText: 'Add a comment...',
                  suffixIcon: IconButton(
                    onPressed: () => _addComment(item, 'Sample comment'),
                    icon: const Icon(Icons.send),
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _addComment(SharedItem item, String comment) async {
    if (_currentSession == null) return;

    final currentUser = CollaborationService.currentUser;
    if (currentUser == null) return;

    try {
      await CollaborationService.addComment(
        sessionId: _currentSession!.id,
        userId: currentUser.id,
        itemId: item.id,
        comment: comment,
      );

      if (mounted) {
        Navigator.of(context).pop();
        _showSnackBar('Comment added', Colors.green);
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar('Failed to add comment: $e', Colors.red);
      }
    }
  }

  void _refreshSessions() {
    _loadSessions();
    _showSnackBar('Sessions refreshed', Colors.blue);
  }

  void _scrollChatToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_chatScrollController.hasClients) {
        _chatScrollController.animateTo(
          _chatScrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _showNotification(String message) {
    _showSnackBar(message, Colors.blue);
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
