// import 'package:flutter/foundation.dart'; // Reserved for future debugging

// Loan Model
class Loan {
  final String id;
  final String name;
  final LoanType type;
  final double principalAmount;
  final double currentBalance;
  final double interestRate;
  final int termMonths;
  final double monthlyPayment;
  final DateTime startDate;
  final DateTime? maturityDate;
  final String lender;
  final LoanStatus status;
  final List<LoanPayment> payments;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final DateTime lastModified;

  const Loan({
    required this.id,
    required this.name,
    required this.type,
    required this.principalAmount,
    required this.currentBalance,
    required this.interestRate,
    required this.termMonths,
    required this.monthlyPayment,
    required this.startDate,
    this.maturityDate,
    required this.lender,
    required this.status,
    required this.payments,
    required this.metadata,
    required this.createdAt,
    required this.lastModified,
  });

  factory Loan.fromJson(Map<String, dynamic> json) {
    return Loan(
      id: json['id'] as String,
      name: json['name'] as String,
      type: LoanType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => LoanType.personal,
      ),
      principalAmount: (json['principal_amount'] as num).toDouble(),
      currentBalance: (json['current_balance'] as num).toDouble(),
      interestRate: (json['interest_rate'] as num).toDouble(),
      termMonths: json['term_months'] as int,
      monthlyPayment: (json['monthly_payment'] as num).toDouble(),
      startDate: DateTime.parse(json['start_date'] as String),
      maturityDate: json['maturity_date'] != null 
          ? DateTime.parse(json['maturity_date'] as String) 
          : null,
      lender: json['lender'] as String,
      status: LoanStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => LoanStatus.active,
      ),
      payments: (json['payments'] as List<dynamic>?)
          ?.map((e) => LoanPayment.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
      createdAt: DateTime.parse(json['created_at'] as String),
      lastModified: DateTime.parse(json['last_modified'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'principal_amount': principalAmount,
      'current_balance': currentBalance,
      'interest_rate': interestRate,
      'term_months': termMonths,
      'monthly_payment': monthlyPayment,
      'start_date': startDate.toIso8601String(),
      'maturity_date': maturityDate?.toIso8601String(),
      'lender': lender,
      'status': status.name,
      'payments': payments.map((e) => e.toJson()).toList(),
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'last_modified': lastModified.toIso8601String(),
    };
  }

  Loan copyWith({
    String? name,
    double? currentBalance,
    double? interestRate,
    LoanStatus? status,
    List<LoanPayment>? payments,
    Map<String, dynamic>? metadata,
  }) {
    return Loan(
      id: id,
      name: name ?? this.name,
      type: type,
      principalAmount: principalAmount,
      currentBalance: currentBalance ?? this.currentBalance,
      interestRate: interestRate ?? this.interestRate,
      termMonths: termMonths,
      monthlyPayment: monthlyPayment,
      startDate: startDate,
      maturityDate: maturityDate,
      lender: lender,
      status: status ?? this.status,
      payments: payments ?? this.payments,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt,
      lastModified: DateTime.now(),
    );
  }

  // Calculate remaining payments
  int get remainingPayments {
    final paidPayments = payments.where((p) => p.status == PaymentStatus.paid).length;
    return termMonths - paidPayments;
  }

  // Calculate total interest paid
  double get totalInterestPaid {
    return payments
        .where((p) => p.status == PaymentStatus.paid)
        .fold<double>(0, (sum, payment) => sum + payment.interestAmount);
  }

  // Calculate total principal paid
  double get totalPrincipalPaid {
    return payments
        .where((p) => p.status == PaymentStatus.paid)
        .fold<double>(0, (sum, payment) => sum + payment.principalAmount);
  }

  // Calculate next payment date
  DateTime? get nextPaymentDate {
    final lastPayment = payments.isNotEmpty 
        ? payments.reduce((a, b) => a.dueDate.isAfter(b.dueDate) ? a : b)
        : null;
    
    if (lastPayment != null) {
      return DateTime(lastPayment.dueDate.year, lastPayment.dueDate.month + 1, lastPayment.dueDate.day);
    }
    
    return DateTime(startDate.year, startDate.month + 1, startDate.day);
  }
}

// Loan Payment Model
class LoanPayment {
  final String id;
  final String loanId;
  final int paymentNumber;
  final double amount;
  final double principalAmount;
  final double interestAmount;
  final double remainingBalance;
  final DateTime dueDate;
  final DateTime? paidDate;
  final PaymentStatus status;
  final String? notes;
  final Map<String, dynamic> metadata;

  const LoanPayment({
    required this.id,
    required this.loanId,
    required this.paymentNumber,
    required this.amount,
    required this.principalAmount,
    required this.interestAmount,
    required this.remainingBalance,
    required this.dueDate,
    this.paidDate,
    required this.status,
    this.notes,
    required this.metadata,
  });

  factory LoanPayment.fromJson(Map<String, dynamic> json) {
    return LoanPayment(
      id: json['id'] as String,
      loanId: json['loan_id'] as String,
      paymentNumber: json['payment_number'] as int,
      amount: (json['amount'] as num).toDouble(),
      principalAmount: (json['principal_amount'] as num).toDouble(),
      interestAmount: (json['interest_amount'] as num).toDouble(),
      remainingBalance: (json['remaining_balance'] as num).toDouble(),
      dueDate: DateTime.parse(json['due_date'] as String),
      paidDate: json['paid_date'] != null 
          ? DateTime.parse(json['paid_date'] as String) 
          : null,
      status: PaymentStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => PaymentStatus.pending,
      ),
      notes: json['notes'] as String?,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'loan_id': loanId,
      'payment_number': paymentNumber,
      'amount': amount,
      'principal_amount': principalAmount,
      'interest_amount': interestAmount,
      'remaining_balance': remainingBalance,
      'due_date': dueDate.toIso8601String(),
      'paid_date': paidDate?.toIso8601String(),
      'status': status.name,
      'notes': notes,
      'metadata': metadata,
    };
  }

  LoanPayment copyWith({
    PaymentStatus? status,
    DateTime? paidDate,
    String? notes,
  }) {
    return LoanPayment(
      id: id,
      loanId: loanId,
      paymentNumber: paymentNumber,
      amount: amount,
      principalAmount: principalAmount,
      interestAmount: interestAmount,
      remainingBalance: remainingBalance,
      dueDate: dueDate,
      paidDate: paidDate ?? this.paidDate,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      metadata: metadata,
    );
  }

  bool get isOverdue {
    return status == PaymentStatus.pending && DateTime.now().isAfter(dueDate);
  }
}

// Financial Forecast Model
class FinancialForecast {
  final String id;
  final String name;
  final ForecastType type;
  final DateTime startDate;
  final DateTime endDate;
  final List<ForecastItem> items;
  final ForecastSummary summary;
  final Map<String, dynamic> assumptions;
  final DateTime createdAt;
  final DateTime lastModified;

  const FinancialForecast({
    required this.id,
    required this.name,
    required this.type,
    required this.startDate,
    required this.endDate,
    required this.items,
    required this.summary,
    required this.assumptions,
    required this.createdAt,
    required this.lastModified,
  });

  factory FinancialForecast.fromJson(Map<String, dynamic> json) {
    return FinancialForecast(
      id: json['id'] as String,
      name: json['name'] as String,
      type: ForecastType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ForecastType.cashFlow,
      ),
      startDate: DateTime.parse(json['start_date'] as String),
      endDate: DateTime.parse(json['end_date'] as String),
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => ForecastItem.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      summary: ForecastSummary.fromJson(json['summary'] as Map<String, dynamic>),
      assumptions: Map<String, dynamic>.from(json['assumptions'] as Map? ?? {}),
      createdAt: DateTime.parse(json['created_at'] as String),
      lastModified: DateTime.parse(json['last_modified'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'items': items.map((e) => e.toJson()).toList(),
      'summary': summary.toJson(),
      'assumptions': assumptions,
      'created_at': createdAt.toIso8601String(),
      'last_modified': lastModified.toIso8601String(),
    };
  }
}

// Forecast Item Model
class ForecastItem {
  final String id;
  final String forecastId;
  final DateTime date;
  final String category;
  final String description;
  final double amount;
  final ForecastItemType type;
  final double confidence;
  final Map<String, dynamic> metadata;

  const ForecastItem({
    required this.id,
    required this.forecastId,
    required this.date,
    required this.category,
    required this.description,
    required this.amount,
    required this.type,
    required this.confidence,
    required this.metadata,
  });

  factory ForecastItem.fromJson(Map<String, dynamic> json) {
    return ForecastItem(
      id: json['id'] as String,
      forecastId: json['forecast_id'] as String,
      date: DateTime.parse(json['date'] as String),
      category: json['category'] as String,
      description: json['description'] as String,
      amount: (json['amount'] as num).toDouble(),
      type: ForecastItemType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ForecastItemType.income,
      ),
      confidence: (json['confidence'] as num).toDouble(),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'forecast_id': forecastId,
      'date': date.toIso8601String(),
      'category': category,
      'description': description,
      'amount': amount,
      'type': type.name,
      'confidence': confidence,
      'metadata': metadata,
    };
  }
}

// Forecast Summary Model
class ForecastSummary {
  final double totalIncome;
  final double totalExpenses;
  final double netCashFlow;
  final double averageMonthlyIncome;
  final double averageMonthlyExpenses;
  final double projectedSavings;
  final double confidenceScore;
  final List<String> keyInsights;
  final List<String> recommendations;

  const ForecastSummary({
    required this.totalIncome,
    required this.totalExpenses,
    required this.netCashFlow,
    required this.averageMonthlyIncome,
    required this.averageMonthlyExpenses,
    required this.projectedSavings,
    required this.confidenceScore,
    required this.keyInsights,
    required this.recommendations,
  });

  factory ForecastSummary.fromJson(Map<String, dynamic> json) {
    return ForecastSummary(
      totalIncome: (json['total_income'] as num).toDouble(),
      totalExpenses: (json['total_expenses'] as num).toDouble(),
      netCashFlow: (json['net_cash_flow'] as num).toDouble(),
      averageMonthlyIncome: (json['average_monthly_income'] as num).toDouble(),
      averageMonthlyExpenses: (json['average_monthly_expenses'] as num).toDouble(),
      projectedSavings: (json['projected_savings'] as num).toDouble(),
      confidenceScore: (json['confidence_score'] as num).toDouble(),
      keyInsights: List<String>.from(json['key_insights'] as List? ?? []),
      recommendations: List<String>.from(json['recommendations'] as List? ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_income': totalIncome,
      'total_expenses': totalExpenses,
      'net_cash_flow': netCashFlow,
      'average_monthly_income': averageMonthlyIncome,
      'average_monthly_expenses': averageMonthlyExpenses,
      'projected_savings': projectedSavings,
      'confidence_score': confidenceScore,
      'key_insights': keyInsights,
      'recommendations': recommendations,
    };
  }
}

// Enums
enum LoanType {
  personal,
  mortgage,
  auto,
  student,
  business,
  creditCard,
  lineOfCredit,
}

enum LoanStatus {
  active,
  paid,
  defaulted,
  deferred,
  refinanced,
}

enum PaymentStatus {
  pending,
  paid,
  overdue,
  partial,
  skipped,
}

enum ForecastType {
  cashFlow,
  budget,
  investment,
  retirement,
  debt,
}

enum ForecastItemType {
  income,
  expense,
  investment,
  debt,
  tax,
}
