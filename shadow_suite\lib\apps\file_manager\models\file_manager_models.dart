import 'dart:io';

// File System Item Model
class FileSystemItem {
  final String path;
  final String name;
  final FileSystemItemType type;
  final int size;
  final DateTime lastModified;
  final DateTime lastAccessed;
  final bool isHidden;
  final bool isReadOnly;
  final String? mimeType;
  final String? extension;
  final FileSystemItem? parent;

  const FileSystemItem({
    required this.path,
    required this.name,
    required this.type,
    required this.size,
    required this.lastModified,
    required this.lastAccessed,
    required this.isHidden,
    required this.isReadOnly,
    this.mimeType,
    this.extension,
    this.parent,
  });

  factory FileSystemItem.fromFileSystemEntity(FileSystemEntity entity) {
    final stat = entity.statSync();
    final isDirectory = entity is Directory;
    final name = entity.path.split(Platform.pathSeparator).last;

    return FileSystemItem(
      path: entity.path,
      name: name,
      type: isDirectory
          ? FileSystemItemType.directory
          : FileSystemItemType.file,
      size: stat.size,
      lastModified: stat.modified,
      lastAccessed: stat.accessed,
      isHidden: name.startsWith('.'),
      isReadOnly: stat.mode & 0x80 == 0, // Check write permission
      mimeType: isDirectory ? null : _getMimeType(name),
      extension: isDirectory ? null : _getExtension(name),
    );
  }

  static String? _getMimeType(String fileName) {
    final extension = _getExtension(fileName)?.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'mp4':
        return 'video/mp4';
      case 'mp3':
        return 'audio/mpeg';
      case 'pdf':
        return 'application/pdf';
      case 'txt':
        return 'text/plain';
      case 'zip':
        return 'application/zip';
      case 'rar':
        return 'application/x-rar-compressed';
      case '7z':
        return 'application/x-7z-compressed';
      default:
        return 'application/octet-stream';
    }
  }

  static String? _getExtension(String fileName) {
    final lastDot = fileName.lastIndexOf('.');
    return lastDot != -1 ? fileName.substring(lastDot + 1) : null;
  }

  bool get isDirectory => type == FileSystemItemType.directory;
  bool get isFile => type == FileSystemItemType.file;
  bool get isSymlink => type == FileSystemItemType.symlink;
  bool get isImage => mimeType?.startsWith('image/') ?? false;
  bool get isVideo => mimeType?.startsWith('video/') ?? false;
  bool get isAudio => mimeType?.startsWith('audio/') ?? false;
  bool get isDocument => _isDocument();
  bool get isArchive => _isArchive();
  bool get isEbook => _isEbook();

  bool _isDocument() {
    if (mimeType == null) return false;
    return mimeType!.contains('pdf') ||
        mimeType!.contains('word') ||
        mimeType!.contains('excel') ||
        mimeType!.contains('powerpoint') ||
        extension == 'doc' ||
        extension == 'docx' ||
        extension == 'xls' ||
        extension == 'xlsx' ||
        extension == 'ppt' ||
        extension == 'pptx';
  }

  bool _isArchive() {
    return extension == 'zip' ||
        extension == 'rar' ||
        extension == '7z' ||
        extension == 'tar' ||
        extension == 'gz';
  }

  bool _isEbook() {
    return extension == 'epub' ||
        extension == 'mobi' ||
        extension == 'azw' ||
        extension == 'azw3';
  }
}

// File Operation Model
class FileOperation {
  final String id;
  final FileOperationType type;
  final String sourcePath;
  final String? destinationPath;
  FileOperationStatus status;
  double progress;
  int totalBytes;
  int processedBytes;
  final DateTime startTime;
  DateTime? endTime;
  String? errorMessage;

  FileOperation({
    required this.id,
    required this.type,
    required this.sourcePath,
    this.destinationPath,
    required this.status,
    required this.progress,
    required this.totalBytes,
    required this.processedBytes,
    required this.startTime,
    this.endTime,
    this.errorMessage,
  });

  factory FileOperation.fromJson(Map<String, dynamic> json) {
    return FileOperation(
      id: json['id'] as String,
      type: FileOperationType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => FileOperationType.copy,
      ),
      sourcePath: json['source_path'] as String,
      destinationPath: json['destination_path'] as String?,
      status: FileOperationStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => FileOperationStatus.pending,
      ),
      progress: (json['progress'] as num).toDouble(),
      totalBytes: json['total_bytes'] as int,
      processedBytes: json['processed_bytes'] as int,
      startTime: DateTime.parse(json['start_time'] as String),
      endTime: json['end_time'] != null
          ? DateTime.parse(json['end_time'] as String)
          : null,
      errorMessage: json['error_message'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'source_path': sourcePath,
      'destination_path': destinationPath,
      'status': status.name,
      'progress': progress,
      'total_bytes': totalBytes,
      'processed_bytes': processedBytes,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'error_message': errorMessage,
    };
  }
}

// Network Share Model
class NetworkShare {
  final String id;
  final String name;
  final String ipAddress;
  final int port;
  final ShareProtocol protocol;
  final bool isSecure;
  final String? username;
  final String? password;
  final ShareStatus status;
  final DateTime lastConnected;
  final List<String> sharedPaths;
  final int connectedClients;
  final bool isActive;

  const NetworkShare({
    required this.id,
    required this.name,
    required this.ipAddress,
    required this.port,
    required this.protocol,
    required this.isSecure,
    this.username,
    this.password,
    required this.status,
    required this.lastConnected,
    required this.sharedPaths,
    this.connectedClients = 0,
    this.isActive = false,
  });

  factory NetworkShare.fromJson(Map<String, dynamic> json) {
    return NetworkShare(
      id: json['id'] as String,
      name: json['name'] as String,
      ipAddress: json['ip_address'] as String,
      port: json['port'] as int,
      protocol: ShareProtocol.values.firstWhere(
        (e) => e.name == json['protocol'],
        orElse: () => ShareProtocol.ftp,
      ),
      isSecure: json['is_secure'] as bool,
      username: json['username'] as String?,
      password: json['password'] as String?,
      status: ShareStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ShareStatus.disconnected,
      ),
      lastConnected: DateTime.parse(json['last_connected'] as String),
      sharedPaths: List<String>.from(json['shared_paths'] as List? ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'ip_address': ipAddress,
      'port': port,
      'protocol': protocol.name,
      'is_secure': isSecure,
      'username': username,
      'password': password,
      'status': status.name,
      'last_connected': lastConnected.toIso8601String(),
      'shared_paths': sharedPaths,
    };
  }
}

// File Transfer Model
class FileTransfer {
  final String id;
  final String fileName;
  final String sourcePath;
  final String destinationPath;
  final TransferDirection direction;
  final TransferStatus status;
  final int totalBytes;
  final int transferredBytes;
  final double speed; // bytes per second
  final DateTime startTime;
  final DateTime? endTime;
  final String? errorMessage;

  const FileTransfer({
    required this.id,
    required this.fileName,
    required this.sourcePath,
    required this.destinationPath,
    required this.direction,
    required this.status,
    required this.totalBytes,
    required this.transferredBytes,
    required this.speed,
    required this.startTime,
    this.endTime,
    this.errorMessage,
  });

  double get progress => totalBytes > 0 ? transferredBytes / totalBytes : 0.0;

  Duration? get estimatedTimeRemaining {
    if (speed <= 0 || status != TransferStatus.transferring) return null;
    final remainingBytes = totalBytes - transferredBytes;
    final remainingSeconds = remainingBytes / speed;
    return Duration(seconds: remainingSeconds.round());
  }
}

// Archive Entry Model
class ArchiveEntry {
  final String name;
  final String path;
  final int compressedSize;
  final int uncompressedSize;
  final DateTime lastModified;
  final bool isDirectory;
  final String? comment;

  const ArchiveEntry({
    required this.name,
    required this.path,
    required this.compressedSize,
    required this.uncompressedSize,
    required this.lastModified,
    required this.isDirectory,
    this.comment,
  });

  double get compressionRatio {
    return uncompressedSize > 0 ? compressedSize / uncompressedSize : 0.0;
  }
}

// Enums
enum FileSystemItemType { file, directory, symlink }

enum FileOperationType { copy, move, delete, rename, compress, extract }

enum FileOperationStatus { pending, inProgress, completed, failed, cancelled }

enum ShareProtocol { ftp, sftp, smb, webdav, http }

enum ShareStatus { connected, connecting, disconnected, error }

enum TransferDirection { upload, download }

enum TransferStatus {
  pending,
  transferring,
  completed,
  failed,
  cancelled,
  paused,
}

// File Manager Change Event
class FileManagerChangeEvent {
  final FileManagerChangeType type;
  final String? path;
  final FileSystemItem? item;
  final DateTime timestamp;

  const FileManagerChangeEvent({
    required this.type,
    this.path,
    this.item,
    required this.timestamp,
  });
}

enum FileManagerChangeType {
  directoryChanged,
  fileCreated,
  fileDeleted,
  fileModified,
  operationStarted,
  operationCompleted,
  transferStarted,
  transferCompleted,
}
