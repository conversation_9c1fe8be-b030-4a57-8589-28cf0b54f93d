import 'dart:convert';

// Enums
enum AccountType { 
  checking, 
  savings, 
  credit, 
  cash, 
  investment, 
  loan 
}

enum TransactionType { 
  income, 
  expense, 
  transfer 
}

enum CategoryType { 
  income, 
  expense 
}

enum BudgetPeriod { 
  weekly, 
  monthly, 
  quarterly, 
  yearly 
}

enum GoalType { 
  saving, 
  debt, 
  investment, 
  purchase 
}

enum GoalStatus { 
  active, 
  completed, 
  paused, 
  cancelled 
}

// Account Model
class FinanceAccount {
  final String id;
  final String name;
  final AccountType type;
  final String bankName;
  final String accountNumber;
  final double initialBalance;
  final double currentBalance;
  final String currency;
  final String color;
  final String icon;
  final bool showInTotal;
  final bool allowNegative;
  final bool isActive;
  final String? description;
  final DateTime createdAt;
  final DateTime lastModified;

  const FinanceAccount({
    required this.id,
    required this.name,
    required this.type,
    this.bankName = '',
    this.accountNumber = '',
    required this.initialBalance,
    required this.currentBalance,
    this.currency = 'USD',
    this.color = '#3498DB',
    this.icon = 'account_balance_wallet',
    this.showInTotal = true,
    this.allowNegative = false,
    this.isActive = true,
    this.description,
    required this.createdAt,
    required this.lastModified,
  });

  FinanceAccount copyWith({
    String? id,
    String? name,
    AccountType? type,
    String? bankName,
    String? accountNumber,
    double? initialBalance,
    double? currentBalance,
    String? currency,
    String? color,
    String? icon,
    bool? showInTotal,
    bool? allowNegative,
    bool? isActive,
    String? description,
    DateTime? createdAt,
    DateTime? lastModified,
  }) {
    return FinanceAccount(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      bankName: bankName ?? this.bankName,
      accountNumber: accountNumber ?? this.accountNumber,
      initialBalance: initialBalance ?? this.initialBalance,
      currentBalance: currentBalance ?? this.currentBalance,
      currency: currency ?? this.currency,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      showInTotal: showInTotal ?? this.showInTotal,
      allowNegative: allowNegative ?? this.allowNegative,
      isActive: isActive ?? this.isActive,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      lastModified: lastModified ?? this.lastModified,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'bankName': bankName,
      'accountNumber': accountNumber,
      'initialBalance': initialBalance,
      'currentBalance': currentBalance,
      'currency': currency,
      'color': color,
      'icon': icon,
      'showInTotal': showInTotal,
      'allowNegative': allowNegative,
      'isActive': isActive,
      'description': description,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastModified': lastModified.millisecondsSinceEpoch,
    };
  }

  factory FinanceAccount.fromMap(Map<String, dynamic> map) {
    return FinanceAccount(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      type: AccountType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => AccountType.checking,
      ),
      bankName: map['bankName'] ?? '',
      accountNumber: map['accountNumber'] ?? '',
      initialBalance: (map['initialBalance'] ?? 0).toDouble(),
      currentBalance: (map['currentBalance'] ?? 0).toDouble(),
      currency: map['currency'] ?? 'USD',
      color: map['color'] ?? '#3498DB',
      icon: map['icon'] ?? 'account_balance_wallet',
      showInTotal: map['showInTotal'] ?? true,
      allowNegative: map['allowNegative'] ?? false,
      isActive: map['isActive'] ?? true,
      description: map['description'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      lastModified: DateTime.fromMillisecondsSinceEpoch(map['lastModified'] ?? 0),
    );
  }

  String toJson() => json.encode(toMap());
  factory FinanceAccount.fromJson(String source) => 
      FinanceAccount.fromMap(json.decode(source));
}

// Transaction Model
class FinanceTransaction {
  final String id;
  final double amount;
  final TransactionType type;
  final String accountId;
  final String? toAccountId; // For transfers
  final String categoryId;
  final String description;
  final DateTime date;
  final List<String> tags;
  final String? receiptPath;
  final String? notes;
  final bool isRecurring;
  final String? recurringPattern;
  final DateTime createdAt;

  const FinanceTransaction({
    required this.id,
    required this.amount,
    required this.type,
    required this.accountId,
    this.toAccountId,
    required this.categoryId,
    this.description = '',
    required this.date,
    this.tags = const [],
    this.receiptPath,
    this.notes,
    this.isRecurring = false,
    this.recurringPattern,
    required this.createdAt,
  });

  FinanceTransaction copyWith({
    String? id,
    double? amount,
    TransactionType? type,
    String? accountId,
    String? toAccountId,
    String? categoryId,
    String? description,
    DateTime? date,
    List<String>? tags,
    String? receiptPath,
    String? notes,
    bool? isRecurring,
    String? recurringPattern,
    DateTime? createdAt,
  }) {
    return FinanceTransaction(
      id: id ?? this.id,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      accountId: accountId ?? this.accountId,
      toAccountId: toAccountId ?? this.toAccountId,
      categoryId: categoryId ?? this.categoryId,
      description: description ?? this.description,
      date: date ?? this.date,
      tags: tags ?? this.tags,
      receiptPath: receiptPath ?? this.receiptPath,
      notes: notes ?? this.notes,
      isRecurring: isRecurring ?? this.isRecurring,
      recurringPattern: recurringPattern ?? this.recurringPattern,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'amount': amount,
      'type': type.name,
      'accountId': accountId,
      'toAccountId': toAccountId,
      'categoryId': categoryId,
      'description': description,
      'date': date.millisecondsSinceEpoch,
      'tags': tags,
      'receiptPath': receiptPath,
      'notes': notes,
      'isRecurring': isRecurring,
      'recurringPattern': recurringPattern,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  factory FinanceTransaction.fromMap(Map<String, dynamic> map) {
    return FinanceTransaction(
      id: map['id'] ?? '',
      amount: (map['amount'] ?? 0).toDouble(),
      type: TransactionType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => TransactionType.expense,
      ),
      accountId: map['accountId'] ?? '',
      toAccountId: map['toAccountId'],
      categoryId: map['categoryId'] ?? '',
      description: map['description'] ?? '',
      date: DateTime.fromMillisecondsSinceEpoch(map['date'] ?? 0),
      tags: List<String>.from(map['tags'] ?? []),
      receiptPath: map['receiptPath'],
      notes: map['notes'],
      isRecurring: map['isRecurring'] ?? false,
      recurringPattern: map['recurringPattern'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
    );
  }

  String toJson() => json.encode(toMap());
  factory FinanceTransaction.fromJson(String source) => 
      FinanceTransaction.fromMap(json.decode(source));
}
