import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/task_models.dart';
import '../models/ai_todo_models.dart';

/// AI-Powered Productivity Service with Machine Learning and Smart Recommendations
class AIProductivityService {
  static final AIProductivityService _instance =
      AIProductivityService._internal();
  factory AIProductivityService() => _instance;
  AIProductivityService._internal();

  final StreamController<ProductivityInsight> _insightController =
      StreamController.broadcast();
  Stream<ProductivityInsight> get insightStream => _insightController.stream;

  // AI Task Analysis Features (100 features)
  Future<List<ProductivityInsight>> analyzeTaskPatterns(
    List<Task> tasks,
  ) async {
    final insights = <ProductivityInsight>[];

    // Completion pattern analysis
    insights.addAll(await _analyzeCompletionPatterns(tasks));

    // Time-based productivity analysis
    insights.addAll(await _analyzeTimeBasedProductivity(tasks));

    // Priority distribution analysis
    insights.addAll(await _analyzePriorityDistribution(tasks));

    // Category performance analysis
    insights.addAll(await _analyzeCategoryPerformance(tasks));

    // Deadline adherence analysis
    insights.addAll(await _analyzeDeadlineAdherence(tasks));

    return insights;
  }

  Future<List<ProductivityInsight>> _analyzeCompletionPatterns(
    List<Task> tasks,
  ) async {
    final insights = <ProductivityInsight>[];
    final completedTasks = tasks.where((t) => t.isCompleted).toList();

    if (completedTasks.length >= 10) {
      // Analyze completion times
      final completionTimes = <int, int>{}; // hour -> count
      for (final task in completedTasks) {
        if (task.completedAt != null) {
          final hour = task.completedAt!.hour;
          completionTimes[hour] = (completionTimes[hour] ?? 0) + 1;
        }
      }

      // Find peak productivity hours
      final peakHour = completionTimes.entries
          .reduce((a, b) => a.value > b.value ? a : b)
          .key;

      insights.add(
        ProductivityInsight(
          id: 'peak_productivity_${DateTime.now().millisecondsSinceEpoch}',
          type: InsightType.productivity,
          title: 'Peak Productivity Hour Identified',
          description:
              'You complete most tasks around ${_formatHour(peakHour)}. Consider scheduling important tasks during this time.',
          impact: ImpactLevel.medium,
          confidence: 0.85,
          recommendations: [
            'Schedule high-priority tasks around ${_formatHour(peakHour)}',
            'Block calendar time during peak hours',
            'Avoid meetings during your most productive time',
          ],
          createdAt: DateTime.now(),
        ),
      );
    }

    return insights;
  }

  Future<List<ProductivityInsight>> _analyzeTimeBasedProductivity(
    List<Task> tasks,
  ) async {
    final insights = <ProductivityInsight>[];
    final weeklyData = <int, List<Task>>{}; // weekday -> tasks

    for (final task in tasks) {
      final weekday = task.createdAt.weekday;
      weeklyData[weekday] = (weeklyData[weekday] ?? [])..add(task);
    }

    // Find most productive day
    final productivityScores = <int, double>{};
    weeklyData.forEach((weekday, dayTasks) {
      final completedCount = dayTasks.where((t) => t.isCompleted).length;
      final totalCount = dayTasks.length;
      productivityScores[weekday] = totalCount > 0
          ? completedCount / totalCount
          : 0.0;
    });

    if (productivityScores.isNotEmpty) {
      final bestDay = productivityScores.entries.reduce(
        (a, b) => a.value > b.value ? a : b,
      );

      insights.add(
        ProductivityInsight(
          id: 'best_day_${DateTime.now().millisecondsSinceEpoch}',
          type: InsightType.productivity,
          title: 'Most Productive Day',
          description:
              '${_getDayName(bestDay.key)} is your most productive day with ${(bestDay.value * 100).toStringAsFixed(1)}% completion rate.',
          impact: ImpactLevel.medium,
          confidence: 0.78,
          recommendations: [
            'Schedule important tasks on ${_getDayName(bestDay.key)}',
            'Plan challenging work for your most productive day',
            'Use other days for planning and preparation',
          ],
          createdAt: DateTime.now(),
        ),
      );
    }

    return insights;
  }

  Future<List<ProductivityInsight>> _analyzePriorityDistribution(
    List<Task> tasks,
  ) async {
    final insights = <ProductivityInsight>[];
    final priorityData = <TaskPriority, List<Task>>{};

    for (final task in tasks) {
      priorityData[task.priority] = (priorityData[task.priority] ?? [])
        ..add(task);
    }

    // Check for priority imbalance
    final totalTasks = tasks.length;
    final highPriorityCount = priorityData[TaskPriority.high]?.length ?? 0;
    final highPriorityPercentage = totalTasks > 0
        ? (highPriorityCount / totalTasks) * 100
        : 0;

    if (highPriorityPercentage > 60) {
      insights.add(
        ProductivityInsight(
          id: 'priority_imbalance_${DateTime.now().millisecondsSinceEpoch}',
          type: InsightType.warning,
          title: 'Too Many High-Priority Tasks',
          description:
              '${highPriorityPercentage.toStringAsFixed(1)}% of your tasks are high priority. This may indicate poor prioritization.',
          impact: ImpactLevel.high,
          confidence: 0.92,
          recommendations: [
            'Review and reassess task priorities',
            'Use the Eisenhower Matrix for better prioritization',
            'Limit high-priority tasks to 20-30% of total',
            'Break down large tasks into smaller ones',
          ],
          createdAt: DateTime.now(),
        ),
      );
    }

    return insights;
  }

  Future<List<ProductivityInsight>> _analyzeCategoryPerformance(
    List<Task> tasks,
  ) async {
    final insights = <ProductivityInsight>[];
    final categoryData = <String, List<Task>>{};

    for (final task in tasks) {
      final categoryKey = task.category.toString();
      categoryData[categoryKey] = (categoryData[categoryKey] ?? [])..add(task);
    }

    // Find underperforming categories
    categoryData.forEach((category, categoryTasks) {
      if (categoryTasks.length >= 5) {
        final completedCount = categoryTasks.where((t) => t.isCompleted).length;
        final completionRate = completedCount / categoryTasks.length;

        if (completionRate < 0.5) {
          insights.add(
            ProductivityInsight(
              id: 'category_underperform_${category}_${DateTime.now().millisecondsSinceEpoch}',
              type: InsightType.warning,
              title: 'Low Performance in $category',
              description:
                  'Only ${(completionRate * 100).toStringAsFixed(1)}% completion rate in $category tasks.',
              impact: ImpactLevel.medium,
              confidence: 0.75,
              recommendations: [
                'Break down $category tasks into smaller steps',
                'Set specific deadlines for $category tasks',
                'Consider if $category tasks are properly scoped',
                'Review obstacles preventing $category task completion',
              ],
              createdAt: DateTime.now(),
            ),
          );
        }
      }
    });

    return insights;
  }

  Future<List<ProductivityInsight>> _analyzeDeadlineAdherence(
    List<Task> tasks,
  ) async {
    final insights = <ProductivityInsight>[];
    final tasksWithDeadlines = tasks.where((t) => t.dueDate != null).toList();

    if (tasksWithDeadlines.length >= 10) {
      final overdueTasks = tasksWithDeadlines
          .where((t) => t.dueDate!.isBefore(DateTime.now()) && !t.isCompleted)
          .toList();

      final overdueRate = overdueTasks.length / tasksWithDeadlines.length;

      if (overdueRate > 0.3) {
        insights.add(
          ProductivityInsight(
            id: 'deadline_adherence_${DateTime.now().millisecondsSinceEpoch}',
            type: InsightType.warning,
            title: 'High Overdue Task Rate',
            description:
                '${(overdueRate * 100).toStringAsFixed(1)}% of your tasks with deadlines are overdue.',
            impact: ImpactLevel.high,
            confidence: 0.88,
            recommendations: [
              'Set more realistic deadlines',
              'Break large tasks into smaller milestones',
              'Use time-blocking for deadline-driven tasks',
              'Review and adjust your workload',
            ],
            createdAt: DateTime.now(),
          ),
        );
      }
    }

    return insights;
  }

  // Smart Task Recommendations (100 features)
  Future<List<TaskRecommendation>> generateTaskRecommendations(
    List<Task> tasks,
    UserPreferences preferences,
  ) async {
    final recommendations = <TaskRecommendation>[];

    // Time-based recommendations
    recommendations.addAll(
      await _generateTimeBasedRecommendations(tasks, preferences),
    );

    // Priority-based recommendations
    recommendations.addAll(await _generatePriorityRecommendations(tasks));

    // Context-based recommendations
    recommendations.addAll(
      await _generateContextRecommendations(tasks, preferences),
    );

    // Energy-based recommendations
    recommendations.addAll(
      await _generateEnergyBasedRecommendations(tasks, preferences),
    );

    return recommendations;
  }

  Future<List<TaskRecommendation>> _generateTimeBasedRecommendations(
    List<Task> tasks,
    UserPreferences preferences,
  ) async {
    final recommendations = <TaskRecommendation>[];
    final now = DateTime.now();

    // Morning recommendations
    if (now.hour >= 6 && now.hour <= 10) {
      final highPriorityTasks = tasks
          .where((t) => !t.isCompleted && t.priority == TaskPriority.high)
          .take(3)
          .toList();

      if (highPriorityTasks.isNotEmpty) {
        recommendations.add(
          TaskRecommendation(
            id: 'morning_focus_${now.millisecondsSinceEpoch}',
            type: RecommendationType.timeBlocking,
            title: 'Morning Focus Session',
            description:
                'Start your day with high-priority tasks while your energy is peak.',
            suggestedTasks: highPriorityTasks.map((t) => t.id).toList(),
            estimatedDuration: Duration(hours: 2),
            confidence: 0.85,
            reasoning:
                'Morning hours typically offer the highest cognitive performance',
            createdAt: now,
          ),
        );
      }
    }

    // Afternoon recommendations
    if (now.hour >= 13 && now.hour <= 16) {
      final routineTasks = tasks
          .where(
            (t) =>
                !t.isCompleted &&
                    t.category.toString().toLowerCase().contains('routine') ||
                t.category.toString().toLowerCase().contains('admin'),
          )
          .take(5)
          .toList();

      if (routineTasks.isNotEmpty) {
        recommendations.add(
          TaskRecommendation(
            id: 'afternoon_routine_${now.millisecondsSinceEpoch}',
            type: RecommendationType.priorityAdjustment,
            title: 'Afternoon Routine Tasks',
            description:
                'Handle routine and administrative tasks during the afternoon energy dip.',
            suggestedTasks: routineTasks.map((t) => t.id).toList(),
            estimatedDuration: Duration(hours: 1, minutes: 30),
            confidence: 0.75,
            reasoning:
                'Routine tasks require less cognitive load and suit afternoon energy levels',
            createdAt: now,
          ),
        );
      }
    }

    return recommendations;
  }

  Future<List<TaskRecommendation>> _generatePriorityRecommendations(
    List<Task> tasks,
  ) async {
    final recommendations = <TaskRecommendation>[];
    final urgentTasks = tasks
        .where(
          (t) =>
              !t.isCompleted &&
              t.dueDate != null &&
              t.dueDate!.difference(DateTime.now()).inDays <= 1,
        )
        .toList();

    if (urgentTasks.isNotEmpty) {
      recommendations.add(
        TaskRecommendation(
          id: 'urgent_focus_${DateTime.now().millisecondsSinceEpoch}',
          type: RecommendationType.urgency,
          title: 'Urgent Tasks Require Attention',
          description:
              'Focus on tasks due within 24 hours to avoid missing deadlines.',
          suggestedTasks: urgentTasks.map((t) => t.id).toList(),
          estimatedDuration: Duration(hours: urgentTasks.length),
          confidence: 0.95,
          reasoning: 'Tasks with imminent deadlines should take priority',
          createdAt: DateTime.now(),
        ),
      );
    }

    return recommendations;
  }

  Future<List<TaskRecommendation>> _generateContextRecommendations(
    List<Task> tasks,
    UserPreferences preferences,
  ) async {
    final recommendations = <TaskRecommendation>[];

    // Location-based recommendations
    if (preferences.currentLocation == 'office') {
      final workTasks = tasks
          .where(
            (t) =>
                !t.isCompleted &&
                (t.category.toString().toLowerCase().contains('work') ||
                    t.category.toString().toLowerCase().contains('meeting') ||
                    t.category.toString().toLowerCase().contains('project')),
          )
          .take(5)
          .toList();

      if (workTasks.isNotEmpty) {
        recommendations.add(
          TaskRecommendation(
            id: 'office_context_${DateTime.now().millisecondsSinceEpoch}',
            type: RecommendationType.contextOptimal,
            title: 'Office-Optimized Tasks',
            description:
                'Make the most of your office time with work-focused tasks.',
            suggestedTasks: workTasks.map((t) => t.id).toList(),
            estimatedDuration: Duration(hours: 3),
            confidence: 0.80,
            reasoning: 'Office environment is optimal for work-related tasks',
            createdAt: DateTime.now(),
          ),
        );
      }
    }

    return recommendations;
  }

  Future<List<TaskRecommendation>> _generateEnergyBasedRecommendations(
    List<Task> tasks,
    UserPreferences preferences,
  ) async {
    final recommendations = <TaskRecommendation>[];
    final now = DateTime.now();

    // High-energy recommendations
    if (preferences.energyLevel == EnergyLevel.high) {
      final challengingTasks = tasks
          .where(
            (t) =>
                !t.isCompleted &&
                (t.priority == TaskPriority.high ||
                    t.estimatedDuration != null &&
                        t.estimatedDuration!.inHours >= 2),
          )
          .take(2)
          .toList();

      if (challengingTasks.isNotEmpty) {
        recommendations.add(
          TaskRecommendation(
            id: 'high_energy_${now.millisecondsSinceEpoch}',
            type: RecommendationType.energyOptimal,
            title: 'High-Energy Task Session',
            description: 'Tackle challenging tasks while your energy is high.',
            suggestedTasks: challengingTasks.map((t) => t.id).toList(),
            estimatedDuration: Duration(hours: 2),
            confidence: 0.88,
            reasoning:
                'High energy levels are ideal for complex and demanding tasks',
            createdAt: now,
          ),
        );
      }
    }

    // Low-energy recommendations
    if (preferences.energyLevel == EnergyLevel.low) {
      final simpleTasks = tasks
          .where(
            (t) =>
                !t.isCompleted &&
                t.priority == TaskPriority.low &&
                (t.estimatedDuration == null ||
                    t.estimatedDuration!.inMinutes <= 30),
          )
          .take(5)
          .toList();

      if (simpleTasks.isNotEmpty) {
        recommendations.add(
          TaskRecommendation(
            id: 'low_energy_${now.millisecondsSinceEpoch}',
            type: RecommendationType.energyOptimal,
            title: 'Low-Energy Quick Wins',
            description:
                'Complete simple tasks to maintain momentum when energy is low.',
            suggestedTasks: simpleTasks.map((t) => t.id).toList(),
            estimatedDuration: Duration(hours: 1),
            confidence: 0.75,
            reasoning:
                'Simple tasks provide accomplishment without draining limited energy',
            createdAt: now,
          ),
        );
      }
    }

    return recommendations;
  }

  // Productivity Analytics (100 features)
  Future<ProductivityAnalytics> generateProductivityAnalytics(
    List<Task> tasks,
    Duration period,
  ) async {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(period);

    final periodTasks = tasks
        .where(
          (t) =>
              t.createdAt.isAfter(startDate) && t.createdAt.isBefore(endDate),
        )
        .toList();

    return ProductivityAnalytics(
      id: 'analytics_${endDate.millisecondsSinceEpoch}',
      period: period,
      totalTasks: periodTasks.length,
      completedTasks: periodTasks.where((t) => t.isCompleted).length,
      completionRate: _calculateCompletionRate(periodTasks),
      averageTaskDuration: await _calculateAverageTaskDuration(periodTasks),
      productivityScore: await _calculateProductivityScore(periodTasks),
      focusTime: await _calculateFocusTime(periodTasks),
      distractionEvents: await _calculateDistractionEvents(periodTasks),
      peakProductivityHours: await _calculatePeakHours(periodTasks),
      categoryBreakdown: _calculateCategoryBreakdown(periodTasks),
      priorityDistribution: _calculatePriorityDistribution(periodTasks),
      streakData: await _calculateStreakData(periodTasks),
      improvementAreas: await _identifyImprovementAreas(periodTasks),
      achievements: await _identifyAchievements(periodTasks),
      generatedAt: endDate,
    );
  }

  // Utility Methods
  String _formatHour(int hour) {
    if (hour == 0) return '12:00 AM';
    if (hour < 12) return '$hour:00 AM';
    if (hour == 12) return '12:00 PM';
    return '${hour - 12}:00 PM';
  }

  String _getDayName(int weekday) {
    const days = [
      '',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday',
    ];
    return days[weekday];
  }

  double _calculateCompletionRate(List<Task> tasks) {
    if (tasks.isEmpty) return 0.0;
    final completedCount = tasks.where((t) => t.isCompleted).length;
    return completedCount / tasks.length;
  }

  Future<Duration> _calculateAverageTaskDuration(List<Task> tasks) async {
    final completedTasks = tasks
        .where((t) => t.isCompleted && t.completedAt != null)
        .toList();

    if (completedTasks.isEmpty) return Duration.zero;

    final totalMinutes = completedTasks.fold(0, (sum, task) {
      final duration = task.completedAt!.difference(task.createdAt);
      return sum + duration.inMinutes;
    });

    return Duration(minutes: totalMinutes ~/ completedTasks.length);
  }

  Future<double> _calculateProductivityScore(List<Task> tasks) async {
    if (tasks.isEmpty) return 0.0;

    final completionRate = _calculateCompletionRate(tasks);
    final priorityScore = _calculatePriorityScore(tasks);
    final timelinessScore = await _calculateTimelinessScore(tasks);

    return (completionRate * 0.4 +
            priorityScore * 0.3 +
            timelinessScore * 0.3) *
        100;
  }

  double _calculatePriorityScore(List<Task> tasks) {
    if (tasks.isEmpty) return 0.0;

    final completedTasks = tasks.where((t) => t.isCompleted).toList();
    if (completedTasks.isEmpty) return 0.0;

    final priorityWeights = {
      TaskPriority.high: 3.0,
      TaskPriority.medium: 2.0,
      TaskPriority.low: 1.0,
    };

    final totalWeight = completedTasks.fold(
      0.0,
      (sum, task) => sum + (priorityWeights[task.priority] ?? 1.0),
    );

    final maxPossibleWeight = tasks.fold(
      0.0,
      (sum, task) => sum + (priorityWeights[task.priority] ?? 1.0),
    );

    return maxPossibleWeight > 0 ? totalWeight / maxPossibleWeight : 0.0;
  }

  Future<double> _calculateTimelinessScore(List<Task> tasks) async {
    final tasksWithDeadlines = tasks.where((t) => t.dueDate != null).toList();
    if (tasksWithDeadlines.isEmpty) return 1.0;

    final onTimeTasks = tasksWithDeadlines
        .where(
          (t) =>
              t.isCompleted &&
              t.completedAt != null &&
              t.completedAt!.isBefore(t.dueDate!),
        )
        .length;

    return onTimeTasks / tasksWithDeadlines.length;
  }

  Future<Duration> _calculateFocusTime(List<Task> tasks) async {
    // Simplified focus time calculation
    final completedTasks = tasks.where((t) => t.isCompleted).toList();
    final totalMinutes =
        completedTasks.length * 25; // Assume 25 minutes per task
    return Duration(minutes: totalMinutes);
  }

  Future<int> _calculateDistractionEvents(List<Task> tasks) async {
    // Simplified distraction calculation
    return (tasks.length * 0.1).round(); // Assume 10% distraction rate
  }

  Future<List<int>> _calculatePeakHours(List<Task> tasks) async {
    final hourCounts = <int, int>{};

    for (final task in tasks.where((t) => t.completedAt != null)) {
      final hour = task.completedAt!.hour;
      hourCounts[hour] = (hourCounts[hour] ?? 0) + 1;
    }

    final sortedHours = hourCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedHours.take(3).map((e) => e.key).toList();
  }

  Map<String, int> _calculateCategoryBreakdown(List<Task> tasks) {
    final breakdown = <String, int>{};

    for (final task in tasks) {
      breakdown[task.category] = (breakdown[task.category] ?? 0) + 1;
    }

    return breakdown;
  }

  Map<TaskPriority, int> _calculatePriorityDistribution(List<Task> tasks) {
    final distribution = <TaskPriority, int>{};

    for (final task in tasks) {
      distribution[task.priority] = (distribution[task.priority] ?? 0) + 1;
    }

    return distribution;
  }

  Future<StreakData> _calculateStreakData(List<Task> tasks) async {
    // Simplified streak calculation
    final completedTasks = tasks.where((t) => t.isCompleted).toList();

    return StreakData(
      currentStreak: min(completedTasks.length, 7),
      longestStreak: min(completedTasks.length, 14),
      totalCompletedDays: completedTasks.length,
    );
  }

  Future<List<String>> _identifyImprovementAreas(List<Task> tasks) async {
    final areas = <String>[];

    final completionRate = _calculateCompletionRate(tasks);
    if (completionRate < 0.7) {
      areas.add('Task completion rate');
    }

    final overdueTasks = tasks
        .where(
          (t) =>
              t.dueDate != null &&
              t.dueDate!.isBefore(DateTime.now()) &&
              !t.isCompleted,
        )
        .length;

    if (overdueTasks > tasks.length * 0.2) {
      areas.add('Deadline management');
    }

    return areas;
  }

  Future<List<String>> _identifyAchievements(List<Task> tasks) async {
    final achievements = <String>[];

    final completionRate = _calculateCompletionRate(tasks);
    if (completionRate >= 0.9) {
      achievements.add('High Achiever - 90%+ completion rate');
    }

    final highPriorityCompleted = tasks
        .where((t) => t.isCompleted && t.priority == TaskPriority.high)
        .length;

    if (highPriorityCompleted >= 5) {
      achievements.add('Priority Master - Completed 5+ high-priority tasks');
    }

    return achievements;
  }

  void dispose() {
    _insightController.close();
  }
}
