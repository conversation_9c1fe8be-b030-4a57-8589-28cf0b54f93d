import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/excel_tool.dart';

// Excel Tools Provider
final excelToolsProvider = StateNotifierProvider<ExcelToolsNotifier, List<ExcelTool>>((ref) {
  return ExcelToolsNotifier();
});

// Excel Tools State Notifier
class ExcelToolsNotifier extends StateNotifier<List<ExcelTool>> {
  ExcelToolsNotifier() : super([]);

  void addTool(ExcelTool tool) {
    state = [...state, tool];
  }

  void updateTool(ExcelTool updatedTool) {
    state = [
      for (final tool in state)
        if (tool.id == updatedTool.id)
          updatedTool
        else
          tool,
    ];
  }

  void removeTool(String toolId) {
    state = state.where((tool) => tool.id != toolId).toList();
  }

  void clearTools() {
    state = [];
  }
}

// Current editing tool provider
final currentExcelToolProvider = StateProvider<ExcelTool?>((ref) => null);

// Tool search and filter providers
final toolsSearchQueryProvider = StateProvider<String>((ref) => '');

final toolsFilterProvider = StateProvider<String>((ref) => 'all');

final toolsCategoryProvider = StateProvider<String>((ref) => 'all');
