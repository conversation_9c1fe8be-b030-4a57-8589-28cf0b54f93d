import '../models/spreadsheet.dart';
import '../models/spreadsheet_cell.dart';
import '../utils/cell_address_parser.dart';

// Helper function to create SpreadsheetCell with correct row/column parameters
SpreadsheetCell _createCell(String address, {
  required String rawValue,
  required CellDataType dataType,
  CellFormat format = const CellFormat(),
  dynamic calculatedValue,
}) {
  final coords = CellAddressParser.parseAddress(address);
  return SpreadsheetCell(
    row: coords['row']!,
    column: coords['column']!,
    rawValue: rawValue,
    dataType: dataType,
    format: format,
    calculatedValue: calculatedValue,
  );
}

// Simplified Excel Template Library with working templates
class SimplifiedExcelTemplates {
  static List<Spreadsheet> getTemplates() {
    return [
      _createBudgetTemplate(),
      _createInvoiceTemplate(),
      _createExpenseTrackerTemplate(),
      _createLoanCalculatorTemplate(),
      _createInventoryTemplate(),
    ];
  }

  static Spreadsheet _createBudgetTemplate() {
    return Spreadsheet(
      id: 'budget_template',
      name: 'Monthly Budget',
      sheets: [
        SpreadsheetSheet(
          name: 'Budget',
          cells: {
            'A1': _createCell('A1', rawValue: 'MONTHLY BUDGET', dataType: CellDataType.text, format: const CellFormat(isBold: true, fontSize: 18)),
            'A3': _createCell('A3', rawValue: 'INCOME', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'A4': _createCell('A4', rawValue: 'Salary', dataType: CellDataType.text),
            'B4': _createCell('B4', rawValue: '5000', dataType: CellDataType.number),
            'A5': _createCell('A5', rawValue: 'Freelance', dataType: CellDataType.text),
            'B5': _createCell('B5', rawValue: '1000', dataType: CellDataType.number),
            'A6': _createCell('A6', rawValue: 'Total Income', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'B6': _createCell('B6', rawValue: '=SUM(B4:B5)', dataType: CellDataType.formula),

            'A8': _createCell('A8', rawValue: 'EXPENSES', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'A9': _createCell('A9', rawValue: 'Rent', dataType: CellDataType.text),
            'B9': _createCell('B9', rawValue: '1500', dataType: CellDataType.number),
            'A10': _createCell('A10', rawValue: 'Food', dataType: CellDataType.text),
            'B10': _createCell('B10', rawValue: '800', dataType: CellDataType.number),
            'A11': _createCell('A11', rawValue: 'Transportation', dataType: CellDataType.text),
            'B11': _createCell('B11', rawValue: '300', dataType: CellDataType.number),
            'A12': _createCell('A12', rawValue: 'Utilities', dataType: CellDataType.text),
            'B12': _createCell('B12', rawValue: '200', dataType: CellDataType.number),
            'A13': _createCell('A13', rawValue: 'Total Expenses', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'B13': _createCell('B13', rawValue: '=SUM(B9:B12)', dataType: CellDataType.formula),

            'A15': _createCell('A15', rawValue: 'Net Income', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'B15': _createCell('B15', rawValue: '=B6-B13', dataType: CellDataType.formula),
          },
        ),
      ],
    );
  }

  static Spreadsheet _createInvoiceTemplate() {
    return Spreadsheet(
      id: 'invoice_template',
      name: 'Invoice',
      sheets: [
        SpreadsheetSheet(
          name: 'Invoice',
          cells: {
            'A1': _createCell('A1', rawValue: 'INVOICE', dataType: CellDataType.text, format: const CellFormat(isBold: true, fontSize: 20)),
            'A3': _createCell('A3', rawValue: 'Invoice #:', dataType: CellDataType.text),
            'B3': _createCell('B3', rawValue: 'INV-001', dataType: CellDataType.text),
            'A4': _createCell('A4', rawValue: 'Date:', dataType: CellDataType.text),
            'B4': _createCell('B4', rawValue: '=TODAY()', dataType: CellDataType.formula),

            'A6': _createCell('A6', rawValue: 'Bill To:', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'A7': _createCell('A7', rawValue: 'Client Name', dataType: CellDataType.text),
            'A8': _createCell('A8', rawValue: 'Client Address', dataType: CellDataType.text),

            'A10': _createCell('A10', rawValue: 'Description', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'B10': _createCell('B10', rawValue: 'Quantity', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'C10': _createCell('C10', rawValue: 'Rate', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'D10': _createCell('D10', rawValue: 'Amount', dataType: CellDataType.text, format: const CellFormat(isBold: true)),

            'A11': _createCell('A11', rawValue: 'Web Development', dataType: CellDataType.text),
            'B11': _createCell('B11', rawValue: '40', dataType: CellDataType.number),
            'C11': _createCell('C11', rawValue: '100', dataType: CellDataType.number),
            'D11': _createCell('D11', rawValue: '=B11*C11', dataType: CellDataType.formula),

            'A12': _createCell('A12', rawValue: 'Consulting', dataType: CellDataType.text),
            'B12': _createCell('B12', rawValue: '10', dataType: CellDataType.number),
            'C12': _createCell('C12', rawValue: '150', dataType: CellDataType.number),
            'D12': _createCell('D12', rawValue: '=B12*C12', dataType: CellDataType.formula),

            'A14': _createCell('A14', rawValue: 'Subtotal:', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'D14': _createCell('D14', rawValue: '=SUM(D11:D12)', dataType: CellDataType.formula),
            'A15': _createCell('A15', rawValue: 'Tax (10%):', dataType: CellDataType.text),
            'D15': _createCell('D15', rawValue: '=D14*0.1', dataType: CellDataType.formula),
            'A16': _createCell('A16', rawValue: 'Total:', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'D16': _createCell('D16', rawValue: '=D14+D15', dataType: CellDataType.formula),
          },
        ),
      ],
    );
  }

  static Spreadsheet _createExpenseTrackerTemplate() {
    return Spreadsheet(
      id: 'expense_tracker',
      name: 'Expense Tracker',
      sheets: [
        SpreadsheetSheet(
          name: 'Expenses',
          cells: {
            'A1': _createCell('A1', rawValue: 'EXPENSE TRACKER', dataType: CellDataType.text, format: const CellFormat(isBold: true, fontSize: 16)),
            'A3': _createCell('A3', rawValue: 'Date', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'B3': _createCell('B3', rawValue: 'Category', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'C3': _createCell('C3', rawValue: 'Description', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'D3': _createCell('D3', rawValue: 'Amount', dataType: CellDataType.text, format: const CellFormat(isBold: true)),

            'A4': _createCell('A4', rawValue: '2024-01-01', dataType: CellDataType.text),
            'B4': _createCell('B4', rawValue: 'Food', dataType: CellDataType.text),
            'C4': _createCell('C4', rawValue: 'Grocery shopping', dataType: CellDataType.text),
            'D4': _createCell('D4', rawValue: '150', dataType: CellDataType.number),

            'A5': _createCell('A5', rawValue: '2024-01-02', dataType: CellDataType.text),
            'B5': _createCell('B5', rawValue: 'Transport', dataType: CellDataType.text),
            'C5': _createCell('C5', rawValue: 'Gas', dataType: CellDataType.text),
            'D5': _createCell('D5', rawValue: '60', dataType: CellDataType.number),

            'A6': _createCell('A6', rawValue: '2024-01-03', dataType: CellDataType.text),
            'B6': _createCell('B6', rawValue: 'Entertainment', dataType: CellDataType.text),
            'C6': _createCell('C6', rawValue: 'Movie tickets', dataType: CellDataType.text),
            'D6': _createCell('D6', rawValue: '25', dataType: CellDataType.number),

            'A8': _createCell('A8', rawValue: 'Total Expenses:', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'D8': _createCell('D8', rawValue: '=SUM(D4:D6)', dataType: CellDataType.formula),
          },
        ),
      ],
    );
  }

  static Spreadsheet _createLoanCalculatorTemplate() {
    return Spreadsheet(
      id: 'loan_calculator',
      name: 'Loan Calculator',
      sheets: [
        SpreadsheetSheet(
          name: 'Calculator',
          cells: {
            'A1': _createCell('A1', rawValue: 'LOAN CALCULATOR', dataType: CellDataType.text, format: const CellFormat(isBold: true, fontSize: 16)),
            'A3': _createCell('A3', rawValue: 'Loan Amount:', dataType: CellDataType.text),
            'B3': _createCell('B3', rawValue: '250000', dataType: CellDataType.number),
            'A4': _createCell('A4', rawValue: 'Interest Rate (%):', dataType: CellDataType.text),
            'B4': _createCell('B4', rawValue: '5.5', dataType: CellDataType.number),
            'A5': _createCell('A5', rawValue: 'Loan Term (years):', dataType: CellDataType.text),
            'B5': _createCell('B5', rawValue: '30', dataType: CellDataType.number),

            'A7': _createCell('A7', rawValue: 'Monthly Payment:', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'B7': _createCell('B7', rawValue: '=PMT(B4/100/12,B5*12,-B3)', dataType: CellDataType.formula),
            'A8': _createCell('A8', rawValue: 'Total Interest:', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'B8': _createCell('B8', rawValue: '=B7*B5*12-B3', dataType: CellDataType.formula),
            'A9': _createCell('A9', rawValue: 'Total Amount:', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'B9': _createCell('B9', rawValue: '=B3+B8', dataType: CellDataType.formula),
          },
        ),
      ],
    );
  }

  static Spreadsheet _createInventoryTemplate() {
    return Spreadsheet(
      id: 'inventory_template',
      name: 'Inventory Management',
      sheets: [
        SpreadsheetSheet(
          name: 'Inventory',
          cells: {
            'A1': _createCell('A1', rawValue: 'INVENTORY MANAGEMENT', dataType: CellDataType.text, format: const CellFormat(isBold: true, fontSize: 16)),
            'A3': _createCell('A3', rawValue: 'Item Code', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'B3': _createCell('B3', rawValue: 'Item Name', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'C3': _createCell('C3', rawValue: 'Quantity', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'D3': _createCell('D3', rawValue: 'Unit Price', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'E3': _createCell('E3', rawValue: 'Total Value', dataType: CellDataType.text, format: const CellFormat(isBold: true)),

            'A4': _createCell('A4', rawValue: 'ITM001', dataType: CellDataType.text),
            'B4': _createCell('B4', rawValue: 'Laptop', dataType: CellDataType.text),
            'C4': _createCell('C4', rawValue: '50', dataType: CellDataType.number),
            'D4': _createCell('D4', rawValue: '1200', dataType: CellDataType.number),
            'E4': _createCell('E4', rawValue: '=C4*D4', dataType: CellDataType.formula),

            'A5': _createCell('A5', rawValue: 'ITM002', dataType: CellDataType.text),
            'B5': _createCell('B5', rawValue: 'Mouse', dataType: CellDataType.text),
            'C5': _createCell('C5', rawValue: '200', dataType: CellDataType.number),
            'D5': _createCell('D5', rawValue: '25', dataType: CellDataType.number),
            'E5': _createCell('E5', rawValue: '=C5*D5', dataType: CellDataType.formula),

            'A6': _createCell('A6', rawValue: 'ITM003', dataType: CellDataType.text),
            'B6': _createCell('B6', rawValue: 'Keyboard', dataType: CellDataType.text),
            'C6': _createCell('C6', rawValue: '150', dataType: CellDataType.number),
            'D6': _createCell('D6', rawValue: '75', dataType: CellDataType.number),
            'E6': _createCell('E6', rawValue: '=C6*D6', dataType: CellDataType.formula),

            'A8': _createCell('A8', rawValue: 'Total Inventory Value:', dataType: CellDataType.text, format: const CellFormat(isBold: true)),
            'E8': _createCell('E8', rawValue: '=SUM(E4:E6)', dataType: CellDataType.formula),
          },
        ),
      ],
    );
  }
}
