import 'package:uuid/uuid.dart';

enum BudgetPeriod {
  weekly('Weekly'),
  monthly('Monthly'),
  quarterly('Quarterly'),
  yearly('Yearly');

  const BudgetPeriod(this.displayName);
  final String displayName;

  static List<String> get allPeriods =>
      BudgetPeriod.values.map((e) => e.displayName).toList();
}

enum BudgetStatus {
  active('Active'),
  paused('Paused'),
  completed('Completed'),
  exceeded('Exceeded');

  const BudgetStatus(this.displayName);
  final String displayName;
}

class Budget {
  final String id;
  final String name;
  final String category;
  final double amount;
  final double spent;
  final BudgetPeriod period;
  final BudgetStatus status;
  final DateTime startDate;
  final DateTime endDate;
  final String color;
  final bool alertsEnabled;
  final double alertThreshold; // Percentage (0.0 to 1.0)
  final DateTime createdAt;
  final DateTime updatedAt;

  Budget({
    String? id,
    required this.name,
    required this.category,
    required this.amount,
    this.spent = 0.0,
    required this.period,
    this.status = BudgetStatus.active,
    required this.startDate,
    required this.endDate,
    this.color = '#4CAF50',
    this.alertsEnabled = true,
    this.alertThreshold = 0.8,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'amount': amount,
      'spent': spent,
      'period': period.name,
      'status': status.name,
      'startDate': startDate.millisecondsSinceEpoch,
      'endDate': endDate.millisecondsSinceEpoch,
      'color': color,
      'alertsEnabled': alertsEnabled ? 1 : 0,
      'alertThreshold': alertThreshold,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory Budget.fromMap(Map<String, dynamic> map) {
    return Budget(
      id: map['id'],
      name: map['name'],
      category: map['category'],
      amount: map['amount'].toDouble(),
      spent: map['spent'].toDouble(),
      period: BudgetPeriod.values.firstWhere((e) => e.name == map['period']),
      status: BudgetStatus.values.firstWhere((e) => e.name == map['status']),
      startDate: DateTime.fromMillisecondsSinceEpoch(map['startDate']),
      endDate: DateTime.fromMillisecondsSinceEpoch(map['endDate']),
      color: map['color'],
      alertsEnabled: map['alertsEnabled'] == 1,
      alertThreshold: map['alertThreshold'].toDouble(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
    );
  }

  Budget copyWith({
    String? name,
    String? category,
    double? amount,
    double? spent,
    BudgetPeriod? period,
    BudgetStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    String? color,
    bool? alertsEnabled,
    double? alertThreshold,
    DateTime? updatedAt,
  }) {
    return Budget(
      id: id,
      name: name ?? this.name,
      category: category ?? this.category,
      amount: amount ?? this.amount,
      spent: spent ?? this.spent,
      period: period ?? this.period,
      status: status ?? this.status,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      color: color ?? this.color,
      alertsEnabled: alertsEnabled ?? this.alertsEnabled,
      alertThreshold: alertThreshold ?? this.alertThreshold,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  double get progressPercentage {
    if (amount <= 0) return 0.0;
    return (spent / amount).clamp(0.0, 1.0);
  }

  double get remainingAmount {
    return (amount - spent).clamp(0.0, double.infinity);
  }

  bool get isOverBudget {
    return spent > amount;
  }

  bool get shouldAlert {
    return alertsEnabled && progressPercentage >= alertThreshold;
  }

  String get formattedAmount {
    return '\$${amount.toStringAsFixed(2)}';
  }

  String get formattedSpent {
    return '\$${spent.toStringAsFixed(2)}';
  }

  String get formattedRemaining {
    return '\$${remainingAmount.toStringAsFixed(2)}';
  }

  String get progressText {
    return '$formattedSpent of $formattedAmount';
  }

  int get daysRemaining {
    final now = DateTime.now();
    if (now.isAfter(endDate)) return 0;
    return endDate.difference(now).inDays;
  }

  String get formattedDateRange {
    final startFormatted =
        '${startDate.day}/${startDate.month}/${startDate.year}';
    final endFormatted = '${endDate.day}/${endDate.month}/${endDate.year}';
    return '$startFormatted - $endFormatted';
  }
}
