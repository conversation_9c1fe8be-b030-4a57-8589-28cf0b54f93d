import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/tool_models.dart';

/// AI-Powered Tool Service with Smart Generation and Optimization
class AIToolService {
  static final AIToolService _instance = AIToolService._internal();
  factory AIToolService() => _instance;
  AIToolService._internal();

  final StreamController<ToolInsight> _insightController = StreamController.broadcast();
  Stream<ToolInsight> get insightStream => _insightController.stream;

  // Smart Tool Generation Features (250 features)
  Future<List<ToolSuggestion>> generateToolSuggestions(
    List<CustomTool> existingTools,
    UserWorkflow workflow,
    List<ToolUsagePattern> usagePatterns,
  ) async {
    final suggestions = <ToolSuggestion>[];
    
    // Analyze workflow gaps
    suggestions.addAll(await _analyzeWorkflowGaps(existingTools, workflow));
    
    // Suggest tool combinations
    suggestions.addAll(await _suggestToolCombinations(existingTools, usagePatterns));
    
    // Recommend automation opportunities
    suggestions.addAll(await _recommendAutomationOpportunities(usagePatterns));
    
    // Suggest performance optimizations
    suggestions.addAll(await _suggestPerformanceOptimizations(existingTools, usagePatterns));
    
    // Recommend integration opportunities
    suggestions.addAll(await _recommendIntegrationOpportunities(existingTools, workflow));
    
    return suggestions;
  }

  Future<List<ToolSuggestion>> _analyzeWorkflowGaps(
    List<CustomTool> existingTools,
    UserWorkflow workflow,
  ) async {
    final suggestions = <ToolSuggestion>[];
    
    // Identify missing tool categories
    final existingCategories = existingTools.map((t) => t.category).toSet();
    const essentialCategories = {
      'Text Processing', 'Data Conversion', 'File Management', 
      'Calculation', 'Validation', 'Formatting'
    };
    
    final missingCategories = essentialCategories.difference(existingCategories);
    
    for (final category in missingCategories) {
      suggestions.add(ToolSuggestion(
        id: 'gap_${category.toLowerCase().replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}',
        type: SuggestionType.workflowGap,
        title: 'Add $category Tool',
        description: 'Your workflow could benefit from a $category tool',
        suggestedTool: _generateToolForCategory(category),
        priority: Priority.medium,
        confidence: 0.75,
        estimatedBenefit: 'Complete your workflow toolkit',
        implementationComplexity: _getCategoryComplexity(category),
        createdAt: DateTime.now(),
      ));
    }
    
    // Analyze workflow step inefficiencies
    for (final step in workflow.steps) {
      if (step.estimatedTime.inMinutes > 10 && !_hasToolForStep(existingTools, step)) {
        suggestions.add(ToolSuggestion(
          id: 'efficiency_${step.id}_${DateTime.now().millisecondsSinceEpoch}',
          type: SuggestionType.efficiency,
          title: 'Automate "${step.name}"',
          description: 'This step takes ${step.estimatedTime.inMinutes} minutes and could be automated',
          suggestedTool: _generateToolForWorkflowStep(step),
          priority: Priority.high,
          confidence: 0.85,
          estimatedBenefit: 'Save ${step.estimatedTime.inMinutes} minutes per execution',
          implementationComplexity: ImplementationComplexity.medium,
          createdAt: DateTime.now(),
        ));
      }
    }
    
    return suggestions;
  }

  Future<List<ToolSuggestion>> _suggestToolCombinations(
    List<CustomTool> existingTools,
    List<ToolUsagePattern> usagePatterns,
  ) async {
    final suggestions = <ToolSuggestion>[];
    
    // Find frequently used tool pairs
    final toolPairs = <String, int>{};
    for (final pattern in usagePatterns) {
      if (pattern.toolSequence.length >= 2) {
        for (int i = 0; i < pattern.toolSequence.length - 1; i++) {
          final pair = '${pattern.toolSequence[i]}_${pattern.toolSequence[i + 1]}';
          toolPairs[pair] = (toolPairs[pair] ?? 0) + 1;
        }
      }
    }
    
    // Suggest combinations for frequent pairs
    final frequentPairs = toolPairs.entries.where((e) => e.value >= 3).toList();
    for (final pair in frequentPairs.take(5)) {
      final toolIds = pair.key.split('_');
      final tool1 = existingTools.firstWhere((t) => t.id == toolIds[0], orElse: () => existingTools.first);
      final tool2 = existingTools.firstWhere((t) => t.id == toolIds[1], orElse: () => existingTools.first);
      
      suggestions.add(ToolSuggestion(
        id: 'combination_${pair.key}_${DateTime.now().millisecondsSinceEpoch}',
        type: SuggestionType.combination,
        title: 'Combine ${tool1.name} + ${tool2.name}',
        description: 'You often use these tools together (${pair.value} times)',
        suggestedTool: _generateCombinedTool(tool1, tool2),
        priority: Priority.medium,
        confidence: 0.80,
        estimatedBenefit: 'Streamlined workflow with single tool',
        implementationComplexity: ImplementationComplexity.high,
        createdAt: DateTime.now(),
      ));
    }
    
    return suggestions;
  }

  Future<List<ToolSuggestion>> _recommendAutomationOpportunities(List<ToolUsagePattern> usagePatterns) async {
    final suggestions = <ToolSuggestion>[];
    
    // Find repetitive patterns
    final repetitivePatterns = usagePatterns.where((p) => p.frequency >= 5).toList();
    
    for (final pattern in repetitivePatterns.take(3)) {
      suggestions.add(ToolSuggestion(
        id: 'automation_${pattern.id}_${DateTime.now().millisecondsSinceEpoch}',
        type: SuggestionType.automation,
        title: 'Automate Repetitive Pattern',
        description: 'Pattern "${pattern.name}" is used ${pattern.frequency} times',
        suggestedTool: _generateAutomationTool(pattern),
        priority: Priority.high,
        confidence: 0.90,
        estimatedBenefit: 'Eliminate repetitive manual work',
        implementationComplexity: ImplementationComplexity.medium,
        createdAt: DateTime.now(),
      ));
    }
    
    return suggestions;
  }

  Future<List<ToolSuggestion>> _suggestPerformanceOptimizations(
    List<CustomTool> existingTools,
    List<ToolUsagePattern> usagePatterns,
  ) async {
    final suggestions = <ToolSuggestion>[];
    
    // Find slow tools
    final slowTools = existingTools.where((t) => t.averageExecutionTime.inSeconds > 5).toList();
    
    for (final tool in slowTools.take(3)) {
      suggestions.add(ToolSuggestion(
        id: 'optimization_${tool.id}_${DateTime.now().millisecondsSinceEpoch}',
        type: SuggestionType.optimization,
        title: 'Optimize "${tool.name}"',
        description: 'This tool takes ${tool.averageExecutionTime.inSeconds} seconds on average',
        suggestedTool: _generateOptimizedTool(tool),
        priority: Priority.medium,
        confidence: 0.70,
        estimatedBenefit: 'Faster execution and better user experience',
        implementationComplexity: ImplementationComplexity.high,
        createdAt: DateTime.now(),
      ));
    }
    
    return suggestions;
  }

  Future<List<ToolSuggestion>> _recommendIntegrationOpportunities(
    List<CustomTool> existingTools,
    UserWorkflow workflow,
  ) async {
    final suggestions = <ToolSuggestion>[];
    
    // Suggest API integrations
    const popularAPIs = ['REST API', 'GraphQL', 'Database', 'File System', 'Web Scraping'];
    
    for (final api in popularAPIs.take(3)) {
      if (!existingTools.any((t) => t.integrations.contains(api))) {
        suggestions.add(ToolSuggestion(
          id: 'integration_${api.toLowerCase().replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}',
          type: SuggestionType.integration,
          title: 'Add $api Integration',
          description: 'Expand capabilities with $api integration',
          suggestedTool: _generateIntegrationTool(api),
          priority: Priority.low,
          confidence: 0.65,
          estimatedBenefit: 'Enhanced connectivity and data access',
          implementationComplexity: ImplementationComplexity.high,
          createdAt: DateTime.now(),
        ));
      }
    }
    
    return suggestions;
  }

  // Code Generation Features (250 features)
  Future<GeneratedCode> generateToolCode(
    ToolSpecification specification,
    CodeGenerationSettings settings,
  ) async {
    final codeComponents = <CodeComponent>[];
    
    // Generate main function
    codeComponents.add(await _generateMainFunction(specification, settings));
    
    // Generate helper functions
    codeComponents.addAll(await _generateHelperFunctions(specification, settings));
    
    // Generate validation logic
    codeComponents.add(await _generateValidationLogic(specification, settings));
    
    // Generate error handling
    codeComponents.add(await _generateErrorHandling(specification, settings));
    
    // Generate documentation
    codeComponents.add(await _generateDocumentation(specification, settings));
    
    // Generate tests
    if (settings.includeTests) {
      codeComponents.addAll(await _generateTests(specification, settings));
    }
    
    final fullCode = _combineCodeComponents(codeComponents, settings);
    
    return GeneratedCode(
      id: 'generated_${specification.id}_${DateTime.now().millisecondsSinceEpoch}',
      specificationId: specification.id,
      language: settings.targetLanguage,
      framework: settings.targetFramework,
      code: fullCode,
      components: codeComponents,
      complexity: _calculateCodeComplexity(codeComponents),
      estimatedLines: _countCodeLines(fullCode),
      dependencies: _extractDependencies(codeComponents),
      qualityScore: await _calculateQualityScore(fullCode, specification),
      generatedAt: DateTime.now(),
    );
  }

  Future<CodeComponent> _generateMainFunction(
    ToolSpecification specification,
    CodeGenerationSettings settings,
  ) async {
    final functionName = _sanitizeName(specification.name);
    final parameters = specification.inputs.map((input) => 
      '${_mapDataType(input.dataType, settings.targetLanguage)} ${_sanitizeName(input.name)}'
    ).join(', ');
    
    final returnType = _mapDataType(specification.output.dataType, settings.targetLanguage);
    
    String code;
    switch (settings.targetLanguage) {
      case 'dart':
        code = '''
$returnType $functionName($parameters) {
  // TODO: Implement ${specification.name} logic
  ${_generateMainLogic(specification, settings)}
  
  return result;
}''';
        break;
      case 'javascript':
        code = '''
function $functionName($parameters) {
  // TODO: Implement ${specification.name} logic
  ${_generateMainLogic(specification, settings)}
  
  return result;
}''';
        break;
      case 'python':
        code = '''
def $functionName($parameters):
    """${specification.description}"""
    # TODO: Implement ${specification.name} logic
    ${_generateMainLogic(specification, settings)}
    
    return result''';
        break;
      default:
        code = '// Unsupported language: ${settings.targetLanguage}';
    }
    
    return CodeComponent(
      type: ComponentType.mainFunction,
      name: functionName,
      code: code,
      dependencies: [],
      complexity: _calculateFunctionComplexity(specification),
    );
  }

  Future<List<CodeComponent>> _generateHelperFunctions(
    ToolSpecification specification,
    CodeGenerationSettings settings,
  ) async {
    final helpers = <CodeComponent>[];
    
    // Generate input validation helper
    if (specification.inputs.isNotEmpty) {
      helpers.add(CodeComponent(
        type: ComponentType.helper,
        name: 'validateInputs',
        code: _generateInputValidation(specification, settings),
        dependencies: [],
        complexity: 2,
      ));
    }
    
    // Generate output formatting helper
    helpers.add(CodeComponent(
      type: ComponentType.helper,
      name: 'formatOutput',
      code: _generateOutputFormatting(specification, settings),
      dependencies: [],
      complexity: 1,
    ));
    
    // Generate utility helpers based on tool type
    if (specification.category == 'Text Processing') {
      helpers.add(CodeComponent(
        type: ComponentType.helper,
        name: 'processText',
        code: _generateTextProcessingHelper(specification, settings),
        dependencies: [],
        complexity: 3,
      ));
    }
    
    return helpers;
  }

  Future<CodeComponent> _generateValidationLogic(
    ToolSpecification specification,
    CodeGenerationSettings settings,
  ) async {
    final validationCode = specification.inputs.map((input) {
      switch (input.dataType) {
        case DataType.string:
          return 'if (${_sanitizeName(input.name)} == null || ${_sanitizeName(input.name)}.isEmpty) throw ArgumentError("${input.name} cannot be empty");';
        case DataType.number:
          return 'if (${_sanitizeName(input.name)} == null) throw ArgumentError("${input.name} is required");';
        case DataType.boolean:
          return 'if (${_sanitizeName(input.name)} == null) throw ArgumentError("${input.name} is required");';
        default:
          return '// Validation for ${input.name}';
      }
    }).join('\n  ');
    
    return CodeComponent(
      type: ComponentType.validation,
      name: 'validation',
      code: '''
void validateInputs(${ specification.inputs.map((i) => '${_mapDataType(i.dataType, settings.targetLanguage)} ${_sanitizeName(i.name)}').join(', ')}) {
  $validationCode
}''',
      dependencies: [],
      complexity: specification.inputs.length,
    );
  }

  Future<CodeComponent> _generateErrorHandling(
    ToolSpecification specification,
    CodeGenerationSettings settings,
  ) async {
    String errorHandlingCode;
    
    switch (settings.targetLanguage) {
      case 'dart':
        errorHandlingCode = '''
try {
  // Main execution logic here
} catch (e) {
  throw ToolExecutionException('Error in ${specification.name}: \$e');
}''';
        break;
      case 'javascript':
        errorHandlingCode = '''
try {
  // Main execution logic here
} catch (error) {
  throw new Error(\`Error in ${specification.name}: \${error.message}\`);
}''';
        break;
      case 'python':
        errorHandlingCode = '''
try:
    # Main execution logic here
    pass
except Exception as e:
    raise Exception(f"Error in ${specification.name}: {str(e)}")''';
        break;
      default:
        errorHandlingCode = '// Error handling not implemented for ${settings.targetLanguage}';
    }
    
    return CodeComponent(
      type: ComponentType.errorHandling,
      name: 'errorHandling',
      code: errorHandlingCode,
      dependencies: [],
      complexity: 2,
    );
  }

  Future<CodeComponent> _generateDocumentation(
    ToolSpecification specification,
    CodeGenerationSettings settings,
  ) async {
    final docCode = '''
/**
 * ${specification.name}
 * 
 * ${specification.description}
 * 
 * Parameters:
${specification.inputs.map((input) => ' * @param ${input.name} - ${input.description}').join('\n')}
 * 
 * Returns:
 * @returns ${specification.output.description}
 * 
 * Example:
 * ```
 * final result = ${_sanitizeName(specification.name)}(${specification.inputs.map((i) => 'example${i.name}').join(', ')});
 * ```
 */''';
    
    return CodeComponent(
      type: ComponentType.documentation,
      name: 'documentation',
      code: docCode,
      dependencies: [],
      complexity: 0,
    );
  }

  Future<List<CodeComponent>> _generateTests(
    ToolSpecification specification,
    CodeGenerationSettings settings,
  ) async {
    final tests = <CodeComponent>[];
    
    // Generate basic test
    tests.add(CodeComponent(
      type: ComponentType.test,
      name: 'basicTest',
      code: _generateBasicTest(specification, settings),
      dependencies: ['test'],
      complexity: 2,
    ));
    
    // Generate edge case tests
    tests.add(CodeComponent(
      type: ComponentType.test,
      name: 'edgeCaseTests',
      code: _generateEdgeCaseTests(specification, settings),
      dependencies: ['test'],
      complexity: 3,
    ));
    
    return tests;
  }

  // Performance Optimization Features (250 features)
  Future<OptimizationResult> optimizeToolPerformance(
    CustomTool tool,
    List<PerformanceMetric> metrics,
  ) async {
    final optimizations = <Optimization>[];
    
    // Analyze execution time
    final avgExecutionTime = metrics
        .map((m) => m.executionTime.inMilliseconds)
        .reduce((a, b) => a + b) / metrics.length;
    
    if (avgExecutionTime > 1000) {
      optimizations.add(Optimization(
        type: OptimizationType.executionTime,
        description: 'Reduce execution time through algorithm optimization',
        estimatedImprovement: 0.3,
        implementationEffort: ImplementationEffort.medium,
        suggestedChanges: [
          'Use more efficient algorithms',
          'Implement caching for repeated operations',
          'Optimize data structures',
          'Reduce unnecessary computations',
        ],
      ));
    }
    
    // Analyze memory usage
    final avgMemoryUsage = metrics
        .map((m) => m.memoryUsage)
        .reduce((a, b) => a + b) / metrics.length;
    
    if (avgMemoryUsage > 100 * 1024 * 1024) { // 100MB
      optimizations.add(Optimization(
        type: OptimizationType.memoryUsage,
        description: 'Optimize memory usage to reduce resource consumption',
        estimatedImprovement: 0.4,
        implementationEffort: ImplementationEffort.high,
        suggestedChanges: [
          'Implement lazy loading',
          'Use streaming for large data sets',
          'Optimize data structures',
          'Implement garbage collection strategies',
        ],
      ));
    }
    
    // Analyze error rates
    final errorRate = metrics.where((m) => m.hasError).length / metrics.length;
    
    if (errorRate > 0.05) { // 5% error rate
      optimizations.add(Optimization(
        type: OptimizationType.reliability,
        description: 'Improve error handling and reliability',
        estimatedImprovement: 0.5,
        implementationEffort: ImplementationEffort.medium,
        suggestedChanges: [
          'Add comprehensive input validation',
          'Implement retry mechanisms',
          'Add better error messages',
          'Include fallback strategies',
        ],
      ));
    }
    
    return OptimizationResult(
      id: 'optimization_${tool.id}_${DateTime.now().millisecondsSinceEpoch}',
      toolId: tool.id,
      optimizations: optimizations,
      overallImprovement: optimizations.fold(0.0, (sum, o) => sum + o.estimatedImprovement) / optimizations.length,
      implementationTime: _estimateImplementationTime(optimizations),
      generatedAt: DateTime.now(),
    );
  }

  // Utility Methods
  ToolSpecification _generateToolForCategory(String category) {
    return ToolSpecification(
      id: 'spec_${category.toLowerCase().replaceAll(' ', '_')}',
      name: '${category} Tool',
      description: 'A tool for $category operations',
      category: category,
      inputs: [
        InputParameter(
          name: 'input',
          dataType: DataType.string,
          description: 'Input data',
          isRequired: true,
        ),
      ],
      output: OutputParameter(
        dataType: DataType.string,
        description: 'Processed output',
      ),
      complexity: ToolComplexity.medium,
    );
  }

  ImplementationComplexity _getCategoryComplexity(String category) {
    const complexCategories = {'Data Conversion', 'Validation'};
    return complexCategories.contains(category) 
        ? ImplementationComplexity.high 
        : ImplementationComplexity.medium;
  }

  bool _hasToolForStep(List<CustomTool> tools, WorkflowStep step) {
    return tools.any((tool) => 
        tool.name.toLowerCase().contains(step.name.toLowerCase()) ||
        tool.description.toLowerCase().contains(step.name.toLowerCase())
    );
  }

  ToolSpecification _generateToolForWorkflowStep(WorkflowStep step) {
    return ToolSpecification(
      id: 'spec_${step.id}',
      name: '${step.name} Automation',
      description: 'Automated tool for ${step.name}',
      category: 'Automation',
      inputs: [
        InputParameter(
          name: 'input',
          dataType: DataType.string,
          description: 'Input for ${step.name}',
          isRequired: true,
        ),
      ],
      output: OutputParameter(
        dataType: DataType.string,
        description: 'Result of ${step.name}',
      ),
      complexity: ToolComplexity.medium,
    );
  }

  ToolSpecification _generateCombinedTool(CustomTool tool1, CustomTool tool2) {
    return ToolSpecification(
      id: 'spec_combined_${tool1.id}_${tool2.id}',
      name: '${tool1.name} + ${tool2.name}',
      description: 'Combined functionality of ${tool1.name} and ${tool2.name}',
      category: 'Combined',
      inputs: [
        InputParameter(
          name: 'input',
          dataType: DataType.string,
          description: 'Input for combined operation',
          isRequired: true,
        ),
      ],
      output: OutputParameter(
        dataType: DataType.string,
        description: 'Combined result',
      ),
      complexity: ToolComplexity.high,
    );
  }

  ToolSpecification _generateAutomationTool(ToolUsagePattern pattern) {
    return ToolSpecification(
      id: 'spec_automation_${pattern.id}',
      name: '${pattern.name} Automation',
      description: 'Automated version of ${pattern.name}',
      category: 'Automation',
      inputs: [
        InputParameter(
          name: 'input',
          dataType: DataType.string,
          description: 'Input for automation',
          isRequired: true,
        ),
      ],
      output: OutputParameter(
        dataType: DataType.string,
        description: 'Automated result',
      ),
      complexity: ToolComplexity.high,
    );
  }

  ToolSpecification _generateOptimizedTool(CustomTool tool) {
    return ToolSpecification(
      id: 'spec_optimized_${tool.id}',
      name: '${tool.name} (Optimized)',
      description: 'Optimized version of ${tool.name}',
      category: tool.category,
      inputs: [
        InputParameter(
          name: 'input',
          dataType: DataType.string,
          description: 'Input for optimized tool',
          isRequired: true,
        ),
      ],
      output: OutputParameter(
        dataType: DataType.string,
        description: 'Optimized result',
      ),
      complexity: ToolComplexity.medium,
    );
  }

  ToolSpecification _generateIntegrationTool(String api) {
    return ToolSpecification(
      id: 'spec_integration_${api.toLowerCase().replaceAll(' ', '_')}',
      name: '$api Integration Tool',
      description: 'Tool for $api integration',
      category: 'Integration',
      inputs: [
        InputParameter(
          name: 'endpoint',
          dataType: DataType.string,
          description: '$api endpoint',
          isRequired: true,
        ),
      ],
      output: OutputParameter(
        dataType: DataType.string,
        description: '$api response',
      ),
      complexity: ToolComplexity.high,
    );
  }

  String _sanitizeName(String name) {
    return name.replaceAll(RegExp(r'[^a-zA-Z0-9_]'), '_').toLowerCase();
  }

  String _mapDataType(DataType dataType, String language) {
    switch (language) {
      case 'dart':
        switch (dataType) {
          case DataType.string: return 'String';
          case DataType.number: return 'double';
          case DataType.boolean: return 'bool';
          case DataType.list: return 'List<dynamic>';
          case DataType.map: return 'Map<String, dynamic>';
        }
      case 'javascript':
        return 'any'; // JavaScript is dynamically typed
      case 'python':
        switch (dataType) {
          case DataType.string: return 'str';
          case DataType.number: return 'float';
          case DataType.boolean: return 'bool';
          case DataType.list: return 'list';
          case DataType.map: return 'dict';
        }
      default:
        return 'unknown';
    }
  }

  String _generateMainLogic(ToolSpecification specification, CodeGenerationSettings settings) {
    switch (specification.category) {
      case 'Text Processing':
        return 'final result = input.trim().toUpperCase();';
      case 'Calculation':
        return 'final result = input * 2;';
      default:
        return 'final result = input;';
    }
  }

  int _calculateFunctionComplexity(ToolSpecification specification) {
    return specification.inputs.length + (specification.complexity == ToolComplexity.high ? 3 : 1);
  }

  String _generateInputValidation(ToolSpecification specification, CodeGenerationSettings settings) {
    return '// Input validation logic for ${specification.name}';
  }

  String _generateOutputFormatting(ToolSpecification specification, CodeGenerationSettings settings) {
    return '// Output formatting logic for ${specification.name}';
  }

  String _generateTextProcessingHelper(ToolSpecification specification, CodeGenerationSettings settings) {
    return '// Text processing helper for ${specification.name}';
  }

  String _combineCodeComponents(List<CodeComponent> components, CodeGenerationSettings settings) {
    return components.map((c) => c.code).join('\n\n');
  }

  int _calculateCodeComplexity(List<CodeComponent> components) {
    return components.fold(0, (sum, c) => sum + c.complexity);
  }

  int _countCodeLines(String code) {
    return code.split('\n').length;
  }

  List<String> _extractDependencies(List<CodeComponent> components) {
    final dependencies = <String>{};
    for (final component in components) {
      dependencies.addAll(component.dependencies);
    }
    return dependencies.toList();
  }

  Future<double> _calculateQualityScore(String code, ToolSpecification specification) async {
    // Simplified quality score calculation
    double score = 0.8;
    
    if (code.contains('TODO')) score -= 0.1;
    if (code.contains('throw')) score += 0.1;
    if (code.contains('/**')) score += 0.1;
    
    return score.clamp(0.0, 1.0);
  }

  String _generateBasicTest(ToolSpecification specification, CodeGenerationSettings settings) {
    return '''
test('${specification.name} basic functionality', () {
  // Arrange
  final input = 'test input';
  
  // Act
  final result = ${_sanitizeName(specification.name)}(input);
  
  // Assert
  expect(result, isNotNull);
});''';
  }

  String _generateEdgeCaseTests(ToolSpecification specification, CodeGenerationSettings settings) {
    return '''
test('${specification.name} edge cases', () {
  // Test empty input
  expect(() => ${_sanitizeName(specification.name)}(''), throwsArgumentError);
  
  // Test null input
  expect(() => ${_sanitizeName(specification.name)}(null), throwsArgumentError);
});''';
  }

  Duration _estimateImplementationTime(List<Optimization> optimizations) {
    final totalEffort = optimizations.fold(0, (sum, o) {
      switch (o.implementationEffort) {
        case ImplementationEffort.low: return sum + 1;
        case ImplementationEffort.medium: return sum + 3;
        case ImplementationEffort.high: return sum + 8;
      }
    });
    
    return Duration(hours: totalEffort);
  }

  void dispose() {
    _insightController.close();
  }
}
