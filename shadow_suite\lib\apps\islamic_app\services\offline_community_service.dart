import 'dart:async';
import 'dart:math';
import '../models/community_models.dart';
import '../../../core/database/database_service.dart';
import '../../../core/services/error_handler.dart' as error_handler;

class OfflineCommunityService {
  static final List<PrayerGroup> _prayerGroups = [];
  static final List<MosqueInfo> _mosques = [];
  static final List<IslamicEvent> _events = [];
  static final List<CommunityMember> _members = [];
  static final List<DuaCollection> _duaCollections = [];
  
  // Initialize offline community service
  static Future<void> initialize() async {
    await _loadPrayerGroups();
    await _loadMosques();
    await _loadEvents();
    await _loadMembers();
    await _loadDuaCollections();
    await _initializeDefaultData();
  }

  // FEATURE 11: Local Prayer Groups Management
  static Future<PrayerGroup> createPrayerGroup({
    required String name,
    required String description,
    required String location,
    required List<String> prayerTimes,
    required String contactInfo,
    int maxMembers = 50,
  }) async {
    try {
      final group = PrayerGroup(
        id: 'group_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        description: description,
        location: location,
        prayerTimes: prayerTimes,
        contactInfo: contactInfo,
        maxMembers: maxMembers,
        currentMembers: 1, // Creator is first member
        isActive: true,
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
        metadata: {},
      );

      await DatabaseService.safeInsert('prayer_groups', group.toJson());
      _prayerGroups.add(group);
      
      return group;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Create prayer group');
      rethrow;
    }
  }

  // FEATURE 12: Offline Mosque Directory
  static Future<MosqueInfo> addMosque({
    required String name,
    required String address,
    required double latitude,
    required double longitude,
    String? phone,
    String? website,
    String? imam,
    List<String>? services,
    Map<String, String>? prayerSchedule,
  }) async {
    try {
      final mosque = MosqueInfo(
        id: 'mosque_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        address: address,
        latitude: latitude,
        longitude: longitude,
        phone: phone,
        website: website,
        imam: imam,
        services: services ?? [],
        prayerSchedule: prayerSchedule ?? {},
        rating: 0.0,
        reviews: [],
        isVerified: false,
        addedAt: DateTime.now(),
        metadata: {},
      );

      await DatabaseService.safeInsert('mosques', mosque.toJson());
      _mosques.add(mosque);
      
      return mosque;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Add mosque');
      rethrow;
    }
  }

  // FEATURE 13: Local Islamic Events Management
  static Future<IslamicEvent> createEvent({
    required String title,
    required String description,
    required DateTime startDate,
    required DateTime endDate,
    required String location,
    required IslamicEventCategory category,
    String? organizer,
    bool isRecurring = false,
    Map<String, dynamic>? details,
  }) async {
    try {
      final event = IslamicEvent(
        id: 'event_${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        description: description,
        startDate: startDate,
        endDate: endDate,
        location: location,
        category: category,
        organizer: organizer,
        isRecurring: isRecurring,
        attendees: [],
        maxAttendees: 100,
        isActive: true,
        details: details ?? {},
        createdAt: DateTime.now(),
      );

      await DatabaseService.safeInsert('islamic_events', event.toJson());
      _events.add(event);
      
      return event;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Create Islamic event');
      rethrow;
    }
  }

  // FEATURE 14: Community Member Management
  static Future<CommunityMember> addMember({
    required String name,
    required String email,
    String? phone,
    String? location,
    List<String>? interests,
    MemberRole role = MemberRole.member,
  }) async {
    try {
      final member = CommunityMember(
        id: 'member_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        email: email,
        phone: phone,
        location: location,
        interests: interests ?? [],
        role: role,
        joinedAt: DateTime.now(),
        isActive: true,
        contributions: 0,
        reputation: 0,
        metadata: {},
      );

      await DatabaseService.safeInsert('community_members', member.toJson());
      _members.add(member);
      
      return member;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Add community member');
      rethrow;
    }
  }

  // FEATURE 15: Offline Dua Collections
  static Future<DuaCollection> createDuaCollection({
    required String name,
    required String description,
    required List<Dua> duas,
    DuaCategory category = DuaCategory.general,
    String? language,
  }) async {
    try {
      final collection = DuaCollection(
        id: 'dua_collection_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        description: description,
        category: category,
        language: language ?? 'ar',
        duas: duas,
        isOfficial: false,
        downloadCount: 0,
        rating: 0.0,
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      await DatabaseService.safeInsert('dua_collections', collection.toJson());
      _duaCollections.add(collection);
      
      return collection;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Create dua collection');
      rethrow;
    }
  }

  // Search and filter methods
  static List<MosqueInfo> findNearbyMosques({
    required double latitude,
    required double longitude,
    double radiusKm = 10.0,
  }) {
    try {
      return _mosques.where((mosque) {
        final distance = _calculateDistance(
          latitude, longitude, mosque.latitude, mosque.longitude);
        return distance <= radiusKm;
      }).toList()
        ..sort((a, b) {
          final distanceA = _calculateDistance(latitude, longitude, a.latitude, a.longitude);
          final distanceB = _calculateDistance(latitude, longitude, b.latitude, b.longitude);
          return distanceA.compareTo(distanceB);
        });
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Find nearby mosques');
      return [];
    }
  }

  static List<PrayerGroup> searchPrayerGroups({
    String? location,
    List<String>? prayerTimes,
    bool activeOnly = true,
  }) {
    try {
      var groups = _prayerGroups.where((group) {
        if (activeOnly && !group.isActive) return false;
        if (location != null && !group.location.toLowerCase().contains(location.toLowerCase())) return false;
        if (prayerTimes != null) {
          final hasCommonPrayer = prayerTimes.any((prayer) => group.prayerTimes.contains(prayer));
          if (!hasCommonPrayer) return false;
        }
        return true;
      }).toList();
      
      return groups..sort((a, b) => a.name.compareTo(b.name));
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Search prayer groups');
      return [];
    }
  }

  static List<IslamicEvent> getUpcomingEvents({
    IslamicEventCategory? category,
    int daysAhead = 30,
  }) {
    try {
      final now = DateTime.now();
      final endDate = now.add(Duration(days: daysAhead));
      
      var events = _events.where((event) {
        if (!event.isActive) return false;
        if (event.startDate.isBefore(now)) return false;
        if (event.startDate.isAfter(endDate)) return false;
        if (category != null && event.category != category) return false;
        return true;
      }).toList();
      
      return events..sort((a, b) => a.startDate.compareTo(b.startDate));
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Get upcoming events');
      return [];
    }
  }

  static List<DuaCollection> searchDuaCollections({
    String? query,
    DuaCategory? category,
    String? language,
  }) {
    try {
      var collections = _duaCollections.where((collection) {
        if (query != null) {
          final queryLower = query.toLowerCase();
          final nameMatch = collection.name.toLowerCase().contains(queryLower);
          final descMatch = collection.description.toLowerCase().contains(queryLower);
          if (!nameMatch && !descMatch) return false;
        }
        if (category != null && collection.category != category) return false;
        if (language != null && collection.language != language) return false;
        return true;
      }).toList();
      
      return collections..sort((a, b) => b.rating.compareTo(a.rating));
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Search dua collections');
      return [];
    }
  }

  // Helper methods
  static double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // Earth's radius in kilometers
    
    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);
    final double lat1Rad = _degreesToRadians(lat1);
    final double lat2Rad = _degreesToRadians(lat2);

    final double a =
        sin(dLat / 2) * sin(dLat / 2) +
        cos(lat1Rad) * cos(lat2Rad) *
        sin(dLon / 2) * sin(dLon / 2);

    final double c = 2 * asin(sqrt(a));
    
    return earthRadius * c;
  }

  static double _degreesToRadians(double degrees) {
    return degrees * (pi / 180);
  }

  // Data loading methods
  static Future<void> _loadPrayerGroups() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM prayer_groups');
      _prayerGroups.clear();
      for (final row in results) {
        _prayerGroups.add(PrayerGroup.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load prayer groups');
    }
  }

  static Future<void> _loadMosques() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM mosques');
      _mosques.clear();
      for (final row in results) {
        _mosques.add(MosqueInfo.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load mosques');
    }
  }

  static Future<void> _loadEvents() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM islamic_events');
      _events.clear();
      for (final row in results) {
        _events.add(IslamicEvent.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load events');
    }
  }

  static Future<void> _loadMembers() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM community_members');
      _members.clear();
      for (final row in results) {
        _members.add(CommunityMember.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load members');
    }
  }

  static Future<void> _loadDuaCollections() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM dua_collections');
      _duaCollections.clear();
      for (final row in results) {
        _duaCollections.add(DuaCollection.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load dua collections');
    }
  }

  static Future<void> _initializeDefaultData() async {
    try {
      // Add default dua collections if none exist
      if (_duaCollections.isEmpty) {
        await _addDefaultDuaCollections();
      }
      
      // Add sample mosques if none exist
      if (_mosques.isEmpty) {
        await _addSampleMosques();
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Initialize default community data');
    }
  }

  static Future<void> _addDefaultDuaCollections() async {
    final defaultDuas = [
      Dua(
        id: 'dua_1',
        title: 'Morning Remembrance',
        arabicText: 'أَصْبَحْنَا وَأَصْبَحَ الْمُلْكُ لِلَّهِ',
        transliteration: 'Asbahna wa asbahal-mulku lillah',
        translation: 'We have reached the morning and at this very time unto Allah belongs all sovereignty.',
        category: DuaCategory.morning,
        source: 'Muslim',
        benefits: ['Protection', 'Blessing'],
        audioPath: null,
      ),
      Dua(
        id: 'dua_2',
        title: 'Evening Remembrance',
        arabicText: 'أَمْسَيْنَا وَأَمْسَى الْمُلْكُ لِلَّهِ',
        transliteration: 'Amsayna wa amsal-mulku lillah',
        translation: 'We have reached the evening and at this very time unto Allah belongs all sovereignty.',
        category: DuaCategory.evening,
        source: 'Muslim',
        benefits: ['Protection', 'Peace'],
        audioPath: null,
      ),
    ];

    await createDuaCollection(
      name: 'Daily Remembrance',
      description: 'Essential daily duas for morning and evening',
      duas: defaultDuas,
      category: DuaCategory.daily,
      language: 'ar',
    );
  }

  static Future<void> _addSampleMosques() async {
    final sampleMosques = [
      {
        'name': 'Central Mosque',
        'address': '123 Main Street, City Center',
        'latitude': 40.7128,
        'longitude': -74.0060,
        'services': ['Daily Prayers', 'Friday Prayers', 'Islamic Classes'],
      },
      {
        'name': 'Community Islamic Center',
        'address': '456 Oak Avenue, Suburb',
        'latitude': 40.7589,
        'longitude': -73.9851,
        'services': ['Daily Prayers', 'Youth Programs', 'Community Events'],
      },
    ];

    for (final mosque in sampleMosques) {
      await addMosque(
        name: mosque['name'] as String,
        address: mosque['address'] as String,
        latitude: mosque['latitude'] as double,
        longitude: mosque['longitude'] as double,
        services: mosque['services'] as List<String>,
      );
    }
  }

  // Getters
  static List<PrayerGroup> get prayerGroups => List.unmodifiable(_prayerGroups);
  static List<MosqueInfo> get mosques => List.unmodifiable(_mosques);
  static List<IslamicEvent> get events => List.unmodifiable(_events);
  static List<CommunityMember> get members => List.unmodifiable(_members);
  static List<DuaCollection> get duaCollections => List.unmodifiable(_duaCollections);

  // Dispose
  static void dispose() {
    _prayerGroups.clear();
    _mosques.clear();
    _events.clear();
    _members.clear();
    _duaCollections.clear();
  }
}
