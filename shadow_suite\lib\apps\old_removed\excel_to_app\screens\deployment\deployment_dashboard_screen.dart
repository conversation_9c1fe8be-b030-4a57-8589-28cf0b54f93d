import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_theme.dart';
import '../../services/integration_testing_service.dart';
import '../../services/performance_optimizer.dart';

/// Deployment dashboard screen for final integration testing and deployment preparation
class DeploymentDashboardScreen extends ConsumerStatefulWidget {
  const DeploymentDashboardScreen({super.key});

  @override
  ConsumerState<DeploymentDashboardScreen> createState() =>
      _DeploymentDashboardScreenState();
}

class _DeploymentDashboardScreenState
    extends ConsumerState<DeploymentDashboardScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  IntegrationTestReport? _testReport;
  DeploymentReadiness? _deploymentReadiness;
  bool _isRunningTests = false;
  // bool _isCheckingReadiness = false; // Unused variable

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    IntegrationTestingService.initialize();
    _loadInitialData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadInitialData() {
    setState(() {
      _testReport = null;
      _deploymentReadiness = IntegrationTestingService.deploymentReadiness;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Deployment Dashboard'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _runIntegrationTests,
            icon: const Icon(Icons.play_arrow),
            tooltip: 'Run Tests',
          ),
          IconButton(
            onPressed: _checkDeploymentReadiness,
            icon: const Icon(Icons.assessment),
            tooltip: 'Check Readiness',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Overview'),
            Tab(icon: Icon(Icons.bug_report), text: 'Tests'),
            Tab(icon: Icon(Icons.checklist), text: 'Checklist'),
            Tab(icon: Icon(Icons.rocket_launch), text: 'Deploy'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildTestsTab(),
          _buildChecklistTab(),
          _buildDeployTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildReadinessCard(),
          const SizedBox(height: 16),
          _buildQuickStatsCard(),
          const SizedBox(height: 16),
          _buildServiceStatusCard(),
          const SizedBox(height: 16),
          _buildPerformanceCard(),
        ],
      ),
    );
  }

  Widget _buildReadinessCard() {
    final readiness = _deploymentReadiness;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  readiness?.isReady == true
                      ? Icons.check_circle
                      : Icons.warning,
                  color: readiness?.isReady == true
                      ? Colors.green
                      : Colors.orange,
                  size: 32,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Deployment Readiness',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      Text(
                        readiness?.isReady == true
                            ? 'Ready for deployment'
                            : 'Not ready for deployment',
                        style: TextStyle(
                          color: readiness?.isReady == true
                              ? Colors.green
                              : Colors.orange,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                if (readiness != null)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: _getScoreColor(readiness.readinessScore),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      '${readiness.readinessScore.toStringAsFixed(0)}%',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            if (readiness != null) ...[
              const SizedBox(height: 16),
              if (readiness.criticalIssues.isNotEmpty) ...[
                const Text(
                  'Critical Issues:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
                ...readiness.criticalIssues.map(
                  (issue) => Padding(
                    padding: const EdgeInsets.only(left: 16, top: 4),
                    child: Row(
                      children: [
                        const Icon(Icons.error, color: Colors.red, size: 16),
                        const SizedBox(width: 8),
                        Expanded(child: Text(issue)),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 8),
              ],
              if (readiness.warnings.isNotEmpty) ...[
                const Text(
                  'Warnings:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                ),
                ...readiness.warnings.map(
                  (warning) => Padding(
                    padding: const EdgeInsets.only(left: 16, top: 4),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.warning,
                          color: Colors.orange,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(child: Text(warning)),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStatsCard() {
    final testReport = _testReport;
    final performanceMetrics = PerformanceOptimizer.getMetrics();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Statistics',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Tests Passed',
                    testReport != null
                        ? '${testReport.passedTests}/${testReport.totalTests}'
                        : 'N/A',
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Success Rate',
                    testReport != null
                        ? '${(testReport.successRate * 100).toStringAsFixed(1)}%'
                        : 'N/A',
                    Icons.trending_up,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Avg Response',
                    '${_getAverageResponseTime(performanceMetrics)}ms',
                    Icons.speed,
                    Colors.purple,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Operations',
                    '${performanceMetrics.length}',
                    Icons.analytics,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildServiceStatusCard() {
    final serviceStatuses = IntegrationTestingService.serviceStatuses;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Service Status',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            if (serviceStatuses.isEmpty)
              const Text('No service status available')
            else
              ...serviceStatuses.entries.map((entry) {
                final status = entry.value;
                return ListTile(
                  leading: Icon(
                    _getStatusIcon(status.status),
                    color: _getStatusColor(status.status),
                  ),
                  title: Text(status.name),
                  subtitle: Text(
                    status.error ??
                        'Last checked: ${_formatTime(status.lastCheck)}',
                  ),
                  trailing: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(status.status),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      status.status.name.toUpperCase(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                );
              }),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceCard() {
    final metrics = PerformanceOptimizer.getMetrics();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Performance Metrics',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            _buildMetricRow(
              'Total Operations',
              '${_getTotalOperations(metrics)}',
            ),
            _buildMetricRow(
              'Average Execution Time',
              '${_getAverageResponseTime(metrics)}ms',
            ),
            _buildMetricRow(
              'Fastest Operation',
              '${_getFastestOperation(metrics)}ms',
            ),
            _buildMetricRow(
              'Slowest Operation',
              '${_getSlowestOperation(metrics)}ms',
            ),
            _buildMetricRow(
              'Cache Hit Rate',
              '${_getCacheHitRate(metrics).toStringAsFixed(1)}%',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Widget _buildTestsTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _isRunningTests ? null : _runIntegrationTests,
                  icon: _isRunningTests
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.play_arrow),
                  label: Text(
                    _isRunningTests
                        ? 'Running Tests...'
                        : 'Run Integration Tests',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _clearTestResults,
                  icon: const Icon(Icons.clear),
                  label: const Text('Clear Results'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: _testReport != null
                ? _buildTestResults()
                : const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.bug_report, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text('No test results available'),
                        Text('Run integration tests to see results'),
                      ],
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestResults() {
    final report = _testReport!;

    return Column(
      children: [
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        '${report.passedTests}',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      const Text('Passed'),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        '${report.failedTests}',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                      const Text('Failed'),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        '${(report.successRate * 100).toStringAsFixed(1)}%',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                      const Text('Success Rate'),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        '${report.duration.inMilliseconds}ms',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.purple,
                        ),
                      ),
                      const Text('Duration'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: ListView.builder(
            itemCount: report.testResults.length,
            itemBuilder: (context, index) {
              final result = report.testResults[index];
              return Card(
                child: ListTile(
                  leading: Icon(
                    result.passed ? Icons.check_circle : Icons.error,
                    color: result.passed ? Colors.green : Colors.red,
                  ),
                  title: Text(result.testName),
                  subtitle: result.error != null
                      ? Text(
                          result.error!,
                          style: const TextStyle(color: Colors.red),
                        )
                      : Text(_formatTime(result.timestamp)),
                  trailing: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: result.passed ? Colors.green : Colors.red,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      result.passed ? 'PASS' : 'FAIL',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildChecklistTab() {
    final checklist = IntegrationTestingService.generateDeploymentChecklist();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Deployment Checklist',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: checklist.length,
              itemBuilder: (context, index) {
                final item = checklist[index];
                return Card(
                  child: CheckboxListTile(
                    value: item.isCompleted,
                    onChanged: null, // Read-only for now
                    title: Text(item.title),
                    subtitle: Text(item.description),
                    secondary: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: _getPriorityColor(item.priority),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        item.priority.name.toUpperCase(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeployTab() {
    final readiness = _deploymentReadiness;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Icon(
                    Icons.rocket_launch,
                    size: 64,
                    color: readiness?.isReady == true
                        ? Colors.green
                        : Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Excel-to-App Builder',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    readiness?.isReady == true
                        ? 'Ready for deployment!'
                        : 'Complete checklist items before deployment',
                    style: TextStyle(
                      color: readiness?.isReady == true
                          ? Colors.green
                          : Colors.orange,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: readiness?.isReady == true
                              ? _deployToProduction
                              : null,
                          icon: const Icon(Icons.cloud_upload),
                          label: const Text('Deploy to Production'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: _deployToStaging,
                          icon: const Icon(Icons.cloud_queue),
                          label: const Text('Deploy to Staging'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          if (readiness != null && readiness.recommendations.isNotEmpty)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Deployment Recommendations:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    ...readiness.recommendations
                        .take(5)
                        .map(
                          (rec) => Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.lightbulb,
                                  color: Colors.amber,
                                  size: 16,
                                ),
                                const SizedBox(width: 8),
                                Expanded(child: Text(rec)),
                              ],
                            ),
                          ),
                        ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _runIntegrationTests() async {
    setState(() => _isRunningTests = true);

    try {
      final report = await IntegrationTestingService.runIntegrationTests();
      setState(() {
        _testReport = report;
      });

      _showSnackBar(
        'Integration tests completed: ${report.passedTests}/${report.totalTests} passed',
        report.successRate >= 0.95 ? Colors.green : Colors.orange,
      );
    } catch (e) {
      _showSnackBar('Failed to run integration tests: $e', Colors.red);
    } finally {
      setState(() => _isRunningTests = false);
    }
  }

  void _checkDeploymentReadiness() async {
    // setState(() => _isCheckingReadiness = true);

    try {
      final readiness =
          await IntegrationTestingService.checkDeploymentReadiness();
      setState(() {
        _deploymentReadiness = readiness;
        _testReport = readiness.testReport;
      });

      _showSnackBar(
        'Deployment readiness: ${readiness.readinessScore.toStringAsFixed(0)}%',
        readiness.isReady ? Colors.green : Colors.orange,
      );
    } catch (e) {
      _showSnackBar('Failed to check deployment readiness: $e', Colors.red);
    } finally {
      // setState(() => _isCheckingReadiness = false);
    }
  }

  void _clearTestResults() {
    IntegrationTestingService.clearTestResults();
    setState(() {
      _testReport = null;
      _deploymentReadiness = null;
    });
    _showSnackBar('Test results cleared', Colors.blue);
  }

  void _deployToProduction() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Deploy to Production'),
        content: const Text(
          'Are you sure you want to deploy to production? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showSnackBar('Deployment to production initiated', Colors.green);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            child: const Text('Deploy'),
          ),
        ],
      ),
    );
  }

  void _deployToStaging() {
    _showSnackBar('Deployment to staging initiated', Colors.blue);
  }

  Color _getScoreColor(double score) {
    if (score >= 90) return Colors.green;
    if (score >= 70) return Colors.orange;
    return Colors.red;
  }

  IconData _getStatusIcon(ServiceHealthStatus status) {
    switch (status) {
      case ServiceHealthStatus.healthy:
        return Icons.check_circle;
      case ServiceHealthStatus.degraded:
        return Icons.warning;
      case ServiceHealthStatus.unhealthy:
        return Icons.error;
    }
  }

  Color _getStatusColor(ServiceHealthStatus status) {
    switch (status) {
      case ServiceHealthStatus.healthy:
        return Colors.green;
      case ServiceHealthStatus.degraded:
        return Colors.orange;
      case ServiceHealthStatus.unhealthy:
        return Colors.red;
    }
  }

  Color _getPriorityColor(ChecklistPriority priority) {
    switch (priority) {
      case ChecklistPriority.critical:
        return Colors.red;
      case ChecklistPriority.high:
        return Colors.orange;
      case ChecklistPriority.medium:
        return Colors.blue;
      case ChecklistPriority.low:
        return Colors.grey;
    }
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Get average response time from performance metrics map
  int _getAverageResponseTime(Map<String, PerformanceMetric> metrics) {
    if (metrics.isEmpty) return 0;

    final totalTime = metrics.values.fold(
      0.0,
      (sum, metric) => sum + metric.averageTime,
    );
    return (totalTime / metrics.length).round();
  }

  /// Get total operations from performance metrics map
  int _getTotalOperations(Map<String, PerformanceMetric> metrics) {
    return metrics.values.fold(0, (sum, metric) => sum + metric.callCount);
  }

  /// Get fastest operation time from performance metrics map
  int _getFastestOperation(Map<String, PerformanceMetric> metrics) {
    if (metrics.isEmpty) return 0;
    return metrics.values.map((m) => m.minTime).reduce((a, b) => a < b ? a : b);
  }

  /// Get slowest operation time from performance metrics map
  int _getSlowestOperation(Map<String, PerformanceMetric> metrics) {
    if (metrics.isEmpty) return 0;
    return metrics.values.map((m) => m.maxTime).reduce((a, b) => a > b ? a : b);
  }

  /// Calculate cache hit rate from performance metrics map
  double _getCacheHitRate(Map<String, PerformanceMetric> metrics) {
    if (metrics.isEmpty) return 0.0;

    final totalCalls = metrics.values.fold(
      0,
      (sum, metric) => sum + metric.callCount,
    );
    final totalErrors = metrics.values.fold(
      0,
      (sum, metric) => sum + metric.errorCount,
    );

    if (totalCalls == 0) return 0.0;
    return ((totalCalls - totalErrors) / totalCalls) * 100;
  }
}
