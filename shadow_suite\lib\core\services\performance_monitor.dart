import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';

class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  final Map<String, Stopwatch> _timers = {};
  final List<PerformanceMetric> _metrics = [];
  final Map<String, int> _counters = {};
  Timer? _memoryMonitorTimer;
  bool _isMonitoring = false;

  // Performance thresholds
  static const int maxStartupTime = 3000; // 3 seconds
  static const int maxNavigationTime = 300; // 300ms
  static const int maxDatabaseQueryTime = 100; // 100ms
  static const int maxUIRenderTime = 16; // 16ms (60fps)
  static const int maxMemoryUsageMB = 512; // 512MB
  static const double maxCPUUsagePercent = 80.0; // 80%

  // Initialize performance monitoring
  void initialize() {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    _startMemoryMonitoring();
    _setupFrameCallbacks();
    
    if (kDebugMode) {
      debugPrint('Performance monitoring initialized');
    }
  }

  // Stop performance monitoring
  void dispose() {
    _isMonitoring = false;
    _memoryMonitorTimer?.cancel();
    _timers.clear();
    _metrics.clear();
    _counters.clear();
  }

  // Start timing an operation
  void startTimer(String operation) {
    _timers[operation] = Stopwatch()..start();
  }

  // Stop timing an operation and record the metric
  void stopTimer(String operation, {Map<String, dynamic>? metadata}) {
    final timer = _timers[operation];
    if (timer != null) {
      timer.stop();
      final duration = timer.elapsedMilliseconds;
      
      _recordMetric(PerformanceMetric(
        operation: operation,
        duration: duration,
        timestamp: DateTime.now(),
        metadata: metadata,
      ));
      
      _timers.remove(operation);
      
      // Check against thresholds
      _checkThreshold(operation, duration);
    }
  }

  // Record a custom metric
  void recordMetric(String operation, int duration, {Map<String, dynamic>? metadata}) {
    _recordMetric(PerformanceMetric(
      operation: operation,
      duration: duration,
      timestamp: DateTime.now(),
      metadata: metadata,
    ));
  }

  // Increment a counter
  void incrementCounter(String counter) {
    _counters[counter] = (_counters[counter] ?? 0) + 1;
  }

  // Get current performance statistics
  PerformanceStats getStats() {
    final now = DateTime.now();
    final recentMetrics = _metrics.where(
      (m) => now.difference(m.timestamp).inMinutes < 5,
    ).toList();

    return PerformanceStats(
      totalOperations: _metrics.length,
      recentOperations: recentMetrics.length,
      averageResponseTime: _calculateAverageResponseTime(recentMetrics),
      slowestOperations: _getSlowOperations(recentMetrics),
      memoryUsage: _getCurrentMemoryUsage(),
      counters: Map.from(_counters),
      generatedAt: now,
    );
  }

  // Get performance report
  PerformanceReport generateReport() {
    final stats = getStats();
    final issues = _identifyPerformanceIssues();
    final recommendations = _generateRecommendations(issues);

    return PerformanceReport(
      stats: stats,
      issues: issues,
      recommendations: recommendations,
      generatedAt: DateTime.now(),
    );
  }

  // Monitor app startup performance
  void monitorAppStartup() {
    startTimer('app_startup');
    
    // Monitor until first frame is rendered
    WidgetsBinding.instance.addPostFrameCallback((_) {
      stopTimer('app_startup');
    });
  }

  // Monitor navigation performance
  void monitorNavigation(String route) {
    startTimer('navigation_$route');
    
    // Stop timer after next frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      stopTimer('navigation_$route');
    });
  }

  // Monitor database operation performance
  Future<T> monitorDatabaseOperation<T>(String operation, Future<T> Function() operation_) async {
    startTimer('db_$operation');
    try {
      final result = await operation_();
      stopTimer('db_$operation');
      return result;
    } catch (e) {
      stopTimer('db_$operation', metadata: {'error': e.toString()});
      rethrow;
    }
  }

  // Monitor widget build performance
  void monitorWidgetBuild(String widgetName, VoidCallback buildFunction) {
    startTimer('widget_build_$widgetName');
    buildFunction();
    stopTimer('widget_build_$widgetName');
  }

  // Private methods
  void _recordMetric(PerformanceMetric metric) {
    _metrics.add(metric);
    
    // Keep only last 1000 metrics to prevent memory issues
    if (_metrics.length > 1000) {
      _metrics.removeRange(0, _metrics.length - 1000);
    }
    
    if (kDebugMode && metric.duration > 100) {
      debugPrint('Performance: ${metric.operation} took ${metric.duration}ms');
    }
  }

  void _checkThreshold(String operation, int duration) {
    String? warning;
    
    if (operation == 'app_startup' && duration > maxStartupTime) {
      warning = 'App startup took ${duration}ms (threshold: ${maxStartupTime}ms)';
    } else if (operation.startsWith('navigation_') && duration > maxNavigationTime) {
      warning = 'Navigation took ${duration}ms (threshold: ${maxNavigationTime}ms)';
    } else if (operation.startsWith('db_') && duration > maxDatabaseQueryTime) {
      warning = 'Database operation took ${duration}ms (threshold: ${maxDatabaseQueryTime}ms)';
    } else if (operation.startsWith('widget_build_') && duration > maxUIRenderTime) {
      warning = 'Widget build took ${duration}ms (threshold: ${maxUIRenderTime}ms)';
    }
    
    if (warning != null && kDebugMode) {
      debugPrint('Performance Warning: $warning');
    }
  }

  void _startMemoryMonitoring() {
    _memoryMonitorTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      final memoryUsage = _getCurrentMemoryUsage();
      
      recordMetric('memory_usage', memoryUsage.toInt(), metadata: {
        'timestamp': DateTime.now().toIso8601String(),
      });
      
      if (memoryUsage > maxMemoryUsageMB && kDebugMode) {
        debugPrint('Performance Warning: High memory usage: ${memoryUsage.toStringAsFixed(1)}MB');
      }
    });
  }

  void _setupFrameCallbacks() {
    if (kDebugMode) {
      // Monitor frame rendering performance
      WidgetsBinding.instance.addPersistentFrameCallback((timeStamp) {
        // Record frame timing if needed
      });
    }
  }

  double _calculateAverageResponseTime(List<PerformanceMetric> metrics) {
    if (metrics.isEmpty) return 0.0;
    
    final total = metrics.fold<int>(0, (sum, metric) => sum + metric.duration);
    return total / metrics.length;
  }

  List<PerformanceMetric> _getSlowOperations(List<PerformanceMetric> metrics) {
    final sorted = List<PerformanceMetric>.from(metrics)
      ..sort((a, b) => b.duration.compareTo(a.duration));
    
    return sorted.take(5).toList();
  }

  double _getCurrentMemoryUsage() {
    // This is a simplified implementation
    // In a real app, you might use platform-specific methods
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        // Use platform channels to get actual memory usage
        return 0.0; // Placeholder
      } else {
        // For desktop platforms, estimate based on Dart VM
        return ProcessInfo.currentRss / (1024 * 1024); // Convert to MB
      }
    } catch (e) {
      return 0.0;
    }
  }

  List<PerformanceIssue> _identifyPerformanceIssues() {
    final issues = <PerformanceIssue>[];
    final recentMetrics = _metrics.where(
      (m) => DateTime.now().difference(m.timestamp).inMinutes < 10,
    ).toList();

    // Check for slow operations
    final slowOperations = recentMetrics.where((m) => m.duration > 500).toList();
    if (slowOperations.isNotEmpty) {
      issues.add(PerformanceIssue(
        type: IssueType.slowOperation,
        severity: IssueSeverity.medium,
        description: 'Found ${slowOperations.length} operations taking >500ms',
        affectedOperations: slowOperations.map((m) => m.operation).toSet().toList(),
      ));
    }

    // Check for memory issues
    final memoryMetrics = recentMetrics.where((m) => m.operation == 'memory_usage').toList();
    if (memoryMetrics.any((m) => m.duration > maxMemoryUsageMB)) {
      issues.add(PerformanceIssue(
        type: IssueType.highMemoryUsage,
        severity: IssueSeverity.high,
        description: 'Memory usage exceeded ${maxMemoryUsageMB}MB threshold',
        affectedOperations: ['memory_usage'],
      ));
    }

    // Check for frequent operations
    final operationCounts = <String, int>{};
    for (final metric in recentMetrics) {
      operationCounts[metric.operation] = (operationCounts[metric.operation] ?? 0) + 1;
    }

    final frequentOps = operationCounts.entries.where((e) => e.value > 100).toList();
    if (frequentOps.isNotEmpty) {
      issues.add(PerformanceIssue(
        type: IssueType.frequentOperations,
        severity: IssueSeverity.low,
        description: 'Some operations are called very frequently',
        affectedOperations: frequentOps.map((e) => e.key).toList(),
      ));
    }

    return issues;
  }

  List<String> _generateRecommendations(List<PerformanceIssue> issues) {
    final recommendations = <String>[];

    for (final issue in issues) {
      switch (issue.type) {
        case IssueType.slowOperation:
          recommendations.add('Consider optimizing slow operations: ${issue.affectedOperations.join(", ")}');
          recommendations.add('Add caching for frequently accessed data');
          recommendations.add('Use lazy loading for large datasets');
          break;
          
        case IssueType.highMemoryUsage:
          recommendations.add('Review memory usage and dispose of unused objects');
          recommendations.add('Implement image caching and compression');
          recommendations.add('Use pagination for large lists');
          break;
          
        case IssueType.frequentOperations:
          recommendations.add('Consider batching frequent operations: ${issue.affectedOperations.join(", ")}');
          recommendations.add('Implement debouncing for user input operations');
          recommendations.add('Use memoization for expensive calculations');
          break;
      }
    }

    // General recommendations
    if (recommendations.isEmpty) {
      recommendations.add('Performance is within acceptable limits');
      recommendations.add('Continue monitoring for potential issues');
    }

    return recommendations;
  }

  // Export performance data
  Map<String, dynamic> exportData() {
    return {
      'metrics': _metrics.map((m) => m.toJson()).toList(),
      'counters': _counters,
      'stats': getStats().toJson(),
      'exportedAt': DateTime.now().toIso8601String(),
    };
  }

  // Clear performance data
  void clearData() {
    _metrics.clear();
    _counters.clear();
    
    if (kDebugMode) {
      debugPrint('Performance data cleared');
    }
  }
}

// Data classes
class PerformanceMetric {
  final String operation;
  final int duration;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  const PerformanceMetric({
    required this.operation,
    required this.duration,
    required this.timestamp,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'operation': operation,
      'duration': duration,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }
}

class PerformanceStats {
  final int totalOperations;
  final int recentOperations;
  final double averageResponseTime;
  final List<PerformanceMetric> slowestOperations;
  final double memoryUsage;
  final Map<String, int> counters;
  final DateTime generatedAt;

  const PerformanceStats({
    required this.totalOperations,
    required this.recentOperations,
    required this.averageResponseTime,
    required this.slowestOperations,
    required this.memoryUsage,
    required this.counters,
    required this.generatedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'totalOperations': totalOperations,
      'recentOperations': recentOperations,
      'averageResponseTime': averageResponseTime,
      'slowestOperations': slowestOperations.map((m) => m.toJson()).toList(),
      'memoryUsage': memoryUsage,
      'counters': counters,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }
}

class PerformanceReport {
  final PerformanceStats stats;
  final List<PerformanceIssue> issues;
  final List<String> recommendations;
  final DateTime generatedAt;

  const PerformanceReport({
    required this.stats,
    required this.issues,
    required this.recommendations,
    required this.generatedAt,
  });
}

class PerformanceIssue {
  final IssueType type;
  final IssueSeverity severity;
  final String description;
  final List<String> affectedOperations;

  const PerformanceIssue({
    required this.type,
    required this.severity,
    required this.description,
    required this.affectedOperations,
  });
}

enum IssueType { slowOperation, highMemoryUsage, frequentOperations }
enum IssueSeverity { low, medium, high }

// Platform-specific process info
class ProcessInfo {
  static int get currentRss {
    // This would be implemented with platform-specific code
    // For now, return a mock value
    return 100 * 1024 * 1024; // 100MB
  }
}
