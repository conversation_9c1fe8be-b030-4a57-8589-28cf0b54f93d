import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/excel_app_tool.dart';
import '../services/excel_app_providers.dart';
import 'excel_to_app_main.dart';
import 'excel_app_tool_card.dart';
import 'excel_app_create_tool_dialog.dart';
import 'tool_runtime_screen.dart';

class ExcelAppDashboard extends ConsumerStatefulWidget {
  const ExcelAppDashboard({super.key});

  @override
  ConsumerState<ExcelAppDashboard> createState() => _ExcelAppDashboardState();
}

class _ExcelAppDashboardState extends ConsumerState<ExcelAppDashboard> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Initialize the database and load tools
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(excelAppDatabaseProvider).initialize();
      ref.read(excelAppToolsProvider.notifier).loadTools();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final toolsAsync = ref.watch(filteredToolsProvider);
    // final searchQuery = ref.watch(toolsSearchQueryProvider); // Reserved for search functionality
    // final filter = ref.watch(toolsFilterProvider); // Reserved for filter functionality

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Column(
        children: [
          _buildHeader(context),
          _buildSearchAndFilters(context),
          Expanded(child: _buildToolsGrid(context, toolsAsync)),
        ],
      ),
      floatingActionButton: _buildCreateToolFAB(context),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return ExcelToAppHeader(
      title: 'Excel to App',
      subtitle: 'Transform your spreadsheets into interactive mobile apps',
      actions: [
        IconButton(
          onPressed: () => _showImportDialog(context),
          icon: const Icon(Icons.file_upload),
          tooltip: 'Import Tools',
        ),
        const SizedBox(width: 8),
        IconButton(
          onPressed: () => _refreshTools(),
          icon: const Icon(Icons.refresh),
          tooltip: 'Refresh',
        ),
      ],
    );
  }

  Widget _buildSearchAndFilters(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search tools...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          _searchController.clear();
                          ref.read(toolsSearchQueryProvider.notifier).state =
                              '';
                        },
                        icon: const Icon(Icons.clear),
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.white,
              ),
              onChanged: (value) {
                ref.read(toolsSearchQueryProvider.notifier).state = value;
              },
            ),
          ),
          const SizedBox(width: 16),
          _buildFilterDropdown(),
        ],
      ),
    );
  }

  Widget _buildFilterDropdown() {
    final filter = ref.watch(toolsFilterProvider);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: filter,
          items: const [
            DropdownMenuItem(value: 'all', child: Text('All Tools')),
            DropdownMenuItem(value: 'recent', child: Text('Recent')),
            DropdownMenuItem(value: 'favorites', child: Text('Favorites')),
          ],
          onChanged: (value) {
            if (value != null) {
              ref.read(toolsFilterProvider.notifier).state = value;
            }
          },
        ),
      ),
    );
  }

  Widget _buildToolsGrid(
    BuildContext context,
    AsyncValue<List<ExcelAppTool>> toolsAsync,
  ) {
    return toolsAsync.when(
      data: (tools) {
        if (tools.isEmpty) {
          return _buildEmptyState(context);
        }

        return Padding(
          padding: const EdgeInsets.all(16),
          child: GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: _getCrossAxisCount(context),
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 0.8,
            ),
            itemCount: tools.length,
            itemBuilder: (context, index) {
              return ExcelAppToolCard(
                tool: tools[index],
                onTap: () => _runTool(tools[index]),
                onEdit: () => _editTool(tools[index]),
                onDuplicate: () => _duplicateTool(tools[index]),
                onDelete: () => _deleteTool(tools[index]),
                onShare: () => _shareTool(tools[index]),
              );
            },
          ),
        );
      },
      loading: () => const ExcelToAppLoading(message: 'Loading your tools...'),
      error: (error, stackTrace) => ExcelToAppError(
        message: 'Failed to load tools: $error',
        onRetry: _refreshTools,
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final searchQuery = ref.watch(toolsSearchQueryProvider);

    if (searchQuery.isNotEmpty) {
      return ExcelToAppEmpty(
        title: 'No tools found',
        message:
            'No tools match your search criteria.\nTry adjusting your search terms.',
        icon: Icons.search_off,
      );
    }

    return ExcelToAppEmpty(
      title: 'No tools yet',
      message:
          'Create your first Excel-to-App tool to get started.\nTransform spreadsheets into interactive mobile apps!',
      icon: Icons.table_view,
      actionText: 'Create Your First Tool',
      onAction: () => _showCreateToolDialog(context),
    );
  }

  Widget _buildCreateToolFAB(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: () => _showCreateToolDialog(context),
      backgroundColor: const Color(0xFF3498DB),
      foregroundColor: Colors.white,
      icon: const Icon(Icons.add),
      label: const Text('Create Tool'),
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 4;
    if (width > 800) return 3;
    if (width > 600) return 2;
    return 1;
  }

  void _refreshTools() {
    ref.read(excelAppToolsProvider.notifier).loadTools();
  }

  void _showCreateToolDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const ExcelAppCreateToolDialog(),
    );
  }

  void _showImportDialog(BuildContext context) {
    ref.read(excelToAppCurrentScreenProvider.notifier).state =
        ExcelToAppScreen.importTools;
  }

  void _runTool(ExcelAppTool tool) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: SizedBox(
          width: 600,
          height: 500,
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: const BoxDecoration(
                  color: Color(0xFF3498DB),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.play_arrow, color: Colors.white),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Run Tool: ${tool.name}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close, color: Colors.white),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () {
                                Navigator.of(context).pop();
                                _runToolInPopup(tool);
                              },
                              icon: const Icon(Icons.open_in_new),
                              label: const Text('Run in Popup'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF27AE60),
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () {
                                Navigator.of(context).pop();
                                _runToolFullScreen(tool);
                              },
                              icon: const Icon(Icons.fullscreen),
                              label: const Text('Run Full Screen'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF3498DB),
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.blue.shade200),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Tool Information',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF2C3E50),
                              ),
                            ),
                            const SizedBox(height: 8),
                            _buildInfoRow(
                              'Components',
                              '${tool.uiComponents.length}',
                            ),
                            _buildInfoRow(
                              'Spreadsheet Size',
                              '${tool.spreadsheet.columns}x${tool.spreadsheet.rows}',
                            ),
                            _buildInfoRow('Security', tool.securityType.name),
                            _buildInfoRow(
                              'Last Modified',
                              _formatDate(tool.lastModified),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _editTool(ExcelAppTool tool) {
    // Set the current tool and navigate to create screen (which handles editing)
    ref.read(currentExcelAppToolProvider.notifier).setTool(tool);
    ref.read(excelToAppCurrentScreenProvider.notifier).state =
        ExcelToAppScreen.createTool;
  }

  void _duplicateTool(ExcelAppTool tool) {
    showDialog(
      context: context,
      builder: (context) => _DuplicateToolDialog(tool: tool),
    );
  }

  void _deleteTool(ExcelAppTool tool) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Tool'),
        content: Text(
          'Are you sure you want to delete "${tool.name}"?\nThis action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(excelAppToolsProvider.notifier).deleteTool(tool.id);
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('${tool.name} deleted'),
                  backgroundColor: const Color(0xFFE74C3C),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFE74C3C),
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _runToolInPopup(ExcelAppTool tool) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => ToolRuntimeScreen(tool: tool, isPopup: true),
    );
  }

  void _runToolFullScreen(ExcelAppTool tool) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ToolRuntimeScreen(tool: tool, isPopup: false),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              color: Color(0xFF7F8C8D),
            ),
          ),
          Text(value, style: const TextStyle(color: Color(0xFF2C3E50))),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _shareTool(ExcelAppTool tool) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.share, color: Color(0xFF3498DB)),
            const SizedBox(width: 8),
            Text('Share ${tool.name}'),
          ],
        ),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Choose how to share this tool:',
                style: TextStyle(color: Color(0xFF7F8C8D)),
              ),
              const SizedBox(height: 20),
              _buildShareOption(
                Icons.link,
                'Generate Share Link',
                'Create a shareable link for this tool',
                () => _generateShareLink(tool),
              ),
              _buildShareOption(
                Icons.file_download,
                'Export Tool File',
                'Download tool as a file to share manually',
                () => _exportToolFile(tool),
              ),
              _buildShareOption(
                Icons.qr_code,
                'QR Code',
                'Generate QR code for easy mobile sharing',
                () => _generateQRCode(tool),
              ),
              _buildShareOption(
                Icons.email,
                'Send via Email',
                'Share tool through email',
                () => _shareViaEmail(tool),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildShareOption(
    IconData icon,
    String title,
    String description,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: () {
        Navigator.pop(context);
        onTap();
      },
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF3498DB).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(icon, color: const Color(0xFF3498DB), size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF2C3E50),
                    ),
                  ),
                  Text(
                    description,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFF7F8C8D),
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Color(0xFF95A5A6),
            ),
          ],
        ),
      ),
    );
  }

  void _generateShareLink(ExcelAppTool tool) {
    final shareLink = 'https://shadowsuite.app/tools/${tool.id}';
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.link, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text('Share link: $shareLink')),
            TextButton(
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Link copied to clipboard'),
                    backgroundColor: Color(0xFF27AE60),
                  ),
                );
              },
              child: const Text('COPY', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
        backgroundColor: const Color(0xFF3498DB),
        duration: const Duration(seconds: 5),
      ),
    );
  }

  void _exportToolFile(ExcelAppTool tool) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Exporting ${tool.name}.xlsx...'),
        backgroundColor: const Color(0xFF27AE60),
      ),
    );
  }

  void _generateQRCode(ExcelAppTool tool) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('QR Code'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                border: Border.all(color: const Color(0xFFE9ECEF)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.qr_code, size: 64, color: Color(0xFF95A5A6)),
                    SizedBox(height: 8),
                    Text('QR Code would appear here'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text('Scan to access ${tool.name}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _shareViaEmail(ExcelAppTool tool) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening email to share ${tool.name}...'),
        backgroundColor: const Color(0xFF3498DB),
      ),
    );
  }
}

class _DuplicateToolDialog extends ConsumerStatefulWidget {
  final ExcelAppTool tool;

  const _DuplicateToolDialog({required this.tool});

  @override
  ConsumerState<_DuplicateToolDialog> createState() =>
      _DuplicateToolDialogState();
}

class _DuplicateToolDialogState extends ConsumerState<_DuplicateToolDialog> {
  late TextEditingController _nameController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: '${widget.tool.name} (Copy)');
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Duplicate Tool'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text('Create a copy of "${widget.tool.name}"'),
          const SizedBox(height: 16),
          TextField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'New tool name',
              border: OutlineInputBorder(),
            ),
            autofocus: true,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _duplicateTool,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Duplicate'),
        ),
      ],
    );
  }

  void _duplicateTool() async {
    if (_nameController.text.trim().isEmpty) return;

    setState(() => _isLoading = true);

    try {
      await ref
          .read(excelAppToolsProvider.notifier)
          .duplicateTool(widget.tool.id, _nameController.text.trim());

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${_nameController.text.trim()} created'),
            backgroundColor: const Color(0xFF27AE60),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to duplicate tool: $e'),
            backgroundColor: const Color(0xFFE74C3C),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
