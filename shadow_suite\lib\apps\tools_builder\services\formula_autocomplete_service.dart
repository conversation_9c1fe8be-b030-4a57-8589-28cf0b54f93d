
// Formula Auto-Complete Service for Excel-like functionality
class FormulaAutoCompleteService {
  static const Map<String, FunctionDefinition> _functions = {
    // Mathematical Functions
    'SUM': FunctionDefinition(
      name: 'SUM',
      description: 'Adds all numbers in a range of cells',
      syntax: 'SUM(number1, [number2], ...)',
      category: 'Math',
      parameters: ['number1', 'number2 (optional)'],
      example: 'SUM(A1:A10)',
    ),
    'AVERAGE': FunctionDefinition(
      name: 'AVERAGE',
      description: 'Returns the average of numbers',
      syntax: 'AVERAGE(number1, [number2], ...)',
      category: 'Statistical',
      parameters: ['number1', 'number2 (optional)'],
      example: 'AVERAGE(A1:A10)',
    ),
    'COUNT': FunctionDefinition(
      name: 'COUNT',
      description: 'Counts the number of cells that contain numbers',
      syntax: 'COUNT(value1, [value2], ...)',
      category: 'Statistical',
      parameters: ['value1', 'value2 (optional)'],
      example: 'COUNT(A1:A10)',
    ),
    'MAX': FunctionDefinition(
      name: 'MAX',
      description: 'Returns the largest value in a set of values',
      syntax: 'MAX(number1, [number2], ...)',
      category: 'Statistical',
      parameters: ['number1', 'number2 (optional)'],
      example: 'MAX(A1:A10)',
    ),
    'MIN': FunctionDefinition(
      name: 'MIN',
      description: 'Returns the smallest value in a set of values',
      syntax: 'MIN(number1, [number2], ...)',
      category: 'Statistical',
      parameters: ['number1', 'number2 (optional)'],
      example: 'MIN(A1:A10)',
    ),
    'SQRT': FunctionDefinition(
      name: 'SQRT',
      description: 'Returns the square root of a number',
      syntax: 'SQRT(number)',
      category: 'Math',
      parameters: ['number'],
      example: 'SQRT(16)',
    ),
    'POWER': FunctionDefinition(
      name: 'POWER',
      description: 'Returns the result of a number raised to a power',
      syntax: 'POWER(number, power)',
      category: 'Math',
      parameters: ['number', 'power'],
      example: 'POWER(2, 3)',
    ),
    'ABS': FunctionDefinition(
      name: 'ABS',
      description: 'Returns the absolute value of a number',
      syntax: 'ABS(number)',
      category: 'Math',
      parameters: ['number'],
      example: 'ABS(-5)',
    ),
    'ROUND': FunctionDefinition(
      name: 'ROUND',
      description: 'Rounds a number to a specified number of digits',
      syntax: 'ROUND(number, num_digits)',
      category: 'Math',
      parameters: ['number', 'num_digits'],
      example: 'ROUND(3.14159, 2)',
    ),

    // Logical Functions
    'IF': FunctionDefinition(
      name: 'IF',
      description: 'Returns one value if condition is true, another if false',
      syntax: 'IF(logical_test, value_if_true, value_if_false)',
      category: 'Logical',
      parameters: ['logical_test', 'value_if_true', 'value_if_false'],
      example: 'IF(A1>10, "High", "Low")',
    ),
    'AND': FunctionDefinition(
      name: 'AND',
      description: 'Returns TRUE if all arguments are TRUE',
      syntax: 'AND(logical1, [logical2], ...)',
      category: 'Logical',
      parameters: ['logical1', 'logical2 (optional)'],
      example: 'AND(A1>0, B1<100)',
    ),
    'OR': FunctionDefinition(
      name: 'OR',
      description: 'Returns TRUE if any argument is TRUE',
      syntax: 'OR(logical1, [logical2], ...)',
      category: 'Logical',
      parameters: ['logical1', 'logical2 (optional)'],
      example: 'OR(A1>0, B1<100)',
    ),
    'NOT': FunctionDefinition(
      name: 'NOT',
      description: 'Reverses the logic of its argument',
      syntax: 'NOT(logical)',
      category: 'Logical',
      parameters: ['logical'],
      example: 'NOT(A1>10)',
    ),

    // Text Functions
    'CONCATENATE': FunctionDefinition(
      name: 'CONCATENATE',
      description: 'Joins several text strings into one text string',
      syntax: 'CONCATENATE(text1, [text2], ...)',
      category: 'Text',
      parameters: ['text1', 'text2 (optional)'],
      example: 'CONCATENATE("Hello", " ", "World")',
    ),
    'LEFT': FunctionDefinition(
      name: 'LEFT',
      description: 'Returns the leftmost characters from a text value',
      syntax: 'LEFT(text, [num_chars])',
      category: 'Text',
      parameters: ['text', 'num_chars (optional)'],
      example: 'LEFT("Hello", 2)',
    ),
    'RIGHT': FunctionDefinition(
      name: 'RIGHT',
      description: 'Returns the rightmost characters from a text value',
      syntax: 'RIGHT(text, [num_chars])',
      category: 'Text',
      parameters: ['text', 'num_chars (optional)'],
      example: 'RIGHT("Hello", 2)',
    ),
    'MID': FunctionDefinition(
      name: 'MID',
      description: 'Returns characters from the middle of a text string',
      syntax: 'MID(text, start_num, num_chars)',
      category: 'Text',
      parameters: ['text', 'start_num', 'num_chars'],
      example: 'MID("Hello", 2, 3)',
    ),
    'LEN': FunctionDefinition(
      name: 'LEN',
      description: 'Returns the number of characters in a text string',
      syntax: 'LEN(text)',
      category: 'Text',
      parameters: ['text'],
      example: 'LEN("Hello")',
    ),
    'UPPER': FunctionDefinition(
      name: 'UPPER',
      description: 'Converts text to uppercase',
      syntax: 'UPPER(text)',
      category: 'Text',
      parameters: ['text'],
      example: 'UPPER("hello")',
    ),
    'LOWER': FunctionDefinition(
      name: 'LOWER',
      description: 'Converts text to lowercase',
      syntax: 'LOWER(text)',
      category: 'Text',
      parameters: ['text'],
      example: 'LOWER("HELLO")',
    ),

    // Lookup Functions
    'VLOOKUP': FunctionDefinition(
      name: 'VLOOKUP',
      description: 'Looks up a value in the first column and returns a value in the same row',
      syntax: 'VLOOKUP(lookup_value, table_array, col_index_num, [range_lookup])',
      category: 'Lookup',
      parameters: ['lookup_value', 'table_array', 'col_index_num', 'range_lookup (optional)'],
      example: 'VLOOKUP(A1, B:D, 2, FALSE)',
    ),
    'HLOOKUP': FunctionDefinition(
      name: 'HLOOKUP',
      description: 'Looks up a value in the top row and returns a value in the same column',
      syntax: 'HLOOKUP(lookup_value, table_array, row_index_num, [range_lookup])',
      category: 'Lookup',
      parameters: ['lookup_value', 'table_array', 'row_index_num', 'range_lookup (optional)'],
      example: 'HLOOKUP(A1, B1:D5, 2, FALSE)',
    ),
    'INDEX': FunctionDefinition(
      name: 'INDEX',
      description: 'Returns a value from a table based on row and column numbers',
      syntax: 'INDEX(array, row_num, [column_num])',
      category: 'Lookup',
      parameters: ['array', 'row_num', 'column_num (optional)'],
      example: 'INDEX(A1:C10, 5, 2)',
    ),
    'MATCH': FunctionDefinition(
      name: 'MATCH',
      description: 'Returns the relative position of an item in an array',
      syntax: 'MATCH(lookup_value, lookup_array, [match_type])',
      category: 'Lookup',
      parameters: ['lookup_value', 'lookup_array', 'match_type (optional)'],
      example: 'MATCH("Apple", A1:A10, 0)',
    ),

    // Date Functions
    'TODAY': FunctionDefinition(
      name: 'TODAY',
      description: 'Returns the current date',
      syntax: 'TODAY()',
      category: 'Date',
      parameters: [],
      example: 'TODAY()',
    ),
    'NOW': FunctionDefinition(
      name: 'NOW',
      description: 'Returns the current date and time',
      syntax: 'NOW()',
      category: 'Date',
      parameters: [],
      example: 'NOW()',
    ),
    'YEAR': FunctionDefinition(
      name: 'YEAR',
      description: 'Returns the year of a date',
      syntax: 'YEAR(serial_number)',
      category: 'Date',
      parameters: ['serial_number'],
      example: 'YEAR(TODAY())',
    ),
    'MONTH': FunctionDefinition(
      name: 'MONTH',
      description: 'Returns the month of a date',
      syntax: 'MONTH(serial_number)',
      category: 'Date',
      parameters: ['serial_number'],
      example: 'MONTH(TODAY())',
    ),
    'DAY': FunctionDefinition(
      name: 'DAY',
      description: 'Returns the day of a date',
      syntax: 'DAY(serial_number)',
      category: 'Date',
      parameters: ['serial_number'],
      example: 'DAY(TODAY())',
    ),
  };

  // Get function suggestions based on input
  static List<FunctionDefinition> getSuggestions(String input) {
    if (input.isEmpty) {
      return _functions.values.toList()..sort((a, b) => a.name.compareTo(b.name));
    }

    final upperInput = input.toUpperCase();
    final suggestions = _functions.values
        .where((func) => func.name.startsWith(upperInput))
        .toList();

    // Sort by relevance (exact match first, then alphabetical)
    suggestions.sort((a, b) {
      if (a.name == upperInput) return -1;
      if (b.name == upperInput) return 1;
      return a.name.compareTo(b.name);
    });

    return suggestions;
  }

  // Get function definition by name
  static FunctionDefinition? getFunction(String name) {
    return _functions[name.toUpperCase()];
  }

  // Get all function categories
  static List<String> getCategories() {
    final categories = _functions.values.map((f) => f.category).toSet().toList();
    categories.sort();
    return categories;
  }

  // Get functions by category
  static List<FunctionDefinition> getFunctionsByCategory(String category) {
    return _functions.values
        .where((f) => f.category == category)
        .toList()
      ..sort((a, b) => a.name.compareTo(b.name));
  }

  // Validate formula syntax
  static FormulaValidationResult validateFormula(String formula) {
    if (formula.isEmpty) {
      return FormulaValidationResult(isValid: false, error: 'Formula cannot be empty');
    }

    if (!formula.startsWith('=')) {
      return FormulaValidationResult(isValid: false, error: 'Formula must start with =');
    }

    final formulaContent = formula.substring(1);
    
    // Check for balanced parentheses
    int openParens = 0;
    for (int i = 0; i < formulaContent.length; i++) {
      if (formulaContent[i] == '(') {
        openParens++;
      } else if (formulaContent[i] == ')') {
        openParens--;
        if (openParens < 0) {
          return FormulaValidationResult(
            isValid: false, 
            error: 'Unmatched closing parenthesis at position ${i + 1}'
          );
        }
      }
    }

    if (openParens > 0) {
      return FormulaValidationResult(
        isValid: false, 
        error: 'Missing $openParens closing parenthesis${openParens > 1 ? 'es' : ''}'
      );
    }

    // Check for valid function names
    final functionPattern = RegExp(r'([A-Z]+)\s*\(');
    final matches = functionPattern.allMatches(formulaContent);
    
    for (final match in matches) {
      final functionName = match.group(1)!;
      if (!_functions.containsKey(functionName)) {
        return FormulaValidationResult(
          isValid: false, 
          error: 'Unknown function: $functionName'
        );
      }
    }

    return FormulaValidationResult(isValid: true);
  }

  // Parse cell references from formula
  static List<String> extractCellReferences(String formula) {
    final cellPattern = RegExp(r'\$?[A-Z]+\$?[0-9]+(?::\$?[A-Z]+\$?[0-9]+)?');
    final matches = cellPattern.allMatches(formula);
    return matches.map((m) => m.group(0)!).toList();
  }

  // Check for circular references
  static bool hasCircularReference(String cellAddress, String formula, Map<String, String> allFormulas) {
    final references = extractCellReferences(formula);
    return _checkCircularRecursive(cellAddress, references, allFormulas, <String>{});
  }

  static bool _checkCircularRecursive(
    String currentCell, 
    List<String> references, 
    Map<String, String> allFormulas, 
    Set<String> visited
  ) {
    if (visited.contains(currentCell)) {
      return true; // Circular reference found
    }

    visited.add(currentCell);

    for (final ref in references) {
      if (ref == currentCell) {
        return true; // Direct self-reference
      }

      final refFormula = allFormulas[ref];
      if (refFormula != null && refFormula.startsWith('=')) {
        final refReferences = extractCellReferences(refFormula);
        if (_checkCircularRecursive(currentCell, refReferences, allFormulas, Set.from(visited))) {
          return true;
        }
      }
    }

    return false;
  }

  // Get function signature for current cursor position
  static FunctionSignatureInfo? getFunctionSignature(String formula, int cursorPosition) {
    if (cursorPosition <= 0 || cursorPosition > formula.length) return null;

    // Find the function we're currently in
    int openParens = 0;
    int functionStart = -1;
    
    for (int i = cursorPosition - 1; i >= 0; i--) {
      if (formula[i] == ')') {
        openParens++;
      } else if (formula[i] == '(') {
        if (openParens == 0) {
          // Found the opening parenthesis of our function
          functionStart = i;
          break;
        }
        openParens--;
      }
    }

    if (functionStart == -1) return null;

    // Find the function name
    int nameStart = functionStart - 1;
    while (nameStart >= 0 && RegExp(r'[A-Z]').hasMatch(formula[nameStart])) {
      nameStart--;
    }
    nameStart++;

    if (nameStart >= functionStart) return null;

    final functionName = formula.substring(nameStart, functionStart);
    final functionDef = getFunction(functionName);
    if (functionDef == null) return null;

    // Count current parameter position
    int parameterIndex = 0;
    int parenLevel = 0;
    
    for (int i = functionStart + 1; i < cursorPosition; i++) {
      if (formula[i] == '(') {
        parenLevel++;
      } else if (formula[i] == ')') {
        parenLevel--;
      } else if (formula[i] == ',' && parenLevel == 0) {
        parameterIndex++;
      }
    }

    return FunctionSignatureInfo(
      function: functionDef,
      currentParameterIndex: parameterIndex,
    );
  }
}

// Function Definition Model
class FunctionDefinition {
  final String name;
  final String description;
  final String syntax;
  final String category;
  final List<String> parameters;
  final String example;

  const FunctionDefinition({
    required this.name,
    required this.description,
    required this.syntax,
    required this.category,
    required this.parameters,
    required this.example,
  });

  @override
  String toString() => name;
}

// Formula Validation Result
class FormulaValidationResult {
  final bool isValid;
  final String? error;
  final String? warning;

  FormulaValidationResult({
    required this.isValid,
    this.error,
    this.warning,
  });
}

// Function Signature Information
class FunctionSignatureInfo {
  final FunctionDefinition function;
  final int currentParameterIndex;

  FunctionSignatureInfo({
    required this.function,
    required this.currentParameterIndex,
  });

  String get currentParameterName {
    if (currentParameterIndex < function.parameters.length) {
      return function.parameters[currentParameterIndex];
    }
    return '';
  }
}
