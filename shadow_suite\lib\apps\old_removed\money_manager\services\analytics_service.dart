import 'dart:async';
import 'dart:math';
import '../models/money_manager_models.dart';
import 'money_manager_database.dart';

/// Advanced analytics service for Money Manager
class AnalyticsService {
  static bool _isInitialized = false;
  static final StreamController<AnalyticsUpdate> _updateController =
      StreamController<AnalyticsUpdate>.broadcast();

  /// Initialize analytics service
  static Future<void> initialize() async {
    if (_isInitialized) return;
    _isInitialized = true;
  }

  /// Get spending trends analysis
  static Future<SpendingTrends> getSpendingTrends({
    DateTime? startDate,
    DateTime? endDate,
    String? categoryId,
  }) async {
    try {
      final now = DateTime.now();
      final start = startDate ?? DateTime(now.year, now.month - 6, 1);
      final end = endDate ?? now;

      final transactions = await MoneyManagerDatabase.getTransactions();
      final filteredTransactions = transactions.where((t) {
        final inDateRange = t.date.isAfter(start) && t.date.isBefore(end);
        final matchesCategory = categoryId == null || t.categoryId == categoryId;
        return inDateRange && matchesCategory && t.type == TransactionType.expense;
      }).toList();

      // Group by month
      final monthlyData = <String, double>{};
      for (final transaction in filteredTransactions) {
        final monthKey = '${transaction.date.year}-${transaction.date.month.toString().padLeft(2, '0')}';
        monthlyData[monthKey] = (monthlyData[monthKey] ?? 0) + transaction.amount;
      }

      // Calculate trends
      final months = monthlyData.keys.toList()..sort();
      final amounts = months.map((month) => monthlyData[month]!).toList();
      
      double trend = 0.0;
      if (amounts.length >= 2) {
        final firstHalf = amounts.take(amounts.length ~/ 2).toList();
        final secondHalf = amounts.skip(amounts.length ~/ 2).toList();
        final firstAvg = firstHalf.reduce((a, b) => a + b) / firstHalf.length;
        final secondAvg = secondHalf.reduce((a, b) => a + b) / secondHalf.length;
        trend = ((secondAvg - firstAvg) / firstAvg) * 100;
      }

      return SpendingTrends(
        monthlyData: monthlyData,
        totalSpent: amounts.fold(0.0, (sum, amount) => sum + amount),
        averageMonthly: amounts.isNotEmpty ? amounts.reduce((a, b) => a + b) / amounts.length : 0.0,
        trendPercentage: trend,
        peakMonth: months.isNotEmpty ? months[amounts.indexOf(amounts.reduce(max))] : '',
        lowestMonth: months.isNotEmpty ? months[amounts.indexOf(amounts.reduce(min))] : '',
      );
    } catch (e) {
      return SpendingTrends.empty();
    }
  }

  /// Get category breakdown analysis
  static Future<CategoryBreakdown> getCategoryBreakdown({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final now = DateTime.now();
      final start = startDate ?? DateTime(now.year, now.month, 1);
      final end = endDate ?? now;

      final transactions = await MoneyManagerDatabase.getTransactions();
      final categories = await MoneyManagerDatabase.getCategories();
      
      final filteredTransactions = transactions.where((t) {
        return t.date.isAfter(start) && 
               t.date.isBefore(end) && 
               t.type == TransactionType.expense;
      }).toList();

      final categoryTotals = <String, double>{};
      final categoryTransactionCounts = <String, int>{};

      for (final transaction in filteredTransactions) {
        categoryTotals[transaction.categoryId] = 
            (categoryTotals[transaction.categoryId] ?? 0) + transaction.amount;
        categoryTransactionCounts[transaction.categoryId] = 
            (categoryTransactionCounts[transaction.categoryId] ?? 0) + 1;
      }

      final totalSpent = categoryTotals.values.fold(0.0, (sum, amount) => sum + amount);
      
      final categoryData = <CategoryData>[];
      for (final entry in categoryTotals.entries) {
        final category = categories.firstWhere(
          (c) => c.id == entry.key,
          orElse: () => Category(
            id: entry.key,
            name: 'Unknown',
            type: CategoryType.expense,
            color: '#95A5A6',
            icon: 'help',
            createdAt: DateTime.now(),
          ),
        );

        categoryData.add(CategoryData(
          category: category,
          amount: entry.value,
          percentage: totalSpent > 0 ? (entry.value / totalSpent) * 100 : 0,
          transactionCount: categoryTransactionCounts[entry.key] ?? 0,
        ));
      }

      categoryData.sort((a, b) => b.amount.compareTo(a.amount));

      return CategoryBreakdown(
        categoryData: categoryData,
        totalSpent: totalSpent,
        topCategory: categoryData.isNotEmpty ? categoryData.first : null,
        categoryCount: categoryData.length,
      );
    } catch (e) {
      return CategoryBreakdown.empty();
    }
  }

  /// Get income vs expenses analysis
  static Future<IncomeExpenseAnalysis> getIncomeExpenseAnalysis({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final now = DateTime.now();
      final start = startDate ?? DateTime(now.year, now.month, 1);
      final end = endDate ?? now;

      final transactions = await MoneyManagerDatabase.getTransactions();
      final filteredTransactions = transactions.where((t) {
        return t.date.isAfter(start) && t.date.isBefore(end);
      }).toList();

      double totalIncome = 0.0;
      double totalExpenses = 0.0;
      int incomeCount = 0;
      int expenseCount = 0;

      final dailyIncome = <String, double>{};
      final dailyExpenses = <String, double>{};

      for (final transaction in filteredTransactions) {
        final dayKey = '${transaction.date.year}-${transaction.date.month.toString().padLeft(2, '0')}-${transaction.date.day.toString().padLeft(2, '0')}';
        
        if (transaction.type == TransactionType.income) {
          totalIncome += transaction.amount;
          incomeCount++;
          dailyIncome[dayKey] = (dailyIncome[dayKey] ?? 0) + transaction.amount;
        } else if (transaction.type == TransactionType.expense) {
          totalExpenses += transaction.amount;
          expenseCount++;
          dailyExpenses[dayKey] = (dailyExpenses[dayKey] ?? 0) + transaction.amount;
        }
      }

      final netIncome = totalIncome - totalExpenses;
      final savingsRate = totalIncome > 0 ? (netIncome / totalIncome) * 100 : 0.0;

      return IncomeExpenseAnalysis(
        totalIncome: totalIncome,
        totalExpenses: totalExpenses,
        netIncome: netIncome,
        savingsRate: savingsRate,
        incomeTransactionCount: incomeCount,
        expenseTransactionCount: expenseCount,
        averageIncome: incomeCount > 0 ? totalIncome / incomeCount : 0.0,
        averageExpense: expenseCount > 0 ? totalExpenses / expenseCount : 0.0,
        dailyIncome: dailyIncome,
        dailyExpenses: dailyExpenses,
      );
    } catch (e) {
      return IncomeExpenseAnalysis.empty();
    }
  }

  /// Get budget performance analysis
  static Future<BudgetPerformance> getBudgetPerformance() async {
    try {
      final budgets = await MoneyManagerDatabase.getBudgets();
      final transactions = await MoneyManagerDatabase.getTransactions();

      final budgetPerformanceData = <BudgetPerformanceData>[];

      for (final budget in budgets) {
        final budgetTransactions = transactions.where((t) {
          return t.categoryId == budget.categoryId &&
                 t.date.isAfter(budget.startDate) &&
                 t.date.isBefore(budget.endDate) &&
                 t.type == TransactionType.expense;
        }).toList();

        final actualSpent = budgetTransactions.fold(0.0, (sum, t) => sum + t.amount);
        final variance = actualSpent - budget.amount;
        final variancePercentage = budget.amount > 0 ? (variance / budget.amount) * 100 : 0.0;

        budgetPerformanceData.add(BudgetPerformanceData(
          budget: budget,
          actualSpent: actualSpent,
          variance: variance,
          variancePercentage: variancePercentage,
          transactionCount: budgetTransactions.length,
          isOverBudget: actualSpent > budget.amount,
        ));
      }

      final totalBudgeted = budgets.fold(0.0, (sum, b) => sum + b.amount);
      final totalSpent = budgetPerformanceData.fold(0.0, (sum, b) => sum + b.actualSpent);
      final overBudgetCount = budgetPerformanceData.where((b) => b.isOverBudget).length;

      return BudgetPerformance(
        budgetData: budgetPerformanceData,
        totalBudgeted: totalBudgeted,
        totalSpent: totalSpent,
        totalVariance: totalSpent - totalBudgeted,
        overBudgetCount: overBudgetCount,
        budgetUtilization: totalBudgeted > 0 ? (totalSpent / totalBudgeted) * 100 : 0.0,
      );
    } catch (e) {
      return BudgetPerformance.empty();
    }
  }

  /// Get financial health score
  static Future<FinancialHealthScore> getFinancialHealthScore() async {
    try {
      final accounts = await MoneyManagerDatabase.getAccounts();
      final incomeExpenseAnalysis = await getIncomeExpenseAnalysis();
      final budgetPerformance = await getBudgetPerformance();

      // Calculate various health metrics
      final totalBalance = accounts.fold(0.0, (sum, a) => sum + a.currentBalance);
      final emergencyFundRatio = _calculateEmergencyFundRatio(accounts, incomeExpenseAnalysis);
      final debtToIncomeRatio = _calculateDebtToIncomeRatio(accounts, incomeExpenseAnalysis);
      final budgetAdherence = budgetPerformance.budgetUtilization <= 100 ? 100 - budgetPerformance.budgetUtilization : 0.0;

      // Calculate overall score (0-100)
      double score = 0.0;
      score += (incomeExpenseAnalysis.savingsRate.clamp(0, 20) / 20) * 25; // 25% weight
      score += (emergencyFundRatio.clamp(0, 6) / 6) * 25; // 25% weight
      score += ((100 - debtToIncomeRatio.clamp(0, 100)) / 100) * 25; // 25% weight
      score += (budgetAdherence.clamp(0, 100) / 100) * 25; // 25% weight

      return FinancialHealthScore(
        overallScore: score.round(),
        savingsRate: incomeExpenseAnalysis.savingsRate,
        emergencyFundRatio: emergencyFundRatio,
        debtToIncomeRatio: debtToIncomeRatio,
        budgetAdherence: budgetAdherence,
        totalNetWorth: totalBalance,
        recommendations: _generateRecommendations(score, incomeExpenseAnalysis, budgetPerformance),
      );
    } catch (e) {
      return FinancialHealthScore.empty();
    }
  }

  static double _calculateEmergencyFundRatio(List<Account> accounts, IncomeExpenseAnalysis analysis) {
    final emergencyFunds = accounts
        .where((a) => a.type == AccountType.savings)
        .fold(0.0, (sum, a) => sum + a.currentBalance);
    
    final monthlyExpenses = analysis.totalExpenses;
    return monthlyExpenses > 0 ? emergencyFunds / monthlyExpenses : 0.0;
  }

  static double _calculateDebtToIncomeRatio(List<Account> accounts, IncomeExpenseAnalysis analysis) {
    final totalDebt = accounts
        .where((a) => a.type == AccountType.credit || a.type == AccountType.loan)
        .fold(0.0, (sum, a) => sum + a.currentBalance.abs());
    
    return analysis.totalIncome > 0 ? (totalDebt / analysis.totalIncome) * 100 : 0.0;
  }

  static List<String> _generateRecommendations(
    double score,
    IncomeExpenseAnalysis analysis,
    BudgetPerformance budgetPerformance,
  ) {
    final recommendations = <String>[];

    if (score < 50) {
      recommendations.add('Focus on building an emergency fund');
      recommendations.add('Review and reduce unnecessary expenses');
    }

    if (analysis.savingsRate < 10) {
      recommendations.add('Aim to save at least 10% of your income');
    }

    if (budgetPerformance.overBudgetCount > 0) {
      recommendations.add('Review budgets for overspent categories');
    }

    if (analysis.netIncome < 0) {
      recommendations.add('Urgent: Expenses exceed income - create a debt reduction plan');
    }

    return recommendations;
  }

  /// Get analytics updates stream
  static Stream<AnalyticsUpdate> get updatesStream => _updateController.stream;

  /// Dispose resources
  static void dispose() {
    _updateController.close();
  }
}

/// Analytics models
class SpendingTrends {
  final Map<String, double> monthlyData;
  final double totalSpent;
  final double averageMonthly;
  final double trendPercentage;
  final String peakMonth;
  final String lowestMonth;

  const SpendingTrends({
    required this.monthlyData,
    required this.totalSpent,
    required this.averageMonthly,
    required this.trendPercentage,
    required this.peakMonth,
    required this.lowestMonth,
  });

  factory SpendingTrends.empty() => const SpendingTrends(
    monthlyData: {},
    totalSpent: 0.0,
    averageMonthly: 0.0,
    trendPercentage: 0.0,
    peakMonth: '',
    lowestMonth: '',
  );
}

class CategoryBreakdown {
  final List<CategoryData> categoryData;
  final double totalSpent;
  final CategoryData? topCategory;
  final int categoryCount;

  const CategoryBreakdown({
    required this.categoryData,
    required this.totalSpent,
    required this.topCategory,
    required this.categoryCount,
  });

  factory CategoryBreakdown.empty() => const CategoryBreakdown(
    categoryData: [],
    totalSpent: 0.0,
    topCategory: null,
    categoryCount: 0,
  );
}

class CategoryData {
  final Category category;
  final double amount;
  final double percentage;
  final int transactionCount;

  const CategoryData({
    required this.category,
    required this.amount,
    required this.percentage,
    required this.transactionCount,
  });
}

class IncomeExpenseAnalysis {
  final double totalIncome;
  final double totalExpenses;
  final double netIncome;
  final double savingsRate;
  final int incomeTransactionCount;
  final int expenseTransactionCount;
  final double averageIncome;
  final double averageExpense;
  final Map<String, double> dailyIncome;
  final Map<String, double> dailyExpenses;

  const IncomeExpenseAnalysis({
    required this.totalIncome,
    required this.totalExpenses,
    required this.netIncome,
    required this.savingsRate,
    required this.incomeTransactionCount,
    required this.expenseTransactionCount,
    required this.averageIncome,
    required this.averageExpense,
    required this.dailyIncome,
    required this.dailyExpenses,
  });

  factory IncomeExpenseAnalysis.empty() => const IncomeExpenseAnalysis(
    totalIncome: 0.0,
    totalExpenses: 0.0,
    netIncome: 0.0,
    savingsRate: 0.0,
    incomeTransactionCount: 0,
    expenseTransactionCount: 0,
    averageIncome: 0.0,
    averageExpense: 0.0,
    dailyIncome: {},
    dailyExpenses: {},
  );
}

class BudgetPerformance {
  final List<BudgetPerformanceData> budgetData;
  final double totalBudgeted;
  final double totalSpent;
  final double totalVariance;
  final int overBudgetCount;
  final double budgetUtilization;

  const BudgetPerformance({
    required this.budgetData,
    required this.totalBudgeted,
    required this.totalSpent,
    required this.totalVariance,
    required this.overBudgetCount,
    required this.budgetUtilization,
  });

  factory BudgetPerformance.empty() => const BudgetPerformance(
    budgetData: [],
    totalBudgeted: 0.0,
    totalSpent: 0.0,
    totalVariance: 0.0,
    overBudgetCount: 0,
    budgetUtilization: 0.0,
  );
}

class BudgetPerformanceData {
  final Budget budget;
  final double actualSpent;
  final double variance;
  final double variancePercentage;
  final int transactionCount;
  final bool isOverBudget;

  const BudgetPerformanceData({
    required this.budget,
    required this.actualSpent,
    required this.variance,
    required this.variancePercentage,
    required this.transactionCount,
    required this.isOverBudget,
  });
}

class FinancialHealthScore {
  final int overallScore;
  final double savingsRate;
  final double emergencyFundRatio;
  final double debtToIncomeRatio;
  final double budgetAdherence;
  final double totalNetWorth;
  final List<String> recommendations;

  const FinancialHealthScore({
    required this.overallScore,
    required this.savingsRate,
    required this.emergencyFundRatio,
    required this.debtToIncomeRatio,
    required this.budgetAdherence,
    required this.totalNetWorth,
    required this.recommendations,
  });

  factory FinancialHealthScore.empty() => const FinancialHealthScore(
    overallScore: 0,
    savingsRate: 0.0,
    emergencyFundRatio: 0.0,
    debtToIncomeRatio: 0.0,
    budgetAdherence: 0.0,
    totalNetWorth: 0.0,
    recommendations: [],
  );
}

class AnalyticsUpdate {
  final String type;
  final DateTime timestamp;
  final Map<String, dynamic>? data;

  const AnalyticsUpdate({
    required this.type,
    required this.timestamp,
    this.data,
  });
}
