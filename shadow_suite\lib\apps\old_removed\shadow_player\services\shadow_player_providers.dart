import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/media_models.dart';
import '../shadow_player_main.dart';
import 'media_library_service.dart';
import 'simple_media_player_service.dart';
import '../../../core/settings/comprehensive_settings_system.dart';

// Core Service Providers
final mediaLibraryServiceProvider = Provider<MediaLibraryService>((ref) {
  return MediaLibraryService();
});

final mediaPlayerServiceProvider = Provider<MediaPlayerService>((ref) {
  return MediaPlayerService();
});

// Note: These services will be implemented in future updates
// final mediaScannerServiceProvider = Provider<MediaScannerService>((ref) {
//   return MediaScannerService();
// });

// final playlistServiceProvider = Provider<PlaylistService>((ref) {
//   return PlaylistService();
// });

// final mediaDatabaseServiceProvider = Provider<MediaDatabaseService>((ref) {
//   return MediaDatabaseService();
// });

// State Providers
final currentShadowPlayerTabProvider = StateProvider<ShadowPlayerTab>((ref) {
  return ShadowPlayerTab.video;
});

final mediaScanningProvider = StateProvider<bool>((ref) {
  return false;
});

final mediaPlayerStateProvider = StateProvider<PlayerState>((ref) {
  return PlayerState.stopped;
});

final currentPlayingMediaProvider = StateProvider<MediaFile?>((ref) {
  return null;
});

final currentPlaylistProvider = StateProvider<Playlist?>((ref) {
  return null;
});

final playModeProvider = StateProvider<PlayMode>((ref) {
  return PlayMode.sequential;
});

final volumeProvider = StateProvider<double>((ref) {
  return 1.0;
});

final playbackSpeedProvider = StateProvider<double>((ref) {
  return 1.0;
});

final currentPositionProvider = StateProvider<Duration>((ref) {
  return Duration.zero;
});

final totalDurationProvider = StateProvider<Duration>((ref) {
  return Duration.zero;
});

// Media Library Providers
final allMediaFilesProvider =
    StateNotifierProvider<MediaFilesNotifier, List<MediaFile>>((ref) {
      return MediaFilesNotifier();
    });

final videoFilesProvider = Provider<List<MediaFile>>((ref) {
  final allFiles = ref.watch(allMediaFilesProvider);
  return allFiles.where((file) => file.type == MediaType.video).toList();
});

final audioFilesProvider = Provider<List<MediaFile>>((ref) {
  final allFiles = ref.watch(allMediaFilesProvider);
  return allFiles.where((file) => file.type == MediaType.audio).toList();
});

final favoriteMediaProvider = Provider<List<MediaFile>>((ref) {
  final allFiles = ref.watch(allMediaFilesProvider);
  return allFiles.where((file) => file.isFavorite).toList();
});

final recentlyPlayedProvider = Provider<List<MediaFile>>((ref) {
  final allFiles = ref.watch(allMediaFilesProvider);
  final recentFiles = allFiles
      .where((file) => file.lastPlayed != null)
      .toList();
  recentFiles.sort((a, b) => b.lastPlayed!.compareTo(a.lastPlayed!));
  return recentFiles.take(50).toList();
});

final mostPlayedProvider = Provider<List<MediaFile>>((ref) {
  final allFiles = ref.watch(allMediaFilesProvider);
  final playedFiles = allFiles.where((file) => file.playCount > 0).toList();
  playedFiles.sort((a, b) => b.playCount.compareTo(a.playCount));
  return playedFiles.take(50).toList();
});

// Playlist Providers
final playlistsProvider =
    StateNotifierProvider<PlaylistsNotifier, List<Playlist>>((ref) {
      return PlaylistsNotifier();
    });

final currentPlaylistIndexProvider = StateProvider<int>((ref) {
  return 0;
});

// Filter and Search Providers
final videoViewModeProvider = StateProvider<ViewMode>((ref) {
  return ViewMode.grid;
});

final musicViewModeProvider = StateProvider<ViewMode>((ref) {
  return ViewMode.list;
});

final videoSortByProvider = StateProvider<SortBy>((ref) {
  return SortBy.name;
});

final musicSortByProvider = StateProvider<SortBy>((ref) {
  return SortBy.artist;
});

final videoSortOrderProvider = StateProvider<SortOrder>((ref) {
  return SortOrder.ascending;
});

final musicSortOrderProvider = StateProvider<SortOrder>((ref) {
  return SortOrder.ascending;
});

final videoSearchQueryProvider = StateProvider<String>((ref) {
  return '';
});

final musicSearchQueryProvider = StateProvider<String>((ref) {
  return '';
});

final videoFilterProvider = StateProvider<VideoFilter>((ref) {
  return VideoFilter();
});

final musicFilterProvider = StateProvider<MusicFilter>((ref) {
  return MusicFilter();
});

// Filtered Media Providers
final filteredVideoFilesProvider = Provider<List<MediaFile>>((ref) {
  final videos = ref.watch(videoFilesProvider);
  final searchQuery = ref.watch(videoSearchQueryProvider);
  final sortBy = ref.watch(videoSortByProvider);
  final sortOrder = ref.watch(videoSortOrderProvider);
  final filter = ref.watch(videoFilterProvider);

  var filteredVideos = videos;

  // Apply search filter
  if (searchQuery.isNotEmpty) {
    filteredVideos = filteredVideos.where((video) {
      return video.displayName.toLowerCase().contains(
            searchQuery.toLowerCase(),
          ) ||
          (video.metadata.title?.toLowerCase().contains(
                searchQuery.toLowerCase(),
              ) ??
              false);
    }).toList();
  }

  // Apply advanced filters
  filteredVideos = _applyVideoFilter(filteredVideos, filter);

  // Apply sorting
  filteredVideos = _sortMediaFiles(filteredVideos, sortBy, sortOrder);

  return filteredVideos;
});

final filteredAudioFilesProvider = Provider<List<MediaFile>>((ref) {
  final audios = ref.watch(audioFilesProvider);
  final searchQuery = ref.watch(musicSearchQueryProvider);
  final sortBy = ref.watch(musicSortByProvider);
  final sortOrder = ref.watch(musicSortOrderProvider);
  final filter = ref.watch(musicFilterProvider);

  var filteredAudios = audios;

  // Apply search filter
  if (searchQuery.isNotEmpty) {
    filteredAudios = filteredAudios.where((audio) {
      return audio.displayName.toLowerCase().contains(
            searchQuery.toLowerCase(),
          ) ||
          (audio.metadata.title?.toLowerCase().contains(
                searchQuery.toLowerCase(),
              ) ??
              false) ||
          (audio.metadata.artist?.toLowerCase().contains(
                searchQuery.toLowerCase(),
              ) ??
              false) ||
          (audio.metadata.album?.toLowerCase().contains(
                searchQuery.toLowerCase(),
              ) ??
              false);
    }).toList();
  }

  // Apply advanced filters
  filteredAudios = _applyMusicFilter(filteredAudios, filter);

  // Apply sorting
  filteredAudios = _sortMediaFiles(filteredAudios, sortBy, sortOrder);

  return filteredAudios;
});

// Artists and Albums Providers
final artistsProvider = Provider<List<String>>((ref) {
  final audios = ref.watch(audioFilesProvider);
  final artists = audios
      .map((audio) => audio.metadata.artist ?? 'Unknown Artist')
      .toSet()
      .toList();
  artists.sort();
  return artists;
});

final albumsProvider = Provider<List<String>>((ref) {
  final audios = ref.watch(audioFilesProvider);
  final albums = audios
      .map((audio) => audio.metadata.album ?? 'Unknown Album')
      .toSet()
      .toList();
  albums.sort();
  return albums;
});

final genresProvider = Provider<List<String>>((ref) {
  final audios = ref.watch(audioFilesProvider);
  final genres = audios
      .map((audio) => audio.metadata.genre ?? 'Unknown Genre')
      .toSet()
      .toList();
  genres.sort();
  return genres;
});

// Settings Providers (connected to unified settings system)
final scanLocationsProvider = StateProvider<List<String>>((ref) {
  return ComprehensiveSettingsSystem.getValue<List<String>>(
    'scan_locations',
    defaultValue: ['/storage/emulated/0/'],
  );
});

final supportedVideoFormatsProvider = StateProvider<List<String>>((ref) {
  return ComprehensiveSettingsSystem.getValue<List<String>>(
    'supported_video_formats',
    defaultValue: ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm', '3gp'],
  );
});

final supportedAudioFormatsProvider = StateProvider<List<String>>((ref) {
  return ComprehensiveSettingsSystem.getValue<List<String>>(
    'supported_audio_formats',
    defaultValue: ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a', 'opus'],
  );
});

final autoScanEnabledProvider = StateProvider<bool>((ref) {
  return ComprehensiveSettingsSystem.getValue<bool>(
    'auto_scan_enabled',
    defaultValue: true,
  );
});

final generateThumbnailsProvider = StateProvider<bool>((ref) {
  return ComprehensiveSettingsSystem.getValue<bool>(
    'generate_thumbnails',
    defaultValue: true,
  );
});

final backgroundPlaybackProvider = StateProvider<bool>((ref) {
  return ComprehensiveSettingsSystem.getValue<bool>(
    'background_playback',
    defaultValue: true,
  );
});

final videoQualityProvider = StateProvider<String>((ref) {
  return ComprehensiveSettingsSystem.getValue<String>(
    'video_quality',
    defaultValue: 'auto',
  );
});

final audioQualityProvider = StateProvider<String>((ref) {
  return ComprehensiveSettingsSystem.getValue<String>(
    'audio_quality',
    defaultValue: 'high',
  );
});

final equalizerEnabledProvider = StateProvider<bool>((ref) {
  return ComprehensiveSettingsSystem.getValue<bool>(
    'equalizer_enabled',
    defaultValue: false,
  );
});

final crossfadeDurationProvider = StateProvider<double>((ref) {
  return ComprehensiveSettingsSystem.getValue<double>(
    'crossfade_duration',
    defaultValue: 3.0,
  );
});

// State Notifiers
class MediaFilesNotifier extends StateNotifier<List<MediaFile>> {
  MediaFilesNotifier() : super([]);

  void setMediaFiles(List<MediaFile> files) {
    state = files;
  }

  void addMediaFile(MediaFile file) {
    state = [...state, file];
  }

  void updateMediaFile(MediaFile updatedFile) {
    state = state.map((file) {
      return file.id == updatedFile.id ? updatedFile : file;
    }).toList();
  }

  void removeMediaFile(String fileId) {
    state = state.where((file) => file.id != fileId).toList();
  }

  void toggleFavorite(String fileId) {
    state = state.map((file) {
      if (file.id == fileId) {
        return file.copyWith(isFavorite: !file.isFavorite);
      }
      return file;
    }).toList();
  }

  void incrementPlayCount(String fileId) {
    state = state.map((file) {
      if (file.id == fileId) {
        return file.copyWith(
          playCount: file.playCount + 1,
          lastPlayed: DateTime.now(),
        );
      }
      return file;
    }).toList();
  }
}

class PlaylistsNotifier extends StateNotifier<List<Playlist>> {
  PlaylistsNotifier() : super([]);

  void setPlaylists(List<Playlist> playlists) {
    state = playlists;
  }

  void addPlaylist(Playlist playlist) {
    state = [...state, playlist];
  }

  void updatePlaylist(Playlist updatedPlaylist) {
    state = state.map((playlist) {
      return playlist.id == updatedPlaylist.id ? updatedPlaylist : playlist;
    }).toList();
  }

  void removePlaylist(String playlistId) {
    state = state.where((playlist) => playlist.id != playlistId).toList();
  }
}

// Filter Classes
class VideoFilter {
  final List<String> formats;
  final Duration? minDuration;
  final Duration? maxDuration;
  final List<String> resolutions;
  final DateTime? dateFrom;
  final DateTime? dateTo;
  final int? minSize;
  final int? maxSize;

  const VideoFilter({
    this.formats = const [],
    this.minDuration,
    this.maxDuration,
    this.resolutions = const [],
    this.dateFrom,
    this.dateTo,
    this.minSize,
    this.maxSize,
  });

  VideoFilter copyWith({
    List<String>? formats,
    Duration? minDuration,
    Duration? maxDuration,
    List<String>? resolutions,
    DateTime? dateFrom,
    DateTime? dateTo,
    int? minSize,
    int? maxSize,
  }) {
    return VideoFilter(
      formats: formats ?? this.formats,
      minDuration: minDuration ?? this.minDuration,
      maxDuration: maxDuration ?? this.maxDuration,
      resolutions: resolutions ?? this.resolutions,
      dateFrom: dateFrom ?? this.dateFrom,
      dateTo: dateTo ?? this.dateTo,
      minSize: minSize ?? this.minSize,
      maxSize: maxSize ?? this.maxSize,
    );
  }
}

class MusicFilter {
  final List<String> formats;
  final List<String> artists;
  final List<String> albums;
  final List<String> genres;
  final int? minYear;
  final int? maxYear;
  final Duration? minDuration;
  final Duration? maxDuration;

  const MusicFilter({
    this.formats = const [],
    this.artists = const [],
    this.albums = const [],
    this.genres = const [],
    this.minYear,
    this.maxYear,
    this.minDuration,
    this.maxDuration,
  });
}

// Helper Functions
List<MediaFile> _applyVideoFilter(List<MediaFile> videos, VideoFilter filter) {
  return videos.where((video) {
    // Format filter
    if (filter.formats.isNotEmpty) {
      final extension = video.path.split('.').last.toLowerCase();
      if (!filter.formats.contains(extension)) return false;
    }

    // Duration filter
    if (filter.minDuration != null && video.duration != null) {
      if (video.duration! < filter.minDuration!) return false;
    }
    if (filter.maxDuration != null && video.duration != null) {
      if (video.duration! > filter.maxDuration!) return false;
    }

    // Resolution filter
    if (filter.resolutions.isNotEmpty && video.metadata.resolution != null) {
      if (!filter.resolutions.contains(video.metadata.resolution)) return false;
    }

    // Date filter
    if (filter.dateFrom != null) {
      if (video.dateModified.isBefore(filter.dateFrom!)) return false;
    }
    if (filter.dateTo != null) {
      if (video.dateModified.isAfter(filter.dateTo!)) return false;
    }

    // Size filter
    if (filter.minSize != null) {
      if (video.size < filter.minSize!) return false;
    }
    if (filter.maxSize != null) {
      if (video.size > filter.maxSize!) return false;
    }

    return true;
  }).toList();
}

List<MediaFile> _applyMusicFilter(List<MediaFile> audios, MusicFilter filter) {
  return audios.where((audio) {
    // Format filter
    if (filter.formats.isNotEmpty) {
      final extension = audio.path.split('.').last.toLowerCase();
      if (!filter.formats.contains(extension)) return false;
    }

    // Artist filter
    if (filter.artists.isNotEmpty) {
      final artist = audio.metadata.artist ?? 'Unknown Artist';
      if (!filter.artists.contains(artist)) return false;
    }

    // Album filter
    if (filter.albums.isNotEmpty) {
      final album = audio.metadata.album ?? 'Unknown Album';
      if (!filter.albums.contains(album)) return false;
    }

    // Genre filter
    if (filter.genres.isNotEmpty) {
      final genre = audio.metadata.genre ?? 'Unknown Genre';
      if (!filter.genres.contains(genre)) return false;
    }

    // Year filter
    if (filter.minYear != null && audio.metadata.year != null) {
      if (audio.metadata.year! < filter.minYear!) return false;
    }
    if (filter.maxYear != null && audio.metadata.year != null) {
      if (audio.metadata.year! > filter.maxYear!) return false;
    }

    // Duration filter
    if (filter.minDuration != null && audio.duration != null) {
      if (audio.duration! < filter.minDuration!) return false;
    }
    if (filter.maxDuration != null && audio.duration != null) {
      if (audio.duration! > filter.maxDuration!) return false;
    }

    return true;
  }).toList();
}

List<MediaFile> _sortMediaFiles(
  List<MediaFile> files,
  SortBy sortBy,
  SortOrder order,
) {
  final sortedFiles = List<MediaFile>.from(files);

  sortedFiles.sort((a, b) {
    int comparison = 0;

    switch (sortBy) {
      case SortBy.name:
        comparison = a.displayName.compareTo(b.displayName);
        break;
      case SortBy.dateAdded:
        comparison = a.dateAdded.compareTo(b.dateAdded);
        break;
      case SortBy.dateModified:
        comparison = a.dateModified.compareTo(b.dateModified);
        break;
      case SortBy.size:
        comparison = a.size.compareTo(b.size);
        break;
      case SortBy.duration:
        if (a.duration != null && b.duration != null) {
          comparison = a.duration!.compareTo(b.duration!);
        }
        break;
      case SortBy.artist:
        final artistA = a.metadata.artist ?? 'Unknown Artist';
        final artistB = b.metadata.artist ?? 'Unknown Artist';
        comparison = artistA.compareTo(artistB);
        break;
      case SortBy.album:
        final albumA = a.metadata.album ?? 'Unknown Album';
        final albumB = b.metadata.album ?? 'Unknown Album';
        comparison = albumA.compareTo(albumB);
        break;
    }

    return order == SortOrder.ascending ? comparison : -comparison;
  });

  return sortedFiles;
}
