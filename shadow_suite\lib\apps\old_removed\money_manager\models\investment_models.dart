// import 'package:flutter/foundation.dart'; // Reserved for future debugging

// Investment Portfolio Model
class InvestmentPortfolio {
  final String id;
  final String name;
  final String description;
  final double totalValue;
  final double totalCost;
  final double totalGainLoss;
  final double gainLossPercentage;
  final List<Investment> investments;
  final DateTime createdAt;
  final DateTime lastModified;

  const InvestmentPortfolio({
    required this.id,
    required this.name,
    required this.description,
    required this.totalValue,
    required this.totalCost,
    required this.totalGainLoss,
    required this.gainLossPercentage,
    required this.investments,
    required this.createdAt,
    required this.lastModified,
  });

  factory InvestmentPortfolio.fromJson(Map<String, dynamic> json) {
    return InvestmentPortfolio(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      totalValue: (json['total_value'] as num).toDouble(),
      totalCost: (json['total_cost'] as num).toDouble(),
      totalGainLoss: (json['total_gain_loss'] as num).toDouble(),
      gainLossPercentage: (json['gain_loss_percentage'] as num).toDouble(),
      investments: (json['investments'] as List<dynamic>?)
          ?.map((e) => Investment.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      createdAt: DateTime.parse(json['created_at'] as String),
      lastModified: DateTime.parse(json['last_modified'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'total_value': totalValue,
      'total_cost': totalCost,
      'total_gain_loss': totalGainLoss,
      'gain_loss_percentage': gainLossPercentage,
      'investments': investments.map((e) => e.toJson()).toList(),
      'created_at': createdAt.toIso8601String(),
      'last_modified': lastModified.toIso8601String(),
    };
  }

  InvestmentPortfolio copyWith({
    String? name,
    String? description,
    List<Investment>? investments,
  }) {
    final updatedInvestments = investments ?? this.investments;
    final totalValue = updatedInvestments.fold<double>(0, (sum, inv) => sum + inv.currentValue);
    final totalCost = updatedInvestments.fold<double>(0, (sum, inv) => sum + inv.totalCost);
    final totalGainLoss = totalValue - totalCost;
    final gainLossPercentage = totalCost > 0 ? (totalGainLoss / totalCost) * 100 : 0;

    return InvestmentPortfolio(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      totalValue: totalValue,
      totalCost: totalCost,
      totalGainLoss: totalGainLoss,
      gainLossPercentage: gainLossPercentage.toDouble(),
      investments: updatedInvestments,
      createdAt: createdAt,
      lastModified: DateTime.now(),
    );
  }
}

// Individual Investment Model
class Investment {
  final String id;
  final String portfolioId;
  final String symbol;
  final String name;
  final InvestmentType type;
  final double quantity;
  final double averageCost;
  final double currentPrice;
  final double currentValue;
  final double totalCost;
  final double gainLoss;
  final double gainLossPercentage;
  final String currency;
  final String exchange;
  final DateTime purchaseDate;
  final DateTime lastPriceUpdate;
  final Map<String, dynamic> metadata;

  const Investment({
    required this.id,
    required this.portfolioId,
    required this.symbol,
    required this.name,
    required this.type,
    required this.quantity,
    required this.averageCost,
    required this.currentPrice,
    required this.currentValue,
    required this.totalCost,
    required this.gainLoss,
    required this.gainLossPercentage,
    required this.currency,
    required this.exchange,
    required this.purchaseDate,
    required this.lastPriceUpdate,
    required this.metadata,
  });

  factory Investment.fromJson(Map<String, dynamic> json) {
    return Investment(
      id: json['id'] as String,
      portfolioId: json['portfolio_id'] as String,
      symbol: json['symbol'] as String,
      name: json['name'] as String,
      type: InvestmentType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => InvestmentType.stock,
      ),
      quantity: (json['quantity'] as num).toDouble(),
      averageCost: (json['average_cost'] as num).toDouble(),
      currentPrice: (json['current_price'] as num).toDouble(),
      currentValue: (json['current_value'] as num).toDouble(),
      totalCost: (json['total_cost'] as num).toDouble(),
      gainLoss: (json['gain_loss'] as num).toDouble(),
      gainLossPercentage: (json['gain_loss_percentage'] as num).toDouble(),
      currency: json['currency'] as String? ?? 'USD',
      exchange: json['exchange'] as String? ?? '',
      purchaseDate: DateTime.parse(json['purchase_date'] as String),
      lastPriceUpdate: DateTime.parse(json['last_price_update'] as String),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'portfolio_id': portfolioId,
      'symbol': symbol,
      'name': name,
      'type': type.name,
      'quantity': quantity,
      'average_cost': averageCost,
      'current_price': currentPrice,
      'current_value': currentValue,
      'total_cost': totalCost,
      'gain_loss': gainLoss,
      'gain_loss_percentage': gainLossPercentage,
      'currency': currency,
      'exchange': exchange,
      'purchase_date': purchaseDate.toIso8601String(),
      'last_price_update': lastPriceUpdate.toIso8601String(),
      'metadata': metadata,
    };
  }

  Investment copyWith({
    double? currentPrice,
    double? quantity,
    Map<String, dynamic>? metadata,
  }) {
    final updatedQuantity = quantity ?? this.quantity;
    final updatedPrice = currentPrice ?? this.currentPrice;
    final updatedValue = updatedQuantity * updatedPrice;
    final updatedGainLoss = updatedValue - totalCost;
    final updatedGainLossPercentage = totalCost > 0 ? (updatedGainLoss / totalCost) * 100 : 0;

    return Investment(
      id: id,
      portfolioId: portfolioId,
      symbol: symbol,
      name: name,
      type: type,
      quantity: updatedQuantity,
      averageCost: averageCost,
      currentPrice: updatedPrice,
      currentValue: updatedValue,
      totalCost: totalCost,
      gainLoss: updatedGainLoss,
      gainLossPercentage: updatedGainLossPercentage.toDouble(),
      currency: currency,
      exchange: exchange,
      purchaseDate: purchaseDate,
      lastPriceUpdate: currentPrice != null ? DateTime.now() : lastPriceUpdate,
      metadata: metadata ?? this.metadata,
    );
  }
}

// Investment Transaction Model
class InvestmentTransaction {
  final String id;
  final String investmentId;
  final InvestmentTransactionType type;
  final double quantity;
  final double price;
  final double totalAmount;
  final double fees;
  final DateTime date;
  final String notes;
  final Map<String, dynamic> metadata;

  const InvestmentTransaction({
    required this.id,
    required this.investmentId,
    required this.type,
    required this.quantity,
    required this.price,
    required this.totalAmount,
    required this.fees,
    required this.date,
    required this.notes,
    required this.metadata,
  });

  factory InvestmentTransaction.fromJson(Map<String, dynamic> json) {
    return InvestmentTransaction(
      id: json['id'] as String,
      investmentId: json['investment_id'] as String,
      type: InvestmentTransactionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => InvestmentTransactionType.buy,
      ),
      quantity: (json['quantity'] as num).toDouble(),
      price: (json['price'] as num).toDouble(),
      totalAmount: (json['total_amount'] as num).toDouble(),
      fees: (json['fees'] as num).toDouble(),
      date: DateTime.parse(json['date'] as String),
      notes: json['notes'] as String? ?? '',
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'investment_id': investmentId,
      'type': type.name,
      'quantity': quantity,
      'price': price,
      'total_amount': totalAmount,
      'fees': fees,
      'date': date.toIso8601String(),
      'notes': notes,
      'metadata': metadata,
    };
  }
}

// Market Data Model
class MarketData {
  final String symbol;
  final double price;
  final double change;
  final double changePercentage;
  final double volume;
  final double marketCap;
  final double high52Week;
  final double low52Week;
  final DateTime lastUpdate;

  const MarketData({
    required this.symbol,
    required this.price,
    required this.change,
    required this.changePercentage,
    required this.volume,
    required this.marketCap,
    required this.high52Week,
    required this.low52Week,
    required this.lastUpdate,
  });

  factory MarketData.fromJson(Map<String, dynamic> json) {
    return MarketData(
      symbol: json['symbol'] as String,
      price: (json['price'] as num).toDouble(),
      change: (json['change'] as num).toDouble(),
      changePercentage: (json['change_percentage'] as num).toDouble(),
      volume: (json['volume'] as num).toDouble(),
      marketCap: (json['market_cap'] as num).toDouble(),
      high52Week: (json['high_52_week'] as num).toDouble(),
      low52Week: (json['low_52_week'] as num).toDouble(),
      lastUpdate: DateTime.parse(json['last_update'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'symbol': symbol,
      'price': price,
      'change': change,
      'change_percentage': changePercentage,
      'volume': volume,
      'market_cap': marketCap,
      'high_52_week': high52Week,
      'low_52_week': low52Week,
      'last_update': lastUpdate.toIso8601String(),
    };
  }
}

// Enums
enum InvestmentType {
  stock,
  bond,
  etf,
  mutualFund,
  cryptocurrency,
  commodity,
  realEstate,
  option,
  future,
  forex,
}

enum InvestmentTransactionType {
  buy,
  sell,
  dividend,
  split,
  merger,
  spinoff,
}

// Investment Performance Analytics
class InvestmentPerformance {
  final String investmentId;
  final double totalReturn;
  final double annualizedReturn;
  final double volatility;
  final double sharpeRatio;
  final double beta;
  final double alpha;
  final Map<String, double> monthlyReturns;
  final DateTime calculatedAt;

  const InvestmentPerformance({
    required this.investmentId,
    required this.totalReturn,
    required this.annualizedReturn,
    required this.volatility,
    required this.sharpeRatio,
    required this.beta,
    required this.alpha,
    required this.monthlyReturns,
    required this.calculatedAt,
  });

  factory InvestmentPerformance.fromJson(Map<String, dynamic> json) {
    return InvestmentPerformance(
      investmentId: json['investment_id'] as String,
      totalReturn: (json['total_return'] as num).toDouble(),
      annualizedReturn: (json['annualized_return'] as num).toDouble(),
      volatility: (json['volatility'] as num).toDouble(),
      sharpeRatio: (json['sharpe_ratio'] as num).toDouble(),
      beta: (json['beta'] as num).toDouble(),
      alpha: (json['alpha'] as num).toDouble(),
      monthlyReturns: Map<String, double>.from(json['monthly_returns'] as Map),
      calculatedAt: DateTime.parse(json['calculated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'investment_id': investmentId,
      'total_return': totalReturn,
      'annualized_return': annualizedReturn,
      'volatility': volatility,
      'sharpe_ratio': sharpeRatio,
      'beta': beta,
      'alpha': alpha,
      'monthly_returns': monthlyReturns,
      'calculated_at': calculatedAt.toIso8601String(),
    };
  }
}
