import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';

// Centralized Database Error Handler
class DatabaseErrorHandler {
  static const String _logTag = 'DatabaseError';

  // Handle database exceptions with proper logging and user-friendly messages
  static DatabaseError handleException(dynamic exception, StackTrace? stackTrace, {
    String? operation,
    String? tableName,
    Map<String, dynamic>? context,
  }) {
    final error = _categorizeError(exception);
    
    // Log the error for debugging
    _logError(error, exception, stackTrace, operation, tableName, context);
    
    return error;
  }

  // Categorize database errors into user-friendly types
  static DatabaseError _categorizeError(dynamic exception) {
    if (exception is DatabaseException) {
      return _handleDatabaseException(exception);
    } else if (exception is ArgumentError) {
      return DatabaseError(
        type: DatabaseErrorType.invalidInput,
        message: 'Invalid input provided',
        userMessage: 'Please check your input and try again',
        originalException: exception,
      );
    } else if (exception is StateError) {
      return DatabaseError(
        type: DatabaseErrorType.connectionError,
        message: 'Database connection issue',
        userMessage: 'Unable to connect to database. Please restart the app',
        originalException: exception,
      );
    } else {
      return DatabaseError(
        type: DatabaseErrorType.unknown,
        message: 'Unknown database error: ${exception.toString()}',
        userMessage: 'An unexpected error occurred. Please try again',
        originalException: exception,
      );
    }
  }

  // Handle specific SQLite database exceptions
  static DatabaseError _handleDatabaseException(DatabaseException exception) {
    final message = exception.toString().toLowerCase();
    
    if (message.contains('unique constraint')) {
      return DatabaseError(
        type: DatabaseErrorType.duplicateEntry,
        message: 'Duplicate entry detected',
        userMessage: 'This item already exists. Please use a different name or identifier',
        originalException: exception,
      );
    } else if (message.contains('foreign key constraint')) {
      return DatabaseError(
        type: DatabaseErrorType.foreignKeyConstraint,
        message: 'Foreign key constraint violation',
        userMessage: 'Cannot delete this item because it is being used elsewhere',
        originalException: exception,
      );
    } else if (message.contains('not null constraint')) {
      return DatabaseError(
        type: DatabaseErrorType.missingRequiredField,
        message: 'Required field is missing',
        userMessage: 'Please fill in all required fields',
        originalException: exception,
      );
    } else if (message.contains('no such table')) {
      return DatabaseError(
        type: DatabaseErrorType.tableNotFound,
        message: 'Database table not found',
        userMessage: 'Database structure issue. Please restart the app',
        originalException: exception,
      );
    } else if (message.contains('no such column')) {
      return DatabaseError(
        type: DatabaseErrorType.columnNotFound,
        message: 'Database column not found',
        userMessage: 'Database structure issue. Please update the app',
        originalException: exception,
      );
    } else if (message.contains('database is locked')) {
      return DatabaseError(
        type: DatabaseErrorType.databaseLocked,
        message: 'Database is locked',
        userMessage: 'Database is busy. Please wait a moment and try again',
        originalException: exception,
      );
    } else if (message.contains('disk i/o error') || message.contains('disk full')) {
      return DatabaseError(
        type: DatabaseErrorType.diskError,
        message: 'Disk I/O error',
        userMessage: 'Storage issue detected. Please free up disk space',
        originalException: exception,
      );
    } else {
      return DatabaseError(
        type: DatabaseErrorType.sqlError,
        message: 'SQL execution error: ${exception.toString()}',
        userMessage: 'Database operation failed. Please try again',
        originalException: exception,
      );
    }
  }

  // Log errors with appropriate detail level
  static void _logError(
    DatabaseError error,
    dynamic originalException,
    StackTrace? stackTrace,
    String? operation,
    String? tableName,
    Map<String, dynamic>? context,
  ) {
    final logMessage = StringBuffer();
    logMessage.writeln('Database Error: ${error.type.name}');
    logMessage.writeln('Message: ${error.message}');
    
    if (operation != null) {
      logMessage.writeln('Operation: $operation');
    }
    
    if (tableName != null) {
      logMessage.writeln('Table: $tableName');
    }
    
    if (context != null && context.isNotEmpty) {
      logMessage.writeln('Context: $context');
    }
    
    logMessage.writeln('Original Exception: $originalException');
    
    if (kDebugMode) {
      developer.log(
        logMessage.toString(),
        name: _logTag,
        error: originalException,
        stackTrace: stackTrace,
      );
    }
  }

  // Retry mechanism for transient errors
  static Future<T> retryOperation<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration delay = const Duration(milliseconds: 500),
    bool Function(DatabaseError)? shouldRetry,
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        return await operation();
      } catch (e, stackTrace) {
        attempts++;
        final error = handleException(e, stackTrace);
        
        // Check if we should retry this error
        final canRetry = shouldRetry?.call(error) ?? _shouldRetryByDefault(error);
        
        if (attempts >= maxRetries || !canRetry) {
          throw error;
        }
        
        // Wait before retrying
        await Future.delayed(delay * attempts);
      }
    }
    
    throw DatabaseError(
      type: DatabaseErrorType.unknown,
      message: 'Max retry attempts exceeded',
      userMessage: 'Operation failed after multiple attempts',
    );
  }

  // Default retry logic
  static bool _shouldRetryByDefault(DatabaseError error) {
    switch (error.type) {
      case DatabaseErrorType.databaseLocked:
      case DatabaseErrorType.connectionError:
        return true;
      case DatabaseErrorType.duplicateEntry:
      case DatabaseErrorType.foreignKeyConstraint:
      case DatabaseErrorType.missingRequiredField:
      case DatabaseErrorType.invalidInput:
        return false;
      default:
        return false;
    }
  }

  // Validate database operation inputs
  static void validateInput(Map<String, dynamic> data, List<String> requiredFields) {
    for (final field in requiredFields) {
      if (!data.containsKey(field) || data[field] == null) {
        throw DatabaseError(
          type: DatabaseErrorType.missingRequiredField,
          message: 'Required field missing: $field',
          userMessage: 'Please provide a value for $field',
        );
      }
    }
  }

  // Sanitize input data
  static Map<String, dynamic> sanitizeInput(Map<String, dynamic> data) {
    final sanitized = <String, dynamic>{};
    
    for (final entry in data.entries) {
      final key = entry.key;
      final value = entry.value;
      
      if (value is String) {
        // Trim whitespace and handle empty strings
        final trimmed = value.trim();
        sanitized[key] = trimmed.isEmpty ? null : trimmed;
      } else {
        sanitized[key] = value;
      }
    }
    
    return sanitized;
  }
}

// Database Error Types
enum DatabaseErrorType {
  connectionError,
  tableNotFound,
  columnNotFound,
  duplicateEntry,
  foreignKeyConstraint,
  missingRequiredField,
  invalidInput,
  databaseLocked,
  diskError,
  sqlError,
  migrationError,
  backupError,
  restoreError,
  unknown,
}

// Database Error Model
class DatabaseError implements Exception {
  final DatabaseErrorType type;
  final String message;
  final String userMessage;
  final dynamic originalException;
  final DateTime timestamp;

  DatabaseError({
    required this.type,
    required this.message,
    required this.userMessage,
    this.originalException,
  }) : timestamp = DateTime.now();

  @override
  String toString() {
    return 'DatabaseError(${type.name}): $message';
  }

  // Check if this is a critical error that requires app restart
  bool get isCritical {
    switch (type) {
      case DatabaseErrorType.tableNotFound:
      case DatabaseErrorType.columnNotFound:
      case DatabaseErrorType.migrationError:
        return true;
      default:
        return false;
    }
  }

  // Check if this error can be retried
  bool get isRetryable {
    switch (type) {
      case DatabaseErrorType.databaseLocked:
      case DatabaseErrorType.connectionError:
        return true;
      default:
        return false;
    }
  }
}

// Database Operation Result
class DatabaseResult<T> {
  final bool success;
  final T? data;
  final DatabaseError? error;

  const DatabaseResult.success(this.data) : success = true, error = null;
  const DatabaseResult.failure(this.error) : success = false, data = null;

  // Helper methods
  bool get isSuccess => success;
  bool get isFailure => !success;
  
  R fold<R>(R Function(T data) onSuccess, R Function(DatabaseError error) onFailure) {
    if (success && data != null) {
      return onSuccess(data as T);
    } else if (error != null) {
      return onFailure(error!);
    } else {
      throw StateError('Invalid DatabaseResult state');
    }
  }
}
