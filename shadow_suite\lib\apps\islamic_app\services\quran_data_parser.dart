import 'package:flutter/services.dart';
import '../models/surah.dart';
import '../models/verse.dart' as verse_model;
import 'islamic_database_service.dart';

class QuranDataParser {
  static Future<void> parseAndPopulateQuran() async {
    try {
      // Check if data is already populated to avoid re-parsing
      final existingVerses = await IslamicDatabaseService.getVersesBySurah(1);
      if (existingVerses.isNotEmpty) {
        return; // Data already exists, no need to re-parse
      }

      // First, populate surahs from the existing QuranData
      for (final surahData in QuranData.surahs) {
        final surah = Surah(
          number: surahData['number'],
          nameArabic: surahData['nameArabic'],
          nameEnglish: surahData['nameEnglish'],
          nameTransliteration: surahData['nameTransliteration'],
          verseCount: surahData['verseCount'],
          revelationType: surahData['revelationType'],
          verses: [], // Will be populated separately
        );

        await IslamicDatabaseService.insertSurah(surah);
      }

      // Now read and parse verses from the Quran.txt file
      final content = await rootBundle.loadString('assets/Quran.txt');
      final lines = content.split('\n');

      // Use batch processing for better performance
      final verses = <verse_model.Verse>[];

      for (final line in lines) {
        if (line.trim().isEmpty) continue;

        final parts = line.split('|');
        if (parts.length != 3) continue;

        final surahNumber = int.tryParse(parts[0]);
        final verseNumber = int.tryParse(parts[1]);
        final arabicText = parts[2];

        if (surahNumber == null || verseNumber == null) continue;

        final verse = verse_model.Verse(
          surahNumber: surahNumber,
          verseNumber: verseNumber,
          textArabic: arabicText,
          textEnglish: _getEnglishTranslation(surahNumber, verseNumber),
          textTransliteration: _getTransliteration(surahNumber, verseNumber),
          juzNumber: _getJuzNumber(surahNumber, verseNumber),
          hizbNumber: _getHizbNumber(surahNumber, verseNumber),
          rukuNumber: _getRukuNumber(surahNumber, verseNumber),
        );

        verses.add(verse);

        // Insert in batches of 50 for better performance
        if (verses.length >= 50) {
          await _insertVersesBatch(verses);
          verses.clear();
        }
      }

      // Insert remaining verses
      if (verses.isNotEmpty) {
        await _insertVersesBatch(verses);
      }

      // Successfully populated Quran database
    } catch (e) {
      // Error parsing Quran data
      rethrow;
    }
  }

  static String _getEnglishTranslation(int surahNumber, int verseNumber) {
    // This is a simplified translation mapping
    // In a real implementation, you would have a complete translation database
    if (surahNumber == 1) {
      switch (verseNumber) {
        case 1: return 'In the name of Allah, the Entirely Merciful, the Especially Merciful.';
        case 2: return '[All] praise is [due] to Allah, Lord of the worlds -';
        case 3: return 'The Entirely Merciful, the Especially Merciful,';
        case 4: return 'Sovereign of the Day of Recompense.';
        case 5: return 'It is You we worship and You we ask for help.';
        case 6: return 'Guide us to the straight path -';
        case 7: return 'The path of those upon whom You have bestowed favor, not of those who have evoked [Your] anger or of those who are astray.';
        default: return 'Translation not available';
      }
    }
    return 'Translation not available for Surah $surahNumber, Verse $verseNumber';
  }

  static String _getTransliteration(int surahNumber, int verseNumber) {
    // This is a simplified transliteration mapping
    if (surahNumber == 1) {
      switch (verseNumber) {
        case 1: return 'Bismillahi\'r-rahmani\'r-raheem';
        case 2: return 'Alhamdu lillahi rabbi\'l-alameen';
        case 3: return 'Ar-rahmani\'r-raheem';
        case 4: return 'Maliki yawmi\'d-deen';
        case 5: return 'Iyyaka na\'budu wa iyyaka nasta\'een';
        case 6: return 'Ihdina\'s-sirata\'l-mustaqeem';
        case 7: return 'Sirata\'lladhina an\'amta alayhim ghayri\'l-maghdubi alayhim wa la\'d-dalleen';
        default: return 'Transliteration not available';
      }
    }
    return 'Transliteration not available for Surah $surahNumber, Verse $verseNumber';
  }

  static int _getJuzNumber(int surahNumber, int verseNumber) {
    // Simplified juz mapping - in reality this would be more complex
    if (surahNumber <= 2) return 1;
    if (surahNumber <= 4) return 2;
    if (surahNumber <= 6) return 3;
    // ... continue for all 30 juz
    return ((surahNumber - 1) ~/ 4) + 1; // Simplified calculation
  }

  static int _getHizbNumber(int surahNumber, int verseNumber) {
    // Simplified hizb mapping
    return ((surahNumber - 1) ~/ 2) + 1; // Simplified calculation
  }

  static int _getRukuNumber(int surahNumber, int verseNumber) {
    // Simplified ruku mapping
    return ((verseNumber - 1) ~/ 10) + 1; // Simplified calculation
  }

  static Future<void> _insertVersesBatch(List<verse_model.Verse> verses) async {
    for (final verse in verses) {
      try {
        await IslamicDatabaseService.insertVerse(verse);
      } catch (e) {
        // Continue with other verses if one fails
        continue;
      }
    }
  }
}