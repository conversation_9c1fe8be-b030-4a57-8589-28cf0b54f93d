import 'package:flutter/material.dart';
import '../services/comprehensive_permission_detection_service.dart';
import '../services/enhanced_permission_management_service.dart';

/// Comprehensive permission dashboard widget for Shadow Suite
class PermissionDashboardWidget extends StatefulWidget {
  const PermissionDashboardWidget({super.key});

  @override
  State<PermissionDashboardWidget> createState() =>
      _PermissionDashboardWidgetState();
}

class _PermissionDashboardWidgetState extends State<PermissionDashboardWidget> {
  final EnhancedPermissionManagementService _permissionService =
      EnhancedPermissionManagementService();

  Map<String, AppPermissionStatus>? _allAppPermissions;
  PermissionUsageStats? _usageStats;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializePermissions();
  }

  Future<void> _initializePermissions() async {
    try {
      await _permissionService.initialize();
      await _loadPermissionData();
    } catch (e) {
      debugPrint('Error initializing permissions: $e');
    }
  }

  Future<void> _loadPermissionData() async {
    setState(() => _isLoading = true);

    try {
      final detectionService = ComprehensivePermissionDetectionService();
      final allPermissions = await detectionService.getAllAppPermissions();
      final stats = await _permissionService.getPermissionUsageStats();

      setState(() {
        _allAppPermissions = allPermissions;
        _usageStats = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      debugPrint('Error loading permission data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading permission status...'),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPermissionOverview(),
          const SizedBox(height: 24),
          _buildAppPermissionsList(),
          const SizedBox(height: 24),
          _buildQuickActions(),
        ],
      ),
    );
  }

  Widget _buildPermissionOverview() {
    if (_usageStats == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Permission Overview',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Apps with All Permissions',
                    '${_usageStats!.appsWithAllPermissions}/${_usageStats!.totalApps}',
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    'Granted Permissions',
                    '${_usageStats!.grantedPermissions}/${_usageStats!.totalPermissions}',
                    Icons.security,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: _usageStats!.grantedPercentage / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                _usageStats!.grantedPercentage > 80
                    ? Colors.green
                    : Colors.orange,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${_usageStats!.grantedPercentage.toStringAsFixed(1)}% of permissions granted',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAppPermissionsList() {
    if (_allAppPermissions == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'App Permissions',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ..._allAppPermissions!.entries.map(
              (entry) => _buildAppPermissionTile(entry.key, entry.value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppPermissionTile(String appName, AppPermissionStatus status) {
    final displayName = _getAppDisplayName(appName);
    final hasAllPermissions = status.hasAllPermissions;
    final missingCount = status.missingPermissions.length;

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: hasAllPermissions ? Colors.green : Colors.orange,
        child: Icon(
          hasAllPermissions ? Icons.check : Icons.warning,
          color: Colors.white,
        ),
      ),
      title: Text(displayName),
      subtitle: hasAllPermissions
          ? const Text('All permissions granted')
          : Text(
              '$missingCount permission${missingCount > 1 ? 's' : ''} missing',
            ),
      trailing: hasAllPermissions
          ? null
          : TextButton(
              onPressed: () => _requestAppPermissions(appName),
              child: const Text('Grant'),
            ),
      onTap: () => _showAppPermissionDetails(appName, status),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Quick Actions',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _requestAllMissingPermissions,
                    icon: const Icon(Icons.security),
                    label: const Text('Grant All'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _loadPermissionData,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Refresh'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _showPermissionReport,
                icon: const Icon(Icons.analytics),
                label: const Text('View Detailed Report'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getAppDisplayName(String appName) {
    switch (appName) {
      case 'file_manager':
        return 'File Manager';
      case 'smart_gallery':
        return 'Smart Gallery+';
      case 'shadow_player':
        return 'Shadow Player';
      case 'money_manager':
        return 'Money Manager';
      case 'quran_suite':
        return 'Quran Suite';
      case 'excel_to_app':
        return 'Excel-to-App Builder';
      default:
        return appName;
    }
  }

  Future<void> _requestAppPermissions(String appName) async {
    try {
      final result = await _permissionService.requestPermissionsWithFallback(
        context,
        appName,
      );

      if (mounted) {
        if (result.success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Permissions granted for ${_getAppDisplayName(appName)}',
              ),
              backgroundColor: Colors.green,
            ),
          );
        } else if (result.userCancelled) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Permission request cancelled')),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Some permissions were denied for ${_getAppDisplayName(appName)}',
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }

      await _loadPermissionData();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error requesting permissions: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _requestAllMissingPermissions() async {
    if (_allAppPermissions == null) return;

    final appsWithMissingPermissions = _allAppPermissions!.entries
        .where((entry) => !entry.value.hasAllPermissions)
        .map((entry) => entry.key)
        .toList();

    if (appsWithMissingPermissions.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('All permissions are already granted')),
      );
      return;
    }

    for (final appName in appsWithMissingPermissions) {
      await _requestAppPermissions(appName);
    }
  }

  void _showAppPermissionDetails(String appName, AppPermissionStatus status) {
    showDialog(
      context: context,
      builder: (context) => AppPermissionDetailsDialog(
        appName: appName,
        status: status,
        onRequestPermissions: () => _requestAppPermissions(appName),
      ),
    );
  }

  void _showPermissionReport() {
    // Navigate to detailed permission report screen
    Navigator.of(context).pushNamed('/permission-report');
  }
}

/// App permission details dialog
class AppPermissionDetailsDialog extends StatelessWidget {
  final String appName;
  final AppPermissionStatus status;
  final VoidCallback onRequestPermissions;

  const AppPermissionDetailsDialog({
    super.key,
    required this.appName,
    required this.status,
    required this.onRequestPermissions,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('${_getAppDisplayName(appName)} Permissions'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...status.requiredPermissions.map((permission) {
            final isGranted =
                status.permissionStatuses[permission] ==
                PermissionStatus.granted;
            return ListTile(
              leading: Icon(
                isGranted ? Icons.check_circle : Icons.cancel,
                color: isGranted ? Colors.green : Colors.red,
              ),
              title: Text(_getPermissionDisplayName(permission)),
              subtitle: Text(isGranted ? 'Granted' : 'Not granted'),
              dense: true,
            );
          }),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
        if (!status.hasAllPermissions)
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onRequestPermissions();
            },
            child: const Text('Grant Permissions'),
          ),
      ],
    );
  }

  String _getAppDisplayName(String appName) {
    switch (appName) {
      case 'file_manager':
        return 'File Manager';
      case 'smart_gallery':
        return 'Smart Gallery+';
      case 'shadow_player':
        return 'Shadow Player';
      case 'money_manager':
        return 'Money Manager';
      case 'quran_suite':
        return 'Quran Suite';
      case 'excel_to_app':
        return 'Excel-to-App Builder';
      default:
        return appName;
    }
  }

  String _getPermissionDisplayName(String permission) {
    switch (permission) {
      case 'storage':
        return 'Storage Access';
      case 'camera':
        return 'Camera Access';
      case 'location':
        return 'Location Access';
      case 'notifications':
        return 'Notifications';
      case 'audio':
        return 'Audio Recording';
      case 'media_access':
        return 'Media Library Access';
      case 'manage_external_storage':
        return 'Manage External Storage';
      case 'photos':
        return 'Photo Library Access';
      case 'file_access':
        return 'File System Access';
      default:
        return permission;
    }
  }
}
