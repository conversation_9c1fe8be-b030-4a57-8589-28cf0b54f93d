import 'dart:async';
import 'dart:math' as math;
import '../models/enhanced_quran_models.dart';
import '../../../core/database/database_service.dart';
import '../../../core/services/error_handler.dart' as error_handler;

class EnhancedIslamicService {
  static final List<QuranTranslation> _translations = [];
  static final List<QuranTafseer> _tafseers = [];
  static final List<PrayerCustomization> _prayerCustomizations = [];
  static final List<IslamicCalendarEvent> _calendarEvents = [];
  static final List<HajjUmrahGuide> _pilgrimageGuides = [];
  
  // Initialize enhanced Islamic service
  static Future<void> initialize() async {
    await _loadTranslations();
    await _loadTafseers();
    await _loadPrayerCustomizations();
    await _loadCalendarEvents();
    await _loadPilgrimageGuides();
    await _initializeDefaultData();
  }

  // FEATURE 1: Multiple Quran Translations
  static Future<QuranTranslation> addTranslation({
    required String name,
    required String language,
    required String translator,
    required String description,
    required Map<int, Map<int, String>> verses,
    bool isDefault = false,
  }) async {
    try {
      final translation = QuranTranslation(
        id: 'translation_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        language: language,
        translator: translator,
        description: description,
        isDefault: isDefault,
        verses: verses,
        lastUpdated: DateTime.now(),
        metadata: {},
      );

      await DatabaseService.safeInsert('quran_translations', translation.toJson());
      _translations.add(translation);
      
      return translation;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Add Quran translation');
      rethrow;
    }
  }

  // FEATURE 2: Advanced Tafseer Integration
  static Future<QuranTafseer> addTafseer({
    required String name,
    required String author,
    required String language,
    required TafseerType type,
    required String description,
    required Map<int, Map<int, String>> commentary,
  }) async {
    try {
      final tafseer = QuranTafseer(
        id: 'tafseer_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        author: author,
        language: language,
        type: type,
        description: description,
        commentary: commentary,
        lastUpdated: DateTime.now(),
        metadata: {},
      );

      await DatabaseService.safeInsert('quran_tafseers', tafseer.toJson());
      _tafseers.add(tafseer);
      
      return tafseer;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Add Quran tafseer');
      rethrow;
    }
  }

  // FEATURE 3: Advanced Prayer Customization
  static Future<PrayerCustomization> updatePrayerSettings({
    required String userId,
    PrayerCalculationMethod? calculationMethod,
    AsrCalculationMethod? asrMethod,
    HighLatitudeRule? highLatitudeRule,
    Map<String, int>? timeAdjustments,
    bool? enableNotifications,
    Map<String, bool>? notificationSettings,
    Map<String, String>? customAdhan,
    bool? enableQiblaDirection,
    double? qiblaAdjustment,
    Map<String, dynamic>? customSettings,
  }) async {
    try {
      var existingCustomization = _prayerCustomizations
          .where((c) => c.userId == userId)
          .firstOrNull;

      if (existingCustomization != null) {
        final updatedCustomization = existingCustomization.copyWith(
          calculationMethod: calculationMethod,
          asrMethod: asrMethod,
          highLatitudeRule: highLatitudeRule,
          timeAdjustments: timeAdjustments,
          enableNotifications: enableNotifications,
          notificationSettings: notificationSettings,
          customAdhan: customAdhan,
          enableQiblaDirection: enableQiblaDirection,
          qiblaAdjustment: qiblaAdjustment,
          customSettings: customSettings,
        );

        await DatabaseService.safeUpdate(
          'prayer_customizations',
          updatedCustomization.toJson(),
          where: 'id = ?',
          whereArgs: [existingCustomization.id],
        );

        final index = _prayerCustomizations.indexWhere((c) => c.id == existingCustomization.id);
        if (index != -1) {
          _prayerCustomizations[index] = updatedCustomization;
        }

        return updatedCustomization;
      } else {
        final newCustomization = PrayerCustomization(
          id: 'prayer_custom_${DateTime.now().millisecondsSinceEpoch}',
          userId: userId,
          calculationMethod: calculationMethod ?? PrayerCalculationMethod.muslimWorldLeague,
          asrMethod: asrMethod ?? AsrCalculationMethod.shafi,
          highLatitudeRule: highLatitudeRule ?? HighLatitudeRule.middleOfTheNight,
          timeAdjustments: timeAdjustments ?? {},
          enableNotifications: enableNotifications ?? true,
          notificationSettings: notificationSettings ?? {},
          customAdhan: customAdhan ?? {},
          enableQiblaDirection: enableQiblaDirection ?? true,
          qiblaAdjustment: qiblaAdjustment ?? 0.0,
          customSettings: customSettings ?? {},
          lastModified: DateTime.now(),
        );

        await DatabaseService.safeInsert('prayer_customizations', newCustomization.toJson());
        _prayerCustomizations.add(newCustomization);

        return newCustomization;
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Update prayer settings');
      rethrow;
    }
  }

  // FEATURE 4: Islamic Calendar Events
  static Future<IslamicCalendarEvent> addCalendarEvent({
    required String name,
    required String description,
    required IslamicEventType type,
    required DateTime gregorianDate,
    required IslamicDate islamicDate,
    bool isRecurring = false,
    EventSignificance significance = EventSignificance.medium,
    List<String>? traditions,
    List<String>? supplications,
  }) async {
    try {
      final event = IslamicCalendarEvent(
        id: 'event_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        description: description,
        type: type,
        gregorianDate: gregorianDate,
        islamicDate: islamicDate,
        isRecurring: isRecurring,
        significance: significance,
        traditions: traditions ?? [],
        supplications: supplications ?? [],
        metadata: {},
      );

      await DatabaseService.safeInsert('islamic_calendar_events', event.toJson());
      _calendarEvents.add(event);
      
      return event;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Add Islamic calendar event');
      rethrow;
    }
  }

  // FEATURE 5: Hajj/Umrah Guides
  static Future<HajjUmrahGuide> createPilgrimageGuide({
    required String title,
    required PilgrimageType type,
    required List<PilgrimageStep> steps,
    List<String>? supplications,
    List<String>? tips,
    Map<String, String>? translations,
  }) async {
    try {
      final guide = HajjUmrahGuide(
        id: 'guide_${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        type: type,
        steps: steps,
        supplications: supplications ?? [],
        tips: tips ?? [],
        translations: translations ?? {},
        lastUpdated: DateTime.now(),
      );

      await DatabaseService.safeInsert('hajj_umrah_guides', guide.toJson());
      _pilgrimageGuides.add(guide);
      
      return guide;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Create pilgrimage guide');
      rethrow;
    }
  }

  // FEATURE 6: Enhanced Prayer Time Calculations
  static Map<String, DateTime> calculatePrayerTimes({
    required double latitude,
    required double longitude,
    required DateTime date,
    PrayerCalculationMethod method = PrayerCalculationMethod.muslimWorldLeague,
    AsrCalculationMethod asrMethod = AsrCalculationMethod.shafi,
    HighLatitudeRule highLatitudeRule = HighLatitudeRule.middleOfTheNight,
    Map<String, int>? adjustments,
  }) {
    try {
      // Get calculation parameters for the method
      final params = _getCalculationParameters(method);
      
      // Calculate Julian day
      final julianDay = _calculateJulianDay(date);
      
      // Calculate sun declination
      final sunDeclination = _calculateSunDeclination(julianDay);
      
      // Calculate equation of time
      final equationOfTime = _calculateEquationOfTime(julianDay);
      
      // Calculate prayer times
      final times = <String, DateTime>{};
      
      // Fajr
      final fajrTime = _calculatePrayerTime(
        latitude, longitude, date, params.fajrAngle, sunDeclination, equationOfTime, true);
      times['fajr'] = _applyAdjustment(fajrTime, adjustments?['fajr'] ?? 0);
      
      // Sunrise
      final sunriseTime = _calculatePrayerTime(
        latitude, longitude, date, 0.833, sunDeclination, equationOfTime, true);
      times['sunrise'] = _applyAdjustment(sunriseTime, adjustments?['sunrise'] ?? 0);
      
      // Dhuhr
      final dhuhrTime = _calculateDhuhrTime(longitude, date, equationOfTime);
      times['dhuhr'] = _applyAdjustment(dhuhrTime, adjustments?['dhuhr'] ?? 0);
      
      // Asr
      final asrTime = _calculateAsrTime(
        latitude, longitude, date, sunDeclination, equationOfTime, asrMethod);
      times['asr'] = _applyAdjustment(asrTime, adjustments?['asr'] ?? 0);
      
      // Maghrib
      final maghribTime = _calculatePrayerTime(
        latitude, longitude, date, 0.833, sunDeclination, equationOfTime, false);
      times['maghrib'] = _applyAdjustment(maghribTime, adjustments?['maghrib'] ?? 0);
      
      // Isha
      final ishaTime = _calculatePrayerTime(
        latitude, longitude, date, params.ishaAngle, sunDeclination, equationOfTime, false);
      times['isha'] = _applyAdjustment(ishaTime, adjustments?['isha'] ?? 0);
      
      return times;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Calculate prayer times');
      return {};
    }
  }

  // FEATURE 7: Qibla Direction with High Accuracy
  static double calculateQiblaDirection({
    required double latitude,
    required double longitude,
    double adjustment = 0.0,
  }) {
    try {
      // Kaaba coordinates
      const kaabaLatitude = 21.4225;
      const kaabaLongitude = 39.8262;
      
      // Convert to radians
      final lat1 = latitude * (math.pi / 180);
      final lon1 = longitude * (math.pi / 180);
      final lat2 = kaabaLatitude * (math.pi / 180);
      final lon2 = kaabaLongitude * (math.pi / 180);
      
      // Calculate bearing
      final deltaLon = lon2 - lon1;
      final y = math.sin(deltaLon) * math.cos(lat2);
      final x = math.cos(lat1) * math.sin(lat2) - 
                math.sin(lat1) * math.cos(lat2) * math.cos(deltaLon);
      
      var bearing = math.atan2(y, x) * (180 / math.pi);
      bearing = (bearing + 360) % 360; // Normalize to 0-360
      
      return (bearing + adjustment) % 360;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Calculate Qibla direction');
      return 0.0;
    }
  }

  // FEATURE 8: Islamic Finance Calculators
  static Map<String, double> calculateZakat({
    double? cash,
    double? gold,
    double? silver,
    double? investments,
    double? businessAssets,
    double? debts,
  }) {
    try {
      final results = <String, double>{};
      
      // Nisab values (approximate, should be updated regularly)
      const goldNisab = 87.48; // grams
      const silverNisab = 612.36; // grams
      const goldPricePerGram = 60.0; // USD (should be real-time)
      const silverPricePerGram = 0.8; // USD (should be real-time)
      
      final goldNisabValue = goldNisab * goldPricePerGram;
      final silverNisabValue = silverNisab * silverPricePerGram;
      final nisabThreshold = math.min(goldNisabValue, silverNisabValue);
      
      // Calculate total zakatable wealth
      final totalWealth = (cash ?? 0) + 
                         (gold ?? 0) + 
                         (silver ?? 0) + 
                         (investments ?? 0) + 
                         (businessAssets ?? 0) - 
                         (debts ?? 0);
      
      results['totalWealth'] = totalWealth;
      results['nisabThreshold'] = nisabThreshold;
      results['isZakatDue'] = totalWealth >= nisabThreshold ? 1.0 : 0.0;
      
      if (totalWealth >= nisabThreshold) {
        results['zakatAmount'] = totalWealth * 0.025; // 2.5%
      } else {
        results['zakatAmount'] = 0.0;
      }
      
      return results;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Calculate Zakat');
      return {};
    }
  }

  // FEATURE 9: Scholarly Content Management
  static Future<ScholarlyContent> addScholarlyContent({
    required String title,
    required String author,
    required ScholarlyContentType type,
    required String content,
    required String language,
    List<String>? tags,
    List<String>? references,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final scholarlyContent = ScholarlyContent(
        id: 'scholarly_${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        author: author,
        type: type,
        content: content,
        language: language,
        tags: tags ?? [],
        references: references ?? [],
        metadata: metadata ?? {},
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      await DatabaseService.safeInsert('scholarly_content', scholarlyContent.toJson());
      
      return scholarlyContent;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Add scholarly content');
      rethrow;
    }
  }

  // FEATURE 10: Personalized Islamic Learning Paths
  static Future<LearningPath> createLearningPath({
    required String userId,
    required String title,
    required String description,
    required LearningLevel level,
    required List<LearningModule> modules,
    Map<String, dynamic>? preferences,
  }) async {
    try {
      final learningPath = LearningPath(
        id: 'learning_${DateTime.now().millisecondsSinceEpoch}',
        userId: userId,
        title: title,
        description: description,
        level: level,
        modules: modules,
        progress: 0.0,
        preferences: preferences ?? {},
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      await DatabaseService.safeInsert('learning_paths', learningPath.toJson());
      
      return learningPath;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Create learning path');
      rethrow;
    }
  }

  // Helper methods for prayer time calculations
  static PrayerCalculationParameters _getCalculationParameters(PrayerCalculationMethod method) {
    switch (method) {
      case PrayerCalculationMethod.muslimWorldLeague:
        return PrayerCalculationParameters(fajrAngle: 18.0, ishaAngle: 17.0);
      case PrayerCalculationMethod.egyptianGeneralAuthorityOfSurvey:
        return PrayerCalculationParameters(fajrAngle: 19.5, ishaAngle: 17.5);
      case PrayerCalculationMethod.universityOfIslamicSciencesKarachi:
        return PrayerCalculationParameters(fajrAngle: 18.0, ishaAngle: 18.0);
      case PrayerCalculationMethod.ummAlQuraUniversityMakkah:
        return PrayerCalculationParameters(fajrAngle: 18.5, ishaAngle: 90.0);
      default:
        return PrayerCalculationParameters(fajrAngle: 18.0, ishaAngle: 17.0);
    }
  }

  static double _calculateJulianDay(DateTime date) {
    final a = (14 - date.month) ~/ 12;
    final y = date.year - a;
    final m = date.month + 12 * a - 3;

    return date.day + (153 * m + 2) ~/ 5 + 365 * y + y ~/ 4 - y ~/ 100 + y ~/ 400 - 32045;
  }

  static double _calculateSunDeclination(double julianDay) {
    final n = julianDay - 2451545.0;
    final l = (280.460 + 0.9856474 * n) % 360;
    final g = math.pi / 180 * ((357.528 + 0.9856003 * n) % 360);
    final lambda = math.pi / 180 * (l + 1.915 * math.sin(g) + 0.020 * math.sin(2 * g));

    return math.asin(math.sin(23.439 * math.pi / 180) * math.sin(lambda));
  }

  static double _calculateEquationOfTime(double julianDay) {
    final n = julianDay - 2451545.0;
    final l = (280.460 + 0.9856474 * n) % 360;
    final g = math.pi / 180 * ((357.528 + 0.9856003 * n) % 360);
    final lambda = l + 1.915 * math.sin(g) + 0.020 * math.sin(2 * g);
    final alpha = lambda - 0.0057183;

    return 4 * (l - 0.0057183 - alpha);
  }

  static DateTime _calculatePrayerTime(
    double latitude, double longitude, DateTime date, double angle,
    double sunDeclination, double equationOfTime, bool isBefore) {

    final latRad = latitude * math.pi / 180;
    final angleRad = angle * math.pi / 180;

    final cosH = (math.cos(angleRad) - math.sin(latRad) * math.sin(sunDeclination)) /
                 (math.cos(latRad) * math.cos(sunDeclination));

    if (cosH.abs() > 1) {
      // Extreme latitude case
      return date;
    }

    final h = math.acos(cosH) * 180 / math.pi;
    final timeOffset = isBefore ? -h : h;

    final prayerTime = 12 + timeOffset / 15 - longitude / 15 + equationOfTime / 60;

    final hours = prayerTime.floor();
    final minutes = ((prayerTime - hours) * 60).round();

    return DateTime(date.year, date.month, date.day, hours, minutes);
  }

  static DateTime _calculateDhuhrTime(double longitude, DateTime date, double equationOfTime) {
    final dhuhrTime = 12 - longitude / 15 + equationOfTime / 60;
    final hours = dhuhrTime.floor();
    final minutes = ((dhuhrTime - hours) * 60).round();

    return DateTime(date.year, date.month, date.day, hours, minutes);
  }

  static DateTime _calculateAsrTime(
    double latitude, double longitude, DateTime date,
    double sunDeclination, double equationOfTime, AsrCalculationMethod method) {

    final latRad = latitude * math.pi / 180;
    final shadowFactor = method == AsrCalculationMethod.hanafi ? 2.0 : 1.0;

    final tanH = 1 / (shadowFactor + math.tan((math.pi / 2 - latRad - sunDeclination).abs()));
    final h = math.atan(tanH) * 180 / math.pi;

    final asrTime = 12 + h / 15 - longitude / 15 + equationOfTime / 60;
    final hours = asrTime.floor();
    final minutes = ((asrTime - hours) * 60).round();

    return DateTime(date.year, date.month, date.day, hours, minutes);
  }

  static DateTime _applyAdjustment(DateTime time, int adjustmentMinutes) {
    return time.add(Duration(minutes: adjustmentMinutes));
  }

  // Data loading methods
  static Future<void> _loadTranslations() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM quran_translations');
      _translations.clear();
      for (final row in results) {
        _translations.add(QuranTranslation.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load translations');
    }
  }

  static Future<void> _loadTafseers() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM quran_tafseers');
      _tafseers.clear();
      for (final row in results) {
        _tafseers.add(QuranTafseer.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load tafseers');
    }
  }

  static Future<void> _loadPrayerCustomizations() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM prayer_customizations');
      _prayerCustomizations.clear();
      for (final row in results) {
        _prayerCustomizations.add(PrayerCustomization.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load prayer customizations');
    }
  }

  static Future<void> _loadCalendarEvents() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM islamic_calendar_events');
      _calendarEvents.clear();
      for (final row in results) {
        _calendarEvents.add(IslamicCalendarEvent.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load calendar events');
    }
  }

  static Future<void> _loadPilgrimageGuides() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM hajj_umrah_guides');
      _pilgrimageGuides.clear();
      for (final row in results) {
        _pilgrimageGuides.add(HajjUmrahGuide.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load pilgrimage guides');
    }
  }

  static Future<void> _initializeDefaultData() async {
    try {
      // Initialize default translations if none exist
      if (_translations.isEmpty) {
        await _addDefaultTranslations();
      }

      // Initialize default calendar events
      if (_calendarEvents.isEmpty) {
        await _addDefaultCalendarEvents();
      }

      // Initialize default pilgrimage guides
      if (_pilgrimageGuides.isEmpty) {
        await _addDefaultPilgrimageGuides();
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Initialize default data');
    }
  }

  static Future<void> _addDefaultTranslations() async {
    // Add sample translations (in production, load from comprehensive database)
    final sampleVerses = <int, Map<int, String>>{
      1: {
        1: 'In the name of Allah, the Entirely Merciful, the Especially Merciful.',
        2: '[All] praise is [due] to Allah, Lord of the worlds -',
        3: 'The Entirely Merciful, the Especially Merciful,',
        4: 'Sovereign of the Day of Recompense.',
        5: 'It is You we worship and You we ask for help.',
        6: 'Guide us to the straight path -',
        7: 'The path of those upon whom You have bestowed favor, not of those who have evoked [Your] anger or of those who are astray.',
      }
    };

    await addTranslation(
      name: 'Sahih International',
      language: 'English',
      translator: 'Sahih International',
      description: 'Clear and accurate English translation',
      verses: sampleVerses,
      isDefault: true,
    );
  }

  static Future<void> _addDefaultCalendarEvents() async {
    // Add major Islamic events
    final events = [
      {
        'name': 'Ramadan Begins',
        'description': 'The holy month of fasting begins',
        'type': IslamicEventType.religious,
        'significance': EventSignificance.critical,
      },
      {
        'name': 'Eid al-Fitr',
        'description': 'Festival of Breaking the Fast',
        'type': IslamicEventType.religious,
        'significance': EventSignificance.critical,
      },
      {
        'name': 'Eid al-Adha',
        'description': 'Festival of Sacrifice',
        'type': IslamicEventType.religious,
        'significance': EventSignificance.critical,
      },
    ];

    for (final event in events) {
      await addCalendarEvent(
        name: event['name'] as String,
        description: event['description'] as String,
        type: event['type'] as IslamicEventType,
        gregorianDate: DateTime.now().add(Duration(days: 30)), // Placeholder
        islamicDate: IslamicDate(
          day: 1,
          month: 9,
          year: 1445,
          monthName: 'Ramadan',
          dayName: 'Monday',
        ),
        significance: event['significance'] as EventSignificance,
        isRecurring: true,
      );
    }
  }

  static Future<void> _addDefaultPilgrimageGuides() async {
    // Add Umrah guide
    final umrahSteps = [
      PilgrimageStep(
        id: 'umrah_step_1',
        title: 'Ihram',
        description: 'Enter the state of consecration',
        order: 1,
        isRequired: true,
        supplications: ['Labbayka Allahumma labbayk...'],
        metadata: {},
      ),
      PilgrimageStep(
        id: 'umrah_step_2',
        title: 'Tawaf',
        description: 'Circumambulate the Kaaba seven times',
        order: 2,
        isRequired: true,
        supplications: ['Bismillahi Allahu akbar...'],
        metadata: {},
      ),
    ];

    await createPilgrimageGuide(
      title: 'Complete Umrah Guide',
      type: PilgrimageType.umrah,
      steps: umrahSteps,
      tips: [
        'Maintain wudu throughout the rituals',
        'Stay hydrated and take breaks when needed',
        'Follow the crowd flow during busy times',
      ],
    );
  }

  // Getters
  static List<QuranTranslation> get translations => List.unmodifiable(_translations);
  static List<QuranTafseer> get tafseers => List.unmodifiable(_tafseers);
  static List<PrayerCustomization> get prayerCustomizations => List.unmodifiable(_prayerCustomizations);
  static List<IslamicCalendarEvent> get calendarEvents => List.unmodifiable(_calendarEvents);
  static List<HajjUmrahGuide> get pilgrimageGuides => List.unmodifiable(_pilgrimageGuides);

  // Dispose
  static void dispose() {
    _translations.clear();
    _tafseers.clear();
    _prayerCustomizations.clear();
    _calendarEvents.clear();
    _pilgrimageGuides.clear();
  }
}

// Helper classes
class PrayerCalculationParameters {
  final double fajrAngle;
  final double ishaAngle;

  const PrayerCalculationParameters({
    required this.fajrAngle,
    required this.ishaAngle,
  });
}

// Additional models for features 9-10
class ScholarlyContent {
  final String id;
  final String title;
  final String author;
  final ScholarlyContentType type;
  final String content;
  final String language;
  final List<String> tags;
  final List<String> references;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final DateTime lastModified;

  const ScholarlyContent({
    required this.id,
    required this.title,
    required this.author,
    required this.type,
    required this.content,
    required this.language,
    required this.tags,
    required this.references,
    required this.metadata,
    required this.createdAt,
    required this.lastModified,
  });

  factory ScholarlyContent.fromJson(Map<String, dynamic> json) {
    return ScholarlyContent(
      id: json['id'] as String,
      title: json['title'] as String,
      author: json['author'] as String,
      type: ScholarlyContentType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ScholarlyContentType.article,
      ),
      content: json['content'] as String,
      language: json['language'] as String,
      tags: List<String>.from(json['tags'] as List? ?? []),
      references: List<String>.from(json['references'] as List? ?? []),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
      createdAt: DateTime.parse(json['created_at'] as String),
      lastModified: DateTime.parse(json['last_modified'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'author': author,
      'type': type.name,
      'content': content,
      'language': language,
      'tags': tags,
      'references': references,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'last_modified': lastModified.toIso8601String(),
    };
  }
}

class LearningPath {
  final String id;
  final String userId;
  final String title;
  final String description;
  final LearningLevel level;
  final List<LearningModule> modules;
  final double progress;
  final Map<String, dynamic> preferences;
  final DateTime createdAt;
  final DateTime lastModified;

  const LearningPath({
    required this.id,
    required this.userId,
    required this.title,
    required this.description,
    required this.level,
    required this.modules,
    required this.progress,
    required this.preferences,
    required this.createdAt,
    required this.lastModified,
  });

  factory LearningPath.fromJson(Map<String, dynamic> json) {
    return LearningPath(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      level: LearningLevel.values.firstWhere(
        (e) => e.name == json['level'],
        orElse: () => LearningLevel.beginner,
      ),
      modules: (json['modules'] as List<dynamic>?)
          ?.map((e) => LearningModule.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      progress: (json['progress'] as num?)?.toDouble() ?? 0.0,
      preferences: Map<String, dynamic>.from(json['preferences'] as Map? ?? {}),
      createdAt: DateTime.parse(json['created_at'] as String),
      lastModified: DateTime.parse(json['last_modified'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'description': description,
      'level': level.name,
      'modules': modules.map((e) => e.toJson()).toList(),
      'progress': progress,
      'preferences': preferences,
      'created_at': createdAt.toIso8601String(),
      'last_modified': lastModified.toIso8601String(),
    };
  }
}

class LearningModule {
  final String id;
  final String title;
  final String description;
  final int order;
  final bool isCompleted;
  final Map<String, dynamic> content;

  const LearningModule({
    required this.id,
    required this.title,
    required this.description,
    required this.order,
    required this.isCompleted,
    required this.content,
  });

  factory LearningModule.fromJson(Map<String, dynamic> json) {
    return LearningModule(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      order: json['order'] as int,
      isCompleted: json['is_completed'] as bool? ?? false,
      content: Map<String, dynamic>.from(json['content'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'order': order,
      'is_completed': isCompleted,
      'content': content,
    };
  }
}

// Additional enums
enum ScholarlyContentType { article, book, lecture, fatwa, commentary }
enum LearningLevel { beginner, intermediate, advanced, scholar }
