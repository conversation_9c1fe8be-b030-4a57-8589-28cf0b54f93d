import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/spreadsheet_cell.dart';
import '../models/spreadsheet.dart';
import '../services/enhanced_formula_engine.dart';
import '../services/formula_autocomplete_service.dart';
import 'formula_autocomplete_widget.dart';

// Enhanced Spreadsheet with full Excel-like keyboard support
class EnhancedSpreadsheet extends ConsumerStatefulWidget {
  final int rows;
  final int columns;
  final Function(int row, int col, dynamic value)? onCellChanged;
  final Function(int row, int col)? onCellSelected;

  const EnhancedSpreadsheet({
    super.key,
    this.rows = 100,
    this.columns = 26,
    this.onCellChanged,
    this.onCellSelected,
  });

  @override
  ConsumerState<EnhancedSpreadsheet> createState() =>
      _EnhancedSpreadsheetState();
}

class _EnhancedSpreadsheetState extends ConsumerState<EnhancedSpreadsheet> {
  final Map<String, SpreadsheetCell> _cells = {};
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();
  final FocusNode _focusNode = FocusNode();

  int _selectedRow = 0;
  int _selectedCol = 0;
  bool _isEditing = false;
  String _editingValue = '';

  final TextEditingController _editController = TextEditingController();
  final Set<String> _referencedCells = {};

  // Clipboard support
  List<List<SpreadsheetCell?>>? _clipboard;
  int _clipboardStartRow = 0;
  int _clipboardStartCol = 0;

  @override
  void initState() {
    super.initState();
    _focusNode.requestFocus();
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    _focusNode.dispose();
    _editController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      onKeyEvent: _handleKeyEvent,
      child: Container(
        decoration: BoxDecoration(border: Border.all(color: Colors.grey[300]!)),
        child: Column(
          children: [
            // Formula bar
            _buildFormulaBar(),

            // Spreadsheet grid
            Expanded(
              child: Row(
                children: [
                  // Row headers
                  _buildRowHeaders(),

                  // Main grid
                  Expanded(
                    child: Column(
                      children: [
                        // Column headers
                        _buildColumnHeaders(),

                        // Data grid
                        Expanded(child: _buildDataGrid()),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormulaBar() {
    final cellAddress = _getCellAddress(_selectedRow, _selectedCol);
    final cell = _cells[cellAddress];
    final displayValue = _isEditing
        ? _editingValue
        : (cell?.formula ?? cell?.rawValue ?? '');
    final isInFormulaMode = _isEditing && _editingValue.startsWith('=');

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: FormulaAutoCompleteWidget(
        controller: _isEditing
            ? _editController
            : TextEditingController(text: displayValue),
        onFormulaChanged: (value) {
          if (_isEditing) {
            setState(() {
              _editingValue = value;
            });
          }
        },
        onFormulaSubmitted: (value) {
          _commitEdit(value);
        },
        onCellReferenceMode: () {
          // Enable cell reference selection mode
          // Cell reference mode functionality can be implemented here
        },
        isInFormulaMode: isInFormulaMode,
        currentCellAddress: cellAddress,
      ),
    );
  }

  Widget _buildRowHeaders() {
    return SizedBox(
      width: 50,
      child: Column(
        children: [
          // Empty corner cell
          Container(
            height: 30,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              border: Border.all(color: Colors.grey[300]!),
            ),
          ),

          // Row numbers
          Expanded(
            child: ListView.builder(
              controller: _verticalController,
              itemCount: widget.rows,
              itemBuilder: (context, index) {
                final isSelected = index == _selectedRow;
                return Container(
                  height: 25,
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.blue[100] : Colors.grey[200],
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: Center(
                    child: Text(
                      '${index + 1}',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: isSelected
                            ? FontWeight.bold
                            : FontWeight.normal,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColumnHeaders() {
    return SizedBox(
      height: 30,
      child: ListView.builder(
        controller: _horizontalController,
        scrollDirection: Axis.horizontal,
        itemCount: widget.columns,
        itemBuilder: (context, index) {
          final isSelected = index == _selectedCol;
          final columnName = _getColumnName(index);

          return Container(
            width: 100,
            decoration: BoxDecoration(
              color: isSelected ? Colors.blue[100] : Colors.grey[200],
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Center(
              child: Text(
                columnName,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDataGrid() {
    return ListView.builder(
      controller: _verticalController,
      itemCount: widget.rows,
      itemBuilder: (context, rowIndex) {
        return SizedBox(
          height: 25,
          child: ListView.builder(
            controller: _horizontalController,
            scrollDirection: Axis.horizontal,
            itemCount: widget.columns,
            itemBuilder: (context, colIndex) {
              return _buildCell(rowIndex, colIndex);
            },
          ),
        );
      },
    );
  }

  Widget _buildCell(int row, int col) {
    final cellAddress = _getCellAddress(row, col);
    final cell = _cells[cellAddress];
    final isSelected = row == _selectedRow && col == _selectedCol;
    final isEditing = _isEditing && isSelected;

    String displayValue = '';
    if (isEditing) {
      displayValue = _editingValue;
    } else if (cell != null) {
      displayValue = cell.calculatedValue?.toString() ?? cell.rawValue;
    }

    return GestureDetector(
      onTap: () => _selectCell(row, col),
      onDoubleTap: () => _startEditing(),
      child: Container(
        width: 100,
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue[50] : Colors.white,
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
        child: isEditing
            ? TextField(
                controller: _editController,
                decoration: const InputDecoration(
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.zero,
                ),
                style: const TextStyle(fontSize: 12),
                onSubmitted: (value) => _commitEdit(value),
                autofocus: true,
              )
            : Text(
                displayValue,
                style: TextStyle(
                  fontSize: 12,
                  color: cell?.dataType == CellDataType.error
                      ? Colors.red
                      : Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
              ),
      ),
    );
  }

  KeyEventResult _handleKeyEvent(FocusNode node, KeyEvent event) {
    if (event is! KeyDownEvent) return KeyEventResult.ignored;

    final isCtrlPressed = HardwareKeyboard.instance.isControlPressed;
    final isShiftPressed = HardwareKeyboard.instance.isShiftPressed;

    // Handle editing mode keys
    if (_isEditing) {
      if (event.logicalKey == LogicalKeyboardKey.enter) {
        _commitEdit(_editController.text);
        return KeyEventResult.handled;
      } else if (event.logicalKey == LogicalKeyboardKey.escape) {
        _cancelEdit();
        return KeyEventResult.handled;
      }
      return KeyEventResult.ignored;
    }

    // Navigation keys
    if (event.logicalKey == LogicalKeyboardKey.arrowUp) {
      _moveSelection(-1, 0, isCtrlPressed, isShiftPressed);
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
      _moveSelection(1, 0, isCtrlPressed, isShiftPressed);
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.arrowLeft) {
      _moveSelection(0, -1, isCtrlPressed, isShiftPressed);
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.arrowRight) {
      _moveSelection(0, 1, isCtrlPressed, isShiftPressed);
      return KeyEventResult.handled;
    }

    // Page navigation
    if (event.logicalKey == LogicalKeyboardKey.pageUp) {
      _moveSelection(-10, 0, false, false);
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.pageDown) {
      _moveSelection(10, 0, false, false);
      return KeyEventResult.handled;
    }

    // Home/End keys
    if (event.logicalKey == LogicalKeyboardKey.home) {
      if (isCtrlPressed) {
        _selectCell(0, 0);
      } else {
        _selectCell(_selectedRow, 0);
      }
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.end) {
      if (isCtrlPressed) {
        _selectCell(widget.rows - 1, widget.columns - 1);
      } else {
        _selectCell(_selectedRow, widget.columns - 1);
      }
      return KeyEventResult.handled;
    }

    // Edit mode
    if (event.logicalKey == LogicalKeyboardKey.f2) {
      _startEditing();
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.enter) {
      _startEditing();
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.delete) {
      _deleteCell();
      return KeyEventResult.handled;
    }

    // Clipboard operations
    if (isCtrlPressed) {
      if (event.logicalKey == LogicalKeyboardKey.keyC) {
        _copyCell();
        return KeyEventResult.handled;
      } else if (event.logicalKey == LogicalKeyboardKey.keyV) {
        _pasteCell();
        return KeyEventResult.handled;
      } else if (event.logicalKey == LogicalKeyboardKey.keyX) {
        _cutCell();
        return KeyEventResult.handled;
      }
    }

    // Start typing
    if (event.character != null && event.character!.isNotEmpty) {
      _startEditing(initialValue: event.character!);
      return KeyEventResult.handled;
    }

    return KeyEventResult.ignored;
  }

  void _selectCell(int row, int col) {
    setState(() {
      _selectedRow = row.clamp(0, widget.rows - 1);
      _selectedCol = col.clamp(0, widget.columns - 1);
      _isEditing = false;
    });

    widget.onCellSelected?.call(_selectedRow, _selectedCol);
    _ensureCellVisible();
  }

  void _moveSelection(
    int deltaRow,
    int deltaCol,
    bool isCtrlPressed,
    bool isShiftPressed,
  ) {
    int newRow = _selectedRow;
    int newCol = _selectedCol;

    if (isCtrlPressed) {
      // Ctrl+Arrow: Jump to edge of data region
      if (deltaRow != 0) {
        newRow = deltaRow > 0 ? _findLastDataRow() : _findFirstDataRow();
      } else {
        newCol = deltaCol > 0 ? _findLastDataCol() : _findFirstDataCol();
      }
    } else {
      newRow += deltaRow;
      newCol += deltaCol;
    }

    _selectCell(newRow, newCol);
  }

  void _startEditing({String? initialValue}) {
    final cellAddress = _getCellAddress(_selectedRow, _selectedCol);
    final cell = _cells[cellAddress];

    setState(() {
      _isEditing = true;
      _editingValue = initialValue ?? cell?.formula ?? cell?.rawValue ?? '';
      _editController.text = _editingValue;
      _referencedCells.clear();
    });
  }

  void _commitEdit(String value) {
    final cellAddress = _getCellAddress(_selectedRow, _selectedCol);

    // Validate formula if it starts with =
    if (value.startsWith('=')) {
      final validation = FormulaAutoCompleteService.validateFormula(value);
      if (!validation.isValid) {
        // Show error and don't commit
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Formula error: ${validation.error}'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
    }

    SpreadsheetCell cell;

    // Calculate formula if needed
    if (value.startsWith('=')) {
      try {
        // Create a temporary spreadsheet for formula calculation
        final sheet = SpreadsheetSheet(name: 'Sheet1', cells: _cells);
        final spreadsheet = Spreadsheet(name: 'temp', sheets: [sheet]);
        final result = EnhancedFormulaEngine.calculateFormula(
          value,
          spreadsheet,
        );
        cell = SpreadsheetCell(
          row: _selectedRow,
          column: _selectedCol,
          rawValue: value,
          calculatedValue: result,
          formula: value,
          dataType: _getDataType(result),
        );
      } catch (e) {
        cell = SpreadsheetCell(
          row: _selectedRow,
          column: _selectedCol,
          rawValue: value,
          calculatedValue: '#ERROR!',
          formula: value,
          dataType: CellDataType.error,
        );
      }
    } else {
      final parsedValue = _parseValue(value);
      cell = SpreadsheetCell(
        row: _selectedRow,
        column: _selectedCol,
        rawValue: value,
        calculatedValue: parsedValue,
        dataType: _getDataType(parsedValue),
      );
    }

    setState(() {
      _cells[cellAddress] = cell;
      _isEditing = false;
      _editingValue = '';
      _referencedCells.clear();
    });

    widget.onCellChanged?.call(_selectedRow, _selectedCol, value);
  }

  void _cancelEdit() {
    setState(() {
      _isEditing = false;
      _editingValue = '';
    });
  }

  void _deleteCell() {
    final cellAddress = _getCellAddress(_selectedRow, _selectedCol);
    setState(() {
      _cells.remove(cellAddress);
    });
    widget.onCellChanged?.call(_selectedRow, _selectedCol, null);
  }

  void _copyCell() {
    final cellAddress = _getCellAddress(_selectedRow, _selectedCol);
    final cell = _cells[cellAddress];
    if (cell != null) {
      _clipboard = [
        [cell],
      ];
      _clipboardStartRow = _selectedRow;
      _clipboardStartCol = _selectedCol;
    }
  }

  void _cutCell() {
    _copyCell();
    _deleteCell();
  }

  void _pasteCell() {
    if (_clipboard == null || _clipboard!.isEmpty) return;

    final deltaRow = _selectedRow - _clipboardStartRow;
    final deltaCol = _selectedCol - _clipboardStartCol;

    for (int row = 0; row < _clipboard!.length; row++) {
      for (int col = 0; col < _clipboard![row].length; col++) {
        final sourceCell = _clipboard![row][col];
        if (sourceCell != null) {
          final targetRow = _clipboardStartRow + row + deltaRow;
          final targetCol = _clipboardStartCol + col + deltaCol;

          if (targetRow >= 0 &&
              targetRow < widget.rows &&
              targetCol >= 0 &&
              targetCol < widget.columns) {
            final targetAddress = _getCellAddress(targetRow, targetCol);
            final newCell = SpreadsheetCell(
              row: targetRow,
              column: targetCol,
              rawValue: sourceCell.rawValue,
              dataType: sourceCell.dataType,
              format: sourceCell.format,
            );

            setState(() {
              _cells[targetAddress] = newCell;
            });

            widget.onCellChanged?.call(
              targetRow,
              targetCol,
              sourceCell.rawValue,
            );
          }
        }
      }
    }
  }

  void _ensureCellVisible() {
    // Scroll to make selected cell visible
    const cellHeight = 25.0;
    const cellWidth = 100.0;

    final targetY = _selectedRow * cellHeight;
    final targetX = _selectedCol * cellWidth;

    _verticalController.animateTo(
      targetY,
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
    );

    _horizontalController.animateTo(
      targetX,
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
    );
  }

  int _findFirstDataRow() {
    for (int row = 0; row < widget.rows; row++) {
      for (int col = 0; col < widget.columns; col++) {
        final cellAddress = _getCellAddress(row, col);
        if (_cells.containsKey(cellAddress)) {
          return row;
        }
      }
    }
    return 0;
  }

  int _findLastDataRow() {
    for (int row = widget.rows - 1; row >= 0; row--) {
      for (int col = 0; col < widget.columns; col++) {
        final cellAddress = _getCellAddress(row, col);
        if (_cells.containsKey(cellAddress)) {
          return row;
        }
      }
    }
    return widget.rows - 1;
  }

  int _findFirstDataCol() {
    for (int col = 0; col < widget.columns; col++) {
      for (int row = 0; row < widget.rows; row++) {
        final cellAddress = _getCellAddress(row, col);
        if (_cells.containsKey(cellAddress)) {
          return col;
        }
      }
    }
    return 0;
  }

  int _findLastDataCol() {
    for (int col = widget.columns - 1; col >= 0; col--) {
      for (int row = 0; row < widget.rows; row++) {
        final cellAddress = _getCellAddress(row, col);
        if (_cells.containsKey(cellAddress)) {
          return col;
        }
      }
    }
    return widget.columns - 1;
  }

  String _getCellAddress(int row, int col) {
    return '${_getColumnName(col)}${row + 1}';
  }

  String _getColumnName(int index) {
    String result = '';
    while (index >= 0) {
      result = String.fromCharCode(65 + (index % 26)) + result;
      index = (index ~/ 26) - 1;
    }
    return result;
  }

  // Helper method to parse string values to appropriate types
  dynamic _parseValue(String value) {
    if (value.isEmpty) return '';

    // Try to parse as number
    final numValue = double.tryParse(value);
    if (numValue != null) return numValue;

    // Try to parse as boolean
    if (value.toLowerCase() == 'true') return true;
    if (value.toLowerCase() == 'false') return false;

    // Return as string
    return value;
  }

  // Helper method to determine data type
  CellDataType _getDataType(dynamic value) {
    if (value == null || value.toString().isEmpty) return CellDataType.text;
    if (value is num) return CellDataType.number;
    if (value is bool) return CellDataType.boolean;
    if (value is DateTime) return CellDataType.date;
    if (value.toString().startsWith('#') && value.toString().endsWith('!')) {
      return CellDataType.error;
    }
    return CellDataType.text;
  }
}
