import 'dart:convert';

// Core Media Models for ShadowPlayer
class MediaFile {
  final String id;
  final String path;
  final String name;
  final String displayName;
  final MediaType type;
  final int size;
  final DateTime dateModified;
  final DateTime dateAdded;
  final Duration? duration;
  final String? thumbnailPath;
  final MediaMetadata metadata;
  final bool isFavorite;
  final int playCount;
  final DateTime? lastPlayed;
  final List<String> tags;

  const MediaFile({
    required this.id,
    required this.path,
    required this.name,
    required this.displayName,
    required this.type,
    required this.size,
    required this.dateModified,
    required this.dateAdded,
    this.duration,
    this.thumbnailPath,
    required this.metadata,
    this.isFavorite = false,
    this.playCount = 0,
    this.lastPlayed,
    this.tags = const [],
  });

  MediaFile copyWith({
    String? id,
    String? path,
    String? name,
    String? displayName,
    MediaType? type,
    int? size,
    DateTime? dateModified,
    DateTime? dateAdded,
    Duration? duration,
    String? thumbnailPath,
    MediaMetadata? metadata,
    bool? isFavorite,
    int? playCount,
    DateTime? lastPlayed,
    List<String>? tags,
  }) {
    return MediaFile(
      id: id ?? this.id,
      path: path ?? this.path,
      name: name ?? this.name,
      displayName: displayName ?? this.displayName,
      type: type ?? this.type,
      size: size ?? this.size,
      dateModified: dateModified ?? this.dateModified,
      dateAdded: dateAdded ?? this.dateAdded,
      duration: duration ?? this.duration,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      metadata: metadata ?? this.metadata,
      isFavorite: isFavorite ?? this.isFavorite,
      playCount: playCount ?? this.playCount,
      lastPlayed: lastPlayed ?? this.lastPlayed,
      tags: tags ?? this.tags,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'path': path,
      'name': name,
      'displayName': displayName,
      'type': type.name,
      'size': size,
      'dateModified': dateModified.millisecondsSinceEpoch,
      'dateAdded': dateAdded.millisecondsSinceEpoch,
      'duration': duration?.inMilliseconds,
      'thumbnailPath': thumbnailPath,
      'metadata': metadata.toMap(),
      'isFavorite': isFavorite,
      'playCount': playCount,
      'lastPlayed': lastPlayed?.millisecondsSinceEpoch,
      'tags': tags,
    };
  }

  factory MediaFile.fromMap(Map<String, dynamic> map) {
    return MediaFile(
      id: map['id'] ?? '',
      path: map['path'] ?? '',
      name: map['name'] ?? '',
      displayName: map['displayName'] ?? '',
      type: MediaType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => MediaType.unknown,
      ),
      size: map['size']?.toInt() ?? 0,
      dateModified: DateTime.fromMillisecondsSinceEpoch(
        map['dateModified'] ?? 0,
      ),
      dateAdded: DateTime.fromMillisecondsSinceEpoch(map['dateAdded'] ?? 0),
      duration: map['duration'] != null
          ? Duration(milliseconds: map['duration'])
          : null,
      thumbnailPath: map['thumbnailPath'],
      metadata: MediaMetadata.fromMap(map['metadata'] ?? {}),
      isFavorite: map['isFavorite'] ?? false,
      playCount: map['playCount']?.toInt() ?? 0,
      lastPlayed: map['lastPlayed'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['lastPlayed'])
          : null,
      tags: List<String>.from(map['tags'] ?? []),
    );
  }

  String toJson() => json.encode(toMap());
  factory MediaFile.fromJson(String source) =>
      MediaFile.fromMap(json.decode(source));
}

class MediaMetadata {
  final String? title;
  final String? artist;
  final String? album;
  final String? genre;
  final int? year;
  final int? trackNumber;
  final String? albumArtist;
  final String? composer;
  final int? bitrate;
  final String? codec;
  final String? resolution;
  final double? frameRate;
  final String? aspectRatio;
  final Map<String, dynamic> customFields;

  const MediaMetadata({
    this.title,
    this.artist,
    this.album,
    this.genre,
    this.year,
    this.trackNumber,
    this.albumArtist,
    this.composer,
    this.bitrate,
    this.codec,
    this.resolution,
    this.frameRate,
    this.aspectRatio,
    this.customFields = const {},
  });

  MediaMetadata copyWith({
    String? title,
    String? artist,
    String? album,
    String? genre,
    int? year,
    int? trackNumber,
    String? albumArtist,
    String? composer,
    int? bitrate,
    String? codec,
    String? resolution,
    double? frameRate,
    String? aspectRatio,
    Map<String, dynamic>? customFields,
  }) {
    return MediaMetadata(
      title: title ?? this.title,
      artist: artist ?? this.artist,
      album: album ?? this.album,
      genre: genre ?? this.genre,
      year: year ?? this.year,
      trackNumber: trackNumber ?? this.trackNumber,
      albumArtist: albumArtist ?? this.albumArtist,
      composer: composer ?? this.composer,
      bitrate: bitrate ?? this.bitrate,
      codec: codec ?? this.codec,
      resolution: resolution ?? this.resolution,
      frameRate: frameRate ?? this.frameRate,
      aspectRatio: aspectRatio ?? this.aspectRatio,
      customFields: customFields ?? this.customFields,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'artist': artist,
      'album': album,
      'genre': genre,
      'year': year,
      'trackNumber': trackNumber,
      'albumArtist': albumArtist,
      'composer': composer,
      'bitrate': bitrate,
      'codec': codec,
      'resolution': resolution,
      'frameRate': frameRate,
      'aspectRatio': aspectRatio,
      'customFields': customFields,
    };
  }

  factory MediaMetadata.fromMap(Map<String, dynamic> map) {
    return MediaMetadata(
      title: map['title'],
      artist: map['artist'],
      album: map['album'],
      genre: map['genre'],
      year: map['year']?.toInt(),
      trackNumber: map['trackNumber']?.toInt(),
      albumArtist: map['albumArtist'],
      composer: map['composer'],
      bitrate: map['bitrate']?.toInt(),
      codec: map['codec'],
      resolution: map['resolution'],
      frameRate: map['frameRate']?.toDouble(),
      aspectRatio: map['aspectRatio'],
      customFields: Map<String, dynamic>.from(map['customFields'] ?? {}),
    );
  }

  String toJson() => json.encode(toMap());
  factory MediaMetadata.fromJson(String source) =>
      MediaMetadata.fromMap(json.decode(source));
}

class Playlist {
  final String id;
  final String name;
  final String description;
  final List<String> mediaIds;
  final DateTime createdAt;
  final DateTime lastModified;
  final String? thumbnailPath;
  final bool isSystemPlaylist;
  final PlaylistType type;

  const Playlist({
    required this.id,
    required this.name,
    this.description = '',
    required this.mediaIds,
    required this.createdAt,
    required this.lastModified,
    this.thumbnailPath,
    this.isSystemPlaylist = false,
    this.type = PlaylistType.custom,
  });

  Playlist copyWith({
    String? id,
    String? name,
    String? description,
    List<String>? mediaIds,
    DateTime? createdAt,
    DateTime? lastModified,
    String? thumbnailPath,
    bool? isSystemPlaylist,
    PlaylistType? type,
  }) {
    return Playlist(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      mediaIds: mediaIds ?? this.mediaIds,
      createdAt: createdAt ?? this.createdAt,
      lastModified: lastModified ?? this.lastModified,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      isSystemPlaylist: isSystemPlaylist ?? this.isSystemPlaylist,
      type: type ?? this.type,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'mediaIds': mediaIds,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastModified': lastModified.millisecondsSinceEpoch,
      'thumbnailPath': thumbnailPath,
      'isSystemPlaylist': isSystemPlaylist,
      'type': type.name,
    };
  }

  factory Playlist.fromMap(Map<String, dynamic> map) {
    return Playlist(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      mediaIds: List<String>.from(map['mediaIds'] ?? []),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      lastModified: DateTime.fromMillisecondsSinceEpoch(
        map['lastModified'] ?? 0,
      ),
      thumbnailPath: map['thumbnailPath'],
      isSystemPlaylist: map['isSystemPlaylist'] ?? false,
      type: PlaylistType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => PlaylistType.custom,
      ),
    );
  }

  String toJson() => json.encode(toMap());
  factory Playlist.fromJson(String source) =>
      Playlist.fromMap(json.decode(source));
}

// Enums
enum MediaType { video, audio, unknown }

enum PlaylistType {
  custom,
  favorites,
  recentlyPlayed,
  mostPlayed,
  recentlyAdded,
}

enum ViewMode { grid, list, folder }

enum SortBy { name, dateAdded, dateModified, size, duration, artist, album }

enum SortOrder { ascending, descending }

enum PlayMode { sequential, shuffle, repeatAll, repeatOne }

enum PlayerState { stopped, playing, paused, buffering, error }

enum ScreenRatio { original, fitToScreen, stretch, crop, fullWidth }

/// Additional models for ShadowPlayer service

/// Playback states
enum PlaybackState { stopped, loading, playing, paused, buffering, error }

/// Repeat modes
enum RepeatMode { none, one, all }

/// Playback event types
enum PlaybackEventType {
  initialized,
  loading,
  mediaChanged,
  playlistChanged,
  resumed,
  paused,
  stopped,
  skippedToNext,
  skippedToPrevious,
  seeked,
  volumeChanged,
  speedChanged,
  repeatModeChanged,
  shuffleToggled,
  equalizerChanged,
  effectsChanged,
  sleepTimerSet,
  sleepTimerCancelled,
  sleepTimerExpired,
  trackEnded,
  fileProcessed,
  error,
}

/// Playback quality levels
enum PlaybackQuality { low, medium, high, lossless }

/// Media item for playback service
class MediaItem {
  final String id;
  final String title;
  final String? artist;
  final String? album;
  final String? genre;
  final Duration duration;
  final String filePath;
  final String? artworkPath;
  final int? trackNumber;
  final int? year;
  final int? bitrate;
  final String? format;

  const MediaItem({
    required this.id,
    required this.title,
    this.artist,
    this.album,
    this.genre,
    required this.duration,
    required this.filePath,
    this.artworkPath,
    this.trackNumber,
    this.year,
    this.bitrate,
    this.format,
  });

  MediaItem copyWith({
    String? id,
    String? title,
    String? artist,
    String? album,
    String? genre,
    Duration? duration,
    String? filePath,
    String? artworkPath,
    int? trackNumber,
    int? year,
    int? bitrate,
    String? format,
  }) {
    return MediaItem(
      id: id ?? this.id,
      title: title ?? this.title,
      artist: artist ?? this.artist,
      album: album ?? this.album,
      genre: genre ?? this.genre,
      duration: duration ?? this.duration,
      filePath: filePath ?? this.filePath,
      artworkPath: artworkPath ?? this.artworkPath,
      trackNumber: trackNumber ?? this.trackNumber,
      year: year ?? this.year,
      bitrate: bitrate ?? this.bitrate,
      format: format ?? this.format,
    );
  }
}

/// Equalizer settings
class EqualizerSettings {
  final String presetName;
  final List<double> bands;
  final bool enabled;

  const EqualizerSettings({
    required this.presetName,
    required this.bands,
    this.enabled = true,
  });

  static EqualizerSettings flat() {
    return const EqualizerSettings(
      presetName: 'Flat',
      bands: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    );
  }

  static EqualizerSettings rock() {
    return const EqualizerSettings(
      presetName: 'Rock',
      bands: [8, 4, -5, -8, -3, 4, 8, 11, 11, 11],
    );
  }

  static EqualizerSettings pop() {
    return const EqualizerSettings(
      presetName: 'Pop',
      bands: [-1, 4, 7, 8, 5, 0, -2, -2, -1, -1],
    );
  }

  static EqualizerSettings jazz() {
    return const EqualizerSettings(
      presetName: 'Jazz',
      bands: [4, 2, 0, 2, -2, -2, 0, 2, 4, 6],
    );
  }

  static EqualizerSettings classical() {
    return const EqualizerSettings(
      presetName: 'Classical',
      bands: [5, 3, -2, 4, -2, -2, 0, 2, 3, 4],
    );
  }
}

/// Audio effects
class AudioEffects {
  final bool bassBoostEnabled;
  final double bassBoostStrength;
  final bool virtualizerEnabled;
  final double virtualizerStrength;
  final bool reverbEnabled;
  final double reverbLevel;

  const AudioEffects({
    this.bassBoostEnabled = false,
    this.bassBoostStrength = 0.5,
    this.virtualizerEnabled = false,
    this.virtualizerStrength = 0.5,
    this.reverbEnabled = false,
    this.reverbLevel = 0.5,
  });
}

/// Playback event
class PlaybackEvent {
  final PlaybackEventType type;
  final String? message;
  final DateTime timestamp;
  final double? progress;

  const PlaybackEvent({
    required this.type,
    this.message,
    required this.timestamp,
    this.progress,
  });
}

/// Playback statistics
class PlaybackStatistics {
  final Duration totalPlayTime;
  final int tracksPlayed;
  final String favoriteGenre;
  final Duration averageSessionLength;
  final String mostPlayedArtist;
  final PlaybackQuality playbackQuality;

  const PlaybackStatistics({
    required this.totalPlayTime,
    required this.tracksPlayed,
    required this.favoriteGenre,
    required this.averageSessionLength,
    required this.mostPlayedArtist,
    required this.playbackQuality,
  });
}
