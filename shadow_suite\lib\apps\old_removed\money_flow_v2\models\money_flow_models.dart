import 'dart:convert';

// Account Types
enum AccountType {
  checking,
  savings,
  credit,
  investment,
  cash;

  String get displayName {
    switch (this) {
      case AccountType.checking:
        return 'Checking';
      case AccountType.savings:
        return 'Savings';
      case AccountType.credit:
        return 'Credit Card';
      case AccountType.investment:
        return 'Investment';
      case AccountType.cash:
        return 'Cash';
    }
  }
}

// Transaction Types
enum TransactionType {
  income,
  expense,
  transfer;

  String get displayName {
    switch (this) {
      case TransactionType.income:
        return 'Income';
      case TransactionType.expense:
        return 'Expense';
      case TransactionType.transfer:
        return 'Transfer';
    }
  }
}

// Budget Period
enum BudgetPeriod {
  weekly,
  monthly,
  quarterly,
  yearly;

  String get displayName {
    switch (this) {
      case BudgetPeriod.weekly:
        return 'Weekly';
      case BudgetPeriod.monthly:
        return 'Monthly';
      case BudgetPeriod.quarterly:
        return 'Quarterly';
      case BudgetPeriod.yearly:
        return 'Yearly';
    }
  }
}

// Goal Category
enum GoalCategory {
  emergencyFund,
  vacation,
  car,
  house,
  education,
  retirement,
  other;

  String get displayName {
    switch (this) {
      case GoalCategory.emergencyFund:
        return 'Emergency Fund';
      case GoalCategory.vacation:
        return 'Vacation';
      case GoalCategory.car:
        return 'Car';
      case GoalCategory.house:
        return 'House';
      case GoalCategory.education:
        return 'Education';
      case GoalCategory.retirement:
        return 'Retirement';
      case GoalCategory.other:
        return 'Other';
    }
  }
}

// Account Model
class MoneyAccount {
  final String id;
  final String name;
  final AccountType type;
  final String bankName;
  final String accountNumber;
  final double balance;
  final String currency;
  final String color;
  final bool isActive;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;

  MoneyAccount({
    required this.id,
    required this.name,
    required this.type,
    required this.bankName,
    required this.accountNumber,
    required this.balance,
    this.currency = 'USD',
    this.color = '#2196F3',
    this.isActive = true,
    this.description,
    required this.createdAt,
    required this.updatedAt,
  });

  MoneyAccount copyWith({
    String? id,
    String? name,
    AccountType? type,
    String? bankName,
    String? accountNumber,
    double? balance,
    String? currency,
    String? color,
    bool? isActive,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MoneyAccount(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      bankName: bankName ?? this.bankName,
      accountNumber: accountNumber ?? this.accountNumber,
      balance: balance ?? this.balance,
      currency: currency ?? this.currency,
      color: color ?? this.color,
      isActive: isActive ?? this.isActive,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'bankName': bankName,
      'accountNumber': accountNumber,
      'balance': balance,
      'currency': currency,
      'color': color,
      'isActive': isActive ? 1 : 0,
      'description': description,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory MoneyAccount.fromMap(Map<String, dynamic> map) {
    return MoneyAccount(
      id: map['id'],
      name: map['name'],
      type: AccountType.values.firstWhere((e) => e.name == map['type']),
      bankName: map['bankName'],
      accountNumber: map['accountNumber'],
      balance: map['balance'].toDouble(),
      currency: map['currency'] ?? 'USD',
      color: map['color'] ?? '#2196F3',
      isActive: map['isActive'] == 1,
      description: map['description'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
    );
  }

  String toJson() => json.encode(toMap());
  factory MoneyAccount.fromJson(String source) => MoneyAccount.fromMap(json.decode(source));
}

// Transaction Model
class MoneyTransactionV2 {
  final String id;
  final String accountId;
  final String? toAccountId;
  final TransactionType type;
  final double amount;
  final String category;
  final String description;
  final String? notes;
  final DateTime date;
  final DateTime createdAt;
  final DateTime updatedAt;

  MoneyTransactionV2({
    required this.id,
    required this.accountId,
    this.toAccountId,
    required this.type,
    required this.amount,
    required this.category,
    required this.description,
    this.notes,
    required this.date,
    required this.createdAt,
    required this.updatedAt,
  });

  MoneyTransactionV2 copyWith({
    String? id,
    String? accountId,
    String? toAccountId,
    TransactionType? type,
    double? amount,
    String? category,
    String? description,
    String? notes,
    DateTime? date,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MoneyTransactionV2(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      toAccountId: toAccountId ?? this.toAccountId,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      category: category ?? this.category,
      description: description ?? this.description,
      notes: notes ?? this.notes,
      date: date ?? this.date,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'accountId': accountId,
      'toAccountId': toAccountId,
      'type': type.name,
      'amount': amount,
      'category': category,
      'description': description,
      'notes': notes,
      'date': date.millisecondsSinceEpoch,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory MoneyTransactionV2.fromMap(Map<String, dynamic> map) {
    return MoneyTransactionV2(
      id: map['id'],
      accountId: map['accountId'],
      toAccountId: map['toAccountId'],
      type: TransactionType.values.firstWhere((e) => e.name == map['type']),
      amount: map['amount'].toDouble(),
      category: map['category'],
      description: map['description'],
      notes: map['notes'],
      date: DateTime.fromMillisecondsSinceEpoch(map['date']),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
    );
  }

  String toJson() => json.encode(toMap());
  factory MoneyTransactionV2.fromJson(String source) => MoneyTransactionV2.fromMap(json.decode(source));
}

// Budget Model
class MoneyBudget {
  final String id;
  final String name;
  final String category;
  final double amount;
  final double spent;
  final BudgetPeriod period;
  final DateTime startDate;
  final DateTime endDate;
  final String color;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  MoneyBudget({
    required this.id,
    required this.name,
    required this.category,
    required this.amount,
    this.spent = 0.0,
    required this.period,
    required this.startDate,
    required this.endDate,
    this.color = '#4CAF50',
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  double get remainingAmount => amount - spent;
  double get percentageUsed => amount > 0 ? (spent / amount) * 100 : 0;
  bool get isOverBudget => spent > amount;

  MoneyBudget copyWith({
    String? id,
    String? name,
    String? category,
    double? amount,
    double? spent,
    BudgetPeriod? period,
    DateTime? startDate,
    DateTime? endDate,
    String? color,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MoneyBudget(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      amount: amount ?? this.amount,
      spent: spent ?? this.spent,
      period: period ?? this.period,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      color: color ?? this.color,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'amount': amount,
      'spent': spent,
      'period': period.name,
      'startDate': startDate.millisecondsSinceEpoch,
      'endDate': endDate.millisecondsSinceEpoch,
      'color': color,
      'isActive': isActive ? 1 : 0,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory MoneyBudget.fromMap(Map<String, dynamic> map) {
    return MoneyBudget(
      id: map['id'],
      name: map['name'],
      category: map['category'],
      amount: map['amount'].toDouble(),
      spent: map['spent'].toDouble(),
      period: BudgetPeriod.values.firstWhere((e) => e.name == map['period']),
      startDate: DateTime.fromMillisecondsSinceEpoch(map['startDate']),
      endDate: DateTime.fromMillisecondsSinceEpoch(map['endDate']),
      color: map['color'],
      isActive: map['isActive'] == 1,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
    );
  }

  String toJson() => json.encode(toMap());
  factory MoneyBudget.fromJson(String source) => MoneyBudget.fromMap(json.decode(source));
}
