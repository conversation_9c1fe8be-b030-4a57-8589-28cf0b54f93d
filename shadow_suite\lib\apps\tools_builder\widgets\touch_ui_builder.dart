import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_component.dart';
import '../services/cell_binding_service.dart';
import 'cell_binding_manager.dart';

// Touch-friendly UI Builder with gesture support
class TouchUIBuilder extends ConsumerStatefulWidget {
  final List<UIComponent> components;
  final Function(UIComponent) onComponentSelected;
  final Function(UIComponent) onComponentMoved;
  final Function(UIComponent) onComponentResized;
  final Function(UIComponent) onComponentDeleted;
  final Function(Offset) onCanvasTapped;
  final bool isEditMode;

  const TouchUIBuilder({
    super.key,
    required this.components,
    required this.onComponentSelected,
    required this.onComponentMoved,
    required this.onComponentResized,
    required this.onComponentDeleted,
    required this.onCanvasTapped,
    this.isEditMode = true,
  });

  @override
  ConsumerState<TouchUIBuilder> createState() => _TouchUIBuilderState();
}

class _TouchUIBuilderState extends ConsumerState<TouchUIBuilder> {
  String? _selectedComponentId;
  Offset? _dragStartPosition;
  Offset? _componentStartPosition;
  bool _isDragging = false;
  bool _isResizing = false;
  double _scaleFactor = 1.0;
  Offset _panOffset = Offset.zero;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (_selectedComponentId != null) {
          setState(() {
            _selectedComponentId = null;
          });
        }
      },
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.grey[100],
        child: Listener(
          onPointerSignal: (event) {
            if (event is PointerScrollEvent) {
              // Handle mouse wheel zoom
              final delta = event.scrollDelta.dy;
              setState(() {
                _scaleFactor = (_scaleFactor - delta * 0.001).clamp(0.1, 5.0);
              });
            }
          },
          child: GestureDetector(
            // Multi-touch pan and zoom support
            onScaleStart: (details) {
              _dragStartPosition = details.localFocalPoint;
            },
            onScaleUpdate: (details) {
              if (details.pointerCount == 1) {
                // Single finger pan
                if (_dragStartPosition != null) {
                  final delta = details.localFocalPoint - _dragStartPosition!;
                  setState(() {
                    _panOffset += delta;
                    _dragStartPosition = details.localFocalPoint;
                  });
                }
              } else if (details.pointerCount == 2) {
                // Two finger pinch-to-zoom
                setState(() {
                  _scaleFactor = (_scaleFactor * details.scale).clamp(0.1, 5.0);
                });
              }
            },
            onScaleEnd: (details) {
              _dragStartPosition = null;
            },
            child: Stack(
              children: [
                // Grid background
                if (widget.isEditMode) _buildGridBackground(),

                // Transform for pan and zoom
                Transform(
                  transform: Matrix4.identity()
                    ..translate(_panOffset.dx, _panOffset.dy)
                    ..scale(_scaleFactor),
                  child: Stack(
                    children: [
                      // Components
                      ...widget.components.map(
                        (component) => _buildComponent(component),
                      ),

                      // Selection overlay
                      if (_selectedComponentId != null)
                        _buildSelectionOverlay(),
                    ],
                  ),
                ),

                // Touch controls overlay
                if (widget.isEditMode) _buildTouchControls(),

                // Multi-touch gesture indicators
                if (widget.isEditMode) _buildGestureIndicators(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGridBackground() {
    return CustomPaint(
      size: Size.infinite,
      painter: GridPainter(
        gridSize: 20.0 * _scaleFactor,
        color: Colors.grey[300]!,
      ),
    );
  }

  Widget _buildComponent(UIComponent component) {
    final isSelected = component.id == _selectedComponentId;

    return Positioned(
      left: component.x,
      top: component.y,
      child: GestureDetector(
        onTap: () {
          if (widget.isEditMode) {
            setState(() {
              _selectedComponentId = component.id;
            });
            widget.onComponentSelected(component);
          }
        },
        onPanStart: widget.isEditMode
            ? (details) {
                _dragStartPosition = details.localPosition;
                _componentStartPosition = Offset(component.x, component.y);
                _isDragging = true;
              }
            : null,
        onPanUpdate: widget.isEditMode
            ? (details) {
                if (_isDragging &&
                    _dragStartPosition != null &&
                    _componentStartPosition != null) {
                  final delta = details.localPosition - _dragStartPosition!;
                  final newPosition = _componentStartPosition! + delta;

                  final updatedComponent = component.copyWith(
                    x: newPosition.dx,
                    y: newPosition.dy,
                  );

                  widget.onComponentMoved(updatedComponent);
                }
              }
            : null,
        onPanEnd: widget.isEditMode
            ? (details) {
                _isDragging = false;
                _dragStartPosition = null;
                _componentStartPosition = null;
              }
            : null,
        onLongPress: widget.isEditMode
            ? () {
                _showComponentContextMenu(component);
              }
            : null,
        child: Container(
          decoration: BoxDecoration(
            border: isSelected
                ? Border.all(color: Colors.blue, width: 2)
                : null,
            borderRadius: BorderRadius.circular(4),
          ),
          child: _buildComponentWidget(component),
        ),
      ),
    );
  }

  Widget _buildComponentWidget(UIComponent component) {
    final bindingService = ref.watch(cellBindingServiceProvider);
    final bindings = bindingService.getComponentBindings(component.id);
    final isolatedState = bindingService.getIsolatedState(component.id);

    switch (component.type) {
      case ComponentType.textInput:
        return _buildTextInput(component, bindings, isolatedState);
      case ComponentType.numberInput:
        return _buildNumberInput(component, bindings, isolatedState);
      case ComponentType.button:
        return _buildButton(component, bindings, isolatedState);
      case ComponentType.label:
        return _buildLabel(component, bindings, isolatedState);
      case ComponentType.checkbox:
        return _buildCheckbox(component, bindings, isolatedState);
      case ComponentType.slider:
        return _buildSlider(component, bindings, isolatedState);
      default:
        return _buildLabel(component, bindings, isolatedState);
    }
  }

  Widget _buildTextInput(
    UIComponent component,
    List<CellBinding> bindings,
    Map<String, dynamic> isolatedState,
  ) {
    final value = isolatedState['value'] ?? component.properties['value'] ?? '';

    return SizedBox(
      width: component.style.width ?? 200,
      height: component.style.height ?? 40,
      child: TextField(
        controller: TextEditingController(text: value.toString()),
        decoration: InputDecoration(
          labelText: component.label,
          hintText: component.placeholder,
          border: const OutlineInputBorder(),
        ),
        onChanged: (newValue) {
          final bindingService = ref.read(cellBindingServiceProvider);
          bindingService.updateComponentValue(component.id, 'value', newValue);
        },
      ),
    );
  }

  Widget _buildNumberInput(
    UIComponent component,
    List<CellBinding> bindings,
    Map<String, dynamic> isolatedState,
  ) {
    final value = isolatedState['value'] ?? component.properties['value'] ?? 0;

    return SizedBox(
      width: component.style.width ?? 200,
      height: component.style.height ?? 40,
      child: TextField(
        controller: TextEditingController(text: value.toString()),
        decoration: InputDecoration(
          labelText: component.label,
          border: const OutlineInputBorder(),
        ),
        keyboardType: TextInputType.number,
        onChanged: (newValue) {
          final numValue = double.tryParse(newValue) ?? 0;
          final bindingService = ref.read(cellBindingServiceProvider);
          bindingService.updateComponentValue(component.id, 'value', numValue);
        },
      ),
    );
  }

  Widget _buildButton(
    UIComponent component,
    List<CellBinding> bindings,
    Map<String, dynamic> isolatedState,
  ) {
    final enabled = isolatedState['enabled'] ?? component.isEnabled;

    return SizedBox(
      width: component.style.width ?? 120,
      height: component.style.height ?? 40,
      child: ElevatedButton(
        onPressed: enabled
            ? () {
                final bindingService = ref.read(cellBindingServiceProvider);
                bindingService.updateComponentValue(
                  component.id,
                  'clicked',
                  true,
                );
              }
            : null,
        child: Text(component.label),
      ),
    );
  }

  Widget _buildLabel(
    UIComponent component,
    List<CellBinding> bindings,
    Map<String, dynamic> isolatedState,
  ) {
    final text = isolatedState['text'] ?? component.label;

    return Container(
      width: component.style.width ?? 100,
      height: component.style.height ?? 30,
      alignment: Alignment.centerLeft,
      child: Text(
        text.toString(),
        style: TextStyle(
          fontSize: component.style.fontSize ?? 14,
          fontWeight: component.style.isBold
              ? FontWeight.bold
              : FontWeight.normal,
          fontStyle: component.style.isItalic
              ? FontStyle.italic
              : FontStyle.normal,
          color: component.style.textColor != null
              ? Color(
                  int.parse(
                    component.style.textColor!.replaceFirst('#', '0xFF'),
                  ),
                )
              : null,
        ),
      ),
    );
  }

  Widget _buildCheckbox(
    UIComponent component,
    List<CellBinding> bindings,
    Map<String, dynamic> isolatedState,
  ) {
    final value =
        isolatedState['value'] ?? component.properties['value'] ?? false;

    return SizedBox(
      width: component.style.width ?? 200,
      height: component.style.height ?? 40,
      child: CheckboxListTile(
        title: Text(component.label),
        value: value as bool,
        onChanged: (newValue) {
          final bindingService = ref.read(cellBindingServiceProvider);
          bindingService.updateComponentValue(
            component.id,
            'value',
            newValue ?? false,
          );
        },
      ),
    );
  }

  Widget _buildSlider(
    UIComponent component,
    List<CellBinding> bindings,
    Map<String, dynamic> isolatedState,
  ) {
    final value =
        (isolatedState['value'] ?? component.properties['value'] ?? 0.0)
            as double;
    final min = (component.properties['min'] ?? 0.0) as double;
    final max = (component.properties['max'] ?? 100.0) as double;

    return SizedBox(
      width: component.style.width ?? 200,
      height: component.style.height ?? 60,
      child: Column(
        children: [
          Text(component.label),
          Slider(
            value: value.clamp(min, max),
            min: min,
            max: max,
            onChanged: (newValue) {
              final bindingService = ref.read(cellBindingServiceProvider);
              bindingService.updateComponentValue(
                component.id,
                'value',
                newValue,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSelectionOverlay() {
    final selectedComponent = widget.components.firstWhere(
      (c) => c.id == _selectedComponentId,
      orElse: () => widget.components.first,
    );

    return Positioned(
      left: selectedComponent.x - 4,
      top: selectedComponent.y - 4,
      child: Container(
        width: (selectedComponent.style.width ?? 100) + 8,
        height: (selectedComponent.style.height ?? 40) + 8,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.blue, width: 2),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Stack(
          children: [
            // Resize handles
            ..._buildResizeHandles(selectedComponent),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildResizeHandles(UIComponent component) {
    const handleSize = 8.0;

    return [
      // Top-left
      Positioned(
        left: -handleSize / 2,
        top: -handleSize / 2,
        child: _buildResizeHandle('tl'),
      ),
      // Top-right
      Positioned(
        right: -handleSize / 2,
        top: -handleSize / 2,
        child: _buildResizeHandle('tr'),
      ),
      // Bottom-left
      Positioned(
        left: -handleSize / 2,
        bottom: -handleSize / 2,
        child: _buildResizeHandle('bl'),
      ),
      // Bottom-right
      Positioned(
        right: -handleSize / 2,
        bottom: -handleSize / 2,
        child: _buildResizeHandle('br'),
      ),
    ];
  }

  Widget _buildResizeHandle(String handle) {
    return GestureDetector(
      onPanStart: (details) {
        _isResizing = true;
      },
      onPanUpdate: (details) {
        if (_isResizing && _selectedComponentId != null) {
          // Implement resize logic here
          // This would update the component's width/height based on the handle being dragged
        }
      },
      onPanEnd: (details) {
        _isResizing = false;
      },
      child: Container(
        width: 8,
        height: 8,
        decoration: const BoxDecoration(
          color: Colors.blue,
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  Widget _buildTouchControls() {
    return Positioned(
      top: 16,
      right: 16,
      child: Column(
        children: [
          // Zoom controls
          FloatingActionButton.small(
            heroTag: 'zoom_in',
            onPressed: () {
              setState(() {
                _scaleFactor = (_scaleFactor * 1.2).clamp(0.5, 3.0);
              });
            },
            child: const Icon(Icons.zoom_in),
          ),
          const SizedBox(height: 8),
          FloatingActionButton.small(
            heroTag: 'zoom_out',
            onPressed: () {
              setState(() {
                _scaleFactor = (_scaleFactor / 1.2).clamp(0.5, 3.0);
              });
            },
            child: const Icon(Icons.zoom_out),
          ),
          const SizedBox(height: 8),
          FloatingActionButton.small(
            heroTag: 'reset_view',
            onPressed: () {
              setState(() {
                _scaleFactor = 1.0;
                _panOffset = Offset.zero;
              });
            },
            child: const Icon(Icons.center_focus_strong),
          ),
        ],
      ),
    );
  }

  void _showComponentContextMenu(UIComponent component) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Edit Properties'),
              onTap: () {
                Navigator.pop(context);
                // Open properties panel
              },
            ),
            ListTile(
              leading: const Icon(Icons.link),
              title: const Text('Manage Bindings'),
              onTap: () {
                Navigator.pop(context);
                _showBindingManager(component);
              },
            ),
            ListTile(
              leading: const Icon(Icons.copy),
              title: const Text('Duplicate'),
              onTap: () {
                Navigator.pop(context);
                // Duplicate component
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete),
              title: const Text('Delete'),
              onTap: () {
                Navigator.pop(context);
                widget.onComponentDeleted(component);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGestureIndicators() {
    return Positioned(
      bottom: 16,
      left: 16,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.black87,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Touch Gestures:',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '• Single tap: Select component',
              style: TextStyle(color: Colors.white70, fontSize: 10),
            ),
            Text(
              '• Long press: Context menu',
              style: TextStyle(color: Colors.white70, fontSize: 10),
            ),
            Text(
              '• Drag: Move component',
              style: TextStyle(color: Colors.white70, fontSize: 10),
            ),
            Text(
              '• Two fingers: Pan canvas',
              style: TextStyle(color: Colors.white70, fontSize: 10),
            ),
            Text(
              '• Pinch: Zoom in/out',
              style: TextStyle(color: Colors.white70, fontSize: 10),
            ),
            Text(
              'Scale: ${_scaleFactor.toStringAsFixed(1)}x',
              style: TextStyle(color: Colors.blue[300], fontSize: 10),
            ),
          ],
        ),
      ),
    );
  }

  void _showBindingManager(UIComponent component) {
    showDialog(
      context: context,
      builder: (context) => CellBindingManager(
        component: component,
        onComponentUpdated: widget.onComponentMoved,
      ),
    );
  }
}

// Grid painter for background
class GridPainter extends CustomPainter {
  final double gridSize;
  final Color color;

  GridPainter({required this.gridSize, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 0.5;

    // Draw vertical lines
    for (double x = 0; x < size.width; x += gridSize) {
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }

    // Draw horizontal lines
    for (double y = 0; y < size.height; y += gridSize) {
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
