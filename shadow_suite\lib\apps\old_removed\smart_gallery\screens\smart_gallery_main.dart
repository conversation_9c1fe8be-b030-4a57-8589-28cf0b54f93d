import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/smart_gallery_service.dart';
import '../models/smart_gallery_models.dart';
import '../../../core/widgets/unified_components.dart';

import '../../../core/services/storage_scanner_service.dart';
import 'media_viewer_screen.dart';
import 'filter_dialog.dart';

/// Main Smart Gallery screen with AI-powered photo/video management
class SmartGalleryMain extends ConsumerStatefulWidget {
  const SmartGalleryMain({super.key});

  @override
  ConsumerState<SmartGalleryMain> createState() => _SmartGalleryMainState();
}

class _SmartGalleryMainState extends ConsumerState<SmartGalleryMain>
    with TickerProviderStateMixin {
  late TabController _tabController;
  SmartGalleryFilter _currentFilter = const SmartGalleryFilter();
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeService();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _initializeService() async {
    await SmartGalleryService.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'SmartGallery+',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        backgroundColor: const Color(0xFF8E44AD),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: _showSearchDialog,
            tooltip: 'Search Media',
          ),
          IconButton(
            icon: const Icon(Icons.tune, color: Colors.white),
            onPressed: _showFilterDialog,
            tooltip: 'Filter & Sort',
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _refreshGallery,
            tooltip: 'Refresh Gallery',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.photo_library), text: 'All Media'),
            Tab(icon: Icon(Icons.favorite), text: 'Favorites'),
            Tab(icon: Icon(Icons.face), text: 'People'),
            Tab(icon: Icon(Icons.analytics), text: 'AI Insights'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllMediaTab(),
          _buildFavoritesTab(),
          _buildPeopleTab(),
          _buildAIInsightsTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _scanForNewMedia,
        backgroundColor: const Color(0xFF8E44AD),
        tooltip: 'Scan for New Media',
        child: const Icon(Icons.add_a_photo, color: Colors.white),
      ),
    );
  }

  Widget _buildAllMediaTab() {
    return Column(
      children: [
        _buildStatsHeader(),
        Expanded(
          child: Consumer(
            builder: (context, ref, child) {
              final itemsAsync = ref.watch(
                smartGalleryItemsProvider(_currentFilter),
              );

              return itemsAsync.when(
                data: (items) => _buildMediaGrid(items),
                loading: () => const Center(
                  child: UnifiedLoadingIndicator(
                    message: 'Loading media files...',
                  ),
                ),
                error: (error, stack) => _buildErrorState(error),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStatsHeader() {
    return Consumer(
      builder: (context, ref, child) {
        final statsAsync = ref.watch(smartGalleryStatsProvider);

        return statsAsync.when(
          data: (stats) => UnifiedCard(
            margin: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Total Items',
                    stats.totalItems.toString(),
                    Icons.photo_library,
                    const Color(0xFF3498DB),
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Images',
                    stats.totalImages.toString(),
                    Icons.image,
                    const Color(0xFF27AE60),
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Videos',
                    stats.totalVideos.toString(),
                    Icons.videocam,
                    const Color(0xFFE74C3C),
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Favorites',
                    stats.favoritesCount.toString(),
                    Icons.favorite,
                    const Color(0xFFF39C12),
                  ),
                ),
              ],
            ),
          ),
          loading: () => const SizedBox(height: 80),
          error: (error, stack) => const SizedBox(height: 80),
        );
      },
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Colors.grey),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildMediaGrid(List<SmartGalleryItem> items) {
    if (items.isEmpty) {
      return _buildEmptyState();
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1.0,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        return _buildMediaTile(items[index]);
      },
    );
  }

  Widget _buildMediaTile(SmartGalleryItem item) {
    return GestureDetector(
      onTap: () => _openMediaViewer(item),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[300] ?? Colors.grey),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            fit: StackFit.expand,
            children: [
              // Thumbnail or placeholder
              Container(
                color: Colors.grey[200] ?? Colors.grey.shade200,
                child: Icon(
                  item.type == MediaType.video ? Icons.videocam : Icons.image,
                  size: 48,
                  color: Colors.grey[400] ?? Colors.grey.shade400,
                ),
              ),

              // Overlay indicators
              Positioned(
                top: 4,
                right: 4,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (item.isFavorite)
                      Container(
                        padding: const EdgeInsets.all(2),
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.favorite,
                          size: 12,
                          color: Colors.white,
                        ),
                      ),
                    if (item.isLocked)
                      Container(
                        padding: const EdgeInsets.all(2),
                        decoration: const BoxDecoration(
                          color: Colors.orange,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.lock,
                          size: 12,
                          color: Colors.white,
                        ),
                      ),
                  ],
                ),
              ),

              // AI tags indicator
              if (item.aiTags.isNotEmpty)
                Positioned(
                  bottom: 4,
                  left: 4,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 4,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Icon(
                      Icons.auto_awesome,
                      size: 12,
                      color: Colors.white,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.photo_library_outlined, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No media files found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tap the + button to scan for media files',
            style: TextStyle(color: Colors.grey[500]),
          ),
          const SizedBox(height: 24),
          UnifiedButton(
            text: 'Scan for Media',
            icon: Icons.refresh,
            onPressed: _scanForNewMedia,
            color: const Color(0xFF8E44AD),
          ),
        ],
      ),
    );
  }

  Widget _buildFavoritesTab() {
    final favoritesFilter = _currentFilter.copyWith(
      // Add favorites filter when implemented
    );

    return Consumer(
      builder: (context, ref, child) {
        final itemsAsync = ref.watch(
          smartGalleryItemsProvider(favoritesFilter),
        );

        return itemsAsync.when(
          data: (items) {
            final favorites = items.where((item) => item.isFavorite).toList();
            return _buildMediaGrid(favorites);
          },
          loading: () => const Center(child: UnifiedLoadingIndicator()),
          error: (error, stack) => _buildErrorState(error),
        );
      },
    );
  }

  Widget _buildPeopleTab() {
    return Consumer(
      builder: (context, ref, child) {
        final mediaItems = ref.watch(smartGalleryItemsProvider(null));

        return mediaItems.when(
          data: (items) {
            // Filter items that have face detection data
            final peopleItems = items
                .where(
                  (item) => item.aiTags.any(
                    (tag) =>
                        tag.toLowerCase().contains('person') ||
                        tag.toLowerCase().contains('face') ||
                        tag.toLowerCase().contains('people'),
                  ),
                )
                .toList();

            if (peopleItems.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.face, size: 64, color: Colors.grey),
                    const SizedBox(height: 16),
                    const Text(
                      'No People Detected',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'AI face detection will identify people in your photos',
                      style: TextStyle(color: Colors.grey),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: () {
                        // Trigger AI analysis
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'AI analysis started - check back soon!',
                            ),
                            backgroundColor: Colors.blue,
                          ),
                        );
                      },
                      icon: const Icon(Icons.psychology),
                      label: const Text('Start AI Analysis'),
                    ),
                  ],
                ),
              );
            }

            return GridView.builder(
              padding: const EdgeInsets.all(16),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 0.8,
              ),
              itemCount: peopleItems.length,
              itemBuilder: (context, index) {
                final item = peopleItems[index];
                return _buildPeopleCard(item);
              },
            );
          },
          loading: () => const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Analyzing photos for people...'),
              ],
            ),
          ),
          error: (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text('Error: $error'),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPeopleCard(SmartGalleryItem item) {
    return Card(
      elevation: 4,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            child: ClipRRect(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(8),
              ),
              child: Image.network(
                item.path,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey[300],
                    child: const Icon(
                      Icons.person,
                      size: 48,
                      color: Colors.grey,
                    ),
                  );
                },
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.name,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Wrap(
                  spacing: 4,
                  children: item.aiTags
                      .where(
                        (tag) =>
                            tag.toLowerCase().contains('person') ||
                            tag.toLowerCase().contains('face') ||
                            tag.toLowerCase().contains('people'),
                      )
                      .take(2)
                      .map(
                        (tag) => Chip(
                          label: Text(
                            tag,
                            style: const TextStyle(fontSize: 10),
                          ),
                          backgroundColor: Colors.blue[100],
                        ),
                      )
                      .toList(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAIInsightsTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.analytics, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'AI Insights',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'Smart categorization and analysis\ncoming soon!',
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          const Text(
            'Error loading media',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            textAlign: TextAlign.center,
            style: const TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 24),
          UnifiedButton(
            text: 'Retry',
            icon: Icons.refresh,
            onPressed: _refreshGallery,
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Media'),
        content: TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: 'Search by name, tags, or text...',
            prefixIcon: Icon(Icons.search),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          UnifiedButton(
            text: 'Search',
            onPressed: () {
              setState(() {
                _currentFilter = _currentFilter.copyWith(
                  textQuery: _searchController.text,
                );
              });
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() async {
    final result = await showDialog<SmartGalleryFilter>(
      context: context,
      builder: (context) => FilterDialog(initialFilter: _currentFilter),
    );

    if (result != null) {
      setState(() {
        _currentFilter = result;
      });
      _refreshGallery();
    }
  }

  void _refreshGallery() {
    ref.invalidate(smartGalleryItemsProvider);
    ref.invalidate(smartGalleryStatsProvider);
  }

  void _scanForNewMedia() async {
    try {
      // Use the new storage scanner for comprehensive media discovery
      final result = await StorageScannerService.performFullScan(
        includeImages: true,
        includeVideos: true,
        includeAudio: false, // SmartGallery+ focuses on images and videos
        includeDocuments: false,
        includeOthers: false,
      );

      // Process the scan results through SmartGallery service
      await SmartGalleryService.processScanResults(result);

      _refreshGallery();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Scan completed! Found ${result.images.length} images and ${result.videos.length} videos.',
            ),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Scan failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _openMediaViewer(SmartGalleryItem item) {
    final itemsAsync = ref.read(smartGalleryItemsProvider(_currentFilter));

    itemsAsync.whenData((items) {
      final currentIndex = items.indexWhere((i) => i.id == item.id);
      if (currentIndex != -1) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => MediaViewerScreen(
              item: item,
              allItems: items,
              currentIndex: currentIndex,
            ),
          ),
        );
      }
    });
  }
}

// Extension to add copyWith method to SmartGalleryFilter
extension SmartGalleryFilterExtension on SmartGalleryFilter {
  SmartGalleryFilter copyWith({
    MediaType? mediaType,
    DateRange? dateRange,
    SizeRange? sizeRange,
    List<String>? tags,
    List<String>? people,
    LocationData? location,
    double? locationRadius,
    bool? hasFaces,
    bool? hasLocation,
    bool? hasOcrText,
    String? textQuery,
    SortBy? sortBy,
    SortOrder? sortOrder,
  }) {
    return SmartGalleryFilter(
      mediaType: mediaType ?? this.mediaType,
      dateRange: dateRange ?? this.dateRange,
      sizeRange: sizeRange ?? this.sizeRange,
      tags: tags ?? this.tags,
      people: people ?? this.people,
      location: location ?? this.location,
      locationRadius: locationRadius ?? this.locationRadius,
      hasFaces: hasFaces ?? this.hasFaces,
      hasLocation: hasLocation ?? this.hasLocation,
      hasOcrText: hasOcrText ?? this.hasOcrText,
      textQuery: textQuery ?? this.textQuery,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }
}
