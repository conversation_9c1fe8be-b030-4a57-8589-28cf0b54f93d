import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'screens/voice_recorder_dashboard.dart';
import 'screens/recordings_list_screen.dart';
import 'screens/recording_screen.dart';
import 'screens/voice_settings_screen.dart';

/// Main Voice Recorder Application
class VoiceRecorderMain extends ConsumerStatefulWidget {
  const VoiceRecorderMain({super.key});

  @override
  ConsumerState<VoiceRecorderMain> createState() => _VoiceRecorderMainState();
}

class _VoiceRecorderMainState extends ConsumerState<VoiceRecorderMain> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const VoiceRecorderDashboard(),
    const RecordingScreen(),
    const RecordingsListScreen(),
    const VoiceSettingsScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        selectedItemColor: Colors.blueGrey,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.mic),
            label: 'Record',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.library_music),
            label: 'Recordings',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'Settings',
          ),
        ],
      ),
    );
  }
}
