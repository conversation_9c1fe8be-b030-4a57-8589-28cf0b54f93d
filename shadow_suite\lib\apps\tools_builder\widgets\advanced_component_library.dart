import 'package:flutter/material.dart';
import '../models/ui_component.dart';

// Advanced UI Component Library with 20+ component types
class AdvancedComponentLibrary {
  static List<ComponentType> getAllComponentTypes() {
    return [
      // Basic Components
      ComponentType.textInput,
      ComponentType.button,
      ComponentType.label,
      ComponentType.checkbox,
      ComponentType.radioButton,
      ComponentType.dropdown,
      ComponentType.slider,
      ComponentType.progressBar,
      ComponentType.image,
      ComponentType.divider,

      // Advanced Components
      ComponentType.dataTable,
      ComponentType.chart,
      ComponentType.calendar,
      ComponentType.timeInput,
      ComponentType.colorPicker,
      ComponentType.fileUpload,
      ComponentType.richTextEditor,
      ComponentType.codeEditor,
      ComponentType.map,
      ComponentType.video,
      ComponentType.audio,
      ComponentType.qrCode,
      ComponentType.barcode,
      ComponentType.signature,
      ComponentType.drawing,
    ];
  }

  static Widget buildComponentPreview(ComponentType type) {
    switch (type) {
      case ComponentType.textInput:
        return const TextField(
          decoration: InputDecoration(
            labelText: 'Text Input',
            border: OutlineInputBorder(),
          ),
        );
      
      case ComponentType.button:
        return ElevatedButton(
          onPressed: () {},
          child: const Text('Button'),
        );
      
      case ComponentType.label:
        return const Text(
          'Label Text',
          style: TextStyle(fontSize: 16),
        );
      
      case ComponentType.checkbox:
        return Checkbox(
          value: true,
          onChanged: (value) {},
        );
      
      case ComponentType.radioButton:
        return Radio<int>(
          value: 1,
          groupValue: 1,
          onChanged: (value) {},
        );
      
      case ComponentType.dropdown:
        return DropdownButton<String>(
          value: 'Option 1',
          items: ['Option 1', 'Option 2', 'Option 3']
              .map((e) => DropdownMenuItem(value: e, child: Text(e)))
              .toList(),
          onChanged: (value) {},
        );
      
      case ComponentType.slider:
        return Slider(
          value: 0.5,
          onChanged: (value) {},
        );
      
      case ComponentType.progressBar:
        return const LinearProgressIndicator(value: 0.7);
      
      case ComponentType.image:
        return Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            border: Border.all(color: Colors.grey),
          ),
          child: const Icon(Icons.image, size: 50, color: Colors.grey),
        );
      
      case ComponentType.divider:
        return const Divider(thickness: 2);
      
      case ComponentType.dataTable:
        return DataTable(
          columns: const [
            DataColumn(label: Text('Name')),
            DataColumn(label: Text('Value')),
          ],
          rows: const [
            DataRow(cells: [
              DataCell(Text('Item 1')),
              DataCell(Text('100')),
            ]),
            DataRow(cells: [
              DataCell(Text('Item 2')),
              DataCell(Text('200')),
            ]),
          ],
        );
      
      case ComponentType.chart:
        return Container(
          width: 200,
          height: 150,
          decoration: BoxDecoration(
            color: Colors.blue[50],
            border: Border.all(color: Colors.blue),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.bar_chart, size: 40, color: Colors.blue),
                Text('Chart', style: TextStyle(color: Colors.blue)),
              ],
            ),
          ),
        );
      
      case ComponentType.calendar:
        return Container(
          width: 200,
          height: 150,
          decoration: BoxDecoration(
            color: Colors.green[50],
            border: Border.all(color: Colors.green),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.calendar_today, size: 40, color: Colors.green),
                Text('Calendar', style: TextStyle(color: Colors.green)),
              ],
            ),
          ),
        );
      
      case ComponentType.timeInput:
        return Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.orange),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.access_time, color: Colors.orange),
              SizedBox(width: 8),
              Text('12:00 PM', style: TextStyle(color: Colors.orange)),
            ],
          ),
        );
      
      case ComponentType.colorPicker:
        return Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: Colors.red,
            border: Border.all(color: Colors.black),
            borderRadius: BorderRadius.circular(25),
          ),
          child: const Icon(Icons.palette, color: Colors.white),
        );
      
      case ComponentType.fileUpload:
        return Container(
          width: 150,
          height: 100,
          decoration: BoxDecoration(
            color: Colors.purple[50],
            border: Border.all(color: Colors.purple, style: BorderStyle.solid),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.cloud_upload, size: 30, color: Colors.purple),
                Text('Upload File', style: TextStyle(color: Colors.purple, fontSize: 12)),
              ],
            ),
          ),
        );
      
      case ComponentType.richTextEditor:
        return Container(
          width: 200,
          height: 100,
          decoration: BoxDecoration(
            color: Colors.amber[50],
            border: Border.all(color: Colors.amber),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Padding(
            padding: EdgeInsets.all(8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.format_bold, size: 16),
                    Icon(Icons.format_italic, size: 16),
                    Icon(Icons.format_underlined, size: 16),
                  ],
                ),
                Divider(height: 8),
                Text('Rich Text Editor', style: TextStyle(fontSize: 12)),
              ],
            ),
          ),
        );
      
      case ComponentType.codeEditor:
        return Container(
          width: 200,
          height: 100,
          decoration: BoxDecoration(
            color: Colors.grey[900],
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Padding(
            padding: EdgeInsets.all(8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('function() {', style: TextStyle(color: Colors.green, fontSize: 12, fontFamily: 'monospace')),
                Text('  return true;', style: TextStyle(color: Colors.white, fontSize: 12, fontFamily: 'monospace')),
                Text('}', style: TextStyle(color: Colors.green, fontSize: 12, fontFamily: 'monospace')),
              ],
            ),
          ),
        );
      
      case ComponentType.map:
        return Container(
          width: 200,
          height: 150,
          decoration: BoxDecoration(
            color: Colors.teal[50],
            border: Border.all(color: Colors.teal),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.map, size: 40, color: Colors.teal),
                Text('Map', style: TextStyle(color: Colors.teal)),
              ],
            ),
          ),
        );
      
      case ComponentType.video:
        return Container(
          width: 200,
          height: 120,
          decoration: BoxDecoration(
            color: Colors.black,
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Icon(Icons.play_circle_fill, size: 50, color: Colors.white),
          ),
        );
      
      case ComponentType.audio:
        return Container(
          width: 200,
          height: 60,
          decoration: BoxDecoration(
            color: Colors.indigo[50],
            border: Border.all(color: Colors.indigo),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.play_arrow, color: Colors.indigo),
              SizedBox(width: 8),
              Expanded(child: LinearProgressIndicator(value: 0.3)),
              SizedBox(width: 8),
              Text('2:30', style: TextStyle(color: Colors.indigo, fontSize: 12)),
            ],
          ),
        );
      
      case ComponentType.qrCode:
        return Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.black),
          ),
          child: const Center(
            child: Icon(Icons.qr_code, size: 60),
          ),
        );
      
      case ComponentType.barcode:
        return Container(
          width: 150,
          height: 60,
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.black),
          ),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.view_week, size: 30),
                  ],
                ),
                Text('123456789', style: TextStyle(fontSize: 10)),
              ],
            ),
          ),
        );
      
      case ComponentType.signature:
        return Container(
          width: 200,
          height: 80,
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Text(
              'Signature Pad',
              style: TextStyle(color: Colors.grey, fontStyle: FontStyle.italic),
            ),
          ),
        );
      
      case ComponentType.drawing:
        return Container(
          width: 200,
          height: 150,
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.brush, size: 40, color: Colors.grey),
                Text('Drawing Canvas', style: TextStyle(color: Colors.grey)),
              ],
            ),
          ),
        );
      
      default:
        return Container(
          width: 100,
          height: 50,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            border: Border.all(color: Colors.grey),
          ),
          child: Center(
            child: Text(type.toString().split('.').last),
          ),
        );
    }
  }

  static String getComponentName(ComponentType type) {
    switch (type) {
      case ComponentType.textInput:
        return 'Text Input';
      case ComponentType.button:
        return 'Button';
      case ComponentType.label:
        return 'Label';
      case ComponentType.checkbox:
        return 'Checkbox';
      case ComponentType.radioButton:
        return 'Radio Button';
      case ComponentType.dropdown:
        return 'Dropdown';
      case ComponentType.slider:
        return 'Slider';
      case ComponentType.progressBar:
        return 'Progress Bar';
      case ComponentType.image:
        return 'Image';
      case ComponentType.divider:
        return 'Divider';
      case ComponentType.dataTable:
        return 'Data Table';
      case ComponentType.chart:
        return 'Chart';
      case ComponentType.calendar:
        return 'Calendar';
      case ComponentType.timeInput:
        return 'Time Input';
      case ComponentType.colorPicker:
        return 'Color Picker';
      case ComponentType.fileUpload:
        return 'File Upload';
      case ComponentType.richTextEditor:
        return 'Rich Text Editor';
      case ComponentType.codeEditor:
        return 'Code Editor';
      case ComponentType.map:
        return 'Map';
      case ComponentType.video:
        return 'Video Player';
      case ComponentType.audio:
        return 'Audio Player';
      case ComponentType.qrCode:
        return 'QR Code';
      case ComponentType.barcode:
        return 'Barcode';
      case ComponentType.signature:
        return 'Signature Pad';
      case ComponentType.drawing:
        return 'Drawing Canvas';
      default:
        return type.toString().split('.').last;
    }
  }

  static IconData getComponentIcon(ComponentType type) {
    switch (type) {
      case ComponentType.textInput:
        return Icons.text_fields;
      case ComponentType.button:
        return Icons.smart_button;
      case ComponentType.label:
        return Icons.label;
      case ComponentType.checkbox:
        return Icons.check_box;
      case ComponentType.radioButton:
        return Icons.radio_button_checked;
      case ComponentType.dropdown:
        return Icons.arrow_drop_down_circle;
      case ComponentType.slider:
        return Icons.tune;
      case ComponentType.progressBar:
        return Icons.linear_scale;
      case ComponentType.image:
        return Icons.image;
      case ComponentType.divider:
        return Icons.horizontal_rule;
      case ComponentType.dataTable:
        return Icons.table_chart;
      case ComponentType.chart:
        return Icons.bar_chart;
      case ComponentType.calendar:
        return Icons.calendar_today;
      case ComponentType.timeInput:
        return Icons.access_time;
      case ComponentType.colorPicker:
        return Icons.palette;
      case ComponentType.fileUpload:
        return Icons.cloud_upload;
      case ComponentType.richTextEditor:
        return Icons.text_format;
      case ComponentType.codeEditor:
        return Icons.code;
      case ComponentType.map:
        return Icons.map;
      case ComponentType.video:
        return Icons.video_library;
      case ComponentType.audio:
        return Icons.audio_file;
      case ComponentType.qrCode:
        return Icons.qr_code;
      case ComponentType.barcode:
        return Icons.view_week;
      case ComponentType.signature:
        return Icons.draw;
      case ComponentType.drawing:
        return Icons.brush;
      default:
        return Icons.widgets;
    }
  }
}
