import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// Imports for existing Shadow Suite apps only
import '../../apps/tools_builder/standalone_main.dart';
import '../../apps/unified_finance/unified_finance_main.dart';
import '../../apps/file_manager/file_manager_main.dart';
import '../../apps/islamic_app/islamic_app_main.dart';
import '../../apps/notes_app/notes_app_main.dart';
import '../../apps/athkar_app/athkar_app_main.dart';
import '../../apps/hadith_app/hadith_app_main.dart';
import '../../apps/todo_app/todo_app_main.dart';
import '../../apps/voice_recorder/voice_recorder_main.dart';

/// Main launcher widget for Shadow Suite applications
class ShadowSuiteLauncher extends ConsumerStatefulWidget {
  const ShadowSuiteLauncher({super.key});

  @override
  ConsumerState<ShadowSuiteLauncher> createState() =>
      _ShadowSuiteLauncherState();
}

class _ShadowSuiteLauncherState extends ConsumerState<ShadowSuiteLauncher> {
  int _selectedIndex = 0;

  final List<LauncherApp> _apps = [
    LauncherApp(
      number: 1,
      name: 'Tools Builder',
      description: 'Create custom tools and spreadsheets',
      icon: Icons.build,
      color: Colors.orange,
      builder: (context) => const ToolsBuilderStandaloneHome(),
    ),
    LauncherApp(
      number: 2,
      name: 'Unified Finance',
      description: 'Complete financial management suite',
      icon: Icons.account_balance_wallet,
      color: Colors.blue,
      builder: (context) => const UnifiedFinanceMain(),
    ),
    LauncherApp(
      number: 3,
      name: 'File Manager',
      description: 'Advanced file management and organization',
      icon: Icons.folder,
      color: Colors.brown,
      builder: (context) => const FileManagerMain(),
    ),
    LauncherApp(
      number: 4,
      name: 'Islamic App',
      description: 'Islamic tools and Quran resources',
      icon: Icons.mosque,
      color: Colors.teal.shade700,
      builder: (context) => const IslamicAppMain(),
    ),
    LauncherApp(
      number: 5,
      name: 'Notes App',
      description: 'Note-taking and organization',
      icon: Icons.note,
      color: Colors.teal,
      builder: (context) => const NotesAppMain(),
    ),
    LauncherApp(
      number: 6,
      name: 'Athkar App',
      description: 'Islamic remembrance and dhikr',
      icon: Icons.favorite,
      color: Colors.green,
      builder: (context) => const AthkarAppMain(),
    ),
    LauncherApp(
      number: 7,
      name: 'Hadith App',
      description: 'Hadith collection and study',
      icon: Icons.menu_book,
      color: Colors.deepOrange,
      builder: (context) => const HadithAppMain(),
    ),
    LauncherApp(
      number: 8,
      name: 'Todo App',
      description: 'Task management and productivity',
      icon: Icons.check_circle,
      color: Colors.indigo,
      builder: (context) => const TodoAppMain(),
    ),
    LauncherApp(
      number: 9,
      name: 'Voice Recorder',
      description: 'Voice recording and memos',
      icon: Icons.mic,
      color: Colors.red,
      builder: (context) => const VoiceRecorderMain(),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Shadow Suite'),
        centerTitle: true,
        elevation: 0,
      ),
      body: _selectedIndex == -1
          ? _buildLauncherGrid()
          : _apps[_selectedIndex].builder(context),
      bottomNavigationBar: _selectedIndex == -1
          ? null
          : BottomNavigationBar(
              currentIndex: 0,
              items: [
                const BottomNavigationBarItem(
                  icon: Icon(Icons.home),
                  label: 'Home',
                ),
                BottomNavigationBarItem(
                  icon: Icon(_apps[_selectedIndex].icon),
                  label: _apps[_selectedIndex].name,
                ),
              ],
              onTap: (index) {
                if (index == 0) {
                  setState(() {
                    _selectedIndex = -1;
                  });
                }
              },
            ),
    );
  }

  Widget _buildLauncherGrid() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Welcome to Shadow Suite',
            style: Theme.of(
              context,
            ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'Choose an application to get started',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),
          Expanded(
            child: LayoutBuilder(
              builder: (context, constraints) {
                // Responsive grid based on screen width
                int crossAxisCount = 2;
                if (constraints.maxWidth > 600) {
                  crossAxisCount = 3;
                }
                if (constraints.maxWidth > 900) {
                  crossAxisCount = 4;
                }

                return GridView.builder(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: crossAxisCount,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    childAspectRatio: 1.1,
                  ),
                  itemCount: _apps.length,
                  itemBuilder: (context, index) {
                    final app = _apps[index];
                    return _buildAppCard(app, index);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppCard(LauncherApp app, int index) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedIndex = index;
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                app.color.withValues(alpha: 0.1),
                app.color.withValues(alpha: 0.05),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Sequential number badge
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: app.color,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${app.number}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(height: 6),
              Icon(app.icon, size: 32, color: app.color),
              const SizedBox(height: 8),
              Text(
                app.name,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  fontSize: 13,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2),
              Text(
                app.description,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                  fontSize: 10,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build placeholder app for apps that are not yet implemented
  static Widget _buildPlaceholderApp(String appName, String message) {
    return Scaffold(
      appBar: AppBar(title: Text(appName), centerTitle: true),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.construction, size: 100, color: Colors.grey[400]),
            const SizedBox(height: 24),
            Text(
              appName,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
            ),
            const SizedBox(height: 32),
            Builder(
              builder: (context) => ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Back to Launcher'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Model for launcher applications
class LauncherApp {
  final int number;
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final Widget Function(BuildContext) builder;

  const LauncherApp({
    required this.number,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.builder,
  });
}
