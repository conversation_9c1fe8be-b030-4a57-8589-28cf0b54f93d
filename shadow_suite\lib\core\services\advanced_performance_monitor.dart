import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:io';
import 'dart:convert';

// Advanced Performance Monitor Provider
final advancedPerformanceMonitorProvider = Provider<AdvancedPerformanceMonitor>(
  (ref) {
    return AdvancedPerformanceMonitor();
  },
);

// Performance Metrics Provider
final performanceMetricsProvider =
    StateNotifierProvider<PerformanceMetricsNotifier, PerformanceMetrics>((
      ref,
    ) {
      return PerformanceMetricsNotifier();
    });

// Performance Alerts Provider
final performanceAlertsProvider =
    StateNotifierProvider<PerformanceAlertsNotifier, List<PerformanceAlert>>((
      ref,
    ) {
      return PerformanceAlertsNotifier();
    });

// Advanced Performance Monitor
class AdvancedPerformanceMonitor {
  static final AdvancedPerformanceMonitor _instance =
      AdvancedPerformanceMonitor._internal();
  factory AdvancedPerformanceMonitor() => _instance;
  AdvancedPerformanceMonitor._internal();

  final List<PerformanceMetric> _metrics = [];
  final List<PerformanceAlert> _alerts = [];
  late File _metricsFile;

  // Performance thresholds (in milliseconds)
  static const double responseTimeThreshold = 100.0;
  static const double startupTimeThreshold = 3000.0;
  static const double transitionTimeThreshold = 300.0;
  static const double databaseQueryThreshold = 50.0;
  static const double fileOperationThreshold = 200.0;
  static const double networkRequestThreshold = 1000.0;

  // Initialize performance monitoring
  Future<void> initialize() async {
    await _initializeMetricsFile();
    await _loadExistingMetrics();
    _startPerformanceTracking();
  }

  Future<void> _initializeMetricsFile() async {
    try {
      final appDir = await _getAppDirectory();
      _metricsFile = File('${appDir.path}/performance_metrics.json');

      if (!await _metricsFile.exists()) {
        await _metricsFile.create(recursive: true);
        await _metricsFile.writeAsString('[]');
      }
    } catch (e) {
      debugPrint('Error initializing metrics file: $e');
    }
  }

  Future<Directory> _getAppDirectory() async {
    if (Platform.isAndroid) {
      return Directory('/data/data/com.shadowsuite.app/files');
    } else if (Platform.isWindows) {
      final userProfile = Platform.environment['USERPROFILE'] ?? '';
      return Directory('$userProfile/AppData/Local/ShadowSuite');
    } else {
      return Directory.current;
    }
  }

  Future<void> _loadExistingMetrics() async {
    try {
      if (await _metricsFile.exists()) {
        final content = await _metricsFile.readAsString();
        if (content.isNotEmpty && content != '[]') {
          final List<dynamic> data = jsonDecode(content);
          _metrics.clear();
          _metrics.addAll(data.map((e) => PerformanceMetric.fromJson(e)));
        }
      }
    } catch (e) {
      debugPrint('Error loading existing metrics: $e');
    }
  }

  void _startPerformanceTracking() {
    // Monitor frame rendering performance
    WidgetsBinding.instance.addPersistentFrameCallback(_onFrameRendered);
  }

  void _onFrameRendered(Duration timestamp) {
    // Track frame rendering time
    final frameTime =
        timestamp.inMicroseconds / 1000.0; // Convert to milliseconds

    if (frameTime > 16.67) {
      // 60 FPS threshold
      _recordMetric(
        PerformanceMetric(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          timestamp: DateTime.now(),
          type: PerformanceType.frameRendering,
          operation: 'frame_render',
          duration: Duration(microseconds: frameTime.round() * 1000),
          threshold: 16.67,
          isThresholdExceeded: true,
          metadata: {'fps': (1000 / frameTime).toStringAsFixed(1)},
        ),
      );
    }
  }

  // Record performance metric
  void recordMetric({
    required PerformanceType type,
    required String operation,
    required Duration duration,
    double? customThreshold,
    Map<String, dynamic>? metadata,
  }) {
    final threshold = customThreshold ?? _getThresholdForType(type);
    final isExceeded = duration.inMilliseconds > threshold;

    final metric = PerformanceMetric(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      timestamp: DateTime.now(),
      type: type,
      operation: operation,
      duration: duration,
      threshold: threshold,
      isThresholdExceeded: isExceeded,
      metadata: metadata ?? {},
    );

    _recordMetric(metric);

    // Create alert if threshold exceeded
    if (isExceeded) {
      _createPerformanceAlert(metric);
    }
  }

  void _recordMetric(PerformanceMetric metric) {
    _metrics.add(metric);
    _saveMetrics();

    if (kDebugMode && metric.isThresholdExceeded) {
      debugPrint(
        'PERFORMANCE ALERT: ${metric.operation} took ${metric.duration.inMilliseconds}ms (threshold: ${metric.threshold}ms)',
      );
    }
  }

  void _createPerformanceAlert(PerformanceMetric metric) {
    final alert = PerformanceAlert(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      timestamp: DateTime.now(),
      type: metric.type,
      operation: metric.operation,
      actualDuration: metric.duration,
      expectedThreshold: metric.threshold,
      severity: _calculateSeverity(
        metric.duration.inMilliseconds.toDouble(),
        metric.threshold,
      ),
      message:
          'Performance issue detected: ${metric.operation} took ${metric.duration.inMilliseconds}ms (expected: <${metric.threshold}ms)',
      metadata: metric.metadata,
    );

    _alerts.add(alert);
  }

  AlertSeverity _calculateSeverity(double actual, double threshold) {
    final ratio = actual / threshold;
    if (ratio > 5.0) return AlertSeverity.critical;
    if (ratio > 3.0) return AlertSeverity.high;
    if (ratio > 2.0) return AlertSeverity.medium;
    return AlertSeverity.low;
  }

  double _getThresholdForType(PerformanceType type) {
    switch (type) {
      case PerformanceType.appStartup:
        return startupTimeThreshold;
      case PerformanceType.screenTransition:
        return transitionTimeThreshold;
      case PerformanceType.databaseQuery:
        return databaseQueryThreshold;
      case PerformanceType.fileOperation:
        return fileOperationThreshold;
      case PerformanceType.networkRequest:
        return networkRequestThreshold;
      case PerformanceType.userInteraction:
        return responseTimeThreshold;
      case PerformanceType.frameRendering:
        return 16.67; // 60 FPS
      case PerformanceType.memoryUsage:
        return 100.0; // 100MB threshold
      case PerformanceType.cpuUsage:
        return 80.0; // 80% CPU threshold
    }
  }

  // Measure operation performance
  Future<T> measureOperation<T>({
    required Future<T> Function() operation,
    required PerformanceType type,
    required String operationName,
    double? customThreshold,
    Map<String, dynamic>? metadata,
  }) async {
    final stopwatch = Stopwatch()..start();

    try {
      final result = await operation();
      stopwatch.stop();

      recordMetric(
        type: type,
        operation: operationName,
        duration: stopwatch.elapsed,
        customThreshold: customThreshold,
        metadata: metadata,
      );

      return result;
    } catch (e) {
      stopwatch.stop();

      recordMetric(
        type: type,
        operation: '$operationName (failed)',
        duration: stopwatch.elapsed,
        customThreshold: customThreshold,
        metadata: {...?metadata, 'error': e.toString(), 'failed': true},
      );

      rethrow;
    }
  }

  // Measure synchronous operation
  T measureSyncOperation<T>({
    required T Function() operation,
    required PerformanceType type,
    required String operationName,
    double? customThreshold,
    Map<String, dynamic>? metadata,
  }) {
    final stopwatch = Stopwatch()..start();

    try {
      final result = operation();
      stopwatch.stop();

      recordMetric(
        type: type,
        operation: operationName,
        duration: stopwatch.elapsed,
        customThreshold: customThreshold,
        metadata: metadata,
      );

      return result;
    } catch (e) {
      stopwatch.stop();

      recordMetric(
        type: type,
        operation: '$operationName (failed)',
        duration: stopwatch.elapsed,
        customThreshold: customThreshold,
        metadata: {...?metadata, 'error': e.toString(), 'failed': true},
      );

      rethrow;
    }
  }

  // Get performance statistics
  PerformanceStatistics getStatistics() {
    final now = DateTime.now();
    final last24Hours = now.subtract(const Duration(hours: 24));
    final last7Days = now.subtract(const Duration(days: 7));

    final metrics24h = _metrics
        .where((m) => m.timestamp.isAfter(last24Hours))
        .toList();
    final metrics7d = _metrics
        .where((m) => m.timestamp.isAfter(last7Days))
        .toList();

    final alerts24h = _alerts
        .where((a) => a.timestamp.isAfter(last24Hours))
        .length;
    final alerts7d = _alerts
        .where((a) => a.timestamp.isAfter(last7Days))
        .length;

    return PerformanceStatistics(
      totalMetrics: _metrics.length,
      metricsLast24Hours: metrics24h.length,
      metricsLast7Days: metrics7d.length,
      alertsLast24Hours: alerts24h,
      alertsLast7Days: alerts7d,
      averageResponseTime: _calculateAverageResponseTime(metrics24h),
      slowestOperations: _getSlowestOperations(metrics24h),
      performanceScore: _calculatePerformanceScore(metrics24h),
      thresholdViolations: metrics24h
          .where((m) => m.isThresholdExceeded)
          .length,
    );
  }

  double _calculateAverageResponseTime(List<PerformanceMetric> metrics) {
    if (metrics.isEmpty) return 0.0;

    final userInteractionMetrics = metrics
        .where((m) => m.type == PerformanceType.userInteraction)
        .toList();

    if (userInteractionMetrics.isEmpty) return 0.0;

    final totalTime = userInteractionMetrics
        .map((m) => m.duration.inMilliseconds)
        .reduce((a, b) => a + b);

    return totalTime / userInteractionMetrics.length;
  }

  List<PerformanceMetric> _getSlowestOperations(
    List<PerformanceMetric> metrics,
  ) {
    final sorted = List<PerformanceMetric>.from(metrics)
      ..sort((a, b) => b.duration.compareTo(a.duration));
    return sorted.take(10).toList();
  }

  double _calculatePerformanceScore(List<PerformanceMetric> metrics) {
    if (metrics.isEmpty) return 100.0;

    final violations = metrics.where((m) => m.isThresholdExceeded).length;
    final violationRate = violations / metrics.length;

    // Score from 0-100, where 100 is perfect performance
    return (1.0 - violationRate) * 100.0;
  }

  Future<void> _saveMetrics() async {
    try {
      // Keep only last 10000 metrics
      if (_metrics.length > 10000) {
        _metrics.removeRange(0, _metrics.length - 10000);
      }

      final jsonData = _metrics.map((m) => m.toJson()).toList();
      await _metricsFile.writeAsString(jsonEncode(jsonData));
    } catch (e) {
      debugPrint('Error saving metrics: $e');
    }
  }

  // Export performance data
  Future<String> exportPerformanceData() async {
    final exportData = {
      'export_timestamp': DateTime.now().toIso8601String(),
      'app_version': '1.0.0',
      'build_number': '1',
      'metrics': _metrics.map((m) => m.toJson()).toList(),
      'alerts': _alerts.map((a) => a.toJson()).toList(),
      'statistics': getStatistics().toJson(),
    };

    return jsonEncode(exportData);
  }

  // Clear old metrics
  Future<void> clearOldMetrics({Duration? olderThan}) async {
    final cutoff = DateTime.now().subtract(
      olderThan ?? const Duration(days: 7),
    );

    _metrics.removeWhere((m) => m.timestamp.isBefore(cutoff));
    _alerts.removeWhere((a) => a.timestamp.isBefore(cutoff));

    await _saveMetrics();
  }

  // Get recent alerts
  List<PerformanceAlert> getRecentAlerts({int limit = 50}) {
    final sorted = List<PerformanceAlert>.from(_alerts)
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return sorted.take(limit).toList();
  }

  // Get metrics by type
  List<PerformanceMetric> getMetricsByType(
    PerformanceType type, {
    Duration? timeRange,
  }) {
    var filtered = _metrics.where((m) => m.type == type);

    if (timeRange != null) {
      final cutoff = DateTime.now().subtract(timeRange);
      filtered = filtered.where((m) => m.timestamp.isAfter(cutoff));
    }

    return filtered.toList();
  }
}

// Performance Metric Model
class PerformanceMetric {
  final String id;
  final DateTime timestamp;
  final PerformanceType type;
  final String operation;
  final Duration duration;
  final double threshold;
  final bool isThresholdExceeded;
  final Map<String, dynamic> metadata;

  const PerformanceMetric({
    required this.id,
    required this.timestamp,
    required this.type,
    required this.operation,
    required this.duration,
    required this.threshold,
    required this.isThresholdExceeded,
    required this.metadata,
  });

  factory PerformanceMetric.fromJson(Map<String, dynamic> json) {
    return PerformanceMetric(
      id: json['id'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      type: PerformanceType.values.firstWhere((e) => e.name == json['type']),
      operation: json['operation'] as String,
      duration: Duration(milliseconds: json['duration_ms'] as int),
      threshold: (json['threshold'] as num).toDouble(),
      isThresholdExceeded: json['isThresholdExceeded'] as bool,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'type': type.name,
      'operation': operation,
      'duration_ms': duration.inMilliseconds,
      'threshold': threshold,
      'isThresholdExceeded': isThresholdExceeded,
      'metadata': metadata,
    };
  }
}

// Performance Alert Model
class PerformanceAlert {
  final String id;
  final DateTime timestamp;
  final PerformanceType type;
  final String operation;
  final Duration actualDuration;
  final double expectedThreshold;
  final AlertSeverity severity;
  final String message;
  final Map<String, dynamic> metadata;

  const PerformanceAlert({
    required this.id,
    required this.timestamp,
    required this.type,
    required this.operation,
    required this.actualDuration,
    required this.expectedThreshold,
    required this.severity,
    required this.message,
    required this.metadata,
  });

  factory PerformanceAlert.fromJson(Map<String, dynamic> json) {
    return PerformanceAlert(
      id: json['id'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      type: PerformanceType.values.firstWhere((e) => e.name == json['type']),
      operation: json['operation'] as String,
      actualDuration: Duration(milliseconds: json['actual_duration_ms'] as int),
      expectedThreshold: (json['expected_threshold'] as num).toDouble(),
      severity: AlertSeverity.values.firstWhere(
        (e) => e.name == json['severity'],
      ),
      message: json['message'] as String,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'type': type.name,
      'operation': operation,
      'actual_duration_ms': actualDuration.inMilliseconds,
      'expected_threshold': expectedThreshold,
      'severity': severity.name,
      'message': message,
      'metadata': metadata,
    };
  }
}

// Performance Statistics Model
class PerformanceStatistics {
  final int totalMetrics;
  final int metricsLast24Hours;
  final int metricsLast7Days;
  final int alertsLast24Hours;
  final int alertsLast7Days;
  final double averageResponseTime;
  final List<PerformanceMetric> slowestOperations;
  final double performanceScore;
  final int thresholdViolations;

  const PerformanceStatistics({
    required this.totalMetrics,
    required this.metricsLast24Hours,
    required this.metricsLast7Days,
    required this.alertsLast24Hours,
    required this.alertsLast7Days,
    required this.averageResponseTime,
    required this.slowestOperations,
    required this.performanceScore,
    required this.thresholdViolations,
  });

  Map<String, dynamic> toJson() {
    return {
      'totalMetrics': totalMetrics,
      'metricsLast24Hours': metricsLast24Hours,
      'metricsLast7Days': metricsLast7Days,
      'alertsLast24Hours': alertsLast24Hours,
      'alertsLast7Days': alertsLast7Days,
      'averageResponseTime': averageResponseTime,
      'slowestOperations': slowestOperations.map((m) => m.toJson()).toList(),
      'performanceScore': performanceScore,
      'thresholdViolations': thresholdViolations,
    };
  }
}

// Performance Metrics Model for State
class PerformanceMetrics {
  final double averageResponseTime;
  final double performanceScore;
  final int totalAlerts;
  final DateTime lastUpdated;

  const PerformanceMetrics({
    required this.averageResponseTime,
    required this.performanceScore,
    required this.totalAlerts,
    required this.lastUpdated,
  });

  PerformanceMetrics copyWith({
    double? averageResponseTime,
    double? performanceScore,
    int? totalAlerts,
    DateTime? lastUpdated,
  }) {
    return PerformanceMetrics(
      averageResponseTime: averageResponseTime ?? this.averageResponseTime,
      performanceScore: performanceScore ?? this.performanceScore,
      totalAlerts: totalAlerts ?? this.totalAlerts,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

// Performance Metrics Notifier
class PerformanceMetricsNotifier extends StateNotifier<PerformanceMetrics> {
  PerformanceMetricsNotifier()
    : super(
        PerformanceMetrics(
          averageResponseTime: 0.0,
          performanceScore: 100.0,
          totalAlerts: 0,
          lastUpdated: DateTime.now(),
        ),
      );

  void updateMetrics(PerformanceStatistics stats) {
    state = state.copyWith(
      averageResponseTime: stats.averageResponseTime,
      performanceScore: stats.performanceScore,
      totalAlerts: stats.alertsLast24Hours,
      lastUpdated: DateTime.now(),
    );
  }
}

// Performance Alerts Notifier
class PerformanceAlertsNotifier extends StateNotifier<List<PerformanceAlert>> {
  PerformanceAlertsNotifier() : super([]);

  void addAlert(PerformanceAlert alert) {
    state = [alert, ...state];
  }

  void clearAlerts() {
    state = [];
  }

  void loadAlerts(List<PerformanceAlert> alerts) {
    state = alerts;
  }
}

// Enums
enum PerformanceType {
  appStartup,
  screenTransition,
  userInteraction,
  databaseQuery,
  fileOperation,
  networkRequest,
  frameRendering,
  memoryUsage,
  cpuUsage,
}

enum AlertSeverity { low, medium, high, critical }
