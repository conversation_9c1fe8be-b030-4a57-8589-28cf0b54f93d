import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'screens/money_manager_dashboard.dart';
import 'screens/accounts_screen.dart';
import 'screens/transactions_screen.dart';
import 'screens/categories_screen.dart';
import 'screens/budgets_screen.dart';
import 'screens/goals_screen.dart';
import 'screens/reports_screen.dart';
import 'services/money_manager_database.dart';
import 'services/money_manager_providers.dart';

enum MoneyManagerScreen {
  dashboard,
  accounts,
  transactions,
  categories,
  budgets,
  goals,
  reports,
}

final currentMoneyManagerScreenProvider = StateProvider<MoneyManagerScreen>((
  ref,
) {
  return MoneyManagerScreen.dashboard;
});

class MoneyManagerMain extends ConsumerStatefulWidget {
  const MoneyManagerMain({super.key});

  @override
  ConsumerState<MoneyManagerMain> createState() => _MoneyManagerMainState();
}

class _MoneyManagerMainState extends ConsumerState<MoneyManagerMain> {
  @override
  void initState() {
    super.initState();
    _initializeDatabase();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateScreenFromRoute();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateScreenFromRoute();
  }

  void _updateScreenFromRoute() {
    final location = GoRouterState.of(context).uri.toString();
    MoneyManagerScreen newScreen = MoneyManagerScreen.dashboard;

    if (location.contains('/money-manager/accounts')) {
      newScreen = MoneyManagerScreen.accounts;
    } else if (location.contains('/money-manager/transactions')) {
      newScreen = MoneyManagerScreen.transactions;
    } else if (location.contains('/money-manager/budgets')) {
      newScreen = MoneyManagerScreen.budgets;
    } else if (location.contains('/money-manager/reports')) {
      newScreen = MoneyManagerScreen.reports;
    } else if (location.contains('/money-manager/dashboard') ||
        location == '/money-manager') {
      newScreen = MoneyManagerScreen.dashboard;
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(currentMoneyManagerScreenProvider.notifier).state = newScreen;
    });
  }

  Future<void> _initializeDatabase() async {
    try {
      await MoneyManagerDatabase.initialize();
      // Refresh all providers after database initialization
      ref.read(accountsProvider.notifier).loadAccounts();
      ref.read(categoriesProvider.notifier).loadCategories();
      ref.read(transactionsProvider.notifier).loadTransactions();
      ref.read(budgetsProvider.notifier).loadBudgets();
      ref.read(goalsProvider.notifier).loadGoals();
    } catch (e) {
      debugPrint('Failed to initialize Money Manager database: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentScreen = ref.watch(currentMoneyManagerScreenProvider);

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.blue.shade50, Colors.white],
        ),
      ),
      child: _buildMainContent(currentScreen),
    );
  }

  Widget _buildMainContent(MoneyManagerScreen screen) {
    switch (screen) {
      case MoneyManagerScreen.dashboard:
        return const MoneyManagerDashboard();
      case MoneyManagerScreen.accounts:
        return const AccountsScreen();
      case MoneyManagerScreen.transactions:
        return const TransactionsScreen();
      case MoneyManagerScreen.categories:
        return const CategoriesScreen();
      case MoneyManagerScreen.budgets:
        return const BudgetsScreen();
      case MoneyManagerScreen.goals:
        return const GoalsScreen();
      case MoneyManagerScreen.reports:
        return const ReportsScreen();
    }
  }
}
