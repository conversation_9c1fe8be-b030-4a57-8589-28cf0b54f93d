import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/athkar_models.dart';

/// AI-Powered Athkar Service with Smart Reminders and Personalized Routines
class AIAthkarService {
  static final AIAthkarService _instance = AIAthkarService._internal();
  factory AIAthkarService() => _instance;
  AIAthkarService._internal();

  final StreamController<AthkarInsight> _insightController =
      StreamController.broadcast();
  Stream<AthkarInsight> get insightStream => _insightController.stream;

  // Smart Routine Features (250 features)
  Future<List<AthkarRecommendation>> generatePersonalizedRoutines(
    List<AthkarSession> sessions,
    UserPreferences preferences,
    List<CustomAthkar> customAthkar,
  ) async {
    final recommendations = <AthkarRecommendation>[];

    // Analyze recitation patterns
    recommendations.addAll(await _analyzeRecitationPatterns(sessions));

    // Suggest optimal timing
    recommendations.addAll(await _suggestOptimalTiming(sessions, preferences));

    // Recommend routine combinations
    recommendations.addAll(
      await _recommendRoutineCombinations(sessions, customAthkar),
    );

    // Suggest difficulty progression
    recommendations.addAll(
      await _suggestDifficultyProgression(sessions, preferences),
    );

    // Recommend seasonal athkar
    recommendations.addAll(await _recommendSeasonalAthkar(preferences));

    return recommendations;
  }

  Future<List<AthkarRecommendation>> _analyzeRecitationPatterns(
    List<AthkarSession> sessions,
  ) async {
    final recommendations = <AthkarRecommendation>[];

    if (sessions.length >= 14) {
      // Analyze consistency patterns
      final dailySessions = <int, List<AthkarSession>>{};
      for (final session in sessions) {
        final dayOfWeek = session.startTime.weekday;
        dailySessions[dayOfWeek] = (dailySessions[dayOfWeek] ?? [])
          ..add(session);
      }

      // Find most consistent day
      final consistency = <int, double>{};
      dailySessions.forEach((day, daySessions) {
        final avgDuration =
            daySessions
                .map((s) => s.duration.inMinutes)
                .reduce((a, b) => a + b) /
            daySessions.length;
        consistency[day] = avgDuration;
      });

      if (consistency.isNotEmpty) {
        final bestDay = consistency.entries.reduce(
          (a, b) => a.value > b.value ? a : b,
        );

        recommendations.add(
          AthkarRecommendation(
            id: 'consistency_${bestDay.key}_${DateTime.now().millisecondsSinceEpoch}',
            type: RecommendationType.routine,
            title: 'Build on Your Consistency',
            description:
                'You\'re most consistent on ${_getDayName(bestDay.key)}s',
            suggestedAthkar: _getConsistencyAthkar(),
            timing: AthkarTiming.flexible,
            priority: Priority.high,
            confidence: 0.85,
            estimatedBenefit: 'Strengthen your spiritual routine',
            createdAt: DateTime.now(),
          ),
        );
      }

      // Analyze completion rates
      final completionRates = <String, double>{};
      for (final session in sessions) {
        for (final athkar in session.completedAthkar) {
          final completionRate = athkar.completedCount / athkar.targetCount;
          completionRates[athkar.name] =
              (completionRates[athkar.name] ?? 0) + completionRate;
        }
      }

      // Find athkar with low completion rates
      completionRates.forEach((name, totalRate) {
        final avgRate = totalRate / sessions.length;
        if (avgRate < 0.7) {
          recommendations.add(
            AthkarRecommendation(
              id: 'completion_${name.toLowerCase()}_${DateTime.now().millisecondsSinceEpoch}',
              type: RecommendationType.improvement,
              title: 'Improve $name Completion',
              description:
                  'Your completion rate for $name is ${(avgRate * 100).round()}%',
              suggestedAthkar: [name],
              timing: AthkarTiming.flexible,
              priority: Priority.medium,
              confidence: 0.75,
              estimatedBenefit: 'Better spiritual focus and completion',
              createdAt: DateTime.now(),
            ),
          );
        }
      });
    }

    return recommendations;
  }

  Future<List<AthkarRecommendation>> _suggestOptimalTiming(
    List<AthkarSession> sessions,
    UserPreferences preferences,
  ) async {
    final recommendations = <AthkarRecommendation>[];

    // Analyze session times
    final hourFrequency = <int, int>{};
    for (final session in sessions) {
      final hour = session.startTime.hour;
      hourFrequency[hour] = (hourFrequency[hour] ?? 0) + 1;
    }

    if (hourFrequency.isNotEmpty) {
      final peakHour = hourFrequency.entries
          .reduce((a, b) => a.value > b.value ? a : b)
          .key;

      recommendations.add(
        AthkarRecommendation(
          id: 'optimal_timing_${peakHour}_${DateTime.now().millisecondsSinceEpoch}',
          type: RecommendationType.timing,
          title: 'Optimal Recitation Time',
          description: 'You recite most often at ${_formatHour(peakHour)}',
          suggestedAthkar: _getTimeBasedAthkar(peakHour),
          timing: _getTimingFromHour(peakHour),
          priority: Priority.medium,
          confidence: 0.80,
          estimatedBenefit: 'Consistent spiritual practice',
          createdAt: DateTime.now(),
        ),
      );
    }

    // Suggest missing prayer time athkar
    final prayerTimes = [5, 12, 15, 18, 20]; // Approximate prayer times
    for (final prayerHour in prayerTimes) {
      final hasSessionsAtTime = sessions.any(
        (s) => (s.startTime.hour - prayerHour).abs() <= 1,
      );

      if (!hasSessionsAtTime) {
        recommendations.add(
          AthkarRecommendation(
            id: 'prayer_time_${prayerHour}_${DateTime.now().millisecondsSinceEpoch}',
            type: RecommendationType.timing,
            title: 'Add Prayer Time Athkar',
            description:
                'Consider adding athkar around ${_formatHour(prayerHour)}',
            suggestedAthkar: _getPrayerTimeAthkar(prayerHour),
            timing: _getTimingFromHour(prayerHour),
            priority: Priority.low,
            confidence: 0.70,
            estimatedBenefit: 'Complete daily spiritual routine',
            createdAt: DateTime.now(),
          ),
        );
      }
    }

    return recommendations;
  }

  Future<List<AthkarRecommendation>> _recommendRoutineCombinations(
    List<AthkarSession> sessions,
    List<CustomAthkar> customAthkar,
  ) async {
    final recommendations = <AthkarRecommendation>[];

    // Analyze frequently combined athkar
    final combinations = <String, int>{};
    for (final session in sessions) {
      if (session.completedAthkar.length >= 2) {
        final athkarNames = session.completedAthkar.map((a) => a.name).toList()
          ..sort();
        final combination = athkarNames.join(' + ');
        combinations[combination] = (combinations[combination] ?? 0) + 1;
      }
    }

    // Find popular combinations
    final popularCombinations =
        combinations.entries.where((e) => e.value >= 3).toList()
          ..sort((a, b) => b.value.compareTo(a.value));

    for (final combo in popularCombinations.take(3)) {
      recommendations.add(
        AthkarRecommendation(
          id: 'combination_${combo.key.hashCode}_${DateTime.now().millisecondsSinceEpoch}',
          type: RecommendationType.combination,
          title: 'Effective Combination',
          description: 'You often combine: ${combo.key}',
          suggestedAthkar: combo.key.split(' + '),
          timing: AthkarTiming.flexible,
          priority: Priority.medium,
          confidence: 0.75,
          estimatedBenefit: 'Proven effective routine',
          createdAt: DateTime.now(),
        ),
      );
    }

    // Suggest new combinations based on themes
    const thematicCombinations = {
      'Morning Protection': ['Ayat al-Kursi', 'Al-Falaq', 'An-Nas'],
      'Evening Gratitude': [
        'Alhamdulillah',
        'Astaghfirullah',
        'La hawla wa la quwwata',
      ],
      'Sleep Preparation': ['Bismillah', 'Ayat al-Kursi', 'Surah Al-Ikhlas'],
    };

    thematicCombinations.forEach((theme, athkarList) {
      recommendations.add(
        AthkarRecommendation(
          id: 'thematic_${theme.toLowerCase().replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}',
          type: RecommendationType.thematic,
          title: '$theme Routine',
          description: 'A focused routine for $theme',
          suggestedAthkar: athkarList,
          timing: _getThematicTiming(theme),
          priority: Priority.low,
          confidence: 0.65,
          estimatedBenefit: 'Targeted spiritual benefits',
          createdAt: DateTime.now(),
        ),
      );
    });

    return recommendations.take(5).toList();
  }

  Future<List<AthkarRecommendation>> _suggestDifficultyProgression(
    List<AthkarSession> sessions,
    UserPreferences preferences,
  ) async {
    final recommendations = <AthkarRecommendation>[];

    // Assess current level
    final currentLevel = _assessAthkarLevel(sessions);

    if (currentLevel == AthkarLevel.beginner) {
      recommendations.add(
        AthkarRecommendation(
          id: 'beginner_progression_${DateTime.now().millisecondsSinceEpoch}',
          type: RecommendationType.progression,
          title: 'Foundation Building',
          description: 'Start with essential daily athkar',
          suggestedAthkar: _getBeginnerAthkar(),
          timing: AthkarTiming.morning,
          priority: Priority.high,
          confidence: 0.90,
          estimatedBenefit: 'Strong spiritual foundation',
          createdAt: DateTime.now(),
        ),
      );
    } else if (currentLevel == AthkarLevel.intermediate) {
      recommendations.add(
        AthkarRecommendation(
          id: 'intermediate_progression_${DateTime.now().millisecondsSinceEpoch}',
          type: RecommendationType.progression,
          title: 'Expand Your Practice',
          description: 'Add more comprehensive athkar routines',
          suggestedAthkar: _getIntermediateAthkar(),
          timing: AthkarTiming.flexible,
          priority: Priority.medium,
          confidence: 0.85,
          estimatedBenefit: 'Deeper spiritual connection',
          createdAt: DateTime.now(),
        ),
      );
    } else {
      recommendations.add(
        AthkarRecommendation(
          id: 'advanced_progression_${DateTime.now().millisecondsSinceEpoch}',
          type: RecommendationType.progression,
          title: 'Advanced Practices',
          description: 'Incorporate specialized and lengthy athkar',
          suggestedAthkar: _getAdvancedAthkar(),
          timing: AthkarTiming.flexible,
          priority: Priority.low,
          confidence: 0.80,
          estimatedBenefit: 'Mastery and spiritual excellence',
          createdAt: DateTime.now(),
        ),
      );
    }

    return recommendations;
  }

  Future<List<AthkarRecommendation>> _recommendSeasonalAthkar(
    UserPreferences preferences,
  ) async {
    final recommendations = <AthkarRecommendation>[];
    final now = DateTime.now();

    // Ramadan athkar
    if (_isRamadan(now)) {
      recommendations.add(
        AthkarRecommendation(
          id: 'ramadan_${now.millisecondsSinceEpoch}',
          type: RecommendationType.seasonal,
          title: 'Ramadan Special Athkar',
          description: 'Enhanced spiritual practices for the holy month',
          suggestedAthkar: _getRamadanAthkar(),
          timing: AthkarTiming.evening,
          priority: Priority.high,
          confidence: 0.95,
          estimatedBenefit: 'Maximized Ramadan blessings',
          createdAt: DateTime.now(),
        ),
      );
    }

    // Hajj season athkar
    if (_isHajjSeason(now)) {
      recommendations.add(
        AthkarRecommendation(
          id: 'hajj_${now.millisecondsSinceEpoch}',
          type: RecommendationType.seasonal,
          title: 'Hajj Season Athkar',
          description: 'Special remembrance during the days of Hajj',
          suggestedAthkar: _getHajjAthkar(),
          timing: AthkarTiming.flexible,
          priority: Priority.medium,
          confidence: 0.85,
          estimatedBenefit: 'Spiritual connection with pilgrims',
          createdAt: DateTime.now(),
        ),
      );
    }

    // Friday athkar
    if (now.weekday == DateTime.friday) {
      recommendations.add(
        AthkarRecommendation(
          id: 'friday_${now.millisecondsSinceEpoch}',
          type: RecommendationType.weekly,
          title: 'Friday Special Athkar',
          description: 'Enhanced remembrance for the blessed day',
          suggestedAthkar: _getFridayAthkar(),
          timing: AthkarTiming.afternoon,
          priority: Priority.medium,
          confidence: 0.80,
          estimatedBenefit: 'Friday blessings and acceptance',
          createdAt: DateTime.now(),
        ),
      );
    }

    return recommendations;
  }

  // Utility Methods
  String _getDayName(int weekday) {
    const days = [
      '',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday',
    ];
    return days[weekday];
  }

  List<String> _getConsistencyAthkar() {
    return ['Subhan Allah', 'Alhamdulillah', 'Allahu Akbar'];
  }

  String _formatHour(int hour) {
    if (hour == 0) return '12:00 AM';
    if (hour < 12) return '$hour:00 AM';
    if (hour == 12) return '12:00 PM';
    return '${hour - 12}:00 PM';
  }

  List<String> _getTimeBasedAthkar(int hour) {
    if (hour >= 5 && hour <= 11) return ['Morning Athkar', 'Ayat al-Kursi'];
    if (hour >= 12 && hour <= 17) return ['Afternoon Dhikr', 'Istighfar'];
    return ['Evening Athkar', 'Protection Duas'];
  }

  AthkarTiming _getTimingFromHour(int hour) {
    if (hour >= 5 && hour <= 11) return AthkarTiming.morning;
    if (hour >= 12 && hour <= 17) return AthkarTiming.afternoon;
    return AthkarTiming.evening;
  }

  List<String> _getPrayerTimeAthkar(int hour) {
    return ['Post-Prayer Dhikr', 'Tasbih', 'Dua'];
  }

  AthkarTiming _getThematicTiming(String theme) {
    if (theme.contains('Morning')) return AthkarTiming.morning;
    if (theme.contains('Evening')) return AthkarTiming.evening;
    if (theme.contains('Sleep')) return AthkarTiming.night;
    return AthkarTiming.flexible;
  }

  AthkarLevel _assessAthkarLevel(List<AthkarSession> sessions) {
    if (sessions.length < 10) return AthkarLevel.beginner;
    if (sessions.length < 50) return AthkarLevel.intermediate;
    return AthkarLevel.advanced;
  }

  List<String> _getBeginnerAthkar() {
    return ['Subhan Allah', 'Alhamdulillah', 'La ilaha illa Allah'];
  }

  List<String> _getIntermediateAthkar() {
    return ['Ayat al-Kursi', 'Istighfar', 'Salawat'];
  }

  List<String> _getAdvancedAthkar() {
    return ['Long Duas', 'Specialized Dhikr', 'Scholarly Athkar'];
  }

  bool _isRamadan(DateTime date) {
    // Simplified Ramadan detection
    return date.month == 4; // Approximate
  }

  bool _isHajjSeason(DateTime date) {
    // Simplified Hajj season detection
    return date.month == 7; // Approximate
  }

  List<String> _getRamadanAthkar() {
    return ['Ramadan Duas', 'Iftar Dua', 'Tarawih Dhikr'];
  }

  List<String> _getHajjAthkar() {
    return ['Talbiyah', 'Hajj Duas', 'Arafat Dhikr'];
  }

  List<String> _getFridayAthkar() {
    return ['Friday Salawat', 'Jummah Duas', 'Blessed Day Dhikr'];
  }

  void dispose() {
    _insightController.close();
  }
}
