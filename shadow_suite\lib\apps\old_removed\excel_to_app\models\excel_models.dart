import 'excel_advanced_models.dart';

// Excel Workbook Model
class ExcelWorkbook {
  final String id;
  final String name;
  final String filePath;
  final List<ExcelWorksheet> worksheets;
  final Map<String, dynamic> properties;
  final DateTime createdAt;
  final DateTime lastModified;
  final int fileSize;
  final bool isProtected;
  final String? password;

  const ExcelWorkbook({
    required this.id,
    required this.name,
    required this.filePath,
    required this.worksheets,
    required this.properties,
    required this.createdAt,
    required this.lastModified,
    required this.fileSize,
    required this.isProtected,
    this.password,
  });

  factory ExcelWorkbook.fromJson(Map<String, dynamic> json) {
    return ExcelWorkbook(
      id: json['id'] as String,
      name: json['name'] as String,
      filePath: json['file_path'] as String,
      worksheets: (json['worksheets'] as List<dynamic>?)
          ?.map((e) => ExcelWorksheet.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      properties: Map<String, dynamic>.from(json['properties'] as Map? ?? {}),
      createdAt: DateTime.parse(json['created_at'] as String),
      lastModified: DateTime.parse(json['last_modified'] as String),
      fileSize: json['file_size'] as int,
      isProtected: json['is_protected'] as bool,
      password: json['password'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'file_path': filePath,
      'worksheets': worksheets.map((e) => e.toJson()).toList(),
      'properties': properties,
      'created_at': createdAt.toIso8601String(),
      'last_modified': lastModified.toIso8601String(),
      'file_size': fileSize,
      'is_protected': isProtected,
      'password': password,
    };
  }
}

// Excel Worksheet Model
class ExcelWorksheet {
  final String id;
  final String name;
  final Map<String, ExcelCell> cells;
  final List<ExcelChart> charts;
  final List<ExcelTable> tables;
  final List<ExcelPivotTable> pivotTables;
  final WorksheetProperties properties;
  final bool isVisible;
  final bool isProtected;

  const ExcelWorksheet({
    required this.id,
    required this.name,
    required this.cells,
    required this.charts,
    required this.tables,
    required this.pivotTables,
    required this.properties,
    required this.isVisible,
    required this.isProtected,
  });

  factory ExcelWorksheet.fromJson(Map<String, dynamic> json) {
    return ExcelWorksheet(
      id: json['id'] as String,
      name: json['name'] as String,
      cells: (json['cells'] as Map<String, dynamic>? ?? {}).map(
        (key, value) => MapEntry(key, ExcelCell.fromJson(value as Map<String, dynamic>)),
      ),
      charts: (json['charts'] as List<dynamic>?)
          ?.map((e) => ExcelChart.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      tables: (json['tables'] as List<dynamic>?)
          ?.map((e) => ExcelTable.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      pivotTables: (json['pivot_tables'] as List<dynamic>?)
          ?.map((e) => ExcelPivotTable.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      properties: WorksheetProperties.fromJson(json['properties'] as Map<String, dynamic>),
      isVisible: json['is_visible'] as bool,
      isProtected: json['is_protected'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'cells': cells.map((key, value) => MapEntry(key, value.toJson())),
      'charts': charts.map((e) => e.toJson()).toList(),
      'tables': tables.map((e) => e.toJson()).toList(),
      'pivot_tables': pivotTables.map((e) => e.toJson()).toList(),
      'properties': properties.toJson(),
      'is_visible': isVisible,
      'is_protected': isProtected,
    };
  }
}

// Excel Cell Model
class ExcelCell {
  final String address;
  final dynamic value;
  final String? formula;
  final CellDataType dataType;
  final CellFormat format;
  final String? comment;
  final bool isLocked;
  final List<String> dependencies;

  const ExcelCell({
    required this.address,
    this.value,
    this.formula,
    required this.dataType,
    required this.format,
    this.comment,
    required this.isLocked,
    required this.dependencies,
  });

  factory ExcelCell.fromJson(Map<String, dynamic> json) {
    return ExcelCell(
      address: json['address'] as String,
      value: json['value'],
      formula: json['formula'] as String?,
      dataType: CellDataType.values.firstWhere(
        (e) => e.name == json['data_type'],
        orElse: () => CellDataType.text,
      ),
      format: CellFormat.fromJson(json['format'] as Map<String, dynamic>),
      comment: json['comment'] as String?,
      isLocked: json['is_locked'] as bool,
      dependencies: List<String>.from(json['dependencies'] as List? ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'address': address,
      'value': value,
      'formula': formula,
      'data_type': dataType.name,
      'format': format.toJson(),
      'comment': comment,
      'is_locked': isLocked,
      'dependencies': dependencies,
    };
  }
}

// Cell Format Model
class CellFormat {
  final String? fontFamily;
  final double? fontSize;
  final bool isBold;
  final bool isItalic;
  final bool isUnderline;
  final String? textColor;
  final String? backgroundColor;
  final TextAlignment horizontalAlignment;
  final TextAlignment verticalAlignment;
  final String? numberFormat;
  final BorderStyle borders;

  const CellFormat({
    this.fontFamily,
    this.fontSize,
    required this.isBold,
    required this.isItalic,
    required this.isUnderline,
    this.textColor,
    this.backgroundColor,
    required this.horizontalAlignment,
    required this.verticalAlignment,
    this.numberFormat,
    required this.borders,
  });

  factory CellFormat.fromJson(Map<String, dynamic> json) {
    return CellFormat(
      fontFamily: json['font_family'] as String?,
      fontSize: (json['font_size'] as num?)?.toDouble(),
      isBold: json['is_bold'] as bool? ?? false,
      isItalic: json['is_italic'] as bool? ?? false,
      isUnderline: json['is_underline'] as bool? ?? false,
      textColor: json['text_color'] as String?,
      backgroundColor: json['background_color'] as String?,
      horizontalAlignment: TextAlignment.values.firstWhere(
        (e) => e.name == json['horizontal_alignment'],
        orElse: () => TextAlignment.left,
      ),
      verticalAlignment: TextAlignment.values.firstWhere(
        (e) => e.name == json['vertical_alignment'],
        orElse: () => TextAlignment.top,
      ),
      numberFormat: json['number_format'] as String?,
      borders: BorderStyle.fromJson(json['borders'] as Map<String, dynamic>? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'font_family': fontFamily,
      'font_size': fontSize,
      'is_bold': isBold,
      'is_italic': isItalic,
      'is_underline': isUnderline,
      'text_color': textColor,
      'background_color': backgroundColor,
      'horizontal_alignment': horizontalAlignment.name,
      'vertical_alignment': verticalAlignment.name,
      'number_format': numberFormat,
      'borders': borders.toJson(),
    };
  }
}

// Border Style Model
class BorderStyle {
  final String? topColor;
  final String? rightColor;
  final String? bottomColor;
  final String? leftColor;
  final BorderWeight topWeight;
  final BorderWeight rightWeight;
  final BorderWeight bottomWeight;
  final BorderWeight leftWeight;

  const BorderStyle({
    this.topColor,
    this.rightColor,
    this.bottomColor,
    this.leftColor,
    required this.topWeight,
    required this.rightWeight,
    required this.bottomWeight,
    required this.leftWeight,
  });

  factory BorderStyle.fromJson(Map<String, dynamic> json) {
    return BorderStyle(
      topColor: json['top_color'] as String?,
      rightColor: json['right_color'] as String?,
      bottomColor: json['bottom_color'] as String?,
      leftColor: json['left_color'] as String?,
      topWeight: BorderWeight.values.firstWhere(
        (e) => e.name == json['top_weight'],
        orElse: () => BorderWeight.none,
      ),
      rightWeight: BorderWeight.values.firstWhere(
        (e) => e.name == json['right_weight'],
        orElse: () => BorderWeight.none,
      ),
      bottomWeight: BorderWeight.values.firstWhere(
        (e) => e.name == json['bottom_weight'],
        orElse: () => BorderWeight.none,
      ),
      leftWeight: BorderWeight.values.firstWhere(
        (e) => e.name == json['left_weight'],
        orElse: () => BorderWeight.none,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'top_color': topColor,
      'right_color': rightColor,
      'bottom_color': bottomColor,
      'left_color': leftColor,
      'top_weight': topWeight.name,
      'right_weight': rightWeight.name,
      'bottom_weight': bottomWeight.name,
      'left_weight': leftWeight.name,
    };
  }
}

// Worksheet Properties Model
class WorksheetProperties {
  final double defaultRowHeight;
  final double defaultColumnWidth;
  final bool showGridlines;
  final bool showHeaders;
  final String? backgroundImage;
  final double zoomLevel;
  final bool isRightToLeft;

  const WorksheetProperties({
    required this.defaultRowHeight,
    required this.defaultColumnWidth,
    required this.showGridlines,
    required this.showHeaders,
    this.backgroundImage,
    required this.zoomLevel,
    required this.isRightToLeft,
  });

  factory WorksheetProperties.fromJson(Map<String, dynamic> json) {
    return WorksheetProperties(
      defaultRowHeight: (json['default_row_height'] as num).toDouble(),
      defaultColumnWidth: (json['default_column_width'] as num).toDouble(),
      showGridlines: json['show_gridlines'] as bool,
      showHeaders: json['show_headers'] as bool,
      backgroundImage: json['background_image'] as String?,
      zoomLevel: (json['zoom_level'] as num).toDouble(),
      isRightToLeft: json['is_right_to_left'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'default_row_height': defaultRowHeight,
      'default_column_width': defaultColumnWidth,
      'show_gridlines': showGridlines,
      'show_headers': showHeaders,
      'background_image': backgroundImage,
      'zoom_level': zoomLevel,
      'is_right_to_left': isRightToLeft,
    };
  }
}

// Enums
enum CellDataType { text, number, date, boolean, formula, error }
enum TextAlignment { left, center, right, justify, top, middle, bottom }
enum BorderWeight { none, thin, medium, thick }
