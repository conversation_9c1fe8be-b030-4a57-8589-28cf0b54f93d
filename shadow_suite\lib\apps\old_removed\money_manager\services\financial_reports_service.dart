import 'dart:math' as math;
import '../models/money_manager_models.dart';
import 'money_manager_database.dart';

class FinancialReportsService {
  // Generate comprehensive financial report
  static Future<FinancialReport> generateReport({
    required DateTime startDate,
    required DateTime endDate,
    List<String>? accountIds,
    List<String>? categoryIds,
  }) async {
    final transactions = await _getFilteredTransactions(
      startDate: startDate,
      endDate: endDate,
      accountIds: accountIds,
      categoryIds: categoryIds,
    );

    final accounts = await MoneyManagerDatabase.getAccounts();
    final categories = await MoneyManagerDatabase.getCategories();

    final summary = _generateSummary(transactions);
    final categoryBreakdown = _generateCategoryBreakdown(transactions, categories);
    final accountBreakdown = _generateAccountBreakdown(transactions, accounts);
    final trends = _generateTrends(transactions, startDate, endDate);
    final insights = _generateInsights(transactions, summary, trends);

    return FinancialReport(
      startDate: startDate,
      endDate: endDate,
      summary: summary,
      categoryBreakdown: categoryBreakdown,
      accountBreakdown: accountBreakdown,
      trends: trends,
      insights: insights,
      generatedAt: DateTime.now(),
    );
  }

  // Generate monthly comparison report
  static Future<MonthlyComparison> generateMonthlyComparison({
    required int year,
    required int month,
  }) async {
    final currentMonth = DateTime(year, month);
    final previousMonth = DateTime(year, month - 1);
    final nextMonth = DateTime(year, month + 1);

    final currentTransactions = await _getFilteredTransactions(
      startDate: currentMonth,
      endDate: nextMonth,
    );

    final previousTransactions = await _getFilteredTransactions(
      startDate: previousMonth,
      endDate: currentMonth,
    );

    final currentSummary = _generateSummary(currentTransactions);
    final previousSummary = _generateSummary(previousTransactions);

    return MonthlyComparison(
      currentMonth: currentMonth,
      currentSummary: currentSummary,
      previousSummary: previousSummary,
      incomeChange: _calculatePercentageChange(
        previousSummary.totalIncome,
        currentSummary.totalIncome,
      ),
      expenseChange: _calculatePercentageChange(
        previousSummary.totalExpenses,
        currentSummary.totalExpenses,
      ),
      netIncomeChange: _calculatePercentageChange(
        previousSummary.netIncome,
        currentSummary.netIncome,
      ),
    );
  }

  // Generate spending patterns analysis
  static Future<SpendingPatterns> analyzeSpendingPatterns({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final transactions = await _getFilteredTransactions(
      startDate: startDate,
      endDate: endDate,
    );

    final expenseTransactions = transactions
        .where((t) => t.type == TransactionType.expense)
        .toList();

    final dailySpending = _analyzeDailySpending(expenseTransactions);
    final weeklySpending = _analyzeWeeklySpending(expenseTransactions);
    final monthlySpending = _analyzeMonthlySpending(expenseTransactions);
    final categoryPatterns = _analyzeCategoryPatterns(expenseTransactions);

    return SpendingPatterns(
      dailyAverage: dailySpending.average,
      dailyPeak: dailySpending.peak,
      weeklyAverage: weeklySpending.average,
      weeklyPeak: weeklySpending.peak,
      monthlyAverage: monthlySpending.average,
      monthlyPeak: monthlySpending.peak,
      topSpendingDay: dailySpending.topDay,
      topSpendingCategory: categoryPatterns.topCategory,
      spendingConsistency: _calculateSpendingConsistency(expenseTransactions),
      seasonalTrends: _analyzeSeasonalTrends(expenseTransactions),
    );
  }

  // Generate cash flow analysis
  static Future<CashFlowAnalysis> analyzeCashFlow({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final transactions = await _getFilteredTransactions(
      startDate: startDate,
      endDate: endDate,
    );

    final dailyCashFlow = <DateTime, double>{};
    var runningBalance = 0.0;

    // Group transactions by day
    final transactionsByDay = <DateTime, List<Transaction>>{};
    for (final transaction in transactions) {
      final day = DateTime(
        transaction.date.year,
        transaction.date.month,
        transaction.date.day,
      );
      transactionsByDay.putIfAbsent(day, () => []).add(transaction);
    }

    // Calculate daily cash flow
    final sortedDays = transactionsByDay.keys.toList()..sort();
    for (final day in sortedDays) {
      final dayTransactions = transactionsByDay[day]!;
      var dayFlow = 0.0;

      for (final transaction in dayTransactions) {
        if (transaction.type == TransactionType.income) {
          dayFlow += transaction.amount;
        } else {
          dayFlow -= transaction.amount;
        }
      }

      runningBalance += dayFlow;
      dailyCashFlow[day] = runningBalance;
    }

    final projectedCashFlow = _projectCashFlow(dailyCashFlow, endDate);

    return CashFlowAnalysis(
      dailyCashFlow: dailyCashFlow,
      projectedCashFlow: projectedCashFlow,
      averageDailyFlow: _calculateAverageDailyFlow(dailyCashFlow),
      cashFlowTrend: _determineCashFlowTrend(dailyCashFlow),
      volatility: _calculateCashFlowVolatility(dailyCashFlow),
    );
  }

  // Helper methods
  static Future<List<Transaction>> _getFilteredTransactions({
    required DateTime startDate,
    required DateTime endDate,
    List<String>? accountIds,
    List<String>? categoryIds,
  }) async {
    final allTransactions = await MoneyManagerDatabase.getTransactions();

    return allTransactions.where((transaction) {
      // Date filter
      if (transaction.date.isBefore(startDate) ||
          transaction.date.isAfter(endDate)) {
        return false;
      }

      // Account filter
      if (accountIds != null && !accountIds.contains(transaction.accountId)) {
        return false;
      }

      // Category filter
      if (categoryIds != null && !categoryIds.contains(transaction.categoryId)) {
        return false;
      }

      return true;
    }).toList();
  }

  static FinancialSummary _generateSummary(List<Transaction> transactions) {
    var totalIncome = 0.0;
    var totalExpenses = 0.0;
    var totalTransfers = 0.0;

    for (final transaction in transactions) {
      switch (transaction.type) {
        case TransactionType.income:
          totalIncome += transaction.amount;
          break;
        case TransactionType.expense:
          totalExpenses += transaction.amount;
          break;
        case TransactionType.transfer:
          totalTransfers += transaction.amount;
          break;
      }
    }

    return FinancialSummary(
      totalIncome: totalIncome,
      totalExpenses: totalExpenses,
      totalTransfers: totalTransfers,
      netIncome: totalIncome - totalExpenses,
      transactionCount: transactions.length,
      averageTransactionAmount: transactions.isNotEmpty
          ? transactions.map((t) => t.amount).reduce((a, b) => a + b) /
              transactions.length
          : 0.0,
    );
  }

  static List<CategoryBreakdown> _generateCategoryBreakdown(
    List<Transaction> transactions,
    List<Category> categories,
  ) {
    final categoryTotals = <String, double>{};
    final categoryTransactionCounts = <String, int>{};

    for (final transaction in transactions) {
      categoryTotals[transaction.categoryId] =
          (categoryTotals[transaction.categoryId] ?? 0) + transaction.amount;
      categoryTransactionCounts[transaction.categoryId] =
          (categoryTransactionCounts[transaction.categoryId] ?? 0) + 1;
    }

    final totalAmount = categoryTotals.values.fold<double>(0.0, (a, b) => a + b);

    return categoryTotals.entries.map((entry) {
      final category = categories.firstWhere(
        (c) => c.id == entry.key,
        orElse: () => Category(
          id: entry.key,
          name: 'Unknown',
          type: CategoryType.expense,
          createdAt: DateTime.now(),
        ),
      );

      return CategoryBreakdown(
        categoryId: entry.key,
        categoryName: category.name,
        amount: entry.value,
        percentage: totalAmount > 0 ? (entry.value / totalAmount) * 100 : 0,
        transactionCount: categoryTransactionCounts[entry.key] ?? 0,
        averageAmount: (categoryTransactionCounts[entry.key] ?? 0) > 0
            ? entry.value / (categoryTransactionCounts[entry.key]!)
            : 0,
      );
    }).toList()
      ..sort((a, b) => b.amount.compareTo(a.amount));
  }

  static List<AccountBreakdown> _generateAccountBreakdown(
    List<Transaction> transactions,
    List<Account> accounts,
  ) {
    final accountTotals = <String, double>{};
    final accountTransactionCounts = <String, int>{};

    for (final transaction in transactions) {
      accountTotals[transaction.accountId] =
          (accountTotals[transaction.accountId] ?? 0) + transaction.amount;
      accountTransactionCounts[transaction.accountId] =
          (accountTransactionCounts[transaction.accountId] ?? 0) + 1;
    }

    return accountTotals.entries.map((entry) {
      final account = accounts.firstWhere(
        (a) => a.id == entry.key,
        orElse: () => Account(
          id: entry.key,
          name: 'Unknown',
          type: AccountType.checking,
          initialBalance: 0,
          currentBalance: 0,
          currency: 'USD',
          color: '#3498DB',
          icon: 'account_balance',
          showInTotal: true,
          allowNegative: false,
          createdAt: DateTime.now(),
          lastModified: DateTime.now(),
        ),
      );

      return AccountBreakdown(
        accountId: entry.key,
        accountName: account.name,
        amount: entry.value,
        transactionCount: accountTransactionCounts[entry.key] ?? 0,
        currentBalance: account.currentBalance,
      );
    }).toList()
      ..sort((a, b) => b.amount.compareTo(a.amount));
  }

  static List<TrendData> _generateTrends(
    List<Transaction> transactions,
    DateTime startDate,
    DateTime endDate,
  ) {
    final trends = <TrendData>[];
    final dailyTotals = <DateTime, double>{};

    // Group transactions by day
    for (final transaction in transactions) {
      final day = DateTime(
        transaction.date.year,
        transaction.date.month,
        transaction.date.day,
      );

      if (transaction.type == TransactionType.expense) {
        dailyTotals[day] = (dailyTotals[day] ?? 0) + transaction.amount;
      }
    }

    // Create trend data points
    var currentDate = startDate;
    while (currentDate.isBefore(endDate)) {
      trends.add(TrendData(
        date: currentDate,
        amount: dailyTotals[currentDate] ?? 0.0,
      ));
      currentDate = currentDate.add(const Duration(days: 1));
    }

    return trends;
  }

  static List<FinancialInsight> _generateInsights(
    List<Transaction> transactions,
    FinancialSummary summary,
    List<TrendData> trends,
  ) {
    final insights = <FinancialInsight>[];

    // Spending insights
    if (summary.totalExpenses > summary.totalIncome) {
      insights.add(FinancialInsight(
        type: InsightType.warning,
        title: 'Spending Exceeds Income',
        description:
            'Your expenses (\$${summary.totalExpenses.toStringAsFixed(2)}) exceed your income (\$${summary.totalIncome.toStringAsFixed(2)}) by \$${(summary.totalExpenses - summary.totalIncome).toStringAsFixed(2)}.',
        recommendation:
            'Consider reducing expenses or finding additional income sources.',
        impact: InsightImpact.high,
      ));
    }

    // Savings rate insight
    final savingsRate = summary.totalIncome > 0
        ? ((summary.totalIncome - summary.totalExpenses) / summary.totalIncome) *
            100
        : 0;

    if (savingsRate < 10) {
      insights.add(FinancialInsight(
        type: InsightType.suggestion,
        title: 'Low Savings Rate',
        description:
            'Your current savings rate is ${savingsRate.toStringAsFixed(1)}%. Financial experts recommend saving at least 20% of income.',
        recommendation:
            'Try to increase your savings rate by reducing discretionary spending.',
        impact: InsightImpact.medium,
      ));
    }

    return insights;
  }

  static double _calculatePercentageChange(double oldValue, double newValue) {
    if (oldValue == 0) return newValue > 0 ? 100 : 0;
    return ((newValue - oldValue) / oldValue) * 100;
  }

  static SpendingAnalysis _analyzeDailySpending(List<Transaction> transactions) {
    final dailyTotals = <DateTime, double>{};

    for (final transaction in transactions) {
      final day = DateTime(
        transaction.date.year,
        transaction.date.month,
        transaction.date.day,
      );
      dailyTotals[day] = (dailyTotals[day] ?? 0) + transaction.amount;
    }

    final amounts = dailyTotals.values.toList();
    final average = amounts.isNotEmpty
        ? amounts.reduce((a, b) => a + b) / amounts.length
        : 0.0;
    final peak = amounts.isNotEmpty ? amounts.reduce(math.max) : 0.0;

    final topDay = dailyTotals.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;

    return SpendingAnalysis(
      average: average,
      peak: peak,
      topDay: topDay,
    );
  }

  static SpendingAnalysis _analyzeWeeklySpending(List<Transaction> transactions) {
    // Similar implementation for weekly analysis
    return SpendingAnalysis(average: 0, peak: 0, topDay: DateTime.now());
  }

  static SpendingAnalysis _analyzeMonthlySpending(List<Transaction> transactions) {
    // Similar implementation for monthly analysis
    return SpendingAnalysis(average: 0, peak: 0, topDay: DateTime.now());
  }

  static CategoryPatterns _analyzeCategoryPatterns(List<Transaction> transactions) {
    final categoryTotals = <String, double>{};

    for (final transaction in transactions) {
      categoryTotals[transaction.categoryId] =
          (categoryTotals[transaction.categoryId] ?? 0) + transaction.amount;
    }

    final topCategory = categoryTotals.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;

    return CategoryPatterns(topCategory: topCategory);
  }

  static double _calculateSpendingConsistency(List<Transaction> transactions) {
    // Calculate coefficient of variation for spending consistency
    final dailyTotals = <DateTime, double>{};

    for (final transaction in transactions) {
      final day = DateTime(
        transaction.date.year,
        transaction.date.month,
        transaction.date.day,
      );
      dailyTotals[day] = (dailyTotals[day] ?? 0) + transaction.amount;
    }

    final amounts = dailyTotals.values.toList();
    if (amounts.length < 2) return 100.0;

    final mean = amounts.reduce((a, b) => a + b) / amounts.length;
    final variance = amounts
            .map((amount) => math.pow(amount - mean, 2))
            .reduce((a, b) => a + b) /
        amounts.length;
    final standardDeviation = math.sqrt(variance);

    final coefficientOfVariation = mean > 0 ? (standardDeviation / mean) : 0;
    return math.max(0, 100 - (coefficientOfVariation * 100));
  }

  static List<SeasonalTrend> _analyzeSeasonalTrends(List<Transaction> transactions) {
    // Analyze spending patterns by season/month
    return [];
  }

  static double _calculateAverageDailyFlow(Map<DateTime, double> dailyCashFlow) {
    if (dailyCashFlow.isEmpty) return 0.0;
    return dailyCashFlow.values.reduce((a, b) => a + b) / dailyCashFlow.length;
  }

  static CashFlowTrend _determineCashFlowTrend(Map<DateTime, double> dailyCashFlow) {
    if (dailyCashFlow.length < 2) return CashFlowTrend.stable;

    final values = dailyCashFlow.values.toList();
    final firstHalf = values.take(values.length ~/ 2).toList();
    final secondHalf = values.skip(values.length ~/ 2).toList();

    final firstAvg = firstHalf.reduce((a, b) => a + b) / firstHalf.length;
    final secondAvg = secondHalf.reduce((a, b) => a + b) / secondHalf.length;

    if (secondAvg > firstAvg * 1.05) {
      return CashFlowTrend.improving;
    } else if (secondAvg < firstAvg * 0.95) {
      return CashFlowTrend.declining;
    } else {
      return CashFlowTrend.stable;
    }
  }

  static double _calculateCashFlowVolatility(Map<DateTime, double> dailyCashFlow) {
    if (dailyCashFlow.length < 2) return 0.0;

    final values = dailyCashFlow.values.toList();
    final mean = values.reduce((a, b) => a + b) / values.length;
    final variance = values
            .map((value) => math.pow(value - mean, 2))
            .reduce((a, b) => a + b) /
        values.length;

    return math.sqrt(variance);
  }

  static Map<DateTime, double> _projectCashFlow(
    Map<DateTime, double> historicalFlow,
    DateTime endDate,
  ) {
    // Simple projection based on recent trends
    if (historicalFlow.isEmpty) return {};

    final lastDate = historicalFlow.keys.reduce((a, b) => a.isAfter(b) ? a : b);
    final lastValue = historicalFlow[lastDate]!;

    // Calculate average daily change
    final values = historicalFlow.values.toList();
    var totalChange = 0.0;
    for (int i = 1; i < values.length; i++) {
      totalChange += values[i] - values[i - 1];
    }
    final avgDailyChange = values.length > 1 ? totalChange / (values.length - 1) : 0;

    final projection = <DateTime, double>{};
    var currentDate = lastDate.add(const Duration(days: 1));
    var currentValue = lastValue;

    while (currentDate.isBefore(endDate)) {
      currentValue += avgDailyChange;
      projection[currentDate] = currentValue;
      currentDate = currentDate.add(const Duration(days: 1));
    }

    return projection;
  }
}

// Data classes for financial reports
class FinancialReport {
  final DateTime startDate;
  final DateTime endDate;
  final FinancialSummary summary;
  final List<CategoryBreakdown> categoryBreakdown;
  final List<AccountBreakdown> accountBreakdown;
  final List<TrendData> trends;
  final List<FinancialInsight> insights;
  final DateTime generatedAt;

  const FinancialReport({
    required this.startDate,
    required this.endDate,
    required this.summary,
    required this.categoryBreakdown,
    required this.accountBreakdown,
    required this.trends,
    required this.insights,
    required this.generatedAt,
  });
}

class FinancialSummary {
  final double totalIncome;
  final double totalExpenses;
  final double totalTransfers;
  final double netIncome;
  final int transactionCount;
  final double averageTransactionAmount;

  const FinancialSummary({
    required this.totalIncome,
    required this.totalExpenses,
    required this.totalTransfers,
    required this.netIncome,
    required this.transactionCount,
    required this.averageTransactionAmount,
  });
}

class CategoryBreakdown {
  final String categoryId;
  final String categoryName;
  final double amount;
  final double percentage;
  final int transactionCount;
  final double averageAmount;

  const CategoryBreakdown({
    required this.categoryId,
    required this.categoryName,
    required this.amount,
    required this.percentage,
    required this.transactionCount,
    required this.averageAmount,
  });
}

class AccountBreakdown {
  final String accountId;
  final String accountName;
  final double amount;
  final int transactionCount;
  final double currentBalance;

  const AccountBreakdown({
    required this.accountId,
    required this.accountName,
    required this.amount,
    required this.transactionCount,
    required this.currentBalance,
  });
}

class TrendData {
  final DateTime date;
  final double amount;

  const TrendData({
    required this.date,
    required this.amount,
  });
}

class FinancialInsight {
  final InsightType type;
  final String title;
  final String description;
  final String recommendation;
  final InsightImpact impact;

  const FinancialInsight({
    required this.type,
    required this.title,
    required this.description,
    required this.recommendation,
    required this.impact,
  });
}

class MonthlyComparison {
  final DateTime currentMonth;
  final FinancialSummary currentSummary;
  final FinancialSummary previousSummary;
  final double incomeChange;
  final double expenseChange;
  final double netIncomeChange;

  const MonthlyComparison({
    required this.currentMonth,
    required this.currentSummary,
    required this.previousSummary,
    required this.incomeChange,
    required this.expenseChange,
    required this.netIncomeChange,
  });
}

class SpendingPatterns {
  final double dailyAverage;
  final double dailyPeak;
  final double weeklyAverage;
  final double weeklyPeak;
  final double monthlyAverage;
  final double monthlyPeak;
  final DateTime topSpendingDay;
  final String topSpendingCategory;
  final double spendingConsistency;
  final List<SeasonalTrend> seasonalTrends;

  const SpendingPatterns({
    required this.dailyAverage,
    required this.dailyPeak,
    required this.weeklyAverage,
    required this.weeklyPeak,
    required this.monthlyAverage,
    required this.monthlyPeak,
    required this.topSpendingDay,
    required this.topSpendingCategory,
    required this.spendingConsistency,
    required this.seasonalTrends,
  });
}

class CashFlowAnalysis {
  final Map<DateTime, double> dailyCashFlow;
  final Map<DateTime, double> projectedCashFlow;
  final double averageDailyFlow;
  final CashFlowTrend cashFlowTrend;
  final double volatility;

  const CashFlowAnalysis({
    required this.dailyCashFlow,
    required this.projectedCashFlow,
    required this.averageDailyFlow,
    required this.cashFlowTrend,
    required this.volatility,
  });
}

// Helper classes
class SpendingAnalysis {
  final double average;
  final double peak;
  final DateTime topDay;

  const SpendingAnalysis({
    required this.average,
    required this.peak,
    required this.topDay,
  });
}

class CategoryPatterns {
  final String topCategory;

  const CategoryPatterns({required this.topCategory});
}

class SeasonalTrend {
  final String season;
  final double averageSpending;

  const SeasonalTrend({
    required this.season,
    required this.averageSpending,
  });
}

// Enums
enum InsightType { suggestion, warning, achievement, trend }
enum InsightImpact { low, medium, high }
enum CashFlowTrend { improving, stable, declining }
