import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'database_initializer.dart';

class DatabaseManager {
  static Database? _database;
  static const String _databaseName = 'shadow_suite.db';
  static const int _databaseVersion = 1;

  // Singleton pattern
  static final DatabaseManager _instance = DatabaseManager._internal();
  factory DatabaseManager() => _instance;
  DatabaseManager._internal();

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    // Ensure database factory is initialized
    await DatabaseInitializer.ensureInitialized();

    // Get the documents directory
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final dbPath = join(documentsDirectory.path, 'ShadowSuite', _databaseName);

    // Ensure directory exists
    await Directory(dirname(dbPath)).create(recursive: true);

    // Use safe database opening with proper initialization
    final db = await DatabaseInitializer.safeOpenDatabase(
      dbPath,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
      onOpen: _onOpen,
    );

    if (db == null) {
      throw Exception('Failed to initialize Database Manager');
    }

    return db;
  }

  Future<void> _onCreate(Database db, int version) async {
    await _createTables(db);
    await _insertDefaultData(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database schema migrations
    for (int version = oldVersion + 1; version <= newVersion; version++) {
      await _migrateToVersion(db, version);
    }
  }

  Future<void> _onOpen(Database db) async {
    // Enable foreign key constraints
    await db.execute('PRAGMA foreign_keys = ON');
  }

  Future<void> _createTables(Database db) async {
    // Money Manager Tables
    await _createMoneyManagerTables(db);

    // Islamic App Tables
    await _createIslamicAppTables(db);

    // Memo Suite Tables
    await _createMemoSuiteTables(db);

    // Excel to App Tables
    await _createExcelToAppTables(db);

    // Settings Tables
    await _createSettingsTables(db);
  }

  Future<void> _createMoneyManagerTables(Database db) async {
    // Accounts table
    await db.execute('''
      CREATE TABLE accounts (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        initial_balance REAL NOT NULL DEFAULT 0.0,
        current_balance REAL NOT NULL DEFAULT 0.0,
        currency TEXT NOT NULL DEFAULT 'USD',
        color TEXT NOT NULL DEFAULT '#3498DB',
        description TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at INTEGER NOT NULL,
        last_modified INTEGER NOT NULL
      )
    ''');

    // Categories table
    await db.execute('''
      CREATE TABLE categories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        color TEXT DEFAULT '#95A5A6',
        icon TEXT DEFAULT 'category',
        parent_id TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (parent_id) REFERENCES categories (id) ON DELETE SET NULL
      )
    ''');

    // Transactions table
    await db.execute('''
      CREATE TABLE transactions (
        id TEXT PRIMARY KEY,
        amount REAL NOT NULL,
        type TEXT NOT NULL,
        account_id TEXT NOT NULL,
        to_account_id TEXT,
        category_id TEXT NOT NULL,
        description TEXT NOT NULL,
        notes TEXT,
        tags TEXT,
        date INTEGER NOT NULL,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (account_id) REFERENCES accounts (id) ON DELETE CASCADE,
        FOREIGN KEY (to_account_id) REFERENCES accounts (id) ON DELETE SET NULL,
        FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE RESTRICT
      )
    ''');

    // Budgets table
    await db.execute('''
      CREATE TABLE budgets (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        amount REAL NOT NULL,
        category_id TEXT,
        period_type TEXT NOT NULL,
        start_date INTEGER NOT NULL,
        end_date INTEGER NOT NULL,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE
      )
    ''');
  }

  Future<void> _createIslamicAppTables(Database db) async {
    // Bookmarks table
    await db.execute('''
      CREATE TABLE quran_bookmarks (
        id TEXT PRIMARY KEY,
        surah_number INTEGER NOT NULL,
        ayah_number INTEGER NOT NULL,
        title TEXT,
        notes TEXT,
        created_at INTEGER NOT NULL
      )
    ''');

    // Dhikr sessions table
    await db.execute('''
      CREATE TABLE dhikr_sessions (
        id TEXT PRIMARY KEY,
        category TEXT NOT NULL,
        total_count INTEGER NOT NULL,
        current_count INTEGER NOT NULL DEFAULT 0,
        is_completed INTEGER NOT NULL DEFAULT 0,
        start_time INTEGER NOT NULL,
        end_time INTEGER,
        created_at INTEGER NOT NULL
      )
    ''');

    // Prayer times table
    await db.execute('''
      CREATE TABLE prayer_times (
        id TEXT PRIMARY KEY,
        date TEXT NOT NULL,
        fajr TEXT NOT NULL,
        dhuhr TEXT NOT NULL,
        asr TEXT NOT NULL,
        maghrib TEXT NOT NULL,
        isha TEXT NOT NULL,
        created_at INTEGER NOT NULL
      )
    ''');
  }

  Future<void> _createMemoSuiteTables(Database db) async {
    // Voice memos table
    await db.execute('''
      CREATE TABLE voice_memos (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        file_path TEXT NOT NULL,
        duration_ms INTEGER NOT NULL,
        category TEXT NOT NULL DEFAULT 'General',
        tags TEXT,
        transcription TEXT,
        created_at INTEGER NOT NULL,
        last_modified INTEGER NOT NULL
      )
    ''');

    // Text notes table
    await db.execute('''
      CREATE TABLE text_notes (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        category TEXT NOT NULL DEFAULT 'General',
        tags TEXT,
        is_pinned INTEGER NOT NULL DEFAULT 0,
        created_at INTEGER NOT NULL,
        last_modified INTEGER NOT NULL
      )
    ''');
  }

  Future<void> _createExcelToAppTables(Database db) async {
    // Excel tools table
    await db.execute('''
      CREATE TABLE excel_tools (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        security_type TEXT NOT NULL DEFAULT 'none',
        spreadsheet_data TEXT NOT NULL,
        ui_components_data TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        last_modified INTEGER NOT NULL
      )
    ''');
  }

  Future<void> _createSettingsTables(Database db) async {
    // App settings table
    await db.execute('''
      CREATE TABLE app_settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        app_module TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        last_modified INTEGER NOT NULL
      )
    ''');

    // User preferences table
    await db.execute('''
      CREATE TABLE user_preferences (
        id TEXT PRIMARY KEY,
        theme_mode TEXT NOT NULL DEFAULT 'system',
        primary_color TEXT NOT NULL DEFAULT '#3498DB',
        font_size TEXT NOT NULL DEFAULT 'medium',
        language TEXT NOT NULL DEFAULT 'en',
        created_at INTEGER NOT NULL,
        last_modified INTEGER NOT NULL
      )
    ''');
  }

  Future<void> _insertDefaultData(Database db) async {
    final now = DateTime.now().millisecondsSinceEpoch;

    // Insert default categories
    await db.insert('categories', {
      'id': 'cat_food',
      'name': 'Food & Dining',
      'type': 'expense',
      'color': '#E74C3C',
      'icon': 'restaurant',
      'created_at': now,
    });

    await db.insert('categories', {
      'id': 'cat_transport',
      'name': 'Transportation',
      'type': 'expense',
      'color': '#3498DB',
      'icon': 'directions_car',
      'created_at': now,
    });

    await db.insert('categories', {
      'id': 'cat_salary',
      'name': 'Salary',
      'type': 'income',
      'color': '#27AE60',
      'icon': 'work',
      'created_at': now,
    });

    // Insert default user preferences
    await db.insert('user_preferences', {
      'id': 'default_prefs',
      'theme_mode': 'system',
      'primary_color': '#3498DB',
      'font_size': 'medium',
      'language': 'en',
      'created_at': now,
      'last_modified': now,
    });
  }

  Future<void> _migrateToVersion(Database db, int version) async {
    switch (version) {
      case 2:
        // Future migration example
        // await db.execute('ALTER TABLE accounts ADD COLUMN new_field TEXT');
        break;
      // Add more migration cases as needed
    }
  }

  // Backup and restore methods
  Future<String> createBackup() async {
    final db = await database;
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final backupPath = join(
      documentsDirectory.path,
      'ShadowSuite',
      'Backups',
      'backup_${DateTime.now().millisecondsSinceEpoch}.db',
    );

    await Directory(dirname(backupPath)).create(recursive: true);
    await File(db.path).copy(backupPath);

    return backupPath;
  }

  Future<void> restoreFromBackup(String backupPath) async {
    if (!await File(backupPath).exists()) {
      throw Exception('Backup file not found');
    }

    await _database?.close();
    _database = null;

    final documentsDirectory = await getApplicationDocumentsDirectory();
    final dbPath = join(documentsDirectory.path, 'ShadowSuite', _databaseName);

    await File(backupPath).copy(dbPath);

    // Reinitialize database
    _database = await _initDatabase();
  }

  Future<void> clearAllData() async {
    final db = await database;

    // Get all table names
    final tables = await db.rawQuery(
      "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'",
    );

    // Clear all tables
    for (final table in tables) {
      await db.delete(table['name'] as String);
    }

    // Reinsert default data
    await _insertDefaultData(db);
  }

  Future<void> close() async {
    await _database?.close();
    _database = null;
  }
}
