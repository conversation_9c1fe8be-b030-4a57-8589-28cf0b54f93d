import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';
import '../models/spreadsheet.dart';

// Performance Optimizer for Tools Builder
class PerformanceOptimizer {
  static const int _maxCacheSize = 1000;
  static const int _maxUndoSteps = 50;
  static const Duration _debounceDelay = Duration(milliseconds: 300);
  
  // Cache for calculated values
  static final LRUCache<String, dynamic> _calculationCache = LRUCache(_maxCacheSize);
  
  // Undo/Redo system
  static final Queue<SpreadsheetSnapshot> _undoStack = Queue<SpreadsheetSnapshot>();
  static final Queue<SpreadsheetSnapshot> _redoStack = Queue<SpreadsheetSnapshot>();
  
  // Debounce timers
  static final Map<String, Timer> _debounceTimers = {};
  
  // Dirty cell tracking
  static final Set<String> _dirtyCells = <String>{};
  
  // Component rendering optimization
  static final Map<String, ComponentRenderCache> _componentCache = {};
  
  /// Optimize spreadsheet calculations with caching
  static dynamic getCachedCalculation(String formula, Map<String, dynamic> context) {
    final cacheKey = _generateCacheKey(formula, context);
    
    if (_calculationCache.containsKey(cacheKey)) {
      return _calculationCache.get(cacheKey);
    }
    
    return null;
  }
  
  /// Cache calculation result
  static void cacheCalculation(String formula, Map<String, dynamic> context, dynamic result) {
    final cacheKey = _generateCacheKey(formula, context);
    _calculationCache.put(cacheKey, result);
  }
  
  /// Generate cache key from formula and context
  static String _generateCacheKey(String formula, Map<String, dynamic> context) {
    final contextHash = context.entries
        .map((e) => '${e.key}:${e.value}')
        .join('|');
    return '$formula#$contextHash';
  }
  
  /// Invalidate cache for specific cells
  static void invalidateCache(List<String> cellAddresses) {
    _calculationCache.clear(); // For simplicity, clear all cache
    _dirtyCells.addAll(cellAddresses);
  }
  
  /// Add spreadsheet snapshot for undo/redo
  static void addSnapshot(Spreadsheet spreadsheet, String action) {
    final snapshot = SpreadsheetSnapshot(
      spreadsheet: spreadsheet,
      action: action,
      timestamp: DateTime.now(),
    );
    
    _undoStack.addLast(snapshot);
    
    // Limit undo stack size
    while (_undoStack.length > _maxUndoSteps) {
      _undoStack.removeFirst();
    }
    
    // Clear redo stack when new action is performed
    _redoStack.clear();
  }
  
  /// Undo last action
  static SpreadsheetSnapshot? undo() {
    if (_undoStack.isEmpty) return null;
    
    final snapshot = _undoStack.removeLast();
    _redoStack.addLast(snapshot);
    
    return _undoStack.isNotEmpty ? _undoStack.last : null;
  }
  
  /// Redo last undone action
  static SpreadsheetSnapshot? redo() {
    if (_redoStack.isEmpty) return null;
    
    final snapshot = _redoStack.removeLast();
    _undoStack.addLast(snapshot);
    
    return snapshot;
  }
  
  /// Debounce function calls
  static void debounce(String key, VoidCallback callback) {
    _debounceTimers[key]?.cancel();
    _debounceTimers[key] = Timer(_debounceDelay, callback);
  }
  
  /// Optimize component rendering with caching
  static ComponentRenderCache? getCachedComponent(String componentId) {
    return _componentCache[componentId];
  }
  
  /// Cache component render data
  static void cacheComponent(String componentId, ComponentRenderCache cache) {
    _componentCache[componentId] = cache;
  }
  
  /// Clear component cache
  static void clearComponentCache() {
    _componentCache.clear();
  }
  
  /// Get dirty cells that need recalculation
  static Set<String> getDirtyCells() {
    return Set.from(_dirtyCells);
  }
  
  /// Mark cells as clean after recalculation
  static void markCellsClean(List<String> cellAddresses) {
    _dirtyCells.removeAll(cellAddresses);
  }
  
  /// Optimize large dataset operations
  static List<T> optimizeListOperations<T>(
    List<T> items,
    bool Function(T) filter,
    int Function(T, T)? sorter,
    {int? limit}
  ) {
    // Use efficient filtering and sorting for large datasets
    var result = items.where(filter);
    
    if (sorter != null) {
      final list = result.toList();
      list.sort(sorter);
      result = list;
    }
    
    if (limit != null && limit > 0) {
      result = result.take(limit);
    }
    
    return result.toList();
  }
  
  /// Memory cleanup
  static void cleanup() {
    _calculationCache.clear();
    _componentCache.clear();
    _dirtyCells.clear();
    
    // Cancel all debounce timers
    for (final timer in _debounceTimers.values) {
      timer.cancel();
    }
    _debounceTimers.clear();
    
    // Limit undo/redo stacks
    while (_undoStack.length > _maxUndoSteps ~/ 2) {
      _undoStack.removeFirst();
    }
    while (_redoStack.length > _maxUndoSteps ~/ 2) {
      _redoStack.removeFirst();
    }
  }
  
  /// Get performance metrics
  static PerformanceMetrics getMetrics() {
    return PerformanceMetrics(
      cacheSize: _calculationCache.length,
      undoStackSize: _undoStack.length,
      redoStackSize: _redoStack.length,
      dirtyCellsCount: _dirtyCells.length,
      componentCacheSize: _componentCache.length,
      activeTimersCount: _debounceTimers.length,
    );
  }
}

// LRU Cache implementation
class LRUCache<K, V> {
  final int maxSize;
  final LinkedHashMap<K, V> _cache = LinkedHashMap<K, V>();
  
  LRUCache(this.maxSize);
  
  V? get(K key) {
    if (!_cache.containsKey(key)) return null;
    
    // Move to end (most recently used)
    final value = _cache.remove(key) as V;
    _cache[key] = value;
    return value;
  }
  
  void put(K key, V value) {
    if (_cache.containsKey(key)) {
      _cache.remove(key);
    } else if (_cache.length >= maxSize) {
      // Remove least recently used
      _cache.remove(_cache.keys.first);
    }
    
    _cache[key] = value;
  }
  
  bool containsKey(K key) => _cache.containsKey(key);
  int get length => _cache.length;
  void clear() => _cache.clear();
}

// Spreadsheet snapshot for undo/redo
class SpreadsheetSnapshot {
  final Spreadsheet spreadsheet;
  final String action;
  final DateTime timestamp;
  
  SpreadsheetSnapshot({
    required this.spreadsheet,
    required this.action,
    required this.timestamp,
  });
}

// Component render cache
class ComponentRenderCache {
  final String componentId;
  final Map<String, dynamic> properties;
  final DateTime lastRendered;
  final String renderHash;
  
  ComponentRenderCache({
    required this.componentId,
    required this.properties,
    required this.lastRendered,
    required this.renderHash,
  });
  
  bool isValid(Map<String, dynamic> currentProperties) {
    final currentHash = _generateHash(currentProperties);
    return renderHash == currentHash;
  }
  
  String _generateHash(Map<String, dynamic> properties) {
    return properties.entries
        .map((e) => '${e.key}:${e.value}')
        .join('|')
        .hashCode
        .toString();
  }
}

// Performance metrics
class PerformanceMetrics {
  final int cacheSize;
  final int undoStackSize;
  final int redoStackSize;
  final int dirtyCellsCount;
  final int componentCacheSize;
  final int activeTimersCount;
  
  PerformanceMetrics({
    required this.cacheSize,
    required this.undoStackSize,
    required this.redoStackSize,
    required this.dirtyCellsCount,
    required this.componentCacheSize,
    required this.activeTimersCount,
  });
  
  Map<String, dynamic> toMap() {
    return {
      'cacheSize': cacheSize,
      'undoStackSize': undoStackSize,
      'redoStackSize': redoStackSize,
      'dirtyCellsCount': dirtyCellsCount,
      'componentCacheSize': componentCacheSize,
      'activeTimersCount': activeTimersCount,
    };
  }
  
  @override
  String toString() {
    return 'PerformanceMetrics(cache: $cacheSize, undo: $undoStackSize, redo: $redoStackSize, dirty: $dirtyCellsCount, components: $componentCacheSize, timers: $activeTimersCount)';
  }
}
