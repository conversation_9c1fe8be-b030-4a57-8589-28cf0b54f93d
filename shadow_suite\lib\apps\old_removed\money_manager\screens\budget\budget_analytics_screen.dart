import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:math' as math;
import '../../services/budget_service.dart';
import '../../models/money_manager_models.dart';
import '../../../../core/theme/app_theme.dart';

class BudgetAnalyticsScreen extends ConsumerStatefulWidget {
  const BudgetAnalyticsScreen({super.key});

  @override
  ConsumerState<BudgetAnalyticsScreen> createState() => _BudgetAnalyticsScreenState();
}

class _BudgetAnalyticsScreenState extends ConsumerState<BudgetAnalyticsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  List<Budget> _budgets = [];
  List<BudgetAlert> _alerts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      final budgets = await BudgetService.getActiveBudgets();
      final alerts = await BudgetService.getBudgetAlerts();
      
      setState(() {
        _budgets = budgets;
        _alerts = alerts;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading data: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Budget Analytics'),
        backgroundColor: AppTheme.moneyManagerColor,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Overview'),
            Tab(icon: Icon(Icons.trending_up), text: 'Trends'),
            Tab(icon: Icon(Icons.warning), text: 'Alerts'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildTrendsTab(),
                _buildAlertsTab(),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewBudget,
        backgroundColor: AppTheme.moneyManagerColor,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSummaryCards(),
          const SizedBox(height: 24),
          _buildBudgetsList(),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    final totalBudgets = _budgets.length;
    final activeBudgets = _budgets.length; // All budgets considered active for now
    final criticalBudgets = _alerts.where((a) => a.severity == AlertSeverity.high).length;

    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'Total Budgets',
            totalBudgets.toString(),
            Icons.account_balance_wallet,
            AppTheme.moneyManagerColor,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            'Active',
            activeBudgets.toString(),
            Icons.trending_up,
            Colors.green,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            'Critical',
            criticalBudgets.toString(),
            Icons.warning,
            Colors.red,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBudgetsList() {
    if (_budgets.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            children: [
              Icon(
                Icons.account_balance_wallet_outlined,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'No budgets found',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Create your first budget to start tracking your spending',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _createNewBudget,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.moneyManagerColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Create Budget'),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Active Budgets',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...(_budgets.map((budget) => _buildBudgetCard(budget))),
      ],
    );
  }

  Widget _buildBudgetCard(Budget budget) {
    return FutureBuilder<BudgetAnalytics>(
      future: BudgetService.getBudgetAnalytics(budget.id),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(child: Text(budget.name)),
                  const CircularProgressIndicator(),
                ],
              ),
            ),
          );
        }

        final analytics = snapshot.data!;
        final statusColor = _getStatusColor(analytics.status);

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        budget.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: statusColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: statusColor),
                      ),
                      child: Text(
                        analytics.status.name.toUpperCase(),
                        style: TextStyle(
                          color: statusColor,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                
                // Progress bar
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '\$${analytics.spentAmount.toStringAsFixed(2)} spent',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        Text(
                          '\$${analytics.remainingAmount.toStringAsFixed(2)} remaining',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(
                      value: (analytics.spentPercentage / 100).clamp(0.0, 1.0),
                      backgroundColor: Colors.grey[300],
                      valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${analytics.spentPercentage.toStringAsFixed(1)}% of \$${analytics.budgetAmount.toStringAsFixed(2)}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // Analytics summary
                Row(
                  children: [
                    Expanded(
                      child: _buildAnalyticsItem(
                        'Daily Avg',
                        '\$${analytics.dailyAverage.toStringAsFixed(2)}',
                        Icons.calendar_today,
                      ),
                    ),
                    Expanded(
                      child: _buildAnalyticsItem(
                        'Projected',
                        '\$${analytics.projectedSpending.toStringAsFixed(2)}',
                        Icons.trending_up,
                      ),
                    ),
                    Expanded(
                      child: _buildAnalyticsItem(
                        'Days Left',
                        analytics.daysRemaining.toString(),
                        Icons.schedule,
                      ),
                    ),
                  ],
                ),
                
                if (analytics.recommendations.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.lightbulb, color: Colors.blue, size: 16),
                            const SizedBox(width: 4),
                            Text(
                              'Recommendation',
                              style: TextStyle(
                                color: Colors.blue,
                                fontWeight: FontWeight.w600,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          analytics.recommendations.first,
                          style: TextStyle(
                            color: Colors.blue[800],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAnalyticsItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  Widget _buildTrendsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Spending Trends',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          if (_budgets.isNotEmpty)
            ..._budgets.map((budget) => _buildTrendChart(budget))
          else
            const Center(
              child: Text('No budget data available for trends'),
            ),
        ],
      ),
    );
  }

  Widget _buildTrendChart(Budget budget) {
    return FutureBuilder<List<SpendingTrend>>(
      future: BudgetService.getSpendingTrends(budget.id),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Text(budget.name),
                  const SizedBox(height: 16),
                  const CircularProgressIndicator(),
                ],
              ),
            ),
          );
        }

        final trends = snapshot.data!;
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  budget.name,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  height: 200,
                  child: CustomPaint(
                    painter: TrendChartPainter(trends, budget.amount),
                    size: const Size(double.infinity, 200),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAlertsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Budget Alerts',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          if (_alerts.isEmpty)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  children: [
                    Icon(
                      Icons.check_circle,
                      size: 64,
                      color: Colors.green[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No alerts',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.green[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'All your budgets are on track!',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            ..._alerts.map((alert) => _buildAlertCard(alert)),
        ],
      ),
    );
  }

  Widget _buildAlertCard(BudgetAlert alert) {
    final severityColor = _getSeverityColor(alert.severity);
    final typeIcon = _getAlertTypeIcon(alert.type);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: severityColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                typeIcon,
                color: severityColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    alert.budgetName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    alert.message,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${alert.percentage.toStringAsFixed(1)}% • \$${alert.amount.toStringAsFixed(2)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: severityColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(BudgetStatus status) {
    switch (status) {
      case BudgetStatus.underSpent:
        return Colors.blue;
      case BudgetStatus.onTrack:
        return Colors.green;
      case BudgetStatus.warning:
        return Colors.orange;
      case BudgetStatus.critical:
        return Colors.red;
      case BudgetStatus.exceeded:
        return Colors.red[800]!;
    }
  }

  Color _getSeverityColor(AlertSeverity severity) {
    switch (severity) {
      case AlertSeverity.low:
        return Colors.blue;
      case AlertSeverity.medium:
        return Colors.orange;
      case AlertSeverity.high:
        return Colors.red;
    }
  }

  IconData _getAlertTypeIcon(BudgetAlertType type) {
    switch (type) {
      case BudgetAlertType.warning:
        return Icons.warning;
      case BudgetAlertType.overspending:
        return Icons.trending_up;
      case BudgetAlertType.projection:
        return Icons.timeline;
      case BudgetAlertType.reminder:
        return Icons.notifications;
    }
  }

  void _createNewBudget() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create New Budget'),
        content: const Text('Budget creation form will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

// Custom painter for trend charts
class TrendChartPainter extends CustomPainter {
  final List<SpendingTrend> trends;
  final double budgetAmount;

  TrendChartPainter(this.trends, this.budgetAmount);

  @override
  void paint(Canvas canvas, Size size) {
    if (trends.isEmpty) return;

    final paint = Paint()
      ..color = AppTheme.moneyManagerColor
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final budgetLinePaint = Paint()
      ..color = Colors.red
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    final path = Path();
    final maxSpending = math.max(budgetAmount, trends.last.cumulativeSpending);

    for (int i = 0; i < trends.length; i++) {
      final x = (i / (trends.length - 1)) * size.width;
      final y = size.height - (trends[i].cumulativeSpending / maxSpending) * size.height;

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    canvas.drawPath(path, paint);

    // Draw budget line
    final budgetY = size.height - (budgetAmount / maxSpending) * size.height;
    canvas.drawLine(
      Offset(0, budgetY),
      Offset(size.width, budgetY),
      budgetLinePaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
