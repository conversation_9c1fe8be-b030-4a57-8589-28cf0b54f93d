import 'dart:async';
import 'dart:io';
import '../models/media_models.dart';
import '../../../core/services/error_handler.dart' as error_handler;

class MediaPlayerService {
  static final List<MediaFile> _mediaFiles = [];
  static MediaFile? _currentMedia;
  static MediaPlayerState _playerState = MediaPlayerState.stopped;
  static Duration _currentPosition = Duration.zero;
  static Duration _totalDuration = Duration.zero;
  static double _volume = 1.0;
  static bool _isMuted = false;
  static final PlaybackMode _playbackMode = PlaybackMode.normal;
  
  static final StreamController<MediaPlayerEvent> _eventController = 
      StreamController<MediaPlayerEvent>.broadcast();
  
  // Initialize media player service
  static Future<void> initialize() async {
    try {
      await _loadMediaLibrary();
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Initialize media player');
    }
  }

  // MEDIA PLAYBACK CONTROL
  
  // Play media file
  static Future<void> playMedia(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('Media file not found: $filePath');
      }
      
      final mediaFile = await _createMediaFile(file);
      _currentMedia = mediaFile;
      
      // Start playback (simplified implementation)
      _playerState = MediaPlayerState.playing;
      _currentPosition = Duration.zero;
      _totalDuration = mediaFile.duration;
      
      _notifyEvent(MediaPlayerEvent(
        type: MediaEventType.playbackStarted,
        mediaFile: mediaFile,
        timestamp: DateTime.now(),
      ));
      
      // Start position tracking
      _startPositionTracking();
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Play media: $filePath');
      rethrow;
    }
  }

  // Pause playback
  static Future<void> pausePlayback() async {
    try {
      if (_playerState == MediaPlayerState.playing) {
        _playerState = MediaPlayerState.paused;
        
        _notifyEvent(MediaPlayerEvent(
          type: MediaEventType.playbackPaused,
          mediaFile: _currentMedia,
          timestamp: DateTime.now(),
        ));
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Pause playback');
    }
  }

  // Resume playback
  static Future<void> resumePlayback() async {
    try {
      if (_playerState == MediaPlayerState.paused) {
        _playerState = MediaPlayerState.playing;
        
        _notifyEvent(MediaPlayerEvent(
          type: MediaEventType.playbackResumed,
          mediaFile: _currentMedia,
          timestamp: DateTime.now(),
        ));
        
        _startPositionTracking();
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Resume playback');
    }
  }

  // Stop playback
  static Future<void> stopPlayback() async {
    try {
      _playerState = MediaPlayerState.stopped;
      _currentPosition = Duration.zero;
      
      _notifyEvent(MediaPlayerEvent(
        type: MediaEventType.playbackStopped,
        mediaFile: _currentMedia,
        timestamp: DateTime.now(),
      ));
      
      _currentMedia = null;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Stop playback');
    }
  }

  // Seek to position
  static Future<void> seekTo(Duration position) async {
    try {
      if (_currentMedia != null && position <= _totalDuration) {
        _currentPosition = position;
        
        _notifyEvent(MediaPlayerEvent(
          type: MediaEventType.positionChanged,
          mediaFile: _currentMedia,
          position: position,
          timestamp: DateTime.now(),
        ));
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Seek to position');
    }
  }

  // Set volume
  static Future<void> setVolume(double volume) async {
    try {
      _volume = volume.clamp(0.0, 1.0);
      _isMuted = _volume == 0.0;
      
      _notifyEvent(MediaPlayerEvent(
        type: MediaEventType.volumeChanged,
        mediaFile: _currentMedia,
        volume: _volume,
        timestamp: DateTime.now(),
      ));
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Set volume');
    }
  }

  // Toggle mute
  static Future<void> toggleMute() async {
    try {
      _isMuted = !_isMuted;
      
      _notifyEvent(MediaPlayerEvent(
        type: MediaEventType.muteToggled,
        mediaFile: _currentMedia,
        isMuted: _isMuted,
        timestamp: DateTime.now(),
      ));
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Toggle mute');
    }
  }

  // PLAYLIST MANAGEMENT
  
  // Create playlist
  static Future<MediaPlaylist> createPlaylist({
    required String name,
    required List<String> filePaths,
    String? description,
  }) async {
    try {
      final mediaFiles = <MediaFile>[];
      
      for (final filePath in filePaths) {
        final file = File(filePath);
        if (await file.exists()) {
          final mediaFile = await _createMediaFile(file);
          mediaFiles.add(mediaFile);
        }
      }
      
      final playlist = MediaPlaylist(
        id: 'playlist_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        description: description,
        mediaFiles: mediaFiles,
        currentIndex: 0,
        isShuffled: false,
        repeatMode: RepeatMode.none,
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );
      
      return playlist;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Create playlist');
      rethrow;
    }
  }

  // Play next in playlist
  static Future<void> playNext() async {
    try {
      // Implementation for playing next track
      _notifyEvent(MediaPlayerEvent(
        type: MediaEventType.trackChanged,
        mediaFile: _currentMedia,
        timestamp: DateTime.now(),
      ));
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Play next');
    }
  }

  // Play previous in playlist
  static Future<void> playPrevious() async {
    try {
      // Implementation for playing previous track
      _notifyEvent(MediaPlayerEvent(
        type: MediaEventType.trackChanged,
        mediaFile: _currentMedia,
        timestamp: DateTime.now(),
      ));
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Play previous');
    }
  }

  // MEDIA INFORMATION EXTRACTION
  
  // Get media metadata
  static Future<MediaMetadata> getMediaMetadata(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('File not found: $filePath');
      }
      
      // Extract metadata (simplified implementation)
      return MediaMetadata(
        title: _extractTitle(filePath),
        artist: _extractArtist(filePath),
        album: _extractAlbum(filePath),
        duration: await _extractDuration(filePath),
        bitrate: _extractBitrate(filePath),
        sampleRate: _extractSampleRate(filePath),
        format: _extractFormat(filePath),
        fileSize: await file.length(),
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Get media metadata');
      rethrow;
    }
  }

  // HELPER METHODS
  
  static Future<void> _loadMediaLibrary() async {
    try {
      // Load media files from common directories
      final mediaDirs = [
        '/storage/emulated/0/Music',
        '/storage/emulated/0/Movies',
        '/storage/emulated/0/Pictures',
        '/storage/emulated/0/Download',
      ];
      
      _mediaFiles.clear();
      
      for (final dirPath in mediaDirs) {
        final directory = Directory(dirPath);
        if (await directory.exists()) {
          await for (final entity in directory.list(recursive: true)) {
            if (entity is File && _isMediaFile(entity.path)) {
              try {
                final mediaFile = await _createMediaFile(entity);
                _mediaFiles.add(mediaFile);
              } catch (e) {
                // Skip files that can't be processed
              }
            }
          }
        }
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Load media library');
    }
  }

  static Future<MediaFile> _createMediaFile(File file) async {
    final metadata = await getMediaMetadata(file.path);
    
    return MediaFile(
      id: 'media_${DateTime.now().millisecondsSinceEpoch}',
      filePath: file.path,
      fileName: file.path.split('/').last,
      mediaType: _getMediaType(file.path),
      metadata: metadata,
      duration: metadata.duration,
      fileSize: metadata.fileSize,
      lastModified: await file.lastModified(),
    );
  }

  static bool _isMediaFile(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();
    const audioExtensions = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg'];
    const videoExtensions = ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv'];
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    
    return audioExtensions.contains(extension) ||
           videoExtensions.contains(extension) ||
           imageExtensions.contains(extension);
  }

  static MediaType _getMediaType(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();
    const audioExtensions = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg'];
    const videoExtensions = ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv'];
    
    if (audioExtensions.contains(extension)) return MediaType.audio;
    if (videoExtensions.contains(extension)) return MediaType.video;
    return MediaType.image;
  }

  static String _extractTitle(String filePath) {
    // Extract title from filename (simplified)
    final fileName = filePath.split('/').last;
    final nameWithoutExtension = fileName.split('.').first;
    return nameWithoutExtension.replaceAll('_', ' ').replaceAll('-', ' ');
  }

  static String _extractArtist(String filePath) {
    // Extract artist from path or filename (simplified)
    return 'Unknown Artist';
  }

  static String _extractAlbum(String filePath) {
    // Extract album from path or filename (simplified)
    return 'Unknown Album';
  }

  static Future<Duration> _extractDuration(String filePath) async {
    // Extract duration from media file (simplified)
    return const Duration(minutes: 3, seconds: 30);
  }

  static int _extractBitrate(String filePath) {
    // Extract bitrate from media file (simplified)
    return 320000; // 320 kbps
  }

  static int _extractSampleRate(String filePath) {
    // Extract sample rate from media file (simplified)
    return 44100; // 44.1 kHz
  }

  static String _extractFormat(String filePath) {
    return filePath.split('.').last.toUpperCase();
  }

  static void _startPositionTracking() {
    Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_playerState == MediaPlayerState.playing) {
        _currentPosition = Duration(seconds: _currentPosition.inSeconds + 1);
        
        if (_currentPosition >= _totalDuration) {
          _playerState = MediaPlayerState.stopped;
          _currentPosition = Duration.zero;
          timer.cancel();
          
          _notifyEvent(MediaPlayerEvent(
            type: MediaEventType.playbackCompleted,
            mediaFile: _currentMedia,
            timestamp: DateTime.now(),
          ));
        } else {
          _notifyEvent(MediaPlayerEvent(
            type: MediaEventType.positionChanged,
            mediaFile: _currentMedia,
            position: _currentPosition,
            timestamp: DateTime.now(),
          ));
        }
      } else {
        timer.cancel();
      }
    });
  }

  static void _notifyEvent(MediaPlayerEvent event) {
    _eventController.add(event);
  }

  // GETTERS
  static List<MediaFile> get mediaFiles => List.unmodifiable(_mediaFiles);
  static MediaFile? get currentMedia => _currentMedia;
  static MediaPlayerState get playerState => _playerState;
  static Duration get currentPosition => _currentPosition;
  static Duration get totalDuration => _totalDuration;
  static double get volume => _volume;
  static bool get isMuted => _isMuted;
  static PlaybackMode get playbackMode => _playbackMode;
  static Stream<MediaPlayerEvent> get eventStream => _eventController.stream;

  // DISPOSE
  static void dispose() {
    _mediaFiles.clear();
    _currentMedia = null;
    _playerState = MediaPlayerState.stopped;
    _eventController.close();
  }
}
