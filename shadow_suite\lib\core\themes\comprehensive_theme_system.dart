import 'package:flutter/material.dart';
import 'theme_models_base.dart';

class ComprehensiveThemeSystem {
  static final Map<String, ShadowSuiteTheme> _themes = <String, ShadowSuiteTheme>{};
  static String _currentThemeId = 'default_dark';
  
  // Initialize all themes
  static void initialize() {
    _registerDefaultThemes();
  }
  
  // Register all default themes
  static void _registerDefaultThemes() {
    // 1. Default Dark Theme
    _themes['default_dark'] = ShadowSuiteTheme(
      id: 'default_dark',
      name: 'Shadow Dark',
      description: 'Professional dark theme with blue accents',
      category: ThemeCategory.dark,
      colorScheme: _createDarkColorScheme(),
      typography: _createDefaultTypography(),
      customizations: _createDefaultCustomizations(),
    );
    
    // 2. Default Light Theme
    _themes['default_light'] = ShadowSuiteTheme(
      id: 'default_light',
      name: 'Shadow Light',
      description: 'Clean light theme with blue accents',
      category: ThemeCategory.light,
      colorScheme: _createLightColorScheme(),
      typography: _createDefaultTypography(),
      customizations: _createDefaultCustomizations(),
    );
    
    // 3. High Contrast Theme
    _themes['high_contrast'] = ShadowSuiteTheme(
      id: 'high_contrast',
      name: 'High Contrast',
      description: 'Maximum contrast for accessibility',
      category: ThemeCategory.accessibility,
      colorScheme: _createHighContrastColorScheme(),
      typography: _createAccessibleTypography(),
      customizations: _createAccessibleCustomizations(),
    );
    
    // 4. Colorful Theme
    _themes['colorful'] = ShadowSuiteTheme(
      id: 'colorful',
      name: 'Vibrant Colors',
      description: 'Bright and energetic color palette',
      category: ThemeCategory.colorful,
      colorScheme: _createColorfulColorScheme(),
      typography: _createDefaultTypography(),
      customizations: _createColorfulCustomizations(),
    );
    
    // 5. Minimal Theme
    _themes['minimal'] = ShadowSuiteTheme(
      id: 'minimal',
      name: 'Minimal',
      description: 'Clean and simple design',
      category: ThemeCategory.minimal,
      colorScheme: _createMinimalColorScheme(),
      typography: _createMinimalTypography(),
      customizations: _createMinimalCustomizations(),
    );
    
    // 6. Professional Theme
    _themes['professional'] = ShadowSuiteTheme(
      id: 'professional',
      name: 'Professional',
      description: 'Business-focused design',
      category: ThemeCategory.professional,
      colorScheme: _createProfessionalColorScheme(),
      typography: _createProfessionalTypography(),
      customizations: _createProfessionalCustomizations(),
    );
    
    // 7. Islamic Theme
    _themes['islamic'] = ShadowSuiteTheme(
      id: 'islamic',
      name: 'Islamic Green',
      description: 'Traditional Islamic colors',
      category: ThemeCategory.cultural,
      colorScheme: _createIslamicColorScheme(),
      typography: _createDefaultTypography(),
      customizations: _createIslamicCustomizations(),
    );
    
    // 8. Ocean Theme
    _themes['ocean'] = ShadowSuiteTheme(
      id: 'ocean',
      name: 'Ocean Blue',
      description: 'Calming ocean-inspired colors',
      category: ThemeCategory.nature,
      colorScheme: _createOceanColorScheme(),
      typography: _createDefaultTypography(),
      customizations: _createOceanCustomizations(),
    );
    
    // 9. Sunset Theme
    _themes['sunset'] = ShadowSuiteTheme(
      id: 'sunset',
      name: 'Sunset Orange',
      description: 'Warm sunset colors',
      category: ThemeCategory.nature,
      colorScheme: _createSunsetColorScheme(),
      typography: _createDefaultTypography(),
      customizations: _createSunsetCustomizations(),
    );
    
    // 10. Monochrome Theme
    _themes['monochrome'] = ShadowSuiteTheme(
      id: 'monochrome',
      name: 'Monochrome',
      description: 'Black and white only',
      category: ThemeCategory.minimal,
      colorScheme: _createMonochromeColorScheme(),
      typography: _createDefaultTypography(),
      customizations: _createMonochromeCustomizations(),
    );
  }
  
  // Color scheme creators
  static ColorScheme _createDarkColorScheme() {
    return const ColorScheme.dark(
      primary: Color(0xFF2196F3),
      primaryContainer: Color(0xFF1976D2),
      secondary: Color(0xFF03DAC6),
      secondaryContainer: Color(0xFF018786),
      surface: Color(0xFF121212),
      surfaceContainerHighest: Color(0xFF1E1E1E),
      error: Color(0xFFCF6679),
      onPrimary: Color(0xFFFFFFFF),
      onSecondary: Color(0xFF000000),
      onSurface: Color(0xFFFFFFFF),
      onError: Color(0xFF000000),
    );
  }
  
  static ColorScheme _createLightColorScheme() {
    return const ColorScheme.light(
      primary: Color(0xFF1976D2),
      primaryContainer: Color(0xFFBBDEFB),
      secondary: Color(0xFF018786),
      secondaryContainer: Color(0xFFB2DFDB),
      surface: Color(0xFFFFFFFF),
      surfaceContainerHighest: Color(0xFFF5F5F5),
      error: Color(0xFFB00020),
      onPrimary: Color(0xFFFFFFFF),
      onSecondary: Color(0xFFFFFFFF),
      onSurface: Color(0xFF000000),
      onError: Color(0xFFFFFFFF),
    );
  }
  
  static ColorScheme _createHighContrastColorScheme() {
    return const ColorScheme.dark(
      primary: Color(0xFFFFFFFF),
      primaryContainer: Color(0xFF000000),
      secondary: Color(0xFFFFFF00),
      secondaryContainer: Color(0xFF000000),
      surface: Color(0xFF000000),
      surfaceContainerHighest: Color(0xFF333333),
      error: Color(0xFFFF0000),
      onPrimary: Color(0xFF000000),
      onSecondary: Color(0xFF000000),
      onSurface: Color(0xFFFFFFFF),
      onError: Color(0xFFFFFFFF),
    );
  }
  
  static ColorScheme _createColorfulColorScheme() {
    return const ColorScheme.light(
      primary: Color(0xFFE91E63),
      primaryContainer: Color(0xFFF8BBD9),
      secondary: Color(0xFF9C27B0),
      secondaryContainer: Color(0xFFE1BEE7),
      surface: Color(0xFFFFFFFF),
      surfaceContainerHighest: Color(0xFFFFF3E0),
      error: Color(0xFFFF5722),
      onPrimary: Color(0xFFFFFFFF),
      onSecondary: Color(0xFFFFFFFF),
      onSurface: Color(0xFF212121),
      onError: Color(0xFFFFFFFF),
    );
  }
  
  static ColorScheme _createMinimalColorScheme() {
    return const ColorScheme.light(
      primary: Color(0xFF424242),
      primaryContainer: Color(0xFFE0E0E0),
      secondary: Color(0xFF757575),
      secondaryContainer: Color(0xFFEEEEEE),
      surface: Color(0xFFFAFAFA),
      surfaceContainerHighest: Color(0xFFF5F5F5),
      error: Color(0xFF616161),
      onPrimary: Color(0xFFFFFFFF),
      onSecondary: Color(0xFFFFFFFF),
      onSurface: Color(0xFF212121),
      onError: Color(0xFFFFFFFF),
    );
  }
  
  static ColorScheme _createProfessionalColorScheme() {
    return const ColorScheme.light(
      primary: Color(0xFF1565C0),
      primaryContainer: Color(0xFFE3F2FD),
      secondary: Color(0xFF455A64),
      secondaryContainer: Color(0xFFECEFF1),
      surface: Color(0xFFFFFFFF),
      surfaceContainerHighest: Color(0xFFF8F9FA),
      error: Color(0xFFD32F2F),
      onPrimary: Color(0xFFFFFFFF),
      onSecondary: Color(0xFFFFFFFF),
      onSurface: Color(0xFF263238),
      onError: Color(0xFFFFFFFF),
    );
  }
  
  static ColorScheme _createIslamicColorScheme() {
    return const ColorScheme.light(
      primary: Color(0xFF2E7D32),
      primaryContainer: Color(0xFFC8E6C9),
      secondary: Color(0xFF795548),
      secondaryContainer: Color(0xFFD7CCC8),
      surface: Color(0xFFF1F8E9),
      surfaceContainerHighest: Color(0xFFE8F5E8),
      error: Color(0xFFD32F2F),
      onPrimary: Color(0xFFFFFFFF),
      onSecondary: Color(0xFFFFFFFF),
      onSurface: Color(0xFF1B5E20),
      onError: Color(0xFFFFFFFF),
    );
  }
  
  static ColorScheme _createOceanColorScheme() {
    return const ColorScheme.light(
      primary: Color(0xFF0277BD),
      primaryContainer: Color(0xFFB3E5FC),
      secondary: Color(0xFF00695C),
      secondaryContainer: Color(0xFFB2DFDB),
      surface: Color(0xFFE0F2F1),
      surfaceContainerHighest: Color(0xFFB2EBF2),
      error: Color(0xFF00838F),
      onPrimary: Color(0xFFFFFFFF),
      onSecondary: Color(0xFFFFFFFF),
      onSurface: Color(0xFF006064),
      onError: Color(0xFFFFFFFF),
    );
  }
  
  static ColorScheme _createSunsetColorScheme() {
    return const ColorScheme.light(
      primary: Color(0xFFFF6F00),
      primaryContainer: Color(0xFFFFE0B2),
      secondary: Color(0xFFD84315),
      secondaryContainer: Color(0xFFFFCCBC),
      surface: Color(0xFFFFF3E0),
      surfaceContainerHighest: Color(0xFFFFE0B2),
      error: Color(0xFFBF360C),
      onPrimary: Color(0xFFFFFFFF),
      onSecondary: Color(0xFFFFFFFF),
      onSurface: Color(0xFFE65100),
      onError: Color(0xFFFFFFFF),
    );
  }
  
  static ColorScheme _createMonochromeColorScheme() {
    return const ColorScheme.light(
      primary: Color(0xFF000000),
      primaryContainer: Color(0xFFE0E0E0),
      secondary: Color(0xFF424242),
      secondaryContainer: Color(0xFFBDBDBD),
      surface: Color(0xFFFFFFFF),
      surfaceContainerHighest: Color(0xFFF5F5F5),
      error: Color(0xFF000000),
      onPrimary: Color(0xFFFFFFFF),
      onSecondary: Color(0xFFFFFFFF),
      onSurface: Color(0xFF000000),
      onError: Color(0xFFFFFFFF),
    );
  }
  
  // Typography creators
  static TextTheme _createDefaultTypography() {
    return const TextTheme(
      displayLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.w300),
      displayMedium: TextStyle(fontSize: 28, fontWeight: FontWeight.w400),
      displaySmall: TextStyle(fontSize: 24, fontWeight: FontWeight.w400),
      headlineLarge: TextStyle(fontSize: 22, fontWeight: FontWeight.w500),
      headlineMedium: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
      headlineSmall: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
      titleLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      titleMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
      titleSmall: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
      bodyLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.w400),
      bodyMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
      bodySmall: TextStyle(fontSize: 12, fontWeight: FontWeight.w400),
      labelLarge: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
      labelMedium: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
      labelSmall: TextStyle(fontSize: 10, fontWeight: FontWeight.w500),
    );
  }
  
  static TextTheme _createAccessibleTypography() {
    return const TextTheme(
      displayLarge: TextStyle(fontSize: 36, fontWeight: FontWeight.w700),
      displayMedium: TextStyle(fontSize: 32, fontWeight: FontWeight.w700),
      displaySmall: TextStyle(fontSize: 28, fontWeight: FontWeight.w700),
      headlineLarge: TextStyle(fontSize: 26, fontWeight: FontWeight.w600),
      headlineMedium: TextStyle(fontSize: 24, fontWeight: FontWeight.w600),
      headlineSmall: TextStyle(fontSize: 22, fontWeight: FontWeight.w600),
      titleLarge: TextStyle(fontSize: 20, fontWeight: FontWeight.w700),
      titleMedium: TextStyle(fontSize: 18, fontWeight: FontWeight.w700),
      titleSmall: TextStyle(fontSize: 16, fontWeight: FontWeight.w700),
      bodyLarge: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
      bodyMedium: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
      bodySmall: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
      labelLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      labelMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
      labelSmall: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
    );
  }
  
  static TextTheme _createMinimalTypography() {
    return const TextTheme(
      displayLarge: TextStyle(fontSize: 28, fontWeight: FontWeight.w200),
      displayMedium: TextStyle(fontSize: 24, fontWeight: FontWeight.w200),
      displaySmall: TextStyle(fontSize: 20, fontWeight: FontWeight.w300),
      headlineLarge: TextStyle(fontSize: 18, fontWeight: FontWeight.w300),
      headlineMedium: TextStyle(fontSize: 16, fontWeight: FontWeight.w300),
      headlineSmall: TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
      titleLarge: TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
      titleMedium: TextStyle(fontSize: 12, fontWeight: FontWeight.w400),
      titleSmall: TextStyle(fontSize: 10, fontWeight: FontWeight.w500),
      bodyLarge: TextStyle(fontSize: 14, fontWeight: FontWeight.w300),
      bodyMedium: TextStyle(fontSize: 12, fontWeight: FontWeight.w300),
      bodySmall: TextStyle(fontSize: 10, fontWeight: FontWeight.w300),
      labelLarge: TextStyle(fontSize: 12, fontWeight: FontWeight.w400),
      labelMedium: TextStyle(fontSize: 10, fontWeight: FontWeight.w400),
      labelSmall: TextStyle(fontSize: 8, fontWeight: FontWeight.w400),
    );
  }
  
  static TextTheme _createProfessionalTypography() {
    return const TextTheme(
      displayLarge: TextStyle(fontSize: 30, fontWeight: FontWeight.w600, fontFamily: 'Roboto'),
      displayMedium: TextStyle(fontSize: 26, fontWeight: FontWeight.w600, fontFamily: 'Roboto'),
      displaySmall: TextStyle(fontSize: 22, fontWeight: FontWeight.w500, fontFamily: 'Roboto'),
      headlineLarge: TextStyle(fontSize: 20, fontWeight: FontWeight.w500, fontFamily: 'Roboto'),
      headlineMedium: TextStyle(fontSize: 18, fontWeight: FontWeight.w500, fontFamily: 'Roboto'),
      headlineSmall: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, fontFamily: 'Roboto'),
      titleLarge: TextStyle(fontSize: 15, fontWeight: FontWeight.w600, fontFamily: 'Roboto'),
      titleMedium: TextStyle(fontSize: 13, fontWeight: FontWeight.w600, fontFamily: 'Roboto'),
      titleSmall: TextStyle(fontSize: 11, fontWeight: FontWeight.w600, fontFamily: 'Roboto'),
      bodyLarge: TextStyle(fontSize: 15, fontWeight: FontWeight.w400, fontFamily: 'Roboto'),
      bodyMedium: TextStyle(fontSize: 13, fontWeight: FontWeight.w400, fontFamily: 'Roboto'),
      bodySmall: TextStyle(fontSize: 11, fontWeight: FontWeight.w400, fontFamily: 'Roboto'),
      labelLarge: TextStyle(fontSize: 13, fontWeight: FontWeight.w500, fontFamily: 'Roboto'),
      labelMedium: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, fontFamily: 'Roboto'),
      labelSmall: TextStyle(fontSize: 9, fontWeight: FontWeight.w500, fontFamily: 'Roboto'),
    );
  }
  
  // Customization creators
  static ThemeCustomizations _createDefaultCustomizations() {
    return ThemeCustomizations(
      borderRadius: 8.0,
      elevation: 4.0,
      animationDuration: const Duration(milliseconds: 200),
      spacing: 16.0,
      iconSize: 24.0,
      buttonHeight: 48.0,
      enableAnimations: true,
      enableHapticFeedback: true,
      enableSounds: false,
      padding: const EdgeInsets.all(16.0),
    );
  }
  
  static ThemeCustomizations _createAccessibleCustomizations() {
    return ThemeCustomizations(
      borderRadius: 4.0,
      elevation: 8.0,
      animationDuration: const Duration(milliseconds: 400),
      spacing: 24.0,
      iconSize: 32.0,
      buttonHeight: 56.0,
      enableAnimations: false,
      enableHapticFeedback: true,
      enableSounds: true,
      padding: const EdgeInsets.all(24.0),
    );
  }
  
  static ThemeCustomizations _createColorfulCustomizations() {
    return ThemeCustomizations(
      borderRadius: 16.0,
      elevation: 6.0,
      animationDuration: const Duration(milliseconds: 300),
      spacing: 20.0,
      iconSize: 28.0,
      buttonHeight: 52.0,
      enableAnimations: true,
      enableHapticFeedback: true,
      enableSounds: false,
      padding: const EdgeInsets.all(20.0),
    );
  }
  
  static ThemeCustomizations _createMinimalCustomizations() {
    return ThemeCustomizations(
      borderRadius: 2.0,
      elevation: 1.0,
      animationDuration: const Duration(milliseconds: 150),
      spacing: 12.0,
      iconSize: 20.0,
      buttonHeight: 40.0,
      enableAnimations: true,
      enableHapticFeedback: false,
      enableSounds: false,
      padding: const EdgeInsets.all(12.0),
    );
  }

  static ThemeCustomizations _createProfessionalCustomizations() {
    return ThemeCustomizations(
      borderRadius: 4.0,
      elevation: 2.0,
      animationDuration: const Duration(milliseconds: 200),
      spacing: 16.0,
      iconSize: 22.0,
      buttonHeight: 44.0,
      enableAnimations: true,
      enableHapticFeedback: false,
      enableSounds: false,
      padding: const EdgeInsets.all(16.0),
    );
  }
  
  static ThemeCustomizations _createIslamicCustomizations() {
    return ThemeCustomizations(
      borderRadius: 12.0,
      elevation: 3.0,
      animationDuration: const Duration(milliseconds: 250),
      spacing: 18.0,
      iconSize: 26.0,
      buttonHeight: 50.0,
      enableAnimations: true,
      enableHapticFeedback: true,
      enableSounds: false,
      padding: const EdgeInsets.all(18.0),
    );
  }

  static ThemeCustomizations _createOceanCustomizations() {
    return ThemeCustomizations(
      borderRadius: 20.0,
      elevation: 5.0,
      animationDuration: const Duration(milliseconds: 350),
      spacing: 20.0,
      iconSize: 26.0,
      buttonHeight: 50.0,
      enableAnimations: true,
      enableHapticFeedback: true,
      enableSounds: false,
      padding: const EdgeInsets.all(20.0),
    );
  }

  static ThemeCustomizations _createSunsetCustomizations() {
    return ThemeCustomizations(
      borderRadius: 14.0,
      elevation: 4.0,
      animationDuration: const Duration(milliseconds: 280),
      spacing: 18.0,
      iconSize: 25.0,
      buttonHeight: 48.0,
      enableAnimations: true,
      enableHapticFeedback: true,
      enableSounds: false,
      padding: const EdgeInsets.all(18.0),
    );
  }

  static ThemeCustomizations _createMonochromeCustomizations() {
    return ThemeCustomizations(
      borderRadius: 0.0,
      elevation: 0.0,
      animationDuration: const Duration(milliseconds: 100),
      spacing: 16.0,
      iconSize: 24.0,
      buttonHeight: 48.0,
      enableAnimations: false,
      enableHapticFeedback: false,
      enableSounds: false,
      padding: const EdgeInsets.all(16.0),
    );
  }
  
  // Theme management
  static ShadowSuiteTheme get currentTheme => _themes[_currentThemeId]!;
  static List<ShadowSuiteTheme> get availableThemes => _themes.values.toList();
  
  static void setTheme(String themeId) {
    if (_themes.containsKey(themeId)) {
      _currentThemeId = themeId;
    }
  }
  
  static void registerCustomTheme(ShadowSuiteTheme theme) {
    _themes[theme.id] = theme;
  }
  
  static ThemeData buildFlutterTheme(ShadowSuiteTheme theme) {
    return ThemeData(
      colorScheme: theme.colorScheme,
      textTheme: theme.typography,
      useMaterial3: true,
      visualDensity: VisualDensity.adaptivePlatformDensity,
    );
  }
}
