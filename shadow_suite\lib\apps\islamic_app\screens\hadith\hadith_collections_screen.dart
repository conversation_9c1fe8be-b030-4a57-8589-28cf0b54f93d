import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/islamic_providers.dart';

class HadithCollectionsScreen extends ConsumerWidget {
  const HadithCollectionsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text('Hadith Collections'),
        backgroundColor: const Color(0xFF27AE60),
        foregroundColor: Colors.white,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            onPressed: () {
              ref.read(islamicAppCurrentScreenProvider.notifier).state =
                  IslamicAppScreen.dashboard;
            },
            icon: const Icon(Icons.home),
            tooltip: 'Back to Dashboard',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: 32),
            _buildCollectionsList(context, ref),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF27AE60),
            const Color(0xFF27AE60).withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Hadith Collections',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Authentic sayings and teachings of Prophet Muhammad (ﷺ)',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
          const Icon(Icons.library_books, size: 64, color: Colors.white),
        ],
      ),
    );
  }

  Widget _buildCollectionsList(BuildContext context, WidgetRef ref) {
    final collections = [
      HadithCollection(
        id: 'bukhari',
        name: 'Sahih al-Bukhari',
        arabicName: 'صحيح البخاري',
        description: 'The most authentic collection of Hadith',
        totalHadith: 7563,
        compiler: 'Imam al-Bukhari',
        icon: Icons.star,
        color: const Color(0xFF27AE60),
      ),
      HadithCollection(
        id: 'muslim',
        name: 'Sahih Muslim',
        arabicName: 'صحيح مسلم',
        description: 'Second most authentic collection',
        totalHadith: 7190,
        compiler: 'Imam Muslim',
        icon: Icons.verified,
        color: const Color(0xFF3498DB),
      ),
      HadithCollection(
        id: 'abudawud',
        name: 'Sunan Abu Dawud',
        arabicName: 'سنن أبي داود',
        description: 'Collection focusing on legal matters',
        totalHadith: 5274,
        compiler: 'Abu Dawud',
        icon: Icons.gavel,
        color: const Color(0xFF9B59B6),
      ),
      HadithCollection(
        id: 'tirmidhi',
        name: 'Jami\' at-Tirmidhi',
        arabicName: 'جامع الترمذي',
        description: 'Collection with detailed commentary',
        totalHadith: 3956,
        compiler: 'At-Tirmidhi',
        icon: Icons.comment,
        color: const Color(0xFFE67E22),
      ),
      HadithCollection(
        id: 'nasai',
        name: 'Sunan an-Nasa\'i',
        arabicName: 'سنن النسائي',
        description: 'Collection emphasizing authenticity',
        totalHadith: 5761,
        compiler: 'An-Nasa\'i',
        icon: Icons.check_circle,
        color: const Color(0xFF1ABC9C),
      ),
      HadithCollection(
        id: 'ibnmajah',
        name: 'Sunan Ibn Majah',
        arabicName: 'سنن ابن ماجه',
        description: 'Collection completing the six major books',
        totalHadith: 4341,
        compiler: 'Ibn Majah',
        icon: Icons.book,
        color: const Color(0xFFE74C3C),
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Major Collections (Kutub as-Sittah)',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'The six most authentic collections of Hadith in Islamic literature',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: const Color(0xFF7F8C8D)),
        ),
        const SizedBox(height: 24),
        ...collections.map(
          (collection) => _buildCollectionCard(context, ref, collection),
        ),
      ],
    );
  }

  Widget _buildCollectionCard(
    BuildContext context,
    WidgetRef ref,
    HadithCollection collection,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          // Navigate to hadith reading screen
          ref.read(islamicAppCurrentScreenProvider.notifier).state =
              IslamicAppScreen.hadithReading;
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: collection.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(collection.icon, color: collection.color, size: 32),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      collection.name,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF2C3E50),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      collection.arabicName,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: collection.color,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      collection.description,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: const Color(0xFF7F8C8D),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(Icons.person, size: 16, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                        Text(
                          collection.compiler,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: Colors.grey[600]),
                        ),
                        const SizedBox(width: 16),
                        Icon(
                          Icons.format_list_numbered,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${collection.totalHadith} Hadith',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, color: Colors.grey[400], size: 16),
            ],
          ),
        ),
      ),
    );
  }
}

class HadithCollection {
  final String id;
  final String name;
  final String arabicName;
  final String description;
  final int totalHadith;
  final String compiler;
  final IconData icon;
  final Color color;

  const HadithCollection({
    required this.id,
    required this.name,
    required this.arabicName,
    required this.description,
    required this.totalHadith,
    required this.compiler,
    required this.icon,
    required this.color,
  });
}
