import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/advanced_settings_service.dart';
import '../widgets/unified_components.dart';

/// Advanced settings screen with 100+ customization options
class AdvancedSettingsScreen extends ConsumerStatefulWidget {
  const AdvancedSettingsScreen({super.key});

  @override
  ConsumerState<AdvancedSettingsScreen> createState() =>
      _AdvancedSettingsScreenState();
}

class _AdvancedSettingsScreenState extends ConsumerState<AdvancedSettingsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _showAdvanced = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _initializeSettings();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeSettings() async {
    await AdvancedSettingsService.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Advanced Settings',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        backgroundColor: const Color(0xFF2C3E50),
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(
              _showAdvanced ? Icons.visibility_off : Icons.visibility,
              color: Colors.white,
            ),
            onPressed: () {
              setState(() {
                _showAdvanced = !_showAdvanced;
              });
            },
            tooltip: _showAdvanced ? 'Hide Advanced' : 'Show Advanced',
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _resetToDefaults,
            tooltip: 'Reset to Defaults',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.upload),
                    SizedBox(width: 8),
                    Text('Export Profile'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'import',
                child: Row(
                  children: [
                    Icon(Icons.download),
                    SizedBox(width: 8),
                    Text('Import Profile'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'reset_all',
                child: Row(
                  children: [
                    Icon(Icons.restore),
                    SizedBox(width: 8),
                    Text('Reset All'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.palette), text: 'UI/UX'),
            Tab(icon: Icon(Icons.view_quilt), text: 'Layout'),
            Tab(icon: Icon(Icons.speed), text: 'Performance'),
            Tab(icon: Icon(Icons.accessibility), text: 'Accessibility'),
            Tab(icon: Icon(Icons.security), text: 'Privacy'),
            Tab(icon: Icon(Icons.keyboard), text: 'Shortcuts'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildUIUXSettings(),
          _buildLayoutSettings(),
          _buildPerformanceSettings(),
          _buildAccessibilitySettings(),
          _buildPrivacySettings(),
          _buildShortcutsSettings(),
        ],
      ),
    );
  }

  Widget _buildUIUXSettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Theme & Colors'),
          _buildColorSetting('Primary Color', 'ui', 'primary_color'),
          _buildColorSetting('Accent Color', 'ui', 'accent_color'),
          _buildSwitchSetting('Dark Mode', 'ui', 'dark_mode'),

          const SizedBox(height: 24),
          _buildSectionHeader('Typography'),
          _buildDropdownSetting('Font Family', 'ui', 'font_family', [
            'Roboto',
            'Open Sans',
            'Lato',
            'Montserrat',
            'Source Sans Pro',
          ]),
          _buildSliderSetting(
            'Font Size Scale',
            'ui',
            'font_size_scale',
            0.8,
            2.0,
          ),

          const SizedBox(height: 24),
          _buildSectionHeader('Visual Effects'),
          _buildSliderSetting(
            'Border Radius',
            'ui',
            'border_radius',
            0.0,
            20.0,
          ),
          _buildSliderSetting('Elevation', 'ui', 'elevation', 0.0, 24.0),
          _buildSliderSetting(
            'Animation Duration (ms)',
            'ui',
            'animation_duration',
            100.0,
            1000.0,
          ),
          _buildSwitchSetting('Enable Animations', 'ui', 'enable_animations'),

          const SizedBox(height: 24),
          _buildSectionHeader('Layout'),
          _buildSliderSetting(
            'Sidebar Width',
            'ui',
            'sidebar_width',
            200.0,
            400.0,
          ),
        ],
      ),
    );
  }

  Widget _buildLayoutSettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Grid & Lists'),
          _buildSliderSetting(
            'Grid Columns',
            'layout',
            'grid_columns',
            1.0,
            6.0,
          ),
          _buildSliderSetting(
            'List Item Height',
            'layout',
            'list_item_height',
            48.0,
            120.0,
          ),
          _buildSwitchSetting('Compact Mode', 'layout', 'compact_mode'),
          _buildSwitchSetting('Show Thumbnails', 'layout', 'show_thumbnails'),

          const SizedBox(height: 24),
          _buildSectionHeader('Navigation'),
          _buildDropdownSetting('Tab Position', 'layout', 'tab_position', [
            'top',
            'bottom',
            'left',
            'right',
          ]),
        ],
      ),
    );
  }

  Widget _buildPerformanceSettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Memory & Cache'),
          _buildSliderSetting(
            'Cache Size (MB)',
            'performance',
            'cache_size_mb',
            50.0,
            1000.0,
          ),
          _buildSliderSetting(
            'Memory Limit (MB)',
            'performance',
            'memory_limit_mb',
            256.0,
            2048.0,
          ),
          _buildSliderSetting(
            'Auto Cleanup (hours)',
            'performance',
            'auto_cleanup_interval',
            1.0,
            168.0,
          ),

          const SizedBox(height: 24),
          _buildSectionHeader('Processing'),
          _buildSliderSetting(
            'Max Concurrent Operations',
            'performance',
            'max_concurrent_operations',
            1.0,
            16.0,
          ),
          _buildSwitchSetting(
            'Hardware Acceleration',
            'performance',
            'enable_hardware_acceleration',
          ),
        ],
      ),
    );
  }

  Widget _buildAccessibilitySettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Visual Accessibility'),
          _buildSwitchSetting(
            'High Contrast',
            'accessibility',
            'high_contrast',
          ),
          _buildSwitchSetting('Large Text', 'accessibility', 'large_text'),
          _buildSwitchSetting(
            'Reduce Motion',
            'accessibility',
            'reduce_motion',
          ),
          _buildSwitchSetting(
            'Focus Indicators',
            'accessibility',
            'focus_indicators',
          ),

          const SizedBox(height: 24),
          _buildSectionHeader('Screen Reader'),
          _buildSwitchSetting(
            'Screen Reader Support',
            'accessibility',
            'screen_reader_support',
          ),
        ],
      ),
    );
  }

  Widget _buildPrivacySettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Data & Analytics'),
          _buildSwitchSetting(
            'Enable Analytics',
            'privacy',
            'enable_analytics',
          ),
          _buildSwitchSetting(
            'Encrypt Local Data',
            'privacy',
            'encrypt_local_data',
          ),
          _buildSwitchSetting(
            'Clear Cache on Exit',
            'privacy',
            'clear_cache_on_exit',
          ),

          const SizedBox(height: 24),
          _buildSectionHeader('Security'),
          _buildSliderSetting(
            'Auto Lock Timeout (sec)',
            'privacy',
            'auto_lock_timeout',
            60.0,
            3600.0,
          ),
          _buildSwitchSetting(
            'Require Auth for Sensitive',
            'privacy',
            'require_auth_for_sensitive',
          ),
        ],
      ),
    );
  }

  Widget _buildShortcutsSettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Keyboard Shortcuts'),
          _buildShortcutItem('New Document', 'Ctrl+N'),
          _buildShortcutItem('Open File', 'Ctrl+O'),
          _buildShortcutItem('Save', 'Ctrl+S'),
          _buildShortcutItem('Search', 'Ctrl+F'),
          _buildShortcutItem('Settings', 'Ctrl+,'),
          _buildShortcutItem('Toggle Sidebar', 'Ctrl+B'),
          _buildShortcutItem('Quick Actions', 'Ctrl+K'),
          _buildShortcutItem('Switch Tab', 'Ctrl+Tab'),
          _buildShortcutItem('Close Tab', 'Ctrl+W'),
          _buildShortcutItem('Refresh', 'F5'),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Color(0xFF2C3E50),
        ),
      ),
    );
  }

  Widget _buildSwitchSetting(String title, String category, String key) {
    final value = AdvancedSettingsService.getSetting(category, key, false);

    return UnifiedCard(
      margin: const EdgeInsets.only(bottom: 8),
      child: SwitchListTile(
        title: Text(title),
        value: value,
        onChanged: (newValue) {
          AdvancedSettingsService.setSetting(category, key, newValue);
          setState(() {});
        },
      ),
    );
  }

  Widget _buildSliderSetting(
    String title,
    String category,
    String key,
    double min,
    double max,
  ) {
    final value = AdvancedSettingsService.getSetting(
      category,
      key,
      min,
    ).toDouble();

    return UnifiedCard(
      margin: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListTile(
            title: Text(title),
            trailing: Text(
              value.toStringAsFixed(key.contains('duration') ? 0 : 1),
            ),
          ),
          Slider(
            value: value,
            min: min,
            max: max,
            divisions: key.contains('duration') ? null : 20,
            onChanged: (newValue) {
              AdvancedSettingsService.setSetting(
                category,
                key,
                key.contains('duration') ? newValue.round() : newValue,
              );
              setState(() {});
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDropdownSetting(
    String title,
    String category,
    String key,
    List<String> options,
  ) {
    final value = AdvancedSettingsService.getSetting(
      category,
      key,
      options.first,
    );

    return UnifiedCard(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: Text(title),
        trailing: DropdownButton<String>(
          value: value,
          items: options
              .map(
                (option) =>
                    DropdownMenuItem(value: option, child: Text(option)),
              )
              .toList(),
          onChanged: (newValue) {
            if (newValue != null) {
              AdvancedSettingsService.setSetting(category, key, newValue);
              setState(() {});
            }
          },
        ),
      ),
    );
  }

  Widget _buildColorSetting(String title, String category, String key) {
    final colorValue = AdvancedSettingsService.getSetting(
      category,
      key,
      '0xFF2C3E50',
    );
    final color = Color(int.parse(colorValue.toString()));

    return UnifiedCard(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: Text(title),
        trailing: GestureDetector(
          onTap: () => _showColorPicker(title, category, key, color),
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildShortcutItem(String action, String shortcut) {
    return UnifiedCard(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: Text(action),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(6),
          ),
          child: Text(
            shortcut,
            style: const TextStyle(
              fontFamily: 'monospace',
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        onTap: () => _editShortcut(action, shortcut),
      ),
    );
  }

  void _showColorPicker(
    String title,
    String category,
    String key,
    Color currentColor,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Select $title'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: currentColor,
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Color picker functionality will be implemented with a color picker package',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  void _editShortcut(String action, String shortcut) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit Shortcut: $action'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Current shortcut: $shortcut'),
            const SizedBox(height: 16),
            const TextField(
              decoration: InputDecoration(
                labelText: 'New shortcut',
                hintText: 'Press keys to record shortcut',
                border: OutlineInputBorder(),
              ),
              readOnly: true,
            ),
            const SizedBox(height: 8),
            const Text(
              'Shortcut recording functionality will be implemented with a keyboard listener package',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _resetToDefaults() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text(
          'Are you sure you want to reset all settings to their default values?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          UnifiedButton(
            text: 'Reset',
            onPressed: () async {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              await AdvancedSettingsService.resetToDefaults();
              setState(() {});
              if (mounted) {
                navigator.pop();
                scaffoldMessenger.showSnackBar(
                  const SnackBar(content: Text('Settings reset to defaults')),
                );
              }
            },
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportProfile();
        break;
      case 'import':
        _importProfile();
        break;
      case 'reset_all':
        _resetToDefaults();
        break;
    }
  }

  void _exportProfile() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Profile'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.download, size: 64, color: Colors.blue),
            SizedBox(height: 16),
            Text(
              'Profile export functionality will save all your settings to a file.',
            ),
            SizedBox(height: 8),
            Text(
              'This feature requires file system access and will be implemented with file picker packages.',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _importProfile() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import Profile'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.upload, size: 64, color: Colors.green),
            SizedBox(height: 16),
            Text(
              'Profile import functionality will load settings from a file.',
            ),
            SizedBox(height: 8),
            Text(
              'This feature requires file system access and will be implemented with file picker packages.',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
