import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/media_models.dart';

/// Provider for the media library
final mediaLibraryProvider =
    StateNotifierProvider<MediaLibraryNotifier, List<MediaFile>>((ref) {
      return MediaLibraryNotifier();
    });

/// Provider for loading state
final mediaLibraryLoadingProvider = StateProvider<bool>((ref) => false);

/// Provider for audio files only
final audioFilesProvider = Provider<List<MediaFile>>((ref) {
  final allFiles = ref.watch(mediaLibraryProvider);
  return allFiles.where((file) => file.type == MediaType.audio).toList();
});

/// Provider for video files only
final videoFilesProvider = Provider<List<MediaFile>>((ref) {
  final allFiles = ref.watch(mediaLibraryProvider);
  return allFiles.where((file) => file.type == MediaType.video).toList();
});

/// Provider for currently playing media
final currentPlayingMediaProvider = StateProvider<MediaFile?>((ref) => null);

/// Provider for playback state
final playbackStateProvider = StateProvider<PlaybackState>(
  (ref) => PlaybackState.stopped,
);

/// Provider for current position
final currentPositionProvider = StateProvider<Duration>((ref) => Duration.zero);

/// Provider for total duration
final totalDurationProvider = StateProvider<Duration>((ref) => Duration.zero);

/// Provider for shuffle mode
final shuffleModeProvider = StateProvider<bool>((ref) => false);

/// Provider for repeat mode
final repeatModeProvider = StateProvider<RepeatMode>((ref) => RepeatMode.none);

/// Provider for volume
final volumeProvider = StateProvider<double>((ref) => 0.7);

/// Provider for playlists
final playlistsProvider =
    StateNotifierProvider<PlaylistsNotifier, List<Playlist>>((ref) {
      return PlaylistsNotifier();
    });

/// Provider for player settings
final playerSettingsProvider =
    StateNotifierProvider<PlayerSettingsNotifier, PlayerSettings>((ref) {
      return PlayerSettingsNotifier();
    });

/// Media library notifier
class MediaLibraryNotifier extends StateNotifier<List<MediaFile>> {
  MediaLibraryNotifier() : super([]) {
    _loadMockData();
  }

  void _loadMockData() {
    // Mock data for demonstration
    state = [
      MediaFile(
        id: '1',
        name: 'Beautiful Song',
        path: '/storage/music/song1.mp3',
        displayName: 'Beautiful Song',
        type: MediaType.audio,
        size: 5242880, // 5MB
        dateAdded: DateTime.now().subtract(const Duration(days: 1)),
        dateModified: DateTime.now().subtract(const Duration(days: 1)),
        duration: const Duration(minutes: 3, seconds: 45),
        metadata: MediaMetadata(
          title: 'Beautiful Song',
          artist: 'Amazing Artist',
          album: 'Great Album',
          year: 2023,
        ),
      ),
      MediaFile(
        id: '2',
        name: 'Another Track',
        path: '/storage/music/song2.mp3',
        displayName: 'Another Track',
        type: MediaType.audio,
        size: 4194304, // 4MB
        dateAdded: DateTime.now().subtract(const Duration(days: 2)),
        dateModified: DateTime.now().subtract(const Duration(days: 2)),
        duration: const Duration(minutes: 4, seconds: 12),
        metadata: MediaMetadata(
          title: 'Another Track',
          artist: 'Cool Artist',
          album: 'Awesome Album',
          year: 2023,
        ),
      ),
      MediaFile(
        id: '3',
        name: 'Sample Video',
        path: '/storage/videos/movie1.mp4',
        displayName: 'Sample Video',
        type: MediaType.video,
        size: 104857600, // 100MB
        dateAdded: DateTime.now().subtract(const Duration(days: 3)),
        dateModified: DateTime.now().subtract(const Duration(days: 3)),
        duration: const Duration(minutes: 15, seconds: 30),
        metadata: MediaMetadata(title: 'Sample Video', artist: 'Video Creator'),
      ),
    ];
  }

  void scanForMedia() {
    // Simulate scanning for media files
    // In a real app, this would scan the file system
    _loadMockData();
  }

  void addMediaFile(MediaFile file) {
    state = [...state, file];
  }

  void removeMediaFile(String id) {
    state = state.where((file) => file.id != id).toList();
  }

  void updateMediaFile(MediaFile updatedFile) {
    state = state
        .map((file) => file.id == updatedFile.id ? updatedFile : file)
        .toList();
  }

  void clearLibrary() {
    state = [];
  }
}

/// Playback states
enum PlaybackState { stopped, playing, paused, loading }

/// Repeat modes
enum RepeatMode { none, all, one }

/// Playlists notifier
class PlaylistsNotifier extends StateNotifier<List<Playlist>> {
  PlaylistsNotifier() : super([]) {
    _loadMockPlaylists();
  }

  void _loadMockPlaylists() {
    state = [
      Playlist(
        id: '1',
        name: 'My Favorites',
        description: 'My favorite songs',
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
        lastModified: DateTime.now().subtract(const Duration(days: 7)),
        mediaIds: ['1', '2'],
      ),
      Playlist(
        id: '2',
        name: 'Workout Mix',
        description: 'High energy songs for workouts',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        lastModified: DateTime.now().subtract(const Duration(days: 3)),
        mediaIds: ['2'],
      ),
    ];
  }

  void createPlaylist(String name, String description) {
    final now = DateTime.now();
    final newPlaylist = Playlist(
      id: now.millisecondsSinceEpoch.toString(),
      name: name,
      description: description,
      createdAt: now,
      lastModified: now,
      mediaIds: [],
    );
    state = [...state, newPlaylist];
  }

  void updatePlaylist(String id, String name, String description) {
    state = state.map((playlist) {
      if (playlist.id == id) {
        return playlist.copyWith(name: name, description: description);
      }
      return playlist;
    }).toList();
  }

  void deletePlaylist(String id) {
    state = state.where((playlist) => playlist.id != id).toList();
  }

  void addTrackToPlaylist(String playlistId, String trackId) {
    state = state.map((playlist) {
      if (playlist.id == playlistId) {
        final updatedMediaIds = [...playlist.mediaIds, trackId];
        return playlist.copyWith(
          mediaIds: updatedMediaIds,
          lastModified: DateTime.now(),
        );
      }
      return playlist;
    }).toList();
  }

  void removeTrackFromPlaylist(String playlistId, String trackId) {
    state = state.map((playlist) {
      if (playlist.id == playlistId) {
        final updatedMediaIds = playlist.mediaIds
            .where((id) => id != trackId)
            .toList();
        return playlist.copyWith(
          mediaIds: updatedMediaIds,
          lastModified: DateTime.now(),
        );
      }
      return playlist;
    }).toList();
  }
}

/// Player settings model
class PlayerSettings {
  final bool autoPlayNext;
  final bool shuffleByDefault;
  final bool repeatByDefault;
  final double defaultVolume;
  final bool showThumbnails;
  final bool showFileExtensions;
  final String theme; // 'system', 'light', 'dark'

  const PlayerSettings({
    this.autoPlayNext = true,
    this.shuffleByDefault = false,
    this.repeatByDefault = false,
    this.defaultVolume = 0.7,
    this.showThumbnails = true,
    this.showFileExtensions = false,
    this.theme = 'system',
  });

  PlayerSettings copyWith({
    bool? autoPlayNext,
    bool? shuffleByDefault,
    bool? repeatByDefault,
    double? defaultVolume,
    bool? showThumbnails,
    bool? showFileExtensions,
    String? theme,
  }) {
    return PlayerSettings(
      autoPlayNext: autoPlayNext ?? this.autoPlayNext,
      shuffleByDefault: shuffleByDefault ?? this.shuffleByDefault,
      repeatByDefault: repeatByDefault ?? this.repeatByDefault,
      defaultVolume: defaultVolume ?? this.defaultVolume,
      showThumbnails: showThumbnails ?? this.showThumbnails,
      showFileExtensions: showFileExtensions ?? this.showFileExtensions,
      theme: theme ?? this.theme,
    );
  }
}

/// Player settings notifier
class PlayerSettingsNotifier extends StateNotifier<PlayerSettings> {
  PlayerSettingsNotifier() : super(const PlayerSettings());

  void updateAutoPlayNext(bool value) {
    state = state.copyWith(autoPlayNext: value);
  }

  void updateShuffleByDefault(bool value) {
    state = state.copyWith(shuffleByDefault: value);
  }

  void updateRepeatByDefault(bool value) {
    state = state.copyWith(repeatByDefault: value);
  }

  void updateDefaultVolume(double value) {
    state = state.copyWith(defaultVolume: value);
  }

  void updateShowThumbnails(bool value) {
    state = state.copyWith(showThumbnails: value);
  }

  void updateShowFileExtensions(bool value) {
    state = state.copyWith(showFileExtensions: value);
  }

  void updateTheme(String theme) {
    state = state.copyWith(theme: theme);
  }

  void resetToDefaults() {
    state = const PlayerSettings();
  }
}
