import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/excel_app_tool.dart';
import '../services/excel_app_providers.dart';
import 'cell_binding_picker.dart';

class ExcelUIBuilder extends ConsumerStatefulWidget {
  final ExcelAppTool tool;

  const ExcelUIBuilder({
    super.key,
    required this.tool,
  });

  @override
  ConsumerState<ExcelUIBuilder> createState() => _ExcelUIBuilderState();
}

class _ExcelUIBuilderState extends ConsumerState<ExcelUIBuilder> {
  UIComponent? _selectedComponent;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        _buildComponentPalette(),
        Expanded(
          child: _buildCanvas(),
        ),
        _buildPropertiesPanel(),
      ],
    );
  }

  Widget _buildComponentPalette() {
    return Container(
      width: 250,
      decoration: const BoxDecoration(
        color: Color(0xFFF8F9FA),
        border: Border(
          right: BorderSide(color: Color(0xFFE9ECEF)),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Color(0xFFE9ECEF)),
              ),
            ),
            child: const Text(
              'Component Palette',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2C3E50),
              ),
            ),
          ),
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                _buildComponentCategory('Input Components', [
                  _buildComponentItem('Text Field', Icons.text_fields, ComponentType.textInput),
                  _buildComponentItem('Number Field', Icons.numbers, ComponentType.numberInput),
                  _buildComponentItem('Dropdown', Icons.arrow_drop_down, ComponentType.dropdown),
                  _buildComponentItem('Checkbox', Icons.check_box, ComponentType.label),
                  _buildComponentItem('Radio Button', Icons.radio_button_checked, ComponentType.label),
                ]),
                const SizedBox(height: 16),
                _buildComponentCategory('Display Components', [
                  _buildComponentItem('Label', Icons.label, ComponentType.label),
                  _buildComponentItem('Image', Icons.image, ComponentType.label),
                  _buildComponentItem('Chart', Icons.bar_chart, ComponentType.chart),
                  _buildComponentItem('Table', Icons.table_view, ComponentType.label),
                ]),
                const SizedBox(height: 16),
                _buildComponentCategory('Action Components', [
                  _buildComponentItem('Button', Icons.smart_button, ComponentType.label),
                  _buildComponentItem('Icon Button', Icons.radio_button_unchecked, ComponentType.label),
                ]),
                const SizedBox(height: 16),
                _buildComponentCategory('Layout Components', [
                  _buildComponentItem('Container', Icons.crop_square, ComponentType.container),
                  _buildComponentItem('Row', Icons.view_column, ComponentType.container),
                  _buildComponentItem('Column', Icons.view_agenda, ComponentType.container),
                ]),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComponentCategory(String title, List<Widget> components) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 8),
        ...components,
      ],
    );
  }

  Widget _buildComponentItem(String name, IconData icon, ComponentType type) {
    return Draggable<ComponentType>(
      data: type,
      feedback: Material(
        elevation: 4,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: const Color(0xFF3498DB),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Text(
                name,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: const Color(0xFFE9ECEF)),
        ),
        child: Row(
          children: [
            Icon(icon, color: const Color(0xFF3498DB), size: 20),
            const SizedBox(width: 12),
            Text(
              name,
              style: const TextStyle(
                color: Color(0xFF2C3E50),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCanvas() {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: DragTarget<ComponentType>(
        onAcceptWithDetails: (details) {
          _addComponent(details.data);
        },
        builder: (context, candidateData, rejectedData) {
          return InteractiveViewer(
            boundaryMargin: const EdgeInsets.all(100),
            minScale: 0.5,
            maxScale: 3.0,
            constrained: false,
            child: SizedBox(
              width: 2000, // Large canvas size
              height: 1500,
              child: Stack(
                children: [
                  // Canvas background
                  Container(
                    width: double.infinity,
                    height: double.infinity,
                    decoration: BoxDecoration(
                      color: candidateData.isNotEmpty
                          ? const Color(0xFF3498DB).withValues(alpha: 0.1)
                          : const Color(0xFFF8F9FA),
                      border: candidateData.isNotEmpty
                          ? Border.all(color: const Color(0xFF3498DB), width: 2)
                          : null,
                    ),
                    child: widget.tool.uiComponents.isEmpty
                        ? const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.widgets,
                                  size: 64,
                                  color: Color(0xFFBDC3C7),
                                ),
                                SizedBox(height: 16),
                                Text(
                                  'Drag components here to build your UI',
                                  style: TextStyle(
                                    fontSize: 18,
                                    color: Color(0xFF7F8C8D),
                                  ),
                                ),
                                SizedBox(height: 8),
                                Text(
                                  'Use mouse wheel to zoom, drag to pan',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Color(0xFF95A5A6),
                                  ),
                                ),
                              ],
                            ),
                          )
                        : null,
                  ),
                  // Render components
                  ...widget.tool.uiComponents.map((component) => _buildComponentWidget(component)),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildComponentWidget(UIComponent component) {
    final isSelected = _selectedComponent?.id == component.id;
    
    return Positioned(
      left: component.x,
      top: component.y,
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedComponent = component;
          });
        },
        onPanUpdate: (details) {
          _updateComponentPosition(component, details.delta);
        },
        child: Container(
          decoration: BoxDecoration(
            border: isSelected 
                ? Border.all(color: const Color(0xFF3498DB), width: 2)
                : null,
          ),
          child: _renderComponent(component),
        ),
      ),
    );
  }

  Widget _renderComponent(UIComponent component) {
    final boundValue = _getBoundCellValue(component);
    final hasBinding = component.boundCell != null && component.boundCell!.isNotEmpty;

    switch (component.type) {
      case ComponentType.textInput:
        return SizedBox(
          width: component.width,
          height: component.height,
          child: Stack(
            children: [
              TextField(
                controller: TextEditingController(text: boundValue ?? ''),
                decoration: InputDecoration(
                  hintText: hasBinding ? 'Bound to ${component.boundCell}' : 'Text Field',
                  border: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: hasBinding ? const Color(0xFF27AE60) : const Color(0xFFBDC3C7),
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: hasBinding ? const Color(0xFF27AE60) : const Color(0xFFBDC3C7),
                    ),
                  ),
                ),
                readOnly: true, // Read-only in design mode
              ),
              if (hasBinding)
                Positioned(
                  top: 2,
                  right: 2,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                    decoration: BoxDecoration(
                      color: const Color(0xFF27AE60),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      component.boundCell!,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 8,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      case ComponentType.label:
        return SizedBox(
          width: component.width,
          height: component.height,
          child: Stack(
            children: [
              Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  color: hasBinding ? const Color(0xFF27AE60) : const Color(0xFF3498DB),
                  borderRadius: BorderRadius.circular(4),
                  border: hasBinding ? Border.all(color: const Color(0xFF27AE60), width: 2) : null,
                ),
                child: Center(
                  child: Text(
                    boundValue ?? component.label,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              if (hasBinding)
                Positioned(
                  top: 2,
                  right: 2,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      component.boundCell!,
                      style: const TextStyle(
                        color: Color(0xFF27AE60),
                        fontSize: 8,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      default:
        return Container(
          width: component.width,
          height: component.height,
          decoration: BoxDecoration(
            color: hasBinding ? const Color(0xFF27AE60).withValues(alpha: 0.1) : const Color(0xFFE9ECEF),
            border: Border.all(
              color: hasBinding ? const Color(0xFF27AE60) : const Color(0xFFBDC3C7),
            ),
          ),
          child: Stack(
            children: [
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      component.type.toString().split('.').last,
                      style: TextStyle(
                        color: hasBinding ? const Color(0xFF27AE60) : const Color(0xFF7F8C8D),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (boundValue != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        boundValue,
                        style: const TextStyle(
                          color: Color(0xFF27AE60),
                          fontSize: 10,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
              if (hasBinding)
                Positioned(
                  top: 2,
                  right: 2,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                    decoration: BoxDecoration(
                      color: const Color(0xFF27AE60),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      component.boundCell!,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 8,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
    }
  }

  Widget _buildPropertiesPanel() {
    return Container(
      width: 300,
      decoration: const BoxDecoration(
        color: Color(0xFFF8F9FA),
        border: Border(
          left: BorderSide(color: Color(0xFFE9ECEF)),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Color(0xFFE9ECEF)),
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.tune,
                  color: Color(0xFF2C3E50),
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Properties',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                const Spacer(),
                if (_selectedComponent != null)
                  IconButton(
                    onPressed: _deleteSelectedComponent,
                    icon: const Icon(Icons.delete, color: Color(0xFFE74C3C), size: 18),
                    tooltip: 'Delete Component',
                  ),
              ],
            ),
          ),
          Expanded(
            child: _selectedComponent != null
                ? _buildComponentProperties()
                : _buildEmptyPropertiesState(),
          ),
        ],
      ),
    );
  }

  Widget _buildComponentProperties() {
    if (_selectedComponent == null) return const SizedBox();

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildPropertySection(
          'Component Info',
          Icons.info_outline,
          [
            _buildInfoProperty('Type', _selectedComponent!.type.toString().split('.').last),
            _buildInfoProperty('ID', _selectedComponent!.id),
          ],
        ),
        const SizedBox(height: 24),
        _buildPropertySection(
          'Position & Size',
          Icons.crop_free,
          [
            _buildNumberProperty('X Position', _selectedComponent!.x, (value) {
              _updateComponentProperty('x', value);
            }),
            _buildNumberProperty('Y Position', _selectedComponent!.y, (value) {
              _updateComponentProperty('y', value);
            }),
            _buildNumberProperty('Width', _selectedComponent!.width, (value) {
              _updateComponentProperty('width', value);
            }),
            _buildNumberProperty('Height', _selectedComponent!.height, (value) {
              _updateComponentProperty('height', value);
            }),
          ],
        ),
        const SizedBox(height: 24),
        _buildPropertySection(
          'Content',
          Icons.text_fields,
          [
            _buildTextProperty('Label', _selectedComponent!.label, (value) {
              _updateComponentProperty('label', value);
            }),
            _buildCellBindingProperty(),
          ],
        ),
      ],
    );
  }

  void _addComponent(ComponentType type) {
    final newComponent = UIComponent(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: type,
      label: type.toString().split('.').last,
      x: 100.0,
      y: 100.0,
      width: 150.0,
      height: 40.0,
      boundCell: null,
      properties: {},
    );

    final updatedTool = widget.tool.copyWith(
      uiComponents: [...widget.tool.uiComponents, newComponent],
    );

    ref.read(currentExcelAppToolProvider.notifier).setTool(updatedTool);
  }

  void _updateComponentPosition(UIComponent component, Offset delta) {
    final updatedComponent = component.copyWith(
      x: component.x + delta.dx,
      y: component.y + delta.dy,
    );

    final updatedComponents = widget.tool.uiComponents.map((c) {
      return c.id == component.id ? updatedComponent : c;
    }).toList();

    final updatedTool = widget.tool.copyWith(
      uiComponents: updatedComponents,
    );

    ref.read(currentExcelAppToolProvider.notifier).setTool(updatedTool);
  }

  void _deleteSelectedComponent() {
    if (_selectedComponent == null) return;

    final updatedComponents = widget.tool.uiComponents
        .where((c) => c.id != _selectedComponent!.id)
        .toList();

    final updatedTool = widget.tool.copyWith(uiComponents: updatedComponents);
    ref.read(currentExcelAppToolProvider.notifier).setTool(updatedTool);

    setState(() {
      _selectedComponent = null;
    });
  }

  Widget _buildEmptyPropertiesState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.touch_app,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Select a component\nto edit properties',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 24),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFF8F9FA),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: const Color(0xFFE9ECEF)),
            ),
            child: Column(
              children: [
                const Icon(
                  Icons.lightbulb_outline,
                  color: Color(0xFF3498DB),
                  size: 24,
                ),
                const SizedBox(height: 8),
                Text(
                  'Tip: Drag components from the palette to the canvas',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.grey[700],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPropertySection(String title, IconData icon, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: const Color(0xFF3498DB)),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2C3E50),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildInfoProperty(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF7F8C8D),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF2C3E50),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNumberProperty(String label, double value, Function(double) onChanged) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF7F8C8D),
            ),
          ),
          const SizedBox(height: 4),
          TextFormField(
            initialValue: value.toString(),
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              isDense: true,
            ),
            keyboardType: TextInputType.number,
            onChanged: (text) {
              final newValue = double.tryParse(text);
              if (newValue != null) {
                onChanged(newValue);
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTextProperty(String label, String value, Function(String) onChanged) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF7F8C8D),
            ),
          ),
          const SizedBox(height: 4),
          TextFormField(
            initialValue: value,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              isDense: true,
            ),
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  Widget _buildCellBindingProperty() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Cell Binding',
            style: TextStyle(
              fontSize: 12,
              color: Color(0xFF7F8C8D),
            ),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  initialValue: _selectedComponent?.boundCell ?? '',
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    isDense: true,
                    hintText: 'e.g., A1',
                  ),
                  onChanged: (value) {
                    _updateComponentProperty('boundCell', value);
                  },
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: _showCellPicker,
                icon: const Icon(Icons.grid_on, size: 18),
                tooltip: 'Pick Cell',
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _updateComponentProperty(String property, dynamic value) {
    if (_selectedComponent == null) return;

    UIComponent updatedComponent;
    switch (property) {
      case 'x':
        updatedComponent = _selectedComponent!.copyWith(x: value as double);
        break;
      case 'y':
        updatedComponent = _selectedComponent!.copyWith(y: value as double);
        break;
      case 'width':
        updatedComponent = _selectedComponent!.copyWith(width: value as double);
        break;
      case 'height':
        updatedComponent = _selectedComponent!.copyWith(height: value as double);
        break;
      case 'label':
        updatedComponent = _selectedComponent!.copyWith(label: value as String);
        break;
      case 'boundCell':
        updatedComponent = _selectedComponent!.copyWith(boundCell: value as String?);
        break;
      default:
        return;
    }

    final updatedComponents = widget.tool.uiComponents.map((c) {
      return c.id == _selectedComponent!.id ? updatedComponent : c;
    }).toList();

    final updatedTool = widget.tool.copyWith(uiComponents: updatedComponents);
    ref.read(currentExcelAppToolProvider.notifier).setTool(updatedTool);

    setState(() {
      _selectedComponent = updatedComponent;
    });
  }

  void _showCellPicker() {
    showDialog(
      context: context,
      builder: (context) => CellBindingPicker(
        tool: widget.tool,
        initialCell: _selectedComponent?.boundCell,
        onCellSelected: (cellAddress) {
          if (cellAddress != null) {
            _updateComponentProperty('boundCell', cellAddress);
          }
        },
      ),
    );
  }

  String? _getBoundCellValue(UIComponent component) {
    if (component.boundCell == null || component.boundCell!.isEmpty) {
      return null;
    }

    final cell = widget.tool.spreadsheet.cells[component.boundCell!];
    if (cell == null) return null;

    // If it's a formula, calculate the result
    if (cell.isFormula && cell.formula != null) {
      // For now, return the formula itself
      // In a real implementation, you'd calculate the result
      return cell.formula!;
    }

    return cell.value?.toString();
  }
}
