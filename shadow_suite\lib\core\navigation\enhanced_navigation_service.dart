import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Enhanced Navigation Service with Real-time Updates and Context Awareness
class EnhancedNavigationService extends ChangeNotifier {
  static final EnhancedNavigationService _instance = EnhancedNavigationService._internal();
  factory EnhancedNavigationService() => _instance;
  EnhancedNavigationService._internal();

  // Navigation state
  int _currentIndex = 0;
  String _currentRoute = '/dashboard';
  final Map<String, dynamic> _routeData = {};
  final List<NavigationHistoryItem> _navigationHistory = [];
  
  // Real-time navigation events
  final StreamController<NavigationEvent> _navigationController = StreamController.broadcast();
  
  // Navigation items with enhanced metadata
  final List<NavigationItem> _navigationItems = [
    NavigationItem(
      id: 'dashboard',
      title: 'Dashboard',
      icon: Icons.dashboard,
      route: '/dashboard',
      index: 0,
      category: NavigationCategory.main,
      badge: null,
      isEnabled: true,
      shortcuts: ['Ctrl+1', 'Cmd+1'],
    ),
    NavigationItem(
      id: 'money_manager',
      title: 'Money Manager',
      icon: Icons.account_balance_wallet,
      route: '/money_manager',
      index: 1,
      category: NavigationCategory.apps,
      badge: null,
      isEnabled: true,
      shortcuts: ['Ctrl+2', 'Cmd+2'],
    ),
    NavigationItem(
      id: 'islamic_app',
      title: 'Islamic App',
      icon: Icons.mosque,
      route: '/islamic_app',
      index: 2,
      category: NavigationCategory.apps,
      badge: null,
      isEnabled: true,
      shortcuts: ['Ctrl+3', 'Cmd+3'],
    ),
    NavigationItem(
      id: 'memo_suite',
      title: 'Memo Suite',
      icon: Icons.note,
      route: '/memo_suite',
      index: 3,
      category: NavigationCategory.apps,
      badge: null,
      isEnabled: true,
      shortcuts: ['Ctrl+4', 'Cmd+4'],
    ),
    NavigationItem(
      id: 'tools_builder',
      title: 'Tools Builder',
      icon: Icons.build,
      route: '/tools_builder',
      index: 4,
      category: NavigationCategory.apps,
      badge: NavigationBadge(
        text: 'NEW',
        color: Colors.green,
        type: BadgeType.text,
      ),
      isEnabled: true,
      shortcuts: ['Ctrl+5', 'Cmd+5'],
      subItems: [
        NavigationSubItem(
          id: 'create_tool',
          title: 'Create Tool',
          icon: Icons.add,
          route: '/tools_builder/create',
        ),
        NavigationSubItem(
          id: 'my_tools',
          title: 'My Tools',
          icon: Icons.folder,
          route: '/tools_builder/my_tools',
        ),
        NavigationSubItem(
          id: 'templates',
          title: 'Templates',
          icon: Icons.description,
          route: '/tools_builder/templates',
        ),
        NavigationSubItem(
          id: 'formula_help',
          title: 'Formula Help',
          icon: Icons.help,
          route: '/tools_builder/formula_help',
        ),
      ],
    ),
    NavigationItem(
      id: 'file_manager',
      title: 'File Manager',
      icon: Icons.folder,
      route: '/file_manager',
      index: 5,
      category: NavigationCategory.apps,
      badge: null,
      isEnabled: true,
      shortcuts: ['Ctrl+6', 'Cmd+6'],
    ),
    NavigationItem(
      id: 'settings',
      title: 'Settings',
      icon: Icons.settings,
      route: '/settings',
      index: 6,
      category: NavigationCategory.system,
      badge: null,
      isEnabled: true,
      shortcuts: ['Ctrl+,', 'Cmd+,'],
    ),
  ];

  // Getters
  int get currentIndex => _currentIndex;
  String get currentRoute => _currentRoute;
  List<NavigationItem> get navigationItems => List.unmodifiable(_navigationItems);
  List<NavigationHistoryItem> get navigationHistory => List.unmodifiable(_navigationHistory);
  Stream<NavigationEvent> get navigationStream => _navigationController.stream;

  // Navigation methods with real-time updates
  Future<void> navigateToIndex(int index, {Map<String, dynamic>? data}) async {
    if (index < 0 || index >= _navigationItems.length) return;
    
    final item = _navigationItems[index];
    if (!item.isEnabled) return;

    await navigateToRoute(item.route, data: data);
  }

  Future<void> navigateToRoute(String route, {Map<String, dynamic>? data}) async {
    final startTime = DateTime.now();
    
    try {
      // Store previous state
      final previousIndex = _currentIndex;
      final previousRoute = _currentRoute;
      
      // Find navigation item for route
      final item = _navigationItems.firstWhere(
        (item) => item.route == route,
        orElse: () => _navigationItems[0], // Default to dashboard
      );
      
      // Update state
      _currentIndex = item.index;
      _currentRoute = route;
      
      if (data != null) {
        _routeData[route] = data;
      }
      
      // Add to history
      _addToHistory(NavigationHistoryItem(
        route: route,
        title: item.title,
        timestamp: DateTime.now(),
        data: data,
      ));
      
      // Emit navigation event
      _navigationController.add(NavigationEvent(
        type: NavigationEventType.navigate,
        fromRoute: previousRoute,
        toRoute: route,
        fromIndex: previousIndex,
        toIndex: _currentIndex,
        data: data,
        timestamp: DateTime.now(),
        duration: DateTime.now().difference(startTime),
      ));
      
      notifyListeners();
      
    } catch (error) {
      _navigationController.add(NavigationEvent(
        type: NavigationEventType.error,
        fromRoute: _currentRoute,
        toRoute: route,
        fromIndex: _currentIndex,
        toIndex: _currentIndex,
        error: error.toString(),
        timestamp: DateTime.now(),
        duration: DateTime.now().difference(startTime),
      ));
    }
  }

  // Tools Builder specific navigation
  Future<void> navigateToToolsBuilder({String? subRoute, Map<String, dynamic>? data}) async {
    final route = subRoute != null ? '/tools_builder/$subRoute' : '/tools_builder';
    await navigateToRoute(route, data: data);
  }

  Future<void> navigateToCreateTool({String? templateId}) async {
    await navigateToToolsBuilder(
      subRoute: 'create',
      data: templateId != null ? {'templateId': templateId} : null,
    );
  }

  Future<void> navigateToEditTool(String toolId) async {
    await navigateToToolsBuilder(
      subRoute: 'edit',
      data: {'toolId': toolId},
    );
  }

  Future<void> navigateToRunTool(String toolId, {bool isPopup = false}) async {
    await navigateToToolsBuilder(
      subRoute: 'run',
      data: {'toolId': toolId, 'isPopup': isPopup},
    );
  }

  // Badge management for real-time updates
  void updateBadge(String itemId, NavigationBadge? badge) {
    final itemIndex = _navigationItems.indexWhere((item) => item.id == itemId);
    if (itemIndex != -1) {
      _navigationItems[itemIndex] = _navigationItems[itemIndex].copyWith(badge: badge);
      notifyListeners();
      
      _navigationController.add(NavigationEvent(
        type: NavigationEventType.badgeUpdate,
        fromRoute: _currentRoute,
        toRoute: _currentRoute,
        fromIndex: _currentIndex,
        toIndex: _currentIndex,
        data: {'itemId': itemId, 'badge': badge},
        timestamp: DateTime.now(),
      ));
    }
  }

  void updateToolsBuilderBadge({int? toolCount, bool? hasNewFeatures}) {
    NavigationBadge? badge;
    
    if (hasNewFeatures == true) {
      badge = NavigationBadge(
        text: 'NEW',
        color: Colors.green,
        type: BadgeType.text,
      );
    } else if (toolCount != null && toolCount > 0) {
      badge = NavigationBadge(
        text: toolCount.toString(),
        color: Colors.blue,
        type: BadgeType.count,
      );
    }
    
    updateBadge('tools_builder', badge);
  }

  // Route data management
  T? getRouteData<T>(String route, String key) {
    final data = _routeData[route];
    if (data is Map<String, dynamic> && data.containsKey(key)) {
      return data[key] as T?;
    }
    return null;
  }

  void setRouteData(String route, String key, dynamic value) {
    _routeData.putIfAbsent(route, () => <String, dynamic>{})[key] = value;
  }

  void clearRouteData(String route) {
    _routeData.remove(route);
  }

  // History management
  void _addToHistory(NavigationHistoryItem item) {
    _navigationHistory.add(item);
    
    // Keep only last 50 items
    while (_navigationHistory.length > 50) {
      _navigationHistory.removeAt(0);
    }
  }

  NavigationHistoryItem? getLastVisited(String route) {
    for (int i = _navigationHistory.length - 1; i >= 0; i--) {
      if (_navigationHistory[i].route == route) {
        return _navigationHistory[i];
      }
    }
    return null;
  }

  List<NavigationHistoryItem> getRecentHistory({int limit = 10}) {
    final recent = _navigationHistory.reversed.take(limit).toList();
    return recent.reversed.toList();
  }

  // Quick actions for Tools Builder
  List<QuickAction> getToolsBuilderQuickActions() {
    return [
      QuickAction(
        id: 'create_tool',
        title: 'Create New Tool',
        icon: Icons.add,
        onTap: () => navigateToCreateTool(),
      ),
      QuickAction(
        id: 'browse_templates',
        title: 'Browse Templates',
        icon: Icons.description,
        onTap: () => navigateToToolsBuilder(subRoute: 'templates'),
      ),
      QuickAction(
        id: 'formula_help',
        title: 'Formula Help',
        icon: Icons.help,
        onTap: () => navigateToToolsBuilder(subRoute: 'formula_help'),
      ),
      QuickAction(
        id: 'my_tools',
        title: 'My Tools',
        icon: Icons.folder,
        onTap: () => navigateToToolsBuilder(subRoute: 'my_tools'),
      ),
    ];
  }

  // Search functionality
  List<NavigationItem> searchNavigation(String query) {
    if (query.isEmpty) return _navigationItems;
    
    final lowerQuery = query.toLowerCase();
    return _navigationItems.where((item) {
      return item.title.toLowerCase().contains(lowerQuery) ||
             item.id.toLowerCase().contains(lowerQuery) ||
             (item.subItems?.any((sub) => 
               sub.title.toLowerCase().contains(lowerQuery) ||
               sub.id.toLowerCase().contains(lowerQuery)
             ) ?? false);
    }).toList();
  }

  // Cleanup
  @override
  void dispose() {
    _navigationController.close();
    super.dispose();
  }
}

// Supporting Models
class NavigationItem {
  final String id;
  final String title;
  final IconData icon;
  final String route;
  final int index;
  final NavigationCategory category;
  final NavigationBadge? badge;
  final bool isEnabled;
  final List<String> shortcuts;
  final List<NavigationSubItem>? subItems;

  const NavigationItem({
    required this.id,
    required this.title,
    required this.icon,
    required this.route,
    required this.index,
    required this.category,
    this.badge,
    this.isEnabled = true,
    this.shortcuts = const [],
    this.subItems,
  });

  NavigationItem copyWith({
    String? id,
    String? title,
    IconData? icon,
    String? route,
    int? index,
    NavigationCategory? category,
    NavigationBadge? badge,
    bool? isEnabled,
    List<String>? shortcuts,
    List<NavigationSubItem>? subItems,
  }) {
    return NavigationItem(
      id: id ?? this.id,
      title: title ?? this.title,
      icon: icon ?? this.icon,
      route: route ?? this.route,
      index: index ?? this.index,
      category: category ?? this.category,
      badge: badge ?? this.badge,
      isEnabled: isEnabled ?? this.isEnabled,
      shortcuts: shortcuts ?? this.shortcuts,
      subItems: subItems ?? this.subItems,
    );
  }
}

class NavigationSubItem {
  final String id;
  final String title;
  final IconData icon;
  final String route;

  const NavigationSubItem({
    required this.id,
    required this.title,
    required this.icon,
    required this.route,
  });
}

class NavigationBadge {
  final String text;
  final Color color;
  final BadgeType type;

  const NavigationBadge({
    required this.text,
    required this.color,
    required this.type,
  });
}

class NavigationHistoryItem {
  final String route;
  final String title;
  final DateTime timestamp;
  final Map<String, dynamic>? data;

  const NavigationHistoryItem({
    required this.route,
    required this.title,
    required this.timestamp,
    this.data,
  });
}

class NavigationEvent {
  final NavigationEventType type;
  final String fromRoute;
  final String toRoute;
  final int fromIndex;
  final int toIndex;
  final Map<String, dynamic>? data;
  final String? error;
  final DateTime timestamp;
  final Duration? duration;

  const NavigationEvent({
    required this.type,
    required this.fromRoute,
    required this.toRoute,
    required this.fromIndex,
    required this.toIndex,
    this.data,
    this.error,
    required this.timestamp,
    this.duration,
  });
}

class QuickAction {
  final String id;
  final String title;
  final IconData icon;
  final VoidCallback onTap;

  const QuickAction({
    required this.id,
    required this.title,
    required this.icon,
    required this.onTap,
  });
}

// Enums
enum NavigationCategory {
  main,
  apps,
  system,
}

enum BadgeType {
  count,
  text,
  dot,
}

enum NavigationEventType {
  navigate,
  badgeUpdate,
  error,
}

// Provider for Riverpod integration
final enhancedNavigationServiceProvider = ChangeNotifierProvider<EnhancedNavigationService>((ref) {
  return EnhancedNavigationService();
});
