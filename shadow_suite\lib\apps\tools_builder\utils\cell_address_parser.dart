// Utility functions for parsing Excel cell addresses
class CellAddressParser {
  /// Parse a cell address like "A1" and return row and column indices (0-based)
  static Map<String, int> parseAddress(String cellAddress) {
    final match = RegExp(r'^([A-Z]+)(\d+)$').firstMatch(cellAddress.toUpperCase());
    if (match == null) {
      return {'row': 0, 'column': 0};
    }
    
    final columnStr = match.group(1)!;
    final rowStr = match.group(2)!;
    
    // Convert column letters to 0-based index
    int column = 0;
    for (int i = 0; i < columnStr.length; i++) {
      column = column * 26 + (columnStr.codeUnitAt(i) - 'A'.codeUnitAt(0) + 1);
    }
    column -= 1; // Convert to 0-based
    
    // Convert row number to 0-based index
    int row = int.parse(rowStr) - 1;
    
    return {'row': row, 'column': column};
  }
  
  /// Get row index from cell address (0-based)
  static int getRow(String cellAddress) {
    return parseAddress(cellAddress)['row']!;
  }
  
  /// Get column index from cell address (0-based)
  static int getColumn(String cellAddress) {
    return parseAddress(cellAddress)['column']!;
  }
  
  /// Convert column index to letter (A, B, C, etc.)
  static String columnToLetter(int column) {
    String result = '';
    int col = column + 1; // Convert to 1-based
    while (col > 0) {
      col--;
      result = String.fromCharCode('A'.codeUnitAt(0) + (col % 26)) + result;
      col ~/= 26;
    }
    return result;
  }
  
  /// Convert row and column indices to cell address
  static String indicesToAddress(int row, int column) {
    return '${columnToLetter(column)}${row + 1}';
  }
}
