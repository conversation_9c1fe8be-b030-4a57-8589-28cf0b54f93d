import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// UI Builder Theme System with pre-built app themes
class UIBuilderThemeSystem {
  static List<AppTheme> getAllThemes() {
    return [
      _createMaterialTheme(),
      _createCupertinoTheme(),
      _createFluentTheme(),
      _createMinimalTheme(),
      _createDarkTheme(),
      _createNeonTheme(),
      _createClassicTheme(),
      _createModernTheme(),
    ];
  }

  // 1. Android Material Theme
  static AppTheme _createMaterialTheme() {
    return AppTheme(
      id: 'material',
      name: 'Material Design',
      description: 'Google\'s Material Design system',
      platform: ThemePlatform.android,
      colorScheme: const AppColorScheme(
        primary: Color(0xFF6200EE),
        primaryVariant: Color(0xFF3700B3),
        secondary: Color(0xFF03DAC6),
        secondaryVariant: Color(0xFF018786),
        background: Color(0xFFFFFFFF),
        surface: Color(0xFFFFFFFF),
        error: Color(0xFFB00020),
        onPrimary: Color(0xFFFFFFFF),
        onSecondary: Color(0xFF000000),
        onBackground: Color(0xFF000000),
        onSurface: Color(0xFF000000),
        onError: Color(0xFFFFFFFF),
      ),
      typography: const AppTypography(
        fontFamily: 'Roboto',
        headlineLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.w400),
        headlineMedium: TextStyle(fontSize: 28, fontWeight: FontWeight.w400),
        headlineSmall: TextStyle(fontSize: 24, fontWeight: FontWeight.w400),
        titleLarge: TextStyle(fontSize: 22, fontWeight: FontWeight.w500),
        titleMedium: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        titleSmall: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        bodyLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.w400),
        bodyMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
        bodySmall: TextStyle(fontSize: 12, fontWeight: FontWeight.w400),
        labelLarge: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        labelMedium: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
        labelSmall: TextStyle(fontSize: 11, fontWeight: FontWeight.w500),
      ),
      spacing: const AppSpacing(xs: 4, sm: 8, md: 16, lg: 24, xl: 32, xxl: 48),
      borderRadius: const AppBorderRadius(
        small: 4,
        medium: 8,
        large: 12,
        extraLarge: 16,
      ),
      shadows: const AppShadows(
        small: [
          BoxShadow(
            color: Color(0x1F000000),
            blurRadius: 2,
            offset: Offset(0, 1),
          ),
        ],
        medium: [
          BoxShadow(
            color: Color(0x24000000),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
        large: [
          BoxShadow(
            color: Color(0x29000000),
            blurRadius: 8,
            offset: Offset(0, 4),
          ),
        ],
      ),
      componentStyles: MaterialComponentStyles(),
    );
  }

  // 2. iOS Cupertino Theme
  static AppTheme _createCupertinoTheme() {
    return AppTheme(
      id: 'cupertino',
      name: 'iOS Cupertino',
      description: 'Apple\'s iOS design language',
      platform: ThemePlatform.ios,
      colorScheme: const AppColorScheme(
        primary: Color(0xFF007AFF),
        primaryVariant: Color(0xFF0051D5),
        secondary: Color(0xFF5AC8FA),
        secondaryVariant: Color(0xFF007AFF),
        background: Color(0xFFF2F2F7),
        surface: Color(0xFFFFFFFF),
        error: Color(0xFFFF3B30),
        onPrimary: Color(0xFFFFFFFF),
        onSecondary: Color(0xFF000000),
        onBackground: Color(0xFF000000),
        onSurface: Color(0xFF000000),
        onError: Color(0xFFFFFFFF),
      ),
      typography: const AppTypography(
        fontFamily: 'SF Pro Display',
        headlineLarge: TextStyle(fontSize: 34, fontWeight: FontWeight.w700),
        headlineMedium: TextStyle(fontSize: 28, fontWeight: FontWeight.w700),
        headlineSmall: TextStyle(fontSize: 22, fontWeight: FontWeight.w600),
        titleLarge: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
        titleMedium: TextStyle(fontSize: 17, fontWeight: FontWeight.w600),
        titleSmall: TextStyle(fontSize: 15, fontWeight: FontWeight.w600),
        bodyLarge: TextStyle(fontSize: 17, fontWeight: FontWeight.w400),
        bodyMedium: TextStyle(fontSize: 15, fontWeight: FontWeight.w400),
        bodySmall: TextStyle(fontSize: 13, fontWeight: FontWeight.w400),
        labelLarge: TextStyle(fontSize: 15, fontWeight: FontWeight.w600),
        labelMedium: TextStyle(fontSize: 13, fontWeight: FontWeight.w600),
        labelSmall: TextStyle(fontSize: 11, fontWeight: FontWeight.w600),
      ),
      spacing: const AppSpacing(xs: 4, sm: 8, md: 16, lg: 20, xl: 32, xxl: 44),
      borderRadius: const AppBorderRadius(
        small: 6,
        medium: 10,
        large: 14,
        extraLarge: 20,
      ),
      shadows: const AppShadows(
        small: [
          BoxShadow(
            color: Color(0x0F000000),
            blurRadius: 1,
            offset: Offset(0, 1),
          ),
        ],
        medium: [
          BoxShadow(
            color: Color(0x14000000),
            blurRadius: 3,
            offset: Offset(0, 2),
          ),
        ],
        large: [
          BoxShadow(
            color: Color(0x1F000000),
            blurRadius: 6,
            offset: Offset(0, 4),
          ),
        ],
      ),
      componentStyles: CupertinoComponentStyles(),
    );
  }

  // 3. Windows Fluent Theme
  static AppTheme _createFluentTheme() {
    return AppTheme(
      id: 'fluent',
      name: 'Windows Fluent',
      description: 'Microsoft\'s Fluent Design System',
      platform: ThemePlatform.windows,
      colorScheme: const AppColorScheme(
        primary: Color(0xFF0078D4),
        primaryVariant: Color(0xFF106EBE),
        secondary: Color(0xFF40E0D0),
        secondaryVariant: Color(0xFF00CED1),
        background: Color(0xFFF3F2F1),
        surface: Color(0xFFFFFFFF),
        error: Color(0xFFD13438),
        onPrimary: Color(0xFFFFFFFF),
        onSecondary: Color(0xFF000000),
        onBackground: Color(0xFF323130),
        onSurface: Color(0xFF323130),
        onError: Color(0xFFFFFFFF),
      ),
      typography: const AppTypography(
        fontFamily: 'Segoe UI',
        headlineLarge: TextStyle(fontSize: 40, fontWeight: FontWeight.w300),
        headlineMedium: TextStyle(fontSize: 32, fontWeight: FontWeight.w300),
        headlineSmall: TextStyle(fontSize: 24, fontWeight: FontWeight.w400),
        titleLarge: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
        titleMedium: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        titleSmall: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        bodyLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.w400),
        bodyMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
        bodySmall: TextStyle(fontSize: 12, fontWeight: FontWeight.w400),
        labelLarge: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        labelMedium: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
        labelSmall: TextStyle(fontSize: 10, fontWeight: FontWeight.w600),
      ),
      spacing: const AppSpacing(xs: 4, sm: 8, md: 12, lg: 20, xl: 32, xxl: 40),
      borderRadius: const AppBorderRadius(
        small: 2,
        medium: 4,
        large: 8,
        extraLarge: 12,
      ),
      shadows: const AppShadows(
        small: [
          BoxShadow(
            color: Color(0x14000000),
            blurRadius: 2,
            offset: Offset(0, 1),
          ),
        ],
        medium: [
          BoxShadow(
            color: Color(0x1F000000),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
        large: [
          BoxShadow(
            color: Color(0x29000000),
            blurRadius: 8,
            offset: Offset(0, 4),
          ),
        ],
      ),
      componentStyles: FluentComponentStyles(),
    );
  }

  // 4. Minimal Theme
  static AppTheme _createMinimalTheme() {
    return AppTheme(
      id: 'minimal',
      name: 'Minimal',
      description: 'Clean and minimal design',
      platform: ThemePlatform.web,
      colorScheme: const AppColorScheme(
        primary: Color(0xFF000000),
        primaryVariant: Color(0xFF424242),
        secondary: Color(0xFF9E9E9E),
        secondaryVariant: Color(0xFF757575),
        background: Color(0xFFFFFFFF),
        surface: Color(0xFFFAFAFA),
        error: Color(0xFFE53E3E),
        onPrimary: Color(0xFFFFFFFF),
        onSecondary: Color(0xFF000000),
        onBackground: Color(0xFF000000),
        onSurface: Color(0xFF000000),
        onError: Color(0xFFFFFFFF),
      ),
      typography: const AppTypography(
        fontFamily: 'Inter',
        headlineLarge: TextStyle(fontSize: 36, fontWeight: FontWeight.w300),
        headlineMedium: TextStyle(fontSize: 28, fontWeight: FontWeight.w300),
        headlineSmall: TextStyle(fontSize: 24, fontWeight: FontWeight.w400),
        titleLarge: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
        titleMedium: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        titleSmall: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        bodyLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.w400),
        bodyMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
        bodySmall: TextStyle(fontSize: 12, fontWeight: FontWeight.w400),
        labelLarge: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        labelMedium: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
        labelSmall: TextStyle(fontSize: 10, fontWeight: FontWeight.w500),
      ),
      spacing: const AppSpacing(xs: 2, sm: 4, md: 8, lg: 16, xl: 24, xxl: 32),
      borderRadius: const AppBorderRadius(
        small: 0,
        medium: 2,
        large: 4,
        extraLarge: 8,
      ),
      shadows: const AppShadows(
        small: [],
        medium: [
          BoxShadow(
            color: Color(0x0A000000),
            blurRadius: 1,
            offset: Offset(0, 1),
          ),
        ],
        large: [
          BoxShadow(
            color: Color(0x14000000),
            blurRadius: 2,
            offset: Offset(0, 2),
          ),
        ],
      ),
      componentStyles: MinimalComponentStyles(),
    );
  }

  // Additional theme creation methods would go here...
  static AppTheme _createDarkTheme() =>
      _createMaterialTheme().copyWith(id: 'dark', name: 'Dark Theme');
  static AppTheme _createNeonTheme() =>
      _createMaterialTheme().copyWith(id: 'neon', name: 'Neon Theme');
  static AppTheme _createClassicTheme() =>
      _createMaterialTheme().copyWith(id: 'classic', name: 'Classic Theme');
  static AppTheme _createModernTheme() =>
      _createMaterialTheme().copyWith(id: 'modern', name: 'Modern Theme');
}

// Theme Data Models
enum ThemePlatform { android, ios, windows, web, universal }

class AppTheme {
  final String id;
  final String name;
  final String description;
  final ThemePlatform platform;
  final AppColorScheme colorScheme;
  final AppTypography typography;
  final AppSpacing spacing;
  final AppBorderRadius borderRadius;
  final AppShadows shadows;
  final ComponentStyles componentStyles;

  const AppTheme({
    required this.id,
    required this.name,
    required this.description,
    required this.platform,
    required this.colorScheme,
    required this.typography,
    required this.spacing,
    required this.borderRadius,
    required this.shadows,
    required this.componentStyles,
  });

  AppTheme copyWith({
    String? id,
    String? name,
    String? description,
    ThemePlatform? platform,
    AppColorScheme? colorScheme,
    AppTypography? typography,
    AppSpacing? spacing,
    AppBorderRadius? borderRadius,
    AppShadows? shadows,
    ComponentStyles? componentStyles,
  }) {
    return AppTheme(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      platform: platform ?? this.platform,
      colorScheme: colorScheme ?? this.colorScheme,
      typography: typography ?? this.typography,
      spacing: spacing ?? this.spacing,
      borderRadius: borderRadius ?? this.borderRadius,
      shadows: shadows ?? this.shadows,
      componentStyles: componentStyles ?? this.componentStyles,
    );
  }
}

class AppColorScheme {
  final Color primary;
  final Color primaryVariant;
  final Color secondary;
  final Color secondaryVariant;
  final Color background;
  final Color surface;
  final Color error;
  final Color onPrimary;
  final Color onSecondary;
  final Color onBackground;
  final Color onSurface;
  final Color onError;

  const AppColorScheme({
    required this.primary,
    required this.primaryVariant,
    required this.secondary,
    required this.secondaryVariant,
    required this.background,
    required this.surface,
    required this.error,
    required this.onPrimary,
    required this.onSecondary,
    required this.onBackground,
    required this.onSurface,
    required this.onError,
  });
}

class AppTypography {
  final String fontFamily;
  final TextStyle headlineLarge;
  final TextStyle headlineMedium;
  final TextStyle headlineSmall;
  final TextStyle titleLarge;
  final TextStyle titleMedium;
  final TextStyle titleSmall;
  final TextStyle bodyLarge;
  final TextStyle bodyMedium;
  final TextStyle bodySmall;
  final TextStyle labelLarge;
  final TextStyle labelMedium;
  final TextStyle labelSmall;

  const AppTypography({
    required this.fontFamily,
    required this.headlineLarge,
    required this.headlineMedium,
    required this.headlineSmall,
    required this.titleLarge,
    required this.titleMedium,
    required this.titleSmall,
    required this.bodyLarge,
    required this.bodyMedium,
    required this.bodySmall,
    required this.labelLarge,
    required this.labelMedium,
    required this.labelSmall,
  });
}

class AppSpacing {
  final double xs;
  final double sm;
  final double md;
  final double lg;
  final double xl;
  final double xxl;

  const AppSpacing({
    required this.xs,
    required this.sm,
    required this.md,
    required this.lg,
    required this.xl,
    required this.xxl,
  });
}

class AppBorderRadius {
  final double small;
  final double medium;
  final double large;
  final double extraLarge;

  const AppBorderRadius({
    required this.small,
    required this.medium,
    required this.large,
    required this.extraLarge,
  });
}

class AppShadows {
  final List<BoxShadow> small;
  final List<BoxShadow> medium;
  final List<BoxShadow> large;

  const AppShadows({
    required this.small,
    required this.medium,
    required this.large,
  });
}

// Component Styles
abstract class ComponentStyles {
  ButtonStyle get primaryButton;
  ButtonStyle get secondaryButton;
  InputDecorationTheme get textField;
  CardTheme get card;
}

class MaterialComponentStyles implements ComponentStyles {
  @override
  ButtonStyle get primaryButton => ElevatedButton.styleFrom(
    elevation: 2,
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
  );

  @override
  ButtonStyle get secondaryButton => OutlinedButton.styleFrom(
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
  );

  @override
  InputDecorationTheme get textField => const InputDecorationTheme(
    border: OutlineInputBorder(),
    contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
  );

  @override
  CardTheme get card => CardTheme(
    elevation: 2,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
  );
}

class CupertinoComponentStyles implements ComponentStyles {
  @override
  ButtonStyle get primaryButton => ElevatedButton.styleFrom(
    elevation: 0,
    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
  );

  @override
  ButtonStyle get secondaryButton => OutlinedButton.styleFrom(
    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
  );

  @override
  InputDecorationTheme get textField => InputDecorationTheme(
    border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
  );

  @override
  CardTheme get card => CardTheme(
    elevation: 1,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
  );
}

class FluentComponentStyles implements ComponentStyles {
  @override
  ButtonStyle get primaryButton => ElevatedButton.styleFrom(
    elevation: 0,
    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
  );

  @override
  ButtonStyle get secondaryButton => OutlinedButton.styleFrom(
    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
  );

  @override
  InputDecorationTheme get textField => InputDecorationTheme(
    border: OutlineInputBorder(borderRadius: BorderRadius.circular(4)),
    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
  );

  @override
  CardTheme get card => CardTheme(
    elevation: 2,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
  );
}

class MinimalComponentStyles implements ComponentStyles {
  @override
  ButtonStyle get primaryButton => ElevatedButton.styleFrom(
    elevation: 0,
    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
  );

  @override
  ButtonStyle get secondaryButton => OutlinedButton.styleFrom(
    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
  );

  @override
  InputDecorationTheme get textField => InputDecorationTheme(
    border: OutlineInputBorder(borderRadius: BorderRadius.circular(2)),
    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
  );

  @override
  CardTheme get card => CardTheme(
    elevation: 0,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
  );
}

// Providers
final selectedThemeProvider = StateProvider<AppTheme>((ref) {
  return UIBuilderThemeSystem.getAllThemes().first;
});

final availableThemesProvider = Provider<List<AppTheme>>((ref) {
  return UIBuilderThemeSystem.getAllThemes();
});

final themeCustomizationProvider =
    StateNotifierProvider<ThemeCustomizationNotifier, Map<String, dynamic>>((
      ref,
    ) {
      return ThemeCustomizationNotifier();
    });

class ThemeCustomizationNotifier extends StateNotifier<Map<String, dynamic>> {
  ThemeCustomizationNotifier() : super({});

  void updateColor(String key, Color color) {
    state = {...state, key: color.toARGB32()};
  }

  void updateSpacing(String key, double value) {
    state = {...state, key: value};
  }

  void updateTypography(String key, TextStyle style) {
    state = {...state, key: style};
  }

  void resetCustomizations() {
    state = {};
  }
}
