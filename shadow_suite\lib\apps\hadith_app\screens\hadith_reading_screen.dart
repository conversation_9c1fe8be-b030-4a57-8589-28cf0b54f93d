import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class HadithReadingScreen extends ConsumerStatefulWidget {
  final String hadithId;
  final String collection;

  const HadithReadingScreen({
    super.key,
    required this.hadithId,
    required this.collection,
  });

  @override
  ConsumerState<HadithReadingScreen> createState() => _HadithReadingScreenState();
}

class _HadithReadingScreenState extends ConsumerState<HadithReadingScreen> {
  bool _isBookmarked = false;
  double _fontSize = 16.0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.collection),
        backgroundColor: Colors.brown,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () => _toggleBookmark(),
            icon: Icon(_isBookmarked ? Icons.bookmark : Icons.bookmark_border),
          ),
          IconButton(
            onPressed: () => _shareHadith(),
            icon: const Icon(Icons.share),
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'font_size', child: Text('Font Size')),
              const PopupMenuItem(value: 'copy', child: Text('Copy Text')),
              const PopupMenuItem(value: 'report', child: Text('Report Issue')),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHadithHeader(),
            const SizedBox(height: 24),
            _buildHadithText(),
            const SizedBox(height: 24),
            _buildHadithDetails(),
            const SizedBox(height: 24),
            _buildRelatedHadiths(),
          ],
        ),
      ),
    );
  }

  Widget _buildHadithHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.brown.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.brown,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  widget.collection,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'Hadith ${widget.hadithId}',
                  style: TextStyle(
                    color: Colors.orange[700],
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Book: Faith and Belief',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Chapter: The Pillars of Islam',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHadithText() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.format_quote, color: Colors.brown, size: 24),
              const SizedBox(width: 8),
              Text(
                'Hadith Text',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.brown,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Narrated by Abu Huraira (may Allah be pleased with him):',
            style: TextStyle(
              fontSize: _fontSize - 2,
              fontWeight: FontWeight.bold,
              color: Colors.brown,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'The Messenger of Allah (peace and blessings be upon him) said: "Faith (Iman) consists of more than sixty branches (i.e. parts). And Haya (modesty, self-respect, bashfulness, and scruple) is a part of faith."',
            style: TextStyle(
              fontSize: _fontSize,
              height: 1.8,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            '- Prophet Muhammad (peace and blessings be upon him)',
            style: TextStyle(
              fontSize: _fontSize - 2,
              fontStyle: FontStyle.italic,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHadithDetails() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Hadith Details',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildDetailRow('Narrator', 'Abu Huraira (RA)'),
          _buildDetailRow('Grade', 'Sahih (Authentic)'),
          _buildDetailRow('Reference', 'Sahih Bukhari 9'),
          _buildDetailRow('Arabic Reference', 'صحيح البخاري ٩'),
          _buildDetailRow('In-Book Reference', 'Book 2, Hadith 2'),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: Colors.black87),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRelatedHadiths() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Related Hadiths',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 5,
            itemBuilder: (context, index) {
              return _buildRelatedHadithCard(index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildRelatedHadithCard(int index) {
    return Container(
      width: 200,
      margin: const EdgeInsets.only(right: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Sahih Bukhari ${index + 10}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 12,
              color: Colors.brown,
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: Text(
              'About the importance of prayer in Islam...',
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey[600],
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(height: 8),
          Align(
            alignment: Alignment.centerRight,
            child: TextButton(
              onPressed: () {},
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize: const Size(0, 0),
              ),
              child: const Text(
                'Read',
                style: TextStyle(fontSize: 11),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _toggleBookmark() {
    setState(() {
      _isBookmarked = !_isBookmarked;
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_isBookmarked ? 'Hadith bookmarked' : 'Bookmark removed'),
      ),
    );
  }

  void _shareHadith() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Hadith shared successfully')),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'font_size':
        _showFontSizeDialog();
        break;
      case 'copy':
        _copyHadithText();
        break;
      case 'report':
        _reportIssue();
        break;
    }
  }

  void _showFontSizeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Font Size'),
        content: StatefulBuilder(
          builder: (context, setDialogState) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Font Size: ${_fontSize.toInt()}'),
              Slider(
                value: _fontSize,
                min: 12.0,
                max: 24.0,
                divisions: 12,
                onChanged: (value) {
                  setDialogState(() => _fontSize = value);
                  setState(() => _fontSize = value);
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  void _copyHadithText() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Hadith text copied to clipboard')),
    );
  }

  void _reportIssue() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Thank you for your feedback')),
    );
  }
}
