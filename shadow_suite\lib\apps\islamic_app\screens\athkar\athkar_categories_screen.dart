import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../../../../core/theme/app_theme.dart';
import '../../services/islamic_providers.dart';
import '../../models/athkar.dart';

class AthkarCategoriesScreen extends ConsumerWidget {
  const AthkarCategoriesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dhikrAsync = ref.watch(dhikrProvider);
    final customAthkarAsync = ref.watch(customAthkarProvider);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Athkar Categories'),
        backgroundColor: AppTheme.islamicAppColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.dashboard;
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              // Clear selected provider for new creation
              ref.read(selectedCustomAthkarProvider.notifier).state = null;
              ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.customAthkarCreator;
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWelcomeSection(context),
            const SizedBox(height: 32),
            _buildPredefinedCategories(context, ref, dhikrAsync),
            const SizedBox(height: 32),
            _buildCustomAthkar(context, ref, customAthkarAsync),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.islamicAppColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppTheme.islamicAppColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.beenhere,
            size: 32,
            color: AppTheme.islamicAppColor,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Athkar & Dhikr',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.islamicAppColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Remember Allah with beautiful supplications',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPredefinedCategories(BuildContext context, WidgetRef ref, AsyncValue<List<Dhikr>> dhikrAsync) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Traditional Athkar',
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        const SizedBox(height: 16),
        dhikrAsync.when(
          data: (allDhikr) {
            final categories = AthkarCategory.values.where((c) => c != AthkarCategory.custom).toList();
            
            return StaggeredGrid.count(
              crossAxisCount: 2,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              children: categories.map((category) {
                final categoryDhikr = allDhikr.where((d) => d.category == category).toList();
                return _buildCategoryCard(context, ref, category, categoryDhikr.length);
              }).toList(),
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(
            child: Column(
              children: [
                Icon(Icons.error, size: 64, color: Colors.red[300]),
                const SizedBox(height: 16),
                Text('Error loading athkar: $error'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => ref.refresh(dhikrProvider),
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryCard(BuildContext context, WidgetRef ref, AthkarCategory category, int dhikrCount) {
    final categoryInfo = _getCategoryInfo(category);
    
    return Card(
      child: InkWell(
        onTap: () {
          ref.read(selectedAthkarCategoryProvider.notifier).state = category;
          ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.dhikrCounter;
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: categoryInfo['color'].withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      categoryInfo['icon'],
                      color: categoryInfo['color'],
                      size: 24,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppTheme.islamicAppColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '$dhikrCount',
                      style: TextStyle(
                        color: AppTheme.islamicAppColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                category.displayName,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                categoryInfo['description'],
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 12),
              Text(
                categoryInfo['time'],
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: categoryInfo['color'],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomAthkar(BuildContext context, WidgetRef ref, AsyncValue<List<CustomAthkarRoutine>> customAthkarAsync) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Custom Athkar',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            TextButton.icon(
              onPressed: () {
                // Clear selected provider for new creation
                ref.read(selectedCustomAthkarProvider.notifier).state = null;
                ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.customAthkarCreator;
              },
              icon: const Icon(Icons.add),
              label: const Text('Create'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        customAthkarAsync.when(
          data: (customRoutines) {
            if (customRoutines.isEmpty) {
              return _buildEmptyCustomAthkar(context, ref);
            }
            
            return Column(
              children: customRoutines.map((routine) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: _buildCustomAthkarCard(context, ref, routine),
              )).toList(),
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Text('Error loading custom athkar: $error'),
        ),
      ],
    );
  }

  Widget _buildEmptyCustomAthkar(BuildContext context, WidgetRef ref) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Icon(
              Icons.auto_awesome,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No Custom Athkar Yet',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Create your own personalized dhikr routines',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                // Clear selected provider for new creation
                ref.read(selectedCustomAthkarProvider.notifier).state = null;
                ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.customAthkarCreator;
              },
              icon: const Icon(Icons.add),
              label: const Text('Create Custom Athkar'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.islamicAppColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomAthkarCard(BuildContext context, WidgetRef ref, CustomAthkarRoutine routine) {
    final color = Color(int.parse(routine.color.replaceFirst('#', '0xFF')));
    
    return Card(
      child: InkWell(
        onTap: () {
          ref.read(selectedCustomAthkarProvider.notifier).state = routine;
          ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.dhikrCounter;
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 4,
                height: 60,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      routine.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      routine.description,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.format_list_numbered,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${routine.dhikrItems.length} dhikr',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(width: 16),
                        Icon(
                          Icons.repeat,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${routine.totalDhikrCount} total',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              PopupMenuButton<String>(
                onSelected: (value) => _handleCustomAthkarAction(context, ref, routine, value),
                itemBuilder: (context) => [
                  const PopupMenuItem(value: 'edit', child: Text('Edit')),
                  const PopupMenuItem(value: 'duplicate', child: Text('Duplicate')),
                  const PopupMenuItem(value: 'delete', child: Text('Delete')),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Map<String, dynamic> _getCategoryInfo(AthkarCategory category) {
    switch (category) {
      case AthkarCategory.morning:
        return {
          'icon': Icons.wb_sunny,
          'color': Colors.orange,
          'description': 'Start your day with remembrance of Allah',
          'time': 'After Fajr until sunrise',
        };
      case AthkarCategory.evening:
        return {
          'icon': Icons.nights_stay,
          'color': Colors.indigo,
          'description': 'End your day with gratitude and protection',
          'time': 'After Asr until Maghrib',
        };
      case AthkarCategory.afterPrayer:
        return {
          'icon': Icons.mosque,
          'color': Colors.green,
          'description': 'Dhikr to recite after each prayer',
          'time': 'After each Salah',
        };
      case AthkarCategory.sleeping:
        return {
          'icon': Icons.bedtime,
          'color': Colors.purple,
          'description': 'Supplications before sleep',
          'time': 'Before going to bed',
        };
      case AthkarCategory.eating:
        return {
          'icon': Icons.restaurant,
          'color': Colors.brown,
          'description': 'Duas before and after meals',
          'time': 'Before and after eating',
        };
      case AthkarCategory.travel:
        return {
          'icon': Icons.directions_car,
          'color': Colors.blue,
          'description': 'Prayers for safe journey',
          'time': 'When traveling',
        };
      default:
        return {
          'icon': Icons.auto_awesome,
          'color': AppTheme.islamicAppColor,
          'description': 'Custom dhikr routines',
          'time': 'Anytime',
        };
    }
  }

  void _handleCustomAthkarAction(BuildContext context, WidgetRef ref, CustomAthkarRoutine routine, String action) {
    switch (action) {
      case 'edit':
        ref.read(selectedCustomAthkarProvider.notifier).state = routine;
        ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.customAthkarCreator;
        break;
      case 'duplicate':
        _duplicateCustomAthkar(context, ref, routine);
        break;
      case 'delete':
        _showDeleteDialog(context, ref, routine);
        break;
    }
  }

  void _duplicateCustomAthkar(BuildContext context, WidgetRef ref, CustomAthkarRoutine routine) {
    final duplicatedRoutine = CustomAthkarRoutine(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: '${routine.name} (Copy)',
      description: routine.description,
      color: routine.color,
      dhikrItems: routine.dhikrItems.map((item) => CustomDhikrItem(
        dhikrId: item.dhikrId,
        count: item.count,
        order: item.order,
      )).toList(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    ref.read(customAthkarProvider.notifier).addCustomAthkar(duplicatedRoutine);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Duplicated "${routine.name}" successfully'),
        backgroundColor: AppTheme.islamicAppColor,
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, WidgetRef ref, CustomAthkarRoutine routine) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Custom Athkar'),
        content: Text('Are you sure you want to delete "${routine.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              ref.read(customAthkarProvider.notifier).deleteCustomAthkar(routine.id);
              Navigator.of(context).pop();
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
