import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/services.dart';

/// Advanced Markdown Editor with live preview and formatting tools
class MarkdownEditorScreen extends ConsumerStatefulWidget {
  final String? noteId;
  final String? initialTitle;
  final String? initialContent;

  const MarkdownEditorScreen({
    super.key,
    this.noteId,
    this.initialTitle,
    this.initialContent,
  });

  @override
  ConsumerState<MarkdownEditorScreen> createState() => _MarkdownEditorScreenState();
}

class _MarkdownEditorScreenState extends ConsumerState<MarkdownEditorScreen> {
  late TextEditingController _titleController;
  late TextEditingController _contentController;
  bool _showPreview = false;
  bool _hasUnsavedChanges = false;
  String _selectedFormat = 'markdown';

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.initialTitle ?? '');
    _contentController = TextEditingController(text: widget.initialContent ?? '');
    
    _titleController.addListener(_onContentChanged);
    _contentController.addListener(_onContentChanged);
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  void _onContentChanged() {
    if (!_hasUnsavedChanges) {
      setState(() => _hasUnsavedChanges = true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Markdown Editor'),
        backgroundColor: Colors.amber,
        foregroundColor: Colors.black,
        actions: [
          IconButton(
            icon: Icon(_showPreview ? Icons.edit : Icons.preview),
            onPressed: () => setState(() => _showPreview = !_showPreview),
            tooltip: _showPreview ? 'Edit Mode' : 'Preview Mode',
          ),
          IconButton(
            icon: const Icon(Icons.format_paint),
            onPressed: _showFormattingToolbar,
          ),
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _hasUnsavedChanges ? _saveNote : null,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildTitleField(),
          _buildFormatSelector(),
          Expanded(
            child: _showPreview ? _buildPreviewMode() : _buildEditMode(),
          ),
          if (!_showPreview) _buildFormattingToolbar(),
        ],
      ),
    );
  }

  Widget _buildTitleField() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.amber.withValues(alpha: 0.1),
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: TextField(
        controller: _titleController,
        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        decoration: const InputDecoration(
          hintText: 'Note title...',
          border: InputBorder.none,
          prefixIcon: Icon(Icons.title),
        ),
      ),
    );
  }

  Widget _buildFormatSelector() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          const Text('Format: '),
          DropdownButton<String>(
            value: _selectedFormat,
            items: const [
              DropdownMenuItem(value: 'markdown', child: Text('Markdown')),
              DropdownMenuItem(value: 'rich_text', child: Text('Rich Text')),
              DropdownMenuItem(value: 'plain_text', child: Text('Plain Text')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() => _selectedFormat = value);
              }
            },
          ),
          const Spacer(),
          if (_hasUnsavedChanges)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.orange,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Text(
                'Unsaved',
                style: TextStyle(color: Colors.white, fontSize: 12),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildEditMode() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _contentController,
        maxLines: null,
        expands: true,
        style: const TextStyle(fontFamily: 'monospace', fontSize: 14),
        decoration: const InputDecoration(
          hintText: 'Start writing your note...\n\n# Markdown Syntax Examples:\n\n**Bold text**\n*Italic text*\n- List item\n1. Numbered list\n[Link](url)\n![Image](url)\n\n```\nCode block\n```',
          border: OutlineInputBorder(),
          alignLabelWithHint: true,
        ),
      ),
    );
  }

  Widget _buildPreviewMode() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: _buildMarkdownPreview(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMarkdownPreview() {
    final content = _contentController.text;
    if (content.isEmpty) {
      return const Text(
        'Preview will appear here...',
        style: TextStyle(color: Colors.grey, fontStyle: FontStyle.italic),
      );
    }

    // Simple markdown parsing for demonstration
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: _parseMarkdown(content),
    );
  }

  List<Widget> _parseMarkdown(String content) {
    final lines = content.split('\n');
    final widgets = <Widget>[];

    for (final line in lines) {
      if (line.startsWith('# ')) {
        widgets.add(Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Text(
            line.substring(2),
            style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
        ));
      } else if (line.startsWith('## ')) {
        widgets.add(Padding(
          padding: const EdgeInsets.symmetric(vertical: 6),
          child: Text(
            line.substring(3),
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
        ));
      } else if (line.startsWith('### ')) {
        widgets.add(Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Text(
            line.substring(4),
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
        ));
      } else if (line.startsWith('- ') || line.startsWith('* ')) {
        widgets.add(Padding(
          padding: const EdgeInsets.only(left: 16, bottom: 4),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('• '),
              Expanded(child: Text(_parseInlineMarkdown(line.substring(2)))),
            ],
          ),
        ));
      } else if (line.startsWith('```')) {
        // Code block handling would go here
        widgets.add(Container(
          margin: const EdgeInsets.symmetric(vertical: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Text(
            'Code block',
            style: TextStyle(fontFamily: 'monospace'),
          ),
        ));
      } else if (line.trim().isNotEmpty) {
        widgets.add(Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Text(_parseInlineMarkdown(line)),
        ));
      } else {
        widgets.add(const SizedBox(height: 8));
      }
    }

    return widgets;
  }

  String _parseInlineMarkdown(String text) {
    // Simple inline markdown parsing
    return text
        .replaceAll(RegExp(r'\*\*(.*?)\*\*'), r'$1') // Bold
        .replaceAll(RegExp(r'\*(.*?)\*'), r'$1'); // Italic
  }

  Widget _buildFormattingToolbar() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            _buildToolbarButton('H1', () => _insertMarkdown('# ', '')),
            _buildToolbarButton('H2', () => _insertMarkdown('## ', '')),
            _buildToolbarButton('H3', () => _insertMarkdown('### ', '')),
            const VerticalDivider(),
            _buildToolbarButton('B', () => _insertMarkdown('**', '**')),
            _buildToolbarButton('I', () => _insertMarkdown('*', '*')),
            _buildToolbarButton('U', () => _insertMarkdown('<u>', '</u>')),
            const VerticalDivider(),
            _buildToolbarButton('•', () => _insertMarkdown('- ', '')),
            _buildToolbarButton('1.', () => _insertMarkdown('1. ', '')),
            _buildToolbarButton('[]', () => _insertMarkdown('- [ ] ', '')),
            const VerticalDivider(),
            _buildToolbarButton('Link', () => _insertMarkdown('[', '](url)')),
            _buildToolbarButton('Image', () => _insertMarkdown('![', '](url)')),
            _buildToolbarButton('Code', () => _insertMarkdown('`', '`')),
            _buildToolbarButton('Quote', () => _insertMarkdown('> ', '')),
          ],
        ),
      ),
    );
  }

  Widget _buildToolbarButton(String label, VoidCallback onPressed) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 2),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.amber.withValues(alpha: 0.1),
          foregroundColor: Colors.amber.shade800,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          minimumSize: const Size(32, 32),
        ),
        child: Text(label, style: const TextStyle(fontSize: 12)),
      ),
    );
  }

  void _insertMarkdown(String before, String after) {
    final text = _contentController.text;
    final selection = _contentController.selection;
    final selectedText = selection.textInside(text);
    
    final newText = text.replaceRange(
      selection.start,
      selection.end,
      '$before$selectedText$after',
    );
    
    _contentController.text = newText;
    _contentController.selection = TextSelection.collapsed(
      offset: selection.start + before.length + selectedText.length,
    );
  }

  void _showFormattingToolbar() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Markdown Cheat Sheet',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildCheatSheetItem('# Heading 1', 'Large heading'),
            _buildCheatSheetItem('## Heading 2', 'Medium heading'),
            _buildCheatSheetItem('**Bold text**', 'Bold formatting'),
            _buildCheatSheetItem('*Italic text*', 'Italic formatting'),
            _buildCheatSheetItem('- List item', 'Bullet list'),
            _buildCheatSheetItem('1. Numbered item', 'Numbered list'),
            _buildCheatSheetItem('[Link text](url)', 'Hyperlink'),
            _buildCheatSheetItem('![Alt text](url)', 'Image'),
            _buildCheatSheetItem('`code`', 'Inline code'),
            _buildCheatSheetItem('> Quote', 'Blockquote'),
          ],
        ),
      ),
    );
  }

  Widget _buildCheatSheetItem(String syntax, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              syntax,
              style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              description,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ),
        ],
      ),
    );
  }

  void _saveNote() {
    setState(() => _hasUnsavedChanges = false);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Note saved successfully'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
