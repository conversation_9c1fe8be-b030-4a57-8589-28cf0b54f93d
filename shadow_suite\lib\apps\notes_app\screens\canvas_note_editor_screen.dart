import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/note_models.dart';
import '../services/notes_service.dart';

/// Canvas Note Editor with drawing capabilities and color support
class CanvasNoteEditorScreen extends ConsumerStatefulWidget {
  final Note? note;
  final bool isNewNote;

  const CanvasNoteEditorScreen({
    super.key,
    this.note,
    this.isNewNote = false,
  });

  @override
  ConsumerState<CanvasNoteEditorScreen> createState() => _CanvasNoteEditorScreenState();
}

class _CanvasNoteEditorScreenState extends ConsumerState<CanvasNoteEditorScreen> {
  late TextEditingController _titleController;
  late Color _selectedColor;
  late NotePriority _selectedPriority;
  List<DrawingStroke> _strokes = [];
  List<String> _tags = [];
  bool _isPinned = false;
  bool _hasUnsavedChanges = false;

  // Drawing settings
  Color _currentStrokeColor = Colors.black;
  double _currentStrokeWidth = 3.0;
  Color _canvasBackgroundColor = Colors.white;

  final List<Color> _availableColors = [
    Colors.white,
    Colors.red.shade100,
    Colors.orange.shade100,
    Colors.yellow.shade100,
    Colors.green.shade100,
    Colors.blue.shade100,
    Colors.purple.shade100,
    Colors.pink.shade100,
    Colors.grey.shade100,
  ];

  final List<Color> _strokeColors = [
    Colors.black,
    Colors.red,
    Colors.blue,
    Colors.green,
    Colors.orange,
    Colors.purple,
    Colors.pink,
    Colors.brown,
  ];

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.note?.title ?? '');
    _selectedColor = widget.note?.color ?? Colors.white;
    _selectedPriority = widget.note?.priority ?? NotePriority.medium;
    _tags = List.from(widget.note?.tags ?? []);
    _isPinned = widget.note?.isPinned ?? false;

    // Load existing canvas data
    if (widget.note?.canvasData != null) {
      _strokes = List.from(widget.note!.canvasData!.strokes);
      _canvasBackgroundColor = widget.note!.canvasData!.backgroundColor;
    }

    _titleController.addListener(_onContentChanged);
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  void _onContentChanged() {
    if (!_hasUnsavedChanges) {
      setState(() => _hasUnsavedChanges = true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _selectedColor,
      appBar: AppBar(
        title: Text(widget.isNewNote ? 'New Canvas Note' : 'Edit Canvas'),
        backgroundColor: _selectedColor,
        foregroundColor: _getContrastColor(_selectedColor),
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(_isPinned ? Icons.push_pin : Icons.push_pin_outlined),
            onPressed: () => setState(() => _isPinned = !_isPinned),
          ),
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: _clearCanvas,
          ),
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _hasUnsavedChanges ? _saveNote : null,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildColorPicker(),
          _buildTitleSection(),
          _buildDrawingToolbar(),
          Expanded(child: _buildCanvas()),
          _buildTagsSection(),
        ],
      ),
    );
  }

  Widget _buildColorPicker() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          const Text('Note Color: ', style: TextStyle(fontWeight: FontWeight.bold)),
          Expanded(
            child: SizedBox(
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _availableColors.length,
                itemBuilder: (context, index) {
                  final color = _availableColors[index];
                  final isSelected = color == _selectedColor;
                  return GestureDetector(
                    onTap: () => setState(() => _selectedColor = color),
                    child: Container(
                      width: 40,
                      height: 40,
                      margin: const EdgeInsets.only(right: 8),
                      decoration: BoxDecoration(
                        color: color,
                        border: Border.all(
                          color: isSelected ? Colors.black : Colors.grey,
                          width: isSelected ? 3 : 1,
                        ),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: isSelected
                          ? const Icon(Icons.check, color: Colors.black)
                          : null,
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTitleSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: TextField(
        controller: _titleController,
        style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        decoration: const InputDecoration(
          hintText: 'Canvas title...',
          border: InputBorder.none,
        ),
        maxLines: null,
      ),
    );
  }

  Widget _buildDrawingToolbar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Column(
        children: [
          // Stroke color picker
          Row(
            children: [
              const Text('Pen Color: ', style: TextStyle(fontWeight: FontWeight.bold)),
              Expanded(
                child: SizedBox(
                  height: 30,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _strokeColors.length,
                    itemBuilder: (context, index) {
                      final color = _strokeColors[index];
                      final isSelected = color == _currentStrokeColor;
                      return GestureDetector(
                        onTap: () => setState(() => _currentStrokeColor = color),
                        child: Container(
                          width: 30,
                          height: 30,
                          margin: const EdgeInsets.only(right: 8),
                          decoration: BoxDecoration(
                            color: color,
                            border: Border.all(
                              color: isSelected ? Colors.white : Colors.grey,
                              width: isSelected ? 3 : 1,
                            ),
                            borderRadius: BorderRadius.circular(15),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // Stroke width slider
          Row(
            children: [
              const Text('Pen Size: ', style: TextStyle(fontWeight: FontWeight.bold)),
              Expanded(
                child: Slider(
                  value: _currentStrokeWidth,
                  min: 1.0,
                  max: 10.0,
                  divisions: 9,
                  label: _currentStrokeWidth.round().toString(),
                  onChanged: (value) => setState(() => _currentStrokeWidth = value),
                ),
              ),
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: _currentStrokeColor,
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCanvas() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _canvasBackgroundColor,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: CustomPaint(
          painter: CanvasPainter(_strokes),
          child: GestureDetector(
            onPanStart: _onPanStart,
            onPanUpdate: _onPanUpdate,
            onPanEnd: _onPanEnd,
            child: Container(
              width: double.infinity,
              height: double.infinity,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTagsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text('Tags: ', style: TextStyle(fontWeight: FontWeight.bold)),
              IconButton(
                icon: const Icon(Icons.add),
                onPressed: _showAddTagDialog,
                iconSize: 20,
              ),
            ],
          ),
          if (_tags.isNotEmpty)
            Wrap(
              spacing: 8,
              children: _tags.map((tag) {
                return Chip(
                  label: Text(tag),
                  onDeleted: () {
                    setState(() => _tags.remove(tag));
                    _onContentChanged();
                  },
                  backgroundColor: _selectedColor.withValues(alpha: 0.3),
                );
              }).toList(),
            ),
        ],
      ),
    );
  }

  void _onPanStart(DragStartDetails details) {
    final newStroke = DrawingStroke(
      points: [details.localPosition],
      color: _currentStrokeColor,
      strokeWidth: _currentStrokeWidth,
      createdAt: DateTime.now(),
    );
    setState(() => _strokes.add(newStroke));
    _onContentChanged();
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (_strokes.isNotEmpty) {
      final lastStroke = _strokes.last;
      final updatedStroke = lastStroke.copyWith(
        points: [...lastStroke.points, details.localPosition],
      );
      setState(() => _strokes[_strokes.length - 1] = updatedStroke);
    }
  }

  void _onPanEnd(DragEndDetails details) {
    // Stroke is complete
  }

  void _clearCanvas() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Canvas'),
        content: const Text('Are you sure you want to clear all drawings?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() => _strokes.clear());
              _onContentChanged();
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Clear', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Color _getContrastColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  void _showAddTagDialog() {
    String newTag = '';
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Tag'),
        content: TextField(
          onChanged: (value) => newTag = value,
          decoration: const InputDecoration(
            hintText: 'Enter tag name',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (newTag.isNotEmpty && !_tags.contains(newTag)) {
                setState(() => _tags.add(newTag));
                _onContentChanged();
              }
              Navigator.pop(context);
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  Future<void> _saveNote() async {
    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a title')),
      );
      return;
    }

    try {
      final notesService = ref.read(notesServiceProvider);
      final now = DateTime.now();

      final canvasData = CanvasData(
        strokes: _strokes,
        backgroundColor: _canvasBackgroundColor,
        canvasSize: const Size(400, 600), // Default size
      );

      final note = Note(
        id: widget.note?.id ?? now.millisecondsSinceEpoch.toString(),
        title: _titleController.text.trim(),
        content: '', // Content is in canvas data
        type: NoteType.canvas,
        priority: _selectedPriority,
        color: _selectedColor,
        tags: _tags,
        createdAt: widget.note?.createdAt ?? now,
        updatedAt: now,
        isPinned: _isPinned,
        isArchived: widget.note?.isArchived ?? false,
        isFavorite: widget.note?.isFavorite ?? false,
        canvasData: canvasData,
      );

      if (widget.isNewNote) {
        await notesService.addNote(note);
      } else {
        await notesService.updateNote(note);
      }

      setState(() => _hasUnsavedChanges = false);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Canvas note saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving canvas note: $e')),
        );
      }
    }
  }
}

/// Custom painter for drawing strokes on canvas
class CanvasPainter extends CustomPainter {
  final List<DrawingStroke> strokes;

  CanvasPainter(this.strokes);

  @override
  void paint(Canvas canvas, Size size) {
    for (final stroke in strokes) {
      final paint = Paint()
        ..color = stroke.color
        ..strokeWidth = stroke.strokeWidth
        ..strokeCap = StrokeCap.round
        ..style = PaintingStyle.stroke;

      if (stroke.points.length > 1) {
        final path = Path();
        path.moveTo(stroke.points.first.dx, stroke.points.first.dy);
        
        for (int i = 1; i < stroke.points.length; i++) {
          path.lineTo(stroke.points[i].dx, stroke.points[i].dy);
        }
        
        canvas.drawPath(path, paint);
      } else if (stroke.points.isNotEmpty) {
        // Draw a single point
        canvas.drawCircle(stroke.points.first, stroke.strokeWidth / 2, paint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
