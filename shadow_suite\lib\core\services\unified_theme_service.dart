import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../theme/app_theme.dart';
import '../themes/comprehensive_theme_system.dart';
import '../themes/theme_models_base.dart';

/// Unified theme service for consistent theming across all mini-apps
class UnifiedThemeService {
  static ShadowSuiteTheme? _currentTheme;
  static final Map<String, Color> _appColors = {
    'memo_suite': AppTheme.memoSuiteColor,
    'islamic_app': AppTheme.islamicAppColor,
    'money_manager': AppTheme.moneyManagerColor,
    'excel_to_app': AppTheme.excelToAppColor,
    'file_manager': AppTheme.toolsBuilderColor,
    'shadow_player': const Color(0xFFE74C3C),
    'money_flow': AppTheme.moneyFlowColor,
  };

  /// Initialize the unified theme system
  static void initialize() {
    ComprehensiveThemeSystem.initialize();
    _currentTheme = ComprehensiveThemeSystem.currentTheme;
  }

  /// Get current theme
  static ShadowSuiteTheme get currentTheme {
    return _currentTheme ?? ComprehensiveThemeSystem.currentTheme;
  }

  /// Set theme for all apps
  static void setTheme(String themeId) {
    ComprehensiveThemeSystem.setTheme(themeId);
    _currentTheme = ComprehensiveThemeSystem.currentTheme;
  }

  /// Get app-specific color
  static Color getAppColor(String appId) {
    return _appColors[appId] ?? AppTheme.primaryColor;
  }

  /// Get themed color scheme for specific app
  static ColorScheme getAppColorScheme(String appId, {bool isDark = false}) {
    final appColor = getAppColor(appId);
    return ColorScheme.fromSeed(
      seedColor: appColor,
      brightness: isDark ? Brightness.dark : Brightness.light,
    );
  }

  /// Create unified theme data for specific app
  static ThemeData createAppTheme(String appId, {bool isDark = false}) {
    final theme = currentTheme;
    final appColor = getAppColor(appId);
    final colorScheme = getAppColorScheme(appId, isDark: isDark);

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      textTheme: theme.typography,

      // App Bar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: appColor,
        foregroundColor: Colors.white,
        elevation: theme.customizations.elevation,
        centerTitle: true,
        titleTextStyle: theme.typography.titleLarge?.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),

      // Card Theme
      cardTheme: CardThemeData(
        elevation: theme.customizations.elevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            theme.customizations.borderRadius,
          ),
        ),
        margin: EdgeInsets.all(theme.customizations.spacing / 2),
      ),

      // Button Themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: appColor,
          foregroundColor: Colors.white,
          elevation: theme.customizations.elevation,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              theme.customizations.borderRadius,
            ),
          ),
          padding: theme.customizations.padding,
          minimumSize: Size(0, theme.customizations.buttonHeight),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: appColor,
          side: BorderSide(color: appColor),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              theme.customizations.borderRadius,
            ),
          ),
          padding: theme.customizations.padding,
          minimumSize: Size(0, theme.customizations.buttonHeight),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: appColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              theme.customizations.borderRadius,
            ),
          ),
          padding: theme.customizations.padding,
          minimumSize: Size(0, theme.customizations.buttonHeight),
        ),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            theme.customizations.borderRadius,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            theme.customizations.borderRadius,
          ),
          borderSide: BorderSide(color: appColor, width: 2),
        ),
        contentPadding: theme.customizations.padding,
      ),

      // List Tile Theme
      listTileTheme: ListTileThemeData(
        contentPadding: theme.customizations.padding,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            theme.customizations.borderRadius,
          ),
        ),
      ),

      // Icon Theme
      iconTheme: IconThemeData(
        size: theme.customizations.iconSize,
        color: colorScheme.onSurface,
      ),

      // Floating Action Button Theme
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: appColor,
        foregroundColor: Colors.white,
        elevation: theme.customizations.elevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            theme.customizations.borderRadius * 2,
          ),
        ),
      ),

      // Tab Bar Theme
      tabBarTheme: TabBarThemeData(
        labelColor: appColor,
        unselectedLabelColor: colorScheme.onSurface.withValues(alpha: 0.6),
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(color: appColor, width: 3),
        ),
      ),

      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        selectedItemColor: appColor,
        unselectedItemColor: colorScheme.onSurface.withValues(alpha: 0.6),
        elevation: theme.customizations.elevation,
      ),

      // Drawer Theme
      drawerTheme: DrawerThemeData(
        elevation: theme.customizations.elevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(theme.customizations.borderRadius),
            bottomRight: Radius.circular(theme.customizations.borderRadius),
          ),
        ),
      ),

      // Dialog Theme
      dialogTheme: DialogThemeData(
        elevation: theme.customizations.elevation * 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            theme.customizations.borderRadius * 2,
          ),
        ),
      ),

      // Snack Bar Theme
      snackBarTheme: SnackBarThemeData(
        backgroundColor: appColor,
        contentTextStyle: const TextStyle(color: Colors.white),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            theme.customizations.borderRadius,
          ),
        ),
        behavior: SnackBarBehavior.floating,
      ),

      // Switch Theme
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return appColor;
          }
          return null;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return appColor.withValues(alpha: 0.5);
          }
          return null;
        }),
      ),

      // Checkbox Theme
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return appColor;
          }
          return null;
        }),
      ),

      // Radio Theme
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return appColor;
          }
          return null;
        }),
      ),

      // Slider Theme
      sliderTheme: SliderThemeData(
        activeTrackColor: appColor,
        thumbColor: appColor,
        overlayColor: appColor.withValues(alpha: 0.2),
      ),

      // Progress Indicator Theme
      progressIndicatorTheme: ProgressIndicatorThemeData(color: appColor),
    );
  }

  /// Get consistent spacing values
  static double getSpacing(SpacingSize size) {
    final spacing = currentTheme.customizations.spacing;
    switch (size) {
      case SpacingSize.xs:
        return spacing * 0.25;
      case SpacingSize.sm:
        return spacing * 0.5;
      case SpacingSize.md:
        return spacing;
      case SpacingSize.lg:
        return spacing * 1.5;
      case SpacingSize.xl:
        return spacing * 2;
      case SpacingSize.xxl:
        return spacing * 3;
    }
  }

  /// Get consistent border radius
  static double getBorderRadius(BorderRadiusSize size) {
    final radius = currentTheme.customizations.borderRadius;
    switch (size) {
      case BorderRadiusSize.xs:
        return radius * 0.25;
      case BorderRadiusSize.sm:
        return radius * 0.5;
      case BorderRadiusSize.md:
        return radius;
      case BorderRadiusSize.lg:
        return radius * 1.5;
      case BorderRadiusSize.xl:
        return radius * 2;
    }
  }

  /// Get consistent elevation
  static double getElevation(ElevationSize size) {
    final elevation = currentTheme.customizations.elevation;
    switch (size) {
      case ElevationSize.none:
        return 0;
      case ElevationSize.sm:
        return elevation * 0.5;
      case ElevationSize.md:
        return elevation;
      case ElevationSize.lg:
        return elevation * 2;
      case ElevationSize.xl:
        return elevation * 3;
    }
  }
}

/// Spacing size enumeration
enum SpacingSize { xs, sm, md, lg, xl, xxl }

/// Border radius size enumeration
enum BorderRadiusSize { xs, sm, md, lg, xl }

/// Elevation size enumeration
enum ElevationSize { none, sm, md, lg, xl }

/// Unified theme provider
final unifiedThemeProvider =
    StateNotifierProvider<UnifiedThemeNotifier, String>((ref) {
      return UnifiedThemeNotifier();
    });

class UnifiedThemeNotifier extends StateNotifier<String> {
  UnifiedThemeNotifier() : super('default_dark') {
    UnifiedThemeService.initialize();
  }

  void setTheme(String themeId) {
    state = themeId;
    UnifiedThemeService.setTheme(themeId);
  }
}

/// App theme provider for specific apps
final appThemeProvider = Provider.family<ThemeData, String>((ref, appId) {
  ref.watch(unifiedThemeProvider); // Watch for theme changes
  return UnifiedThemeService.createAppTheme(appId);
});
