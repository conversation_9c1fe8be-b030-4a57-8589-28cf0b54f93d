import 'dart:async';
import 'dart:math' as math;
import '../models/investment_models.dart';
import '../../../core/database/database_service.dart';
// import '../../../core/services/network_service.dart'; // Reserved for future API integration
import '../../../core/services/error_handler.dart' as error_handler;

class InvestmentService {
  static final List<InvestmentPortfolio> _portfolios = [];
  static final List<Investment> _investments = [];
  static final List<InvestmentTransaction> _transactions = [];
  static final Map<String, MarketData> _marketDataCache = {};
  static Timer? _marketDataTimer;

  // Initialize investment service
  static Future<void> initialize() async {
    await _loadPortfolios();
    await _loadInvestments();
    await _loadTransactions();
    _startMarketDataUpdates();
  }

  // FEATURE 1: Portfolio Management
  static Future<InvestmentPortfolio> createPortfolio({
    required String name,
    required String description,
  }) async {
    try {
      final portfolio = InvestmentPortfolio(
        id: 'portfolio_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        description: description,
        totalValue: 0,
        totalCost: 0,
        totalGainLoss: 0,
        gainLossPercentage: 0,
        investments: [],
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      await DatabaseService.safeInsert(
        'investment_portfolios',
        portfolio.toJson(),
      );
      _portfolios.add(portfolio);

      return portfolio;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.database,
        context: 'Create investment portfolio',
      );
      rethrow;
    }
  }

  // FEATURE 2: Investment Tracking
  static Future<Investment> addInvestment({
    required String portfolioId,
    required String symbol,
    required String name,
    required InvestmentType type,
    required double quantity,
    required double purchasePrice,
    required String currency,
    String? exchange,
  }) async {
    try {
      final totalCost = quantity * purchasePrice;
      final currentPrice = await _getCurrentPrice(symbol) ?? purchasePrice;
      final currentValue = quantity * currentPrice;
      final gainLoss = currentValue - totalCost;
      final gainLossPercentage = totalCost > 0
          ? (gainLoss / totalCost) * 100
          : 0;

      final investment = Investment(
        id: 'investment_${DateTime.now().millisecondsSinceEpoch}',
        portfolioId: portfolioId,
        symbol: symbol,
        name: name,
        type: type,
        quantity: quantity,
        averageCost: purchasePrice,
        currentPrice: currentPrice,
        currentValue: currentValue,
        totalCost: totalCost,
        gainLoss: gainLoss,
        gainLossPercentage: gainLossPercentage.toDouble(),
        currency: currency,
        exchange: exchange ?? '',
        purchaseDate: DateTime.now(),
        lastPriceUpdate: DateTime.now(),
        metadata: {},
      );

      await DatabaseService.safeInsert('investments', investment.toJson());
      _investments.add(investment);

      // Update portfolio totals
      await _updatePortfolioTotals(portfolioId);

      return investment;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.database,
        context: 'Add investment',
      );
      rethrow;
    }
  }

  // FEATURE 3: Real-time Market Data
  static Future<MarketData?> getMarketData(String symbol) async {
    try {
      // Check cache first
      if (_marketDataCache.containsKey(symbol)) {
        final cached = _marketDataCache[symbol]!;
        if (DateTime.now().difference(cached.lastUpdate).inMinutes < 5) {
          return cached;
        }
      }

      // Simulate market data API call (in production, use real API)
      final marketData = await _fetchMarketDataFromAPI(symbol);
      if (marketData != null) {
        _marketDataCache[symbol] = marketData;
      }

      return marketData;
    } catch (error) {
      error_handler.ErrorHandler.handleNetworkError(
        error,
        operation: 'Get market data for $symbol',
      );
      return null;
    }
  }

  // FEATURE 4: Portfolio Performance Analytics
  static Future<InvestmentPerformance> calculatePortfolioPerformance(
    String portfolioId,
  ) async {
    try {
      final portfolio = _portfolios.firstWhere((p) => p.id == portfolioId);
      final investments = _investments
          .where((i) => i.portfolioId == portfolioId)
          .toList();

      if (investments.isEmpty) {
        return InvestmentPerformance(
          investmentId: portfolioId,
          totalReturn: 0,
          annualizedReturn: 0,
          volatility: 0,
          sharpeRatio: 0,
          beta: 0,
          alpha: 0,
          monthlyReturns: {},
          calculatedAt: DateTime.now(),
        );
      }

      final totalReturn = portfolio.gainLossPercentage;
      final annualizedReturn = _calculateAnnualizedReturn(investments);
      final volatility = _calculateVolatility(investments);
      final sharpeRatio = _calculateSharpeRatio(annualizedReturn, volatility);
      final beta = _calculateBeta(investments);
      final alpha = _calculateAlpha(annualizedReturn, beta);
      final monthlyReturns = await _calculateMonthlyReturns(portfolioId);

      return InvestmentPerformance(
        investmentId: portfolioId,
        totalReturn: totalReturn,
        annualizedReturn: annualizedReturn,
        volatility: volatility,
        sharpeRatio: sharpeRatio,
        beta: beta,
        alpha: alpha,
        monthlyReturns: monthlyReturns,
        calculatedAt: DateTime.now(),
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Calculate portfolio performance',
      );
      rethrow;
    }
  }

  // FEATURE 5: Investment Transactions
  static Future<InvestmentTransaction> recordTransaction({
    required String investmentId,
    required InvestmentTransactionType type,
    required double quantity,
    required double price,
    required double fees,
    String? notes,
  }) async {
    try {
      final totalAmount = (quantity * price) + fees;

      final transaction = InvestmentTransaction(
        id: 'transaction_${DateTime.now().millisecondsSinceEpoch}',
        investmentId: investmentId,
        type: type,
        quantity: quantity,
        price: price,
        totalAmount: totalAmount,
        fees: fees,
        date: DateTime.now(),
        notes: notes ?? '',
        metadata: {},
      );

      await DatabaseService.safeInsert(
        'investment_transactions',
        transaction.toJson(),
      );
      _transactions.add(transaction);

      // Update investment based on transaction
      await _updateInvestmentFromTransaction(transaction);

      return transaction;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.database,
        context: 'Record investment transaction',
      );
      rethrow;
    }
  }

  // FEATURE 6: Dividend Tracking
  static Future<List<InvestmentTransaction>> getDividends({
    String? portfolioId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      var dividends = _transactions
          .where((t) => t.type == InvestmentTransactionType.dividend)
          .toList();

      if (portfolioId != null) {
        final portfolioInvestments = _investments
            .where((i) => i.portfolioId == portfolioId)
            .map((i) => i.id)
            .toSet();
        dividends = dividends
            .where((d) => portfolioInvestments.contains(d.investmentId))
            .toList();
      }

      if (startDate != null) {
        dividends = dividends.where((d) => d.date.isAfter(startDate)).toList();
      }

      if (endDate != null) {
        dividends = dividends.where((d) => d.date.isBefore(endDate)).toList();
      }

      return dividends..sort((a, b) => b.date.compareTo(a.date));
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Get dividends',
      );
      return [];
    }
  }

  // FEATURE 7: Asset Allocation Analysis
  static Map<InvestmentType, double> getAssetAllocation(String portfolioId) {
    try {
      final investments = _investments
          .where((i) => i.portfolioId == portfolioId)
          .toList();
      final totalValue = investments.fold<double>(
        0,
        (sum, inv) => sum + inv.currentValue,
      );

      if (totalValue == 0) return {};

      final allocation = <InvestmentType, double>{};
      for (final investment in investments) {
        final percentage = (investment.currentValue / totalValue) * 100;
        allocation[investment.type] =
            (allocation[investment.type] ?? 0) + percentage;
      }

      return allocation;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Get asset allocation',
      );
      return {};
    }
  }

  // FEATURE 8: Investment Alerts
  static List<InvestmentAlert> getInvestmentAlerts() {
    try {
      final alerts = <InvestmentAlert>[];

      for (final investment in _investments) {
        // Price change alerts
        if (investment.gainLossPercentage < -10) {
          alerts.add(
            InvestmentAlert(
              type: AlertType.priceDown,
              message:
                  '${investment.symbol} is down ${investment.gainLossPercentage.abs().toStringAsFixed(1)}%',
              severity: AlertSeverity.warning,
              investmentId: investment.id,
            ),
          );
        } else if (investment.gainLossPercentage > 20) {
          alerts.add(
            InvestmentAlert(
              type: AlertType.priceUp,
              message:
                  '${investment.symbol} is up ${investment.gainLossPercentage.toStringAsFixed(1)}%',
              severity: AlertSeverity.info,
              investmentId: investment.id,
            ),
          );
        }

        // Volume alerts
        final marketData = _marketDataCache[investment.symbol];
        if (marketData != null && marketData.volume > 1000000) {
          alerts.add(
            InvestmentAlert(
              type: AlertType.highVolume,
              message: '${investment.symbol} has high trading volume',
              severity: AlertSeverity.info,
              investmentId: investment.id,
            ),
          );
        }
      }

      return alerts;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Get investment alerts',
      );
      return [];
    }
  }

  // FEATURE 9: Investment Research
  static Future<InvestmentResearch> getInvestmentResearch(String symbol) async {
    try {
      // Simulate research data (in production, integrate with financial APIs)
      return InvestmentResearch(
        symbol: symbol,
        analystRating: 'Buy',
        targetPrice: 150.0,
        priceTargets: [140.0, 150.0, 160.0],
        recommendations: [
          'Strong fundamentals',
          'Growing market share',
          'Positive earnings outlook',
        ],
        riskFactors: ['Market volatility', 'Regulatory changes', 'Competition'],
        lastUpdated: DateTime.now(),
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Get investment research',
      );
      rethrow;
    }
  }

  // FEATURE 10: Portfolio Rebalancing
  static Future<List<RebalanceRecommendation>> getRebalanceRecommendations(
    String portfolioId,
    Map<InvestmentType, double> targetAllocation,
  ) async {
    try {
      final currentAllocation = getAssetAllocation(portfolioId);
      final recommendations = <RebalanceRecommendation>[];

      for (final entry in targetAllocation.entries) {
        final type = entry.key;
        final targetPercentage = entry.value;
        final currentPercentage = currentAllocation[type] ?? 0;
        final difference = targetPercentage - currentPercentage;

        if (difference.abs() > 5) {
          // 5% threshold
          recommendations.add(
            RebalanceRecommendation(
              investmentType: type,
              currentPercentage: currentPercentage,
              targetPercentage: targetPercentage,
              difference: difference,
              action: difference > 0
                  ? RebalanceAction.buy
                  : RebalanceAction.sell,
              amount: difference.abs(),
            ),
          );
        }
      }

      return recommendations;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Get rebalance recommendations',
      );
      return [];
    }
  }

  // Helper methods
  static Future<void> _loadPortfolios() async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM investment_portfolios',
      );
      _portfolios.clear();
      for (final row in results) {
        _portfolios.add(InvestmentPortfolio.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Load portfolios',
      );
    }
  }

  static Future<void> _loadInvestments() async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM investments',
      );
      _investments.clear();
      for (final row in results) {
        _investments.add(Investment.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Load investments',
      );
    }
  }

  static Future<void> _loadTransactions() async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM investment_transactions',
      );
      _transactions.clear();
      for (final row in results) {
        _transactions.add(InvestmentTransaction.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Load transactions',
      );
    }
  }

  static void _startMarketDataUpdates() {
    _marketDataTimer = Timer.periodic(const Duration(minutes: 5), (
      timer,
    ) async {
      for (final investment in _investments) {
        await getMarketData(investment.symbol);
      }
    });
  }

  static Future<double?> _getCurrentPrice(String symbol) async {
    final marketData = await getMarketData(symbol);
    return marketData?.price;
  }

  static Future<MarketData?> _fetchMarketDataFromAPI(String symbol) async {
    // Simulate API call with realistic data
    await Future.delayed(const Duration(milliseconds: 500));

    final random = math.Random();
    final basePrice = 100 + random.nextDouble() * 400; // $100-$500
    final change = (random.nextDouble() - 0.5) * 10; // -$5 to +$5
    final changePercentage = (change / basePrice) * 100;

    return MarketData(
      symbol: symbol,
      price: basePrice + change,
      change: change,
      changePercentage: changePercentage,
      volume: random.nextInt(10000000).toDouble(),
      marketCap: (basePrice * random.nextInt(1000000000)).toDouble(),
      high52Week: basePrice * (1 + random.nextDouble() * 0.5),
      low52Week: basePrice * (1 - random.nextDouble() * 0.3),
      lastUpdate: DateTime.now(),
    );
  }

  static Future<void> _updatePortfolioTotals(String portfolioId) async {
    try {
      final investments = _investments
          .where((i) => i.portfolioId == portfolioId)
          .toList();
      final totalValue = investments.fold<double>(
        0,
        (sum, inv) => sum + inv.currentValue,
      );
      final totalCost = investments.fold<double>(
        0,
        (sum, inv) => sum + inv.totalCost,
      );
      final totalGainLoss = totalValue - totalCost;
      final gainLossPercentage = totalCost > 0
          ? (totalGainLoss / totalCost) * 100
          : 0;

      await DatabaseService.safeUpdate(
        'investment_portfolios',
        {
          'total_value': totalValue,
          'total_cost': totalCost,
          'total_gain_loss': totalGainLoss,
          'gain_loss_percentage': gainLossPercentage,
          'last_modified': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [portfolioId],
      );

      // Update local cache
      final portfolioIndex = _portfolios.indexWhere((p) => p.id == portfolioId);
      if (portfolioIndex != -1) {
        _portfolios[portfolioIndex] = _portfolios[portfolioIndex].copyWith(
          investments: investments,
        );
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Update portfolio totals',
      );
    }
  }

  static Future<void> _updateInvestmentFromTransaction(
    InvestmentTransaction transaction,
  ) async {
    try {
      final investmentIndex = _investments.indexWhere(
        (i) => i.id == transaction.investmentId,
      );
      if (investmentIndex == -1) return;

      final investment = _investments[investmentIndex];
      double newQuantity = investment.quantity;
      // double newAverageCost = investment.averageCost; // Reserved for future average cost calculations

      switch (transaction.type) {
        case InvestmentTransactionType.buy:
          // final totalCost = (investment.quantity * investment.averageCost) + transaction.totalAmount; // Reserved for future calculations
          newQuantity = investment.quantity + transaction.quantity;
          // newAverageCost = newQuantity > 0 ? totalCost / newQuantity : 0; // Reserved for future calculations
          break;
        case InvestmentTransactionType.sell:
          newQuantity = investment.quantity - transaction.quantity;
          break;
        case InvestmentTransactionType.dividend:
          // Dividends don't affect quantity or cost basis
          break;
        default:
          break;
      }

      final updatedInvestment = investment.copyWith(quantity: newQuantity);
      _investments[investmentIndex] = updatedInvestment;

      await DatabaseService.safeUpdate(
        'investments',
        updatedInvestment.toJson(),
        where: 'id = ?',
        whereArgs: [investment.id],
      );

      await _updatePortfolioTotals(investment.portfolioId);
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Update investment from transaction',
      );
    }
  }

  static double _calculateAnnualizedReturn(List<Investment> investments) {
    // Simplified calculation - in production, use more sophisticated methods
    final totalReturn = investments.fold<double>(
      0,
      (sum, inv) => sum + inv.gainLossPercentage,
    );
    return investments.isNotEmpty ? totalReturn / investments.length : 0;
  }

  static double _calculateVolatility(List<Investment> investments) {
    // Simplified volatility calculation
    if (investments.length < 2) return 0;

    final returns = investments.map((inv) => inv.gainLossPercentage).toList();
    final mean = returns.reduce((a, b) => a + b) / returns.length;
    final variance =
        returns.map((r) => math.pow(r - mean, 2)).reduce((a, b) => a + b) /
        returns.length;
    return math.sqrt(variance);
  }

  static double _calculateSharpeRatio(
    double annualizedReturn,
    double volatility,
  ) {
    const riskFreeRate = 2.0; // Assume 2% risk-free rate
    return volatility > 0 ? (annualizedReturn - riskFreeRate) / volatility : 0;
  }

  static double _calculateBeta(List<Investment> investments) {
    // Simplified beta calculation - in production, compare against market index
    // Calculate actual beta: covariance(investment, market) / variance(market)
    // For simplified calculation, assume portfolio beta is weighted average
    if (investments.isEmpty) return 1.0;

    double totalValue = investments.fold(
      0.0,
      (sum, inv) => sum + inv.currentValue,
    );
    double weightedBeta = 0.0;

    for (final investment in investments) {
      double weight = investment.currentValue / totalValue;
      // Simplified beta based on investment type
      double beta = _getTypicalBeta(investment.type);
      weightedBeta += weight * beta;
    }

    return weightedBeta;
  }

  static double _getTypicalBeta(InvestmentType type) {
    // Typical beta values for different investment types
    switch (type) {
      case InvestmentType.stock:
        return 1.2; // Stocks typically have beta > 1
      case InvestmentType.bond:
        return 0.3; // Bonds have low beta
      case InvestmentType.mutualFund:
        return 1.0; // Mutual funds close to market
      case InvestmentType.etf:
        return 1.0; // ETFs track market
      case InvestmentType.realEstate:
        return 0.8; // Real estate less volatile
      case InvestmentType.cryptocurrency:
        return 2.0; // Crypto highly volatile
      case InvestmentType.commodity:
        return 1.5; // Commodities moderately volatile
      case InvestmentType.option:
        return 1.8; // Options are volatile
      case InvestmentType.future:
        return 1.6; // Futures moderately volatile
      case InvestmentType.forex:
        return 1.4; // Forex moderately volatile
    }
  }

  static double _calculateAlpha(double annualizedReturn, double beta) {
    const marketReturn = 8.0; // Assume 8% market return
    const riskFreeRate = 2.0;
    return annualizedReturn -
        (riskFreeRate + beta * (marketReturn - riskFreeRate));
  }

  static Future<Map<String, double>> _calculateMonthlyReturns(
    String portfolioId,
  ) async {
    // Simplified monthly returns calculation
    final returns = <String, double>{};
    final now = DateTime.now();

    for (int i = 0; i < 12; i++) {
      final month = DateTime(now.year, now.month - i);
      final monthKey =
          '${month.year}-${month.month.toString().padLeft(2, '0')}';
      returns[monthKey] =
          (math.Random().nextDouble() - 0.5) * 20; // -10% to +10%
    }

    return returns;
  }

  // Getters
  static List<InvestmentPortfolio> get portfolios =>
      List.unmodifiable(_portfolios);
  static List<Investment> get investments => List.unmodifiable(_investments);
  static List<InvestmentTransaction> get transactions =>
      List.unmodifiable(_transactions);

  // Dispose
  static void dispose() {
    _marketDataTimer?.cancel();
    _portfolios.clear();
    _investments.clear();
    _transactions.clear();
    _marketDataCache.clear();
  }
}

// Additional data classes for investment features
class InvestmentAlert {
  final AlertType type;
  final String message;
  final AlertSeverity severity;
  final String investmentId;

  const InvestmentAlert({
    required this.type,
    required this.message,
    required this.severity,
    required this.investmentId,
  });
}

class InvestmentResearch {
  final String symbol;
  final String analystRating;
  final double targetPrice;
  final List<double> priceTargets;
  final List<String> recommendations;
  final List<String> riskFactors;
  final DateTime lastUpdated;

  const InvestmentResearch({
    required this.symbol,
    required this.analystRating,
    required this.targetPrice,
    required this.priceTargets,
    required this.recommendations,
    required this.riskFactors,
    required this.lastUpdated,
  });
}

class RebalanceRecommendation {
  final InvestmentType investmentType;
  final double currentPercentage;
  final double targetPercentage;
  final double difference;
  final RebalanceAction action;
  final double amount;

  const RebalanceRecommendation({
    required this.investmentType,
    required this.currentPercentage,
    required this.targetPercentage,
    required this.difference,
    required this.action,
    required this.amount,
  });
}

enum AlertType { priceUp, priceDown, highVolume, news, earnings }

enum AlertSeverity { info, warning, critical }

enum RebalanceAction { buy, sell, hold }
