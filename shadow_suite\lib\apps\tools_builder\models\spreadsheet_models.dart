/// Models for spreadsheet functionality
library;

/// Spreadsheet model
class Spreadsheet {
  final String id;
  final String name;
  final String description;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<SpreadsheetSheet> sheets;

  const Spreadsheet({
    required this.id,
    required this.name,
    required this.description,
    required this.createdAt,
    required this.updatedAt,
    required this.sheets,
  });

  Spreadsheet copyWith({
    String? id,
    String? name,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<SpreadsheetSheet>? sheets,
  }) {
    return Spreadsheet(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      sheets: sheets ?? this.sheets,
    );
  }
}

/// Spreadsheet sheet model
class SpreadsheetSheet {
  final String id;
  final String name;
  final Map<String, SpreadsheetCell> cells;
  final int rows;
  final int columns;

  const SpreadsheetSheet({
    required this.id,
    required this.name,
    required this.cells,
    this.rows = 100,
    this.columns = 26,
  });

  SpreadsheetSheet copyWith({
    String? id,
    String? name,
    Map<String, SpreadsheetCell>? cells,
    int? rows,
    int? columns,
  }) {
    return SpreadsheetSheet(
      id: id ?? this.id,
      name: name ?? this.name,
      cells: cells ?? this.cells,
      rows: rows ?? this.rows,
      columns: columns ?? this.columns,
    );
  }

  SpreadsheetSheet setCell(int row, int column, SpreadsheetCell cell) {
    final address = _getCellAddress(row, column);
    final updatedCells = Map<String, SpreadsheetCell>.from(cells);
    updatedCells[address] = cell;
    return copyWith(cells: updatedCells);
  }

  SpreadsheetCell? getCell(int row, int column) {
    final address = _getCellAddress(row, column);
    return cells[address];
  }

  String _getCellAddress(int row, int column) {
    final columnLetter = String.fromCharCode(65 + column);
    return '$columnLetter${row + 1}';
  }
}

/// Spreadsheet cell model
class SpreadsheetCell {
  final String address;
  final dynamic value;
  final String? formula;
  final CellFormat format;
  final int row;
  final int column;

  const SpreadsheetCell({
    required this.address,
    this.value,
    this.formula,
    this.format = const CellFormat(),
    required this.row,
    required this.column,
  });

  SpreadsheetCell copyWith({
    String? address,
    dynamic value,
    String? formula,
    CellFormat? format,
    int? row,
    int? column,
  }) {
    return SpreadsheetCell(
      address: address ?? this.address,
      value: value ?? this.value,
      formula: formula ?? this.formula,
      format: format ?? this.format,
      row: row ?? this.row,
      column: column ?? this.column,
    );
  }
}

/// Cell format model
class CellFormat {
  final bool bold;
  final bool italic;
  final bool underline;
  final String? backgroundColor;
  final String? textColor;
  final String? fontSize;
  final String? fontFamily;
  final String? textAlign;

  const CellFormat({
    this.bold = false,
    this.italic = false,
    this.underline = false,
    this.backgroundColor,
    this.textColor,
    this.fontSize,
    this.fontFamily,
    this.textAlign,
  });

  CellFormat copyWith({
    bool? bold,
    bool? italic,
    bool? underline,
    String? backgroundColor,
    String? textColor,
    String? fontSize,
    String? fontFamily,
    String? textAlign,
  }) {
    return CellFormat(
      bold: bold ?? this.bold,
      italic: italic ?? this.italic,
      underline: underline ?? this.underline,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      textColor: textColor ?? this.textColor,
      fontSize: fontSize ?? this.fontSize,
      fontFamily: fontFamily ?? this.fontFamily,
      textAlign: textAlign ?? this.textAlign,
    );
  }
}

/// Cell position model
class CellPosition {
  final int row;
  final int column;

  const CellPosition({required this.row, required this.column});

  String toAddress() {
    final columnLetter = String.fromCharCode(65 + column);
    return '$columnLetter${row + 1}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CellPosition && other.row == row && other.column == column;
  }

  @override
  int get hashCode => row.hashCode ^ column.hashCode;

  @override
  String toString() => toAddress();
}

/// Formula engine for calculating cell values
class FormulaEngine {
  static dynamic evaluateFormula(
    String formula,
    Map<String, SpreadsheetCell> cells,
  ) {
    // Simple formula evaluation - can be enhanced
    if (formula.startsWith('=')) {
      final expression = formula.substring(1);

      // Handle simple SUM function
      if (expression.startsWith('SUM(') && expression.endsWith(')')) {
        final range = expression.substring(4, expression.length - 1);
        return _evaluateSum(range, cells);
      }

      // Handle simple arithmetic
      return _evaluateArithmetic(expression, cells);
    }

    return formula;
  }

  static double _evaluateSum(String range, Map<String, SpreadsheetCell> cells) {
    // Simple range evaluation like A1:A5
    if (range.contains(':')) {
      final parts = range.split(':');
      if (parts.length == 2) {
        // For simplicity, just sum the values in the range
        double sum = 0;
        for (final cell in cells.values) {
          if (cell.value is num) {
            sum += (cell.value as num).toDouble();
          }
        }
        return sum;
      }
    }

    return 0;
  }

  static dynamic _evaluateArithmetic(
    String expression,
    Map<String, SpreadsheetCell> cells,
  ) {
    // Very simple arithmetic evaluation
    // In a real implementation, this would be much more sophisticated
    try {
      // Replace cell references with values
      String processedExpression = expression;
      for (final entry in cells.entries) {
        if (processedExpression.contains(entry.key)) {
          final cellValue = entry.value.value?.toString() ?? '0';
          processedExpression = processedExpression.replaceAll(
            entry.key,
            cellValue,
          );
        }
      }

      // Simple evaluation for basic operations
      if (processedExpression.contains('+')) {
        final parts = processedExpression.split('+');
        if (parts.length == 2) {
          final a = double.tryParse(parts[0].trim()) ?? 0;
          final b = double.tryParse(parts[1].trim()) ?? 0;
          return a + b;
        }
      }

      return double.tryParse(processedExpression) ?? 0;
    } catch (e) {
      return '#ERROR';
    }
  }
}
