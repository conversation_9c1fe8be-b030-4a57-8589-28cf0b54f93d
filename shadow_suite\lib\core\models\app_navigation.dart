import 'package:flutter/material.dart';

enum AppSection {
  dashboard,
  memoSuite,
  islamicApp,
  moneyManager,
  excelToApp,
  fileManager,
  shadowPlayer,
  smartGallery,
  settings,
  profile,
}

class NavigationItem {
  final String title;
  final IconData icon;
  final AppSection section;
  final Color? color;
  final List<SubNavigationItem>? subItems;
  final bool isExpandable;

  const NavigationItem({
    required this.title,
    required this.icon,
    required this.section,
    this.color,
    this.subItems,
    this.isExpandable = false,
  });
}

class SubNavigationItem {
  final String title;
  final IconData icon;
  final String route;
  final Color? color;

  const SubNavigationItem({
    required this.title,
    required this.icon,
    required this.route,
    this.color,
  });
}

class AppNavigation {
  static const List<NavigationItem> mainItems = [
    NavigationItem(
      title: 'Dashboard',
      icon: Icons.dashboard,
      section: AppSection.dashboard,
    ),
    NavigationItem(
      title: 'Settings',
      icon: Icons.settings,
      section: AppSection.settings,
    ),
    NavigationItem(
      title: 'Profile',
      icon: Icons.person,
      section: AppSection.profile,
    ),
  ];

  static const List<NavigationItem> appItems = [
    NavigationItem(
      title: 'Memo Suite',
      icon: Icons.note_alt,
      section: AppSection.memoSuite,
      color: Color(0xFF9B59B6),
      isExpandable: true,
      subItems: [
        SubNavigationItem(
          title: 'Dashboard',
          icon: Icons.dashboard,
          route: '/memo-suite/dashboard',
        ),
        SubNavigationItem(
          title: 'Notes',
          icon: Icons.note,
          route: '/memo-suite/notes',
        ),
        SubNavigationItem(
          title: 'Todos',
          icon: Icons.check_box,
          route: '/memo-suite/todos',
        ),
        SubNavigationItem(
          title: 'Voice Memos',
          icon: Icons.mic,
          route: '/memo-suite/voice-memos',
        ),
        SubNavigationItem(
          title: 'Calendar',
          icon: Icons.calendar_today,
          route: '/memo-suite/calendar',
        ),
      ],
    ),
    NavigationItem(
      title: 'Islamic App',
      icon: Icons.mosque,
      section: AppSection.islamicApp,
      color: Color(0xFF27AE60),
      isExpandable: true,
      subItems: [
        SubNavigationItem(
          title: 'Dashboard',
          icon: Icons.dashboard,
          route: '/islamic-app/dashboard',
        ),
        SubNavigationItem(
          title: 'Quran',
          icon: Icons.menu_book,
          route: '/islamic-app/quran',
        ),
        SubNavigationItem(
          title: 'Hadith Collections',
          icon: Icons.library_books,
          route: '/islamic-app/hadith',
        ),
        SubNavigationItem(
          title: 'Tafseer',
          icon: Icons.auto_stories,
          route: '/islamic-app/tafseer',
        ),
        SubNavigationItem(
          title: 'Athkar',
          icon: Icons.beenhere,
          route: '/islamic-app/athkar',
        ),
        SubNavigationItem(
          title: 'Prayer Times',
          icon: Icons.access_time,
          route: '/islamic-app/prayer-times',
        ),
        SubNavigationItem(
          title: 'Qibla Compass',
          icon: Icons.explore,
          route: '/islamic-app/qibla',
        ),
        SubNavigationItem(
          title: 'Bookmarks',
          icon: Icons.bookmark,
          route: '/islamic-app/bookmarks',
        ),
      ],
    ),

    NavigationItem(
      title: 'Money Manager',
      icon: Icons.account_balance_wallet,
      section: AppSection.moneyManager,
      color: Color(0xFF1ABC9C),
      isExpandable: true,
      subItems: [
        SubNavigationItem(
          title: 'Dashboard',
          icon: Icons.dashboard,
          route: '/money-manager/dashboard',
        ),
        SubNavigationItem(
          title: 'Accounts',
          icon: Icons.account_balance,
          route: '/money-manager/accounts',
        ),
        SubNavigationItem(
          title: 'Transactions',
          icon: Icons.receipt_long,
          route: '/money-manager/transactions',
        ),
        SubNavigationItem(
          title: 'Categories',
          icon: Icons.category,
          route: '/money-manager/categories',
        ),
        SubNavigationItem(
          title: 'Budgets',
          icon: Icons.pie_chart,
          route: '/money-manager/budgets',
        ),
        SubNavigationItem(
          title: 'Goals',
          icon: Icons.flag,
          route: '/money-manager/goals',
        ),
        SubNavigationItem(
          title: 'Reports',
          icon: Icons.analytics,
          route: '/money-manager/reports',
        ),
      ],
    ),

    NavigationItem(
      title: 'Excel to App',
      icon: Icons.table_view,
      section: AppSection.excelToApp,
      color: Color(0xFF3498DB),
      isExpandable: true,
      subItems: [
        SubNavigationItem(
          title: 'Dashboard',
          icon: Icons.dashboard,
          route: '/excel-to-app/dashboard',
        ),
        SubNavigationItem(
          title: 'Create Tool',
          icon: Icons.add_circle,
          route: '/excel-to-app/create',
        ),
        SubNavigationItem(
          title: 'My Tools',
          icon: Icons.folder,
          route: '/excel-to-app/my-tools',
        ),
        SubNavigationItem(
          title: 'Import Tools',
          icon: Icons.file_upload,
          route: '/excel-to-app/import',
        ),
      ],
    ),

    NavigationItem(
      title: 'File Manager',
      icon: Icons.folder_open,
      section: AppSection.fileManager,
      color: Color(0xFFE67E22),
      isExpandable: true,
      subItems: [
        SubNavigationItem(
          title: 'Dashboard',
          icon: Icons.dashboard,
          route: '/file-manager/dashboard',
        ),
        SubNavigationItem(
          title: 'Browse Files',
          icon: Icons.folder,
          route: '/file-manager/browse',
        ),
        SubNavigationItem(
          title: 'Cloud Storage',
          icon: Icons.cloud,
          route: '/file-manager/cloud',
        ),
        SubNavigationItem(
          title: 'Network Shares',
          icon: Icons.share,
          route: '/file-manager/network',
        ),
        SubNavigationItem(
          title: 'Media Player',
          icon: Icons.play_circle,
          route: '/file-manager/media',
        ),
        SubNavigationItem(
          title: 'File Operations',
          icon: Icons.build,
          route: '/file-manager/operations',
        ),
      ],
    ),

    NavigationItem(
      title: 'ShadowPlayer',
      icon: Icons.play_circle_filled,
      section: AppSection.shadowPlayer,
      color: Color(0xFFE74C3C),
      isExpandable: true,
      subItems: [
        SubNavigationItem(
          title: 'Video Library',
          icon: Icons.video_library,
          route: '/shadow-player/video',
        ),
        SubNavigationItem(
          title: 'Music Library',
          icon: Icons.library_music,
          route: '/shadow-player/music',
        ),
        SubNavigationItem(
          title: 'Player Settings',
          icon: Icons.settings,
          route: '/shadow-player/settings',
        ),
      ],
    ),

    NavigationItem(
      title: 'SmartGallery+',
      icon: Icons.photo_library,
      section: AppSection.smartGallery,
      color: Color(0xFF8E44AD),
      isExpandable: true,
      subItems: [
        SubNavigationItem(
          title: 'All Media',
          icon: Icons.photo_library,
          route: '/smart-gallery/all',
        ),
        SubNavigationItem(
          title: 'Favorites',
          icon: Icons.favorite,
          route: '/smart-gallery/favorites',
        ),
        SubNavigationItem(
          title: 'People',
          icon: Icons.face,
          route: '/smart-gallery/people',
        ),
        SubNavigationItem(
          title: 'AI Insights',
          icon: Icons.analytics,
          route: '/smart-gallery/insights',
        ),
      ],
    ),

    NavigationItem(
      title: 'Settings',
      icon: Icons.settings,
      section: AppSection.settings,
      color: Color(0xFF95A5A6),
      isExpandable: false,
      subItems: [],
    ),
  ];
}
