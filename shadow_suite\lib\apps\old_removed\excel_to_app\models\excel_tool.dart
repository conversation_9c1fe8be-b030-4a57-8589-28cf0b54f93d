// import 'package:flutter/material.dart'; // Reserved for future UI integration
import 'ui_component.dart';

class ExcelTool {
  final String id;
  final String name;
  final String description;
  final DateTime createdAt;
  final DateTime lastModified;
  final List<UIComponent> uiComponents;

  const ExcelTool({
    required this.id,
    required this.name,
    required this.description,
    required this.createdAt,
    required this.lastModified,
    this.uiComponents = const [],
  });

  ExcelTool copyWith({
    String? id,
    String? name,
    String? description,
    DateTime? createdAt,
    DateTime? lastModified,
    List<UIComponent>? uiComponents,
  }) {
    return ExcelTool(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      lastModified: lastModified ?? this.lastModified,
      uiComponents: uiComponents ?? this.uiComponents,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'lastModified': lastModified.toIso8601String(),
      'uiComponents': uiComponents.map((c) => c.toJson()).toList(),
    };
  }

  factory ExcelTool.fromJson(Map<String, dynamic> json) {
    return ExcelTool(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastModified: DateTime.parse(json['lastModified'] as String),
      uiComponents: (json['uiComponents'] as List<dynamic>?)
          ?.map((c) => UIComponent.fromJson(c as Map<String, dynamic>))
          .toList() ?? [],
    );
  }
}
