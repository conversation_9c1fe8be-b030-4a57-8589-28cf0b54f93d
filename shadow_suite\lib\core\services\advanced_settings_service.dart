import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../database/database_service.dart';
import '../services/error_handler.dart' as error_handler;

/// Advanced settings service with 100+ customization options
class AdvancedSettingsService {
  static bool _isInitialized = false;
  static final StreamController<SettingsUpdate> _updateController =
      StreamController<SettingsUpdate>.broadcast();
  static final Map<String, dynamic> _settingsCache = {};

  /// Initialize advanced settings service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize database tables
      await _initializeDatabaseTables();

      // Load default settings
      await _loadDefaultSettings();

      // Load user settings
      await _loadUserSettings();

      _isInitialized = true;
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.operation,
        context: 'Initialize advanced settings service',
      );
    }
  }

  /// Initialize database tables
  static Future<void> _initializeDatabaseTables() async {
    await DatabaseService.safeExecute('''
      CREATE TABLE IF NOT EXISTS advanced_settings (
        id TEXT PRIMARY KEY,
        category TEXT NOT NULL,
        key TEXT NOT NULL,
        value TEXT NOT NULL,
        data_type TEXT NOT NULL,
        default_value TEXT NOT NULL,
        description TEXT,
        min_value REAL,
        max_value REAL,
        options TEXT,
        requires_restart INTEGER DEFAULT 0,
        is_advanced INTEGER DEFAULT 0,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    await DatabaseService.safeExecute('''
      CREATE TABLE IF NOT EXISTS settings_profiles (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        settings_data TEXT NOT NULL,
        is_active INTEGER DEFAULT 0,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    await DatabaseService.safeExecute('''
      CREATE TABLE IF NOT EXISTS keyboard_shortcuts (
        id TEXT PRIMARY KEY,
        action TEXT NOT NULL,
        key_combination TEXT NOT NULL,
        description TEXT,
        category TEXT NOT NULL,
        is_enabled INTEGER DEFAULT 1,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');
  }

  /// Load default settings
  static Future<void> _loadDefaultSettings() async {
    final defaultSettings = _getDefaultSettings();

    for (final setting in defaultSettings) {
      try {
        // Check if setting already exists
        final existing = await DatabaseService.safeQuery(
          'SELECT id FROM advanced_settings WHERE key = ? AND category = ?',
          [setting.key, setting.category],
        );

        if (existing.isEmpty) {
          // Insert new setting
          await DatabaseService.safeInsert('advanced_settings', {
            'id':
                'setting_${DateTime.now().millisecondsSinceEpoch}_${setting.key}',
            'category': setting.category,
            'key': setting.key,
            'value': setting.defaultValue,
            'data_type': setting.dataType.toString().split('.').last,
            'default_value': setting.defaultValue,
            'description': setting.description,
            'min_value': setting.minValue,
            'max_value': setting.maxValue,
            'options': setting.options?.join(','),
            'requires_restart': setting.requiresRestart ? 1 : 0,
            'is_advanced': setting.isAdvanced ? 1 : 0,
            'created_at': DateTime.now().millisecondsSinceEpoch,
            'updated_at': DateTime.now().millisecondsSinceEpoch,
          });
        }
      } catch (e) {
        // Continue with next setting
      }
    }
  }

  /// Load user settings
  static Future<void> _loadUserSettings() async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT category, key, value, data_type FROM advanced_settings',
      );

      _settingsCache.clear();

      for (final row in results) {
        final category = row['category'] as String;
        final key = row['key'] as String;
        final value = row['value'] as String;
        final dataType = row['data_type'] as String;

        final settingKey = '${category}_$key';
        _settingsCache[settingKey] = _parseValue(value, dataType);
      }
    } catch (e) {
      // Use default settings
    }
  }

  /// Parse value based on data type
  static dynamic _parseValue(String value, String dataType) {
    switch (dataType) {
      case 'bool':
        return value.toLowerCase() == 'true';
      case 'int':
        return int.tryParse(value) ?? 0;
      case 'double':
        return double.tryParse(value) ?? 0.0;
      case 'color':
        return Color(int.tryParse(value) ?? 0xFF000000);
      case 'list':
        return value.split(',').where((item) => item.isNotEmpty).toList();
      default:
        return value;
    }
  }

  /// Get setting value
  static T getSetting<T>(String category, String key, T defaultValue) {
    final settingKey = '${category}_$key';
    return _settingsCache[settingKey] as T? ?? defaultValue;
  }

  /// Set setting value
  static Future<void> setSetting(
    String category,
    String key,
    dynamic value,
  ) async {
    try {
      final settingKey = '${category}_$key';
      _settingsCache[settingKey] = value;

      // Convert value to string for storage
      String stringValue;
      if (value is Color) {
        stringValue = '0x${value.toARGB32().toRadixString(16).padLeft(8, '0')}';
      } else if (value is List) {
        stringValue = value.join(',');
      } else {
        stringValue = value.toString();
      }

      // Update database
      await DatabaseService.safeUpdate(
        'advanced_settings',
        {
          'value': stringValue,
          'updated_at': DateTime.now().millisecondsSinceEpoch,
        },
        where: 'category = ? AND key = ?',
        whereArgs: [category, key],
      );

      // Notify listeners
      _notifyUpdate(
        SettingsUpdate(
          category: category,
          key: key,
          value: value,
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.operation,
        context: 'Set setting failed',
      );
    }
  }

  /// Get all settings by category
  static Map<String, dynamic> getSettingsByCategory(String category) {
    final categorySettings = <String, dynamic>{};

    for (final entry in _settingsCache.entries) {
      if (entry.key.startsWith('${category}_')) {
        final key = entry.key.substring(category.length + 1);
        categorySettings[key] = entry.value;
      }
    }

    return categorySettings;
  }

  /// Reset settings to default
  static Future<void> resetToDefaults([String? category]) async {
    try {
      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (category != null) {
        whereClause = 'WHERE category = ?';
        whereArgs = [category];
      }

      // Reset to default values
      await DatabaseService.safeExecute(
        'UPDATE advanced_settings SET value = default_value, updated_at = ? $whereClause',
        [DateTime.now().millisecondsSinceEpoch, ...whereArgs],
      );

      // Reload settings
      await _loadUserSettings();

      // Notify listeners
      _notifyUpdate(
        SettingsUpdate(
          category: category ?? 'all',
          key: 'reset',
          value: true,
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.operation,
        context: 'Reset settings failed',
      );
    }
  }

  /// Export settings profile
  static Future<String> exportSettingsProfile(
    String name,
    String description,
  ) async {
    try {
      final profileId = 'profile_${DateTime.now().millisecondsSinceEpoch}';
      final settingsData = jsonEncode(_settingsCache);

      await DatabaseService.safeInsert('settings_profiles', {
        'id': profileId,
        'name': name,
        'description': description,
        'settings_data': settingsData,
        'is_active': 0,
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      });

      return profileId;
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.operation,
        context: 'Export settings profile failed',
      );
      rethrow;
    }
  }

  /// Import settings profile
  static Future<void> importSettingsProfile(String profileId) async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT settings_data FROM settings_profiles WHERE id = ?',
        [profileId],
      );

      if (results.isNotEmpty) {
        final settingsData = jsonDecode(
          results.first['settings_data'] as String,
        );

        // Apply settings
        for (final entry in settingsData.entries) {
          final parts = entry.key.split('_');
          if (parts.length >= 2) {
            final category = parts[0];
            final key = parts.sublist(1).join('_');
            await setSetting(category, key, entry.value);
          }
        }

        // Mark profile as active
        await DatabaseService.safeExecute(
          'UPDATE settings_profiles SET is_active = 0',
        );

        await DatabaseService.safeUpdate(
          'settings_profiles',
          {'is_active': 1},
          where: 'id = ?',
          whereArgs: [profileId],
        );
      }
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.operation,
        context: 'Import settings profile failed',
      );
    }
  }

  /// Get default settings definitions
  static List<SettingDefinition> _getDefaultSettings() {
    return [
      // UI/UX Settings
      SettingDefinition(
        category: 'ui',
        key: 'primary_color',
        defaultValue: '0xFF2C3E50',
        dataType: SettingDataType.color,
        description: 'Primary theme color',
      ),
      SettingDefinition(
        category: 'ui',
        key: 'accent_color',
        defaultValue: '0xFF3498DB',
        dataType: SettingDataType.color,
        description: 'Accent theme color',
      ),
      SettingDefinition(
        category: 'ui',
        key: 'font_family',
        defaultValue: 'Roboto',
        dataType: SettingDataType.string,
        description: 'Default font family',
        options: [
          'Roboto',
          'Open Sans',
          'Lato',
          'Montserrat',
          'Source Sans Pro',
        ],
      ),
      SettingDefinition(
        category: 'ui',
        key: 'font_size_scale',
        defaultValue: '1.0',
        dataType: SettingDataType.double,
        description: 'Font size scaling factor',
        minValue: 0.8,
        maxValue: 2.0,
      ),
      SettingDefinition(
        category: 'ui',
        key: 'border_radius',
        defaultValue: '8.0',
        dataType: SettingDataType.double,
        description: 'Default border radius for UI elements',
        minValue: 0.0,
        maxValue: 20.0,
      ),
      SettingDefinition(
        category: 'ui',
        key: 'elevation',
        defaultValue: '4.0',
        dataType: SettingDataType.double,
        description: 'Default elevation for cards and dialogs',
        minValue: 0.0,
        maxValue: 24.0,
      ),
      SettingDefinition(
        category: 'ui',
        key: 'animation_duration',
        defaultValue: '300',
        dataType: SettingDataType.int,
        description: 'Default animation duration in milliseconds',
        minValue: 100,
        maxValue: 1000,
      ),
      SettingDefinition(
        category: 'ui',
        key: 'enable_animations',
        defaultValue: 'true',
        dataType: SettingDataType.bool,
        description: 'Enable UI animations',
      ),
      SettingDefinition(
        category: 'ui',
        key: 'dark_mode',
        defaultValue: 'false',
        dataType: SettingDataType.bool,
        description: 'Enable dark mode',
      ),
      SettingDefinition(
        category: 'ui',
        key: 'sidebar_width',
        defaultValue: '280.0',
        dataType: SettingDataType.double,
        description: 'Sidebar width in pixels',
        minValue: 200.0,
        maxValue: 400.0,
      ),

      // Layout Settings
      SettingDefinition(
        category: 'layout',
        key: 'grid_columns',
        defaultValue: '3',
        dataType: SettingDataType.int,
        description: 'Number of columns in grid views',
        minValue: 1,
        maxValue: 6,
      ),
      SettingDefinition(
        category: 'layout',
        key: 'list_item_height',
        defaultValue: '72.0',
        dataType: SettingDataType.double,
        description: 'Height of list items',
        minValue: 48.0,
        maxValue: 120.0,
      ),
      SettingDefinition(
        category: 'layout',
        key: 'compact_mode',
        defaultValue: 'false',
        dataType: SettingDataType.bool,
        description: 'Enable compact layout mode',
      ),
      SettingDefinition(
        category: 'layout',
        key: 'show_thumbnails',
        defaultValue: 'true',
        dataType: SettingDataType.bool,
        description: 'Show thumbnails in lists',
      ),
      SettingDefinition(
        category: 'layout',
        key: 'tab_position',
        defaultValue: 'top',
        dataType: SettingDataType.string,
        description: 'Position of tabs',
        options: ['top', 'bottom', 'left', 'right'],
      ),

      // Performance Settings
      SettingDefinition(
        category: 'performance',
        key: 'cache_size_mb',
        defaultValue: '100',
        dataType: SettingDataType.int,
        description: 'Cache size in megabytes',
        minValue: 50,
        maxValue: 1000,
      ),
      SettingDefinition(
        category: 'performance',
        key: 'max_concurrent_operations',
        defaultValue: '4',
        dataType: SettingDataType.int,
        description: 'Maximum concurrent operations',
        minValue: 1,
        maxValue: 16,
      ),
      SettingDefinition(
        category: 'performance',
        key: 'enable_hardware_acceleration',
        defaultValue: 'true',
        dataType: SettingDataType.bool,
        description: 'Enable hardware acceleration',
      ),
      SettingDefinition(
        category: 'performance',
        key: 'memory_limit_mb',
        defaultValue: '512',
        dataType: SettingDataType.int,
        description: 'Memory limit in megabytes',
        minValue: 256,
        maxValue: 2048,
      ),
      SettingDefinition(
        category: 'performance',
        key: 'auto_cleanup_interval',
        defaultValue: '24',
        dataType: SettingDataType.int,
        description: 'Auto cleanup interval in hours',
        minValue: 1,
        maxValue: 168,
      ),

      // Accessibility Settings
      SettingDefinition(
        category: 'accessibility',
        key: 'high_contrast',
        defaultValue: 'false',
        dataType: SettingDataType.bool,
        description: 'Enable high contrast mode',
      ),
      SettingDefinition(
        category: 'accessibility',
        key: 'large_text',
        defaultValue: 'false',
        dataType: SettingDataType.bool,
        description: 'Enable large text mode',
      ),
      SettingDefinition(
        category: 'accessibility',
        key: 'screen_reader_support',
        defaultValue: 'true',
        dataType: SettingDataType.bool,
        description: 'Enable screen reader support',
      ),
      SettingDefinition(
        category: 'accessibility',
        key: 'reduce_motion',
        defaultValue: 'false',
        dataType: SettingDataType.bool,
        description: 'Reduce motion and animations',
      ),
      SettingDefinition(
        category: 'accessibility',
        key: 'focus_indicators',
        defaultValue: 'true',
        dataType: SettingDataType.bool,
        description: 'Show focus indicators',
      ),

      // Privacy & Security Settings
      SettingDefinition(
        category: 'privacy',
        key: 'enable_analytics',
        defaultValue: 'false',
        dataType: SettingDataType.bool,
        description: 'Enable usage analytics',
      ),
      SettingDefinition(
        category: 'privacy',
        key: 'auto_lock_timeout',
        defaultValue: '300',
        dataType: SettingDataType.int,
        description: 'Auto lock timeout in seconds',
        minValue: 60,
        maxValue: 3600,
      ),
      SettingDefinition(
        category: 'privacy',
        key: 'require_auth_for_sensitive',
        defaultValue: 'true',
        dataType: SettingDataType.bool,
        description: 'Require authentication for sensitive operations',
      ),
      SettingDefinition(
        category: 'privacy',
        key: 'encrypt_local_data',
        defaultValue: 'false',
        dataType: SettingDataType.bool,
        description: 'Encrypt local data storage',
      ),
      SettingDefinition(
        category: 'privacy',
        key: 'clear_cache_on_exit',
        defaultValue: 'false',
        dataType: SettingDataType.bool,
        description: 'Clear cache when exiting app',
      ),
    ];
  }

  /// Notify settings update
  static void _notifyUpdate(SettingsUpdate update) {
    if (!_updateController.isClosed) {
      _updateController.add(update);
    }
  }

  /// Get updates stream
  static Stream<SettingsUpdate> get updatesStream => _updateController.stream;

  /// Dispose resources
  static void dispose() {
    _updateController.close();
    _settingsCache.clear();
  }
}

/// Setting definition model
class SettingDefinition {
  final String category;
  final String key;
  final String defaultValue;
  final SettingDataType dataType;
  final String description;
  final double? minValue;
  final double? maxValue;
  final List<String>? options;
  final bool requiresRestart;
  final bool isAdvanced;

  const SettingDefinition({
    required this.category,
    required this.key,
    required this.defaultValue,
    required this.dataType,
    required this.description,
    this.minValue,
    this.maxValue,
    this.options,
    this.requiresRestart = false,
    this.isAdvanced = false,
  });
}

/// Setting data types
enum SettingDataType { string, int, double, bool, color, list }

/// Settings update model
class SettingsUpdate {
  final String category;
  final String key;
  final dynamic value;
  final DateTime timestamp;

  const SettingsUpdate({
    required this.category,
    required this.key,
    required this.value,
    required this.timestamp,
  });
}

/// Settings providers
final advancedSettingsServiceProvider = Provider<AdvancedSettingsService>((
  ref,
) {
  return AdvancedSettingsService();
});

final settingsProvider = StreamProvider<SettingsUpdate>((ref) {
  return AdvancedSettingsService.updatesStream;
});

final settingsByCategoryProvider =
    Provider.family<Map<String, dynamic>, String>((ref, category) {
      ref.watch(settingsProvider); // Watch for updates
      return AdvancedSettingsService.getSettingsByCategory(category);
    });
