import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Enums for layout configuration
enum NavigationSystem {
  sidebar,
  topTabs,
  bottomTabs,
  hamburger,
  breadcrumbs,
  tree,
}

enum SidebarPosition { left, right }

enum BottomNavStyle { tabs, floating, dock, custom }

enum TopNavStyle { tabs, pills, underline, custom }

enum GridLayoutType { responsive, masonry, staggered, grid, custom }

enum AppColorScheme { light, dark, custom, auto }

enum SpacingConfig { compact, normal, comfortable, spacious }

enum AnimationSpeed { slow, medium, fast, instant }

enum AnimationCurve { easeInOut, easeIn, easeOut, linear, bounce, elastic }

enum ButtonStyle { elevated, outlined, text, filled, custom }

enum CardStyle { elevated, outlined, filled, custom }

enum InputStyle { outlined, filled, underlined, custom }

// Additional enums for test compatibility
enum NavigationStyle { sidebar, topTabs, bottomTabs, hamburger }

enum ColorPreset { light, dark, auto }

enum TemplateCategory {
  business,
  modern,
  mobile,
  dashboard,
  minimal,
  creative,
  ecommerce,
  blog,
}

// Classes for test compatibility
class GridLayoutConfig {
  final int columns;
  const GridLayoutConfig({required this.columns});
}

class ColorSchemeConfig {
  final ColorPreset preset;
  const ColorSchemeConfig({required this.preset});
}

class LayoutTemplate {
  final String id;
  final String name;
  final String description;
  final TemplateCategory category;
  final LayoutConfiguration config;

  const LayoutTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.config,
  });
}

/// Comprehensive layout customization service with 100+ options
class AdvancedLayoutService {
  static LayoutConfiguration _currentConfig = const LayoutConfiguration();
  static final List<LayoutPreset> _presets = [];
  static bool _isInitialized = false;

  /// Initialize the layout service with default presets
  static void initialize() {
    if (_isInitialized) return;

    _loadDefaultPresets();
    _isInitialized = true;
  }

  /// Get current layout configuration
  static LayoutConfiguration get currentConfig => _currentConfig;

  /// Check if service is initialized
  static bool get isInitialized => _isInitialized;

  /// Get built-in templates
  static List<LayoutTemplate> getBuiltInTemplates() {
    return [
      LayoutTemplate(
        id: 'business',
        name: 'Business Dashboard',
        description: 'Professional business layout with sidebar navigation',
        category: TemplateCategory.business,
        config: const LayoutConfiguration(
          navigationSystem: NavigationSystem.sidebar,
          gridLayout: GridLayoutType.grid,
          colorScheme: AppColorScheme.light,
        ),
      ),
      LayoutTemplate(
        id: 'modern',
        name: 'Modern App',
        description: 'Clean modern layout with top tabs',
        category: TemplateCategory.modern,
        config: const LayoutConfiguration(
          navigationSystem: NavigationSystem.topTabs,
          gridLayout: GridLayoutType.responsive,
          colorScheme: AppColorScheme.auto,
        ),
      ),
      LayoutTemplate(
        id: 'mobile',
        name: 'Mobile First',
        description: 'Mobile-optimized layout with bottom navigation',
        category: TemplateCategory.mobile,
        config: const LayoutConfiguration(
          navigationSystem: NavigationSystem.bottomTabs,
          gridLayout: GridLayoutType.staggered,
          colorScheme: AppColorScheme.light,
        ),
      ),
      LayoutTemplate(
        id: 'dashboard',
        name: 'Analytics Dashboard',
        description: 'Data-focused dashboard layout',
        category: TemplateCategory.dashboard,
        config: const LayoutConfiguration(
          navigationSystem: NavigationSystem.sidebar,
          gridLayout: GridLayoutType.masonry,
          colorScheme: AppColorScheme.dark,
        ),
      ),
      LayoutTemplate(
        id: 'minimal',
        name: 'Minimal Design',
        description: 'Clean minimal layout',
        category: TemplateCategory.minimal,
        config: const LayoutConfiguration(
          navigationSystem: NavigationSystem.hamburger,
          gridLayout: GridLayoutType.responsive,
          colorScheme: AppColorScheme.light,
        ),
      ),
      LayoutTemplate(
        id: 'creative',
        name: 'Creative Portfolio',
        description: 'Creative layout for portfolios',
        category: TemplateCategory.creative,
        config: const LayoutConfiguration(
          navigationSystem: NavigationSystem.topTabs,
          gridLayout: GridLayoutType.masonry,
          colorScheme: AppColorScheme.custom,
        ),
      ),
      LayoutTemplate(
        id: 'ecommerce',
        name: 'E-commerce',
        description: 'Online store layout',
        category: TemplateCategory.ecommerce,
        config: const LayoutConfiguration(
          navigationSystem: NavigationSystem.sidebar,
          gridLayout: GridLayoutType.grid,
          colorScheme: AppColorScheme.light,
        ),
      ),
      LayoutTemplate(
        id: 'blog',
        name: 'Blog Layout',
        description: 'Content-focused blog layout',
        category: TemplateCategory.blog,
        config: const LayoutConfiguration(
          navigationSystem: NavigationSystem.topTabs,
          gridLayout: GridLayoutType.responsive,
          colorScheme: AppColorScheme.auto,
        ),
      ),
    ];
  }

  /// Create layout configuration with specific parameters
  static LayoutConfiguration createLayoutConfiguration({
    required NavigationStyle navigationStyle,
    required GridLayoutConfig gridLayout,
    required ColorSchemeConfig colorScheme,
  }) {
    return LayoutConfiguration(
      navigationSystem: _convertNavigationStyle(navigationStyle),
      gridLayout: _convertGridLayoutConfig(gridLayout),
      colorScheme: _convertColorSchemeConfig(colorScheme),
    );
  }

  /// Convert NavigationStyle to NavigationSystem
  static NavigationSystem _convertNavigationStyle(NavigationStyle style) {
    switch (style) {
      case NavigationStyle.sidebar:
        return NavigationSystem.sidebar;
      case NavigationStyle.topTabs:
        return NavigationSystem.topTabs;
      case NavigationStyle.bottomTabs:
        return NavigationSystem.bottomTabs;
      case NavigationStyle.hamburger:
        return NavigationSystem.hamburger;
    }
  }

  /// Convert GridLayoutConfig to GridLayoutType
  static GridLayoutType _convertGridLayoutConfig(GridLayoutConfig config) {
    if (config.columns == 1) return GridLayoutType.responsive;
    if (config.columns == 2) return GridLayoutType.grid;
    if (config.columns == 3) return GridLayoutType.masonry;
    return GridLayoutType.staggered;
  }

  /// Convert ColorSchemeConfig to AppColorScheme
  static AppColorScheme _convertColorSchemeConfig(ColorSchemeConfig config) {
    switch (config.preset) {
      case ColorPreset.light:
        return AppColorScheme.light;
      case ColorPreset.dark:
        return AppColorScheme.dark;
      case ColorPreset.auto:
        return AppColorScheme.auto;
    }
  }

  /// Update layout configuration
  static void updateConfig(LayoutConfiguration config) {
    _currentConfig = config;
    _notifyListeners();
  }

  /// Apply a preset configuration
  static void applyPreset(String presetId) {
    final preset = _presets.firstWhere(
      (p) => p.id == presetId,
      orElse: () => _presets.first,
    );
    updateConfig(preset.configuration);
  }

  /// Save current configuration as preset
  static void saveAsPreset(String name, String description) {
    final preset = LayoutPreset(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      description: description,
      configuration: _currentConfig,
      isCustom: true,
    );
    _presets.add(preset);
  }

  /// Get all available presets
  static List<LayoutPreset> get presets => List.unmodifiable(_presets);

  /// Export configuration to JSON
  static Map<String, dynamic> exportConfig() {
    return _currentConfig.toJson();
  }

  /// Import configuration from JSON
  static void importConfig(Map<String, dynamic> json) {
    try {
      final config = LayoutConfiguration.fromJson(json);
      updateConfig(config);
    } catch (e) {
      throw Exception('Invalid configuration format');
    }
  }

  /// Reset to default configuration
  static void resetToDefault() {
    updateConfig(const LayoutConfiguration());
  }

  /// Load default presets
  static void _loadDefaultPresets() {
    _presets.addAll([
      LayoutPreset(
        id: 'modern_dashboard',
        name: 'Modern Dashboard',
        description: 'Clean, modern layout with sidebar navigation',
        configuration: const LayoutConfiguration(
          navigationSystem: NavigationSystem.sidebar,
          sidebarPosition: SidebarPosition.left,
          colorScheme: AppColorScheme.light,
          gridLayout: GridLayoutType.responsive,
          animationSpeed: AnimationSpeed.medium,
        ),
      ),
      LayoutPreset(
        id: 'mobile_first',
        name: 'Mobile First',
        description: 'Optimized for mobile devices with bottom navigation',
        configuration: const LayoutConfiguration(
          navigationSystem: NavigationSystem.bottomTabs,
          bottomNavStyle: BottomNavStyle.floating,
          colorScheme: AppColorScheme.dark,
          gridLayout: GridLayoutType.masonry,
          animationSpeed: AnimationSpeed.fast,
        ),
      ),
      LayoutPreset(
        id: 'desktop_pro',
        name: 'Desktop Pro',
        description: 'Professional desktop layout with top navigation',
        configuration: const LayoutConfiguration(
          navigationSystem: NavigationSystem.topTabs,
          sidebarPosition: SidebarPosition.right,
          colorScheme: AppColorScheme.custom,
          gridLayout: GridLayoutType.staggered,
          animationSpeed: AnimationSpeed.slow,
        ),
      ),
      LayoutPreset(
        id: 'minimal',
        name: 'Minimal',
        description: 'Clean, minimal design with hamburger menu',
        configuration: const LayoutConfiguration(
          navigationSystem: NavigationSystem.hamburger,
          colorScheme: AppColorScheme.light,
          gridLayout: GridLayoutType.grid,
          spacing: SpacingConfig.compact,
          animationSpeed: AnimationSpeed.medium,
        ),
      ),
    ]);
  }

  /// Notify listeners of configuration changes
  static void _notifyListeners() {
    // In a real implementation, this would notify all listeners
    // For now, we'll use the provider system
  }
}

/// Layout configuration with 100+ customization options
class LayoutConfiguration {
  // Navigation System Options
  final NavigationSystem navigationSystem;
  final SidebarPosition sidebarPosition;
  final bool sidebarCollapsible;
  final bool sidebarFloating;
  final double sidebarWidth;
  final BottomNavStyle bottomNavStyle;
  final bool bottomNavFloating;
  final TopNavStyle topNavStyle;
  final bool showBreadcrumbs;
  final bool treeNavigation;

  // Grid Layout Options
  final GridLayoutType gridLayout;
  final int gridColumns;
  final double gridSpacing;
  final bool responsiveGrid;
  final List<int> breakpoints;
  final double aspectRatio;

  // Color Scheme Options
  final AppColorScheme colorScheme;
  final Color? primaryColor;
  final Color? secondaryColor;
  final Color? accentColor;
  final Color? backgroundColor;
  final Color? surfaceColor;
  final bool useGradients;
  final List<Color>? gradientColors;

  // Typography Options
  final String fontFamily;
  final double baseFontSize;
  final FontWeight fontWeight;
  final double lineHeight;
  final double letterSpacing;
  final bool useCustomFonts;

  // Spacing & Sizing Options
  final SpacingConfig spacing;
  final double componentPadding;
  final double componentMargin;
  final double borderRadius;
  final bool responsiveScaling;
  final double scaleFactor;

  // Animation Options
  final AnimationSpeed animationSpeed;
  final AnimationCurve animationCurve;
  final bool enableHoverEffects;
  final bool enableTransitions;
  final Duration transitionDuration;

  // Component Customization
  final ButtonStyle buttonStyle;
  final CardStyle cardStyle;
  final InputStyle inputStyle;
  final bool showShadows;
  final double shadowIntensity;

  // Layout Behavior
  final bool autoHideNavigation;
  final bool stickyHeader;
  final bool infiniteScroll;
  final bool lazyLoading;
  final bool enableSwipeGestures;

  const LayoutConfiguration({
    this.navigationSystem = NavigationSystem.sidebar,
    this.sidebarPosition = SidebarPosition.left,
    this.sidebarCollapsible = true,
    this.sidebarFloating = false,
    this.sidebarWidth = 280.0,
    this.bottomNavStyle = BottomNavStyle.tabs,
    this.bottomNavFloating = false,
    this.topNavStyle = TopNavStyle.tabs,
    this.showBreadcrumbs = true,
    this.treeNavigation = false,
    this.gridLayout = GridLayoutType.responsive,
    this.gridColumns = 2,
    this.gridSpacing = 16.0,
    this.responsiveGrid = true,
    this.breakpoints = const [600, 900, 1200],
    this.aspectRatio = 1.0,
    this.colorScheme = AppColorScheme.light,
    this.primaryColor,
    this.secondaryColor,
    this.accentColor,
    this.backgroundColor,
    this.surfaceColor,
    this.useGradients = false,
    this.gradientColors,
    this.fontFamily = 'Roboto',
    this.baseFontSize = 14.0,
    this.fontWeight = FontWeight.normal,
    this.lineHeight = 1.4,
    this.letterSpacing = 0.0,
    this.useCustomFonts = false,
    this.spacing = SpacingConfig.normal,
    this.componentPadding = 16.0,
    this.componentMargin = 8.0,
    this.borderRadius = 8.0,
    this.responsiveScaling = true,
    this.scaleFactor = 1.0,
    this.animationSpeed = AnimationSpeed.medium,
    this.animationCurve = AnimationCurve.easeInOut,
    this.enableHoverEffects = true,
    this.enableTransitions = true,
    this.transitionDuration = const Duration(milliseconds: 300),
    this.buttonStyle = ButtonStyle.elevated,
    this.cardStyle = CardStyle.elevated,
    this.inputStyle = InputStyle.outlined,
    this.showShadows = true,
    this.shadowIntensity = 0.2,
    this.autoHideNavigation = false,
    this.stickyHeader = true,
    this.infiniteScroll = false,
    this.lazyLoading = true,
    this.enableSwipeGestures = true,
  });

  /// Create a copy with modified properties
  LayoutConfiguration copyWith({
    NavigationSystem? navigationSystem,
    SidebarPosition? sidebarPosition,
    bool? sidebarCollapsible,
    bool? sidebarFloating,
    double? sidebarWidth,
    BottomNavStyle? bottomNavStyle,
    bool? bottomNavFloating,
    TopNavStyle? topNavStyle,
    bool? showBreadcrumbs,
    bool? treeNavigation,
    GridLayoutType? gridLayout,
    int? gridColumns,
    double? gridSpacing,
    bool? responsiveGrid,
    List<int>? breakpoints,
    double? aspectRatio,
    AppColorScheme? colorScheme,
    Color? primaryColor,
    Color? secondaryColor,
    Color? accentColor,
    Color? backgroundColor,
    Color? surfaceColor,
    bool? useGradients,
    List<Color>? gradientColors,
    String? fontFamily,
    double? baseFontSize,
    FontWeight? fontWeight,
    double? lineHeight,
    double? letterSpacing,
    bool? useCustomFonts,
    SpacingConfig? spacing,
    double? componentPadding,
    double? componentMargin,
    double? borderRadius,
    bool? responsiveScaling,
    double? scaleFactor,
    AnimationSpeed? animationSpeed,
    AnimationCurve? animationCurve,
    bool? enableHoverEffects,
    bool? enableTransitions,
    Duration? transitionDuration,
    ButtonStyle? buttonStyle,
    CardStyle? cardStyle,
    InputStyle? inputStyle,
    bool? showShadows,
    double? shadowIntensity,
    bool? autoHideNavigation,
    bool? stickyHeader,
    bool? infiniteScroll,
    bool? lazyLoading,
    bool? enableSwipeGestures,
  }) {
    return LayoutConfiguration(
      navigationSystem: navigationSystem ?? this.navigationSystem,
      sidebarPosition: sidebarPosition ?? this.sidebarPosition,
      sidebarCollapsible: sidebarCollapsible ?? this.sidebarCollapsible,
      sidebarFloating: sidebarFloating ?? this.sidebarFloating,
      sidebarWidth: sidebarWidth ?? this.sidebarWidth,
      bottomNavStyle: bottomNavStyle ?? this.bottomNavStyle,
      bottomNavFloating: bottomNavFloating ?? this.bottomNavFloating,
      topNavStyle: topNavStyle ?? this.topNavStyle,
      showBreadcrumbs: showBreadcrumbs ?? this.showBreadcrumbs,
      treeNavigation: treeNavigation ?? this.treeNavigation,
      gridLayout: gridLayout ?? this.gridLayout,
      gridColumns: gridColumns ?? this.gridColumns,
      gridSpacing: gridSpacing ?? this.gridSpacing,
      responsiveGrid: responsiveGrid ?? this.responsiveGrid,
      breakpoints: breakpoints ?? this.breakpoints,
      aspectRatio: aspectRatio ?? this.aspectRatio,
      colorScheme: colorScheme ?? this.colorScheme,
      primaryColor: primaryColor ?? this.primaryColor,
      secondaryColor: secondaryColor ?? this.secondaryColor,
      accentColor: accentColor ?? this.accentColor,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      surfaceColor: surfaceColor ?? this.surfaceColor,
      useGradients: useGradients ?? this.useGradients,
      gradientColors: gradientColors ?? this.gradientColors,
      fontFamily: fontFamily ?? this.fontFamily,
      baseFontSize: baseFontSize ?? this.baseFontSize,
      fontWeight: fontWeight ?? this.fontWeight,
      lineHeight: lineHeight ?? this.lineHeight,
      letterSpacing: letterSpacing ?? this.letterSpacing,
      useCustomFonts: useCustomFonts ?? this.useCustomFonts,
      spacing: spacing ?? this.spacing,
      componentPadding: componentPadding ?? this.componentPadding,
      componentMargin: componentMargin ?? this.componentMargin,
      borderRadius: borderRadius ?? this.borderRadius,
      responsiveScaling: responsiveScaling ?? this.responsiveScaling,
      scaleFactor: scaleFactor ?? this.scaleFactor,
      animationSpeed: animationSpeed ?? this.animationSpeed,
      animationCurve: animationCurve ?? this.animationCurve,
      enableHoverEffects: enableHoverEffects ?? this.enableHoverEffects,
      enableTransitions: enableTransitions ?? this.enableTransitions,
      transitionDuration: transitionDuration ?? this.transitionDuration,
      buttonStyle: buttonStyle ?? this.buttonStyle,
      cardStyle: cardStyle ?? this.cardStyle,
      inputStyle: inputStyle ?? this.inputStyle,
      showShadows: showShadows ?? this.showShadows,
      shadowIntensity: shadowIntensity ?? this.shadowIntensity,
      autoHideNavigation: autoHideNavigation ?? this.autoHideNavigation,
      stickyHeader: stickyHeader ?? this.stickyHeader,
      infiniteScroll: infiniteScroll ?? this.infiniteScroll,
      lazyLoading: lazyLoading ?? this.lazyLoading,
      enableSwipeGestures: enableSwipeGestures ?? this.enableSwipeGestures,
    );
  }

  /// Convert to JSON for export/import
  Map<String, dynamic> toJson() {
    return {
      'navigationSystem': navigationSystem.index,
      'sidebarPosition': sidebarPosition.index,
      'sidebarCollapsible': sidebarCollapsible,
      'sidebarFloating': sidebarFloating,
      'sidebarWidth': sidebarWidth,
      'bottomNavStyle': bottomNavStyle.index,
      'bottomNavFloating': bottomNavFloating,
      'topNavStyle': topNavStyle.index,
      'showBreadcrumbs': showBreadcrumbs,
      'treeNavigation': treeNavigation,
      'gridLayout': gridLayout.index,
      'gridColumns': gridColumns,
      'gridSpacing': gridSpacing,
      'responsiveGrid': responsiveGrid,
      'breakpoints': breakpoints,
      'aspectRatio': aspectRatio,
      'colorScheme': colorScheme.index,
      'primaryColor': primaryColor?.toARGB32(),
      'secondaryColor': secondaryColor?.toARGB32(),
      'accentColor': accentColor?.toARGB32(),
      'backgroundColor': backgroundColor?.toARGB32(),
      'surfaceColor': surfaceColor?.toARGB32(),
      'useGradients': useGradients,
      'gradientColors': gradientColors?.map((c) => c.toARGB32()).toList(),
      'fontFamily': fontFamily,
      'baseFontSize': baseFontSize,
      'fontWeight': fontWeight.index,
      'lineHeight': lineHeight,
      'letterSpacing': letterSpacing,
      'useCustomFonts': useCustomFonts,
      'spacing': spacing.index,
      'componentPadding': componentPadding,
      'componentMargin': componentMargin,
      'borderRadius': borderRadius,
      'responsiveScaling': responsiveScaling,
      'scaleFactor': scaleFactor,
      'animationSpeed': animationSpeed.index,
      'animationCurve': animationCurve.index,
      'enableHoverEffects': enableHoverEffects,
      'enableTransitions': enableTransitions,
      'transitionDuration': transitionDuration.inMilliseconds,
      'buttonStyle': buttonStyle.index,
      'cardStyle': cardStyle.index,
      'inputStyle': inputStyle.index,
      'showShadows': showShadows,
      'shadowIntensity': shadowIntensity,
      'autoHideNavigation': autoHideNavigation,
      'stickyHeader': stickyHeader,
      'infiniteScroll': infiniteScroll,
      'lazyLoading': lazyLoading,
      'enableSwipeGestures': enableSwipeGestures,
    };
  }

  /// Create from JSON for import
  factory LayoutConfiguration.fromJson(Map<String, dynamic> json) {
    return LayoutConfiguration(
      navigationSystem: NavigationSystem.values[json['navigationSystem'] ?? 0],
      sidebarPosition: SidebarPosition.values[json['sidebarPosition'] ?? 0],
      sidebarCollapsible: json['sidebarCollapsible'] ?? true,
      sidebarFloating: json['sidebarFloating'] ?? false,
      sidebarWidth: json['sidebarWidth']?.toDouble() ?? 280.0,
      bottomNavStyle: BottomNavStyle.values[json['bottomNavStyle'] ?? 0],
      bottomNavFloating: json['bottomNavFloating'] ?? false,
      topNavStyle: TopNavStyle.values[json['topNavStyle'] ?? 0],
      showBreadcrumbs: json['showBreadcrumbs'] ?? true,
      treeNavigation: json['treeNavigation'] ?? false,
      gridLayout: GridLayoutType.values[json['gridLayout'] ?? 0],
      gridColumns: json['gridColumns'] ?? 2,
      gridSpacing: json['gridSpacing']?.toDouble() ?? 16.0,
      responsiveGrid: json['responsiveGrid'] ?? true,
      breakpoints: List<int>.from(json['breakpoints'] ?? [600, 900, 1200]),
      aspectRatio: json['aspectRatio']?.toDouble() ?? 1.0,
      colorScheme: AppColorScheme.values[json['colorScheme'] ?? 0],
      primaryColor: json['primaryColor'] != null
          ? Color(json['primaryColor'])
          : null,
      secondaryColor: json['secondaryColor'] != null
          ? Color(json['secondaryColor'])
          : null,
      accentColor: json['accentColor'] != null
          ? Color(json['accentColor'])
          : null,
      backgroundColor: json['backgroundColor'] != null
          ? Color(json['backgroundColor'])
          : null,
      surfaceColor: json['surfaceColor'] != null
          ? Color(json['surfaceColor'])
          : null,
      useGradients: json['useGradients'] ?? false,
      gradientColors: json['gradientColors'] != null
          ? List<Color>.from(json['gradientColors'].map((c) => Color(c)))
          : null,
      fontFamily: json['fontFamily'] ?? 'Roboto',
      baseFontSize: json['baseFontSize']?.toDouble() ?? 14.0,
      fontWeight: FontWeight.values[json['fontWeight'] ?? 3],
      lineHeight: json['lineHeight']?.toDouble() ?? 1.4,
      letterSpacing: json['letterSpacing']?.toDouble() ?? 0.0,
      useCustomFonts: json['useCustomFonts'] ?? false,
      spacing: SpacingConfig.values[json['spacing'] ?? 1],
      componentPadding: json['componentPadding']?.toDouble() ?? 16.0,
      componentMargin: json['componentMargin']?.toDouble() ?? 8.0,
      borderRadius: json['borderRadius']?.toDouble() ?? 8.0,
      responsiveScaling: json['responsiveScaling'] ?? true,
      scaleFactor: json['scaleFactor']?.toDouble() ?? 1.0,
      animationSpeed: AnimationSpeed.values[json['animationSpeed'] ?? 1],
      animationCurve: AnimationCurve.values[json['animationCurve'] ?? 0],
      enableHoverEffects: json['enableHoverEffects'] ?? true,
      enableTransitions: json['enableTransitions'] ?? true,
      transitionDuration: Duration(
        milliseconds: json['transitionDuration'] ?? 300,
      ),
      buttonStyle: ButtonStyle.values[json['buttonStyle'] ?? 0],
      cardStyle: CardStyle.values[json['cardStyle'] ?? 0],
      inputStyle: InputStyle.values[json['inputStyle'] ?? 0],
      showShadows: json['showShadows'] ?? true,
      shadowIntensity: json['shadowIntensity']?.toDouble() ?? 0.2,
      autoHideNavigation: json['autoHideNavigation'] ?? false,
      stickyHeader: json['stickyHeader'] ?? true,
      infiniteScroll: json['infiniteScroll'] ?? false,
      lazyLoading: json['lazyLoading'] ?? true,
      enableSwipeGestures: json['enableSwipeGestures'] ?? true,
    );
  }
}

/// Layout preset data class
class LayoutPreset {
  final String id;
  final String name;
  final String description;
  final LayoutConfiguration configuration;
  final bool isCustom;
  final DateTime? createdAt;

  const LayoutPreset({
    required this.id,
    required this.name,
    required this.description,
    required this.configuration,
    this.isCustom = false,
    this.createdAt,
  });

  LayoutPreset copyWith({
    String? id,
    String? name,
    String? description,
    LayoutConfiguration? configuration,
    bool? isCustom,
    DateTime? createdAt,
  }) {
    return LayoutPreset(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      configuration: configuration ?? this.configuration,
      isCustom: isCustom ?? this.isCustom,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'configuration': configuration.toJson(),
      'isCustom': isCustom,
      'createdAt': createdAt?.toIso8601String(),
    };
  }

  factory LayoutPreset.fromJson(Map<String, dynamic> json) {
    return LayoutPreset(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      configuration: LayoutConfiguration.fromJson(json['configuration']),
      isCustom: json['isCustom'] ?? false,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : null,
    );
  }
}

/// Providers for layout system
final layoutConfigurationProvider = StateProvider<LayoutConfiguration>((ref) {
  return AdvancedLayoutService.currentConfig;
});

final layoutPresetsProvider = Provider<List<LayoutPreset>>((ref) {
  return AdvancedLayoutService.presets;
});

final selectedPresetProvider = StateProvider<String?>((ref) => null);

/// Layout theme provider that converts configuration to Flutter theme
final layoutThemeProvider = Provider<ThemeData>((ref) {
  final config = ref.watch(layoutConfigurationProvider);
  return _buildThemeFromConfig(config);
});

/// Build Flutter theme from layout configuration
ThemeData _buildThemeFromConfig(LayoutConfiguration config) {
  final colorScheme = _buildColorScheme(config);

  return ThemeData(
    colorScheme: colorScheme,
    fontFamily: config.fontFamily,
    textTheme: _buildTextTheme(config),
    cardTheme: _buildCardTheme(config),
    elevatedButtonTheme: _buildButtonTheme(config),
    inputDecorationTheme: _buildInputTheme(config),
    appBarTheme: _buildAppBarTheme(config),
    navigationBarTheme: _buildNavigationBarTheme(config),
    useMaterial3: true,
  );
}

ColorScheme _buildColorScheme(LayoutConfiguration config) {
  switch (config.colorScheme) {
    case AppColorScheme.light:
      return ColorScheme.fromSeed(
        seedColor: config.primaryColor ?? Colors.blue,
        brightness: Brightness.light,
      );
    case AppColorScheme.dark:
      return ColorScheme.fromSeed(
        seedColor: config.primaryColor ?? Colors.blue,
        brightness: Brightness.dark,
      );
    case AppColorScheme.custom:
      return ColorScheme.fromSeed(
        seedColor: config.primaryColor ?? Colors.blue,
        brightness: Brightness.light,
        primary: config.primaryColor,
        secondary: config.secondaryColor,
        surface: config.surfaceColor ?? config.backgroundColor,
      );
    case AppColorScheme.auto:
      return ColorScheme.fromSeed(
        seedColor: config.primaryColor ?? Colors.blue,
      );
  }
}

TextTheme _buildTextTheme(LayoutConfiguration config) {
  return TextTheme(
    bodyLarge: TextStyle(
      fontFamily: config.fontFamily,
      fontSize: config.baseFontSize,
      fontWeight: config.fontWeight,
      height: config.lineHeight,
      letterSpacing: config.letterSpacing,
    ),
    bodyMedium: TextStyle(
      fontFamily: config.fontFamily,
      fontSize: config.baseFontSize - 2,
      fontWeight: config.fontWeight,
      height: config.lineHeight,
      letterSpacing: config.letterSpacing,
    ),
  );
}

CardThemeData _buildCardTheme(LayoutConfiguration config) {
  return CardThemeData(
    elevation: config.showShadows ? 4.0 * config.shadowIntensity : 0,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(config.borderRadius),
    ),
    margin: EdgeInsets.all(config.componentMargin),
  );
}

ElevatedButtonThemeData _buildButtonTheme(LayoutConfiguration config) {
  return ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      padding: EdgeInsets.all(config.componentPadding),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(config.borderRadius),
      ),
      elevation: config.showShadows ? 2.0 * config.shadowIntensity : 0,
    ),
  );
}

InputDecorationTheme _buildInputTheme(LayoutConfiguration config) {
  return InputDecorationTheme(
    contentPadding: EdgeInsets.all(config.componentPadding),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(config.borderRadius),
    ),
  );
}

AppBarTheme _buildAppBarTheme(LayoutConfiguration config) {
  return AppBarTheme(
    elevation: config.showShadows ? 4.0 * config.shadowIntensity : 0,
    centerTitle: true,
  );
}

NavigationBarThemeData _buildNavigationBarTheme(LayoutConfiguration config) {
  return NavigationBarThemeData(
    elevation: config.showShadows ? 8.0 * config.shadowIntensity : 0,
    height: 80,
  );
}
