import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/tools_builder_providers.dart';
import '../models/tool.dart';
import 'templates_screen.dart';
import 'tool_editor_screen.dart';
import 'tools_builder_dashboard.dart';
import 'spreadsheet_editor_screen.dart';
import 'tool_runtime_screen.dart';
import 'tools_builder_settings_screen.dart' as tb_settings;

/// Main home screen for Tools Builder application
class ToolsBuilderHome extends ConsumerStatefulWidget {
  const ToolsBuilderHome({super.key});

  @override
  ConsumerState<ToolsBuilderHome> createState() => _ToolsBuilderHomeState();
}

class _ToolsBuilderHomeState extends ConsumerState<ToolsBuilderHome> {
  int _selectedIndex = 0;

  final List<NavigationItem> _navigationItems = [
    NavigationItem(
      icon: Icons.dashboard,
      label: 'Dashboard',
      builder: (context) => const ToolsBuilderDashboard(),
    ),
    NavigationItem(
      icon: Icons.table_chart,
      label: 'Spreadsheets',
      builder: (context) => const SpreadsheetListScreen(),
    ),
    NavigationItem(
      icon: Icons.description_outlined,
      label: 'Templates',
      builder: (context) => const TemplatesScreen(),
    ),
    NavigationItem(
      icon: Icons.build,
      label: 'Tools',
      builder: (context) => const ToolsListScreen(),
    ),
    NavigationItem(
      icon: Icons.settings,
      label: 'Settings',
      builder: (context) => const tb_settings.ToolsBuilderSettingsScreen(),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _navigationItems[_selectedIndex].builder(context),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        items: _navigationItems
            .map(
              (item) => BottomNavigationBarItem(
                icon: Icon(item.icon),
                label: item.label,
              ),
            )
            .toList(),
      ),
      floatingActionButton: _selectedIndex == 1 || _selectedIndex == 3
          ? FloatingActionButton(
              onPressed: () => _showCreateDialog(),
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  void _showCreateDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(_selectedIndex == 1 ? 'Create New' : 'Create Tool'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_selectedIndex == 1) ...[
              ListTile(
                leading: const Icon(Icons.table_chart),
                title: const Text('New Spreadsheet'),
                onTap: () {
                  Navigator.pop(context);
                  _createNewSpreadsheet();
                },
              ),
              ListTile(
                leading: const Icon(Icons.upload_file),
                title: const Text('Import Spreadsheet'),
                onTap: () {
                  Navigator.pop(context);
                  _importSpreadsheet();
                },
              ),
            ],
            if (_selectedIndex == 3) ...[
              ListTile(
                leading: const Icon(Icons.calculate),
                title: const Text('Calculator Tool'),
                onTap: () {
                  Navigator.pop(context);
                  _createNewTool('calculator');
                },
              ),
              ListTile(
                leading: const Icon(Icons.analytics),
                title: const Text('Analyzer Tool'),
                onTap: () {
                  Navigator.pop(context);
                  _createNewTool('analyzer');
                },
              ),
              ListTile(
                leading: const Icon(Icons.build),
                title: const Text('Custom Tool'),
                onTap: () {
                  Navigator.pop(context);
                  _createNewTool('custom');
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _createNewSpreadsheet() {
    showDialog(
      context: context,
      builder: (context) => CreateSpreadsheetDialog(),
    );
  }

  void _importSpreadsheet() {
    showDialog(
      context: context,
      builder: (context) => ImportSpreadsheetDialog(),
    );
  }

  void _createNewTool(String type) {
    // Use Future.microtask to avoid StateNotifier modification during widget lifecycle
    Future.microtask(() {
      if (!mounted) return;

      // Convert string type to ToolType enum
      ToolType toolType;
      switch (type.toLowerCase()) {
        case 'calculator':
          toolType = ToolType.calculator;
          break;
        case 'analyzer':
          toolType = ToolType.analyzer;
          break;
        case 'custom':
          toolType = ToolType.custom;
          break;
        default:
          toolType = ToolType.custom;
      }

      // Create a new tool based on type
      final newTool = Tool(
        name: 'New ${type.toUpperCase()} Tool',
        description: 'A new $type tool',
        type: toolType,
        category: ToolCategory.utilities,
        creatorId: 'user_001', // Default creator ID
        components: [],
        configuration: {},
      );

      // Set the tool in the provider
      ref.read(currentToolProvider.notifier).setTool(newTool);

      // Navigate to editor
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const ToolEditorScreen()),
      );
    });
  }
}

/// Navigation item model
class NavigationItem {
  final IconData icon;
  final String label;
  final Widget Function(BuildContext) builder;

  const NavigationItem({
    required this.icon,
    required this.label,
    required this.builder,
  });
}

/// Placeholder screen for spreadsheets list
class SpreadsheetListScreen extends ConsumerWidget {
  const SpreadsheetListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Spreadsheets'),
        automaticallyImplyLeading: false,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.table_chart, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No spreadsheets yet',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Create your first spreadsheet to get started',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}

/// Placeholder screen for tools list
class ToolsListScreen extends ConsumerWidget {
  const ToolsListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tools = ref.watch(toolsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Tools'),
        automaticallyImplyLeading: false,
      ),
      body: tools.isEmpty
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.build, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    'No tools yet',
                    style: TextStyle(fontSize: 18, color: Colors.grey),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Create your first tool to get started',
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            )
          : ListView.builder(
              itemCount: tools.length,
              itemBuilder: (context, index) {
                final tool = tools[index];
                return ListTile(
                  leading: Icon(_getToolIcon(tool.type)),
                  title: Text(tool.name),
                  subtitle: Text(tool.description),
                  trailing: IconButton(
                    icon: const Icon(Icons.edit),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => ToolEditorScreen(tool: tool),
                        ),
                      );
                    },
                  ),
                  onTap: () {
                    // Navigate to tool runtime screen
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ToolRuntimeScreen(tool: tool),
                      ),
                    );
                  },
                );
              },
            ),
    );
  }

  IconData _getToolIcon(dynamic type) {
    // Simple icon mapping - can be enhanced
    return Icons.build;
  }
}

/// Dialog for creating a new spreadsheet
class CreateSpreadsheetDialog extends StatefulWidget {
  const CreateSpreadsheetDialog({super.key});

  @override
  State<CreateSpreadsheetDialog> createState() =>
      _CreateSpreadsheetDialogState();
}

class _CreateSpreadsheetDialogState extends State<CreateSpreadsheetDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  int _rows = 100;
  int _columns = 26;

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create New Spreadsheet'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Spreadsheet Name',
                  hintText: 'Enter spreadsheet name',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a spreadsheet name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (optional)',
                  hintText: 'Enter description',
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      initialValue: _rows.toString(),
                      decoration: const InputDecoration(labelText: 'Rows'),
                      keyboardType: TextInputType.number,
                      onChanged: (value) {
                        final rows = int.tryParse(value);
                        if (rows != null && rows > 0) {
                          _rows = rows;
                        }
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      initialValue: _columns.toString(),
                      decoration: const InputDecoration(labelText: 'Columns'),
                      keyboardType: TextInputType.number,
                      onChanged: (value) {
                        final columns = int.tryParse(value);
                        if (columns != null && columns > 0) {
                          _columns = columns;
                        }
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _createSpreadsheet,
          child: const Text('Create'),
        ),
      ],
    );
  }

  void _createSpreadsheet() {
    if (_formKey.currentState!.validate()) {
      // Create a new spreadsheet with the provided data
      Navigator.pop(context);

      // Navigate to spreadsheet editor
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => SpreadsheetEditorScreen(
            spreadsheetName: _nameController.text,
            rows: _rows,
            columns: _columns,
          ),
        ),
      );
    }
  }
}

/// Dialog for importing a spreadsheet
class ImportSpreadsheetDialog extends StatefulWidget {
  const ImportSpreadsheetDialog({super.key});

  @override
  State<ImportSpreadsheetDialog> createState() =>
      _ImportSpreadsheetDialogState();
}

class _ImportSpreadsheetDialogState extends State<ImportSpreadsheetDialog> {
  String? _selectedFile;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Import Spreadsheet'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text('Select a file to import:'),
          const SizedBox(height: 16),
          ListTile(
            leading: const Icon(Icons.file_upload),
            title: Text(_selectedFile ?? 'No file selected'),
            subtitle: const Text('Supported formats: .xlsx, .csv'),
            onTap: _selectFile,
          ),
          if (_isLoading) ...[
            const SizedBox(height: 16),
            const CircularProgressIndicator(),
            const SizedBox(height: 8),
            const Text('Importing...'),
          ],
        ],
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _selectedFile != null && !_isLoading ? _importFile : null,
          child: const Text('Import'),
        ),
      ],
    );
  }

  void _selectFile() async {
    try {
      // For now, simulate file selection
      // In a real implementation, you would use file_picker package
      setState(() {
        _selectedFile = 'example_spreadsheet.xlsx';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('File selected successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error selecting file: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _importFile() {
    setState(() {
      _isLoading = true;
    });

    // Simulate import process
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Spreadsheet "$_selectedFile" imported successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }
}
