import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:io';
import 'dart:convert';

// Comprehensive Error Handler Provider
final comprehensiveErrorHandlerProvider = Provider<ComprehensiveErrorHandler>((
  ref,
) {
  return ComprehensiveErrorHandler();
});

// Error Log Provider
final errorLogProvider =
    StateNotifierProvider<ErrorLogNotifier, List<ErrorLogEntry>>((ref) {
      return ErrorLogNotifier();
    });

// Crash Report Provider
final crashReportProvider =
    StateNotifierProvider<CrashReportNotifier, List<CrashReport>>((ref) {
      return CrashReportNotifier();
    });

// Comprehensive Error Handler
class ComprehensiveErrorHandler {
  static final ComprehensiveErrorHandler _instance =
      ComprehensiveErrorHandler._internal();
  factory ComprehensiveErrorHandler() => _instance;
  ComprehensiveErrorHandler._internal();

  final List<ErrorLogEntry> _errorLog = [];
  final List<CrashReport> _crashReports = [];
  late File _logFile;
  late File _crashFile;

  // Initialize error handling system
  Future<void> initialize() async {
    await _initializeLogFiles();
    _setupErrorHandlers();
    await _loadExistingLogs();
  }

  Future<void> _initializeLogFiles() async {
    try {
      final appDir = await _getAppDirectory();
      _logFile = File('${appDir.path}/error_log.json');
      _crashFile = File('${appDir.path}/crash_reports.json');

      if (!await _logFile.exists()) {
        await _logFile.create(recursive: true);
        await _logFile.writeAsString('[]');
      }

      if (!await _crashFile.exists()) {
        await _crashFile.create(recursive: true);
        await _crashFile.writeAsString('[]');
      }
    } catch (e) {
      debugPrint('Error initializing log files: $e');
    }
  }

  Future<Directory> _getAppDirectory() async {
    if (Platform.isAndroid) {
      return Directory('/data/data/com.shadowsuite.app/files');
    } else if (Platform.isWindows) {
      final userProfile = Platform.environment['USERPROFILE'] ?? '';
      return Directory('$userProfile/AppData/Local/ShadowSuite');
    } else {
      return Directory.current;
    }
  }

  void _setupErrorHandlers() {
    // Handle Flutter framework errors
    FlutterError.onError = (FlutterErrorDetails details) {
      _handleFlutterError(details);
    };

    // Handle platform errors
    PlatformDispatcher.instance.onError = (error, stack) {
      _handlePlatformError(error, stack);
      return true;
    };
  }

  Future<void> _loadExistingLogs() async {
    try {
      // Load error logs
      if (await _logFile.exists()) {
        final logContent = await _logFile.readAsString();
        if (logContent.isNotEmpty && logContent != '[]') {
          final List<dynamic> logData = jsonDecode(logContent);
          _errorLog.clear();
          _errorLog.addAll(logData.map((e) => ErrorLogEntry.fromJson(e)));
        }
      }

      // Load crash reports
      if (await _crashFile.exists()) {
        final crashContent = await _crashFile.readAsString();
        if (crashContent.isNotEmpty && crashContent != '[]') {
          final List<dynamic> crashData = jsonDecode(crashContent);
          _crashReports.clear();
          _crashReports.addAll(crashData.map((e) => CrashReport.fromJson(e)));
        }
      }
    } catch (e) {
      debugPrint('Error loading existing logs: $e');
    }
  }

  void _handleFlutterError(FlutterErrorDetails details) {
    final errorEntry = ErrorLogEntry(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      timestamp: DateTime.now(),
      level: ErrorLevel.error,
      message: details.exception.toString(),
      stackTrace: details.stack?.toString(),
      context: details.context?.toString(),
      library: details.library,
      source: ErrorSource.flutter,
      deviceInfo: _getDeviceInfo(),
      appVersion: '1.0.0',
      buildNumber: '1',
    );

    _logError(errorEntry);

    // Create crash report for critical errors
    if (details.exception is Error ||
        details.exception.toString().contains('RenderFlex')) {
      final crashReport = CrashReport(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        timestamp: DateTime.now(),
        type: CrashType.flutter,
        exception: details.exception.toString(),
        stackTrace: details.stack?.toString() ?? '',
        context: details.context?.toString(),
        deviceInfo: _getDeviceInfo(),
        appVersion: '1.0.0',
        buildNumber: '1',
        isFatal: true,
        userActions: [],
      );

      _logCrash(crashReport);
    }
  }

  bool _handlePlatformError(Object error, StackTrace stack) {
    final errorEntry = ErrorLogEntry(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      timestamp: DateTime.now(),
      level: ErrorLevel.critical,
      message: error.toString(),
      stackTrace: stack.toString(),
      source: ErrorSource.platform,
      deviceInfo: _getDeviceInfo(),
      appVersion: '1.0.0',
      buildNumber: '1',
    );

    _logError(errorEntry);

    // Create crash report for platform errors
    final crashReport = CrashReport(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      timestamp: DateTime.now(),
      type: CrashType.platform,
      exception: error.toString(),
      stackTrace: stack.toString(),
      deviceInfo: _getDeviceInfo(),
      appVersion: '1.0.0',
      buildNumber: '1',
      isFatal: true,
      userActions: [],
    );

    _logCrash(crashReport);
    return true;
  }

  // Log custom errors
  void logError({
    required String message,
    ErrorLevel level = ErrorLevel.error,
    String? stackTrace,
    String? context,
    String? library,
    ErrorSource source = ErrorSource.app,
    Map<String, dynamic>? additionalData,
  }) {
    final errorEntry = ErrorLogEntry(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      timestamp: DateTime.now(),
      level: level,
      message: message,
      stackTrace: stackTrace,
      context: context,
      library: library,
      source: source,
      deviceInfo: _getDeviceInfo(),
      appVersion: '1.0.0',
      buildNumber: '1',
      additionalData: additionalData,
    );

    _logError(errorEntry);
  }

  // Log performance issues
  void logPerformanceIssue({
    required String operation,
    required Duration duration,
    required double threshold,
    Map<String, dynamic>? additionalData,
  }) {
    if (duration.inMilliseconds > threshold) {
      logError(
        message:
            'Performance issue: $operation took ${duration.inMilliseconds}ms (threshold: ${threshold}ms)',
        level: ErrorLevel.warning,
        source: ErrorSource.performance,
        additionalData: {
          'operation': operation,
          'duration_ms': duration.inMilliseconds,
          'threshold_ms': threshold,
          ...?additionalData,
        },
      );
    }
  }

  // Log user actions for crash context
  void logUserAction(String action, {Map<String, dynamic>? data}) {
    // Keep last 10 user actions for crash context
    if (_recentUserActions.length >= 10) {
      _recentUserActions.removeAt(0);
    }

    _recentUserActions.add(
      UserAction(action: action, timestamp: DateTime.now(), data: data),
    );
  }

  final List<UserAction> _recentUserActions = [];

  void _logError(ErrorLogEntry entry) {
    _errorLog.add(entry);
    _saveErrorLog();

    // Print to console in debug mode
    if (kDebugMode) {
      debugPrint('ERROR [${entry.level.name.toUpperCase()}]: ${entry.message}');
      if (entry.stackTrace != null) {
        debugPrint('Stack trace: ${entry.stackTrace}');
      }
    }
  }

  void _logCrash(CrashReport report) {
    _crashReports.add(report);
    _saveCrashReports();

    // Print to console in debug mode
    if (kDebugMode) {
      debugPrint(
        'CRASH [${report.type.name.toUpperCase()}]: ${report.exception}',
      );
      debugPrint('Stack trace: ${report.stackTrace}');
    }
  }

  Future<void> _saveErrorLog() async {
    try {
      // Keep only last 1000 entries
      if (_errorLog.length > 1000) {
        _errorLog.removeRange(0, _errorLog.length - 1000);
      }

      final jsonData = _errorLog.map((e) => e.toJson()).toList();
      await _logFile.writeAsString(jsonEncode(jsonData));
    } catch (e) {
      debugPrint('Error saving error log: $e');
    }
  }

  Future<void> _saveCrashReports() async {
    try {
      // Keep only last 100 crash reports
      if (_crashReports.length > 100) {
        _crashReports.removeRange(0, _crashReports.length - 100);
      }

      final jsonData = _crashReports.map((e) => e.toJson()).toList();
      await _crashFile.writeAsString(jsonEncode(jsonData));
    } catch (e) {
      debugPrint('Error saving crash reports: $e');
    }
  }

  Map<String, dynamic> _getDeviceInfo() {
    return {
      'platform': Platform.operatingSystem,
      'version': Platform.operatingSystemVersion,
      'locale': Platform.localeName,
      'numberOfProcessors': Platform.numberOfProcessors,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  // Get error statistics
  ErrorStatistics getErrorStatistics() {
    final now = DateTime.now();
    final last24Hours = now.subtract(const Duration(hours: 24));
    final last7Days = now.subtract(const Duration(days: 7));

    final errors24h = _errorLog
        .where((e) => e.timestamp.isAfter(last24Hours))
        .length;
    final errors7d = _errorLog
        .where((e) => e.timestamp.isAfter(last7Days))
        .length;
    final crashes24h = _crashReports
        .where((c) => c.timestamp.isAfter(last24Hours))
        .length;
    final crashes7d = _crashReports
        .where((c) => c.timestamp.isAfter(last7Days))
        .length;

    return ErrorStatistics(
      totalErrors: _errorLog.length,
      totalCrashes: _crashReports.length,
      errorsLast24Hours: errors24h,
      errorsLast7Days: errors7d,
      crashesLast24Hours: crashes24h,
      crashesLast7Days: crashes7d,
      lastErrorTime: _errorLog.isNotEmpty ? _errorLog.last.timestamp : null,
      lastCrashTime: _crashReports.isNotEmpty
          ? _crashReports.last.timestamp
          : null,
    );
  }

  // Export logs for debugging
  Future<String> exportLogs() async {
    final exportData = {
      'export_timestamp': DateTime.now().toIso8601String(),
      'app_version': '1.0.0',
      'build_number': '1',
      'device_info': _getDeviceInfo(),
      'error_log': _errorLog.map((e) => e.toJson()).toList(),
      'crash_reports': _crashReports.map((c) => c.toJson()).toList(),
      'statistics': getErrorStatistics().toJson(),
    };

    return jsonEncode(exportData);
  }

  // Clear old logs
  Future<void> clearOldLogs({Duration? olderThan}) async {
    final cutoff = DateTime.now().subtract(
      olderThan ?? const Duration(days: 30),
    );

    _errorLog.removeWhere((e) => e.timestamp.isBefore(cutoff));
    _crashReports.removeWhere((c) => c.timestamp.isBefore(cutoff));

    await _saveErrorLog();
    await _saveCrashReports();
  }

  // Get recent errors
  List<ErrorLogEntry> getRecentErrors({int limit = 50}) {
    final sorted = List<ErrorLogEntry>.from(_errorLog)
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return sorted.take(limit).toList();
  }

  // Get recent crashes
  List<CrashReport> getRecentCrashes({int limit = 20}) {
    final sorted = List<CrashReport>.from(_crashReports)
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return sorted.take(limit).toList();
  }
}

// Error Log Entry Model
class ErrorLogEntry {
  final String id;
  final DateTime timestamp;
  final ErrorLevel level;
  final String message;
  final String? stackTrace;
  final String? context;
  final String? library;
  final ErrorSource source;
  final Map<String, dynamic> deviceInfo;
  final String appVersion;
  final String buildNumber;
  final Map<String, dynamic>? additionalData;

  const ErrorLogEntry({
    required this.id,
    required this.timestamp,
    required this.level,
    required this.message,
    this.stackTrace,
    this.context,
    this.library,
    required this.source,
    required this.deviceInfo,
    required this.appVersion,
    required this.buildNumber,
    this.additionalData,
  });

  factory ErrorLogEntry.fromJson(Map<String, dynamic> json) {
    return ErrorLogEntry(
      id: json['id'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      level: ErrorLevel.values.firstWhere((e) => e.name == json['level']),
      message: json['message'] as String,
      stackTrace: json['stackTrace'] as String?,
      context: json['context'] as String?,
      library: json['library'] as String?,
      source: ErrorSource.values.firstWhere((e) => e.name == json['source']),
      deviceInfo: Map<String, dynamic>.from(json['deviceInfo'] as Map),
      appVersion: json['appVersion'] as String,
      buildNumber: json['buildNumber'] as String,
      additionalData: json['additionalData'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'level': level.name,
      'message': message,
      'stackTrace': stackTrace,
      'context': context,
      'library': library,
      'source': source.name,
      'deviceInfo': deviceInfo,
      'appVersion': appVersion,
      'buildNumber': buildNumber,
      'additionalData': additionalData,
    };
  }
}

// Crash Report Model
class CrashReport {
  final String id;
  final DateTime timestamp;
  final CrashType type;
  final String exception;
  final String stackTrace;
  final String? context;
  final Map<String, dynamic> deviceInfo;
  final String appVersion;
  final String buildNumber;
  final bool isFatal;
  final List<UserAction> userActions;

  const CrashReport({
    required this.id,
    required this.timestamp,
    required this.type,
    required this.exception,
    required this.stackTrace,
    this.context,
    required this.deviceInfo,
    required this.appVersion,
    required this.buildNumber,
    required this.isFatal,
    required this.userActions,
  });

  factory CrashReport.fromJson(Map<String, dynamic> json) {
    return CrashReport(
      id: json['id'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      type: CrashType.values.firstWhere((e) => e.name == json['type']),
      exception: json['exception'] as String,
      stackTrace: json['stackTrace'] as String,
      context: json['context'] as String?,
      deviceInfo: Map<String, dynamic>.from(json['deviceInfo'] as Map),
      appVersion: json['appVersion'] as String,
      buildNumber: json['buildNumber'] as String,
      isFatal: json['isFatal'] as bool,
      userActions:
          (json['userActions'] as List?)
              ?.map((e) => UserAction.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'type': type.name,
      'exception': exception,
      'stackTrace': stackTrace,
      'context': context,
      'deviceInfo': deviceInfo,
      'appVersion': appVersion,
      'buildNumber': buildNumber,
      'isFatal': isFatal,
      'userActions': userActions.map((e) => e.toJson()).toList(),
    };
  }
}

// User Action Model
class UserAction {
  final String action;
  final DateTime timestamp;
  final Map<String, dynamic>? data;

  const UserAction({required this.action, required this.timestamp, this.data});

  factory UserAction.fromJson(Map<String, dynamic> json) {
    return UserAction(
      action: json['action'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      data: json['data'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'action': action,
      'timestamp': timestamp.toIso8601String(),
      'data': data,
    };
  }
}

// Error Statistics Model
class ErrorStatistics {
  final int totalErrors;
  final int totalCrashes;
  final int errorsLast24Hours;
  final int errorsLast7Days;
  final int crashesLast24Hours;
  final int crashesLast7Days;
  final DateTime? lastErrorTime;
  final DateTime? lastCrashTime;

  const ErrorStatistics({
    required this.totalErrors,
    required this.totalCrashes,
    required this.errorsLast24Hours,
    required this.errorsLast7Days,
    required this.crashesLast24Hours,
    required this.crashesLast7Days,
    this.lastErrorTime,
    this.lastCrashTime,
  });

  Map<String, dynamic> toJson() {
    return {
      'totalErrors': totalErrors,
      'totalCrashes': totalCrashes,
      'errorsLast24Hours': errorsLast24Hours,
      'errorsLast7Days': errorsLast7Days,
      'crashesLast24Hours': crashesLast24Hours,
      'crashesLast7Days': crashesLast7Days,
      'lastErrorTime': lastErrorTime?.toIso8601String(),
      'lastCrashTime': lastCrashTime?.toIso8601String(),
    };
  }
}

// Error Log Notifier
class ErrorLogNotifier extends StateNotifier<List<ErrorLogEntry>> {
  ErrorLogNotifier() : super([]);

  void addError(ErrorLogEntry error) {
    state = [...state, error];
  }

  void clearErrors() {
    state = [];
  }

  void loadErrors(List<ErrorLogEntry> errors) {
    state = errors;
  }
}

// Crash Report Notifier
class CrashReportNotifier extends StateNotifier<List<CrashReport>> {
  CrashReportNotifier() : super([]);

  void addCrash(CrashReport crash) {
    state = [...state, crash];
  }

  void clearCrashes() {
    state = [];
  }

  void loadCrashes(List<CrashReport> crashes) {
    state = crashes;
  }
}

// Enums
enum ErrorLevel { debug, info, warning, error, critical }

enum ErrorSource { app, flutter, platform, network, database, performance }

enum CrashType { flutter, platform, network, outOfMemory, timeout }
