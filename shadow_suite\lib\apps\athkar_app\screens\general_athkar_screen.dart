import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class GeneralAthkarScreen extends ConsumerWidget {
  const GeneralAthkarScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('General Athkar'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildCategoryCard(
            context,
            'Tasbih Counter',
            'Digital counter for dhikr',
            Icons.radio_button_checked,
            Colors.blue,
            () => _showTasbihCounter(context),
          ),
          const SizedBox(height: 16),
          _buildCategoryCard(
            context,
            'Daily Dhikr',
            'Essential daily remembrance',
            Icons.today,
            Colors.green,
            () {},
          ),
          const SizedBox(height: 16),
          _buildCategoryCard(
            context,
            'Dua Collection',
            'Comprehensive dua collection',
            Icons.menu_book,
            Colors.purple,
            () {},
          ),
          const SizedBox(height: 16),
          _buildCategoryCard(
            context,
            'Names of Allah',
            '99 Beautiful Names',
            Icons.star,
            Colors.orange,
            () {},
          ),
          const SizedBox(height: 16),
          _buildCategoryCard(
            context,
            'Istighfar',
            'Seeking forgiveness',
            Icons.favorite,
            Colors.red,
            () {},
          ),
          const SizedBox(height: 16),
          _buildCategoryCard(
            context,
            'Salawat',
            'Blessings upon the Prophet',
            Icons.mosque,
            Colors.indigo,
            () {},
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryCard(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color),
        ),
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(description),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: onTap,
      ),
    );
  }

  void _showTasbihCounter(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const TasbihCounterDialog(),
    );
  }
}

class TasbihCounterDialog extends StatefulWidget {
  const TasbihCounterDialog({super.key});

  @override
  State<TasbihCounterDialog> createState() => _TasbihCounterDialogState();
}

class _TasbihCounterDialogState extends State<TasbihCounterDialog> {
  int _count = 0;
  String _selectedDhikr = 'سُبْحَانَ اللَّهِ';

  final List<Map<String, String>> _dhikrOptions = [
    {
      'arabic': 'سُبْحَانَ اللَّهِ',
      'transliteration': 'SubhanAllah',
      'translation': 'Glory is to Allah',
    },
    {
      'arabic': 'الْحَمْدُ لِلَّهِ',
      'transliteration': 'Alhamdulillah',
      'translation': 'All praise is due to Allah',
    },
    {
      'arabic': 'اللَّهُ أَكْبَرُ',
      'transliteration': 'Allahu Akbar',
      'translation': 'Allah is the Greatest',
    },
    {
      'arabic': 'لَا إِلَهَ إِلَّا اللَّهُ',
      'transliteration': 'La ilaha illa Allah',
      'translation': 'There is no god but Allah',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Tasbih Counter',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            
            // Dhikr Selection
            DropdownButtonFormField<String>(
              value: _selectedDhikr,
              decoration: const InputDecoration(
                labelText: 'Select Dhikr',
                border: OutlineInputBorder(),
              ),
              items: _dhikrOptions.map((dhikr) {
                return DropdownMenuItem(
                  value: dhikr['arabic'],
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(dhikr['arabic']!),
                      Text(
                        dhikr['transliteration']!,
                        style: const TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() => _selectedDhikr = value!);
              },
            ),
            const SizedBox(height: 24),

            // Counter Display
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.teal.withValues(alpha: 0.1),
                border: Border.all(color: Colors.teal, width: 2),
              ),
              child: Center(
                child: Text(
                  '$_count',
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.teal,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Current Dhikr Display
            Text(
              _selectedDhikr,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _dhikrOptions.firstWhere((d) => d['arabic'] == _selectedDhikr)['transliteration']!,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontStyle: FontStyle.italic,
                color: Colors.grey[700],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() => _count++);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.teal,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text('Count'),
                  ),
                ),
                const SizedBox(width: 12),
                OutlinedButton(
                  onPressed: () {
                    setState(() => _count = 0);
                  },
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.teal,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text('Reset'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            ),
          ],
        ),
      ),
    );
  }
}
