import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Optimized dashboard for Android with responsive design and mobile-first experience
class OptimizedDashboard extends StatefulWidget {
  const OptimizedDashboard({super.key});

  @override
  State<OptimizedDashboard> createState() => _OptimizedDashboardState();
}

class _OptimizedDashboardState extends State<OptimizedDashboard>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isGridView = true;

  // Animation controllers
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Initialize animations
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );

    // Start animations
    _fadeController.forward();
    _scaleController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Column(
              children: [
                _buildHeader(),
                _buildTabBar(),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildMainApps(),
                      _buildQuickActions(),
                      _buildSettings(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: _buildBottomNavigation(),
      floatingActionButton: _buildFloatingActionButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  /// Build optimized header with proper Android styling
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade700, Colors.blue.shade500],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.dashboard,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Shadow Suite',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'All-in-One Productivity Suite',
                      style: TextStyle(color: Colors.white70, fontSize: 12),
                    ),
                  ],
                ),
              ),
              IconButton(
                icon: Icon(
                  _isGridView ? Icons.view_list : Icons.grid_view,
                  color: Colors.white,
                ),
                onPressed: () {
                  setState(() {
                    _isGridView = !_isGridView;
                  });
                  HapticFeedback.lightImpact();
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build tab bar for navigation
  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: Colors.blue.shade700,
        unselectedLabelColor: Colors.grey.shade600,
        indicatorColor: Colors.blue.shade700,
        indicatorWeight: 3,
        tabs: const [
          Tab(icon: Icon(Icons.apps), text: 'Apps'),
          Tab(icon: Icon(Icons.flash_on), text: 'Quick'),
          Tab(icon: Icon(Icons.settings), text: 'Settings'),
        ],
      ),
    );
  }

  /// Build main apps section with optimized layout
  Widget _buildMainApps() {
    final apps = _getMainApps();

    return Container(
      padding: const EdgeInsets.all(16),
      child: _isGridView ? _buildGridView(apps) : _buildListView(apps),
    );
  }

  /// Build grid view for apps
  Widget _buildGridView(List<AppItem> apps) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.1,
      ),
      itemCount: apps.length,
      itemBuilder: (context, index) {
        final app = apps[index];
        return _buildAppCard(app, isGrid: true);
      },
    );
  }

  /// Build list view for apps
  Widget _buildListView(List<AppItem> apps) {
    return ListView.builder(
      itemCount: apps.length,
      itemBuilder: (context, index) {
        final app = apps[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _buildAppCard(app, isGrid: false),
        );
      },
    );
  }

  /// Build optimized app card with proper touch targets
  Widget _buildAppCard(AppItem app, {required bool isGrid}) {
    return Material(
      elevation: 2,
      borderRadius: BorderRadius.circular(16),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () {
          HapticFeedback.mediumImpact();
          _navigateToApp(app);
        },
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              colors: [app.primaryColor, app.secondaryColor],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: isGrid
              ? _buildGridCardContent(app)
              : _buildListCardContent(app),
        ),
      ),
    );
  }

  /// Build grid card content
  Widget _buildGridCardContent(AppItem app) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(app.icon, size: 32, color: Colors.white),
        ),
        const SizedBox(height: 12),
        Text(
          app.name,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Text(
          app.description,
          style: const TextStyle(color: Colors.white70, fontSize: 10),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  /// Build list card content
  Widget _buildListCardContent(AppItem app) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(app.icon, size: 24, color: Colors.white),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                app.name,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                app.description,
                style: const TextStyle(color: Colors.white70, fontSize: 12),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        const Icon(Icons.arrow_forward_ios, color: Colors.white70, size: 16),
      ],
    );
  }

  /// Build quick actions section
  Widget _buildQuickActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Quick Actions',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: GridView.count(
              crossAxisCount: 3,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              children: _getQuickActions().map((action) {
                return _buildQuickActionCard(action);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// Build quick action card
  Widget _buildQuickActionCard(QuickAction action) {
    return Material(
      elevation: 1,
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          HapticFeedback.lightImpact();
          action.onTap();
        },
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: action.color.withValues(alpha: 0.1),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(action.icon, size: 24, color: action.color),
              const SizedBox(height: 8),
              Text(
                action.name,
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: action.color,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build settings section
  Widget _buildSettings() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Settings',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView(
              children: _getSettingsItems().map((item) {
                return _buildSettingsItem(item);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// Build settings item
  Widget _buildSettingsItem(SettingsItem item) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(item.icon, color: item.color),
        title: Text(item.title),
        subtitle: Text(item.subtitle),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () {
          HapticFeedback.lightImpact();
          item.onTap();
        },
      ),
    );
  }

  /// Build bottom navigation
  Widget _buildBottomNavigation() {
    return BottomAppBar(
      shape: const CircularNotchedRectangle(),
      notchMargin: 8,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          IconButton(
            icon: const Icon(Icons.home),
            onPressed: () {
              HapticFeedback.lightImpact();
              // Navigate to home/dashboard
            },
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              HapticFeedback.lightImpact();
              _showSearchDialog();
            },
          ),
          const SizedBox(width: 40), // Space for FAB
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              HapticFeedback.lightImpact();
              _showNotifications();
            },
          ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              HapticFeedback.lightImpact();
              _showMoreOptions();
            },
          ),
        ],
      ),
    );
  }

  /// Build floating action button
  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: () {
        HapticFeedback.mediumImpact();
        _showQuickMenu();
      },
      backgroundColor: Colors.blue.shade700,
      child: const Icon(Icons.add, color: Colors.white),
    );
  }

  /// Get main apps list
  List<AppItem> _getMainApps() {
    return [
      AppItem(
        name: 'Money Manager',
        description: 'Track expenses and budgets',
        icon: Icons.account_balance_wallet,
        primaryColor: Colors.green.shade600,
        secondaryColor: Colors.green.shade400,
        route: '/money_manager',
      ),
      AppItem(
        name: 'Quran Suite',
        description: 'Read Quran and Islamic content',
        icon: Icons.book,
        primaryColor: Colors.teal.shade600,
        secondaryColor: Colors.teal.shade400,
        route: '/quran_suite',
      ),
      AppItem(
        name: 'Shadow Player',
        description: 'Music and video player',
        icon: Icons.play_circle,
        primaryColor: Colors.purple.shade600,
        secondaryColor: Colors.purple.shade400,
        route: '/shadow_player',
      ),
      AppItem(
        name: 'File Manager',
        description: 'Manage files and folders',
        icon: Icons.folder,
        primaryColor: Colors.blue.shade600,
        secondaryColor: Colors.blue.shade400,
        route: '/file_manager',
      ),
      AppItem(
        name: 'Smart Gallery+',
        description: 'AI-powered photo management',
        icon: Icons.photo_library,
        primaryColor: Colors.orange.shade600,
        secondaryColor: Colors.orange.shade400,
        route: '/smart_gallery',
      ),
      AppItem(
        name: 'Tools Builder',
        description: 'Create custom tools',
        icon: Icons.build,
        primaryColor: Colors.red.shade600,
        secondaryColor: Colors.red.shade400,
        route: '/tools_builder',
      ),
      AppItem(
        name: 'Excel-to-App',
        description: 'Convert Excel to apps',
        icon: Icons.table_chart,
        primaryColor: Colors.indigo.shade600,
        secondaryColor: Colors.indigo.shade400,
        route: '/excel_to_app',
      ),
    ];
  }

  /// Get quick actions list
  List<QuickAction> _getQuickActions() {
    return [
      QuickAction(
        name: 'Add Expense',
        icon: Icons.add_shopping_cart,
        color: Colors.red,
        onTap: () => _quickAddExpense(),
      ),
      QuickAction(
        name: 'Prayer Times',
        icon: Icons.access_time,
        color: Colors.green,
        onTap: () => _showPrayerTimes(),
      ),
      QuickAction(
        name: 'Quick Note',
        icon: Icons.note_add,
        color: Colors.blue,
        onTap: () => _quickNote(),
      ),
      QuickAction(
        name: 'Scan QR',
        icon: Icons.qr_code_scanner,
        color: Colors.orange,
        onTap: () => _scanQR(),
      ),
      QuickAction(
        name: 'Voice Memo',
        icon: Icons.mic,
        color: Colors.purple,
        onTap: () => _voiceMemo(),
      ),
      QuickAction(
        name: 'Calculator',
        icon: Icons.calculate,
        color: Colors.teal,
        onTap: () => _calculator(),
      ),
    ];
  }

  /// Get settings items list
  List<SettingsItem> _getSettingsItems() {
    return [
      SettingsItem(
        title: 'Notifications',
        subtitle: 'Manage app notifications',
        icon: Icons.notifications,
        color: Colors.blue,
        onTap: () => _openNotificationSettings(),
      ),
      SettingsItem(
        title: 'Theme',
        subtitle: 'Dark mode and appearance',
        icon: Icons.palette,
        color: Colors.purple,
        onTap: () => _openThemeSettings(),
      ),
      SettingsItem(
        title: 'Privacy',
        subtitle: 'Security and privacy settings',
        icon: Icons.security,
        color: Colors.green,
        onTap: () => _openPrivacySettings(),
      ),
      SettingsItem(
        title: 'Backup',
        subtitle: 'Data backup and sync',
        icon: Icons.backup,
        color: Colors.orange,
        onTap: () => _openBackupSettings(),
      ),
      SettingsItem(
        title: 'About',
        subtitle: 'App version and info',
        icon: Icons.info,
        color: Colors.grey,
        onTap: () => _showAbout(),
      ),
    ];
  }

  // Navigation and action methods
  void _navigateToApp(AppItem app) {
    // Navigate to specific app
    Navigator.pushNamed(context, app.route);
  }

  void _showSearchDialog() {
    // Show search dialog
  }

  void _showNotifications() {
    // Show notifications
  }

  void _showMoreOptions() {
    // Show more options
  }

  void _showQuickMenu() {
    // Show quick menu
  }

  void _quickAddExpense() {
    // Quick add expense
  }

  void _showPrayerTimes() {
    // Show prayer times
  }

  void _quickNote() {
    // Quick note
  }

  void _scanQR() {
    // Scan QR code
  }

  void _voiceMemo() {
    // Voice memo
  }

  void _calculator() {
    // Calculator
  }

  void _openNotificationSettings() {
    // Open notification settings
  }

  void _openThemeSettings() {
    // Open theme settings
  }

  void _openPrivacySettings() {
    // Open privacy settings
  }

  void _openBackupSettings() {
    // Open backup settings
  }

  void _showAbout() {
    // Show about dialog
  }
}

/// App item model
class AppItem {
  final String name;
  final String description;
  final IconData icon;
  final Color primaryColor;
  final Color secondaryColor;
  final String route;

  const AppItem({
    required this.name,
    required this.description,
    required this.icon,
    required this.primaryColor,
    required this.secondaryColor,
    required this.route,
  });
}

/// Quick action model
class QuickAction {
  final String name;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  const QuickAction({
    required this.name,
    required this.icon,
    required this.color,
    required this.onTap,
  });
}

/// Settings item model
class SettingsItem {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  const SettingsItem({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.onTap,
  });
}
