import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/quran_models.dart';
import '../services/quran_service.dart';

/// Dialog for displaying Tafseer (commentary) for a verse
class TafseerDialog extends StatefulWidget {
  final int surahNumber;
  final int verseNumber;
  final Verse verse;

  const TafseerDialog({
    super.key,
    required this.surahNumber,
    required this.verseNumber,
    required this.verse,
  });

  @override
  State<TafseerDialog> createState() => _TafseerDialogState();
}

class _TafseerDialogState extends State<TafseerDialog> {
  final QuranService _quranService = QuranService();
  List<Tafseer> _tafseers = [];
  bool _isLoading = true;
  String _selectedSource = 'ibn_kathir';

  @override
  void initState() {
    super.initState();
    _loadTafseer();
  }

  Future<void> _loadTafseer() async {
    try {
      final tafseers = await _quranService.getTafseer(
        widget.surahNumber,
        widget.verseNumber,
      );

      if (mounted) {
        setState(() {
          _tafseers = tafseers;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 600, maxHeight: 700),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(context),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _buildContent(context),
            ),
            _buildActions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.book, color: Theme.of(context).primaryColor, size: 24),
              const SizedBox(width: 12),
              Text(
                'Tafseer',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Surah ${widget.surahNumber}, Verse ${widget.verseNumber}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              widget.verse.arabicText,
              textAlign: TextAlign.right,
              textDirection: TextDirection.rtl,
              style: const TextStyle(fontSize: 18, height: 1.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    if (_tafseers.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.book_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No Tafseer available for this verse',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        _buildSourceSelector(context),
        Expanded(child: _buildTafseerContent(context)),
      ],
    );
  }

  Widget _buildSourceSelector(BuildContext context) {
    final sources = _tafseers.map((t) => t.source).toSet().toList();

    if (sources.length <= 1) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Text('Source:', style: Theme.of(context).textTheme.titleSmall),
          const SizedBox(width: 12),
          Expanded(
            child: DropdownButton<String>(
              value: _selectedSource,
              isExpanded: true,
              items: sources.map((source) {
                return DropdownMenuItem<String>(
                  value: source,
                  child: Text(_getSourceDisplayName(source)),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedSource = value;
                  });
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTafseerContent(BuildContext context) {
    final selectedTafseer = _tafseers.firstWhere(
      (t) => t.source == _selectedSource,
      orElse: () => _tafseers.first,
    );

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _getSourceDisplayName(selectedTafseer.source),
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'by ${selectedTafseer.author}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            selectedTafseer.text,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(height: 1.6),
          ),
        ],
      ),
    );
  }

  Widget _buildActions(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor.withValues(alpha: 0.3),
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton.icon(
            onPressed: _copyTafseer,
            icon: const Icon(Icons.content_copy),
            label: const Text('Copy'),
          ),
          const SizedBox(width: 12),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  String _getSourceDisplayName(String? source) {
    if (source == null) return 'Unknown';
    switch (source) {
      case 'ibn_kathir':
        return 'Ibn Kathir';
      case 'jalalayn':
        return 'Jalalayn';
      case 'qurtubi':
        return 'Al-Qurtubi';
      default:
        return source;
    }
  }

  void _copyTafseer() {
    if (_tafseers.isEmpty) return;

    final selectedTafseer = _tafseers.firstWhere(
      (t) => t.source == _selectedSource,
      orElse: () => _tafseers.first,
    );

    final text =
        'Tafseer for Surah ${widget.surahNumber}, Verse ${widget.verseNumber}\n\n'
        '${widget.verse.arabicText}\n\n'
        'Source: ${_getSourceDisplayName(selectedTafseer.source)} by ${selectedTafseer.author}\n\n'
        '${selectedTafseer.text}';

    Clipboard.setData(ClipboardData(text: text));

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Tafseer copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
