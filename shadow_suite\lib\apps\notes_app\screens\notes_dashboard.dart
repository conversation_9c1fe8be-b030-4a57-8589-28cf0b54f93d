import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/note_models.dart';
import '../services/notes_service.dart';

class NotesDashboard extends ConsumerWidget {
  const NotesDashboard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notes = ref.watch(notesProvider);
    final pinnedNotes = ref.watch(pinnedNotesProvider);
    final favoriteNotes = ref.watch(favoriteNotesProvider);
    final textNotes = ref.watch(textNotesProvider);
    final checklistNotes = ref.watch(checklistNotesProvider);
    final canvasNotes = ref.watch(canvasNotesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Notes'),
        backgroundColor: Colors.amber,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context, ref),
          ),
          PopupMenuButton<NoteType>(
            icon: const Icon(Icons.add),
            onSelected: (type) => _createNewNote(context, ref, type),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: NoteType.text,
                child: Row(
                  children: [
                    Icon(Icons.text_fields),
                    SizedBox(width: 8),
                    Text('Text Note'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: NoteType.checklist,
                child: Row(
                  children: [
                    Icon(Icons.checklist),
                    SizedBox(width: 8),
                    Text('Checklist'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: NoteType.canvas,
                child: Row(
                  children: [
                    Icon(Icons.draw),
                    SizedBox(width: 8),
                    Text('Canvas'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Statistics Cards
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Notes',
                    notes.length.toString(),
                    Icons.note,
                    Colors.amber,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Favorites',
                    favoriteNotes.length.toString(),
                    Icons.favorite,
                    Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Text Notes',
                    textNotes.length.toString(),
                    Icons.text_fields,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Checklists',
                    checklistNotes.length.toString(),
                    Icons.checklist,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Note Type Selector
            Row(
              children: [
                Expanded(
                  child: _buildNoteTypeCard(
                    'Text Notes',
                    'Write and organize your thoughts',
                    Icons.text_fields,
                    Colors.blue,
                    () => _createNewNote(context, ref, NoteType.text),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildNoteTypeCard(
                    'Checklists',
                    'Create task lists and todos',
                    Icons.checklist,
                    Colors.green,
                    () => _createNewNote(context, ref, NoteType.checklist),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildNoteTypeCard(
                    'Canvas',
                    'Draw and sketch ideas',
                    Icons.draw,
                    Colors.purple,
                    () => _createNewNote(context, ref, NoteType.canvas),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Pinned Notes
            if (pinnedNotes.isNotEmpty) ...[
              Text(
                'Pinned Notes',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              ...pinnedNotes.map((note) => _buildNoteCard(note, ref)),
              const SizedBox(height: 24),
            ],

            // Recent Notes
            Text(
              'Recent Notes',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (notes.isEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Icon(Icons.note_add, size: 48, color: Colors.grey[400]),
                    const SizedBox(height: 8),
                    Text(
                      'No notes yet',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Tap the + button to create your first note',
                      style: TextStyle(color: Colors.grey[500], fontSize: 12),
                    ),
                  ],
                ),
              )
            else
              ...notes.take(10).map((note) => _buildNoteCard(note, ref)),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(color: Colors.grey[600], fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNoteTypeCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: color,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(color: Colors.grey[600], fontSize: 10),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoteCard(Note note, WidgetRef ref) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: note.color,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getNoteTypeIcon(note.type),
                size: 20,
                color: Colors.grey[700],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  note.title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
              if (note.isPinned)
                Icon(Icons.push_pin, size: 16, color: Colors.grey[600]),
              if (note.isFavorite)
                Icon(Icons.favorite, size: 16, color: Colors.red),
              PopupMenuButton<String>(
                icon: Icon(Icons.more_vert, size: 16, color: Colors.grey[600]),
                onSelected: (action) => _handleNoteAction(action, note, ref),
                itemBuilder: (context) => [
                  const PopupMenuItem(value: 'edit', child: Text('Edit')),
                  const PopupMenuItem(value: 'pin', child: Text('Toggle Pin')),
                  const PopupMenuItem(
                    value: 'favorite',
                    child: Text('Toggle Favorite'),
                  ),
                  const PopupMenuItem(value: 'archive', child: Text('Archive')),
                  const PopupMenuItem(value: 'delete', child: Text('Delete')),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            note.preview,
            style: TextStyle(color: Colors.grey[700], fontSize: 14),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          if (note.type == NoteType.checklist &&
              note.checklistItems.isNotEmpty) ...[
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: note.completionPercentage,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
            ),
          ],
          const SizedBox(height: 8),
          Row(
            children: [
              ...note.tags
                  .take(3)
                  .map(
                    (tag) => Container(
                      margin: const EdgeInsets.only(right: 4),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        tag,
                        style: TextStyle(color: Colors.grey[700], fontSize: 10),
                      ),
                    ),
                  ),
              const Spacer(),
              Text(
                _formatDate(note.updatedAt),
                style: TextStyle(color: Colors.grey[500], fontSize: 10),
              ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getNoteTypeIcon(NoteType type) {
    switch (type) {
      case NoteType.text:
        return Icons.text_fields;
      case NoteType.checklist:
        return Icons.checklist;
      case NoteType.canvas:
        return Icons.draw;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _createNewNote(
    BuildContext context,
    WidgetRef ref,
    NoteType type,
  ) async {
    final now = DateTime.now();
    final note = Note(
      id: now.millisecondsSinceEpoch.toString(),
      title: 'New ${type.name} note',
      type: type,
      createdAt: now,
      updatedAt: now,
    );

    await ref.read(notesServiceProvider).addNote(note);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${type.name} note created'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _handleNoteAction(String action, Note note, WidgetRef ref) async {
    final service = ref.read(notesServiceProvider);

    switch (action) {
      case 'edit':
        // Navigate to edit screen
        break;
      case 'pin':
        await service.togglePin(note.id);
        break;
      case 'favorite':
        await service.toggleFavorite(note.id);
        break;
      case 'archive':
        await service.archiveNote(note.id);
        break;
      case 'delete':
        await service.deleteNote(note.id);
        break;
    }
  }

  void _showSearchDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Notes'),
        content: const Text('Search functionality will be implemented here'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
