import 'dart:math' as math;
import 'performance_optimizer.dart';

class AdvancedFormulaEngine {
  static final Map<String, Function> _functions = {};
  static final Map<String, dynamic> _variables = {};
  static final List<String> _supportedFunctions = [];
  static bool _isInitialized = false;

  // Initialize the formula engine with Excel-compatible functions
  static void initialize() {
    if (_isInitialized) return;

    _registerMathFunctions();
    _registerStatisticalFunctions();
    _registerTextFunctions();
    _registerDateTimeFunctions();
    _registerLogicalFunctions();
    _registerLookupFunctions();
    _registerFinancialFunctions();
    _registerInformationFunctions();
    _registerEngineeringFunctions();
    _registerDatabaseFunctions();
    _registerArrayFunctions();
    _registerAdvancedMathFunctions();
    _registerAdvancedStatisticalFunctions();
    _registerAdvancedTextFunctions();
    _registerAdvancedLogicalFunctions();

    _isInitialized = true;
  }

  // Getters for test compatibility
  static bool get isInitialized => _isInitialized;
  static List<String> get supportedFunctions => getSupportedFunctions();

  // Validate formula method for test compatibility
  static FormulaValidationResult validateFormula(String formula) {
    try {
      evaluate(formula);
      return FormulaValidationResult(isValid: true, error: null);
    } catch (e) {
      return FormulaValidationResult(isValid: false, error: e.toString());
    }
  }

  // Simple evaluate method for testing
  static dynamic evaluate(String formula) {
    return PerformanceOptimizer.measureSync('formula_evaluation', () {
      try {
        // Check cache first
        final cacheKey = 'formula_$formula';
        final cached = PerformanceOptimizer.getCachedValue(cacheKey);
        if (cached != null) {
          return cached;
        }

        // Remove leading = if present
        if (formula.startsWith('=')) {
          formula = formula.substring(1);
        }

        final result = _evaluateSimpleFormula(formula, {});

        // Cache the result for future use
        PerformanceOptimizer.cacheValue(cacheKey, result);

        return result;
      } catch (e) {
        return '#ERROR!';
      }
    });
  }

  // Evaluate a formula
  static FormulaResult evaluateFormula(
    String formula,
    Map<String, dynamic> cellValues,
  ) {
    try {
      // Update variables with current cell values
      _variables.clear();
      _variables.addAll(cellValues);

      // Clean and prepare formula
      final cleanFormula = _preprocessFormula(formula);

      // Simple formula evaluation (basic implementation)
      final result = _evaluateSimpleFormula(cleanFormula, cellValues);

      return FormulaResult(
        value: result,
        isError: false,
        dependencies: _extractCellReferences(formula),
        calculationTime: DateTime.now(),
      );
    } catch (e) {
      return FormulaResult(
        value: '#ERROR!',
        isError: true,
        error: e.toString(),
        dependencies: _extractCellReferences(formula),
        calculationTime: DateTime.now(),
      );
    }
  }

  // Simple formula evaluation implementation
  static dynamic _evaluateSimpleFormula(
    String formula,
    Map<String, dynamic> cellValues,
  ) {
    // Handle basic functions
    if (formula.startsWith('SUM(') && formula.endsWith(')')) {
      final args = _parseArguments(formula.substring(4, formula.length - 1));
      return _sum(args.map((arg) => _resolveValue(arg, cellValues)).toList());
    }

    if (formula.startsWith('AVERAGE(') && formula.endsWith(')')) {
      final args = _parseArguments(formula.substring(8, formula.length - 1));
      return _average(
        args.map((arg) => _resolveValue(arg, cellValues)).toList(),
      );
    }

    // Handle basic arithmetic
    if (formula.contains('+')) {
      final parts = formula.split('+');
      return parts.fold<double>(
        0.0,
        (sum, part) => sum + _resolveNumericValue(part.trim(), cellValues),
      );
    }

    if (formula.contains('-')) {
      final parts = formula.split('-');
      if (parts.length == 2) {
        return _resolveNumericValue(parts[0].trim(), cellValues) -
            _resolveNumericValue(parts[1].trim(), cellValues);
      }
    }

    if (formula.contains('*')) {
      final parts = formula.split('*');
      return parts.fold<double>(
        1.0,
        (product, part) =>
            product * _resolveNumericValue(part.trim(), cellValues),
      );
    }

    if (formula.contains('/')) {
      final parts = formula.split('/');
      if (parts.length == 2) {
        final divisor = _resolveNumericValue(parts[1].trim(), cellValues);
        if (divisor == 0) throw Exception('Division by zero');
        return _resolveNumericValue(parts[0].trim(), cellValues) / divisor;
      }
    }

    // Handle cell references and literals
    return _resolveValue(formula, cellValues);
  }

  static List<String> _parseArguments(String args) {
    return args.split(',').map((arg) => arg.trim()).toList();
  }

  static dynamic _resolveValue(String value, Map<String, dynamic> cellValues) {
    // Check if it's a cell reference
    if (cellValues.containsKey(value)) {
      return cellValues[value];
    }

    // Try to parse as number
    final numValue = double.tryParse(value);
    if (numValue != null) {
      return numValue;
    }

    // Return as string
    return value;
  }

  static double _resolveNumericValue(
    String value,
    Map<String, dynamic> cellValues,
  ) {
    final resolved = _resolveValue(value, cellValues);
    if (resolved is num) {
      return resolved.toDouble();
    }
    return double.tryParse(resolved.toString()) ?? 0.0;
  }

  // Register mathematical functions
  static void _registerMathFunctions() {
    // Basic math functions
    _functions['ABS'] = (List args) => (args[0] as num).abs();
    _functions['ROUND'] = (List args) =>
        _round(args[0] as num, args.length > 1 ? args[1] as int : 0);
    _functions['ROUNDUP'] = (List args) =>
        _roundUp(args[0] as num, args.length > 1 ? args[1] as int : 0);
    _functions['ROUNDDOWN'] = (List args) =>
        _roundDown(args[0] as num, args.length > 1 ? args[1] as int : 0);
    _functions['CEILING'] = (List args) => (args[0] as num).ceil();
    _functions['FLOOR'] = (List args) => (args[0] as num).floor();
    _functions['MOD'] = (List args) => (args[0] as num) % (args[1] as num);
    _functions['POWER'] = (List args) =>
        math.pow(args[0] as num, args[1] as num);
    _functions['SQRT'] = (List args) => math.sqrt(args[0] as num);
    _functions['EXP'] = (List args) => math.exp(args[0] as num);
    _functions['LN'] = (List args) => math.log(args[0] as num);
    _functions['LOG'] = (List args) => args.length > 1
        ? math.log(args[0] as num) / math.log(args[1] as num)
        : math.log(args[0] as num) / math.ln10;
    _functions['LOG10'] = (List args) => math.log(args[0] as num) / math.ln10;

    // Trigonometric functions
    _functions['SIN'] = (List args) => math.sin(args[0] as num);
    _functions['COS'] = (List args) => math.cos(args[0] as num);
    _functions['TAN'] = (List args) => math.tan(args[0] as num);
    _functions['ASIN'] = (List args) => math.asin(args[0] as num);
    _functions['ACOS'] = (List args) => math.acos(args[0] as num);
    _functions['ATAN'] = (List args) => math.atan(args[0] as num);
    _functions['ATAN2'] = (List args) =>
        math.atan2(args[0] as num, args[1] as num);
    _functions['RADIANS'] = (List args) => (args[0] as num) * math.pi / 180;
    _functions['DEGREES'] = (List args) => (args[0] as num) * 180 / math.pi;

    // Constants
    _functions['PI'] = (List args) => math.pi;
    _functions['E'] = (List args) => math.e;

    _supportedFunctions.addAll(_functions.keys);
  }

  // Register statistical functions
  static void _registerStatisticalFunctions() {
    _functions['SUM'] = (List args) => _sum(args);
    _functions['AVERAGE'] = (List args) => _average(args);
    _functions['COUNT'] = (List args) => _count(args);
    _functions['COUNTA'] = (List args) => _countA(args);
    _functions['MIN'] = (List args) => _min(args);
    _functions['MAX'] = (List args) => _max(args);
    _functions['MEDIAN'] = (List args) => _median(args);
    _functions['MODE'] = (List args) => _mode(args);
    _functions['STDEV'] = (List args) => _standardDeviation(args);
    _functions['VAR'] = (List args) => _variance(args);
    _functions['CORREL'] = (List args) =>
        _correlation(args[0] as List, args[1] as List);

    _supportedFunctions.addAll([
      'SUM',
      'AVERAGE',
      'COUNT',
      'COUNTA',
      'MIN',
      'MAX',
      'MEDIAN',
      'MODE',
      'STDEV',
      'VAR',
      'CORREL',
    ]);
  }

  // Register text functions
  static void _registerTextFunctions() {
    _functions['CONCATENATE'] = (List args) => args.join('');
    _functions['LEFT'] = (List args) => (args[0] as String).substring(
      0,
      math.min(args[1] as int, (args[0] as String).length),
    );
    _functions['RIGHT'] = (List args) => (args[0] as String).substring(
      math.max(0, (args[0] as String).length - (args[1] as int)),
    );

    _functions['MID'] = (List args) {
      final text = args[0] as String;
      final start = (args[1] as int) - 1;
      final length = args[2] as int;
      final end = math.min(start + length, text.length);
      return text.substring(start, end);
    };

    _functions['LEN'] = (List args) => (args[0] as String).length;
    _functions['UPPER'] = (List args) => (args[0] as String).toUpperCase();
    _functions['LOWER'] = (List args) => (args[0] as String).toLowerCase();
    _functions['PROPER'] = (List args) => _properCase(args[0] as String);
    _functions['TRIM'] = (List args) => (args[0] as String).trim();
    _functions['FIND'] = (List args) =>
        (args[1] as String).indexOf(args[0] as String) + 1;
    _functions['SEARCH'] = (List args) =>
        (args[1] as String).toLowerCase().indexOf(
          (args[0] as String).toLowerCase(),
        ) +
        1;
    _functions['SUBSTITUTE'] = (List args) =>
        (args[0] as String).replaceAll(args[1] as String, args[2] as String);
    _functions['REPLACE'] = (List args) => _replace(
      args[0] as String,
      args[1] as int,
      args[2] as int,
      args[3] as String,
    );

    _supportedFunctions.addAll([
      'CONCATENATE',
      'LEFT',
      'RIGHT',
      'MID',
      'LEN',
      'UPPER',
      'LOWER',
      'PROPER',
      'TRIM',
      'FIND',
      'SEARCH',
      'SUBSTITUTE',
      'REPLACE',
    ]);
  }

  // Register date/time functions
  static void _registerDateTimeFunctions() {
    _functions['NOW'] = (List args) => DateTime.now();
    _functions['TODAY'] = (List args) => DateTime.now();
    _functions['YEAR'] = (List args) => (args[0] as DateTime).year;
    _functions['MONTH'] = (List args) => (args[0] as DateTime).month;
    _functions['DAY'] = (List args) => (args[0] as DateTime).day;
    _functions['HOUR'] = (List args) => (args[0] as DateTime).hour;
    _functions['MINUTE'] = (List args) => (args[0] as DateTime).minute;
    _functions['SECOND'] = (List args) => (args[0] as DateTime).second;
    _functions['WEEKDAY'] = (List args) => (args[0] as DateTime).weekday;
    _functions['DATE'] = (List args) =>
        DateTime(args[0] as int, args[1] as int, args[2] as int);
    _functions['TIME'] = (List args) => Duration(
      hours: args[0] as int,
      minutes: args[1] as int,
      seconds: args[2] as int,
    );
    _functions['DATEDIF'] = (List args) => _dateDifference(
      args[0] as DateTime,
      args[1] as DateTime,
      args[2] as String,
    );

    _supportedFunctions.addAll([
      'NOW',
      'TODAY',
      'YEAR',
      'MONTH',
      'DAY',
      'HOUR',
      'MINUTE',
      'SECOND',
      'WEEKDAY',
      'DATE',
      'TIME',
      'DATEDIF',
    ]);
  }

  // Register logical functions
  static void _registerLogicalFunctions() {
    _functions['IF'] = (List args) => args[0] ? args[1] : args[2];
    _functions['AND'] = (List args) => args.every((arg) => arg == true);
    _functions['OR'] = (List args) => args.any((arg) => arg == true);
    _functions['NOT'] = (List args) => !(args[0] as bool);
    _functions['TRUE'] = (List args) => true;
    _functions['FALSE'] = (List args) => false;
    _functions['ISNUMBER'] = (List args) => args[0] is num;
    _functions['ISTEXT'] = (List args) => args[0] is String;
    _functions['ISBLANK'] = (List args) => args[0] == null || args[0] == '';
    _functions['ISERROR'] = (List args) =>
        args[0] is String && (args[0] as String).startsWith('#');

    _supportedFunctions.addAll([
      'IF',
      'AND',
      'OR',
      'NOT',
      'TRUE',
      'FALSE',
      'ISNUMBER',
      'ISTEXT',
      'ISBLANK',
      'ISERROR',
    ]);
  }

  // Register lookup functions
  static void _registerLookupFunctions() {
    _functions['VLOOKUP'] = (List args) => _vlookup(
      args[0],
      args[1] as List<List>,
      args[2] as int,
      args.length > 3 ? args[3] as bool : false,
    );
    _functions['HLOOKUP'] = (List args) => _hlookup(
      args[0],
      args[1] as List<List>,
      args[2] as int,
      args.length > 3 ? args[3] as bool : false,
    );
    _functions['INDEX'] = (List args) => _index(
      args[0] as List,
      args[1] as int,
      args.length > 2 ? args[2] as int : null,
    );
    _functions['MATCH'] = (List args) =>
        _match(args[0], args[1] as List, args.length > 2 ? args[2] as int : 1);

    _supportedFunctions.addAll(['VLOOKUP', 'HLOOKUP', 'INDEX', 'MATCH']);
  }

  // Register financial functions
  static void _registerFinancialFunctions() {
    _functions['PMT'] = (List args) =>
        _pmt(args[0] as num, args[1] as int, args[2] as num);
    _functions['PV'] = (List args) =>
        _pv(args[0] as num, args[1] as int, args[2] as num);
    _functions['FV'] = (List args) =>
        _fv(args[0] as num, args[1] as int, args[2] as num);
    _functions['NPV'] = (List args) =>
        _npv(args[0] as num, args.sublist(1).cast<num>());
    _functions['IRR'] = (List args) => _irr(args[0] as List<num>);

    _supportedFunctions.addAll(['PMT', 'PV', 'FV', 'NPV', 'IRR']);
  }

  // Register information functions
  static void _registerInformationFunctions() {
    _functions['CELL'] = (List args) => _cellInfo(args[0] as String, args[1]);
    _functions['INFO'] = (List args) => _systemInfo(args[0] as String);
    _functions['TYPE'] = (List args) => _getType(args[0]);

    _supportedFunctions.addAll(['CELL', 'INFO', 'TYPE']);
  }

  // Helper functions for mathematical operations
  static double _round(num value, int digits) {
    final factor = math.pow(10, digits);
    return (value * factor).round() / factor;
  }

  static double _roundUp(num value, int digits) {
    final factor = math.pow(10, digits);
    return (value * factor).ceil() / factor;
  }

  static double _roundDown(num value, int digits) {
    final factor = math.pow(10, digits);
    return (value * factor).floor() / factor;
  }

  // Helper functions for statistical operations
  static num _sum(List args) {
    return args.whereType<num>().fold<num>(0, (sum, value) => sum + value);
  }

  static double _average(List args) {
    final numbers = args.whereType<num>().toList();
    return numbers.isEmpty ? 0 : _sum(numbers).toDouble() / numbers.length;
  }

  static int _count(List args) {
    return args.whereType<num>().length;
  }

  static int _countA(List args) {
    return args.where((arg) => arg != null && arg != '').length;
  }

  static num _min(List args) {
    final numbers = args.whereType<num>().toList();
    return numbers.isEmpty ? 0 : numbers.reduce(math.min);
  }

  static num _max(List args) {
    final numbers = args.whereType<num>().toList();
    return numbers.isEmpty ? 0 : numbers.reduce(math.max);
  }

  static double _median(List args) {
    final numbers = args.whereType<num>().toList()..sort();
    if (numbers.isEmpty) return 0;

    final middle = numbers.length ~/ 2;
    if (numbers.length % 2 == 0) {
      return (numbers[middle - 1] + numbers[middle]) / 2;
    } else {
      return numbers[middle].toDouble();
    }
  }

  static num _mode(List args) {
    final numbers = args.whereType<num>().toList();
    if (numbers.isEmpty) return 0;

    final frequency = <num, int>{};
    for (final number in numbers) {
      frequency[number] = (frequency[number] ?? 0) + 1;
    }

    var maxCount = 0;
    num mode = 0;
    for (final entry in frequency.entries) {
      if (entry.value > maxCount) {
        maxCount = entry.value;
        mode = entry.key;
      }
    }

    return mode;
  }

  static double _standardDeviation(List args) {
    final numbers = args.whereType<num>().toList();
    if (numbers.length < 2) return 0;

    final mean = _average(numbers);
    final variance =
        numbers.map((x) => math.pow(x - mean, 2)).reduce((a, b) => a + b) /
        (numbers.length - 1);
    return math.sqrt(variance);
  }

  static double _variance(List args) {
    final numbers = args.whereType<num>().toList();
    if (numbers.length < 2) return 0;

    final mean = _average(numbers);
    return numbers.map((x) => math.pow(x - mean, 2)).reduce((a, b) => a + b) /
        (numbers.length - 1);
  }

  static double _correlation(List x, List y) {
    final xNums = x.whereType<num>().toList();
    final yNums = y.whereType<num>().toList();

    if (xNums.length != yNums.length || xNums.length < 2) return 0;

    final meanX = xNums.reduce((a, b) => a + b) / xNums.length;
    final meanY = yNums.reduce((a, b) => a + b) / yNums.length;

    var numerator = 0.0;
    var sumXSquared = 0.0;
    var sumYSquared = 0.0;

    for (int i = 0; i < xNums.length; i++) {
      final deltaX = xNums[i] - meanX;
      final deltaY = yNums[i] - meanY;
      numerator += deltaX * deltaY;
      sumXSquared += deltaX * deltaX;
      sumYSquared += deltaY * deltaY;
    }

    final denominator = math.sqrt(sumXSquared * sumYSquared);
    return denominator == 0 ? 0 : numerator / denominator;
  }

  // Helper functions for text operations
  static String _properCase(String text) {
    return text
        .split(' ')
        .map((word) {
          if (word.isEmpty) return word;
          return word[0].toUpperCase() + word.substring(1).toLowerCase();
        })
        .join(' ');
  }

  static String _replace(
    String text,
    int start,
    int length,
    String replacement,
  ) {
    final startIndex = start - 1; // Excel uses 1-based indexing
    final endIndex = startIndex + length;
    return text.substring(0, startIndex) +
        replacement +
        text.substring(endIndex);
  }

  // Helper functions for date operations
  static int _dateDifference(DateTime start, DateTime end, String unit) {
    switch (unit.toUpperCase()) {
      case 'Y':
        return end.year - start.year;
      case 'M':
        return (end.year - start.year) * 12 + (end.month - start.month);
      case 'D':
        return end.difference(start).inDays;
      default:
        return 0;
    }
  }

  // Helper functions for lookup operations
  static dynamic _vlookup(
    dynamic lookupValue,
    List<List> table,
    int colIndex,
    bool exactMatch,
  ) {
    for (final row in table) {
      if (row.isNotEmpty) {
        if (exactMatch) {
          if (row[0] == lookupValue) {
            return colIndex <= row.length ? row[colIndex - 1] : '#N/A';
          }
        } else {
          // Approximate match logic would go here
          if (row[0] == lookupValue) {
            return colIndex <= row.length ? row[colIndex - 1] : '#N/A';
          }
        }
      }
    }
    return '#N/A';
  }

  static dynamic _hlookup(
    dynamic lookupValue,
    List<List> table,
    int rowIndex,
    bool exactMatch,
  ) {
    if (table.isNotEmpty && table[0].isNotEmpty) {
      final headerRow = table[0];
      for (int i = 0; i < headerRow.length; i++) {
        if (exactMatch) {
          if (headerRow[i] == lookupValue) {
            return rowIndex <= table.length ? table[rowIndex - 1][i] : '#N/A';
          }
        } else {
          // Approximate match logic would go here
          if (headerRow[i] == lookupValue) {
            return rowIndex <= table.length ? table[rowIndex - 1][i] : '#N/A';
          }
        }
      }
    }
    return '#N/A';
  }

  static dynamic _index(List array, int row, int? col) {
    if (array.isEmpty) return '#REF!';

    if (col == null) {
      // Single dimension array
      return row <= array.length ? array[row - 1] : '#REF!';
    } else {
      // Two dimension array
      if (array is List<List>) {
        return row <= array.length && col <= array[row - 1].length
            ? array[row - 1][col - 1]
            : '#REF!';
      }
    }
    return '#REF!';
  }

  static int _match(dynamic lookupValue, List array, int matchType) {
    for (int i = 0; i < array.length; i++) {
      if (matchType == 0) {
        // Exact match
        if (array[i] == lookupValue) return i + 1;
      } else if (matchType == 1) {
        // Less than or equal to
        if (array[i] <= lookupValue) return i + 1;
      } else if (matchType == -1) {
        // Greater than or equal to
        if (array[i] >= lookupValue) return i + 1;
      }
    }
    return -1; // #N/A
  }

  // Helper functions for financial operations
  static double _pmt(num rate, int nper, num pv) {
    if (rate == 0) return -pv / nper;
    return -pv *
        (rate * math.pow(1 + rate, nper)) /
        (math.pow(1 + rate, nper) - 1);
  }

  static double _pv(num rate, int nper, num pmt) {
    if (rate == 0) return (-pmt * nper).toDouble();
    return (-pmt * (1 - math.pow(1 + rate, -nper)) / rate).toDouble();
  }

  static double _fv(num rate, int nper, num pmt) {
    if (rate == 0) return (-pmt * nper).toDouble();
    return (-pmt * (math.pow(1 + rate, nper) - 1) / rate).toDouble();
  }

  static double _npv(num rate, List<num> values) {
    var npv = 0.0;
    for (int i = 0; i < values.length; i++) {
      npv += values[i] / math.pow(1 + rate, i + 1);
    }
    return npv;
  }

  static double _irr(List<num> values) {
    // Simplified IRR calculation using Newton-Raphson method
    var rate = 0.1; // Initial guess
    const tolerance = 1e-6;
    const maxIterations = 100;

    for (int i = 0; i < maxIterations; i++) {
      var npv = 0.0;
      var dnpv = 0.0;

      for (int j = 0; j < values.length; j++) {
        final power = math.pow(1 + rate, j);
        npv += values[j] / power;
        dnpv -= j * values[j] / (power * (1 + rate));
      }

      if (npv.abs() < tolerance) break;
      rate = rate - npv / dnpv;
    }

    return rate;
  }

  // Helper functions for information operations
  static dynamic _cellInfo(String infoType, dynamic cell) {
    switch (infoType.toLowerCase()) {
      case 'type':
        return _getType(cell);
      case 'contents':
        return cell;
      default:
        return '#N/A';
    }
  }

  static String _systemInfo(String infoType) {
    switch (infoType.toLowerCase()) {
      case 'system':
        return 'Flutter';
      case 'version':
        return '1.0.0';
      default:
        return '#N/A';
    }
  }

  static int _getType(dynamic value) {
    if (value is num) return 1;
    if (value is String) return 2;
    if (value is bool) return 4;
    if (value == null) return 1; // Blank
    return 64; // Error
  }

  // Utility functions
  static String _preprocessFormula(String formula) {
    // Remove leading = if present
    if (formula.startsWith('=')) {
      formula = formula.substring(1);
    }

    // Convert Excel cell references to variables
    formula = formula.replaceAllMapped(
      RegExp(r'([A-Z]+)(\d+)'),
      (match) => 'cell_${match.group(1)}_${match.group(2)}',
    );

    return formula;
  }

  static List<String> _extractCellReferences(String formula) {
    final references = <String>[];
    final regex = RegExp(r'([A-Z]+)(\d+)');
    final matches = regex.allMatches(formula);

    for (final match in matches) {
      references.add('${match.group(1)}${match.group(2)}');
    }

    return references;
  }

  // Get list of supported functions
  static List<String> getSupportedFunctions() {
    return List.from(_supportedFunctions)..sort();
  }

  // Get function help
  static FunctionHelp? getFunctionHelp(String functionName) {
    return _functionHelpData[functionName.toUpperCase()];
  }

  // Register engineering functions
  static void _registerEngineeringFunctions() {
    _functions['CONVERT'] = (List args) =>
        _convert(args[0] as num, args[1] as String, args[2] as String);
    _functions['HEX2DEC'] = (List args) =>
        int.parse(args[0] as String, radix: 16);
    _functions['DEC2HEX'] = (List args) =>
        (args[0] as int).toRadixString(16).toUpperCase();
    _functions['BIN2DEC'] = (List args) =>
        int.parse(args[0] as String, radix: 2);
    _functions['DEC2BIN'] = (List args) => (args[0] as int).toRadixString(2);
    _functions['OCT2DEC'] = (List args) =>
        int.parse(args[0] as String, radix: 8);
    _functions['DEC2OCT'] = (List args) => (args[0] as int).toRadixString(8);
    _functions['BITAND'] = (List args) => (args[0] as int) & (args[1] as int);
    _functions['BITOR'] = (List args) => (args[0] as int) | (args[1] as int);
    _functions['BITXOR'] = (List args) => (args[0] as int) ^ (args[1] as int);
    _functions['BITLSHIFT'] = (List args) =>
        (args[0] as int) << (args[1] as int);
    _functions['BITRSHIFT'] = (List args) =>
        (args[0] as int) >> (args[1] as int);

    _supportedFunctions.addAll([
      'CONVERT',
      'HEX2DEC',
      'DEC2HEX',
      'BIN2DEC',
      'DEC2BIN',
      'OCT2DEC',
      'DEC2OCT',
      'BITAND',
      'BITOR',
      'BITXOR',
      'BITLSHIFT',
      'BITRSHIFT',
    ]);
  }

  // Register database functions
  static void _registerDatabaseFunctions() {
    _functions['DSUM'] = (List args) =>
        _dsum(args[0] as List<List>, args[1] as String, args[2] as Map);
    _functions['DCOUNT'] = (List args) =>
        _dcount(args[0] as List<List>, args[1] as String, args[2] as Map);
    _functions['DAVERAGE'] = (List args) =>
        _daverage(args[0] as List<List>, args[1] as String, args[2] as Map);
    _functions['DMAX'] = (List args) =>
        _dmax(args[0] as List<List>, args[1] as String, args[2] as Map);
    _functions['DMIN'] = (List args) =>
        _dmin(args[0] as List<List>, args[1] as String, args[2] as Map);
    _functions['DGET'] = (List args) =>
        _dget(args[0] as List<List>, args[1] as String, args[2] as Map);

    _supportedFunctions.addAll([
      'DSUM',
      'DCOUNT',
      'DAVERAGE',
      'DMAX',
      'DMIN',
      'DGET',
    ]);
  }

  // Register array functions
  static void _registerArrayFunctions() {
    _functions['TRANSPOSE'] = (List args) => _transpose(args[0] as List<List>);
    _functions['SORT'] = (List args) =>
        _sortArray(args[0] as List, args.length > 1 ? args[1] as bool : true);
    _functions['FILTER'] = (List args) =>
        _filterArray(args[0] as List, args[1] as List<bool>);
    _functions['UNIQUE'] = (List args) => _uniqueArray(args[0] as List);
    _functions['SEQUENCE'] = (List args) =>
        _sequence(args[0] as int, args.length > 1 ? args[1] as int : 1);
    _functions['RANDARRAY'] = (List args) =>
        _randArray(args[0] as int, args.length > 1 ? args[1] as int : 1);

    _supportedFunctions.addAll([
      'TRANSPOSE',
      'SORT',
      'FILTER',
      'UNIQUE',
      'SEQUENCE',
      'RANDARRAY',
    ]);
  }

  // Register advanced mathematical functions
  static void _registerAdvancedMathFunctions() {
    _functions['FACT'] = (List args) => _factorial(args[0] as int);
    _functions['FACTDOUBLE'] = (List args) => _doubleFactorial(args[0] as int);
    _functions['COMBIN'] = (List args) =>
        _combination(args[0] as int, args[1] as int);
    _functions['PERMUT'] = (List args) =>
        _permutation(args[0] as int, args[1] as int);
    _functions['GCD'] = (List args) => _gcd(args.cast<int>());
    _functions['LCM'] = (List args) => _lcm(args.cast<int>());
    _functions['MROUND'] = (List args) =>
        _mround(args[0] as num, args[1] as num);
    _functions['QUOTIENT'] = (List args) =>
        (args[0] as num) ~/ (args[1] as num);
    _functions['SIGN'] = (List args) => (args[0] as num).sign.toInt();
    _functions['SUMPRODUCT'] = (List args) =>
        _sumProduct(args[0] as List, args[1] as List);
    _functions['SUMSQ'] = (List args) => _sumSquares(args);
    _functions['SUMIF'] = (List args) => _sumIf(
      args[0] as List,
      args[1],
      args.length > 2 ? args[2] as List : null,
    );
    _functions['COUNTIF'] = (List args) => _countIf(args[0] as List, args[1]);
    _functions['AVERAGEIF'] = (List args) => _averageIf(
      args[0] as List,
      args[1],
      args.length > 2 ? args[2] as List : null,
    );

    _supportedFunctions.addAll([
      'FACT',
      'FACTDOUBLE',
      'COMBIN',
      'PERMUT',
      'GCD',
      'LCM',
      'MROUND',
      'QUOTIENT',
      'SIGN',
      'SUMPRODUCT',
      'SUMSQ',
      'SUMIF',
      'COUNTIF',
      'AVERAGEIF',
    ]);
  }

  // Register advanced statistical functions
  static void _registerAdvancedStatisticalFunctions() {
    _functions['PERCENTILE'] = (List args) =>
        _percentile(args[0] as List, args[1] as num);
    _functions['QUARTILE'] = (List args) =>
        _quartile(args[0] as List, args[1] as int);
    _functions['RANK'] = (List args) => _rank(
      args[0],
      args[1] as List,
      args.length > 2 ? args[2] as bool : false,
    );
    _functions['LARGE'] = (List args) =>
        _large(args[0] as List, args[1] as int);
    _functions['SMALL'] = (List args) =>
        _small(args[0] as List, args[1] as int);
    _functions['FREQUENCY'] = (List args) =>
        _frequency(args[0] as List, args[1] as List);
    _functions['SKEW'] = (List args) => _skewness(args);
    _functions['KURT'] = (List args) => _kurtosis(args);
    _functions['GEOMEAN'] = (List args) => _geometricMean(args);
    _functions['HARMEAN'] = (List args) => _harmonicMean(args);
    _functions['TRIMMEAN'] = (List args) =>
        _trimmedMean(args[0] as List, args[1] as num);
    _functions['CONFIDENCE'] = (List args) =>
        _confidence(args[0] as num, args[1] as num, args[2] as int);

    _supportedFunctions.addAll([
      'PERCENTILE',
      'QUARTILE',
      'RANK',
      'LARGE',
      'SMALL',
      'FREQUENCY',
      'SKEW',
      'KURT',
      'GEOMEAN',
      'HARMEAN',
      'TRIMMEAN',
      'CONFIDENCE',
    ]);
  }

  // Register advanced text functions
  static void _registerAdvancedTextFunctions() {
    _functions['TEXTJOIN'] = (List args) =>
        _textJoin(args[0] as String, args[1] as bool, args.sublist(2));
    _functions['CONCAT'] = (List args) => args.join('');
    _functions['EXACT'] = (List args) =>
        (args[0] as String) == (args[1] as String);
    _functions['REPT'] = (List args) => (args[0] as String) * (args[1] as int);
    _functions['REVERSE'] = (List args) =>
        (args[0] as String).split('').reversed.join('');
    _functions['CLEAN'] = (List args) => _cleanText(args[0] as String);
    _functions['CODE'] = (List args) => (args[0] as String).codeUnitAt(0);
    _functions['CHAR'] = (List args) => String.fromCharCode(args[0] as int);
    _functions['DOLLAR'] = (List args) =>
        _formatCurrency(args[0] as num, args.length > 1 ? args[1] as int : 2);
    _functions['FIXED'] = (List args) =>
        _formatNumber(args[0] as num, args.length > 1 ? args[1] as int : 2);
    _functions['VALUE'] = (List args) =>
        double.tryParse(args[0] as String) ?? 0;
    _functions['T'] = (List args) => args[0] is String ? args[0] : '';

    _supportedFunctions.addAll([
      'TEXTJOIN',
      'CONCAT',
      'EXACT',
      'REPT',
      'REVERSE',
      'CLEAN',
      'CODE',
      'CHAR',
      'DOLLAR',
      'FIXED',
      'VALUE',
      'T',
    ]);
  }

  // Register advanced logical functions
  static void _registerAdvancedLogicalFunctions() {
    _functions['IFS'] = (List args) => _ifs(args);
    _functions['SWITCH'] = (List args) => _switch(args);
    _functions['IFERROR'] = (List args) => _ifError(args[0], args[1]);
    _functions['IFNA'] = (List args) => _ifNA(args[0], args[1]);
    _functions['XOR'] = (List args) => _xor(args.cast<bool>());
    _functions['CHOOSE'] = (List args) =>
        _choose(args[0] as int, args.sublist(1));

    _supportedFunctions.addAll([
      'IFS',
      'SWITCH',
      'IFERROR',
      'IFNA',
      'XOR',
      'CHOOSE',
    ]);
  }

  // Function help data
  static const Map<String, FunctionHelp> _functionHelpData = {
    'SUM': FunctionHelp(
      name: 'SUM',
      description: 'Adds all the numbers in a range of cells',
      syntax: 'SUM(number1, [number2], ...)',
      example: 'SUM(A1:A10)',
    ),
    'AVERAGE': FunctionHelp(
      name: 'AVERAGE',
      description: 'Returns the average of its arguments',
      syntax: 'AVERAGE(number1, [number2], ...)',
      example: 'AVERAGE(A1:A10)',
    ),
    'IF': FunctionHelp(
      name: 'IF',
      description: 'Specifies a logical test to perform',
      syntax: 'IF(logical_test, value_if_true, value_if_false)',
      example: 'IF(A1>10, "High", "Low")',
    ),
    'VLOOKUP': FunctionHelp(
      name: 'VLOOKUP',
      description:
          'Looks up a value in the first column of a table and returns a value in the same row from another column',
      syntax:
          'VLOOKUP(lookup_value, table_array, col_index_num, [range_lookup])',
      example: 'VLOOKUP(A1, B1:D10, 3, FALSE)',
    ),
    'SUMIF': FunctionHelp(
      name: 'SUMIF',
      description: 'Adds the cells specified by a given criteria',
      syntax: 'SUMIF(range, criteria, [sum_range])',
      example: 'SUMIF(A1:A10, ">5", B1:B10)',
    ),
    // Add more function help as needed
  };

  // Helper functions for advanced operations
  static double _convert(num value, String fromUnit, String toUnit) {
    // Simplified conversion - in real implementation, would have comprehensive unit conversion
    final conversions = {
      'ft_m': 0.3048,
      'm_ft': 3.28084,
      'in_cm': 2.54,
      'cm_in': 0.393701,
      'lb_kg': 0.453592,
      'kg_lb': 2.20462,
    };

    final key = '${fromUnit}_$toUnit';
    return conversions.containsKey(key)
        ? value * conversions[key]!
        : value.toDouble();
  }

  static int _factorial(int n) {
    if (n <= 1) return 1;
    return n * _factorial(n - 1);
  }

  static int _doubleFactorial(int n) {
    if (n <= 1) return 1;
    return n * _doubleFactorial(n - 2);
  }

  static int _combination(int n, int r) {
    return _factorial(n) ~/ (_factorial(r) * _factorial(n - r));
  }

  static int _permutation(int n, int r) {
    return _factorial(n) ~/ _factorial(n - r);
  }

  static int _gcd(List<int> numbers) {
    return numbers.reduce((a, b) => _gcdTwo(a, b));
  }

  static int _gcdTwo(int a, int b) {
    while (b != 0) {
      final temp = b;
      b = a % b;
      a = temp;
    }
    return a;
  }

  static int _lcm(List<int> numbers) {
    return numbers.reduce((a, b) => (a * b) ~/ _gcdTwo(a, b));
  }

  static double _mround(num number, num multiple) {
    return ((number / multiple).round() * multiple).toDouble();
  }

  static num _sumProduct(List array1, List array2) {
    var sum = 0.0;
    final length = math.min(array1.length, array2.length);
    for (int i = 0; i < length; i++) {
      if (array1[i] is num && array2[i] is num) {
        sum += (array1[i] as num) * (array2[i] as num);
      }
    }
    return sum;
  }

  static num _sumSquares(List args) {
    return args.whereType<num>().fold<num>(
      0,
      (sum, value) => sum + value * value,
    );
  }

  static num _sumIf(List range, dynamic criteria, List? sumRange) {
    final actualSumRange = sumRange ?? range;
    var sum = 0.0;

    for (int i = 0; i < range.length && i < actualSumRange.length; i++) {
      if (_meetsCriteria(range[i], criteria)) {
        if (actualSumRange[i] is num) {
          sum += (actualSumRange[i] as num);
        }
      }
    }
    return sum;
  }

  static int _countIf(List range, dynamic criteria) {
    return range.where((value) => _meetsCriteria(value, criteria)).length;
  }

  static double _averageIf(List range, dynamic criteria, List? averageRange) {
    final actualAverageRange = averageRange ?? range;
    var sum = 0.0;
    var count = 0;

    for (int i = 0; i < range.length && i < actualAverageRange.length; i++) {
      if (_meetsCriteria(range[i], criteria)) {
        if (actualAverageRange[i] is num) {
          sum += (actualAverageRange[i] as num);
          count++;
        }
      }
    }
    return count > 0 ? sum / count : 0;
  }

  static bool _meetsCriteria(dynamic value, dynamic criteria) {
    if (criteria is String) {
      if (criteria.startsWith('>')) {
        final threshold = double.tryParse(criteria.substring(1));
        return threshold != null && value is num && value > threshold;
      } else if (criteria.startsWith('<')) {
        final threshold = double.tryParse(criteria.substring(1));
        return threshold != null && value is num && value < threshold;
      } else if (criteria.startsWith('=')) {
        return value.toString() == criteria.substring(1);
      }
      return value.toString() == criteria;
    }
    return value == criteria;
  }

  // Database function helpers
  static num _dsum(List<List> database, String field, Map criteria) {
    final fieldIndex = _getFieldIndex(database, field);
    if (fieldIndex == -1) return 0;

    var sum = 0.0;
    for (int i = 1; i < database.length; i++) {
      if (_matchesCriteria(database[i], database[0], criteria)) {
        final value = database[i][fieldIndex];
        if (value is num) sum += value;
      }
    }
    return sum;
  }

  static int _dcount(List<List> database, String field, Map criteria) {
    final fieldIndex = _getFieldIndex(database, field);
    if (fieldIndex == -1) return 0;

    var count = 0;
    for (int i = 1; i < database.length; i++) {
      if (_matchesCriteria(database[i], database[0], criteria)) {
        final value = database[i][fieldIndex];
        if (value is num) count++;
      }
    }
    return count;
  }

  static double _daverage(List<List> database, String field, Map criteria) {
    final sum = _dsum(database, field, criteria);
    final count = _dcount(database, field, criteria);
    return count > 0 ? sum / count : 0;
  }

  static num _dmax(List<List> database, String field, Map criteria) {
    final fieldIndex = _getFieldIndex(database, field);
    if (fieldIndex == -1) return 0;

    num? max;
    for (int i = 1; i < database.length; i++) {
      if (_matchesCriteria(database[i], database[0], criteria)) {
        final value = database[i][fieldIndex];
        if (value is num) {
          max = max == null ? value : math.max(max, value);
        }
      }
    }
    return max ?? 0;
  }

  static num _dmin(List<List> database, String field, Map criteria) {
    final fieldIndex = _getFieldIndex(database, field);
    if (fieldIndex == -1) return 0;

    num? min;
    for (int i = 1; i < database.length; i++) {
      if (_matchesCriteria(database[i], database[0], criteria)) {
        final value = database[i][fieldIndex];
        if (value is num) {
          min = min == null ? value : math.min(min, value);
        }
      }
    }
    return min ?? 0;
  }

  static dynamic _dget(List<List> database, String field, Map criteria) {
    final fieldIndex = _getFieldIndex(database, field);
    if (fieldIndex == -1) return '#N/A';

    for (int i = 1; i < database.length; i++) {
      if (_matchesCriteria(database[i], database[0], criteria)) {
        return database[i][fieldIndex];
      }
    }
    return '#N/A';
  }

  static int _getFieldIndex(List<List> database, String field) {
    if (database.isEmpty) return -1;
    final headers = database[0];
    for (int i = 0; i < headers.length; i++) {
      if (headers[i].toString() == field) return i;
    }
    return -1;
  }

  static bool _matchesCriteria(List row, List headers, Map criteria) {
    for (final entry in criteria.entries) {
      final fieldIndex = headers.indexOf(entry.key);
      if (fieldIndex == -1) return false;
      if (!_meetsCriteria(row[fieldIndex], entry.value)) return false;
    }
    return true;
  }

  // Array function helpers
  static List<List> _transpose(List<List> matrix) {
    if (matrix.isEmpty) return [];
    final rows = matrix.length;
    final cols = matrix[0].length;
    final result = List.generate(
      cols,
      (i) => List.generate(rows, (j) => matrix[j][i]),
    );
    return result;
  }

  static List _sortArray(List array, bool ascending) {
    final sorted = List.from(array);
    sorted.sort((a, b) {
      if (a is num && b is num) {
        return ascending ? a.compareTo(b) : b.compareTo(a);
      }
      return ascending
          ? a.toString().compareTo(b.toString())
          : b.toString().compareTo(a.toString());
    });
    return sorted;
  }

  static List _filterArray(List array, List<bool> criteria) {
    final result = <dynamic>[];
    for (int i = 0; i < array.length && i < criteria.length; i++) {
      if (criteria[i]) result.add(array[i]);
    }
    return result;
  }

  static List _uniqueArray(List array) {
    return array.toSet().toList();
  }

  static List<List<int>> _sequence(int rows, int cols) {
    final result = <List<int>>[];
    var value = 1;
    for (int i = 0; i < rows; i++) {
      final row = <int>[];
      for (int j = 0; j < cols; j++) {
        row.add(value++);
      }
      result.add(row);
    }
    return result;
  }

  static List<List<double>> _randArray(int rows, int cols) {
    final result = <List<double>>[];
    final random = math.Random();
    for (int i = 0; i < rows; i++) {
      final row = <double>[];
      for (int j = 0; j < cols; j++) {
        row.add(random.nextDouble());
      }
      result.add(row);
    }
    return result;
  }

  // Statistical function helpers
  static double _percentile(List array, num k) {
    final numbers = array.whereType<num>().toList()..sort();
    if (numbers.isEmpty) return 0;

    final index = k * (numbers.length - 1);
    final lower = index.floor();
    final upper = index.ceil();

    if (lower == upper) {
      return numbers[lower].toDouble();
    } else {
      final weight = index - lower;
      return (numbers[lower] * (1 - weight) + numbers[upper] * weight)
          .toDouble();
    }
  }

  static double _quartile(List array, int quart) {
    switch (quart) {
      case 0:
        return _min(array).toDouble();
      case 1:
        return _percentile(array, 0.25);
      case 2:
        return _median(array);
      case 3:
        return _percentile(array, 0.75);
      case 4:
        return _max(array).toDouble();
      default:
        return 0;
    }
  }

  static int _rank(dynamic value, List array, bool descending) {
    final sorted = _sortArray(array, !descending);
    return sorted.indexOf(value) + 1;
  }

  static num _large(List array, int k) {
    final numbers = array.whereType<num>().toList()
      ..sort((a, b) => b.compareTo(a));
    return k <= numbers.length ? numbers[k - 1] : 0;
  }

  static num _small(List array, int k) {
    final numbers = array.whereType<num>().toList()..sort();
    return k <= numbers.length ? numbers[k - 1] : 0;
  }

  static List<int> _frequency(List data, List bins) {
    final result = List<int>.filled(bins.length + 1, 0);
    for (final value in data) {
      if (value is num) {
        var binIndex = bins.length;
        for (int i = 0; i < bins.length; i++) {
          if (value <= bins[i]) {
            binIndex = i;
            break;
          }
        }
        result[binIndex]++;
      }
    }
    return result;
  }

  static double _skewness(List args) {
    final numbers = args.whereType<num>().toList();
    if (numbers.length < 3) return 0;

    final mean = _average(numbers);
    final n = numbers.length;
    final m3 =
        numbers.map((x) => math.pow(x - mean, 3)).reduce((a, b) => a + b) / n;
    final m2 =
        numbers.map((x) => math.pow(x - mean, 2)).reduce((a, b) => a + b) / n;

    return m3 / math.pow(m2, 1.5);
  }

  static double _kurtosis(List args) {
    final numbers = args.whereType<num>().toList();
    if (numbers.length < 4) return 0;

    final mean = _average(numbers);
    final n = numbers.length;
    final m4 =
        numbers.map((x) => math.pow(x - mean, 4)).reduce((a, b) => a + b) / n;
    final m2 =
        numbers.map((x) => math.pow(x - mean, 2)).reduce((a, b) => a + b) / n;

    return m4 / (m2 * m2) - 3;
  }

  static double _geometricMean(List args) {
    final numbers = args.whereType<num>().where((x) => x > 0).toList();
    if (numbers.isEmpty) return 0;

    final product = numbers.reduce((a, b) => a * b);
    return math.pow(product, 1.0 / numbers.length).toDouble();
  }

  static double _harmonicMean(List args) {
    final numbers = args.whereType<num>().where((x) => x != 0).toList();
    if (numbers.isEmpty) return 0;

    final reciprocalSum = numbers.map((x) => 1 / x).reduce((a, b) => a + b);
    return numbers.length / reciprocalSum;
  }

  static double _trimmedMean(List array, num percent) {
    final numbers = array.whereType<num>().toList()..sort();
    if (numbers.isEmpty) return 0;

    final trimCount = (numbers.length * percent / 2).floor();
    final trimmed = numbers.sublist(trimCount, numbers.length - trimCount);
    return _average(trimmed);
  }

  static double _confidence(num alpha, num standardDev, int size) {
    // Simplified confidence interval calculation
    final zScore = 1.96; // For 95% confidence (alpha = 0.05)
    return zScore * standardDev / math.sqrt(size);
  }

  // Text function helpers
  static String _textJoin(String delimiter, bool ignoreEmpty, List values) {
    final filtered = ignoreEmpty
        ? values.where((v) => v != null && v.toString().isNotEmpty)
        : values;
    return filtered.map((v) => v.toString()).join(delimiter);
  }

  static String _cleanText(String text) {
    return text.replaceAll(RegExp(r'[^\x20-\x7E]'), '');
  }

  static String _formatCurrency(num value, int decimals) {
    return '\$${value.toStringAsFixed(decimals)}';
  }

  static String _formatNumber(num value, int decimals) {
    return value.toStringAsFixed(decimals);
  }

  // Logical function helpers
  static dynamic _ifs(List args) {
    for (int i = 0; i < args.length - 1; i += 2) {
      if (args[i] == true) return args[i + 1];
    }
    return args.length % 2 == 1 ? args.last : '#N/A';
  }

  static dynamic _switch(List args) {
    final expression = args[0];
    for (int i = 1; i < args.length - 1; i += 2) {
      if (expression == args[i]) return args[i + 1];
    }
    return args.length % 2 == 0 ? args.last : '#N/A';
  }

  static dynamic _ifError(dynamic value, dynamic valueIfError) {
    return value is String && value.startsWith('#') ? valueIfError : value;
  }

  static dynamic _ifNA(dynamic value, dynamic valueIfNA) {
    return value == '#N/A' ? valueIfNA : value;
  }

  static bool _xor(List<bool> args) {
    return args.where((x) => x).length % 2 == 1;
  }

  static dynamic _choose(int index, List values) {
    return index > 0 && index <= values.length ? values[index - 1] : '#VALUE!';
  }
}

// Data classes
class FormulaResult {
  final dynamic value;
  final bool isError;
  final String? error;
  final List<String> dependencies;
  final DateTime calculationTime;

  const FormulaResult({
    required this.value,
    required this.isError,
    this.error,
    required this.dependencies,
    required this.calculationTime,
  });
}

class FunctionHelp {
  final String name;
  final String description;
  final String syntax;
  final String example;

  const FunctionHelp({
    required this.name,
    required this.description,
    required this.syntax,
    required this.example,
  });
}

/// Formula validation result for testing
class FormulaValidationResult {
  final bool isValid;
  final String? error;

  const FormulaValidationResult({required this.isValid, this.error});
}
