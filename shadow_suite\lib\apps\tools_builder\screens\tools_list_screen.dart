import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/tool.dart';
import 'tool_editor_screen.dart';
import 'tool_runtime_screen.dart';

class ToolsListScreen extends ConsumerWidget {
  const ToolsListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Tools'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () => _createNewTool(context),
            icon: const Icon(Icons.add),
          ),
        ],
      ),
      body: _buildToolsList(context),
    );
  }

  Widget _buildToolsList(BuildContext context) {
    // Mock data for demonstration
    final tools = [
      {
        'name': 'Expense Tracker',
        'description': 'Track daily expenses with categories',
        'type': 'Financial',
        'lastUsed': '2 hours ago',
        'status': 'Published',
      },
      {
        'name': 'Grade Calculator',
        'description': 'Calculate GPA and course grades',
        'type': 'Education',
        'lastUsed': '1 day ago',
        'status': 'Draft',
      },
      {
        'name': 'Inventory Manager',
        'description': 'Manage product inventory and stock',
        'type': 'Business',
        'lastUsed': '3 days ago',
        'status': 'Published',
      },
      {
        'name': 'Time Tracker',
        'description': 'Track time spent on projects',
        'type': 'Productivity',
        'lastUsed': '1 week ago',
        'status': 'Draft',
      },
    ];

    if (tools.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: tools.length,
      itemBuilder: (context, index) {
        final tool = tools[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(Icons.build, color: Colors.orange),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            tool['name'] as String,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            tool['description'] as String,
                            style: TextStyle(color: Colors.grey[600]),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(
                          tool['status'] as String,
                        ).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        tool['status'] as String,
                        style: TextStyle(
                          color: _getStatusColor(tool['status'] as String),
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(Icons.category, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      tool['type'] as String,
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                    const SizedBox(width: 16),
                    Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      'Last used: ${tool['lastUsed']}',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    ElevatedButton.icon(
                      onPressed: () => _runTool(context, index),
                      icon: const Icon(Icons.play_arrow, size: 16),
                      label: const Text('Run'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        minimumSize: const Size(80, 32),
                      ),
                    ),
                    const SizedBox(width: 8),
                    OutlinedButton.icon(
                      onPressed: () => _editTool(context, index),
                      icon: const Icon(Icons.edit, size: 16),
                      label: const Text('Edit'),
                      style: OutlinedButton.styleFrom(
                        minimumSize: const Size(80, 32),
                      ),
                    ),
                    const Spacer(),
                    PopupMenuButton<String>(
                      onSelected: (value) =>
                          _handleToolAction(context, value, index),
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'duplicate',
                          child: Text('Duplicate'),
                        ),
                        const PopupMenuItem(
                          value: 'export',
                          child: Text('Export'),
                        ),
                        const PopupMenuItem(
                          value: 'share',
                          child: Text('Share'),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Text('Delete'),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.build, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No Tools Created Yet',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first tool to get started',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _createNewTool(context),
            icon: const Icon(Icons.add),
            label: const Text('Create Tool'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Published':
        return Colors.green;
      case 'Draft':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  void _createNewTool(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ToolEditorScreen()),
    );
  }

  void _runTool(BuildContext context, int index) {
    // Create a mock tool for demonstration
    final mockTool = Tool(
      id: 'tool_$index',
      name: 'Mock Tool $index',
      description: 'A demonstration tool',
      type: ToolType.calculator,
      category: ToolCategory.finance,
      creatorId: 'demo_user',
      components: [],
      spreadsheet: null,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ToolRuntimeScreen(tool: mockTool),
      ),
    );
  }

  void _editTool(BuildContext context, int index) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ToolEditorScreen()),
    );
  }

  void _handleToolAction(BuildContext context, String action, int index) {
    switch (action) {
      case 'duplicate':
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Tool duplicated')));
        break;
      case 'export':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Export functionality coming soon')),
        );
        break;
      case 'share':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Share functionality coming soon')),
        );
        break;
      case 'delete':
        _deleteTool(context, index);
        break;
    }
  }

  void _deleteTool(BuildContext context, int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Tool'),
        content: const Text(
          'Are you sure you want to delete this tool? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(const SnackBar(content: Text('Tool deleted')));
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
