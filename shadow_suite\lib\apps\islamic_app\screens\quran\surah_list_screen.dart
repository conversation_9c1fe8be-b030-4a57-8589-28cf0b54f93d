import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_theme.dart';
import '../../services/islamic_providers.dart';
import '../../models/surah.dart';

class SurahListScreen extends ConsumerStatefulWidget {
  const SurahListScreen({super.key});

  @override
  ConsumerState<SurahListScreen> createState() => _SurahListScreenState();
}

class _SurahListScreenState extends ConsumerState<SurahListScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final surahsAsync = ref.watch(surahsProvider);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Quran - Surahs'),
        backgroundColor: AppTheme.islamicAppColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.dashboard;
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.quranSearch;
            },
          ),
          IconButton(
            icon: const Icon(Icons.bookmark),
            onPressed: () {
              ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.bookmarks;
            },
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchSection(),
          Expanded(
            child: surahsAsync.when(
              data: (surahs) {
                final filteredSurahs = _filterSurahs(surahs);
                return _buildSurahList(context, ref, filteredSurahs);
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error, size: 64, color: Colors.red[300]),
                    const SizedBox(height: 16),
                    Text('Error loading surahs: $error'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => ref.refresh(surahsProvider),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Colors.grey, width: 0.2)),
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search surahs by name or number...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  List<Surah> _filterSurahs(List<Surah> surahs) {
    if (_searchQuery.isEmpty) return surahs;
    
    return surahs.where((surah) {
      final query = _searchQuery.toLowerCase();
      return surah.nameEnglish.toLowerCase().contains(query) ||
             surah.nameTransliteration.toLowerCase().contains(query) ||
             surah.nameArabic.contains(_searchQuery) ||
             surah.number.toString().contains(_searchQuery);
    }).toList();
  }

  Widget _buildSurahList(BuildContext context, WidgetRef ref, List<Surah> surahs) {
    if (surahs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No surahs found',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your search terms',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: surahs.length,
      itemBuilder: (context, index) {
        final surah = surahs[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: _buildSurahCard(context, ref, surah),
        );
      },
    );
  }

  Widget _buildSurahCard(BuildContext context, WidgetRef ref, Surah surah) {
    return Card(
      child: InkWell(
        onTap: () {
          ref.read(selectedSurahProvider.notifier).state = surah;
          ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.quranReading;
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Surah number
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: AppTheme.islamicAppColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: AppTheme.islamicAppColor.withValues(alpha: 0.3),
                    width: 2,
                  ),
                ),
                child: Center(
                  child: Text(
                    surah.number.toString(),
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.islamicAppColor,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // Surah info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            surah.nameEnglish,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Text(
                          surah.nameArabic,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontFamily: 'Arabic',
                            color: AppTheme.islamicAppColor,
                            fontWeight: FontWeight.bold,
                          ),
                          textDirection: TextDirection.rtl,
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      surah.nameTransliteration,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontStyle: FontStyle.italic,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: _getRevelationColor(surah.revelationType).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            surah.revelationType,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: _getRevelationColor(surah.revelationType),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Icon(
                          Icons.format_list_numbered,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${surah.verseCount} verses',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                Icons.chevron_right,
                color: Colors.grey[400],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getRevelationColor(String revelationType) {
    switch (revelationType.toLowerCase()) {
      case 'meccan':
        return Colors.orange;
      case 'medinan':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}
