import 'dart:async';
import '../../../core/services/notification_service.dart';

/// Money Manager specific notification service
/// Handles budget alerts, bill reminders, goal notifications, and financial alerts
class MoneyNotificationService {
  static final MoneyNotificationService _instance =
      MoneyNotificationService._internal();
  factory MoneyNotificationService() => _instance;
  MoneyNotificationService._internal();

  final NotificationService _notificationService = NotificationService();
  static const String _appId = 'money_manager';

  /// Initialize Money Manager notifications
  Future<void> initialize() async {
    await _notificationService.initialize();
  }

  /// Schedule budget alert notification
  Future<void> scheduleBudgetAlert({
    required Budget budget,
    required double currentSpending,
    required double threshold, // Percentage (e.g., 0.8 for 80%)
  }) async {
    final percentage = (currentSpending / budget.amount) * 100;
    final thresholdPercentage = threshold * 100;

    if (percentage >= thresholdPercentage) {
      await _notificationService.showNotification(
        appId: _appId,
        title: 'Budget Alert: ${budget.category}',
        body:
            'You\'ve spent ${percentage.toStringAsFixed(1)}% of your ${budget.category} budget (${budget.currency}${currentSpending.toStringAsFixed(2)} of ${budget.currency}${budget.amount.toStringAsFixed(2)})',
        type: NotificationType.alert,
        payload: 'budget_alert:${budget.id}',
      );
    }
  }

  /// Schedule bill reminder notification
  Future<void> scheduleBillReminder({
    required Bill bill,
    int daysBefore = 3,
  }) async {
    final reminderDate = bill.dueDate.subtract(Duration(days: daysBefore));

    if (reminderDate.isAfter(DateTime.now())) {
      await _notificationService.scheduleNotification(
        appId: _appId,
        title: 'Bill Reminder: ${bill.name}',
        body:
            'Your ${bill.name} bill of ${bill.currency}${bill.amount.toStringAsFixed(2)} is due on ${_formatDate(bill.dueDate)}',
        scheduledTime: reminderDate,
        type: NotificationType.reminder,
        payload: 'bill_reminder:${bill.id}',
      );
    }
  }

  /// Schedule recurring bill reminders
  Future<void> scheduleRecurringBillReminders({
    required Bill bill,
    int daysBefore = 3,
    int occurrences = 12, // Number of future reminders
  }) async {
    if (bill.isRecurring && bill.recurringInterval != null) {
      DateTime nextDueDate = bill.dueDate;

      for (int i = 0; i < occurrences; i++) {
        final reminderDate = nextDueDate.subtract(Duration(days: daysBefore));

        if (reminderDate.isAfter(DateTime.now())) {
          await _notificationService.scheduleNotification(
            appId: _appId,
            title: 'Recurring Bill: ${bill.name}',
            body:
                'Your recurring ${bill.name} bill of ${bill.currency}${bill.amount.toStringAsFixed(2)} is due on ${_formatDate(nextDueDate)}',
            scheduledTime: reminderDate,
            type: NotificationType.reminder,
            payload: 'recurring_bill:${bill.id}:$i',
          );
        }

        // Calculate next due date based on interval
        switch (bill.recurringInterval!) {
          case RecurringInterval.weekly:
            nextDueDate = nextDueDate.add(const Duration(days: 7));
            break;
          case RecurringInterval.monthly:
            nextDueDate = DateTime(
              nextDueDate.year,
              nextDueDate.month + 1,
              nextDueDate.day,
            );
            break;
          case RecurringInterval.quarterly:
            nextDueDate = DateTime(
              nextDueDate.year,
              nextDueDate.month + 3,
              nextDueDate.day,
            );
            break;
          case RecurringInterval.yearly:
            nextDueDate = DateTime(
              nextDueDate.year + 1,
              nextDueDate.month,
              nextDueDate.day,
            );
            break;
        }
      }
    }
  }

  /// Schedule goal achievement notification
  Future<void> scheduleGoalNotification({
    required FinancialGoal goal,
    required double currentAmount,
  }) async {
    final percentage = (currentAmount / goal.targetAmount) * 100;

    // Notify at milestones: 25%, 50%, 75%, 90%, 100%
    final milestones = [25.0, 50.0, 75.0, 90.0, 100.0];

    for (final milestone in milestones) {
      if (percentage >= milestone &&
          !goal.notifiedMilestones.contains(milestone)) {
        String title;
        String body;

        if (milestone == 100.0) {
          title = 'Goal Achieved! 🎉';
          body =
              'Congratulations! You\'ve reached your goal "${goal.name}" of ${goal.currency}${goal.targetAmount.toStringAsFixed(2)}';
        } else {
          title = 'Goal Progress: ${milestone.toInt()}%';
          body =
              'You\'re ${milestone.toInt()}% towards your goal "${goal.name}" (${goal.currency}${currentAmount.toStringAsFixed(2)} of ${goal.currency}${goal.targetAmount.toStringAsFixed(2)})';
        }

        await _notificationService.showNotification(
          appId: _appId,
          title: title,
          body: body,
          type: NotificationType.general,
          payload: 'goal_progress:${goal.id}:$milestone',
        );

        // Mark milestone as notified
        goal.notifiedMilestones.add(milestone);
      }
    }
  }

  /// Schedule low balance alert
  Future<void> scheduleLowBalanceAlert({
    required Account account,
    double threshold = 100.0,
  }) async {
    if (account.balance <= threshold) {
      await _notificationService.showNotification(
        appId: _appId,
        title: 'Low Balance Alert',
        body:
            'Your ${account.name} account balance is low: ${account.currency}${account.balance.toStringAsFixed(2)}',
        type: NotificationType.alert,
        payload: 'low_balance:${account.id}',
      );
    }
  }

  /// Schedule monthly spending summary
  Future<void> scheduleMonthlySpendingSummary({
    required double totalSpent,
    required double budgetTotal,
    required Map<String, double> categorySpending,
  }) async {
    final now = DateTime.now();
    final lastDayOfMonth = DateTime(now.year, now.month + 1, 0);
    final summaryDate = DateTime(
      lastDayOfMonth.year,
      lastDayOfMonth.month,
      lastDayOfMonth.day,
      20,
      0,
    ); // 8 PM on last day

    if (summaryDate.isAfter(DateTime.now())) {
      final percentage = (totalSpent / budgetTotal) * 100;
      final topCategory = categorySpending.entries.reduce(
        (a, b) => a.value > b.value ? a : b,
      );

      await _notificationService.scheduleNotification(
        appId: _appId,
        title: 'Monthly Spending Summary',
        body:
            'This month you spent \$${totalSpent.toStringAsFixed(2)} (${percentage.toStringAsFixed(1)}% of budget). Top category: ${topCategory.key} (\$${topCategory.value.toStringAsFixed(2)})',
        scheduledTime: summaryDate,
        type: NotificationType.general,
        payload: 'monthly_summary:${now.year}:${now.month}',
      );
    }
  }

  /// Schedule investment performance notification
  Future<void> scheduleInvestmentNotification({
    required Investment investment,
    required double currentValue,
    required double changePercentage,
  }) async {
    // Notify for significant changes (>5% gain or >3% loss)
    if (changePercentage >= 5.0 || changePercentage <= -3.0) {
      final isGain = changePercentage > 0;
      final title = isGain ? 'Investment Gain 📈' : 'Investment Alert 📉';
      final emoji = isGain ? '🟢' : '🔴';

      await _notificationService.showNotification(
        appId: _appId,
        title: title,
        body:
            '$emoji ${investment.name}: ${changePercentage > 0 ? '+' : ''}${changePercentage.toStringAsFixed(2)}% (${investment.currency}${currentValue.toStringAsFixed(2)})',
        type: isGain ? NotificationType.general : NotificationType.alert,
        payload: 'investment:${investment.id}:$changePercentage',
      );
    }
  }

  /// Schedule debt payoff reminder
  Future<void> scheduleDebtPayoffReminder({
    required Debt debt,
    int daysBefore = 5,
  }) async {
    if (debt.nextPaymentDate != null) {
      final reminderDate = debt.nextPaymentDate!.subtract(
        Duration(days: daysBefore),
      );

      if (reminderDate.isAfter(DateTime.now())) {
        await _notificationService.scheduleNotification(
          appId: _appId,
          title: 'Debt Payment Reminder',
          body:
              'Your ${debt.name} payment of ${debt.currency}${debt.minimumPayment.toStringAsFixed(2)} is due on ${_formatDate(debt.nextPaymentDate!)}',
          scheduledTime: reminderDate,
          type: NotificationType.reminder,
          payload: 'debt_payment:${debt.id}',
        );
      }
    }
  }

  /// Schedule expense category limit notification
  Future<void> scheduleExpenseCategoryLimit({
    required String category,
    required double spent,
    required double limit,
  }) async {
    final percentage = (spent / limit) * 100;

    if (percentage >= 90.0) {
      await _notificationService.showNotification(
        appId: _appId,
        title: 'Category Limit Alert',
        body:
            'You\'ve spent ${percentage.toStringAsFixed(1)}% of your $category limit (\$${spent.toStringAsFixed(2)} of \$${limit.toStringAsFixed(2)})',
        type: NotificationType.alert,
        payload: 'category_limit:$category',
      );
    }
  }

  /// Schedule weekly financial review reminder
  Future<void> scheduleWeeklyReviewReminder() async {
    final now = DateTime.now();
    final nextSunday = now.add(Duration(days: 7 - now.weekday));
    final reviewTime = DateTime(
      nextSunday.year,
      nextSunday.month,
      nextSunday.day,
      19,
      0,
    ); // 7 PM Sunday

    await _notificationService.scheduleNotification(
      appId: _appId,
      title: 'Weekly Financial Review',
      body: 'Time to review your weekly spending and update your budgets',
      scheduledTime: reviewTime,
      type: NotificationType.reminder,
      recurring: true,
      recurringInterval: const Duration(days: 7),
      payload: 'weekly_review',
    );
  }

  /// Cancel all Money Manager notifications
  Future<void> cancelAllNotifications() async {
    await _notificationService.cancelAllNotifications(_appId);
  }

  /// Cancel specific notification by type and ID
  Future<void> cancelNotification(String type, String id) async {
    final notifications = _notificationService.getScheduledNotifications(
      _appId,
    );
    final notification = notifications.firstWhere(
      (n) => n.payload == '$type:$id',
      orElse: () => throw StateError('Notification not found'),
    );

    if (notification.id.isNotEmpty) {
      await _notificationService.cancelNotification(notification.id);
    }
  }

  /// Get Money Manager notification settings
  NotificationSettings getNotificationSettings() {
    return _notificationService.getNotificationSettings(_appId);
  }

  /// Update Money Manager notification settings
  void updateNotificationSettings(NotificationSettings settings) {
    _notificationService.updateNotificationSettings(_appId, settings);
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Money Manager specific models for notifications
enum RecurringInterval { weekly, monthly, quarterly, yearly }

class Budget {
  final String id;
  final String category;
  final double amount;
  final String currency;
  final DateTime startDate;
  final DateTime endDate;

  const Budget({
    required this.id,
    required this.category,
    required this.amount,
    required this.currency,
    required this.startDate,
    required this.endDate,
  });
}

class Bill {
  final String id;
  final String name;
  final double amount;
  final String currency;
  final DateTime dueDate;
  final bool isRecurring;
  final RecurringInterval? recurringInterval;

  const Bill({
    required this.id,
    required this.name,
    required this.amount,
    required this.currency,
    required this.dueDate,
    required this.isRecurring,
    this.recurringInterval,
  });
}

class FinancialGoal {
  final String id;
  final String name;
  final double targetAmount;
  final String currency;
  final DateTime targetDate;
  final List<double> notifiedMilestones;

  FinancialGoal({
    required this.id,
    required this.name,
    required this.targetAmount,
    required this.currency,
    required this.targetDate,
    List<double>? notifiedMilestones,
  }) : notifiedMilestones = notifiedMilestones ?? [];
}

class Account {
  final String id;
  final String name;
  final double balance;
  final String currency;
  final String type;

  const Account({
    required this.id,
    required this.name,
    required this.balance,
    required this.currency,
    required this.type,
  });
}

class Investment {
  final String id;
  final String name;
  final double initialValue;
  final String currency;
  final DateTime purchaseDate;

  const Investment({
    required this.id,
    required this.name,
    required this.initialValue,
    required this.currency,
    required this.purchaseDate,
  });
}

class Debt {
  final String id;
  final String name;
  final double totalAmount;
  final double minimumPayment;
  final String currency;
  final DateTime? nextPaymentDate;

  const Debt({
    required this.id,
    required this.name,
    required this.totalAmount,
    required this.minimumPayment,
    required this.currency,
    this.nextPaymentDate,
  });
}
