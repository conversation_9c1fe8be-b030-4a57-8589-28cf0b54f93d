import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/shadow_player_providers.dart';

class MusicFilterPanel extends ConsumerStatefulWidget {
  final ScrollController scrollController;

  const MusicFilterPanel({super.key, required this.scrollController});

  @override
  ConsumerState<MusicFilterPanel> createState() => _MusicFilterPanelState();
}

class _MusicFilterPanelState extends ConsumerState<MusicFilterPanel> {
  @override
  Widget build(BuildContext context) {
    final filter = ref.watch(musicFilterProvider);
    final supportedFormats = ref.watch(supportedAudioFormatsProvider);
    final artists = ref.watch(artistsProvider);
    final albums = ref.watch(albumsProvider);
    final genres = ref.watch(genresProvider);

    return Container(
      decoration: const BoxDecoration(
        color: Color(0xFF2C3E50),
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        children: [
          // Handle Bar
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: const Color(0xFF7F8C8D),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(24),
            child: Row(
              children: [
                const Text(
                  'Filter Music',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _clearFilters,
                  child: const Text(
                    'Clear All',
                    style: TextStyle(color: Color(0xFFE74C3C), fontSize: 14),
                  ),
                ),
              ],
            ),
          ),

          // Filter Content
          Expanded(
            child: ListView(
              controller: widget.scrollController,
              padding: const EdgeInsets.symmetric(horizontal: 24),
              children: [
                // File Formats
                _buildFilterSection(
                  title: 'Audio Formats',
                  child: Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: supportedFormats.map((format) {
                      final isSelected = filter.formats.contains(format);
                      return FilterChip(
                        label: Text(format.toUpperCase()),
                        selected: isSelected,
                        onSelected: (selected) {
                          _toggleFormat(format, selected);
                        },
                        backgroundColor: const Color(0xFF34495E),
                        selectedColor: const Color(0xFF3498DB),
                        labelStyle: TextStyle(
                          color: isSelected
                              ? Colors.white
                              : const Color(0xFFBDC3C7),
                          fontSize: 12,
                        ),
                      );
                    }).toList(),
                  ),
                ),

                // Artists
                if (artists.isNotEmpty)
                  _buildFilterSection(
                    title: 'Artists',
                    child: Container(
                      constraints: const BoxConstraints(maxHeight: 200),
                      child: SingleChildScrollView(
                        child: Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: artists.take(20).map((artist) {
                            final isSelected = filter.artists.contains(artist);
                            return FilterChip(
                              label: Text(
                                artist,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              selected: isSelected,
                              onSelected: (selected) {
                                _toggleArtist(artist, selected);
                              },
                              backgroundColor: const Color(0xFF34495E),
                              selectedColor: const Color(0xFF27AE60),
                              labelStyle: TextStyle(
                                color: isSelected
                                    ? Colors.white
                                    : const Color(0xFFBDC3C7),
                                fontSize: 11,
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                  ),

                // Albums
                if (albums.isNotEmpty)
                  _buildFilterSection(
                    title: 'Albums',
                    child: Container(
                      constraints: const BoxConstraints(maxHeight: 200),
                      child: SingleChildScrollView(
                        child: Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: albums.take(20).map((album) {
                            final isSelected = filter.albums.contains(album);
                            return FilterChip(
                              label: Text(
                                album,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              selected: isSelected,
                              onSelected: (selected) {
                                _toggleAlbum(album, selected);
                              },
                              backgroundColor: const Color(0xFF34495E),
                              selectedColor: const Color(0xFF9B59B6),
                              labelStyle: TextStyle(
                                color: isSelected
                                    ? Colors.white
                                    : const Color(0xFFBDC3C7),
                                fontSize: 11,
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                  ),

                // Genres
                if (genres.isNotEmpty)
                  _buildFilterSection(
                    title: 'Genres',
                    child: Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: genres.map((genre) {
                        final isSelected = filter.genres.contains(genre);
                        return FilterChip(
                          label: Text(genre),
                          selected: isSelected,
                          onSelected: (selected) {
                            _toggleGenre(genre, selected);
                          },
                          backgroundColor: const Color(0xFF34495E),
                          selectedColor: const Color(0xFFE67E22),
                          labelStyle: TextStyle(
                            color: isSelected
                                ? Colors.white
                                : const Color(0xFFBDC3C7),
                            fontSize: 12,
                          ),
                        );
                      }).toList(),
                    ),
                  ),

                // Year Range
                _buildFilterSection(
                  title: 'Release Year',
                  child: Column(
                    children: [
                      _buildYearSlider(
                        'From Year',
                        filter.minYear,
                        (year) => _updateFilter(filter.copyWith(minYear: year)),
                      ),
                      const SizedBox(height: 16),
                      _buildYearSlider(
                        'To Year',
                        filter.maxYear,
                        (year) => _updateFilter(filter.copyWith(maxYear: year)),
                      ),
                    ],
                  ),
                ),

                // Duration Range
                _buildFilterSection(
                  title: 'Duration',
                  child: Column(
                    children: [
                      _buildDurationSlider(
                        'Minimum Duration',
                        filter.minDuration,
                        (duration) => _updateFilter(
                          filter.copyWith(minDuration: duration),
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildDurationSlider(
                        'Maximum Duration',
                        filter.maxDuration,
                        (duration) => _updateFilter(
                          filter.copyWith(maxDuration: duration),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),
              ],
            ),
          ),

          // Apply Button
          Container(
            padding: const EdgeInsets.all(24),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF3498DB),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Apply Filters',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection({required String title, required Widget child}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          child,
        ],
      ),
    );
  }

  Widget _buildYearSlider(
    String label,
    int? value,
    ValueChanged<int?> onChanged,
  ) {
    final year = value ?? DateTime.now().year;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: const TextStyle(color: Color(0xFFBDC3C7), fontSize: 14),
            ),
            const Spacer(),
            Text(
              value != null ? year.toString() : 'Any',
              style: const TextStyle(
                color: Color(0xFF3498DB),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Slider(
          value: year.toDouble(),
          min: 1950,
          max: DateTime.now().year.toDouble(),
          divisions: DateTime.now().year - 1950,
          activeColor: const Color(0xFF3498DB),
          inactiveColor: const Color(0xFF34495E),
          onChanged: (newValue) {
            if (newValue == 1950) {
              onChanged(null);
            } else {
              onChanged(newValue.round());
            }
          },
        ),
      ],
    );
  }

  Widget _buildDurationSlider(
    String label,
    Duration? value,
    ValueChanged<Duration?> onChanged,
  ) {
    final minutes = value?.inMinutes ?? 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: const TextStyle(color: Color(0xFFBDC3C7), fontSize: 14),
            ),
            const Spacer(),
            Text(
              value != null ? _formatDuration(value) : 'Any',
              style: const TextStyle(
                color: Color(0xFF3498DB),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Slider(
          value: minutes.toDouble(),
          min: 0,
          max: 15, // 15 minutes max
          divisions: 30,
          activeColor: const Color(0xFF3498DB),
          inactiveColor: const Color(0xFF34495E),
          onChanged: (newValue) {
            if (newValue == 0) {
              onChanged(null);
            } else {
              onChanged(Duration(minutes: newValue.round()));
            }
          },
        ),
      ],
    );
  }

  void _toggleFormat(String format, bool selected) {
    final filter = ref.read(musicFilterProvider);
    final formats = List<String>.from(filter.formats);

    if (selected) {
      formats.add(format);
    } else {
      formats.remove(format);
    }

    _updateFilter(filter.copyWith(formats: formats));
  }

  void _toggleArtist(String artist, bool selected) {
    final filter = ref.read(musicFilterProvider);
    final artists = List<String>.from(filter.artists);

    if (selected) {
      artists.add(artist);
    } else {
      artists.remove(artist);
    }

    _updateFilter(filter.copyWith(artists: artists));
  }

  void _toggleAlbum(String album, bool selected) {
    final filter = ref.read(musicFilterProvider);
    final albums = List<String>.from(filter.albums);

    if (selected) {
      albums.add(album);
    } else {
      albums.remove(album);
    }

    _updateFilter(filter.copyWith(albums: albums));
  }

  void _toggleGenre(String genre, bool selected) {
    final filter = ref.read(musicFilterProvider);
    final genres = List<String>.from(filter.genres);

    if (selected) {
      genres.add(genre);
    } else {
      genres.remove(genre);
    }

    _updateFilter(filter.copyWith(genres: genres));
  }

  void _updateFilter(MusicFilter newFilter) {
    ref.read(musicFilterProvider.notifier).state = newFilter;
  }

  void _clearFilters() {
    ref.read(musicFilterProvider.notifier).state = const MusicFilter();
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }
}

// Extension for MusicFilter copyWith
extension MusicFilterExtension on MusicFilter {
  MusicFilter copyWith({
    List<String>? formats,
    List<String>? artists,
    List<String>? albums,
    List<String>? genres,
    int? minYear,
    int? maxYear,
    Duration? minDuration,
    Duration? maxDuration,
  }) {
    return MusicFilter(
      formats: formats ?? this.formats,
      artists: artists ?? this.artists,
      albums: albums ?? this.albums,
      genres: genres ?? this.genres,
      minYear: minYear ?? this.minYear,
      maxYear: maxYear ?? this.maxYear,
      minDuration: minDuration ?? this.minDuration,
      maxDuration: maxDuration ?? this.maxDuration,
    );
  }
}
