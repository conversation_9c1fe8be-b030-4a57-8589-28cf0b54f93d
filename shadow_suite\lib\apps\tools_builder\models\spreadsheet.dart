import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'spreadsheet_cell.dart';

/// Main spreadsheet model for tools builder
class Spreadsheet {
  final String id;
  final String name;
  final String? description;
  final List<SpreadsheetSheet> sheets;
  final int activeSheetIndex;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final DateTime lastModified;
  final String? filePath;
  final bool isReadOnly;

  Spreadsheet({
    String? id,
    required this.name,
    this.description,
    this.sheets = const [],
    this.activeSheetIndex = 0,
    this.metadata = const {},
    DateTime? createdAt,
    DateTime? lastModified,
    this.filePath,
    this.isReadOnly = false,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       lastModified = lastModified ?? DateTime.now();

  SpreadsheetSheet? get activeSheet {
    if (activeSheetIndex >= 0 && activeSheetIndex < sheets.length) {
      return sheets[activeSheetIndex];
    }
    return null;
  }

  Spreadsheet copyWith({
    String? id,
    String? name,
    String? description,
    List<SpreadsheetSheet>? sheets,
    int? activeSheetIndex,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? lastModified,
    String? filePath,
    bool? isReadOnly,
  }) {
    return Spreadsheet(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      sheets: sheets ?? this.sheets,
      activeSheetIndex: activeSheetIndex ?? this.activeSheetIndex,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      lastModified: lastModified ?? this.lastModified,
      filePath: filePath ?? this.filePath,
      isReadOnly: isReadOnly ?? this.isReadOnly,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'sheets': sheets.map((sheet) => sheet.toMap()).toList(),
      'activeSheetIndex': activeSheetIndex,
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
      'lastModified': lastModified.toIso8601String(),
      'filePath': filePath,
      'isReadOnly': isReadOnly,
    };
  }

  factory Spreadsheet.fromMap(Map<String, dynamic> map) {
    return Spreadsheet(
      id: map['id'],
      name: map['name'] ?? '',
      description: map['description'],
      sheets: List<SpreadsheetSheet>.from(
        (map['sheets'] ?? []).map((sheet) => SpreadsheetSheet.fromMap(sheet)),
      ),
      activeSheetIndex: map['activeSheetIndex']?.toInt() ?? 0,
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
      createdAt: DateTime.parse(map['createdAt']),
      lastModified: DateTime.parse(map['lastModified']),
      filePath: map['filePath'],
      isReadOnly: map['isReadOnly'] ?? false,
    );
  }

  String toJson() => json.encode(toMap());
  factory Spreadsheet.fromJson(String source) =>
      Spreadsheet.fromMap(json.decode(source));
}

/// Individual sheet within a spreadsheet
class SpreadsheetSheet {
  final String id;
  final String name;
  final Map<String, SpreadsheetCell> cells;
  final int rows;
  final int columns;
  final bool isHidden;
  final bool isProtected;
  final Map<String, dynamic> metadata;
  final Map<int, double> columnWidths;
  final Map<int, double> rowHeights;
  final String? protectionPassword;
  final String? tabColor;
  final int index;
  final double defaultRowHeight;
  final double defaultColumnWidth;
  final Set<int> hiddenRows;
  final Set<int> hiddenColumns;
  final Map<String, dynamic> printSettings;

  SpreadsheetSheet({
    String? id,
    required this.name,
    this.cells = const {},
    this.rows = 1000,
    this.columns = 26,
    this.isHidden = false,
    this.isProtected = false,
    this.metadata = const {},
    this.columnWidths = const {},
    this.rowHeights = const {},
    this.protectionPassword,
    this.tabColor,
    this.index = 0,
    this.defaultRowHeight = 20.0,
    this.defaultColumnWidth = 100.0,
    this.hiddenRows = const {},
    this.hiddenColumns = const {},
    this.printSettings = const {},
  }) : id = id ?? const Uuid().v4();

  SpreadsheetCell? getCell(int row, int column) {
    final address = SpreadsheetCell.columnToLetter(column) + row.toString();
    return cells[address];
  }

  SpreadsheetSheet setCell(int row, int column, SpreadsheetCell cell) {
    final address = SpreadsheetCell.columnToLetter(column) + row.toString();
    final newCells = Map<String, SpreadsheetCell>.from(cells);
    newCells[address] = cell;
    return copyWith(cells: newCells);
  }

  SpreadsheetSheet copyWith({
    String? id,
    String? name,
    Map<String, SpreadsheetCell>? cells,
    int? rows,
    int? columns,
    bool? isHidden,
    bool? isProtected,
    Map<String, dynamic>? metadata,
    Map<int, double>? columnWidths,
    Map<int, double>? rowHeights,
    String? protectionPassword,
    String? tabColor,
    int? index,
    double? defaultRowHeight,
    double? defaultColumnWidth,
    Set<int>? hiddenRows,
    Set<int>? hiddenColumns,
    Map<String, dynamic>? printSettings,
  }) {
    return SpreadsheetSheet(
      id: id ?? this.id,
      name: name ?? this.name,
      cells: cells ?? this.cells,
      rows: rows ?? this.rows,
      columns: columns ?? this.columns,
      isHidden: isHidden ?? this.isHidden,
      isProtected: isProtected ?? this.isProtected,
      metadata: metadata ?? this.metadata,
      columnWidths: columnWidths ?? this.columnWidths,
      rowHeights: rowHeights ?? this.rowHeights,
      protectionPassword: protectionPassword ?? this.protectionPassword,
      tabColor: tabColor ?? this.tabColor,
      index: index ?? this.index,
      defaultRowHeight: defaultRowHeight ?? this.defaultRowHeight,
      defaultColumnWidth: defaultColumnWidth ?? this.defaultColumnWidth,
      hiddenRows: hiddenRows ?? this.hiddenRows,
      hiddenColumns: hiddenColumns ?? this.hiddenColumns,
      printSettings: printSettings ?? this.printSettings,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'cells': cells.map((key, value) => MapEntry(key, value.toMap())),
      'rows': rows,
      'columns': columns,
      'isHidden': isHidden,
      'isProtected': isProtected,
      'metadata': metadata,
      'columnWidths': columnWidths,
      'rowHeights': rowHeights,
      'protectionPassword': protectionPassword,
      'tabColor': tabColor,
      'index': index,
      'defaultRowHeight': defaultRowHeight,
      'defaultColumnWidth': defaultColumnWidth,
      'hiddenRows': hiddenRows.toList(),
      'hiddenColumns': hiddenColumns.toList(),
      'printSettings': printSettings,
    };
  }

  factory SpreadsheetSheet.fromMap(Map<String, dynamic> map) {
    return SpreadsheetSheet(
      id: map['id'],
      name: map['name'] ?? '',
      cells: Map<String, SpreadsheetCell>.from(
        (map['cells'] ?? {}).map(
          (key, value) => MapEntry(key, SpreadsheetCell.fromMap(value)),
        ),
      ),
      rows: map['rows']?.toInt() ?? 1000,
      columns: map['columns']?.toInt() ?? 26,
      isHidden: map['isHidden'] ?? false,
      isProtected: map['isProtected'] ?? false,
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
      columnWidths: Map<int, double>.from(map['columnWidths'] ?? {}),
      rowHeights: Map<int, double>.from(map['rowHeights'] ?? {}),
      protectionPassword: map['protectionPassword'],
      tabColor: map['tabColor'],
      index: map['index']?.toInt() ?? 0,
      defaultRowHeight: map['defaultRowHeight']?.toDouble() ?? 20.0,
      defaultColumnWidth: map['defaultColumnWidth']?.toDouble() ?? 100.0,
      hiddenRows: Set<int>.from(map['hiddenRows'] ?? []),
      hiddenColumns: Set<int>.from(map['hiddenColumns'] ?? []),
      printSettings: Map<String, dynamic>.from(map['printSettings'] ?? {}),
    );
  }

  String toJson() => json.encode(toMap());
  factory SpreadsheetSheet.fromJson(String source) =>
      SpreadsheetSheet.fromMap(json.decode(source));
}

/// Range selection model
class SpreadsheetRange {
  final int startRow;
  final int startColumn;
  final int endRow;
  final int endColumn;

  const SpreadsheetRange({
    required this.startRow,
    required this.startColumn,
    required this.endRow,
    required this.endColumn,
  });

  bool contains(int row, int column) {
    return row >= startRow &&
        row <= endRow &&
        column >= startColumn &&
        column <= endColumn;
  }

  List<String> getCellAddresses() {
    final addresses = <String>[];
    for (int row = startRow; row <= endRow; row++) {
      for (int col = startColumn; col <= endColumn; col++) {
        addresses.add(SpreadsheetCell.columnToLetter(col) + row.toString());
      }
    }
    return addresses;
  }

  String get rangeAddress {
    final startAddr =
        SpreadsheetCell.columnToLetter(startColumn) + startRow.toString();
    final endAddr =
        SpreadsheetCell.columnToLetter(endColumn) + endRow.toString();
    return '$startAddr:$endAddr';
  }
}

/// Spreadsheet calculation context
class CalculationContext {
  final Spreadsheet spreadsheet;
  final SpreadsheetSheet sheet;
  final Set<String> calculatingCells;

  const CalculationContext({
    required this.spreadsheet,
    required this.sheet,
    this.calculatingCells = const {},
  });

  CalculationContext copyWith({
    Spreadsheet? spreadsheet,
    SpreadsheetSheet? sheet,
    Set<String>? calculatingCells,
  }) {
    return CalculationContext(
      spreadsheet: spreadsheet ?? this.spreadsheet,
      sheet: sheet ?? this.sheet,
      calculatingCells: calculatingCells ?? this.calculatingCells,
    );
  }
}
