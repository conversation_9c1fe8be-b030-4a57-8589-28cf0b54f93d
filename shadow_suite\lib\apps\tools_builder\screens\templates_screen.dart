import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/tools_builder_providers.dart';

/// Templates Screen
class TemplatesScreen extends ConsumerWidget {
  const TemplatesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final templates = ref.watch(templatesProvider);
    final searchQuery = ref.watch(templateSearchQueryProvider);
    final selectedCategory = ref.watch(selectedTemplateCategoryProvider);

    final filteredTemplates = _filterTemplates(
      templates,
      searchQuery,
      selectedCategory,
    );
    final categories = ref.read(templatesProvider.notifier).getCategories();

    return Scaffold(
      body: Column(
        children: [
          _buildHeader(context, ref),
          _buildSearchAndFilter(ref, categories),
          Expanded(child: _buildTemplatesGrid(filteredTemplates, ref)),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Icon(Icons.description_outlined, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text(
            'App Templates',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _createCustomTemplate(ref),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.read(templatesProvider.notifier).loadTemplates();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilter(WidgetRef ref, List<String> categories) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          TextField(
            decoration: InputDecoration(
              hintText: 'Search templates...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: (query) {
              ref.read(templateSearchQueryProvider.notifier).state = query;
            },
          ),
          const SizedBox(height: 12),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                FilterChip(
                  label: const Text('All'),
                  selected: ref.watch(selectedTemplateCategoryProvider) == null,
                  onSelected: (_) {
                    ref.read(selectedTemplateCategoryProvider.notifier).state =
                        null;
                  },
                ),
                const SizedBox(width: 8),
                ...categories.map(
                  (category) => Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(category),
                      selected:
                          ref.watch(selectedTemplateCategoryProvider) ==
                          category,
                      onSelected: (_) {
                        ref
                                .read(selectedTemplateCategoryProvider.notifier)
                                .state =
                            category;
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplatesGrid(List<dynamic> templates, WidgetRef ref) {
    if (templates.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.description_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No templates found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: templates.length,
      itemBuilder: (context, index) {
        final template = templates[index];
        return _buildTemplateCard(template, ref);
      },
    );
  }

  Widget _buildTemplateCard(dynamic template, WidgetRef ref) {
    return Card(
      clipBehavior: Clip.antiAlias,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: _getCategoryColor(
                  template.category,
                ).withValues(alpha: 0.1),
              ),
              child: Icon(
                _getCategoryIcon(template.category),
                size: 48,
                color: _getCategoryColor(template.category),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    template.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    template.description,
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const Spacer(),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => _useTemplate(template, ref),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF1976D2),
                            foregroundColor: Colors.white,
                            minimumSize: const Size(0, 32),
                          ),
                          child: const Text('Use'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed: () => _previewTemplate(template),
                        icon: const Icon(Icons.preview),
                        iconSize: 20,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<dynamic> _filterTemplates(
    List<dynamic> templates,
    String searchQuery,
    String? selectedCategory,
  ) {
    return templates.where((template) {
      final matchesSearch =
          searchQuery.isEmpty ||
          template.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
          template.description.toLowerCase().contains(
            searchQuery.toLowerCase(),
          );

      final matchesCategory =
          selectedCategory == null || template.category == selectedCategory;

      return matchesSearch && matchesCategory;
    }).toList();
  }

  Color _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'business':
        return Colors.blue;
      case 'finance':
        return Colors.green;
      case 'education':
        return Colors.orange;
      case 'productivity':
        return Colors.purple;
      case 'health':
        return Colors.red;
      case 'entertainment':
        return Colors.pink;
      default:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'business':
        return Icons.business;
      case 'finance':
        return Icons.attach_money;
      case 'education':
        return Icons.school;
      case 'productivity':
        return Icons.work;
      case 'health':
        return Icons.health_and_safety;
      case 'entertainment':
        return Icons.sports_esports;
      default:
        return Icons.apps;
    }
  }

  void _useTemplate(dynamic template, WidgetRef ref) {
    showDialog(
      context: ref.context,
      builder: (context) => AlertDialog(
        title: Text('Use Template: ${template.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(template.description),
            const SizedBox(height: 16),
            const Text(
              'Features included:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            ...template.features.map(
              (feature) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  children: [
                    const Icon(Icons.check, color: Colors.green, size: 16),
                    const SizedBox(width: 8),
                    Expanded(child: Text(feature)),
                  ],
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _createAppFromTemplate(template, ref);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF1976D2),
              foregroundColor: Colors.white,
            ),
            child: const Text('Create App'),
          ),
        ],
      ),
    );
  }

  void _previewTemplate(dynamic template) {
    // Implementation for previewing template
  }

  void _createCustomTemplate(WidgetRef ref) {
    // Implementation for creating custom template
  }

  void _createAppFromTemplate(dynamic template, WidgetRef ref) {
    // Implementation for creating app from template
  }
}
