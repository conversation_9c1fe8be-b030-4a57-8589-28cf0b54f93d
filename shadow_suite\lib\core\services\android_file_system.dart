import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:io';

// Android File System Service Provider
final androidFileSystemProvider = Provider<AndroidFileSystem>((ref) {
  return AndroidFileSystem();
});

// Storage Info Provider
final storageInfoProvider =
    StateNotifierProvider<StorageInfoNotifier, StorageInfo>((ref) {
      return StorageInfoNotifier();
    });

// File Permissions Provider
final filePermissionsProvider =
    StateNotifierProvider<FilePermissionsNotifier, FilePermissions>((ref) {
      return FilePermissionsNotifier();
    });

// Android File System Service
class AndroidFileSystem {
  static const MethodChannel _channel = MethodChannel(
    'shadow_suite/file_system',
  );

  // Initialize file system
  Future<void> initialize() async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('initializeFileSystem');
    } catch (e) {
      debugPrint('Error initializing file system: $e');
    }
  }

  // Request storage permissions
  Future<bool> requestStoragePermissions() async {
    if (!Platform.isAndroid) return true;

    try {
      final bool granted = await _channel.invokeMethod(
        'requestStoragePermissions',
      );
      return granted;
    } catch (e) {
      debugPrint('Error requesting storage permissions: $e');
      return false;
    }
  }

  // Check storage permissions
  Future<FilePermissions> checkPermissions() async {
    if (!Platform.isAndroid) {
      return FilePermissions(
        hasReadExternalStorage: true,
        hasWriteExternalStorage: true,
        hasManageExternalStorage: true,
        hasAccessMediaLocation: true,
        canAccessAllFiles: true,
        lastChecked: DateTime.now(),
      );
    }

    try {
      final Map<String, dynamic> permissions = await _channel.invokeMethod(
        'checkPermissions',
      );
      return FilePermissions.fromMap(permissions);
    } catch (e) {
      debugPrint('Error checking permissions: $e');
      return FilePermissions(
        hasReadExternalStorage: false,
        hasWriteExternalStorage: false,
        hasManageExternalStorage: false,
        hasAccessMediaLocation: false,
        canAccessAllFiles: false,
        lastChecked: DateTime.now(),
      );
    }
  }

  // Get storage information
  Future<StorageInfo> getStorageInfo() async {
    if (!Platform.isAndroid) {
      return StorageInfo(
        internalStorageTotal: 1024 * 1024 * 1024 * 64, // 64GB
        internalStorageAvailable: 1024 * 1024 * 1024 * 32, // 32GB
        externalStorageTotal: 0,
        externalStorageAvailable: 0,
        appDataSize: 1024 * 1024 * 10, // 10MB
        cacheSize: 1024 * 1024 * 5, // 5MB
        hasExternalStorage: false,
        isExternalStorageRemovable: false,
        lastUpdated: DateTime.now(),
      );
    }

    try {
      final Map<String, dynamic> storageInfo = await _channel.invokeMethod(
        'getStorageInfo',
      );
      return StorageInfo.fromMap(storageInfo);
    } catch (e) {
      debugPrint('Error getting storage info: $e');
      return StorageInfo(
        internalStorageTotal: 0,
        internalStorageAvailable: 0,
        externalStorageTotal: 0,
        externalStorageAvailable: 0,
        appDataSize: 0,
        cacheSize: 0,
        hasExternalStorage: false,
        isExternalStorageRemovable: false,
        lastUpdated: DateTime.now(),
      );
    }
  }

  // Get app-specific directories
  Future<AndroidDirectories> getAppDirectories() async {
    if (!Platform.isAndroid) {
      return AndroidDirectories(
        internalAppDir: '/data/data/com.shadowsuite.app',
        externalAppDir: '/storage/emulated/0/Android/data/com.shadowsuite.app',
        cacheDir: '/data/data/com.shadowsuite.app/cache',
        filesDir: '/data/data/com.shadowsuite.app/files',
        documentsDir: '/storage/emulated/0/Documents',
        downloadsDir: '/storage/emulated/0/Download',
        picturesDir: '/storage/emulated/0/Pictures',
        moviesDir: '/storage/emulated/0/Movies',
        musicDir: '/storage/emulated/0/Music',
      );
    }

    try {
      final Map<String, dynamic> directories = await _channel.invokeMethod(
        'getAppDirectories',
      );
      return AndroidDirectories.fromMap(directories);
    } catch (e) {
      debugPrint('Error getting app directories: $e');
      return AndroidDirectories(
        internalAppDir: '',
        externalAppDir: '',
        cacheDir: '',
        filesDir: '',
        documentsDir: '',
        downloadsDir: '',
        picturesDir: '',
        moviesDir: '',
        musicDir: '',
      );
    }
  }

  // Create secure app directory
  Future<String?> createSecureDirectory(String name) async {
    if (!Platform.isAndroid) return null;

    try {
      final String? path = await _channel.invokeMethod(
        'createSecureDirectory',
        {'name': name},
      );
      return path;
    } catch (e) {
      debugPrint('Error creating secure directory: $e');
      return null;
    }
  }

  // Copy file to app directory
  Future<bool> copyFileToAppDirectory({
    required String sourcePath,
    required String destinationName,
    bool overwrite = false,
  }) async {
    if (!Platform.isAndroid) return false;

    try {
      final bool success = await _channel
          .invokeMethod('copyFileToAppDirectory', {
            'sourcePath': sourcePath,
            'destinationName': destinationName,
            'overwrite': overwrite,
          });
      return success;
    } catch (e) {
      debugPrint('Error copying file to app directory: $e');
      return false;
    }
  }

  // Share file using Android sharing intent
  Future<void> shareFile({
    required String filePath,
    String? mimeType,
    String? title,
    String? subject,
  }) async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('shareFile', {
        'filePath': filePath,
        'mimeType': mimeType ?? 'application/octet-stream',
        'title': title ?? 'Share File',
        'subject': subject,
      });
    } catch (e) {
      debugPrint('Error sharing file: $e');
    }
  }

  // Open file with external app
  Future<bool> openFileWithExternalApp({
    required String filePath,
    String? mimeType,
  }) async {
    if (!Platform.isAndroid) return false;

    try {
      final bool success = await _channel.invokeMethod(
        'openFileWithExternalApp',
        {
          'filePath': filePath,
          'mimeType': mimeType ?? 'application/octet-stream',
        },
      );
      return success;
    } catch (e) {
      debugPrint('Error opening file with external app: $e');
      return false;
    }
  }

  // Clear app cache
  Future<bool> clearAppCache() async {
    if (!Platform.isAndroid) return true;

    try {
      final bool success = await _channel.invokeMethod('clearAppCache');
      return success;
    } catch (e) {
      debugPrint('Error clearing app cache: $e');
      return false;
    }
  }

  // Get file MIME type
  Future<String> getFileMimeType(String filePath) async {
    if (!Platform.isAndroid) return 'application/octet-stream';

    try {
      final String mimeType = await _channel.invokeMethod('getFileMimeType', {
        'filePath': filePath,
      });
      return mimeType;
    } catch (e) {
      debugPrint('Error getting file MIME type: $e');
      return 'application/octet-stream';
    }
  }

  // Create backup of app data
  Future<String?> createAppDataBackup() async {
    if (!Platform.isAndroid) return null;

    try {
      final String? backupPath = await _channel.invokeMethod(
        'createAppDataBackup',
      );
      return backupPath;
    } catch (e) {
      debugPrint('Error creating app data backup: $e');
      return null;
    }
  }

  // Restore app data from backup
  Future<bool> restoreAppDataFromBackup(String backupPath) async {
    if (!Platform.isAndroid) return false;

    try {
      final bool success = await _channel.invokeMethod(
        'restoreAppDataFromBackup',
        {'backupPath': backupPath},
      );
      return success;
    } catch (e) {
      debugPrint('Error restoring app data from backup: $e');
      return false;
    }
  }
}

// Storage Info Model
class StorageInfo {
  final int internalStorageTotal;
  final int internalStorageAvailable;
  final int externalStorageTotal;
  final int externalStorageAvailable;
  final int appDataSize;
  final int cacheSize;
  final bool hasExternalStorage;
  final bool isExternalStorageRemovable;
  final DateTime lastUpdated;

  const StorageInfo({
    required this.internalStorageTotal,
    required this.internalStorageAvailable,
    required this.externalStorageTotal,
    required this.externalStorageAvailable,
    required this.appDataSize,
    required this.cacheSize,
    required this.hasExternalStorage,
    required this.isExternalStorageRemovable,
    required this.lastUpdated,
  });

  factory StorageInfo.fromMap(Map<String, dynamic> map) {
    return StorageInfo(
      internalStorageTotal: map['internalStorageTotal'] as int? ?? 0,
      internalStorageAvailable: map['internalStorageAvailable'] as int? ?? 0,
      externalStorageTotal: map['externalStorageTotal'] as int? ?? 0,
      externalStorageAvailable: map['externalStorageAvailable'] as int? ?? 0,
      appDataSize: map['appDataSize'] as int? ?? 0,
      cacheSize: map['cacheSize'] as int? ?? 0,
      hasExternalStorage: map['hasExternalStorage'] as bool? ?? false,
      isExternalStorageRemovable:
          map['isExternalStorageRemovable'] as bool? ?? false,
      lastUpdated: DateTime.fromMillisecondsSinceEpoch(
        map['lastUpdated'] as int? ?? DateTime.now().millisecondsSinceEpoch,
      ),
    );
  }

  double get internalStorageUsedPercentage {
    if (internalStorageTotal == 0) return 0.0;
    return ((internalStorageTotal - internalStorageAvailable) /
            internalStorageTotal) *
        100;
  }

  double get externalStorageUsedPercentage {
    if (externalStorageTotal == 0) return 0.0;
    return ((externalStorageTotal - externalStorageAvailable) /
            externalStorageTotal) *
        100;
  }

  String formatBytes(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }
}

// File Permissions Model
class FilePermissions {
  final bool hasReadExternalStorage;
  final bool hasWriteExternalStorage;
  final bool hasManageExternalStorage;
  final bool hasAccessMediaLocation;
  final bool canAccessAllFiles;
  final DateTime lastChecked;

  const FilePermissions({
    required this.hasReadExternalStorage,
    required this.hasWriteExternalStorage,
    required this.hasManageExternalStorage,
    required this.hasAccessMediaLocation,
    required this.canAccessAllFiles,
    required this.lastChecked,
  });

  factory FilePermissions.fromMap(Map<String, dynamic> map) {
    return FilePermissions(
      hasReadExternalStorage: map['hasReadExternalStorage'] as bool? ?? false,
      hasWriteExternalStorage: map['hasWriteExternalStorage'] as bool? ?? false,
      hasManageExternalStorage:
          map['hasManageExternalStorage'] as bool? ?? false,
      hasAccessMediaLocation: map['hasAccessMediaLocation'] as bool? ?? false,
      canAccessAllFiles: map['canAccessAllFiles'] as bool? ?? false,
      lastChecked: DateTime.fromMillisecondsSinceEpoch(
        map['lastChecked'] as int? ?? DateTime.now().millisecondsSinceEpoch,
      ),
    );
  }

  bool get hasAllPermissions =>
      hasReadExternalStorage &&
      hasWriteExternalStorage &&
      hasManageExternalStorage &&
      hasAccessMediaLocation &&
      canAccessAllFiles;

  bool get hasBasicPermissions =>
      hasReadExternalStorage && hasWriteExternalStorage;
}

// Android Directories Model
class AndroidDirectories {
  final String internalAppDir;
  final String externalAppDir;
  final String cacheDir;
  final String filesDir;
  final String documentsDir;
  final String downloadsDir;
  final String picturesDir;
  final String moviesDir;
  final String musicDir;

  const AndroidDirectories({
    required this.internalAppDir,
    required this.externalAppDir,
    required this.cacheDir,
    required this.filesDir,
    required this.documentsDir,
    required this.downloadsDir,
    required this.picturesDir,
    required this.moviesDir,
    required this.musicDir,
  });

  factory AndroidDirectories.fromMap(Map<String, dynamic> map) {
    return AndroidDirectories(
      internalAppDir: map['internalAppDir'] as String? ?? '',
      externalAppDir: map['externalAppDir'] as String? ?? '',
      cacheDir: map['cacheDir'] as String? ?? '',
      filesDir: map['filesDir'] as String? ?? '',
      documentsDir: map['documentsDir'] as String? ?? '',
      downloadsDir: map['downloadsDir'] as String? ?? '',
      picturesDir: map['picturesDir'] as String? ?? '',
      moviesDir: map['moviesDir'] as String? ?? '',
      musicDir: map['musicDir'] as String? ?? '',
    );
  }
}

// Storage Info Notifier
class StorageInfoNotifier extends StateNotifier<StorageInfo> {
  StorageInfoNotifier()
    : super(
        StorageInfo(
          internalStorageTotal: 0,
          internalStorageAvailable: 0,
          externalStorageTotal: 0,
          externalStorageAvailable: 0,
          appDataSize: 0,
          cacheSize: 0,
          hasExternalStorage: false,
          isExternalStorageRemovable: false,
          lastUpdated: DateTime.now(),
        ),
      );

  Future<void> refreshStorageInfo() async {
    final fileSystem = AndroidFileSystem();
    final storageInfo = await fileSystem.getStorageInfo();
    state = storageInfo;
  }
}

// File Permissions Notifier
class FilePermissionsNotifier extends StateNotifier<FilePermissions> {
  FilePermissionsNotifier()
    : super(
        FilePermissions(
          hasReadExternalStorage: false,
          hasWriteExternalStorage: false,
          hasManageExternalStorage: false,
          hasAccessMediaLocation: false,
          canAccessAllFiles: false,
          lastChecked: DateTime.now(),
        ),
      );

  Future<void> checkPermissions() async {
    final fileSystem = AndroidFileSystem();
    final permissions = await fileSystem.checkPermissions();
    state = permissions;
  }

  Future<bool> requestPermissions() async {
    final fileSystem = AndroidFileSystem();
    final granted = await fileSystem.requestStoragePermissions();

    if (granted) {
      await checkPermissions();
    }

    return granted;
  }
}

// Android Sharing and Deep Linking Service
class AndroidSharingService {
  static const MethodChannel _channel = MethodChannel('shadow_suite/sharing');

  // Share text content
  Future<void> shareText({
    required String text,
    String? subject,
    String? title,
  }) async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('shareText', {
        'text': text,
        'subject': subject,
        'title': title ?? 'Share from Shadow Suite',
      });
    } catch (e) {
      debugPrint('Error sharing text: $e');
    }
  }

  // Share multiple files
  Future<void> shareFiles({
    required List<String> filePaths,
    String? title,
    String? subject,
    List<String>? mimeTypes,
  }) async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('shareFiles', {
        'filePaths': filePaths,
        'title': title ?? 'Share from Shadow Suite',
        'subject': subject,
        'mimeTypes': mimeTypes,
      });
    } catch (e) {
      debugPrint('Error sharing files: $e');
    }
  }

  // Handle incoming shared content
  Future<SharedContent?> getSharedContent() async {
    if (!Platform.isAndroid) return null;

    try {
      final Map<String, dynamic>? content = await _channel.invokeMethod(
        'getSharedContent',
      );
      if (content != null) {
        return SharedContent.fromMap(content);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting shared content: $e');
      return null;
    }
  }

  // Handle deep link
  Future<String?> getDeepLink() async {
    if (!Platform.isAndroid) return null;

    try {
      final String? deepLink = await _channel.invokeMethod('getDeepLink');
      return deepLink;
    } catch (e) {
      debugPrint('Error getting deep link: $e');
      return null;
    }
  }

  // Register deep link handler
  Future<void> registerDeepLinkHandler() async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('registerDeepLinkHandler');
    } catch (e) {
      debugPrint('Error registering deep link handler: $e');
    }
  }
}

// Shared Content Model
class SharedContent {
  final String type;
  final String? text;
  final List<String> filePaths;
  final String? subject;
  final Map<String, String> extras;

  const SharedContent({
    required this.type,
    this.text,
    this.filePaths = const [],
    this.subject,
    this.extras = const {},
  });

  factory SharedContent.fromMap(Map<String, dynamic> map) {
    return SharedContent(
      type: map['type'] as String? ?? 'unknown',
      text: map['text'] as String?,
      filePaths: List<String>.from(map['filePaths'] as List? ?? []),
      subject: map['subject'] as String?,
      extras: Map<String, String>.from(map['extras'] as Map? ?? {}),
    );
  }

  bool get hasText => text != null && text!.isNotEmpty;
  bool get hasFiles => filePaths.isNotEmpty;
  bool get isTextShare => type == 'text/plain';
  bool get isFileShare =>
      type.startsWith('application/') ||
      type.startsWith('image/') ||
      type.startsWith('video/');
}
