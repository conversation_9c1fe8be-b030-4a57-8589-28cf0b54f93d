// Database models for SmartGallery+ (using simple Dart classes)

/// Smart Gallery media item model
class SmartGalleryItem {
  final String id;
  final String path;
  final String name;
  final MediaType type;
  final int size;
  final DateTime dateCreated;
  final DateTime dateModified;
  final int? width;
  final int? height;
  final Duration? duration;
  final String? thumbnailPath;
  final bool isHidden;
  final bool isFavorite;
  final bool isLocked;
  final List<String> aiTags;
  final List<String> customTags;
  final String? ocrText;
  final List<FaceData> faces;
  final LocationData? location;
  final CameraData? cameraData;

  const SmartGalleryItem({
    required this.id,
    required this.path,
    required this.name,
    required this.type,
    required this.size,
    required this.dateCreated,
    required this.dateModified,
    this.width,
    this.height,
    this.duration,
    this.thumbnailPath,
    this.isHidden = false,
    this.isFavorite = false,
    this.isLocked = false,
    this.aiTags = const [],
    this.customTags = const [],
    this.ocrText,
    this.faces = const [],
    this.location,
    this.cameraData,
  });

  SmartGalleryItem copyWith({
    String? id,
    String? path,
    String? name,
    MediaType? type,
    int? size,
    DateTime? dateCreated,
    DateTime? dateModified,
    int? width,
    int? height,
    Duration? duration,
    String? thumbnailPath,
    bool? isHidden,
    bool? isFavorite,
    bool? isLocked,
    List<String>? aiTags,
    List<String>? customTags,
    String? ocrText,
    List<FaceData>? faces,
    LocationData? location,
    CameraData? cameraData,
  }) {
    return SmartGalleryItem(
      id: id ?? this.id,
      path: path ?? this.path,
      name: name ?? this.name,
      type: type ?? this.type,
      size: size ?? this.size,
      dateCreated: dateCreated ?? this.dateCreated,
      dateModified: dateModified ?? this.dateModified,
      width: width ?? this.width,
      height: height ?? this.height,
      duration: duration ?? this.duration,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      isHidden: isHidden ?? this.isHidden,
      isFavorite: isFavorite ?? this.isFavorite,
      isLocked: isLocked ?? this.isLocked,
      aiTags: aiTags ?? this.aiTags,
      customTags: customTags ?? this.customTags,
      ocrText: ocrText ?? this.ocrText,
      faces: faces ?? this.faces,
      location: location ?? this.location,
      cameraData: cameraData ?? this.cameraData,
    );
  }
}

/// Media type enumeration
enum MediaType { image, video, gif, raw }

/// Face detection data
class FaceData {
  final String id;
  final String? personName;
  final double confidence;
  final BoundingBox boundingBox;
  final List<double> embedding;

  const FaceData({
    required this.id,
    this.personName,
    required this.confidence,
    required this.boundingBox,
    required this.embedding,
  });
}

/// Bounding box for face detection
class BoundingBox {
  final double x;
  final double y;
  final double width;
  final double height;

  const BoundingBox({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });
}

/// Location data from EXIF
class LocationData {
  final double latitude;
  final double longitude;
  final double? altitude;
  final String? address;

  const LocationData({
    required this.latitude,
    required this.longitude,
    this.altitude,
    this.address,
  });
}

/// Camera data from EXIF
class CameraData {
  final String? make;
  final String? model;
  final String? lens;
  final double? focalLength;
  final double? aperture;
  final String? iso;
  final String? shutterSpeed;

  const CameraData({
    this.make,
    this.model,
    this.lens,
    this.focalLength,
    this.aperture,
    this.iso,
    this.shutterSpeed,
  });
}

/// Smart Gallery album
class SmartGalleryAlbum {
  final String id;
  final String name;
  final String? description;
  final AlbumType type;
  final List<String> mediaIds;
  final String? coverImageId;
  final DateTime dateCreated;
  final DateTime dateModified;
  final bool isLocked;

  const SmartGalleryAlbum({
    required this.id,
    required this.name,
    this.description,
    required this.type,
    required this.mediaIds,
    this.coverImageId,
    required this.dateCreated,
    required this.dateModified,
    this.isLocked = false,
  });
}

/// Album type enumeration
enum AlbumType { manual, smart, favorites, hidden, people, places, things }

/// Search filter for Smart Gallery
class SmartGalleryFilter {
  final MediaType? mediaType;
  final DateRange? dateRange;
  final SizeRange? sizeRange;
  final List<String>? tags;
  final List<String>? people;
  final LocationData? location;
  final double? locationRadius;
  final bool? hasFaces;
  final bool? hasLocation;
  final bool? hasOcrText;
  final String? textQuery;
  final SortBy sortBy;
  final SortOrder sortOrder;

  const SmartGalleryFilter({
    this.mediaType,
    this.dateRange,
    this.sizeRange,
    this.tags,
    this.people,
    this.location,
    this.locationRadius,
    this.hasFaces,
    this.hasLocation,
    this.hasOcrText,
    this.textQuery,
    this.sortBy = SortBy.dateModified,
    this.sortOrder = SortOrder.descending,
  });
}

/// Date range filter
class DateRange {
  final DateTime start;
  final DateTime end;

  const DateRange({required this.start, required this.end});
}

/// Size range filter
class SizeRange {
  final int minSize;
  final int maxSize;

  const SizeRange({required this.minSize, required this.maxSize});
}

/// Sort by options
enum SortBy { dateCreated, dateModified, name, size, type }

/// Sort order options
enum SortOrder { ascending, descending }

/// AI processing status
enum AIProcessingStatus { pending, processing, completed, failed }

/// AI processing result
class AIProcessingResult {
  final String mediaId;
  final AIProcessingStatus status;
  final List<String> detectedObjects;
  final List<String> sceneLabels;
  final String? ocrText;
  final List<FaceData> faces;
  final double confidence;
  final DateTime processedAt;
  final String? error;

  const AIProcessingResult({
    required this.mediaId,
    required this.status,
    required this.detectedObjects,
    required this.sceneLabels,
    this.ocrText,
    required this.faces,
    required this.confidence,
    required this.processedAt,
    this.error,
  });
}

/// Smart Gallery statistics
class SmartGalleryStats {
  final int totalItems;
  final int totalImages;
  final int totalVideos;
  final int totalSize;
  final int favoritesCount;
  final int hiddenCount;
  final int lockedCount;
  final int peopleCount;
  final int placesCount;
  final int aiProcessedCount;
  final DateTime lastScanTime;

  const SmartGalleryStats({
    required this.totalItems,
    required this.totalImages,
    required this.totalVideos,
    required this.totalSize,
    required this.favoritesCount,
    required this.hiddenCount,
    required this.lockedCount,
    required this.peopleCount,
    required this.placesCount,
    required this.aiProcessedCount,
    required this.lastScanTime,
  });
}

// Note: Database tables are created using SQL statements in the service layer
// instead of using Drift annotations for better compatibility

/// AI model types for analysis
enum ModelType {
  objectDetection,
  faceDetection,
  sceneClassification,
  ocr,
  imageClassification,
}

/// Analysis stages for progress tracking
enum AnalysisStage {
  started,
  objectDetection,
  faceDetection,
  sceneClassification,
  ocr,
  colorAnalysis,
  qualityAssessment,
  processing,
  completed,
  failed,
}

/// Search match types
enum SearchMatchType { arabic, transliteration, translation }

/// Tag categories
enum TagCategory { object, scene, people, color, quality, content }

/// Tag sources
enum TagSource {
  objectDetection,
  sceneClassification,
  faceDetection,
  colorAnalysis,
  qualityAssessment,
  ocr,
}

/// Gender detection results
enum Gender { male, female, unknown }

/// Emotion detection results
enum Emotion { happy, sad, angry, surprised, neutral, fear, disgust }

/// Face landmark types
enum LandmarkType { leftEye, rightEye, nose, mouth, leftEar, rightEar }

/// Color temperature types
enum ColorTemperature { warm, cool, neutral }

/// Camera movement types
enum CameraMovement { stable, pan, tilt, zoom, shake }

/// AI model information
class AIModel {
  final String name;
  final ModelType type;
  final String version;
  final double accuracy;
  final bool isLoaded;

  const AIModel({
    required this.name,
    required this.type,
    required this.version,
    required this.accuracy,
    required this.isLoaded,
  });
}

/// Analysis progress tracking
class AnalysisProgress {
  final String filePath;
  final AnalysisStage stage;
  final double progress;
  final String message;

  const AnalysisProgress({
    required this.filePath,
    required this.stage,
    required this.progress,
    required this.message,
  });
}

/// Base analysis result
abstract class AnalysisResult {
  final String filePath;
  final DateTime analysisTime;
  final double confidence;

  const AnalysisResult({
    required this.filePath,
    required this.analysisTime,
    required this.confidence,
  });
}

/// Object detection result
class ObjectDetection {
  final String label;
  final double confidence;
  final BoundingBox boundingBox;

  const ObjectDetection({
    required this.label,
    required this.confidence,
    required this.boundingBox,
  });
}

/// Object detection analysis result
class ObjectDetectionResult {
  final List<ObjectDetection> detections;
  final Duration processingTime;
  final String modelUsed;

  const ObjectDetectionResult({
    required this.detections,
    required this.processingTime,
    required this.modelUsed,
  });
}

/// Face landmark
class FaceLandmark {
  final LandmarkType type;
  final double x;
  final double y;

  const FaceLandmark({required this.type, required this.x, required this.y});
}

/// Face attributes
class FaceAttributes {
  final int age;
  final Gender gender;
  final Emotion emotion;
  final bool hasGlasses;
  final bool hasBeard;

  const FaceAttributes({
    required this.age,
    required this.gender,
    required this.emotion,
    required this.hasGlasses,
    required this.hasBeard,
  });
}

/// Face detection result
class FaceDetection {
  final BoundingBox boundingBox;
  final double confidence;
  final List<FaceLandmark> landmarks;
  final FaceAttributes attributes;

  const FaceDetection({
    required this.boundingBox,
    required this.confidence,
    required this.landmarks,
    required this.attributes,
  });
}

/// Face detection analysis result
class FaceDetectionResult {
  final List<FaceDetection> faces;
  final Duration processingTime;
  final String modelUsed;

  const FaceDetectionResult({
    required this.faces,
    required this.processingTime,
    required this.modelUsed,
  });
}

/// Scene classification result
class SceneClassification {
  final String label;
  final double confidence;

  const SceneClassification({required this.label, required this.confidence});
}

/// Scene classification analysis result
class SceneClassificationResult {
  final List<SceneClassification> scenes;
  final Duration processingTime;
  final String modelUsed;

  const SceneClassificationResult({
    required this.scenes,
    required this.processingTime,
    required this.modelUsed,
  });
}

/// Text block for OCR
class TextBlock {
  final String text;
  final double confidence;
  final BoundingBox boundingBox;

  const TextBlock({
    required this.text,
    required this.confidence,
    required this.boundingBox,
  });
}

/// OCR analysis result
class OCRResult {
  final String text;
  final double confidence;
  final List<TextBlock> textBlocks;
  final Duration processingTime;
  final String modelUsed;

  const OCRResult({
    required this.text,
    required this.confidence,
    required this.textBlocks,
    required this.processingTime,
    required this.modelUsed,
  });
}

/// Color information
class ColorInfo {
  final int color;
  final double percentage;
  final String name;

  const ColorInfo({
    required this.color,
    required this.percentage,
    required this.name,
  });
}

/// Color analysis result
class ColorAnalysisResult {
  final List<ColorInfo> dominantColors;
  final double averageBrightness;
  final double colorfulness;
  final double contrast;
  final ColorTemperature temperature;

  const ColorAnalysisResult({
    required this.dominantColors,
    required this.averageBrightness,
    required this.colorfulness,
    required this.contrast,
    required this.temperature,
  });
}

/// Quality assessment result
class QualityAssessmentResult {
  final double overallScore;
  final double sharpness;
  final double brightness;
  final double contrast;
  final double saturation;
  final double noise;
  final double blur;
  final double exposure;

  const QualityAssessmentResult({
    required this.overallScore,
    required this.sharpness,
    required this.brightness,
    required this.contrast,
    required this.saturation,
    required this.noise,
    required this.blur,
    required this.exposure,
  });
}

/// Image analysis result
class ImageAnalysisResult extends AnalysisResult {
  final ObjectDetectionResult objectDetection;
  final FaceDetectionResult faceDetection;
  final SceneClassificationResult sceneClassification;
  final OCRResult ocrResult;
  final ColorAnalysisResult colorAnalysis;
  final QualityAssessmentResult qualityAssessment;

  const ImageAnalysisResult({
    required super.filePath,
    required super.analysisTime,
    required super.confidence,
    required this.objectDetection,
    required this.faceDetection,
    required this.sceneClassification,
    required this.ocrResult,
    required this.colorAnalysis,
    required this.qualityAssessment,
  });
}

/// Scene change detection
class SceneChange {
  final Duration timestamp;
  final double confidence;

  const SceneChange({required this.timestamp, required this.confidence});
}

/// Motion analysis result
class MotionAnalysisResult {
  final double averageMotion;
  final List<double> motionVectors;
  final CameraMovement cameraMovement;
  final List<SceneChange> sceneChanges;

  const MotionAnalysisResult({
    required this.averageMotion,
    required this.motionVectors,
    required this.cameraMovement,
    required this.sceneChanges,
  });
}

/// Audio event detection
class AudioEvent {
  final Duration timestamp;
  final String eventType;
  final double confidence;

  const AudioEvent({
    required this.timestamp,
    required this.eventType,
    required this.confidence,
  });
}

/// Audio analysis result
class AudioAnalysisResult {
  final bool hasAudio;
  final double volume;
  final bool speechDetected;
  final bool musicDetected;
  final double noiseLevel;
  final List<AudioEvent> audioEvents;

  const AudioAnalysisResult({
    required this.hasAudio,
    required this.volume,
    required this.speechDetected,
    required this.musicDetected,
    required this.noiseLevel,
    required this.audioEvents,
  });
}

/// Scene segment
class SceneSegment {
  final Duration startTime;
  final Duration endTime;
  final String sceneType;
  final double confidence;

  const SceneSegment({
    required this.startTime,
    required this.endTime,
    required this.sceneType,
    required this.confidence,
  });
}

/// Scene segmentation result
class SceneSegmentationResult {
  final List<SceneSegment> segments;

  const SceneSegmentationResult({required this.segments});
}

/// Video analysis result
class VideoAnalysisResult extends AnalysisResult {
  final List<ImageAnalysisResult> frameAnalyses;
  final MotionAnalysisResult motionAnalysis;
  final AudioAnalysisResult audioAnalysis;
  final SceneSegmentationResult sceneSegmentation;
  final Duration duration;

  const VideoAnalysisResult({
    required super.filePath,
    required super.analysisTime,
    required super.confidence,
    required this.frameAnalyses,
    required this.motionAnalysis,
    required this.audioAnalysis,
    required this.sceneSegmentation,
    required this.duration,
  });
}

/// Search options
class SearchOptions {
  final bool searchInArabic;
  final bool searchInTransliteration;
  final bool searchInTranslation;
  final int maxResults;
  final double minRelevanceScore;

  const SearchOptions({
    this.searchInArabic = true,
    this.searchInTransliteration = true,
    this.searchInTranslation = true,
    this.maxResults = 50,
    this.minRelevanceScore = 0.1,
  });

  static SearchOptions defaultOptions() => const SearchOptions();
}

/// Search result
class SearchResult {
  final String filePath;
  final double relevanceScore;
  final List<String> matchedFeatures;
  final AnalysisResult analysisResult;

  const SearchResult({
    required this.filePath,
    required this.relevanceScore,
    required this.matchedFeatures,
    required this.analysisResult,
  });
}

/// Smart tag
class SmartTag {
  final String label;
  final double confidence;
  final TagCategory category;
  final TagSource source;

  const SmartTag({
    required this.label,
    required this.confidence,
    required this.category,
    required this.source,
  });
}

/// Similarity result
class SimilarityResult {
  final String filePath;
  final double similarityScore;
  final List<String> matchedFeatures;

  const SimilarityResult({
    required this.filePath,
    required this.similarityScore,
    required this.matchedFeatures,
  });
}

/// Analysis statistics
class AnalysisStatistics {
  final int totalAnalyzed;
  final int imageAnalyses;
  final int videoAnalyses;
  final int cacheSize;
  final int modelsLoaded;
  final double averageConfidence;

  const AnalysisStatistics({
    required this.totalAnalyzed,
    required this.imageAnalyses,
    required this.videoAnalyses,
    required this.cacheSize,
    required this.modelsLoaded,
    required this.averageConfidence,
  });
}
