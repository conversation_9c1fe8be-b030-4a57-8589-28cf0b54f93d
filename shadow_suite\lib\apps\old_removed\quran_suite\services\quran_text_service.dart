import 'dart:async';
import '../models/quran_models.dart';

/// Comprehensive Quran text service with multiple text versions and features
class QuranTextService {
  static final QuranTextService _instance = QuranTextService._internal();
  factory QuranTextService() => _instance;
  QuranTextService._internal();

  // Text data storage
  final Map<TextVersion, List<Surah>> _quranTexts = {};
  final Map<String, List<Tafseer>> _tafseerData = {};
  final Map<String, List<Translation>> _translations = {};
  final Map<String, BookmarkData> _bookmarks = {};
  final List<ReadingSession> _readingSessions = [];

  // Current state
  TextVersion _currentTextVersion = TextVersion.uthmani;
  String _currentLanguage = 'en';
  ReadingSettings _readingSettings = ReadingSettings.defaultSettings();

  // Event streams
  final StreamController<QuranEvent> _eventController =
      StreamController<QuranEvent>.broadcast();
  final StreamController<ReadingProgress> _progressController =
      StreamController<ReadingProgress>.broadcast();

  /// Stream of Quran events
  Stream<QuranEvent> get events => _eventController.stream;

  /// Stream of reading progress updates
  Stream<ReadingProgress> get progressStream => _progressController.stream;

  /// Current text version
  TextVersion get currentTextVersion => _currentTextVersion;

  /// Current language
  String get currentLanguage => _currentLanguage;

  /// Current reading settings
  ReadingSettings get readingSettings => _readingSettings;

  /// Initialize the Quran text service
  Future<void> initialize() async {
    try {
      // Load Quran texts for all versions
      await _loadQuranTexts();

      // Load tafseer data
      await _loadTafseerData();

      // Load translations
      await _loadTranslations();

      // Load user bookmarks
      await _loadBookmarks();

      // Load reading sessions
      await _loadReadingSessions();

      _emitEvent(
        QuranEvent(
          type: QuranEventType.initialized,
          message: 'Quran text service initialized',
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      _emitEvent(
        QuranEvent(
          type: QuranEventType.error,
          message: 'Failed to initialize: $e',
          timestamp: DateTime.now(),
        ),
      );
      rethrow;
    }
  }

  /// Get all surahs for current text version
  List<Surah> getSurahs() {
    return _quranTexts[_currentTextVersion] ?? [];
  }

  /// Get specific surah by number
  Surah? getSurah(int surahNumber) {
    final surahs = getSurahs();
    if (surahNumber < 1 || surahNumber > surahs.length) return null;
    return surahs[surahNumber - 1];
  }

  /// Get specific verse
  Verse? getVerse(int surahNumber, int verseNumber) {
    final surah = getSurah(surahNumber);
    if (surah == null) return null;

    if (verseNumber < 1 || verseNumber > surah.verses.length) return null;
    return surah.verses[verseNumber - 1];
  }

  /// Get verses in a range
  List<Verse> getVerseRange(int surahNumber, int startVerse, int endVerse) {
    final surah = getSurah(surahNumber);
    if (surah == null) return [];

    final start = (startVerse - 1).clamp(0, surah.verses.length - 1);
    final end = endVerse.clamp(startVerse, surah.verses.length);

    return surah.verses.sublist(start, end);
  }

  /// Search verses by text
  Future<List<SearchResult>> searchVerses(
    String query, {
    SearchOptions? options,
  }) async {
    final searchOptions = options ?? SearchOptions.defaultOptions();
    final results = <SearchResult>[];
    final queryLower = query.toLowerCase();

    for (final surah in getSurahs()) {
      for (final verse in surah.verses) {
        // Search in Arabic text
        if (searchOptions.searchInArabic &&
            verse.arabicText.toLowerCase().contains(queryLower)) {
          results.add(
            SearchResult(
              surahNumber: surah.number,
              verseNumber: verse.number,
              verse: verse,
              matchType: SearchMatchType.arabic,
              relevanceScore: _calculateRelevanceScore(verse.arabicText, query),
            ),
          );
        }

        // Search in transliteration
        if (searchOptions.searchInTransliteration &&
            verse.transliteration != null &&
            verse.transliteration!.toLowerCase().contains(queryLower)) {
          results.add(
            SearchResult(
              surahNumber: surah.number,
              verseNumber: verse.number,
              verse: verse,
              matchType: SearchMatchType.transliteration,
              relevanceScore: _calculateRelevanceScore(
                verse.transliteration!,
                query,
              ),
            ),
          );
        }

        // Search in translation
        if (searchOptions.searchInTranslation) {
          final translation = _getVerseTranslation(surah.number, verse.number);
          if (translation != null &&
              translation.text.toLowerCase().contains(queryLower)) {
            results.add(
              SearchResult(
                surahNumber: surah.number,
                verseNumber: verse.number,
                verse: verse,
                matchType: SearchMatchType.translation,
                relevanceScore: _calculateRelevanceScore(
                  translation.text,
                  query,
                ),
              ),
            );
          }
        }
      }
    }

    // Sort by relevance score
    results.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));

    return results.take(searchOptions.maxResults).toList();
  }

  /// Get tafseer for a verse
  List<Tafseer> getVerseTafseer(int surahNumber, int verseNumber) {
    final verseKey = '$surahNumber:$verseNumber';
    return _tafseerData[verseKey] ?? [];
  }

  /// Get translation for a verse
  Translation? getVerseTranslation(int surahNumber, int verseNumber) {
    return _getVerseTranslation(surahNumber, verseNumber);
  }

  /// Add bookmark
  Future<void> addBookmark(
    int surahNumber,
    int verseNumber, {
    String? note,
    BookmarkCategory? category,
  }) async {
    final bookmarkId = '${surahNumber}_$verseNumber';
    final bookmark = BookmarkData(
      id: bookmarkId,
      surahNumber: surahNumber,
      verseNumber: verseNumber,
      title: 'Surah $surahNumber, Verse $verseNumber',
      notes: note,
      note: note, // Keep for backward compatibility
      category: category ?? BookmarkCategory.general,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    _bookmarks[bookmarkId] = bookmark;
    await _saveBookmarks();

    _emitEvent(
      QuranEvent(
        type: QuranEventType.bookmarkAdded,
        message: 'Bookmark added for $surahNumber:$verseNumber',
        timestamp: DateTime.now(),
      ),
    );
  }

  /// Remove bookmark
  Future<void> removeBookmark(int surahNumber, int verseNumber) async {
    final bookmarkId = '${surahNumber}_$verseNumber';
    _bookmarks.remove(bookmarkId);
    await _saveBookmarks();

    _emitEvent(
      QuranEvent(
        type: QuranEventType.bookmarkRemoved,
        message: 'Bookmark removed for $surahNumber:$verseNumber',
        timestamp: DateTime.now(),
      ),
    );
  }

  /// Get all bookmarks
  List<BookmarkData> getBookmarks() {
    return _bookmarks.values.toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  /// Check if verse is bookmarked
  bool isBookmarked(int surahNumber, int verseNumber) {
    final bookmarkId = '${surahNumber}_$verseNumber';
    return _bookmarks.containsKey(bookmarkId);
  }

  /// Start reading session
  Future<void> startReadingSession(int surahNumber, int verseNumber) async {
    final session = ReadingSession(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      startTime: DateTime.now(),
      startSurah: surahNumber,
      startVerse: verseNumber,
      currentSurah: surahNumber,
      currentVerse: verseNumber,
      versesRead: 0,
      isActive: true,
    );

    _readingSessions.add(session);
    await _saveReadingSessions();

    _emitEvent(
      QuranEvent(
        type: QuranEventType.sessionStarted,
        message: 'Reading session started',
        timestamp: DateTime.now(),
      ),
    );
  }

  /// Update reading progress
  Future<void> updateReadingProgress(int surahNumber, int verseNumber) async {
    final activeSession = _readingSessions.lastWhere(
      (session) => session.isActive,
      orElse: () => ReadingSession.empty(),
    );

    if (activeSession.id.isNotEmpty) {
      activeSession.currentSurah = surahNumber;
      activeSession.currentVerse = verseNumber;
      activeSession.versesRead++;
      activeSession.lastReadTime = DateTime.now();

      await _saveReadingSessions();

      final progress = ReadingProgress(
        sessionId: activeSession.id,
        surahNumber: surahNumber,
        verseNumber: verseNumber,
        versesRead: activeSession.versesRead,
        timeSpent: DateTime.now().difference(activeSession.startTime),
        readingSpeed: _calculateReadingSpeed(activeSession),
      );

      _progressController.add(progress);
    }
  }

  /// End reading session
  Future<void> endReadingSession() async {
    final activeSession = _readingSessions.lastWhere(
      (session) => session.isActive,
      orElse: () => ReadingSession.empty(),
    );

    if (activeSession.id.isNotEmpty) {
      activeSession.endTime = DateTime.now();
      activeSession.isActive = false;
      activeSession.duration = activeSession.endTime!.difference(
        activeSession.startTime,
      );

      await _saveReadingSessions();

      _emitEvent(
        QuranEvent(
          type: QuranEventType.sessionEnded,
          message: 'Reading session ended',
          timestamp: DateTime.now(),
        ),
      );
    }
  }

  /// Set text version
  Future<void> setTextVersion(TextVersion version) async {
    _currentTextVersion = version;

    _emitEvent(
      QuranEvent(
        type: QuranEventType.textVersionChanged,
        message: 'Text version changed to ${version.name}',
        timestamp: DateTime.now(),
      ),
    );
  }

  /// Set language
  Future<void> setLanguage(String languageCode) async {
    _currentLanguage = languageCode;

    _emitEvent(
      QuranEvent(
        type: QuranEventType.languageChanged,
        message: 'Language changed to $languageCode',
        timestamp: DateTime.now(),
      ),
    );
  }

  /// Update reading settings
  Future<void> updateReadingSettings(ReadingSettings settings) async {
    _readingSettings = settings;

    _emitEvent(
      QuranEvent(
        type: QuranEventType.settingsChanged,
        message: 'Reading settings updated',
        timestamp: DateTime.now(),
      ),
    );
  }

  /// Get reading statistics
  ReadingStatistics getReadingStatistics() {
    final totalSessions = _readingSessions.length;
    final completedSessions = _readingSessions.where((s) => !s.isActive).length;
    final totalVersesRead = _readingSessions.fold(
      0,
      (sum, session) => sum + session.versesRead,
    );
    final totalTimeSpent = _readingSessions
        .where((s) => s.duration != null)
        .fold(Duration.zero, (sum, session) => sum + session.duration!);

    final averageSessionLength = completedSessions > 0
        ? Duration(
            milliseconds: totalTimeSpent.inMilliseconds ~/ completedSessions,
          )
        : Duration.zero;

    final averageReadingSpeed = totalTimeSpent.inMinutes > 0
        ? totalVersesRead / totalTimeSpent.inMinutes
        : 0.0;

    return ReadingStatistics(
      totalSessions: totalSessions,
      totalVersesRead: totalVersesRead,
      totalTimeSpent: totalTimeSpent,
      averageSessionLength: averageSessionLength,
      averageReadingSpeed: averageReadingSpeed,
      bookmarksCount: _bookmarks.length,
      favoriteSuprah: _getMostReadSurah(),
      currentStreak: _getCurrentReadingStreak(),
    );
  }

  /// Get verse of the day
  Verse getVerseOfTheDay() {
    final now = DateTime.now();
    final dayOfYear = now.difference(DateTime(now.year, 1, 1)).inDays + 1;

    // Use day of year to select a verse
    final surahs = getSurahs();
    final totalVerses = surahs.fold(
      0,
      (sum, surah) => sum + surah.verses.length,
    );
    final verseIndex =
        (dayOfYear * 17) % totalVerses; // Use prime number for distribution

    int currentIndex = 0;
    for (final surah in surahs) {
      if (currentIndex + surah.verses.length > verseIndex) {
        return surah.verses[verseIndex - currentIndex];
      }
      currentIndex += surah.verses.length;
    }

    // Fallback to first verse
    return surahs.first.verses.first;
  }

  // Private methods
  Future<void> _loadQuranTexts() async {
    // Load Uthmani text
    _quranTexts[TextVersion.uthmani] = await _loadQuranText('uthmani');

    // Load simplified text
    _quranTexts[TextVersion.simplified] = await _loadQuranText('simplified');

    // Load Indo-Pak text
    _quranTexts[TextVersion.indoPak] = await _loadQuranText('indo_pak');
  }

  Future<List<Surah>> _loadQuranText(String version) async {
    // In a real implementation, this would load from assets or database
    // For now, return sample data
    return List.generate(114, (index) {
      final surahNumber = index + 1;
      return Surah(
        number: surahNumber,
        name: 'Surah $surahNumber',
        arabicName: 'سورة $surahNumber',
        englishName: 'Chapter $surahNumber',
        revelationType: surahNumber <= 86
            ? RevelationType.meccan
            : RevelationType.medinan,
        versesCount: _getVerseCount(surahNumber),
        revelationOrder: surahNumber, // Default to sequential order
        verses: _generateVerses(surahNumber, _getVerseCount(surahNumber)),
      );
    });
  }

  List<Verse> _generateVerses(int surahNumber, int verseCount) {
    return List.generate(verseCount, (index) {
      final verseNumber = index + 1;
      return Verse(
        number: verseNumber,
        arabicText: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        transliteration: 'Bismillahi rahmani raheem',
        juzNumber: _getJuzNumber(surahNumber, verseNumber),
        hizbNumber: _getHizbNumber(surahNumber, verseNumber),
        rukuNumber: _getRukuNumber(surahNumber, verseNumber),
        sajdah: _hasSajdah(surahNumber, verseNumber),
      );
    });
  }

  Future<void> _loadTafseerData() async {
    // Load tafseer data from assets or database
    // For now, use sample data
  }

  Future<void> _loadTranslations() async {
    // Load translation data from assets or database
    // For now, use sample data
  }

  Future<void> _loadBookmarks() async {
    // Load bookmarks from local storage
    // For now, use empty data
  }

  Future<void> _saveBookmarks() async {
    // Save bookmarks to local storage
    // Implementation would persist to database
  }

  Future<void> _loadReadingSessions() async {
    // Load reading sessions from local storage
    // For now, use empty data
  }

  Future<void> _saveReadingSessions() async {
    // Save reading sessions to local storage
    // Implementation would persist to database
  }

  Translation? _getVerseTranslation(int surahNumber, int verseNumber) {
    final verseKey = '$surahNumber:$verseNumber';
    final translations = _translations[verseKey];
    if (translations == null || translations.isEmpty) return null;

    // Return translation for current language
    return translations.firstWhere(
      (t) => t.language == _currentLanguage,
      orElse: () => translations.first,
    );
  }

  double _calculateRelevanceScore(String text, String query) {
    final textLower = text.toLowerCase();
    final queryLower = query.toLowerCase();

    // Exact match gets highest score
    if (textLower == queryLower) return 1.0;

    // Word match gets high score
    if (textLower.split(' ').contains(queryLower)) return 0.8;

    // Contains match gets medium score
    if (textLower.contains(queryLower)) return 0.6;

    // Partial match gets low score
    final words = queryLower.split(' ');
    final matchingWords = words
        .where((word) => textLower.contains(word))
        .length;
    return (matchingWords / words.length) * 0.4;
  }

  double _calculateReadingSpeed(ReadingSession session) {
    final timeSpent = DateTime.now().difference(session.startTime);
    return timeSpent.inMinutes > 0
        ? session.versesRead / timeSpent.inMinutes
        : 0.0;
  }

  String _getMostReadSurah() {
    final surahCounts = <int, int>{};

    for (final session in _readingSessions) {
      surahCounts[session.currentSurah] =
          (surahCounts[session.currentSurah] ?? 0) + 1;
    }

    if (surahCounts.isEmpty) return 'Al-Fatiha';

    final mostReadSurahNumber = surahCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;

    final surah = getSurah(mostReadSurahNumber);
    return surah?.name ?? 'Al-Fatiha';
  }

  int _getCurrentReadingStreak() {
    if (_readingSessions.isEmpty) return 0;

    final now = DateTime.now();
    int streak = 0;

    for (int i = 0; i < 365; i++) {
      final checkDate = now.subtract(Duration(days: i));
      final hasSession = _readingSessions.any(
        (session) =>
            session.startTime.year == checkDate.year &&
            session.startTime.month == checkDate.month &&
            session.startTime.day == checkDate.day,
      );

      if (hasSession) {
        streak++;
      } else {
        break;
      }
    }

    return streak;
  }

  int _getVerseCount(int surahNumber) {
    // Simplified verse counts for demo
    const verseCounts = [
      7,
      286,
      200,
      176,
      120,
      165,
      206,
      75,
      129,
      109,
      123,
      111,
      43,
      52,
      99,
      128,
      111,
      110,
      98,
      135,
      112,
      78,
      118,
      64,
      77,
      227,
      93,
      88,
      69,
      60,
      34,
      30,
      73,
      54,
      45,
      83,
      182,
      88,
      75,
      85,
      54,
      53,
      89,
      59,
      37,
      35,
      38,
      29,
      18,
      45,
      60,
      49,
      62,
      55,
      78,
      96,
      29,
      22,
      24,
      13,
      14,
      11,
      11,
      18,
      12,
      12,
      30,
      52,
      52,
      44,
      28,
      28,
      20,
      56,
      40,
      31,
      50,
      40,
      46,
      42,
      29,
      19,
      36,
      25,
      22,
      17,
      19,
      26,
      30,
      20,
      15,
      21,
      11,
      8,
      8,
      19,
      5,
      8,
      8,
      11,
      11,
      8,
      3,
      9,
      5,
      4,
      7,
      3,
      6,
      3,
      5,
      4,
      5,
      6,
    ];

    return surahNumber <= verseCounts.length ? verseCounts[surahNumber - 1] : 7;
  }

  int _getJuzNumber(int surahNumber, int verseNumber) {
    // Simplified juz calculation
    return ((surahNumber - 1) ~/ 4) + 1;
  }

  int _getHizbNumber(int surahNumber, int verseNumber) {
    // Simplified hizb calculation
    return ((surahNumber - 1) ~/ 2) + 1;
  }

  int _getRukuNumber(int surahNumber, int verseNumber) {
    // Simplified ruku calculation
    return (verseNumber ~/ 10) + 1;
  }

  bool _hasSajdah(int surahNumber, int verseNumber) {
    // Simplified sajdah detection
    const sajdahVerses = {
      7: [206],
      13: [15],
      16: [50],
      17: [109],
      19: [58],
      22: [18, 77],
      25: [60],
      27: [26],
      32: [15],
      38: [24],
      41: [38],
      53: [62],
      84: [21],
      96: [19],
    };

    return sajdahVerses[surahNumber]?.contains(verseNumber) ?? false;
  }

  void _emitEvent(QuranEvent event) {
    _eventController.add(event);
  }

  /// Dispose resources
  void dispose() {
    _eventController.close();
    _progressController.close();
  }
}
