/// Quran insight generated by AI analysis
class QuranInsight {
  final String id;
  final InsightType type;
  final String title;
  final String description;
  final double confidence;
  final List<String> recommendations;
  final DateTime createdAt;
  final Map<String, dynamic>? metadata;

  const QuranInsight({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.confidence,
    required this.recommendations,
    required this.createdAt,
    this.metadata,
  });

  QuranInsight copyWith({
    String? id,
    InsightType? type,
    String? title,
    String? description,
    double? confidence,
    List<String>? recommendations,
    DateTime? createdAt,
    Map<String, dynamic>? metadata,
  }) {
    return QuranInsight(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      confidence: confidence ?? this.confidence,
      recommendations: recommendations ?? this.recommendations,
      createdAt: createdAt ?? this.createdAt,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'confidence': confidence,
      'recommendations': recommendations,
      'createdAt': createdAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  factory QuranInsight.fromJson(Map<String, dynamic> json) {
    return QuranInsight(
      id: json['id'],
      type: InsightType.values.firstWhere((e) => e.name == json['type']),
      title: json['title'],
      description: json['description'],
      confidence: json['confidence'].toDouble(),
      recommendations: List<String>.from(json['recommendations']),
      createdAt: DateTime.parse(json['createdAt']),
      metadata: json['metadata'],
    );
  }
}

/// Types of Quran insights
enum InsightType { pattern, comprehension, suggestion, resource }

/// Recitation recommendation from AI
class RecitationRecommendation {
  final String id;
  final RecommendationType type;
  final String title;
  final String description;
  final String suggestedAction;
  final double confidence;
  final String estimatedBenefit;
  final DateTime createdAt;

  const RecitationRecommendation({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.suggestedAction,
    required this.confidence,
    required this.estimatedBenefit,
    required this.createdAt,
  });

  RecitationRecommendation copyWith({
    String? id,
    RecommendationType? type,
    String? title,
    String? description,
    String? suggestedAction,
    double? confidence,
    String? estimatedBenefit,
    DateTime? createdAt,
  }) {
    return RecitationRecommendation(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      suggestedAction: suggestedAction ?? this.suggestedAction,
      confidence: confidence ?? this.confidence,
      estimatedBenefit: estimatedBenefit ?? this.estimatedBenefit,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'suggestedAction': suggestedAction,
      'confidence': confidence,
      'estimatedBenefit': estimatedBenefit,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory RecitationRecommendation.fromJson(Map<String, dynamic> json) {
    return RecitationRecommendation(
      id: json['id'],
      type: RecommendationType.values.firstWhere((e) => e.name == json['type']),
      title: json['title'],
      description: json['description'],
      suggestedAction: json['suggestedAction'],
      confidence: json['confidence'].toDouble(),
      estimatedBenefit: json['estimatedBenefit'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Types of recitation recommendations
enum RecommendationType {
  schedule,
  improvement,
  portion,
  memorization,
  review,
  themed,
}

/// Study insight for Quran learning
class StudyInsight {
  final String id;
  final InsightType type;
  final String title;
  final String description;
  final String recommendation;
  final double confidence;
  final Map<String, dynamic> supportingData;
  final DateTime createdAt;

  const StudyInsight({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.recommendation,
    required this.confidence,
    required this.supportingData,
    required this.createdAt,
  });

  StudyInsight copyWith({
    String? id,
    InsightType? type,
    String? title,
    String? description,
    String? recommendation,
    double? confidence,
    Map<String, dynamic>? supportingData,
    DateTime? createdAt,
  }) {
    return StudyInsight(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      recommendation: recommendation ?? this.recommendation,
      confidence: confidence ?? this.confidence,
      supportingData: supportingData ?? this.supportingData,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'recommendation': recommendation,
      'confidence': confidence,
      'supportingData': supportingData,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory StudyInsight.fromJson(Map<String, dynamic> json) {
    return StudyInsight(
      id: json['id'],
      type: InsightType.values.firstWhere((e) => e.name == json['type']),
      title: json['title'],
      description: json['description'],
      recommendation: json['recommendation'],
      confidence: json['confidence'].toDouble(),
      supportingData: Map<String, dynamic>.from(json['supportingData']),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Memorization plan generated by AI
class MemorizationPlan {
  final String id;
  final List<String> targetSurahs;
  final int dailyGoal;
  final ReviewSchedule reviewSchedule;
  final List<Milestone> milestones;
  final DateTime estimatedCompletion;
  final MemorizationDifficulty difficulty;
  final DateTime createdAt;

  const MemorizationPlan({
    required this.id,
    required this.targetSurahs,
    required this.dailyGoal,
    required this.reviewSchedule,
    required this.milestones,
    required this.estimatedCompletion,
    required this.difficulty,
    required this.createdAt,
  });

  MemorizationPlan copyWith({
    String? id,
    List<String>? targetSurahs,
    int? dailyGoal,
    ReviewSchedule? reviewSchedule,
    List<Milestone>? milestones,
    DateTime? estimatedCompletion,
    MemorizationDifficulty? difficulty,
    DateTime? createdAt,
  }) {
    return MemorizationPlan(
      id: id ?? this.id,
      targetSurahs: targetSurahs ?? this.targetSurahs,
      dailyGoal: dailyGoal ?? this.dailyGoal,
      reviewSchedule: reviewSchedule ?? this.reviewSchedule,
      milestones: milestones ?? this.milestones,
      estimatedCompletion: estimatedCompletion ?? this.estimatedCompletion,
      difficulty: difficulty ?? this.difficulty,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'targetSurahs': targetSurahs,
      'dailyGoal': dailyGoal,
      'reviewSchedule': reviewSchedule.toJson(),
      'milestones': milestones.map((m) => m.toJson()).toList(),
      'estimatedCompletion': estimatedCompletion.toIso8601String(),
      'difficulty': difficulty.name,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory MemorizationPlan.fromJson(Map<String, dynamic> json) {
    return MemorizationPlan(
      id: json['id'],
      targetSurahs: List<String>.from(json['targetSurahs']),
      dailyGoal: json['dailyGoal'],
      reviewSchedule: ReviewSchedule.fromJson(json['reviewSchedule']),
      milestones: (json['milestones'] as List)
          .map((m) => Milestone.fromJson(m))
          .toList(),
      estimatedCompletion: DateTime.parse(json['estimatedCompletion']),
      difficulty: MemorizationDifficulty.values.firstWhere(
        (e) => e.name == json['difficulty'],
      ),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Review schedule for memorization
class ReviewSchedule {
  final List<String> daily;
  final List<String> weekly;
  final List<String> monthly;

  const ReviewSchedule({
    required this.daily,
    required this.weekly,
    required this.monthly,
  });

  ReviewSchedule copyWith({
    List<String>? daily,
    List<String>? weekly,
    List<String>? monthly,
  }) {
    return ReviewSchedule(
      daily: daily ?? this.daily,
      weekly: weekly ?? this.weekly,
      monthly: monthly ?? this.monthly,
    );
  }

  Map<String, dynamic> toJson() {
    return {'daily': daily, 'weekly': weekly, 'monthly': monthly};
  }

  factory ReviewSchedule.fromJson(Map<String, dynamic> json) {
    return ReviewSchedule(
      daily: List<String>.from(json['daily']),
      weekly: List<String>.from(json['weekly']),
      monthly: List<String>.from(json['monthly']),
    );
  }
}

/// Memorization milestone
class Milestone {
  final String id;
  final String title;
  final String description;
  final DateTime targetDate;
  final int verses;
  final bool isCompleted;

  const Milestone({
    required this.id,
    required this.title,
    required this.description,
    required this.targetDate,
    required this.verses,
    required this.isCompleted,
  });

  Milestone copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? targetDate,
    int? verses,
    bool? isCompleted,
  }) {
    return Milestone(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      targetDate: targetDate ?? this.targetDate,
      verses: verses ?? this.verses,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'targetDate': targetDate.toIso8601String(),
      'verses': verses,
      'isCompleted': isCompleted,
    };
  }

  factory Milestone.fromJson(Map<String, dynamic> json) {
    return Milestone(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      targetDate: DateTime.parse(json['targetDate']),
      verses: json['verses'],
      isCompleted: json['isCompleted'],
    );
  }
}

/// Memorization difficulty levels
enum MemorizationDifficulty { easy, medium, hard }

/// Progress analytics for Quran study
class ProgressAnalytics {
  final String id;
  final int totalReadingSessions;
  final int totalVersesRead;
  final int totalMemorized;
  final Duration averageSessionDuration;
  final int readingStreak;
  final int memorizationStreak;
  final List<WeeklyProgress> weeklyProgress;
  final List<MonthlyProgress> monthlyProgress;
  final double completionPercentage;
  final List<String> strongestAreas;
  final List<String> improvementAreas;
  final List<String> achievements;
  final DateTime generatedAt;

  const ProgressAnalytics({
    required this.id,
    required this.totalReadingSessions,
    required this.totalVersesRead,
    required this.totalMemorized,
    required this.averageSessionDuration,
    required this.readingStreak,
    required this.memorizationStreak,
    required this.weeklyProgress,
    required this.monthlyProgress,
    required this.completionPercentage,
    required this.strongestAreas,
    required this.improvementAreas,
    required this.achievements,
    required this.generatedAt,
  });

  ProgressAnalytics copyWith({
    String? id,
    int? totalReadingSessions,
    int? totalVersesRead,
    int? totalMemorized,
    Duration? averageSessionDuration,
    int? readingStreak,
    int? memorizationStreak,
    List<WeeklyProgress>? weeklyProgress,
    List<MonthlyProgress>? monthlyProgress,
    double? completionPercentage,
    List<String>? strongestAreas,
    List<String>? improvementAreas,
    List<String>? achievements,
    DateTime? generatedAt,
  }) {
    return ProgressAnalytics(
      id: id ?? this.id,
      totalReadingSessions: totalReadingSessions ?? this.totalReadingSessions,
      totalVersesRead: totalVersesRead ?? this.totalVersesRead,
      totalMemorized: totalMemorized ?? this.totalMemorized,
      averageSessionDuration:
          averageSessionDuration ?? this.averageSessionDuration,
      readingStreak: readingStreak ?? this.readingStreak,
      memorizationStreak: memorizationStreak ?? this.memorizationStreak,
      weeklyProgress: weeklyProgress ?? this.weeklyProgress,
      monthlyProgress: monthlyProgress ?? this.monthlyProgress,
      completionPercentage: completionPercentage ?? this.completionPercentage,
      strongestAreas: strongestAreas ?? this.strongestAreas,
      improvementAreas: improvementAreas ?? this.improvementAreas,
      achievements: achievements ?? this.achievements,
      generatedAt: generatedAt ?? this.generatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'totalReadingSessions': totalReadingSessions,
      'totalVersesRead': totalVersesRead,
      'totalMemorized': totalMemorized,
      'averageSessionDuration': averageSessionDuration.inMinutes,
      'readingStreak': readingStreak,
      'memorizationStreak': memorizationStreak,
      'weeklyProgress': weeklyProgress.map((w) => w.toJson()).toList(),
      'monthlyProgress': monthlyProgress.map((m) => m.toJson()).toList(),
      'completionPercentage': completionPercentage,
      'strongestAreas': strongestAreas,
      'improvementAreas': improvementAreas,
      'achievements': achievements,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }

  factory ProgressAnalytics.fromJson(Map<String, dynamic> json) {
    return ProgressAnalytics(
      id: json['id'],
      totalReadingSessions: json['totalReadingSessions'],
      totalVersesRead: json['totalVersesRead'],
      totalMemorized: json['totalMemorized'],
      averageSessionDuration: Duration(minutes: json['averageSessionDuration']),
      readingStreak: json['readingStreak'],
      memorizationStreak: json['memorizationStreak'],
      weeklyProgress: (json['weeklyProgress'] as List)
          .map((w) => WeeklyProgress.fromJson(w))
          .toList(),
      monthlyProgress: (json['monthlyProgress'] as List)
          .map((m) => MonthlyProgress.fromJson(m))
          .toList(),
      completionPercentage: json['completionPercentage'].toDouble(),
      strongestAreas: List<String>.from(json['strongestAreas']),
      improvementAreas: List<String>.from(json['improvementAreas']),
      achievements: List<String>.from(json['achievements']),
      generatedAt: DateTime.parse(json['generatedAt']),
    );
  }
}

/// Weekly progress data
class WeeklyProgress {
  final DateTime weekStart;
  final int sessionsCount;
  final int totalVersesRead;
  final Duration totalDuration;

  const WeeklyProgress({
    required this.weekStart,
    required this.sessionsCount,
    required this.totalVersesRead,
    required this.totalDuration,
  });

  WeeklyProgress copyWith({
    DateTime? weekStart,
    int? sessionsCount,
    int? totalVersesRead,
    Duration? totalDuration,
  }) {
    return WeeklyProgress(
      weekStart: weekStart ?? this.weekStart,
      sessionsCount: sessionsCount ?? this.sessionsCount,
      totalVersesRead: totalVersesRead ?? this.totalVersesRead,
      totalDuration: totalDuration ?? this.totalDuration,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'weekStart': weekStart.toIso8601String(),
      'sessionsCount': sessionsCount,
      'totalVersesRead': totalVersesRead,
      'totalDuration': totalDuration.inMinutes,
    };
  }

  factory WeeklyProgress.fromJson(Map<String, dynamic> json) {
    return WeeklyProgress(
      weekStart: DateTime.parse(json['weekStart']),
      sessionsCount: json['sessionsCount'],
      totalVersesRead: json['totalVersesRead'],
      totalDuration: Duration(minutes: json['totalDuration']),
    );
  }
}

/// Monthly progress data
class MonthlyProgress {
  final DateTime monthStart;
  final int sessionsCount;
  final int totalVersesRead;
  final Duration totalDuration;

  const MonthlyProgress({
    required this.monthStart,
    required this.sessionsCount,
    required this.totalVersesRead,
    required this.totalDuration,
  });

  MonthlyProgress copyWith({
    DateTime? monthStart,
    int? sessionsCount,
    int? totalVersesRead,
    Duration? totalDuration,
  }) {
    return MonthlyProgress(
      monthStart: monthStart ?? this.monthStart,
      sessionsCount: sessionsCount ?? this.sessionsCount,
      totalVersesRead: totalVersesRead ?? this.totalVersesRead,
      totalDuration: totalDuration ?? this.totalDuration,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'monthStart': monthStart.toIso8601String(),
      'sessionsCount': sessionsCount,
      'totalVersesRead': totalVersesRead,
      'totalDuration': totalDuration.inMinutes,
    };
  }

  factory MonthlyProgress.fromJson(Map<String, dynamic> json) {
    return MonthlyProgress(
      monthStart: DateTime.parse(json['monthStart']),
      sessionsCount: json['sessionsCount'],
      totalVersesRead: json['totalVersesRead'],
      totalDuration: Duration(minutes: json['totalDuration']),
    );
  }
}
