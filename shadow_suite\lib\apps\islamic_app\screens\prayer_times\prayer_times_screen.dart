import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import '../../services/prayer_times_service.dart';
import '../../../../core/theme/app_theme.dart';

class PrayerTimesScreen extends ConsumerStatefulWidget {
  const PrayerTimesScreen({super.key});

  @override
  ConsumerState<PrayerTimesScreen> createState() => _PrayerTimesScreenState();
}

class _PrayerTimesScreenState extends ConsumerState<PrayerTimesScreen>
    with TickerProviderStateMixin {
  PrayerTimes? _prayerTimes;
  NextPrayer? _nextPrayer;
  Timer? _timer;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  
  // Default location (can be changed by user)
  final double _latitude = 21.4225; // Makkah
  final double _longitude = 39.8262;
  final double _timezone = 3.0; // UTC+3
  String _calculationMethod = 'muslim_world_league';
  final String _locationName = 'Makkah, Saudi Arabia';

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    _pulseController.repeat(reverse: true);
    
    _calculatePrayerTimes();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _pulseController.dispose();
    super.dispose();
  }

  void _calculatePrayerTimes() {
    final now = DateTime.now();
    final prayerTimes = PrayerTimesService.calculatePrayerTimes(
      date: now,
      latitude: _latitude,
      longitude: _longitude,
      timezone: _timezone,
      calculationMethodKey: _calculationMethod,
    );
    
    setState(() {
      _prayerTimes = prayerTimes;
      _nextPrayer = PrayerTimesService.getNextPrayer(prayerTimes);
    });
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_prayerTimes != null) {
        setState(() {
          _nextPrayer = PrayerTimesService.getNextPrayer(_prayerTimes!);
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Prayer Times'),
        backgroundColor: AppTheme.islamicAppColor,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettings,
          ),
          IconButton(
            icon: const Icon(Icons.location_on),
            onPressed: _showLocationPicker,
          ),
        ],
      ),
      body: _prayerTimes == null
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildLocationCard(),
                  const SizedBox(height: 16),
                  _buildNextPrayerCard(),
                  const SizedBox(height: 24),
                  _buildPrayerTimesList(),
                  const SizedBox(height: 24),
                  _buildQiblaCard(),
                ],
              ),
            ),
    );
  }

  Widget _buildLocationCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.islamicAppColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.location_on,
                color: AppTheme.islamicAppColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _locationName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${_latitude.toStringAsFixed(4)}, ${_longitude.toStringAsFixed(4)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    _prayerTimes!.calculationMethod,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.islamicAppColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNextPrayerCard() {
    if (_nextPrayer == null) return const SizedBox.shrink();

    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Card(
            color: AppTheme.islamicAppColor.withValues(alpha: 0.1),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppTheme.islamicAppColor,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: const Icon(
                          Icons.access_time,
                          color: Colors.white,
                          size: 32,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Next Prayer',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Colors.grey[600],
                              ),
                            ),
                            Text(
                              _nextPrayer!.name,
                              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: AppTheme.islamicAppColor,
                              ),
                            ),
                            Text(
                              _prayerTimes!.formatTime(_nextPrayer!.time),
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                color: AppTheme.islamicAppColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Column(
                        children: [
                          Text(
                            'Time Remaining',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            _nextPrayer!.formattedTimeRemaining,
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppTheme.islamicAppColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPrayerTimesList() {
    final prayers = [
      {'name': 'Fajr', 'time': _prayerTimes!.fajr, 'icon': Icons.wb_twilight},
      {'name': 'Sunrise', 'time': _prayerTimes!.sunrise, 'icon': Icons.wb_sunny},
      {'name': 'Dhuhr', 'time': _prayerTimes!.dhuhr, 'icon': Icons.wb_sunny_outlined},
      {'name': 'Asr', 'time': _prayerTimes!.asr, 'icon': Icons.wb_cloudy},
      {'name': 'Maghrib', 'time': _prayerTimes!.maghrib, 'icon': Icons.wb_twilight},
      {'name': 'Isha', 'time': _prayerTimes!.isha, 'icon': Icons.nightlight_round},
    ];

    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Today\'s Prayer Times',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.islamicAppColor,
              ),
            ),
          ),
          ...prayers.map((prayer) {
            final isNext = _nextPrayer?.name == prayer['name'];
            return Container(
              color: isNext ? AppTheme.islamicAppColor.withValues(alpha: 0.05) : null,
              child: ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isNext 
                        ? AppTheme.islamicAppColor 
                        : AppTheme.islamicAppColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    prayer['icon'] as IconData,
                    color: isNext ? Colors.white : AppTheme.islamicAppColor,
                    size: 20,
                  ),
                ),
                title: Text(
                  prayer['name'] as String,
                  style: TextStyle(
                    fontWeight: isNext ? FontWeight.bold : FontWeight.w500,
                    color: isNext ? AppTheme.islamicAppColor : null,
                  ),
                ),
                trailing: Text(
                  _prayerTimes!.formatTime(prayer['time'] as DateTime),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: isNext ? FontWeight.bold : FontWeight.w500,
                    color: isNext ? AppTheme.islamicAppColor : Colors.grey[700],
                  ),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildQiblaCard() {
    final qiblaDirection = PrayerTimesService.calculateQiblaDirection(
      latitude: _latitude,
      longitude: _longitude,
    );

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.islamicAppColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Transform.rotate(
                angle: qiblaDirection * 3.14159 / 180,
                child: Icon(
                  Icons.navigation,
                  color: AppTheme.islamicAppColor,
                  size: 24,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Qibla Direction',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${qiblaDirection.toStringAsFixed(1)}° from North',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.islamicAppColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            ElevatedButton(
              onPressed: () => _showQiblaCompass(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.islamicAppColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Compass'),
            ),
          ],
        ),
      ),
    );
  }

  void _showSettings() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Prayer Time Settings',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text('Calculation Method'),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: _calculationMethod,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
              ),
              items: PrayerTimesService.calculationMethods.entries.map((entry) {
                return DropdownMenuItem(
                  value: entry.key,
                  child: Text(entry.value.name),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _calculationMethod = value;
                  });
                  _calculatePrayerTimes();
                  Navigator.pop(context);
                }
              },
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Close'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showLocationPicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Change Location'),
        content: const Text('Location picker will be implemented with GPS and manual entry options.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showQiblaCompass() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Qibla Compass'),
        content: const Text('Interactive Qibla compass will be implemented with device orientation.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
