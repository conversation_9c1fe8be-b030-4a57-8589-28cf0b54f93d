import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_theme.dart';
import '../../widgets/visual_formula_builder_widget.dart';
import '../../services/visual_formula_builder_service.dart';

/// Advanced formula builder screen with visual interface
class FormulaBuilderScreen extends ConsumerStatefulWidget {
  const FormulaBuilderScreen({super.key});

  @override
  ConsumerState<FormulaBuilderScreen> createState() =>
      _FormulaBuilderScreenState();
}

class _FormulaBuilderScreenState extends ConsumerState<FormulaBuilderScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final List<FormulaProject> _projects = [];
  FormulaProject? _currentProject;
  final TextEditingController _projectNameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    VisualFormulaBuilderService.initialize();
    _createDefaultProject();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _projectNameController.dispose();
    super.dispose();
  }

  void _createDefaultProject() {
    final project = FormulaProject(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: 'New Formula Project',
      description: 'Build your Excel formulas visually',
      formulas: [],
      createdAt: DateTime.now(),
    );

    setState(() {
      _projects.add(project);
      _currentProject = project;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Formula Builder'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showProjectManager,
            icon: const Icon(Icons.folder),
            tooltip: 'Projects',
          ),
          IconButton(
            onPressed: _saveProject,
            icon: const Icon(Icons.save),
            tooltip: 'Save Project',
          ),
          IconButton(
            onPressed: _exportFormulas,
            icon: const Icon(Icons.download),
            tooltip: 'Export Formulas',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.build), text: 'Visual Builder'),
            Tab(icon: Icon(Icons.code), text: 'Formula Editor'),
            Tab(icon: Icon(Icons.library_books), text: 'Formula Library'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildVisualBuilderTab(),
          _buildFormulaEditorTab(),
          _buildFormulaLibraryTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _createNewFormula,
        icon: const Icon(Icons.add),
        label: const Text('New Formula'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  Widget _buildVisualBuilderTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildProjectHeader(),
          const SizedBox(height: 16),
          Expanded(
            child: VisualFormulaBuilderWidget(
              onFormulaChanged: _onFormulaChanged,
              initialFormula: _currentProject?.formulas.isNotEmpty == true
                  ? _currentProject!.formulas.last.formula
                  : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProjectHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _currentProject?.name ?? 'No Project',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _currentProject?.description ?? '',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: _editProjectDetails,
                  icon: const Icon(Icons.edit),
                  tooltip: 'Edit Project',
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildStatCard(
                  'Formulas',
                  '${_currentProject?.formulas.length ?? 0}',
                ),
                const SizedBox(width: 16),
                _buildStatCard(
                  'Functions Used',
                  '${_getUniqueFunctionsCount()}',
                ),
                const SizedBox(width: 16),
                _buildStatCard('Complexity', _getComplexityLevel()),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String label, String value) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppTheme.primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormulaEditorTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Formula Editor',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    decoration: const InputDecoration(
                      labelText: 'Formula Name',
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      // Handle formula name change
                    },
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    decoration: const InputDecoration(
                      labelText: 'Description',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 2,
                    onChanged: (value) {
                      // Handle description change
                    },
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    decoration: const InputDecoration(
                      labelText: 'Formula',
                      hintText: 'Enter your Excel formula...',
                      border: OutlineInputBorder(),
                      prefixText: '= ',
                    ),
                    maxLines: 3,
                    onChanged: (value) {
                      // Handle formula change
                    },
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      ElevatedButton.icon(
                        onPressed: _validateCurrentFormula,
                        icon: const Icon(Icons.check),
                        label: const Text('Validate'),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton.icon(
                        onPressed: _testCurrentFormula,
                        icon: const Icon(Icons.play_arrow),
                        label: const Text('Test'),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton.icon(
                        onPressed: _saveCurrentFormula,
                        icon: const Icon(Icons.save),
                        label: const Text('Save'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Expanded(child: _buildFormulasList()),
        ],
      ),
    );
  }

  Widget _buildFormulasList() {
    final formulas = _currentProject?.formulas ?? [];

    if (formulas.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.functions, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No formulas created yet'),
            Text('Create your first formula to get started'),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: formulas.length,
      itemBuilder: (context, index) {
        final formula = formulas[index];
        return Card(
          child: ListTile(
            title: Text(formula.name),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(formula.description),
                const SizedBox(height: 4),
                Text(
                  formula.formula,
                  style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                ),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  onPressed: () => _editFormula(formula),
                  icon: const Icon(Icons.edit),
                ),
                IconButton(
                  onPressed: () => _deleteFormula(formula),
                  icon: const Icon(Icons.delete),
                ),
              ],
            ),
            isThreeLine: true,
          ),
        );
      },
    );
  }

  Widget _buildFormulaLibraryTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Formula Library',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  const Text('Browse and use pre-built formula templates'),
                  const SizedBox(height: 16),
                  TextField(
                    decoration: const InputDecoration(
                      labelText: 'Search Templates',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      // Handle search
                    },
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Expanded(child: _buildTemplatesList()),
        ],
      ),
    );
  }

  Widget _buildTemplatesList() {
    final templates = _getFormulaTemplates();

    return ListView.builder(
      itemCount: templates.length,
      itemBuilder: (context, index) {
        final template = templates[index];
        return Card(
          child: ListTile(
            title: Text(template.name),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(template.description),
                const SizedBox(height: 4),
                Text(
                  template.formula,
                  style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                ),
              ],
            ),
            trailing: ElevatedButton(
              onPressed: () => _useTemplate(template),
              child: const Text('Use'),
            ),
            isThreeLine: true,
          ),
        );
      },
    );
  }

  List<FormulaTemplate> _getFormulaTemplates() {
    return [
      FormulaTemplate(
        id: '1',
        name: 'Sum with Condition',
        description: 'Sum values based on a condition',
        nodes: [],
        formula: 'SUMIF(A:A, ">100", B:B)',
        createdAt: DateTime.now(),
      ),
      FormulaTemplate(
        id: '2',
        name: 'Average Excluding Zeros',
        description: 'Calculate average while excluding zero values',
        nodes: [],
        formula: 'AVERAGEIF(A:A, ">0")',
        createdAt: DateTime.now(),
      ),
      FormulaTemplate(
        id: '3',
        name: 'Nested IF Statement',
        description: 'Multi-level conditional logic',
        nodes: [],
        formula: 'IF(A1>90, "A", IF(A1>80, "B", IF(A1>70, "C", "F")))',
        createdAt: DateTime.now(),
      ),
      FormulaTemplate(
        id: '4',
        name: 'VLOOKUP with Error Handling',
        description: 'Safe VLOOKUP that handles errors gracefully',
        nodes: [],
        formula: 'IFERROR(VLOOKUP(A1, B:D, 3, FALSE), "Not Found")',
        createdAt: DateTime.now(),
      ),
    ];
  }

  void _onFormulaChanged(String formula) {
    // Handle formula changes from visual builder
  }

  void _showProjectManager() {
    // Show project management dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Project Manager'),
        content: const Text(
          'Project management features will be implemented here',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _saveProject() {
    // Save current project
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Project saved successfully'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _exportFormulas() {
    // Export formulas to file
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Formulas exported successfully'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _createNewFormula() {
    // Create new formula
    final formula = ProjectFormula(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: 'New Formula ${(_currentProject?.formulas.length ?? 0) + 1}',
      description: 'Enter description here',
      formula: '',
      createdAt: DateTime.now(),
    );

    setState(() {
      _currentProject?.formulas.add(formula);
    });
  }

  void _editProjectDetails() {
    // Edit project details
  }

  void _validateCurrentFormula() {
    // Validate current formula
  }

  void _testCurrentFormula() {
    // Test current formula
  }

  void _saveCurrentFormula() {
    // Save current formula
  }

  void _editFormula(ProjectFormula formula) {
    // Edit formula
  }

  void _deleteFormula(ProjectFormula formula) {
    // Delete formula
    setState(() {
      _currentProject?.formulas.remove(formula);
    });
  }

  void _useTemplate(FormulaTemplate template) {
    // Use template
    final formula = ProjectFormula(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: template.name,
      description: template.description,
      formula: template.formula,
      createdAt: DateTime.now(),
    );

    setState(() {
      _currentProject?.formulas.add(formula);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Template "${template.name}" added to project'),
        backgroundColor: Colors.green,
      ),
    );
  }

  int _getUniqueFunctionsCount() {
    final functions = <String>{};
    for (final formula in _currentProject?.formulas ?? []) {
      final matches = RegExp(r'[A-Z]+(?=\()').allMatches(formula.formula);
      for (final match in matches) {
        functions.add(match.group(0)!);
      }
    }
    return functions.length;
  }

  String _getComplexityLevel() {
    final formulaCount = _currentProject?.formulas.length ?? 0;
    final functionCount = _getUniqueFunctionsCount();

    if (formulaCount == 0) return 'None';
    if (formulaCount <= 3 && functionCount <= 5) return 'Simple';
    if (formulaCount <= 10 && functionCount <= 15) return 'Medium';
    return 'Complex';
  }
}

/// Formula project data class
class FormulaProject {
  final String id;
  final String name;
  final String description;
  final List<ProjectFormula> formulas;
  final DateTime createdAt;

  FormulaProject({
    required this.id,
    required this.name,
    required this.description,
    required this.formulas,
    required this.createdAt,
  });
}

/// Project formula data class
class ProjectFormula {
  final String id;
  final String name;
  final String description;
  final String formula;
  final DateTime createdAt;

  ProjectFormula({
    required this.id,
    required this.name,
    required this.description,
    required this.formula,
    required this.createdAt,
  });
}
