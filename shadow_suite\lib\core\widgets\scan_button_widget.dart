import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/storage_scanner_service.dart';
import '../services/app_permissions_manager.dart';
import 'scan_progress_widget.dart';

/// Reusable scan button widget for different apps
class ScanButtonWidget extends ConsumerStatefulWidget {
  final String appName;
  final FileType? specificFileType;
  final Function(StorageScanResult)? onScanComplete;
  final Function(List<File>)? onQuickScanComplete;
  final bool showProgress;
  final String buttonText;
  final IconData buttonIcon;

  const ScanButtonWidget({
    super.key,
    required this.appName,
    this.specificFileType,
    this.onScanComplete,
    this.onQuickScanComplete,
    this.showProgress = true,
    this.buttonText = 'Scan Storage',
    this.buttonIcon = Icons.search,
  });

  @override
  ConsumerState<ScanButtonWidget> createState() => _ScanButtonWidgetState();
}

class _ScanButtonWidgetState extends ConsumerState<ScanButtonWidget> {
  StorageScanResult? _lastScanResult;

  @override
  Widget build(BuildContext context) {
    final isScanning = ref.watch(isScanningProvider);

    return Column(
      children: [
        // Scan button
        ElevatedButton.icon(
          onPressed: isScanning ? null : _performScan,
          icon: isScanning
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Icon(widget.buttonIcon),
          label: Text(isScanning ? 'Scanning...' : widget.buttonText),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),

        // Quick scan options
        if (!isScanning && widget.specificFileType == null) ...[
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [
              _buildQuickScanChip(
                context,
                FileType.image,
                'Images',
                Icons.image,
              ),
              _buildQuickScanChip(
                context,
                FileType.video,
                'Videos',
                Icons.videocam,
              ),
              _buildQuickScanChip(
                context,
                FileType.audio,
                'Audio',
                Icons.audiotrack,
              ),
              _buildQuickScanChip(
                context,
                FileType.document,
                'Docs',
                Icons.description,
              ),
            ],
          ),
        ],

        // Progress indicator
        if (widget.showProgress && isScanning)
          const Padding(
            padding: EdgeInsets.only(top: 16),
            child: ScanProgressWidget(),
          ),

        // Last scan results
        if (_lastScanResult != null && !isScanning)
          Padding(
            padding: const EdgeInsets.only(top: 16),
            child: ScanResultsSummary(
              results: _lastScanResult!,
              onViewDetails: () => _showScanDetails(context),
            ),
          ),
      ],
    );
  }

  Widget _buildQuickScanChip(
    BuildContext context,
    FileType fileType,
    String label,
    IconData icon,
  ) {
    return ActionChip(
      avatar: Icon(icon, size: 16),
      label: Text(label),
      onPressed: () => _performQuickScan(fileType),
      backgroundColor: Theme.of(context).colorScheme.surface,
      side: BorderSide(color: Theme.of(context).colorScheme.outline),
    );
  }

  Future<void> _performScan() async {
    try {
      // Check permissions first
      final hasPermissions = await _checkPermissions();
      if (!hasPermissions) return;

      // Scanning state is managed by the StorageScannerService

      StorageScanResult result;

      if (widget.specificFileType != null) {
        // Perform targeted scan for specific file type
        final files = await StorageScannerService.quickScan(
          widget.specificFileType!,
        );
        result = StorageScanResult();

        switch (widget.specificFileType!) {
          case FileType.image:
            result.images.addAll(files);
            break;
          case FileType.video:
            result.videos.addAll(files);
            break;
          case FileType.audio:
            result.audio.addAll(files);
            break;
          case FileType.document:
            result.documents.addAll(files);
            break;
          case FileType.other:
            result.others.addAll(files);
            break;
        }

        widget.onQuickScanComplete?.call(files);
      } else {
        // Perform full scan
        result = await StorageScannerService.performFullScan(
          includeImages: _shouldIncludeFileType(FileType.image),
          includeVideos: _shouldIncludeFileType(FileType.video),
          includeAudio: _shouldIncludeFileType(FileType.audio),
          includeDocuments: _shouldIncludeFileType(FileType.document),
          includeOthers: false, // Usually not needed
        );
      }

      setState(() {
        _lastScanResult = result;
      });

      widget.onScanComplete?.call(result);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Scan completed! Found ${result.totalFiles} files.'),
            action: SnackBarAction(
              label: 'View',
              onPressed: () => _showScanDetails(context),
            ),
          ),
        );
      }
    } catch (e) {
      // Scanning state is managed by the StorageScannerService

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Scan failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _performQuickScan(FileType fileType) async {
    try {
      // Check permissions first
      final hasPermissions = await _checkPermissions();
      if (!hasPermissions) return;

      final files = await StorageScannerService.quickScan(fileType);

      widget.onQuickScanComplete?.call(files);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Found ${files.length} ${_getFileTypeLabel(fileType).toLowerCase()} files.',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Quick scan failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<bool> _checkPermissions() async {
    final appName = _getAppNameForPermissions();
    if (appName == null) return true;

    final hasPermissions = await AppPermissionsManager.ensureAppPermissions(
      context,
      appName,
    );

    if (!hasPermissions && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Storage permissions are required to scan files.'),
          backgroundColor: Colors.orange,
        ),
      );
    }

    return hasPermissions;
  }

  String? _getAppNameForPermissions() {
    switch (widget.appName.toLowerCase()) {
      case 'file_manager':
      case 'file manager':
        return 'file_manager';
      case 'smartgallery':
      case 'smart gallery':
        return 'smartgallery';
      case 'shadow_player':
      case 'shadow player':
        return 'shadow_player';
      default:
        return null;
    }
  }

  bool _shouldIncludeFileType(FileType fileType) {
    // Determine which file types to include based on the app
    switch (widget.appName.toLowerCase()) {
      case 'smartgallery':
      case 'smart gallery':
        return fileType == FileType.image || fileType == FileType.video;
      case 'shadow_player':
      case 'shadow player':
        return fileType == FileType.video || fileType == FileType.audio;
      case 'file_manager':
      case 'file manager':
        return true; // Include all file types
      default:
        return true;
    }
  }

  String _getFileTypeLabel(FileType fileType) {
    switch (fileType) {
      case FileType.image:
        return 'Images';
      case FileType.video:
        return 'Videos';
      case FileType.audio:
        return 'Audio';
      case FileType.document:
        return 'Documents';
      case FileType.other:
        return 'Other';
    }
  }

  void _showScanDetails(BuildContext context) {
    if (_lastScanResult == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Scan Results Details'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow(
                'Images',
                _lastScanResult!.images.length,
                Icons.image,
              ),
              _buildDetailRow(
                'Videos',
                _lastScanResult!.videos.length,
                Icons.videocam,
              ),
              _buildDetailRow(
                'Audio Files',
                _lastScanResult!.audio.length,
                Icons.audiotrack,
              ),
              _buildDetailRow(
                'Documents',
                _lastScanResult!.documents.length,
                Icons.description,
              ),
              if (_lastScanResult!.others.isNotEmpty)
                _buildDetailRow(
                  'Other Files',
                  _lastScanResult!.others.length,
                  Icons.insert_drive_file,
                ),
              const Divider(),
              _buildDetailRow(
                'Total Files',
                _lastScanResult!.totalFiles,
                Icons.folder,
                isTotal: true,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _performScan(); // Rescan
            },
            child: const Text('Rescan'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    int count,
    IconData icon, {
    bool isTotal = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
          Text(
            count.toString(),
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: isTotal ? Theme.of(context).colorScheme.primary : null,
            ),
          ),
        ],
      ),
    );
  }
}
