

// Media File Model
class MediaFile {
  final String id;
  final String filePath;
  final String fileName;
  final MediaType mediaType;
  final MediaMetadata metadata;
  final Duration duration;
  final int fileSize;
  final DateTime lastModified;

  const MediaFile({
    required this.id,
    required this.filePath,
    required this.fileName,
    required this.mediaType,
    required this.metadata,
    required this.duration,
    required this.fileSize,
    required this.lastModified,
  });

  factory MediaFile.fromJson(Map<String, dynamic> json) {
    return MediaFile(
      id: json['id'] as String,
      filePath: json['file_path'] as String,
      fileName: json['file_name'] as String,
      mediaType: MediaType.values.firstWhere(
        (e) => e.name == json['media_type'],
        orElse: () => MediaType.audio,
      ),
      metadata: MediaMetadata.fromJson(json['metadata'] as Map<String, dynamic>),
      duration: Duration(milliseconds: json['duration_ms'] as int),
      fileSize: json['file_size'] as int,
      lastModified: DateTime.parse(json['last_modified'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'file_path': filePath,
      'file_name': fileName,
      'media_type': mediaType.name,
      'metadata': metadata.toJson(),
      'duration_ms': duration.inMilliseconds,
      'file_size': fileSize,
      'last_modified': lastModified.toIso8601String(),
    };
  }
}

// Media Metadata Model
class MediaMetadata {
  final String title;
  final String artist;
  final String album;
  final Duration duration;
  final int bitrate;
  final int sampleRate;
  final String format;
  final int fileSize;

  const MediaMetadata({
    required this.title,
    required this.artist,
    required this.album,
    required this.duration,
    required this.bitrate,
    required this.sampleRate,
    required this.format,
    required this.fileSize,
  });

  factory MediaMetadata.fromJson(Map<String, dynamic> json) {
    return MediaMetadata(
      title: json['title'] as String,
      artist: json['artist'] as String,
      album: json['album'] as String,
      duration: Duration(milliseconds: json['duration_ms'] as int),
      bitrate: json['bitrate'] as int,
      sampleRate: json['sample_rate'] as int,
      format: json['format'] as String,
      fileSize: json['file_size'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'artist': artist,
      'album': album,
      'duration_ms': duration.inMilliseconds,
      'bitrate': bitrate,
      'sample_rate': sampleRate,
      'format': format,
      'file_size': fileSize,
    };
  }
}

// Media Playlist Model
class MediaPlaylist {
  final String id;
  final String name;
  final String? description;
  final List<MediaFile> mediaFiles;
  final int currentIndex;
  final bool isShuffled;
  final RepeatMode repeatMode;
  final DateTime createdAt;
  final DateTime lastModified;

  const MediaPlaylist({
    required this.id,
    required this.name,
    this.description,
    required this.mediaFiles,
    required this.currentIndex,
    required this.isShuffled,
    required this.repeatMode,
    required this.createdAt,
    required this.lastModified,
  });

  factory MediaPlaylist.fromJson(Map<String, dynamic> json) {
    return MediaPlaylist(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      mediaFiles: (json['media_files'] as List<dynamic>)
          .map((e) => MediaFile.fromJson(e as Map<String, dynamic>))
          .toList(),
      currentIndex: json['current_index'] as int,
      isShuffled: json['is_shuffled'] as bool,
      repeatMode: RepeatMode.values.firstWhere(
        (e) => e.name == json['repeat_mode'],
        orElse: () => RepeatMode.none,
      ),
      createdAt: DateTime.parse(json['created_at'] as String),
      lastModified: DateTime.parse(json['last_modified'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'media_files': mediaFiles.map((e) => e.toJson()).toList(),
      'current_index': currentIndex,
      'is_shuffled': isShuffled,
      'repeat_mode': repeatMode.name,
      'created_at': createdAt.toIso8601String(),
      'last_modified': lastModified.toIso8601String(),
    };
  }

  MediaFile? get currentMedia {
    if (currentIndex >= 0 && currentIndex < mediaFiles.length) {
      return mediaFiles[currentIndex];
    }
    return null;
  }

  Duration get totalDuration {
    return mediaFiles.fold(
      Duration.zero,
      (total, media) => total + media.duration,
    );
  }

  int get totalFiles => mediaFiles.length;
}

// Media Player Event Model
class MediaPlayerEvent {
  final MediaEventType type;
  final MediaFile? mediaFile;
  final Duration? position;
  final double? volume;
  final bool? isMuted;
  final DateTime timestamp;

  const MediaPlayerEvent({
    required this.type,
    this.mediaFile,
    this.position,
    this.volume,
    this.isMuted,
    required this.timestamp,
  });
}

// Album Model
class Album {
  final String id;
  final String name;
  final String artist;
  final List<MediaFile> tracks;
  final String? coverArtPath;
  final int year;
  final String genre;
  final Duration totalDuration;

  const Album({
    required this.id,
    required this.name,
    required this.artist,
    required this.tracks,
    this.coverArtPath,
    required this.year,
    required this.genre,
    required this.totalDuration,
  });

  int get trackCount => tracks.length;
}

// Artist Model
class Artist {
  final String id;
  final String name;
  final List<Album> albums;
  final List<MediaFile> tracks;
  final String? biography;
  final String? imageUrl;

  const Artist({
    required this.id,
    required this.name,
    required this.albums,
    required this.tracks,
    this.biography,
    this.imageUrl,
  });

  int get albumCount => albums.length;
  int get trackCount => tracks.length;
}

// Genre Model
class Genre {
  final String id;
  final String name;
  final List<MediaFile> tracks;
  final List<Album> albums;
  final List<Artist> artists;

  const Genre({
    required this.id,
    required this.name,
    required this.tracks,
    required this.albums,
    required this.artists,
  });

  int get trackCount => tracks.length;
  int get albumCount => albums.length;
  int get artistCount => artists.length;
}

// Equalizer Preset Model
class EqualizerPreset {
  final String id;
  final String name;
  final List<double> frequencies;
  final List<double> gains;
  final bool isCustom;

  const EqualizerPreset({
    required this.id,
    required this.name,
    required this.frequencies,
    required this.gains,
    required this.isCustom,
  });

  factory EqualizerPreset.fromJson(Map<String, dynamic> json) {
    return EqualizerPreset(
      id: json['id'] as String,
      name: json['name'] as String,
      frequencies: List<double>.from(json['frequencies'] as List),
      gains: List<double>.from(json['gains'] as List),
      isCustom: json['is_custom'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'frequencies': frequencies,
      'gains': gains,
      'is_custom': isCustom,
    };
  }
}

// Media Library Statistics Model
class MediaLibraryStats {
  final int totalFiles;
  final int audioFiles;
  final int videoFiles;
  final int imageFiles;
  final Duration totalDuration;
  final int totalSize;
  final int totalArtists;
  final int totalAlbums;
  final int totalGenres;

  const MediaLibraryStats({
    required this.totalFiles,
    required this.audioFiles,
    required this.videoFiles,
    required this.imageFiles,
    required this.totalDuration,
    required this.totalSize,
    required this.totalArtists,
    required this.totalAlbums,
    required this.totalGenres,
  });
}

// Enums
enum MediaType { audio, video, image }
enum MediaPlayerState { stopped, playing, paused, buffering, error }
enum PlaybackMode { normal, shuffle, repeat }
enum RepeatMode { none, one, all }
enum MediaEventType {
  playbackStarted,
  playbackPaused,
  playbackResumed,
  playbackStopped,
  playbackCompleted,
  trackChanged,
  positionChanged,
  volumeChanged,
  muteToggled,
  playlistChanged,
  equalizerChanged,
}

// Extension methods
extension MediaTypeExtension on MediaType {
  String get displayName {
    switch (this) {
      case MediaType.audio:
        return 'Audio';
      case MediaType.video:
        return 'Video';
      case MediaType.image:
        return 'Image';
    }
  }

  List<String> get supportedExtensions {
    switch (this) {
      case MediaType.audio:
        return ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma'];
      case MediaType.video:
        return ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm'];
      case MediaType.image:
        return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
    }
  }
}

extension MediaPlayerStateExtension on MediaPlayerState {
  String get displayName {
    switch (this) {
      case MediaPlayerState.stopped:
        return 'Stopped';
      case MediaPlayerState.playing:
        return 'Playing';
      case MediaPlayerState.paused:
        return 'Paused';
      case MediaPlayerState.buffering:
        return 'Buffering';
      case MediaPlayerState.error:
        return 'Error';
    }
  }

  bool get isPlaying => this == MediaPlayerState.playing;
  bool get isPaused => this == MediaPlayerState.paused;
  bool get isStopped => this == MediaPlayerState.stopped;
  bool get isBuffering => this == MediaPlayerState.buffering;
  bool get hasError => this == MediaPlayerState.error;
}
