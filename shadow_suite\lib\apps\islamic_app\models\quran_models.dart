

// Quran Surah Model
class QuranSurah {
  final int number;
  final String nameArabic;
  final String nameEnglish;
  final String nameTransliteration;
  final int versesCount;
  final RevelationType revelationType;
  final int revelationOrder;
  final String meaning;
  final String description;

  const Quran<PERSON><PERSON><PERSON>({
    required this.number,
    required this.nameArabic,
    required this.nameEnglish,
    required this.nameTransliteration,
    required this.versesCount,
    required this.revelationType,
    required this.revelationOrder,
    required this.meaning,
    required this.description,
  });

  factory QuranSurah.fromJson(Map<String, dynamic> json) {
    return QuranSurah(
      number: json['number'] as int,
      nameArabic: json['name_arabic'] as String,
      nameEnglish: json['name_english'] as String,
      nameTransliteration: json['name_transliteration'] as String,
      versesCount: json['verses_count'] as int,
      revelationType: RevelationType.values.firstWhere(
        (e) => e.name == json['revelation_type'],
        orElse: () => RevelationType.meccan,
      ),
      revelationOrder: json['revelation_order'] as int,
      meaning: json['meaning'] as String,
      description: json['description'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'name_arabic': nameArabic,
      'name_english': nameEnglish,
      'name_transliteration': nameTransliteration,
      'verses_count': versesCount,
      'revelation_type': revelationType.name,
      'revelation_order': revelationOrder,
      'meaning': meaning,
      'description': description,
    };
  }
}

// Quran Verse Model
class QuranVerse {
  final int surahNumber;
  final int verseNumber;
  final String arabicText;
  final String transliteration;
  final int wordCount;
  final int letterCount;
  final List<String> keywords;
  final bool isSajdah;
  final int hizbNumber;
  final int juzNumber;
  final int rukuNumber;

  const QuranVerse({
    required this.surahNumber,
    required this.verseNumber,
    required this.arabicText,
    required this.transliteration,
    required this.wordCount,
    required this.letterCount,
    required this.keywords,
    required this.isSajdah,
    required this.hizbNumber,
    required this.juzNumber,
    required this.rukuNumber,
  });

  factory QuranVerse.fromJson(Map<String, dynamic> json) {
    return QuranVerse(
      surahNumber: json['surah_number'] as int,
      verseNumber: json['verse_number'] as int,
      arabicText: json['arabic_text'] as String,
      transliteration: json['transliteration'] as String,
      wordCount: json['word_count'] as int,
      letterCount: json['letter_count'] as int,
      keywords: List<String>.from(json['keywords'] as List? ?? []),
      isSajdah: json['is_sajdah'] as bool,
      hizbNumber: json['hizb_number'] as int,
      juzNumber: json['juz_number'] as int,
      rukuNumber: json['ruku_number'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'surah_number': surahNumber,
      'verse_number': verseNumber,
      'arabic_text': arabicText,
      'transliteration': transliteration,
      'word_count': wordCount,
      'letter_count': letterCount,
      'keywords': keywords,
      'is_sajdah': isSajdah,
      'hizb_number': hizbNumber,
      'juz_number': juzNumber,
      'ruku_number': rukuNumber,
    };
  }
}

// Quran Translation Model
class QuranTranslation {
  final String id;
  final String name;
  final String language;
  final String translator;
  final bool isDefault;
  final bool isOffline;
  final int downloadSize;
  final DateTime createdAt;

  const QuranTranslation({
    required this.id,
    required this.name,
    required this.language,
    required this.translator,
    required this.isDefault,
    required this.isOffline,
    required this.downloadSize,
    required this.createdAt,
  });

  factory QuranTranslation.fromJson(Map<String, dynamic> json) {
    return QuranTranslation(
      id: json['id'] as String,
      name: json['name'] as String,
      language: json['language'] as String,
      translator: json['translator'] as String,
      isDefault: json['is_default'] as bool,
      isOffline: json['is_offline'] as bool,
      downloadSize: json['download_size'] as int,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'language': language,
      'translator': translator,
      'is_default': isDefault,
      'is_offline': isOffline,
      'download_size': downloadSize,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Tafseer Entry Model
class TafseerEntry {
  final String id;
  final int surahNumber;
  final int verseNumber;
  final String source;
  final String language;
  final String commentary;
  final String scholar;
  final bool isVerified;
  final DateTime createdAt;

  const TafseerEntry({
    required this.id,
    required this.surahNumber,
    required this.verseNumber,
    required this.source,
    required this.language,
    required this.commentary,
    required this.scholar,
    required this.isVerified,
    required this.createdAt,
  });

  factory TafseerEntry.fromJson(Map<String, dynamic> json) {
    return TafseerEntry(
      id: json['id'] as String,
      surahNumber: json['surah_number'] as int,
      verseNumber: json['verse_number'] as int,
      source: json['source'] as String,
      language: json['language'] as String,
      commentary: json['commentary'] as String,
      scholar: json['scholar'] as String,
      isVerified: json['is_verified'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'surah_number': surahNumber,
      'verse_number': verseNumber,
      'source': source,
      'language': language,
      'commentary': commentary,
      'scholar': scholar,
      'is_verified': isVerified,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Quran Search Result Model
class QuranSearchResult {
  final int surahNumber;
  final int verseNumber;
  final String arabicText;
  final String? translationText;
  final String matchedText;
  final double relevanceScore;

  const QuranSearchResult({
    required this.surahNumber,
    required this.verseNumber,
    required this.arabicText,
    this.translationText,
    required this.matchedText,
    required this.relevanceScore,
  });
}

// Quran Reading Progress Model
class QuranReadingProgress {
  final String id;
  final String userId;
  final int surahNumber;
  final int verseNumber;
  final DateTime lastReadAt;
  final int totalReadingTime;
  final bool isCompleted;
  final Map<String, dynamic> notes;

  const QuranReadingProgress({
    required this.id,
    required this.userId,
    required this.surahNumber,
    required this.verseNumber,
    required this.lastReadAt,
    required this.totalReadingTime,
    required this.isCompleted,
    required this.notes,
  });

  factory QuranReadingProgress.fromJson(Map<String, dynamic> json) {
    return QuranReadingProgress(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      surahNumber: json['surah_number'] as int,
      verseNumber: json['verse_number'] as int,
      lastReadAt: DateTime.parse(json['last_read_at'] as String),
      totalReadingTime: json['total_reading_time'] as int,
      isCompleted: json['is_completed'] as bool,
      notes: Map<String, dynamic>.from(json['notes'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'surah_number': surahNumber,
      'verse_number': verseNumber,
      'last_read_at': lastReadAt.toIso8601String(),
      'total_reading_time': totalReadingTime,
      'is_completed': isCompleted,
      'notes': notes,
    };
  }
}

// Quran Bookmark Model
class QuranBookmark {
  final String id;
  final String userId;
  final int surahNumber;
  final int verseNumber;
  final String title;
  final String? note;
  final List<String> tags;
  final DateTime createdAt;

  const QuranBookmark({
    required this.id,
    required this.userId,
    required this.surahNumber,
    required this.verseNumber,
    required this.title,
    this.note,
    required this.tags,
    required this.createdAt,
  });

  factory QuranBookmark.fromJson(Map<String, dynamic> json) {
    return QuranBookmark(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      surahNumber: json['surah_number'] as int,
      verseNumber: json['verse_number'] as int,
      title: json['title'] as String,
      note: json['note'] as String?,
      tags: List<String>.from(json['tags'] as List? ?? []),
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'surah_number': surahNumber,
      'verse_number': verseNumber,
      'title': title,
      'note': note,
      'tags': tags,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Quran Change Event Model
class QuranChangeEvent {
  final QuranChangeType type;
  final int? surahNumber;
  final int? verseNumber;
  final String? translationId;
  final DateTime timestamp;

  const QuranChangeEvent({
    required this.type,
    this.surahNumber,
    this.verseNumber,
    this.translationId,
    required this.timestamp,
  });
}

// Enums
enum RevelationType { meccan, medinan }
enum QuranChangeType { surahChanged, verseChanged, translationChanged, bookmarkAdded, progressUpdated }
