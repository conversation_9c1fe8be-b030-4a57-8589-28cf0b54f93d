import 'package:flutter/foundation.dart';
import 'dart:async';
import 'dart:io';

class ErrorHandler {
  static final List<ErrorLog> _errorLogs = [];
  static StreamController<ErrorNotification>? _errorStreamController;
  
  // Initialize error handling system
  static void initialize() {
    _errorStreamController = StreamController<ErrorNotification>.broadcast();
    
    // Set up global error handling
    FlutterError.onError = (FlutterErrorDetails details) {
      handleFlutterError(details);
    };
    
    // Handle errors outside of Flutter
    PlatformDispatcher.instance.onError = (error, stack) {
      handleError(error, stack, ErrorType.platform);
      return true;
    };
  }
  
  // Handle Flutter framework errors
  static void handleFlutterError(FlutterErrorDetails details) {
    final error = details.exception;
    final stackTrace = details.stack;
    final context = details.context?.toString() ?? 'Unknown context';
    
    _logError(ErrorLog(
      error: error,
      stackTrace: stackTrace,
      type: ErrorType.flutter,
      context: context,
      timestamp: DateTime.now(),
      severity: _determineSeverity(error),
    ));
    
    if (kDebugMode) {
      FlutterError.presentError(details);
    }
    
    _notifyError(ErrorNotification(
      message: _getUserFriendlyMessage(error),
      type: ErrorType.flutter,
      severity: _determineSeverity(error),
    ));
  }
  
  // Handle general errors with context
  static void handleError(
    dynamic error, 
    StackTrace? stackTrace, 
    ErrorType type, {
    String? context,
    Map<String, dynamic>? metadata,
  }) {
    try {
      final errorLog = ErrorLog(
        error: error,
        stackTrace: stackTrace,
        type: type,
        context: context ?? 'Unknown',
        timestamp: DateTime.now(),
        severity: _determineSeverity(error),
        metadata: metadata,
      );
      
      _logError(errorLog);
      
      if (kDebugMode) {
        debugPrint('Error [${type.name}]: $error');
        if (stackTrace != null) {
          debugPrint('Stack trace: $stackTrace');
        }
        if (metadata != null) {
          debugPrint('Metadata: $metadata');
        }
      }
      
      _notifyError(ErrorNotification(
        message: _getUserFriendlyMessage(error),
        type: type,
        severity: _determineSeverity(error),
        context: context,
      ));
      
    } catch (e) {
      // Fallback error handling
      if (kDebugMode) {
        debugPrint('Error in error handler: $e');
        debugPrint('Original error: $error');
      }
    }
  }
  
  // Handle network errors specifically
  static void handleNetworkError(dynamic error, {String? operation}) {
    String userMessage;
    
    if (error is SocketException) {
      userMessage = 'No internet connection. Please check your network settings.';
    } else if (error is TimeoutException) {
      userMessage = 'Request timed out. Please try again.';
    } else if (error is HttpException) {
      userMessage = 'Server error. Please try again later.';
    } else {
      userMessage = 'Network error occurred. Please check your connection.';
    }
    
    handleError(error, null, ErrorType.network, 
      context: operation,
      metadata: {'user_message': userMessage}
    );
  }
  
  // Handle database errors
  static void handleDatabaseError(dynamic error, {String? operation, String? table}) {
    String userMessage;
    
    if (error.toString().contains('UNIQUE constraint')) {
      userMessage = 'This item already exists. Please use a different name.';
    } else if (error.toString().contains('NOT NULL constraint')) {
      userMessage = 'Required information is missing. Please fill all required fields.';
    } else if (error.toString().contains('FOREIGN KEY constraint')) {
      userMessage = 'Cannot delete this item as it is being used elsewhere.';
    } else {
      userMessage = 'Database error occurred. Please try again.';
    }
    
    handleError(error, null, ErrorType.database,
      context: operation,
      metadata: {
        'table': table,
        'user_message': userMessage,
      }
    );
  }
  
  // Handle validation errors
  static void handleValidationError(String field, String message, {dynamic value}) {
    final error = ValidationException(field, message, value);
    
    handleError(error, null, ErrorType.validation,
      context: 'Field validation: $field',
      metadata: {
        'field': field,
        'value': value?.toString(),
        'user_message': message,
      }
    );
  }
  
  // Handle permission errors
  static void handlePermissionError(String permission, {String? feature}) {
    final userMessage = 'Permission required: $permission. Please grant permission in settings.';
    
    handleError(
      PermissionException(permission, feature),
      null,
      ErrorType.permission,
      context: feature,
      metadata: {'user_message': userMessage}
    );
  }
  
  // Safe execution wrapper
  static Future<T?> safeExecute<T>(
    Future<T> Function() operation, {
    String? operationName,
    T? fallbackValue,
    bool showUserError = true,
  }) async {
    try {
      return await operation();
    } catch (error, stackTrace) {
      handleError(error, stackTrace, ErrorType.operation,
        context: operationName ?? 'Safe execution',
      );
      
      if (showUserError && _errorStreamController != null) {
        _notifyError(ErrorNotification(
          message: _getUserFriendlyMessage(error),
          type: ErrorType.operation,
          severity: _determineSeverity(error),
          context: operationName,
        ));
      }
      
      return fallbackValue;
    }
  }
  
  // Safe synchronous execution wrapper
  static T? safeExecuteSync<T>(
    T Function() operation, {
    String? operationName,
    T? fallbackValue,
    bool showUserError = true,
  }) {
    try {
      return operation();
    } catch (error, stackTrace) {
      handleError(error, stackTrace, ErrorType.operation,
        context: operationName ?? 'Safe sync execution',
      );
      
      if (showUserError && _errorStreamController != null) {
        _notifyError(ErrorNotification(
          message: _getUserFriendlyMessage(error),
          type: ErrorType.operation,
          severity: _determineSeverity(error),
          context: operationName,
        ));
      }
      
      return fallbackValue;
    }
  }
  
  // Get error stream for UI notifications
  static Stream<ErrorNotification>? get errorStream => _errorStreamController?.stream;
  
  // Get error logs for debugging
  static List<ErrorLog> getErrorLogs({ErrorType? type, ErrorSeverity? severity}) {
    var logs = List<ErrorLog>.from(_errorLogs);
    
    if (type != null) {
      logs = logs.where((log) => log.type == type).toList();
    }
    
    if (severity != null) {
      logs = logs.where((log) => log.severity == severity).toList();
    }
    
    return logs..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }
  
  // Clear error logs
  static void clearErrorLogs() {
    _errorLogs.clear();
  }
  
  // Export error logs for debugging
  static String exportErrorLogs() {
    final buffer = StringBuffer();
    buffer.writeln('Shadow Suite Error Log Export');
    buffer.writeln('Generated: ${DateTime.now().toIso8601String()}');
    buffer.writeln('Total Errors: ${_errorLogs.length}');
    buffer.writeln('=' * 50);
    buffer.writeln();
    
    for (final log in _errorLogs) {
      buffer.writeln('Timestamp: ${log.timestamp.toIso8601String()}');
      buffer.writeln('Type: ${log.type.name}');
      buffer.writeln('Severity: ${log.severity.name}');
      buffer.writeln('Context: ${log.context}');
      buffer.writeln('Error: ${log.error}');
      if (log.stackTrace != null) {
        buffer.writeln('Stack Trace:');
        buffer.writeln(log.stackTrace.toString());
      }
      if (log.metadata != null) {
        buffer.writeln('Metadata: ${log.metadata}');
      }
      buffer.writeln('-' * 30);
      buffer.writeln();
    }
    
    return buffer.toString();
  }
  
  // Private helper methods
  static void _logError(ErrorLog errorLog) {
    _errorLogs.add(errorLog);
    
    // Keep only last 100 errors to prevent memory issues
    if (_errorLogs.length > 100) {
      _errorLogs.removeRange(0, _errorLogs.length - 100);
    }
  }
  
  static void _notifyError(ErrorNotification notification) {
    _errorStreamController?.add(notification);
  }
  
  static ErrorSeverity _determineSeverity(dynamic error) {
    if (error is ValidationException || error is PermissionException) {
      return ErrorSeverity.warning;
    } else if (error is SocketException || error is TimeoutException) {
      return ErrorSeverity.error;
    } else if (error is OutOfMemoryError || error is StackOverflowError) {
      return ErrorSeverity.critical;
    } else {
      return ErrorSeverity.error;
    }
  }
  
  static String _getUserFriendlyMessage(dynamic error) {
    if (error is ValidationException) {
      return error.message;
    } else if (error is PermissionException) {
      return 'Permission required: ${error.permission}';
    } else if (error is SocketException) {
      return 'No internet connection available';
    } else if (error is TimeoutException) {
      return 'Operation timed out. Please try again';
    } else if (error is FormatException) {
      return 'Invalid data format';
    } else if (error is RangeError) {
      return 'Value out of valid range';
    } else if (error is ArgumentError) {
      return 'Invalid input provided';
    } else if (error.toString().contains('permission')) {
      return 'Permission denied. Please check app permissions';
    } else if (error.toString().contains('network') || error.toString().contains('connection')) {
      return 'Network connection error';
    } else {
      return 'An unexpected error occurred. Please try again';
    }
  }
  
  // Dispose resources
  static void dispose() {
    _errorStreamController?.close();
    _errorStreamController = null;
    _errorLogs.clear();
  }
}

// Error data models
class ErrorLog {
  final dynamic error;
  final StackTrace? stackTrace;
  final ErrorType type;
  final String context;
  final DateTime timestamp;
  final ErrorSeverity severity;
  final Map<String, dynamic>? metadata;

  const ErrorLog({
    required this.error,
    this.stackTrace,
    required this.type,
    required this.context,
    required this.timestamp,
    required this.severity,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'error': error.toString(),
      'stackTrace': stackTrace?.toString(),
      'type': type.name,
      'context': context,
      'timestamp': timestamp.toIso8601String(),
      'severity': severity.name,
      'metadata': metadata,
    };
  }
}

class ErrorNotification {
  final String message;
  final ErrorType type;
  final ErrorSeverity severity;
  final String? context;
  final DateTime timestamp;

  ErrorNotification({
    required this.message,
    required this.type,
    required this.severity,
    this.context,
  }) : timestamp = DateTime.now();
}

// Custom exceptions
class ValidationException implements Exception {
  final String field;
  final String message;
  final dynamic value;

  const ValidationException(this.field, this.message, this.value);

  @override
  String toString() => 'ValidationException: $field - $message (value: $value)';
}

class PermissionException implements Exception {
  final String permission;
  final String? feature;

  const PermissionException(this.permission, this.feature);

  @override
  String toString() => 'PermissionException: $permission${feature != null ? ' for $feature' : ''}';
}

class DatabaseException implements Exception {
  final String operation;
  final String? table;
  final dynamic originalError;

  const DatabaseException(this.operation, this.table, this.originalError);

  @override
  String toString() => 'DatabaseException: $operation${table != null ? ' on $table' : ''} - $originalError';
}

class NetworkException implements Exception {
  final String operation;
  final int? statusCode;
  final dynamic originalError;

  const NetworkException(this.operation, this.statusCode, this.originalError);

  @override
  String toString() => 'NetworkException: $operation${statusCode != null ? ' ($statusCode)' : ''} - $originalError';
}

// Enums
enum ErrorType {
  flutter,
  platform,
  network,
  database,
  validation,
  permission,
  operation,
  ui,
  audio,
  file,
  security,
}

enum ErrorSeverity {
  info,
  warning,
  error,
  critical,
}
