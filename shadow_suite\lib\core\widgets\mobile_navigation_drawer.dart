import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Mobile-optimized navigation drawer with hierarchical structure
class MobileNavigationDrawer extends ConsumerWidget {
  const MobileNavigationDrawer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentRoute = GoRouterState.of(context).uri.toString();

    return Drawer(
      child: Column(
        children: [
          _buildDrawerHeader(context, ref),
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildMainNavigationSection(context, currentRoute),
                const Divider(),
                _buildMiniAppsSection(context, currentRoute),
                const Divider(),
                _buildSettingsSection(context, currentRoute),
                const Divider(),
                _buildQuickActionsSection(context, ref),
              ],
            ),
          ),
          _buildFooter(context),
        ],
      ),
    );
  }

  Widget _buildDrawerHeader(BuildContext context, WidgetRef ref) {
    return DrawerHeader(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFF2C3E50), Color(0xFF3498DB)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const CircleAvatar(
            radius: 30,
            backgroundColor: Colors.white,
            child: Icon(Icons.dashboard, size: 30, color: Color(0xFF2C3E50)),
          ),
          const SizedBox(height: 12),
          const Text(
            'Shadow Suite',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            'Productivity & Islamic Apps',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainNavigationSection(
    BuildContext context,
    String currentRoute,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Main'),
        _buildNavigationTile(
          context,
          'Dashboard',
          Icons.dashboard,
          '/dashboard',
          currentRoute,
        ),
        _buildNavigationTile(
          context,
          'Profile',
          Icons.person,
          '/profile',
          currentRoute,
        ),
      ],
    );
  }

  Widget _buildMiniAppsSection(BuildContext context, String currentRoute) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Mini Apps'),
        _buildNavigationTile(
          context,
          'Money Manager',
          Icons.account_balance_wallet,
          '/money-manager',
          currentRoute,
          subtitle: 'Financial tracking & budgets',
        ),
        _buildNavigationTile(
          context,
          'File Manager',
          Icons.folder,
          '/file-manager',
          currentRoute,
          subtitle: 'File operations & cloud sync',
        ),
        _buildNavigationTile(
          context,
          'Tools Builder',
          Icons.build,
          '/excel-to-app',
          currentRoute,
          subtitle: 'Excel to app converter',
        ),
        _buildNavigationTile(
          context,
          'Islamic App',
          Icons.book,
          '/islamic-app',
          currentRoute,
          subtitle: 'Quran, Hadith & Prayer times',
        ),
        _buildNavigationTile(
          context,
          'Memo Suite',
          Icons.note,
          '/memo-suite',
          currentRoute,
          subtitle: 'Notes, todos & voice memos',
        ),
        _buildNavigationTile(
          context,
          'Shadow Player',
          Icons.play_circle,
          '/shadow-player',
          currentRoute,
          subtitle: 'Media player & library',
        ),
        _buildNavigationTile(
          context,
          'Smart Gallery+',
          Icons.photo_library,
          '/smart-gallery',
          currentRoute,
          subtitle: 'AI-powered photo management',
        ),
      ],
    );
  }

  Widget _buildSettingsSection(BuildContext context, String currentRoute) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Settings'),
        _buildNavigationTile(
          context,
          'App Settings',
          Icons.settings,
          '/settings',
          currentRoute,
        ),
        _buildNavigationTile(
          context,
          'Layout Settings',
          Icons.view_quilt,
          '/settings/layout',
          currentRoute,
        ),
        _buildNavigationTile(
          context,
          'Advanced Settings',
          Icons.tune,
          '/settings/advanced',
          currentRoute,
        ),
      ],
    );
  }

  Widget _buildQuickActionsSection(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Quick Actions'),
        ListTile(
          leading: const Icon(Icons.add_circle, color: Color(0xFF27AE60)),
          title: const Text('Add Transaction'),
          onTap: () {
            Navigator.of(context).pop();
            _showAddTransaction(context, ref);
          },
        ),
        ListTile(
          leading: const Icon(Icons.note_add, color: Color(0xFF3498DB)),
          title: const Text('Quick Note'),
          onTap: () {
            Navigator.of(context).pop();
            _showQuickNote(context, ref);
          },
        ),
        ListTile(
          leading: const Icon(Icons.mic, color: Color(0xFFE74C3C)),
          title: const Text('Voice Memo'),
          onTap: () {
            Navigator.of(context).pop();
            _showVoiceMemo(context, ref);
          },
        ),
        ListTile(
          leading: const Icon(Icons.search, color: Color(0xFF9B59B6)),
          title: const Text('Global Search'),
          onTap: () {
            Navigator.of(context).pop();
            _showGlobalSearch(context);
          },
        ),
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Color(0xFF7F8C8D),
        ),
      ),
    );
  }

  Widget _buildNavigationTile(
    BuildContext context,
    String title,
    IconData icon,
    String route,
    String currentRoute, {
    String? subtitle,
  }) {
    final isSelected = currentRoute.startsWith(route) && route != '/';

    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? Theme.of(context).colorScheme.primary : null,
      ),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          color: isSelected ? Theme.of(context).colorScheme.primary : null,
        ),
      ),
      subtitle: subtitle != null ? Text(subtitle) : null,
      selected: isSelected,
      selectedTileColor: Theme.of(
        context,
      ).colorScheme.primary.withValues(alpha: 0.1),
      onTap: () {
        Navigator.of(context).pop();
        if (route != currentRoute) {
          context.go(route);
        }
      },
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(color: Theme.of(context).dividerColor, width: 1),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                icon: const Icon(Icons.help_outline),
                onPressed: () => _showHelp(context),
                tooltip: 'Help',
              ),
              IconButton(
                icon: const Icon(Icons.info_outline),
                onPressed: () => _showAbout(context),
                tooltip: 'About',
              ),
              IconButton(
                icon: const Icon(Icons.feedback),
                onPressed: () => _showFeedback(context),
                tooltip: 'Feedback',
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Shadow Suite v1.0.0',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddTransaction(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => const AddTransactionDialog(),
    );
  }

  void _showQuickNote(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => const QuickNoteDialog(),
    );
  }

  void _showVoiceMemo(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => const VoiceMemoDialog(),
    );
  }

  void _showGlobalSearch(BuildContext context) {
    showSearch(context: context, delegate: GlobalSearchDelegate());
  }

  void _showHelp(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Help'),
        content: const Text('Help documentation will be implemented'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showAbout(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'Shadow Suite',
      applicationVersion: '1.0.0',
      applicationIcon: const Icon(Icons.dashboard, size: 48),
      children: [
        const Text('A comprehensive productivity and Islamic app suite.'),
      ],
    );
  }

  void _showFeedback(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Feedback'),
        content: const Text('Feedback system will be implemented'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

// Placeholder widgets that will be implemented in their respective modules
class AddTransactionDialog extends StatelessWidget {
  const AddTransactionDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Transaction'),
      content: const Text('Transaction dialog will be implemented'),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
      ],
    );
  }
}

class QuickNoteDialog extends StatelessWidget {
  const QuickNoteDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      padding: const EdgeInsets.all(16),
      child: const Center(child: Text('Quick note dialog will be implemented')),
    );
  }
}

class VoiceMemoDialog extends StatelessWidget {
  const VoiceMemoDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.4,
      padding: const EdgeInsets.all(16),
      child: const Center(child: Text('Voice memo dialog will be implemented')),
    );
  }
}

class GlobalSearchDelegate extends SearchDelegate<String> {
  @override
  List<Widget> buildActions(BuildContext context) => [
    IconButton(icon: const Icon(Icons.clear), onPressed: () => query = ''),
  ];

  @override
  Widget buildLeading(BuildContext context) => IconButton(
    icon: const Icon(Icons.arrow_back),
    onPressed: () => close(context, ''),
  );

  @override
  Widget buildResults(BuildContext context) =>
      const Center(child: Text('Search results will be implemented'));

  @override
  Widget buildSuggestions(BuildContext context) =>
      const Center(child: Text('Search suggestions will be implemented'));
}
