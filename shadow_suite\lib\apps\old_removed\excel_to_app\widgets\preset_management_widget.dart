import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/preset_template_service.dart';

/// Comprehensive preset management widget with templates and categories
class PresetManagementWidget extends StatefulWidget {
  const PresetManagementWidget({super.key});

  @override
  State<PresetManagementWidget> createState() => _PresetManagementWidgetState();
}

class _PresetManagementWidgetState extends State<PresetManagementWidget>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  String? _selectedCategoryId;
  bool _showFeaturedOnly = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    PresetTemplateService.initialize();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          Expanded(
            child: Tab<PERSON>arView(
              controller: _tabController,
              children: [
                _buildBrowseTab(),
                _buildCategoriesTab(),
                _buildManageTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          const Icon(Icons.bookmark, size: 24),
          const SizedBox(width: 8),
          Text(
            'Preset Templates',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const Spacer(),
          IconButton(
            onPressed: _showCreateTemplateDialog,
            icon: const Icon(Icons.add),
            tooltip: 'Create Template',
          ),
          IconButton(
            onPressed: _showImportDialog,
            icon: const Icon(Icons.upload),
            tooltip: 'Import Templates',
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return TabBar(
      controller: _tabController,
      tabs: const [
        Tab(icon: Icon(Icons.explore), text: 'Browse'),
        Tab(icon: Icon(Icons.category), text: 'Categories'),
        Tab(icon: Icon(Icons.settings), text: 'Manage'),
      ],
    );
  }

  Widget _buildBrowseTab() {
    return Column(
      children: [
        _buildSearchAndFilters(),
        Expanded(child: _buildTemplateGrid()),
      ],
    );
  }

  Widget _buildSearchAndFilters() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          TextField(
            decoration: const InputDecoration(
              labelText: 'Search Templates',
              hintText: 'Search by name, description, or tags...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) => setState(() => _searchQuery = value),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String?>(
                  value: _selectedCategoryId,
                  decoration: const InputDecoration(
                    labelText: 'Category',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    const DropdownMenuItem<String?>(
                      value: null,
                      child: Text('All Categories'),
                    ),
                    ...PresetTemplateService.categories.map((category) {
                      return DropdownMenuItem<String?>(
                        value: category.id,
                        child: Row(
                          children: [
                            Icon(
                              category.icon,
                              size: 16,
                              color: category.color,
                            ),
                            const SizedBox(width: 8),
                            Text(category.name),
                          ],
                        ),
                      );
                    }),
                  ],
                  onChanged: (value) =>
                      setState(() => _selectedCategoryId = value),
                ),
              ),
              const SizedBox(width: 16),
              FilterChip(
                label: const Text('Featured'),
                selected: _showFeaturedOnly,
                onSelected: (value) =>
                    setState(() => _showFeaturedOnly = value),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateGrid() {
    List<PresetTemplate> templates = PresetTemplateService.templates;

    // Apply filters
    if (_searchQuery.isNotEmpty) {
      templates = PresetTemplateService.searchTemplates(_searchQuery);
    }

    if (_selectedCategoryId != null) {
      templates = templates
          .where((t) => t.categoryId == _selectedCategoryId)
          .toList();
    }

    if (_showFeaturedOnly) {
      templates = templates.where((t) => t.isFeatured).toList();
    }

    if (templates.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No templates found'),
            Text('Try adjusting your search or filters'),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.2,
      ),
      itemCount: templates.length,
      itemBuilder: (context, index) {
        return _buildTemplateCard(templates[index]);
      },
    );
  }

  Widget _buildTemplateCard(PresetTemplate template) {
    final category = PresetTemplateService.categories.firstWhere(
      (c) => c.id == template.categoryId,
    );

    return Card(
      elevation: 4,
      child: InkWell(
        onTap: () => _applyTemplate(template),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(category.icon, color: category.color, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      template.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (template.isFeatured)
                    const Icon(Icons.star, color: Colors.amber, size: 16),
                  PopupMenuButton<String>(
                    onSelected: (action) =>
                        _handleTemplateAction(action, template),
                    itemBuilder: (context) => [
                      const PopupMenuItem(value: 'apply', child: Text('Apply')),
                      const PopupMenuItem(
                        value: 'preview',
                        child: Text('Preview'),
                      ),
                      const PopupMenuItem(
                        value: 'export',
                        child: Text('Export'),
                      ),
                      if (!template.isBuiltIn)
                        const PopupMenuItem(
                          value: 'delete',
                          child: Text('Delete'),
                        ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Expanded(
                child: Text(
                  template.description,
                  style: const TextStyle(fontSize: 12),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 4,
                children: template.tags.take(3).map((tag) {
                  return Chip(
                    label: Text(tag, style: const TextStyle(fontSize: 10)),
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  );
                }).toList(),
              ),
              const SizedBox(height: 4),
              Text(
                'by ${template.author}',
                style: const TextStyle(fontSize: 10, color: Colors.grey),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoriesTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: PresetTemplateService.categories.length,
      itemBuilder: (context, index) {
        final category = PresetTemplateService.categories[index];
        final templateCount = PresetTemplateService.getTemplatesByCategory(
          category.id,
        ).length;

        return Card(
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: category.color.withValues(alpha: 0.2),
              child: Icon(category.icon, color: category.color),
            ),
            title: Text(category.name),
            subtitle: Text('${category.description}\n$templateCount templates'),
            isThreeLine: true,
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              setState(() {
                _selectedCategoryId = category.id;
                _tabController.animateTo(0);
              });
            },
          ),
        );
      },
    );
  }

  Widget _buildManageTab() {
    final userTemplates = PresetTemplateService.templates
        .where((t) => !t.isBuiltIn)
        .toList();

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _exportAllTemplates,
                  icon: const Icon(Icons.download),
                  label: const Text('Export All'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _showImportDialog,
                  icon: const Icon(Icons.upload),
                  label: const Text('Import'),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: userTemplates.isEmpty
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.bookmark_border, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text('No custom templates'),
                      Text('Create your first template to get started'),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: userTemplates.length,
                  itemBuilder: (context, index) {
                    return _buildManageTemplateCard(userTemplates[index]);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildManageTemplateCard(PresetTemplate template) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        title: Text(template.name),
        subtitle: Text(template.description),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => _exportTemplate(template),
              icon: const Icon(Icons.download),
              tooltip: 'Export',
            ),
            IconButton(
              onPressed: () => _deleteTemplate(template),
              icon: const Icon(Icons.delete),
              tooltip: 'Delete',
            ),
          ],
        ),
        onTap: () => _applyTemplate(template),
      ),
    );
  }

  void _applyTemplate(PresetTemplate template) {
    PresetTemplateService.applyTemplate(template);
    _showSnackBar(
      'Template "${template.name}" applied successfully',
      Colors.green,
    );
  }

  void _handleTemplateAction(String action, PresetTemplate template) {
    switch (action) {
      case 'apply':
        _applyTemplate(template);
        break;
      case 'preview':
        _showPreviewDialog(template);
        break;
      case 'export':
        _exportTemplate(template);
        break;
      case 'delete':
        _deleteTemplate(template);
        break;
    }
  }

  void _showCreateTemplateDialog() {
    // Implementation for create template dialog
    _showSnackBar('Create template dialog would open here', Colors.blue);
  }

  void _showImportDialog() {
    // Implementation for import dialog
    _showSnackBar('Import dialog would open here', Colors.blue);
  }

  void _showPreviewDialog(PresetTemplate template) {
    // Implementation for preview dialog
    _showSnackBar(
      'Preview for "${template.name}" would show here',
      Colors.blue,
    );
  }

  void _exportTemplate(PresetTemplate template) {
    final jsonString = PresetTemplateService.exportTemplate(template);
    Clipboard.setData(ClipboardData(text: jsonString));
    _showSnackBar('Template exported to clipboard', Colors.green);
  }

  void _exportAllTemplates() {
    final userTemplates = PresetTemplateService.templates
        .where((t) => !t.isBuiltIn)
        .toList();

    if (userTemplates.isEmpty) {
      _showSnackBar('No custom templates to export', Colors.orange);
      return;
    }

    final jsonString = PresetTemplateService.exportTemplates(userTemplates);
    Clipboard.setData(ClipboardData(text: jsonString));
    _showSnackBar(
      '${userTemplates.length} templates exported to clipboard',
      Colors.green,
    );
  }

  void _deleteTemplate(PresetTemplate template) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Template'),
        content: Text('Are you sure you want to delete "${template.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              if (PresetTemplateService.deleteTemplate(template.id)) {
                setState(() {});
                _showSnackBar('Template deleted successfully', Colors.green);
              } else {
                _showSnackBar('Failed to delete template', Colors.red);
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
