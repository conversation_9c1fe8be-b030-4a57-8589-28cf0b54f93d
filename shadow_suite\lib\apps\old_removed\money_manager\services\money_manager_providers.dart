import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/money_manager_models.dart';
import 'money_manager_database.dart';

// Database Provider
final moneyManagerDatabaseProvider = Provider<MoneyManagerDatabase>((ref) {
  return MoneyManagerDatabase();
});

// Global refresh trigger for cross-provider updates
final refreshTriggerProvider = StateProvider<int>((ref) => 0);

// Account Providers
final accountsProvider = StateNotifierProvider<AccountsNotifier, AsyncValue<List<Account>>>((ref) {
  return AccountsNotifier();
});

final selectedAccountProvider = StateProvider<Account?>((ref) => null);

class AccountsNotifier extends StateNotifier<AsyncValue<List<Account>>> {
  AccountsNotifier() : super(const AsyncValue.loading()) {
    loadAccounts();
  }

  void refreshFromExternal() {
    loadAccounts();
  }

  Future<void> loadAccounts() async {
    try {
      state = const AsyncValue.loading();
      final accounts = await MoneyManagerDatabase.getAccounts();
      state = AsyncValue.data(accounts);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addAccount(Account account) async {
    try {
      await MoneyManagerDatabase.saveAccount(account);
      // Optimized real-time update: add to existing list instead of full reload
      state.whenData((accounts) {
        final updatedAccounts = [...accounts, account];
        state = AsyncValue.data(updatedAccounts);
      });
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateAccount(Account account) async {
    try {
      await MoneyManagerDatabase.saveAccount(account);
      // Optimized real-time update: update specific item in list
      state.whenData((accounts) {
        final updatedAccounts = accounts.map((a) => a.id == account.id ? account : a).toList();
        state = AsyncValue.data(updatedAccounts);
      });
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteAccount(String id) async {
    try {
      await MoneyManagerDatabase.deleteAccount(id);
      // Optimized real-time update: remove from existing list
      state.whenData((accounts) {
        final updatedAccounts = accounts.where((a) => a.id != id).toList();
        state = AsyncValue.data(updatedAccounts);
      });
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateAccountBalance(String accountId, double newBalance) async {
    try {
      final account = await MoneyManagerDatabase.getAccount(accountId);
      if (account != null) {
        final updatedAccount = account.copyWith(
          currentBalance: newBalance,
          lastModified: DateTime.now(),
        );
        await MoneyManagerDatabase.saveAccount(updatedAccount);
        await loadAccounts();
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Category Providers
final categoriesProvider = StateNotifierProvider<CategoriesNotifier, AsyncValue<List<Category>>>((ref) {
  return CategoriesNotifier();
});

final selectedCategoryProvider = StateProvider<Category?>((ref) => null);

final incomeCategoriesProvider = Provider<AsyncValue<List<Category>>>((ref) {
  final categoriesAsync = ref.watch(categoriesProvider);
  return categoriesAsync.when(
    data: (categories) => AsyncValue.data(
      categories.where((cat) => cat.type == CategoryType.income).toList(),
    ),
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

final expenseCategoriesProvider = Provider<AsyncValue<List<Category>>>((ref) {
  final categoriesAsync = ref.watch(categoriesProvider);
  return categoriesAsync.when(
    data: (categories) => AsyncValue.data(
      categories.where((cat) => cat.type == CategoryType.expense).toList(),
    ),
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

class CategoriesNotifier extends StateNotifier<AsyncValue<List<Category>>> {
  CategoriesNotifier() : super(const AsyncValue.loading()) {
    loadCategories();
  }

  Future<void> loadCategories() async {
    try {
      state = const AsyncValue.loading();
      final categories = await MoneyManagerDatabase.getCategories();
      state = AsyncValue.data(categories);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addCategory(Category category) async {
    try {
      await MoneyManagerDatabase.saveCategory(category);
      // Optimized real-time update
      state.whenData((categories) {
        final updatedCategories = [...categories, category];
        state = AsyncValue.data(updatedCategories);
      });
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateCategory(Category category) async {
    try {
      await MoneyManagerDatabase.saveCategory(category);
      // Optimized real-time update
      state.whenData((categories) {
        final updatedCategories = categories.map((c) => c.id == category.id ? category : c).toList();
        state = AsyncValue.data(updatedCategories);
      });
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteCategory(String id) async {
    try {
      await MoneyManagerDatabase.deleteCategory(id);
      // Optimized real-time update
      state.whenData((categories) {
        final updatedCategories = categories.where((c) => c.id != id).toList();
        state = AsyncValue.data(updatedCategories);
      });
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Transaction Providers
final transactionsProvider = StateNotifierProvider<TransactionsNotifier, AsyncValue<List<Transaction>>>((ref) {
  return TransactionsNotifier(ref);
});

final selectedTransactionProvider = StateProvider<Transaction?>((ref) => null);

final recentTransactionsProvider = Provider<AsyncValue<List<Transaction>>>((ref) {
  final transactionsAsync = ref.watch(transactionsProvider);
  return transactionsAsync.when(
    data: (transactions) {
      final sortedTransactions = List<Transaction>.from(transactions);
      sortedTransactions.sort((a, b) => b.date.compareTo(a.date));
      return AsyncValue.data(sortedTransactions.take(10).toList());
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

class TransactionsNotifier extends StateNotifier<AsyncValue<List<Transaction>>> {
  final Ref _ref;

  TransactionsNotifier(this._ref) : super(const AsyncValue.loading()) {
    loadTransactions();
  }

  Future<void> loadTransactions() async {
    try {
      state = const AsyncValue.loading();
      final transactions = await MoneyManagerDatabase.getTransactions();
      state = AsyncValue.data(transactions);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addTransaction(Transaction transaction) async {
    try {
      await MoneyManagerDatabase.saveTransaction(transaction);

      // Update account balances
      await _updateAccountBalances(transaction);

      // Trigger account provider refresh for real-time balance updates
      _ref.read(accountsProvider.notifier).refreshFromExternal();

      // Optimized real-time update: add to existing list
      state.whenData((transactions) {
        final updatedTransactions = [...transactions, transaction];
        // Sort by date descending for proper display
        updatedTransactions.sort((a, b) => b.date.compareTo(a.date));
        state = AsyncValue.data(updatedTransactions);
      });
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateTransaction(Transaction transaction) async {
    try {
      await MoneyManagerDatabase.saveTransaction(transaction);
      // Optimized real-time update
      state.whenData((transactions) {
        final updatedTransactions = transactions.map((t) => t.id == transaction.id ? transaction : t).toList();
        updatedTransactions.sort((a, b) => b.date.compareTo(a.date));
        state = AsyncValue.data(updatedTransactions);
      });
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteTransaction(String id) async {
    try {
      // Get the transaction before deleting to reverse account balance changes
      final transactionToDelete = state.value?.firstWhere((t) => t.id == id);

      await MoneyManagerDatabase.deleteTransaction(id);

      // Reverse account balance changes
      if (transactionToDelete != null) {
        await _reverseAccountBalances(transactionToDelete);

        // Trigger account provider refresh for real-time balance updates
        _ref.read(accountsProvider.notifier).refreshFromExternal();
      }

      // Optimized real-time update
      state.whenData((transactions) {
        final updatedTransactions = transactions.where((t) => t.id != id).toList();
        state = AsyncValue.data(updatedTransactions);
      });
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> _updateAccountBalances(Transaction transaction) async {
    final fromAccount = await MoneyManagerDatabase.getAccount(transaction.accountId);
    if (fromAccount != null) {
      double newBalance = fromAccount.currentBalance;
      
      switch (transaction.type) {
        case TransactionType.income:
          newBalance += transaction.amount;
          break;
        case TransactionType.expense:
          newBalance -= transaction.amount;
          break;
        case TransactionType.transfer:
          newBalance -= transaction.amount;
          
          // Update destination account for transfers
          if (transaction.toAccountId != null) {
            final toAccount = await MoneyManagerDatabase.getAccount(transaction.toAccountId!);
            if (toAccount != null) {
              final updatedToAccount = toAccount.copyWith(
                currentBalance: toAccount.currentBalance + transaction.amount,
                lastModified: DateTime.now(),
              );
              await MoneyManagerDatabase.saveAccount(updatedToAccount);
            }
          }
          break;
      }
      
      final updatedFromAccount = fromAccount.copyWith(
        currentBalance: newBalance,
        lastModified: DateTime.now(),
      );
      await MoneyManagerDatabase.saveAccount(updatedFromAccount);
    }
  }

  Future<void> _reverseAccountBalances(Transaction transaction) async {
    final fromAccount = await MoneyManagerDatabase.getAccount(transaction.accountId);
    if (fromAccount != null) {
      double newBalance = fromAccount.currentBalance;

      // Reverse the transaction effects
      switch (transaction.type) {
        case TransactionType.income:
          newBalance -= transaction.amount; // Reverse income
          break;
        case TransactionType.expense:
          newBalance += transaction.amount; // Reverse expense
          break;
        case TransactionType.transfer:
          newBalance += transaction.amount; // Reverse outgoing transfer

          // Reverse destination account for transfers
          if (transaction.toAccountId != null) {
            final toAccount = await MoneyManagerDatabase.getAccount(transaction.toAccountId!);
            if (toAccount != null) {
              final updatedToAccount = toAccount.copyWith(
                currentBalance: toAccount.currentBalance - transaction.amount,
                lastModified: DateTime.now(),
              );
              await MoneyManagerDatabase.saveAccount(updatedToAccount);
            }
          }
          break;
      }

      final updatedFromAccount = fromAccount.copyWith(
        currentBalance: newBalance,
        lastModified: DateTime.now(),
      );
      await MoneyManagerDatabase.saveAccount(updatedFromAccount);
    }
  }
}

// Budget Providers
final budgetsProvider = StateNotifierProvider<BudgetsNotifier, AsyncValue<List<Budget>>>((ref) {
  return BudgetsNotifier();
});

final selectedBudgetProvider = StateProvider<Budget?>((ref) => null);

class BudgetsNotifier extends StateNotifier<AsyncValue<List<Budget>>> {
  BudgetsNotifier() : super(const AsyncValue.loading()) {
    loadBudgets();
  }

  Future<void> loadBudgets() async {
    try {
      state = const AsyncValue.loading();
      final budgets = await MoneyManagerDatabase.getBudgets();
      state = AsyncValue.data(budgets);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addBudget(Budget budget) async {
    try {
      await MoneyManagerDatabase.saveBudget(budget);
      // Optimized real-time update
      state.whenData((budgets) {
        final updatedBudgets = [...budgets, budget];
        state = AsyncValue.data(updatedBudgets);
      });
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateBudget(Budget budget) async {
    try {
      await MoneyManagerDatabase.saveBudget(budget);
      // Optimized real-time update
      state.whenData((budgets) {
        final updatedBudgets = budgets.map((b) => b.id == budget.id ? budget : b).toList();
        state = AsyncValue.data(updatedBudgets);
      });
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteBudget(String id) async {
    try {
      await MoneyManagerDatabase.deleteBudget(id);
      // Optimized real-time update
      state.whenData((budgets) {
        final updatedBudgets = budgets.where((b) => b.id != id).toList();
        state = AsyncValue.data(updatedBudgets);
      });
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Goal Providers
final goalsProvider = StateNotifierProvider<GoalsNotifier, AsyncValue<List<Goal>>>((ref) {
  return GoalsNotifier();
});

final selectedGoalProvider = StateProvider<Goal?>((ref) => null);

class GoalsNotifier extends StateNotifier<AsyncValue<List<Goal>>> {
  GoalsNotifier() : super(const AsyncValue.loading()) {
    loadGoals();
  }

  Future<void> loadGoals() async {
    try {
      state = const AsyncValue.loading();
      final goals = await MoneyManagerDatabase.getGoals();
      state = AsyncValue.data(goals);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addGoal(Goal goal) async {
    try {
      await MoneyManagerDatabase.saveGoal(goal);
      // Optimized real-time update
      state.whenData((goals) {
        final updatedGoals = [...goals, goal];
        state = AsyncValue.data(updatedGoals);
      });
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateGoal(Goal goal) async {
    try {
      await MoneyManagerDatabase.saveGoal(goal);
      // Optimized real-time update
      state.whenData((goals) {
        final updatedGoals = goals.map((g) => g.id == goal.id ? goal : g).toList();
        state = AsyncValue.data(updatedGoals);
      });
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteGoal(String id) async {
    try {
      await MoneyManagerDatabase.deleteGoal(id);
      // Optimized real-time update
      state.whenData((goals) {
        final updatedGoals = goals.where((g) => g.id != id).toList();
        state = AsyncValue.data(updatedGoals);
      });
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Analytics Providers
final totalBalanceProvider = FutureProvider<double>((ref) async {
  return await MoneyManagerDatabase.getTotalBalance();
});

final accountBalancesProvider = FutureProvider<Map<String, double>>((ref) async {
  return await MoneyManagerDatabase.getAccountBalances();
});

// Filter and Search Providers
final transactionFilterProvider = StateProvider<TransactionFilter>((ref) => TransactionFilter());
final categoryFilterProvider = StateProvider<CategoryType?>((ref) => null);
final dateRangeFilterProvider = StateProvider<DateRange?>((ref) => null);

// Filter Models
class TransactionFilter {
  final String? accountId;
  final String? categoryId;
  final TransactionType? type;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? searchQuery;

  const TransactionFilter({
    this.accountId,
    this.categoryId,
    this.type,
    this.startDate,
    this.endDate,
    this.searchQuery,
  });

  TransactionFilter copyWith({
    String? accountId,
    String? categoryId,
    TransactionType? type,
    DateTime? startDate,
    DateTime? endDate,
    String? searchQuery,
  }) {
    return TransactionFilter(
      accountId: accountId ?? this.accountId,
      categoryId: categoryId ?? this.categoryId,
      type: type ?? this.type,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

class DateRange {
  final DateTime start;
  final DateTime end;

  const DateRange({required this.start, required this.end});
}
