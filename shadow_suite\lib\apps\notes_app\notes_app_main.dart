import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'screens/notes_dashboard.dart';
import 'screens/notes_list_screen.dart';

import 'screens/notes_search_screen.dart';
import 'screens/notes_settings_screen.dart';
import 'screens/text_note_editor_screen.dart';
import 'screens/checklist_note_editor_screen.dart';
import 'screens/canvas_note_editor_screen.dart';
import 'models/note_models.dart';

/// Main Notes Application
class NotesAppMain extends ConsumerStatefulWidget {
  const NotesAppMain({super.key});

  @override
  ConsumerState<NotesAppMain> createState() => _NotesAppMainState();
}

class _NotesAppMainState extends ConsumerState<NotesAppMain> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const NotesDashboard(),
    const NotesListScreen(),
    const NotesSearchScreen(),
    const NotesSettingsScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        selectedItemColor: Colors.amber,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.note), label: 'Notes'),
          BottomNavigationBarItem(icon: Icon(Icons.search), label: 'Search'),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'Settings',
          ),
        ],
      ),
      floatingActionButton: _selectedIndex < 2
          ? FloatingActionButton(
              onPressed: _showCreateNoteDialog,
              backgroundColor: Colors.amber,
              child: const Icon(Icons.add, color: Colors.white),
            )
          : null,
    );
  }

  void _showCreateNoteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create New Note'),
        contentPadding: const EdgeInsets.all(20),
        actionsPadding: const EdgeInsets.all(20),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Choose the type of note you want to create:'),
            const SizedBox(height: 20),
            _buildNoteTypeOption(
              'Text Note',
              'Create a simple text note with formatting',
              Icons.text_fields,
              Colors.blue,
              () => _createNote(NoteType.text),
            ),
            const SizedBox(height: 12),
            _buildNoteTypeOption(
              'Checklist',
              'Create a checklist with checkboxes',
              Icons.checklist,
              Colors.green,
              () => _createNote(NoteType.checklist),
            ),
            const SizedBox(height: 12),
            _buildNoteTypeOption(
              'Canvas',
              'Create a drawing or sketch note',
              Icons.brush,
              Colors.purple,
              () => _createNote(NoteType.canvas),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildNoteTypeOption(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: () {
        Navigator.pop(context);
        onTap();
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: Colors.grey[400], size: 16),
          ],
        ),
      ),
    );
  }

  void _createNote(NoteType type) {
    Widget screen;
    switch (type) {
      case NoteType.text:
        screen = const TextNoteEditorScreen(isNewNote: true);
        break;
      case NoteType.checklist:
        screen = const ChecklistNoteEditorScreen(isNewNote: true);
        break;
      case NoteType.canvas:
        screen = const CanvasNoteEditorScreen(isNewNote: true);
        break;
    }

    Navigator.push(context, MaterialPageRoute(builder: (context) => screen));
  }
}
