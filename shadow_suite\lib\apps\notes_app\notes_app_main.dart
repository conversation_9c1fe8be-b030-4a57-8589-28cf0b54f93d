import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'screens/notes_dashboard.dart';
import 'screens/notes_list_screen.dart';
import 'screens/note_editor_screen.dart';
import 'screens/notes_search_screen.dart';
import 'screens/notes_settings_screen.dart';

/// Main Notes Application
class NotesAppMain extends ConsumerStatefulWidget {
  const NotesAppMain({super.key});

  @override
  ConsumerState<NotesAppMain> createState() => _NotesAppMainState();
}

class _NotesAppMainState extends ConsumerState<NotesAppMain> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const NotesDashboard(),
    const NotesListScreen(),
    const NotesSearchScreen(),
    const NotesSettingsScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        selectedItemColor: Colors.amber,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.note),
            label: 'Notes',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.search),
            label: 'Search',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'Settings',
          ),
        ],
      ),
      floatingActionButton: _selectedIndex < 2
          ? FloatingActionButton(
              onPressed: () => _createNewNote(),
              backgroundColor: Colors.amber,
              child: const Icon(Icons.add, color: Colors.white),
            )
          : null,
    );
  }

  void _createNewNote() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const NoteEditorScreen(),
      ),
    );
  }
}
