import 'package:flutter/material.dart';
import '../services/excel_function_validator.dart';

/// Widget for testing and validating Excel functions
class FunctionTestingWidget extends StatefulWidget {
  const FunctionTestingWidget({super.key});

  @override
  State<FunctionTestingWidget> createState() => _FunctionTestingWidgetState();
}

class _FunctionTestingWidgetState extends State<FunctionTestingWidget> {
  ValidationReport? _lastReport;
  Map<String, PerformanceBenchmark>? _benchmarks;
  bool _isRunningTests = false;
  bool _isRunningBenchmarks = false;
  String _customFormula = '';
  String _expectedResult = '';

  @override
  void initState() {
    super.initState();
    ExcelFunctionValidator.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            _buildActionButtons(),
            const SizedBox(height: 24),
            _buildCustomTesting(),
            const SizedBox(height: 24),
            if (_lastReport != null) _buildTestResults(),
            if (_benchmarks != null) ...[
              const SizedBox(height: 24),
              _buildPerformanceResults(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Icon(Icons.science, size: 24),
        const SizedBox(width: 8),
        Text(
          'Function Testing & Validation',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const Spacer(),
        if (_isRunningTests || _isRunningBenchmarks)
          const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isRunningTests ? null : _runAllTests,
            icon: const Icon(Icons.play_arrow),
            label: const Text('Run All Tests'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isRunningBenchmarks ? null : _runBenchmarks,
            icon: const Icon(Icons.speed),
            label: const Text('Performance Test'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.secondary,
              foregroundColor: Theme.of(context).colorScheme.onSecondary,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _validateCoreIntegrity,
            icon: const Icon(Icons.verified),
            label: const Text('Core Check'),
          ),
        ),
      ],
    );
  }

  Widget _buildCustomTesting() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Custom Formula Testing',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              flex: 2,
              child: TextField(
                decoration: const InputDecoration(
                  labelText: 'Formula',
                  hintText: 'e.g., SUM(1,2,3)',
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) => _customFormula = value,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextField(
                decoration: const InputDecoration(
                  labelText: 'Expected Result',
                  hintText: 'e.g., 6',
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) => _expectedResult = value,
              ),
            ),
            const SizedBox(width: 16),
            ElevatedButton(
              onPressed: _testCustomFormula,
              child: const Text('Test'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTestResults() {
    final report = _lastReport!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Test Results', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 16),

        // Summary Cards
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'Total Tests',
                '${report.totalTests}',
                Colors.blue,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildSummaryCard(
                'Passed',
                '${report.passedTests}',
                Colors.green,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildSummaryCard(
                'Failed',
                '${report.failedTests}',
                Colors.red,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildSummaryCard(
                'Success Rate',
                '${(report.successRate * 100).toStringAsFixed(1)}%',
                report.successRate > 0.95 ? Colors.green : Colors.orange,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Detailed Results
        Container(
          height: 300,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ListView.builder(
            itemCount: report.results.length,
            itemBuilder: (context, index) {
              final result = report.results[index];
              return _buildTestResultTile(result);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, Color color) {
    return Card(
      color: color.withValues(alpha: 0.1),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestResultTile(ValidationResult result) {
    return ListTile(
      leading: Icon(
        result.passed ? Icons.check_circle : Icons.error,
        color: result.passed ? Colors.green : Colors.red,
      ),
      title: Text('${result.functionName} - ${result.testName}'),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Formula: ${result.formula}'),
          Text('Expected: ${result.expectedResult}'),
          Text('Actual: ${result.actualResult}'),
          if (result.errorMessage != null)
            Text(
              'Error: ${result.errorMessage}',
              style: const TextStyle(color: Colors.red),
            ),
        ],
      ),
      trailing: Text(
        '${result.executionTime.inMicroseconds}μs',
        style: TextStyle(
          color: result.executionTime.inMicroseconds < 100000
              ? Colors.green
              : Colors.orange,
          fontSize: 12,
        ),
      ),
      isThreeLine: true,
    );
  }

  Widget _buildPerformanceResults() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Performance Benchmarks',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 16),

        Container(
          height: 200,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ListView.builder(
            itemCount: _benchmarks!.length,
            itemBuilder: (context, index) {
              final entry = _benchmarks!.entries.elementAt(index);
              final benchmark = entry.value;
              return _buildBenchmarkTile(benchmark);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildBenchmarkTile(PerformanceBenchmark benchmark) {
    return ListTile(
      leading: Icon(
        benchmark.meetsPerformanceTarget ? Icons.speed : Icons.warning,
        color: benchmark.meetsPerformanceTarget ? Colors.green : Colors.orange,
      ),
      title: Text(benchmark.functionName),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Avg: ${benchmark.averageExecutionTime.inMicroseconds}μs'),
          Text('Min: ${benchmark.minExecutionTime.inMicroseconds}μs'),
          Text('Max: ${benchmark.maxExecutionTime.inMicroseconds}μs'),
        ],
      ),
      trailing: Text(
        '${benchmark.totalExecutions} runs',
        style: const TextStyle(fontSize: 12),
      ),
    );
  }

  Future<void> _runAllTests() async {
    setState(() => _isRunningTests = true);

    try {
      final report = await ExcelFunctionValidator.runAllTests();
      setState(() => _lastReport = report);

      _showSnackBar(
        'Tests completed: ${report.passedTests}/${report.totalTests} passed',
        report.successRate > 0.95 ? Colors.green : Colors.orange,
      );
    } catch (e) {
      _showSnackBar('Test execution failed: $e', Colors.red);
    } finally {
      setState(() => _isRunningTests = false);
    }
  }

  Future<void> _runBenchmarks() async {
    setState(() => _isRunningBenchmarks = true);

    try {
      final benchmarks =
          await ExcelFunctionValidator.getPerformanceBenchmarks();
      setState(() => _benchmarks = benchmarks);

      final slowFunctions = benchmarks.values
          .where((b) => !b.meetsPerformanceTarget)
          .length;

      _showSnackBar(
        'Benchmarks completed: ${benchmarks.length - slowFunctions}/${benchmarks.length} meet performance targets',
        slowFunctions == 0 ? Colors.green : Colors.orange,
      );
    } catch (e) {
      _showSnackBar('Benchmark execution failed: $e', Colors.red);
    } finally {
      setState(() => _isRunningBenchmarks = false);
    }
  }

  Future<void> _validateCoreIntegrity() async {
    try {
      final isValid = await ExcelFunctionValidator.validateCoreIntegrity();
      _showSnackBar(
        isValid
            ? 'Core functions are working correctly'
            : 'Core function validation failed',
        isValid ? Colors.green : Colors.red,
      );
    } catch (e) {
      _showSnackBar('Core validation failed: $e', Colors.red);
    }
  }

  Future<void> _testCustomFormula() async {
    if (_customFormula.isEmpty) {
      _showSnackBar('Please enter a formula', Colors.orange);
      return;
    }

    try {
      dynamic expectedValue = _expectedResult;

      // Try to parse as number if possible
      if (_expectedResult.isNotEmpty) {
        final numValue = double.tryParse(_expectedResult);
        if (numValue != null) {
          expectedValue = numValue;
        }
      }

      final result = await ExcelFunctionValidator.validateFormula(
        _customFormula,
        expectedValue,
      );

      _showSnackBar(
        result.passed
            ? 'Formula test passed: ${result.actualResult}'
            : 'Formula test failed: ${result.errorMessage}',
        result.passed ? Colors.green : Colors.red,
      );
    } catch (e) {
      _showSnackBar('Custom test failed: $e', Colors.red);
    }
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
