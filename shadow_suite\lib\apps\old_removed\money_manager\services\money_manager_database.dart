import 'package:sqflite_common_ffi/sqflite_ffi.dart' hide Transaction;
import '../../../core/database/database_manager.dart';
import '../models/money_manager_models.dart';

class MoneyManagerDatabase {
  static final DatabaseManager _dbManager = DatabaseManager();

  static Future<void> initialize() async {
    // Database is automatically initialized by DatabaseManager
    await _initializeDefaultData();
  }

  static Future<void> _initializeDefaultData() async {
    // Initialize default categories if none exist
    final categories = await getCategories();
    if (categories.isEmpty) {
      await _createDefaultCategories();
    }

    // Initialize sample accounts if none exist
    final accounts = await getAccounts();
    if (accounts.isEmpty) {
      await _createSampleAccounts();
    }
  }

  // Account CRUD Operations
  static Future<List<Account>> getAccounts() async {
    final db = await _dbManager.database;
    final maps = await db.query(
      'accounts',
      where: 'is_active = ?',
      whereArgs: [1],
    );

    return maps
        .map(
          (map) => Account(
            id: map['id'] as String,
            name: map['name'] as String,
            type: AccountType.values.firstWhere((e) => e.name == map['type']),
            initialBalance: map['initial_balance'] as double,
            currentBalance: map['current_balance'] as double,
            currency: map['currency'] as String,
            color: map['color'] as String,
            icon: 'account_balance_wallet', // Default icon
            showInTotal: true, // Default value
            allowNegative: false, // Default value
            createdAt: DateTime.fromMillisecondsSinceEpoch(
              map['created_at'] as int,
            ),
            lastModified: DateTime.fromMillisecondsSinceEpoch(
              map['last_modified'] as int,
            ),
          ),
        )
        .toList();
  }

  static Future<Account?> getAccount(String id) async {
    final db = await _dbManager.database;
    final maps = await db.query('accounts', where: 'id = ?', whereArgs: [id]);

    if (maps.isEmpty) return null;

    final map = maps.first;
    return Account(
      id: map['id'] as String,
      name: map['name'] as String,
      type: AccountType.values.firstWhere((e) => e.name == map['type']),
      initialBalance: map['initial_balance'] as double,
      currentBalance: map['current_balance'] as double,
      currency: map['currency'] as String,
      color: map['color'] as String,
      icon: 'account_balance_wallet', // Default icon
      showInTotal: true, // Default value
      allowNegative: false, // Default value
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      lastModified: DateTime.fromMillisecondsSinceEpoch(
        map['last_modified'] as int,
      ),
    );
  }

  static Future<void> saveAccount(Account account) async {
    final db = await _dbManager.database;
    await db.insert('accounts', {
      'id': account.id,
      'name': account.name,
      'type': account.type.name,
      'initial_balance': account.initialBalance,
      'current_balance': account.currentBalance,
      'currency': account.currency,
      'color': account.color,
      'description': null, // Account model doesn't have description field
      'created_at': account.createdAt.millisecondsSinceEpoch,
      'last_modified': account.lastModified.millisecondsSinceEpoch,
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  static Future<void> deleteAccount(String id) async {
    final db = await _dbManager.database;
    await db.update(
      'accounts',
      {'is_active': 0},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Category CRUD Operations
  static Future<List<Category>> getCategories() async {
    final db = await _dbManager.database;
    final maps = await db.query(
      'categories',
      where: 'is_active = ?',
      whereArgs: [1],
    );

    return maps
        .map(
          (map) => Category(
            id: map['id'] as String,
            name: map['name'] as String,
            type: CategoryType.values.firstWhere((e) => e.name == map['type']),
            color: (map['color'] as String?) ?? '#95A5A6',
            icon: (map['icon'] as String?) ?? 'category',
            createdAt: DateTime.fromMillisecondsSinceEpoch(
              map['created_at'] as int,
            ),
          ),
        )
        .toList();
  }

  static Future<void> saveCategory(Category category) async {
    final db = await _dbManager.database;
    await db.insert('categories', {
      'id': category.id,
      'name': category.name,
      'type': category.type.name,
      'color': category.color,
      'icon': category.icon,
      'parent_id': null, // Category model doesn't have parentId field
      'created_at': category.createdAt.millisecondsSinceEpoch,
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  // Transaction CRUD Operations
  static Future<List<Transaction>> getTransactions() async {
    final db = await _dbManager.database;
    final maps = await db.query('transactions', orderBy: 'date DESC');

    return maps
        .map(
          (map) => Transaction(
            id: map['id'] as String,
            amount: map['amount'] as double,
            type: TransactionType.values.firstWhere(
              (e) => e.name == map['type'],
            ),
            accountId: map['account_id'] as String,
            toAccountId: map['to_account_id'] as String?,
            categoryId: map['category_id'] as String,
            description: map['description'] as String,
            notes: map['notes'] as String?,
            tags: map['tags'] != null
                ? (map['tags'] as String).split(',')
                : <String>[],
            date: DateTime.fromMillisecondsSinceEpoch(map['date'] as int),
            createdAt: DateTime.fromMillisecondsSinceEpoch(
              map['created_at'] as int,
            ),
          ),
        )
        .toList();
  }

  static Future<void> saveTransaction(Transaction transaction) async {
    final db = await _dbManager.database;
    await db.insert('transactions', {
      'id': transaction.id,
      'amount': transaction.amount,
      'type': transaction.type.name,
      'account_id': transaction.accountId,
      'to_account_id': transaction.toAccountId,
      'category_id': transaction.categoryId,
      'description': transaction.description,
      'notes': transaction.notes,
      'tags': transaction.tags.join(','),
      'date': transaction.date.millisecondsSinceEpoch,
      'created_at': transaction.createdAt.millisecondsSinceEpoch,
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  static Future<void> deleteTransaction(String id) async {
    final db = await _dbManager.database;
    await db.delete('transactions', where: 'id = ?', whereArgs: [id]);
  }

  // Helper methods for sample data
  static Future<void> _createDefaultCategories() async {
    final defaultCategories = [
      Category(
        id: 'cat_salary',
        name: 'Salary',
        type: CategoryType.income,
        color: '#27AE60',
        icon: 'work',
        createdAt: DateTime.now(),
      ),
      Category(
        id: 'cat_food',
        name: 'Food & Dining',
        type: CategoryType.expense,
        color: '#E74C3C',
        icon: 'restaurant',
        createdAt: DateTime.now(),
      ),
      Category(
        id: 'cat_transport',
        name: 'Transportation',
        type: CategoryType.expense,
        color: '#F39C12',
        icon: 'directions_car',
        createdAt: DateTime.now(),
      ),
    ];

    for (final category in defaultCategories) {
      await saveCategory(category);
    }
  }

  static Future<void> _createSampleAccounts() async {
    final sampleAccounts = [
      Account(
        id: 'acc_checking',
        name: 'Main Checking',
        type: AccountType.checking,
        initialBalance: 5000.0,
        currentBalance: 5000.0,
        currency: 'USD',
        color: '#3498DB',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        lastModified: DateTime.now(),
      ),
      Account(
        id: 'acc_savings',
        name: 'Emergency Fund',
        type: AccountType.savings,
        initialBalance: 10000.0,
        currentBalance: 10000.0,
        currency: 'USD',
        color: '#27AE60',
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
        lastModified: DateTime.now(),
      ),
    ];

    for (final account in sampleAccounts) {
      await saveAccount(account);
    }
  }

  // Analytics methods
  static Future<double> getTotalBalance() async {
    final accounts = await getAccounts();
    return accounts.fold<double>(
      0.0,
      (sum, account) => sum + account.currentBalance,
    );
  }

  static Future<List<Transaction>> getTransactionsByAccount(
    String accountId,
  ) async {
    final db = await _dbManager.database;
    final maps = await db.query(
      'transactions',
      where: 'account_id = ? OR to_account_id = ?',
      whereArgs: [accountId, accountId],
      orderBy: 'date DESC',
    );

    return maps
        .map(
          (map) => Transaction(
            id: map['id'] as String,
            amount: map['amount'] as double,
            type: TransactionType.values.firstWhere(
              (e) => e.name == map['type'],
            ),
            accountId: map['account_id'] as String,
            toAccountId: map['to_account_id'] as String?,
            categoryId: map['category_id'] as String,
            description: map['description'] as String,
            notes: map['notes'] as String?,
            tags: map['tags'] != null
                ? (map['tags'] as String).split(',')
                : <String>[],
            date: DateTime.fromMillisecondsSinceEpoch(map['date'] as int),
            createdAt: DateTime.fromMillisecondsSinceEpoch(
              map['created_at'] as int,
            ),
          ),
        )
        .toList();
  }

  // Additional methods needed by providers
  static Future<void> deleteCategory(String id) async {
    final db = await _dbManager.database;
    await db.update(
      'categories',
      {'is_active': 0},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  static Future<Map<String, double>> getAccountBalances() async {
    final accounts = await getAccounts();
    final balances = <String, double>{};
    for (final account in accounts) {
      balances[account.id] = account.currentBalance;
    }
    return balances;
  }

  // Budget operations (placeholder implementations)
  static Future<List<Budget>> getBudgets() async {
    // Return empty list for now - Budget functionality can be implemented later
    return <Budget>[];
  }

  static Future<void> saveBudget(Budget budget) async {
    final db = await _dbManager.database;
    await db.insert(
      'budgets',
      budget.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  static Future<void> deleteBudget(String id) async {
    final db = await _dbManager.database;
    await db.delete('budgets', where: 'id = ?', whereArgs: [id]);
  }

  // Goal operations (placeholder implementations)
  static Future<List<Goal>> getGoals() async {
    // Return empty list for now - Goal functionality can be implemented later
    return <Goal>[];
  }

  static Future<void> saveGoal(Goal goal) async {
    final db = await _dbManager.database;
    await db.insert(
      'goals',
      goal.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  static Future<void> deleteGoal(String id) async {
    final db = await _dbManager.database;
    await db.delete('goals', where: 'id = ?', whereArgs: [id]);
  }
}
