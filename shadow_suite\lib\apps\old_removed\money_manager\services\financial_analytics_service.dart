import 'dart:async';
import 'dart:math' as math;
import '../models/money_manager_models.dart';

/// Advanced financial analytics service with comprehensive analysis
class FinancialAnalyticsService {
  static final FinancialAnalyticsService _instance =
      FinancialAnalyticsService._internal();
  factory FinancialAnalyticsService() => _instance;
  FinancialAnalyticsService._internal();

  // Event controller for analytics events
  final StreamController<AnalyticsEvent> _eventController =
      StreamController<AnalyticsEvent>.broadcast();

  final Map<String, FinancialReport> _reportCache = {};

  /// Stream of analytics events
  Stream<AnalyticsEvent> get events => _eventController.stream;

  /// Generate comprehensive financial report
  Future<FinancialReport> generateReport({
    required List<Transaction> transactions,
    required List<Account> accounts,
    required List<Budget> budgets,
    required DateRange dateRange,
    ReportType reportType = ReportType.comprehensive,
  }) async {
    final reportId = _generateReportId(dateRange, reportType);

    if (_reportCache.containsKey(reportId)) {
      return _reportCache[reportId]!;
    }

    _emitEvent(
      AnalyticsEvent(
        type: AnalyticsEventType.reportGenerationStarted,
        message: 'Generating ${reportType.name} report',
        timestamp: DateTime.now(),
      ),
    );

    try {
      // Filter transactions by date range
      final filteredTransactions = transactions
          .where(
            (t) =>
                t.date.isAfter(
                  dateRange.startDate.subtract(const Duration(days: 1)),
                ) &&
                t.date.isBefore(dateRange.endDate.add(const Duration(days: 1))),
          )
          .toList();

      // Generate report sections
      final incomeAnalysis = await _analyzeIncome(
        filteredTransactions,
        dateRange,
      );
      final expenseAnalysis = await _analyzeExpenses(
        filteredTransactions,
        dateRange,
      );
      final budgetAnalysis = await _analyzeBudgets(
        budgets,
        filteredTransactions,
        dateRange,
      );
      final accountAnalysis = await _analyzeAccounts(
        accounts,
        filteredTransactions,
      );
      final trendAnalysis = await _analyzeTrends(
        filteredTransactions,
        dateRange,
      );
      final categoryAnalysis = await _analyzeCategories(filteredTransactions);
      final cashFlowAnalysis = await _analyzeCashFlow(
        filteredTransactions,
        dateRange,
      );
      final savingsAnalysis = await _analyzeSavings(
        filteredTransactions,
        accounts,
        dateRange,
      );
      final investmentAnalysis = await _analyzeInvestments(
        accounts,
        filteredTransactions,
      );
      final debtAnalysis = await _analyzeDebt(accounts, filteredTransactions);

      final report = FinancialReport(
        id: reportId,
        reportType: reportType,
        dateRange: dateRange,
        generatedAt: DateTime.now(),
        incomeAnalysis: incomeAnalysis,
        expenseAnalysis: expenseAnalysis,
        budgetAnalysis: budgetAnalysis,
        accountAnalysis: accountAnalysis,
        trendAnalysis: trendAnalysis,
        categoryAnalysis: categoryAnalysis,
        cashFlowAnalysis: cashFlowAnalysis,
        savingsAnalysis: savingsAnalysis,
        investmentAnalysis: investmentAnalysis,
        debtAnalysis: debtAnalysis,
        insights: await _generateInsights(
          filteredTransactions,
          accounts,
          budgets,
          dateRange,
        ),
        recommendations: await _generateRecommendations(
          filteredTransactions,
          accounts,
          budgets,
        ),
      );

      _reportCache[reportId] = report;

      _emitEvent(
        AnalyticsEvent(
          type: AnalyticsEventType.reportGenerated,
          message: 'Report generated successfully',
          timestamp: DateTime.now(),
        ),
      );

      return report;
    } catch (e) {
      _emitEvent(
        AnalyticsEvent(
          type: AnalyticsEventType.reportGenerationFailed,
          message: 'Report generation failed: $e',
          timestamp: DateTime.now(),
        ),
      );
      rethrow;
    }
  }

  /// Analyze income patterns and trends
  Future<IncomeAnalysis> _analyzeIncome(
    List<Transaction> transactions,
    DateRange dateRange,
  ) async {
    final incomeTransactions = transactions.where((t) => t.amount > 0).toList();

    if (incomeTransactions.isEmpty) {
      return IncomeAnalysis(
        totalIncome: 0,
        averageMonthlyIncome: 0,
        incomeGrowthRate: 0,
        primaryIncomeSource: '',
        incomeStability: 0,
        seasonalPatterns: [],
        incomeBySource: {},
        monthlyIncome: {},
      );
    }

    final totalIncome = incomeTransactions.fold(
      0.0,
      (sum, t) => sum + t.amount,
    );
    final monthsInRange = _getMonthsInRange(dateRange);
    final averageMonthlyIncome = totalIncome / monthsInRange;

    // Calculate income by source
    final incomeBySource = <String, double>{};
    for (final transaction in incomeTransactions) {
      final source = transaction.description.isNotEmpty
          ? transaction.description
          : 'Other';
      incomeBySource[source] =
          (incomeBySource[source] ?? 0) + transaction.amount;
    }

    // Find primary income source
    final primaryIncomeSource = incomeBySource.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;

    // Calculate monthly income distribution
    final monthlyIncome = <String, double>{};
    for (final transaction in incomeTransactions) {
      final monthKey =
          '${transaction.date.year}-${transaction.date.month.toString().padLeft(2, '0')}';
      monthlyIncome[monthKey] =
          (monthlyIncome[monthKey] ?? 0) + transaction.amount;
    }

    // Calculate income growth rate
    final sortedMonths = monthlyIncome.keys.toList()..sort();
    double incomeGrowthRate = 0;
    if (sortedMonths.length >= 2) {
      final firstMonth = monthlyIncome[sortedMonths.first]!;
      final lastMonth = monthlyIncome[sortedMonths.last]!;
      incomeGrowthRate = ((lastMonth - firstMonth) / firstMonth) * 100;
    }

    // Calculate income stability (coefficient of variation)
    final incomeValues = monthlyIncome.values.toList();
    final mean =
        incomeValues.fold(0.0, (sum, value) => sum + value) /
        incomeValues.length;
    final variance =
        incomeValues.fold(
          0.0,
          (sum, value) => sum + math.pow(value - mean, 2),
        ) /
        incomeValues.length;
    final standardDeviation = math.sqrt(variance);
    final incomeStability = mean > 0
        ? (1 - (standardDeviation / mean)) * 100
        : 0;

    // Detect seasonal patterns
    final seasonalPatterns = _detectSeasonalPatterns(incomeTransactions);

    return IncomeAnalysis(
      totalIncome: totalIncome,
      averageMonthlyIncome: averageMonthlyIncome,
      incomeGrowthRate: incomeGrowthRate,
      primaryIncomeSource: primaryIncomeSource,
      incomeStability: incomeStability.toDouble(),
      seasonalPatterns: seasonalPatterns,
      incomeBySource: incomeBySource,
      monthlyIncome: monthlyIncome,
    );
  }

  /// Analyze expense patterns and categories
  Future<ExpenseAnalysis> _analyzeExpenses(
    List<Transaction> transactions,
    DateRange dateRange,
  ) async {
    final expenseTransactions = transactions
        .where((t) => t.amount < 0)
        .toList();

    if (expenseTransactions.isEmpty) {
      return ExpenseAnalysis(
        totalExpenses: 0,
        averageMonthlyExpenses: 0,
        expenseGrowthRate: 0,
        largestExpenseCategory: '',
        expensesByCategory: {},
        monthlyExpenses: {},
        topExpenses: [],
        recurringExpenses: [],
      );
    }

    final totalExpenses = expenseTransactions.fold(
      0.0,
      (sum, t) => sum + t.amount.abs(),
    );
    final monthsInRange = _getMonthsInRange(dateRange);
    final averageMonthlyExpenses = totalExpenses / monthsInRange;

    // Calculate expenses by category
    final expensesByCategory = <String, double>{};
    for (final transaction in expenseTransactions) {
      final category = transaction.categoryId.isNotEmpty
          ? transaction.categoryId
          : 'Uncategorized';
      expensesByCategory[category] =
          (expensesByCategory[category] ?? 0) + transaction.amount.abs();
    }

    // Find largest expense category
    final largestExpenseCategory = expensesByCategory.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;

    // Calculate monthly expenses
    final monthlyExpenses = <String, double>{};
    for (final transaction in expenseTransactions) {
      final monthKey =
          '${transaction.date.year}-${transaction.date.month.toString().padLeft(2, '0')}';
      monthlyExpenses[monthKey] =
          (monthlyExpenses[monthKey] ?? 0) + transaction.amount.abs();
    }

    // Calculate expense growth rate
    final sortedMonths = monthlyExpenses.keys.toList()..sort();
    double expenseGrowthRate = 0;
    if (sortedMonths.length >= 2) {
      final firstMonth = monthlyExpenses[sortedMonths.first]!;
      final lastMonth = monthlyExpenses[sortedMonths.last]!;
      expenseGrowthRate = ((lastMonth - firstMonth) / firstMonth) * 100;
    }

    // Find top expenses
    final topExpenses =
        expenseTransactions
            .map(
              (t) => ExpenseItem(
                description: t.description,
                amount: t.amount.abs(),
                category: t.categoryId.isNotEmpty
                    ? t.categoryId
                    : 'Uncategorized',
                date: t.date,
              ),
            )
            .toList()
          ..sort((a, b) => b.amount.compareTo(a.amount));

    // Detect recurring expenses
    final recurringExpenses = _detectRecurringExpenses(expenseTransactions);

    return ExpenseAnalysis(
      totalExpenses: totalExpenses,
      averageMonthlyExpenses: averageMonthlyExpenses,
      expenseGrowthRate: expenseGrowthRate,
      largestExpenseCategory: largestExpenseCategory,
      expensesByCategory: expensesByCategory,
      monthlyExpenses: monthlyExpenses,
      topExpenses: topExpenses.take(10).toList(),
      recurringExpenses: recurringExpenses,
    );
  }

  /// Analyze budget performance
  Future<BudgetAnalysis> _analyzeBudgets(
    List<Budget> budgets,
    List<Transaction> transactions,
    DateRange dateRange,
  ) async {
    final budgetPerformance = <BudgetPerformance>[];
    double totalBudgeted = 0;
    double totalSpent = 0;
    int budgetsOnTrack = 0;
    int budgetsOverspent = 0;

    for (final budget in budgets) {
      final categoryTransactions = transactions
          .where((t) => t.categoryId == budget.categoryId && t.amount < 0)
          .toList();

      final spent = categoryTransactions.fold(
        0.0,
        (sum, t) => sum + t.amount.abs(),
      );
      final remaining = budget.amount - spent;
      final percentageUsed = budget.amount > 0
          ? (spent / budget.amount) * 100
          : 0;

      final performance = BudgetPerformance(
        budget: budget,
        spent: spent,
        remaining: remaining,
        percentageUsed: percentageUsed.toDouble(),
        isOnTrack: percentageUsed <= 100,
        daysRemaining: budget.endDate.difference(DateTime.now()).inDays,
        projectedSpending: _calculateProjectedSpending(
          categoryTransactions,
          budget.endDate,
        ),
      );

      budgetPerformance.add(performance);
      totalBudgeted += budget.amount;
      totalSpent += spent;

      if (percentageUsed <= 100) {
        budgetsOnTrack++;
      } else {
        budgetsOverspent++;
      }
    }

    final overallBudgetHealth = budgets.isNotEmpty
        ? (budgetsOnTrack / budgets.length) * 100
        : 100;

    return BudgetAnalysis(
      budgetPerformance: budgetPerformance,
      totalBudgeted: totalBudgeted,
      totalSpent: totalSpent,
      budgetsOnTrack: budgetsOnTrack,
      budgetsOverspent: budgetsOverspent,
      overallBudgetHealth: overallBudgetHealth.toDouble(),
      budgetVariance: totalBudgeted - totalSpent,
    );
  }

  /// Analyze account balances and performance
  Future<AccountAnalysis> _analyzeAccounts(
    List<Account> accounts,
    List<Transaction> transactions,
  ) async {
    final accountPerformance = <AccountPerformance>[];
    double totalBalance = 0;
    double totalAssets = 0;
    double totalLiabilities = 0;

    for (final account in accounts) {
      final accountTransactions = transactions
          .where((t) => t.accountId == account.id)
          .toList();
      final balance = account.currentBalance;
      final monthlyChange = _calculateMonthlyChange(accountTransactions);
      final transactionCount = accountTransactions.length;

      final performance = AccountPerformance(
        account: account,
        currentBalance: balance,
        monthlyChange: monthlyChange,
        transactionCount: transactionCount,
        averageTransactionAmount: transactionCount > 0
            ? accountTransactions.fold(0.0, (sum, t) => sum + t.amount.abs()) /
                  transactionCount
            : 0,
        lastTransactionDate: accountTransactions.isNotEmpty
            ? accountTransactions
                  .map((t) => t.date)
                  .reduce((a, b) => a.isAfter(b) ? a : b)
            : null,
      );

      accountPerformance.add(performance);
      totalBalance += balance;

      if (account.type == AccountType.checking ||
          account.type == AccountType.savings) {
        totalAssets += balance;
      } else if (account.type == AccountType.credit) {
        totalLiabilities += balance.abs();
      }
    }

    final netWorth = totalAssets - totalLiabilities;
    final debtToAssetRatio = totalAssets > 0
        ? (totalLiabilities / totalAssets) * 100
        : 0;

    return AccountAnalysis(
      accountPerformance: accountPerformance,
      totalBalance: totalBalance,
      totalAssets: totalAssets,
      totalLiabilities: totalLiabilities,
      netWorth: netWorth,
      debtToAssetRatio: debtToAssetRatio.toDouble(),
      accountCount: accounts.length,
    );
  }

  /// Analyze financial trends over time
  Future<TrendAnalysis> _analyzeTrends(
    List<Transaction> transactions,
    DateRange dateRange,
  ) async {
    final monthlyData = <String, MonthlyData>{};

    for (final transaction in transactions) {
      final monthKey =
          '${transaction.date.year}-${transaction.date.month.toString().padLeft(2, '0')}';

      if (!monthlyData.containsKey(monthKey)) {
        monthlyData[monthKey] = MonthlyData(
          month: monthKey,
          income: 0,
          expenses: 0,
          netFlow: 0,
          transactionCount: 0,
        );
      }

      final data = monthlyData[monthKey]!;
      if (transaction.amount > 0) {
        data.income += transaction.amount;
      } else {
        data.expenses += transaction.amount.abs();
      }
      data.netFlow = data.income - data.expenses;
      data.transactionCount++;
    }

    final sortedMonths = monthlyData.keys.toList()..sort();
    final trendData = sortedMonths.map((month) => monthlyData[month]!).toList();

    // Calculate trends
    final incomeTrend = _calculateTrend(
      trendData.map((d) => d.income).toList(),
    );
    final expenseTrend = _calculateTrend(
      trendData.map((d) => d.expenses).toList(),
    );
    final netFlowTrend = _calculateTrend(
      trendData.map((d) => d.netFlow).toList(),
    );

    return TrendAnalysis(
      monthlyData: trendData,
      incomeTrend: incomeTrend,
      expenseTrend: expenseTrend,
      netFlowTrend: netFlowTrend,
      volatility: _calculateVolatility(
        trendData.map((d) => d.netFlow).toList(),
      ),
      seasonality: _detectSeasonality(trendData),
    );
  }

  /// Analyze spending by categories
  Future<CategoryAnalysis> _analyzeCategories(
    List<Transaction> transactions,
  ) async {
    final categoryData = <String, CategoryData>{};

    for (final transaction in transactions.where((t) => t.amount < 0)) {
      final category = transaction.categoryId.isNotEmpty
          ? transaction.categoryId
          : 'Uncategorized';

      if (!categoryData.containsKey(category)) {
        categoryData[category] = CategoryData(
          category: category,
          totalSpent: 0,
          transactionCount: 0,
          averageAmount: 0,
          percentage: 0,
          trend: TrendDirection.stable,
        );
      }

      final data = categoryData[category]!;
      data.totalSpent += transaction.amount.abs();
      data.transactionCount++;
    }

    final totalSpent = categoryData.values.fold(
      0.0,
      (sum, data) => sum + data.totalSpent,
    );

    // Calculate percentages and averages
    for (final data in categoryData.values) {
      data.percentage = totalSpent > 0
          ? (data.totalSpent / totalSpent) * 100
          : 0;
      data.averageAmount = data.transactionCount > 0
          ? data.totalSpent / data.transactionCount
          : 0;
    }

    // Sort by total spent
    final sortedCategories = categoryData.values.toList()
      ..sort((a, b) => b.totalSpent.compareTo(a.totalSpent));

    return CategoryAnalysis(
      categoryData: sortedCategories,
      topCategories: sortedCategories.take(5).toList(),
      categoryCount: categoryData.length,
      averageSpendingPerCategory: totalSpent / categoryData.length,
    );
  }

  /// Analyze cash flow patterns
  Future<CashFlowAnalysis> _analyzeCashFlow(
    List<Transaction> transactions,
    DateRange dateRange,
  ) async {
    final dailyCashFlow = <DateTime, double>{};
    double runningBalance = 0;

    // Sort transactions by date
    final sortedTransactions = transactions.toList()
      ..sort((a, b) => a.date.compareTo(b.date));

    for (final transaction in sortedTransactions) {
      final date = DateTime(
        transaction.date.year,
        transaction.date.month,
        transaction.date.day,
      );
      dailyCashFlow[date] = (dailyCashFlow[date] ?? 0) + transaction.amount;
    }

    final cashFlowData = <CashFlowData>[];
    for (final entry in dailyCashFlow.entries) {
      runningBalance += entry.value;
      cashFlowData.add(
        CashFlowData(
          date: entry.key,
          inflow: entry.value > 0 ? entry.value : 0,
          outflow: entry.value < 0 ? entry.value.abs() : 0,
          netFlow: entry.value,
          runningBalance: runningBalance,
        ),
      );
    }

    final averageDailyFlow = cashFlowData.isNotEmpty
        ? cashFlowData.fold(0.0, (sum, data) => sum + data.netFlow) /
              cashFlowData.length
        : 0;

    final cashFlowVolatility = _calculateVolatility(
      cashFlowData.map((d) => d.netFlow).toList(),
    );

    // Find cash flow patterns
    final positiveFlowDays = cashFlowData.where((d) => d.netFlow > 0).length;
    final negativeFlowDays = cashFlowData.where((d) => d.netFlow < 0).length;

    return CashFlowAnalysis(
      dailyCashFlow: cashFlowData,
      averageDailyFlow: averageDailyFlow.toDouble(),
      cashFlowVolatility: cashFlowVolatility,
      positiveFlowDays: positiveFlowDays,
      negativeFlowDays: negativeFlowDays,
      longestPositiveStreak: _calculateLongestStreak(cashFlowData, true),
      longestNegativeStreak: _calculateLongestStreak(cashFlowData, false),
    );
  }

  /// Analyze savings patterns and goals
  Future<SavingsAnalysis> _analyzeSavings(
    List<Transaction> transactions,
    List<Account> accounts,
    DateRange dateRange,
  ) async {
    final savingsAccounts = accounts
        .where((a) => a.type == AccountType.savings)
        .toList();
    final totalSavings = savingsAccounts.fold(
      0.0,
      (sum, account) => sum + account.currentBalance,
    );

    // Calculate savings rate
    final income = transactions
        .where((t) => t.amount > 0)
        .fold(0.0, (sum, t) => sum + t.amount);
    final expenses = transactions
        .where((t) => t.amount < 0)
        .fold(0.0, (sum, t) => sum + t.amount.abs());
    final savingsRate = income > 0 ? ((income - expenses) / income) * 100 : 0;

    // Calculate monthly savings
    final monthlySavings = <String, double>{};
    for (final transaction in transactions) {
      if (savingsAccounts.any((a) => a.id == transaction.accountId)) {
        final monthKey =
            '${transaction.date.year}-${transaction.date.month.toString().padLeft(2, '0')}';
        monthlySavings[monthKey] =
            (monthlySavings[monthKey] ?? 0) + transaction.amount;
      }
    }

    final averageMonthlySavings = monthlySavings.values.isNotEmpty
        ? monthlySavings.values.fold(0.0, (sum, amount) => sum + amount) /
              monthlySavings.length
        : 0;

    return SavingsAnalysis(
      totalSavings: totalSavings,
      savingsRate: savingsRate.toDouble(),
      averageMonthlySavings: averageMonthlySavings.toDouble(),
      savingsAccounts: savingsAccounts.length,
      monthlySavings: monthlySavings,
      savingsGrowthRate: _calculateSavingsGrowthRate(monthlySavings),
    );
  }

  // Helper methods
  double _getMonthsInRange(DateRange dateRange) {
    final difference = dateRange.endDate.difference(dateRange.startDate);
    return difference.inDays / 30.44; // Average days per month
  }

  List<SeasonalPattern> _detectSeasonalPatterns(
    List<Transaction> transactions,
  ) {
    final monthlyTotals = <int, double>{};

    for (final transaction in transactions) {
      final month = transaction.date.month;
      monthlyTotals[month] = (monthlyTotals[month] ?? 0) + transaction.amount;
    }

    final patterns = <SeasonalPattern>[];
    final averageAmount =
        monthlyTotals.values.fold(0.0, (sum, amount) => sum + amount) /
        monthlyTotals.length;

    for (final entry in monthlyTotals.entries) {
      if (entry.value > averageAmount * 1.2) {
        patterns.add(
          SeasonalPattern(
            month: entry.key,
            pattern: 'High activity',
            amount: entry.value,
            variance: ((entry.value - averageAmount) / averageAmount) * 100,
          ),
        );
      } else if (entry.value < averageAmount * 0.8) {
        patterns.add(
          SeasonalPattern(
            month: entry.key,
            pattern: 'Low activity',
            amount: entry.value,
            variance: ((entry.value - averageAmount) / averageAmount) * 100,
          ),
        );
      }
    }

    return patterns;
  }

  List<RecurringExpense> _detectRecurringExpenses(
    List<Transaction> transactions,
  ) {
    final recurringExpenses = <RecurringExpense>[];
    final groupedByDescription = <String, List<Transaction>>{};

    // Group transactions by description
    for (final transaction in transactions) {
      final description = transaction.description.toLowerCase().trim();
      if (description.isNotEmpty) {
        groupedByDescription[description] =
            (groupedByDescription[description] ?? [])..add(transaction);
      }
    }

    // Find recurring patterns
    for (final entry in groupedByDescription.entries) {
      final transactions = entry.value;
      if (transactions.length >= 3) {
        // At least 3 occurrences
        final amounts = transactions.map((t) => t.amount.abs()).toList();
        final averageAmount =
            amounts.fold(0.0, (sum, amount) => sum + amount) / amounts.length;

        // Check if amounts are similar (within 10% variance)
        final variance = amounts
            .map((amount) => (amount - averageAmount).abs())
            .reduce(math.max);
        if (variance / averageAmount <= 0.1) {
          // Calculate frequency
          transactions.sort((a, b) => a.date.compareTo(b.date));
          final intervals = <int>[];
          for (int i = 1; i < transactions.length; i++) {
            intervals.add(
              transactions[i].date.difference(transactions[i - 1].date).inDays,
            );
          }

          final averageInterval =
              intervals.fold(0, (sum, interval) => sum + interval) /
              intervals.length;

          recurringExpenses.add(
            RecurringExpense(
              description: entry.key,
              averageAmount: averageAmount,
              frequency: _determineFrequency(averageInterval),
              lastOccurrence: transactions.last.date,
              nextExpected: transactions.last.date.add(
                Duration(days: averageInterval.round()),
              ),
              confidence: _calculateRecurrenceConfidence(intervals),
            ),
          );
        }
      }
    }

    return recurringExpenses
      ..sort((a, b) => b.averageAmount.compareTo(a.averageAmount));
  }

  double _calculateProjectedSpending(
    List<Transaction> transactions,
    DateTime endDate,
  ) {
    if (transactions.isEmpty) return 0;

    final now = DateTime.now();
    final daysElapsed = now.difference(transactions.first.date).inDays;
    final totalSpent = transactions.fold(0.0, (sum, t) => sum + t.amount.abs());
    final dailyAverage = daysElapsed > 0 ? totalSpent / daysElapsed : 0;
    final remainingDays = endDate.difference(now).inDays;

    return totalSpent + (dailyAverage * remainingDays);
  }

  double _calculateMonthlyChange(List<Transaction> transactions) {
    if (transactions.length < 2) return 0;

    final now = DateTime.now();
    final lastMonth = DateTime(now.year, now.month - 1, now.day);

    final currentMonthTransactions = transactions
        .where((t) => t.date.isAfter(lastMonth))
        .toList();
    final previousMonthTransactions = transactions
        .where(
          (t) =>
              t.date.isBefore(lastMonth) &&
              t.date.isAfter(DateTime(now.year, now.month - 2, now.day)),
        )
        .toList();

    final currentTotal = currentMonthTransactions.fold(
      0.0,
      (sum, t) => sum + t.amount,
    );
    final previousTotal = previousMonthTransactions.fold(
      0.0,
      (sum, t) => sum + t.amount,
    );

    return currentTotal - previousTotal;
  }

  TrendDirection _calculateTrend(List<double> values) {
    if (values.length < 2) return TrendDirection.stable;

    final firstHalf = values.take(values.length ~/ 2).toList();
    final secondHalf = values.skip(values.length ~/ 2).toList();

    final firstAverage =
        firstHalf.fold(0.0, (sum, value) => sum + value) / firstHalf.length;
    final secondAverage =
        secondHalf.fold(0.0, (sum, value) => sum + value) / secondHalf.length;

    final change = ((secondAverage - firstAverage) / firstAverage) * 100;

    if (change > 5) return TrendDirection.increasing;
    if (change < -5) return TrendDirection.decreasing;
    return TrendDirection.stable;
  }

  double _calculateVolatility(List<double> values) {
    if (values.length < 2) return 0;

    final mean = values.fold(0.0, (sum, value) => sum + value) / values.length;
    final variance =
        values.fold(0.0, (sum, value) => sum + math.pow(value - mean, 2)) /
        values.length;
    return math.sqrt(variance);
  }

  bool _detectSeasonality(List<MonthlyData> data) {
    // Simple seasonality detection based on recurring patterns
    if (data.length < 12) return false;

    final monthlyAverages = <int, double>{};
    for (final monthData in data) {
      final month = int.parse(monthData.month.split('-')[1]);
      monthlyAverages[month] =
          (monthlyAverages[month] ?? 0) + monthData.netFlow;
    }

    final values = monthlyAverages.values.toList();
    final mean = values.fold(0.0, (sum, value) => sum + value) / values.length;
    final variance =
        values.fold(0.0, (sum, value) => sum + math.pow(value - mean, 2)) /
        values.length;
    final standardDeviation = math.sqrt(variance);

    // If standard deviation is more than 20% of mean, consider it seasonal
    return mean > 0 && (standardDeviation / mean) > 0.2;
  }

  int _calculateLongestStreak(List<CashFlowData> data, bool positive) {
    int longestStreak = 0;
    int currentStreak = 0;

    for (final cashFlow in data) {
      if ((positive && cashFlow.netFlow > 0) ||
          (!positive && cashFlow.netFlow < 0)) {
        currentStreak++;
        longestStreak = math.max(longestStreak, currentStreak);
      } else {
        currentStreak = 0;
      }
    }

    return longestStreak;
  }

  double _calculateSavingsGrowthRate(Map<String, double> monthlySavings) {
    final sortedMonths = monthlySavings.keys.toList()..sort();
    if (sortedMonths.length < 2) return 0;

    final firstMonth = monthlySavings[sortedMonths.first]!;
    final lastMonth = monthlySavings[sortedMonths.last]!;

    return firstMonth != 0 ? ((lastMonth - firstMonth) / firstMonth) * 100 : 0;
  }

  String _determineFrequency(double averageInterval) {
    if (averageInterval <= 7) return 'Weekly';
    if (averageInterval <= 14) return 'Bi-weekly';
    if (averageInterval <= 31) return 'Monthly';
    if (averageInterval <= 93) return 'Quarterly';
    if (averageInterval <= 186) return 'Semi-annually';
    if (averageInterval <= 366) return 'Annually';
    return 'Irregular';
  }

  double _calculateRecurrenceConfidence(List<int> intervals) {
    if (intervals.isEmpty) return 0;

    final mean =
        intervals.fold(0, (sum, interval) => sum + interval) / intervals.length;
    final variance =
        intervals.fold(
          0.0,
          (sum, interval) => sum + math.pow(interval - mean, 2),
        ) /
        intervals.length;
    final standardDeviation = math.sqrt(variance);

    // Lower variance means higher confidence
    return mean > 0 ? math.max(0, 1 - (standardDeviation / mean)) : 0;
  }

  Future<List<FinancialInsight>> _generateInsights(
    List<Transaction> transactions,
    List<Account> accounts,
    List<Budget> budgets,
    DateRange dateRange,
  ) async {
    final insights = <FinancialInsight>[];

    // Income insights
    final income = transactions
        .where((t) => t.amount > 0)
        .fold(0.0, (sum, t) => sum + t.amount);
    final expenses = transactions
        .where((t) => t.amount < 0)
        .fold(0.0, (sum, t) => sum + t.amount.abs());

    if (income > expenses) {
      insights.add(
        FinancialInsight(
          type: InsightType.positive,
          title: 'Positive Cash Flow',
          description:
              'You saved \$${(income - expenses).toStringAsFixed(2)} this period',
          impact: InsightImpact.high,
          actionable: true,
          recommendation:
              'Consider investing this surplus or increasing your emergency fund',
        ),
      );
    } else if (expenses > income) {
      insights.add(
        FinancialInsight(
          type: InsightType.warning,
          title: 'Spending Exceeds Income',
          description:
              'You spent \$${(expenses - income).toStringAsFixed(2)} more than you earned',
          impact: InsightImpact.high,
          actionable: true,
          recommendation: 'Review your expenses and identify areas to cut back',
        ),
      );
    }

    // Budget insights
    for (final budget in budgets) {
      final spent = transactions
          .where((t) => t.categoryId == budget.categoryId && t.amount < 0)
          .fold(0.0, (sum, t) => sum + t.amount.abs());

      if (spent > budget.amount * 0.9) {
        insights.add(
          FinancialInsight(
            type: InsightType.warning,
            title: 'Budget Alert: ${budget.categoryId}',
            description:
                'You\'ve used ${((spent / budget.amount) * 100).toStringAsFixed(1)}% of your ${budget.categoryId} budget',
            impact: InsightImpact.medium,
            actionable: true,
            recommendation: 'Monitor spending in this category closely',
          ),
        );
      }
    }

    return insights;
  }

  Future<List<FinancialRecommendation>> _generateRecommendations(
    List<Transaction> transactions,
    List<Account> accounts,
    List<Budget> budgets,
  ) async {
    final recommendations = <FinancialRecommendation>[];

    // Emergency fund recommendation
    final savingsBalance = accounts
        .where((a) => a.type == AccountType.savings)
        .fold(0.0, (sum, account) => sum + account.currentBalance);

    final monthlyExpenses =
        transactions
            .where((t) => t.amount < 0)
            .fold(0.0, (sum, t) => sum + t.amount.abs()) /
        3; // Assuming 3 months of data

    if (savingsBalance < monthlyExpenses * 3) {
      recommendations.add(
        FinancialRecommendation(
          type: RecommendationType.savings,
          title: 'Build Emergency Fund',
          description:
              'Your emergency fund should cover 3-6 months of expenses',
          priority: RecommendationPriority.high,
          estimatedImpact: 'High financial security',
          actionSteps: [
            'Set up automatic transfers to savings',
            'Aim to save \$${((monthlyExpenses * 3) - savingsBalance).toStringAsFixed(2)}',
            'Consider high-yield savings accounts',
          ],
        ),
      );
    }

    // Debt reduction recommendation
    final debtAccounts = accounts
        .where((a) => a.type == AccountType.credit && a.currentBalance > 0)
        .toList();
    if (debtAccounts.isNotEmpty) {
      final totalDebt = debtAccounts.fold(
        0.0,
        (sum, account) => sum + account.currentBalance,
      );

      // Use totalDebt for debt analysis
      if (totalDebt > 10000) {
        recommendations.add(
          FinancialRecommendation(
            type: RecommendationType.debt,
            title: 'High Debt Alert',
            description: 'Consider debt consolidation or payment plan',
            priority: RecommendationPriority.high,
            estimatedImpact: 'Reduce monthly interest payments',
            actionSteps: [
              'Review high-interest debts',
              'Consider debt consolidation',
              'Create payment plan',
            ],
          ),
        );
      }
    }

    return recommendations;
  }

  Future<InvestmentAnalysis> _analyzeInvestments(
    List<Account> accounts,
    List<Transaction> transactions,
  ) async {
    final investmentAccounts = accounts
        .where((a) => a.type == AccountType.investment)
        .toList();
    final totalInvestments = investmentAccounts.fold(
      0.0,
      (sum, account) => sum + account.currentBalance,
    );

    return InvestmentAnalysis(
      totalInvestments: totalInvestments,
      investmentAccounts: investmentAccounts.length,
      averageReturn: 0, // Would calculate based on historical data
      riskLevel: RiskLevel.moderate,
      diversificationScore: 0, // Would calculate based on portfolio composition
      recommendedAllocation: {},
    );
  }

  Future<DebtAnalysis> _analyzeDebt(
    List<Account> accounts,
    List<Transaction> transactions,
  ) async {
    final debtAccounts = accounts
        .where((a) => a.type == AccountType.credit)
        .toList();
    final totalDebt = debtAccounts.fold(
      0.0,
      (sum, account) => sum + account.currentBalance,
    );

    return DebtAnalysis(
      totalDebt: totalDebt,
      debtAccounts: debtAccounts.length,
      averageInterestRate: 0, // Would calculate based on account data
      monthlyPayments: 0, // Would calculate based on transactions
      payoffTimeline: {},
      debtToIncomeRatio: 0,
    );
  }

  String _generateReportId(DateRange dateRange, ReportType reportType) {
    return '${reportType.name}_${dateRange.startDate.millisecondsSinceEpoch}_${dateRange.endDate.millisecondsSinceEpoch}';
  }

  void _emitEvent(AnalyticsEvent event) {
    _eventController.add(event);
  }

  void dispose() {
    _eventController.close();
  }
}
