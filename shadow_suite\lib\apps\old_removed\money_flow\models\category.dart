import 'package:uuid/uuid.dart';

enum CategoryType {
  income,
  expense,
  transfer;

  String get displayName {
    switch (this) {
      case CategoryType.income:
        return 'Income';
      case CategoryType.expense:
        return 'Expense';
      case CategoryType.transfer:
        return 'Transfer';
    }
  }
}

class TransactionCategory {
  final String id;
  final String name;
  final CategoryType type;
  final String icon;
  final String color;
  final bool isDefault;
  final bool isActive;
  final int usageCount;
  final DateTime createdAt;
  final DateTime updatedAt;

  TransactionCategory({
    String? id,
    required this.name,
    required this.type,
    this.icon = 'category',
    this.color = '#2196F3',
    this.isDefault = false,
    this.isActive = true,
    this.usageCount = 0,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'icon': icon,
      'color': color,
      'isDefault': isDefault ? 1 : 0,
      'isActive': isActive ? 1 : 0,
      'usageCount': usageCount,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory TransactionCategory.fromMap(Map<String, dynamic> map) {
    return TransactionCategory(
      id: map['id'],
      name: map['name'],
      type: CategoryType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => CategoryType.expense,
      ),
      icon: map['icon'] ?? 'category',
      color: map['color'] ?? '#2196F3',
      isDefault: map['isDefault'] == 1,
      isActive: map['isActive'] == 1,
      usageCount: map['usageCount'] ?? 0,
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }

  TransactionCategory copyWith({
    String? name,
    CategoryType? type,
    String? icon,
    String? color,
    bool? isDefault,
    bool? isActive,
    int? usageCount,
    DateTime? updatedAt,
  }) {
    return TransactionCategory(
      id: id,
      name: name ?? this.name,
      type: type ?? this.type,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      isDefault: isDefault ?? this.isDefault,
      isActive: isActive ?? this.isActive,
      usageCount: usageCount ?? this.usageCount,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TransactionCategory && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Predefined category icons
class CategoryIcons {
  static const Map<String, String> icons = {
    // Income icons
    'salary': '💰',
    'business': '💼',
    'investment': '📈',
    'freelance': '💻',
    'bonus': '🎁',
    'rental': '🏠',
    'dividend': '📊',
    'interest': '🏦',
    'gift': '🎉',
    'refund': '↩️',
    
    // Expense icons
    'food': '🍽️',
    'groceries': '🛒',
    'restaurant': '🍕',
    'coffee': '☕',
    'transportation': '🚗',
    'gas': '⛽',
    'parking': '🅿️',
    'taxi': '🚕',
    'public_transport': '🚌',
    'shopping': '🛍️',
    'clothing': '👕',
    'electronics': '📱',
    'books': '📚',
    'entertainment': '🎬',
    'movies': '🎭',
    'music': '🎵',
    'games': '🎮',
    'sports': '⚽',
    'healthcare': '🏥',
    'medicine': '💊',
    'doctor': '👨‍⚕️',
    'dental': '🦷',
    'education': '🎓',
    'school': '🏫',
    'course': '📖',
    'utilities': '💡',
    'electricity': '⚡',
    'water': '💧',
    'internet': '🌐',
    'phone': '📞',
    'insurance': '🛡️',
    'rent': '🏡',
    'mortgage': '🏘️',
    'maintenance': '🔧',
    'travel': '✈️',
    'hotel': '🏨',
    'vacation': '🏖️',
    'fitness': '💪',
    'gym': '🏋️',
    'beauty': '💄',
    'haircut': '💇',
    'pet': '🐕',
    'charity': '❤️',
    'gift_expense': '🎁',
    'tax': '📋',
    'bank_fee': '🏦',
    'subscription': '📺',
    'other': '📦',
    
    // Transfer icons
    'transfer': '🔄',
    'savings': '💰',
    'investment_transfer': '📈',
    'loan': '💳',
    'payment': '💸',
  };

  static String getIcon(String iconKey) {
    return icons[iconKey] ?? '📦';
  }

  static List<String> getIconsByType(CategoryType type) {
    switch (type) {
      case CategoryType.income:
        return [
          'salary', 'business', 'investment', 'freelance', 'bonus',
          'rental', 'dividend', 'interest', 'gift', 'refund'
        ];
      case CategoryType.expense:
        return [
          'food', 'groceries', 'restaurant', 'coffee', 'transportation',
          'gas', 'parking', 'taxi', 'public_transport', 'shopping',
          'clothing', 'electronics', 'books', 'entertainment', 'movies',
          'music', 'games', 'sports', 'healthcare', 'medicine',
          'doctor', 'dental', 'education', 'school', 'course',
          'utilities', 'electricity', 'water', 'internet', 'phone',
          'insurance', 'rent', 'mortgage', 'maintenance', 'travel',
          'hotel', 'vacation', 'fitness', 'gym', 'beauty',
          'haircut', 'pet', 'charity', 'gift_expense', 'tax',
          'bank_fee', 'subscription', 'other'
        ];
      case CategoryType.transfer:
        return [
          'transfer', 'savings', 'investment_transfer', 'loan', 'payment'
        ];
    }
  }

  static List<MapEntry<String, String>> getAllIcons() {
    return icons.entries.toList();
  }
}

// Default categories
class DefaultCategories {
  static List<TransactionCategory> getDefaultCategories() {
    return [
      // Income categories
      TransactionCategory(
        name: 'Salary',
        type: CategoryType.income,
        icon: 'salary',
        color: '#4CAF50',
        isDefault: true,
      ),
      TransactionCategory(
        name: 'Business Income',
        type: CategoryType.income,
        icon: 'business',
        color: '#2196F3',
        isDefault: true,
      ),
      TransactionCategory(
        name: 'Investment Returns',
        type: CategoryType.income,
        icon: 'investment',
        color: '#FF9800',
        isDefault: true,
      ),
      TransactionCategory(
        name: 'Freelance',
        type: CategoryType.income,
        icon: 'freelance',
        color: '#9C27B0',
        isDefault: true,
      ),
      
      // Expense categories
      TransactionCategory(
        name: 'Food & Dining',
        type: CategoryType.expense,
        icon: 'food',
        color: '#FF5722',
        isDefault: true,
      ),
      TransactionCategory(
        name: 'Groceries',
        type: CategoryType.expense,
        icon: 'groceries',
        color: '#4CAF50',
        isDefault: true,
      ),
      TransactionCategory(
        name: 'Transportation',
        type: CategoryType.expense,
        icon: 'transportation',
        color: '#2196F3',
        isDefault: true,
      ),
      TransactionCategory(
        name: 'Shopping',
        type: CategoryType.expense,
        icon: 'shopping',
        color: '#E91E63',
        isDefault: true,
      ),
      TransactionCategory(
        name: 'Entertainment',
        type: CategoryType.expense,
        icon: 'entertainment',
        color: '#9C27B0',
        isDefault: true,
      ),
      TransactionCategory(
        name: 'Bills & Utilities',
        type: CategoryType.expense,
        icon: 'utilities',
        color: '#FF9800',
        isDefault: true,
      ),
      TransactionCategory(
        name: 'Healthcare',
        type: CategoryType.expense,
        icon: 'healthcare',
        color: '#F44336',
        isDefault: true,
      ),
      TransactionCategory(
        name: 'Education',
        type: CategoryType.expense,
        icon: 'education',
        color: '#3F51B5',
        isDefault: true,
      ),
      TransactionCategory(
        name: 'Travel',
        type: CategoryType.expense,
        icon: 'travel',
        color: '#00BCD4',
        isDefault: true,
      ),
      TransactionCategory(
        name: 'Other',
        type: CategoryType.expense,
        icon: 'other',
        color: '#607D8B',
        isDefault: true,
      ),
      
      // Transfer categories
      TransactionCategory(
        name: 'Account Transfer',
        type: CategoryType.transfer,
        icon: 'transfer',
        color: '#795548',
        isDefault: true,
      ),
      TransactionCategory(
        name: 'Savings',
        type: CategoryType.transfer,
        icon: 'savings',
        color: '#4CAF50',
        isDefault: true,
      ),
    ];
  }
}
