import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'dart:math' as math; // Reserved for future mathematical operations
import '../../models/ui_component.dart';
// import '../../models/excel_tool.dart'; // Reserved for future tool integration
// import '../../services/excel_tools_provider.dart'; // Reserved for future tool provider integration
import '../../../../core/theme/app_theme.dart';
import 'advanced_layout_customization_screen.dart';

class VisualUIBuilderScreen extends ConsumerStatefulWidget {
  final String toolId;

  const VisualUIBuilderScreen({super.key, required this.toolId});

  @override
  ConsumerState<VisualUIBuilderScreen> createState() =>
      _VisualUIBuilderScreenState();
}

class _VisualUIBuilderScreenState extends ConsumerState<VisualUIBuilderScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  List<UIComponent> _components = [];
  UIComponent? _selectedComponent;
  bool _isPreviewMode = false;
  double _canvasScale = 1.0;
  Offset _canvasOffset = Offset.zero;

  // Theme/Background selection
  String? _selectedTheme;
  String? _customBackgroundPath;
  Color _backgroundColor = Colors.white;

  // Component palette
  final List<ComponentTemplate> _componentTemplates = [
    ComponentTemplate('Text', Icons.text_fields, UIComponentType.text),
    ComponentTemplate('Button', Icons.smart_button, UIComponentType.button),
    ComponentTemplate('Input', Icons.input, UIComponentType.textField),
    ComponentTemplate('Image', Icons.image, UIComponentType.image),
    ComponentTemplate('Chart', Icons.bar_chart, UIComponentType.chart),
    ComponentTemplate('Table', Icons.table_chart, UIComponentType.table),
    ComponentTemplate(
      'Container',
      Icons.crop_square,
      UIComponentType.container,
    ),
    ComponentTemplate('List', Icons.list, UIComponentType.list),
    ComponentTemplate('Card', Icons.credit_card, UIComponentType.card),
    ComponentTemplate('Slider', Icons.tune, UIComponentType.slider),
    ComponentTemplate('Switch', Icons.toggle_on, UIComponentType.switch_),
    ComponentTemplate('Checkbox', Icons.check_box, UIComponentType.checkbox),
    ComponentTemplate(
      'Radio',
      Icons.radio_button_checked,
      UIComponentType.radio,
    ),
    ComponentTemplate(
      'Dropdown',
      Icons.arrow_drop_down,
      UIComponentType.dropdown,
    ),
    ComponentTemplate(
      'Progress',
      Icons.linear_scale,
      UIComponentType.progressBar,
    ),
    ComponentTemplate(
      'Divider',
      Icons.horizontal_rule,
      UIComponentType.divider,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadExistingComponents();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadExistingComponents() {
    // Load existing components for the tool
    setState(() {
      _components = [];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('UI Builder'),
        backgroundColor: AppTheme.excelToAppColor,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.palette), text: 'Design'),
            Tab(icon: Icon(Icons.wallpaper), text: 'Themes'),
            Tab(icon: Icon(Icons.settings), text: 'Properties'),
            Tab(icon: Icon(Icons.preview), text: 'Preview'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.tune),
            onPressed: _openAdvancedLayoutCustomization,
            tooltip: 'Advanced Layout',
          ),
          IconButton(
            icon: Icon(_isPreviewMode ? Icons.edit : Icons.preview),
            onPressed: () {
              setState(() {
                _isPreviewMode = !_isPreviewMode;
              });
            },
          ),
          IconButton(icon: const Icon(Icons.save), onPressed: _saveComponents),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildDesignTab(),
          _buildThemesTab(),
          _buildPropertiesTab(),
          _buildPreviewTab(),
        ],
      ),
    );
  }

  Widget _buildDesignTab() {
    return Row(
      children: [
        // Component Palette
        Container(
          width: 250,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            border: Border(right: BorderSide(color: Colors.grey[300]!)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'Components',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Expanded(
                child: ListView.builder(
                  itemCount: _componentTemplates.length,
                  itemBuilder: (context, index) {
                    final template = _componentTemplates[index];
                    return Draggable<ComponentTemplate>(
                      data: template,
                      feedback: Material(
                        elevation: 4,
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: AppTheme.excelToAppColor,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                template.icon,
                                color: Colors.white,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                template.name,
                                style: const TextStyle(color: Colors.white),
                              ),
                            ],
                          ),
                        ),
                      ),
                      child: ListTile(
                        leading: Icon(
                          template.icon,
                          color: AppTheme.excelToAppColor,
                        ),
                        title: Text(template.name),
                        dense: true,
                        onTap: () => _addComponent(template),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),

        // Canvas Area
        Expanded(child: _buildCanvas()),

        // Component Tree
        Container(
          width: 200,
          decoration: BoxDecoration(
            color: Colors.grey[50],
            border: Border(left: BorderSide(color: Colors.grey[300]!)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'Component Tree',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Expanded(child: _buildComponentTree()),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildThemesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Theme & Background Selection',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),

          // Prebuilt Themes Section
          _buildThemeSection(),
          const SizedBox(height: 32),

          // Custom Background Section
          _buildCustomBackgroundSection(),
          const SizedBox(height: 32),

          // Background Color Section
          _buildBackgroundColorSection(),
          const SizedBox(height: 32),

          // Preview Section
          _buildThemePreviewSection(),
        ],
      ),
    );
  }

  Widget _buildCanvas() {
    return Container(
      color: Colors.white,
      child: Stack(
        children: [
          // Grid background
          CustomPaint(painter: GridPainter(), size: Size.infinite),

          // Drop target
          DragTarget<ComponentTemplate>(
            onAcceptWithDetails: (details) {
              _addComponentAtPosition(details.data, Offset(100, 100));
            },
            builder: (context, candidateData, rejectedData) {
              return Transform.scale(
                scale: _canvasScale,
                child: Transform.translate(
                  offset: _canvasOffset,
                  child: Stack(
                    children: [
                      // Components
                      ..._components.map(
                        (component) => _buildCanvasComponent(component),
                      ),

                      // Selection overlay
                      if (_selectedComponent != null)
                        _buildSelectionOverlay(_selectedComponent!),
                    ],
                  ),
                ),
              );
            },
          ),

          // Canvas controls
          Positioned(top: 16, right: 16, child: _buildCanvasControls()),
        ],
      ),
    );
  }

  Widget _buildCanvasComponent(UIComponent component) {
    return Positioned(
      left: component.position.dx,
      top: component.position.dy,
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedComponent = component;
          });
        },
        onPanUpdate: (details) {
          if (_selectedComponent == component) {
            setState(() {
              final newPosition = component.position + details.delta;
              final updatedComponent = component.copyWith(
                position: newPosition,
              );
              final index = _components.indexOf(component);
              _components[index] = updatedComponent;
              _selectedComponent = updatedComponent;
            });
          }
        },
        child: Container(
          width: component.size.width,
          height: component.size.height,
          decoration: BoxDecoration(
            border: _selectedComponent == component
                ? Border.all(color: AppTheme.excelToAppColor, width: 2)
                : null,
          ),
          child: _renderComponent(component),
        ),
      ),
    );
  }

  Widget _renderComponent(UIComponent component) {
    switch (component.type) {
      case UIComponentType.text:
        return Text(
          component.properties['text'] ?? 'Text',
          style: TextStyle(
            fontSize: component.properties['fontSize']?.toDouble() ?? 16.0,
            fontWeight: component.properties['bold'] == true
                ? FontWeight.bold
                : FontWeight.normal,
            color: Color(component.properties['color'] ?? 0xFF000000),
          ),
        );

      case UIComponentType.button:
        return ElevatedButton(
          onPressed: () {},
          style: ElevatedButton.styleFrom(
            backgroundColor: Color(
              component.properties['backgroundColor'] ?? 0xFF2196F3,
            ),
            foregroundColor: Color(
              component.properties['textColor'] ?? 0xFFFFFFFF,
            ),
          ),
          child: Text(component.properties['text'] ?? 'Button'),
        );

      case UIComponentType.textField:
        return TextField(
          decoration: InputDecoration(
            hintText: component.properties['placeholder'] ?? 'Enter text...',
            border: const OutlineInputBorder(),
          ),
        );

      case UIComponentType.image:
        return Container(
          decoration: BoxDecoration(
            color: Colors.grey[300],
            border: Border.all(color: Colors.grey),
          ),
          child: const Center(
            child: Icon(Icons.image, size: 48, color: Colors.grey),
          ),
        );

      case UIComponentType.container:
        return Container(
          decoration: BoxDecoration(
            color: Color(component.properties['backgroundColor'] ?? 0xFFF5F5F5),
            borderRadius: BorderRadius.circular(
              component.properties['borderRadius']?.toDouble() ?? 0,
            ),
            border: Border.all(
              color: Color(component.properties['borderColor'] ?? 0xFFE0E0E0),
              width: component.properties['borderWidth']?.toDouble() ?? 1,
            ),
          ),
          child: const Center(child: Text('Container')),
        );

      case UIComponentType.chart:
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.grey),
          ),
          child: const Center(
            child: Icon(Icons.bar_chart, size: 48, color: Colors.blue),
          ),
        );

      default:
        return Container(
          color: Colors.grey[200],
          child: Center(child: Text(component.type.name)),
        );
    }
  }

  Widget _buildSelectionOverlay(UIComponent component) {
    return Positioned(
      left: component.position.dx - 4,
      top: component.position.dy - 4,
      child: Container(
        width: component.size.width + 8,
        height: component.size.height + 8,
        decoration: BoxDecoration(
          border: Border.all(color: AppTheme.excelToAppColor, width: 2),
        ),
        child: Stack(
          children: [
            // Resize handles
            ..._buildResizeHandles(component),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildResizeHandles(UIComponent component) {
    const handleSize = 8.0;
    return [
      // Top-left
      Positioned(
        left: -handleSize / 2,
        top: -handleSize / 2,
        child: _buildResizeHandle(
          () => _resizeComponent(component, ResizeDirection.topLeft),
        ),
      ),
      // Top-right
      Positioned(
        right: -handleSize / 2,
        top: -handleSize / 2,
        child: _buildResizeHandle(
          () => _resizeComponent(component, ResizeDirection.topRight),
        ),
      ),
      // Bottom-left
      Positioned(
        left: -handleSize / 2,
        bottom: -handleSize / 2,
        child: _buildResizeHandle(
          () => _resizeComponent(component, ResizeDirection.bottomLeft),
        ),
      ),
      // Bottom-right
      Positioned(
        right: -handleSize / 2,
        bottom: -handleSize / 2,
        child: _buildResizeHandle(
          () => _resizeComponent(component, ResizeDirection.bottomRight),
        ),
      ),
    ];
  }

  Widget _buildResizeHandle(VoidCallback onPan) {
    return GestureDetector(
      onPanUpdate: (details) => onPan(),
      child: Container(
        width: 8,
        height: 8,
        decoration: BoxDecoration(
          color: AppTheme.excelToAppColor,
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  Widget _buildCanvasControls() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.3),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              IconButton(
                icon: const Icon(Icons.zoom_in),
                onPressed: () {
                  setState(() {
                    _canvasScale = (_canvasScale * 1.2).clamp(0.1, 3.0);
                  });
                },
              ),
              Text('${(_canvasScale * 100).round()}%'),
              IconButton(
                icon: const Icon(Icons.zoom_out),
                onPressed: () {
                  setState(() {
                    _canvasScale = (_canvasScale / 1.2).clamp(0.1, 3.0);
                  });
                },
              ),
              const Divider(),
              IconButton(
                icon: const Icon(Icons.center_focus_strong),
                onPressed: () {
                  setState(() {
                    _canvasScale = 1.0;
                    _canvasOffset = Offset.zero;
                  });
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildComponentTree() {
    return ListView.builder(
      itemCount: _components.length,
      itemBuilder: (context, index) {
        final component = _components[index];
        final isSelected = _selectedComponent == component;

        return ListTile(
          leading: Icon(
            _getComponentIcon(component.type),
            color: isSelected ? AppTheme.excelToAppColor : Colors.grey,
          ),
          title: Text(
            component.properties['name'] ?? component.type.name,
            style: TextStyle(
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              color: isSelected ? AppTheme.excelToAppColor : null,
            ),
          ),
          dense: true,
          selected: isSelected,
          onTap: () {
            setState(() {
              _selectedComponent = component;
            });
          },
          trailing: PopupMenuButton<String>(
            onSelected: (action) => _handleComponentAction(component, action),
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'duplicate', child: Text('Duplicate')),
              const PopupMenuItem(value: 'delete', child: Text('Delete')),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPropertiesTab() {
    if (_selectedComponent == null) {
      return const Center(
        child: Text('Select a component to edit its properties'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Properties',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildPropertyEditor(_selectedComponent!),
        ],
      ),
    );
  }

  Widget _buildPropertyEditor(UIComponent component) {
    return Column(
      children: [
        // Common properties
        _buildPropertyField('Name', component.properties['name'] ?? '', (
          value,
        ) {
          component.properties['name'] = value;
        }),

        // Type-specific properties
        ..._buildTypeSpecificProperties(component),

        const SizedBox(height: 16),

        // Position and size
        Text(
          'Layout',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildNumberField('X', component.position.dx, (value) {
                setState(() {
                  final updatedComponent = component.copyWith(
                    position: Offset(value, component.position.dy),
                  );
                  final index = _components.indexOf(component);
                  _components[index] = updatedComponent;
                  _selectedComponent = updatedComponent;
                });
              }),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildNumberField('Y', component.position.dy, (value) {
                setState(() {
                  final updatedComponent = component.copyWith(
                    position: Offset(component.position.dx, value),
                  );
                  final index = _components.indexOf(component);
                  _components[index] = updatedComponent;
                  _selectedComponent = updatedComponent;
                });
              }),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildNumberField('Width', component.size.width, (value) {
                setState(() {
                  final updatedComponent = component.copyWith(
                    size: Size(value, component.size.height),
                  );
                  final index = _components.indexOf(component);
                  _components[index] = updatedComponent;
                  _selectedComponent = updatedComponent;
                });
              }),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildNumberField('Height', component.size.height, (
                value,
              ) {
                setState(() {
                  final updatedComponent = component.copyWith(
                    size: Size(component.size.width, value),
                  );
                  final index = _components.indexOf(component);
                  _components[index] = updatedComponent;
                  _selectedComponent = updatedComponent;
                });
              }),
            ),
          ],
        ),
      ],
    );
  }

  List<Widget> _buildTypeSpecificProperties(UIComponent component) {
    switch (component.type) {
      case UIComponentType.text:
        return [
          _buildPropertyField('Text', component.properties['text'] ?? 'Text', (
            value,
          ) {
            setState(() {
              component.properties['text'] = value;
            });
          }),
          _buildNumberField(
            'Font Size',
            component.properties['fontSize']?.toDouble() ?? 16.0,
            (value) {
              setState(() {
                component.properties['fontSize'] = value;
              });
            },
          ),
          _buildColorField(
            'Color',
            component.properties['color'] ?? 0xFF000000,
            (value) {
              setState(() {
                component.properties['color'] = value;
              });
            },
          ),
        ];

      case UIComponentType.button:
        return [
          _buildPropertyField(
            'Text',
            component.properties['text'] ?? 'Button',
            (value) {
              setState(() {
                component.properties['text'] = value;
              });
            },
          ),
          _buildColorField(
            'Background Color',
            component.properties['backgroundColor'] ?? 0xFF2196F3,
            (value) {
              setState(() {
                component.properties['backgroundColor'] = value;
              });
            },
          ),
          _buildColorField(
            'Text Color',
            component.properties['textColor'] ?? 0xFFFFFFFF,
            (value) {
              setState(() {
                component.properties['textColor'] = value;
              });
            },
          ),
        ];

      default:
        return [];
    }
  }

  Widget _buildPropertyField(
    String label,
    String value,
    Function(String) onChanged,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: TextFormField(
        initialValue: value,
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
        ),
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildNumberField(
    String label,
    double value,
    Function(double) onChanged,
  ) {
    return TextFormField(
      initialValue: value.toString(),
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
      ),
      keyboardType: TextInputType.number,
      onChanged: (text) {
        final number = double.tryParse(text);
        if (number != null) {
          onChanged(number);
        }
      },
    );
  }

  Widget _buildColorField(String label, int value, Function(int) onChanged) {
    return ListTile(
      title: Text(label),
      trailing: GestureDetector(
        onTap: () => _showColorPicker(value, onChanged),
        child: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Color(value),
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ),
    );
  }

  Widget _buildPreviewTab() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'App Preview',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Stack(
                children: _components
                    .map((component) => _renderComponent(component))
                    .toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _addComponent(ComponentTemplate template) {
    _addComponentAtPosition(template, Offset(50, 50));
  }

  void _addComponentAtPosition(ComponentTemplate template, Offset position) {
    final component = UIComponent(
      id: 'component_${DateTime.now().millisecondsSinceEpoch}',
      type: template.type,
      position: position,
      size: _getDefaultSize(template.type),
      properties: _getDefaultProperties(template.type),
    );

    setState(() {
      _components.add(component);
      _selectedComponent = component;
    });
  }

  Size _getDefaultSize(UIComponentType type) {
    switch (type) {
      case UIComponentType.text:
        return const Size(100, 30);
      case UIComponentType.button:
        return const Size(120, 40);
      case UIComponentType.textField:
        return const Size(200, 40);
      case UIComponentType.image:
        return const Size(150, 150);
      case UIComponentType.container:
        return const Size(200, 100);
      default:
        return const Size(100, 50);
    }
  }

  Map<String, dynamic> _getDefaultProperties(UIComponentType type) {
    switch (type) {
      case UIComponentType.text:
        return {'text': 'Text', 'fontSize': 16.0, 'color': 0xFF000000};
      case UIComponentType.button:
        return {
          'text': 'Button',
          'backgroundColor': 0xFF2196F3,
          'textColor': 0xFFFFFFFF,
        };
      case UIComponentType.textField:
        return {'placeholder': 'Enter text...'};
      case UIComponentType.container:
        return {
          'backgroundColor': 0xFFF5F5F5,
          'borderRadius': 0.0,
          'borderColor': 0xFFE0E0E0,
          'borderWidth': 1.0,
        };
      default:
        return {};
    }
  }

  IconData _getComponentIcon(UIComponentType type) {
    switch (type) {
      case UIComponentType.text:
        return Icons.text_fields;
      case UIComponentType.button:
        return Icons.smart_button;
      case UIComponentType.textField:
        return Icons.input;
      case UIComponentType.image:
        return Icons.image;
      case UIComponentType.container:
        return Icons.crop_square;
      default:
        return Icons.widgets;
    }
  }

  void _resizeComponent(UIComponent component, ResizeDirection direction) {
    // Implement resize logic based on direction
  }

  void _handleComponentAction(UIComponent component, String action) {
    switch (action) {
      case 'duplicate':
        final duplicate = UIComponent(
          id: 'component_${DateTime.now().millisecondsSinceEpoch}',
          type: component.type,
          position: component.position + const Offset(20, 20),
          size: component.size,
          properties: Map.from(component.properties),
        );
        setState(() {
          _components.add(duplicate);
        });
        break;
      case 'delete':
        setState(() {
          _components.remove(component);
          if (_selectedComponent == component) {
            _selectedComponent = null;
          }
        });
        break;
    }
  }

  void _showColorPicker(int currentColor, Function(int) onChanged) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Pick a Color'),
        content: const Text('Color picker will be implemented here'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _saveComponents() {
    // Save components to the tool
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('UI components saved successfully'),
        backgroundColor: AppTheme.excelToAppColor,
      ),
    );
  }

  // Theme-related methods
  Widget _buildThemeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Prebuilt Themes',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _prebuiltThemes.length,
            itemBuilder: (context, index) {
              final theme = _prebuiltThemes[index];
              final isSelected = _selectedTheme == theme['name'];

              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedTheme = theme['name'];
                    _backgroundColor = theme['backgroundColor'];
                    _customBackgroundPath = null;
                  });
                },
                child: Container(
                  width: 100,
                  margin: const EdgeInsets.only(right: 12),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: isSelected
                          ? AppTheme.excelToAppColor
                          : Colors.grey[300]!,
                      width: isSelected ? 3 : 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            color: theme['backgroundColor'],
                            borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(7),
                            ),
                            gradient: theme['gradient'],
                          ),
                          child: Center(
                            child: Icon(
                              Icons.apps,
                              color: theme['iconColor'],
                              size: 32,
                            ),
                          ),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.all(8),
                        child: Text(
                          theme['name'],
                          style: const TextStyle(fontSize: 12),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCustomBackgroundSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Custom Background',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    Icon(Icons.image, size: 48, color: Colors.grey[400]),
                    const SizedBox(height: 8),
                    Text(
                      _customBackgroundPath != null
                          ? 'Custom image selected'
                          : 'No custom background',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 16),
            Column(
              children: [
                ElevatedButton.icon(
                  onPressed: _pickCustomBackground,
                  icon: const Icon(Icons.folder_open),
                  label: const Text('Browse'),
                ),
                const SizedBox(height: 8),
                if (_customBackgroundPath != null)
                  TextButton.icon(
                    onPressed: () {
                      setState(() {
                        _customBackgroundPath = null;
                        _selectedTheme = null;
                      });
                    },
                    icon: const Icon(Icons.clear),
                    label: const Text('Remove'),
                  ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBackgroundColorSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Background Color',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: _colorOptions.map((color) {
            final isSelected = _backgroundColor == color;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _backgroundColor = color;
                  _selectedTheme = null;
                  _customBackgroundPath = null;
                });
              },
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected
                        ? AppTheme.excelToAppColor
                        : Colors.grey[300]!,
                    width: isSelected ? 3 : 1,
                  ),
                ),
                child: isSelected
                    ? const Icon(Icons.check, color: Colors.white, size: 20)
                    : null,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildThemePreviewSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Preview',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            ElevatedButton.icon(
              onPressed: _applySelectedTheme,
              icon: const Icon(Icons.check),
              label: const Text('Apply Theme'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            color: _backgroundColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
            image: _customBackgroundPath != null
                ? DecorationImage(
                    image: AssetImage(_customBackgroundPath!),
                    fit: BoxFit.cover,
                  )
                : null,
          ),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.apps, size: 48, color: Colors.white70),
                SizedBox(height: 8),
                Text(
                  'App Preview',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _pickCustomBackground() async {
    // In a real implementation, this would use file_picker package
    // For now, simulate file selection
    setState(() {
      _customBackgroundPath = 'assets/images/custom_background.jpg';
      _selectedTheme = null;
    });

    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Custom background selected')));
  }

  void _applySelectedTheme() {
    // Apply the selected theme to the canvas
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          _selectedTheme != null
              ? 'Applied theme: $_selectedTheme'
              : _customBackgroundPath != null
              ? 'Applied custom background'
              : 'Applied background color',
        ),
      ),
    );
  }

  // Theme data
  List<Map<String, dynamic>> get _prebuiltThemes => [
    {
      'name': 'Modern Blue',
      'backgroundColor': const Color(0xFF2196F3),
      'iconColor': Colors.white,
      'gradient': const LinearGradient(
        colors: [Color(0xFF2196F3), Color(0xFF1976D2)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    },
    {
      'name': 'Dark Theme',
      'backgroundColor': const Color(0xFF212121),
      'iconColor': Colors.white,
      'gradient': const LinearGradient(
        colors: [Color(0xFF212121), Color(0xFF424242)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    },
    {
      'name': 'Green Nature',
      'backgroundColor': const Color(0xFF4CAF50),
      'iconColor': Colors.white,
      'gradient': const LinearGradient(
        colors: [Color(0xFF4CAF50), Color(0xFF388E3C)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    },
    {
      'name': 'Purple Gradient',
      'backgroundColor': const Color(0xFF9C27B0),
      'iconColor': Colors.white,
      'gradient': const LinearGradient(
        colors: [Color(0xFF9C27B0), Color(0xFF7B1FA2)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    },
    {
      'name': 'Orange Sunset',
      'backgroundColor': const Color(0xFFFF9800),
      'iconColor': Colors.white,
      'gradient': const LinearGradient(
        colors: [Color(0xFFFF9800), Color(0xFFF57C00)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    },
  ];

  List<Color> get _colorOptions => [
    Colors.white,
    Colors.grey[100]!,
    Colors.blue[50]!,
    Colors.green[50]!,
    Colors.purple[50]!,
    Colors.orange[50]!,
    Colors.red[50]!,
    Colors.teal[50]!,
    const Color(0xFF2196F3),
    const Color(0xFF4CAF50),
    const Color(0xFF9C27B0),
    const Color(0xFFFF9800),
    const Color(0xFFF44336),
    const Color(0xFF009688),
    const Color(0xFF212121),
    const Color(0xFF424242),
  ];
}

// Helper classes
class ComponentTemplate {
  final String name;
  final IconData icon;
  final UIComponentType type;

  const ComponentTemplate(this.name, this.icon, this.type);
}

enum ResizeDirection { topLeft, topRight, bottomLeft, bottomRight }

// Custom painter for grid
class GridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.2)
      ..strokeWidth = 0.5;

    const gridSize = 20.0;

    // Draw vertical lines
    for (double x = 0; x < size.width; x += gridSize) {
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }

    // Draw horizontal lines
    for (double y = 0; y < size.height; y += gridSize) {
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

extension on _VisualUIBuilderScreenState {
  void _openAdvancedLayoutCustomization() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AdvancedLayoutCustomizationScreen(),
      ),
    );
  }
}
