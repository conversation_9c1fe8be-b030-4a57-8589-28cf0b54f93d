import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/file_manager_service.dart';

class FileManagerToolbar extends ConsumerWidget {
  const FileManagerToolbar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      height: 48,
      decoration: const BoxDecoration(
        color: Color(0xFFF8F9FA),
        border: Border(
          bottom: BorderSide(color: Color(0xFFE9ECEF)),
        ),
      ),
      child: Row(
        children: [
          const SizedBox(width: 8),
          
          // File operations
          _buildToolbarButton(
            icon: Icons.create_new_folder,
            label: 'New Folder',
            onPressed: () => _createNewFolder(context),
          ),
          
          _buildToolbarButton(
            icon: Icons.copy,
            label: 'Copy',
            onPressed: () => _copySelected(context),
          ),
          
          _buildToolbarButton(
            icon: Icons.content_cut,
            label: 'Cut',
            onPressed: () => _cutSelected(context),
          ),
          
          _buildToolbarButton(
            icon: Icons.content_paste,
            label: 'Paste',
            onPressed: () => _pasteFiles(context),
          ),
          
          const VerticalDivider(width: 16),
          
          _buildToolbarButton(
            icon: Icons.delete,
            label: 'Delete',
            onPressed: () => _deleteSelected(context),
          ),
          
          _buildToolbarButton(
            icon: Icons.drive_file_rename_outline,
            label: 'Rename',
            onPressed: () => _renameSelected(context),
          ),
          
          const VerticalDivider(width: 16),
          
          _buildToolbarButton(
            icon: Icons.archive,
            label: 'Create ZIP',
            onPressed: () => _createZip(context),
          ),
          
          _buildToolbarButton(
            icon: Icons.unarchive,
            label: 'Extract',
            onPressed: () => _extractArchive(context),
          ),
          
          const VerticalDivider(width: 16),
          
          _buildToolbarButton(
            icon: Icons.info,
            label: 'Properties',
            onPressed: () => _showProperties(context),
          ),
          
          const Spacer(),
          
          // View options
          _buildToolbarButton(
            icon: Icons.view_list,
            label: 'List View',
            onPressed: () => _setListView(ref),
          ),
          
          _buildToolbarButton(
            icon: Icons.grid_view,
            label: 'Grid View',
            onPressed: () => _setGridView(ref),
          ),
          
          const SizedBox(width: 8),
        ],
      ),
    );
  }

  Widget _buildToolbarButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Tooltip(
      message: label,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(4),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 18,
                color: const Color(0xFF495057),
              ),
              const SizedBox(height: 2),
              Text(
                label,
                style: const TextStyle(
                  fontSize: 10,
                  color: Color(0xFF495057),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _createNewFolder(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        String folderName = '';
        return AlertDialog(
          title: const Text('Create New Folder'),
          content: TextField(
            decoration: const InputDecoration(
              hintText: 'Folder name',
              border: OutlineInputBorder(),
            ),
            onChanged: (value) => folderName = value,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (folderName.isNotEmpty) {
                  try {
                    await FileManagerService.createDirectory(folderName);
                    if (context.mounted) {
                      Navigator.pop(context);
                      _showSuccess(context, 'Folder created successfully');
                    }
                  } catch (error) {
                    if (context.mounted) {
                      _showError(context, 'Error creating folder: $error');
                    }
                  }
                }
              },
              child: const Text('Create'),
            ),
          ],
        );
      },
    );
  }

  void _copySelected(BuildContext context) {
    // Implement copy functionality
    _showInfo(context, 'Copy functionality will be implemented');
  }

  void _cutSelected(BuildContext context) {
    // Implement cut functionality
    _showInfo(context, 'Cut functionality will be implemented');
  }

  void _pasteFiles(BuildContext context) {
    // Implement paste functionality
    _showInfo(context, 'Paste functionality will be implemented');
  }

  void _deleteSelected(BuildContext context) {
    // Implement delete functionality
    _showInfo(context, 'Delete functionality will be implemented');
  }

  void _renameSelected(BuildContext context) {
    // Implement rename functionality
    _showInfo(context, 'Rename functionality will be implemented');
  }

  void _createZip(BuildContext context) {
    // Implement ZIP creation
    _showInfo(context, 'ZIP creation functionality will be implemented');
  }

  void _extractArchive(BuildContext context) {
    // Implement archive extraction
    _showInfo(context, 'Archive extraction functionality will be implemented');
  }

  void _showProperties(BuildContext context) {
    // Implement properties dialog
    _showInfo(context, 'Properties dialog will be implemented');
  }

  void _setListView(WidgetRef ref) {
    // Implement list view setting
  }

  void _setGridView(WidgetRef ref) {
    // Implement grid view setting
  }

  void _showSuccess(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF27AE60),
      ),
    );
  }

  void _showError(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showInfo(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF3498DB),
      ),
    );
  }
}
