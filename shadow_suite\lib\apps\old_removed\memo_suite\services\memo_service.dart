import 'dart:async';
import 'dart:io';
import 'dart:convert';
import '../models/memo_models.dart';
import '../../../core/database/database_service.dart';
import '../../../core/services/error_handler.dart' as error_handler;

class MemoService {
  static final List<Memo> _memos = [];
  static final List<MemoTemplate> _templates = [];
  static final List<VoiceRecording> _voiceRecordings = [];
  static final List<CollaborationSession> _collaborationSessions = [];
  static final List<MemoVersion> _versions = [];

  static final StreamController<MemoChangeEvent> _changeController =
      StreamController<MemoChangeEvent>.broadcast();

  // Initialize memo service
  static Future<void> initialize() async {
    await _loadAllData();
    await _initializeDefaultTemplates();
  }

  // FEATURE 1: Advanced Note-Taking with Rich Text
  static Future<Memo> createMemo({
    required String title,
    required String content,
    required MemoType type,
    List<String>? tags,
    MemoCategory category = MemoCategory.general,
    bool isPinned = false,
    String? parentId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final memo = Memo(
        id: 'memo_${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        content: content,
        type: type,
        tags: tags ?? [],
        category: category,
        isPinned: isPinned,
        isArchived: false,
        isEncrypted: false,
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
        parentId: parentId,
        attachments: [],
        metadata: metadata ?? {},
      );

      await DatabaseService.safeInsert('memos', memo.toJson());
      _memos.add(memo);

      // Create initial version
      await _createVersion(memo, 'Initial creation');

      _notifyChange(
        MemoChangeEvent(
          type: MemoChangeType.created,
          memoId: memo.id,
          timestamp: DateTime.now(),
        ),
      );

      return memo;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.database,
        context: 'Create memo',
      );
      rethrow;
    }
  }

  // FEATURE 2: AI-Powered Transcription (Offline)
  static Future<VoiceRecording> recordVoice({
    required String memoId,
    required String filePath,
    required Duration duration,
    AudioQuality quality = AudioQuality.medium,
  }) async {
    try {
      final file = File(filePath);
      final fileSize = await file.length();

      final recording = VoiceRecording(
        id: 'voice_${DateTime.now().millisecondsSinceEpoch}',
        memoId: memoId,
        filePath: filePath,
        duration: duration,
        recordedAt: DateTime.now(),
        fileSize: fileSize,
        quality: quality,
      );

      await DatabaseService.safeInsert('voice_recordings', recording.toJson());
      _voiceRecordings.add(recording);

      // Trigger offline transcription
      await _transcribeAudio(recording);

      return recording;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.database,
        context: 'Record voice',
      );
      rethrow;
    }
  }

  // FEATURE 3: Collaborative Editing (Local Network)
  static Future<CollaborationSession> startCollaboration({
    required String memoId,
    required String ownerId,
    List<String>? participants,
    Map<String, dynamic>? settings,
  }) async {
    try {
      final session = CollaborationSession(
        id: 'collab_${DateTime.now().millisecondsSinceEpoch}',
        memoId: memoId,
        participants: participants ?? [ownerId],
        ownerId: ownerId,
        status: SessionStatus.active,
        startedAt: DateTime.now(),
        changes: [],
        settings: settings ?? {},
      );

      await DatabaseService.safeInsert(
        'collaboration_sessions',
        session.toJson(),
      );
      _collaborationSessions.add(session);

      return session;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.database,
        context: 'Start collaboration',
      );
      rethrow;
    }
  }

  // FEATURE 4: Version Control System
  static Future<MemoVersion> createVersion({
    required String memoId,
    String? changeDescription,
    required String authorId,
  }) async {
    try {
      final memo = _memos.firstWhere((m) => m.id == memoId);
      return await _createVersion(memo, changeDescription, authorId);
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Create version',
      );
      rethrow;
    }
  }

  // FEATURE 5: Smart Search and Filtering
  static List<Memo> searchMemos({
    String? query,
    List<String>? tags,
    MemoCategory? category,
    MemoType? type,
    bool? isPinned,
    bool? isArchived,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    try {
      var results = _memos.where((memo) {
        // Query search
        if (query != null && query.isNotEmpty) {
          final queryLower = query.toLowerCase();
          final titleMatch = memo.title.toLowerCase().contains(queryLower);
          final contentMatch = memo.content.toLowerCase().contains(queryLower);
          final tagMatch = memo.tags.any(
            (tag) => tag.toLowerCase().contains(queryLower),
          );
          if (!titleMatch && !contentMatch && !tagMatch) return false;
        }

        // Filter by tags
        if (tags != null && tags.isNotEmpty) {
          if (!tags.any((tag) => memo.tags.contains(tag))) return false;
        }

        // Filter by category
        if (category != null && memo.category != category) return false;

        // Filter by type
        if (type != null && memo.type != type) return false;

        // Filter by pinned status
        if (isPinned != null && memo.isPinned != isPinned) return false;

        // Filter by archived status
        if (isArchived != null && memo.isArchived != isArchived) return false;

        // Filter by date range
        if (startDate != null && memo.createdAt.isBefore(startDate)) {
          return false;
        }
        if (endDate != null && memo.createdAt.isAfter(endDate)) {
          return false;
        }

        return true;
      }).toList();

      // Sort by relevance and last modified
      results.sort((a, b) {
        if (a.isPinned && !b.isPinned) return -1;
        if (!a.isPinned && b.isPinned) return 1;
        return b.lastModified.compareTo(a.lastModified);
      });

      return results;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Search memos',
      );
      return [];
    }
  }

  // FEATURE 6: Template System
  static Future<MemoTemplate> createTemplate({
    required String name,
    required String description,
    required String content,
    required MemoType type,
    List<String>? placeholders,
    MemoCategory category = MemoCategory.general,
  }) async {
    try {
      final template = MemoTemplate(
        id: 'template_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        description: description,
        content: content,
        type: type,
        placeholders: placeholders ?? [],
        category: category,
        isBuiltIn: false,
        usageCount: 0,
        createdAt: DateTime.now(),
      );

      await DatabaseService.safeInsert('memo_templates', template.toJson());
      _templates.add(template);

      return template;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.database,
        context: 'Create template',
      );
      rethrow;
    }
  }

  // FEATURE 7: Auto-Save and Draft Management
  static Future<void> autoSave(String memoId, String content) async {
    try {
      final memoIndex = _memos.indexWhere((m) => m.id == memoId);
      if (memoIndex == -1) return;

      final updatedMemo = _memos[memoIndex].copyWith(content: content);

      await DatabaseService.safeUpdate(
        'memos',
        updatedMemo.toJson(),
        where: 'id = ?',
        whereArgs: [memoId],
      );

      _memos[memoIndex] = updatedMemo;

      _notifyChange(
        MemoChangeEvent(
          type: MemoChangeType.autoSaved,
          memoId: memoId,
          timestamp: DateTime.now(),
        ),
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.database,
        context: 'Auto-save memo',
      );
    }
  }

  // FEATURE 8: Export and Import Capabilities
  static Future<String> exportMemo(String memoId, ExportFormat format) async {
    try {
      final memo = _memos.firstWhere((m) => m.id == memoId);

      switch (format) {
        case ExportFormat.markdown:
          return _exportToMarkdown(memo);
        case ExportFormat.html:
          return _exportToHtml(memo);
        case ExportFormat.pdf:
          return await _exportToPdf(memo);
        case ExportFormat.json:
          return jsonEncode(memo.toJson());
        case ExportFormat.txt:
          return _exportToText(memo);
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Export memo',
      );
      rethrow;
    }
  }

  // FEATURE 9: Tagging and Organization System
  static Future<void> addTag(String memoId, String tag) async {
    try {
      final memoIndex = _memos.indexWhere((m) => m.id == memoId);
      if (memoIndex == -1) return;

      final memo = _memos[memoIndex];
      if (memo.tags.contains(tag)) return;

      final updatedMemo = memo.copyWith(tags: [...memo.tags, tag]);

      await DatabaseService.safeUpdate(
        'memos',
        updatedMemo.toJson(),
        where: 'id = ?',
        whereArgs: [memoId],
      );

      _memos[memoIndex] = updatedMemo;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.database,
        context: 'Add tag',
      );
    }
  }

  // FEATURE 10: Offline Synchronization
  static Future<void> syncOfflineChanges() async {
    try {
      // In a real implementation, this would sync with other devices
      // For offline-first, we ensure all changes are persisted locally

      for (final memo in _memos) {
        await DatabaseService.safeUpdate(
          'memos',
          memo.toJson(),
          where: 'id = ?',
          whereArgs: [memo.id],
        );
      }

      _notifyChange(
        MemoChangeEvent(
          type: MemoChangeType.synced,
          memoId: '',
          timestamp: DateTime.now(),
        ),
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.database,
        context: 'Sync offline changes',
      );
    }
  }

  // Helper methods
  static Future<MemoVersion> _createVersion(
    Memo memo, [
    String? description,
    String? authorId,
  ]) async {
    final existingVersions = _versions
        .where((v) => v.memoId == memo.id)
        .toList();
    final versionNumber = existingVersions.length + 1;

    final version = MemoVersion(
      id: 'version_${DateTime.now().millisecondsSinceEpoch}',
      memoId: memo.id,
      versionNumber: versionNumber,
      content: memo.content,
      changeDescription: description,
      authorId: authorId ?? 'system',
      createdAt: DateTime.now(),
      changedSections: [],
      metadata: {},
    );

    await DatabaseService.safeInsert('memo_versions', version.toJson());
    _versions.add(version);

    return version;
  }

  static Future<void> _transcribeAudio(VoiceRecording recording) async {
    try {
      // Offline transcription using local speech recognition
      // This is a simplified implementation - in production, use a proper offline STT library
      final transcription = 'Transcribed content from audio recording';

      final updatedRecording = VoiceRecording(
        id: recording.id,
        memoId: recording.memoId,
        filePath: recording.filePath,
        duration: recording.duration,
        transcription: transcription,
        confidence: 0.85,
        recordedAt: recording.recordedAt,
        fileSize: recording.fileSize,
        quality: recording.quality,
      );

      await DatabaseService.safeUpdate(
        'voice_recordings',
        updatedRecording.toJson(),
        where: 'id = ?',
        whereArgs: [recording.id],
      );

      final recordingIndex = _voiceRecordings.indexWhere(
        (r) => r.id == recording.id,
      );
      if (recordingIndex != -1) {
        _voiceRecordings[recordingIndex] = updatedRecording;
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Transcribe audio',
      );
    }
  }

  static String _exportToMarkdown(Memo memo) {
    final buffer = StringBuffer();
    buffer.writeln('# ${memo.title}');
    buffer.writeln();
    buffer.writeln('**Created:** ${memo.createdAt.toIso8601String()}');
    buffer.writeln('**Modified:** ${memo.lastModified.toIso8601String()}');
    buffer.writeln('**Category:** ${memo.category.name}');
    if (memo.tags.isNotEmpty) {
      buffer.writeln('**Tags:** ${memo.tags.join(', ')}');
    }
    buffer.writeln();
    buffer.writeln('---');
    buffer.writeln();
    buffer.writeln(memo.content);
    return buffer.toString();
  }

  static String _exportToHtml(Memo memo) {
    return '''
<!DOCTYPE html>
<html>
<head>
    <title>${memo.title}</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { border-bottom: 1px solid #ccc; padding-bottom: 20px; margin-bottom: 20px; }
        .meta { color: #666; font-size: 14px; }
        .content { line-height: 1.6; }
    </style>
</head>
<body>
    <div class="header">
        <h1>${memo.title}</h1>
        <div class="meta">
            <p>Created: ${memo.createdAt.toIso8601String()}</p>
            <p>Modified: ${memo.lastModified.toIso8601String()}</p>
            <p>Category: ${memo.category.name}</p>
            ${memo.tags.isNotEmpty ? '<p>Tags: ${memo.tags.join(', ')}</p>' : ''}
        </div>
    </div>
    <div class="content">
        ${memo.content.replaceAll('\n', '<br>')}
    </div>
</body>
</html>
''';
  }

  static Future<String> _exportToPdf(Memo memo) async {
    // In a real implementation, use a PDF generation library like pdf package
    // Generate a unique filename with timestamp
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final filename = 'memo_${memo.id}_$timestamp.pdf';

    // Simulate PDF generation process
    await Future.delayed(const Duration(milliseconds: 500));

    return 'exports/$filename';
  }

  static String _exportToText(Memo memo) {
    final buffer = StringBuffer();
    buffer.writeln(memo.title);
    buffer.writeln('=' * memo.title.length);
    buffer.writeln();
    buffer.writeln('Created: ${memo.createdAt.toIso8601String()}');
    buffer.writeln('Modified: ${memo.lastModified.toIso8601String()}');
    buffer.writeln('Category: ${memo.category.name}');
    if (memo.tags.isNotEmpty) {
      buffer.writeln('Tags: ${memo.tags.join(', ')}');
    }
    buffer.writeln();
    buffer.writeln(memo.content);
    return buffer.toString();
  }

  static void _notifyChange(MemoChangeEvent event) {
    _changeController.add(event);
  }

  // Data loading methods
  static Future<void> _loadAllData() async {
    await Future.wait([
      _loadMemos(),
      _loadTemplates(),
      _loadVoiceRecordings(),
      _loadCollaborationSessions(),
      _loadVersions(),
    ]);
  }

  static Future<void> _loadMemos() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM memos');
      _memos.clear();
      for (final row in results) {
        _memos.add(Memo.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Load memos',
      );
    }
  }

  static Future<void> _loadTemplates() async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM memo_templates',
      );
      _templates.clear();
      for (final row in results) {
        _templates.add(MemoTemplate.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Load templates',
      );
    }
  }

  static Future<void> _loadVoiceRecordings() async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM voice_recordings',
      );
      _voiceRecordings.clear();
      for (final row in results) {
        _voiceRecordings.add(VoiceRecording.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Load voice recordings',
      );
    }
  }

  static Future<void> _loadCollaborationSessions() async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM collaboration_sessions',
      );
      _collaborationSessions.clear();
      for (final row in results) {
        _collaborationSessions.add(CollaborationSession.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Load collaboration sessions',
      );
    }
  }

  static Future<void> _loadVersions() async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM memo_versions',
      );
      _versions.clear();
      for (final row in results) {
        _versions.add(MemoVersion.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Load versions',
      );
    }
  }

  static Future<void> _initializeDefaultTemplates() async {
    if (_templates.isEmpty) {
      await _addDefaultTemplates();
    }
  }

  static Future<void> _addDefaultTemplates() async {
    final defaultTemplates = [
      {
        'name': 'Meeting Notes',
        'description': 'Template for meeting notes',
        'content': '''# Meeting Notes - {{date}}

## Attendees
- {{attendees}}

## Agenda
1. {{agenda_item_1}}
2. {{agenda_item_2}}

## Discussion
{{discussion}}

## Action Items
- [ ] {{action_item_1}}
- [ ] {{action_item_2}}

## Next Meeting
Date: {{next_meeting_date}}
''',
        'type': MemoType.markdown,
        'category': MemoCategory.meeting,
        'placeholders': [
          'date',
          'attendees',
          'agenda_item_1',
          'agenda_item_2',
          'discussion',
          'action_item_1',
          'action_item_2',
          'next_meeting_date',
        ],
      },
      {
        'name': 'Daily Journal',
        'description': 'Template for daily journaling',
        'content': '''# Daily Journal - {{date}}

## Mood
{{mood}}

## Today's Highlights
- {{highlight_1}}
- {{highlight_2}}
- {{highlight_3}}

## Challenges
{{challenges}}

## Tomorrow's Goals
- {{goal_1}}
- {{goal_2}}

## Gratitude
{{gratitude}}
''',
        'type': MemoType.markdown,
        'category': MemoCategory.personal,
        'placeholders': [
          'date',
          'mood',
          'highlight_1',
          'highlight_2',
          'highlight_3',
          'challenges',
          'goal_1',
          'goal_2',
          'gratitude',
        ],
      },
    ];

    for (final templateData in defaultTemplates) {
      await createTemplate(
        name: templateData['name'] as String,
        description: templateData['description'] as String,
        content: templateData['content'] as String,
        type: templateData['type'] as MemoType,
        placeholders: templateData['placeholders'] as List<String>,
        category: templateData['category'] as MemoCategory,
      );
    }
  }

  // Getters
  static List<Memo> get memos => List.unmodifiable(_memos);
  static List<MemoTemplate> get templates => List.unmodifiable(_templates);
  static List<VoiceRecording> get voiceRecordings =>
      List.unmodifiable(_voiceRecordings);
  static List<CollaborationSession> get collaborationSessions =>
      List.unmodifiable(_collaborationSessions);
  static List<MemoVersion> get versions => List.unmodifiable(_versions);
  static Stream<MemoChangeEvent> get changeStream => _changeController.stream;

  // Dispose
  static void dispose() {
    _memos.clear();
    _templates.clear();
    _voiceRecordings.clear();
    _collaborationSessions.clear();
    _versions.clear();
    _changeController.close();
  }
}

// Additional models and enums
enum ExportFormat { markdown, html, pdf, json, txt }

enum MemoChangeType { created, updated, deleted, autoSaved, synced }

class MemoChangeEvent {
  final MemoChangeType type;
  final String memoId;
  final DateTime timestamp;

  const MemoChangeEvent({
    required this.type,
    required this.memoId,
    required this.timestamp,
  });
}
