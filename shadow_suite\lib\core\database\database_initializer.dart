import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

/// Centralized database initialization service
/// Ensures proper database factory setup across all platforms
class DatabaseInitializer {
  static bool _isInitialized = false;
  static final DatabaseInitializer _instance = DatabaseInitializer._internal();
  
  factory DatabaseInitializer() => _instance;
  DatabaseInitializer._internal();

  /// Initialize database factory for all platforms
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kIsWeb) {
        // Web platform - no database initialization needed
        // Use in-memory storage or IndexedDB through other packages
        print('DatabaseInitializer: Web platform detected - skipping SQLite initialization');
      } else if (Platform.isAndroid || Platform.isIOS) {
        // Mobile platforms - use default sqflite implementation
        print('DatabaseInitializer: Mobile platform detected - using default sqflite');
        // No additional setup needed for mobile
      } else if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
        // Desktop platforms - use FFI implementation
        print('DatabaseInitializer: Desktop platform detected - initializing sqflite_ffi');
        sqfliteFfiInit();
        databaseFactory = databaseFactoryFfi;
      } else {
        print('DatabaseInitializer: Unknown platform - using default sqflite');
      }

      _isInitialized = true;
      print('DatabaseInitializer: Database factory initialized successfully');
    } catch (e) {
      print('DatabaseInitializer: Error initializing database factory: $e');
      // Don't rethrow - allow app to continue with potential fallback
    }
  }

  /// Check if database factory is properly initialized
  static bool get isInitialized => _isInitialized;

  /// Get current database factory (for debugging)
  static DatabaseFactory get currentFactory => databaseFactory;

  /// Ensure database factory is initialized before any database operations
  static Future<void> ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Safe database opening with proper error handling
  static Future<Database?> safeOpenDatabase(
    String path, {
    int? version,
    OnDatabaseConfigureFn? onConfigure,
    OnDatabaseCreateFn? onCreate,
    OnDatabaseVersionChangeFn? onUpgrade,
    OnDatabaseVersionChangeFn? onDowngrade,
    OnDatabaseOpenFn? onOpen,
    bool readOnly = false,
    bool singleInstance = true,
  }) async {
    try {
      // Ensure database factory is initialized
      await ensureInitialized();

      // Open database with proper error handling
      return await openDatabase(
        path,
        version: version,
        onConfigure: onConfigure,
        onCreate: onCreate,
        onUpgrade: onUpgrade,
        onDowngrade: onDowngrade,
        onOpen: onOpen,
        readOnly: readOnly,
        singleInstance: singleInstance,
      );
    } catch (e) {
      print('DatabaseInitializer: Error opening database at $path: $e');
      
      // For web platform, return null to allow fallback to in-memory storage
      if (kIsWeb) {
        print('DatabaseInitializer: Web platform - returning null for fallback storage');
        return null;
      }
      
      // For other platforms, rethrow the error
      rethrow;
    }
  }

  /// Reset initialization state (for testing)
  static void reset() {
    _isInitialized = false;
  }
}
