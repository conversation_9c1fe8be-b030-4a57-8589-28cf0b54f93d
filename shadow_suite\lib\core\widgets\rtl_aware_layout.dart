import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/settings_service.dart';

/// RTL-aware layout wrapper that automatically applies text direction
/// and UI mirroring based on current language settings
class RTLAwareLayout extends ConsumerWidget {
  final Widget child;
  final bool forceRTL;
  final bool forceLTR;

  const RTLAwareLayout({
    super.key,
    required this.child,
    this.forceRTL = false,
    this.forceLTR = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final language = ref.watch(languageProvider);
    final rtlEnabled = ref.watch(rtlProvider);
    
    // Determine text direction
    TextDirection textDirection;
    if (forceRTL) {
      textDirection = TextDirection.rtl;
    } else if (forceLTR) {
      textDirection = TextDirection.ltr;
    } else if (language == 'ar' || rtlEnabled) {
      textDirection = TextDirection.rtl;
    } else {
      textDirection = TextDirection.ltr;
    }

    return Directionality(
      textDirection: textDirection,
      child: child,
    );
  }
}

/// RTL-aware text widget that automatically applies proper text direction
class RTLAwareText extends ConsumerWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final bool? softWrap;

  const RTLAwareText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.softWrap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final language = ref.watch(languageProvider);
    final rtlEnabled = ref.watch(rtlProvider);
    
    // Auto-detect text direction based on content and language
    TextDirection textDirection;
    if (language == 'ar' || rtlEnabled) {
      textDirection = TextDirection.rtl;
    } else {
      textDirection = TextDirection.ltr;
    }

    // Auto-align text based on direction
    TextAlign effectiveTextAlign = textAlign ?? 
        (textDirection == TextDirection.rtl ? TextAlign.right : TextAlign.left);

    return Directionality(
      textDirection: textDirection,
      child: Text(
        text,
        style: style,
        textAlign: effectiveTextAlign,
        maxLines: maxLines,
        overflow: overflow,
        softWrap: softWrap,
        textDirection: textDirection,
      ),
    );
  }
}

/// RTL-aware row that automatically reverses children for RTL languages
class RTLAwareRow extends ConsumerWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;

  const RTLAwareRow({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final language = ref.watch(languageProvider);
    final rtlEnabled = ref.watch(rtlProvider);
    
    final isRTL = language == 'ar' || rtlEnabled;
    final effectiveChildren = isRTL ? children.reversed.toList() : children;
    
    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: effectiveChildren,
    );
  }
}

/// RTL-aware padding that automatically adjusts for text direction
class RTLAwarePadding extends ConsumerWidget {
  final Widget child;
  final double? left;
  final double? right;
  final double? top;
  final double? bottom;
  final double? horizontal;
  final double? vertical;
  final double? all;

  const RTLAwarePadding({
    super.key,
    required this.child,
    this.left,
    this.right,
    this.top,
    this.bottom,
    this.horizontal,
    this.vertical,
    this.all,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final language = ref.watch(languageProvider);
    final rtlEnabled = ref.watch(rtlProvider);
    
    final isRTL = language == 'ar' || rtlEnabled;
    
    EdgeInsets padding;
    if (all != null) {
      padding = EdgeInsets.all(all!);
    } else if (horizontal != null || vertical != null) {
      padding = EdgeInsets.symmetric(
        horizontal: horizontal ?? 0,
        vertical: vertical ?? 0,
      );
    } else {
      // Swap left and right for RTL
      final effectiveLeft = isRTL ? (right ?? 0) : (left ?? 0);
      final effectiveRight = isRTL ? (left ?? 0) : (right ?? 0);
      
      padding = EdgeInsets.only(
        left: effectiveLeft,
        right: effectiveRight,
        top: top ?? 0,
        bottom: bottom ?? 0,
      );
    }

    return Padding(
      padding: padding,
      child: child,
    );
  }
}

/// RTL-aware margin that automatically adjusts for text direction
class RTLAwareMargin extends ConsumerWidget {
  final Widget child;
  final double? left;
  final double? right;
  final double? top;
  final double? bottom;
  final double? horizontal;
  final double? vertical;
  final double? all;

  const RTLAwareMargin({
    super.key,
    required this.child,
    this.left,
    this.right,
    this.top,
    this.bottom,
    this.horizontal,
    this.vertical,
    this.all,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final language = ref.watch(languageProvider);
    final rtlEnabled = ref.watch(rtlProvider);
    
    final isRTL = language == 'ar' || rtlEnabled;
    
    EdgeInsets margin;
    if (all != null) {
      margin = EdgeInsets.all(all!);
    } else if (horizontal != null || vertical != null) {
      margin = EdgeInsets.symmetric(
        horizontal: horizontal ?? 0,
        vertical: vertical ?? 0,
      );
    } else {
      // Swap left and right for RTL
      final effectiveLeft = isRTL ? (right ?? 0) : (left ?? 0);
      final effectiveRight = isRTL ? (left ?? 0) : (right ?? 0);
      
      margin = EdgeInsets.only(
        left: effectiveLeft,
        right: effectiveRight,
        top: top ?? 0,
        bottom: bottom ?? 0,
      );
    }

    return Container(
      margin: margin,
      child: child,
    );
  }
}

/// RTL-aware icon button that automatically adjusts icon direction
class RTLAwareIconButton extends ConsumerWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final String? tooltip;
  final Color? color;
  final double? iconSize;
  final bool autoFlip;

  const RTLAwareIconButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.tooltip,
    this.color,
    this.iconSize,
    this.autoFlip = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final language = ref.watch(languageProvider);
    final rtlEnabled = ref.watch(rtlProvider);
    
    final isRTL = language == 'ar' || rtlEnabled;
    
    // Auto-flip directional icons for RTL
    IconData effectiveIcon = icon;
    if (autoFlip && isRTL) {
      switch (icon) {
        case Icons.arrow_forward:
          effectiveIcon = Icons.arrow_back;
          break;
        case Icons.arrow_back:
          effectiveIcon = Icons.arrow_forward;
          break;
        case Icons.arrow_forward_ios:
          effectiveIcon = Icons.arrow_back_ios;
          break;
        case Icons.arrow_back_ios:
          effectiveIcon = Icons.arrow_forward_ios;
          break;
        case Icons.chevron_right:
          effectiveIcon = Icons.chevron_left;
          break;
        case Icons.chevron_left:
          effectiveIcon = Icons.chevron_right;
          break;
        default:
          effectiveIcon = icon;
      }
    }

    return IconButton(
      icon: Icon(effectiveIcon),
      onPressed: onPressed,
      tooltip: tooltip,
      color: color,
      iconSize: iconSize,
    );
  }
}

/// Utility class for RTL-aware layout helpers
class RTLUtils {
  /// Get effective text direction based on language
  static TextDirection getTextDirection(String language, bool rtlEnabled) {
    return (language == 'ar' || rtlEnabled) ? TextDirection.rtl : TextDirection.ltr;
  }
  
  /// Check if current language is RTL
  static bool isRTL(String language, bool rtlEnabled) {
    return language == 'ar' || rtlEnabled;
  }
  
  /// Get appropriate text alignment for current direction
  static TextAlign getTextAlign(String language, bool rtlEnabled, {TextAlign? fallback}) {
    final isRTL = language == 'ar' || rtlEnabled;
    return fallback ?? (isRTL ? TextAlign.right : TextAlign.left);
  }
  
  /// Swap EdgeInsets for RTL
  static EdgeInsets swapHorizontalInsets(EdgeInsets insets, bool isRTL) {
    if (!isRTL) return insets;
    return EdgeInsets.only(
      left: insets.right,
      right: insets.left,
      top: insets.top,
      bottom: insets.bottom,
    );
  }
}
