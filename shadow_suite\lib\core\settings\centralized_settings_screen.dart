import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../theme/app_theme.dart';
import '../providers/settings_providers.dart';

class CentralizedSettingsScreen extends ConsumerStatefulWidget {
  const CentralizedSettingsScreen({super.key});

  @override
  ConsumerState<CentralizedSettingsScreen> createState() =>
      _CentralizedSettingsScreenState();
}

class _CentralizedSettingsScreenState
    extends ConsumerState<CentralizedSettingsScreen> {
  final Map<String, bool> _expandedSections = {
    'appearance': false,
    'apps': false,
    'data': false,
    'privacy': false,
    'advanced': false,
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWelcomeCard(),
            const SizedBox(height: 24),
            _buildSettingsSection(
              'appearance',
              'Appearance & Theme',
              Icons.palette,
              _buildAppearanceSettings(),
            ),
            const SizedBox(height: 16),
            _buildSettingsSection(
              'apps',
              'App Settings',
              Icons.apps,
              _buildAppSettings(),
            ),
            const SizedBox(height: 16),
            _buildSettingsSection(
              'data',
              'Data Management',
              Icons.storage,
              _buildDataSettings(),
            ),
            const SizedBox(height: 16),
            _buildSettingsSection(
              'privacy',
              'Privacy & Security',
              Icons.security,
              _buildPrivacySettings(),
            ),
            const SizedBox(height: 16),
            _buildSettingsSection(
              'advanced',
              'Advanced Settings',
              Icons.settings_applications,
              _buildAdvancedSettings(),
            ),
            const SizedBox(height: 32),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.settings,
                color: AppTheme.primaryColor,
                size: 32,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Shadow Suite Settings',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Customize your experience across all mini-apps',
                    style: Theme.of(
                      context,
                    ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsSection(
    String key,
    String title,
    IconData icon,
    Widget content,
  ) {
    final isExpanded = _expandedSections[key] ?? false;

    return Card(
      child: Column(
        children: [
          ListTile(
            leading: Icon(icon, color: AppTheme.primaryColor),
            title: Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            trailing: Icon(
              isExpanded ? Icons.expand_less : Icons.expand_more,
              color: AppTheme.primaryColor,
            ),
            onTap: () {
              setState(() {
                _expandedSections[key] = !isExpanded;
              });
            },
          ),
          if (isExpanded) ...[
            const Divider(height: 1),
            Padding(padding: const EdgeInsets.all(16), child: content),
          ],
        ],
      ),
    );
  }

  Widget _buildAppearanceSettings() {
    final themeMode = ref.watch(themeModeProvider);
    final primaryColor = ref.watch(primaryColorProvider);
    final fontSize = ref.watch(fontSizeProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Theme Mode',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        SegmentedButton<ThemeMode>(
          segments: const [
            ButtonSegment(value: ThemeMode.system, label: Text('System')),
            ButtonSegment(value: ThemeMode.light, label: Text('Light')),
            ButtonSegment(value: ThemeMode.dark, label: Text('Dark')),
          ],
          selected: {themeMode},
          onSelectionChanged: (Set<ThemeMode> selection) {
            ref.read(themeModeProvider.notifier).setThemeMode(selection.first);
          },
        ),
        const SizedBox(height: 24),
        Text(
          'Primary Color',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: AppTheme.colorOptions.map((color) {
            final isSelected = color.toARGB32() == primaryColor.toARGB32();
            return GestureDetector(
              onTap: () {
                ref.read(primaryColorProvider.notifier).setPrimaryColor(color);
              },
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected ? Colors.black : Colors.grey[300]!,
                    width: isSelected ? 3 : 1,
                  ),
                ),
                child: isSelected
                    ? const Icon(Icons.check, color: Colors.white, size: 20)
                    : null,
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 24),
        Text(
          'Font Size',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        SegmentedButton<FontSize>(
          segments: const [
            ButtonSegment(value: FontSize.small, label: Text('Small')),
            ButtonSegment(value: FontSize.medium, label: Text('Medium')),
            ButtonSegment(value: FontSize.large, label: Text('Large')),
          ],
          selected: {fontSize},
          onSelectionChanged: (Set<FontSize> selection) {
            ref.read(fontSizeProvider.notifier).setFontSize(selection.first);
          },
        ),
      ],
    );
  }

  Widget _buildAppSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildAppSettingsTile(
          'Money Manager',
          'Financial tracking and budgeting',
          Icons.account_balance_wallet,
          AppTheme.moneyManagerColor,
          () => _showAppSettings('money_manager'),
        ),
        const SizedBox(height: 12),
        _buildAppSettingsTile(
          'Islamic App',
          'Quran, prayers, and Islamic tools',
          Icons.mosque,
          AppTheme.islamicAppColor,
          () => _showAppSettings('islamic_app'),
        ),
        const SizedBox(height: 12),
        _buildAppSettingsTile(
          'Memo Suite',
          'Notes and voice recordings',
          Icons.note,
          AppTheme.memoSuiteColor,
          () => _showAppSettings('memo_suite'),
        ),
        const SizedBox(height: 12),
        _buildAppSettingsTile(
          'ShadowPlayer',
          'Media player and library',
          Icons.play_circle_filled,
          const Color(0xFFE74C3C),
          () => _showAppSettings('shadow_player'),
        ),
        const SizedBox(height: 12),
        _buildAppSettingsTile(
          'Tools Builder',
          'Excel to App conversion and spreadsheet tools',
          Icons.build,
          AppTheme.excelToAppColor,
          () => _showToolsBuilderSettings(),
        ),
      ],
    );
  }

  Widget _buildAppSettingsTile(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
        subtitle: Text(subtitle),
        trailing: Icon(Icons.arrow_forward_ios, size: 16, color: color),
        onTap: onTap,
      ),
    );
  }

  Widget _buildDataSettings() {
    return Column(
      children: [
        ListTile(
          leading: const Icon(Icons.backup),
          title: const Text('Create Backup'),
          subtitle: const Text('Export all your data'),
          trailing: const Icon(Icons.arrow_forward_ios, size: 16),
          onTap: _createBackup,
        ),
        ListTile(
          leading: const Icon(Icons.restore),
          title: const Text('Restore from Backup'),
          subtitle: const Text('Import previously saved data'),
          trailing: const Icon(Icons.arrow_forward_ios, size: 16),
          onTap: _restoreBackup,
        ),
        ListTile(
          leading: const Icon(Icons.delete_forever, color: Colors.red),
          title: const Text(
            'Clear All Data',
            style: TextStyle(color: Colors.red),
          ),
          subtitle: const Text('Permanently delete all data'),
          trailing: const Icon(
            Icons.arrow_forward_ios,
            size: 16,
            color: Colors.red,
          ),
          onTap: _clearAllData,
        ),
      ],
    );
  }

  Widget _buildPrivacySettings() {
    return Column(
      children: [
        SwitchListTile(
          title: const Text('Analytics'),
          subtitle: const Text('Help improve the app'),
          value: ref.watch(analyticsEnabledProvider),
          onChanged: (value) {
            ref
                .read(analyticsEnabledProvider.notifier)
                .setAnalyticsEnabled(value);
          },
        ),
        SwitchListTile(
          title: const Text('Crash Reports'),
          subtitle: const Text('Automatically send crash reports'),
          value: ref.watch(crashReportsEnabledProvider),
          onChanged: (value) {
            ref
                .read(crashReportsEnabledProvider.notifier)
                .setCrashReportsEnabled(value);
          },
        ),
      ],
    );
  }

  Widget _buildAdvancedSettings() {
    return Column(
      children: [
        ListTile(
          leading: const Icon(Icons.developer_mode),
          title: const Text('Developer Options'),
          subtitle: const Text('Advanced debugging features'),
          trailing: const Icon(Icons.arrow_forward_ios, size: 16),
          onTap: _showDeveloperOptions,
        ),
        ListTile(
          leading: const Icon(Icons.info),
          title: const Text('About'),
          subtitle: const Text('Version and license information'),
          trailing: const Icon(Icons.arrow_forward_ios, size: 16),
          onTap: _showAbout,
        ),
      ],
    );
  }

  Widget _buildFooter() {
    return Center(
      child: Column(
        children: [
          Text(
            'Shadow Suite v1.0.0',
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'Built with Flutter',
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  void _showAppSettings(String appId) {
    // Navigate to app-specific settings
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${appId.replaceAll('_', ' ').toUpperCase()} Settings'),
        content: const Text('App-specific settings will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showToolsBuilderSettings() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 600,
          height: 500,
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.build, color: AppTheme.excelToAppColor, size: 28),
                  const SizedBox(width: 12),
                  Text(
                    'Tools Builder Settings',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.excelToAppColor,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildToolsBuilderSection('Formula Engine', [
                        _buildToolsBuilderToggle(
                          'Advanced Formulas',
                          'Enable complex Excel formula support',
                          true,
                        ),
                        _buildToolsBuilderToggle(
                          'Real-time Calculation',
                          'Calculate formulas as you type',
                          true,
                        ),
                        _buildToolsBuilderToggle(
                          'Formula Auto-complete',
                          'Show function suggestions',
                          true,
                        ),
                      ]),
                      const SizedBox(height: 20),
                      _buildToolsBuilderSection('Data Features', [
                        _buildToolsBuilderToggle(
                          'Cell Validation',
                          'Validate cell data types',
                          true,
                        ),
                        _buildToolsBuilderToggle(
                          'Chart Generation',
                          'Generate charts from data',
                          true,
                        ),
                        _buildToolsBuilderToggle(
                          'Pivot Tables',
                          'Enable pivot table creation',
                          true,
                        ),
                      ]),
                      const SizedBox(height: 20),
                      _buildToolsBuilderSection('Performance', [
                        _buildToolsBuilderSlider(
                          'Max Cells per Sheet',
                          100000,
                          1000,
                          1000000,
                        ),
                        _buildToolsBuilderSlider(
                          'Calculation Timeout (sec)',
                          30,
                          5,
                          300,
                        ),
                        _buildToolsBuilderSlider(
                          'Auto-save Interval (min)',
                          5,
                          1,
                          60,
                        ),
                      ]),
                      const SizedBox(height: 20),
                      _buildToolsBuilderSection('User Interface', [
                        _buildToolsBuilderToggle(
                          'Touch Gestures',
                          'Enable touch gestures for mobile',
                          true,
                        ),
                        _buildToolsBuilderToggle(
                          'Keyboard Shortcuts',
                          'Enable Excel-style shortcuts',
                          true,
                        ),
                        _buildToolsBuilderDropdown('Default Theme', 'modern', [
                          'modern',
                          'classic',
                          'minimal',
                        ]),
                      ]),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Tools Builder settings saved'),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.excelToAppColor,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Save Settings'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _createBackup() {
    // Implement backup functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Backup functionality will be implemented')),
    );
  }

  void _restoreBackup() {
    // Implement restore functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Restore functionality will be implemented'),
      ),
    );
  }

  void _clearAllData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text(
          'This will permanently delete all your data. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Clear data functionality will be implemented'),
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _showDeveloperOptions() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Developer options will be implemented')),
    );
  }

  void _showAbout() {
    showAboutDialog(
      context: context,
      applicationName: 'Shadow Suite',
      applicationVersion: '1.0.0',
      applicationLegalese: '© 2024 Shadow Suite. All rights reserved.',
      children: [
        const Text(
          'A comprehensive productivity suite with multiple mini-apps.',
        ),
      ],
    );
  }

  // Tools Builder Settings Helper Methods
  Widget _buildToolsBuilderSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.excelToAppColor,
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildToolsBuilderToggle(
    String title,
    String description,
    bool value,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: (newValue) {
              // Handle toggle change
            },
            activeColor: AppTheme.excelToAppColor,
          ),
        ],
      ),
    );
  }

  Widget _buildToolsBuilderSlider(
    String title,
    double value,
    double min,
    double max,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
              Text(
                value.toInt().toString(),
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.excelToAppColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Slider(
            value: value,
            min: min,
            max: max,
            divisions: ((max - min) / (max > 1000 ? 1000 : 1)).round(),
            activeColor: AppTheme.excelToAppColor,
            onChanged: (newValue) {
              // Handle slider change
            },
          ),
        ],
      ),
    );
  }

  Widget _buildToolsBuilderDropdown(
    String title,
    String value,
    List<String> options,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
            ),
          ),
          DropdownButton<String>(
            value: value,
            underline: const SizedBox(),
            items: options
                .map(
                  (option) => DropdownMenuItem(
                    value: option,
                    child: Text(option.toUpperCase()),
                  ),
                )
                .toList(),
            onChanged: (newValue) {
              // Handle dropdown change
            },
          ),
        ],
      ),
    );
  }
}
