import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import '../models/excel_app_tool.dart';
import '../services/excel_app_providers.dart';
import 'excel_to_app_main.dart';

class ExcelAppImportTools extends ConsumerStatefulWidget {
  const ExcelAppImportTools({super.key});

  @override
  ConsumerState<ExcelAppImportTools> createState() => _ExcelAppImportToolsState();
}

class _ExcelAppImportToolsState extends ConsumerState<ExcelAppImportTools> {
  final List<ExcelAppTool> _importedTools = [];
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Column(
        children: [
          ExcelToAppHeader(
            title: 'Import Tools',
            subtitle: 'Import Excel-to-App tools from files or templates',
            actions: [
              IconButton(
                onPressed: _importFromFile,
                icon: const Icon(Icons.file_upload),
                tooltip: 'Import from File',
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: _importFromClipboard,
                icon: const Icon(Icons.content_paste),
                tooltip: 'Import from Clipboard',
              ),
            ],
          ),
          Expanded(
            child: _importedTools.isEmpty ? _buildEmptyState() : _buildImportedToolsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: const Color(0xFF3498DB).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Icon(
              Icons.file_upload,
              size: 64,
              color: Color(0xFF3498DB),
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'Import Excel-to-App Tools',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 12),
          const Text(
            'Import tools from files, templates, or shared sources',
            style: TextStyle(
              fontSize: 16,
              color: Color(0xFF7F8C8D),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          Wrap(
            spacing: 16,
            runSpacing: 16,
            children: [
              _buildImportOption(
                'Import from File',
                'Load tools from .json files',
                Icons.file_upload,
                const Color(0xFF3498DB),
                _importFromFile,
              ),
              _buildImportOption(
                'Import from Clipboard',
                'Paste tool data from clipboard',
                Icons.content_paste,
                const Color(0xFF27AE60),
                _importFromClipboard,
              ),
              _buildImportOption(
                'Import from Excel',
                'Load data from .xlsx/.xls files',
                Icons.table_chart,
                const Color(0xFFF39C12),
                _importFromExcel,
              ),
              _buildImportOption(
                'Browse Templates',
                'Choose from pre-built templates',
                Icons.library_books,
                const Color(0xFF9B59B6),
                _browseTemplates,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildImportOption(String title, String subtitle, IconData icon, Color color, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: 200,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: const Color(0xFFE9ECEF)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, size: 32, color: color),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2C3E50),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF7F8C8D),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImportedToolsList() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: Color(0xFFF8F9FA),
            border: Border(
              bottom: BorderSide(color: Color(0xFFE9ECEF)),
            ),
          ),
          child: Row(
            children: [
              const Icon(Icons.check_circle, color: Color(0xFF27AE60)),
              const SizedBox(width: 12),
              Text(
                '${_importedTools.length} tool(s) ready to import',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF2C3E50),
                ),
              ),
              const Spacer(),
              ElevatedButton(
                onPressed: _isLoading ? null : _importAllTools,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF27AE60),
                  foregroundColor: Colors.white,
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                      )
                    : const Text('Import All'),
              ),
              const SizedBox(width: 12),
              TextButton(
                onPressed: _clearImportedTools,
                child: const Text('Clear All'),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _importedTools.length,
            itemBuilder: (context, index) {
              final tool = _importedTools[index];
              return _buildImportedToolCard(tool, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildImportedToolCard(ExcelAppTool tool, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF3498DB).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.table_view,
                color: Color(0xFF3498DB),
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    tool.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2C3E50),
                    ),
                  ),
                  if (tool.description.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      tool.description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF7F8C8D),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      _buildToolInfo('Components', '${tool.uiComponents.length}'),
                      const SizedBox(width: 16),
                      _buildToolInfo('Size', '${tool.spreadsheet.columns}x${tool.spreadsheet.rows}'),
                      const SizedBox(width: 16),
                      _buildToolInfo('Security', tool.securityType.name),
                    ],
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: () => _removeImportedTool(index),
              icon: const Icon(Icons.close, color: Color(0xFFE74C3C)),
              tooltip: 'Remove from import list',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToolInfo(String label, String value) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          '$label: ',
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF95A5A6),
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
          ),
        ),
      ],
    );
  }

  Future<void> _importFromFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        for (final file in result.files) {
          if (file.bytes != null) {
            final content = String.fromCharCodes(file.bytes!);
            await _processImportData(content, file.name);
          }
        }
      }
    } catch (e) {
      _showErrorDialog('Failed to import from file: $e');
    }
  }

  Future<void> _importFromExcel() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.bytes != null) {
          await _processExcelFile(file.bytes!, file.name);
        }
      }
    } catch (e) {
      _showErrorDialog('Failed to import Excel file: $e');
    }
  }

  Future<void> _importFromClipboard() async {
    final controller = TextEditingController();

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import from Clipboard'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Paste the tool data (JSON format) below:'),
              const SizedBox(height: 16),
              TextField(
                controller: controller,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  hintText: 'Paste tool JSON data here...',
                ),
                maxLines: 10,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(controller.text),
            child: const Text('Import'),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty) {
      await _processImportData(result, 'Clipboard');
    }
  }

  Future<void> _browseTemplates() async {
    final templates = _getBuiltInTemplates();

    final selectedTemplate = await showDialog<ExcelAppTool>(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 600,
          height: 500,
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Choose a Template',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    childAspectRatio: 1.2,
                  ),
                  itemCount: templates.length,
                  itemBuilder: (context, index) {
                    final template = templates[index];
                    return InkWell(
                      onTap: () => Navigator.of(context).pop(template),
                      borderRadius: BorderRadius.circular(12),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border.all(color: const Color(0xFFE9ECEF)),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          children: [
                            Icon(
                              Icons.library_books,
                              size: 48,
                              color: Color(0xFF9B59B6),
                            ),
                            const SizedBox(height: 12),
                            Text(
                              template.name,
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              template.description,
                              style: const TextStyle(
                                fontSize: 12,
                                color: Color(0xFF7F8C8D),
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );

    if (selectedTemplate != null) {
      setState(() {
        _importedTools.add(selectedTemplate);
      });
    }
  }

  Future<void> _processImportData(String data, String source) async {
    try {
      final tool = ExcelAppTool.fromJson(data);
      setState(() {
        _importedTools.add(tool);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Tool "${tool.name}" added to import list from $source'),
            backgroundColor: const Color(0xFF27AE60),
          ),
        );
      }
    } catch (e) {
      _showErrorDialog('Invalid tool data from $source: $e');
    }
  }

  Future<void> _processExcelFile(List<int> bytes, String fileName) async {
    try {
      // Create a tool from Excel file data
      final tool = await _createToolFromExcelData(bytes, fileName);

      setState(() {
        _importedTools.add(tool);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Excel file "$fileName" converted to tool successfully'),
            backgroundColor: const Color(0xFF27AE60),
          ),
        );
      }
    } catch (e) {
      _showErrorDialog('Failed to process Excel file "$fileName": $e');
    }
  }

  Future<ExcelAppTool> _createToolFromExcelData(List<int> bytes, String fileName) async {
    // Simplified Excel parsing - in a real app, use the 'excel' package
    final now = DateTime.now();
    final toolName = fileName.replaceAll(RegExp(r'\.(xlsx|xls)$'), '');

    // Determine spreadsheet dimensions based on file size
    int columns = 50; // Default
    int rows = 50;    // Default

    // Auto-resize based on estimated data size
    final fileSizeKB = bytes.length / 1024;
    if (fileSizeKB > 100) {
      columns = 70;
      rows = 70;
    }
    if (fileSizeKB > 500) {
      columns = 100;
      rows = 100;
    }

    // Create sample cells (in real implementation, parse actual Excel data)
    final Map<String, ExcelCell> cells = {};

    // Add header row
    for (int col = 0; col < 10; col++) {
      final address = '${String.fromCharCode(65 + col)}1';
      cells[address] = ExcelCell(
        address: address,
        value: 'Column ${String.fromCharCode(65 + col)}',
        formula: null,
      );
    }

    // Add some sample data
    for (int row = 2; row <= 5; row++) {
      for (int col = 0; col < 5; col++) {
        final address = '${String.fromCharCode(65 + col)}$row';
        cells[address] = ExcelCell(
          address: address,
          value: 'Data $row-${col + 1}',
          formula: null,
        );
      }
    }

    return ExcelAppTool(
      id: 'excel_import_${now.millisecondsSinceEpoch}',
      name: toolName,
      description: 'Imported from Excel file: $fileName',
      securityType: SecurityType.none,
      spreadsheet: ExcelSpreadsheet(
        name: toolName,
        columns: columns,
        rows: rows,
        cells: cells,
        lastModified: now,
      ),
      uiComponents: [],
      createdAt: now,
      lastModified: now,
    );
  }

  Future<void> _importAllTools() async {
    setState(() => _isLoading = true);

    try {
      for (final tool in _importedTools) {
        // Generate new ID and update timestamps
        final now = DateTime.now();
        final importedTool = tool.copyWith(
          id: now.millisecondsSinceEpoch.toString(),
          name: '${tool.name} (Imported)',
          createdAt: now,
          lastModified: now,
        );

        await ref.read(excelAppToolsProvider.notifier).saveTool(importedTool);
      }

      setState(() {
        _importedTools.clear();
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('All tools imported successfully!'),
            backgroundColor: Color(0xFF27AE60),
          ),
        );
      }

      // Navigate back to dashboard
      ref.read(excelToAppCurrentScreenProvider.notifier).state = ExcelToAppScreen.dashboard;
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorDialog('Failed to import tools: $e');
    }
  }

  void _removeImportedTool(int index) {
    setState(() {
      _importedTools.removeAt(index);
    });
  }

  void _clearImportedTools() {
    setState(() {
      _importedTools.clear();
    });
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  List<ExcelAppTool> _getBuiltInTemplates() {
    final now = DateTime.now();

    return [
      ExcelAppTool(
        id: 'template_expense_tracker',
        name: 'Expense Tracker',
        description: 'Track daily expenses with categories and budgets',
        securityType: SecurityType.none,
        spreadsheet: ExcelSpreadsheet(
          name: 'Expenses',
          columns: 5,
          rows: 20,
          cells: {
            'A1': ExcelCell(address: 'A1', value: 'Date', formula: null),
            'B1': ExcelCell(address: 'B1', value: 'Category', formula: null),
            'C1': ExcelCell(address: 'C1', value: 'Description', formula: null),
            'D1': ExcelCell(address: 'D1', value: 'Amount', formula: null),
            'E1': ExcelCell(address: 'E1', value: 'Balance', formula: null),
          },
          lastModified: now,
        ),
        uiComponents: [],
        createdAt: now,
        lastModified: now,
      ),
      ExcelAppTool(
        id: 'template_inventory',
        name: 'Inventory Manager',
        description: 'Manage product inventory with stock levels',
        securityType: SecurityType.none,
        spreadsheet: ExcelSpreadsheet(
          name: 'Inventory',
          columns: 6,
          rows: 25,
          cells: {
            'A1': ExcelCell(address: 'A1', value: 'Product ID', formula: null),
            'B1': ExcelCell(address: 'B1', value: 'Product Name', formula: null),
            'C1': ExcelCell(address: 'C1', value: 'Category', formula: null),
            'D1': ExcelCell(address: 'D1', value: 'Stock', formula: null),
            'E1': ExcelCell(address: 'E1', value: 'Price', formula: null),
            'F1': ExcelCell(address: 'F1', value: 'Total Value', formula: null),
          },
          lastModified: now,
        ),
        uiComponents: [],
        createdAt: now,
        lastModified: now,
      ),
      ExcelAppTool(
        id: 'template_calculator',
        name: 'Calculator App',
        description: 'Simple calculator with basic operations',
        securityType: SecurityType.none,
        spreadsheet: ExcelSpreadsheet(
          name: 'Calculator',
          columns: 3,
          rows: 10,
          cells: {
            'A1': ExcelCell(address: 'A1', value: 'Input 1', formula: null),
            'B1': ExcelCell(address: 'B1', value: 'Operation', formula: null),
            'C1': ExcelCell(address: 'C1', value: 'Input 2', formula: null),
            'A2': ExcelCell(address: 'A2', value: 'Result', formula: null),
          },
          lastModified: now,
        ),
        uiComponents: [],
        createdAt: now,
        lastModified: now,
      ),
      ExcelAppTool(
        id: 'template_todo',
        name: 'Todo List',
        description: 'Simple task management with priorities',
        securityType: SecurityType.none,
        spreadsheet: ExcelSpreadsheet(
          name: 'Tasks',
          columns: 4,
          rows: 15,
          cells: {
            'A1': ExcelCell(address: 'A1', value: 'Task', formula: null),
            'B1': ExcelCell(address: 'B1', value: 'Priority', formula: null),
            'C1': ExcelCell(address: 'C1', value: 'Status', formula: null),
            'D1': ExcelCell(address: 'D1', value: 'Due Date', formula: null),
          },
          lastModified: now,
        ),
        uiComponents: [],
        createdAt: now,
        lastModified: now,
      ),
    ];
  }
}
