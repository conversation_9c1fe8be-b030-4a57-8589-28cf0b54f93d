import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/excel_formula_engine.dart';
import '../models/excel_app_tool.dart';

class EnhancedFormulaBar extends ConsumerStatefulWidget {
  final String? selectedCell;
  final ExcelAppTool tool;
  final Function(String cellAddress, String value) onCellUpdate;

  const EnhancedFormulaBar({
    super.key,
    required this.selectedCell,
    required this.tool,
    required this.onCellUpdate,
  });

  @override
  ConsumerState<EnhancedFormulaBar> createState() => _EnhancedFormulaBarState();
}

class _EnhancedFormulaBarState extends ConsumerState<EnhancedFormulaBar> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  final ExcelFormulaEngine _formulaEngine = ExcelFormulaEngine();
  
  List<String> _suggestions = [];
  bool _showSuggestions = false;
  int _selectedSuggestionIndex = -1;
  // String _currentInput = ''; // Reserved for future input tracking
  FormulaValidationResult? _validationResult;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _focusNode = FocusNode();
    _updateFormulaBar();
  }

  @override
  void didUpdateWidget(EnhancedFormulaBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedCell != widget.selectedCell) {
      _updateFormulaBar();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _updateFormulaBar() {
    if (widget.selectedCell != null) {
      final cell = widget.tool.spreadsheet.cells[widget.selectedCell];
      _controller.text = cell?.formula ?? cell?.value?.toString() ?? '';
      _validateFormula(_controller.text);
    } else {
      _controller.clear();
      _validationResult = null;
    }
  }

  void _onTextChanged(String value) {
    setState(() {
      // _currentInput = value; // Reserved for future input tracking
    });

    _validateFormula(value);
    
    if (value.startsWith('=')) {
      _updateSuggestions(value);
    } else {
      _hideSuggestions();
    }
  }

  void _validateFormula(String formula) {
    if (formula.startsWith('=')) {
      _validationResult = _formulaEngine.validateFormula(formula);
    } else {
      _validationResult = null;
    }
    setState(() {});
  }

  void _updateSuggestions(String input) {
    if (!input.startsWith('=')) {
      _hideSuggestions();
      return;
    }

    final expression = input.substring(1);
    final lastWord = _getLastWord(expression);
    
    if (lastWord.isNotEmpty && RegExp(r'^[A-Z]*$').hasMatch(lastWord)) {
      final suggestions = _formulaEngine.getFunctionSuggestions(lastWord);
      setState(() {
        _suggestions = suggestions;
        _showSuggestions = suggestions.isNotEmpty;
        _selectedSuggestionIndex = -1;
      });
    } else {
      _hideSuggestions();
    }
  }

  String _getLastWord(String text) {
    final match = RegExp(r'[A-Z]*$').firstMatch(text);
    return match?.group(0) ?? '';
  }

  void _hideSuggestions() {
    setState(() {
      _showSuggestions = false;
      _suggestions.clear();
      _selectedSuggestionIndex = -1;
    });
  }

  void _selectSuggestion(String suggestion) {
    final currentText = _controller.text;
    if (currentText.startsWith('=')) {
      final expression = currentText.substring(1);
      final lastWord = _getLastWord(expression);
      
      if (lastWord.isNotEmpty) {
        final newExpression = '${expression.substring(0, expression.length - lastWord.length)}$suggestion(';
        _controller.text = '=$newExpression';
        _controller.selection = TextSelection.fromPosition(
          TextPosition(offset: _controller.text.length),
        );
      }
    }
    
    _hideSuggestions();
    _focusNode.requestFocus();
  }

  void _applyFormula() {
    if (widget.selectedCell == null) return;

    final value = _controller.text;
    widget.onCellUpdate(widget.selectedCell!, value);
    
    // Clear formula engine cache to ensure recalculation
    _formulaEngine.clearCache();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Formula input bar
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Row(
            children: [
              // Cell address
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  widget.selectedCell ?? '',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              
              // Formula input
              Expanded(
                child: TextField(
                  controller: _controller,
                  focusNode: _focusNode,
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    hintText: 'Enter value or formula (=SUM(A1:A5))',
                    contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  ),
                  style: const TextStyle(fontFamily: 'monospace'),
                  onChanged: _onTextChanged,
                  onSubmitted: (_) => _applyFormula(),
                  enableInteractiveSelection: true,
                  textInputAction: TextInputAction.done,
                  keyboardType: TextInputType.text,
                  maxLines: 1,
                  autocorrect: false,
                  enableSuggestions: false,
                ),
              ),
              
              // Apply button
              IconButton(
                onPressed: _applyFormula,
                icon: const Icon(Icons.check, color: Colors.green),
                tooltip: 'Apply Formula',
              ),
            ],
          ),
        ),
        
        // Validation message
        if (_validationResult != null) ...[
          const SizedBox(height: 4),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _validationResult!.isValid ? Colors.green.shade50 : Colors.red.shade50,
              border: Border.all(
                color: _validationResult!.isValid ? Colors.green : Colors.red,
              ),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              children: [
                Icon(
                  _validationResult!.isValid ? Icons.check_circle : Icons.error,
                  size: 16,
                  color: _validationResult!.isValid ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _validationResult!.message,
                    style: TextStyle(
                      fontSize: 12,
                      color: _validationResult!.isValid ? Colors.green.shade700 : Colors.red.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
        
        // Function suggestions
        if (_showSuggestions) ...[
          const SizedBox(height: 4),
          Container(
            constraints: const BoxConstraints(maxHeight: 200),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(4),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _suggestions.length,
              itemBuilder: (context, index) {
                final suggestion = _suggestions[index];
                final isSelected = index == _selectedSuggestionIndex;
                
                return InkWell(
                  onTap: () => _selectSuggestion(suggestion),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    color: isSelected ? Colors.blue.shade50 : null,
                    child: Row(
                      children: [
                        Icon(
                          Icons.functions,
                          size: 16,
                          color: Colors.blue.shade600,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                suggestion,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                              Text(
                                _formulaEngine.getFunctionSyntax(suggestion),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ],
    );
  }
}
