

// Export Project Model
class ExportProject {
  final String id;
  final String appId;
  final String appName;
  final ExportPlatform targetPlatform;
  final String outputPath;
  final List<String> enabledFeatures;
  final ExportOptions options;
  final ExportStatus status;
  final double progress;
  final String? errorMessage;
  final DateTime createdAt;
  final DateTime lastModified;
  final String? finalExecutablePath;

  const ExportProject({
    required this.id,
    required this.appId,
    required this.appName,
    required this.targetPlatform,
    required this.outputPath,
    required this.enabledFeatures,
    required this.options,
    required this.status,
    required this.progress,
    this.errorMessage,
    required this.createdAt,
    required this.lastModified,
    this.finalExecutablePath,
  });

  factory ExportProject.fromJson(Map<String, dynamic> json) {
    return ExportProject(
      id: json['id'] as String,
      appId: json['app_id'] as String,
      appName: json['app_name'] as String,
      targetPlatform: ExportPlatform.values.firstWhere(
        (e) => e.name == json['target_platform'],
        orElse: () => ExportPlatform.windowsEXE,
      ),
      outputPath: json['output_path'] as String,
      enabledFeatures: List<String>.from(json['enabled_features'] as List? ?? []),
      options: ExportOptions.fromJson(json['options'] as Map<String, dynamic>),
      status: ExportStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ExportStatus.preparing,
      ),
      progress: (json['progress'] as num).toDouble(),
      errorMessage: json['error_message'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastModified: DateTime.parse(json['last_modified'] as String),
      finalExecutablePath: json['final_executable_path'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'app_id': appId,
      'app_name': appName,
      'target_platform': targetPlatform.name,
      'output_path': outputPath,
      'enabled_features': enabledFeatures,
      'options': options.toJson(),
      'status': status.name,
      'progress': progress,
      'error_message': errorMessage,
      'created_at': createdAt.toIso8601String(),
      'last_modified': lastModified.toIso8601String(),
      'final_executable_path': finalExecutablePath,
    };
  }

  ExportProject copyWith({
    ExportStatus? status,
    double? progress,
    String? errorMessage,
    String? finalExecutablePath,
  }) {
    return ExportProject(
      id: id,
      appId: appId,
      appName: appName,
      targetPlatform: targetPlatform,
      outputPath: outputPath,
      enabledFeatures: enabledFeatures,
      options: options,
      status: status ?? this.status,
      progress: progress ?? this.progress,
      errorMessage: errorMessage ?? this.errorMessage,
      createdAt: createdAt,
      lastModified: DateTime.now(),
      finalExecutablePath: finalExecutablePath ?? this.finalExecutablePath,
    );
  }
}

// Export Options Model
class ExportOptions {
  final String appIcon;
  final String appVersion;
  final String packageName;
  final bool includeDebugInfo;
  final bool optimizeForSize;
  final bool enableObfuscation;
  final Map<String, String> buildVariables;
  final List<String> additionalAssets;
  final SigningConfig? signingConfig;

  const ExportOptions({
    required this.appIcon,
    required this.appVersion,
    required this.packageName,
    required this.includeDebugInfo,
    required this.optimizeForSize,
    required this.enableObfuscation,
    required this.buildVariables,
    required this.additionalAssets,
    this.signingConfig,
  });

  factory ExportOptions.defaultWindows() {
    return const ExportOptions(
      appIcon: 'assets/icons/app_icon.ico',
      appVersion: '1.0.0',
      packageName: 'com.shadowsuite.app',
      includeDebugInfo: false,
      optimizeForSize: true,
      enableObfuscation: true,
      buildVariables: {},
      additionalAssets: [],
    );
  }

  factory ExportOptions.defaultAndroid() {
    return const ExportOptions(
      appIcon: 'assets/icons/app_icon.png',
      appVersion: '1.0.0',
      packageName: 'com.shadowsuite.app',
      includeDebugInfo: false,
      optimizeForSize: true,
      enableObfuscation: true,
      buildVariables: {},
      additionalAssets: [],
    );
  }

  factory ExportOptions.defaultiOS() {
    return const ExportOptions(
      appIcon: 'assets/icons/app_icon.png',
      appVersion: '1.0.0',
      packageName: 'com.shadowsuite.app',
      includeDebugInfo: false,
      optimizeForSize: true,
      enableObfuscation: true,
      buildVariables: {},
      additionalAssets: [],
    );
  }

  factory ExportOptions.fromJson(Map<String, dynamic> json) {
    return ExportOptions(
      appIcon: json['app_icon'] as String,
      appVersion: json['app_version'] as String,
      packageName: json['package_name'] as String,
      includeDebugInfo: json['include_debug_info'] as bool,
      optimizeForSize: json['optimize_for_size'] as bool,
      enableObfuscation: json['enable_obfuscation'] as bool,
      buildVariables: Map<String, String>.from(json['build_variables'] as Map? ?? {}),
      additionalAssets: List<String>.from(json['additional_assets'] as List? ?? []),
      signingConfig: json['signing_config'] != null 
          ? SigningConfig.fromJson(json['signing_config'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'app_icon': appIcon,
      'app_version': appVersion,
      'package_name': packageName,
      'include_debug_info': includeDebugInfo,
      'optimize_for_size': optimizeForSize,
      'enable_obfuscation': enableObfuscation,
      'build_variables': buildVariables,
      'additional_assets': additionalAssets,
      'signing_config': signingConfig?.toJson(),
    };
  }
}

// Signing Configuration Model
class SigningConfig {
  final String keystorePath;
  final String keystorePassword;
  final String keyAlias;
  final String keyPassword;

  const SigningConfig({
    required this.keystorePath,
    required this.keystorePassword,
    required this.keyAlias,
    required this.keyPassword,
  });

  factory SigningConfig.fromJson(Map<String, dynamic> json) {
    return SigningConfig(
      keystorePath: json['keystore_path'] as String,
      keystorePassword: json['keystore_password'] as String,
      keyAlias: json['key_alias'] as String,
      keyPassword: json['key_password'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'keystore_path': keystorePath,
      'keystore_password': keystorePassword,
      'key_alias': keyAlias,
      'key_password': keyPassword,
    };
  }
}

// Build Command Model
class BuildCommand {
  final String command;
  final List<String> arguments;
  final Map<String, String> environment;

  const BuildCommand({
    required this.command,
    required this.arguments,
    required this.environment,
  });
}

// Export Progress Event Model
class ExportProgressEvent {
  final String projectId;
  final ExportStatus status;
  final double progress;
  final String? message;
  final DateTime timestamp;

  const ExportProgressEvent({
    required this.projectId,
    required this.status,
    required this.progress,
    this.message,
    required this.timestamp,
  });
}

// Export Template Model
class ExportTemplate {
  final String id;
  final String name;
  final String description;
  final ExportPlatform targetPlatform;
  final ExportOptions defaultOptions;
  final List<String> requiredFeatures;
  final List<String> optionalFeatures;
  final Map<String, dynamic> templateConfig;

  const ExportTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.targetPlatform,
    required this.defaultOptions,
    required this.requiredFeatures,
    required this.optionalFeatures,
    required this.templateConfig,
  });

  factory ExportTemplate.fromJson(Map<String, dynamic> json) {
    return ExportTemplate(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      targetPlatform: ExportPlatform.values.firstWhere(
        (e) => e.name == json['target_platform'],
        orElse: () => ExportPlatform.windowsEXE,
      ),
      defaultOptions: ExportOptions.fromJson(json['default_options'] as Map<String, dynamic>),
      requiredFeatures: List<String>.from(json['required_features'] as List? ?? []),
      optionalFeatures: List<String>.from(json['optional_features'] as List? ?? []),
      templateConfig: Map<String, dynamic>.from(json['template_config'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'target_platform': targetPlatform.name,
      'default_options': defaultOptions.toJson(),
      'required_features': requiredFeatures,
      'optional_features': optionalFeatures,
      'template_config': templateConfig,
    };
  }
}

// Export Statistics Model
class ExportStatistics {
  final int totalExports;
  final int successfulExports;
  final int failedExports;
  final Map<ExportPlatform, int> platformBreakdown;
  final Map<String, int> appBreakdown;
  final double averageExportTime;
  final DateTime lastExport;

  const ExportStatistics({
    required this.totalExports,
    required this.successfulExports,
    required this.failedExports,
    required this.platformBreakdown,
    required this.appBreakdown,
    required this.averageExportTime,
    required this.lastExport,
  });
}

// Enums
enum ExportPlatform {
  windowsEXE,
  androidAPK,
  iOSIPA,
  macOSApp,
  linuxAppImage,
  webApp,
}

enum ExportStatus {
  preparing,
  building,
  packaging,
  signing,
  completed,
  failed,
  cancelled,
}

// Extension methods
extension ExportPlatformExtension on ExportPlatform {
  String get displayName {
    switch (this) {
      case ExportPlatform.windowsEXE:
        return 'Windows EXE';
      case ExportPlatform.androidAPK:
        return 'Android APK';
      case ExportPlatform.iOSIPA:
        return 'iOS IPA';
      case ExportPlatform.macOSApp:
        return 'macOS App';
      case ExportPlatform.linuxAppImage:
        return 'Linux AppImage';
      case ExportPlatform.webApp:
        return 'Web App';
    }
  }

  String get fileExtension {
    switch (this) {
      case ExportPlatform.windowsEXE:
        return '.exe';
      case ExportPlatform.androidAPK:
        return '.apk';
      case ExportPlatform.iOSIPA:
        return '.ipa';
      case ExportPlatform.macOSApp:
        return '.app';
      case ExportPlatform.linuxAppImage:
        return '.AppImage';
      case ExportPlatform.webApp:
        return '.zip';
    }
  }

  List<String> get requiredTools {
    switch (this) {
      case ExportPlatform.windowsEXE:
        return ['flutter', 'visual-studio'];
      case ExportPlatform.androidAPK:
        return ['flutter', 'android-sdk'];
      case ExportPlatform.iOSIPA:
        return ['flutter', 'xcode'];
      case ExportPlatform.macOSApp:
        return ['flutter', 'xcode'];
      case ExportPlatform.linuxAppImage:
        return ['flutter', 'gcc'];
      case ExportPlatform.webApp:
        return ['flutter'];
    }
  }
}

extension ExportStatusExtension on ExportStatus {
  String get displayName {
    switch (this) {
      case ExportStatus.preparing:
        return 'Preparing';
      case ExportStatus.building:
        return 'Building';
      case ExportStatus.packaging:
        return 'Packaging';
      case ExportStatus.signing:
        return 'Signing';
      case ExportStatus.completed:
        return 'Completed';
      case ExportStatus.failed:
        return 'Failed';
      case ExportStatus.cancelled:
        return 'Cancelled';
    }
  }

  bool get isInProgress {
    return this == ExportStatus.preparing ||
           this == ExportStatus.building ||
           this == ExportStatus.packaging ||
           this == ExportStatus.signing;
  }

  bool get isCompleted {
    return this == ExportStatus.completed;
  }

  bool get isFailed {
    return this == ExportStatus.failed || this == ExportStatus.cancelled;
  }
}
