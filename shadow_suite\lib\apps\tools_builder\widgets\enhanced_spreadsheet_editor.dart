import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/spreadsheet.dart';
import '../models/spreadsheet_cell.dart';
import '../services/enhanced_formula_engine.dart';
import '../utils/cell_address_parser.dart';

class EnhancedSpreadsheetEditor extends ConsumerStatefulWidget {
  final Spreadsheet spreadsheet;
  final Function(Spreadsheet) onSpreadsheetChanged;

  const EnhancedSpreadsheetEditor({
    super.key,
    required this.spreadsheet,
    required this.onSpreadsheetChanged,
  });

  @override
  ConsumerState<EnhancedSpreadsheetEditor> createState() =>
      _EnhancedSpreadsheetEditorState();
}

class _EnhancedSpreadsheetEditorState
    extends ConsumerState<EnhancedSpreadsheetEditor> {
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();
  final TextEditingController _formulaController = TextEditingController();

  String? _selectedCell;

  final Map<String, TextEditingController> _cellControllers = {};

  @override
  void initState() {
    super.initState();
    _selectedCell = 'A1';
    _updateFormulaBar();
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    _formulaController.dispose();
    for (final controller in _cellControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildToolbar(),
        _buildFormulaBar(),
        Expanded(child: _buildSpreadsheetGrid()),
        _buildStatusBar(),
      ],
    );
  }

  Widget _buildToolbar() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.undo),
            onPressed: _undo,
            tooltip: 'Undo',
          ),
          IconButton(
            icon: const Icon(Icons.redo),
            onPressed: _redo,
            tooltip: 'Redo',
          ),
          const VerticalDivider(),
          IconButton(
            icon: const Icon(Icons.format_bold),
            onPressed: _toggleBold,
            tooltip: 'Bold',
          ),
          IconButton(
            icon: const Icon(Icons.format_italic),
            onPressed: _toggleItalic,
            tooltip: 'Italic',
          ),
          const VerticalDivider(),
          IconButton(
            icon: const Icon(Icons.functions),
            onPressed: _showFunctionDialog,
            tooltip: 'Insert Function',
          ),
          IconButton(
            icon: const Icon(Icons.calculate),
            onPressed: _recalculateAll,
            tooltip: 'Recalculate All',
          ),
        ],
      ),
    );
  }

  Widget _buildFormulaBar() {
    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          Container(
            width: 80,
            padding: const EdgeInsets.symmetric(horizontal: 8),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Text(
                _selectedCell ?? 'A1',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: TextField(
              controller: _formulaController,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ),
                hintText: 'Enter formula or value',
              ),
              onSubmitted: _onFormulaSubmitted,
              onChanged: _onFormulaChanged,
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.check, color: Colors.green),
            onPressed: () => _onFormulaSubmitted(_formulaController.text),
            tooltip: 'Apply',
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Colors.red),
            onPressed: _cancelEdit,
            tooltip: 'Cancel',
          ),
        ],
      ),
    );
  }

  Widget _buildSpreadsheetGrid() {
    return Container(
      decoration: BoxDecoration(border: Border.all(color: Colors.grey[300]!)),
      child: Column(
        children: [
          _buildColumnHeaders(),
          Expanded(
            child: Row(
              children: [
                _buildRowHeaders(),
                Expanded(child: _buildCellGrid()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColumnHeaders() {
    return SizedBox(
      height: 30,
      child: Row(
        children: [
          Container(
            width: 50,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              border: Border.all(color: Colors.grey[300]!),
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              controller: _horizontalController,
              scrollDirection: Axis.horizontal,
              child: Row(
                children: List.generate(26, (index) {
                  final column = String.fromCharCode('A'.codeUnitAt(0) + index);
                  return Container(
                    width: 100,
                    height: 30,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: Center(
                      child: Text(
                        column,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  );
                }),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRowHeaders() {
    return SizedBox(
      width: 50,
      child: SingleChildScrollView(
        controller: _verticalController,
        child: Column(
          children: List.generate(100, (index) {
            final row = index + 1;
            return Container(
              width: 50,
              height: 30,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Center(
                child: Text(
                  row.toString(),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            );
          }),
        ),
      ),
    );
  }

  Widget _buildCellGrid() {
    return SingleChildScrollView(
      controller: _horizontalController,
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        controller: _verticalController,
        child: SizedBox(
          width: 26 * 100.0, // 26 columns * 100px width
          height: 100 * 30.0, // 100 rows * 30px height
          child: Stack(
            children: [
              // Grid lines
              CustomPaint(
                size: Size(26 * 100.0, 100 * 30.0),
                painter: GridPainter(),
              ),
              // Cells
              ...List.generate(100, (rowIndex) {
                return List.generate(26, (colIndex) {
                  final cellAddress = CellAddressParser.indicesToAddress(
                    rowIndex,
                    colIndex,
                  );
                  return _buildCell(cellAddress, rowIndex, colIndex);
                });
              }).expand((row) => row),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCell(String address, int row, int col) {
    final isSelected = _selectedCell == address;
    final activeSheet = widget.spreadsheet.activeSheet;
    final cell = activeSheet?.getCell(row, col);

    return Positioned(
      left: col * 100.0,
      top: row * 30.0,
      width: 100,
      height: 30,
      child: GestureDetector(
        onTap: () => _selectCell(address),
        onDoubleTap: () => _editCell(address),
        child: Container(
          decoration: BoxDecoration(
            color: isSelected ? Colors.blue[100] : Colors.white,
            border: Border.all(
              color: isSelected ? Colors.blue : Colors.grey[300]!,
              width: isSelected ? 2 : 1,
            ),
          ),
          padding: const EdgeInsets.all(2),
          child: _buildCellContent(cell, address),
        ),
      ),
    );
  }

  Widget _buildCellContent(SpreadsheetCell? cell, String address) {
    if (cell == null) {
      return const SizedBox.shrink();
    }

    final displayValue = cell.calculatedValue?.toString() ?? cell.rawValue;

    return Align(
      alignment: cell.dataType == CellDataType.number
          ? Alignment.centerRight
          : Alignment.centerLeft,
      child: Text(
        displayValue,
        style: TextStyle(
          fontWeight: cell.format.isBold ? FontWeight.bold : FontWeight.normal,
          fontStyle: cell.format.isItalic ? FontStyle.italic : FontStyle.normal,
          fontSize: cell.format.fontSize ?? 12,
        ),
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }

  Widget _buildStatusBar() {
    return Container(
      height: 25,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          Text(
            'Ready',
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
          ),
          const Spacer(),
          if (_selectedCell != null) ...[
            Text(
              'Cell: $_selectedCell',
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
            const SizedBox(width: 16),
          ],
          Text(
            'Sheet: ${widget.spreadsheet.activeSheet?.name ?? 'Sheet1'}',
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  void _selectCell(String address) {
    setState(() {
      _selectedCell = address;
    });
    _updateFormulaBar();
  }

  void _editCell(String address) {
    _selectCell(address);
    _formulaController.selection = TextSelection(
      baseOffset: 0,
      extentOffset: _formulaController.text.length,
    );
  }

  void _updateFormulaBar() {
    if (_selectedCell == null) return;

    final coords = CellAddressParser.parseAddress(_selectedCell!);
    final activeSheet = widget.spreadsheet.activeSheet;
    final cell = activeSheet?.getCell(coords['row']!, coords['column']!);

    _formulaController.text = cell?.rawValue ?? '';
  }

  void _onFormulaSubmitted(String value) {
    if (_selectedCell == null) return;

    _updateCellValue(_selectedCell!, value);
  }

  void _onFormulaChanged(String value) {
    // Real-time formula validation could go here
  }

  void _cancelEdit() {
    _updateFormulaBar();
  }

  void _updateCellValue(String address, String value) {
    final coords = CellAddressParser.parseAddress(address);
    final row = coords['row']!;
    final column = coords['column']!;

    final activeSheet = widget.spreadsheet.activeSheet;
    if (activeSheet == null) return;

    // Determine data type
    CellDataType dataType = CellDataType.text;
    dynamic calculatedValue = value;

    if (value.startsWith('=')) {
      dataType = CellDataType.formula;
      calculatedValue = EnhancedFormulaEngine.calculateFormula(
        value,
        widget.spreadsheet,
      );
    } else if (double.tryParse(value) != null) {
      dataType = CellDataType.number;
      calculatedValue = double.parse(value);
    } else if (value.toLowerCase() == 'true' ||
        value.toLowerCase() == 'false') {
      dataType = CellDataType.boolean;
      calculatedValue = value.toLowerCase() == 'true';
    }

    final cell = SpreadsheetCell(
      row: row,
      column: column,
      rawValue: value,
      dataType: dataType,
      calculatedValue: calculatedValue,
      formula: value.startsWith('=') ? value : null,
    );

    final updatedSheet = activeSheet.setCell(cell.row, cell.column, cell);
    final updatedSheets = widget.spreadsheet.sheets
        .map((s) => s.id == updatedSheet.id ? updatedSheet : s)
        .toList();

    final updatedSpreadsheet = widget.spreadsheet.copyWith(
      sheets: updatedSheets,
    );
    widget.onSpreadsheetChanged(updatedSpreadsheet);

    // Trigger recalculation of dependent cells
    _recalculateDependents(address);
  }

  void _recalculateDependents(String changedCell) {
    // This would implement dependency tracking and recalculation
    // For now, we'll just trigger a full recalculation
    _recalculateAll();
  }

  void _recalculateAll() {
    // Recalculate all formula cells
    final activeSheet = widget.spreadsheet.activeSheet;
    if (activeSheet == null) return;

    final updatedCells = <String, SpreadsheetCell>{};

    for (final cell in activeSheet.cells.values) {
      if (cell.dataType == CellDataType.formula && cell.formula != null) {
        final calculatedValue = EnhancedFormulaEngine.calculateFormula(
          cell.formula!,
          widget.spreadsheet,
        );
        updatedCells[cell.cellAddress] = cell.copyWith(
          calculatedValue: calculatedValue,
        );
      } else {
        updatedCells[cell.cellAddress] = cell;
      }
    }

    final updatedSheet = activeSheet.copyWith(cells: updatedCells);
    final updatedSheets = widget.spreadsheet.sheets
        .map((s) => s.id == updatedSheet.id ? updatedSheet : s)
        .toList();

    final updatedSpreadsheet = widget.spreadsheet.copyWith(
      sheets: updatedSheets,
    );
    widget.onSpreadsheetChanged(updatedSpreadsheet);
  }

  void _undo() {
    // Implement undo functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Undo functionality will be implemented')),
    );
  }

  void _redo() {
    // Implement redo functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Redo functionality will be implemented')),
    );
  }

  void _toggleBold() {
    // Implement bold formatting
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Bold formatting will be implemented')),
    );
  }

  void _toggleItalic() {
    // Implement italic formatting
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Italic formatting will be implemented')),
    );
  }

  void _showFunctionDialog() {
    // Show function insertion dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Function dialog will be implemented')),
    );
  }
}

class GridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey[300]!
      ..strokeWidth = 1;

    // Draw vertical lines
    for (int i = 0; i <= 26; i++) {
      final x = i * 100.0;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }

    // Draw horizontal lines
    for (int i = 0; i <= 100; i++) {
      final y = i * 30.0;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
