import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../theme/app_theme.dart';
import '../services/settings_service.dart';
import '../widgets/custom_color_picker.dart';
import 'layout_settings_screen.dart';

class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: AppTheme.primaryColor,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.palette), text: 'Appearance'),
            Tab(icon: Icon(Icons.apps), text: 'Apps'),
            Tab(icon: Icon(Icons.keyboard), text: 'Shortcuts'),
            Tab(icon: Icon(Icons.backup), text: 'Backup'),
            Tab(icon: Icon(Icons.security), text: 'Security'),
            Tab(icon: Icon(Icons.settings), text: 'Advanced'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAppearanceTab(),
          _buildAppsTab(),
          _buildShortcutsTab(),
          _buildBackupTab(),
          _buildSecurityTab(),
          _buildAdvancedTab(),
        ],
      ),
    );
  }

  Widget _buildAppearanceTab() {
    final themeMode = ref.watch(themeModeProvider);
    final customColor = ref.watch(customThemeColorProvider);
    final fontSize = ref.watch(fontSizeProvider);
    final language = ref.watch(languageProvider);

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Theme'),
        Card(
          child: Column(
            children: [
              ListTile(
                title: const Text('Theme Mode'),
                subtitle: Text(themeMode.name.toUpperCase()),
                trailing: DropdownButton<ThemeMode>(
                  value: themeMode,
                  items: ThemeMode.values.map((mode) {
                    return DropdownMenuItem(
                      value: mode,
                      child: Text(mode.name.toUpperCase()),
                    );
                  }).toList(),
                  onChanged: (mode) {
                    if (mode != null) {
                      ref.read(themeModeProvider.notifier).setThemeMode(mode);
                      setState(() {}); // Refresh UI immediately
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Theme changed to ${mode.name}'),
                          duration: const Duration(seconds: 1),
                        ),
                      );
                    }
                  },
                ),
              ),
              ListTile(
                title: const Text('Custom Theme Color'),
                subtitle: const Text('Choose your preferred accent color'),
                trailing: GestureDetector(
                  onTap: () => _showColorPicker(context, customColor),
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: customColor,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.grey),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        _buildSectionHeader('Typography'),
        Card(
          child: Column(
            children: [
              ListTile(
                title: const Text('Font Size'),
                subtitle: Text('${fontSize.toInt()}px'),
                trailing: SizedBox(
                  width: 200,
                  child: Slider(
                    value: fontSize,
                    min: 10,
                    max: 24,
                    divisions: 14,
                    onChanged: (value) {
                      ref.read(fontSizeProvider.notifier).setFontSize(value);
                      setState(() {}); // Refresh UI immediately
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'Font size changed to ${value.toInt()}',
                          ),
                          duration: const Duration(seconds: 1),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        _buildSectionHeader('Layout System'),
        Card(
          child: ListTile(
            title: const Text('Layout Settings'),
            subtitle: const Text(
              'Choose between Desktop, Material Design, or Android Native layouts',
            ),
            leading: const Icon(Icons.view_quilt),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const LayoutSettingsScreen(),
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 16),

        _buildSectionHeader('Localization'),
        Card(
          child: Column(
            children: [
              ListTile(
                title: const Text('Language'),
                subtitle: Text(_getLanguageName(language)),
                trailing: DropdownButton<String>(
                  value: language,
                  items: const [
                    DropdownMenuItem(value: 'en', child: Text('English')),
                    DropdownMenuItem(value: 'ar', child: Text('العربية')),
                    DropdownMenuItem(value: 'es', child: Text('Español')),
                    DropdownMenuItem(value: 'fr', child: Text('Français')),
                  ],
                  onChanged: (lang) {
                    if (lang != null) {
                      ref.read(languageProvider.notifier).setLanguage(lang);
                      setState(() {}); // Refresh UI immediately
                    }
                  },
                ),
              ),
              Consumer(
                builder: (context, ref, child) {
                  final rtlEnabled = ref.watch(rtlProvider);
                  return SwitchListTile(
                    title: const Text('Right-to-Left (RTL)'),
                    subtitle: const Text(
                      'Enable RTL text direction and UI mirroring',
                    ),
                    value: rtlEnabled,
                    onChanged: (value) {
                      ref.read(rtlProvider.notifier).setRTL(value);
                      setState(() {}); // Refresh UI immediately
                    },
                    activeColor: AppTheme.primaryColor,
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAppsTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Mini Apps'),
        _buildAppSettingsCard(
          'Money Flow',
          Icons.account_balance_wallet,
          'money_flow',
        ),
        _buildAppSettingsCard('Excel to App', Icons.table_view, 'excel_to_app'),
        _buildAppSettingsCard('Quran Suite', Icons.mosque, 'quran_suite'),
        _buildAppSettingsCard('Tools Builder', Icons.build, 'tools_builder'),
      ],
    );
  }

  Widget _buildAppSettingsCard(String appName, IconData icon, String appId) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Icon(icon, size: 32),
        title: Text(appName),
        subtitle: const Text('Customize app-specific settings'),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: () => _showAppSettings(context, appName, appId),
      ),
    );
  }

  Widget _buildShortcutsTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Global Shortcuts'),
        Card(
          child: Column(
            children: [
              _buildShortcutTile('New Note', 'Ctrl+N', 'new_note'),
              _buildShortcutTile('Search', 'Ctrl+F', 'search'),
              _buildShortcutTile('Settings', 'Ctrl+,', 'settings'),
              _buildShortcutTile('Save', 'Ctrl+S', 'save'),
              _buildShortcutTile('Undo', 'Ctrl+Z', 'undo'),
              _buildShortcutTile('Redo', 'Ctrl+Y', 'redo'),
            ],
          ),
        ),
        const SizedBox(height: 16),

        _buildSectionHeader('App-Specific Shortcuts'),
        Card(
          child: Column(
            children: [
              _buildShortcutTile(
                'Quick Add Transaction',
                'Ctrl+T',
                'quick_transaction',
              ),
              _buildShortcutTile(
                'Open Calculator',
                'Ctrl+Shift+C',
                'calculator',
              ),
              _buildShortcutTile('Voice Memo', 'Ctrl+R', 'voice_memo'),
              _buildShortcutTile('Prayer Times', 'Ctrl+P', 'prayer_times'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildShortcutTile(String title, String shortcut, String action) {
    return ListTile(
      title: Text(title),
      subtitle: Text(shortcut),
      trailing: TextButton(
        onPressed: () => _editShortcut(context, title, action, shortcut),
        child: const Text('Edit'),
      ),
    );
  }

  Widget _buildBackupTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Automatic Backup'),
        Card(
          child: Column(
            children: [
              SwitchListTile(
                title: const Text('Enable Auto Backup'),
                subtitle: const Text('Automatically backup your data'),
                value: SettingsService.getAutoBackup(),
                onChanged: (value) {
                  SettingsService.setAutoBackup(value);
                  setState(() {});
                },
              ),
              ListTile(
                title: const Text('Backup Interval'),
                subtitle: Text(
                  'Every ${SettingsService.getBackupInterval()} hours',
                ),
                trailing: DropdownButton<int>(
                  value: SettingsService.getBackupInterval(),
                  items: const [
                    DropdownMenuItem(value: 1, child: Text('1 hour')),
                    DropdownMenuItem(value: 6, child: Text('6 hours')),
                    DropdownMenuItem(value: 12, child: Text('12 hours')),
                    DropdownMenuItem(value: 24, child: Text('24 hours')),
                    DropdownMenuItem(value: 168, child: Text('1 week')),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      SettingsService.setBackupInterval(value);
                      setState(() {});
                    }
                  },
                ),
              ),
              ListTile(
                title: const Text('Backup Location'),
                subtitle: Text(
                  SettingsService.getBackupLocation().isEmpty
                      ? 'Default location'
                      : SettingsService.getBackupLocation(),
                ),
                trailing: TextButton(
                  onPressed: () => _selectBackupLocation(),
                  child: const Text('Change'),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        _buildSectionHeader('Manual Backup'),
        Card(
          child: Column(
            children: [
              ListTile(
                title: const Text('Export Settings'),
                subtitle: const Text('Save all settings to a file'),
                trailing: ElevatedButton(
                  onPressed: () => _exportSettings(),
                  child: const Text('Export'),
                ),
              ),
              ListTile(
                title: const Text('Import Settings'),
                subtitle: const Text('Load settings from a file'),
                trailing: ElevatedButton(
                  onPressed: () => _importSettings(),
                  child: const Text('Import'),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSecurityTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('App Protection'),
        Card(
          child: Column(
            children: [
              SwitchListTile(
                title: const Text('Password Protection'),
                subtitle: const Text('Require password to open the app'),
                value: SettingsService.getPasswordProtection(),
                onChanged: (value) {
                  if (value) {
                    _setupPassword();
                  } else {
                    SettingsService.setPasswordProtection(false);
                    setState(() {});
                  }
                },
              ),
              if (SettingsService.getPasswordProtection())
                ListTile(
                  title: const Text('Change Password'),
                  subtitle: const Text('Update your app password'),
                  trailing: TextButton(
                    onPressed: () => _changePassword(),
                    child: const Text('Change'),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAdvancedTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Performance'),
        Card(
          child: Column(
            children: [
              SwitchListTile(
                title: const Text('Animations'),
                subtitle: const Text('Enable smooth animations'),
                value: SettingsService.getAnimationsEnabled(),
                onChanged: (value) {
                  SettingsService.setAnimationsEnabled(value);
                  setState(() {});
                },
              ),
              SwitchListTile(
                title: const Text('High Performance Mode'),
                subtitle: const Text(
                  'Optimize for performance over visual effects',
                ),
                value: SettingsService.getHighPerformanceMode(),
                onChanged: (value) {
                  SettingsService.setHighPerformanceMode(value);
                  setState(() {});
                },
              ),
              SwitchListTile(
                title: const Text('Compact Mode'),
                subtitle: const Text('Use smaller UI elements'),
                value: SettingsService.getCompactMode(),
                onChanged: (value) {
                  SettingsService.setCompactMode(value);
                  setState(() {});
                },
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        _buildSectionHeader('Reset'),
        Card(
          child: Column(
            children: [
              ListTile(
                title: const Text('Reset All Settings'),
                subtitle: const Text('Restore all settings to default values'),
                trailing: ElevatedButton(
                  onPressed: () => _resetAllSettings(),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                  child: const Text('Reset'),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8, top: 16),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }

  String _getLanguageName(String code) {
    switch (code) {
      case 'en':
        return 'English';
      case 'ar':
        return 'العربية';
      case 'es':
        return 'Español';
      case 'fr':
        return 'Français';
      default:
        return 'English';
    }
  }

  void _showColorPicker(BuildContext context, Color currentColor) {
    showDialog(
      context: context,
      builder: (context) => CustomColorPicker(
        initialColor: currentColor,
        onColorChanged: (color) {
          ref.read(customThemeColorProvider.notifier).setColor(color);
        },
      ),
    );
  }

  void _showAppSettings(BuildContext context, String appName, String appId) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 600,
          height: 500,
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    _getAppIcon(appId),
                    size: 32,
                    color: AppTheme.primaryColor,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '$appName Settings',
                          style: Theme.of(context).textTheme.headlineSmall
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          'Customize $appName specific preferences',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Expanded(child: _buildAppSpecificSettings(appId)),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Close'),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: () {
                      // Save settings
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Settings saved successfully'),
                        ),
                      );
                    },
                    child: const Text('Save Changes'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _editShortcut(
    BuildContext context,
    String title,
    String action,
    String currentShortcut,
  ) {
    // Implementation for editing keyboard shortcuts
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit Shortcut: $title'),
        content: const Text('Shortcut editing will be implemented here'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _selectBackupLocation() {
    // Implementation for selecting backup location
  }

  void _exportSettings() {
    // Implementation for exporting settings
  }

  void _importSettings() {
    // Implementation for importing settings
  }

  void _setupPassword() {
    // Implementation for setting up password
  }

  void _changePassword() {
    // Implementation for changing password
  }

  void _resetAllSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset All Settings'),
        content: const Text(
          'Are you sure you want to reset all settings to default values? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              SettingsService.resetAllSettings();
              Navigator.of(context).pop();
              setState(() {});
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  IconData _getAppIcon(String appId) {
    switch (appId) {
      case 'money_flow':
        return Icons.account_balance_wallet;
      case 'excel_to_app':
        return Icons.table_view;
      case 'quran_suite':
        return Icons.mosque;
      case 'tools_builder':
        return Icons.build;
      default:
        return Icons.apps;
    }
  }

  Widget _buildAppSpecificSettings(String appId) {
    switch (appId) {
      case 'money_flow':
        return _buildMoneyFlowSettings();
      case 'excel_to_app':
        return _buildExcelToAppSettings();
      case 'quran_suite':
        return _buildQuranSuiteSettings();
      case 'tools_builder':
        return _buildToolsBuilderSettings();
      default:
        return const Center(child: Text('No settings available for this app'));
    }
  }

  Widget _buildMoneyFlowSettings() {
    return ListView(
      children: [
        _buildSettingsSection('Data Management', [
          SwitchListTile(
            title: const Text('Auto-save transactions'),
            subtitle: const Text('Automatically save transactions as you type'),
            value: true,
            onChanged: (value) => setState(() {}),
          ),
          ListTile(
            title: const Text('Auto-save interval'),
            subtitle: const Text('Every 30 seconds'),
            trailing: DropdownButton<int>(
              value: 30,
              items: const [
                DropdownMenuItem(value: 10, child: Text('10 seconds')),
                DropdownMenuItem(value: 30, child: Text('30 seconds')),
                DropdownMenuItem(value: 60, child: Text('1 minute')),
                DropdownMenuItem(value: 300, child: Text('5 minutes')),
              ],
              onChanged: (value) => setState(() {}),
            ),
          ),
        ]),
        _buildSettingsSection('Privacy', [
          SwitchListTile(
            title: const Text('Hide account balances'),
            subtitle: const Text('Show *** instead of actual amounts'),
            value: false,
            onChanged: (value) => setState(() {}),
          ),
          SwitchListTile(
            title: const Text('Require PIN for transactions'),
            subtitle: const Text(
              'Ask for PIN before adding/editing transactions',
            ),
            value: false,
            onChanged: (value) => setState(() {}),
          ),
        ]),
        _buildSettingsSection('Display', [
          ListTile(
            title: const Text('Default currency'),
            subtitle: const Text('USD'),
            trailing: DropdownButton<String>(
              value: 'USD',
              items: const [
                DropdownMenuItem(value: 'USD', child: Text('USD')),
                DropdownMenuItem(value: 'EUR', child: Text('EUR')),
                DropdownMenuItem(value: 'GBP', child: Text('GBP')),
                DropdownMenuItem(value: 'SAR', child: Text('SAR')),
              ],
              onChanged: (value) => setState(() {}),
            ),
          ),
          SwitchListTile(
            title: const Text('Show transaction categories'),
            subtitle: const Text('Display category icons in transaction lists'),
            value: true,
            onChanged: (value) => setState(() {}),
          ),
        ]),
      ],
    );
  }

  Widget _buildExcelToAppSettings() {
    return ListView(
      children: [
        _buildSettingsSection('Data Management', [
          SwitchListTile(
            title: const Text('Auto-save tools'),
            subtitle: const Text('Automatically save changes to tools'),
            value: true,
            onChanged: (value) => setState(() {}),
          ),
          ListTile(
            title: const Text('Auto-save interval'),
            subtitle: const Text('Every 30 seconds'),
            trailing: DropdownButton<int>(
              value: 30,
              items: const [
                DropdownMenuItem(value: 15, child: Text('15 seconds')),
                DropdownMenuItem(value: 30, child: Text('30 seconds')),
                DropdownMenuItem(value: 60, child: Text('1 minute')),
                DropdownMenuItem(value: 120, child: Text('2 minutes')),
              ],
              onChanged: (value) => setState(() {}),
            ),
          ),
        ]),
        _buildSettingsSection('Privacy', [
          SwitchListTile(
            title: const Text('Tool sharing permissions'),
            subtitle: const Text('Allow tools to be shared with other users'),
            value: true,
            onChanged: (value) => setState(() {}),
          ),
          SwitchListTile(
            title: const Text('Data encryption'),
            subtitle: const Text('Encrypt tool data for enhanced security'),
            value: false,
            onChanged: (value) => setState(() {}),
          ),
        ]),
        _buildSettingsSection('Layout & Theme', [
          ListTile(
            title: const Text('Grid size'),
            subtitle: const Text('Medium (20px)'),
            trailing: DropdownButton<String>(
              value: 'medium',
              items: const [
                DropdownMenuItem(value: 'small', child: Text('Small (10px)')),
                DropdownMenuItem(value: 'medium', child: Text('Medium (20px)')),
                DropdownMenuItem(value: 'large', child: Text('Large (30px)')),
              ],
              onChanged: (value) => setState(() {}),
            ),
          ),
          SwitchListTile(
            title: const Text('Show grid lines'),
            subtitle: const Text('Display grid lines in the canvas'),
            value: true,
            onChanged: (value) => setState(() {}),
          ),
        ]),
      ],
    );
  }

  Widget _buildQuranSuiteSettings() {
    return ListView(
      children: [
        _buildSettingsSection('Reading Preferences', [
          ListTile(
            title: const Text('Arabic text style'),
            subtitle: const Text('Uthmani'),
            trailing: DropdownButton<String>(
              value: 'uthmani',
              items: const [
                DropdownMenuItem(value: 'uthmani', child: Text('Uthmani')),
                DropdownMenuItem(
                  value: 'simplified',
                  child: Text('Simplified'),
                ),
                DropdownMenuItem(value: 'indopak', child: Text('Indo-Pak')),
              ],
              onChanged: (value) => setState(() {}),
            ),
          ),
          ListTile(
            title: const Text('Translation language'),
            subtitle: const Text('English'),
            trailing: DropdownButton<String>(
              value: 'en',
              items: const [
                DropdownMenuItem(value: 'en', child: Text('English')),
                DropdownMenuItem(value: 'ar', child: Text('Arabic')),
                DropdownMenuItem(value: 'ur', child: Text('Urdu')),
                DropdownMenuItem(value: 'fr', child: Text('French')),
              ],
              onChanged: (value) => setState(() {}),
            ),
          ),
          SwitchListTile(
            title: const Text('Show verse numbers'),
            subtitle: const Text('Display verse numbers in Arabic text'),
            value: true,
            onChanged: (value) => setState(() {}),
          ),
        ]),
        _buildSettingsSection('Audio', [
          ListTile(
            title: const Text('Default reciter'),
            subtitle: const Text('Mishary Rashid Alafasy'),
            trailing: DropdownButton<String>(
              value: 'alafasy',
              items: const [
                DropdownMenuItem(
                  value: 'alafasy',
                  child: Text('Mishary Alafasy'),
                ),
                DropdownMenuItem(
                  value: 'sudais',
                  child: Text('Abdul Rahman Al-Sudais'),
                ),
                DropdownMenuItem(
                  value: 'husary',
                  child: Text('Mahmoud Khalil Al-Husary'),
                ),
              ],
              onChanged: (value) => setState(() {}),
            ),
          ),
          SwitchListTile(
            title: const Text('Auto-play next verse'),
            subtitle: const Text('Automatically play the next verse'),
            value: false,
            onChanged: (value) => setState(() {}),
          ),
        ]),
      ],
    );
  }

  Widget _buildToolsBuilderSettings() {
    return ListView(
      children: [
        _buildSettingsSection('Excel Functionality', [
          SwitchListTile(
            title: const Text('Enable all Excel functions'),
            subtitle: const Text('Support for advanced Excel formulas'),
            value: true,
            onChanged: (value) => setState(() {}),
          ),
          SwitchListTile(
            title: const Text('Real-time calculations'),
            subtitle: const Text('Update formulas as you type'),
            value: true,
            onChanged: (value) => setState(() {}),
          ),
          ListTile(
            title: const Text('Calculation precision'),
            subtitle: const Text('15 decimal places'),
            trailing: DropdownButton<int>(
              value: 15,
              items: const [
                DropdownMenuItem(value: 2, child: Text('2 decimal places')),
                DropdownMenuItem(value: 5, child: Text('5 decimal places')),
                DropdownMenuItem(value: 10, child: Text('10 decimal places')),
                DropdownMenuItem(value: 15, child: Text('15 decimal places')),
              ],
              onChanged: (value) => setState(() {}),
            ),
          ),
        ]),
        _buildSettingsSection('UI Builder', [
          SwitchListTile(
            title: const Text('Touch-optimized controls'),
            subtitle: const Text('Larger touch targets for mobile devices'),
            value: true,
            onChanged: (value) => setState(() {}),
          ),
          SwitchListTile(
            title: const Text('Show component outlines'),
            subtitle: const Text('Display borders around UI components'),
            value: true,
            onChanged: (value) => setState(() {}),
          ),
          ListTile(
            title: const Text('Default component size'),
            subtitle: const Text('Medium'),
            trailing: DropdownButton<String>(
              value: 'medium',
              items: const [
                DropdownMenuItem(value: 'small', child: Text('Small')),
                DropdownMenuItem(value: 'medium', child: Text('Medium')),
                DropdownMenuItem(value: 'large', child: Text('Large')),
              ],
              onChanged: (value) => setState(() {}),
            ),
          ),
        ]),
      ],
    );
  }

  Widget _buildSettingsSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
        ),
        Card(child: Column(children: children)),
      ],
    );
  }
}
