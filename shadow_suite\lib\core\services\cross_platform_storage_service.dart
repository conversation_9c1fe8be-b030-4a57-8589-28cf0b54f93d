import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path/path.dart' as path;

/// Cross-platform storage access service for Windows and Android
class CrossPlatformStorageService {
  static const MethodChannel _channel = MethodChannel('shadow_suite/storage');

  // Storage permission status
  static bool _hasStoragePermission = false;
  static bool _hasManageExternalStorage = false;
  static DateTime? _lastPermissionCheck;

  /// Check if permission check cache is valid (within 5 minutes)
  static bool _isPermissionCacheValid() {
    return _lastPermissionCheck != null &&
        DateTime.now().difference(_lastPermissionCheck!).inMinutes < 5;
  }

  /// Initialize storage service
  static Future<void> initialize() async {
    try {
      await checkPermissions();
      await _initializePlatformSpecific();
    } catch (e) {
      debugPrint('Error initializing storage service: $e');
    }
  }

  /// Check current storage permissions
  static Future<StoragePermissionStatus> checkPermissions() async {
    try {
      // Use cache if valid
      if (_isPermissionCacheValid()) {
        return StoragePermissionStatus(
          hasBasicStorage: _hasStoragePermission,
          hasManageExternalStorage: _hasManageExternalStorage,
          hasMediaAccess: _hasStoragePermission,
          canAccessNetworkDrives: Platform.isWindows,
          lastChecked: _lastPermissionCheck!,
        );
      }

      if (Platform.isWindows) {
        // Windows has full file system access by default
        _hasStoragePermission = true;
        _hasManageExternalStorage = true;
        _lastPermissionCheck = DateTime.now();

        return StoragePermissionStatus(
          hasBasicStorage: true,
          hasManageExternalStorage: true,
          hasMediaAccess: true,
          canAccessNetworkDrives: true,
          lastChecked: DateTime.now(),
        );
      } else if (Platform.isAndroid) {
        final Map<String, dynamic> result = await _channel.invokeMethod(
          'checkPermissions',
        );

        _hasStoragePermission = result['hasStoragePermission'] ?? false;
        _hasManageExternalStorage = result['hasManageExternalStorage'] ?? false;
        _lastPermissionCheck = DateTime.now();

        return StoragePermissionStatus(
          hasBasicStorage: _hasStoragePermission,
          hasManageExternalStorage: _hasManageExternalStorage,
          hasMediaAccess: result['hasMediaAccess'] ?? false,
          canAccessNetworkDrives: false,
          lastChecked: DateTime.now(),
        );
      }
    } catch (e) {
      debugPrint('Error checking permissions: $e');
    }

    return StoragePermissionStatus(
      hasBasicStorage: false,
      hasManageExternalStorage: false,
      hasMediaAccess: false,
      canAccessNetworkDrives: false,
      lastChecked: DateTime.now(),
    );
  }

  /// Request storage permissions
  static Future<bool> requestPermissions() async {
    try {
      if (Platform.isWindows) {
        // Windows doesn't need permission requests
        return true;
      } else if (Platform.isAndroid) {
        final bool granted = await _channel.invokeMethod('requestPermissions');
        if (granted) {
          await checkPermissions();
        }
        return granted;
      }
    } catch (e) {
      debugPrint('Error requesting permissions: $e');
    }
    return false;
  }

  /// Get available storage locations
  static Future<List<StorageLocation>> getStorageLocations() async {
    try {
      if (Platform.isWindows) {
        return await _getWindowsStorageLocations();
      } else if (Platform.isAndroid) {
        return await _getAndroidStorageLocations();
      }
    } catch (e) {
      debugPrint('Error getting storage locations: $e');
    }
    return [];
  }

  /// Get network drives (Windows only)
  static Future<List<NetworkDrive>> getNetworkDrives() async {
    if (!Platform.isWindows) return [];

    try {
      final result = await Process.run('wmic', [
        'logicaldisk',
        'get',
        'size,freespace,caption,drivetype',
      ]);
      final drives = <NetworkDrive>[];

      // Parse wmic output to find network drives (DriveType = 4)
      final lines = result.stdout.toString().split('\n');
      for (final line in lines) {
        if (line.contains('4')) {
          // Network drive type
          final parts = line.trim().split(RegExp(r'\s+'));
          if (parts.length >= 4) {
            drives.add(
              NetworkDrive(
                name: parts[0],
                path: parts[0],
                totalSpace: int.tryParse(parts[2]) ?? 0,
                freeSpace: int.tryParse(parts[1]) ?? 0,
                isConnected: true,
              ),
            );
          }
        }
      }

      return drives;
    } catch (e) {
      debugPrint('Error getting network drives: $e');
      return [];
    }
  }

  /// Check if path is accessible
  static Future<bool> isPathAccessible(String path) async {
    try {
      final directory = Directory(path);
      return await directory.exists();
    } catch (e) {
      return false;
    }
  }

  /// Get directory contents with permission handling
  static Future<List<FileSystemEntity>> getDirectoryContents(
    String path,
  ) async {
    try {
      final directory = Directory(path);
      if (!await directory.exists()) {
        throw Exception('Directory does not exist: $path');
      }

      final contents = <FileSystemEntity>[];
      await for (final entity in directory.list()) {
        contents.add(entity);
      }

      return contents;
    } catch (e) {
      debugPrint('Error accessing directory $path: $e');
      rethrow;
    }
  }

  /// Platform-specific initialization
  static Future<void> _initializePlatformSpecific() async {
    if (Platform.isWindows) {
      await _initializeWindows();
    } else if (Platform.isAndroid) {
      await _initializeAndroid();
    }
  }

  static Future<void> _initializeWindows() async {
    // Initialize Windows-specific storage features
    debugPrint('Initialized Windows storage access');
  }

  static Future<void> _initializeAndroid() async {
    try {
      await _channel.invokeMethod('initializeAndroid');
      debugPrint('Initialized Android storage access');
    } catch (e) {
      debugPrint('Error initializing Android storage: $e');
    }
  }

  static Future<List<StorageLocation>> _getWindowsStorageLocations() async {
    final locations = <StorageLocation>[];

    try {
      // Get all drive letters
      final result = await Process.run('wmic', [
        'logicaldisk',
        'get',
        'size,freespace,caption',
      ]);
      final lines = result.stdout.toString().split('\n');

      for (final line in lines) {
        final trimmed = line.trim();
        if (trimmed.isNotEmpty && trimmed.contains(':')) {
          final parts = trimmed.split(RegExp(r'\s+'));
          if (parts.isNotEmpty) {
            final driveLetter = parts[0];
            locations.add(
              StorageLocation(
                name: 'Local Disk ($driveLetter)',
                path: '$driveLetter\\',
                type: StorageType.internal,
                isAccessible: true,
                totalSpace: 0, // Will be calculated later
                freeSpace: 0,
              ),
            );
          }
        }
      }

      // Add common Windows directories
      locations.addAll([
        StorageLocation(
          name: 'Documents',
          path: path.join(
            Platform.environment['USERPROFILE'] ?? '',
            'Documents',
          ),
          type: StorageType.documents,
          isAccessible: true,
          totalSpace: 0,
          freeSpace: 0,
        ),
        StorageLocation(
          name: 'Downloads',
          path: path.join(
            Platform.environment['USERPROFILE'] ?? '',
            'Downloads',
          ),
          type: StorageType.downloads,
          isAccessible: true,
          totalSpace: 0,
          freeSpace: 0,
        ),
        StorageLocation(
          name: 'Pictures',
          path: path.join(
            Platform.environment['USERPROFILE'] ?? '',
            'Pictures',
          ),
          type: StorageType.pictures,
          isAccessible: true,
          totalSpace: 0,
          freeSpace: 0,
        ),
        StorageLocation(
          name: 'Videos',
          path: path.join(Platform.environment['USERPROFILE'] ?? '', 'Videos'),
          type: StorageType.videos,
          isAccessible: true,
          totalSpace: 0,
          freeSpace: 0,
        ),
        StorageLocation(
          name: 'Music',
          path: path.join(Platform.environment['USERPROFILE'] ?? '', 'Music'),
          type: StorageType.music,
          isAccessible: true,
          totalSpace: 0,
          freeSpace: 0,
        ),
      ]);
    } catch (e) {
      debugPrint('Error getting Windows storage locations: $e');
    }

    return locations;
  }

  static Future<List<StorageLocation>> _getAndroidStorageLocations() async {
    try {
      final Map<String, dynamic> result = await _channel.invokeMethod(
        'getStorageLocations',
      );
      final locations = <StorageLocation>[];

      for (final location in result['locations'] as List) {
        locations.add(
          StorageLocation.fromMap(location as Map<String, dynamic>),
        );
      }

      return locations;
    } catch (e) {
      debugPrint('Error getting Android storage locations: $e');
      return [];
    }
  }
}

/// Storage permission status model
class StoragePermissionStatus {
  final bool hasBasicStorage;
  final bool hasManageExternalStorage;
  final bool hasMediaAccess;
  final bool canAccessNetworkDrives;
  final DateTime lastChecked;

  const StoragePermissionStatus({
    required this.hasBasicStorage,
    required this.hasManageExternalStorage,
    required this.hasMediaAccess,
    required this.canAccessNetworkDrives,
    required this.lastChecked,
  });

  bool get hasAllPermissions =>
      hasBasicStorage && hasManageExternalStorage && hasMediaAccess;
}

/// Storage location model
class StorageLocation {
  final String name;
  final String path;
  final StorageType type;
  final bool isAccessible;
  final int totalSpace;
  final int freeSpace;

  const StorageLocation({
    required this.name,
    required this.path,
    required this.type,
    required this.isAccessible,
    required this.totalSpace,
    required this.freeSpace,
  });

  factory StorageLocation.fromMap(Map<String, dynamic> map) {
    return StorageLocation(
      name: map['name'] as String,
      path: map['path'] as String,
      type: StorageType.values[map['type'] as int],
      isAccessible: map['isAccessible'] as bool,
      totalSpace: map['totalSpace'] as int,
      freeSpace: map['freeSpace'] as int,
    );
  }
}

/// Network drive model
class NetworkDrive {
  final String name;
  final String path;
  final int totalSpace;
  final int freeSpace;
  final bool isConnected;

  const NetworkDrive({
    required this.name,
    required this.path,
    required this.totalSpace,
    required this.freeSpace,
    required this.isConnected,
  });
}

/// Storage type enumeration
enum StorageType {
  internal,
  external,
  removable,
  network,
  documents,
  downloads,
  pictures,
  videos,
  music,
}

/// Storage service provider
final storageServiceProvider = Provider<CrossPlatformStorageService>((ref) {
  return CrossPlatformStorageService();
});

/// Storage permissions provider
final storagePermissionsProvider =
    StateNotifierProvider<StoragePermissionsNotifier, StoragePermissionStatus>((
      ref,
    ) {
      return StoragePermissionsNotifier();
    });

class StoragePermissionsNotifier
    extends StateNotifier<StoragePermissionStatus> {
  StoragePermissionsNotifier()
    : super(
        StoragePermissionStatus(
          hasBasicStorage: false,
          hasManageExternalStorage: false,
          hasMediaAccess: false,
          canAccessNetworkDrives: false,
          lastChecked: DateTime.now(),
        ),
      ) {
    checkPermissions();
  }

  Future<void> checkPermissions() async {
    final status = await CrossPlatformStorageService.checkPermissions();
    state = status;
  }

  Future<bool> requestPermissions() async {
    final granted = await CrossPlatformStorageService.requestPermissions();
    if (granted) {
      await checkPermissions();
    }
    return granted;
  }
}
