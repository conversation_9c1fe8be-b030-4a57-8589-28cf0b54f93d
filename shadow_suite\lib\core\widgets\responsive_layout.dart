import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../services/layout_service.dart';
import '../services/route_tracker_service.dart';
import 'unified_navigation_bar.dart';
import 'mobile_navigation_drawer.dart';
import '../themes/theme_models_base.dart';

// Responsive Layout Widget
class ResponsiveLayout extends ConsumerStatefulWidget {
  final Widget child;
  final bool autoAdapt;

  const ResponsiveLayout({
    super.key,
    required this.child,
    this.autoAdapt = true,
  });

  @override
  ConsumerState<ResponsiveLayout> createState() => _ResponsiveLayoutState();
}

class _ResponsiveLayoutState extends ConsumerState<ResponsiveLayout> {
  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final layoutConfig = ref.watch(currentLayoutProvider);
    final layoutService = ref.read(layoutServiceProvider);

    // Update screen size in provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(screenSizeProvider.notifier).state = screenSize;
    });

    // Auto-adapt layout if enabled
    if (widget.autoAdapt) {
      final shouldAdapt = layoutService.shouldAdaptLayout(
        screenSize,
        layoutConfig,
      );
      if (shouldAdapt) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          final deviceType = layoutService.getDeviceType(screenSize);
          final recommendedLayout = layoutService.getRecommendedLayout(
            deviceType,
          );
          final newConfig = layoutService.getLayoutConfig(
            recommendedLayout,
            deviceType,
          );
          ref.read(currentLayoutProvider.notifier).updateLayout(newConfig);
        });
      }
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        return _buildLayoutSystem(context, layoutConfig, constraints);
      },
    );
  }

  Widget _buildLayoutSystem(
    BuildContext context,
    LayoutConfiguration config,
    BoxConstraints constraints,
  ) {
    switch (config.layoutSystem) {
      case LayoutSystem.desktopOptimized:
        return _buildDesktopLayout(context, config, constraints);
      case LayoutSystem.materialDesignMobile:
        return _buildMaterialDesignLayout(context, config, constraints);
      case LayoutSystem.androidNativeSmall:
        return _buildAndroidNativeLayout(context, config, constraints);
    }
  }

  Widget _buildDesktopLayout(
    BuildContext context,
    LayoutConfiguration config,
    BoxConstraints constraints,
  ) {
    final isMobile = constraints.maxWidth < config.breakpoints.tablet;
    final currentRoute = GoRouterState.of(context).uri.toString();

    return Scaffold(
      appBar: UnifiedNavigationBar(
        currentRoute: currentRoute,
        isMobile: isMobile,
      ),

      body: Row(
        children: [
          // Sidebar
          if (!isMobile || !config.enableSidebarCollapse)
            Container(
              width: config.sidebarWidth,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                border: Border(
                  right: BorderSide(
                    color: Theme.of(context).dividerColor,
                    width: 1,
                  ),
                ),
              ),
              child: const MobileNavigationDrawer(),
            ),

          // Main content
          Expanded(child: widget.child),
        ],
      ),

      // Mobile drawer
      drawer: isMobile && config.enableSidebarCollapse
          ? Drawer(
              width: config.sidebarWidth,
              child: const MobileNavigationDrawer(),
            )
          : null,
    );
  }

  Widget _buildMaterialDesignLayout(
    BuildContext context,
    LayoutConfiguration config,
    BoxConstraints constraints,
  ) {
    final isMobile = constraints.maxWidth < config.breakpoints.tablet;
    final currentRoute = GoRouterState.of(context).uri.toString();

    return Scaffold(
      appBar: UnifiedNavigationBar(
        currentRoute: currentRoute,
        isMobile: isMobile,
      ),

      body: widget.child,

      // Unified sidebar navigation for all screen sizes
      drawer: Drawer(
        width: isMobile ? config.sidebarWidth * 0.85 : config.sidebarWidth,
        child: const MobileNavigationDrawer(),
      ),
    );
  }

  Widget _buildAndroidNativeLayout(
    BuildContext context,
    LayoutConfiguration config,
    BoxConstraints constraints,
  ) {
    final isMobile = constraints.maxWidth < config.breakpoints.tablet;
    final currentRoute = GoRouterState.of(context).uri.toString();

    return Scaffold(
      appBar: UnifiedNavigationBar(
        currentRoute: currentRoute,
        isMobile: isMobile,
      ),

      body: widget.child,

      // Unified sidebar navigation - optimized for mobile
      drawer: Drawer(
        width: isMobile ? config.sidebarWidth * 0.9 : config.sidebarWidth,
        child: const MobileNavigationDrawer(),
      ),
    );
  }
}

// SidebarContent removed - using unified MobileNavigationDrawer instead

// Mobile-optimized breadcrumb widget for better navigation context
class MobileBreadcrumb extends ConsumerWidget {
  const MobileBreadcrumb({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final routeInfo = ref.watch(routeInfoProvider);

    if (routeInfo.breadcrumb.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.8),
        border: Border(
          bottom: BorderSide(color: Theme.of(context).dividerColor, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.location_on,
            size: 16,
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              routeInfo.breadcrumb.join(' > '),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.8),
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
