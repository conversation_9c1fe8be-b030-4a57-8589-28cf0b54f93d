import 'package:flutter/material.dart';
import '../models/file_manager_models.dart';
import '../services/file_operations_service.dart';

class FileContextMenu extends StatelessWidget {
  final List<FileSystemItem> selectedItems;
  final VoidCallback? onRefresh;
  final Function(String)? onNavigate;

  const FileContextMenu({
    super.key,
    required this.selectedItems,
    this.onRefresh,
    this.onNavigate,
  });

  @override
  Widget build(BuildContext context) {
    final hasSelection = selectedItems.isNotEmpty;
    final singleSelection = selectedItems.length == 1;
    final selectedItem = singleSelection ? selectedItems.first : null;
    final isDirectory = selectedItem?.isDirectory ?? false;

    return Container(
      width: 200,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(color: Colors.black26, blurRadius: 8, offset: Offset(0, 4)),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // File operations
          if (hasSelection) ...[
            _buildMenuItem(
              icon: Icons.content_copy,
              label: 'Copy',
              shortcut: 'Ctrl+C',
              onTap: () => _copyItems(context),
            ),
            _buildMenuItem(
              icon: Icons.content_cut,
              label: 'Cut',
              shortcut: 'Ctrl+X',
              onTap: () => _cutItems(context),
            ),
          ],

          if (FileOperationsService.hasClipboardItems) ...[
            _buildMenuItem(
              icon: Icons.content_paste,
              label: 'Paste',
              shortcut: 'Ctrl+V',
              onTap: () => _pasteItems(context),
            ),
          ],

          if (hasSelection) ...[
            const Divider(height: 1),
            _buildMenuItem(
              icon: Icons.delete,
              label: 'Delete',
              shortcut: 'Del',
              onTap: () => _deleteItems(context),
              isDestructive: true,
            ),

            if (singleSelection) ...[
              _buildMenuItem(
                icon: Icons.edit,
                label: 'Rename',
                shortcut: 'F2',
                onTap: () => _renameItem(context),
              ),
            ],
          ],

          const Divider(height: 1),

          // Navigation
          if (singleSelection && isDirectory) ...[
            _buildMenuItem(
              icon: Icons.folder_open,
              label: 'Open',
              shortcut: 'Enter',
              onTap: () => _openItem(context),
            ),
            _buildMenuItem(
              icon: Icons.launch,
              label: 'Open in New Pane',
              onTap: () => _openInNewPane(context),
            ),
          ],

          if (singleSelection && !isDirectory) ...[
            _buildMenuItem(
              icon: Icons.open_in_new,
              label: 'Open',
              shortcut: 'Enter',
              onTap: () => _openItem(context),
            ),
            _buildMenuItem(
              icon: Icons.edit,
              label: 'Edit',
              onTap: () => _editFile(context),
            ),
          ],

          const Divider(height: 1),

          // Creation
          _buildMenuItem(
            icon: Icons.create_new_folder,
            label: 'New Folder',
            shortcut: 'Ctrl+Shift+N',
            onTap: () => _createNewFolder(context),
          ),
          _buildMenuItem(
            icon: Icons.note_add,
            label: 'New File',
            onTap: () => _createNewFile(context),
          ),

          const Divider(height: 1),

          // Selection
          _buildMenuItem(
            icon: Icons.select_all,
            label: 'Select All',
            shortcut: 'Ctrl+A',
            onTap: () => _selectAll(context),
          ),

          if (hasSelection) ...[
            _buildMenuItem(
              icon: Icons.deselect,
              label: 'Deselect All',
              shortcut: 'Ctrl+D',
              onTap: () => _deselectAll(context),
            ),
          ],

          const Divider(height: 1),

          // Properties
          if (singleSelection) ...[
            _buildMenuItem(
              icon: Icons.info,
              label: 'Properties',
              shortcut: 'Alt+Enter',
              onTap: () => _showProperties(context),
            ),
          ],

          // Refresh
          _buildMenuItem(
            icon: Icons.refresh,
            label: 'Refresh',
            shortcut: 'F5',
            onTap: () => _refresh(context),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String label,
    String? shortcut,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          children: [
            Icon(
              icon,
              size: 16,
              color: isDestructive ? Colors.red : const Color(0xFF495057),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                label,
                style: TextStyle(
                  fontSize: 13,
                  color: isDestructive ? Colors.red : const Color(0xFF495057),
                ),
              ),
            ),
            if (shortcut != null) ...[
              Text(
                shortcut,
                style: const TextStyle(fontSize: 11, color: Color(0xFF7F8C8D)),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _copyItems(BuildContext context) {
    final paths = selectedItems.map((item) => item.path).toList();
    FileOperationsService.copyToClipboard(paths);
    Navigator.pop(context);
    _showSnackBar(context, 'Copied ${selectedItems.length} item(s)');
  }

  void _cutItems(BuildContext context) {
    final paths = selectedItems.map((item) => item.path).toList();
    FileOperationsService.cutToClipboard(paths);
    Navigator.pop(context);
    _showSnackBar(context, 'Cut ${selectedItems.length} item(s)');
  }

  void _pasteItems(BuildContext context) async {
    Navigator.pop(context);
    try {
      // Get current directory path from first selected item or use default
      final currentPath = selectedItems.isNotEmpty
          ? selectedItems.first.path.substring(
              0,
              selectedItems.first.path.lastIndexOf('/'),
            )
          : '/storage/emulated/0';
      await FileOperationsService.pasteFromClipboard(currentPath);
      onRefresh?.call();
      if (context.mounted) {
        _showSnackBar(context, 'Items pasted successfully');
      }
    } catch (error) {
      if (context.mounted) {
        _showErrorSnackBar(context, 'Error pasting items: $error');
      }
    }
  }

  void _deleteItems(BuildContext context) async {
    Navigator.pop(context);

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Items'),
        content: Text(
          'Are you sure you want to delete ${selectedItems.length} item(s)? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final paths = selectedItems.map((item) => item.path).toList();
        await FileOperationsService.deleteItems(paths);
        onRefresh?.call();
        if (context.mounted) {
          _showSnackBar(context, 'Items deleted successfully');
        }
      } catch (error) {
        if (context.mounted) {
          _showErrorSnackBar(context, 'Error deleting items: $error');
        }
      }
    }
  }

  void _renameItem(BuildContext context) {
    Navigator.pop(context);

    if (selectedItems.isEmpty) return;

    final item = selectedItems.first;
    String newName = item.name;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Rename Item'),
        content: TextField(
          controller: TextEditingController(text: newName),
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            labelText: 'New name',
          ),
          onChanged: (value) => newName = value,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              if (newName.isNotEmpty && newName != item.name) {
                try {
                  await FileOperationsService.renameItem(item.path, newName);
                  onRefresh?.call();
                  if (context.mounted) {
                    _showSnackBar(context, 'Item renamed successfully');
                  }
                } catch (error) {
                  if (context.mounted) {
                    _showErrorSnackBar(context, 'Error renaming item: $error');
                  }
                }
              }
            },
            child: const Text('Rename'),
          ),
        ],
      ),
    );
  }

  void _openItem(BuildContext context) {
    Navigator.pop(context);
    if (selectedItems.isNotEmpty) {
      final item = selectedItems.first;
      if (item.isDirectory) {
        onNavigate?.call(item.path);
      } else {
        // Open file with default application
        _showSnackBar(context, 'Opening ${item.name}...');
      }
    }
  }

  void _openInNewPane(BuildContext context) {
    Navigator.pop(context);
    // Implement open in new pane functionality
    if (selectedItems.isNotEmpty) {
      final item = selectedItems.first;
      if (item.isDirectory) {
        // In a real implementation, this would open a new pane/tab
        // For now, navigate to the directory
        onNavigate?.call(item.path);
        _showSnackBar(context, 'Opening ${item.name} in new pane...');
      }
    }
  }

  void _editFile(BuildContext context) {
    Navigator.pop(context);
    // Implement file editing functionality
    if (selectedItems.isNotEmpty) {
      final item = selectedItems.first;
      if (!item.isDirectory) {
        // In a real implementation, this would open the file in an editor
        // For now, show a message indicating the file would be opened
        _showSnackBar(context, 'Opening ${item.name} for editing...');
        // Could navigate to a text editor screen here
      }
    }
  }

  void _createNewFolder(BuildContext context) {
    Navigator.pop(context);
    // Implement new folder creation functionality
    String folderName = 'New Folder';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create New Folder'),
        content: TextField(
          controller: TextEditingController(text: folderName),
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            labelText: 'Folder name',
          ),
          onChanged: (value) => folderName = value,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              if (folderName.isNotEmpty) {
                try {
                  // In a real implementation, create the folder
                  _showSnackBar(context, 'Created folder: $folderName');
                  onRefresh?.call();
                } catch (error) {
                  _showErrorSnackBar(context, 'Error creating folder: $error');
                }
              }
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  void _createNewFile(BuildContext context) {
    Navigator.pop(context);
    // Implement new file creation functionality
    String fileName = 'New File.txt';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create New File'),
        content: TextField(
          controller: TextEditingController(text: fileName),
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            labelText: 'File name',
          ),
          onChanged: (value) => fileName = value,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              if (fileName.isNotEmpty) {
                try {
                  // In a real implementation, create the file
                  _showSnackBar(context, 'Created file: $fileName');
                  onRefresh?.call();
                } catch (error) {
                  _showErrorSnackBar(context, 'Error creating file: $error');
                }
              }
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  void _selectAll(BuildContext context) {
    Navigator.pop(context);
    // Implement select all functionality
    // In a real implementation, this would select all items in the current directory
    // For now, show a message indicating the action
    _showSnackBar(context, 'Selecting all items in current directory...');
    onRefresh?.call();
  }

  void _deselectAll(BuildContext context) {
    Navigator.pop(context);
    FileOperationsService.clearSelection();
    onRefresh?.call();
  }

  void _showProperties(BuildContext context) {
    Navigator.pop(context);
    if (selectedItems.isNotEmpty) {
      final item = selectedItems.first;
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Properties: ${item.name}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Type: ${item.isDirectory ? 'Folder' : 'File'}'),
              Text('Size: ${_formatFileSize(item.size)}'),
              Text('Modified: ${_formatDate(item.lastModified)}'),
              Text('Path: ${item.path}'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            ),
          ],
        ),
      );
    }
  }

  void _refresh(BuildContext context) {
    Navigator.pop(context);
    onRefresh?.call();
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF27AE60),
      ),
    );
  }

  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}
