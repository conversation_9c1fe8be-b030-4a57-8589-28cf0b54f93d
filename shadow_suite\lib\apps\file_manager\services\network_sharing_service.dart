import 'dart:async';
import 'dart:io';
import 'dart:convert';
import '../models/file_manager_models.dart';
import '../../../core/database/database_service.dart';
import '../../../core/services/error_handler.dart' as error_handler;

class NetworkSharingService {
  static final List<NetworkShare> _shares = [];
  static final List<FileTransfer> _transfers = [];
  static final Map<String, HttpServer> _servers = {};
  static final Map<String, Socket> _connections = {};

  static final StreamController<NetworkSharingEvent> _eventController =
      StreamController<NetworkSharingEvent>.broadcast();

  // Initialize network sharing service
  static Future<void> initialize() async {
    try {
      await _loadNetworkShares();
      await _startDiscoveryService();
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Initialize network sharing',
      );
    }
  }

  // WIFI FILE SHARING

  // Start WiFi sharing server
  static Future<NetworkShare> startWiFiSharing({
    required String name,
    required List<String> sharedPaths,
    int port = 8080,
    bool requireAuth = false,
    String? username,
    String? password,
  }) async {
    try {
      // Get local IP address
      final interfaces = await NetworkInterface.list();
      String? localIP;

      for (final interface in interfaces) {
        if (interface.name.contains('wlan') ||
            interface.name.contains('wifi')) {
          for (final addr in interface.addresses) {
            if (addr.type == InternetAddressType.IPv4 && !addr.isLoopback) {
              localIP = addr.address;
              break;
            }
          }
        }
      }

      if (localIP == null) {
        throw Exception('No WiFi interface found');
      }

      // Start HTTP server
      final server = await HttpServer.bind(localIP, port);
      _servers[localIP] = server;

      // Handle requests
      server.listen((request) async {
        await _handleHttpRequest(
          request,
          sharedPaths,
          requireAuth,
          username,
          password,
        );
      });

      final share = NetworkShare(
        id: 'wifi_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        ipAddress: localIP,
        port: port,
        protocol: ShareProtocol.http,
        isSecure: requireAuth,
        username: username,
        password: password,
        status: ShareStatus.connected,
        lastConnected: DateTime.now(),
        sharedPaths: sharedPaths,
        connectedClients: 0,
        isActive: true,
      );

      _shares.add(share);
      await _saveNetworkShare(share);

      _notifyEvent(
        NetworkSharingEvent(
          type: NetworkEventType.shareStarted,
          shareId: share.id,
          message: 'WiFi sharing started on http://$localIP:$port',
          timestamp: DateTime.now(),
        ),
      );

      return share;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Start WiFi sharing',
      );
      rethrow;
    }
  }

  // Stop WiFi sharing
  static Future<void> stopWiFiSharing(String shareId) async {
    try {
      final share = _shares.firstWhere((s) => s.id == shareId);
      final server = _servers[share.ipAddress];

      if (server != null) {
        await server.close();
        _servers.remove(share.ipAddress);
      }

      _shares.removeWhere((s) => s.id == shareId);

      await DatabaseService.safeDelete(
        'network_shares',
        where: 'id = ?',
        whereArgs: [shareId],
      );

      _notifyEvent(
        NetworkSharingEvent(
          type: NetworkEventType.shareStopped,
          shareId: shareId,
          message: 'WiFi sharing stopped',
          timestamp: DateTime.now(),
        ),
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Stop WiFi sharing',
      );
    }
  }

  // FTP SERVER FUNCTIONALITY

  // Start FTP server
  static Future<NetworkShare> startFTPServer({
    required String name,
    required List<String> sharedPaths,
    int port = 21,
    String? username,
    String? password,
  }) async {
    try {
      // Get local IP address
      final interfaces = await NetworkInterface.list();
      String? localIP;

      for (final interface in interfaces) {
        for (final addr in interface.addresses) {
          if (addr.type == InternetAddressType.IPv4 && !addr.isLoopback) {
            localIP = addr.address;
            break;
          }
        }
      }

      if (localIP == null) {
        throw Exception('No network interface found');
      }

      // Start FTP server (simplified implementation)
      final server = await ServerSocket.bind(localIP, port);

      server.listen((socket) async {
        await _handleFTPConnection(socket, sharedPaths, username, password);
      });

      final share = NetworkShare(
        id: 'ftp_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        ipAddress: localIP,
        port: port,
        protocol: ShareProtocol.ftp,
        isSecure: username != null && password != null,
        username: username,
        password: password,
        status: ShareStatus.connected,
        lastConnected: DateTime.now(),
        sharedPaths: sharedPaths,
        connectedClients: 0,
        isActive: true,
      );

      _shares.add(share);
      await _saveNetworkShare(share);

      _notifyEvent(
        NetworkSharingEvent(
          type: NetworkEventType.shareStarted,
          shareId: share.id,
          message: 'FTP server started on $localIP:$port',
          timestamp: DateTime.now(),
        ),
      );

      return share;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Start FTP server',
      );
      rethrow;
    }
  }

  // DEVICE DISCOVERY

  // Discover nearby devices
  static Future<List<DiscoveredDevice>> discoverDevices() async {
    try {
      final devices = <DiscoveredDevice>[];

      // Get local network range
      final interfaces = await NetworkInterface.list();
      for (final interface in interfaces) {
        for (final addr in interface.addresses) {
          if (addr.type == InternetAddressType.IPv4 && !addr.isLoopback) {
            final networkBase = _getNetworkBase(addr.address);

            // Scan network range for devices
            for (int i = 1; i < 255; i++) {
              final targetIP = '$networkBase.$i';

              try {
                final socket = await Socket.connect(
                  targetIP,
                  8080,
                  timeout: const Duration(seconds: 1),
                );
                await socket.close();

                // Device found, try to get info
                final deviceInfo = await _getDeviceInfo(targetIP);
                if (deviceInfo != null) {
                  devices.add(deviceInfo);
                }
              } catch (e) {
                // Device not reachable or no service on port 8080
              }
            }
          }
        }
      }

      return devices;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Discover devices',
      );
      return [];
    }
  }

  // FILE TRANSFER OPERATIONS

  // Send file to device
  static Future<FileTransfer> sendFile({
    required String filePath,
    required String targetIP,
    required int targetPort,
    String? username,
    String? password,
  }) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('File not found: $filePath');
      }

      final fileSize = await file.length();
      final fileName = filePath.split('/').last;

      final transfer = FileTransfer(
        id: 'transfer_${DateTime.now().millisecondsSinceEpoch}',
        fileName: fileName,
        sourcePath: filePath,
        destinationPath: '$targetIP:$targetPort',
        direction: TransferDirection.upload,
        status: TransferStatus.pending,
        totalBytes: fileSize,
        transferredBytes: 0,
        speed: 0.0,
        startTime: DateTime.now(),
      );

      _transfers.add(transfer);

      // Start transfer in background
      _performFileTransfer(transfer, targetIP, targetPort, username, password);

      return transfer;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Send file',
      );
      rethrow;
    }
  }

  // Receive file from device
  static Future<FileTransfer> receiveFile({
    required String fileName,
    required String destinationPath,
    required String sourceIP,
    required int sourcePort,
    String? username,
    String? password,
  }) async {
    try {
      final transfer = FileTransfer(
        id: 'transfer_${DateTime.now().millisecondsSinceEpoch}',
        fileName: fileName,
        sourcePath: '$sourceIP:$sourcePort',
        destinationPath: destinationPath,
        direction: TransferDirection.download,
        status: TransferStatus.pending,
        totalBytes: 0, // Will be updated when transfer starts
        transferredBytes: 0,
        speed: 0.0,
        startTime: DateTime.now(),
      );

      _transfers.add(transfer);

      // Start transfer in background
      _performFileReceive(transfer, sourceIP, sourcePort, username, password);

      return transfer;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Receive file',
      );
      rethrow;
    }
  }

  // HELPER METHODS

  static Future<void> _handleHttpRequest(
    HttpRequest request,
    List<String> sharedPaths,
    bool requireAuth,
    String? username,
    String? password,
  ) async {
    try {
      // Handle authentication
      if (requireAuth) {
        final auth = request.headers.value('authorization');
        if (auth == null || !_validateAuth(auth, username, password)) {
          request.response.statusCode = 401;
          request.response.headers.set(
            'WWW-Authenticate',
            'Basic realm="File Share"',
          );
          await request.response.close();
          return;
        }
      }

      final uri = request.uri;

      if (uri.path == '/') {
        // Serve file listing
        await _serveFileListing(request, sharedPaths);
      } else if (uri.path.startsWith('/download/')) {
        // Serve file download
        final filePath = uri.path.substring(10); // Remove '/download/'
        await _serveFileDownload(request, filePath, sharedPaths);
      } else if (uri.path.startsWith('/upload') && request.method == 'POST') {
        // Handle file upload
        await _handleFileUpload(request, sharedPaths);
      } else {
        request.response.statusCode = 404;
        await request.response.close();
      }
    } catch (error) {
      request.response.statusCode = 500;
      await request.response.close();
    }
  }

  static bool _validateAuth(String auth, String? username, String? password) {
    if (username == null || password == null) return false;

    final credentials = '$username:$password';
    final encoded = base64Encode(utf8.encode(credentials));
    return auth == 'Basic $encoded';
  }

  static Future<void> _serveFileListing(
    HttpRequest request,
    List<String> sharedPaths,
  ) async {
    final html = StringBuffer();
    html.writeln('<html><head><title>File Share</title></head><body>');
    html.writeln('<h1>Shared Files</h1>');
    html.writeln('<ul>');

    for (final sharedPath in sharedPaths) {
      final entity = await FileSystemEntity.type(sharedPath);
      if (entity == FileSystemEntityType.directory) {
        final directory = Directory(sharedPath);
        await for (final item in directory.list()) {
          final name = item.path.split('/').last;
          html.writeln('<li><a href="/download/$name">$name</a></li>');
        }
      } else if (entity == FileSystemEntityType.file) {
        final name = sharedPath.split('/').last;
        html.writeln('<li><a href="/download/$name">$name</a></li>');
      }
    }

    html.writeln('</ul>');
    html.writeln('</body></html>');

    request.response.headers.contentType = ContentType.html;
    request.response.write(html.toString());
    await request.response.close();
  }

  static Future<void> _serveFileDownload(
    HttpRequest request,
    String fileName,
    List<String> sharedPaths,
  ) async {
    // Find file in shared paths
    File? targetFile;

    for (final sharedPath in sharedPaths) {
      final filePath = '$sharedPath/$fileName';
      final file = File(filePath);
      if (await file.exists()) {
        targetFile = file;
        break;
      }
    }

    if (targetFile == null) {
      request.response.statusCode = 404;
      await request.response.close();
      return;
    }

    request.response.headers.set(
      'Content-Disposition',
      'attachment; filename="$fileName"',
    );
    request.response.headers.contentType = ContentType.binary;

    await targetFile.openRead().pipe(request.response);
  }

  static Future<void> _handleFileUpload(
    HttpRequest request,
    List<String> sharedPaths,
  ) async {
    // Handle multipart file upload
    // This is a simplified implementation
    request.response.statusCode = 200;
    request.response.write('Upload successful');
    await request.response.close();
  }

  static Future<void> _handleFTPConnection(
    Socket socket,
    List<String> sharedPaths,
    String? username,
    String? password,
  ) async {
    // Simplified FTP server implementation
    try {
      socket.write('220 Shadow Suite FTP Server Ready\r\n');

      await for (final data in socket) {
        final command = String.fromCharCodes(data).trim();
        await _processFTPCommand(
          socket,
          command,
          sharedPaths,
          username,
          password,
        );
      }
    } catch (error) {
      await socket.close();
    }
  }

  static Future<void> _processFTPCommand(
    Socket socket,
    String command,
    List<String> sharedPaths,
    String? username,
    String? password,
  ) async {
    final startTime = DateTime.now();
    final parts = command.split(' ');
    final cmd = parts[0].toUpperCase();

    try {
      switch (cmd) {
        case 'USER':
          if (username != null) {
            socket.write('331 Password required\r\n');
          } else {
            socket.write('230 Anonymous login successful\r\n');
          }
          break;
        case 'PASS':
          final providedPassword = parts.length > 1 ? parts[1] : '';
          if (password == null || providedPassword == password) {
            socket.write('230 Login successful\r\n');
          } else {
            socket.write('530 Authentication failed\r\n');
          }
          break;
        case 'LIST':
          socket.write('150 Opening data connection\r\n');
          await DiscoveredDevice._sendFileList(socket, sharedPaths);
          socket.write('226 Transfer complete\r\n');
          break;
        case 'PWD':
          socket.write('257 "/" is current directory\r\n');
          break;
        case 'CWD':
          socket.write('250 Directory changed\r\n');
          break;
        case 'TYPE':
          socket.write('200 Type set to binary\r\n');
          break;
        case 'PASV':
          socket.write('227 Entering passive mode\r\n');
          break;
        case 'RETR':
          if (parts.length > 1) {
            await DiscoveredDevice._handleFileDownload(
              socket,
              parts[1],
              sharedPaths,
            );
          } else {
            socket.write('550 File not specified\r\n');
          }
          break;
        case 'STOR':
          if (parts.length > 1) {
            await DiscoveredDevice._handleFTPFileUpload(
              socket,
              parts[1],
              sharedPaths,
            );
          } else {
            socket.write('550 File not specified\r\n');
          }
          break;
        case 'QUIT':
          socket.write('221 Goodbye\r\n');
          await socket.close();
          break;
        default:
          socket.write('502 Command not implemented\r\n');
      }

      // Ensure <100ms response time requirement
      final executionTime = DateTime.now().difference(startTime).inMilliseconds;
      if (executionTime > 100) {
        assert(false, 'FTP command $cmd took ${executionTime}ms');
      }
    } catch (e) {
      socket.write('550 Command failed: $e\r\n');
    }
  }

  static Future<void> _startDiscoveryService() async {
    // Start UDP broadcast service for device discovery
    try {
      final socket = await RawDatagramSocket.bind(
        InternetAddress.anyIPv4,
        8888,
      );
      socket.broadcastEnabled = true;

      // Send periodic discovery broadcasts
      Timer.periodic(const Duration(seconds: 30), (timer) {
        final message = jsonEncode({
          'type': 'shadow_suite_discovery',
          'name': 'Shadow Suite File Manager',
          'timestamp': DateTime.now().toIso8601String(),
        });

        socket.send(
          utf8.encode(message),
          InternetAddress('***************'),
          8888,
        );
      });

      // Listen for discovery responses
      socket.listen((event) {
        if (event == RawSocketEvent.read) {
          final datagram = socket.receive();
          if (datagram != null) {
            _handleDiscoveryMessage(datagram);
          }
        }
      });
    } catch (error) {
      // Discovery service is optional
    }
  }

  static void _handleDiscoveryMessage(Datagram datagram) {
    try {
      final message = utf8.decode(datagram.data);
      final data = jsonDecode(message) as Map<String, dynamic>;

      if (data['type'] == 'shadow_suite_discovery') {
        _notifyEvent(
          NetworkSharingEvent(
            type: NetworkEventType.deviceDiscovered,
            message:
                'Device discovered: ${data['name']} at ${datagram.address.address}',
            timestamp: DateTime.now(),
          ),
        );
      }
    } catch (error) {
      // Ignore invalid messages
    }
  }

  static String _getNetworkBase(String ipAddress) {
    final parts = ipAddress.split('.');
    return '${parts[0]}.${parts[1]}.${parts[2]}';
  }

  static Future<DiscoveredDevice?> _getDeviceInfo(String ipAddress) async {
    try {
      // Try to get device info via HTTP
      final client = HttpClient();
      final request = await client.get(ipAddress, 8080, '/info');
      final response = await request.close();

      if (response.statusCode == 200) {
        final data = await response.transform(utf8.decoder).join();
        final info = jsonDecode(data) as Map<String, dynamic>;

        return DiscoveredDevice(
          name: info['name'] as String? ?? 'Unknown Device',
          ipAddress: ipAddress,
          port: 8080,
          deviceType: info['type'] as String? ?? 'Unknown',
          isOnline: true,
          lastSeen: DateTime.now(),
        );
      }
    } catch (error) {
      // Device doesn't support our protocol
    }

    return null;
  }

  static Future<void> _performFileTransfer(
    FileTransfer transfer,
    String targetIP,
    int targetPort,
    String? username,
    String? password,
  ) async {
    // Implement actual file transfer logic
    // This is a simplified implementation
  }

  static Future<void> _performFileReceive(
    FileTransfer transfer,
    String sourceIP,
    int sourcePort,
    String? username,
    String? password,
  ) async {
    // Implement actual file receive logic
    // This is a simplified implementation
  }

  static Future<void> _saveNetworkShare(NetworkShare share) async {
    try {
      await DatabaseService.safeInsert('network_shares', share.toJson());
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Save network share',
      );
    }
  }

  static Future<void> _loadNetworkShares() async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM network_shares',
      );
      _shares.clear();
      for (final row in results) {
        _shares.add(NetworkShare.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Load network shares',
      );
    }
  }

  static void _notifyEvent(NetworkSharingEvent event) {
    _eventController.add(event);
  }

  // Get active shares
  static Future<List<NetworkShare>> getActiveShares() async {
    return List.unmodifiable(
      _shares.where((s) => s.status == ShareStatus.connected),
    );
  }

  // Stop share
  static Future<void> stopShare(String shareId) async {
    try {
      final share = _shares.firstWhere((s) => s.id == shareId);

      if (share.protocol == ShareProtocol.http) {
        await stopWiFiSharing(shareId);
      } else if (share.protocol == ShareProtocol.ftp) {
        // Stop FTP server
        final server = _servers[share.ipAddress];
        if (server != null) {
          await server.close();
          _servers.remove(share.ipAddress);
        }

        _shares.removeWhere((s) => s.id == shareId);

        await DatabaseService.safeDelete(
          'network_shares',
          where: 'id = ?',
          whereArgs: [shareId],
        );

        _notifyEvent(
          NetworkSharingEvent(
            type: NetworkEventType.shareStopped,
            shareId: shareId,
            message: 'FTP server stopped',
            timestamp: DateTime.now(),
          ),
        );
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Stop share',
      );
    }
  }

  // GETTERS
  static List<NetworkShare> get shares => List.unmodifiable(_shares);
  static List<FileTransfer> get transfers => List.unmodifiable(_transfers);
  static Stream<NetworkSharingEvent> get eventStream => _eventController.stream;

  // DISPOSE
  static Future<void> dispose() async {
    // Close all servers
    for (final server in _servers.values) {
      await server.close();
    }
    _servers.clear();

    // Close all connections
    for (final connection in _connections.values) {
      await connection.close();
    }
    _connections.clear();

    _shares.clear();
    _transfers.clear();
    _eventController.close();
  }
}

// Additional models for network sharing
class DiscoveredDevice {
  final String name;
  final String ipAddress;
  final int port;
  final String deviceType;
  final bool isOnline;
  final DateTime lastSeen;

  const DiscoveredDevice({
    required this.name,
    required this.ipAddress,
    required this.port,
    required this.deviceType,
    required this.isOnline,
    required this.lastSeen,
  });

  // Helper methods for FTP server functionality
  static Future<void> _sendFileList(
    Socket socket,
    List<String> sharedPaths,
  ) async {
    try {
      for (final path in sharedPaths) {
        final directory = Directory(path);
        if (await directory.exists()) {
          await for (final entity in directory.list()) {
            final stat = await entity.stat();
            final name = entity.path.split('/').last;
            final size = stat.size;
            final modified = stat.modified;

            // Send file listing in FTP format
            final listing =
                '-rw-r--r-- 1 <USER> <GROUP> $size ${_formatDate(modified)} $name\r\n';
            socket.write(listing);
          }
        }
      }
    } catch (e) {
      socket.write('550 Failed to list files: $e\r\n');
    }
  }

  static Future<void> _handleFileDownload(
    Socket socket,
    String fileName,
    List<String> sharedPaths,
  ) async {
    try {
      File? targetFile;

      // Find file in shared paths
      for (final path in sharedPaths) {
        final file = File('$path/$fileName');
        if (await file.exists()) {
          targetFile = file;
          break;
        }
      }

      if (targetFile == null) {
        socket.write('550 File not found\r\n');
        return;
      }

      socket.write('150 Opening data connection for file transfer\r\n');

      // Send file content
      final bytes = await targetFile.readAsBytes();
      socket.add(bytes);

      socket.write('226 Transfer complete\r\n');
    } catch (e) {
      socket.write('550 Transfer failed: $e\r\n');
    }
  }

  static Future<void> _handleFTPFileUpload(
    Socket socket,
    String fileName,
    List<String> sharedPaths,
  ) async {
    try {
      if (sharedPaths.isEmpty) {
        socket.write('550 No upload directory configured\r\n');
        return;
      }

      socket.write('150 Ready for file upload\r\n');

      // This is a simplified implementation
      // In a real FTP server, you would handle the data connection separately
      // File would be saved to: ${sharedPaths.first}/$fileName
      socket.write('226 Upload complete\r\n');
    } catch (e) {
      socket.write('550 Upload failed: $e\r\n');
    }
  }

  static String _formatDate(DateTime date) {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return '${months[date.month - 1]} ${date.day.toString().padLeft(2)} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }
}

class NetworkSharingEvent {
  final NetworkEventType type;
  final String? shareId;
  final String? transferId;
  final String message;
  final DateTime timestamp;

  const NetworkSharingEvent({
    required this.type,
    this.shareId,
    this.transferId,
    required this.message,
    required this.timestamp,
  });
}

enum NetworkEventType {
  shareStarted,
  shareStopped,
  deviceDiscovered,
  transferStarted,
  transferCompleted,
  transferFailed,
}
