import 'dart:async';
import '../models/task_models.dart';
import '../models/ai_todo_models.dart';

/// Advanced Task Automation Service with Smart Rules and AI-Powered Actions
class TaskAutomationService {
  static final TaskAutomationService _instance =
      TaskAutomationService._internal();
  factory TaskAutomationService() => _instance;
  TaskAutomationService._internal();

  final List<TaskAutomationRule> _rules = [];
  final StreamController<AutomationEvent> _eventController =
      StreamController.broadcast();
  Stream<AutomationEvent> get eventStream => _eventController.stream;

  Timer? _automationTimer;
  bool _isRunning = false;

  // Automation Rule Management (100 features)
  void startAutomation() {
    if (_isRunning) return;

    _isRunning = true;
    _automationTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _processAutomationRules();
    });
  }

  void stopAutomation() {
    _isRunning = false;
    _automationTimer?.cancel();
    _automationTimer = null;
  }

  String addRule(TaskAutomationRule rule) {
    _rules.add(rule);
    _eventController.add(
      AutomationEvent(
        id: 'rule_added_${DateTime.now().millisecondsSinceEpoch}',
        type: AutomationEventType.ruleAdded,
        message: 'Automation rule "${rule.name}" added successfully',
        timestamp: DateTime.now(),
        ruleId: rule.id,
      ),
    );
    return rule.id;
  }

  void removeRule(String ruleId) {
    final removedRule = _rules.where((r) => r.id == ruleId).firstOrNull;
    _rules.removeWhere((r) => r.id == ruleId);

    if (removedRule != null) {
      _eventController.add(
        AutomationEvent(
          id: 'rule_removed_${DateTime.now().millisecondsSinceEpoch}',
          type: AutomationEventType.ruleRemoved,
          message: 'Automation rule "${removedRule.name}" removed',
          timestamp: DateTime.now(),
          ruleId: ruleId,
        ),
      );
    }
  }

  void updateRule(TaskAutomationRule updatedRule) {
    final index = _rules.indexWhere((r) => r.id == updatedRule.id);
    if (index != -1) {
      _rules[index] = updatedRule;
      _eventController.add(
        AutomationEvent(
          id: 'rule_updated_${DateTime.now().millisecondsSinceEpoch}',
          type: AutomationEventType.ruleUpdated,
          message: 'Automation rule "${updatedRule.name}" updated',
          timestamp: DateTime.now(),
          ruleId: updatedRule.id,
        ),
      );
    }
  }

  List<TaskAutomationRule> getRules() => List.unmodifiable(_rules);

  TaskAutomationRule? getRule(String ruleId) {
    return _rules.where((r) => r.id == ruleId).firstOrNull;
  }

  // Smart Task Creation (100 features)
  Future<List<Task>> generateSmartTasks(
    List<Task> existingTasks,
    UserPreferences preferences,
  ) async {
    final smartTasks = <Task>[];

    // Generate recurring tasks
    smartTasks.addAll(await _generateRecurringTasks(existingTasks));

    // Generate follow-up tasks
    smartTasks.addAll(await _generateFollowUpTasks(existingTasks));

    // Generate habit-based tasks
    smartTasks.addAll(await _generateHabitTasks(existingTasks, preferences));

    // Generate deadline-driven tasks
    smartTasks.addAll(await _generateDeadlineTasks(existingTasks));

    // Generate context-based tasks
    smartTasks.addAll(await _generateContextTasks(existingTasks, preferences));

    return smartTasks;
  }

  Future<List<Task>> _generateRecurringTasks(List<Task> existingTasks) async {
    final recurringTasks = <Task>[];
    final now = DateTime.now();

    // Analyze patterns for daily tasks
    final dailyPatterns = _findDailyPatterns(existingTasks);
    for (final pattern in dailyPatterns) {
      if (_shouldCreateRecurringTask(pattern, now)) {
        recurringTasks.add(
          Task(
            id: 'recurring_daily_${now.millisecondsSinceEpoch}_${pattern.hashCode}',
            title: pattern.title,
            description: 'Auto-generated recurring task based on your patterns',
            category: TaskCategory.work,
            priority: pattern.priority,
            dueDate: DateTime(now.year, now.month, now.day, 23, 59),
            createdAt: now,
            updatedAt: now,
            tags: [...pattern.tags, 'auto-generated', 'recurring'],
            estimatedMinutes: pattern.estimatedDuration?.inMinutes ?? 0,
          ),
        );
      }
    }

    // Analyze patterns for weekly tasks
    final weeklyPatterns = _findWeeklyPatterns(existingTasks);
    for (final pattern in weeklyPatterns) {
      if (_shouldCreateWeeklyTask(pattern, now)) {
        recurringTasks.add(
          Task(
            id: 'recurring_weekly_${now.millisecondsSinceEpoch}_${pattern.hashCode}',
            title: pattern.title,
            description: 'Auto-generated weekly task based on your patterns',
            category: TaskCategory.work,
            priority: pattern.priority,
            dueDate: _getNextWeeklyDueDate(pattern, now),
            createdAt: now,
            updatedAt: now,
            tags: [...pattern.tags, 'auto-generated', 'weekly'],
            estimatedMinutes: pattern.estimatedDuration?.inMinutes ?? 0,
          ),
        );
      }
    }

    return recurringTasks;
  }

  Future<List<Task>> _generateFollowUpTasks(List<Task> existingTasks) async {
    final followUpTasks = <Task>[];
    final now = DateTime.now();

    // Find recently completed tasks that might need follow-up
    final recentlyCompleted = existingTasks
        .where(
          (t) =>
              t.isCompleted &&
              t.completedAt != null &&
              now.difference(t.completedAt!).inDays <= 7,
        )
        .toList();

    for (final task in recentlyCompleted) {
      final followUps = _generateFollowUpForTask(task, now);
      followUpTasks.addAll(followUps);
    }

    return followUpTasks;
  }

  List<Task> _generateFollowUpForTask(Task completedTask, DateTime now) {
    final followUps = <Task>[];

    // Meeting follow-ups
    if (completedTask.category.toString().toLowerCase().contains('meeting')) {
      followUps.add(
        Task(
          id: 'followup_meeting_${now.millisecondsSinceEpoch}',
          title: 'Follow up on ${completedTask.title}',
          description: 'Send meeting notes and action items',
          category: TaskCategory.work,
          priority: TaskPriority.medium,
          dueDate: now.add(const Duration(days: 1)),
          createdAt: now,
          updatedAt: now,
          tags: ['auto-generated', 'follow-up', 'meeting'],
          estimatedMinutes: 30,
        ),
      );
    }

    // Project follow-ups
    if (completedTask.category.toString().toLowerCase().contains('project')) {
      followUps.add(
        Task(
          id: 'followup_project_${now.millisecondsSinceEpoch}',
          title: 'Review progress on ${completedTask.title}',
          description: 'Check project status and next steps',
          category: TaskCategory.work,
          priority: TaskPriority.medium,
          dueDate: now.add(const Duration(days: 3)),
          createdAt: now,
          updatedAt: now,
          tags: ['auto-generated', 'follow-up', 'project'],
          estimatedMinutes: 45,
        ),
      );
    }

    // Learning follow-ups
    if (completedTask.category.toString().toLowerCase().contains('learning') ||
        completedTask.category.toString().toLowerCase().contains('study')) {
      followUps.add(
        Task(
          id: 'followup_learning_${now.millisecondsSinceEpoch}',
          title: 'Practice ${completedTask.title}',
          description: 'Apply what you learned',
          category: TaskCategory.education,
          priority: TaskPriority.low,
          dueDate: now.add(const Duration(days: 2)),
          createdAt: now,
          updatedAt: now,
          tags: ['auto-generated', 'follow-up', 'practice'],
          estimatedMinutes: 60,
        ),
      );
    }

    return followUps;
  }

  Future<List<Task>> _generateHabitTasks(
    List<Task> existingTasks,
    UserPreferences preferences,
  ) async {
    final habitTasks = <Task>[];
    final now = DateTime.now();

    // Analyze habit patterns
    final habitPatterns = _analyzeHabitPatterns(existingTasks);

    for (final habit in habitPatterns) {
      if (_shouldCreateHabitTask(habit, now)) {
        habitTasks.add(
          Task(
            id: 'habit_${now.millisecondsSinceEpoch}_${habit.hashCode}',
            title: habit.title,
            description: 'Daily habit task - ${habit.description}',
            category: TaskCategory.personal,
            priority: TaskPriority.low,
            dueDate: DateTime(now.year, now.month, now.day, 23, 59),
            createdAt: now,
            updatedAt: now,
            tags: ['auto-generated', 'habit', 'daily'],
            estimatedMinutes: habit.estimatedDuration.inMinutes,
          ),
        );
      }
    }

    return habitTasks;
  }

  Future<List<Task>> _generateDeadlineTasks(List<Task> existingTasks) async {
    final deadlineTasks = <Task>[];
    final now = DateTime.now();

    // Find tasks with approaching deadlines that might need preparation
    final upcomingDeadlines = existingTasks
        .where(
          (t) =>
              !t.isCompleted &&
              t.dueDate != null &&
              t.dueDate!.difference(now).inDays <= 7 &&
              t.dueDate!.difference(now).inDays > 0,
        )
        .toList();

    for (final task in upcomingDeadlines) {
      final prepTasks = _generatePreparationTasks(task, now);
      deadlineTasks.addAll(prepTasks);
    }

    return deadlineTasks;
  }

  List<Task> _generatePreparationTasks(Task upcomingTask, DateTime now) {
    final prepTasks = <Task>[];

    // Generate preparation task 2 days before deadline
    final prepDate = upcomingTask.dueDate!.subtract(const Duration(days: 2));
    if (prepDate.isAfter(now)) {
      prepTasks.add(
        Task(
          id: 'prep_${upcomingTask.id}_${now.millisecondsSinceEpoch}',
          title: 'Prepare for ${upcomingTask.title}',
          description: 'Gather materials and review requirements',
          category: TaskCategory.work,
          priority: TaskPriority.medium,
          dueDate: prepDate,
          createdAt: now,
          updatedAt: now,
          tags: ['auto-generated', 'preparation'],
          estimatedMinutes: 30,
        ),
      );
    }

    // Generate reminder task 1 day before deadline
    final reminderDate = upcomingTask.dueDate!.subtract(
      const Duration(days: 1),
    );
    if (reminderDate.isAfter(now)) {
      prepTasks.add(
        Task(
          id: 'reminder_${upcomingTask.id}_${now.millisecondsSinceEpoch}',
          title: 'Final check for ${upcomingTask.title}',
          description: 'Last-minute review and preparation',
          category: TaskCategory.work,
          priority: TaskPriority.high,
          dueDate: reminderDate,
          createdAt: now,
          updatedAt: now,
          tags: ['auto-generated', 'reminder'],
          estimatedMinutes: 15,
        ),
      );
    }

    return prepTasks;
  }

  Future<List<Task>> _generateContextTasks(
    List<Task> existingTasks,
    UserPreferences preferences,
  ) async {
    final contextTasks = <Task>[];
    final now = DateTime.now();

    // Generate location-based tasks
    if (preferences.currentLocation == 'office') {
      contextTasks.addAll(_generateOfficeTasks(existingTasks, now));
    } else if (preferences.currentLocation == 'home') {
      contextTasks.addAll(_generateHomeTasks(existingTasks, now));
    }

    // Generate time-based tasks
    if (now.hour >= 9 && now.hour <= 17) {
      contextTasks.addAll(_generateWorkHourTasks(existingTasks, now));
    } else {
      contextTasks.addAll(_generatePersonalTimeTasks(existingTasks, now));
    }

    return contextTasks;
  }

  List<Task> _generateOfficeTasks(List<Task> existingTasks, DateTime now) {
    final officeTasks = <Task>[];

    // Check if there are pending work-related tasks
    final workTasks = existingTasks
        .where(
          (t) =>
              !t.isCompleted &&
              (t.category.toString().toLowerCase().contains('work') ||
                  t.category.toString().toLowerCase().contains('office') ||
                  t.category.toString().toLowerCase().contains('meeting')),
        )
        .toList();

    if (workTasks.isEmpty) {
      officeTasks.add(
        Task(
          id: 'office_context_${now.millisecondsSinceEpoch}',
          title: 'Review pending work items',
          description: 'Check emails and update project status',
          category: TaskCategory.work,
          priority: TaskPriority.medium,
          dueDate: now.add(const Duration(hours: 2)),
          createdAt: now,
          updatedAt: now,
          tags: ['auto-generated', 'office', 'context'],
          estimatedMinutes: 30,
        ),
      );
    }

    return officeTasks;
  }

  List<Task> _generateHomeTasks(List<Task> existingTasks, DateTime now) {
    final homeTasks = <Task>[];

    // Check if there are pending personal tasks
    final personalTasks = existingTasks
        .where(
          (t) =>
              !t.isCompleted &&
              (t.category.toString().toLowerCase().contains('personal') ||
                  t.category.toString().toLowerCase().contains('home') ||
                  t.category.toString().toLowerCase().contains('family')),
        )
        .toList();

    if (personalTasks.isEmpty && now.hour >= 18) {
      homeTasks.add(
        Task(
          id: 'home_context_${now.millisecondsSinceEpoch}',
          title: 'Plan tomorrow\'s activities',
          description: 'Review schedule and prepare for tomorrow',
          category: TaskCategory.personal,
          priority: TaskPriority.low,
          dueDate: DateTime(now.year, now.month, now.day, 22, 0),
          createdAt: now,
          updatedAt: now,
          tags: ['auto-generated', 'home', 'planning'],
          estimatedMinutes: 20,
        ),
      );
    }

    return homeTasks;
  }

  List<Task> _generateWorkHourTasks(List<Task> existingTasks, DateTime now) {
    final workTasks = <Task>[];

    // Generate focus session task if no high-priority tasks exist
    final highPriorityTasks = existingTasks
        .where((t) => !t.isCompleted && t.priority == TaskPriority.high)
        .toList();

    if (highPriorityTasks.isEmpty) {
      workTasks.add(
        Task(
          id: 'focus_session_${now.millisecondsSinceEpoch}',
          title: 'Deep work focus session',
          description: 'Dedicated time for important work',
          category: TaskCategory.work,
          priority: TaskPriority.medium,
          dueDate: now.add(const Duration(hours: 2)),
          createdAt: now,
          updatedAt: now,
          tags: ['auto-generated', 'focus', 'deep-work'],
          estimatedMinutes: 90,
        ),
      );
    }

    return workTasks;
  }

  List<Task> _generatePersonalTimeTasks(
    List<Task> existingTasks,
    DateTime now,
  ) {
    final personalTasks = <Task>[];

    // Generate reflection task in the evening
    if (now.hour >= 20 && now.hour <= 22) {
      personalTasks.add(
        Task(
          id: 'reflection_${now.millisecondsSinceEpoch}',
          title: 'Daily reflection',
          description:
              'Reflect on today\'s accomplishments and plan improvements',
          category: TaskCategory.personal,
          priority: TaskPriority.low,
          dueDate: DateTime(now.year, now.month, now.day, 23, 0),
          createdAt: now,
          updatedAt: now,
          tags: ['auto-generated', 'reflection', 'personal'],
          estimatedMinutes: 15,
        ),
      );
    }

    return personalTasks;
  }

  // Smart Task Prioritization (100 features)
  Future<List<Task>> reprioritizeTasks(
    List<Task> tasks,
    UserPreferences preferences,
  ) async {
    final prioritizedTasks = <Task>[];

    for (final task in tasks) {
      final newPriority = await _calculateSmartPriority(
        task,
        tasks,
        preferences,
      );
      prioritizedTasks.add(task.copyWith(priority: newPriority));
    }

    return prioritizedTasks;
  }

  Future<TaskPriority> _calculateSmartPriority(
    Task task,
    List<Task> allTasks,
    UserPreferences preferences,
  ) async {
    double priorityScore = 0.0;

    // Deadline urgency (40% weight)
    if (task.dueDate != null) {
      final daysUntilDue = task.dueDate!.difference(DateTime.now()).inDays;
      if (daysUntilDue <= 1) {
        priorityScore += 0.4;
      } else if (daysUntilDue <= 3) {
        priorityScore += 0.3;
      } else if (daysUntilDue <= 7) {
        priorityScore += 0.2;
      } else {
        priorityScore += 0.1;
      }
    }

    // Category importance (30% weight)
    final categoryPreference =
        preferences.categoryPreferences[task.category.toString()] ?? 0.5;
    priorityScore += categoryPreference * 0.3;

    // Current priority (20% weight)
    switch (task.priority) {
      case TaskPriority.urgent:
        priorityScore += 0.25;
        break;
      case TaskPriority.high:
        priorityScore += 0.2;
        break;
      case TaskPriority.medium:
        priorityScore += 0.1;
        break;
      case TaskPriority.low:
        priorityScore += 0.05;
        break;
    }

    // Task age (10% weight)
    final daysSinceCreated = DateTime.now().difference(task.createdAt).inDays;
    if (daysSinceCreated >= 7) {
      priorityScore += 0.1;
    } else if (daysSinceCreated >= 3) {
      priorityScore += 0.05;
    }

    // Convert score to priority
    if (priorityScore >= 0.7) {
      return TaskPriority.high;
    } else if (priorityScore >= 0.4) {
      return TaskPriority.medium;
    } else {
      return TaskPriority.low;
    }
  }

  // Utility Methods
  void _processAutomationRules() {
    for (final rule in _rules) {
      if (rule.isEnabled && _shouldExecuteRule(rule)) {
        _executeRule(rule);
      }
    }
  }

  bool _shouldExecuteRule(TaskAutomationRule rule) {
    final now = DateTime.now();

    switch (rule.schedule?.frequency ?? ScheduleFrequency.daily) {
      case ScheduleFrequency.daily:
        return rule.lastExecuted == null ||
            now.difference(rule.lastExecuted!).inDays >= 1;
      case ScheduleFrequency.weekly:
        return rule.lastExecuted == null ||
            now.difference(rule.lastExecuted!).inDays >= 7;
      case ScheduleFrequency.monthly:
        return rule.lastExecuted == null ||
            now.difference(rule.lastExecuted!).inDays >= 30;
      case ScheduleFrequency.yearly:
        return rule.lastExecuted == null ||
            now.difference(rule.lastExecuted!).inDays >= 365;
    }
  }

  Future<void> _executeRule(TaskAutomationRule rule) async {
    try {
      switch (rule.type) {
        case TaskAutomationRuleType.recurring:
          await _executeRecurringRule(rule);
          break;
        case TaskAutomationRuleType.conditional:
          await _executeConditionalRule(rule);
          break;
        case TaskAutomationRuleType.scheduled:
          await _executeScheduledRule(rule);
          break;
        case TaskAutomationRuleType.trigger:
          await _executeTriggerRule(rule);
          break;
      }

      // Note: lastExecuted would be updated in a real implementation
      // by creating a new rule instance with copyWith
    } catch (e) {
      _eventController.add(
        AutomationEvent(
          id: 'rule_error_${DateTime.now().millisecondsSinceEpoch}',
          type: AutomationEventType.ruleExecuted,
          message: 'Error executing rule "${rule.name}": $e',
          timestamp: DateTime.now(),
          ruleId: rule.id,
        ),
      );
    }
  }

  Future<void> _executeRecurringRule(TaskAutomationRule rule) async {
    // Implementation for recurring task creation
  }

  Future<void> _executeConditionalRule(TaskAutomationRule rule) async {
    // Implementation for conditional task automation
  }

  Future<void> _executeScheduledRule(TaskAutomationRule rule) async {
    // Implementation for scheduled task automation
  }

  Future<void> _executeTriggerRule(TaskAutomationRule rule) async {
    // Implementation for trigger-based automation
  }

  List<TaskPattern> _findDailyPatterns(List<Task> tasks) {
    // Simplified pattern detection
    final patterns = <TaskPattern>[];
    final taskGroups = <String, List<Task>>{};

    // Group similar tasks
    for (final task in tasks) {
      final key = '${task.category}_${task.title.toLowerCase()}';
      taskGroups[key] = (taskGroups[key] ?? [])..add(task);
    }

    // Find patterns that occur frequently
    taskGroups.forEach((key, groupTasks) {
      if (groupTasks.length >= 3) {
        patterns.add(
          TaskPattern(
            title: groupTasks.first.title,
            category: groupTasks.first.category.toString(),
            priority: groupTasks.first.priority,
            tags: groupTasks.first.tags,
            estimatedDuration: groupTasks.first.estimatedDuration,
            frequency: PatternFrequency.daily,
          ),
        );
      }
    });

    return patterns;
  }

  List<TaskPattern> _findWeeklyPatterns(List<Task> tasks) {
    // Similar to daily patterns but for weekly occurrences
    return _findDailyPatterns(
      tasks,
    ).map((p) => p.copyWith(frequency: PatternFrequency.weekly)).toList();
  }

  bool _shouldCreateRecurringTask(TaskPattern pattern, DateTime now) {
    // Check if a similar task doesn't already exist for today
    return true; // Simplified logic
  }

  bool _shouldCreateWeeklyTask(TaskPattern pattern, DateTime now) {
    // Check if a similar task doesn't already exist for this week
    return now.weekday == 1; // Create on Mondays
  }

  DateTime _getNextWeeklyDueDate(TaskPattern pattern, DateTime now) {
    // Calculate next week's due date
    return now.add(Duration(days: 7 - now.weekday + 1));
  }

  List<HabitPattern> _analyzeHabitPatterns(List<Task> tasks) {
    // Analyze tasks to identify habit patterns
    final habits = <HabitPattern>[];

    // Find tasks that occur regularly
    final habitTasks = tasks
        .where(
          (t) =>
              t.tags.contains('habit') ||
              t.category.toString().toLowerCase().contains('habit') ||
              t.category.toString().toLowerCase().contains('routine'),
        )
        .toList();

    for (final task in habitTasks) {
      habits.add(
        HabitPattern(
          title: task.title,
          description: task.description,
          estimatedDuration:
              task.estimatedDuration ?? const Duration(minutes: 15),
          frequency: HabitFrequency.daily,
        ),
      );
    }

    return habits;
  }

  bool _shouldCreateHabitTask(HabitPattern habit, DateTime now) {
    // Check if habit task should be created today
    return true; // Simplified logic
  }

  void dispose() {
    stopAutomation();
    _eventController.close();
  }
}
