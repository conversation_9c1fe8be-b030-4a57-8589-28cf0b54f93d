import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Settings state providers
final autoSaveProvider = StateProvider<bool>((ref) => true);
final defaultTemplateProvider = StateProvider<String>((ref) => 'Calculator');
final gridSizeProvider = StateProvider<String>((ref) => '10x10');
final themeProvider = StateProvider<String>((ref) => 'Light');
final formulaEngineProvider = StateProvider<String>((ref) => 'Basic');
final backupFrequencyProvider = StateProvider<String>((ref) => 'Daily');

class ToolsBuilderSettingsScreen extends ConsumerWidget {
  const ToolsBuilderSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSettingsSection(context, 'General', [
            _buildSettingsTile(
              context,
              'Auto-save',
              'Automatically save changes',
              Icons.save,
              trailing: Switch(
                value: ref.watch(autoSaveProvider),
                onChanged: (value) {
                  ref.read(autoSaveProvider.notifier).state = value;
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'Auto-save ${value ? 'enabled' : 'disabled'}',
                      ),
                    ),
                  );
                },
              ),
            ),
            _buildSettingsTile(
              context,
              'Default Template',
              'Choose default template for new tools',
              Icons.description,
              onTap: () => _showTemplateDialog(context, ref),
            ),
            _buildSettingsTile(
              context,
              'Grid Size',
              'Default spreadsheet grid size',
              Icons.grid_on,
              onTap: () => _showGridSizeDialog(context, ref),
            ),
          ]),
          const SizedBox(height: 24),
          _buildSettingsSection(context, 'Editor', [
            _buildSettingsTile(
              context,
              'Formula Bar',
              'Show formula bar in editor',
              Icons.functions,
              trailing: Switch(value: true, onChanged: (value) {}),
            ),
            _buildSettingsTile(
              context,
              'Syntax Highlighting',
              'Enable formula syntax highlighting',
              Icons.highlight,
              trailing: Switch(value: true, onChanged: (value) {}),
            ),
            _buildSettingsTile(
              context,
              'Auto-complete',
              'Enable formula auto-completion',
              Icons.auto_fix_high,
              trailing: Switch(value: true, onChanged: (value) {}),
            ),
          ]),
          const SizedBox(height: 24),
          _buildSettingsSection(context, 'Export & Import', [
            _buildSettingsTile(
              context,
              'Default Export Format',
              'Excel (.xlsx)',
              Icons.file_download,
              onTap: () {},
            ),
            _buildSettingsTile(
              context,
              'Import Settings',
              'Configure import preferences',
              Icons.file_upload,
              onTap: () {},
            ),
            _buildSettingsTile(
              context,
              'Backup Tools',
              'Backup all tools to cloud',
              Icons.cloud_upload,
              onTap: () {},
            ),
          ]),
          const SizedBox(height: 24),
          _buildSettingsSection(context, 'Performance', [
            _buildSettingsTile(
              context,
              'Calculation Mode',
              'Automatic',
              Icons.calculate,
              onTap: () {},
            ),
            _buildSettingsTile(
              context,
              'Memory Usage',
              'Optimize for large spreadsheets',
              Icons.memory,
              trailing: Switch(value: false, onChanged: (value) {}),
            ),
            _buildSettingsTile(
              context,
              'Clear Cache',
              'Clear temporary files and cache',
              Icons.clear_all,
              onTap: () => _showClearCacheDialog(context),
            ),
          ]),
          const SizedBox(height: 24),
          _buildSettingsSection(context, 'About', [
            _buildSettingsTile(context, 'Version', '1.0.0', Icons.info),
            _buildSettingsTile(
              context,
              'Help & Support',
              'Get help and documentation',
              Icons.help,
              onTap: () {},
            ),
            _buildSettingsTile(
              context,
              'Privacy Policy',
              'View privacy policy',
              Icons.privacy_tip,
              onTap: () {},
            ),
          ]),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(
    BuildContext context,
    String title,
    List<Widget> children,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.orange,
          ),
        ),
        const SizedBox(height: 8),
        Card(elevation: 2, child: Column(children: children)),
      ],
    );
  }

  Widget _buildSettingsTile(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon, {
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: Colors.orange),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing:
          trailing ?? (onTap != null ? const Icon(Icons.chevron_right) : null),
      onTap: onTap,
    );
  }

  void _showClearCacheDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cache'),
        content: const Text(
          'This will clear all temporary files and cached data. Your tools and spreadsheets will not be affected.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Cache cleared successfully')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _showTemplateDialog(BuildContext context, WidgetRef ref) {
    final templates = [
      'Calculator',
      'Form Builder',
      'Data Tracker',
      'Survey Tool',
      'Budget Planner',
    ];
    final currentTemplate = ref.read(defaultTemplateProvider);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Default Template'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: templates.map((template) {
            return RadioListTile<String>(
              title: Text(template),
              value: template,
              groupValue: currentTemplate,
              onChanged: (value) {
                if (value != null) {
                  ref.read(defaultTemplateProvider.notifier).state = value;
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Default template set to $value')),
                  );
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showGridSizeDialog(BuildContext context, WidgetRef ref) {
    final gridSizes = ['5x5', '10x10', '15x15', '20x20', '25x25'];
    final currentSize = ref.read(gridSizeProvider);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Default Grid Size'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: gridSizes.map((size) {
            return RadioListTile<String>(
              title: Text(size),
              value: size,
              groupValue: currentSize,
              onChanged: (value) {
                if (value != null) {
                  ref.read(gridSizeProvider.notifier).state = value;
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Default grid size set to $value')),
                  );
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}
