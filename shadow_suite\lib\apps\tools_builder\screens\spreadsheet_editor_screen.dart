import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/spreadsheet.dart';
import '../models/spreadsheet_cell.dart';
import '../services/tools_builder_providers.dart';
import '../services/formula_engine.dart';
import '../widgets/enhanced_spreadsheet_editor.dart';
import '../widgets/enhanced_formula_bar.dart';

/// Screen for editing spreadsheets
class SpreadsheetEditorScreen extends ConsumerStatefulWidget {
  final String spreadsheetName;
  final int rows;
  final int columns;
  final Spreadsheet? existingSpreadsheet;

  const SpreadsheetEditorScreen({
    super.key,
    required this.spreadsheetName,
    this.rows = 100,
    this.columns = 26,
    this.existingSpreadsheet,
  });

  @override
  ConsumerState<SpreadsheetEditorScreen> createState() =>
      _SpreadsheetEditorScreenState();
}

class _SpreadsheetEditorScreenState
    extends ConsumerState<SpreadsheetEditorScreen> {
  late Spreadsheet _spreadsheet;
  late FormulaEngine _formulaEngine;
  String? _selectedCellAddress;

  @override
  void initState() {
    super.initState();
    _formulaEngine = FormulaEngine();
    _initializeSpreadsheet();
  }

  void _initializeSpreadsheet() {
    if (widget.existingSpreadsheet != null) {
      _spreadsheet = widget.existingSpreadsheet!;
    } else {
      // Create a new spreadsheet
      _spreadsheet = Spreadsheet(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: widget.spreadsheetName,
        description: '',
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
        sheets: [
          SpreadsheetSheet(
            id: 'sheet_1',
            name: 'Sheet1',
            cells: {},
            rows: widget.rows,
            columns: widget.columns,
          ),
        ],
      );
    }

    // Load the spreadsheet into the provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(spreadsheetCalculationProvider.notifier)
          .setSpreadsheet(_spreadsheet);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.spreadsheetName),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveSpreadsheet,
            tooltip: 'Save',
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareSpreadsheet,
            tooltip: 'Share',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('Export'),
                ),
              ),
              const PopupMenuItem(
                value: 'print',
                child: ListTile(
                  leading: Icon(Icons.print),
                  title: Text('Print'),
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: ListTile(
                  leading: Icon(Icons.settings),
                  title: Text('Settings'),
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Formula bar
          EnhancedFormulaBar(onFormulaChanged: _onFormulaChanged),
          const Divider(height: 1),

          // Spreadsheet editor
          Expanded(
            child: EnhancedSpreadsheetEditor(
              spreadsheet: _spreadsheet,
              onSpreadsheetChanged: _onSpreadsheetChanged,
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          // Sheet tabs
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _spreadsheet.sheets.length,
              itemBuilder: (context, index) {
                final sheet = _spreadsheet.sheets[index];
                final isActive =
                    index == 0; // For now, always show first sheet as active

                return Container(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 2,
                    vertical: 4,
                  ),
                  child: Material(
                    color: isActive
                        ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(4),
                    child: InkWell(
                      onTap: () => _selectSheet(index),
                      borderRadius: BorderRadius.circular(4),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        child: Text(
                          sheet.name,
                          style: TextStyle(
                            fontWeight: isActive
                                ? FontWeight.bold
                                : FontWeight.normal,
                            color: isActive
                                ? Theme.of(context).primaryColor
                                : null,
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          // Add sheet button
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addSheet,
            tooltip: 'Add Sheet',
          ),
        ],
      ),
    );
  }

  void _onFormulaChanged(String formula) {
    if (_selectedCellAddress == null) return;

    // Parse cell address (e.g., "A1" -> row: 0, col: 0)
    final cellRef = _parseCellAddress(_selectedCellAddress!);
    if (cellRef == null) return;

    final sheet = _spreadsheet.activeSheet;
    if (sheet == null) return;

    // Apply formula to the selected cell
    final row = cellRef['row']!;
    final col = cellRef['col']!;
    final cell =
        sheet.getCell(row, col) ??
        SpreadsheetCell(row: row, column: col, rawValue: '');

    // Evaluate the formula using the formula engine
    final result = _formulaEngine.evaluateFormula(
      formula,
      _spreadsheet,
      _selectedCellAddress,
    );

    // Update the cell with the formula and result
    final updatedCell = cell.copyWith(
      rawValue: formula,
      calculatedValue: result,
      dataType: CellDataType.formula,
    );

    // Update the sheet with the new cell
    final updatedSheet = sheet.copyWith(
      cells: Map.from(sheet.cells)..[_getCellKey(row, col)] = updatedCell,
    );

    // Update the spreadsheet
    final updatedSheets = List<SpreadsheetSheet>.from(_spreadsheet.sheets);
    updatedSheets[_spreadsheet.activeSheetIndex] = updatedSheet;

    setState(() {
      _spreadsheet = _spreadsheet.copyWith(sheets: updatedSheets);
    });
  }

  Map<String, int>? _parseCellAddress(String address) {
    final match = RegExp(r'^([A-Z]+)(\d+)$').firstMatch(address);
    if (match == null) return null;

    final column = match.group(1)!;
    final row = int.parse(match.group(2)!) - 1; // Convert to 0-based
    final colIndex = _columnToIndex(column);

    return {'row': row, 'col': colIndex};
  }

  int _columnToIndex(String column) {
    int result = 0;
    for (int i = 0; i < column.length; i++) {
      result = result * 26 + (column.codeUnitAt(i) - 'A'.codeUnitAt(0) + 1);
    }
    return result - 1;
  }

  String _getCellKey(int row, int col) {
    return '${row}_$col';
  }

  void _onSpreadsheetChanged(Spreadsheet updatedSpreadsheet) {
    setState(() {
      _spreadsheet = updatedSpreadsheet;
    });

    // Update the provider
    ref
        .read(spreadsheetCalculationProvider.notifier)
        .setSpreadsheet(_spreadsheet);
  }

  void _selectSheet(int index) {
    // TODO: Implement sheet selection
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Selected sheet: ${_spreadsheet.sheets[index].name}'),
      ),
    );
  }

  void _addSheet() {
    final newSheet = SpreadsheetSheet(
      id: 'sheet_${_spreadsheet.sheets.length + 1}',
      name: 'Sheet${_spreadsheet.sheets.length + 1}',
      cells: {},
      rows: 100,
      columns: 26,
    );

    setState(() {
      _spreadsheet = _spreadsheet.copyWith(
        sheets: [..._spreadsheet.sheets, newSheet],
        lastModified: DateTime.now(),
      );
    });

    _onSpreadsheetChanged(_spreadsheet);
  }

  void _saveSpreadsheet() async {
    try {
      // Update the spreadsheet's last modified time
      final updatedSpreadsheet = _spreadsheet.copyWith(
        lastModified: DateTime.now(),
      );

      // Save to the tools builder provider (simplified for now)
      // In a real implementation, this would save to database
      print('Saving spreadsheet: ${updatedSpreadsheet.name}');

      setState(() {
        _spreadsheet = updatedSpreadsheet;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Spreadsheet saved successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to save spreadsheet: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _shareSpreadsheet() {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share functionality coming soon')),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportSpreadsheet();
        break;
      case 'print':
        _printSpreadsheet();
        break;
      case 'settings':
        _showSettings();
        break;
    }
  }

  void _exportSpreadsheet() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Spreadsheet'),
        content: const Text('Choose export format:'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Exported as Excel file')),
              );
            },
            child: const Text('Excel (.xlsx)'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Exported as CSV file')),
              );
            },
            child: const Text('CSV'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _printSpreadsheet() {
    // TODO: Implement print functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Print functionality coming soon')),
    );
  }

  void _showSettings() {
    // TODO: Implement settings dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Settings dialog coming soon')),
    );
  }
}
