import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import '../../../core/database/database_service.dart';
import '../models/voice_recording_models.dart';

/// Voice Recorder Service with real audio functionality
class VoiceRecorderService {
  static final VoiceRecorderService _instance =
      VoiceRecorderService._internal();
  factory VoiceRecorderService() => _instance;
  VoiceRecorderService._internal();

  // In-memory storage with database persistence
  final List<VoiceRecording> _recordings = [];
  RecordingSession? _currentSession;
  PlaybackSession? _currentPlayback;
  VoiceRecorderSettings _settings = const VoiceRecorderSettings();

  final ValueNotifier<int> _stateNotifier = ValueNotifier<int>(0);
  final ValueNotifier<RecordingSession?> _sessionNotifier =
      ValueNotifier<RecordingSession?>(null);
  final ValueNotifier<PlaybackSession?> _playbackNotifier =
      ValueNotifier<PlaybackSession?>(null);

  // Database instance
  Database? _database;

  // Timers for recording and playback
  Timer? _recordingTimer;
  Timer? _playbackTimer;

  /// Initialize voice recorder service
  Future<void> initialize() async {
    await _initializeDatabase();
    await _loadRecordingsFromDatabase();
    await _loadSettingsFromDatabase();
    if (_recordings.isEmpty) {
      _loadSampleRecordings();
    }
  }

  Future<void> _initializeDatabase() async {
    _database = await DatabaseService.database;

    // Create voice_recordings table
    await _database!.execute('''
      CREATE TABLE IF NOT EXISTS voice_recordings (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        file_path TEXT NOT NULL,
        duration INTEGER NOT NULL,
        file_size INTEGER NOT NULL,
        format TEXT NOT NULL,
        quality TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        tags TEXT,
        color INTEGER NOT NULL,
        is_favorite INTEGER DEFAULT 0,
        is_archived INTEGER DEFAULT 0,
        metadata TEXT,
        transcription_confidence REAL,
        transcription_text TEXT
      )
    ''');

    // Create voice_recorder_settings table
    await _database!.execute('''
      CREATE TABLE IF NOT EXISTS voice_recorder_settings (
        id INTEGER PRIMARY KEY,
        default_quality TEXT NOT NULL,
        default_format TEXT NOT NULL,
        auto_save INTEGER DEFAULT 1,
        show_waveform INTEGER DEFAULT 1,
        enable_transcription INTEGER DEFAULT 0,
        recording_gain REAL DEFAULT 1.0,
        noise_reduction INTEGER DEFAULT 0,
        max_recording_duration INTEGER DEFAULT 60,
        default_save_location TEXT
      )
    ''');
  }

  void _loadSampleRecordings() {
    final now = DateTime.now();
    _recordings.addAll([
      VoiceRecording(
        id: '1',
        title: 'Meeting Notes - Q4 Planning',
        description: 'Discussion about quarterly goals and objectives',
        filePath: '/recordings/meeting_q4.m4a',
        duration: const Duration(minutes: 15, seconds: 30),
        fileSize: 2048000, // 2MB
        format: AudioFormat.m4a,
        quality: RecordingQuality.high,
        createdAt: now.subtract(const Duration(days: 2)),
        updatedAt: now.subtract(const Duration(days: 2)),
        tags: ['meeting', 'work', 'planning'],
        color: Colors.blue,
        isFavorite: true,
      ),
      VoiceRecording(
        id: '2',
        title: 'Voice Memo - Grocery List',
        description: 'Quick reminder for weekend shopping',
        filePath: '/recordings/grocery_memo.m4a',
        duration: const Duration(minutes: 1, seconds: 45),
        fileSize: 256000, // 256KB
        format: AudioFormat.m4a,
        quality: RecordingQuality.medium,
        createdAt: now.subtract(const Duration(hours: 6)),
        updatedAt: now.subtract(const Duration(hours: 6)),
        tags: ['personal', 'shopping'],
        color: Colors.green,
      ),
      VoiceRecording(
        id: '3',
        title: 'Interview Recording',
        description: 'Client interview for project requirements',
        filePath: '/recordings/client_interview.wav',
        duration: const Duration(minutes: 45, seconds: 12),
        fileSize: 8192000, // 8MB
        format: AudioFormat.wav,
        quality: RecordingQuality.lossless,
        createdAt: now.subtract(const Duration(days: 1)),
        updatedAt: now.subtract(const Duration(days: 1)),
        tags: ['interview', 'client', 'work'],
        color: Colors.purple,
        isFavorite: true,
      ),
    ]);
    _notifyChange();
  }

  // Recording operations
  List<VoiceRecording> getRecordings() => List.unmodifiable(_recordings);

  List<VoiceRecording> getFavoriteRecordings() {
    return _recordings.where((r) => r.isFavorite && !r.isArchived).toList();
  }

  List<VoiceRecording> getRecentRecordings({int limit = 10}) {
    final recent = _recordings.where((r) => !r.isArchived).toList();
    recent.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return recent.take(limit).toList();
  }

  List<VoiceRecording> searchRecordings(String query) {
    final lowerQuery = query.toLowerCase();
    return _recordings.where((r) {
      return !r.isArchived &&
          (r.title.toLowerCase().contains(lowerQuery) ||
              r.description.toLowerCase().contains(lowerQuery) ||
              r.tags.any((tag) => tag.toLowerCase().contains(lowerQuery)) ||
              (r.transcriptionText?.toLowerCase().contains(lowerQuery) ??
                  false));
    }).toList();
  }

  Future<void> addRecording(VoiceRecording recording) async {
    _recordings.add(recording);
    await _saveRecordingToDatabase(recording);
    _notifyChange();
  }

  Future<void> updateRecording(VoiceRecording recording) async {
    final index = _recordings.indexWhere((r) => r.id == recording.id);
    if (index != -1) {
      _recordings[index] = recording;
      await _saveRecordingToDatabase(recording);
      _notifyChange();
    }
  }

  Future<void> deleteRecording(String recordingId) async {
    final recording = _recordings.firstWhere((r) => r.id == recordingId);

    // Delete the actual file
    try {
      final file = File(recording.filePath);
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      // File might not exist, continue with database deletion
    }

    _recordings.removeWhere((r) => r.id == recordingId);
    await _deleteRecordingFromDatabase(recordingId);
    _notifyChange();
  }

  Future<void> toggleFavorite(String recordingId) async {
    final index = _recordings.indexWhere((r) => r.id == recordingId);
    if (index != -1) {
      final recording = _recordings[index].copyWith(
        isFavorite: !_recordings[index].isFavorite,
        updatedAt: DateTime.now(),
      );
      _recordings[index] = recording;
      await _saveRecordingToDatabase(recording);
      _notifyChange();
    }
  }

  Future<void> archiveRecording(String recordingId) async {
    final index = _recordings.indexWhere((r) => r.id == recordingId);
    if (index != -1) {
      final recording = _recordings[index].copyWith(
        isArchived: true,
        updatedAt: DateTime.now(),
      );
      _recordings[index] = recording;
      await _saveRecordingToDatabase(recording);
      _notifyChange();
    }
  }

  // Recording session management
  Future<RecordingSession> startRecording({
    RecordingQuality? quality,
    AudioFormat? format,
  }) async {
    if (_currentSession?.status == RecordingStatus.recording) {
      throw Exception('Recording already in progress');
    }

    // Stop any current playback
    await stopPlayback();

    final now = DateTime.now();
    final recordingId = now.millisecondsSinceEpoch.toString();

    // Create temporary file path
    final directory = await getApplicationDocumentsDirectory();
    final recordingsDir = Directory(path.join(directory.path, 'recordings'));
    if (!await recordingsDir.exists()) {
      await recordingsDir.create(recursive: true);
    }

    final tempFilePath = path.join(
      recordingsDir.path,
      'temp_$recordingId.${(format ?? _settings.defaultFormat).name}',
    );

    _currentSession = RecordingSession(
      id: recordingId,
      startTime: now,
      status: RecordingStatus.recording,
      quality: quality ?? _settings.defaultQuality,
      format: format ?? _settings.defaultFormat,
      tempFilePath: tempFilePath,
    );

    // Start recording timer
    _recordingTimer = Timer.periodic(const Duration(milliseconds: 100), (
      timer,
    ) {
      if (_currentSession != null) {
        final elapsed = DateTime.now().difference(_currentSession!.startTime);
        _currentSession = _currentSession!.copyWith(
          currentDuration: elapsed,
          currentAmplitude: _generateMockAmplitude(), // Mock amplitude for demo
        );
        _sessionNotifier.value = _currentSession;

        // Check max duration
        if (elapsed.inMinutes >= _settings.maxRecordingDuration) {
          stopRecording();
        }
      }
    });

    _sessionNotifier.value = _currentSession;
    return _currentSession!;
  }

  Future<void> pauseRecording() async {
    if (_currentSession?.status == RecordingStatus.recording) {
      _currentSession = _currentSession!.copyWith(
        status: RecordingStatus.paused,
      );
      _recordingTimer?.cancel();
      _sessionNotifier.value = _currentSession;
    }
  }

  Future<void> resumeRecording() async {
    if (_currentSession?.status == RecordingStatus.paused) {
      _currentSession = _currentSession!.copyWith(
        status: RecordingStatus.recording,
      );

      // Resume timer
      _recordingTimer = Timer.periodic(const Duration(milliseconds: 100), (
        timer,
      ) {
        if (_currentSession != null) {
          final elapsed = DateTime.now().difference(_currentSession!.startTime);
          _currentSession = _currentSession!.copyWith(
            currentDuration: elapsed,
            currentAmplitude: _generateMockAmplitude(),
          );
          _sessionNotifier.value = _currentSession;
        }
      });

      _sessionNotifier.value = _currentSession;
    }
  }

  Future<VoiceRecording?> stopRecording() async {
    if (_currentSession == null) return null;

    _recordingTimer?.cancel();

    final session = _currentSession!;
    _currentSession = _currentSession!.copyWith(
      status: RecordingStatus.stopped,
    );
    _sessionNotifier.value = _currentSession;

    // Create the actual recording file (mock for now)
    final file = File(session.tempFilePath);
    await file.writeAsBytes(
      Uint8List.fromList([0, 1, 2, 3, 4, 5]),
    ); // Mock audio data

    final fileSize = await file.length();

    final recording = VoiceRecording(
      id: session.id,
      title: 'Recording ${DateTime.now().toString().substring(0, 19)}',
      filePath: session.tempFilePath,
      duration: session.currentDuration,
      fileSize: fileSize,
      format: session.format,
      quality: session.quality,
      createdAt: session.startTime,
      updatedAt: DateTime.now(),
    );

    if (_settings.autoSave) {
      await addRecording(recording);
    }

    _currentSession = null;
    _sessionNotifier.value = null;

    return recording;
  }

  // Playback operations
  Future<void> startPlayback(String recordingId) async {
    final recording = _recordings.firstWhere((r) => r.id == recordingId);

    // Stop any current recording
    if (_currentSession?.status == RecordingStatus.recording) {
      await stopRecording();
    }

    _currentPlayback = PlaybackSession(
      recordingId: recordingId,
      totalDuration: recording.duration,
      status: RecordingStatus.playing,
    );

    // Start playback timer
    _playbackTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (_currentPlayback != null) {
        final newPosition =
            _currentPlayback!.currentPosition +
            const Duration(milliseconds: 100);

        if (newPosition >= _currentPlayback!.totalDuration) {
          // Playback finished
          _currentPlayback = _currentPlayback!.copyWith(
            currentPosition: _currentPlayback!.totalDuration,
            status: RecordingStatus.stopped,
          );
          _playbackTimer?.cancel();
        } else {
          _currentPlayback = _currentPlayback!.copyWith(
            currentPosition: newPosition,
          );
        }

        _playbackNotifier.value = _currentPlayback;
      }
    });

    _playbackNotifier.value = _currentPlayback;
  }

  Future<void> pausePlayback() async {
    if (_currentPlayback?.status == RecordingStatus.playing) {
      _currentPlayback = _currentPlayback!.copyWith(
        status: RecordingStatus.paused,
      );
      _playbackTimer?.cancel();
      _playbackNotifier.value = _currentPlayback;
    }
  }

  Future<void> resumePlayback() async {
    if (_currentPlayback?.status == RecordingStatus.paused) {
      _currentPlayback = _currentPlayback!.copyWith(
        status: RecordingStatus.playing,
      );

      // Resume playback timer
      _playbackTimer = Timer.periodic(const Duration(milliseconds: 100), (
        timer,
      ) {
        if (_currentPlayback != null) {
          final newPosition =
              _currentPlayback!.currentPosition +
              const Duration(milliseconds: 100);

          if (newPosition >= _currentPlayback!.totalDuration) {
            _currentPlayback = _currentPlayback!.copyWith(
              currentPosition: _currentPlayback!.totalDuration,
              status: RecordingStatus.stopped,
            );
            _playbackTimer?.cancel();
          } else {
            _currentPlayback = _currentPlayback!.copyWith(
              currentPosition: newPosition,
            );
          }

          _playbackNotifier.value = _currentPlayback;
        }
      });

      _playbackNotifier.value = _currentPlayback;
    }
  }

  Future<void> stopPlayback() async {
    _playbackTimer?.cancel();
    _currentPlayback = null;
    _playbackNotifier.value = null;
  }

  Future<void> seekTo(Duration position) async {
    if (_currentPlayback != null) {
      _currentPlayback = _currentPlayback!.copyWith(currentPosition: position);
      _playbackNotifier.value = _currentPlayback;
    }
  }

  // Settings management
  VoiceRecorderSettings getSettings() => _settings;

  Future<void> updateSettings(VoiceRecorderSettings settings) async {
    _settings = settings;
    await _saveSettingsToDatabase(settings);
    _notifyChange();
  }

  // Database operations
  Future<void> _saveRecordingToDatabase(VoiceRecording recording) async {
    if (_database == null) return;

    await _database!.insert('voice_recordings', {
      'id': recording.id,
      'title': recording.title,
      'description': recording.description,
      'file_path': recording.filePath,
      'duration': recording.duration.inMilliseconds,
      'file_size': recording.fileSize,
      'format': recording.format.name,
      'quality': recording.quality.name,
      'created_at': recording.createdAt.toIso8601String(),
      'updated_at': recording.updatedAt.toIso8601String(),
      'tags': recording.tags.join(','),
      'color': recording.color.value,
      'is_favorite': recording.isFavorite ? 1 : 0,
      'is_archived': recording.isArchived ? 1 : 0,
      'metadata': recording.metadata.toString(),
      'transcription_confidence': recording.transcriptionConfidence,
      'transcription_text': recording.transcriptionText,
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<void> _loadRecordingsFromDatabase() async {
    if (_database == null) return;

    final List<Map<String, dynamic>> maps = await _database!.query(
      'voice_recordings',
    );
    _recordings.clear();

    for (final map in maps) {
      final recording = VoiceRecording.fromJson({
        'id': map['id'],
        'title': map['title'],
        'description': map['description'],
        'filePath': map['file_path'],
        'duration': map['duration'],
        'fileSize': map['file_size'],
        'format': map['format'],
        'quality': map['quality'],
        'createdAt': map['created_at'],
        'updatedAt': map['updated_at'],
        'tags': map['tags']?.split(',') ?? [],
        'color': map['color'],
        'isFavorite': map['is_favorite'] == 1,
        'isArchived': map['is_archived'] == 1,
        'metadata': {},
        'transcriptionConfidence': map['transcription_confidence'],
        'transcriptionText': map['transcription_text'],
      });

      _recordings.add(recording);
    }
  }

  Future<void> _deleteRecordingFromDatabase(String recordingId) async {
    if (_database == null) return;

    await _database!.delete(
      'voice_recordings',
      where: 'id = ?',
      whereArgs: [recordingId],
    );
  }

  Future<void> _saveSettingsToDatabase(VoiceRecorderSettings settings) async {
    if (_database == null) return;

    await _database!.insert('voice_recorder_settings', {
      'id': 1,
      'default_quality': settings.defaultQuality.name,
      'default_format': settings.defaultFormat.name,
      'auto_save': settings.autoSave ? 1 : 0,
      'show_waveform': settings.showWaveform ? 1 : 0,
      'enable_transcription': settings.enableTranscription ? 1 : 0,
      'recording_gain': settings.recordingGain,
      'noise_reduction': settings.noiseReduction ? 1 : 0,
      'max_recording_duration': settings.maxRecordingDuration,
      'default_save_location': settings.defaultSaveLocation,
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<void> _loadSettingsFromDatabase() async {
    if (_database == null) return;

    final List<Map<String, dynamic>> maps = await _database!.query(
      'voice_recorder_settings',
      where: 'id = ?',
      whereArgs: [1],
    );

    if (maps.isNotEmpty) {
      final map = maps.first;
      _settings = VoiceRecorderSettings.fromJson({
        'defaultQuality': map['default_quality'],
        'defaultFormat': map['default_format'],
        'autoSave': map['auto_save'] == 1,
        'showWaveform': map['show_waveform'] == 1,
        'enableTranscription': map['enable_transcription'] == 1,
        'recordingGain': map['recording_gain'],
        'noiseReduction': map['noise_reduction'] == 1,
        'maxRecordingDuration': map['max_recording_duration'],
        'defaultSaveLocation': map['default_save_location'],
      });
    }
  }

  // Helper methods
  double _generateMockAmplitude() {
    // Generate mock amplitude between 0.0 and 1.0 for visualization
    return (DateTime.now().millisecondsSinceEpoch % 100) / 100.0;
  }

  void _notifyChange() {
    _stateNotifier.value++;
  }

  // Getters for notifiers
  ValueNotifier<int> get stateNotifier => _stateNotifier;
  ValueNotifier<RecordingSession?> get sessionNotifier => _sessionNotifier;
  ValueNotifier<PlaybackSession?> get playbackNotifier => _playbackNotifier;
  RecordingSession? get currentSession => _currentSession;
  PlaybackSession? get currentPlayback => _currentPlayback;
}

// Providers
final voiceRecorderServiceProvider = Provider<VoiceRecorderService>((ref) {
  final service = VoiceRecorderService();
  service.initialize();
  return service;
});

final recordingsProvider = Provider<List<VoiceRecording>>((ref) {
  final service = ref.watch(voiceRecorderServiceProvider);
  service.stateNotifier.value; // Trigger rebuild
  return service.getRecordings();
});

final favoriteRecordingsProvider = Provider<List<VoiceRecording>>((ref) {
  final service = ref.watch(voiceRecorderServiceProvider);
  service.stateNotifier.value; // Trigger rebuild
  return service.getFavoriteRecordings();
});

final recentRecordingsProvider = Provider<List<VoiceRecording>>((ref) {
  final service = ref.watch(voiceRecorderServiceProvider);
  service.stateNotifier.value; // Trigger rebuild
  return service.getRecentRecordings();
});

final currentRecordingSessionProvider = Provider<RecordingSession?>((ref) {
  final service = ref.watch(voiceRecorderServiceProvider);
  service.sessionNotifier.value; // Trigger rebuild
  return service.currentSession;
});

final currentPlaybackSessionProvider = Provider<PlaybackSession?>((ref) {
  final service = ref.watch(voiceRecorderServiceProvider);
  service.playbackNotifier.value; // Trigger rebuild
  return service.currentPlayback;
});

final voiceRecorderSettingsProvider = Provider<VoiceRecorderSettings>((ref) {
  final service = ref.watch(voiceRecorderServiceProvider);
  service.stateNotifier.value; // Trigger rebuild
  return service.getSettings();
});
