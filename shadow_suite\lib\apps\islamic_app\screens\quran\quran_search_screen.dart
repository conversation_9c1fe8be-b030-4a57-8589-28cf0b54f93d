import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_theme.dart';
import '../../services/islamic_providers.dart';
import '../../models/verse.dart' as verse_model;
import '../../models/bookmark.dart' as bookmark_model;

class QuranSearchScreen extends ConsumerStatefulWidget {
  const QuranSearchScreen({super.key});

  @override
  ConsumerState<QuranSearchScreen> createState() => _QuranSearchScreenState();
}

class _QuranSearchScreenState extends ConsumerState<QuranSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedFilter = 'All';
  final List<String> _searchHistory = [];

  final List<String> _filterOptions = [
    'All',
    'Arabic Text',
    'English Translation',
    'Transliteration',
    'Meccan <PERSON>ah<PERSON>',
    'Medinan Surahs',
  ];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final searchResultsAsync = ref.watch(quranSearchProvider(_searchQuery));

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Quran Search'),
        backgroundColor: AppTheme.islamicAppColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            ref.read(islamicAppCurrentScreenProvider.notifier).state =
                IslamicAppScreen.surahList;
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () => _showSearchHistory(),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              setState(() {
                _selectedFilter = value;
              });
            },
            itemBuilder: (context) => _filterOptions.map((filter) {
              return PopupMenuItem(
                value: filter,
                child: Row(
                  children: [
                    if (_selectedFilter == filter)
                      Icon(
                        Icons.check,
                        color: AppTheme.islamicAppColor,
                        size: 20,
                      ),
                    if (_selectedFilter == filter) const SizedBox(width: 8),
                    Text(filter),
                  ],
                ),
              );
            }).toList(),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.filter_list),
                  const SizedBox(width: 4),
                  Text(_selectedFilter),
                ],
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchSection(),
          Expanded(
            child: _searchQuery.isEmpty
                ? _buildSearchSuggestions()
                : searchResultsAsync.when(
                    data: (results) => _buildSearchResults(results),
                    loading: () =>
                        const Center(child: CircularProgressIndicator()),
                    error: (error, stack) =>
                        Center(child: Text('Error: $error')),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Colors.grey, width: 0.2)),
      ),
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search Quran verses, surahs, or meanings...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                        });
                        ref.read(quranSearchQueryProvider.notifier).state = '';
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
              ref.read(quranSearchQueryProvider.notifier).state = value;
            },
            onSubmitted: (value) {
              if (value.isNotEmpty && !_searchHistory.contains(value)) {
                setState(() {
                  _searchHistory.insert(0, value);
                  if (_searchHistory.length > 10) {
                    _searchHistory.removeLast();
                  }
                });
              }
            },
          ),
          if (_selectedFilter != 'All') ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppTheme.islamicAppColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.filter_list,
                    size: 16,
                    color: AppTheme.islamicAppColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Filter: $_selectedFilter',
                    style: TextStyle(
                      color: AppTheme.islamicAppColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 4),
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedFilter = 'All';
                      });
                    },
                    child: Icon(
                      Icons.close,
                      size: 16,
                      color: AppTheme.islamicAppColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSearchSuggestions() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(),
          const SizedBox(height: 24),
          _buildQuickSearches(),
          const SizedBox(height: 24),
          _buildSearchTips(),
          if (_searchHistory.isNotEmpty) ...[
            const SizedBox(height: 24),
            _buildRecentSearches(),
          ],
        ],
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.islamicAppColor,
            AppTheme.islamicAppColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Search the Quran',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Find verses by Arabic text, English translation, or meaning',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
          const Icon(Icons.search, size: 48, color: Colors.white),
        ],
      ),
    );
  }

  Widget _buildQuickSearches() {
    final quickSearches = [
      {'title': 'Allah', 'subtitle': 'Find verses mentioning Allah'},
      {'title': 'Prayer', 'subtitle': 'Verses about Salah'},
      {'title': 'Paradise', 'subtitle': 'Descriptions of Jannah'},
      {'title': 'Forgiveness', 'subtitle': 'Verses about Maghfirah'},
      {'title': 'Patience', 'subtitle': 'Verses about Sabr'},
      {'title': 'Gratitude', 'subtitle': 'Verses about Shukr'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Searches',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.islamicAppColor,
          ),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 2.5,
          ),
          itemCount: quickSearches.length,
          itemBuilder: (context, index) {
            final search = quickSearches[index];
            return Card(
              child: InkWell(
                onTap: () => _performQuickSearch(search['title']!),
                borderRadius: BorderRadius.circular(12),
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        search['title']!,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        search['subtitle']!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildSearchTips() {
    final tips = [
      'Use Arabic text to search for specific words',
      'Search in English for meanings and concepts',
      'Use transliteration for pronunciation help',
      'Filter by Meccan or Medinan surahs',
      'Search for specific surah names or numbers',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Search Tips',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.islamicAppColor,
          ),
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: tips
                  .map(
                    (tip) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            margin: const EdgeInsets.only(top: 6),
                            width: 4,
                            height: 4,
                            decoration: BoxDecoration(
                              color: AppTheme.islamicAppColor,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              tip,
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                  .toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRecentSearches() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Searches',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.islamicAppColor,
              ),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _searchHistory.clear();
                });
              },
              child: const Text('Clear All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Card(
          child: Column(
            children: _searchHistory.take(5).map((search) {
              return ListTile(
                leading: const Icon(Icons.history),
                title: Text(search),
                trailing: IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () {
                    setState(() {
                      _searchHistory.remove(search);
                    });
                  },
                ),
                onTap: () => _performQuickSearch(search),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildSearchResults(List<verse_model.Verse> results) {
    final filteredResults = _applyFilter(results);

    if (filteredResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No results found',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'Try different keywords or adjust your filter',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.grey[100],
          child: Row(
            children: [
              Icon(Icons.search, color: AppTheme.islamicAppColor),
              const SizedBox(width: 8),
              Text(
                '${filteredResults.length} result${filteredResults.length == 1 ? '' : 's'} found',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: filteredResults.length,
            itemBuilder: (context, index) {
              final verse = filteredResults[index];
              return _buildSearchResultItem(verse);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSearchResultItem(verse_model.Verse verse) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _navigateToVerse(verse),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.islamicAppColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${verse.surahNumber}:${verse.verseNumber}',
                      style: TextStyle(
                        color: AppTheme.islamicAppColor,
                        fontWeight: FontWeight.w500,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  const Spacer(),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleVerseAction(verse, value),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'bookmark',
                        child: Text('Bookmark'),
                      ),
                      const PopupMenuItem(value: 'copy', child: Text('Copy')),
                      const PopupMenuItem(value: 'share', child: Text('Share')),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                verse.textArabic,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontFamily: 'Arabic',
                  height: 2.0,
                  color: AppTheme.islamicAppColor,
                ),
                textAlign: TextAlign.right,
                textDirection: TextDirection.rtl,
              ),
              const SizedBox(height: 8),
              Text(
                verse.textTransliteration,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontStyle: FontStyle.italic,
                  color: Colors.grey[700],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                verse.textEnglish,
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(height: 1.6),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper methods
  void _performQuickSearch(String query) {
    _searchController.text = query;
    setState(() {
      _searchQuery = query;
    });
    ref.read(quranSearchQueryProvider.notifier).state = query;

    if (!_searchHistory.contains(query)) {
      setState(() {
        _searchHistory.insert(0, query);
        if (_searchHistory.length > 10) {
          _searchHistory.removeLast();
        }
      });
    }
  }

  List<verse_model.Verse> _applyFilter(List<verse_model.Verse> results) {
    switch (_selectedFilter) {
      case 'Arabic Text':
        return results
            .where((verse) => verse.textArabic.contains(_searchQuery))
            .toList();
      case 'English Translation':
        return results
            .where(
              (verse) => verse.textEnglish.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              ),
            )
            .toList();
      case 'Transliteration':
        return results
            .where(
              (verse) => verse.textTransliteration.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              ),
            )
            .toList();
      case 'Meccan Surahs':
        // This would require surah information - for now return all
        return results;
      case 'Medinan Surahs':
        // This would require surah information - for now return all
        return results;
      default:
        return results;
    }
  }

  void _navigateToVerse(verse_model.Verse verse) {
    // Get the surah for this verse
    final surahsAsync = ref.read(surahsProvider);
    surahsAsync.whenData((surahs) {
      final surah = surahs.firstWhere((s) => s.number == verse.surahNumber);
      ref.read(selectedSurahProvider.notifier).state = surah;
      ref.read(islamicAppCurrentScreenProvider.notifier).state =
          IslamicAppScreen.quranReading;
    });
  }

  void _handleVerseAction(verse_model.Verse verse, String action) {
    switch (action) {
      case 'bookmark':
        _showBookmarkDialog(verse);
        break;
      case 'copy':
        _copyVerse(verse);
        break;
      case 'share':
        _shareVerse(verse);
        break;
    }
  }

  void _showBookmarkDialog(verse_model.Verse verse) {
    final titleController = TextEditingController();
    final notesController = TextEditingController();

    titleController.text = 'Verse ${verse.verseNumber}';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Bookmark'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: titleController,
              decoration: const InputDecoration(
                labelText: 'Title',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: notesController,
              decoration: const InputDecoration(
                labelText: 'Notes (optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (titleController.text.isNotEmpty) {
                final bookmark = bookmark_model.Bookmark(
                  surahNumber: verse.surahNumber,
                  verseNumber: verse.verseNumber,
                  title: titleController.text,
                  notes: notesController.text,
                  tags: [],
                );
                ref.read(bookmarksProvider.notifier).addBookmark(bookmark);
                Navigator.of(context).pop();
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(const SnackBar(content: Text('Bookmark added')));
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _copyVerse(verse_model.Verse verse) {
    final text =
        '${verse.textArabic}\n\n${verse.textEnglish}\n\n(${verse.surahNumber}:${verse.verseNumber})';
    // In a real app, you would use Clipboard.setData here
    // Clipboard.setData(ClipboardData(text: text));

    // Use the text variable to avoid unused variable warning
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Verse copied to clipboard: ${text.substring(0, 50)}...'),
      ),
    );
  }

  void _shareVerse(verse_model.Verse verse) {
    // Create a shareable text format
    final shareText =
        '''
📖 Quran Verse (${verse.surahNumber}:${verse.verseNumber})

${verse.textArabic}

${verse.textEnglish}

Shared from Shadow Suite Islamic App
''';

    // Show share options dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Share Verse'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Share this verse with others:'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                shareText,
                style: const TextStyle(fontSize: 12),
                maxLines: 10,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Clipboard.setData(ClipboardData(text: shareText));
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Verse copied to clipboard for sharing'),
                ),
              );
            },
            child: const Text('Copy to Share'),
          ),
        ],
      ),
    );
  }

  void _showSearchHistory() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Search History',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _searchHistory.clear();
                    });
                    Navigator.of(context).pop();
                  },
                  child: const Text('Clear All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_searchHistory.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Text('No search history'),
                ),
              )
            else
              ...(_searchHistory.map(
                (search) => ListTile(
                  leading: const Icon(Icons.history),
                  title: Text(search),
                  trailing: IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () {
                      setState(() {
                        _searchHistory.remove(search);
                      });
                    },
                  ),
                  onTap: () {
                    Navigator.of(context).pop();
                    _performQuickSearch(search);
                  },
                ),
              )),
          ],
        ),
      ),
    );
  }
}
