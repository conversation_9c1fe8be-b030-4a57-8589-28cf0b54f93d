import 'package:flutter/material.dart';

/// Recording quality levels
enum RecordingQuality {
  low,
  medium,
  high,
  lossless,
}

/// Recording status
enum RecordingStatus {
  idle,
  recording,
  paused,
  stopped,
  playing,
  error,
}

/// Audio format types
enum AudioFormat {
  mp3,
  wav,
  aac,
  m4a,
}

/// Voice recording model
class VoiceRecording {
  final String id;
  final String title;
  final String description;
  final String filePath;
  final Duration duration;
  final int fileSize; // in bytes
  final AudioFormat format;
  final RecordingQuality quality;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<String> tags;
  final Color color;
  final bool isFavorite;
  final bool isArchived;
  final Map<String, dynamic> metadata;
  final double? transcriptionConfidence;
  final String? transcriptionText;

  const VoiceRecording({
    required this.id,
    required this.title,
    this.description = '',
    required this.filePath,
    required this.duration,
    required this.fileSize,
    this.format = AudioFormat.m4a,
    this.quality = RecordingQuality.medium,
    required this.createdAt,
    required this.updatedAt,
    this.tags = const [],
    this.color = Colors.blue,
    this.isFavorite = false,
    this.isArchived = false,
    this.metadata = const {},
    this.transcriptionConfidence,
    this.transcriptionText,
  });

  VoiceRecording copyWith({
    String? id,
    String? title,
    String? description,
    String? filePath,
    Duration? duration,
    int? fileSize,
    AudioFormat? format,
    RecordingQuality? quality,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? tags,
    Color? color,
    bool? isFavorite,
    bool? isArchived,
    Map<String, dynamic>? metadata,
    double? transcriptionConfidence,
    String? transcriptionText,
  }) {
    return VoiceRecording(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      filePath: filePath ?? this.filePath,
      duration: duration ?? this.duration,
      fileSize: fileSize ?? this.fileSize,
      format: format ?? this.format,
      quality: quality ?? this.quality,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      tags: tags ?? this.tags,
      color: color ?? this.color,
      isFavorite: isFavorite ?? this.isFavorite,
      isArchived: isArchived ?? this.isArchived,
      metadata: metadata ?? this.metadata,
      transcriptionConfidence: transcriptionConfidence ?? this.transcriptionConfidence,
      transcriptionText: transcriptionText ?? this.transcriptionText,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'filePath': filePath,
      'duration': duration.inMilliseconds,
      'fileSize': fileSize,
      'format': format.name,
      'quality': quality.name,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'tags': tags,
      'color': color.value,
      'isFavorite': isFavorite,
      'isArchived': isArchived,
      'metadata': metadata,
      'transcriptionConfidence': transcriptionConfidence,
      'transcriptionText': transcriptionText,
    };
  }

  factory VoiceRecording.fromJson(Map<String, dynamic> json) {
    return VoiceRecording(
      id: json['id'],
      title: json['title'],
      description: json['description'] ?? '',
      filePath: json['filePath'],
      duration: Duration(milliseconds: json['duration']),
      fileSize: json['fileSize'],
      format: AudioFormat.values.firstWhere((e) => e.name == json['format']),
      quality: RecordingQuality.values.firstWhere((e) => e.name == json['quality']),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      tags: List<String>.from(json['tags'] ?? []),
      color: Color(json['color'] ?? Colors.blue.value),
      isFavorite: json['isFavorite'] ?? false,
      isArchived: json['isArchived'] ?? false,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      transcriptionConfidence: json['transcriptionConfidence']?.toDouble(),
      transcriptionText: json['transcriptionText'],
    );
  }

  String get formattedDuration {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  String get formattedFileSize {
    if (fileSize < 1024) return '${fileSize}B';
    if (fileSize < 1024 * 1024) return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
  }
}

/// Recording session model for active recording
class RecordingSession {
  final String id;
  final DateTime startTime;
  final Duration currentDuration;
  final RecordingStatus status;
  final RecordingQuality quality;
  final AudioFormat format;
  final String tempFilePath;
  final double? currentAmplitude;
  final List<double> amplitudeHistory;

  const RecordingSession({
    required this.id,
    required this.startTime,
    this.currentDuration = Duration.zero,
    this.status = RecordingStatus.idle,
    this.quality = RecordingQuality.medium,
    this.format = AudioFormat.m4a,
    required this.tempFilePath,
    this.currentAmplitude,
    this.amplitudeHistory = const [],
  });

  RecordingSession copyWith({
    String? id,
    DateTime? startTime,
    Duration? currentDuration,
    RecordingStatus? status,
    RecordingQuality? quality,
    AudioFormat? format,
    String? tempFilePath,
    double? currentAmplitude,
    List<double>? amplitudeHistory,
  }) {
    return RecordingSession(
      id: id ?? this.id,
      startTime: startTime ?? this.startTime,
      currentDuration: currentDuration ?? this.currentDuration,
      status: status ?? this.status,
      quality: quality ?? this.quality,
      format: format ?? this.format,
      tempFilePath: tempFilePath ?? this.tempFilePath,
      currentAmplitude: currentAmplitude ?? this.currentAmplitude,
      amplitudeHistory: amplitudeHistory ?? this.amplitudeHistory,
    );
  }

  String get formattedDuration {
    final minutes = currentDuration.inMinutes;
    final seconds = currentDuration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}

/// Playback session model
class PlaybackSession {
  final String recordingId;
  final Duration currentPosition;
  final Duration totalDuration;
  final RecordingStatus status;
  final double volume;
  final double playbackSpeed;

  const PlaybackSession({
    required this.recordingId,
    this.currentPosition = Duration.zero,
    required this.totalDuration,
    this.status = RecordingStatus.idle,
    this.volume = 1.0,
    this.playbackSpeed = 1.0,
  });

  PlaybackSession copyWith({
    String? recordingId,
    Duration? currentPosition,
    Duration? totalDuration,
    RecordingStatus? status,
    double? volume,
    double? playbackSpeed,
  }) {
    return PlaybackSession(
      recordingId: recordingId ?? this.recordingId,
      currentPosition: currentPosition ?? this.currentPosition,
      totalDuration: totalDuration ?? this.totalDuration,
      status: status ?? this.status,
      volume: volume ?? this.volume,
      playbackSpeed: playbackSpeed ?? this.playbackSpeed,
    );
  }

  double get progress {
    if (totalDuration.inMilliseconds == 0) return 0.0;
    return currentPosition.inMilliseconds / totalDuration.inMilliseconds;
  }

  String get formattedPosition {
    final minutes = currentPosition.inMinutes;
    final seconds = currentPosition.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  String get formattedTotal {
    final minutes = totalDuration.inMinutes;
    final seconds = totalDuration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}

/// Voice recorder settings model
class VoiceRecorderSettings {
  final RecordingQuality defaultQuality;
  final AudioFormat defaultFormat;
  final bool autoSave;
  final bool showWaveform;
  final bool enableTranscription;
  final double recordingGain;
  final bool noiseReduction;
  final int maxRecordingDuration; // in minutes
  final String defaultSaveLocation;

  const VoiceRecorderSettings({
    this.defaultQuality = RecordingQuality.medium,
    this.defaultFormat = AudioFormat.m4a,
    this.autoSave = true,
    this.showWaveform = true,
    this.enableTranscription = false,
    this.recordingGain = 1.0,
    this.noiseReduction = false,
    this.maxRecordingDuration = 60,
    this.defaultSaveLocation = '',
  });

  VoiceRecorderSettings copyWith({
    RecordingQuality? defaultQuality,
    AudioFormat? defaultFormat,
    bool? autoSave,
    bool? showWaveform,
    bool? enableTranscription,
    double? recordingGain,
    bool? noiseReduction,
    int? maxRecordingDuration,
    String? defaultSaveLocation,
  }) {
    return VoiceRecorderSettings(
      defaultQuality: defaultQuality ?? this.defaultQuality,
      defaultFormat: defaultFormat ?? this.defaultFormat,
      autoSave: autoSave ?? this.autoSave,
      showWaveform: showWaveform ?? this.showWaveform,
      enableTranscription: enableTranscription ?? this.enableTranscription,
      recordingGain: recordingGain ?? this.recordingGain,
      noiseReduction: noiseReduction ?? this.noiseReduction,
      maxRecordingDuration: maxRecordingDuration ?? this.maxRecordingDuration,
      defaultSaveLocation: defaultSaveLocation ?? this.defaultSaveLocation,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'defaultQuality': defaultQuality.name,
      'defaultFormat': defaultFormat.name,
      'autoSave': autoSave,
      'showWaveform': showWaveform,
      'enableTranscription': enableTranscription,
      'recordingGain': recordingGain,
      'noiseReduction': noiseReduction,
      'maxRecordingDuration': maxRecordingDuration,
      'defaultSaveLocation': defaultSaveLocation,
    };
  }

  factory VoiceRecorderSettings.fromJson(Map<String, dynamic> json) {
    return VoiceRecorderSettings(
      defaultQuality: RecordingQuality.values.firstWhere((e) => e.name == json['defaultQuality']),
      defaultFormat: AudioFormat.values.firstWhere((e) => e.name == json['defaultFormat']),
      autoSave: json['autoSave'] ?? true,
      showWaveform: json['showWaveform'] ?? true,
      enableTranscription: json['enableTranscription'] ?? false,
      recordingGain: json['recordingGain']?.toDouble() ?? 1.0,
      noiseReduction: json['noiseReduction'] ?? false,
      maxRecordingDuration: json['maxRecordingDuration'] ?? 60,
      defaultSaveLocation: json['defaultSaveLocation'] ?? '',
    );
  }
}
