import 'package:flutter/material.dart';
import '../services/quality_assurance_service.dart';

/// Comprehensive quality assurance widget
class QualityAssuranceWidget extends StatefulWidget {
  const QualityAssuranceWidget({super.key});

  @override
  State<QualityAssuranceWidget> createState() => _QualityAssuranceWidgetState();
}

class _QualityAssuranceWidgetState extends State<QualityAssuranceWidget> {
  QualityReport? _lastReport;
  bool _isRunningQA = false;
  QualityCategory? _selectedCategory;

  @override
  void initState() {
    super.initState();
    QualityAssuranceService.initialize();
    _lastReport = QualityAssuranceService.lastReport;
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            _buildActionButtons(),
            const SizedBox(height: 24),
            if (_lastReport != null) ...[
              _buildQualityScore(),
              const SizedBox(height: 24),
              _buildCategoryFilter(),
              const SizedBox(height: 16),
              Expanded(child: _buildResultsList()),
            ] else
              const Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.verified, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text('No quality report available'),
                      Text('Run quality assurance to get started'),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Icon(Icons.verified, size: 24),
        const SizedBox(width: 8),
        Text(
          'Quality Assurance',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const Spacer(),
        if (_isRunningQA)
          const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isRunningQA ? null : _runFullQA,
            icon: const Icon(Icons.play_arrow),
            label: const Text('Run Full QA'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isRunningQA ? null : _runQuickCheck,
            icon: const Icon(Icons.speed),
            label: const Text('Quick Check'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.secondary,
              foregroundColor: Theme.of(context).colorScheme.onSecondary,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _showImprovements,
            icon: const Icon(Icons.lightbulb),
            label: const Text('Suggestions'),
          ),
        ),
      ],
    );
  }

  Widget _buildQualityScore() {
    final score = QualityAssuranceService.getQualityScore();
    final report = _lastReport!;

    Color scoreColor;
    String scoreLabel;

    if (report.isExcellent) {
      scoreColor = Colors.green;
      scoreLabel = 'Excellent';
    } else if (report.isGood) {
      scoreColor = Colors.lightGreen;
      scoreLabel = 'Good';
    } else if (report.isAcceptable) {
      scoreColor = Colors.orange;
      scoreLabel = 'Acceptable';
    } else {
      scoreColor = Colors.red;
      scoreLabel = 'Needs Improvement';
    }

    return Card(
      color: scoreColor.withValues(alpha: 0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Quality Score',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Text(
                        '${score.toStringAsFixed(1)}/100',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: scoreColor,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Chip(
                        label: Text(scoreLabel),
                        backgroundColor: scoreColor.withValues(alpha: 0.2),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Column(
              children: [
                _buildScoreItem(
                  'Passed',
                  '${report.passedChecks}',
                  Colors.green,
                ),
                _buildScoreItem('Failed', '${report.failedChecks}', Colors.red),
                _buildScoreItem(
                  'Critical',
                  '${report.criticalIssues}',
                  Colors.red,
                ),
                _buildScoreItem(
                  'Warnings',
                  '${report.warningIssues}',
                  Colors.orange,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScoreItem(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 60,
            child: Text(label, style: const TextStyle(fontSize: 12)),
          ),
          Text(
            value,
            style: TextStyle(fontWeight: FontWeight.bold, color: color),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryFilter() {
    return Row(
      children: [
        const Text('Filter by category: '),
        const SizedBox(width: 8),
        DropdownButton<QualityCategory?>(
          value: _selectedCategory,
          items: [
            const DropdownMenuItem<QualityCategory?>(
              value: null,
              child: Text('All Categories'),
            ),
            ...QualityCategory.values.map((category) {
              return DropdownMenuItem<QualityCategory?>(
                value: category,
                child: Text(_getCategoryName(category)),
              );
            }),
          ],
          onChanged: (value) => setState(() => _selectedCategory = value),
        ),
      ],
    );
  }

  Widget _buildResultsList() {
    final results = _lastReport!.results;
    final filteredResults = _selectedCategory == null
        ? results
        : results.where((r) => r.category == _selectedCategory).toList();

    if (filteredResults.isEmpty) {
      return const Center(child: Text('No results for selected category'));
    }

    return ListView.builder(
      itemCount: filteredResults.length,
      itemBuilder: (context, index) {
        return _buildResultTile(filteredResults[index]);
      },
    );
  }

  Widget _buildResultTile(QualityCheckResult result) {
    IconData icon;
    Color color;

    if (result.passed) {
      icon = Icons.check_circle;
      color = Colors.green;
    } else {
      switch (result.severity) {
        case QualitySeverity.critical:
          icon = Icons.error;
          color = Colors.red;
          break;
        case QualitySeverity.warning:
          icon = Icons.warning;
          color = Colors.orange;
          break;
        case QualitySeverity.info:
          icon = Icons.info;
          color = Colors.blue;
          break;
      }
    }

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ExpansionTile(
        leading: Icon(icon, color: color),
        title: Text(result.checkName),
        subtitle: Text(result.message),
        trailing: Chip(
          label: Text(_getCategoryName(result.category)),
          backgroundColor: _getCategoryColor(
            result.category,
          ).withValues(alpha: 0.2),
        ),
        children: [
          if (result.details != null)
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Details:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  ...result.details!.entries.map((entry) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Row(
                        children: [
                          SizedBox(width: 120, child: Text('${entry.key}:')),
                          Text('${entry.value}'),
                        ],
                      ),
                    );
                  }),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Future<void> _runFullQA() async {
    setState(() => _isRunningQA = true);

    try {
      final report = await QualityAssuranceService.runQualityAssurance();
      setState(() => _lastReport = report);

      final score = QualityAssuranceService.getQualityScore();
      _showSnackBar(
        'QA completed: Score ${score.toStringAsFixed(1)}/100',
        score >= 80 ? Colors.green : Colors.orange,
      );
    } catch (e) {
      _showSnackBar('QA failed: $e', Colors.red);
    } finally {
      setState(() => _isRunningQA = false);
    }
  }

  Future<void> _runQuickCheck() async {
    setState(() => _isRunningQA = true);

    try {
      // Run only critical checks
      final results = await QualityAssuranceService.runCategoryChecks(
        QualityCategory.functionality,
      );

      final passed = results.where((r) => r.passed).length;
      final total = results.length;

      _showSnackBar(
        'Quick check: $passed/$total critical checks passed',
        passed == total ? Colors.green : Colors.orange,
      );
    } catch (e) {
      _showSnackBar('Quick check failed: $e', Colors.red);
    } finally {
      setState(() => _isRunningQA = false);
    }
  }

  void _showImprovements() {
    final suggestions = QualityAssuranceService.getImprovementSuggestions();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Improvement Suggestions'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: suggestions.length,
            itemBuilder: (context, index) {
              return ListTile(
                leading: const Icon(Icons.lightbulb_outline),
                title: Text(suggestions[index]),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  String _getCategoryName(QualityCategory category) {
    switch (category) {
      case QualityCategory.functionality:
        return 'Functionality';
      case QualityCategory.performance:
        return 'Performance';
      case QualityCategory.usability:
        return 'Usability';
      case QualityCategory.reliability:
        return 'Reliability';
      case QualityCategory.security:
        return 'Security';
      case QualityCategory.compatibility:
        return 'Compatibility';
    }
  }

  Color _getCategoryColor(QualityCategory category) {
    switch (category) {
      case QualityCategory.functionality:
        return Colors.blue;
      case QualityCategory.performance:
        return Colors.green;
      case QualityCategory.usability:
        return Colors.purple;
      case QualityCategory.reliability:
        return Colors.orange;
      case QualityCategory.security:
        return Colors.red;
      case QualityCategory.compatibility:
        return Colors.teal;
    }
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
