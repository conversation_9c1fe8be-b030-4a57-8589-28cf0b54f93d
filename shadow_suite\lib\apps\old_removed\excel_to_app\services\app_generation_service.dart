import 'dart:async';
import 'package:flutter/material.dart';
import 'chart_visualization_service.dart';
import 'performance_optimizer.dart';
import '../models/app_generation_models.dart';

/// Comprehensive app generation service for Excel-to-App builder
class AppGenerationService {
  static bool _isInitialized = false;
  static final Map<String, GeneratedApp> _generatedApps = {};
  static final List<AppTemplate> _appTemplates = [];
  static final Map<String, AppComponent> _componentLibrary = {};

  /// Initialize the app generation service
  static void initialize() {
    if (_isInitialized) return;

    _createAppTemplates();
    _createComponentLibrary();
    _isInitialized = true;
  }

  /// Generate complete app from Excel data and configuration
  static Future<GeneratedApp> generateApp({
    required String appName,
    required List<List<dynamic>> data,
    required AppConfiguration configuration,
  }) async {
    return PerformanceOptimizer.measureAsync('generate_app', () async {
      final appId = DateTime.now().millisecondsSinceEpoch.toString();

      // Analyze data structure
      final dataAnalysis = _analyzeDataStructure(data);

      // Generate screens based on app type
      final screens = await _generateScreens(
        configuration.appType,
        data,
        dataAnalysis,
        configuration,
      );

      // Generate navigation structure
      final navigation = _generateNavigation(
        screens,
        configuration.layoutConfig,
      );

      // Generate data models
      final dataModels = _generateDataModels(dataAnalysis);

      // Generate business logic
      final businessLogic = _generateBusinessLogic(
        data,
        dataAnalysis,
        configuration,
      );

      // Generate UI components
      final uiComponents = _generateUIComponents(screens, configuration);

      // Create app package
      final appPackage = AppPackage(
        id: appId,
        name: appName,
        screens: screens,
        navigation: navigation,
        dataModels: dataModels,
        businessLogic: businessLogic,
        uiComponents: uiComponents,
        configuration: configuration,
      );

      final generatedApp = GeneratedApp(
        id: appId,
        name: appName,
        description: 'Generated from Excel data with ${data.length} rows',
        appPackage: appPackage,
        sourceData: data,
        configuration: configuration,
        createdAt: DateTime.now(),
        status: AppStatus.generated,
      );

      _generatedApps[appId] = generatedApp;
      return generatedApp;
    });
  }

  /// Get available app templates
  static List<AppTemplate> get appTemplates => List.unmodifiable(_appTemplates);

  /// Get component library
  static Map<String, AppComponent> get componentLibrary =>
      Map.unmodifiable(_componentLibrary);

  /// Get generated apps
  static List<GeneratedApp> get generatedApps => _generatedApps.values.toList();

  /// Deploy generated app
  static Future<DeploymentResult> deployApp(
    String appId,
    DeploymentTarget target,
  ) async {
    return PerformanceOptimizer.measureAsync('deploy_app', () async {
      final app = _generatedApps[appId];
      if (app == null) {
        throw ArgumentError('App not found: $appId');
      }

      // Generate deployment package
      final deploymentPackage = await _createDeploymentPackage(app, target);

      // Execute deployment
      final deploymentResult = await _executeDeployment(
        deploymentPackage,
        target,
      );

      // Update app status
      app.status = deploymentResult.success
          ? AppStatus.deployed
          : AppStatus.failed;

      return deploymentResult;
    });
  }

  /// Export app source code
  static Future<ExportResult> exportAppSource(
    String appId,
    ExportFormat format,
  ) async {
    return PerformanceOptimizer.measureAsync('export_app_source', () async {
      final app = _generatedApps[appId];
      if (app == null) {
        throw ArgumentError('App not found: $appId');
      }

      final sourceCode = await _generateSourceCode(app, format);

      return ExportResult(
        success: true,
        format: format,
        data: sourceCode,
        fileName: '${app.name}_source.${format.extension}',
        size: sourceCode.length,
      );
    });
  }

  /// Preview generated app
  static Widget previewApp(String appId) {
    final app = _generatedApps[appId];
    if (app == null) {
      return const Center(child: Text('App not found'));
    }

    return AppPreviewWidget(app: app);
  }

  // Private methods
  static void _createAppTemplates() {
    _appTemplates.addAll([
      AppTemplate(
        id: 'data_table',
        name: 'Data Table App',
        description: 'Searchable and sortable data table with filtering',
        icon: Icons.table_chart,
        appType: AppType.dataTable,
        features: [
          AppFeature.dataDisplay,
          AppFeature.search,
          AppFeature.sorting,
          AppFeature.filtering,
          AppFeature.pagination,
        ],
        estimatedComplexity: AppComplexity.simple,
      ),
      AppTemplate(
        id: 'dashboard',
        name: 'Analytics Dashboard',
        description: 'Interactive dashboard with charts and KPIs',
        icon: Icons.dashboard,
        appType: AppType.dashboard,
        features: [
          AppFeature.charts,
          AppFeature.kpis,
          AppFeature.filtering,
          AppFeature.realTimeUpdates,
          AppFeature.export,
        ],
        estimatedComplexity: AppComplexity.medium,
      ),
      AppTemplate(
        id: 'form_app',
        name: 'Data Entry Form',
        description: 'Form-based data entry with validation',
        icon: Icons.edit_note,
        appType: AppType.form,
        features: [
          AppFeature.dataEntry,
          AppFeature.validation,
          AppFeature.save,
          AppFeature.crud,
        ],
        estimatedComplexity: AppComplexity.simple,
      ),
      AppTemplate(
        id: 'report_app',
        name: 'Report Generator',
        description: 'Generate and export detailed reports',
        icon: Icons.assessment,
        appType: AppType.report,
        features: [
          AppFeature.reportGeneration,
          AppFeature.export,
          AppFeature.scheduling,
          AppFeature.templates,
        ],
        estimatedComplexity: AppComplexity.complex,
      ),
      AppTemplate(
        id: 'calculator',
        name: 'Formula Calculator',
        description: 'Interactive calculator with Excel formulas',
        icon: Icons.calculate,
        appType: AppType.calculator,
        features: [
          AppFeature.formulas,
          AppFeature.calculations,
          AppFeature.history,
          AppFeature.export,
        ],
        estimatedComplexity: AppComplexity.medium,
      ),
    ]);
  }

  static void _createComponentLibrary() {
    _componentLibrary.addAll({
      'data_table': AppComponent(
        id: 'data_table',
        name: 'Data Table',
        description: 'Sortable and filterable data table',
        category: ComponentCategory.display,
        widget: (data) => DataTableComponent(data: data),
      ),
      'chart_line': AppComponent(
        id: 'chart_line',
        name: 'Line Chart',
        description: 'Line chart for trend visualization',
        category: ComponentCategory.chart,
        widget: (data) => LineChartComponent(data: data),
      ),
      'chart_bar': AppComponent(
        id: 'chart_bar',
        name: 'Bar Chart',
        description: 'Bar chart for category comparison',
        category: ComponentCategory.chart,
        widget: (data) => BarChartComponent(data: data),
      ),
      'kpi_card': AppComponent(
        id: 'kpi_card',
        name: 'KPI Card',
        description: 'Key performance indicator display',
        category: ComponentCategory.display,
        widget: (data) => KPICardComponent(data: data),
      ),
      'input_form': AppComponent(
        id: 'input_form',
        name: 'Input Form',
        description: 'Data entry form with validation',
        category: ComponentCategory.input,
        widget: (data) => InputFormComponent(data: data),
      ),
      'filter_panel': AppComponent(
        id: 'filter_panel',
        name: 'Filter Panel',
        description: 'Advanced filtering controls',
        category: ComponentCategory.control,
        widget: (data) => FilterPanelComponent(data: data),
      ),
    });
  }

  static DataAnalysis _analyzeDataStructure(List<List<dynamic>> data) {
    if (data.isEmpty) {
      return DataAnalysis(
        rowCount: 0,
        columnCount: 0,
        hasHeaders: false,
        columnTypes: [],
        dataTypes: {},
        patterns: [],
      );
    }

    final rowCount = data.length;
    final columnCount = data.first.length;
    final hasHeaders = _detectHeaders(data);
    final columnTypes = _detectColumnTypes(data);
    final dataTypes = _analyzeDataTypes(data);
    final patterns = _detectPatterns(data);

    return DataAnalysis(
      rowCount: rowCount,
      columnCount: columnCount,
      hasHeaders: hasHeaders,
      columnTypes: columnTypes,
      dataTypes: dataTypes,
      patterns: patterns,
    );
  }

  static bool _detectHeaders(List<List<dynamic>> data) {
    if (data.length < 2) return false;

    final firstRow = data.first;
    final secondRow = data[1];

    // Check if first row contains text and second row contains numbers
    bool firstRowText = firstRow.every(
      (cell) => cell is String && double.tryParse(cell.toString()) == null,
    );
    bool secondRowNumbers = secondRow.any(
      (cell) => double.tryParse(cell.toString()) != null,
    );

    return firstRowText && secondRowNumbers;
  }

  static List<DataColumnType> _detectColumnTypes(List<List<dynamic>> data) {
    if (data.isEmpty) return [];

    final columnCount = data.first.length;
    final types = <DataColumnType>[];

    for (int col = 0; col < columnCount; col++) {
      final columnData = data
          .skip(1)
          .map((row) => row.length > col ? row[col] : null)
          .toList();
      types.add(_detectColumnType(columnData));
    }

    return types;
  }

  static DataColumnType _detectColumnType(List<dynamic> columnData) {
    final nonNullData = columnData
        .where((value) => value != null && value.toString().isNotEmpty)
        .toList();

    if (nonNullData.isEmpty) return DataColumnType.text;

    // Check for numbers
    if (nonNullData.every(
      (value) => double.tryParse(value.toString()) != null,
    )) {
      if (nonNullData.every(
        (value) => int.tryParse(value.toString()) != null,
      )) {
        return DataColumnType.integer;
      }
      return DataColumnType.decimal;
    }

    // Check for dates
    if (nonNullData.every(
      (value) => DateTime.tryParse(value.toString()) != null,
    )) {
      return DataColumnType.date;
    }

    // Check for booleans
    if (nonNullData.every(
      (value) => [
        'true',
        'false',
        '1',
        '0',
        'yes',
        'no',
      ].contains(value.toString().toLowerCase()),
    )) {
      return DataColumnType.boolean;
    }

    return DataColumnType.text;
  }

  static Map<DataColumnType, int> _analyzeDataTypes(List<List<dynamic>> data) {
    final types = <DataColumnType, int>{};

    for (final row in data.skip(1)) {
      for (final cell in row) {
        final type = _detectColumnType([cell]);
        types[type] = (types[type] ?? 0) + 1;
      }
    }

    return types;
  }

  static List<DataPattern> _detectPatterns(List<List<dynamic>> data) {
    final patterns = <DataPattern>[];

    // Detect time series pattern
    if (_isTimeSeries(data)) {
      patterns.add(DataPattern.timeSeries);
    }

    // Detect categorical data
    if (_hasCategoricalData(data)) {
      patterns.add(DataPattern.categorical);
    }

    // Detect hierarchical structure
    if (_hasHierarchicalStructure(data)) {
      patterns.add(DataPattern.hierarchical);
    }

    // Detect financial data
    if (_hasFinancialData(data)) {
      patterns.add(DataPattern.financial);
    }

    return patterns;
  }

  static bool _isTimeSeries(List<List<dynamic>> data) {
    if (data.length < 2) return false;

    // Check if first column contains dates
    final firstColumn = data
        .skip(1)
        .map((row) => row.isNotEmpty ? row.first : null)
        .toList();
    return firstColumn.any(
      (value) => DateTime.tryParse(value.toString()) != null,
    );
  }

  static bool _hasCategoricalData(List<List<dynamic>> data) {
    if (data.length < 2) return false;

    // Check for repeated string values
    final stringValues = <String>{};
    for (final row in data.skip(1)) {
      for (final cell in row) {
        if (cell is String && double.tryParse(cell) == null) {
          stringValues.add(cell);
        }
      }
    }

    return stringValues.length <
        data.length * 0.5; // Less than 50% unique values
  }

  static bool _hasHierarchicalStructure(List<List<dynamic>> data) {
    if (data.length < 2) return false;

    // Check for parent-child relationships or indentation
    for (final row in data.skip(1)) {
      if (row.isNotEmpty) {
        final firstCell = row.first.toString();
        if (firstCell.startsWith('  ') ||
            firstCell.contains('->') ||
            firstCell.contains('/')) {
          return true;
        }
      }
    }

    return false;
  }

  static bool _hasFinancialData(List<List<dynamic>> data) {
    if (data.isEmpty) return false;

    // Check for financial keywords in headers
    final headers = data.first
        .map((cell) => cell.toString().toLowerCase())
        .toList();
    final financialKeywords = [
      'price',
      'cost',
      'revenue',
      'profit',
      'amount',
      'balance',
      'total',
      'sum',
    ];

    return headers.any(
      (header) => financialKeywords.any((keyword) => header.contains(keyword)),
    );
  }

  static Future<List<AppScreen>> _generateScreens(
    AppType appType,
    List<List<dynamic>> data,
    DataAnalysis analysis,
    AppConfiguration config,
  ) async {
    final screens = <AppScreen>[];

    switch (appType) {
      case AppType.dataTable:
        screens.addAll(await _generateDataTableScreens(data, analysis, config));
        break;
      case AppType.dashboard:
        screens.addAll(await _generateDashboardScreens(data, analysis, config));
        break;
      case AppType.form:
        screens.addAll(await _generateFormScreens(data, analysis, config));
        break;
      case AppType.report:
        screens.addAll(await _generateReportScreens(data, analysis, config));
        break;
      case AppType.calculator:
        screens.addAll(
          await _generateCalculatorScreens(data, analysis, config),
        );
        break;
    }

    return screens;
  }

  static Future<List<AppScreen>> _generateDataTableScreens(
    List<List<dynamic>> data,
    DataAnalysis analysis,
    AppConfiguration config,
  ) async {
    return [
      AppScreen(
        id: 'main_table',
        name: 'Data Table',
        route: '/table',
        screenType: ScreenType.dataTable,
        components: [
          ScreenComponent(
            id: 'data_table',
            type: 'data_table',
            data: data,
            configuration: {'sortable': true, 'filterable': true},
          ),
        ],
      ),
      AppScreen(
        id: 'details',
        name: 'Details',
        route: '/details',
        screenType: ScreenType.detail,
        components: [
          ScreenComponent(
            id: 'detail_view',
            type: 'detail_view',
            data: [],
            configuration: {'editable': true},
          ),
        ],
      ),
    ];
  }

  static Future<List<AppScreen>> _generateDashboardScreens(
    List<List<dynamic>> data,
    DataAnalysis analysis,
    AppConfiguration config,
  ) async {
    final charts = _generateChartsForData(data, analysis);

    return [
      AppScreen(
        id: 'dashboard',
        name: 'Dashboard',
        route: '/dashboard',
        screenType: ScreenType.dashboard,
        components: [
          ScreenComponent(
            id: 'kpi_section',
            type: 'kpi_card',
            data: _generateKPIData(data, analysis),
            configuration: {'layout': 'grid'},
          ),
          ...charts.map(
            (chart) => ScreenComponent(
              id: 'chart_${chart.id}',
              type: chart.type.name,
              data: chart.data,
              configuration: chart.configuration,
            ),
          ),
        ],
      ),
    ];
  }

  static Future<List<AppScreen>> _generateFormScreens(
    List<List<dynamic>> data,
    DataAnalysis analysis,
    AppConfiguration config,
  ) async {
    return [
      AppScreen(
        id: 'form_entry',
        name: 'Data Entry',
        route: '/entry',
        screenType: ScreenType.form,
        components: [
          ScreenComponent(
            id: 'input_form',
            type: 'input_form',
            data: _generateFormFields(analysis),
            configuration: {'validation': true, 'autosave': true},
          ),
        ],
      ),
    ];
  }

  static Future<List<AppScreen>> _generateReportScreens(
    List<List<dynamic>> data,
    DataAnalysis analysis,
    AppConfiguration config,
  ) async {
    return [
      AppScreen(
        id: 'report_generator',
        name: 'Reports',
        route: '/reports',
        screenType: ScreenType.report,
        components: [
          ScreenComponent(
            id: 'report_builder',
            type: 'report_builder',
            data: data,
            configuration: {'templates': true, 'export': true},
          ),
        ],
      ),
    ];
  }

  static Future<List<AppScreen>> _generateCalculatorScreens(
    List<List<dynamic>> data,
    DataAnalysis analysis,
    AppConfiguration config,
  ) async {
    return [
      AppScreen(
        id: 'calculator',
        name: 'Calculator',
        route: '/calculator',
        screenType: ScreenType.calculator,
        components: [
          ScreenComponent(
            id: 'formula_calculator',
            type: 'formula_calculator',
            data: _generateCalculatorData(data, analysis),
            configuration: {'formulas': true, 'history': true},
          ),
        ],
      ),
    ];
  }

  static AppNavigation _generateNavigation(
    List<AppScreen> screens,
    LayoutConfiguration layoutConfig,
  ) {
    final navItems = screens
        .map(
          (screen) => NavigationItem(
            id: screen.id,
            label: screen.name,
            route: screen.route,
            icon: _getScreenIcon(screen.screenType),
          ),
        )
        .toList();

    return AppNavigation(
      type: NavigationType.bottomTabs, // Default navigation type
      items: navItems,
    );
  }

  static IconData _getScreenIcon(ScreenType type) {
    switch (type) {
      case ScreenType.dataTable:
        return Icons.table_chart;
      case ScreenType.dashboard:
        return Icons.dashboard;
      case ScreenType.form:
        return Icons.edit_note;
      case ScreenType.report:
        return Icons.assessment;
      case ScreenType.calculator:
        return Icons.calculate;
      case ScreenType.detail:
        return Icons.info;
    }
  }

  static List<GeneratedChart> _generateChartsForData(
    List<List<dynamic>> data,
    DataAnalysis analysis,
  ) {
    final charts = <GeneratedChart>[];

    // Generate appropriate charts based on data patterns
    if (analysis.patterns.contains(DataPattern.timeSeries)) {
      charts.add(
        GeneratedChart(
          id: 'time_series',
          type: ChartType.line,
          data: data,
          configuration: {'title': 'Time Series Analysis'},
        ),
      );
    }

    if (analysis.patterns.contains(DataPattern.categorical)) {
      charts.add(
        GeneratedChart(
          id: 'category_breakdown',
          type: ChartType.bar,
          data: data,
          configuration: {'title': 'Category Breakdown'},
        ),
      );
    }

    return charts;
  }

  static List<List<dynamic>> _generateKPIData(
    List<List<dynamic>> data,
    DataAnalysis analysis,
  ) {
    final kpis = <List<dynamic>>[];

    if (data.length > 1) {
      // Calculate basic KPIs
      final numericColumns = <int>[];
      for (int i = 0; i < analysis.columnTypes.length; i++) {
        if (analysis.columnTypes[i] == DataColumnType.integer ||
            analysis.columnTypes[i] == DataColumnType.decimal) {
          numericColumns.add(i);
        }
      }

      for (final col in numericColumns) {
        final values = data
            .skip(1)
            .map((row) => double.tryParse(row[col].toString()) ?? 0.0)
            .toList();
        final sum = values.fold<double>(0, (a, b) => a + b);
        final avg = sum / values.length;
        final max = values.fold<double>(values.first, (a, b) => a > b ? a : b);
        final min = values.fold<double>(values.first, (a, b) => a < b ? a : b);

        kpis.addAll([
          ['Total ${data.first[col]}', sum.toStringAsFixed(2)],
          ['Average ${data.first[col]}', avg.toStringAsFixed(2)],
          ['Max ${data.first[col]}', max.toStringAsFixed(2)],
          ['Min ${data.first[col]}', min.toStringAsFixed(2)],
        ]);
      }
    }

    return kpis;
  }

  static List<List<dynamic>> _generateFormFields(DataAnalysis analysis) {
    final fields = <List<dynamic>>[];

    for (int i = 0; i < analysis.columnTypes.length; i++) {
      final type = analysis.columnTypes[i];
      fields.add([
        'field_$i',
        _getFieldType(type),
        true, // required
        _getFieldValidation(type),
      ]);
    }

    return fields;
  }

  static String _getFieldType(DataColumnType type) {
    switch (type) {
      case DataColumnType.integer:
        return 'number';
      case DataColumnType.decimal:
        return 'decimal';
      case DataColumnType.date:
        return 'date';
      case DataColumnType.boolean:
        return 'checkbox';
      case DataColumnType.text:
        return 'text';
    }
  }

  static Map<String, dynamic> _getFieldValidation(DataColumnType type) {
    switch (type) {
      case DataColumnType.integer:
        return {'min': 0, 'max': 999999};
      case DataColumnType.decimal:
        return {'min': 0.0, 'max': 999999.99};
      case DataColumnType.date:
        return {'format': 'yyyy-MM-dd'};
      case DataColumnType.boolean:
        return {};
      case DataColumnType.text:
        return {'minLength': 1, 'maxLength': 255};
    }
  }

  static List<List<dynamic>> _generateCalculatorData(
    List<List<dynamic>> data,
    DataAnalysis analysis,
  ) {
    final calculatorData = <List<dynamic>>[];

    // Generate common formulas based on data
    if (analysis.columnTypes.contains(DataColumnType.integer) ||
        analysis.columnTypes.contains(DataColumnType.decimal)) {
      calculatorData.addAll([
        ['SUM', 'Calculate sum of values'],
        ['AVERAGE', 'Calculate average of values'],
        ['COUNT', 'Count non-empty values'],
        ['MAX', 'Find maximum value'],
        ['MIN', 'Find minimum value'],
      ]);
    }

    if (analysis.patterns.contains(DataPattern.financial)) {
      calculatorData.addAll([
        ['PMT', 'Calculate payment amount'],
        ['PV', 'Calculate present value'],
        ['FV', 'Calculate future value'],
        ['IRR', 'Calculate internal rate of return'],
      ]);
    }

    return calculatorData;
  }

  static List<DataModel> _generateDataModels(DataAnalysis analysis) {
    return [
      DataModel(
        name: 'MainData',
        fields: analysis.columnTypes.asMap().entries.map((entry) {
          return DataField(
            name: 'field_${entry.key}',
            type: entry.value,
            required: true,
          );
        }).toList(),
      ),
    ];
  }

  static BusinessLogic _generateBusinessLogic(
    List<List<dynamic>> data,
    DataAnalysis analysis,
    AppConfiguration config,
  ) {
    final operations = <BusinessOperation>[];

    // Generate CRUD operations
    operations.addAll([
      BusinessOperation(
        name: 'create',
        type: OperationType.create,
        implementation: 'Create new record with validation',
      ),
      BusinessOperation(
        name: 'read',
        type: OperationType.read,
        implementation: 'Fetch records with filtering and pagination',
      ),
      BusinessOperation(
        name: 'update',
        type: OperationType.update,
        implementation: 'Update existing record with validation',
      ),
      BusinessOperation(
        name: 'delete',
        type: OperationType.delete,
        implementation: 'Delete record with confirmation',
      ),
    ]);

    // Generate calculation operations for numeric data
    if (analysis.columnTypes.contains(DataColumnType.integer) ||
        analysis.columnTypes.contains(DataColumnType.decimal)) {
      operations.addAll([
        BusinessOperation(
          name: 'calculate_sum',
          type: OperationType.calculation,
          implementation: 'Calculate sum of numeric columns',
        ),
        BusinessOperation(
          name: 'calculate_average',
          type: OperationType.calculation,
          implementation: 'Calculate average of numeric columns',
        ),
      ]);
    }

    return BusinessLogic(operations: operations);
  }

  static List<UIComponent> _generateUIComponents(
    List<AppScreen> screens,
    AppConfiguration config,
  ) {
    final components = <UIComponent>[];

    for (final screen in screens) {
      for (final component in screen.components) {
        components.add(
          UIComponent(
            id: component.id,
            type: component.type,
            properties: component.configuration,
          ),
        );
      }
    }

    return components;
  }

  static Future<DeploymentPackage> _createDeploymentPackage(
    GeneratedApp app,
    DeploymentTarget target,
  ) async {
    return DeploymentPackage(
      appId: app.id,
      target: target,
      files: await _generateDeploymentFiles(app, target),
      configuration: _generateDeploymentConfig(target),
    );
  }

  static Future<Map<String, String>> _generateDeploymentFiles(
    GeneratedApp app,
    DeploymentTarget target,
  ) async {
    final files = <String, String>{};

    switch (target) {
      case DeploymentTarget.web:
        files['index.html'] = _generateWebHTML(app);
        files['main.dart'] = _generateWebDart(app);
        files['pubspec.yaml'] = _generatePubspec(app);
        break;
      case DeploymentTarget.android:
        files['MainActivity.kt'] = _generateAndroidActivity(app);
        files['build.gradle'] = _generateAndroidGradle(app);
        break;
      case DeploymentTarget.ios:
        files['AppDelegate.swift'] = _generateIOSDelegate(app);
        files['Info.plist'] = _generateIOSPlist(app);
        break;
    }

    return files;
  }

  static Map<String, dynamic> _generateDeploymentConfig(
    DeploymentTarget target,
  ) {
    switch (target) {
      case DeploymentTarget.web:
        return {'renderer': 'html', 'minify': true};
      case DeploymentTarget.android:
        return {'minSdkVersion': 21, 'targetSdkVersion': 33};
      case DeploymentTarget.ios:
        return {'minimumOSVersion': '11.0', 'bitcode': false};
    }
  }

  static Future<DeploymentResult> _executeDeployment(
    DeploymentPackage package,
    DeploymentTarget target,
  ) async {
    // Simulate deployment process
    await Future.delayed(const Duration(seconds: 2));

    return DeploymentResult(
      success: true,
      deploymentUrl: _generateDeploymentUrl(package.appId, target),
      logs: [
        'Deployment started',
        'Files uploaded',
        'Build completed',
        'Deployment successful',
      ],
    );
  }

  static String _generateDeploymentUrl(String appId, DeploymentTarget target) {
    switch (target) {
      case DeploymentTarget.web:
        return 'https://$appId.web.app';
      case DeploymentTarget.android:
        return 'https://play.google.com/store/apps/details?id=com.app.$appId';
      case DeploymentTarget.ios:
        return 'https://apps.apple.com/app/id$appId';
    }
  }

  static Future<List<int>> _generateSourceCode(
    GeneratedApp app,
    ExportFormat format,
  ) async {
    final sourceCode = _generateAppSourceCode(app, format);
    return sourceCode.codeUnits;
  }

  static String _generateAppSourceCode(GeneratedApp app, ExportFormat format) {
    switch (format) {
      case ExportFormat.flutter:
        return _generateFlutterCode(app);
      case ExportFormat.react:
        return _generateReactCode(app);
      case ExportFormat.vue:
        return _generateVueCode(app);
      case ExportFormat.angular:
        return _generateAngularCode(app);
    }
  }

  static String _generateFlutterCode(GeneratedApp app) {
    return '''
import 'package:flutter/material.dart';

void main() {
  runApp(${app.name}App());
}

class ${app.name}App extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '${app.name}',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: MainScreen(),
    );
  }
}

class MainScreen extends StatefulWidget {
  @override
  _MainScreenState createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('${app.name}')),
      body: Center(
        child: Text('Generated App Content'),
      ),
    );
  }
}
''';
  }

  static String _generateReactCode(GeneratedApp app) {
    return '''
import React from 'react';
import './App.css';

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>${app.name}</h1>
        <p>Generated App Content</p>
      </header>
    </div>
  );
}

export default App;
''';
  }

  static String _generateVueCode(GeneratedApp app) {
    return '''
<template>
  <div id="app">
    <header>
      <h1>${app.name}</h1>
    </header>
    <main>
      <p>Generated App Content</p>
    </main>
  </div>
</template>

<script>
export default {
  name: 'App',
  data() {
    return {
      title: '${app.name}'
    }
  }
}
</script>
''';
  }

  static String _generateAngularCode(GeneratedApp app) {
    return '''
import { Component } from '@angular/core';

@Component({
  selector: 'app-root',
  template: `
    <div>
      <h1>\${app.name}</h1>
      <p>Generated App Content</p>
    </div>
  `,
  styleUrls: ['./app.component.css']
})
export class AppComponent {
  title = '${app.name}';
}
''';
  }

  static String _generateWebHTML(GeneratedApp app) {
    return '''
<!DOCTYPE html>
<html>
<head>
    <title>${app.name}</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
    <div id="app">
        <h1>${app.name}</h1>
        <p>Generated Web App</p>
    </div>
    <script src="main.dart.js"></script>
</body>
</html>
''';
  }

  static String _generateWebDart(GeneratedApp app) {
    return '''
import 'dart:html';

void main() {
  querySelector('#app')?.text = '${app.name} - Generated App';
}
''';
  }

  static String _generatePubspec(GeneratedApp app) {
    return '''
name: ${app.name.toLowerCase().replaceAll(' ', '_')}
description: Generated Flutter app from Excel data
version: 1.0.0+1

environment:
  sdk: ">=2.17.0 <4.0.0"
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0

flutter:
  uses-material-design: true
''';
  }

  static String _generateAndroidActivity(GeneratedApp app) {
    return '''
package com.app.${app.name.toLowerCase()}

import io.flutter.embedding.android.FlutterActivity

class MainActivity: FlutterActivity() {
}
''';
  }

  static String _generateAndroidGradle(GeneratedApp app) {
    return '''
android {
    compileSdkVersion 33

    defaultConfig {
        applicationId "com.app.${app.name.toLowerCase()}"
        minSdkVersion 21
        targetSdkVersion 33
        versionCode 1
        versionName "1.0"
    }
}
''';
  }

  static String _generateIOSDelegate(GeneratedApp app) {
    return '''
import UIKit
import Flutter

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
''';
  }

  static String _generateIOSPlist(GeneratedApp app) {
    return '''
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleName</key>
    <string>${app.name}</string>
    <key>CFBundleVersion</key>
    <string>1.0</string>
</dict>
</plist>
''';
  }
}

/// Data classes and enums for app generation
class AppTemplate {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final AppType appType;
  final List<AppFeature> features;
  final AppComplexity estimatedComplexity;

  const AppTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.appType,
    required this.features,
    required this.estimatedComplexity,
  });
}

class AppComponent {
  final String id;
  final String name;
  final String description;
  final ComponentCategory category;
  final Widget Function(dynamic data) widget;

  const AppComponent({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.widget,
  });
}

class GeneratedApp {
  final String id;
  final String name;
  final String description;
  final AppPackage appPackage;
  final List<List<dynamic>> sourceData;
  final AppConfiguration configuration;
  final DateTime createdAt;
  AppStatus status;

  GeneratedApp({
    required this.id,
    required this.name,
    required this.description,
    required this.appPackage,
    required this.sourceData,
    required this.configuration,
    required this.createdAt,
    required this.status,
  });
}

class AppConfiguration {
  final AppType appType;
  final LayoutConfiguration layoutConfig;
  final Map<String, dynamic> customSettings;

  const AppConfiguration({
    required this.appType,
    required this.layoutConfig,
    this.customSettings = const {},
  });
}

class DataAnalysis {
  final int rowCount;
  final int columnCount;
  final bool hasHeaders;
  final List<DataColumnType> columnTypes;
  final Map<DataColumnType, int> dataTypes;
  final List<DataPattern> patterns;

  const DataAnalysis({
    required this.rowCount,
    required this.columnCount,
    required this.hasHeaders,
    required this.columnTypes,
    required this.dataTypes,
    required this.patterns,
  });
}

class AppPackage {
  final String id;
  final String name;
  final List<AppScreen> screens;
  final AppNavigation navigation;
  final List<DataModel> dataModels;
  final BusinessLogic businessLogic;
  final List<UIComponent> uiComponents;
  final AppConfiguration configuration;

  const AppPackage({
    required this.id,
    required this.name,
    required this.screens,
    required this.navigation,
    required this.dataModels,
    required this.businessLogic,
    required this.uiComponents,
    required this.configuration,
  });
}

class AppScreen {
  final String id;
  final String name;
  final String route;
  final ScreenType screenType;
  final List<ScreenComponent> components;

  const AppScreen({
    required this.id,
    required this.name,
    required this.route,
    required this.screenType,
    required this.components,
  });
}

class ScreenComponent {
  final String id;
  final String type;
  final dynamic data;
  final Map<String, dynamic> configuration;

  const ScreenComponent({
    required this.id,
    required this.type,
    required this.data,
    required this.configuration,
  });
}

class AppNavigation {
  final NavigationType type;
  final List<NavigationItem> items;

  const AppNavigation({required this.type, required this.items});
}

class NavigationItem {
  final String id;
  final String label;
  final String route;
  final IconData icon;

  const NavigationItem({
    required this.id,
    required this.label,
    required this.route,
    required this.icon,
  });
}

class GeneratedChart {
  final String id;
  final ChartType type;
  final dynamic data;
  final Map<String, dynamic> configuration;

  const GeneratedChart({
    required this.id,
    required this.type,
    required this.data,
    required this.configuration,
  });
}

class DataModel {
  final String name;
  final List<DataField> fields;

  const DataModel({required this.name, required this.fields});
}

class DataField {
  final String name;
  final DataColumnType type;
  final bool required;

  const DataField({
    required this.name,
    required this.type,
    required this.required,
  });
}

class BusinessLogic {
  final List<BusinessOperation> operations;

  const BusinessLogic({required this.operations});
}

class BusinessOperation {
  final String name;
  final OperationType type;
  final String implementation;

  const BusinessOperation({
    required this.name,
    required this.type,
    required this.implementation,
  });
}

class UIComponent {
  final String id;
  final String type;
  final Map<String, dynamic> properties;

  const UIComponent({
    required this.id,
    required this.type,
    required this.properties,
  });
}

class DeploymentResult {
  final bool success;
  final String deploymentUrl;
  final List<String> logs;

  const DeploymentResult({
    required this.success,
    required this.deploymentUrl,
    required this.logs,
  });
}

class DeploymentPackage {
  final String appId;
  final DeploymentTarget target;
  final Map<String, String> files;
  final Map<String, dynamic> configuration;

  const DeploymentPackage({
    required this.appId,
    required this.target,
    required this.files,
    required this.configuration,
  });
}

class ExportResult {
  final bool success;
  final ExportFormat format;
  final List<int> data;
  final String fileName;
  final int size;

  const ExportResult({
    required this.success,
    required this.format,
    required this.data,
    required this.fileName,
    required this.size,
  });
}

class AppPreviewWidget extends StatelessWidget {
  final GeneratedApp app;

  const AppPreviewWidget({super.key, required this.app});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(app.name),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.phone_android, size: 64, color: Colors.grey[600]),
            const SizedBox(height: 16),
            Text(
              'App Preview',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              app.description,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {},
              icon: const Icon(Icons.play_arrow),
              label: const Text('Launch Preview'),
            ),
          ],
        ),
      ),
    );
  }
}

// Component widgets
class DataTableComponent extends StatelessWidget {
  final dynamic data;

  const DataTableComponent({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Text('Data Table Component'),
    );
  }
}

class LineChartComponent extends StatelessWidget {
  final dynamic data;

  const LineChartComponent({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Text('Line Chart Component'),
    );
  }
}

class BarChartComponent extends StatelessWidget {
  final dynamic data;

  const BarChartComponent({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Text('Bar Chart Component'),
    );
  }
}

class KPICardComponent extends StatelessWidget {
  final dynamic data;

  const KPICardComponent({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Text('KPI Card Component'),
    );
  }
}

class InputFormComponent extends StatelessWidget {
  final dynamic data;

  const InputFormComponent({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Text('Input Form Component'),
    );
  }
}

class FilterPanelComponent extends StatelessWidget {
  final dynamic data;

  const FilterPanelComponent({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Text('Filter Panel Component'),
    );
  }
}

/// Enums
enum AppType { dataTable, dashboard, form, report, calculator }

enum AppFeature {
  dataDisplay,
  search,
  sorting,
  filtering,
  pagination,
  charts,
  kpis,
  realTimeUpdates,
  export,
  dataEntry,
  validation,
  save,
  crud,
  reportGeneration,
  scheduling,
  templates,
  formulas,
  calculations,
  history,
}

enum AppComplexity { simple, medium, complex }

enum ComponentCategory { display, chart, input, control }

enum AppStatus { generated, deployed, failed }

enum ScreenType { dataTable, dashboard, form, report, calculator, detail }

enum NavigationType { sidebar, bottomTabs, topTabs }

enum DataColumnType { text, integer, decimal, date, boolean }

enum DataPattern { timeSeries, categorical, hierarchical, financial }

enum OperationType { create, read, update, delete, calculation }

enum DeploymentTarget { web, android, ios }

enum ExportFormat { flutter, react, vue, angular }

extension ExportFormatExtension on ExportFormat {
  String get extension {
    switch (this) {
      case ExportFormat.flutter:
        return 'dart';
      case ExportFormat.react:
        return 'jsx';
      case ExportFormat.vue:
        return 'vue';
      case ExportFormat.angular:
        return 'ts';
    }
  }
}
