import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/media_models.dart';
import '../../../core/services/fullscreen_service.dart';
import '../../../core/widgets/fullscreen_controls.dart';

/// Full-featured video player with full-screen support
class VideoPlayerScreen extends ConsumerStatefulWidget {
  final MediaFile videoFile;
  final List<MediaFile>? playlist;
  final int? initialIndex;

  const VideoPlayerScreen({
    super.key,
    required this.videoFile,
    this.playlist,
    this.initialIndex,
  });

  @override
  ConsumerState<VideoPlayerScreen> createState() => _VideoPlayerScreenState();
}

class _VideoPlayerScreenState extends ConsumerState<VideoPlayerScreen>
    with TickerProviderStateMixin {
  late AnimationController _controlsAnimationController;
  Timer? _hideControlsTimer;
  bool _isPlaying = false;
  bool _isBuffering = false;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  double _volume = 1.0;
  double _playbackSpeed = 1.0;
  bool _isMuted = false;
  int _currentIndex = 0;
  List<MediaFile> _currentPlaylist = [];

  @override
  void initState() {
    super.initState();

    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Animation setup for future use

    _currentPlaylist = widget.playlist ?? [widget.videoFile];
    _currentIndex = widget.initialIndex ?? 0;

    _initializePlayer();
    _controlsAnimationController.forward();
    _startHideControlsTimer();
  }

  @override
  void dispose() {
    _controlsAnimationController.dispose();
    _hideControlsTimer?.cancel();
    _exitFullScreenIfNeeded();
    super.dispose();
  }

  Future<void> _initializePlayer() async {
    try {
      setState(() {
        _isBuffering = true;
      });

      // Simulate video loading and get duration
      await Future.delayed(const Duration(milliseconds: 500));

      setState(() {
        _totalDuration = const Duration(
          minutes: 2,
          seconds: 30,
        ); // Mock duration
        _isBuffering = false;
      });

      // Start position updates
      _startPositionUpdates();
    } catch (e) {
      setState(() {
        _isBuffering = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load video: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _startPositionUpdates() {
    Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (!mounted || !_isPlaying) {
        timer.cancel();
        return;
      }

      setState(() {
        _currentPosition = Duration(
          milliseconds: _currentPosition.inMilliseconds + 500,
        );

        if (_currentPosition >= _totalDuration) {
          _currentPosition = _totalDuration;
          _isPlaying = false;
          _onVideoCompleted();
        }
      });
    });
  }

  void _onVideoCompleted() {
    if (_currentIndex < _currentPlaylist.length - 1) {
      _playNext();
    } else {
      // Playlist completed
      setState(() {
        _isPlaying = false;
        _currentPosition = Duration.zero;
      });
    }
  }

  void _togglePlayPause() {
    setState(() {
      _isPlaying = !_isPlaying;
    });

    if (_isPlaying) {
      _startPositionUpdates();
    }

    _showControlsTemporarily();
  }

  void _seekTo(Duration position) {
    setState(() {
      _currentPosition = position;
    });
    _showControlsTemporarily();
  }

  void _playNext() {
    if (_currentIndex < _currentPlaylist.length - 1) {
      setState(() {
        _currentIndex++;
        _currentPosition = Duration.zero;
        _isPlaying = true;
      });
      _initializePlayer();
    }
  }

  void _playPrevious() {
    if (_currentIndex > 0) {
      setState(() {
        _currentIndex--;
        _currentPosition = Duration.zero;
        _isPlaying = true;
      });
      _initializePlayer();
    }
  }

  void _toggleMute() {
    setState(() {
      _isMuted = !_isMuted;
    });
    _showControlsTemporarily();
  }

  void _setVolume(double volume) {
    setState(() {
      _volume = volume;
      _isMuted = volume == 0;
    });
  }

  void _setPlaybackSpeed(double speed) {
    setState(() {
      _playbackSpeed = speed;
    });
    _showControlsTemporarily();
  }

  void _showControlsTemporarily() {
    _controlsAnimationController.forward();
    _startHideControlsTimer();
  }

  void _hideControls() {
    _controlsAnimationController.reverse();
    _hideControlsTimer?.cancel();
  }

  void _startHideControlsTimer() {
    _hideControlsTimer?.cancel();
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      if (mounted && _isPlaying) {
        _hideControls();
      }
    });
  }

  Future<void> _toggleFullScreen() async {
    final isFullScreen = ref.read(fullScreenProvider);

    if (isFullScreen) {
      await FullScreenService.exitFullScreen();
      ref.read(fullScreenProvider.notifier).state = false;
    } else {
      await FullScreenService.enterFullScreen();
      ref.read(fullScreenProvider.notifier).state = true;
    }
  }

  Future<void> _exitFullScreenIfNeeded() async {
    final isFullScreen = ref.read(fullScreenProvider);
    if (isFullScreen) {
      await FullScreenService.exitFullScreen();
      ref.read(fullScreenProvider.notifier).state = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isFullScreen = ref.watch(fullScreenProvider);
    final currentFile = _currentPlaylist[_currentIndex];

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: isFullScreen
          ? null
          : AppBar(
              title: Text(currentFile.name),
              backgroundColor: Colors.black,
              foregroundColor: Colors.white,
              actions: [
                FullScreenToggleButton(
                  iconColor: Colors.white,
                  onPressed: _toggleFullScreen,
                ),
              ],
            ),
      body: FullScreenControls(
        topControls: [
          Text(
            currentFile.name,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
        centerControls: [
          IconButton(
            onPressed: _currentIndex > 0 ? _playPrevious : null,
            icon: const Icon(Icons.skip_previous, color: Colors.white),
            iconSize: 32,
          ),
          const SizedBox(width: 16),
          IconButton(
            onPressed: _togglePlayPause,
            icon: Icon(
              _isPlaying ? Icons.pause : Icons.play_arrow,
              color: Colors.white,
            ),
            iconSize: 48,
          ),
          const SizedBox(width: 16),
          IconButton(
            onPressed: _currentIndex < _currentPlaylist.length - 1
                ? _playNext
                : null,
            icon: const Icon(Icons.skip_next, color: Colors.white),
            iconSize: 32,
          ),
        ],
        bottomControls: [
          _buildProgressBar(),
          const SizedBox(width: 16),
          _buildVolumeControl(),
          const SizedBox(width: 16),
          _buildSpeedControl(),
        ],
        onExitFullScreen: () async {
          await FullScreenService.exitFullScreen();
          ref.read(fullScreenProvider.notifier).state = false;
        },
        onToggleFullScreen: _toggleFullScreen,
        child: _buildVideoPlayer(context),
      ),
    );
  }

  Widget _buildVideoPlayer(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black,
      child: Stack(
        children: [
          // Video content placeholder
          Center(
            child: _isBuffering
                ? const CircularProgressIndicator(color: Colors.white)
                : Container(
                    width: double.infinity,
                    height: double.infinity,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [Colors.grey[800]!, Colors.grey[900]!],
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.play_circle_outline,
                          size: 120,
                          color: Colors.white.withValues(alpha: 0.7),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _currentPlaylist[_currentIndex].name,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Video Player Implementation',
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.7),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBar() {
    return Expanded(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: Colors.red,
              inactiveTrackColor: Colors.white.withValues(alpha: 0.3),
              thumbColor: Colors.red,
              overlayColor: Colors.red.withValues(alpha: 0.2),
              trackHeight: 3,
            ),
            child: Slider(
              value: _totalDuration.inMilliseconds > 0
                  ? _currentPosition.inMilliseconds /
                        _totalDuration.inMilliseconds
                  : 0.0,
              onChanged: (value) {
                final position = Duration(
                  milliseconds: (value * _totalDuration.inMilliseconds).round(),
                );
                _seekTo(position);
              },
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatDuration(_currentPosition),
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
              Text(
                _formatDuration(_totalDuration),
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildVolumeControl() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          onPressed: _toggleMute,
          icon: Icon(
            _isMuted ? Icons.volume_off : Icons.volume_up,
            color: Colors.white,
          ),
        ),
        SizedBox(
          width: 60,
          child: SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: Colors.white,
              inactiveTrackColor: Colors.white.withValues(alpha: 0.3),
              thumbColor: Colors.white,
              trackHeight: 2,
            ),
            child: Slider(value: _isMuted ? 0 : _volume, onChanged: _setVolume),
          ),
        ),
      ],
    );
  }

  Widget _buildSpeedControl() {
    return PopupMenuButton<double>(
      icon: const Icon(Icons.speed, color: Colors.white),
      onSelected: _setPlaybackSpeed,
      itemBuilder: (context) => [
        const PopupMenuItem(value: 0.5, child: Text('0.5x')),
        const PopupMenuItem(value: 0.75, child: Text('0.75x')),
        const PopupMenuItem(value: 1.0, child: Text('1.0x')),
        const PopupMenuItem(value: 1.25, child: Text('1.25x')),
        const PopupMenuItem(value: 1.5, child: Text('1.5x')),
        const PopupMenuItem(value: 2.0, child: Text('2.0x')),
      ],
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.speed, color: Colors.white),
          const SizedBox(width: 4),
          Text(
            '${_playbackSpeed}x',
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
          '${minutes.toString().padLeft(2, '0')}:'
          '${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
          '${seconds.toString().padLeft(2, '0')}';
    }
  }
}
