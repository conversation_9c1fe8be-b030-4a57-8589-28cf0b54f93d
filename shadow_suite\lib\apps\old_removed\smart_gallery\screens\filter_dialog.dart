import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/smart_gallery_models.dart';
import '../../../core/widgets/unified_components.dart';

/// Advanced filter dialog for SmartGallery+
class FilterDialog extends ConsumerStatefulWidget {
  final SmartGalleryFilter? initialFilter;

  const FilterDialog({super.key, this.initialFilter});

  @override
  ConsumerState<FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends ConsumerState<FilterDialog>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Filter state
  MediaType? _selectedMediaType;
  // These fields are used for initialization and clearing filters
  List<String> _selectedTags = [];
  List<String> _selectedPeople = [];
  bool? _hasFaces;
  bool? _hasLocation;
  bool? _hasOcrText;
  String _textQuery = '';
  SortBy _sortBy = SortBy.dateModified;
  SortOrder _sortOrder = SortOrder.descending;

  // Date range controllers
  DateTime? _startDate;
  DateTime? _endDate;

  // Size range controllers
  double _minSize = 0;
  double _maxSize = 1000; // MB

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeFromFilter();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _initializeFromFilter() {
    if (widget.initialFilter != null) {
      final filter = widget.initialFilter!;
      _selectedMediaType = filter.mediaType;
      _selectedTags = filter.tags ?? [];
      _selectedPeople = filter.people ?? [];
      _hasFaces = filter.hasFaces;
      _hasLocation = filter.hasLocation;
      _hasOcrText = filter.hasOcrText;
      _textQuery = filter.textQuery ?? '';
      _sortBy = filter.sortBy;
      _sortOrder = filter.sortOrder;

      if (filter.dateRange != null) {
        _startDate = filter.dateRange!.start;
        _endDate = filter.dateRange!.end;
      }

      if (filter.sizeRange != null) {
        _minSize = filter.sizeRange!.minSize / (1024 * 1024); // Convert to MB
        _maxSize = filter.sizeRange!.maxSize / (1024 * 1024);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                const Text(
                  'Advanced Filters',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _clearAllFilters,
                  child: const Text('Clear All'),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Tab bar
            TabBar(
              controller: _tabController,
              labelColor: const Color(0xFF8E44AD),
              unselectedLabelColor: Colors.grey,
              indicatorColor: const Color(0xFF8E44AD),
              tabs: const [
                Tab(icon: Icon(Icons.category), text: 'Type & Date'),
                Tab(icon: Icon(Icons.storage), text: 'Size & Tags'),
                Tab(icon: Icon(Icons.smart_toy), text: 'AI Features'),
                Tab(icon: Icon(Icons.sort), text: 'Sort'),
              ],
            ),

            const SizedBox(height: 16),

            // Tab content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildTypeAndDateTab(),
                  _buildSizeAndTagsTab(),
                  _buildAIFeaturesTab(),
                  _buildSortTab(),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: UnifiedButton(
                    text: 'Cancel',
                    onPressed: () => Navigator.pop(context),
                    type: ButtonType.outlined,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: UnifiedButton(
                    text: 'Apply Filters',
                    onPressed: _applyFilters,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeAndDateTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Media type selection
          const Text(
            'Media Type',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [
              FilterChip(
                label: const Text('All'),
                selected: _selectedMediaType == null,
                onSelected: (selected) {
                  setState(() {
                    _selectedMediaType = selected ? null : _selectedMediaType;
                  });
                },
              ),
              FilterChip(
                label: const Text('Images'),
                selected: _selectedMediaType == MediaType.image,
                onSelected: (selected) {
                  setState(() {
                    _selectedMediaType = selected ? MediaType.image : null;
                  });
                },
              ),
              FilterChip(
                label: const Text('Videos'),
                selected: _selectedMediaType == MediaType.video,
                onSelected: (selected) {
                  setState(() {
                    _selectedMediaType = selected ? MediaType.video : null;
                  });
                },
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Date range selection
          const Text(
            'Date Range',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: UnifiedButton(
                  text: _startDate != null
                      ? 'From: ${_formatDate(_startDate!)}'
                      : 'Select Start Date',
                  onPressed: () => _selectStartDate(),
                  type: ButtonType.outlined,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: UnifiedButton(
                  text: _endDate != null
                      ? 'To: ${_formatDate(_endDate!)}'
                      : 'Select End Date',
                  onPressed: () => _selectEndDate(),
                  type: ButtonType.outlined,
                ),
              ),
            ],
          ),

          if (_startDate != null || _endDate != null) ...[
            const SizedBox(height: 8),
            TextButton(
              onPressed: () {
                setState(() {
                  _startDate = null;
                  _endDate = null;
                });
              },
              child: const Text('Clear Date Range'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSizeAndTagsTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // File size range
          const Text(
            'File Size Range (MB)',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          RangeSlider(
            values: RangeValues(_minSize, _maxSize),
            min: 0,
            max: 1000,
            divisions: 100,
            labels: RangeLabels(
              '${_minSize.toStringAsFixed(1)} MB',
              '${_maxSize.toStringAsFixed(1)} MB',
            ),
            onChanged: (values) {
              setState(() {
                _minSize = values.start;
                _maxSize = values.end;
              });
            },
          ),

          const SizedBox(height: 24),

          // Text search
          const Text(
            'Search Text',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          TextField(
            decoration: const InputDecoration(
              hintText: 'Search in filenames, tags, OCR text...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _textQuery = value;
              });
            },
          ),

          const SizedBox(height: 24),

          // Common tags (simulated)
          const Text(
            'Common Tags',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children:
                [
                  'person',
                  'car',
                  'nature',
                  'building',
                  'food',
                  'animal',
                  'outdoor',
                  'indoor',
                ].map((tag) {
                  return FilterChip(
                    label: Text(tag),
                    selected: _selectedTags.contains(tag),
                    onSelected: (selected) {
                      setState(() {
                        if (selected) {
                          _selectedTags.add(tag);
                        } else {
                          _selectedTags.remove(tag);
                        }
                      });
                    },
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildAIFeaturesTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'AI Features',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          SwitchListTile(
            title: const Text('Has Faces'),
            subtitle: const Text('Show only media with detected faces'),
            value: _hasFaces ?? false,
            onChanged: (value) {
              setState(() {
                _hasFaces = value ? true : null;
              });
            },
          ),

          SwitchListTile(
            title: const Text('Has Location'),
            subtitle: const Text('Show only media with GPS location'),
            value: _hasLocation ?? false,
            onChanged: (value) {
              setState(() {
                _hasLocation = value ? true : null;
              });
            },
          ),

          SwitchListTile(
            title: const Text('Has Text (OCR)'),
            subtitle: const Text('Show only media with detected text'),
            value: _hasOcrText ?? false,
            onChanged: (value) {
              setState(() {
                _hasOcrText = value ? true : null;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSortTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Sort By',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),

          ...SortBy.values.map((sortBy) {
            return RadioListTile<SortBy>(
              title: Text(_getSortByLabel(sortBy)),
              value: sortBy,
              groupValue: _sortBy,
              onChanged: (value) {
                setState(() {
                  _sortBy = value!;
                });
              },
            );
          }),

          const SizedBox(height: 24),

          const Text(
            'Sort Order',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),

          RadioListTile<SortOrder>(
            title: const Text('Newest First'),
            value: SortOrder.descending,
            groupValue: _sortOrder,
            onChanged: (value) {
              setState(() {
                _sortOrder = value!;
              });
            },
          ),

          RadioListTile<SortOrder>(
            title: const Text('Oldest First'),
            value: SortOrder.ascending,
            groupValue: _sortOrder,
            onChanged: (value) {
              setState(() {
                _sortOrder = value!;
              });
            },
          ),
        ],
      ),
    );
  }

  void _selectStartDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      setState(() {
        _startDate = date;
      });
    }
  }

  void _selectEndDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _endDate ?? DateTime.now(),
      firstDate: _startDate ?? DateTime(2000),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      setState(() {
        _endDate = date;
      });
    }
  }

  void _clearAllFilters() {
    setState(() {
      _selectedMediaType = null;
      _selectedTags.clear();
      _selectedPeople.clear();
      _hasFaces = null;
      _hasLocation = null;
      _hasOcrText = null;
      _textQuery = '';
      _sortBy = SortBy.dateModified;
      _sortOrder = SortOrder.descending;
      _startDate = null;
      _endDate = null;
      _minSize = 0;
      _maxSize = 1000;
    });
  }

  void _applyFilters() {
    final filter = SmartGalleryFilter(
      mediaType: _selectedMediaType,
      dateRange: (_startDate != null || _endDate != null)
          ? DateRange(
              start: _startDate ?? DateTime(2000),
              end: _endDate ?? DateTime.now(),
            )
          : null,
      sizeRange: SizeRange(
        minSize: (_minSize * 1024 * 1024).round(),
        maxSize: (_maxSize * 1024 * 1024).round(),
      ),
      tags: _selectedTags.isNotEmpty ? _selectedTags : null,
      people: _selectedPeople.isNotEmpty ? _selectedPeople : null,
      hasFaces: _hasFaces,
      hasLocation: _hasLocation,
      hasOcrText: _hasOcrText,
      textQuery: _textQuery.isNotEmpty ? _textQuery : null,
      sortBy: _sortBy,
      sortOrder: _sortOrder,
    );

    Navigator.pop(context, filter);
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getSortByLabel(SortBy sortBy) {
    switch (sortBy) {
      case SortBy.name:
        return 'Name';
      case SortBy.dateCreated:
        return 'Date Created';
      case SortBy.dateModified:
        return 'Date Modified';
      case SortBy.size:
        return 'File Size';
      case SortBy.type:
        return 'File Type';
    }
  }
}
