import 'dart:async';
import '../models/smart_gallery_models.dart';
import '../../../core/database/database_service.dart';
import '../../../core/services/error_handler.dart' as error_handler;
// Removed unused import

/// Smart album service with AI-powered auto-creation
class SmartAlbumService {
  static bool _isInitialized = false;
  static Timer? _autoCreationTimer;
  static final Map<String, SmartGalleryAlbum> _cachedAlbums = {};

  /// Initialize smart album service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Load existing albums
      await _loadExistingAlbums();

      // Start auto-creation monitoring
      _startAutoCreationMonitoring();

      _isInitialized = true;
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.operation,
        context: 'Initialize smart album service',
      );
    }
  }

  /// Load existing albums from database
  static Future<void> _loadExistingAlbums() async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM smart_gallery_albums',
      );

      _cachedAlbums.clear();
      for (final row in results) {
        final album = _albumFromMap(row);
        _cachedAlbums[album.id] = album;
      }
    } catch (e) {
      // Continue with empty cache
    }
  }

  /// Start auto-creation monitoring
  static void _startAutoCreationMonitoring() {
    _autoCreationTimer?.cancel();
    _autoCreationTimer = Timer.periodic(const Duration(hours: 1), (
      timer,
    ) async {
      await _performAutoCreation();
    });
  }

  /// Perform automatic album creation
  static Future<void> _performAutoCreation() async {
    try {
      // Create albums based on different criteria
      await _createPeopleAlbums();
      await _createLocationAlbums();
      await _createDateAlbums();
      await _createObjectAlbums();
      await _createEventAlbums();
      await _createSeasonalAlbums();
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.operation,
        context: 'Auto album creation failed',
      );
    }
  }

  /// Create albums based on people (face recognition)
  static Future<void> _createPeopleAlbums() async {
    try {
      // Get all media with faces
      final mediaWithFaces = await DatabaseService.safeQuery(
        'SELECT id, faces FROM smart_gallery_items WHERE faces != ""',
      );

      // Group by person
      final peopleGroups = <String, List<String>>{};

      for (final row in mediaWithFaces) {
        final mediaId = row['id'] as String;
        final facesJson = row['faces'] as String;

        // Parse faces (simplified - in production, parse JSON)
        if (facesJson.isNotEmpty) {
          // Extract person names from faces
          final personNames = _extractPersonNames(facesJson);

          for (final personName in personNames) {
            peopleGroups.putIfAbsent(personName, () => []).add(mediaId);
          }
        }
      }

      // Create albums for people with enough photos
      for (final entry in peopleGroups.entries) {
        final personName = entry.key;
        final mediaIds = entry.value;

        if (mediaIds.length >= 5) {
          // Minimum 5 photos
          await _createOrUpdateAlbum(
            name: personName,
            description: 'Photos of $personName',
            type: AlbumType.people,
            mediaIds: mediaIds,
          );
        }
      }
    } catch (e) {
      // Continue with other album types
    }
  }

  /// Create albums based on location
  static Future<void> _createLocationAlbums() async {
    try {
      // Get all media with location data
      final mediaWithLocation = await DatabaseService.safeQuery(
        'SELECT id, location FROM smart_gallery_items WHERE location IS NOT NULL',
      );

      // Group by location
      final locationGroups = <String, List<String>>{};

      for (final row in mediaWithLocation) {
        final mediaId = row['id'] as String;
        final locationJson = row['location'] as String?;

        if (locationJson != null && locationJson.isNotEmpty) {
          // Extract location name (simplified)
          final locationName = _extractLocationName(locationJson);
          locationGroups.putIfAbsent(locationName, () => []).add(mediaId);
        }
      }

      // Create albums for locations with enough photos
      for (final entry in locationGroups.entries) {
        final locationName = entry.key;
        final mediaIds = entry.value;

        if (mediaIds.length >= 3) {
          // Minimum 3 photos
          await _createOrUpdateAlbum(
            name: locationName,
            description: 'Photos taken at $locationName',
            type: AlbumType.places,
            mediaIds: mediaIds,
          );
        }
      }
    } catch (e) {
      // Continue with other album types
    }
  }

  /// Create albums based on date ranges
  static Future<void> _createDateAlbums() async {
    try {
      final now = DateTime.now();

      // Create "This Month" album
      final thisMonthStart = DateTime(now.year, now.month, 1);
      final thisMonthEnd = DateTime(now.year, now.month + 1, 0);

      final thisMonthMedia = await _getMediaInDateRange(
        thisMonthStart,
        thisMonthEnd,
      );
      if (thisMonthMedia.isNotEmpty) {
        await _createOrUpdateAlbum(
          name: 'This Month',
          description: 'Photos from ${_getMonthName(now.month)} ${now.year}',
          type: AlbumType.smart,
          mediaIds: thisMonthMedia,
        );
      }

      // Create "Last 7 Days" album
      final lastWeekStart = now.subtract(const Duration(days: 7));
      final lastWeekMedia = await _getMediaInDateRange(lastWeekStart, now);
      if (lastWeekMedia.isNotEmpty) {
        await _createOrUpdateAlbum(
          name: 'Last 7 Days',
          description: 'Recent photos from the past week',
          type: AlbumType.smart,
          mediaIds: lastWeekMedia,
        );
      }

      // Create yearly albums
      for (int year = now.year; year >= now.year - 5; year--) {
        final yearStart = DateTime(year, 1, 1);
        final yearEnd = DateTime(year, 12, 31);

        final yearMedia = await _getMediaInDateRange(yearStart, yearEnd);
        if (yearMedia.length >= 10) {
          // Minimum 10 photos
          await _createOrUpdateAlbum(
            name: '$year',
            description: 'Photos from $year',
            type: AlbumType.smart,
            mediaIds: yearMedia,
          );
        }
      }
    } catch (e) {
      // Continue with other album types
    }
  }

  /// Create albums based on detected objects
  static Future<void> _createObjectAlbums() async {
    try {
      // Get all media with AI tags
      final mediaWithTags = await DatabaseService.safeQuery(
        'SELECT id, ai_tags FROM smart_gallery_items WHERE ai_tags != ""',
      );

      // Group by object type
      final objectGroups = <String, List<String>>{};

      for (final row in mediaWithTags) {
        final mediaId = row['id'] as String;
        final aiTags = (row['ai_tags'] as String? ?? '').split(',');

        for (final tag in aiTags) {
          final cleanTag = tag.trim();
          if (cleanTag.isNotEmpty && _isInterestingObject(cleanTag)) {
            objectGroups.putIfAbsent(cleanTag, () => []).add(mediaId);
          }
        }
      }

      // Create albums for objects with enough photos
      for (final entry in objectGroups.entries) {
        final objectName = entry.key;
        final mediaIds = entry.value;

        if (mediaIds.length >= 5) {
          // Minimum 5 photos
          await _createOrUpdateAlbum(
            name: '${_capitalizeFirst(objectName)} Photos',
            description: 'Photos containing $objectName',
            type: AlbumType.things,
            mediaIds: mediaIds,
          );
        }
      }
    } catch (e) {
      // Continue with other album types
    }
  }

  /// Create albums based on events (holidays, special occasions)
  static Future<void> _createEventAlbums() async {
    try {
      final now = DateTime.now();

      // Define special dates
      final specialDates = <String, List<DateTime>>{
        'Christmas': [
          DateTime(now.year, 12, 25),
          DateTime(now.year - 1, 12, 25),
        ],
        'New Year': [DateTime(now.year, 1, 1), DateTime(now.year - 1, 1, 1)],
        'Valentine\'s Day': [
          DateTime(now.year, 2, 14),
          DateTime(now.year - 1, 2, 14),
        ],
        'Halloween': [
          DateTime(now.year, 10, 31),
          DateTime(now.year - 1, 10, 31),
        ],
      };

      for (final entry in specialDates.entries) {
        final eventName = entry.key;
        final dates = entry.value;

        final eventMedia = <String>[];

        for (final date in dates) {
          // Get photos from event date (±3 days)
          final startDate = date.subtract(const Duration(days: 3));
          final endDate = date.add(const Duration(days: 3));

          final dateMedia = await _getMediaInDateRange(startDate, endDate);
          eventMedia.addAll(dateMedia);
        }

        if (eventMedia.isNotEmpty) {
          await _createOrUpdateAlbum(
            name: eventName,
            description: 'Photos from $eventName celebrations',
            type: AlbumType.smart,
            mediaIds: eventMedia.toSet().toList(), // Remove duplicates
          );
        }
      }
    } catch (e) {
      // Continue with other album types
    }
  }

  /// Create seasonal albums
  static Future<void> _createSeasonalAlbums() async {
    try {
      final now = DateTime.now();
      final currentYear = now.year;

      // Define seasons
      final seasons = <String, List<DateTime>>{
        'Spring': [DateTime(currentYear, 3, 20), DateTime(currentYear, 6, 20)],
        'Summer': [DateTime(currentYear, 6, 21), DateTime(currentYear, 9, 22)],
        'Autumn': [DateTime(currentYear, 9, 23), DateTime(currentYear, 12, 20)],
        'Winter': [
          DateTime(currentYear, 12, 21),
          DateTime(currentYear + 1, 3, 19),
        ],
      };

      for (final entry in seasons.entries) {
        final seasonName = entry.key;
        final dateRange = entry.value;

        final seasonMedia = await _getMediaInDateRange(
          dateRange[0],
          dateRange[1],
        );

        if (seasonMedia.length >= 10) {
          // Minimum 10 photos
          await _createOrUpdateAlbum(
            name: '$seasonName $currentYear',
            description: 'Photos from $seasonName $currentYear',
            type: AlbumType.smart,
            mediaIds: seasonMedia,
          );
        }
      }
    } catch (e) {
      // Continue
    }
  }

  /// Create or update album
  static Future<void> _createOrUpdateAlbum({
    required String name,
    required String description,
    required AlbumType type,
    required List<String> mediaIds,
  }) async {
    try {
      // Check if album already exists
      SmartGalleryAlbum? existingAlbum;
      try {
        existingAlbum = _cachedAlbums.values.firstWhere(
          (album) => album.name == name && album.type == type,
        );
      } catch (e) {
        existingAlbum = null;
      }

      if (existingAlbum != null) {
        // Update existing album
        await DatabaseService.safeUpdate(
          'smart_gallery_albums',
          {
            'media_ids': mediaIds.join(','),
            'date_modified': DateTime.now().millisecondsSinceEpoch,
          },
          where: 'id = ?',
          whereArgs: [existingAlbum.id],
        );

        // Update cache
        _cachedAlbums[existingAlbum.id] = existingAlbum.copyWith(
          mediaIds: mediaIds,
          dateModified: DateTime.now(),
        );
      } else {
        // Create new album
        final albumId = 'album_${DateTime.now().millisecondsSinceEpoch}';
        final now = DateTime.now();

        await DatabaseService.safeInsert('smart_gallery_albums', {
          'id': albumId,
          'name': name,
          'description': description,
          'type': type.index,
          'media_ids': mediaIds.join(','),
          'cover_image_id': mediaIds.isNotEmpty ? mediaIds.first : null,
          'date_created': now.millisecondsSinceEpoch,
          'date_modified': now.millisecondsSinceEpoch,
          'is_locked': 0,
        });

        // Add to cache
        final newAlbum = SmartGalleryAlbum(
          id: albumId,
          name: name,
          description: description,
          type: type,
          mediaIds: mediaIds,
          coverImageId: mediaIds.isNotEmpty ? mediaIds.first : null,
          dateCreated: now,
          dateModified: now,
        );

        _cachedAlbums[albumId] = newAlbum;
      }
    } catch (e) {
      // Continue with other albums
    }
  }

  /// Get media in date range
  static Future<List<String>> _getMediaInDateRange(
    DateTime start,
    DateTime end,
  ) async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT id FROM smart_gallery_items WHERE date_created BETWEEN ? AND ?',
        [start.millisecondsSinceEpoch, end.millisecondsSinceEpoch],
      );

      return results.map((row) => row['id'] as String).toList();
    } catch (e) {
      return [];
    }
  }

  /// Extract person names from faces JSON
  static List<String> _extractPersonNames(String facesJson) {
    // Simplified extraction - in production, parse actual JSON
    // For now, return simulated person names
    return ['John Doe', 'Jane Smith'];
  }

  /// Extract location name from location JSON
  static String _extractLocationName(String locationJson) {
    // Simplified extraction - in production, parse actual JSON and reverse geocode
    // For now, return simulated location
    return 'Unknown Location';
  }

  /// Check if object is interesting for album creation
  static bool _isInterestingObject(String tag) {
    const interestingObjects = {
      'car',
      'dog',
      'cat',
      'flower',
      'tree',
      'building',
      'food',
      'beach',
      'mountain',
      'sunset',
      'sunrise',
      'party',
      'wedding',
      'birthday',
    };

    return interestingObjects.contains(tag.toLowerCase());
  }

  /// Get month name
  static String _getMonthName(int month) {
    const months = [
      '',
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];

    return months[month];
  }

  /// Capitalize first letter
  static String _capitalizeFirst(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }

  /// Convert database row to SmartGalleryAlbum
  static SmartGalleryAlbum _albumFromMap(Map<String, dynamic> map) {
    return SmartGalleryAlbum(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      type: AlbumType.values[map['type']],
      mediaIds:
          map['media_ids']?.split(',').where((id) => id.isNotEmpty).toList() ??
          [],
      coverImageId: map['cover_image_id'],
      dateCreated: DateTime.fromMillisecondsSinceEpoch(map['date_created']),
      dateModified: DateTime.fromMillisecondsSinceEpoch(map['date_modified']),
      isLocked: map['is_locked'] == 1,
    );
  }

  /// Get all albums
  static Future<List<SmartGalleryAlbum>> getAllAlbums() async {
    if (!_isInitialized) {
      await initialize();
    }

    return _cachedAlbums.values.toList();
  }

  /// Get album by ID
  static SmartGalleryAlbum? getAlbum(String albumId) {
    return _cachedAlbums[albumId];
  }

  /// Create manual album
  static Future<String> createManualAlbum({
    required String name,
    String? description,
    List<String>? mediaIds,
  }) async {
    try {
      final albumId = 'album_${DateTime.now().millisecondsSinceEpoch}';
      final now = DateTime.now();

      await DatabaseService.safeInsert('smart_gallery_albums', {
        'id': albumId,
        'name': name,
        'description': description ?? '',
        'type': AlbumType.manual.index,
        'media_ids': (mediaIds ?? []).join(','),
        'cover_image_id': (mediaIds?.isNotEmpty ?? false)
            ? mediaIds!.first
            : null,
        'date_created': now.millisecondsSinceEpoch,
        'date_modified': now.millisecondsSinceEpoch,
        'is_locked': 0,
      });

      // Add to cache
      final newAlbum = SmartGalleryAlbum(
        id: albumId,
        name: name,
        description: description,
        type: AlbumType.manual,
        mediaIds: mediaIds ?? [],
        coverImageId: (mediaIds?.isNotEmpty ?? false) ? mediaIds!.first : null,
        dateCreated: now,
        dateModified: now,
      );

      _cachedAlbums[albumId] = newAlbum;

      return albumId;
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.operation,
        context: 'Create manual album failed',
      );
      rethrow;
    }
  }

  /// Delete album
  static Future<bool> deleteAlbum(String albumId) async {
    try {
      await DatabaseService.safeDelete(
        'smart_gallery_albums',
        where: 'id = ?',
        whereArgs: [albumId],
      );

      _cachedAlbums.remove(albumId);

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Dispose resources
  static void dispose() {
    _autoCreationTimer?.cancel();
    _cachedAlbums.clear();
  }
}

/// Extension for SmartGalleryAlbum copyWith
extension SmartGalleryAlbumExtension on SmartGalleryAlbum {
  SmartGalleryAlbum copyWith({
    String? id,
    String? name,
    String? description,
    AlbumType? type,
    List<String>? mediaIds,
    String? coverImageId,
    DateTime? dateCreated,
    DateTime? dateModified,
    bool? isLocked,
  }) {
    return SmartGalleryAlbum(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      mediaIds: mediaIds ?? this.mediaIds,
      coverImageId: coverImageId ?? this.coverImageId,
      dateCreated: dateCreated ?? this.dateCreated,
      dateModified: dateModified ?? this.dateModified,
      isLocked: isLocked ?? this.isLocked,
    );
  }
}
