import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/surah.dart' as surah_model;
import '../../models/tafseer.dart';
import '../../services/tafseer_service.dart';
import '../../../../core/theme/app_theme.dart';

class EnhancedQuranReaderScreen extends ConsumerStatefulWidget {
  final int surahNumber;
  final int? initialAyah;

  const EnhancedQuranReaderScreen({
    super.key,
    required this.surahNumber,
    this.initialAyah,
  });

  @override
  ConsumerState<EnhancedQuranReaderScreen> createState() =>
      _EnhancedQuranReaderScreenState();
}

class _EnhancedQuranReaderScreenState
    extends ConsumerState<EnhancedQuranReaderScreen> {
  late PageController _pageController;
  int _currentAyah = 1;
  bool _showTranslation = true;
  bool _showTafseer = false;
  String _selectedTafseer = 'ibn_kathir';
  String _arabicTextStyle = 'uthmani';
  double _arabicFontSize = 24.0;
  double _translationFontSize = 16.0;

  final Map<String, String> _tafseerSources = {
    'ibn_kathir': 'Tafseer Ibn Kathir',
    'jalalayn': 'Tafseer al-Jalalayn',
    'tabari': 'Tafseer at-Tabari',
    'qurtubi': 'Tafseer al-Qurtubi',
    'saadi': 'Tafseer as-Sa\'di',
  };

  final Map<String, String> _arabicTextStyles = {
    'uthmani': 'Uthmani Script',
    'simple': 'Simple Arabic',
    'indopak': 'Indo-Pak Style',
  };

  @override
  void initState() {
    super.initState();
    _currentAyah = widget.initialAyah ?? 1;
    _pageController = PageController(initialPage: _currentAyah - 1);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Mock surah data for now - replace with actual provider
    final surahData = surah_model.Surah(
      number: widget.surahNumber,
      nameArabic: 'سورة ${widget.surahNumber}',
      nameEnglish: 'Chapter ${widget.surahNumber}',
      nameTransliteration: 'Surah ${widget.surahNumber}',
      verseCount: 286,
      revelationType: 'Meccan',
      verses: [],
    );

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(surahData.nameEnglish),
        backgroundColor: AppTheme.islamicAppColor,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.bookmark_border),
            onPressed: _addBookmark,
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showReaderSettings,
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'share',
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text('Share Ayah'),
                ),
              ),
              const PopupMenuItem(
                value: 'copy',
                child: ListTile(
                  leading: Icon(Icons.copy),
                  title: Text('Copy Text'),
                ),
              ),
              const PopupMenuItem(
                value: 'audio',
                child: ListTile(
                  leading: Icon(Icons.play_arrow),
                  title: Text('Play Audio'),
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSurahHeader(surahData),
          _buildControlBar(),
          Expanded(child: _buildAyahContent(surahData)),
          _buildNavigationBar(surahData),
        ],
      ),
    );
  }

  Widget _buildSurahHeader(surah_model.Surah surah) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.islamicAppColor.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(
            color: AppTheme.islamicAppColor.withValues(alpha: 0.3),
          ),
        ),
      ),
      child: Column(
        children: [
          Text(
            surah.nameArabic,
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: AppTheme.islamicAppColor,
              fontFamily: 'Amiri', // Arabic font
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '${surah.nameEnglish} • ${surah.verseCount} Ayahs • ${surah.revelationType}',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'Ayah $_currentAyah of ${surah.verseCount}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppTheme.islamicAppColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          _buildToggleButton(
            'Translation',
            _showTranslation,
            Icons.translate,
            () => setState(() => _showTranslation = !_showTranslation),
          ),
          const SizedBox(width: 8),
          _buildToggleButton(
            'Tafseer',
            _showTafseer,
            Icons.menu_book,
            () => setState(() => _showTafseer = !_showTafseer),
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.text_decrease),
            onPressed: () => setState(() {
              _arabicFontSize = (_arabicFontSize - 2).clamp(16.0, 36.0);
              _translationFontSize = (_translationFontSize - 1).clamp(
                12.0,
                24.0,
              );
            }),
          ),
          IconButton(
            icon: const Icon(Icons.text_increase),
            onPressed: () => setState(() {
              _arabicFontSize = (_arabicFontSize + 2).clamp(16.0, 36.0);
              _translationFontSize = (_translationFontSize + 1).clamp(
                12.0,
                24.0,
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleButton(
    String label,
    bool isActive,
    IconData icon,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isActive ? AppTheme.islamicAppColor : Colors.transparent,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: AppTheme.islamicAppColor, width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isActive ? Colors.white : AppTheme.islamicAppColor,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: isActive ? Colors.white : AppTheme.islamicAppColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAyahContent(surah_model.Surah surah) {
    return PageView.builder(
      controller: _pageController,
      onPageChanged: (index) {
        setState(() {
          _currentAyah = index + 1;
        });
      },
      itemCount: surah.verseCount,
      itemBuilder: (context, index) {
        final ayahNumber = index + 1;
        return _buildAyahPage(surah, ayahNumber);
      },
    );
  }

  Widget _buildAyahPage(surah_model.Surah surah, int ayahNumber) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAyahCard(surah, ayahNumber),
          if (_showTafseer) ...[
            const SizedBox(height: 16),
            _buildTafseerCard(surah.number, ayahNumber),
          ],
        ],
      ),
    );
  }

  Widget _buildAyahCard(surah_model.Surah surah, int ayahNumber) {
    // Mock ayah data - in real implementation, this would come from a database
    final arabicText = _getArabicText(surah.number, ayahNumber);
    final translation = _getTranslation(surah.number, ayahNumber);

    return GestureDetector(
      onTap: () => _showTafseerDialog(surah.number, ayahNumber),
      onLongPress: () => _showTafseerDialog(surah.number, ayahNumber),
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Ayah number badge
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: AppTheme.islamicAppColor,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  'Ayah $ayahNumber',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Arabic text
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppTheme.islamicAppColor.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppTheme.islamicAppColor.withValues(alpha: 0.2),
                  ),
                ),
                child: Text(
                  arabicText,
                  style: TextStyle(
                    fontSize: _arabicFontSize,
                    height: 2.0,
                    fontFamily: 'Amiri',
                    color: AppTheme.islamicAppColor,
                  ),
                  textAlign: TextAlign.right,
                  textDirection: TextDirection.rtl,
                ),
              ),

              if (_showTranslation) ...[
                const SizedBox(height: 16),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Translation',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        translation,
                        style: TextStyle(
                          fontSize: _translationFontSize,
                          height: 1.6,
                          color: Colors.grey[800],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _showTafseerDialog(int surahNumber, int ayahNumber) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppTheme.islamicAppColor,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.menu_book, color: Colors.white),
                    const SizedBox(width: 8),
                    Text(
                      'Tafseer - Surah $surahNumber, Ayah $ayahNumber',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close, color: Colors.white),
                    ),
                  ],
                ),
              ),
              // Content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: FutureBuilder<Tafseer?>(
                    future: TafseerService.getTafseerForVerse(
                      surahNumber,
                      ayahNumber,
                    ),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const Center(child: CircularProgressIndicator());
                      } else if (snapshot.hasError) {
                        return Center(
                          child: Text(
                            'Error loading tafseer: ${snapshot.error}',
                            style: TextStyle(color: Colors.red[600]),
                            textAlign: TextAlign.center,
                          ),
                        );
                      } else {
                        return SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Tafseer source selector
                              DropdownButtonFormField<String>(
                                value: _selectedTafseer,
                                decoration: const InputDecoration(
                                  labelText: 'Tafseer Source',
                                  border: OutlineInputBorder(),
                                ),
                                items: _tafseerSources.entries.map((entry) {
                                  return DropdownMenuItem(
                                    value: entry.key,
                                    child: Text(entry.value),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _selectedTafseer = value;
                                    });
                                  }
                                },
                              ),
                              const SizedBox(height: 16),
                              // Tafseer text
                              Container(
                                width: double.infinity,
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: Colors.amber[50],
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(color: Colors.amber[200]!),
                                ),
                                child: Text(
                                  snapshot.data?.text ??
                                      'No tafseer available for this verse.',
                                  style: TextStyle(
                                    fontSize: 14,
                                    height: 1.6,
                                    color: Colors.grey[800],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      }
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTafseerCard(int surahNumber, int ayahNumber) {
    return FutureBuilder<Tafseer?>(
      future: TafseerService.getTafseerForVerse(surahNumber, ayahNumber),
      builder: (context, snapshot) {
        return Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.menu_book,
                      color: AppTheme.islamicAppColor,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Tafseer',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.islamicAppColor,
                      ),
                    ),
                    const Spacer(),
                    DropdownButton<String>(
                      value: _selectedTafseer,
                      underline: const SizedBox.shrink(),
                      items: _tafseerSources.entries.map((entry) {
                        return DropdownMenuItem(
                          value: entry.key,
                          child: Text(
                            entry.value,
                            style: const TextStyle(fontSize: 12),
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedTafseer = value;
                          });
                        }
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (snapshot.connectionState == ConnectionState.waiting)
                  const Center(child: CircularProgressIndicator())
                else if (snapshot.hasError)
                  Text(
                    'Error loading tafseer: ${snapshot.error}',
                    style: TextStyle(color: Colors.red[600]),
                  )
                else
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.amber[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.amber[200]!),
                    ),
                    child: Text(
                      snapshot.data?.text ?? 'No tafseer available',
                      style: TextStyle(
                        fontSize: 14,
                        height: 1.6,
                        color: Colors.grey[800],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildNavigationBar(surah_model.Surah surah) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: _currentAyah > 1 ? _previousAyah : null,
            icon: const Icon(Icons.arrow_back_ios),
            style: IconButton.styleFrom(
              backgroundColor: _currentAyah > 1
                  ? AppTheme.islamicAppColor
                  : Colors.grey[300],
              foregroundColor: _currentAyah > 1 ? Colors.white : Colors.grey,
            ),
          ),
          Expanded(
            child: Slider(
              value: _currentAyah.toDouble(),
              min: 1,
              max: surah.verseCount.toDouble(),
              divisions: surah.verseCount - 1,
              activeColor: AppTheme.islamicAppColor,
              onChanged: (value) {
                final ayah = value.round();
                setState(() {
                  _currentAyah = ayah;
                });
                _pageController.animateToPage(
                  ayah - 1,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              },
            ),
          ),
          IconButton(
            onPressed: _currentAyah < surah.verseCount ? _nextAyah : null,
            icon: const Icon(Icons.arrow_forward_ios),
            style: IconButton.styleFrom(
              backgroundColor: _currentAyah < surah.verseCount
                  ? AppTheme.islamicAppColor
                  : Colors.grey[300],
              foregroundColor: _currentAyah < surah.verseCount
                  ? Colors.white
                  : Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  // Mock data methods - in real implementation, these would fetch from database
  String _getArabicText(int surahNumber, int ayahNumber) {
    // Sample Arabic text for demonstration
    return 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ';
  }

  String _getTranslation(int surahNumber, int ayahNumber) {
    // Sample translation for demonstration
    return 'In the name of Allah, the Entirely Merciful, the Especially Merciful.';
  }

  void _previousAyah() {
    if (_currentAyah > 1) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _nextAyah() {
    // Use the mock surah data for now
    if (_currentAyah < 286) {
      // Default verse count
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _addBookmark() {
    // Implement bookmark functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Bookmark added for Surah ${widget.surahNumber}, Ayah $_currentAyah',
        ),
        backgroundColor: AppTheme.islamicAppColor,
      ),
    );
  }

  void _showReaderSettings() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Reader Settings',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Text('Arabic Text Style'),
            DropdownButtonFormField<String>(
              value: _arabicTextStyle,
              items: _arabicTextStyles.entries.map((entry) {
                return DropdownMenuItem(
                  value: entry.key,
                  child: Text(entry.value),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _arabicTextStyle = value;
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Close'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'share':
        // Implement share functionality
        break;
      case 'copy':
        // Implement copy functionality
        break;
      case 'audio':
        // Implement audio playback
        break;
    }
  }
}
