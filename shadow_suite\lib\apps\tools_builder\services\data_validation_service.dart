import '../models/spreadsheet_cell.dart';
import '../models/ui_component.dart';

// Advanced Data Validation Service
class DataValidationService {
  // Built-in validation rules
  static final Map<String, ValidationRule> _builtInRules = {
    'required': ValidationRule(
      name: 'Required',
      description: 'Field cannot be empty',
      validator: (value, params) => _validateRequired(value),
    ),
    'number': ValidationRule(
      name: 'Number',
      description: 'Must be a valid number',
      validator: (value, params) => _validateNumber(value, params),
    ),
    'integer': ValidationRule(
      name: 'Integer',
      description: 'Must be a whole number',
      validator: (value, params) => _validateInteger(value, params),
    ),
    'range': ValidationRule(
      name: 'Range',
      description: 'Must be within specified range',
      validator: (value, params) => _validateRange(value, params),
    ),
    'length': ValidationRule(
      name: 'Length',
      description: 'Must meet length requirements',
      validator: (value, params) => _validateLength(value, params),
    ),
    'pattern': ValidationRule(
      name: 'Pattern',
      description: 'Must match specified pattern',
      validator: (value, params) => _validatePattern(value, params),
    ),
    'email': ValidationRule(
      name: 'Email',
      description: 'Must be a valid email address',
      validator: (value, params) => _validateEmail(value),
    ),
    'url': ValidationRule(
      name: 'URL',
      description: 'Must be a valid URL',
      validator: (value, params) => _validateUrl(value),
    ),
    'date': ValidationRule(
      name: 'Date',
      description: 'Must be a valid date',
      validator: (value, params) => _validateDate(value, params),
    ),
    'time': ValidationRule(
      name: 'Time',
      description: 'Must be a valid time',
      validator: (value, params) => _validateTime(value),
    ),
    'custom': ValidationRule(
      name: 'Custom',
      description: 'Custom validation rule',
      validator: (value, params) => _validateCustom(value, params),
    ),
  };

  // Custom validation rules
  static final Map<String, ValidationRule> _customRules = {};

  /// Validate cell value
  static ValidationResult validateCell(
    SpreadsheetCell cell,
    List<CellValidation> validations,
  ) {
    final errors = <String>[];
    final warnings = <String>[];

    for (final validation in validations) {
      final rule = _getRule(validation.ruleName);
      if (rule == null) {
        errors.add('Unknown validation rule: ${validation.ruleName}');
        continue;
      }

      final result = rule.validator(cell.rawValue, validation.parameters);

      if (!result.isValid) {
        if (validation.severity == ValidationSeverity.error) {
          errors.add(result.message ?? rule.description);
        } else {
          warnings.add(result.message ?? rule.description);
        }
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Validate UI component
  static ValidationResult validateComponent(
    UIComponent component,
    List<ComponentValidation> validations,
  ) {
    final errors = <String>[];
    final warnings = <String>[];

    for (final validation in validations) {
      final rule = _getRule(validation.ruleName);
      if (rule == null) {
        errors.add('Unknown validation rule: ${validation.ruleName}');
        continue;
      }

      final value = _getComponentValue(component, validation.property);
      final result = rule.validator(value, validation.parameters);

      if (!result.isValid) {
        if (validation.severity == ValidationSeverity.error) {
          errors.add(
            '${validation.property}: ${result.message ?? rule.description}',
          );
        } else {
          warnings.add(
            '${validation.property}: ${result.message ?? rule.description}',
          );
        }
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Get validation rule
  static ValidationRule? _getRule(String ruleName) {
    return _builtInRules[ruleName] ?? _customRules[ruleName];
  }

  /// Get component property value
  static dynamic _getComponentValue(UIComponent component, String property) {
    switch (property) {
      case 'label':
        return component.label;
      case 'placeholder':
        return component.placeholder;
      case 'helpText':
        return component.helpText;
      case 'x':
        return component.x;
      case 'y':
        return component.y;
      case 'isVisible':
        return component.isVisible;
      case 'isEnabled':
        return component.isEnabled;
      case 'isRequired':
        return component.isRequired;
      default:
        return component.properties[property];
    }
  }

  /// Add custom validation rule
  static void addCustomRule(String name, ValidationRule rule) {
    _customRules[name] = rule;
  }

  /// Remove custom validation rule
  static void removeCustomRule(String name) {
    _customRules.remove(name);
  }

  /// Get all available rules
  static List<ValidationRule> getAllRules() {
    return [..._builtInRules.values, ..._customRules.values];
  }

  // Built-in validation functions
  static ValidationRuleResult _validateRequired(dynamic value) {
    if (value == null || value.toString().trim().isEmpty) {
      return ValidationRuleResult(
        isValid: false,
        message: 'This field is required',
      );
    }
    return ValidationRuleResult(isValid: true);
  }

  static ValidationRuleResult _validateNumber(
    dynamic value,
    Map<String, dynamic> params,
  ) {
    if (value == null || value.toString().trim().isEmpty) {
      return ValidationRuleResult(
        isValid: true,
      ); // Allow empty for non-required fields
    }

    final numValue = double.tryParse(value.toString());
    if (numValue == null) {
      return ValidationRuleResult(
        isValid: false,
        message: 'Must be a valid number',
      );
    }

    // Check decimal places
    if (params.containsKey('decimals')) {
      final decimals = params['decimals'] as int;
      final valueStr = value.toString();
      if (valueStr.contains('.')) {
        final decimalPart = valueStr.split('.')[1];
        if (decimalPart.length > decimals) {
          return ValidationRuleResult(
            isValid: false,
            message: 'Maximum $decimals decimal places allowed',
          );
        }
      }
    }

    return ValidationRuleResult(isValid: true);
  }

  static ValidationRuleResult _validateInteger(
    dynamic value,
    Map<String, dynamic> params,
  ) {
    if (value == null || value.toString().trim().isEmpty) {
      return ValidationRuleResult(isValid: true);
    }

    final intValue = int.tryParse(value.toString());
    if (intValue == null) {
      return ValidationRuleResult(
        isValid: false,
        message: 'Must be a whole number',
      );
    }

    return ValidationRuleResult(isValid: true);
  }

  static ValidationRuleResult _validateRange(
    dynamic value,
    Map<String, dynamic> params,
  ) {
    if (value == null || value.toString().trim().isEmpty) {
      return ValidationRuleResult(isValid: true);
    }

    final numValue = double.tryParse(value.toString());
    if (numValue == null) {
      return ValidationRuleResult(
        isValid: false,
        message: 'Must be a number for range validation',
      );
    }

    final min = params['min'] as double?;
    final max = params['max'] as double?;

    if (min != null && numValue < min) {
      return ValidationRuleResult(
        isValid: false,
        message: 'Must be at least $min',
      );
    }

    if (max != null && numValue > max) {
      return ValidationRuleResult(
        isValid: false,
        message: 'Must be at most $max',
      );
    }

    return ValidationRuleResult(isValid: true);
  }

  static ValidationRuleResult _validateLength(
    dynamic value,
    Map<String, dynamic> params,
  ) {
    if (value == null) {
      return ValidationRuleResult(isValid: true);
    }

    final length = value.toString().length;
    final min = params['min'] as int?;
    final max = params['max'] as int?;

    if (min != null && length < min) {
      return ValidationRuleResult(
        isValid: false,
        message: 'Must be at least $min characters',
      );
    }

    if (max != null && length > max) {
      return ValidationRuleResult(
        isValid: false,
        message: 'Must be at most $max characters',
      );
    }

    return ValidationRuleResult(isValid: true);
  }

  static ValidationRuleResult _validatePattern(
    dynamic value,
    Map<String, dynamic> params,
  ) {
    if (value == null || value.toString().trim().isEmpty) {
      return ValidationRuleResult(isValid: true);
    }

    final pattern = params['pattern'] as String?;
    if (pattern == null) {
      return ValidationRuleResult(
        isValid: false,
        message: 'Pattern not specified',
      );
    }

    final regex = RegExp(pattern);
    if (!regex.hasMatch(value.toString())) {
      final message =
          params['message'] as String? ?? 'Does not match required pattern';
      return ValidationRuleResult(isValid: false, message: message);
    }

    return ValidationRuleResult(isValid: true);
  }

  static ValidationRuleResult _validateEmail(dynamic value) {
    if (value == null || value.toString().trim().isEmpty) {
      return ValidationRuleResult(isValid: true);
    }

    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    if (!emailRegex.hasMatch(value.toString())) {
      return ValidationRuleResult(
        isValid: false,
        message: 'Must be a valid email address',
      );
    }

    return ValidationRuleResult(isValid: true);
  }

  static ValidationRuleResult _validateUrl(dynamic value) {
    if (value == null || value.toString().trim().isEmpty) {
      return ValidationRuleResult(isValid: true);
    }

    final urlRegex = RegExp(r'^https?:\/\/[^\s/$.?#].[^\s]*$');
    if (!urlRegex.hasMatch(value.toString())) {
      return ValidationRuleResult(
        isValid: false,
        message: 'Must be a valid URL',
      );
    }

    return ValidationRuleResult(isValid: true);
  }

  static ValidationRuleResult _validateDate(
    dynamic value,
    Map<String, dynamic> params,
  ) {
    if (value == null || value.toString().trim().isEmpty) {
      return ValidationRuleResult(isValid: true);
    }

    final dateValue = DateTime.tryParse(value.toString());
    if (dateValue == null) {
      return ValidationRuleResult(
        isValid: false,
        message: 'Must be a valid date',
      );
    }

    // Check date range
    final minDate = params['min'] as DateTime?;
    final maxDate = params['max'] as DateTime?;

    if (minDate != null && dateValue.isBefore(minDate)) {
      return ValidationRuleResult(
        isValid: false,
        message:
            'Date must be after ${minDate.toIso8601String().split('T')[0]}',
      );
    }

    if (maxDate != null && dateValue.isAfter(maxDate)) {
      return ValidationRuleResult(
        isValid: false,
        message:
            'Date must be before ${maxDate.toIso8601String().split('T')[0]}',
      );
    }

    return ValidationRuleResult(isValid: true);
  }

  static ValidationRuleResult _validateTime(dynamic value) {
    if (value == null || value.toString().trim().isEmpty) {
      return ValidationRuleResult(isValid: true);
    }

    final timeRegex = RegExp(r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$');
    if (!timeRegex.hasMatch(value.toString())) {
      return ValidationRuleResult(
        isValid: false,
        message: 'Must be a valid time (HH:MM)',
      );
    }

    return ValidationRuleResult(isValid: true);
  }

  static ValidationRuleResult _validateCustom(
    dynamic value,
    Map<String, dynamic> params,
  ) {
    final expression = params['expression'] as String?;
    if (expression == null) {
      return ValidationRuleResult(
        isValid: false,
        message: 'Custom expression not specified',
      );
    }

    // Simple expression evaluation (in a real implementation, use a proper expression parser)
    try {
      final result = _evaluateExpression(expression, value);
      if (!result) {
        final message =
            params['message'] as String? ?? 'Custom validation failed';
        return ValidationRuleResult(isValid: false, message: message);
      }
      return ValidationRuleResult(isValid: true);
    } catch (e) {
      return ValidationRuleResult(
        isValid: false,
        message: 'Invalid custom expression',
      );
    }
  }

  static bool _evaluateExpression(String expression, dynamic value) {
    // Simple expression evaluation - replace with proper parser in production
    // In production, use a proper expression parser
    return true; // Placeholder
  }
}

// Models
class ValidationRule {
  final String name;
  final String description;
  final ValidationRuleResult Function(
    dynamic value,
    Map<String, dynamic> params,
  )
  validator;

  ValidationRule({
    required this.name,
    required this.description,
    required this.validator,
  });
}

class ValidationRuleResult {
  final bool isValid;
  final String? message;

  ValidationRuleResult({required this.isValid, this.message});
}

class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  ValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  bool get hasErrors => errors.isNotEmpty;
  bool get hasWarnings => warnings.isNotEmpty;

  @override
  String toString() {
    if (isValid) return 'Valid';
    return 'Invalid: ${errors.join(', ')}';
  }
}

class CellValidation {
  final String ruleName;
  final Map<String, dynamic> parameters;
  final ValidationSeverity severity;
  final String? customMessage;

  CellValidation({
    required this.ruleName,
    this.parameters = const {},
    this.severity = ValidationSeverity.error,
    this.customMessage,
  });
}

class ComponentValidation {
  final String property;
  final String ruleName;
  final Map<String, dynamic> parameters;
  final ValidationSeverity severity;
  final String? customMessage;

  ComponentValidation({
    required this.property,
    required this.ruleName,
    this.parameters = const {},
    this.severity = ValidationSeverity.error,
    this.customMessage,
  });
}

enum ValidationSeverity { error, warning, info }
