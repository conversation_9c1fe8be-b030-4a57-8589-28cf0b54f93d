import 'dart:async';
import 'package:sqflite/sqflite.dart';
import '../database/database_service.dart';
import '../services/error_handler.dart' as error_handler;

class FeatureControlSystem {
  static final Map<String, AppFeatureSet> _appFeatures = {};
  static final Map<String, bool> _featureStates = {};
  static final StreamController<FeatureChangeEvent> _changeController = 
      StreamController<FeatureChangeEvent>.broadcast();
  
  // Initialize feature control system
  static Future<void> initialize() async {
    await _registerAllAppFeatures();
    await _loadFeatureStates();
  }

  // Register all app features
  static Future<void> _registerAllAppFeatures() async {
    // Money Manager Features
    _appFeatures['money_manager'] = AppFeatureSet(
      appId: 'money_manager',
      appName: 'Money Manager',
      description: 'Complete financial management application',
      features: [
        // Investment Features (10)
        AppFeature(id: 'mm_portfolio_tracking', name: 'Portfolio Tracking', description: 'Track investment portfolios', category: 'Investment', isEnabled: true),
        AppFeature(id: 'mm_stock_analysis', name: 'Stock Analysis', description: 'Analyze stock performance', category: 'Investment', isEnabled: true),
        AppFeature(id: 'mm_crypto_tracking', name: 'Crypto Tracking', description: 'Track cryptocurrency investments', category: 'Investment', isEnabled: true),
        AppFeature(id: 'mm_dividend_tracking', name: 'Dividend Tracking', description: 'Track dividend income', category: 'Investment', isEnabled: true),
        AppFeature(id: 'mm_performance_analytics', name: 'Performance Analytics', description: 'Investment performance analysis', category: 'Investment', isEnabled: true),
        AppFeature(id: 'mm_risk_assessment', name: 'Risk Assessment', description: 'Portfolio risk analysis', category: 'Investment', isEnabled: true),
        AppFeature(id: 'mm_asset_allocation', name: 'Asset Allocation', description: 'Portfolio asset allocation', category: 'Investment', isEnabled: true),
        AppFeature(id: 'mm_rebalancing', name: 'Portfolio Rebalancing', description: 'Automatic portfolio rebalancing', category: 'Investment', isEnabled: true),
        AppFeature(id: 'mm_investment_goals', name: 'Investment Goals', description: 'Set and track investment goals', category: 'Investment', isEnabled: true),
        AppFeature(id: 'mm_market_alerts', name: 'Market Alerts', description: 'Price and market alerts', category: 'Investment', isEnabled: true),
        
        // Loan & Debt Features (10)
        AppFeature(id: 'mm_loan_calculator', name: 'Loan Calculator', description: 'Calculate loan payments', category: 'Debt', isEnabled: true),
        AppFeature(id: 'mm_debt_tracking', name: 'Debt Tracking', description: 'Track all debts', category: 'Debt', isEnabled: true),
        AppFeature(id: 'mm_payment_schedule', name: 'Payment Schedule', description: 'Manage payment schedules', category: 'Debt', isEnabled: true),
        AppFeature(id: 'mm_debt_snowball', name: 'Debt Snowball', description: 'Debt payoff strategies', category: 'Debt', isEnabled: true),
        AppFeature(id: 'mm_credit_monitoring', name: 'Credit Monitoring', description: 'Monitor credit score', category: 'Debt', isEnabled: true),
        AppFeature(id: 'mm_refinancing', name: 'Refinancing Calculator', description: 'Calculate refinancing benefits', category: 'Debt', isEnabled: true),
        AppFeature(id: 'mm_interest_tracking', name: 'Interest Tracking', description: 'Track interest payments', category: 'Debt', isEnabled: true),
        AppFeature(id: 'mm_debt_consolidation', name: 'Debt Consolidation', description: 'Plan debt consolidation', category: 'Debt', isEnabled: true),
        AppFeature(id: 'mm_payment_reminders', name: 'Payment Reminders', description: 'Automated payment reminders', category: 'Debt', isEnabled: true),
        AppFeature(id: 'mm_debt_analytics', name: 'Debt Analytics', description: 'Debt analysis and insights', category: 'Debt', isEnabled: true),
        
        // Tax & Analytics Features (5)
        AppFeature(id: 'mm_tax_calculator', name: 'Tax Calculator', description: 'Calculate taxes', category: 'Tax', isEnabled: true),
        AppFeature(id: 'mm_tax_optimization', name: 'Tax Optimization', description: 'Optimize tax strategies', category: 'Tax', isEnabled: true),
        AppFeature(id: 'mm_expense_analytics', name: 'Expense Analytics', description: 'Analyze spending patterns', category: 'Analytics', isEnabled: true),
        AppFeature(id: 'mm_financial_reports', name: 'Financial Reports', description: 'Generate financial reports', category: 'Analytics', isEnabled: true),
        AppFeature(id: 'mm_budget_insights', name: 'Budget Insights', description: 'AI-powered budget insights', category: 'Analytics', isEnabled: true),
      ],
    );

    // Islamic App Features
    _appFeatures['islamic_app'] = AppFeatureSet(
      appId: 'islamic_app',
      appName: 'Islamic App',
      description: 'Comprehensive Islamic lifestyle application',
      features: [
        // Basic Islamic Features (10)
        AppFeature(id: 'ia_quran_translations', name: 'Quran Translations', description: 'Multiple Quran translations', category: 'Quran', isEnabled: true),
        AppFeature(id: 'ia_tafseer', name: 'Tafseer Integration', description: 'Quran commentary and interpretation', category: 'Quran', isEnabled: true),
        AppFeature(id: 'ia_prayer_times', name: 'Prayer Times', description: 'Accurate prayer time calculations', category: 'Prayer', isEnabled: true),
        AppFeature(id: 'ia_qibla_direction', name: 'Qibla Direction', description: 'Accurate Qibla compass', category: 'Prayer', isEnabled: true),
        AppFeature(id: 'ia_islamic_calendar', name: 'Islamic Calendar', description: 'Hijri calendar with events', category: 'Calendar', isEnabled: true),
        AppFeature(id: 'ia_hajj_umrah', name: 'Hajj/Umrah Guide', description: 'Pilgrimage guidance', category: 'Pilgrimage', isEnabled: true),
        AppFeature(id: 'ia_zakat_calculator', name: 'Zakat Calculator', description: 'Calculate Zakat obligations', category: 'Finance', isEnabled: true),
        AppFeature(id: 'ia_islamic_finance', name: 'Islamic Finance', description: 'Islamic banking tools', category: 'Finance', isEnabled: true),
        AppFeature(id: 'ia_scholarly_content', name: 'Scholarly Content', description: 'Islamic educational content', category: 'Education', isEnabled: true),
        AppFeature(id: 'ia_learning_paths', name: 'Learning Paths', description: 'Personalized Islamic education', category: 'Education', isEnabled: true),
        
        // Community & Advanced Features (15)
        AppFeature(id: 'ia_prayer_groups', name: 'Prayer Groups', description: 'Local prayer group management', category: 'Community', isEnabled: true),
        AppFeature(id: 'ia_mosque_directory', name: 'Mosque Directory', description: 'Offline mosque directory', category: 'Community', isEnabled: true),
        AppFeature(id: 'ia_islamic_events', name: 'Islamic Events', description: 'Community event management', category: 'Community', isEnabled: true),
        AppFeature(id: 'ia_dua_collections', name: 'Dua Collections', description: 'Comprehensive dua library', category: 'Worship', isEnabled: true),
        AppFeature(id: 'ia_islamic_art', name: 'Islamic Art', description: 'Islamic art and calligraphy', category: 'Culture', isEnabled: true),
        AppFeature(id: 'ia_history_timeline', name: 'History Timeline', description: 'Islamic history timeline', category: 'Education', isEnabled: true),
        AppFeature(id: 'ia_quran_memorization', name: 'Quran Memorization', description: 'Memorization tools and tracking', category: 'Quran', isEnabled: true),
        AppFeature(id: 'ia_islamic_podcasts', name: 'Islamic Podcasts', description: 'Offline podcast library', category: 'Media', isEnabled: true),
        AppFeature(id: 'ia_virtual_tours', name: 'Virtual Mosque Tours', description: 'Virtual mosque experiences', category: 'Culture', isEnabled: true),
        AppFeature(id: 'ia_islamic_names', name: 'Islamic Names', description: 'Islamic name meanings', category: 'Reference', isEnabled: true),
        AppFeature(id: 'ia_dream_interpretation', name: 'Dream Interpretation', description: 'Islamic dream interpretation', category: 'Reference', isEnabled: true),
        AppFeature(id: 'ia_charity_tracking', name: 'Charity Tracking', description: 'Track charitable donations', category: 'Finance', isEnabled: true),
        AppFeature(id: 'ia_event_planning', name: 'Event Planning', description: 'Islamic event planning tools', category: 'Community', isEnabled: true),
        AppFeature(id: 'ia_halal_scanner', name: 'Halal Food Scanner', description: 'Offline halal product database', category: 'Lifestyle', isEnabled: true),
        AppFeature(id: 'ia_community_members', name: 'Community Members', description: 'Member management system', category: 'Community', isEnabled: true),
      ],
    );

    // Memo Suite Features
    _appFeatures['memo_suite'] = AppFeatureSet(
      appId: 'memo_suite',
      appName: 'Memo Suite',
      description: 'Advanced note-taking and collaboration platform',
      features: [
        // Core Memo Features (10)
        AppFeature(id: 'ms_rich_text', name: 'Rich Text Editor', description: 'Advanced note-taking with formatting', category: 'Core', isEnabled: true),
        AppFeature(id: 'ms_voice_transcription', name: 'Voice Transcription', description: 'AI-powered offline transcription', category: 'Core', isEnabled: true),
        AppFeature(id: 'ms_collaboration', name: 'Collaborative Editing', description: 'Real-time collaboration', category: 'Core', isEnabled: true),
        AppFeature(id: 'ms_version_control', name: 'Version Control', description: 'Track memo versions and changes', category: 'Core', isEnabled: true),
        AppFeature(id: 'ms_smart_search', name: 'Smart Search', description: 'Advanced search and filtering', category: 'Core', isEnabled: true),
        AppFeature(id: 'ms_templates', name: 'Template System', description: 'Memo templates and quick creation', category: 'Core', isEnabled: true),
        AppFeature(id: 'ms_auto_save', name: 'Auto-save', description: 'Automatic saving and drafts', category: 'Core', isEnabled: true),
        AppFeature(id: 'ms_export_import', name: 'Export/Import', description: 'Multiple format support', category: 'Core', isEnabled: true),
        AppFeature(id: 'ms_tagging', name: 'Tagging System', description: 'Organize with tags and categories', category: 'Core', isEnabled: true),
        AppFeature(id: 'ms_offline_sync', name: 'Offline Sync', description: 'Offline-first synchronization', category: 'Core', isEnabled: true),
        
        // Advanced Memo Features (15)
        AppFeature(id: 'ms_encryption', name: 'Advanced Encryption', description: 'Secure memo encryption', category: 'Security', isEnabled: true),
        AppFeature(id: 'ms_reminders', name: 'Smart Reminders', description: 'Intelligent reminder system', category: 'Productivity', isEnabled: true),
        AppFeature(id: 'ms_attachments', name: 'File Attachments', description: 'Media and file support', category: 'Media', isEnabled: true),
        AppFeature(id: 'ms_cross_reference', name: 'Cross-Reference Links', description: 'Link memos together', category: 'Organization', isEnabled: true),
        AppFeature(id: 'ms_analytics', name: 'Analytics & Insights', description: 'Usage analytics and insights', category: 'Analytics', isEnabled: true),
        AppFeature(id: 'ms_workspaces', name: 'Workspace Management', description: 'Organize memos in workspaces', category: 'Organization', isEnabled: true),
        AppFeature(id: 'ms_shortcuts', name: 'Keyboard Shortcuts', description: 'Quick actions and shortcuts', category: 'Productivity', isEnabled: true),
        AppFeature(id: 'ms_backup_restore', name: 'Backup & Restore', description: 'Advanced backup system', category: 'Security', isEnabled: true),
        AppFeature(id: 'ms_mind_mapping', name: 'Mind Mapping', description: 'Visual mind map integration', category: 'Visualization', isEnabled: true),
        AppFeature(id: 'ms_ocr', name: 'OCR Text Recognition', description: 'Extract text from images', category: 'AI', isEnabled: true),
        AppFeature(id: 'ms_drawing', name: 'Drawing Tools', description: 'Sketching and drawing support', category: 'Creative', isEnabled: true),
        AppFeature(id: 'ms_code_highlighting', name: 'Code Syntax Highlighting', description: 'Programming language support', category: 'Development', isEnabled: true),
        AppFeature(id: 'ms_tables', name: 'Table Integration', description: 'Spreadsheet-like tables', category: 'Data', isEnabled: true),
        AppFeature(id: 'ms_calendar', name: 'Calendar Integration', description: 'Schedule and calendar events', category: 'Productivity', isEnabled: true),
        AppFeature(id: 'ms_presentation', name: 'Presentation Mode', description: 'Present memos as slideshows', category: 'Presentation', isEnabled: true),
      ],
    );

    // Excel to App Features
    _appFeatures['excel_to_app'] = AppFeatureSet(
      appId: 'excel_to_app',
      appName: 'Excel to App',
      description: 'Complete Excel-compatible spreadsheet application',
      features: [
        // Core Excel Features (10)
        AppFeature(id: 'ea_file_import_export', name: 'Excel Import/Export', description: 'Full Excel file compatibility', category: 'Core', isEnabled: true),
        AppFeature(id: 'ea_formula_engine', name: 'Formula Engine', description: 'Real-time Excel formula calculations', category: 'Core', isEnabled: true),
        AppFeature(id: 'ea_charts', name: 'Advanced Charts', description: 'Professional chart creation', category: 'Visualization', isEnabled: true),
        AppFeature(id: 'ea_pivot_tables', name: 'Pivot Tables', description: 'Data analysis with pivot tables', category: 'Analysis', isEnabled: true),
        AppFeature(id: 'ea_data_validation', name: 'Data Validation', description: 'Input controls and validation', category: 'Data', isEnabled: true),
        AppFeature(id: 'ea_conditional_formatting', name: 'Conditional Formatting', description: 'Dynamic cell formatting', category: 'Formatting', isEnabled: true),
        AppFeature(id: 'ea_macros', name: 'Macro Support', description: 'Record and execute macros', category: 'Automation', isEnabled: true),
        AppFeature(id: 'ea_filtering_sorting', name: 'Advanced Filtering', description: 'Data filtering and sorting', category: 'Data', isEnabled: true),
        AppFeature(id: 'ea_multi_sheet', name: 'Multi-sheet Management', description: 'Multiple worksheet support', category: 'Core', isEnabled: true),
        AppFeature(id: 'ea_cell_formatting', name: 'Cell Formatting', description: 'Rich cell styling options', category: 'Formatting', isEnabled: true),
        
        // Advanced Excel Features (15)
        AppFeature(id: 'ea_formula_autocomplete', name: 'Formula Auto-complete', description: 'IntelliSense for formulas', category: 'Productivity', isEnabled: true),
        AppFeature(id: 'ea_custom_functions', name: 'Custom Functions', description: 'VBA and custom function support', category: 'Development', isEnabled: true),
        AppFeature(id: 'ea_data_import', name: 'Data Import', description: 'Import from multiple sources', category: 'Data', isEnabled: true),
        AppFeature(id: 'ea_statistical_functions', name: 'Statistical Functions', description: 'Advanced statistical analysis', category: 'Analysis', isEnabled: true),
        AppFeature(id: 'ea_collaborative_editing', name: 'Collaborative Editing', description: 'Real-time collaboration', category: 'Collaboration', isEnabled: true),
        AppFeature(id: 'ea_version_control', name: 'Version Control', description: 'Track spreadsheet changes', category: 'Collaboration', isEnabled: true),
        AppFeature(id: 'ea_template_library', name: 'Template Library', description: 'Pre-built templates', category: 'Productivity', isEnabled: true),
        AppFeature(id: 'ea_print_layout', name: 'Print Layout', description: 'Professional printing options', category: 'Output', isEnabled: true),
        AppFeature(id: 'ea_password_protection', name: 'Password Protection', description: 'Secure spreadsheet protection', category: 'Security', isEnabled: true),
        AppFeature(id: 'ea_performance_optimization', name: 'Performance Optimization', description: 'Handle large datasets', category: 'Performance', isEnabled: true),
        AppFeature(id: 'ea_mobile_responsive', name: 'Mobile Interface', description: 'Touch-optimized interface', category: 'Mobile', isEnabled: true),
        AppFeature(id: 'ea_accessibility', name: 'Accessibility Features', description: 'Screen reader and accessibility', category: 'Accessibility', isEnabled: true),
        AppFeature(id: 'ea_api_integration', name: 'API Integration', description: 'External data connections', category: 'Integration', isEnabled: true),
        AppFeature(id: 'ea_search_replace', name: 'Advanced Search', description: 'Find and replace with patterns', category: 'Productivity', isEnabled: true),
        AppFeature(id: 'ea_audit_trail', name: 'Audit Trail', description: 'Change tracking and history', category: 'Security', isEnabled: true),
      ],
    );
  }

  // Feature management methods
  static Future<void> enableFeature(String featureId) async {
    try {
      _featureStates[featureId] = true;
      await _saveFeatureState(featureId, true);

      _notifyFeatureChange(FeatureChangeEvent(
        featureId: featureId,
        isEnabled: true,
        timestamp: DateTime.now(),
      ));
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Enable feature: $featureId');
    }
  }

  static Future<void> disableFeature(String featureId) async {
    try {
      _featureStates[featureId] = false;
      await _saveFeatureState(featureId, false);

      _notifyFeatureChange(FeatureChangeEvent(
        featureId: featureId,
        isEnabled: false,
        timestamp: DateTime.now(),
      ));
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Disable feature: $featureId');
    }
  }

  static bool isFeatureEnabled(String featureId) {
    return _featureStates[featureId] ?? true; // Default to enabled
  }

  static Future<void> enableAllFeaturesForApp(String appId) async {
    try {
      final appFeatures = _appFeatures[appId];
      if (appFeatures == null) return;

      for (final feature in appFeatures.features) {
        await enableFeature(feature.id);
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Enable all features for app: $appId');
    }
  }

  static Future<void> disableAllFeaturesForApp(String appId) async {
    try {
      final appFeatures = _appFeatures[appId];
      if (appFeatures == null) return;

      for (final feature in appFeatures.features) {
        await disableFeature(feature.id);
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Disable all features for app: $appId');
    }
  }

  static Future<void> enableFeaturesByCategory(String appId, String category) async {
    try {
      final appFeatures = _appFeatures[appId];
      if (appFeatures == null) return;

      for (final feature in appFeatures.features) {
        if (feature.category == category) {
          await enableFeature(feature.id);
        }
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Enable features by category: $appId/$category');
    }
  }

  static Future<void> disableFeaturesByCategory(String appId, String category) async {
    try {
      final appFeatures = _appFeatures[appId];
      if (appFeatures == null) return;

      for (final feature in appFeatures.features) {
        if (feature.category == category) {
          await disableFeature(feature.id);
        }
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Disable features by category: $appId/$category');
    }
  }

  static Future<FeatureProfile> createFeatureProfile({
    required String name,
    required String description,
    required Map<String, bool> featureStates,
  }) async {
    try {
      final profile = FeatureProfile(
        id: 'profile_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        description: description,
        featureStates: featureStates,
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      await DatabaseService.safeInsert('feature_profiles', profile.toJson());

      return profile;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Create feature profile');
      rethrow;
    }
  }

  static Future<void> applyFeatureProfile(String profileId) async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM feature_profiles WHERE id = ?',
        [profileId],
      );

      if (results.isEmpty) return;

      final profile = FeatureProfile.fromJson(results.first);

      for (final entry in profile.featureStates.entries) {
        _featureStates[entry.key] = entry.value;
        await _saveFeatureState(entry.key, entry.value);
      }

      _notifyFeatureChange(FeatureChangeEvent(
        featureId: 'profile_applied',
        isEnabled: true,
        timestamp: DateTime.now(),
        metadata: {'profile_id': profileId, 'profile_name': profile.name},
      ));
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Apply feature profile: $profileId');
    }
  }

  // Data persistence methods
  static Future<void> _saveFeatureState(String featureId, bool isEnabled) async {
    try {
      await DatabaseService.safeInsert(
        'feature_states',
        {
          'feature_id': featureId,
          'is_enabled': isEnabled,
          'updated_at': DateTime.now().toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Save feature state');
    }
  }

  static Future<void> _loadFeatureStates() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM feature_states');

      for (final row in results) {
        final featureId = row['feature_id'] as String;
        final isEnabled = row['is_enabled'] as bool;
        _featureStates[featureId] = isEnabled;
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load feature states');
    }
  }

  static void _notifyFeatureChange(FeatureChangeEvent event) {
    _changeController.add(event);
  }

  // Getters
  static List<AppFeatureSet> get allAppFeatures => _appFeatures.values.toList();
  static AppFeatureSet? getAppFeatures(String appId) => _appFeatures[appId];
  static Map<String, bool> get allFeatureStates => Map.unmodifiable(_featureStates);
  static Stream<FeatureChangeEvent> get changeStream => _changeController.stream;

  // Utility methods
  static List<AppFeature> getEnabledFeatures(String appId) {
    final appFeatures = _appFeatures[appId];
    if (appFeatures == null) return [];

    return appFeatures.features.where((feature) => isFeatureEnabled(feature.id)).toList();
  }

  static List<AppFeature> getDisabledFeatures(String appId) {
    final appFeatures = _appFeatures[appId];
    if (appFeatures == null) return [];

    return appFeatures.features.where((feature) => !isFeatureEnabled(feature.id)).toList();
  }

  static Map<String, List<AppFeature>> getFeaturesByCategory(String appId) {
    final appFeatures = _appFeatures[appId];
    if (appFeatures == null) return {};

    final categorizedFeatures = <String, List<AppFeature>>{};

    for (final feature in appFeatures.features) {
      if (!categorizedFeatures.containsKey(feature.category)) {
        categorizedFeatures[feature.category] = [];
      }
      categorizedFeatures[feature.category]!.add(feature);
    }

    return categorizedFeatures;
  }

  static FeatureUsageStats getUsageStats(String appId) {
    final appFeatures = _appFeatures[appId];
    if (appFeatures == null) {
      return FeatureUsageStats(
        totalFeatures: 0,
        enabledFeatures: 0,
        disabledFeatures: 0,
        enabledPercentage: 0.0,
      );
    }

    final totalFeatures = appFeatures.features.length;
    final enabledFeatures = appFeatures.features.where((f) => isFeatureEnabled(f.id)).length;
    final disabledFeatures = totalFeatures - enabledFeatures;
    final enabledPercentage = totalFeatures > 0 ? (enabledFeatures / totalFeatures) * 100 : 0.0;

    return FeatureUsageStats(
      totalFeatures: totalFeatures,
      enabledFeatures: enabledFeatures,
      disabledFeatures: disabledFeatures,
      enabledPercentage: enabledPercentage,
    );
  }

  // Dispose
  static void dispose() {
    _appFeatures.clear();
    _featureStates.clear();
    _changeController.close();
  }
}

// Models
class AppFeatureSet {
  final String appId;
  final String appName;
  final String description;
  final List<AppFeature> features;

  const AppFeatureSet({
    required this.appId,
    required this.appName,
    required this.description,
    required this.features,
  });
}

class AppFeature {
  final String id;
  final String name;
  final String description;
  final String category;
  final bool isEnabled;

  const AppFeature({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.isEnabled,
  });
}

class FeatureProfile {
  final String id;
  final String name;
  final String description;
  final Map<String, bool> featureStates;
  final DateTime createdAt;
  final DateTime lastModified;

  const FeatureProfile({
    required this.id,
    required this.name,
    required this.description,
    required this.featureStates,
    required this.createdAt,
    required this.lastModified,
  });

  factory FeatureProfile.fromJson(Map<String, dynamic> json) {
    return FeatureProfile(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      featureStates: Map<String, bool>.from(json['feature_states'] as Map),
      createdAt: DateTime.parse(json['created_at'] as String),
      lastModified: DateTime.parse(json['last_modified'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'feature_states': featureStates,
      'created_at': createdAt.toIso8601String(),
      'last_modified': lastModified.toIso8601String(),
    };
  }
}

class FeatureChangeEvent {
  final String featureId;
  final bool isEnabled;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  const FeatureChangeEvent({
    required this.featureId,
    required this.isEnabled,
    required this.timestamp,
    this.metadata,
  });
}

class FeatureUsageStats {
  final int totalFeatures;
  final int enabledFeatures;
  final int disabledFeatures;
  final double enabledPercentage;

  const FeatureUsageStats({
    required this.totalFeatures,
    required this.enabledFeatures,
    required this.disabledFeatures,
    required this.enabledPercentage,
  });
}
