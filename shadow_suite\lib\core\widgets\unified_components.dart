import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/unified_theme_service.dart';

/// Unified card component with consistent styling
class UnifiedCard extends ConsumerWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Color? color;
  final double? elevation;
  final VoidCallback? onTap;
  final BorderRadiusSize borderRadiusSize;
  final ElevationSize elevationSize;

  const UnifiedCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.color,
    this.elevation,
    this.onTap,
    this.borderRadiusSize = BorderRadiusSize.md,
    this.elevationSize = ElevationSize.md,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final borderRadius = UnifiedThemeService.getBorderRadius(borderRadiusSize);
    final cardElevation =
        elevation ?? UnifiedThemeService.getElevation(elevationSize);
    final defaultPadding = EdgeInsets.all(
      UnifiedThemeService.getSpacing(SpacingSize.md),
    );

    return Card(
      color: color,
      elevation: cardElevation,
      margin:
          margin ??
          EdgeInsets.all(UnifiedThemeService.getSpacing(SpacingSize.sm)),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(borderRadius),
        child: Padding(padding: padding ?? defaultPadding, child: child),
      ),
    );
  }
}

/// Unified button component
class UnifiedButton extends ConsumerWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final ButtonType type;
  final ButtonSize size;
  final Color? color;
  final bool isLoading;
  final bool isFullWidth;

  const UnifiedButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.type = ButtonType.elevated,
    this.size = ButtonSize.medium,
    this.color,
    this.isLoading = false,
    this.isFullWidth = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final spacing = UnifiedThemeService.getSpacing(SpacingSize.sm);
    final borderRadius = UnifiedThemeService.getBorderRadius(
      BorderRadiusSize.md,
    );

    final buttonHeight = _getButtonHeight();
    final fontSize = _getFontSize();
    final padding = _getPadding();

    Widget buttonChild = isLoading
        ? SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                type == ButtonType.elevated
                    ? Colors.white
                    : Theme.of(context).primaryColor,
              ),
            ),
          )
        : Row(
            mainAxisSize: isFullWidth ? MainAxisSize.max : MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (icon != null) ...[
                Icon(icon, size: fontSize * 1.2),
                SizedBox(width: spacing),
              ],
              Text(
                text,
                style: TextStyle(
                  fontSize: fontSize,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          );

    Widget button;
    switch (type) {
      case ButtonType.elevated:
        button = ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: color,
            minimumSize: Size(0, buttonHeight),
            padding: padding,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
            ),
          ),
          child: buttonChild,
        );
        break;
      case ButtonType.outlined:
        button = OutlinedButton(
          onPressed: isLoading ? null : onPressed,
          style: OutlinedButton.styleFrom(
            foregroundColor: color,
            side: color != null ? BorderSide(color: color!) : null,
            minimumSize: Size(0, buttonHeight),
            padding: padding,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
            ),
          ),
          child: buttonChild,
        );
        break;
      case ButtonType.text:
        button = TextButton(
          onPressed: isLoading ? null : onPressed,
          style: TextButton.styleFrom(
            foregroundColor: color,
            minimumSize: Size(0, buttonHeight),
            padding: padding,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
            ),
          ),
          child: buttonChild,
        );
        break;
    }

    return isFullWidth
        ? SizedBox(width: double.infinity, child: button)
        : button;
  }

  double _getButtonHeight() {
    switch (size) {
      case ButtonSize.small:
        return 36;
      case ButtonSize.medium:
        return 44;
      case ButtonSize.large:
        return 52;
    }
  }

  double _getFontSize() {
    switch (size) {
      case ButtonSize.small:
        return 14;
      case ButtonSize.medium:
        return 16;
      case ButtonSize.large:
        return 18;
    }
  }

  EdgeInsets _getPadding() {
    final spacing = UnifiedThemeService.getSpacing(SpacingSize.md);
    switch (size) {
      case ButtonSize.small:
        return EdgeInsets.symmetric(
          horizontal: spacing * 0.75,
          vertical: spacing * 0.5,
        );
      case ButtonSize.medium:
        return EdgeInsets.symmetric(
          horizontal: spacing,
          vertical: spacing * 0.75,
        );
      case ButtonSize.large:
        return EdgeInsets.symmetric(
          horizontal: spacing * 1.25,
          vertical: spacing,
        );
    }
  }
}

/// Unified section header
class UnifiedSectionHeader extends ConsumerWidget {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final Widget? action;
  final Color? color;

  const UnifiedSectionHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    this.action,
    this.color,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final spacing = UnifiedThemeService.getSpacing(SpacingSize.md);
    final theme = Theme.of(context);

    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: UnifiedThemeService.getSpacing(SpacingSize.sm),
      ),
      child: Row(
        children: [
          if (icon != null) ...[
            Icon(icon, color: color ?? theme.primaryColor, size: 24),
            SizedBox(width: spacing),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                if (subtitle != null) ...[
                  SizedBox(
                    height: UnifiedThemeService.getSpacing(SpacingSize.xs),
                  ),
                  Text(
                    subtitle!,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.textTheme.bodyMedium?.color?.withValues(
                        alpha: 0.7,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (action != null) action!,
        ],
      ),
    );
  }
}

/// Unified list tile
class UnifiedListTile extends ConsumerWidget {
  final String title;
  final String? subtitle;
  final IconData? leading;
  final Widget? trailing;
  final VoidCallback? onTap;
  final Color? color;
  final bool dense;

  const UnifiedListTile({
    super.key,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.color,
    this.dense = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final borderRadius = UnifiedThemeService.getBorderRadius(
      BorderRadiusSize.md,
    );
    final spacing = UnifiedThemeService.getSpacing(SpacingSize.md);

    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: UnifiedThemeService.getSpacing(SpacingSize.xs),
        horizontal: UnifiedThemeService.getSpacing(SpacingSize.sm),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius),
          child: Padding(
            padding: EdgeInsets.all(spacing),
            child: Row(
              children: [
                if (leading != null) ...[
                  Icon(
                    leading,
                    color: color ?? Theme.of(context).primaryColor,
                    size: dense ? 20 : 24,
                  ),
                  SizedBox(width: spacing),
                ],
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w500,
                          fontSize: dense ? 14 : 16,
                        ),
                      ),
                      if (subtitle != null) ...[
                        SizedBox(
                          height: UnifiedThemeService.getSpacing(
                            SpacingSize.xs,
                          ),
                        ),
                        Text(
                          subtitle!,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(
                                color: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.color
                                    ?.withValues(alpha: 0.7),
                                fontSize: dense ? 12 : 14,
                              ),
                        ),
                      ],
                    ],
                  ),
                ),
                if (trailing != null) ...[SizedBox(width: spacing), trailing!],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Unified input field
class UnifiedTextField extends ConsumerWidget {
  final String? label;
  final String? hint;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final TextInputType? keyboardType;
  final bool obscureText;
  final int? maxLines;
  final bool enabled;

  const UnifiedTextField({
    super.key,
    this.label,
    this.hint,
    this.prefixIcon,
    this.suffixIcon,
    this.controller,
    this.validator,
    this.onChanged,
    this.keyboardType,
    this.obscureText = false,
    this.maxLines = 1,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final borderRadius = UnifiedThemeService.getBorderRadius(
      BorderRadiusSize.md,
    );
    final spacing = UnifiedThemeService.getSpacing(SpacingSize.md);

    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: UnifiedThemeService.getSpacing(SpacingSize.sm),
      ),
      child: TextFormField(
        controller: controller,
        validator: validator,
        onChanged: onChanged,
        keyboardType: keyboardType,
        obscureText: obscureText,
        maxLines: maxLines,
        enabled: enabled,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          prefixIcon: prefixIcon != null ? Icon(prefixIcon) : null,
          suffixIcon: suffixIcon,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          contentPadding: EdgeInsets.all(spacing),
        ),
      ),
    );
  }
}

/// Unified loading indicator
class UnifiedLoadingIndicator extends ConsumerWidget {
  final String? message;
  final Color? color;
  final double size;

  const UnifiedLoadingIndicator({
    super.key,
    this.message,
    this.color,
    this.size = 24,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final spacing = UnifiedThemeService.getSpacing(SpacingSize.md);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              color ?? Theme.of(context).primaryColor,
            ),
          ),
        ),
        if (message != null) ...[
          SizedBox(height: spacing),
          Text(
            message!,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

/// Button type enumeration
enum ButtonType { elevated, outlined, text }

/// Button size enumeration
enum ButtonSize { small, medium, large }
