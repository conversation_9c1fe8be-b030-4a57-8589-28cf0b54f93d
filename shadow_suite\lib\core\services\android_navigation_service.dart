import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:io';

// Android Navigation Service Provider
final androidNavigationServiceProvider = Provider<AndroidNavigationService>((
  ref,
) {
  return AndroidNavigationService();
});

// Navigation Stack Provider
final navigationStackProvider =
    StateNotifierProvider<NavigationStackNotifier, List<String>>((ref) {
      return NavigationStackNotifier();
    });

// Exit Confirmation Provider
final exitConfirmationProvider = StateProvider<bool>((ref) => false);

// Android Navigation Service
class AndroidNavigationService {
  static const MethodChannel _channel = MethodChannel(
    'shadow_suite/navigation',
  );

  // Navigation stack for proper back button handling
  final List<String> _navigationStack = [];
  DateTime? _lastBackPressed;

  // Initialize Android navigation
  Future<void> initialize() async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('initializeNavigation');

      // Set up system navigation bar
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          systemNavigationBarColor: Color(0xFF2C3E50),
          systemNavigationBarIconBrightness: Brightness.light,
          statusBarColor: Color(0xFF3498DB),
          statusBarIconBrightness: Brightness.light,
        ),
      );
    } catch (e) {
      debugPrint('Error initializing Android navigation: $e');
    }
  }

  // Handle back button press with proper navigation stack
  Future<bool> handleBackPress(BuildContext context, WidgetRef ref) async {
    final navigationStack = ref.read(navigationStackProvider);

    // If there are items in navigation stack, pop the last one
    if (navigationStack.isNotEmpty) {
      ref.read(navigationStackProvider.notifier).pop();
      return false; // Don't exit app
    }

    // Show exit confirmation dialog
    return await _showExitConfirmationDialog(context);
  }

  // Show exit confirmation dialog
  Future<bool> _showExitConfirmationDialog(BuildContext context) async {
    final now = DateTime.now();

    // Double-tap to exit functionality
    if (_lastBackPressed == null ||
        now.difference(_lastBackPressed!) > const Duration(seconds: 2)) {
      _lastBackPressed = now;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Row(
            children: [
              Icon(Icons.info_outline, color: Colors.white),
              SizedBox(width: 8),
              Text('Press back again to exit Shadow Suite'),
            ],
          ),
          backgroundColor: const Color(0xFF3498DB),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      );
      return false;
    }

    // Show confirmation dialog on second press
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            title: const Row(
              children: [
                Icon(Icons.exit_to_app, color: Color(0xFF3498DB)),
                SizedBox(width: 8),
                Text('Exit Shadow Suite'),
              ],
            ),
            content: const Text(
              'Are you sure you want to exit Shadow Suite? Any unsaved changes will be lost.',
              style: TextStyle(fontSize: 16),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text(
                  'Cancel',
                  style: TextStyle(color: Color(0xFF95A5A6)),
                ),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFE74C3C),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Exit'),
              ),
            ],
          ),
        ) ??
        false;
  }

  // Push route to navigation stack
  void pushRoute(String route) {
    _navigationStack.add(route);
  }

  // Pop route from navigation stack
  String? popRoute() {
    if (_navigationStack.isNotEmpty) {
      return _navigationStack.removeLast();
    }
    return null;
  }

  // Clear navigation stack
  void clearStack() {
    _navigationStack.clear();
  }

  // Get current navigation stack
  List<String> get navigationStack => List.unmodifiable(_navigationStack);

  // Set up Android-specific app bar
  PreferredSizeWidget buildAndroidAppBar({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool automaticallyImplyLeading = true,
    Color? backgroundColor,
    Color? foregroundColor,
    double elevation = 2.0,
  }) {
    return AppBar(
      title: Text(title),
      actions: actions,
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading,
      backgroundColor: backgroundColor ?? const Color(0xFF3498DB),
      foregroundColor: foregroundColor ?? Colors.white,
      elevation: elevation,
      centerTitle: false, // Android style
      titleSpacing: 16,
      systemOverlayStyle: const SystemUiOverlayStyle(
        statusBarColor: Color(0xFF2980B9),
        statusBarIconBrightness: Brightness.light,
      ),
    );
  }

  // Create Android-style bottom sheet
  Future<T?> showAndroidBottomSheet<T>({
    required BuildContext context,
    required Widget child,
    bool isScrollControlled = false,
    bool enableDrag = true,
    bool showDragHandle = true,
    Color? backgroundColor,
  }) async {
    return await showModalBottomSheet<T>(
      context: context,
      isScrollControlled: isScrollControlled,
      enableDrag: enableDrag,
      showDragHandle: showDragHandle,
      backgroundColor: backgroundColor ?? Theme.of(context).colorScheme.surface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => child,
    );
  }

  // Show Android-style snackbar
  void showAndroidSnackBar({
    required BuildContext context,
    required String message,
    String? actionLabel,
    VoidCallback? onActionPressed,
    SnackBarType type = SnackBarType.info,
    Duration duration = const Duration(seconds: 4),
  }) {
    Color backgroundColor;
    IconData icon;

    switch (type) {
      case SnackBarType.success:
        backgroundColor = const Color(0xFF27AE60);
        icon = Icons.check_circle_outline;
        break;
      case SnackBarType.error:
        backgroundColor = const Color(0xFFE74C3C);
        icon = Icons.error_outline;
        break;
      case SnackBarType.warning:
        backgroundColor = const Color(0xFFF39C12);
        icon = Icons.warning_outlined;
        break;
      case SnackBarType.info:
        backgroundColor = const Color(0xFF3498DB);
        icon = Icons.info_outline;
        break;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: backgroundColor,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        action: actionLabel != null
            ? SnackBarAction(
                label: actionLabel,
                textColor: Colors.white,
                onPressed: onActionPressed ?? () {},
              )
            : null,
      ),
    );
  }

  // Show Android-style material dialog
  Future<T?> showAndroidDialog<T>({
    required BuildContext context,
    required String title,
    required String content,
    String? positiveButtonText,
    String? negativeButtonText,
    VoidCallback? onPositivePressed,
    VoidCallback? onNegativePressed,
    Widget? customContent,
    bool barrierDismissible = true,
  }) async {
    return await showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
        ),
        content:
            customContent ??
            Text(content, style: const TextStyle(fontSize: 16)),
        actions: [
          if (negativeButtonText != null)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onNegativePressed?.call();
              },
              child: Text(
                negativeButtonText,
                style: const TextStyle(color: Color(0xFF95A5A6)),
              ),
            ),
          if (positiveButtonText != null)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onPositivePressed?.call();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF3498DB),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(positiveButtonText),
            ),
        ],
      ),
    );
  }

  // Handle Android lifecycle events
  Future<void> handleLifecycleEvent(AppLifecycleState state) async {
    switch (state) {
      case AppLifecycleState.resumed:
        await _onAppResumed();
        break;
      case AppLifecycleState.paused:
        await _onAppPaused();
        break;
      case AppLifecycleState.detached:
        await _onAppDetached();
        break;
      case AppLifecycleState.inactive:
        await _onAppInactive();
        break;
      case AppLifecycleState.hidden:
        await _onAppHidden();
        break;
    }
  }

  Future<void> _onAppResumed() async {
    debugPrint('Shadow Suite resumed');
    // Refresh data, check for updates, etc.
  }

  Future<void> _onAppPaused() async {
    debugPrint('Shadow Suite paused');
    // Save current state, pause background tasks
  }

  Future<void> _onAppDetached() async {
    debugPrint('Shadow Suite detached');
    // Clean up resources
  }

  Future<void> _onAppInactive() async {
    debugPrint('Shadow Suite inactive');
    // Prepare for potential pause
  }

  Future<void> _onAppHidden() async {
    debugPrint('Shadow Suite hidden');
    // Handle app being hidden
  }
}

// Navigation Stack Notifier
class NavigationStackNotifier extends StateNotifier<List<String>> {
  NavigationStackNotifier() : super([]);

  void push(String route) {
    state = [...state, route];
  }

  void pop() {
    if (state.isNotEmpty) {
      state = state.sublist(0, state.length - 1);
    }
  }

  void clear() {
    state = [];
  }

  void replace(List<String> newStack) {
    state = newStack;
  }
}

// SnackBar Type Enum
enum SnackBarType { info, success, warning, error }
