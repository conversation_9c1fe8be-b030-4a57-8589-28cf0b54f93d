// Comprehensive authentic Athkar database
class AuthenticAthkarData {
  static const List<Map<String, dynamic>> morningAthkar = [
    {
      'id': 'morning_1',
      'arabicText': 'أَعُوذُ بِاللَّهِ مِنَ الشَّيْطَانِ الرَّجِيمِ',
      'transliteration': 'A\'udhu billahi min ash-<PERSON>\'r-rajeem',
      'englishTranslation': 'I seek refuge in Allah from Satan, the accursed.',
      'count': 1,
      'category': 'morning',
      'source': 'Quran 16:98',
      'benefits': 'Protection from Satan\'s whispers',
    },
    {
      'id': 'morning_2',
      'arabicText': 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
      'transliteration': '<PERSON><PERSON><PERSON><PERSON>\'r-rahmani\'r-raheem',
      'englishTranslation': 'In the name of <PERSON>, the Entirely Merciful, the Especially Merciful.',
      'count': 1,
      'category': 'morning',
      'source': 'Quran 1:1',
      'benefits': 'Blessing and protection in all affairs',
    },
    {
      'id': 'morning_3',
      'arabicText': 'قُلْ هُوَ اللَّهُ أَحَدٌ ۝ اللَّهُ الصَّمَدُ ۝ لَمْ يَلِدْ وَلَمْ يُولَدْ ۝ وَلَمْ يَكُن لَّهُ كُفُوًا أَحَدٌ',
      'transliteration': 'Qul huwa Allahu ahad. Allahu\'s-samad. Lam yalid wa lam yulad. Wa lam yakun lahu kufuwan ahad.',
      'englishTranslation': 'Say: He is Allah, the One! Allah, the Eternal, Absolute; He begets not, nor is He begotten; And there is none like unto Him.',
      'count': 3,
      'category': 'morning',
      'source': 'Quran 112:1-4',
      'benefits': 'Equivalent to one-third of the Quran',
    },
    {
      'id': 'morning_4',
      'arabicText': 'قُلْ أَعُوذُ بِرَبِّ الْفَلَقِ ۝ مِن شَرِّ مَا خَلَقَ ۝ وَمِن شَرِّ غَاسِقٍ إِذَا وَقَبَ ۝ وَمِن شَرِّ النَّفَّاثَاتِ فِي الْعُقَدِ ۝ وَمِن شَرِّ حَاسِدٍ إِذَا حَسَدَ',
      'transliteration': 'Qul a\'udhu bi rabbi\'l-falaq. Min sharri ma khalaq. Wa min sharri ghasiqin idha waqab. Wa min sharri\'n-naffathati fi\'l-\'uqad. Wa min sharri hasidin idha hasad.',
      'englishTranslation': 'Say: I seek refuge with the Lord of the Dawn, From the mischief of created things; From the mischief of Darkness as it overspreads; From the mischief of those who practice secret arts; And from the mischief of the envious one as he practices envy.',
      'count': 3,
      'category': 'morning',
      'source': 'Quran 113:1-5',
      'benefits': 'Protection from all forms of evil',
    },
    {
      'id': 'morning_5',
      'arabicText': 'قُلْ أَعُوذُ بِرَبِّ النَّاسِ ۝ مَلِكِ النَّاسِ ۝ إِلَٰهِ النَّاسِ ۝ مِن شَرِّ الْوَسْوَاسِ الْخَنَّاسِ ۝ الَّذِي يُوَسْوِسُ فِي صُدُورِ النَّاسِ ۝ مِنَ الْجِنَّةِ وَالنَّاسِ',
      'transliteration': 'Qul a\'udhu bi rabbi\'n-nas. Maliki\'n-nas. Ilahi\'n-nas. Min sharri\'l-waswasi\'l-khannas. Alladhi yuwaswisu fi suduri\'n-nas. Mina\'l-jinnati wa\'n-nas.',
      'englishTranslation': 'Say: I seek refuge with the Lord and Cherisher of Mankind, The King (or Ruler) of Mankind, The God (or Judge) of Mankind, From the mischief of the Whisperer (of Evil), who withdraws (after his whisper), (The same) who whispers into the hearts of Mankind, Among Jinns and among men.',
      'count': 3,
      'category': 'morning',
      'source': 'Quran 114:1-6',
      'benefits': 'Protection from whispers of Satan and evil thoughts',
    },
    {
      'id': 'morning_6',
      'arabicText': 'اللَّهُمَّ أَنْتَ رَبِّي لَا إِلَٰهَ إِلَّا أَنْتَ، خَلَقْتَنِي وَأَنَا عَبْدُكَ، وَأَنَا عَلَىٰ عَهْدِكَ وَوَعْدِكَ مَا اسْتَطَعْتُ، أَعُوذُ بِكَ مِنْ شَرِّ مَا صَنَعْتُ، أَبُوءُ لَكَ بِنِعْمَتِكَ عَلَيَّ، وَأَبُوءُ لَكَ بِذَنْبِي فَاغْفِرْ لِي فَإِنَّهُ لَا يَغْفِرُ الذُّنُوبَ إِلَّا أَنْتَ',
      'transliteration': 'Allahumma anta rabbi la ilaha illa ant, khalaqtani wa ana \'abduk, wa ana \'ala \'ahdika wa wa\'dika ma\'stata\'t, a\'udhu bika min sharri ma sana\'t, abu\'u laka bi ni\'matika \'alayy, wa abu\'u laka bi dhanbi faghfir li fa\'innahu la yaghfiru\'dh-dhunuba illa ant.',
      'englishTranslation': 'O Allah! You are my Lord! None has the right to be worshipped but You. You created me and I am Your slave, and I am faithful to my covenant and my promise as much as I can. I seek refuge with You from all the evil I have done. I acknowledge before You all the blessings You have bestowed upon me, and I confess to You all my sins. So I entreat You to forgive my sins, for nobody can forgive sins except You.',
      'count': 1,
      'category': 'morning',
      'source': 'Sahih al-Bukhari 6306',
      'benefits': 'Master of seeking forgiveness (Sayyid al-Istighfar)',
    },
    {
      'id': 'morning_7',
      'arabicText': 'أَصْبَحْنَا وَأَصْبَحَ الْمُلْكُ لِلَّهِ، وَالْحَمْدُ لِلَّهِ، لَا إِلَٰهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَىٰ كُلِّ شَيْءٍ قَدِيرٌ، رَبِّ أَسْأَلُكَ خَيْرَ مَا فِي هَٰذَا الْيَوْمِ وَخَيْرَ مَا بَعْدَهُ، وَأَعُوذُ بِكَ مِنْ شَرِّ مَا فِي هَٰذَا الْيَوْمِ وَشَرِّ مَا بَعْدَهُ، رَبِّ أَعُوذُ بِكَ مِنَ الْكَسَلِ وَسُوءِ الْكِبَرِ، رَبِّ أَعُوذُ بِكَ مِنْ عَذَابٍ فِي النَّارِ وَعَذَابٍ فِي الْقَبْرِ',
      'transliteration': 'Asbahna wa asbaha\'l-mulku lillah, wa\'l-hamdu lillah, la ilaha illa\'llahu wahdahu la sharika lah, lahu\'l-mulku wa lahu\'l-hamd, wa huwa \'ala kulli shay\'in qadir. Rabbi as\'aluka khayra ma fi hadha\'l-yawm wa khayra ma ba\'dah, wa a\'udhu bika min sharri ma fi hadha\'l-yawm wa sharri ma ba\'dah. Rabbi a\'udhu bika mina\'l-kasal wa su\'i\'l-kibar. Rabbi a\'udhu bika min \'adhabin fi\'n-nar wa \'adhabin fi\'l-qabr.',
      'englishTranslation': 'We have reached the morning and at this very time unto Allah belongs all sovereignty. All praise is for Allah. None has the right to be worshipped except Allah, alone, without partner, to Him belongs all sovereignty and praise and He is over all things omnipotent. My Lord, I ask You for the good of this day and the good of what follows it and I take refuge in You from the evil of this day and the evil of what follows it. My Lord, I take refuge in You from laziness and senility. My Lord, I take refuge in You from torment in the Fire and punishment in the grave.',
      'count': 1,
      'category': 'morning',
      'source': 'Sahih Muslim 2723',
      'benefits': 'Comprehensive morning protection and supplication',
    },
  ];

  static const List<Map<String, dynamic>> eveningAthkar = [
    {
      'id': 'evening_1',
      'arabicText': 'أَمْسَيْنَا وَأَمْسَى الْمُلْكُ لِلَّهِ، وَالْحَمْدُ لِلَّهِ، لَا إِلَٰهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَىٰ كُلِّ شَيْءٍ قَدِيرٌ، رَبِّ أَسْأَلُكَ خَيْرَ مَا فِي هَٰذِهِ اللَّيْلَةِ وَخَيْرَ مَا بَعْدَهَا، وَأَعُوذُ بِكَ مِنْ شَرِّ مَا فِي هَٰذِهِ اللَّيْلَةِ وَشَرِّ مَا بَعْدَهَا، رَبِّ أَعُوذُ بِكَ مِنَ الْكَسَلِ وَسُوءِ الْكِبَرِ، رَبِّ أَعُوذُ بِكَ مِنْ عَذَابٍ فِي النَّارِ وَعَذَابٍ فِي الْقَبْرِ',
      'transliteration': 'Amsayna wa amsa\'l-mulku lillah, wa\'l-hamdu lillah, la ilaha illa\'llahu wahdahu la sharika lah, lahu\'l-mulku wa lahu\'l-hamd, wa huwa \'ala kulli shay\'in qadir. Rabbi as\'aluka khayra ma fi hadhihi\'l-laylah wa khayra ma ba\'daha, wa a\'udhu bika min sharri ma fi hadhihi\'l-laylah wa sharri ma ba\'daha. Rabbi a\'udhu bika mina\'l-kasal wa su\'i\'l-kibar. Rabbi a\'udhu bika min \'adhabin fi\'n-nar wa \'adhabin fi\'l-qabr.',
      'englishTranslation': 'We have reached the evening and at this very time unto Allah belongs all sovereignty. All praise is for Allah. None has the right to be worshipped except Allah, alone, without partner, to Him belongs all sovereignty and praise and He is over all things omnipotent. My Lord, I ask You for the good of this night and the good of what follows it and I take refuge in You from the evil of this night and the evil of what follows it. My Lord, I take refuge in You from laziness and senility. My Lord, I take refuge in You from torment in the Fire and punishment in the grave.',
      'count': 1,
      'category': 'evening',
      'source': 'Sahih Muslim 2723',
      'benefits': 'Comprehensive evening protection and supplication',
    },
    {
      'id': 'evening_2',
      'arabicText': 'اللَّهُمَّ بِكَ أَمْسَيْنَا، وَبِكَ أَصْبَحْنَا، وَبِكَ نَحْيَا، وَبِكَ نَمُوتُ، وَإِلَيْكَ النُّشُورُ',
      'transliteration': 'Allahumma bika amsayna, wa bika asbahna, wa bika nahya, wa bika namut, wa ilayka\'n-nushur.',
      'englishTranslation': 'O Allah! By Your leave we have reached the evening and by Your leave we have reached the morning, by Your leave we live and die and unto You is our resurrection.',
      'count': 1,
      'category': 'evening',
      'source': 'Jami\' at-Tirmidhi 3391',
      'benefits': 'Acknowledgment of Allah\'s sovereignty over life and death',
    },
    {
      'id': 'evening_3',
      'arabicText': 'أَمْسَيْنَا عَلَىٰ فِطْرَةِ الْإِسْلَامِ، وَعَلَىٰ كَلِمَةِ الْإِخْلَاصِ، وَعَلَىٰ دِينِ نَبِيِّنَا مُحَمَّدٍ صَلَّى اللَّهُ عَلَيْهِ وَسَلَّمَ، وَعَلَىٰ مِلَّةِ أَبِينَا إِبْرَاهِيمَ حَنِيفًا مُسْلِمًا وَمَا كَانَ مِنَ الْمُشْرِكِينَ',
      'transliteration': 'Amsayna \'ala fitrati\'l-Islam, wa \'ala kalimati\'l-ikhlas, wa \'ala dini nabiyyina Muhammad salla\'llahu \'alayhi wa sallam, wa \'ala millati abina Ibrahim hanifan musliman wa ma kana mina\'l-mushrikin.',
      'englishTranslation': 'We have reached the evening upon the fitrah of Islam, upon the word of pure faith, upon the religion of our Prophet Muhammad (peace be upon him), and upon the religion of our father Ibrahim, who was a Muslim and of true faith and was not of those who associate others with Allah.',
      'count': 1,
      'category': 'evening',
      'source': 'Musnad Ahmad 15360',
      'benefits': 'Reaffirmation of faith and Islamic identity',
    },
  ];

  static const List<Map<String, dynamic>> afterPrayerAthkar = [
    {
      'id': 'after_prayer_1',
      'arabicText': 'أَسْتَغْفِرُ اللَّهَ',
      'transliteration': 'Astaghfiru\'llah',
      'englishTranslation': 'I seek forgiveness of Allah.',
      'count': 3,
      'category': 'afterPrayer',
      'source': 'Sahih Muslim 591',
      'benefits': 'Seeking forgiveness after prayer',
    },
    {
      'id': 'after_prayer_2',
      'arabicText': 'اللَّهُمَّ أَنْتَ السَّلَامُ وَمِنْكَ السَّلَامُ، تَبَارَكْتَ يَا ذَا الْجَلَالِ وَالْإِكْرَامِ',
      'transliteration': 'Allahumma anta\'s-salamu wa minka\'s-salam, tabarakta ya dha\'l-jalali wa\'l-ikram.',
      'englishTranslation': 'O Allah! You are As-Salam (The One Who is free from all defects and deficiencies) and from You is all peace, blessed are You, O Possessor of majesty and honor.',
      'count': 1,
      'category': 'afterPrayer',
      'source': 'Sahih Muslim 592',
      'benefits': 'Praising Allah\'s perfect attributes',
    },
    {
      'id': 'after_prayer_3',
      'arabicText': 'لَا إِلَٰهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَىٰ كُلِّ شَيْءٍ قَدِيرٌ',
      'transliteration': 'La ilaha illa\'llahu wahdahu la sharika lah, lahu\'l-mulku wa lahu\'l-hamd, wa huwa \'ala kulli shay\'in qadir.',
      'englishTranslation': 'None has the right to be worshipped except Allah, alone, without partner. To Him belongs all sovereignty and praise and He is over all things omnipotent.',
      'count': 1,
      'category': 'afterPrayer',
      'source': 'Sahih al-Bukhari 844',
      'benefits': 'Declaration of Allah\'s oneness and sovereignty',
    },
    {
      'id': 'after_prayer_4',
      'arabicText': 'سُبْحَانَ اللَّهِ',
      'transliteration': 'Subhan Allah',
      'englishTranslation': 'Glory is to Allah.',
      'count': 33,
      'category': 'afterPrayer',
      'source': 'Sahih Muslim 596',
      'benefits': 'Glorifying Allah',
    },
    {
      'id': 'after_prayer_5',
      'arabicText': 'الْحَمْدُ لِلَّهِ',
      'transliteration': 'Alhamdu lillah',
      'englishTranslation': 'All praise is due to Allah.',
      'count': 33,
      'category': 'afterPrayer',
      'source': 'Sahih Muslim 596',
      'benefits': 'Praising Allah',
    },
    {
      'id': 'after_prayer_6',
      'arabicText': 'اللَّهُ أَكْبَرُ',
      'transliteration': 'Allahu akbar',
      'englishTranslation': 'Allah is the greatest.',
      'count': 34,
      'category': 'afterPrayer',
      'source': 'Sahih Muslim 596',
      'benefits': 'Declaring Allah\'s greatness',
    },
  ];

  static const List<Map<String, dynamic>> sleepingAthkar = [
    {
      'id': 'sleeping_1',
      'arabicText': 'بِاسْمِكَ رَبِّي وَضَعْتُ جَنْبِي، وَبِكَ أَرْفَعُهُ، فَإِنْ أَمْسَكْتَ نَفْسِي فَارْحَمْهَا، وَإِنْ أَرْسَلْتَهَا فَاحْفَظْهَا بِمَا تَحْفَظُ بِهِ عِبَادَكَ الصَّالِحِينَ',
      'transliteration': 'Bismika rabbi wada\'tu janbi, wa bika arfa\'uh, fa\'in amsakta nafsi farhamha, wa in arsaltaha fahfazha bima tahfazu bihi \'ibadaka\'s-salihin.',
      'englishTranslation': 'With Your name my Lord, I lie down on my side, and with Your help I rise up, so if You should take my soul then have mercy upon it, and if You should return my soul then protect it in the manner You do so with Your righteous servants.',
      'count': 1,
      'category': 'sleeping',
      'source': 'Sahih al-Bukhari 6320',
      'benefits': 'Seeking Allah\'s protection during sleep',
    },
    {
      'id': 'sleeping_2',
      'arabicText': 'اللَّهُمَّ قِنِي عَذَابَكَ يَوْمَ تَبْعَثُ عِبَادَكَ',
      'transliteration': 'Allahumma qini \'adhabaka yawma tab\'athu \'ibadak.',
      'englishTranslation': 'O Allah, protect me from Your punishment on the day You resurrect Your servants.',
      'count': 3,
      'category': 'sleeping',
      'source': 'Sunan Abi Dawud 5045',
      'benefits': 'Seeking protection from punishment on the Day of Judgment',
    },
  ];

  static const List<Map<String, dynamic>> eatingAthkar = [
    {
      'id': 'eating_1',
      'arabicText': 'بِسْمِ اللَّهِ',
      'transliteration': 'Bismillah',
      'englishTranslation': 'In the name of Allah.',
      'count': 1,
      'category': 'eating',
      'source': 'Jami\' at-Tirmidhi 1858',
      'benefits': 'Blessing the food before eating',
    },
    {
      'id': 'eating_2',
      'arabicText': 'الْحَمْدُ لِلَّهِ الَّذِي أَطْعَمَنِي هَٰذَا وَرَزَقَنِيهِ مِنْ غَيْرِ حَوْلٍ مِنِّي وَلَا قُوَّةٍ',
      'transliteration': 'Alhamdu lillahi\'lladhi at\'amani hadha wa razaqanihi min ghayri hawlin minni wa la quwwah.',
      'englishTranslation': 'All praise is due to Allah who has fed me this and provided it for me without any might nor power from myself.',
      'count': 1,
      'category': 'eating',
      'source': 'Jami\' at-Tirmidhi 3458',
      'benefits': 'Thanking Allah after eating',
    },
  ];

  static const List<Map<String, dynamic>> travelAthkar = [
    {
      'id': 'travel_1',
      'arabicText': 'سُبْحَانَ الَّذِي سَخَّرَ لَنَا هَٰذَا وَمَا كُنَّا لَهُ مُقْرِنِينَ ۝ وَإِنَّا إِلَىٰ رَبِّنَا لَمُنقَلِبُونَ',
      'transliteration': 'Subhana\'lladhi sakhkhara lana hadha wa ma kunna lahu muqrinin. Wa inna ila rabbina la munqalibun.',
      'englishTranslation': 'Glory unto Him Who created this transportation, for us, though we were unable to create it on our own. And unto our Lord we shall return.',
      'count': 1,
      'category': 'travel',
      'source': 'Quran 43:13-14',
      'benefits': 'Acknowledging Allah\'s provision of transportation',
    },
    {
      'id': 'travel_2',
      'arabicText': 'اللَّهُمَّ إِنَّا نَسْأَلُكَ فِي سَفَرِنَا هَٰذَا الْبِرَّ وَالتَّقْوَىٰ، وَمِنَ الْعَمَلِ مَا تَرْضَىٰ، اللَّهُمَّ هَوِّنْ عَلَيْنَا سَفَرَنَا هَٰذَا وَاطْوِ عَنَّا بُعْدَهُ، اللَّهُمَّ أَنتَ الصَّاحِبُ فِي السَّفَرِ، وَالْخَلِيفَةُ فِي الْأَهْلِ',
      'transliteration': 'Allahumma inna nas\'aluka fi safarina hadha\'l-birra wa\'t-taqwa, wa mina\'l-\'amali ma tarda. Allahumma hawwin \'alayna safarana hadha wa\'twi \'anna bu\'dah. Allahumma anta\'s-sahibu fi\'s-safar, wa\'l-khalifatu fi\'l-ahl.',
      'englishTranslation': 'O Allah, we ask You on this our journey for goodness and piety, and for works that are pleasing to You. O Allah, lighten this journey for us and make its distance easy. O Allah, You are our companion on the road and the One in Whose care we leave our family.',
      'count': 1,
      'category': 'travel',
      'source': 'Sahih Muslim 1342',
      'benefits': 'Comprehensive travel supplication for safety and blessing',
    },
  ];

  static List<Map<String, dynamic>> getAllAthkar() {
    return [
      ...morningAthkar,
      ...eveningAthkar,
      ...afterPrayerAthkar,
      ...sleepingAthkar,
      ...eatingAthkar,
      ...travelAthkar,
    ];
  }

  static List<Map<String, dynamic>> getAthkarByCategory(String category) {
    switch (category) {
      case 'morning':
        return morningAthkar;
      case 'evening':
        return eveningAthkar;
      case 'afterPrayer':
        return afterPrayerAthkar;
      case 'sleeping':
        return sleepingAthkar;
      case 'eating':
        return eatingAthkar;
      case 'travel':
        return travelAthkar;
      default:
        return [];
    }
  }
}
