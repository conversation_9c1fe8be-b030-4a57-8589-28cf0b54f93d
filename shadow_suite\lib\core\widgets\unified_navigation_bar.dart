import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../navigation/unified_router.dart';

/// Unified navigation bar that adapts to different screen sizes and contexts
class UnifiedNavigationBar extends ConsumerWidget
    implements PreferredSizeWidget {
  final String currentRoute;
  final bool isMobile;
  final bool showBackButton;
  final String? title;
  final List<Widget>? actions;

  const UnifiedNavigationBar({
    super.key,
    required this.currentRoute,
    required this.isMobile,
    this.showBackButton = false,
    this.title,
    this.actions,
  });

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final navigationState = ref.watch(navigationStateProvider);
    final appTitle = _getAppTitle();
    final canGoBack = navigationState.navigationHistory.length > 1;

    return AppBar(
      title: Text(title ?? appTitle),
      backgroundColor: Theme.of(context).colorScheme.primary,
      foregroundColor: Theme.of(context).colorScheme.onPrimary,
      elevation: isMobile ? 2 : 1,
      centerTitle: isMobile,
      leading: _buildLeading(context, ref, canGoBack),
      actions: _buildActions(context, ref),
      bottom: isMobile ? _buildTabBar(context) : null,
    );
  }

  Widget? _buildLeading(BuildContext context, WidgetRef ref, bool canGoBack) {
    if (showBackButton && canGoBack) {
      return IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () => _handleBackNavigation(context, ref),
        tooltip: 'Back',
      );
    }

    if (isMobile && !_isMainDashboard()) {
      return IconButton(
        icon: const Icon(Icons.home),
        onPressed: () => _showDashboardConfirmation(context),
        tooltip: 'Dashboard',
      );
    }

    return null;
  }

  List<Widget> _buildActions(BuildContext context, WidgetRef ref) {
    final defaultActions = <Widget>[
      // Global search
      IconButton(
        icon: const Icon(Icons.search),
        onPressed: () => _showGlobalSearch(context),
        tooltip: 'Global Search',
      ),

      // Notifications
      IconButton(
        icon: const Icon(Icons.notifications),
        onPressed: () => _showNotifications(context),
        tooltip: 'Notifications',
      ),

      // Quick actions menu
      if (isMobile)
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) => _handleQuickAction(context, ref, value),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'add_transaction',
              child: ListTile(
                leading: Icon(Icons.add_circle),
                title: Text('Add Transaction'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'quick_note',
              child: ListTile(
                leading: Icon(Icons.note_add),
                title: Text('Quick Note'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'voice_memo',
              child: ListTile(
                leading: Icon(Icons.mic),
                title: Text('Voice Memo'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'settings',
              child: ListTile(
                leading: Icon(Icons.settings),
                title: Text('Settings'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),

      // Settings (desktop)
      if (!isMobile)
        IconButton(
          icon: const Icon(Icons.settings),
          onPressed: () => context.go('/settings'),
          tooltip: 'Settings',
        ),
    ];

    return [...(actions ?? []), ...defaultActions];
  }

  PreferredSizeWidget? _buildTabBar(BuildContext context) {
    final tabs = _getContextualTabs();
    if (tabs.isEmpty) return null;

    return TabBar(
      tabs: tabs.map((tab) => Tab(text: tab)).toList(),
      isScrollable: true,
      indicatorColor: Theme.of(context).colorScheme.onPrimary,
      labelColor: Theme.of(context).colorScheme.onPrimary,
      unselectedLabelColor: Theme.of(
        context,
      ).colorScheme.onPrimary.withValues(alpha: 0.7),
    );
  }

  String _getAppTitle() {
    if (currentRoute.startsWith('/money-manager')) {
      return 'Money Manager';
    } else if (currentRoute.startsWith('/file-manager')) {
      return 'File Manager';
    } else if (currentRoute.startsWith('/excel-to-app')) {
      return 'Tools Builder';
    } else if (currentRoute.startsWith('/islamic-app')) {
      return 'Islamic App';
    } else if (currentRoute.startsWith('/memo-suite')) {
      return 'Memo Suite';
    } else if (currentRoute.startsWith('/shadow-player')) {
      return 'Shadow Player';
    } else if (currentRoute.startsWith('/smart-gallery')) {
      return 'Smart Gallery+';
    } else if (currentRoute.startsWith('/settings')) {
      return 'Settings';
    }
    return 'Shadow Suite';
  }

  List<String> _getContextualTabs() {
    if (currentRoute.startsWith('/money-manager')) {
      return [
        'Dashboard',
        'Accounts',
        'Transactions',
        'Budgets',
        'Reports',
        'Goals',
      ];
    } else if (currentRoute.startsWith('/file-manager')) {
      return ['Browse', 'Cloud', 'Network', 'Media', 'Operations'];
    } else if (currentRoute.startsWith('/excel-to-app')) {
      return ['Dashboard', 'Create', 'My Tools', 'Import'];
    } else if (currentRoute.startsWith('/islamic-app')) {
      return ['Dashboard', 'Quran', 'Hadith', 'Prayer Times', 'Qibla'];
    } else if (currentRoute.startsWith('/memo-suite')) {
      return ['Dashboard', 'Notes', 'Todos', 'Voice Memos', 'Calendar'];
    } else if (currentRoute.startsWith('/shadow-player')) {
      return ['Video', 'Music', 'Settings'];
    } else if (currentRoute.startsWith('/smart-gallery')) {
      return ['All', 'Favorites', 'People', 'Insights'];
    }
    return [];
  }

  bool _isMainDashboard() {
    return currentRoute == '/dashboard' || currentRoute == '/';
  }

  void _handleBackNavigation(BuildContext context, WidgetRef ref) {
    final navigationState = ref.read(navigationStateProvider.notifier);
    navigationState.goBack();

    final newRoute = ref.read(navigationStateProvider).currentRoute;
    context.go(newRoute);
  }

  void _showDashboardConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Return to Dashboard'),
        content: const Text(
          'Are you sure you want to return to the main dashboard? Any unsaved changes may be lost.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.go('/dashboard');
            },
            child: const Text('Return'),
          ),
        ],
      ),
    );
  }

  void _showGlobalSearch(BuildContext context) {
    showSearch(context: context, delegate: GlobalSearchDelegate());
  }

  void _showNotifications(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => const NotificationPanel(),
    );
  }

  void _handleQuickAction(BuildContext context, WidgetRef ref, String action) {
    switch (action) {
      case 'add_transaction':
        _showAddTransaction(context, ref);
        break;
      case 'quick_note':
        _showQuickNote(context, ref);
        break;
      case 'voice_memo':
        _showVoiceMemo(context, ref);
        break;
      case 'settings':
        context.go('/settings');
        break;
    }
  }

  void _showAddTransaction(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => const AddTransactionDialog(),
    );
  }

  void _showQuickNote(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => const QuickNoteDialog(),
    );
  }

  void _showVoiceMemo(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => const VoiceMemoDialog(),
    );
  }
}

// Placeholder widgets - these will be implemented in their respective modules
class GlobalSearchDelegate extends SearchDelegate<String> {
  @override
  List<Widget> buildActions(BuildContext context) => [
    IconButton(icon: const Icon(Icons.clear), onPressed: () => query = ''),
  ];

  @override
  Widget buildLeading(BuildContext context) => IconButton(
    icon: const Icon(Icons.arrow_back),
    onPressed: () => close(context, ''),
  );

  @override
  Widget buildResults(BuildContext context) =>
      const Center(child: Text('Search results will be implemented'));

  @override
  Widget buildSuggestions(BuildContext context) =>
      const Center(child: Text('Search suggestions will be implemented'));
}

class NotificationPanel extends StatelessWidget {
  const NotificationPanel({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      padding: const EdgeInsets.all(16),
      child: const Center(
        child: Text('Notification panel will be implemented'),
      ),
    );
  }
}

class AddTransactionDialog extends StatelessWidget {
  const AddTransactionDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Transaction'),
      content: const Text('Transaction dialog will be implemented'),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
      ],
    );
  }
}

class QuickNoteDialog extends StatelessWidget {
  const QuickNoteDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      padding: const EdgeInsets.all(16),
      child: const Center(child: Text('Quick note dialog will be implemented')),
    );
  }
}

class VoiceMemoDialog extends StatelessWidget {
  const VoiceMemoDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.4,
      padding: const EdgeInsets.all(16),
      child: const Center(child: Text('Voice memo dialog will be implemented')),
    );
  }
}
