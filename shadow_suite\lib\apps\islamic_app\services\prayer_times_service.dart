import 'dart:math' as math;
// import 'package:flutter/foundation.dart'; // Reserved for future debugging

class PrayerTimesService {
  // Calculation methods
  static const Map<String, CalculationMethod> calculationMethods = {
    'muslim_world_league': CalculationMethod(
      name: 'Muslim World League',
      fajrAngle: 18.0,
      ishaAngle: 17.0,
      maghribAngle: 0.0,
      asrMethod: AsrMethod.standard,
    ),
    'islamic_society_north_america': CalculationMethod(
      name: 'Islamic Society of North America',
      fajrAngle: 15.0,
      ishaAngle: 15.0,
      maghribAngle: 0.0,
      asrMethod: AsrMethod.standard,
    ),
    'egyptian_general_authority': CalculationMethod(
      name: 'Egyptian General Authority of Survey',
      fajrAngle: 19.5,
      ishaAngle: 17.5,
      maghribAngle: 0.0,
      asrMethod: AsrMethod.standard,
    ),
    'umm_al_qura': CalculationMethod(
      name: 'Umm Al-Qura University, Makkah',
      fajrAngle: 18.5,
      ishaAngle: 0.0,
      maghribAngle: 90.0, // 90 minutes after Maghrib
      asrMethod: AsrMethod.standard,
    ),
    'university_of_islamic_sciences_karachi': CalculationMethod(
      name: 'University of Islamic Sciences, Karachi',
      fajrAngle: 18.0,
      ishaAngle: 18.0,
      maghribAngle: 0.0,
      asrMethod: AsrMethod.standard,
    ),
    'institute_of_geophysics_tehran': CalculationMethod(
      name: 'Institute of Geophysics, University of Tehran',
      fajrAngle: 17.7,
      ishaAngle: 14.0,
      maghribAngle: 4.5,
      asrMethod: AsrMethod.standard,
    ),
  };

  // Calculate prayer times for a given date and location
  static PrayerTimes calculatePrayerTimes({
    required DateTime date,
    required double latitude,
    required double longitude,
    required double timezone,
    required String calculationMethodKey,
    double elevation = 0.0,
  }) {
    final method = calculationMethods[calculationMethodKey] ?? 
                   calculationMethods['muslim_world_league']!;

    // Julian day calculation
    final julianDay = _getJulianDay(date);
    
    // Sun's position calculations
    final sunPosition = _calculateSunPosition(julianDay, latitude, longitude);
    
    // Calculate prayer times
    final fajrTime = _calculateFajrTime(sunPosition, method.fajrAngle, timezone);
    final sunriseTime = _calculateSunriseTime(sunPosition, timezone, elevation);
    final dhuhrTime = _calculateDhuhrTime(sunPosition, timezone);
    final asrTime = _calculateAsrTime(sunPosition, timezone, method.asrMethod);
    final maghribTime = _calculateMaghribTime(sunPosition, timezone, elevation);
    final ishaTime = _calculateIshaTime(sunPosition, method, timezone);

    return PrayerTimes(
      date: date,
      fajr: fajrTime,
      sunrise: sunriseTime,
      dhuhr: dhuhrTime,
      asr: asrTime,
      maghrib: maghribTime,
      isha: ishaTime,
      calculationMethod: method.name,
      latitude: latitude,
      longitude: longitude,
    );
  }

  // Get Qibla direction (bearing to Kaaba)
  static double calculateQiblaDirection({
    required double latitude,
    required double longitude,
  }) {
    // Kaaba coordinates
    const kaabaLat = 21.4225; // Makkah latitude
    const kaabaLng = 39.8262; // Makkah longitude

    final lat1 = _degreesToRadians(latitude);
    final lat2 = _degreesToRadians(kaabaLat);
    final deltaLng = _degreesToRadians(kaabaLng - longitude);

    final y = math.sin(deltaLng) * math.cos(lat2);
    final x = math.cos(lat1) * math.sin(lat2) - 
              math.sin(lat1) * math.cos(lat2) * math.cos(deltaLng);

    final bearing = math.atan2(y, x);
    return (_radiansToDegrees(bearing) + 360) % 360;
  }

  // Helper methods
  static double _getJulianDay(DateTime date) {
    final a = (14 - date.month) ~/ 12;
    final y = date.year - a;
    final m = date.month + 12 * a - 3;
    
    return date.day + (153 * m + 2) ~/ 5 + 365 * y + y ~/ 4 - y ~/ 100 + y ~/ 400 - 32045;
  }

  static SunPosition _calculateSunPosition(double julianDay, double latitude, double longitude) {
    final n = julianDay - 2451545.0;
    final l = (280.460 + 0.9856474 * n) % 360;
    final g = _degreesToRadians((357.528 + 0.9856003 * n) % 360);
    final lambda = _degreesToRadians(l + 1.915 * math.sin(g) + 0.020 * math.sin(2 * g));
    
    final alpha = math.atan2(math.cos(_degreesToRadians(23.439)) * math.sin(lambda), math.cos(lambda));
    final delta = math.asin(math.sin(_degreesToRadians(23.439)) * math.sin(lambda));
    
    final eqTime = 4 * (l - 0.0057183 - _radiansToDegrees(alpha));
    
    return SunPosition(
      declination: delta,
      equationOfTime: eqTime,
      julianDay: julianDay,
    );
  }

  static DateTime _calculateFajrTime(SunPosition sunPos, double angle, double timezone) {
    final hourAngle = _calculateHourAngle(-angle, sunPos.declination, _degreesToRadians(0)); // Assuming latitude 0 for simplification
    final time = 12 - hourAngle / 15 - sunPos.equationOfTime / 60 + timezone;
    return _timeToDateTime(time);
  }

  static DateTime _calculateSunriseTime(SunPosition sunPos, double timezone, double elevation) {
    final angle = -0.833 - 0.0347 * math.sqrt(elevation);
    final hourAngle = _calculateHourAngle(angle, sunPos.declination, _degreesToRadians(0));
    final time = 12 - hourAngle / 15 - sunPos.equationOfTime / 60 + timezone;
    return _timeToDateTime(time);
  }

  static DateTime _calculateDhuhrTime(SunPosition sunPos, double timezone) {
    final time = 12 - sunPos.equationOfTime / 60 + timezone;
    return _timeToDateTime(time);
  }

  static DateTime _calculateAsrTime(SunPosition sunPos, double timezone, AsrMethod method) {
    final factor = method == AsrMethod.hanafi ? 2.0 : 1.0;
    final angle = -math.atan(1 / (factor + math.tan(sunPos.declination.abs())));
    final hourAngle = _calculateHourAngle(_radiansToDegrees(angle), sunPos.declination, _degreesToRadians(0));
    final time = 12 + hourAngle / 15 - sunPos.equationOfTime / 60 + timezone;
    return _timeToDateTime(time);
  }

  static DateTime _calculateMaghribTime(SunPosition sunPos, double timezone, double elevation) {
    final angle = -0.833 - 0.0347 * math.sqrt(elevation);
    final hourAngle = _calculateHourAngle(angle, sunPos.declination, _degreesToRadians(0));
    final time = 12 + hourAngle / 15 - sunPos.equationOfTime / 60 + timezone;
    return _timeToDateTime(time);
  }

  static DateTime _calculateIshaTime(SunPosition sunPos, CalculationMethod method, double timezone) {
    if (method.maghribAngle > 0) {
      // Fixed interval after Maghrib
      final maghribTime = _calculateMaghribTime(sunPos, timezone, 0);
      return maghribTime.add(Duration(minutes: method.maghribAngle.toInt()));
    } else {
      // Angle-based calculation
      final hourAngle = _calculateHourAngle(-method.ishaAngle, sunPos.declination, _degreesToRadians(0));
      final time = 12 + hourAngle / 15 - sunPos.equationOfTime / 60 + timezone;
      return _timeToDateTime(time);
    }
  }

  static double _calculateHourAngle(double angle, double declination, double latitude) {
    final angleRad = _degreesToRadians(angle);
    final cosH = (math.sin(angleRad) - math.sin(latitude) * math.sin(declination)) /
                 (math.cos(latitude) * math.cos(declination));
    
    if (cosH > 1 || cosH < -1) {
      return 0; // Sun doesn't reach this angle
    }
    
    return _radiansToDegrees(math.acos(cosH));
  }

  static DateTime _timeToDateTime(double time) {
    final hours = time.floor();
    final minutes = ((time - hours) * 60).round();
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day, hours, minutes);
  }

  static double _degreesToRadians(double degrees) => degrees * math.pi / 180;
  static double _radiansToDegrees(double radians) => radians * 180 / math.pi;

  // Get next prayer time
  static NextPrayer getNextPrayer(PrayerTimes prayerTimes) {
    final now = DateTime.now();
    final prayers = [
      Prayer('Fajr', prayerTimes.fajr),
      Prayer('Sunrise', prayerTimes.sunrise),
      Prayer('Dhuhr', prayerTimes.dhuhr),
      Prayer('Asr', prayerTimes.asr),
      Prayer('Maghrib', prayerTimes.maghrib),
      Prayer('Isha', prayerTimes.isha),
    ];

    for (final prayer in prayers) {
      if (prayer.time.isAfter(now)) {
        final timeRemaining = prayer.time.difference(now);
        return NextPrayer(
          name: prayer.name,
          time: prayer.time,
          timeRemaining: timeRemaining,
        );
      }
    }

    // If no prayer today, return tomorrow's Fajr
    final tomorrowFajr = prayerTimes.fajr.add(const Duration(days: 1));
    return NextPrayer(
      name: 'Fajr',
      time: tomorrowFajr,
      timeRemaining: tomorrowFajr.difference(now),
    );
  }
}

// Data classes
class CalculationMethod {
  final String name;
  final double fajrAngle;
  final double ishaAngle;
  final double maghribAngle;
  final AsrMethod asrMethod;

  const CalculationMethod({
    required this.name,
    required this.fajrAngle,
    required this.ishaAngle,
    required this.maghribAngle,
    required this.asrMethod,
  });
}

class PrayerTimes {
  final DateTime date;
  final DateTime fajr;
  final DateTime sunrise;
  final DateTime dhuhr;
  final DateTime asr;
  final DateTime maghrib;
  final DateTime isha;
  final String calculationMethod;
  final double latitude;
  final double longitude;

  const PrayerTimes({
    required this.date,
    required this.fajr,
    required this.sunrise,
    required this.dhuhr,
    required this.asr,
    required this.maghrib,
    required this.isha,
    required this.calculationMethod,
    required this.latitude,
    required this.longitude,
  });

  String formatTime(DateTime time) {
    final hour = time.hour > 12 ? time.hour - 12 : time.hour;
    final displayHour = hour == 0 ? 12 : hour;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.hour >= 12 ? 'PM' : 'AM';
    return '$displayHour:$minute $period';
  }
}

class SunPosition {
  final double declination;
  final double equationOfTime;
  final double julianDay;

  const SunPosition({
    required this.declination,
    required this.equationOfTime,
    required this.julianDay,
  });
}

class Prayer {
  final String name;
  final DateTime time;

  const Prayer(this.name, this.time);
}

class NextPrayer {
  final String name;
  final DateTime time;
  final Duration timeRemaining;

  const NextPrayer({
    required this.name,
    required this.time,
    required this.timeRemaining,
  });

  String get formattedTimeRemaining {
    final hours = timeRemaining.inHours;
    final minutes = timeRemaining.inMinutes % 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }
}

enum AsrMethod { standard, hanafi }
