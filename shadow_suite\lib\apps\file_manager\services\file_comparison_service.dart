import 'dart:io';
import 'package:flutter/foundation.dart';

class FileComparisonService {
  /// Compare two files and return comparison result
  static Future<FileComparisonResult> compareFiles(String file1Path, String file2Path) async {
    final startTime = DateTime.now();
    
    try {
      final file1 = File(file1Path);
      final file2 = File(file2Path);
      
      // Check if files exist
      if (!await file1.exists()) {
        throw FileSystemException('File not found', file1Path);
      }
      if (!await file2.exists()) {
        throw FileSystemException('File not found', file2Path);
      }
      
      // Get file stats
      final stat1 = await file1.stat();
      final stat2 = await file2.stat();
      
      // Basic comparison
      final sizeMatch = stat1.size == stat2.size;
      final modifiedMatch = stat1.modified == stat2.modified;
      
      // Content comparison
      bool contentMatch = false;
      List<DiffLine>? textDiff;
      
      if (sizeMatch) {
        // If sizes match, compare content
        if (_isTextFile(file1Path) && _isTextFile(file2Path)) {
          // Text file comparison with diff
          final content1 = await file1.readAsString();
          final content2 = await file2.readAsString();
          
          contentMatch = content1 == content2;
          if (!contentMatch) {
            textDiff = _generateTextDiff(content1, content2);
          }
        } else {
          // Binary file comparison
          final bytes1 = await file1.readAsBytes();
          final bytes2 = await file2.readAsBytes();
          
          contentMatch = _compareBinaryContent(bytes1, bytes2);
        }
      }
      
      final duration = DateTime.now().difference(startTime);
      
      return FileComparisonResult(
        file1Path: file1Path,
        file2Path: file2Path,
        file1Size: stat1.size,
        file2Size: stat2.size,
        file1Modified: stat1.modified,
        file2Modified: stat2.modified,
        sizeMatch: sizeMatch,
        modifiedMatch: modifiedMatch,
        contentMatch: contentMatch,
        textDiff: textDiff,
        comparisonDuration: duration,
      );
      
    } catch (error) {
      debugPrint('File comparison error: $error');
      rethrow;
    }
  }
  
  /// Compare multiple files in a directory
  static Future<List<FileComparisonResult>> compareDirectory(
    String dir1Path, 
    String dir2Path,
    {bool recursive = false}
  ) async {
    final results = <FileComparisonResult>[];
    
    final dir1 = Directory(dir1Path);
    final dir2 = Directory(dir2Path);
    
    if (!await dir1.exists() || !await dir2.exists()) {
      throw FileSystemException('Directory not found');
    }
    
    final files1 = <String, FileSystemEntity>{};
    final files2 = <String, FileSystemEntity>{};
    
    // Collect files from first directory
    await for (final entity in dir1.list(recursive: recursive)) {
      if (entity is File) {
        final relativePath = entity.path.substring(dir1Path.length);
        files1[relativePath] = entity;
      }
    }
    
    // Collect files from second directory
    await for (final entity in dir2.list(recursive: recursive)) {
      if (entity is File) {
        final relativePath = entity.path.substring(dir2Path.length);
        files2[relativePath] = entity;
      }
    }
    
    // Compare common files
    for (final relativePath in files1.keys) {
      if (files2.containsKey(relativePath)) {
        try {
          final result = await compareFiles(
            files1[relativePath]!.path,
            files2[relativePath]!.path,
          );
          results.add(result);
        } catch (error) {
          debugPrint('Error comparing $relativePath: $error');
        }
      }
    }
    
    return results;
  }
  
  /// Generate text diff between two strings
  static List<DiffLine> _generateTextDiff(String text1, String text2) {
    final lines1 = text1.split('\n');
    final lines2 = text2.split('\n');
    final diff = <DiffLine>[];
    
    // Simple line-by-line diff algorithm
    int i = 0, j = 0;
    
    while (i < lines1.length || j < lines2.length) {
      if (i >= lines1.length) {
        // Remaining lines in text2 are additions
        diff.add(DiffLine(
          type: DiffType.addition,
          lineNumber1: null,
          lineNumber2: j + 1,
          content: lines2[j],
        ));
        j++;
      } else if (j >= lines2.length) {
        // Remaining lines in text1 are deletions
        diff.add(DiffLine(
          type: DiffType.deletion,
          lineNumber1: i + 1,
          lineNumber2: null,
          content: lines1[i],
        ));
        i++;
      } else if (lines1[i] == lines2[j]) {
        // Lines match
        diff.add(DiffLine(
          type: DiffType.unchanged,
          lineNumber1: i + 1,
          lineNumber2: j + 1,
          content: lines1[i],
        ));
        i++;
        j++;
      } else {
        // Lines differ - simple approach: mark as change
        diff.add(DiffLine(
          type: DiffType.deletion,
          lineNumber1: i + 1,
          lineNumber2: null,
          content: lines1[i],
        ));
        diff.add(DiffLine(
          type: DiffType.addition,
          lineNumber1: null,
          lineNumber2: j + 1,
          content: lines2[j],
        ));
        i++;
        j++;
      }
    }
    
    return diff;
  }
  
  /// Compare binary content
  static bool _compareBinaryContent(Uint8List bytes1, Uint8List bytes2) {
    if (bytes1.length != bytes2.length) return false;
    
    for (int i = 0; i < bytes1.length; i++) {
      if (bytes1[i] != bytes2[i]) return false;
    }
    
    return true;
  }
  
  /// Check if file is likely a text file
  static bool _isTextFile(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();
    const textExtensions = {
      'txt', 'md', 'json', 'xml', 'html', 'css', 'js', 'dart', 'py', 'java',
      'cpp', 'c', 'h', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt', 'scala',
      'sql', 'yaml', 'yml', 'toml', 'ini', 'cfg', 'conf', 'log'
    };
    
    return textExtensions.contains(extension);
  }
  
  /// Calculate file hash for quick comparison
  static Future<String> calculateFileHash(String filePath) async {
    final file = File(filePath);
    final bytes = await file.readAsBytes();
    return bytes.hashCode.toString();
  }
  
  /// Find identical files by hash
  static Future<Map<String, List<String>>> findIdenticalFiles(
    List<String> filePaths
  ) async {
    final hashToFiles = <String, List<String>>{};
    
    for (final filePath in filePaths) {
      try {
        final hash = await calculateFileHash(filePath);
        hashToFiles.putIfAbsent(hash, () => []).add(filePath);
      } catch (error) {
        debugPrint('Error hashing file $filePath: $error');
      }
    }
    
    // Return only groups with multiple files
    return Map.fromEntries(
      hashToFiles.entries.where((entry) => entry.value.length > 1)
    );
  }
}

/// File comparison result
class FileComparisonResult {
  final String file1Path;
  final String file2Path;
  final int file1Size;
  final int file2Size;
  final DateTime file1Modified;
  final DateTime file2Modified;
  final bool sizeMatch;
  final bool modifiedMatch;
  final bool contentMatch;
  final List<DiffLine>? textDiff;
  final Duration comparisonDuration;
  
  const FileComparisonResult({
    required this.file1Path,
    required this.file2Path,
    required this.file1Size,
    required this.file2Size,
    required this.file1Modified,
    required this.file2Modified,
    required this.sizeMatch,
    required this.modifiedMatch,
    required this.contentMatch,
    this.textDiff,
    required this.comparisonDuration,
  });
  
  bool get filesIdentical => sizeMatch && contentMatch;
  
  String get comparisonSummary {
    if (filesIdentical) return 'Files are identical';
    if (!sizeMatch) return 'Different file sizes';
    if (!contentMatch) return 'Different content';
    return 'Files differ';
  }
}

/// Diff line representation
class DiffLine {
  final DiffType type;
  final int? lineNumber1;
  final int? lineNumber2;
  final String content;
  
  const DiffLine({
    required this.type,
    this.lineNumber1,
    this.lineNumber2,
    required this.content,
  });
}

enum DiffType {
  unchanged,
  addition,
  deletion,
  modification,
}
