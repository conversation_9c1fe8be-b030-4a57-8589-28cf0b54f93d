import 'performance_optimizer.dart';

/// Comprehensive data validation service for Excel-to-App builder
class DataValidationService {
  static bool _isInitialized = false;
  static final Map<String, ValidationRule> _validationRules = {};
  static final List<ValidationResult> _validationHistory = [];

  /// Initialize the data validation service
  static void initialize() {
    if (_isInitialized) return;

    _createBuiltInRules();
    _isInitialized = true;
  }

  /// Validate data against specified rules
  static ValidationReport validateData(
    List<List<dynamic>> data, {
    List<String>? ruleIds,
    ValidationOptions? options,
  }) {
    return PerformanceOptimizer.measureSync('validate_data', () {
      final startTime = DateTime.now();
      final results = <ValidationResult>[];
      final appliedOptions = options ?? const ValidationOptions();

      // Apply rules
      final rulesToApply =
          ruleIds
              ?.map((id) => _validationRules[id])
              .where((rule) => rule != null)
              .cast<ValidationRule>()
              .toList() ??
          _validationRules.values.toList();

      for (final rule in rulesToApply) {
        if (!appliedOptions.enabledCategories.contains(rule.category)) continue;

        try {
          final result = _applyRule(rule, data, appliedOptions);
          results.add(result);

          if (appliedOptions.stopOnFirstError && !result.isValid) {
            break;
          }
        } catch (e) {
          results.add(
            ValidationResult(
              ruleId: rule.id,
              ruleName: rule.name,
              isValid: false,
              severity: ValidationSeverity.error,
              message: 'Validation rule failed: ${e.toString()}',
              affectedCells: [],
              suggestions: ['Check rule implementation'],
            ),
          );
        }
      }

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      final report = ValidationReport(
        totalRules: rulesToApply.length,
        passedRules: results.where((r) => r.isValid).length,
        failedRules: results.where((r) => !r.isValid).length,
        results: results,
        duration: duration,
        timestamp: endTime,
        dataSize: DataSize(
          rows: data.length,
          columns: data.isNotEmpty ? data.first.length : 0,
          cells: data.length * (data.isNotEmpty ? data.first.length : 0),
        ),
      );

      _validationHistory.add(
        ValidationResult(
          ruleId: 'validation_report',
          ruleName: 'Data Validation Report',
          isValid: report.failedRules == 0,
          severity: report.failedRules == 0
              ? ValidationSeverity.info
              : ValidationSeverity.warning,
          message:
              'Validation completed: ${report.passedRules}/${report.totalRules} rules passed',
          affectedCells: [],
          suggestions: [],
        ),
      );

      return report;
    });
  }

  /// Add custom validation rule
  static void addValidationRule(ValidationRule rule) {
    _validationRules[rule.id] = rule;
  }

  /// Remove validation rule
  static void removeValidationRule(String ruleId) {
    _validationRules.remove(ruleId);
  }

  /// Get all available validation rules
  static List<ValidationRule> get availableRules =>
      _validationRules.values.toList();

  /// Get rules by category
  static List<ValidationRule> getRulesByCategory(ValidationCategory category) {
    return _validationRules.values
        .where((rule) => rule.category == category)
        .toList();
  }

  /// Get validation history
  static List<ValidationResult> get validationHistory =>
      List.unmodifiable(_validationHistory);

  /// Clear validation history
  static void clearValidationHistory() {
    _validationHistory.clear();
  }

  /// Validate single cell value
  static CellValidationResult validateCell(
    dynamic value,
    CellValidationRule rule,
  ) {
    return PerformanceOptimizer.measureSync('validate_cell', () {
      try {
        final isValid = rule.validator(value);
        return CellValidationResult(
          isValid: isValid,
          value: value,
          rule: rule,
          message: isValid ? null : rule.errorMessage,
          suggestion: isValid ? null : rule.suggestion,
        );
      } catch (e) {
        return CellValidationResult(
          isValid: false,
          value: value,
          rule: rule,
          message: 'Validation failed: ${e.toString()}',
          suggestion: 'Check the value format',
        );
      }
    });
  }

  /// Validate data type consistency
  static DataTypeValidationResult validateDataTypes(List<List<dynamic>> data) {
    return PerformanceOptimizer.measureSync('validate_data_types', () {
      if (data.isEmpty) {
        return DataTypeValidationResult(
          isConsistent: true,
          columnTypes: [],
          inconsistencies: [],
        );
      }

      final columnCount = data.first.length;
      final columnTypes = <DataType>[];
      final inconsistencies = <DataTypeInconsistency>[];

      for (int col = 0; col < columnCount; col++) {
        final columnData = data
            .map((row) => row.length > col ? row[col] : null)
            .toList();
        final detectedType = _detectColumnDataType(columnData);
        columnTypes.add(detectedType);

        // Check for inconsistencies
        for (int row = 0; row < data.length; row++) {
          if (data[row].length > col) {
            final cellValue = data[row][col];
            final cellType = _detectValueDataType(cellValue);

            if (cellType != detectedType && cellType != DataType.empty) {
              inconsistencies.add(
                DataTypeInconsistency(
                  row: row,
                  column: col,
                  expectedType: detectedType,
                  actualType: cellType,
                  value: cellValue,
                ),
              );
            }
          }
        }
      }

      return DataTypeValidationResult(
        isConsistent: inconsistencies.isEmpty,
        columnTypes: columnTypes,
        inconsistencies: inconsistencies,
      );
    });
  }

  /// Validate data completeness
  static CompletenessValidationResult validateCompleteness(
    List<List<dynamic>> data, {
    double requiredCompleteness = 0.95,
  }) {
    return PerformanceOptimizer.measureSync('validate_completeness', () {
      if (data.isEmpty) {
        return CompletenessValidationResult(
          overallCompleteness: 1.0,
          columnCompleteness: [],
          missingCells: [],
          meetsRequirement: true,
        );
      }

      final totalCells = data.length * data.first.length;
      int missingCells = 0;
      final columnMissingCounts = List<int>.filled(data.first.length, 0);
      final missingCellPositions = <CellPosition>[];

      for (int row = 0; row < data.length; row++) {
        for (int col = 0; col < data[row].length; col++) {
          final value = data[row][col];
          if (value == null || value.toString().trim().isEmpty) {
            missingCells++;
            columnMissingCounts[col]++;
            missingCellPositions.add(CellPosition(row: row, column: col));
          }
        }
      }

      final overallCompleteness = (totalCells - missingCells) / totalCells;
      final columnCompleteness = columnMissingCounts
          .map((missing) => (data.length - missing) / data.length)
          .toList();

      return CompletenessValidationResult(
        overallCompleteness: overallCompleteness,
        columnCompleteness: columnCompleteness,
        missingCells: missingCellPositions,
        meetsRequirement: overallCompleteness >= requiredCompleteness,
      );
    });
  }

  /// Validate data ranges and bounds
  static RangeValidationResult validateRanges(
    List<List<dynamic>> data,
    List<ColumnRangeRule> rangeRules,
  ) {
    return PerformanceOptimizer.measureSync('validate_ranges', () {
      final violations = <RangeViolation>[];

      for (final rule in rangeRules) {
        if (rule.columnIndex >= (data.isNotEmpty ? data.first.length : 0)) {
          continue;
        }

        for (int row = 0; row < data.length; row++) {
          if (data[row].length > rule.columnIndex) {
            final value = data[row][rule.columnIndex];
            final numValue = _parseNumber(value);

            if (numValue != null) {
              if (rule.minValue != null && numValue < rule.minValue!) {
                violations.add(
                  RangeViolation(
                    row: row,
                    column: rule.columnIndex,
                    value: value,
                    violationType: RangeViolationType.belowMinimum,
                    expectedRange:
                        '${rule.minValue} - ${rule.maxValue ?? 'unlimited'}',
                  ),
                );
              }

              if (rule.maxValue != null && numValue > rule.maxValue!) {
                violations.add(
                  RangeViolation(
                    row: row,
                    column: rule.columnIndex,
                    value: value,
                    violationType: RangeViolationType.aboveMaximum,
                    expectedRange:
                        '${rule.minValue ?? 'unlimited'} - ${rule.maxValue}',
                  ),
                );
              }
            }
          }
        }
      }

      return RangeValidationResult(
        isValid: violations.isEmpty,
        violations: violations,
        checkedRules: rangeRules.length,
      );
    });
  }

  // Private methods
  static void _createBuiltInRules() {
    // Data structure validation
    _validationRules['consistent_columns'] = ValidationRule(
      id: 'consistent_columns',
      name: 'Consistent Column Count',
      description: 'All rows should have the same number of columns',
      category: ValidationCategory.structure,
      severity: ValidationSeverity.error,
      validator: (data) {
        if (data.isEmpty) return true;
        final expectedColumns = data.first.length;
        return data.every((row) => row.length == expectedColumns);
      },
    );

    _validationRules['non_empty_data'] = ValidationRule(
      id: 'non_empty_data',
      name: 'Non-Empty Data',
      description: 'Data should not be empty',
      category: ValidationCategory.structure,
      severity: ValidationSeverity.error,
      validator: (data) => data.isNotEmpty,
    );

    _validationRules['valid_headers'] = ValidationRule(
      id: 'valid_headers',
      name: 'Valid Headers',
      description: 'First row should contain valid column headers',
      category: ValidationCategory.structure,
      severity: ValidationSeverity.warning,
      validator: (data) {
        if (data.isEmpty) return false;
        final headers = data.first;
        return headers.every(
          (header) => header != null && header.toString().trim().isNotEmpty,
        );
      },
    );

    // Data quality validation
    _validationRules['no_duplicate_rows'] = ValidationRule(
      id: 'no_duplicate_rows',
      name: 'No Duplicate Rows',
      description: 'Data should not contain duplicate rows',
      category: ValidationCategory.quality,
      severity: ValidationSeverity.warning,
      validator: (data) {
        final seen = <String>{};
        for (final row in data) {
          final rowString = row.join('|');
          if (seen.contains(rowString)) return false;
          seen.add(rowString);
        }
        return true;
      },
    );

    _validationRules['reasonable_data_size'] = ValidationRule(
      id: 'reasonable_data_size',
      name: 'Reasonable Data Size',
      description: 'Data size should be within reasonable limits',
      category: ValidationCategory.performance,
      severity: ValidationSeverity.warning,
      validator: (data) {
        final totalCells =
            data.length * (data.isNotEmpty ? data.first.length : 0);
        return totalCells <= 100000; // Max 100k cells
      },
    );

    // Data type validation
    _validationRules['consistent_data_types'] = ValidationRule(
      id: 'consistent_data_types',
      name: 'Consistent Data Types',
      description: 'Each column should have consistent data types',
      category: ValidationCategory.dataType,
      severity: ValidationSeverity.warning,
      validator: (data) {
        final typeResult = validateDataTypes(data);
        return typeResult.isConsistent;
      },
    );
  }

  static ValidationResult _applyRule(
    ValidationRule rule,
    List<List<dynamic>> data,
    ValidationOptions options,
  ) {
    final isValid = rule.validator(data);
    final affectedCells = <CellPosition>[];
    final suggestions = <String>[];

    if (!isValid) {
      // Generate suggestions based on rule type
      switch (rule.category) {
        case ValidationCategory.structure:
          suggestions.addAll([
            'Check data format and structure',
            'Ensure all rows have the same number of columns',
            'Verify headers are properly formatted',
          ]);
          break;
        case ValidationCategory.quality:
          suggestions.addAll([
            'Remove duplicate entries',
            'Clean up inconsistent data',
            'Standardize data formats',
          ]);
          break;
        case ValidationCategory.dataType:
          suggestions.addAll([
            'Convert data to consistent types',
            'Check for formatting issues',
            'Validate numeric and date formats',
          ]);
          break;
        case ValidationCategory.performance:
          suggestions.addAll([
            'Consider reducing data size',
            'Split large datasets',
            'Optimize data structure',
          ]);
          break;
        case ValidationCategory.business:
          suggestions.addAll([
            'Review business rules',
            'Check data against requirements',
            'Validate business logic',
          ]);
          break;
      }
    }

    return ValidationResult(
      ruleId: rule.id,
      ruleName: rule.name,
      isValid: isValid,
      severity: rule.severity,
      message: isValid ? 'Validation passed' : rule.description,
      affectedCells: affectedCells,
      suggestions: suggestions,
    );
  }

  static DataType _detectColumnDataType(List<dynamic> columnData) {
    final nonEmptyData = columnData
        .where((value) => value != null && value.toString().trim().isNotEmpty)
        .toList();

    if (nonEmptyData.isEmpty) return DataType.empty;

    // Check for numbers
    if (nonEmptyData.every((value) => _parseNumber(value) != null)) {
      if (nonEmptyData.every((value) => _parseNumber(value)! % 1 == 0)) {
        return DataType.integer;
      }
      return DataType.decimal;
    }

    // Check for dates
    if (nonEmptyData.every(
      (value) => DateTime.tryParse(value.toString()) != null,
    )) {
      return DataType.date;
    }

    // Check for booleans
    if (nonEmptyData.every((value) => _parseBoolean(value) != null)) {
      return DataType.boolean;
    }

    return DataType.text;
  }

  static DataType _detectValueDataType(dynamic value) {
    if (value == null || value.toString().trim().isEmpty) return DataType.empty;

    if (_parseNumber(value) != null) {
      if (_parseNumber(value)! % 1 == 0) return DataType.integer;
      return DataType.decimal;
    }

    if (DateTime.tryParse(value.toString()) != null) return DataType.date;
    if (_parseBoolean(value) != null) return DataType.boolean;

    return DataType.text;
  }

  static double? _parseNumber(dynamic value) {
    if (value is num) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }

  static bool? _parseBoolean(dynamic value) {
    if (value is bool) return value;
    if (value is String) {
      final lower = value.toLowerCase();
      if (['true', 'yes', '1'].contains(lower)) return true;
      if (['false', 'no', '0'].contains(lower)) return false;
    }
    return null;
  }
}

/// Data classes for validation service
class ValidationRule {
  final String id;
  final String name;
  final String description;
  final ValidationCategory category;
  final ValidationSeverity severity;
  final bool Function(List<List<dynamic>>) validator;

  const ValidationRule({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.severity,
    required this.validator,
  });
}

class ValidationResult {
  final String ruleId;
  final String ruleName;
  final bool isValid;
  final ValidationSeverity severity;
  final String message;
  final List<CellPosition> affectedCells;
  final List<String> suggestions;

  const ValidationResult({
    required this.ruleId,
    required this.ruleName,
    required this.isValid,
    required this.severity,
    required this.message,
    required this.affectedCells,
    required this.suggestions,
  });
}

class ValidationReport {
  final int totalRules;
  final int passedRules;
  final int failedRules;
  final List<ValidationResult> results;
  final Duration duration;
  final DateTime timestamp;
  final DataSize dataSize;

  const ValidationReport({
    required this.totalRules,
    required this.passedRules,
    required this.failedRules,
    required this.results,
    required this.duration,
    required this.timestamp,
    required this.dataSize,
  });

  double get successRate => totalRules > 0 ? passedRules / totalRules : 0.0;
}

class ValidationOptions {
  final Set<ValidationCategory> enabledCategories;
  final bool stopOnFirstError;
  final ValidationSeverity minimumSeverity;

  const ValidationOptions({
    this.enabledCategories = const {
      ValidationCategory.structure,
      ValidationCategory.quality,
      ValidationCategory.dataType,
      ValidationCategory.performance,
      ValidationCategory.business,
    },
    this.stopOnFirstError = false,
    this.minimumSeverity = ValidationSeverity.info,
  });
}

class CellValidationRule {
  final String id;
  final String name;
  final String description;
  final bool Function(dynamic) validator;
  final String errorMessage;
  final String suggestion;

  const CellValidationRule({
    required this.id,
    required this.name,
    required this.description,
    required this.validator,
    required this.errorMessage,
    required this.suggestion,
  });
}

class CellValidationResult {
  final bool isValid;
  final dynamic value;
  final CellValidationRule rule;
  final String? message;
  final String? suggestion;

  const CellValidationResult({
    required this.isValid,
    required this.value,
    required this.rule,
    this.message,
    this.suggestion,
  });
}

class DataTypeValidationResult {
  final bool isConsistent;
  final List<DataType> columnTypes;
  final List<DataTypeInconsistency> inconsistencies;

  const DataTypeValidationResult({
    required this.isConsistent,
    required this.columnTypes,
    required this.inconsistencies,
  });
}

class DataTypeInconsistency {
  final int row;
  final int column;
  final DataType expectedType;
  final DataType actualType;
  final dynamic value;

  const DataTypeInconsistency({
    required this.row,
    required this.column,
    required this.expectedType,
    required this.actualType,
    required this.value,
  });
}

class CompletenessValidationResult {
  final double overallCompleteness;
  final List<double> columnCompleteness;
  final List<CellPosition> missingCells;
  final bool meetsRequirement;

  const CompletenessValidationResult({
    required this.overallCompleteness,
    required this.columnCompleteness,
    required this.missingCells,
    required this.meetsRequirement,
  });
}

class RangeValidationResult {
  final bool isValid;
  final List<RangeViolation> violations;
  final int checkedRules;

  const RangeValidationResult({
    required this.isValid,
    required this.violations,
    required this.checkedRules,
  });
}

class ColumnRangeRule {
  final int columnIndex;
  final double? minValue;
  final double? maxValue;
  final String? columnName;

  const ColumnRangeRule({
    required this.columnIndex,
    this.minValue,
    this.maxValue,
    this.columnName,
  });
}

class RangeViolation {
  final int row;
  final int column;
  final dynamic value;
  final RangeViolationType violationType;
  final String expectedRange;

  const RangeViolation({
    required this.row,
    required this.column,
    required this.value,
    required this.violationType,
    required this.expectedRange,
  });
}

class CellPosition {
  final int row;
  final int column;

  const CellPosition({required this.row, required this.column});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CellPosition &&
          runtimeType == other.runtimeType &&
          row == other.row &&
          column == other.column;

  @override
  int get hashCode => row.hashCode ^ column.hashCode;

  @override
  String toString() => 'CellPosition(row: $row, column: $column)';
}

class DataSize {
  final int rows;
  final int columns;
  final int cells;

  const DataSize({
    required this.rows,
    required this.columns,
    required this.cells,
  });
}

/// Enums
enum ValidationCategory { structure, quality, dataType, performance, business }

enum ValidationSeverity { info, warning, error, critical }

enum DataType { empty, text, integer, decimal, date, boolean }

enum RangeViolationType { belowMinimum, aboveMaximum }
