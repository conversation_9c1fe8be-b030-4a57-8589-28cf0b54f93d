[{"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_accounts.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "uri_does_not_exist", "target": {"$mid": 1, "path": "/diagnostics/uri_does_not_exist", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Target of URI doesn't exist: 'money_flow_main.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.", "source": "dart", "startLineNumber": 3, "startColumn": 8, "endLineNumber": 3, "endColumn": 30, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_accounts.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "creation_with_non_type", "target": {"$mid": 1, "path": "/diagnostics/creation_with_non_type", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The name 'MoneyFlowHeader' isn't a class.\nTry correcting the name to match an existing class.", "source": "dart", "startLineNumber": 12, "startColumn": 15, "endLineNumber": 12, "endColumn": 30, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_budgets.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "uri_does_not_exist", "target": {"$mid": 1, "path": "/diagnostics/uri_does_not_exist", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Target of URI doesn't exist: 'money_flow_main.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.", "source": "dart", "startLineNumber": 3, "startColumn": 8, "endLineNumber": 3, "endColumn": 30, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_budgets.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "creation_with_non_type", "target": {"$mid": 1, "path": "/diagnostics/creation_with_non_type", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The name 'MoneyFlowHeader' isn't a class.\nTry correcting the name to match an existing class.", "source": "dart", "startLineNumber": 12, "startColumn": 15, "endLineNumber": 12, "endColumn": 30, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "uri_does_not_exist", "target": {"$mid": 1, "path": "/diagnostics/uri_does_not_exist", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Target of URI doesn't exist: '../models/money_flow_models.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.", "source": "dart", "startLineNumber": 3, "startColumn": 8, "endLineNumber": 3, "endColumn": 42, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "uri_does_not_exist", "target": {"$mid": 1, "path": "/diagnostics/uri_does_not_exist", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Target of URI doesn't exist: '../services/money_flow_providers.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.", "source": "dart", "startLineNumber": 4, "startColumn": 8, "endLineNumber": 4, "endColumn": 47, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'moneyFlowStatisticsProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 49, "startColumn": 39, "endLineNumber": 49, "endColumn": 66, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'moneyFlowAccountsProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 149, "startColumn": 37, "endLineNumber": 149, "endColumn": 62, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'moneyFlowCurrentScreenProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 181, "startColumn": 32, "endLineNumber": 181, "endColumn": 62, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'MoneyFlowScreen'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 182, "startColumn": 27, "endLineNumber": 182, "endColumn": 42, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'MoneyAccount'.\nTry changing the name to the name of an existing class, or creating a class with the name 'MoneyAccount'.", "source": "dart", "startLineNumber": 209, "startColumn": 28, "endLineNumber": 209, "endColumn": 40, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'moneyFlowBudgetsProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 268, "startColumn": 36, "endLineNumber": 268, "endColumn": 60, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'moneyFlowCurrentScreenProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 300, "startColumn": 32, "endLineNumber": 300, "endColumn": 62, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'MoneyFlowScreen'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 301, "startColumn": 27, "endLineNumber": 301, "endColumn": 42, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'MoneyBudget'.\nTry changing the name to the name of an existing class, or creating a class with the name 'MoneyBudget'.", "source": "dart", "startLineNumber": 328, "startColumn": 27, "endLineNumber": 328, "endColumn": 38, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'moneyFlowRecentTransactionsProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 387, "startColumn": 41, "endLineNumber": 387, "endColumn": 76, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'moneyFlowCurrentScreenProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 419, "startColumn": 32, "endLineNumber": 419, "endColumn": 62, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'MoneyFlowScreen'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 420, "startColumn": 27, "endLineNumber": 420, "endColumn": 42, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'MoneyTransactionV2'.\nTry changing the name to the name of an existing class, or creating a class with the name 'MoneyTransactionV2'.", "source": "dart", "startLineNumber": 447, "startColumn": 32, "endLineNumber": 447, "endColumn": 50, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'TransactionType'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 491, "startColumn": 36, "endLineNumber": 491, "endColumn": 51, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'moneyFlowMonthlySpendingProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 504, "startColumn": 37, "endLineNumber": 504, "endColumn": 69, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "body_might_complete_normally", "target": {"$mid": 1, "path": "/diagnostics/body_might_complete_normally", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The body might complete normally, causing 'null' to be returned, but the return type, 'IconData', is a potentially non-nullable type.\nTry adding either a return or a throw statement at the end.", "source": "dart", "startLineNumber": 583, "startColumn": 12, "endLineNumber": 583, "endColumn": 27, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'AccountType'.\nTry changing the name to the name of an existing class, or creating a class with the name 'AccountType'.", "source": "dart", "startLineNumber": 583, "startColumn": 28, "endLineNumber": 583, "endColumn": 39, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'AccountType'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 585, "startColumn": 12, "endLineNumber": 585, "endColumn": 23, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'AccountType'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 587, "startColumn": 12, "endLineNumber": 587, "endColumn": 23, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'AccountType'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 589, "startColumn": 12, "endLineNumber": 589, "endColumn": 23, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'AccountType'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 591, "startColumn": 12, "endLineNumber": 591, "endColumn": 23, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'AccountType'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 593, "startColumn": 12, "endLineNumber": 593, "endColumn": 23, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "body_might_complete_normally", "target": {"$mid": 1, "path": "/diagnostics/body_might_complete_normally", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The body might complete normally, causing 'null' to be returned, but the return type, 'IconData', is a potentially non-nullable type.\nTry adding either a return or a throw statement at the end.", "source": "dart", "startLineNumber": 598, "startColumn": 12, "endLineNumber": 598, "endColumn": 31, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'TransactionType'.\nTry changing the name to the name of an existing class, or creating a class with the name 'TransactionType'.", "source": "dart", "startLineNumber": 598, "startColumn": 32, "endLineNumber": 598, "endColumn": 47, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'TransactionType'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 600, "startColumn": 12, "endLineNumber": 600, "endColumn": 27, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'TransactionType'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 602, "startColumn": 12, "endLineNumber": 602, "endColumn": 27, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'TransactionType'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 604, "startColumn": 12, "endLineNumber": 604, "endColumn": 27, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "body_might_complete_normally", "target": {"$mid": 1, "path": "/diagnostics/body_might_complete_normally", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The body might complete normally, causing 'null' to be returned, but the return type, 'Color', is a potentially non-nullable type.\nTry adding either a return or a throw statement at the end.", "source": "dart", "startLineNumber": 609, "startColumn": 9, "endLineNumber": 609, "endColumn": 29, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'TransactionType'.\nTry changing the name to the name of an existing class, or creating a class with the name 'TransactionType'.", "source": "dart", "startLineNumber": 609, "startColumn": 30, "endLineNumber": 609, "endColumn": 45, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'TransactionType'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 611, "startColumn": 12, "endLineNumber": 611, "endColumn": 27, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'TransactionType'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 613, "startColumn": 12, "endLineNumber": 613, "endColumn": 27, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_dashboard.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'TransactionType'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 615, "startColumn": 12, "endLineNumber": 615, "endColumn": 27, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_reports.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "uri_does_not_exist", "target": {"$mid": 1, "path": "/diagnostics/uri_does_not_exist", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Target of URI doesn't exist: 'money_flow_main.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.", "source": "dart", "startLineNumber": 3, "startColumn": 8, "endLineNumber": 3, "endColumn": 30, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_reports.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "creation_with_non_type", "target": {"$mid": 1, "path": "/diagnostics/creation_with_non_type", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The name 'MoneyFlowHeader' isn't a class.\nTry correcting the name to match an existing class.", "source": "dart", "startLineNumber": 12, "startColumn": 15, "endLineNumber": 12, "endColumn": 30, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_transactions.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "uri_does_not_exist", "target": {"$mid": 1, "path": "/diagnostics/uri_does_not_exist", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Target of URI doesn't exist: 'money_flow_main.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.", "source": "dart", "startLineNumber": 3, "startColumn": 8, "endLineNumber": 3, "endColumn": 30, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/money_flow_v2/screens/money_flow_transactions.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "creation_with_non_type", "target": {"$mid": 1, "path": "/diagnostics/creation_with_non_type", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The name 'MoneyFlowHeader' isn't a class.\nTry correcting the name to match an existing class.", "source": "dart", "startLineNumber": 12, "startColumn": 15, "endLineNumber": 12, "endColumn": 30, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "uri_does_not_exist", "target": {"$mid": 1, "path": "/diagnostics/uri_does_not_exist", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Target of URI doesn't exist: '../services/media_providers.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.", "source": "dart", "startLineNumber": 3, "startColumn": 8, "endLineNumber": 3, "endColumn": 42, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "uri_does_not_exist", "target": {"$mid": 1, "path": "/diagnostics/uri_does_not_exist", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Target of URI doesn't exist: '../models/media_models.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.", "source": "dart", "startLineNumber": 4, "startColumn": 8, "endLineNumber": 4, "endColumn": 37, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'ViewMode'.\nTry changing the name to the name of an existing class, or creating a class with the name 'ViewMode'.", "source": "dart", "startLineNumber": 16, "startColumn": 3, "endLineNumber": 16, "endColumn": 11, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'ViewMode'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 16, "startColumn": 24, "endLineNumber": 16, "endColumn": 32, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'audioFilesProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 22, "startColumn": 34, "endLineNumber": 22, "endColumn": 52, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'mediaLibraryLoadingProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 23, "startColumn": 33, "endLineNumber": 23, "endColumn": 60, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'ViewMode'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 78, "startColumn": 28, "endLineNumber": 78, "endColumn": 36, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'ViewMode'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 79, "startColumn": 28, "endLineNumber": 79, "endColumn": 36, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'ViewMode'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 83, "startColumn": 42, "endLineNumber": 83, "endColumn": 50, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'ViewMode'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 83, "startColumn": 58, "endLineNumber": 83, "endColumn": 66, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "non_type_as_type_argument", "target": {"$mid": 1, "path": "/diagnostics/non_type_as_type_argument", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The name 'MediaFile' isn't a type, so it can't be used as a type argument.\nTry correcting the name to an existing type, or defining a type named 'MediaFile'.", "source": "dart", "startLineNumber": 134, "startColumn": 31, "endLineNumber": 134, "endColumn": 40, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'ViewMode'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 135, "startColumn": 22, "endLineNumber": 135, "endColumn": 30, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'MediaFile'.\nTry changing the name to the name of an existing class, or creating a class with the name 'MediaFile'.", "source": "dart", "startLineNumber": 159, "startColumn": 30, "endLineNumber": 159, "endColumn": 39, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'MediaFile'.\nTry changing the name to the name of an existing class, or creating a class with the name 'MediaFile'.", "source": "dart", "startLineNumber": 197, "startColumn": 30, "endLineNumber": 197, "endColumn": 39, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "non_type_as_type_argument", "target": {"$mid": 1, "path": "/diagnostics/non_type_as_type_argument", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The name 'MediaFile' isn't a type, so it can't be used as a type argument.\nTry correcting the name to an existing type, or defining a type named 'MediaFile'.", "source": "dart", "startLineNumber": 250, "startColumn": 8, "endLineNumber": 250, "endColumn": 17, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "non_type_as_type_argument", "target": {"$mid": 1, "path": "/diagnostics/non_type_as_type_argument", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The name 'MediaFile' isn't a type, so it can't be used as a type argument.\nTry correcting the name to an existing type, or defining a type named 'MediaFile'.", "source": "dart", "startLineNumber": 250, "startColumn": 40, "endLineNumber": 250, "endColumn": 49, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unchecked_use_of_nullable_value", "target": {"$mid": 1, "path": "/diagnostics/unchecked_use_of_nullable_value", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The property 'metadata' can't be unconditionally accessed because the receiver can be 'null'.\nTry making the access conditional (using '?.') or adding a null check to the target ('!').", "source": "dart", "startLineNumber": 258, "startColumn": 27, "endLineNumber": 258, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unchecked_use_of_nullable_value", "target": {"$mid": 1, "path": "/diagnostics/unchecked_use_of_nullable_value", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The property 'displayName' can't be unconditionally accessed because the receiver can be 'null'.\nTry making the access conditional (using '?.') or adding a null check to the target ('!').", "source": "dart", "startLineNumber": 258, "startColumn": 47, "endLineNumber": 258, "endColumn": 58, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unchecked_use_of_nullable_value", "target": {"$mid": 1, "path": "/diagnostics/unchecked_use_of_nullable_value", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The property 'metadata' can't be unconditionally accessed because the receiver can be 'null'.\nTry making the access conditional (using '?.') or adding a null check to the target ('!').", "source": "dart", "startLineNumber": 259, "startColumn": 15, "endLineNumber": 259, "endColumn": 23, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unchecked_use_of_nullable_value", "target": {"$mid": 1, "path": "/diagnostics/unchecked_use_of_nullable_value", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The property 'displayName' can't be unconditionally accessed because the receiver can be 'null'.\nTry making the access conditional (using '?.') or adding a null check to the target ('!').", "source": "dart", "startLineNumber": 259, "startColumn": 35, "endLineNumber": 259, "endColumn": 46, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unchecked_use_of_nullable_value", "target": {"$mid": 1, "path": "/diagnostics/unchecked_use_of_nullable_value", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The property 'metadata' can't be unconditionally accessed because the receiver can be 'null'.\nTry making the access conditional (using '?.') or adding a null check to the target ('!').", "source": "dart", "startLineNumber": 263, "startColumn": 27, "endLineNumber": 263, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unchecked_use_of_nullable_value", "target": {"$mid": 1, "path": "/diagnostics/unchecked_use_of_nullable_value", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The property 'metadata' can't be unconditionally accessed because the receiver can be 'null'.\nTry making the access conditional (using '?.') or adding a null check to the target ('!').", "source": "dart", "startLineNumber": 264, "startColumn": 15, "endLineNumber": 264, "endColumn": 23, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unchecked_use_of_nullable_value", "target": {"$mid": 1, "path": "/diagnostics/unchecked_use_of_nullable_value", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The property 'metadata' can't be unconditionally accessed because the receiver can be 'null'.\nTry making the access conditional (using '?.') or adding a null check to the target ('!').", "source": "dart", "startLineNumber": 268, "startColumn": 27, "endLineNumber": 268, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unchecked_use_of_nullable_value", "target": {"$mid": 1, "path": "/diagnostics/unchecked_use_of_nullable_value", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The property 'metadata' can't be unconditionally accessed because the receiver can be 'null'.\nTry making the access conditional (using '?.') or adding a null check to the target ('!').", "source": "dart", "startLineNumber": 269, "startColumn": 15, "endLineNumber": 269, "endColumn": 23, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unchecked_use_of_nullable_value", "target": {"$mid": 1, "path": "/diagnostics/unchecked_use_of_nullable_value", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The property 'duration' can't be unconditionally accessed because the receiver can be 'null'.\nTry making the access conditional (using '?.') or adding a null check to the target ('!').", "source": "dart", "startLineNumber": 273, "startColumn": 27, "endLineNumber": 273, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unchecked_use_of_nullable_value", "target": {"$mid": 1, "path": "/diagnostics/unchecked_use_of_nullable_value", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The property 'duration' can't be unconditionally accessed because the receiver can be 'null'.\nTry making the access conditional (using '?.') or adding a null check to the target ('!').", "source": "dart", "startLineNumber": 274, "startColumn": 15, "endLineNumber": 274, "endColumn": 23, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unchecked_use_of_nullable_value", "target": {"$mid": 1, "path": "/diagnostics/unchecked_use_of_nullable_value", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The property 'dateAdded' can't be unconditionally accessed because the receiver can be 'null'.\nTry making the access conditional (using '?.') or adding a null check to the target ('!').", "source": "dart", "startLineNumber": 278, "startColumn": 26, "endLineNumber": 278, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unchecked_use_of_nullable_value", "target": {"$mid": 1, "path": "/diagnostics/unchecked_use_of_nullable_value", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The property 'dateAdded' can't be unconditionally accessed because the receiver can be 'null'.\nTry making the access conditional (using '?.') or adding a null check to the target ('!').", "source": "dart", "startLineNumber": 278, "startColumn": 48, "endLineNumber": 278, "endColumn": 57, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'mediaLibraryProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 297, "startColumn": 14, "endLineNumber": 297, "endColumn": 34, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'MediaFile'.\nTry changing the name to the name of an existing class, or creating a class with the name 'MediaFile'.", "source": "dart", "startLineNumber": 300, "startColumn": 19, "endLineNumber": 300, "endColumn": 28, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'currentPlayingMediaProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 302, "startColumn": 14, "endLineNumber": 302, "endColumn": 41, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'MediaFile'.\nTry changing the name to the name of an existing class, or creating a class with the name 'MediaFile'.", "source": "dart", "startLineNumber": 313, "startColumn": 42, "endLineNumber": 313, "endColumn": 51, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'MediaFile'.\nTry changing the name to the name of an existing class, or creating a class with the name 'MediaFile'.", "source": "dart", "startLineNumber": 333, "startColumn": 23, "endLineNumber": 333, "endColumn": 32, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'MediaFile'.\nTry changing the name to the name of an existing class, or creating a class with the name 'MediaFile'.", "source": "dart", "startLineNumber": 360, "startColumn": 21, "endLineNumber": 360, "endColumn": 30, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'mediaLibraryProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 377, "startColumn": 25, "endLineNumber": 377, "endColumn": 45, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/audio_tab_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement add to playlist", "source": "dart", "startLineNumber": 319, "startColumn": 12, "endLineNumber": 319, "endColumn": 43, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/playlist_detail_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "uri_does_not_exist", "target": {"$mid": 1, "path": "/diagnostics/uri_does_not_exist", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Target of URI doesn't exist: '../services/media_providers.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.", "source": "dart", "startLineNumber": 3, "startColumn": 8, "endLineNumber": 3, "endColumn": 42, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/playlist_detail_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "uri_does_not_exist", "target": {"$mid": 1, "path": "/diagnostics/uri_does_not_exist", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Target of URI doesn't exist: '../models/media_models.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.", "source": "dart", "startLineNumber": 4, "startColumn": 8, "endLineNumber": 4, "endColumn": 37, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/playlist_detail_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'Playlist'.\nTry changing the name to the name of an existing class, or creating a class with the name 'Playlist'.", "source": "dart", "startLineNumber": 8, "startColumn": 9, "endLineNumber": 8, "endColumn": 17, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/playlist_detail_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'mediaLibraryProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 20, "startColumn": 34, "endLineNumber": 20, "endColumn": 54, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/playlist_detail_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'MediaFile'.\nTry changing the name to the name of an existing class, or creating a class with the name 'MediaFile'.", "source": "dart", "startLineNumber": 172, "startColumn": 26, "endLineNumber": 172, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/playlist_detail_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'MediaFile'.\nTry changing the name to the name of an existing class, or creating a class with the name 'MediaFile'.", "source": "dart", "startLineNumber": 265, "startColumn": 19, "endLineNumber": 265, "endColumn": 28, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/playlist_detail_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'currentPlayingMediaProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 267, "startColumn": 14, "endLineNumber": 267, "endColumn": 41, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/playlist_detail_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'MediaFile'.\nTry changing the name to the name of an existing class, or creating a class with the name 'MediaFile'.", "source": "dart", "startLineNumber": 273, "startColumn": 42, "endLineNumber": 273, "endColumn": 51, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/playlist_detail_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'MediaFile'.\nTry changing the name to the name of an existing class, or creating a class with the name 'MediaFile'.", "source": "dart", "startLineNumber": 287, "startColumn": 21, "endLineNumber": 287, "endColumn": 30, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/playlist_detail_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'playlists<PERSON><PERSON><PERSON>'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 289, "startColumn": 15, "endLineNumber": 289, "endColumn": 32, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/playlist_detail_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'MediaFile'.\nTry changing the name to the name of an existing class, or creating a class with the name 'MediaFile'.", "source": "dart", "startLineNumber": 296, "startColumn": 23, "endLineNumber": 296, "endColumn": 32, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/playlist_detail_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Show edit playlist dialog", "source": "dart", "startLineNumber": 210, "startColumn": 8, "endLineNumber": 210, "endColumn": 39, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/playlist_detail_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement playlist playback", "source": "dart", "startLineNumber": 231, "startColumn": 8, "endLineNumber": 231, "endColumn": 41, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/playlist_detail_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement shuffle play", "source": "dart", "startLineNumber": 238, "startColumn": 8, "endLineNumber": 238, "endColumn": 36, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/playlist_detail_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement playlist sharing", "source": "dart", "startLineNumber": 245, "startColumn": 8, "endLineNumber": 245, "endColumn": 40, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/playlist_detail_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement playlist export", "source": "dart", "startLineNumber": 252, "startColumn": 8, "endLineNumber": 252, "endColumn": 39, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/screens/playlist_detail_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Show track selection dialog", "source": "dart", "startLineNumber": 259, "startColumn": 8, "endLineNumber": 259, "endColumn": 41, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/audio_player_sheet.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "uri_does_not_exist", "target": {"$mid": 1, "path": "/diagnostics/uri_does_not_exist", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Target of URI doesn't exist: '../models/media_models.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.", "source": "dart", "startLineNumber": 3, "startColumn": 8, "endLineNumber": 3, "endColumn": 37, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/audio_player_sheet.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'MediaFile'.\nTry changing the name to the name of an existing class, or creating a class with the name 'MediaFile'.", "source": "dart", "startLineNumber": 7, "startColumn": 9, "endLineNumber": 7, "endColumn": 18, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/audio_player_sheet.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unused_field", "target": {"$mid": 1, "path": "/diagnostics/unused_field", "scheme": "https", "authority": "dart.dev"}}, "severity": 4, "message": "The value of the field '_isLoading' isn't used.\nTry removing the field, or using it.", "source": "dart", "startLineNumber": 21, "startColumn": 8, "endLineNumber": 21, "endColumn": 18, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/audio_player_sheet.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement previous track", "source": "dart", "startLineNumber": 319, "startColumn": 8, "endLineNumber": 319, "endColumn": 38, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/audio_player_sheet.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement next track", "source": "dart", "startLineNumber": 326, "startColumn": 8, "endLineNumber": 326, "endColumn": 34, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/audio_player_sheet.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement add to playlist", "source": "dart", "startLineNumber": 367, "startColumn": 20, "endLineNumber": 367, "endColumn": 51, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/audio_player_sheet.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement share", "source": "dart", "startLineNumber": 375, "startColumn": 20, "endLineNumber": 375, "endColumn": 41, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/audio_player_sheet.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Show song info", "source": "dart", "startLineNumber": 383, "startColumn": 20, "endLineNumber": 383, "endColumn": 40, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/music_filter_panel.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "uri_does_not_exist", "target": {"$mid": 1, "path": "/diagnostics/uri_does_not_exist", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Target of URI doesn't exist: '../services/shadow_player_providers.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.", "source": "dart", "startLineNumber": 3, "startColumn": 8, "endLineNumber": 3, "endColumn": 50, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/music_filter_panel.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'musicFilterProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 17, "startColumn": 30, "endLineNumber": 17, "endColumn": 49, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/music_filter_panel.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'supportedAudioFormatsProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 18, "startColumn": 40, "endLineNumber": 18, "endColumn": 69, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/music_filter_panel.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'artists<PERSON><PERSON><PERSON>'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 19, "startColumn": 31, "endLineNumber": 19, "endColumn": 46, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/music_filter_panel.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'albumsProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 20, "startColumn": 30, "endLineNumber": 20, "endColumn": 44, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/music_filter_panel.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'genresProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 21, "startColumn": 30, "endLineNumber": 21, "endColumn": 44, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/music_filter_panel.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'musicFilterProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 393, "startColumn": 29, "endLineNumber": 393, "endColumn": 48, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/music_filter_panel.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'musicFilterProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 406, "startColumn": 29, "endLineNumber": 406, "endColumn": 48, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/music_filter_panel.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'musicFilterProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 419, "startColumn": 29, "endLineNumber": 419, "endColumn": 48, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/music_filter_panel.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'musicFilterProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 432, "startColumn": 29, "endLineNumber": 432, "endColumn": 48, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/music_filter_panel.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'MusicFilter'.\nTry changing the name to the name of an existing class, or creating a class with the name 'MusicFilter'.", "source": "dart", "startLineNumber": 444, "startColumn": 22, "endLineNumber": 444, "endColumn": 33, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/music_filter_panel.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'musicFilterProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 445, "startColumn": 14, "endLineNumber": 445, "endColumn": 33, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/music_filter_panel.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_identifier", "target": {"$mid": 1, "path": "/diagnostics/undefined_identifier", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined name 'musicFilterProvider'.\nTry correcting the name to one that is defined, or defining the name.", "source": "dart", "startLineNumber": 449, "startColumn": 14, "endLineNumber": 449, "endColumn": 33, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/music_filter_panel.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "creation_with_non_type", "target": {"$mid": 1, "path": "/diagnostics/creation_with_non_type", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "The name 'Music<PERSON>ilter' isn't a class.\nTry correcting the name to match an existing class.", "source": "dart", "startLineNumber": 449, "startColumn": 58, "endLineNumber": 449, "endColumn": 69, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/music_filter_panel.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'MusicFilter'.\nTry changing the name to the name of an existing class, or creating a class with the name 'MusicFilter'.", "source": "dart", "startLineNumber": 460, "startColumn": 35, "endLineNumber": 460, "endColumn": 46, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/shadow_player/widgets/music_filter_panel.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "undefined_class", "target": {"$mid": 1, "path": "/diagnostics/undefined_class", "scheme": "https", "authority": "dart.dev"}}, "severity": 8, "message": "Undefined class 'MusicFilter'.\nTry changing the name to the name of an existing class, or creating a class with the name 'MusicFilter'.", "source": "dart", "startLineNumber": 461, "startColumn": 3, "endLineNumber": 461, "endColumn": 14, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/tools_builder/widgets/formula_autocomplete_widget.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unused_element", "target": {"$mid": 1, "path": "/diagnostics/unused_element", "scheme": "https", "authority": "dart.dev"}}, "severity": 4, "message": "The declaration '_handleKeyEvent' isn't referenced.\nTry removing the declaration of '_handleKeyEvent'.", "source": "dart", "startLineNumber": 437, "startColumn": 8, "endLineNumber": 437, "endColumn": 23, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/unified_finance/screens/transactions_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unused_element", "target": {"$mid": 1, "path": "/diagnostics/unused_element", "scheme": "https", "authority": "dart.dev"}}, "severity": 4, "message": "The declaration '_buildTransactionItem' isn't referenced.\nTry removing the declaration of '_buildTransactionItem'.", "source": "dart", "startLineNumber": 53, "startColumn": 10, "endLineNumber": 53, "endColumn": 31, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/unified_finance/widgets/add_goal_dialog.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unused_field", "target": {"$mid": 1, "path": "/diagnostics/unused_field", "scheme": "https", "authority": "dart.dev"}}, "severity": 4, "message": "The value of the field '_selectedIcon' isn't used.\nTry removing the field, or using it.", "source": "dart", "startLineNumber": 22, "startColumn": 12, "endLineNumber": 22, "endColumn": 25, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/core/launcher/new_shadow_suite_launcher.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unused_import", "target": {"$mid": 1, "path": "/diagnostics/unused_import", "scheme": "https", "authority": "dart.dev"}}, "severity": 4, "message": "Unused import: '../../apps/islamic_app/screens/quran/surah_list_screen.dart'.\nTry removing the import directive.", "source": "dart", "startLineNumber": 14, "startColumn": 8, "endLineNumber": 14, "endColumn": 69, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/core/launcher/new_shadow_suite_launcher.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unused_element", "target": {"$mid": 1, "path": "/diagnostics/unused_element", "scheme": "https", "authority": "dart.dev"}}, "severity": 4, "message": "The declaration '_buildPlaceholderApp' isn't referenced.\nTry removing the declaration of '_buildPlaceholderApp'.", "source": "dart", "startLineNumber": 672, "startColumn": 17, "endLineNumber": 672, "endColumn": 37, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/core/launcher/new_shadow_suite_launcher.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Open settings", "source": "dart", "startLineNumber": 164, "startColumn": 16, "endLineNumber": 164, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/core/launcher/new_shadow_suite_launcher.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "deprecated_member_use", "target": {"$mid": 1, "path": "/diagnostics/deprecated_member_use", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss.\nTry replacing the use of the deprecated member with the replacement.", "source": "dart", "startLineNumber": 224, "startColumn": 48, "endLineNumber": 224, "endColumn": 59, "tags": [2], "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/core/launcher/new_shadow_suite_launcher.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "deprecated_member_use", "target": {"$mid": 1, "path": "/diagnostics/deprecated_member_use", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss.\nTry replacing the use of the deprecated member with the replacement.", "source": "dart", "startLineNumber": 469, "startColumn": 34, "endLineNumber": 469, "endColumn": 45, "tags": [2], "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/core/launcher/new_shadow_suite_launcher.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "deprecated_member_use", "target": {"$mid": 1, "path": "/diagnostics/deprecated_member_use", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss.\nTry replacing the use of the deprecated member with the replacement.", "source": "dart", "startLineNumber": 484, "startColumn": 38, "endLineNumber": 484, "endColumn": 49, "tags": [2], "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/core/launcher/new_shadow_suite_launcher.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "deprecated_member_use", "target": {"$mid": 1, "path": "/diagnostics/deprecated_member_use", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss.\nTry replacing the use of the deprecated member with the replacement.", "source": "dart", "startLineNumber": 578, "startColumn": 49, "endLineNumber": 578, "endColumn": 60, "tags": [2], "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/core/launcher/new_shadow_suite_launcher.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "deprecated_member_use", "target": {"$mid": 1, "path": "/diagnostics/deprecated_member_use", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss.\nTry replacing the use of the deprecated member with the replacement.", "source": "dart", "startLineNumber": 581, "startColumn": 34, "endLineNumber": 581, "endColumn": 45, "tags": [2], "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/core/launcher/shadow_suite_launcher.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unused_element", "target": {"$mid": 1, "path": "/diagnostics/unused_element", "scheme": "https", "authority": "dart.dev"}}, "severity": 4, "message": "The declaration '_buildPlaceholderApp' isn't referenced.\nTry removing the declaration of '_buildPlaceholderApp'.", "source": "dart", "startLineNumber": 265, "startColumn": 17, "endLineNumber": 265, "endColumn": 37, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/core/services/comprehensive_permission_detection_service.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "dead_code", "target": {"$mid": 1, "path": "/diagnostics/dead_code", "scheme": "https", "authority": "dart.dev"}}, "severity": 4, "message": "Dead code.\nTry removing the code, or fixing the code before it so that it can be reached.", "source": "dart", "startLineNumber": 184, "startColumn": 15, "endLineNumber": 184, "endColumn": 38, "tags": [1], "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/features/dashboard/dashboard_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unused_element", "target": {"$mid": 1, "path": "/diagnostics/unused_element", "scheme": "https", "authority": "dart.dev"}}, "severity": 4, "message": "The declaration '_buildLoadingStatCard' isn't referenced.\nTry removing the declaration of '_buildLoadingStatCard'.", "source": "dart", "startLineNumber": 257, "startColumn": 10, "endLineNumber": 257, "endColumn": 31, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/features/dashboard/dashboard_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement transaction editing", "source": "dart", "startLineNumber": 816, "startColumn": 18, "endLineNumber": 816, "endColumn": 53, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/test/production_readiness/production_readiness_test.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unnecessary_type_check", "target": {"$mid": 1, "path": "/diagnostics/unnecessary_type_check", "scheme": "https", "authority": "dart.dev"}}, "severity": 4, "message": "Unnecessary type check; the result is always 'true'.\nTry correcting the type check, or removing the type check.", "source": "dart", "startLineNumber": 43, "startColumn": 15, "endLineNumber": 43, "endColumn": 51, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/memo_suite/widgets/canvas_drawing_editor.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "deprecated_member_use", "target": {"$mid": 1, "path": "/diagnostics/deprecated_member_use", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "'value' is deprecated and shouldn't be used. Use component accessors like .r or .g, or toARGB32 for an explicit conversion.\nTry replacing the use of the deprecated member with the replacement.", "source": "dart", "startLineNumber": 14, "startColumn": 28, "endLineNumber": 14, "endColumn": 33, "tags": [2], "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/tools_builder/screens/settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement formula bar setting", "source": "dart", "startLineNumber": 617, "startColumn": 24, "endLineNumber": 617, "endColumn": 59, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/tools_builder/screens/settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement highlight dependencies setting", "source": "dart", "startLineNumber": 637, "startColumn": 24, "endLineNumber": 637, "endColumn": 70, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/tools_builder/screens/settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement formula errors setting", "source": "dart", "startLineNumber": 658, "startColumn": 24, "endLineNumber": 658, "endColumn": 62, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/tools_builder/screens/settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement error notifications setting", "source": "dart", "startLineNumber": 678, "startColumn": 24, "endLineNumber": 678, "endColumn": 67, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/tools_builder/screens/settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement preserve formatting setting", "source": "dart", "startLineNumber": 708, "startColumn": 24, "endLineNumber": 708, "endColumn": 67, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/tools_builder/screens/settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement preserve formulas setting", "source": "dart", "startLineNumber": 723, "startColumn": 24, "endLineNumber": 723, "endColumn": 65, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/tools_builder/screens/settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement auto-detect data types setting", "source": "dart", "startLineNumber": 740, "startColumn": 24, "endLineNumber": 740, "endColumn": 70, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/tools_builder/screens/settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement default export format setting", "source": "dart", "startLineNumber": 772, "startColumn": 28, "endLineNumber": 772, "endColumn": 73, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/tools_builder/screens/settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement compression level setting", "source": "dart", "startLineNumber": 796, "startColumn": 28, "endLineNumber": 796, "endColumn": 69, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/tools_builder/screens/settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement enable backups setting", "source": "dart", "startLineNumber": 824, "startColumn": 24, "endLineNumber": 824, "endColumn": 62, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/tools_builder/screens/settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement encrypt data setting", "source": "dart", "startLineNumber": 837, "startColumn": 24, "endLineNumber": 837, "endColumn": 60, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/tools_builder/screens/settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement debug mode setting", "source": "dart", "startLineNumber": 853, "startColumn": 24, "endLineNumber": 853, "endColumn": 58, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/tools_builder/screens/settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement performance metrics setting", "source": "dart", "startLineNumber": 870, "startColumn": 24, "endLineNumber": 870, "endColumn": 67, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/tools_builder/screens/spreadsheet_editor_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement sheet selection", "source": "dart", "startLineNumber": 284, "startColumn": 8, "endLineNumber": 284, "endColumn": 39, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/tools_builder/screens/spreadsheet_editor_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "avoid_print", "target": {"$mid": 1, "path": "/diagnostics/avoid_print", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't invoke 'print' in production code.\nTry using a logging framework.", "source": "dart", "startLineNumber": 320, "startColumn": 7, "endLineNumber": 320, "endColumn": 12, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/tools_builder/screens/spreadsheet_editor_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement share functionality", "source": "dart", "startLineNumber": 343, "startColumn": 8, "endLineNumber": 343, "endColumn": 43, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/tools_builder/screens/spreadsheet_editor_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement print functionality", "source": "dart", "startLineNumber": 398, "startColumn": 8, "endLineNumber": 398, "endColumn": 43, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/tools_builder/screens/spreadsheet_editor_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement settings dialog", "source": "dart", "startLineNumber": 405, "startColumn": 8, "endLineNumber": 405, "endColumn": 39, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/tools_builder/widgets/virtualized_spreadsheet.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unrelated_type_equality_checks", "target": {"$mid": 1, "path": "/diagnostics/unrelated_type_equality_checks", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "The type of the right operand ('String') isn't a subtype or a supertype of the left operand ('CellPosition?').\nTry changing one or both of the operands.", "source": "dart", "startLineNumber": 287, "startColumn": 43, "endLineNumber": 287, "endColumn": 45, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/unified_finance/models/finance_models.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "deprecated_member_use", "target": {"$mid": 1, "path": "/diagnostics/deprecated_member_use", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "'value' is deprecated and shouldn't be used. Use component accessors like .r or .g, or toARGB32 for an explicit conversion.\nTry replacing the use of the deprecated member with the replacement.", "source": "dart", "startLineNumber": 97, "startColumn": 22, "endLineNumber": 97, "endColumn": 27, "tags": [2], "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/apps/unified_finance/screens/account_details_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "deprecated_member_use", "target": {"$mid": 1, "path": "/diagnostics/deprecated_member_use", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss.\nTry replacing the use of the deprecated member with the replacement.", "source": "dart", "startLineNumber": 75, "startColumn": 49, "endLineNumber": 75, "endColumn": 60, "tags": [2], "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/core/database/database_initializer.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "avoid_print", "target": {"$mid": 1, "path": "/diagnostics/avoid_print", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't invoke 'print' in production code.\nTry using a logging framework.", "source": "dart", "startLineNumber": 22, "startColumn": 9, "endLineNumber": 22, "endColumn": 14, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/core/database/database_initializer.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "avoid_print", "target": {"$mid": 1, "path": "/diagnostics/avoid_print", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't invoke 'print' in production code.\nTry using a logging framework.", "source": "dart", "startLineNumber": 25, "startColumn": 9, "endLineNumber": 25, "endColumn": 14, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/core/database/database_initializer.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "avoid_print", "target": {"$mid": 1, "path": "/diagnostics/avoid_print", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't invoke 'print' in production code.\nTry using a logging framework.", "source": "dart", "startLineNumber": 29, "startColumn": 9, "endLineNumber": 29, "endColumn": 14, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/core/database/database_initializer.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "avoid_print", "target": {"$mid": 1, "path": "/diagnostics/avoid_print", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't invoke 'print' in production code.\nTry using a logging framework.", "source": "dart", "startLineNumber": 33, "startColumn": 9, "endLineNumber": 33, "endColumn": 14, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/core/database/database_initializer.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "avoid_print", "target": {"$mid": 1, "path": "/diagnostics/avoid_print", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't invoke 'print' in production code.\nTry using a logging framework.", "source": "dart", "startLineNumber": 37, "startColumn": 7, "endLineNumber": 37, "endColumn": 12, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/core/database/database_initializer.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "avoid_print", "target": {"$mid": 1, "path": "/diagnostics/avoid_print", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't invoke 'print' in production code.\nTry using a logging framework.", "source": "dart", "startLineNumber": 39, "startColumn": 7, "endLineNumber": 39, "endColumn": 12, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/core/database/database_initializer.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "avoid_print", "target": {"$mid": 1, "path": "/diagnostics/avoid_print", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't invoke 'print' in production code.\nTry using a logging framework.", "source": "dart", "startLineNumber": 86, "startColumn": 7, "endLineNumber": 86, "endColumn": 12, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/core/database/database_initializer.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "avoid_print", "target": {"$mid": 1, "path": "/diagnostics/avoid_print", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't invoke 'print' in production code.\nTry using a logging framework.", "source": "dart", "startLineNumber": 90, "startColumn": 9, "endLineNumber": 90, "endColumn": 14, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/main.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "avoid_print", "target": {"$mid": 1, "path": "/diagnostics/avoid_print", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't invoke 'print' in production code.\nTry using a logging framework.", "source": "dart", "startLineNumber": 58, "startColumn": 5, "endLineNumber": 58, "endColumn": 10, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/main.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "avoid_print", "target": {"$mid": 1, "path": "/diagnostics/avoid_print", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't invoke 'print' in production code.\nTry using a logging framework.", "source": "dart", "startLineNumber": 63, "startColumn": 5, "endLineNumber": 63, "endColumn": 10, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/main.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "avoid_print", "target": {"$mid": 1, "path": "/diagnostics/avoid_print", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't invoke 'print' in production code.\nTry using a logging framework.", "source": "dart", "startLineNumber": 65, "startColumn": 5, "endLineNumber": 65, "endColumn": 10, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/main.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "avoid_print", "target": {"$mid": 1, "path": "/diagnostics/avoid_print", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't invoke 'print' in production code.\nTry using a logging framework.", "source": "dart", "startLineNumber": 253, "startColumn": 5, "endLineNumber": 253, "endColumn": 10, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/main.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "avoid_print", "target": {"$mid": 1, "path": "/diagnostics/avoid_print", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't invoke 'print' in production code.\nTry using a logging framework.", "source": "dart", "startLineNumber": 262, "startColumn": 5, "endLineNumber": 262, "endColumn": 10, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/main.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "avoid_print", "target": {"$mid": 1, "path": "/diagnostics/avoid_print", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't invoke 'print' in production code.\nTry using a logging framework.", "source": "dart", "startLineNumber": 271, "startColumn": 5, "endLineNumber": 271, "endColumn": 10, "extensionID": "Dart-Code.dart-code"}, {"resource": "/D:/projects/t2 - Copy/shadow_suite/lib/main.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "avoid_print", "target": {"$mid": 1, "path": "/diagnostics/avoid_print", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't invoke 'print' in production code.\nTry using a logging framework.", "source": "dart", "startLineNumber": 280, "startColumn": 5, "endLineNumber": 280, "endColumn": 10, "extensionID": "Dart-Code.dart-code"}]