import 'package:flutter/material.dart';

// Theme Category Enum
enum ThemeCategory {
  dark,
  light,
  accessibility,
  colorful,
  minimal,
  professional,
  cultural,
  nature,
}

// Layout System Enum
enum LayoutSystem { desktopOptimized, materialDesignMobile, androidNativeSmall }

// Responsive Breakpoints
class ResponsiveBreakpoints {
  final double mobile;
  final double tablet;
  final double desktop;
  final double largeDesktop;

  const ResponsiveBreakpoints({
    this.mobile = 480,
    this.tablet = 768,
    this.desktop = 1024,
    this.largeDesktop = 1440,
  });

  factory ResponsiveBreakpoints.fromJson(Map<String, dynamic> json) {
    return ResponsiveBreakpoints(
      mobile: (json['mobile'] as num?)?.toDouble() ?? 480,
      tablet: (json['tablet'] as num?)?.toDouble() ?? 768,
      desktop: (json['desktop'] as num?)?.toDouble() ?? 1024,
      largeDesktop: (json['large_desktop'] as num?)?.toDouble() ?? 1440,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'mobile': mobile,
      'tablet': tablet,
      'desktop': desktop,
      'large_desktop': largeDesktop,
    };
  }
}

// Touch Target Configuration
class TouchTargetConfig {
  final double minimumSize;
  final double preferredSize;
  final double spacing;
  final EdgeInsets padding;
  final bool enableHapticFeedback;

  const TouchTargetConfig({
    this.minimumSize = 48.0, // Material Design minimum
    this.preferredSize = 56.0,
    this.spacing = 8.0,
    this.padding = const EdgeInsets.all(8.0),
    this.enableHapticFeedback = true,
  });

  factory TouchTargetConfig.fromJson(Map<String, dynamic> json) {
    return TouchTargetConfig(
      minimumSize: (json['minimum_size'] as num?)?.toDouble() ?? 48.0,
      preferredSize: (json['preferred_size'] as num?)?.toDouble() ?? 56.0,
      spacing: (json['spacing'] as num?)?.toDouble() ?? 8.0,
      padding: EdgeInsets.all((json['padding'] as num?)?.toDouble() ?? 8.0),
      enableHapticFeedback: json['enable_haptic_feedback'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'minimum_size': minimumSize,
      'preferred_size': preferredSize,
      'spacing': spacing,
      'padding': padding.left,
      'enable_haptic_feedback': enableHapticFeedback,
    };
  }
}

// Layout Configuration
class LayoutConfiguration {
  final LayoutSystem layoutSystem;
  final ResponsiveBreakpoints breakpoints;
  final TouchTargetConfig touchTargets;
  final bool enableSidebarCollapse;
  final bool showBottomNavigation;
  final bool enableSwipeGestures;
  final double sidebarWidth;
  final double bottomNavHeight;

  const LayoutConfiguration({
    this.layoutSystem = LayoutSystem.desktopOptimized,
    this.breakpoints = const ResponsiveBreakpoints(),
    this.touchTargets = const TouchTargetConfig(),
    this.enableSidebarCollapse = true,
    this.showBottomNavigation = false,
    this.enableSwipeGestures = true,
    this.sidebarWidth = 280.0,
    this.bottomNavHeight = 80.0,
  });

  factory LayoutConfiguration.fromJson(Map<String, dynamic> json) {
    return LayoutConfiguration(
      layoutSystem: LayoutSystem.values.firstWhere(
        (e) => e.name == json['layout_system'],
        orElse: () => LayoutSystem.desktopOptimized,
      ),
      breakpoints: ResponsiveBreakpoints.fromJson(
        json['breakpoints'] as Map<String, dynamic>? ?? {},
      ),
      touchTargets: TouchTargetConfig.fromJson(
        json['touch_targets'] as Map<String, dynamic>? ?? {},
      ),
      enableSidebarCollapse: json['enable_sidebar_collapse'] as bool? ?? true,
      showBottomNavigation: json['show_bottom_navigation'] as bool? ?? false,
      enableSwipeGestures: json['enable_swipe_gestures'] as bool? ?? true,
      sidebarWidth: (json['sidebar_width'] as num?)?.toDouble() ?? 280.0,
      bottomNavHeight: (json['bottom_nav_height'] as num?)?.toDouble() ?? 80.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'layout_system': layoutSystem.name,
      'breakpoints': breakpoints.toJson(),
      'touch_targets': touchTargets.toJson(),
      'enable_sidebar_collapse': enableSidebarCollapse,
      'show_bottom_navigation': showBottomNavigation,
      'enable_swipe_gestures': enableSwipeGestures,
      'sidebar_width': sidebarWidth,
      'bottom_nav_height': bottomNavHeight,
    };
  }
}

// Theme Customizations Model
class ThemeCustomizations {
  final double borderRadius;
  final double elevation;
  final Duration animationDuration;
  final bool enableAnimations;
  final double iconSize;
  final EdgeInsets padding;
  final double spacing;
  final double buttonHeight;
  final bool enableHapticFeedback;
  final bool enableSounds;
  final LayoutConfiguration layoutConfig;

  const ThemeCustomizations({
    required this.borderRadius,
    required this.elevation,
    required this.animationDuration,
    required this.enableAnimations,
    required this.iconSize,
    required this.padding,
    required this.spacing,
    required this.buttonHeight,
    required this.enableHapticFeedback,
    required this.enableSounds,
    this.layoutConfig = const LayoutConfiguration(),
  });

  factory ThemeCustomizations.fromJson(Map<String, dynamic> json) {
    return ThemeCustomizations(
      borderRadius: (json['border_radius'] as num).toDouble(),
      elevation: (json['elevation'] as num).toDouble(),
      animationDuration: Duration(
        milliseconds: json['animation_duration'] as int,
      ),
      enableAnimations: json['enable_animations'] as bool,
      iconSize: (json['icon_size'] as num).toDouble(),
      padding: EdgeInsets.all((json['padding'] as num).toDouble()),
      spacing: (json['spacing'] as num? ?? 16.0).toDouble(),
      buttonHeight: (json['button_height'] as num? ?? 48.0).toDouble(),
      enableHapticFeedback: json['enable_haptic_feedback'] as bool? ?? true,
      enableSounds: json['enable_sounds'] as bool? ?? false,
      layoutConfig: LayoutConfiguration.fromJson(
        json['layout_config'] as Map<String, dynamic>? ?? {},
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'border_radius': borderRadius,
      'elevation': elevation,
      'animation_duration': animationDuration.inMilliseconds,
      'enable_animations': enableAnimations,
      'icon_size': iconSize,
      'padding': padding.left, // Simplified for JSON
      'spacing': spacing,
      'button_height': buttonHeight,
      'enable_haptic_feedback': enableHapticFeedback,
      'enable_sounds': enableSounds,
      'layout_config': layoutConfig.toJson(),
    };
  }
}

// Shadow Suite Theme Model
class ShadowSuiteTheme {
  final String id;
  final String name;
  final String description;
  final ThemeCategory category;
  final ColorScheme colorScheme;
  final TextTheme typography;
  final ThemeCustomizations customizations;

  const ShadowSuiteTheme({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.colorScheme,
    required this.typography,
    required this.customizations,
  });

  factory ShadowSuiteTheme.fromJson(Map<String, dynamic> json) {
    return ShadowSuiteTheme(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      category: ThemeCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => ThemeCategory.dark,
      ),
      colorScheme: _colorSchemeFromJson(
        json['color_scheme'] as Map<String, dynamic>,
      ),
      typography: _textThemeFromJson(
        json['typography'] as Map<String, dynamic>,
      ),
      customizations: ThemeCustomizations.fromJson(
        json['customizations'] as Map<String, dynamic>,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category.name,
      'color_scheme': _colorSchemeToJson(colorScheme),
      'typography': _textThemeToJson(typography),
      'customizations': customizations.toJson(),
    };
  }

  // Helper methods for ColorScheme serialization
  static Map<String, dynamic> _colorSchemeToJson(ColorScheme colorScheme) {
    return {
      'brightness': colorScheme.brightness.name,
      'primary': colorScheme.primary.toARGB32(),
      'on_primary': colorScheme.onPrimary.toARGB32(),
      'secondary': colorScheme.secondary.toARGB32(),
      'on_secondary': colorScheme.onSecondary.toARGB32(),
      'error': colorScheme.error.toARGB32(),
      'on_error': colorScheme.onError.toARGB32(),
      'surface': colorScheme.surface.toARGB32(),
      'on_surface': colorScheme.onSurface.toARGB32(),
    };
  }

  static ColorScheme _colorSchemeFromJson(Map<String, dynamic> json) {
    return ColorScheme(
      brightness: Brightness.values.firstWhere(
        (e) => e.name == json['brightness'],
        orElse: () => Brightness.dark,
      ),
      primary: Color(json['primary'] as int),
      onPrimary: Color(json['on_primary'] as int),
      secondary: Color(json['secondary'] as int),
      onSecondary: Color(json['on_secondary'] as int),
      error: Color(json['error'] as int),
      onError: Color(json['on_error'] as int),
      surface: Color(json['surface'] as int),
      onSurface: Color(json['on_surface'] as int),
    );
  }

  // Helper methods for TextTheme serialization
  static Map<String, dynamic> _textThemeToJson(TextTheme textTheme) {
    return {
      'display_large_size': textTheme.displayLarge?.fontSize ?? 57.0,
      'display_medium_size': textTheme.displayMedium?.fontSize ?? 45.0,
      'display_small_size': textTheme.displaySmall?.fontSize ?? 36.0,
      'headline_large_size': textTheme.headlineLarge?.fontSize ?? 32.0,
      'headline_medium_size': textTheme.headlineMedium?.fontSize ?? 28.0,
      'headline_small_size': textTheme.headlineSmall?.fontSize ?? 24.0,
      'title_large_size': textTheme.titleLarge?.fontSize ?? 22.0,
      'title_medium_size': textTheme.titleMedium?.fontSize ?? 16.0,
      'title_small_size': textTheme.titleSmall?.fontSize ?? 14.0,
      'body_large_size': textTheme.bodyLarge?.fontSize ?? 16.0,
      'body_medium_size': textTheme.bodyMedium?.fontSize ?? 14.0,
      'body_small_size': textTheme.bodySmall?.fontSize ?? 12.0,
      'label_large_size': textTheme.labelLarge?.fontSize ?? 14.0,
      'label_medium_size': textTheme.labelMedium?.fontSize ?? 12.0,
      'label_small_size': textTheme.labelSmall?.fontSize ?? 11.0,
    };
  }

  static TextTheme _textThemeFromJson(Map<String, dynamic> json) {
    return TextTheme(
      displayLarge: TextStyle(
        fontSize: (json['display_large_size'] as num).toDouble(),
      ),
      displayMedium: TextStyle(
        fontSize: (json['display_medium_size'] as num).toDouble(),
      ),
      displaySmall: TextStyle(
        fontSize: (json['display_small_size'] as num).toDouble(),
      ),
      headlineLarge: TextStyle(
        fontSize: (json['headline_large_size'] as num).toDouble(),
      ),
      headlineMedium: TextStyle(
        fontSize: (json['headline_medium_size'] as num).toDouble(),
      ),
      headlineSmall: TextStyle(
        fontSize: (json['headline_small_size'] as num).toDouble(),
      ),
      titleLarge: TextStyle(
        fontSize: (json['title_large_size'] as num).toDouble(),
      ),
      titleMedium: TextStyle(
        fontSize: (json['title_medium_size'] as num).toDouble(),
      ),
      titleSmall: TextStyle(
        fontSize: (json['title_small_size'] as num).toDouble(),
      ),
      bodyLarge: TextStyle(
        fontSize: (json['body_large_size'] as num).toDouble(),
      ),
      bodyMedium: TextStyle(
        fontSize: (json['body_medium_size'] as num).toDouble(),
      ),
      bodySmall: TextStyle(
        fontSize: (json['body_small_size'] as num).toDouble(),
      ),
      labelLarge: TextStyle(
        fontSize: (json['label_large_size'] as num).toDouble(),
      ),
      labelMedium: TextStyle(
        fontSize: (json['label_medium_size'] as num).toDouble(),
      ),
      labelSmall: TextStyle(
        fontSize: (json['label_small_size'] as num).toDouble(),
      ),
    );
  }
}
