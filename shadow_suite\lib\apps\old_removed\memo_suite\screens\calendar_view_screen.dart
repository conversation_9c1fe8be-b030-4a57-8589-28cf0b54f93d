import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:table_calendar/table_calendar.dart';
import '../models/note.dart';
import '../models/todo.dart';
import '../models/voice_memo.dart';
import '../services/memo_providers.dart';
import '../services/database_service.dart';
import 'notes/note_view_screen.dart';
import 'todos/todo_view_screen.dart';
import 'voice_memos/voice_memo_playback_screen.dart';
import '../services/notification_service.dart';

class CalendarViewScreen extends ConsumerStatefulWidget {
  const CalendarViewScreen({super.key});

  @override
  ConsumerState<CalendarViewScreen> createState() => _CalendarViewScreenState();
}

class _CalendarViewScreenState extends ConsumerState<CalendarViewScreen> {
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  Map<DateTime, List<dynamic>> _events = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _selectedDay = DateTime.now();
    _loadEvents();
  }

  Future<void> _loadEvents() async {
    setState(() => _isLoading = true);
    
    try {
      final notes = await MemoSuiteDatabaseService.getAllNotes();
      final todos = await MemoSuiteDatabaseService.getAllTodos();
      final voiceMemos = await MemoSuiteDatabaseService.getAllVoiceMemos();
      
      final Map<DateTime, List<dynamic>> events = {};
      
      // Add notes to events
      for (final note in notes) {
        final date = DateTime(note.createdAt.year, note.createdAt.month, note.createdAt.day);
        if (events[date] == null) events[date] = [];
        events[date]!.add(note);
      }
      
      // Add todos to events
      for (final todo in todos) {
        final date = DateTime(todo.createdAt.year, todo.createdAt.month, todo.createdAt.day);
        if (events[date] == null) events[date] = [];
        events[date]!.add(todo);
        
        // Also add to due date if different
        if (todo.dueDate != null) {
          final dueDate = DateTime(todo.dueDate!.year, todo.dueDate!.month, todo.dueDate!.day);
          if (dueDate != date) {
            if (events[dueDate] == null) events[dueDate] = [];
            events[dueDate]!.add(todo);
          }
        }
      }
      
      // Add voice memos to events
      for (final voiceMemo in voiceMemos) {
        final date = DateTime(voiceMemo.createdAt.year, voiceMemo.createdAt.month, voiceMemo.createdAt.day);
        if (events[date] == null) events[date] = [];
        events[date]!.add(voiceMemo);
      }
      
      setState(() {
        _events = events;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading calendar events: $e')),
        );
      }
    }
  }

  List<dynamic> _getEventsForDay(DateTime day) {
    final normalizedDay = DateTime(day.year, day.month, day.day);
    return _events[normalizedDay] ?? [];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Calendar View'),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () => _showUpcomingReminders(context),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadEvents,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withValues(alpha: 0.1),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: TableCalendar<dynamic>(
                    firstDay: DateTime.utc(2020, 1, 1),
                    lastDay: DateTime.utc(2030, 12, 31),
                    focusedDay: _focusedDay,
                    selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
                    eventLoader: _getEventsForDay,
                    startingDayOfWeek: StartingDayOfWeek.monday,
                    calendarStyle: CalendarStyle(
                      outsideDaysVisible: false,
                      weekendTextStyle: TextStyle(color: Colors.red.shade600),
                      holidayTextStyle: TextStyle(color: Colors.red.shade600),
                      selectedDecoration: BoxDecoration(
                        color: Colors.blue.shade700,
                        shape: BoxShape.circle,
                      ),
                      todayDecoration: BoxDecoration(
                        color: Colors.blue.shade400,
                        shape: BoxShape.circle,
                      ),
                      markerDecoration: BoxDecoration(
                        color: Colors.orange.shade600,
                        shape: BoxShape.circle,
                      ),
                      markersMaxCount: 3,
                    ),
                    headerStyle: const HeaderStyle(
                      formatButtonVisible: false,
                      titleCentered: true,
                      leftChevronIcon: Icon(Icons.chevron_left),
                      rightChevronIcon: Icon(Icons.chevron_right),
                    ),
                    onDaySelected: (selectedDay, focusedDay) {
                      setState(() {
                        _selectedDay = selectedDay;
                        _focusedDay = focusedDay;
                      });
                    },
                    onPageChanged: (focusedDay) {
                      _focusedDay = focusedDay;
                    },
                  ),
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: _buildEventsList(),
                ),
              ],
            ),
    );
  }

  Widget _buildEventsList() {
    final events = _selectedDay != null ? _getEventsForDay(_selectedDay!) : [];
    
    if (events.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.event_note,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'No events for this day',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: events.length,
      itemBuilder: (context, index) {
        final event = events[index];
        return _buildEventCard(event);
      },
    );
  }

  Widget _buildEventCard(dynamic event) {
    IconData icon;
    Color color;
    String title;
    String subtitle;
    VoidCallback onTap;

    if (event is Note) {
      icon = Icons.note;
      color = Colors.blue;
      title = event.title;
      subtitle = 'Note • ${_formatTime(event.createdAt)}';
      onTap = () {
        // Set the selected note and navigate
        ref.read(selectedNoteProvider.notifier).state = event;
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const NoteViewScreen(),
          ),
        );
      };
    } else if (event is Todo) {
      icon = event.isCompleted ? Icons.check_circle : Icons.radio_button_unchecked;
      color = _getPriorityColor(event.priority.displayName);
      title = event.title;
      subtitle = 'Todo • ${_formatTime(event.createdAt)}${event.dueDate != null ? ' • Due: ${_formatDate(event.dueDate!)}' : ''}';
      onTap = () {
        // Set the selected todo and navigate
        ref.read(selectedTodoProvider.notifier).state = event;
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const TodoViewScreen(),
          ),
        );
      };
    } else if (event is VoiceMemo) {
      icon = Icons.mic;
      color = Colors.purple;
      title = event.title;
      subtitle = 'Voice Memo • ${_formatTime(event.createdAt)} • ${event.duration}s';
      onTap = () {
        // Set the selected voice memo and navigate
        ref.read(selectedVoiceMemoProvider.notifier).state = event;
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const VoiceMemoPlaybackScreen(),
          ),
        );
      };
    } else {
      return const SizedBox.shrink();
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withValues(alpha: 0.1),
          child: Icon(icon, color: color),
        ),
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.w500),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(subtitle),
        onTap: onTap,
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      ),
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _formatDate(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }

  void _showUpcomingReminders(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const UpcomingRemindersDialog(),
    );
  }
}

class UpcomingRemindersDialog extends ConsumerWidget {
  const UpcomingRemindersDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final upcomingRemindersAsync = ref.watch(upcomingRemindersProvider);

    return AlertDialog(
      title: const Text('Upcoming Reminders'),
      content: SizedBox(
        width: 400,
        height: 300,
        child: upcomingRemindersAsync.when(
          data: (reminders) {
            if (reminders.isEmpty) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.notifications_none, size: 64, color: Colors.grey),
                    SizedBox(height: 16),
                    Text('No upcoming reminders'),
                  ],
                ),
              );
            }

            return ListView.builder(
              itemCount: reminders.length,
              itemBuilder: (context, index) {
                final reminder = reminders[index];
                return ListTile(
                  leading: Icon(
                    reminder.type == 'note' ? Icons.note : Icons.task_alt,
                    color: reminder.type == 'note' ? Colors.blue : Colors.orange,
                  ),
                  title: Text(reminder.title),
                  subtitle: Text(_formatReminderTime(reminder.scheduledTime)),
                  onTap: () {
                    Navigator.of(context).pop();
                    // Navigate to the item
                  },
                );
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(
            child: Text('Error loading reminders: $error'),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  String _formatReminderTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final reminderDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

    String dateStr;
    if (reminderDate == today) {
      dateStr = 'Today';
    } else if (reminderDate == tomorrow) {
      dateStr = 'Tomorrow';
    } else {
      dateStr = '${dateTime.day}/${dateTime.month}';
    }

    return '$dateStr at ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
