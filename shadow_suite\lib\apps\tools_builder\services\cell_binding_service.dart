import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_component.dart';

// Enhanced Cell Binding Service for isolated component connections with real-time updates
class CellBindingService {
  final Map<String, CellBinding> _bindings = {};
  final Map<String, ComponentIsolation> _isolations = {};
  final Map<String, StreamController<dynamic>> _cellControllers = {};
  final Map<String, StreamController<dynamic>> _componentControllers = {};

  // Create a new cell binding
  String createBinding({
    required String componentId,
    required String cellAddress,
    required String property,
    BindingDirection direction = BindingDirection.bidirectional,
    String? transformer,
  }) {
    final binding = CellBinding(
      componentId: componentId,
      cellAddress: cellAddress,
      property: property,
      direction: direction,
      transformer: transformer,
    );

    _bindings[binding.id] = binding;
    _setupBindingStreams(binding);
    
    return binding.id;
  }

  // Update an existing binding
  void updateBinding(String bindingId, {
    String? cellAddress,
    String? property,
    BindingDirection? direction,
    String? transformer,
    bool? isActive,
  }) {
    final binding = _bindings[bindingId];
    if (binding == null) return;

    final updatedBinding = binding.copyWith(
      cellAddress: cellAddress,
      property: property,
      direction: direction,
      transformer: transformer,
      isActive: isActive,
    );

    _bindings[bindingId] = updatedBinding;
    _updateBindingStreams(updatedBinding);
  }

  // Remove a binding
  void removeBinding(String bindingId) {
    final binding = _bindings[bindingId];
    if (binding == null) return;

    _cleanupBindingStreams(binding);
    _bindings.remove(bindingId);
  }

  // Get all bindings for a component
  List<CellBinding> getComponentBindings(String componentId) {
    return _bindings.values
        .where((binding) => binding.componentId == componentId)
        .toList();
  }

  // Get all bindings for a cell
  List<CellBinding> getCellBindings(String cellAddress) {
    return _bindings.values
        .where((binding) => binding.cellAddress == cellAddress)
        .toList();
  }

  // Create component isolation
  void createIsolation(String componentId, {
    Set<String>? isolatedProperties,
    bool preventCascading = true,
  }) {
    final isolation = ComponentIsolation(
      componentId: componentId,
      isolatedProperties: isolatedProperties ?? {},
      preventCascading: preventCascading,
    );

    _isolations[componentId] = isolation;
  }

  // Update component isolation
  void updateIsolation(String componentId, {
    Set<String>? isolatedProperties,
    Map<String, dynamic>? isolatedState,
    bool? preventCascading,
  }) {
    final isolation = _isolations[componentId];
    if (isolation == null) return;

    _isolations[componentId] = isolation.copyWith(
      isolatedProperties: isolatedProperties,
      isolatedState: isolatedState,
      preventCascading: preventCascading,
    );
  }

  // Remove component isolation
  void removeIsolation(String componentId) {
    _isolations.remove(componentId);
  }

  // Check if a component is isolated
  bool isComponentIsolated(String componentId) {
    return _isolations.containsKey(componentId);
  }

  // Get component isolation
  ComponentIsolation? getComponentIsolation(String componentId) {
    return _isolations[componentId];
  }

  // Update cell value and propagate to bound components
  void updateCellValue(String cellAddress, dynamic value) {
    final bindings = getCellBindings(cellAddress);
    
    for (final binding in bindings) {
      if (!binding.isActive) continue;
      
      // Check if component is isolated
      final isolation = _isolations[binding.componentId];
      if (isolation != null && isolation.isolatedProperties.contains(binding.property)) {
        // Store in isolated state instead of propagating
        final updatedState = Map<String, dynamic>.from(isolation.isolatedState);
        updatedState[binding.property] = _transformValue(value, binding.transformer);
        
        _isolations[binding.componentId] = isolation.copyWith(
          isolatedState: updatedState,
        );
        continue;
      }

      // Propagate to component if direction allows
      if (binding.direction == BindingDirection.input || 
          binding.direction == BindingDirection.bidirectional) {
        final transformedValue = _transformValue(value, binding.transformer);
        _notifyComponent(binding.componentId, binding.property, transformedValue);
      }
    }
  }

  // Update component value and propagate to bound cells
  void updateComponentValue(String componentId, String property, dynamic value) {
    final bindings = getComponentBindings(componentId)
        .where((binding) => binding.property == property)
        .toList();
    
    for (final binding in bindings) {
      if (!binding.isActive) continue;

      // Check if component is isolated
      final isolation = _isolations[componentId];
      if (isolation != null && isolation.preventCascading) {
        // Don't propagate if cascading is prevented
        continue;
      }

      // Propagate to cell if direction allows
      if (binding.direction == BindingDirection.output || 
          binding.direction == BindingDirection.bidirectional) {
        final transformedValue = _transformValue(value, binding.transformer);
        _notifyCell(binding.cellAddress, transformedValue);
      }
    }
  }

  // Get isolated state for a component
  Map<String, dynamic> getIsolatedState(String componentId) {
    final isolation = _isolations[componentId];
    return isolation?.isolatedState ?? {};
  }

  // Apply isolated state to component
  void applyIsolatedState(String componentId) {
    final isolation = _isolations[componentId];
    if (isolation == null) return;

    for (final entry in isolation.isolatedState.entries) {
      _notifyComponent(componentId, entry.key, entry.value);
    }
  }

  // Clear isolated state
  void clearIsolatedState(String componentId) {
    final isolation = _isolations[componentId];
    if (isolation == null) return;

    _isolations[componentId] = isolation.copyWith(
      isolatedState: {},
    );
  }

  // Get cell value stream
  Stream<dynamic> getCellValueStream(String cellAddress) {
    if (!_cellControllers.containsKey(cellAddress)) {
      _cellControllers[cellAddress] = StreamController<dynamic>.broadcast();
    }
    return _cellControllers[cellAddress]!.stream;
  }

  // Get component value stream
  Stream<dynamic> getComponentValueStream(String componentId, String property) {
    final key = '${componentId}_$property';
    if (!_componentControllers.containsKey(key)) {
      _componentControllers[key] = StreamController<dynamic>.broadcast();
    }
    return _componentControllers[key]!.stream;
  }

  // Private helper methods
  void _setupBindingStreams(CellBinding binding) {
    // Initialize streams if they don't exist
    if (!_cellControllers.containsKey(binding.cellAddress)) {
      _cellControllers[binding.cellAddress] = StreamController<dynamic>.broadcast();
    }
    
    final componentKey = '${binding.componentId}_${binding.property}';
    if (!_componentControllers.containsKey(componentKey)) {
      _componentControllers[componentKey] = StreamController<dynamic>.broadcast();
    }
  }

  void _updateBindingStreams(CellBinding binding) {
    // Update existing streams if needed
    _setupBindingStreams(binding);
  }

  void _cleanupBindingStreams(CellBinding binding) {
    // Clean up streams if no more bindings exist
    final cellBindings = getCellBindings(binding.cellAddress);
    if (cellBindings.length <= 1) {
      _cellControllers[binding.cellAddress]?.close();
      _cellControllers.remove(binding.cellAddress);
    }

    final componentBindings = getComponentBindings(binding.componentId)
        .where((b) => b.property == binding.property)
        .toList();
    if (componentBindings.length <= 1) {
      final componentKey = '${binding.componentId}_${binding.property}';
      _componentControllers[componentKey]?.close();
      _componentControllers.remove(componentKey);
    }
  }

  void _notifyCell(String cellAddress, dynamic value) {
    final controller = _cellControllers[cellAddress];
    if (controller != null && !controller.isClosed) {
      controller.add(value);
    }
  }

  void _notifyComponent(String componentId, String property, dynamic value) {
    final key = '${componentId}_$property';
    final controller = _componentControllers[key];
    if (controller != null && !controller.isClosed) {
      controller.add(value);
    }
  }

  dynamic _transformValue(dynamic value, String? transformer) {
    if (transformer == null || transformer.isEmpty) {
      return value;
    }

    // Simple transformation functions
    switch (transformer.toLowerCase()) {
      case 'string':
        return value?.toString() ?? '';
      case 'number':
        return double.tryParse(value?.toString() ?? '0') ?? 0;
      case 'int':
        return int.tryParse(value?.toString() ?? '0') ?? 0;
      case 'bool':
        return value == true || value?.toString().toLowerCase() == 'true';
      case 'uppercase':
        return value?.toString().toUpperCase() ?? '';
      case 'lowercase':
        return value?.toString().toLowerCase() ?? '';
      default:
        return value;
    }
  }

  // Cleanup all resources
  void dispose() {
    for (final controller in _cellControllers.values) {
      controller.close();
    }
    for (final controller in _componentControllers.values) {
      controller.close();
    }
    
    _cellControllers.clear();
    _componentControllers.clear();
    _bindings.clear();
    _isolations.clear();
  }
}

// Provider for the cell binding service
final cellBindingServiceProvider = Provider<CellBindingService>((ref) {
  final service = CellBindingService();
  ref.onDispose(() => service.dispose());
  return service;
});

// Provider for component bindings
final componentBindingsProvider = Provider.family<List<CellBinding>, String>((ref, componentId) {
  final service = ref.watch(cellBindingServiceProvider);
  return service.getComponentBindings(componentId);
});

// Provider for cell bindings
final cellBindingsProvider = Provider.family<List<CellBinding>, String>((ref, cellAddress) {
  final service = ref.watch(cellBindingServiceProvider);
  return service.getCellBindings(cellAddress);
});

// Provider for component isolation
final componentIsolationProvider = Provider.family<ComponentIsolation?, String>((ref, componentId) {
  final service = ref.watch(cellBindingServiceProvider);
  return service.getComponentIsolation(componentId);
});

// Provider for isolated state
final isolatedStateProvider = Provider.family<Map<String, dynamic>, String>((ref, componentId) {
  final service = ref.watch(cellBindingServiceProvider);
  return service.getIsolatedState(componentId);
});
