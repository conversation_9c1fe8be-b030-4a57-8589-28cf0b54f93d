import 'package:sqflite/sqflite.dart';
import 'database_error_handler.dart';

// Database Performance Optimizer
class DatabaseOptimizer {
  // Optimize database performance with various techniques
  static Future<DatabaseResult<bool>> optimizeDatabase(Database db) async {
    try {
      // Run VACUUM to reclaim space and defragment
      await db.execute('VACUUM');
      
      // Analyze tables to update query planner statistics
      await db.execute('ANALYZE');
      
      // Set optimal PRAGMA settings
      await _setOptimalPragmas(db);
      
      // Create missing indexes
      await _createOptimalIndexes(db);
      
      return const DatabaseResult.success(true);
    } catch (e, stackTrace) {
      final error = DatabaseErrorHandler.handleException(
        e,
        stackTrace,
        operation: 'optimizeDatabase',
      );
      return DatabaseResult.failure(error);
    }
  }

  // Set optimal PRAGMA settings for performance
  static Future<void> _setOptimalPragmas(Database db) async {
    // Enable Write-Ahead Logging for better concurrency
    await db.execute('PRAGMA journal_mode = WAL');
    
    // Set synchronous mode to NORMAL for better performance
    await db.execute('PRAGMA synchronous = NORMAL');
    
    // Increase cache size (in KB)
    await db.execute('PRAGMA cache_size = 10000');
    
    // Set temp store to memory for faster operations
    await db.execute('PRAGMA temp_store = MEMORY');
    
    // Enable foreign key constraints
    await db.execute('PRAGMA foreign_keys = ON');
    
    // Set page size to 4096 for better performance
    await db.execute('PRAGMA page_size = 4096');
    
    // Set mmap size for memory-mapped I/O (256MB)
    await db.execute('PRAGMA mmap_size = 268435456');
  }

  // Create indexes for better query performance
  static Future<void> _createOptimalIndexes(Database db) async {
    final indexes = [
      // Memo Suite indexes
      'CREATE INDEX IF NOT EXISTS idx_notes_created_at ON notes(createdAt)',
      'CREATE INDEX IF NOT EXISTS idx_notes_category ON notes(category)',
      'CREATE INDEX IF NOT EXISTS idx_notes_pinned ON notes(isPinned)',
      'CREATE INDEX IF NOT EXISTS idx_notes_scheduled ON notes(scheduledDate)',
      'CREATE INDEX IF NOT EXISTS idx_todos_status ON todos(status)',
      'CREATE INDEX IF NOT EXISTS idx_todos_due_date ON todos(dueDate)',
      'CREATE INDEX IF NOT EXISTS idx_todos_priority ON todos(priority)',
      
      // Money Flow indexes
      'CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(date)',
      'CREATE INDEX IF NOT EXISTS idx_transactions_account ON transactions(accountId)',
      'CREATE INDEX IF NOT EXISTS idx_transactions_category ON transactions(category)',
      'CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type)',
      'CREATE INDEX IF NOT EXISTS idx_accounts_type ON accounts(type)',
      
      // Islamic App indexes
      'CREATE INDEX IF NOT EXISTS idx_bookmarks_surah ON bookmarks(surahNumber)',
      'CREATE INDEX IF NOT EXISTS idx_bookmarks_verse ON bookmarks(verseNumber)',
      'CREATE INDEX IF NOT EXISTS idx_custom_athkar_created ON custom_athkar(createdAt)',
      
      // Tools Builder indexes
      'CREATE INDEX IF NOT EXISTS idx_tools_created_at ON tools(createdAt)',
      'CREATE INDEX IF NOT EXISTS idx_tools_category ON tools(category)',
      'CREATE INDEX IF NOT EXISTS idx_tools_type ON tools(type)',
    ];

    for (final indexSql in indexes) {
      try {
        await db.execute(indexSql);
      } catch (e) {
        // Continue if index creation fails (might already exist)
      }
    }
  }

  // Analyze query performance
  static Future<QueryAnalysis> analyzeQuery(Database db, String sql, [List<dynamic>? arguments]) async {
    try {
      final stopwatch = Stopwatch()..start();
      
      // Execute EXPLAIN QUERY PLAN
      final explainSql = 'EXPLAIN QUERY PLAN $sql';
      final explainResult = await db.rawQuery(explainSql, arguments);
      
      // Execute the actual query to measure time
      final result = await db.rawQuery(sql, arguments);
      stopwatch.stop();
      
      return QueryAnalysis(
        sql: sql,
        executionTimeMs: stopwatch.elapsedMilliseconds,
        rowCount: result.length,
        queryPlan: explainResult,
        isOptimal: _isQueryOptimal(explainResult),
      );
    } catch (e) {
      return QueryAnalysis(
        sql: sql,
        executionTimeMs: -1,
        rowCount: 0,
        queryPlan: [],
        isOptimal: false,
        error: e.toString(),
      );
    }
  }

  // Check if query is using indexes efficiently
  static bool _isQueryOptimal(List<Map<String, dynamic>> queryPlan) {
    for (final step in queryPlan) {
      final detail = step['detail']?.toString().toLowerCase() ?? '';
      
      // Check for table scans (usually bad for performance)
      if (detail.contains('scan table') && !detail.contains('using index')) {
        return false;
      }
      
      // Check for temporary B-trees (can indicate missing indexes)
      if (detail.contains('use temp b-tree')) {
        return false;
      }
    }
    
    return true;
  }

  // Get database statistics
  static Future<DatabaseStats> getDatabaseStats(Database db) async {
    try {
      // Get page count and page size
      final pageCountResult = await db.rawQuery('PRAGMA page_count');
      final pageSizeResult = await db.rawQuery('PRAGMA page_size');
      
      final pageCount = pageCountResult.first['page_count'] as int;
      final pageSize = pageSizeResult.first['page_size'] as int;
      final totalSize = pageCount * pageSize;
      
      // Get free pages
      final freePagesResult = await db.rawQuery('PRAGMA freelist_count');
      final freePages = freePagesResult.first['freelist_count'] as int;
      final freeSize = freePages * pageSize;
      
      // Get table information
      final tables = await _getTableStats(db);
      
      return DatabaseStats(
        totalSizeBytes: totalSize,
        freeSizeBytes: freeSize,
        usedSizeBytes: totalSize - freeSize,
        pageCount: pageCount,
        pageSize: pageSize,
        freePages: freePages,
        tables: tables,
      );
    } catch (e) {
      return DatabaseStats(
        totalSizeBytes: 0,
        freeSizeBytes: 0,
        usedSizeBytes: 0,
        pageCount: 0,
        pageSize: 0,
        freePages: 0,
        tables: [],
      );
    }
  }

  // Get statistics for each table
  static Future<List<TableStats>> _getTableStats(Database db) async {
    final tables = <TableStats>[];
    
    try {
      // Get all table names
      final tableResult = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
      );
      
      for (final table in tableResult) {
        final tableName = table['name'] as String;
        
        // Get row count
        final countResult = await db.rawQuery('SELECT COUNT(*) as count FROM $tableName');
        final rowCount = countResult.first['count'] as int;
        
        tables.add(TableStats(
          name: tableName,
          rowCount: rowCount,
        ));
      }
    } catch (e) {
      // Continue if table stats fail
    }
    
    return tables;
  }

  // Clean up old data based on retention policies
  static Future<DatabaseResult<int>> cleanupOldData(Database db, {
    int? retentionDays,
  }) async {
    try {
      final cutoffDate = DateTime.now().subtract(Duration(days: retentionDays ?? 365));
      final cutoffTimestamp = cutoffDate.millisecondsSinceEpoch;
      
      int deletedRows = 0;
      
      // Clean up old notes (if not pinned)
      final notesDeleted = await db.delete(
        'notes',
        where: 'createdAt < ? AND isPinned = 0',
        whereArgs: [cutoffTimestamp],
      );
      deletedRows += notesDeleted;
      
      // Clean up completed todos older than retention period
      final todosDeleted = await db.delete(
        'todos',
        where: 'createdAt < ? AND status = ?',
        whereArgs: [cutoffTimestamp, 'done'],
      );
      deletedRows += todosDeleted;
      
      // Clean up old transactions (keep financial data longer)
      final financialCutoff = DateTime.now().subtract(Duration(days: (retentionDays ?? 365) * 3));
      final transactionsDeleted = await db.delete(
        'transactions',
        where: 'createdAt < ?',
        whereArgs: [financialCutoff.millisecondsSinceEpoch],
      );
      deletedRows += transactionsDeleted;
      
      return DatabaseResult.success(deletedRows);
    } catch (e, stackTrace) {
      final error = DatabaseErrorHandler.handleException(
        e,
        stackTrace,
        operation: 'cleanupOldData',
      );
      return DatabaseResult.failure(error);
    }
  }
}

// Query Analysis Result
class QueryAnalysis {
  final String sql;
  final int executionTimeMs;
  final int rowCount;
  final List<Map<String, dynamic>> queryPlan;
  final bool isOptimal;
  final String? error;

  QueryAnalysis({
    required this.sql,
    required this.executionTimeMs,
    required this.rowCount,
    required this.queryPlan,
    required this.isOptimal,
    this.error,
  });

  bool get hasError => error != null;
  bool get isSlowQuery => executionTimeMs > 1000; // More than 1 second
}

// Database Statistics
class DatabaseStats {
  final int totalSizeBytes;
  final int freeSizeBytes;
  final int usedSizeBytes;
  final int pageCount;
  final int pageSize;
  final int freePages;
  final List<TableStats> tables;

  DatabaseStats({
    required this.totalSizeBytes,
    required this.freeSizeBytes,
    required this.usedSizeBytes,
    required this.pageCount,
    required this.pageSize,
    required this.freePages,
    required this.tables,
  });

  String get formattedTotalSize => _formatBytes(totalSizeBytes);
  String get formattedUsedSize => _formatBytes(usedSizeBytes);
  String get formattedFreeSize => _formatBytes(freeSizeBytes);
  
  double get usagePercentage => totalSizeBytes > 0 ? (usedSizeBytes / totalSizeBytes) * 100 : 0;

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}

// Table Statistics
class TableStats {
  final String name;
  final int rowCount;

  TableStats({
    required this.name,
    required this.rowCount,
  });
}
