import 'dart:async';
import '../models/media_models.dart';

class MediaPlayerService {
  MediaFile? _currentMedia;
  Playlist? _currentPlaylist;
  int _currentIndex = 0;
  PlayerState _playerState = PlayerState.stopped;
  PlayMode _playMode = PlayMode.sequential;
  double _volume = 1.0;
  double _playbackSpeed = 1.0;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  final bool _isMuted = false;

  final StreamController<MediaPlayerEvent> _eventController =
      StreamController<MediaPlayerEvent>.broadcast();

  Timer? _positionTimer;

  // Initialize media player
  Future<void> initialize() async {
    // Initialize audio session and media controls
  }

  // Play media file
  Future<void> playMedia(
    MediaFile media, {
    Playlist? playlist,
    int? index,
  }) async {
    _currentMedia = media;
    _currentPlaylist = playlist;
    _currentIndex = index ?? 0;

    await stop();
    await _loadMedia(media);
    await play();

    _notifyEvent(
      MediaPlayerEvent(
        type: MediaPlayerEventType.mediaChanged,
        media: media,
        message: 'Now playing: ${media.displayName}',
        timestamp: DateTime.now(),
      ),
    );
  }

  // Play/Resume
  Future<void> play() async {
    if (_currentMedia == null) return;

    _playerState = PlayerState.playing;
    _startPositionTimer();
    _notifyStateChange();

    _notifyEvent(
      MediaPlayerEvent(
        type: MediaPlayerEventType.playbackStarted,
        media: _currentMedia,
        message: 'Playback started',
        timestamp: DateTime.now(),
      ),
    );
  }

  // Pause
  Future<void> pause() async {
    _playerState = PlayerState.paused;
    _stopPositionTimer();
    _notifyStateChange();

    _notifyEvent(
      MediaPlayerEvent(
        type: MediaPlayerEventType.playbackPaused,
        media: _currentMedia,
        message: 'Playback paused',
        timestamp: DateTime.now(),
      ),
    );
  }

  // Stop
  Future<void> stop() async {
    _playerState = PlayerState.stopped;
    _currentPosition = Duration.zero;
    _stopPositionTimer();
    _notifyStateChange();

    _notifyEvent(
      MediaPlayerEvent(
        type: MediaPlayerEventType.playbackStopped,
        media: _currentMedia,
        message: 'Playback stopped',
        timestamp: DateTime.now(),
      ),
    );
  }

  // Toggle play/pause
  Future<void> togglePlayPause() async {
    if (_playerState == PlayerState.playing) {
      await pause();
    } else if (_playerState == PlayerState.paused ||
        _playerState == PlayerState.stopped) {
      await play();
    }
  }

  // Seek to position
  Future<void> seekTo(Duration position) async {
    if (_currentMedia == null) return;
    _currentPosition = position;

    _notifyEvent(
      MediaPlayerEvent(
        type: MediaPlayerEventType.positionChanged,
        media: _currentMedia,
        position: position,
        message: 'Seeked to ${_formatDuration(position)}',
        timestamp: DateTime.now(),
      ),
    );
  }

  // Next track
  Future<void> nextTrack() async {
    if (_currentPlaylist == null || _currentPlaylist!.mediaIds.isEmpty) return;

    int nextIndex = _getNextIndex();
    if (nextIndex != -1) {
      _currentIndex = nextIndex;
      _notifyEvent(
        MediaPlayerEvent(
          type: MediaPlayerEventType.trackChanged,
          message: 'Next track',
          timestamp: DateTime.now(),
        ),
      );
    }
  }

  // Previous track
  Future<void> previousTrack() async {
    if (_currentPlaylist == null || _currentPlaylist!.mediaIds.isEmpty) return;

    int prevIndex = _getPreviousIndex();
    if (prevIndex != -1) {
      _currentIndex = prevIndex;
      _notifyEvent(
        MediaPlayerEvent(
          type: MediaPlayerEventType.trackChanged,
          message: 'Previous track',
          timestamp: DateTime.now(),
        ),
      );
    }
  }

  // Set volume
  Future<void> setVolume(double volume) async {
    _volume = volume.clamp(0.0, 1.0);

    _notifyEvent(
      MediaPlayerEvent(
        type: MediaPlayerEventType.volumeChanged,
        media: _currentMedia,
        message: 'Volume: ${(_volume * 100).round()}%',
        timestamp: DateTime.now(),
      ),
    );
  }

  // Set playback speed
  Future<void> setPlaybackSpeed(double speed) async {
    _playbackSpeed = speed.clamp(0.25, 4.0);

    _notifyEvent(
      MediaPlayerEvent(
        type: MediaPlayerEventType.speedChanged,
        media: _currentMedia,
        message: 'Speed: ${_playbackSpeed}x',
        timestamp: DateTime.now(),
      ),
    );
  }

  // Set play mode
  void setPlayMode(PlayMode mode) {
    _playMode = mode;

    _notifyEvent(
      MediaPlayerEvent(
        type: MediaPlayerEventType.playModeChanged,
        media: _currentMedia,
        message: 'Play mode: ${mode.name}',
        timestamp: DateTime.now(),
      ),
    );
  }

  // Private helper methods
  Future<void> _loadMedia(MediaFile media) async {
    _totalDuration = media.duration ?? const Duration(minutes: 3);
    _currentPosition = Duration.zero;
  }

  void _startPositionTimer() {
    _stopPositionTimer();
    _positionTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_playerState == PlayerState.playing) {
        _currentPosition = Duration(seconds: _currentPosition.inSeconds + 1);

        if (_currentPosition >= _totalDuration) {
          _onTrackCompleted();
        }

        _notifyEvent(
          MediaPlayerEvent(
            type: MediaPlayerEventType.positionChanged,
            media: _currentMedia,
            position: _currentPosition,
            message: 'Position updated',
            timestamp: DateTime.now(),
          ),
        );
      }
    });
  }

  void _stopPositionTimer() {
    _positionTimer?.cancel();
    _positionTimer = null;
  }

  void _onTrackCompleted() {
    _notifyEvent(
      MediaPlayerEvent(
        type: MediaPlayerEventType.trackCompleted,
        media: _currentMedia,
        message: 'Track completed',
        timestamp: DateTime.now(),
      ),
    );

    switch (_playMode) {
      case PlayMode.sequential:
      case PlayMode.shuffle:
        nextTrack();
        break;
      case PlayMode.repeatOne:
        seekTo(Duration.zero);
        play();
        break;
      case PlayMode.repeatAll:
        if (_isLastTrack()) {
          _currentIndex = 0;
        }
        nextTrack();
        break;
    }
  }

  int _getNextIndex() {
    if (_currentPlaylist == null) return -1;
    return (_currentIndex + 1) % _currentPlaylist!.mediaIds.length;
  }

  int _getPreviousIndex() {
    if (_currentPlaylist == null) return -1;
    return _currentIndex > 0
        ? _currentIndex - 1
        : _currentPlaylist!.mediaIds.length - 1;
  }

  bool _isLastTrack() {
    if (_currentPlaylist == null) return true;
    return _currentIndex >= _currentPlaylist!.mediaIds.length - 1;
  }

  void _notifyStateChange() {
    _notifyEvent(
      MediaPlayerEvent(
        type: MediaPlayerEventType.stateChanged,
        media: _currentMedia,
        state: _playerState,
        message: 'State: ${_playerState.name}',
        timestamp: DateTime.now(),
      ),
    );
  }

  void _notifyEvent(MediaPlayerEvent event) {
    _eventController.add(event);
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  // Getters
  MediaFile? get currentMedia => _currentMedia;
  Playlist? get currentPlaylist => _currentPlaylist;
  int get currentIndex => _currentIndex;
  PlayerState get playerState => _playerState;
  PlayMode get playMode => _playMode;
  double get volume => _volume;
  double get playbackSpeed => _playbackSpeed;
  Duration get currentPosition => _currentPosition;
  Duration get totalDuration => _totalDuration;
  bool get isMuted => _isMuted;
  Stream<MediaPlayerEvent> get eventStream => _eventController.stream;

  // Dispose
  Future<void> dispose() async {
    await stop();
    _stopPositionTimer();
    _eventController.close();
  }
}

// Media Player Event
class MediaPlayerEvent {
  final MediaPlayerEventType type;
  final MediaFile? media;
  final PlayerState? state;
  final Duration? position;
  final String message;
  final DateTime timestamp;

  const MediaPlayerEvent({
    required this.type,
    this.media,
    this.state,
    this.position,
    required this.message,
    required this.timestamp,
  });
}

enum MediaPlayerEventType {
  mediaChanged,
  stateChanged,
  playbackStarted,
  playbackPaused,
  playbackStopped,
  trackChanged,
  trackCompleted,
  positionChanged,
  volumeChanged,
  speedChanged,
  playModeChanged,
  error,
}
