import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_theme.dart';
import '../../services/memo_providers.dart';
import '../../models/todo.dart';

class TodoViewScreen extends ConsumerWidget {
  const TodoViewScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedTodo = ref.watch(selectedTodoProvider);

    if (selectedTodo == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Todo'),
          backgroundColor: AppTheme.memoSuiteColor,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              ref.read(memoSuiteCurrentScreenProvider.notifier).state = MemoSuiteScreen.todosList;
            },
          ),
        ),
        body: const Center(
          child: Text('No todo selected'),
        ),
      );
    }

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Todo Details'),
        backgroundColor: AppTheme.memoSuiteColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            ref.read(memoSuiteCurrentScreenProvider.notifier).state = MemoSuiteScreen.todosList;
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              ref.read(memoSuiteCurrentScreenProvider.notifier).state = MemoSuiteScreen.todoEditor;
            },
          ),
          PopupMenuButton<String>(
            onSelected: (value) => _handleMenuAction(context, ref, selectedTodo, value),
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'move', child: Text('Move Status')),
              const PopupMenuItem(value: 'duplicate', child: Text('Duplicate')),
              const PopupMenuItem(value: 'delete', child: Text('Delete')),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTodoHeader(context, ref, selectedTodo),
            const SizedBox(height: 24),
            _buildTodoContent(context, selectedTodo),
            const SizedBox(height: 24),
            if (selectedTodo.subTasks.isNotEmpty) ...[
              _buildSubTasksSection(context, ref, selectedTodo),
              const SizedBox(height: 24),
            ],
            _buildTodoMetadata(context, selectedTodo),
          ],
        ),
      ),
    );
  }

  Widget _buildTodoHeader(BuildContext context, WidgetRef ref, Todo todo) {
    final priorityColor = _getPriorityColor(todo.priority);
    final statusColor = _getStatusColor(todo.status);
    final isOverdue = todo.isOverdue;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Checkbox(
                  value: todo.isCompleted,
                  onChanged: (value) {
                    final updatedTodo = todo.copyWith(
                      status: value == true ? TodoStatus.done : TodoStatus.todo,
                    );
                    ref.read(todosProvider.notifier).updateTodo(updatedTodo);
                    ref.read(selectedTodoProvider.notifier).state = updatedTodo;
                  },
                  activeColor: priorityColor,
                ),
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: priorityColor,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    todo.title,
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isOverdue ? Colors.red[700] : AppTheme.primaryColor,
                      decoration: todo.isCompleted ? TextDecoration.lineThrough : null,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildStatusChip(todo.status, statusColor),
                const SizedBox(width: 12),
                _buildPriorityChip(todo.priority, priorityColor),
                const SizedBox(width: 12),
                _buildCategoryChip(todo.category),
              ],
            ),
            if (todo.dueDate != null) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Icon(
                    isOverdue ? Icons.warning : Icons.schedule,
                    size: 20,
                    color: isOverdue ? Colors.red : Colors.grey[600],
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Due: ${_formatDueDate(todo.dueDate!)}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: isOverdue ? Colors.red : Colors.grey[700],
                      fontWeight: isOverdue ? FontWeight.w600 : null,
                    ),
                  ),
                  if (isOverdue) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.red[100],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'OVERDUE',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.red[700],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTodoContent(BuildContext context, Todo todo) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.description,
                  color: AppTheme.memoSuiteColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Description',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.memoSuiteColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SelectableText(
              todo.description.isEmpty ? 'No description provided' : todo.description,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                height: 1.6,
                color: todo.description.isEmpty ? Colors.grey[500] : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubTasksSection(BuildContext context, WidgetRef ref, Todo todo) {
    final completedCount = todo.subTasks.where((task) => task.isCompleted).length;
    final totalCount = todo.subTasks.length;
    final progressPercentage = todo.progressPercentage;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.checklist,
                  color: AppTheme.memoSuiteColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Subtasks',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.memoSuiteColor,
                  ),
                ),
                const Spacer(),
                Text(
                  '$completedCount/$totalCount completed',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: progressPercentage,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.memoSuiteColor),
            ),
            const SizedBox(height: 4),
            Text(
              '${(progressPercentage * 100).toInt()}% complete',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),
            ...todo.subTasks.map((subTask) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Checkbox(
                    value: subTask.isCompleted,
                    onChanged: (value) {
                      final updatedSubTasks = todo.subTasks.map((task) {
                        if (task.id == subTask.id) {
                          return task.copyWith(isCompleted: value ?? false);
                        }
                        return task;
                      }).toList();
                      
                      final updatedTodo = todo.copyWith(subTasks: updatedSubTasks);
                      ref.read(todosProvider.notifier).updateTodo(updatedTodo);
                      ref.read(selectedTodoProvider.notifier).state = updatedTodo;
                    },
                  ),
                  Expanded(
                    child: Text(
                      subTask.title,
                      style: TextStyle(
                        decoration: subTask.isCompleted
                            ? TextDecoration.lineThrough
                            : null,
                        color: subTask.isCompleted
                            ? Colors.grey[600]
                            : null,
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildTodoMetadata(BuildContext context, Todo todo) {
    return Card(
      color: Colors.grey[50],
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.grey[600],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Todo Information',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildMetadataRow(context, 'Created', _formatFullDate(todo.createdAt)),
            const SizedBox(height: 8),
            _buildMetadataRow(context, 'Last Modified', _formatFullDate(todo.updatedAt)),
            const SizedBox(height: 8),
            _buildMetadataRow(context, 'Progress', '${(todo.progressPercentage * 100).toInt()}%'),
            const SizedBox(height: 8),
            _buildMetadataRow(context, 'Subtasks', todo.subTasks.length.toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(TodoStatus status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(_getStatusIcon(status), size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            status.displayName,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityChip(TodoPriority priority, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        priority.displayName,
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildCategoryChip(String category) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Text(
        category,
        style: TextStyle(
          color: Colors.grey[700],
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildMetadataRow(BuildContext context, String label, String value) {
    return Row(
      children: [
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Colors.grey[800],
          ),
        ),
      ],
    );
  }

  Color _getPriorityColor(TodoPriority priority) {
    switch (priority) {
      case TodoPriority.low:
        return Colors.green;
      case TodoPriority.medium:
        return Colors.orange;
      case TodoPriority.high:
        return Colors.red;
    }
  }

  Color _getStatusColor(TodoStatus status) {
    switch (status) {
      case TodoStatus.todo:
        return Colors.blue;
      case TodoStatus.inProgress:
        return Colors.orange;
      case TodoStatus.done:
        return Colors.green;
    }
  }

  IconData _getStatusIcon(TodoStatus status) {
    switch (status) {
      case TodoStatus.todo:
        return Icons.radio_button_unchecked;
      case TodoStatus.inProgress:
        return Icons.work_outline;
      case TodoStatus.done:
        return Icons.check_circle;
    }
  }

  String _formatDueDate(DateTime dueDate) {
    return '${dueDate.day}/${dueDate.month}/${dueDate.year}';
  }

  String _formatFullDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} at ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _handleMenuAction(BuildContext context, WidgetRef ref, Todo todo, String action) {
    switch (action) {
      case 'move':
        _showMoveDialog(context, ref, todo);
        break;
      case 'duplicate':
        _duplicateTodo(context, ref, todo);
        break;
      case 'delete':
        _showDeleteDialog(context, ref, todo);
        break;
    }
  }

  void _showMoveDialog(BuildContext context, WidgetRef ref, Todo todo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Move Todo'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: TodoStatus.values.map((status) => ListTile(
            title: Text(status.displayName),
            leading: Icon(_getStatusIcon(status)),
            selected: todo.status == status,
            onTap: () {
              if (todo.status != status) {
                final updatedTodo = todo.copyWith(status: status);
                ref.read(todosProvider.notifier).updateTodo(updatedTodo);
                ref.read(selectedTodoProvider.notifier).state = updatedTodo;
              }
              Navigator.of(context).pop();
            },
          )).toList(),
        ),
      ),
    );
  }

  void _duplicateTodo(BuildContext context, WidgetRef ref, Todo todo) {
    final duplicatedTodo = Todo(
      title: '${todo.title} (Copy)',
      description: todo.description,
      priority: todo.priority,
      status: TodoStatus.todo,
      category: todo.category,
      dueDate: todo.dueDate,
      subTasks: todo.subTasks.map((subTask) => SubTask(
        title: subTask.title,
        isCompleted: false,
      )).toList(),
    );

    ref.read(todosProvider.notifier).addTodo(duplicatedTodo);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Todo duplicated')),
    );
  }

  void _showDeleteDialog(BuildContext context, WidgetRef ref, Todo todo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Todo'),
        content: Text('Are you sure you want to delete "${todo.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              ref.read(todosProvider.notifier).deleteTodo(todo.id);
              Navigator.of(context).pop();
              ref.read(memoSuiteCurrentScreenProvider.notifier).state = MemoSuiteScreen.todosList;
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
