# Shadow Suite - Complete Rebuild Guide

## MAIN BUILD PROMPT

**CRITICAL IMPLEMENTATION STANDARDS:**
- **ZER<PERSON> PLACEHOLDERS POLICY**: No TODO, placeholder, demo, or incomplete implementations
- **NON-STOP IMPLEMENTATION**: Sequential development without pausing between phases
- **ST<PERSON><PERSON><PERSON><PERSON><PERSON> ARCHITECTURE**: Each app completely self-contained within its folder
- **PERFORMANCE REQUIREMENTS**: <100ms response times, <3s startup, <300ms transitions
- **ERROR-FREE POLICY**: Zero diagnostic errors before any build attempts
- **LIMITED RETRY POLICY**: Maximum 3 attempts per issue before changing approach
- **NEVER BUILD UNLESS EXPLICITLY REQUESTED**: No APK/EXE builds without user permission

## SHADOW SUITE OVERVIEW

Shadow Suite is a comprehensive cross-platform application suite featuring **10 STANDALONE MINI-APPS**, each completely self-contained with independent architecture, multiple themes, customizable layouts, and production-ready quality.

### CORE ARCHITECTURE REQUIREMENTS

#### Performance Standards
- **Startup Time**: <3 seconds application launch
- **Response Times**: <100ms for ALL operations
- **Transitions**: <300ms between mini-apps
- **Large Dataset Support**: 1000+ items with smooth performance
- **Memory Efficiency**: Optimized for mobile devices
- **Concurrent Operations**: Thread-safe implementations
- **Offline-First**: Embedded databases, no external dependencies

#### Security & Encryption
- **AES-256 encryption** for files and archives
- **Secure network protocols** with authentication
- **Granular permission management**
- **Data integrity verification** with checksums
- **Thread-safe concurrent operations**

#### UI/UX Requirements
- **Multiple Theme Support**: Dark, Light, Custom themes
- **Multiple Layout Options**: Mobile-first, Desktop, Tablet optimized
- **Full Customization**: Colors, fonts, spacing, animations
- **Responsive Design**: Adaptive layouts for all screen sizes
- **Accessibility**: Screen reader support, keyboard navigation

## COMPLETE MINI-APPS SPECIFICATIONS

### 1. EXCEL-TO-APP BUILDER
**Purpose**: Convert Excel spreadsheets into functional Flutter applications
**Location**: `lib/apps/excel_to_app/`

#### STANDALONE FOLDER STRUCTURE:
```
excel_to_app/
├── core/
│   ├── constants/
│   ├── utils/
│   ├── exceptions/
│   └── extensions/
├── data/
│   ├── datasources/
│   ├── repositories/
│   └── models/
├── features/
│   ├── excel_import/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── formula_engine/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── ui_builder/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   └── app_generator/
│       ├── screens/
│       ├── widgets/
│       └── services/
├── models/
├── services/
├── widgets/
├── screens/
└── excel_to_app_main.dart
```

#### FEATURES & TABS:
- **Import Tab**: Excel file import/parsing
- **Formula Tab**: 200+ Excel functions implementation
- **Design Tab**: UI/UX customization
- **Preview Tab**: Real-time app preview
- **Export Tab**: Generate Flutter code

#### TECHNICAL REQUIREMENTS:
- Complete Excel function library (SUM, AVERAGE, VLOOKUP, etc.)
- Real-time formula calculation engine
- Drag-drop UI builder
- Code generation engine
- Multi-format export (APK, Web, Desktop)

### 2. FILE MANAGER
**Purpose**: Advanced file management with network capabilities
**Location**: `lib/apps/file_manager/`

#### STANDALONE FOLDER STRUCTURE:
```
file_manager/
├── core/
│   ├── constants/
│   ├── utils/
│   ├── exceptions/
│   └── extensions/
├── data/
│   ├── datasources/
│   ├── repositories/
│   └── models/
├── features/
│   ├── file_operations/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── network_sharing/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── encryption/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   └── media_viewer/
│       ├── screens/
│       ├── widgets/
│       └── services/
├── models/
├── services/
├── widgets/
├── screens/
└── file_manager_main.dart
```

#### FEATURES & TABS:
- **Explorer Tab**: Dual-pane file browser
- **Network Tab**: WiFi sharing, FTP server/client
- **Security Tab**: File encryption/decryption
- **Media Tab**: Built-in viewers/editors
- **Archive Tab**: Compression/extraction

#### TECHNICAL REQUIREMENTS:
- Network protocol implementations (FTP, HTTP, SMB)
- AES-256 file encryption
- Media codec support
- P2P file transfer
- LAN discovery protocols

### 3. HADITH APP
**Purpose**: Comprehensive Hadith collection and study
**Location**: `lib/apps/hadith_app/`

#### STANDALONE FOLDER STRUCTURE:
```
hadith_app/
├── core/
│   ├── constants/
│   ├── utils/
│   ├── exceptions/
│   └── extensions/
├── data/
│   ├── datasources/
│   ├── repositories/
│   └── models/
├── features/
│   ├── hadith_browser/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── search/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── bookmarks/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   └── collections/
│       ├── screens/
│       ├── widgets/
│       └── services/
├── models/
├── services/
├── widgets/
├── screens/
└── hadith_app_main.dart
```

#### FEATURES & TABS:
- **Collections Tab**: Bukhari, Muslim, Abu Dawud, etc.
- **Search Tab**: Advanced search across all hadiths
- **Bookmarks Tab**: Saved hadiths with notes
- **Categories Tab**: Hadith classification
- **Study Tab**: Commentary and explanations

#### TECHNICAL REQUIREMENTS:
- Embedded database with 34,458+ authentic hadiths
- Arabic text rendering with RTL support
- Advanced search indexing
- Bookmark management system
- Audio narration support

### 4. MONEY MANAGER
**Purpose**: Personal finance management and budgeting
**Location**: `lib/apps/money_manager/`

#### STANDALONE FOLDER STRUCTURE:
```
money_manager/
├── core/
│   ├── constants/
│   ├── utils/
│   ├── exceptions/
│   └── extensions/
├── data/
│   ├── datasources/
│   ├── repositories/
│   └── models/
├── features/
│   ├── accounts/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── transactions/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── budgets/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── reports/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   └── goals/
│       ├── screens/
│       ├── widgets/
│       └── services/
├── models/
├── services/
├── widgets/
├── screens/
└── money_manager_main.dart
```

#### FEATURES & TABS:
- **Accounts Tab**: Bank accounts, credit cards, cash
- **Transactions Tab**: Income/expense tracking
- **Budget Tab**: Budget creation and monitoring
- **Reports Tab**: Financial analytics and charts
- **Goals Tab**: Savings goals and progress

#### TECHNICAL REQUIREMENTS:
- Multi-currency support
- Transaction categorization
- Budget alerts and notifications
- Financial report generation
- Data export/import capabilities

### 5. NOTES APP
**Purpose**: Advanced note-taking with organization features
**Location**: `lib/apps/notes_app/`

#### STANDALONE FOLDER STRUCTURE:
```
notes_app/
├── core/
│   ├── constants/
│   ├── utils/
│   ├── exceptions/
│   └── extensions/
├── data/
│   ├── datasources/
│   ├── repositories/
│   └── models/
├── features/
│   ├── note_editor/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── organization/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── search/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   └── sync/
│       ├── screens/
│       ├── widgets/
│       └── services/
├── models/
├── services/
├── widgets/
├── screens/
└── notes_app_main.dart
```

#### FEATURES & TABS:
- **Notes Tab**: Create, edit, delete notes
- **Categories Tab**: Color-coded organization
- **Archive Tab**: Archived notes management
- **Search Tab**: Full-text search
- **Settings Tab**: Customization options

#### TECHNICAL REQUIREMENTS:
- Rich text editor with formatting
- Real-time auto-save
- Category and label management
- Full-text search indexing
- Export/import capabilities

### 6. QURAN APP
**Purpose**: Complete Quran reading and study application
**Location**: `lib/apps/quran_app/`

#### STANDALONE FOLDER STRUCTURE:
```
quran_app/
├── core/
│   ├── constants/
│   ├── utils/
│   ├── exceptions/
│   └── extensions/
├── data/
│   ├── datasources/
│   ├── repositories/
│   └── models/
├── features/
│   ├── quran_reader/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── tafseer/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── bookmarks/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── search/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   └── audio/
│       ├── screens/
│       ├── widgets/
│       └── services/
├── models/
├── services/
├── widgets/
├── screens/
└── quran_app_main.dart
```

#### FEATURES & TABS:
- **Quran Tab**: Complete Quran text (6,236 verses)
- **Tafseer Tab**: Commentary from 3 sources
- **Audio Tab**: Recitation with multiple reciters
- **Bookmarks Tab**: Saved verses with notes
- **Search Tab**: Verse and translation search

#### TECHNICAL REQUIREMENTS:
- Embedded Quran database with authentic text
- Arabic text rendering with RTL support
- Multiple tafseer sources integration
- Audio streaming for recitations
- Advanced search across verses and translations

### 7. SHADOW PLAYER
**Purpose**: Advanced media player for audio/video content
**Location**: `lib/apps/shadow_player/`

#### STANDALONE FOLDER STRUCTURE:
```
shadow_player/
├── core/
│   ├── constants/
│   ├── utils/
│   ├── exceptions/
│   └── extensions/
├── data/
│   ├── datasources/
│   ├── repositories/
│   └── models/
├── features/
│   ├── music_player/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── video_player/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── playlists/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   └── media_scanner/
│       ├── screens/
│       ├── widgets/
│       └── services/
├── models/
├── services/
├── widgets/
├── screens/
└── shadow_player_main.dart
```

#### FEATURES & TABS:
- **Music Tab**: Audio playback with playlists
- **Video Tab**: Video playback with multiple view modes
- **Playlists Tab**: Playlist creation and management
- **Settings Tab**: Audio/video preferences
- **Scanner Tab**: Media file discovery

#### TECHNICAL REQUIREMENTS:
- Media metadata extraction and display
- Background audio processing
- Video codec support (MP4, AVI, MKV, etc.)
- Playlist persistence and management
- Gesture controls and equalizer

### 8. SMART GALLERY+
**Purpose**: AI-powered photo/video management with advanced features
**Location**: `lib/apps/smart_gallery/`

#### STANDALONE FOLDER STRUCTURE:
```
smart_gallery/
├── core/
│   ├── constants/
│   ├── utils/
│   ├── exceptions/
│   └── extensions/
├── data/
│   ├── datasources/
│   ├── repositories/
│   └── models/
├── features/
│   ├── gallery_browser/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── ai_processing/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── face_detection/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── ocr/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   └── albums/
│       ├── screens/
│       ├── widgets/
│       └── services/
├── models/
├── services/
├── widgets/
├── screens/
└── smart_gallery_main.dart
```

#### FEATURES & TABS:
- **Gallery Tab**: Photo/video browser with AI sorting
- **Albums Tab**: Smart albums and collections
- **Search Tab**: AI-powered semantic search
- **Face Tab**: Face detection and grouping
- **OCR Tab**: Text extraction from images

#### TECHNICAL REQUIREMENTS:
- TensorFlow Lite integration for AI processing
- MLKit for face detection and OCR
- Support for 200,000+ media files
- Real-time image processing
- Semantic search capabilities

### 9. TODO APP
**Purpose**: Comprehensive task management system
**Location**: `lib/apps/todo_app/`

#### STANDALONE FOLDER STRUCTURE:
```
todo_app/
├── core/
│   ├── constants/
│   ├── utils/
│   ├── exceptions/
│   └── extensions/
├── data/
│   ├── datasources/
│   ├── repositories/
│   └── models/
├── features/
│   ├── task_management/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── categories/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── notifications/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   └── analytics/
│       ├── screens/
│       ├── widgets/
│       └── services/
├── models/
├── services/
├── widgets/
├── screens/
└── todo_app_main.dart
```

#### FEATURES & TABS:
- **Tasks Tab**: Create, edit, manage tasks
- **Categories Tab**: Custom categories with colors/icons
- **Calendar Tab**: Due date management
- **Progress Tab**: Completion statistics
- **Archive Tab**: Completed tasks history

#### TECHNICAL REQUIREMENTS:
- Local notifications system
- Calendar integration
- Task scheduling engine
- Progress analytics and reporting
- Recurring tasks support

### 10. VOICE RECORDER
**Purpose**: Advanced audio recording and management
**Location**: `lib/apps/voice_recorder/`

#### STANDALONE FOLDER STRUCTURE:
```
voice_recorder/
├── core/
│   ├── constants/
│   ├── utils/
│   ├── exceptions/
│   └── extensions/
├── data/
│   ├── datasources/
│   ├── repositories/
│   └── models/
├── features/
│   ├── recording/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── playback/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   ├── editing/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── services/
│   └── transcription/
│       ├── screens/
│       ├── widgets/
│       └── services/
├── models/
├── services/
├── widgets/
├── screens/
└── voice_recorder_main.dart
```

#### FEATURES & TABS:
- **Record Tab**: High-quality audio recording
- **Library Tab**: Recording management and organization
- **Edit Tab**: Audio editing and enhancement
- **Transcribe Tab**: Speech-to-text conversion
- **Share Tab**: Export and sharing options

#### TECHNICAL REQUIREMENTS:
- High-quality audio recording (up to 48kHz)
- Real-time audio visualization
- Audio editing capabilities (trim, merge, effects)
- Speech-to-text transcription
- Multiple audio format support (MP3, WAV, AAC)

## IMPLEMENTATION PHASES

### PHASE 1: CORE INFRASTRUCTURE SETUP
**Duration**: Non-stop implementation
**Requirements**:
- Project initialization with Flutter 3.x
- Core dependency configuration
- Shared utilities and constants
- Theme system with multiple theme support
- Navigation system for 10 mini-apps
- Permission management system

### PHASE 2: SEQUENTIAL APP IMPLEMENTATION
**Order**: Implement all 10 apps sequentially without pausing
**Per App Requirements**:
1. **Complete folder structure** as specified above
2. **All features and tabs** fully implemented
3. **Zero placeholders** - complete business logic
4. **Performance verification** - <100ms response times
5. **Theme integration** - multiple theme support
6. **Testing** - unit, widget, integration tests

**Implementation Order**:
1. Notes App (foundational CRUD operations)
2. Todo App (task management with notifications)
3. Voice Recorder (audio processing)
4. File Manager (file operations and networking)
5. Money Manager (financial calculations)
6. Quran App (Islamic content with RTL support)
7. Hadith App (large dataset management)
8. Shadow Player (media processing)
9. Smart Gallery+ (AI processing)
10. Excel-to-App Builder (code generation)

### PHASE 3: INTEGRATION AND OPTIMIZATION
**Requirements**:
- Cross-app integration testing
- Performance optimization across all apps
- Memory usage optimization
- Battery usage optimization
- Security audit and encryption verification
- UI/UX consistency across all apps

### PHASE 4: COMPREHENSIVE TESTING
**Requirements**:
- Unit tests for all business logic (90%+ coverage)
- Widget tests for all UI components
- Integration tests for complete workflows
- Performance tests with large datasets
- Security tests for encryption and network operations
- Accessibility tests for screen readers and keyboard navigation

## BUILD AND ERROR RESOLUTION POLICIES

### CRITICAL BUILD POLICIES
- **NEVER ATTEMPT BUILDS** unless explicitly requested by user
- **ZERO DIAGNOSTIC ERRORS** required before any build attempts
- **NO APK/EXE GENERATION** without explicit user permission
- **BUILD ONLY AFTER** complete implementation and testing

### ERROR RESOLUTION METHODOLOGY
- **LIMITED RETRY POLICY**: Maximum 3 attempts per specific issue
- **Color.value Deprecation**: Try `toARGB32()`, then alternatives, max 3 attempts
- **Type Conflicts**: Implement proper type conversion, max 3 attempts
- **Missing Properties**: Add required properties or use alternatives, max 3 attempts
- **After 3 Failed Attempts**: Change approach, remove problematic code, or implement workaround

### DIAGNOSTIC RESOLUTION PRIORITY
1. **Critical Compilation Errors** (prevents build)
2. **Type Conflicts and Missing Properties**
3. **Deprecated API Usage**
4. **Unused Variables and Imports**
5. **Code Style and Formatting Issues**

### VERIFICATION PROCESS
- Run `diagnostics` after every 5-10 fixes
- Verify actual error count reduction
- Continue until zero errors, warnings, and info messages
- Test fixes in isolation before proceeding
- Document all solutions for future reference

## TECHNICAL STACK AND DEPENDENCIES

### Core Technologies
- **Framework**: Flutter 3.x
- **State Management**: Riverpod
- **Database**: SQLite with sqflite
- **Storage**: Shared Preferences, Secure Storage
- **Networking**: HTTP, WebSocket, custom protocols
- **Encryption**: AES-256, RSA for key exchange
- **Audio/Video**: just_audio, video_player, camera
- **File Operations**: path_provider, file_picker
- **Notifications**: flutter_local_notifications
- **AI/ML**: TensorFlow Lite, MLKit

### Performance Requirements
- **Startup Time**: <3 seconds for complete app launch
- **Response Times**: <100ms for all user interactions
- **Transitions**: <300ms between mini-apps and screens
- **Large Dataset Support**: Handle 1000+ items smoothly
- **Memory Efficiency**: Optimized for mobile devices
- **Concurrent Operations**: Thread-safe implementations
- **Offline-First**: All apps work without internet connection

### Quality Assurance Standards
- **Zero Placeholders Policy**: Complete business logic implementation
- **Production-Ready Quality**: Enterprise-grade code standards
- **Comprehensive Error Handling**: Graceful failure recovery
- **Security**: AES-256 encryption, secure network protocols
- **Accessibility**: Full screen reader and keyboard support
- **Testing**: 90%+ code coverage with comprehensive test suites

## SUCCESS CRITERIA

### Implementation Success
- All 10 mini-apps fully implemented with complete feature sets
- Zero diagnostic errors across entire codebase
- All performance requirements met (<100ms, <3s, <300ms)
- Complete standalone architecture with no shared dependencies
- Multiple theme support with full customization
- Comprehensive testing suite with 90%+ coverage

### Build Success (Only When Explicitly Requested)
- Successful Windows EXE build
- Successful Android APK build
- All features functional in production builds
- Performance standards maintained in release builds
- Security features working correctly

### Quality Success
- Zero placeholders or incomplete implementations
- Production-ready code quality throughout
- Comprehensive error handling and recovery
- Full accessibility support
- Complete documentation and user guides

---

This comprehensive rebuild guide ensures complete implementation of all 10 standalone mini-apps with production-ready quality, zero placeholders, and systematic error resolution methodology.
