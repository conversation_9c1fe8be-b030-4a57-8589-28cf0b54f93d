import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/finance_service.dart';

/// Finance Manager Settings Screen
class FinanceSettingsScreen extends ConsumerStatefulWidget {
  const FinanceSettingsScreen({super.key});

  @override
  ConsumerState<FinanceSettingsScreen> createState() => _FinanceSettingsScreenState();
}

class _FinanceSettingsScreenState extends ConsumerState<FinanceSettingsScreen> {
  String _defaultCurrency = 'USD';
  bool _showDecimalPlaces = true;
  bool _enableNotifications = true;
  bool _enableBudgetAlerts = true;
  bool _enableGoalReminders = true;
  bool _autoBackup = false;
  int _backupFrequency = 7; // days
  String _dateFormat = 'MM/dd/yyyy';
  String _numberFormat = 'US';
  bool _enableFingerprint = false;
  bool _enablePinLock = false;
  bool _darkMode = false;
  double _fontSize = 16.0;

  final List<String> _currencies = [
    'USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF', 'CNY', 'INR', 'BRL'
  ];

  final List<String> _dateFormats = [
    'MM/dd/yyyy', 'dd/MM/yyyy', 'yyyy-MM-dd', 'dd-MM-yyyy'
  ];

  final List<String> _numberFormats = [
    'US', 'European', 'Indian', 'Custom'
  ];

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  void _loadSettings() {
    // Load settings from service or shared preferences
    // This would typically load from a settings service
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Finance Settings'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveSettings,
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Currency Settings
          _buildSectionHeader('Currency & Display'),
          _buildCurrencySelector(),
          _buildSwitchTile(
            'Show Decimal Places',
            'Display amounts with decimal precision',
            _showDecimalPlaces,
            (value) => setState(() => _showDecimalPlaces = value),
          ),
          _buildDropdownTile(
            'Date Format',
            _dateFormat,
            _dateFormats,
            (value) => setState(() => _dateFormat = value!),
          ),
          _buildDropdownTile(
            'Number Format',
            _numberFormat,
            _numberFormats,
            (value) => setState(() => _numberFormat = value!),
          ),
          
          const SizedBox(height: 24),
          
          // Notifications
          _buildSectionHeader('Notifications'),
          _buildSwitchTile(
            'Enable Notifications',
            'Receive transaction and budget alerts',
            _enableNotifications,
            (value) => setState(() => _enableNotifications = value),
          ),
          _buildSwitchTile(
            'Budget Alerts',
            'Get notified when approaching budget limits',
            _enableBudgetAlerts,
            (value) => setState(() => _enableBudgetAlerts = value),
          ),
          _buildSwitchTile(
            'Goal Reminders',
            'Receive reminders about financial goals',
            _enableGoalReminders,
            (value) => setState(() => _enableGoalReminders = value),
          ),
          
          const SizedBox(height: 24),
          
          // Backup & Security
          _buildSectionHeader('Backup & Security'),
          _buildSwitchTile(
            'Auto Backup',
            'Automatically backup data to cloud',
            _autoBackup,
            (value) => setState(() => _autoBackup = value),
          ),
          _buildSliderTile(
            'Backup Frequency',
            'Days between automatic backups',
            _backupFrequency.toDouble(),
            1.0,
            30.0,
            (value) => setState(() => _backupFrequency = value.round()),
            '${_backupFrequency} days',
          ),
          _buildSwitchTile(
            'Fingerprint Lock',
            'Use fingerprint to secure the app',
            _enableFingerprint,
            (value) => setState(() => _enableFingerprint = value),
          ),
          _buildSwitchTile(
            'PIN Lock',
            'Use PIN to secure the app',
            _enablePinLock,
            (value) => setState(() => _enablePinLock = value),
          ),
          
          const SizedBox(height: 24),
          
          // Appearance
          _buildSectionHeader('Appearance'),
          _buildSwitchTile(
            'Dark Mode',
            'Use dark theme for the app',
            _darkMode,
            (value) => setState(() => _darkMode = value),
          ),
          _buildSliderTile(
            'Font Size',
            'Adjust text size for better readability',
            _fontSize,
            12.0,
            24.0,
            (value) => setState(() => _fontSize = value),
            '${_fontSize.toInt()}pt',
          ),
          
          const SizedBox(height: 24),
          
          // Data Management
          _buildSectionHeader('Data Management'),
          _buildActionTile(
            'Export Data',
            'Export all financial data to CSV',
            Icons.download,
            _exportData,
          ),
          _buildActionTile(
            'Import Data',
            'Import data from CSV file',
            Icons.upload,
            _importData,
          ),
          _buildActionTile(
            'Reset All Data',
            'Delete all financial data (cannot be undone)',
            Icons.delete_forever,
            _resetData,
            isDestructive: true,
          ),
          
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16, top: 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
          color: Colors.green,
        ),
      ),
    );
  }

  Widget _buildCurrencySelector() {
    return ListTile(
      title: const Text('Default Currency'),
      subtitle: Text('All amounts will be displayed in $_defaultCurrency'),
      trailing: DropdownButton<String>(
        value: _defaultCurrency,
        items: _currencies.map((currency) {
          return DropdownMenuItem(
            value: currency,
            child: Text(currency),
          );
        }).toList(),
        onChanged: (value) {
          if (value != null) {
            setState(() => _defaultCurrency = value);
          }
        },
      ),
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      activeColor: Colors.green,
    );
  }

  Widget _buildDropdownTile(
    String title,
    String value,
    List<String> options,
    ValueChanged<String?> onChanged,
  ) {
    return ListTile(
      title: Text(title),
      trailing: DropdownButton<String>(
        value: value,
        items: options.map((option) {
          return DropdownMenuItem(
            value: option,
            child: Text(option),
          );
        }).toList(),
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildSliderTile(
    String title,
    String subtitle,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
    String displayValue,
  ) {
    return Column(
      children: [
        ListTile(
          title: Text(title),
          subtitle: Text(subtitle),
          trailing: Text(displayValue),
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          onChanged: onChanged,
          activeColor: Colors.green,
        ),
      ],
    );
  }

  Widget _buildActionTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap, {
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? Colors.red : Colors.green,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isDestructive ? Colors.red : null,
        ),
      ),
      subtitle: Text(subtitle),
      onTap: onTap,
    );
  }

  void _saveSettings() {
    // Save settings to service or shared preferences
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Settings saved successfully'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _exportData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Data'),
        content: const Text('Export all financial data to CSV file?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Data exported successfully')),
              );
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  void _importData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import Data'),
        content: const Text('Import financial data from CSV file?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Data imported successfully')),
              );
            },
            child: const Text('Import'),
          ),
        ],
      ),
    );
  }

  void _resetData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset All Data'),
        content: const Text(
          'This will permanently delete all your financial data. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('All data has been reset'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}
