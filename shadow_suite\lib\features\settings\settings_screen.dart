import 'package:flutter/material.dart';
import '../../core/theme/app_theme.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Settings'),
        automaticallyImplyLeading: false,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSection(
              context,
              'General',
              [
                _buildSettingTile(
                  context,
                  'Theme',
                  'Light',
                  Icons.palette,
                  onTap: () {},
                ),
                _buildSettingTile(
                  context,
                  'Language',
                  'English',
                  Icons.language,
                  onTap: () {},
                ),
                _buildSettingTile(
                  context,
                  'Notifications',
                  'Enabled',
                  Icons.notifications,
                  onTap: () {},
                ),
              ],
            ),
            const SizedBox(height: 32),
            _buildSection(
              context,
              'Data & Privacy',
              [
                _buildSettingTile(
                  context,
                  'Backup & Sync',
                  'Auto backup enabled',
                  Icons.backup,
                  onTap: () {},
                ),
                _buildSettingTile(
                  context,
                  'Export Data',
                  'Export all app data',
                  Icons.download,
                  onTap: () {},
                ),
                _buildSettingTile(
                  context,
                  'Privacy Policy',
                  'View privacy policy',
                  Icons.privacy_tip,
                  onTap: () {},
                ),
              ],
            ),
            const SizedBox(height: 32),
            _buildSection(
              context,
              'About',
              [
                _buildSettingTile(
                  context,
                  'Version',
                  '1.0.0',
                  Icons.info,
                  onTap: () {},
                ),
                _buildSettingTile(
                  context,
                  'Help & Support',
                  'Get help',
                  Icons.help,
                  onTap: () {},
                ),
                _buildSettingTile(
                  context,
                  'Rate App',
                  'Rate ShadowSuite',
                  Icons.star,
                  onTap: () {},
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        const SizedBox(height: 16),
        Card(
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildSettingTile(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon, {
    VoidCallback? onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppTheme.primaryColor,
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }
}
