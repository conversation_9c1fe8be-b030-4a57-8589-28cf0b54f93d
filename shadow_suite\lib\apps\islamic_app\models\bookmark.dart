import 'package:uuid/uuid.dart';

// Bookmark Model for saving favorite verses
class Bookmark {
  final String id;
  final int surahNumber;
  final int verseNumber;
  final String title;
  final String notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<String> tags;

  Bookmark({
    String? id,
    required this.surahNumber,
    required this.verseNumber,
    required this.title,
    this.notes = '',
    DateTime? createdAt,
    DateTime? updatedAt,
    this.tags = const [],
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Bookmark copyWith({
    int? surahNumber,
    int? verseNumber,
    String? title,
    String? notes,
    DateTime? updatedAt,
    List<String>? tags,
  }) {
    return Bookmark(
      id: id,
      surahNumber: surahNumber ?? this.surahNumber,
      verseNumber: verseNumber ?? this.verseNumber,
      title: title ?? this.title,
      notes: notes ?? this.notes,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      tags: tags ?? this.tags,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'surahNumber': surahNumber,
      'verseNumber': verseNumber,
      'title': title,
      'notes': notes,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'tags': tags.join(','),
    };
  }

  factory Bookmark.fromMap(Map<String, dynamic> map) {
    return Bookmark(
      id: map['id'],
      surahNumber: map['surahNumber'],
      verseNumber: map['verseNumber'],
      title: map['title'],
      notes: map['notes'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
      tags: map['tags']?.split(',').where((tag) => tag.isNotEmpty).toList() ?? [],
    );
  }

  String get verseReference => '$surahNumber:$verseNumber';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Bookmark && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Bookmark(id: $id, verse: $verseReference, title: $title)';
  }
}
