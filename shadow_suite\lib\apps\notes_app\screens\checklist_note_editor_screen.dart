import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/note_models.dart';
import '../services/notes_service.dart';

/// Checklist Note Editor with interactive checkboxes and color support
class ChecklistNoteEditorScreen extends ConsumerStatefulWidget {
  final Note? note;
  final bool isNewNote;

  const ChecklistNoteEditorScreen({
    super.key,
    this.note,
    this.isNewNote = false,
  });

  @override
  ConsumerState<ChecklistNoteEditorScreen> createState() => _ChecklistNoteEditorScreenState();
}

class _ChecklistNoteEditorScreenState extends ConsumerState<ChecklistNoteEditorScreen> {
  late TextEditingController _titleController;
  late Color _selectedColor;
  late NotePriority _selectedPriority;
  List<ChecklistItem> _checklistItems = [];
  List<String> _tags = [];
  bool _isPinned = false;
  bool _hasUnsavedChanges = false;

  final List<Color> _availableColors = [
    Colors.white,
    Colors.red.shade100,
    Colors.orange.shade100,
    Colors.yellow.shade100,
    Colors.green.shade100,
    Colors.blue.shade100,
    Colors.purple.shade100,
    Colors.pink.shade100,
    Colors.grey.shade100,
  ];

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.note?.title ?? '');
    _selectedColor = widget.note?.color ?? Colors.white;
    _selectedPriority = widget.note?.priority ?? NotePriority.medium;
    _checklistItems = List.from(widget.note?.checklistItems ?? []);
    _tags = List.from(widget.note?.tags ?? []);
    _isPinned = widget.note?.isPinned ?? false;

    _titleController.addListener(_onContentChanged);

    // Add initial empty item if new note
    if (widget.isNewNote && _checklistItems.isEmpty) {
      _addNewItem();
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  void _onContentChanged() {
    if (!_hasUnsavedChanges) {
      setState(() => _hasUnsavedChanges = true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _selectedColor,
      appBar: AppBar(
        title: Text(widget.isNewNote ? 'New Checklist' : 'Edit Checklist'),
        backgroundColor: _selectedColor,
        foregroundColor: _getContrastColor(_selectedColor),
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(_isPinned ? Icons.push_pin : Icons.push_pin_outlined),
            onPressed: () => setState(() => _isPinned = !_isPinned),
          ),
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _hasUnsavedChanges ? _saveNote : null,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildColorPicker(),
          _buildPrioritySelector(),
          _buildTitleSection(),
          _buildProgressIndicator(),
          Expanded(child: _buildChecklistItems()),
          _buildTagsSection(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addNewItem,
        backgroundColor: _selectedColor.withValues(alpha: 0.8),
        child: Icon(Icons.add, color: _getContrastColor(_selectedColor)),
      ),
    );
  }

  Widget _buildColorPicker() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          const Text('Color: ', style: TextStyle(fontWeight: FontWeight.bold)),
          Expanded(
            child: SizedBox(
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _availableColors.length,
                itemBuilder: (context, index) {
                  final color = _availableColors[index];
                  final isSelected = color == _selectedColor;
                  return GestureDetector(
                    onTap: () => setState(() => _selectedColor = color),
                    child: Container(
                      width: 40,
                      height: 40,
                      margin: const EdgeInsets.only(right: 8),
                      decoration: BoxDecoration(
                        color: color,
                        border: Border.all(
                          color: isSelected ? Colors.black : Colors.grey,
                          width: isSelected ? 3 : 1,
                        ),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: isSelected
                          ? const Icon(Icons.check, color: Colors.black)
                          : null,
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrioritySelector() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          const Text('Priority: ', style: TextStyle(fontWeight: FontWeight.bold)),
          DropdownButton<NotePriority>(
            value: _selectedPriority,
            onChanged: (priority) {
              if (priority != null) {
                setState(() => _selectedPriority = priority);
                _onContentChanged();
              }
            },
            items: NotePriority.values.map((priority) {
              return DropdownMenuItem(
                value: priority,
                child: Row(
                  children: [
                    Icon(
                      _getPriorityIcon(priority),
                      color: _getPriorityColor(priority),
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(priority.name.toUpperCase()),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildTitleSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _titleController,
        style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        decoration: const InputDecoration(
          hintText: 'Checklist title...',
          border: InputBorder.none,
        ),
        maxLines: null,
      ),
    );
  }

  Widget _buildProgressIndicator() {
    final completedCount = _checklistItems.where((item) => item.isCompleted).length;
    final totalCount = _checklistItems.length;
    final progress = totalCount > 0 ? completedCount / totalCount : 0.0;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Progress: $completedCount of $totalCount completed',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(
                '${(progress * 100).toInt()}%',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey.shade300,
            valueColor: AlwaysStoppedAnimation<Color>(
              progress == 1.0 ? Colors.green : Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChecklistItems() {
    return ReorderableListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _checklistItems.length,
      onReorder: _reorderItems,
      itemBuilder: (context, index) {
        final item = _checklistItems[index];
        return _buildChecklistItem(item, index);
      },
    );
  }

  Widget _buildChecklistItem(ChecklistItem item, int index) {
    return Container(
      key: ValueKey(item.id),
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: item.isCompleted 
            ? Colors.grey.shade100 
            : Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: ListTile(
        leading: Checkbox(
          value: item.isCompleted,
          onChanged: (value) => _toggleItem(index, value ?? false),
        ),
        title: TextField(
          controller: TextEditingController(text: item.text),
          style: TextStyle(
            decoration: item.isCompleted 
                ? TextDecoration.lineThrough 
                : TextDecoration.none,
            color: item.isCompleted 
                ? Colors.grey 
                : Colors.black,
          ),
          decoration: const InputDecoration(
            hintText: 'Enter item...',
            border: InputBorder.none,
          ),
          onChanged: (value) => _updateItemText(index, value),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.drag_handle, color: Colors.grey.shade400),
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () => _deleteItem(index),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTagsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text('Tags: ', style: TextStyle(fontWeight: FontWeight.bold)),
              IconButton(
                icon: const Icon(Icons.add),
                onPressed: _showAddTagDialog,
                iconSize: 20,
              ),
            ],
          ),
          if (_tags.isNotEmpty)
            Wrap(
              spacing: 8,
              children: _tags.map((tag) {
                return Chip(
                  label: Text(tag),
                  onDeleted: () {
                    setState(() => _tags.remove(tag));
                    _onContentChanged();
                  },
                  backgroundColor: _selectedColor.withValues(alpha: 0.3),
                );
              }).toList(),
            ),
        ],
      ),
    );
  }

  void _addNewItem() {
    final newItem = ChecklistItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      text: '',
      isCompleted: false,
      createdAt: DateTime.now(),
    );
    setState(() => _checklistItems.add(newItem));
    _onContentChanged();
  }

  void _toggleItem(int index, bool isCompleted) {
    final item = _checklistItems[index];
    final updatedItem = item.copyWith(
      isCompleted: isCompleted,
      completedAt: isCompleted ? DateTime.now() : null,
    );
    setState(() => _checklistItems[index] = updatedItem);
    _onContentChanged();
  }

  void _updateItemText(int index, String text) {
    final item = _checklistItems[index];
    final updatedItem = item.copyWith(text: text);
    setState(() => _checklistItems[index] = updatedItem);
    _onContentChanged();
  }

  void _deleteItem(int index) {
    setState(() => _checklistItems.removeAt(index));
    _onContentChanged();
  }

  void _reorderItems(int oldIndex, int newIndex) {
    if (newIndex > oldIndex) newIndex--;
    final item = _checklistItems.removeAt(oldIndex);
    setState(() => _checklistItems.insert(newIndex, item));
    _onContentChanged();
  }

  IconData _getPriorityIcon(NotePriority priority) {
    switch (priority) {
      case NotePriority.low:
        return Icons.keyboard_arrow_down;
      case NotePriority.medium:
        return Icons.remove;
      case NotePriority.high:
        return Icons.keyboard_arrow_up;
    }
  }

  Color _getPriorityColor(NotePriority priority) {
    switch (priority) {
      case NotePriority.low:
        return Colors.green;
      case NotePriority.medium:
        return Colors.orange;
      case NotePriority.high:
        return Colors.red;
    }
  }

  Color _getContrastColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  void _showAddTagDialog() {
    String newTag = '';
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Tag'),
        content: TextField(
          onChanged: (value) => newTag = value,
          decoration: const InputDecoration(
            hintText: 'Enter tag name',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (newTag.isNotEmpty && !_tags.contains(newTag)) {
                setState(() => _tags.add(newTag));
                _onContentChanged();
              }
              Navigator.pop(context);
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  Future<void> _saveNote() async {
    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a title')),
      );
      return;
    }

    // Remove empty items
    _checklistItems.removeWhere((item) => item.text.trim().isEmpty);

    try {
      final notesService = ref.read(notesServiceProvider);
      final now = DateTime.now();

      final note = Note(
        id: widget.note?.id ?? now.millisecondsSinceEpoch.toString(),
        title: _titleController.text.trim(),
        content: '', // Content is in checklist items
        type: NoteType.checklist,
        priority: _selectedPriority,
        color: _selectedColor,
        tags: _tags,
        createdAt: widget.note?.createdAt ?? now,
        updatedAt: now,
        isPinned: _isPinned,
        isArchived: widget.note?.isArchived ?? false,
        isFavorite: widget.note?.isFavorite ?? false,
        checklistItems: _checklistItems,
      );

      if (widget.isNewNote) {
        await notesService.addNote(note);
      } else {
        await notesService.updateNote(note);
      }

      setState(() => _hasUnsavedChanges = false);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Checklist saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving checklist: $e')),
        );
      }
    }
  }
}
