import 'dart:async';
import 'package:flutter/foundation.dart';
import 'excel_function_validator.dart';
import 'performance_optimizer.dart';
import 'advanced_layout_service.dart';
import 'settings_persistence_service.dart';
import 'preset_template_service.dart';

/// Comprehensive quality assurance service for Excel-to-App builder
class QualityAssuranceService {
  static bool _isInitialized = false;
  static QualityReport? _lastReport;
  static final List<QualityCheck> _checks = [];

  /// Initialize the QA service
  static void initialize() {
    if (_isInitialized) return;
    
    _setupQualityChecks();
    _isInitialized = true;
  }

  /// Run comprehensive quality assurance tests
  static Future<QualityReport> runQualityAssurance() async {
    await _ensureInitialized();
    
    final startTime = DateTime.now();
    final results = <QualityCheckResult>[];
    
    for (final check in _checks) {
      try {
        final result = await check.execute();
        results.add(result);
      } catch (e) {
        results.add(QualityCheckResult(
          checkName: check.name,
          category: check.category,
          passed: false,
          message: 'Check failed with error: $e',
          severity: QualitySeverity.critical,
        ));
      }
    }
    
    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);
    
    final report = QualityReport(
      results: results,
      totalChecks: results.length,
      passedChecks: results.where((r) => r.passed).length,
      failedChecks: results.where((r) => !r.passed).length,
      criticalIssues: results.where((r) => r.severity == QualitySeverity.critical && !r.passed).length,
      warningIssues: results.where((r) => r.severity == QualitySeverity.warning && !r.passed).length,
      duration: duration,
      timestamp: endTime,
    );
    
    _lastReport = report;
    return report;
  }

  /// Run specific category of checks
  static Future<List<QualityCheckResult>> runCategoryChecks(QualityCategory category) async {
    await _ensureInitialized();
    
    final categoryChecks = _checks.where((check) => check.category == category);
    final results = <QualityCheckResult>[];
    
    for (final check in categoryChecks) {
      try {
        final result = await check.execute();
        results.add(result);
      } catch (e) {
        results.add(QualityCheckResult(
          checkName: check.name,
          category: check.category,
          passed: false,
          message: 'Check failed with error: $e',
          severity: QualitySeverity.critical,
        ));
      }
    }
    
    return results;
  }

  /// Get last quality report
  static QualityReport? get lastReport => _lastReport;

  /// Check if system meets quality standards
  static bool meetsQualityStandards() {
    if (_lastReport == null) return false;
    
    return _lastReport!.criticalIssues == 0 && 
           _lastReport!.successRate >= 0.95;
  }

  /// Get quality score (0-100)
  static double getQualityScore() {
    if (_lastReport == null) return 0.0;
    
    final baseScore = _lastReport!.successRate * 100;
    final criticalPenalty = _lastReport!.criticalIssues * 10;
    final warningPenalty = _lastReport!.warningIssues * 2;
    
    return (baseScore - criticalPenalty - warningPenalty).clamp(0.0, 100.0);
  }

  /// Get improvement suggestions
  static List<String> getImprovementSuggestions() {
    if (_lastReport == null) return ['Run quality assurance first'];
    
    final suggestions = <String>[];
    final failedResults = _lastReport!.failedResults;
    
    // Group by category
    final categorizedIssues = <QualityCategory, List<QualityCheckResult>>{};
    for (final result in failedResults) {
      categorizedIssues.putIfAbsent(result.category, () => []).add(result);
    }
    
    for (final entry in categorizedIssues.entries) {
      final category = entry.key;
      final issues = entry.value;
      
      switch (category) {
        case QualityCategory.functionality:
          suggestions.add('Fix ${issues.length} functionality issues to ensure core features work correctly');
          break;
        case QualityCategory.performance:
          suggestions.add('Address ${issues.length} performance issues to meet <100ms response time targets');
          break;
        case QualityCategory.usability:
          suggestions.add('Improve ${issues.length} usability aspects for better user experience');
          break;
        case QualityCategory.reliability:
          suggestions.add('Resolve ${issues.length} reliability issues to prevent crashes and errors');
          break;
        case QualityCategory.security:
          suggestions.add('Fix ${issues.length} security vulnerabilities to protect user data');
          break;
        case QualityCategory.compatibility:
          suggestions.add('Address ${issues.length} compatibility issues for broader device support');
          break;
      }
    }
    
    if (suggestions.isEmpty) {
      suggestions.add('Excellent! All quality checks are passing. Continue monitoring for any regressions.');
    }
    
    return suggestions;
  }

  // Private methods
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      initialize();
    }
  }

  static void _setupQualityChecks() {
    _checks.addAll([
      // Functionality Checks
      QualityCheck(
        name: 'Excel Functions Validation',
        category: QualityCategory.functionality,
        severity: QualitySeverity.critical,
        execute: () async {
          final report = await ExcelFunctionValidator.runAllTests();
          return QualityCheckResult(
            checkName: 'Excel Functions Validation',
            category: QualityCategory.functionality,
            passed: report.successRate >= 0.95,
            message: 'Excel functions: ${report.passedTests}/${report.totalTests} passed (${(report.successRate * 100).toStringAsFixed(1)}%)',
            severity: QualitySeverity.critical,
            details: {
              'totalTests': report.totalTests,
              'passedTests': report.passedTests,
              'failedTests': report.failedTests,
              'successRate': report.successRate,
            },
          );
        },
      ),
      
      QualityCheck(
        name: 'Core Function Integrity',
        category: QualityCategory.functionality,
        severity: QualitySeverity.critical,
        execute: () async {
          final isValid = await ExcelFunctionValidator.validateCoreIntegrity();
          return QualityCheckResult(
            checkName: 'Core Function Integrity',
            category: QualityCategory.functionality,
            passed: isValid,
            message: isValid ? 'All core functions working correctly' : 'Core function validation failed',
            severity: QualitySeverity.critical,
          );
        },
      ),
      
      // Performance Checks
      QualityCheck(
        name: 'Performance Benchmarks',
        category: QualityCategory.performance,
        severity: QualitySeverity.warning,
        execute: () async {
          final benchmarks = await ExcelFunctionValidator.getPerformanceBenchmarks();
          final slowFunctions = benchmarks.values.where((b) => !b.meetsPerformanceTarget).length;
          final totalFunctions = benchmarks.length;
          
          return QualityCheckResult(
            checkName: 'Performance Benchmarks',
            category: QualityCategory.performance,
            passed: slowFunctions == 0,
            message: 'Performance: ${totalFunctions - slowFunctions}/$totalFunctions functions meet <100ms target',
            severity: QualitySeverity.warning,
            details: {
              'totalFunctions': totalFunctions,
              'fastFunctions': totalFunctions - slowFunctions,
              'slowFunctions': slowFunctions,
            },
          );
        },
      ),
      
      QualityCheck(
        name: 'Memory Usage',
        category: QualityCategory.performance,
        severity: QualitySeverity.warning,
        execute: () async {
          final report = PerformanceOptimizer.getPerformanceReport();
          final memoryEfficient = report.memoryUsage < 1000;
          
          return QualityCheckResult(
            checkName: 'Memory Usage',
            category: QualityCategory.performance,
            passed: memoryEfficient,
            message: 'Memory usage: ${report.memoryUsage} cached items',
            severity: QualitySeverity.warning,
            details: {
              'memoryUsage': report.memoryUsage,
              'cacheHitRate': report.cacheHitRate,
            },
          );
        },
      ),
      
      // Usability Checks
      QualityCheck(
        name: 'Layout Configuration Validation',
        category: QualityCategory.usability,
        severity: QualitySeverity.warning,
        execute: () async {
          try {
            final config = AdvancedLayoutService.currentConfig;
            final isValid = config.gridColumns > 0 && 
                           config.gridSpacing >= 0 && 
                           config.componentPadding >= 0;
            
            return QualityCheckResult(
              checkName: 'Layout Configuration Validation',
              category: QualityCategory.usability,
              passed: isValid,
              message: isValid ? 'Layout configuration is valid' : 'Invalid layout configuration detected',
              severity: QualitySeverity.warning,
            );
          } catch (e) {
            return QualityCheckResult(
              checkName: 'Layout Configuration Validation',
              category: QualityCategory.usability,
              passed: false,
              message: 'Layout configuration check failed: $e',
              severity: QualitySeverity.warning,
            );
          }
        },
      ),
      
      QualityCheck(
        name: 'Preset Templates Validation',
        category: QualityCategory.usability,
        severity: QualitySeverity.info,
        execute: () async {
          try {
            PresetTemplateService.initialize();
            final templates = PresetTemplateService.templates;
            final categories = PresetTemplateService.categories;
            
            return QualityCheckResult(
              checkName: 'Preset Templates Validation',
              category: QualityCategory.usability,
              passed: templates.isNotEmpty && categories.isNotEmpty,
              message: 'Templates: ${templates.length}, Categories: ${categories.length}',
              severity: QualitySeverity.info,
              details: {
                'templateCount': templates.length,
                'categoryCount': categories.length,
                'builtInTemplates': templates.where((t) => t.isBuiltIn).length,
                'featuredTemplates': templates.where((t) => t.isFeatured).length,
              },
            );
          } catch (e) {
            return QualityCheckResult(
              checkName: 'Preset Templates Validation',
              category: QualityCategory.usability,
              passed: false,
              message: 'Template validation failed: $e',
              severity: QualitySeverity.warning,
            );
          }
        },
      ),
      
      // Reliability Checks
      QualityCheck(
        name: 'Settings Persistence',
        category: QualityCategory.reliability,
        severity: QualitySeverity.warning,
        execute: () async {
          try {
            await SettingsPersistenceService.initialize();
            final stats = await SettingsPersistenceService.getStorageStats();
            
            return QualityCheckResult(
              checkName: 'Settings Persistence',
              category: QualityCategory.reliability,
              passed: true,
              message: 'Settings persistence working correctly',
              severity: QualitySeverity.info,
              details: stats,
            );
          } catch (e) {
            return QualityCheckResult(
              checkName: 'Settings Persistence',
              category: QualityCategory.reliability,
              passed: false,
              message: 'Settings persistence failed: $e',
              severity: QualitySeverity.warning,
            );
          }
        },
      ),
      
      // Security Checks
      QualityCheck(
        name: 'Data Validation',
        category: QualityCategory.security,
        severity: QualitySeverity.critical,
        execute: () async {
          // Check for potential security issues in formula evaluation
          final testFormulas = [
            '=SUM(1,2,3)',
            '=AVERAGE(1,2,3,4,5)',
            '=IF(TRUE,"safe","safe")',
          ];
          
          var allSafe = true;
          for (final formula in testFormulas) {
            try {
              ExcelFunctionValidator.validateFormula(formula, 0);
            } catch (e) {
              allSafe = false;
              break;
            }
          }
          
          return QualityCheckResult(
            checkName: 'Data Validation',
            category: QualityCategory.security,
            passed: allSafe,
            message: allSafe ? 'Formula evaluation is secure' : 'Security issues detected in formula evaluation',
            severity: QualitySeverity.critical,
          );
        },
      ),
      
      // Compatibility Checks
      QualityCheck(
        name: 'Platform Compatibility',
        category: QualityCategory.compatibility,
        severity: QualitySeverity.info,
        execute: () async {
          final platform = defaultTargetPlatform;
          final isSupported = [
            TargetPlatform.android,
            TargetPlatform.iOS,
            TargetPlatform.windows,
            TargetPlatform.macOS,
            TargetPlatform.linux,
          ].contains(platform);
          
          return QualityCheckResult(
            checkName: 'Platform Compatibility',
            category: QualityCategory.compatibility,
            passed: isSupported,
            message: 'Platform: $platform ${isSupported ? "(supported)" : "(not supported)"}',
            severity: QualitySeverity.info,
            details: {
              'platform': platform.toString(),
              'isSupported': isSupported,
            },
          );
        },
      ),
    ]);
  }
}

/// Quality check definition
class QualityCheck {
  final String name;
  final QualityCategory category;
  final QualitySeverity severity;
  final Future<QualityCheckResult> Function() execute;

  const QualityCheck({
    required this.name,
    required this.category,
    required this.severity,
    required this.execute,
  });
}

/// Quality check result
class QualityCheckResult {
  final String checkName;
  final QualityCategory category;
  final bool passed;
  final String message;
  final QualitySeverity severity;
  final Map<String, dynamic>? details;

  const QualityCheckResult({
    required this.checkName,
    required this.category,
    required this.passed,
    required this.message,
    required this.severity,
    this.details,
  });
}

/// Quality report
class QualityReport {
  final List<QualityCheckResult> results;
  final int totalChecks;
  final int passedChecks;
  final int failedChecks;
  final int criticalIssues;
  final int warningIssues;
  final Duration duration;
  final DateTime timestamp;

  const QualityReport({
    required this.results,
    required this.totalChecks,
    required this.passedChecks,
    required this.failedChecks,
    required this.criticalIssues,
    required this.warningIssues,
    required this.duration,
    required this.timestamp,
  });

  double get successRate => totalChecks > 0 ? passedChecks / totalChecks : 0.0;
  
  List<QualityCheckResult> get failedResults => 
      results.where((r) => !r.passed).toList();
      
  List<QualityCheckResult> get passedResults => 
      results.where((r) => r.passed).toList();
      
  bool get isExcellent => criticalIssues == 0 && warningIssues <= 2 && successRate >= 0.95;
  bool get isGood => criticalIssues == 0 && successRate >= 0.90;
  bool get isAcceptable => criticalIssues <= 1 && successRate >= 0.80;
}

/// Quality categories
enum QualityCategory {
  functionality,
  performance,
  usability,
  reliability,
  security,
  compatibility,
}

/// Quality severity levels
enum QualitySeverity {
  critical,
  warning,
  info,
}
