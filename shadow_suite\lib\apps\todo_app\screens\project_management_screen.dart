import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/task_models.dart';

/// Advanced Project Management Screen with Kanban boards and Gantt charts
class ProjectManagementScreen extends ConsumerStatefulWidget {
  const ProjectManagementScreen({super.key});

  @override
  ConsumerState<ProjectManagementScreen> createState() =>
      _ProjectManagementScreenState();
}

class _ProjectManagementScreenState
    extends ConsumerState<ProjectManagementScreen> {
  String _selectedView = 'kanban';
  String? _selectedProjectId;

  @override
  Widget build(BuildContext context) {
    final projects = <Project>[]; // Placeholder - replace with actual provider
    final tasks = <Task>[]; // Placeholder - replace with actual provider

    return Scaffold(
      appBar: AppBar(
        title: const Text('Project Management'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) => setState(() => _selectedView = value),
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'kanban', child: Text('Kanban Board')),
              const PopupMenuItem(value: 'gantt', child: Text('Gantt Chart')),
              const PopupMenuItem(
                value: 'timeline',
                child: Text('Timeline View'),
              ),
              const PopupMenuItem(
                value: 'calendar',
                child: Text('Calendar View'),
              ),
            ],
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showCreateProjectDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildProjectSelector(projects),
          _buildViewSelector(),
          Expanded(child: _buildCurrentView(projects, tasks)),
        ],
      ),
    );
  }

  Widget _buildProjectSelector(List<Project> projects) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          const Icon(Icons.folder, color: Colors.red),
          const SizedBox(width: 12),
          Expanded(
            child: DropdownButtonFormField<String>(
              value: _selectedProjectId,
              decoration: const InputDecoration(
                labelText: 'Select Project',
                border: OutlineInputBorder(),
                isDense: true,
              ),
              items: [
                const DropdownMenuItem(
                  value: null,
                  child: Text('All Projects'),
                ),
                ...projects.map((project) {
                  return DropdownMenuItem(
                    value: project.id,
                    child: Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: project.color,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(child: Text(project.name)),
                      ],
                    ),
                  );
                }),
              ],
              onChanged: (value) => setState(() => _selectedProjectId = value),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildViewSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: SegmentedButton<String>(
              segments: const [
                ButtonSegment(
                  value: 'kanban',
                  label: Text('Kanban'),
                  icon: Icon(Icons.view_column),
                ),
                ButtonSegment(
                  value: 'gantt',
                  label: Text('Gantt'),
                  icon: Icon(Icons.timeline),
                ),
                ButtonSegment(
                  value: 'timeline',
                  label: Text('Timeline'),
                  icon: Icon(Icons.schedule),
                ),
                ButtonSegment(
                  value: 'calendar',
                  label: Text('Calendar'),
                  icon: Icon(Icons.calendar_month),
                ),
              ],
              selected: {_selectedView},
              onSelectionChanged: (Set<String> selection) {
                setState(() => _selectedView = selection.first);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentView(List<Project> projects, List<Task> tasks) {
    final filteredTasks = _selectedProjectId != null
        ? tasks.where((task) => task.projectId == _selectedProjectId).toList()
        : tasks;

    switch (_selectedView) {
      case 'kanban':
        return _buildKanbanBoard(filteredTasks);
      case 'gantt':
        return _buildGanttChart(filteredTasks);
      case 'timeline':
        return _buildTimelineView(filteredTasks);
      case 'calendar':
        return _buildCalendarView(filteredTasks);
      default:
        return _buildKanbanBoard(filteredTasks);
    }
  }

  Widget _buildKanbanBoard(List<Task> tasks) {
    final todoTasks = tasks.where((t) => t.status == TaskStatus.todo).toList();
    final inProgressTasks = tasks
        .where((t) => t.status == TaskStatus.inProgress)
        .toList();
    final completedTasks = tasks
        .where((t) => t.status == TaskStatus.completed)
        .toList();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(child: _buildKanbanColumn('To Do', todoTasks, Colors.blue)),
          const SizedBox(width: 16),
          Expanded(
            child: _buildKanbanColumn(
              'In Progress',
              inProgressTasks,
              Colors.orange,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildKanbanColumn(
              'Completed',
              completedTasks,
              Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKanbanColumn(String title, List<Task> tasks, Color color) {
    return Container(
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Text(
                  title,
                  style: TextStyle(fontWeight: FontWeight.bold, color: color),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${tasks.length}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(8),
              itemCount: tasks.length,
              itemBuilder: (context, index) {
                final task = tasks[index];
                return _buildKanbanCard(task);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKanbanCard(Task task) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        elevation: 2,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: task.color.withValues(alpha: 0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: task.color,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      task.title,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  _buildPriorityIcon(task.priority),
                ],
              ),
              if (task.description.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  task.description,
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              if (task.dueDate != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.schedule,
                      size: 12,
                      color: _isOverdue(task.dueDate!)
                          ? Colors.red
                          : Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${task.dueDate!.day}/${task.dueDate!.month}',
                      style: TextStyle(
                        fontSize: 12,
                        color: _isOverdue(task.dueDate!)
                            ? Colors.red
                            : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
              if (task.subtasks.isNotEmpty) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.checklist, size: 12),
                    const SizedBox(width: 4),
                    Text(
                      '${task.subtasks.where((s) => s.isCompleted).length}/${task.subtasks.length}',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGanttChart(List<Task> tasks) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Row(
              children: [
                Icon(Icons.timeline, color: Colors.red),
                SizedBox(width: 12),
                Text(
                  'Gantt Chart View',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: tasks.length,
              itemBuilder: (context, index) {
                final task = tasks[index];
                return _buildGanttRow(task);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGanttRow(Task task) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  task.title,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                if (task.dueDate != null)
                  Text(
                    'Due: ${task.dueDate!.day}/${task.dueDate!.month}/${task.dueDate!.year}',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
              ],
            ),
          ),
          Expanded(
            flex: 3,
            child: Container(
              height: 20,
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(10),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: task.progress,
                child: Container(
                  decoration: BoxDecoration(
                    color: task.color,
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            '${(task.progress * 100).toInt()}%',
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineView(List<Task> tasks) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Row(
              children: [
                Icon(Icons.schedule, color: Colors.red),
                SizedBox(width: 12),
                Text(
                  'Timeline View',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: tasks.length,
              itemBuilder: (context, index) {
                final task = tasks[index];
                return _buildTimelineItem(task, index == tasks.length - 1);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineItem(Task task, bool isLast) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: task.color,
                shape: BoxShape.circle,
              ),
            ),
            if (!isLast)
              Container(width: 2, height: 60, color: Colors.grey.shade300),
          ],
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  task.title,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                if (task.description.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    task.description,
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ],
                const SizedBox(height: 8),
                Row(
                  children: [
                    _buildPriorityIcon(task.priority),
                    const SizedBox(width: 8),
                    Text(
                      task.status.name.toUpperCase(),
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: _getStatusColor(task.status),
                      ),
                    ),
                    if (task.dueDate != null) ...[
                      const Spacer(),
                      Icon(Icons.schedule, size: 12, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Text(
                        '${task.dueDate!.day}/${task.dueDate!.month}',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCalendarView(List<Task> tasks) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Row(
              children: [
                Icon(Icons.calendar_month, color: Colors.red),
                SizedBox(width: 12),
                Text(
                  'Calendar View',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'Calendar integration coming soon...',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityIcon(TaskPriority priority) {
    IconData icon;
    Color color;

    switch (priority) {
      case TaskPriority.urgent:
        icon = Icons.priority_high;
        color = Colors.red;
        break;
      case TaskPriority.high:
        icon = Icons.keyboard_arrow_up;
        color = Colors.orange;
        break;
      case TaskPriority.medium:
        icon = Icons.remove;
        color = Colors.blue;
        break;
      case TaskPriority.low:
        icon = Icons.keyboard_arrow_down;
        color = Colors.green;
        break;
    }

    return Icon(icon, size: 16, color: color);
  }

  Color _getStatusColor(TaskStatus status) {
    switch (status) {
      case TaskStatus.todo:
        return Colors.blue;
      case TaskStatus.inProgress:
        return Colors.orange;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.cancelled:
        return Colors.red;
      case TaskStatus.onHold:
        return Colors.grey;
    }
  }

  bool _isOverdue(DateTime dueDate) {
    return dueDate.isBefore(DateTime.now());
  }

  void _showCreateProjectDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create New Project'),
        content: const Text(
          'Project creation dialog will be implemented here.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }
}
