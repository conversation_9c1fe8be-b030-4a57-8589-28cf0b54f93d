import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/surah.dart';
import '../models/verse.dart' as verse_model;
import '../models/bookmark.dart' as bookmark_model;
import '../models/athkar.dart';
import 'islamic_database_service.dart';
import 'quran_data_parser.dart';

// Surah Providers
final surahsProvider =
    StateNotifierProvider<SurahsNotifier, AsyncValue<List<Surah>>>((ref) {
      return SurahsNotifier();
    });

class SurahsNotifier extends StateNotifier<AsyncValue<List<Surah>>> {
  SurahsNotifier() : super(const AsyncValue.loading()) {
    loadSurahs();
  }

  Future<void> loadSurahs() async {
    try {
      state = const AsyncValue.loading();
      final surahs = await IslamicDatabaseService.getAllSurahs();
      state = AsyncValue.data(surahs);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Verses Providers
final versesProvider =
    StateNotifierProvider.family<
      VersesNotifier,
      AsyncValue<List<verse_model.Verse>>,
      int
    >((ref, surahNumber) {
      return VersesNotifier(surahNumber);
    });

class VersesNotifier
    extends StateNotifier<AsyncValue<List<verse_model.Verse>>> {
  final int surahNumber;

  VersesNotifier(this.surahNumber) : super(const AsyncValue.loading()) {
    loadVerses();
  }

  Future<void> loadVerses() async {
    try {
      state = const AsyncValue.loading();
      var verses = await IslamicDatabaseService.getVersesBySurah(surahNumber);

      // If no verses found, try to parse and populate Quran data with timeout
      if (verses.isEmpty) {
        await QuranDataParser.parseAndPopulateQuran().timeout(
          const Duration(seconds: 30),
          onTimeout: () {
            throw Exception('Quran data loading timed out. Please try again.');
          },
        );
        verses = await IslamicDatabaseService.getVersesBySurah(surahNumber);
      }

      state = AsyncValue.data(verses);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Bookmarks Providers
final bookmarksProvider =
    StateNotifierProvider<
      BookmarksNotifier,
      AsyncValue<List<bookmark_model.Bookmark>>
    >((ref) {
      return BookmarksNotifier();
    });

class BookmarksNotifier
    extends StateNotifier<AsyncValue<List<bookmark_model.Bookmark>>> {
  BookmarksNotifier() : super(const AsyncValue.loading()) {
    loadBookmarks();
  }

  Future<void> loadBookmarks() async {
    try {
      state = const AsyncValue.loading();
      final bookmarks = await IslamicDatabaseService.getAllBookmarks();
      state = AsyncValue.data(bookmarks);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addBookmark(bookmark_model.Bookmark bookmark) async {
    try {
      await IslamicDatabaseService.insertBookmark(bookmark);
      await loadBookmarks();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateBookmark(bookmark_model.Bookmark bookmark) async {
    try {
      await IslamicDatabaseService.updateBookmark(bookmark);
      await loadBookmarks();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteBookmark(String id) async {
    try {
      await IslamicDatabaseService.deleteBookmark(id);
      await loadBookmarks();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Dhikr Providers
final dhikrProvider =
    StateNotifierProvider<DhikrNotifier, AsyncValue<List<Dhikr>>>((ref) {
      return DhikrNotifier();
    });

class DhikrNotifier extends StateNotifier<AsyncValue<List<Dhikr>>> {
  DhikrNotifier() : super(const AsyncValue.loading()) {
    loadDhikr();
  }

  Future<void> loadDhikr() async {
    try {
      state = const AsyncValue.loading();
      final dhikr = await IslamicDatabaseService.getAllDhikr();
      state = AsyncValue.data(dhikr);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<List<Dhikr>> getDhikrByCategory(AthkarCategory category) async {
    return await IslamicDatabaseService.getDhikrByCategory(category);
  }

  Future<String> addCustomDhikr(Dhikr dhikr) async {
    try {
      final dhikrId = await IslamicDatabaseService.insertDhikr(dhikr);
      await loadDhikr(); // Refresh the list
      return dhikrId;
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }
}

// Dhikr Sessions Providers
final dhikrSessionsProvider =
    StateNotifierProvider<
      DhikrSessionsNotifier,
      AsyncValue<List<DhikrSession>>
    >((ref) {
      return DhikrSessionsNotifier();
    });

class DhikrSessionsNotifier
    extends StateNotifier<AsyncValue<List<DhikrSession>>> {
  DhikrSessionsNotifier() : super(const AsyncValue.loading()) {
    loadSessions();
  }

  Future<void> loadSessions() async {
    try {
      state = const AsyncValue.loading();
      final sessions = await IslamicDatabaseService.getAllDhikrSessions();
      state = AsyncValue.data(sessions);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addSession(DhikrSession session) async {
    try {
      await IslamicDatabaseService.insertDhikrSession(session);
      await loadSessions();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateSession(DhikrSession session) async {
    try {
      await IslamicDatabaseService.updateDhikrSession(session);
      await loadSessions();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteSession(String id) async {
    try {
      await IslamicDatabaseService.deleteDhikrSession(id);
      await loadSessions();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Custom Athkar Providers
final customAthkarProvider =
    StateNotifierProvider<
      CustomAthkarNotifier,
      AsyncValue<List<CustomAthkarRoutine>>
    >((ref) {
      return CustomAthkarNotifier();
    });

class CustomAthkarNotifier
    extends StateNotifier<AsyncValue<List<CustomAthkarRoutine>>> {
  CustomAthkarNotifier() : super(const AsyncValue.loading()) {
    loadCustomAthkar();
  }

  Future<void> loadCustomAthkar() async {
    try {
      state = const AsyncValue.loading();
      final routines = await IslamicDatabaseService.getAllCustomAthkar();
      state = AsyncValue.data(routines);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addCustomAthkar(CustomAthkarRoutine routine) async {
    try {
      await IslamicDatabaseService.insertCustomAthkar(routine);
      await loadCustomAthkar();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateCustomAthkar(CustomAthkarRoutine routine) async {
    try {
      await IslamicDatabaseService.updateCustomAthkar(routine);
      await loadCustomAthkar();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteCustomAthkar(String id) async {
    try {
      await IslamicDatabaseService.deleteCustomAthkar(id);
      await loadCustomAthkar();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Statistics Provider
final islamicStatisticsProvider = FutureProvider<Map<String, int>>((ref) async {
  return await IslamicDatabaseService.getStatistics();
});

// Daily Progress Provider
final dailyProgressProvider = FutureProvider<Map<AthkarCategory, int>>((
  ref,
) async {
  return await IslamicDatabaseService.getTodayProgress();
});

// Search Provider
final quranSearchProvider =
    FutureProvider.family<List<verse_model.Verse>, String>((ref, query) async {
      if (query.isEmpty) return [];
      return await IslamicDatabaseService.searchVerses(query);
    });

// UI State Providers
final islamicAppCurrentScreenProvider = StateProvider<IslamicAppScreen>(
  (ref) => IslamicAppScreen.dashboard,
);

enum IslamicAppScreen {
  dashboard,
  athkarCategories,
  dhikrCounter,
  progressTracking,
  customAthkarCreator,
  surahList,
  quranReading,
  quranSearch,
  bookmarks,
  hadithCollections,
  hadithReading,
  tafseerList,
  tafseerReading,
  prayerTimes,
  qiblaCompass,
}

// Selected Item Providers
final selectedSurahProvider = StateProvider<Surah?>((ref) => null);
final selectedDhikrProvider = StateProvider<Dhikr?>((ref) => null);
final selectedBookmarkProvider = StateProvider<bookmark_model.Bookmark?>(
  (ref) => null,
);
final selectedCustomAthkarProvider = StateProvider<CustomAthkarRoutine?>(
  (ref) => null,
);
final currentDhikrSessionProvider = StateProvider<DhikrSession?>((ref) => null);

// Quran Reading Settings
final quranFontSizeProvider = StateProvider<double>((ref) => 18.0);
final quranTranslationFontSizeProvider = StateProvider<double>((ref) => 14.0);
final quranNightModeProvider = StateProvider<bool>((ref) => false);
final quranShowTranslationProvider = StateProvider<bool>((ref) => true);
final quranShowTransliterationProvider = StateProvider<bool>((ref) => false);

enum QuranViewMode { book, verse }

final quranViewModeProvider = StateProvider<QuranViewMode>(
  (ref) => QuranViewMode.verse,
);

// Athkar Filter Providers
final selectedAthkarCategoryProvider = StateProvider<AthkarCategory?>(
  (ref) => null,
);

// Search Providers
final quranSearchQueryProvider = StateProvider<String>((ref) => '');

// Counter State Provider
final dhikrCounterProvider = StateProvider<int>((ref) => 0);
final dhikrTargetCountProvider = StateProvider<int>((ref) => 33);
final dhikrCounterHapticProvider = StateProvider<bool>((ref) => true);
final dhikrCounterSoundProvider = StateProvider<bool>((ref) => true);
