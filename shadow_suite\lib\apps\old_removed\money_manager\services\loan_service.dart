import 'dart:async';
import 'dart:math' as math;
import '../models/loan_models.dart';
import '../../../core/database/database_service.dart';
import '../../../core/services/error_handler.dart' as error_handler;

class LoanService {
  static final List<Loan> _loans = [];
  static final List<LoanPayment> _payments = [];
  static final List<FinancialForecast> _forecasts = [];

  // Initialize loan service
  static Future<void> initialize() async {
    await _loadLoans();
    await _loadPayments();
    await _loadForecasts();
  }

  // FEATURE 11: Loan Management
  static Future<Loan> createLoan({
    required String name,
    required LoanType type,
    required double principalAmount,
    required double interestRate,
    required int termMonths,
    required String lender,
    DateTime? startDate,
  }) async {
    try {
      final loanStartDate = startDate ?? DateTime.now();
      final monthlyPayment = _calculateMonthlyPayment(principalAmount, interestRate, termMonths);
      final maturityDate = DateTime(
        loanStartDate.year,
        loanStartDate.month + termMonths,
        loanStartDate.day,
      );

      final loan = Loan(
        id: 'loan_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        type: type,
        principalAmount: principalAmount,
        currentBalance: principalAmount,
        interestRate: interestRate,
        termMonths: termMonths,
        monthlyPayment: monthlyPayment,
        startDate: loanStartDate,
        maturityDate: maturityDate,
        lender: lender,
        status: LoanStatus.active,
        payments: [],
        metadata: {},
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      await DatabaseService.safeInsert('loans', loan.toJson());
      _loans.add(loan);

      // Generate payment schedule
      await _generatePaymentSchedule(loan);

      return loan;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Create loan');
      rethrow;
    }
  }

  // FEATURE 12: Payment Tracking
  static Future<LoanPayment> recordPayment({
    required String loanId,
    required double amount,
    DateTime? paymentDate,
    String? notes,
  }) async {
    try {
      final loan = _loans.firstWhere((l) => l.id == loanId);
      final nextPayment = _getNextPendingPayment(loanId);

      if (nextPayment == null) {
        throw Exception('No pending payments found for this loan');
      }

      final paidDate = paymentDate ?? DateTime.now();
      final updatedPayment = nextPayment.copyWith(
        status: PaymentStatus.paid,
        paidDate: paidDate,
        notes: notes,
      );

      await DatabaseService.safeUpdate(
        'loan_payments',
        updatedPayment.toJson(),
        where: 'id = ?',
        whereArgs: [nextPayment.id],
      );

      // Update payment in local cache
      final paymentIndex = _payments.indexWhere((p) => p.id == nextPayment.id);
      if (paymentIndex != -1) {
        _payments[paymentIndex] = updatedPayment;
      }

      // Update loan balance
      final newBalance = loan.currentBalance - updatedPayment.principalAmount;
      final updatedLoan = loan.copyWith(currentBalance: newBalance);

      await DatabaseService.safeUpdate(
        'loans',
        updatedLoan.toJson(),
        where: 'id = ?',
        whereArgs: [loanId],
      );

      // Update loan in local cache
      final loanIndex = _loans.indexWhere((l) => l.id == loanId);
      if (loanIndex != -1) {
        _loans[loanIndex] = updatedLoan;
      }

      return updatedPayment;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Record loan payment');
      rethrow;
    }
  }

  // FEATURE 13: Amortization Schedule
  static List<LoanPayment> generateAmortizationSchedule({
    required double principalAmount,
    required double interestRate,
    required int termMonths,
    DateTime? startDate,
  }) {
    try {
      final schedule = <LoanPayment>[];
      final monthlyRate = interestRate / 100 / 12;
      final monthlyPayment = _calculateMonthlyPayment(principalAmount, interestRate, termMonths);
      var remainingBalance = principalAmount;
      final loanStartDate = startDate ?? DateTime.now();

      for (int i = 1; i <= termMonths; i++) {
        final interestAmount = remainingBalance * monthlyRate;
        final principalPayment = monthlyPayment - interestAmount;
        remainingBalance -= principalPayment;

        final dueDate = DateTime(
          loanStartDate.year,
          loanStartDate.month + i,
          loanStartDate.day,
        );

        schedule.add(LoanPayment(
          id: 'payment_${i}_${DateTime.now().millisecondsSinceEpoch}',
          loanId: 'temp_loan_id',
          paymentNumber: i,
          amount: monthlyPayment,
          principalAmount: principalPayment,
          interestAmount: interestAmount,
          remainingBalance: math.max(0, remainingBalance),
          dueDate: dueDate,
          status: PaymentStatus.pending,
          metadata: {},
        ));
      }

      return schedule;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Generate amortization schedule');
      return [];
    }
  }

  // FEATURE 14: Loan Calculator
  static LoanCalculationResult calculateLoan({
    double? principalAmount,
    double? interestRate,
    int? termMonths,
    double? monthlyPayment,
  }) {
    try {
      // Calculate missing parameter based on provided ones
      if (principalAmount != null && interestRate != null && termMonths != null) {
        // Calculate monthly payment
        final payment = _calculateMonthlyPayment(principalAmount, interestRate, termMonths);
        final totalPayments = payment * termMonths;
        final totalInterest = totalPayments - principalAmount;

        return LoanCalculationResult(
          principalAmount: principalAmount,
          interestRate: interestRate,
          termMonths: termMonths,
          monthlyPayment: payment,
          totalPayments: totalPayments,
          totalInterest: totalInterest,
        );
      } else if (principalAmount != null && interestRate != null && monthlyPayment != null) {
        // Calculate term
        final monthlyRate = interestRate / 100 / 12;
        final term = _calculateLoanTerm(principalAmount, monthlyPayment, monthlyRate);
        final totalPayments = monthlyPayment * term;
        final totalInterest = totalPayments - principalAmount;

        return LoanCalculationResult(
          principalAmount: principalAmount,
          interestRate: interestRate,
          termMonths: term,
          monthlyPayment: monthlyPayment,
          totalPayments: totalPayments,
          totalInterest: totalInterest,
        );
      } else {
        throw ArgumentError('Insufficient parameters for loan calculation');
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Calculate loan');
      rethrow;
    }
  }

  // FEATURE 15: Early Payment Analysis
  static EarlyPaymentAnalysis analyzeEarlyPayment({
    required String loanId,
    required double extraPayment,
    EarlyPaymentType type = EarlyPaymentType.monthly,
  }) {
    try {
      final loan = _loans.firstWhere((l) => l.id == loanId);
      final currentSchedule = _payments.where((p) => p.loanId == loanId).toList();

      // Calculate savings with extra payments
      final originalTotalInterest = loan.totalInterestPaid +
          currentSchedule.where((p) => p.status == PaymentStatus.pending)
              .fold<double>(0, (sum, p) => sum + p.interestAmount);

      final newSchedule = _calculateScheduleWithExtraPayments(
        loan, extraPayment, type);

      final newTotalInterest = newSchedule.fold<double>(0, (sum, p) => sum + p.interestAmount);
      final interestSavings = originalTotalInterest - newTotalInterest;
      final timeSavings = loan.termMonths - newSchedule.length;

      return EarlyPaymentAnalysis(
        loanId: loanId,
        extraPayment: extraPayment,
        type: type,
        interestSavings: interestSavings,
        timeSavingsMonths: timeSavings,
        newPayoffDate: newSchedule.isNotEmpty ? newSchedule.last.dueDate : loan.maturityDate!,
        originalPayoffDate: loan.maturityDate!,
        newSchedule: newSchedule,
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Analyze early payment');
      rethrow;
    }
  }

  // FEATURE 16: Refinancing Analysis
  static RefinancingAnalysis analyzeRefinancing({
    required String loanId,
    required double newInterestRate,
    required int newTermMonths,
    required double closingCosts,
  }) {
    try {
      final loan = _loans.firstWhere((l) => l.id == loanId);

      // Current loan details
      final currentMonthlyPayment = loan.monthlyPayment;
      final remainingPayments = loan.remainingPayments;
      final currentTotalCost = currentMonthlyPayment * remainingPayments;

      // New loan details
      final newMonthlyPayment = _calculateMonthlyPayment(
        loan.currentBalance, newInterestRate, newTermMonths);
      final newTotalCost = (newMonthlyPayment * newTermMonths) + closingCosts;

      final monthlySavings = currentMonthlyPayment - newMonthlyPayment;
      final totalSavings = currentTotalCost - newTotalCost;
      final breakEvenMonths = closingCosts / monthlySavings;

      return RefinancingAnalysis(
        loanId: loanId,
        currentMonthlyPayment: currentMonthlyPayment,
        newMonthlyPayment: newMonthlyPayment,
        monthlySavings: monthlySavings,
        totalSavings: totalSavings,
        closingCosts: closingCosts,
        breakEvenMonths: breakEvenMonths,
        newInterestRate: newInterestRate,
        newTermMonths: newTermMonths,
        isRecommended: totalSavings > 0 && breakEvenMonths < 24,
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Analyze refinancing');
      rethrow;
    }
  }

  // FEATURE 17: Debt Consolidation
  static DebtConsolidationAnalysis analyzeDebtConsolidation({
    required List<String> loanIds,
    required double consolidationRate,
    required int consolidationTerm,
    required double consolidationFees,
  }) {
    try {
      final loansToConsolidate = _loans.where((l) => loanIds.contains(l.id)).toList();

      final totalCurrentBalance = loansToConsolidate.fold<double>(0, (sum, loan) => sum + loan.currentBalance);
      final totalCurrentPayments = loansToConsolidate.fold<double>(0, (sum, loan) => sum + loan.monthlyPayment);

      final consolidatedPayment = _calculateMonthlyPayment(
        totalCurrentBalance, consolidationRate, consolidationTerm);

      final monthlySavings = totalCurrentPayments - consolidatedPayment;
      final totalInterestCurrent = loansToConsolidate.fold<double>(0, (sum, loan) {
        final remainingInterest = loan.monthlyPayment * loan.remainingPayments - loan.currentBalance;
        return sum + remainingInterest;
      });

      final totalInterestConsolidated = (consolidatedPayment * consolidationTerm) - totalCurrentBalance;
      final interestSavings = totalInterestCurrent - totalInterestConsolidated - consolidationFees;

      return DebtConsolidationAnalysis(
        loanIds: loanIds,
        totalCurrentBalance: totalCurrentBalance,
        totalCurrentPayments: totalCurrentPayments,
        consolidatedPayment: consolidatedPayment,
        monthlySavings: monthlySavings,
        interestSavings: interestSavings,
        consolidationFees: consolidationFees,
        consolidationRate: consolidationRate,
        consolidationTerm: consolidationTerm,
        isRecommended: interestSavings > 0 && monthlySavings > 0,
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Analyze debt consolidation');
      rethrow;
    }
  }

  // FEATURE 18: Financial Forecasting
  static Future<FinancialForecast> createFinancialForecast({
    required String name,
    required ForecastType type,
    required DateTime startDate,
    required DateTime endDate,
    required Map<String, dynamic> assumptions,
  }) async {
    try {
      final forecastItems = await _generateForecastItems(type, startDate, endDate, assumptions);
      final summary = _calculateForecastSummary(forecastItems);

      final forecast = FinancialForecast(
        id: 'forecast_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        type: type,
        startDate: startDate,
        endDate: endDate,
        items: forecastItems,
        summary: summary,
        assumptions: assumptions,
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      await DatabaseService.safeInsert('financial_forecasts', forecast.toJson());
      _forecasts.add(forecast);

      return forecast;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Create financial forecast');
      rethrow;
    }
  }

  // FEATURE 19: Cash Flow Projection
  static Future<CashFlowProjection> generateCashFlowProjection({
    required DateTime startDate,
    required DateTime endDate,
    required Map<String, double> monthlyIncome,
    required Map<String, double> monthlyExpenses,
  }) async {
    try {
      final projections = <CashFlowItem>[];
      var runningBalance = 0.0;

      final totalMonthlyIncome = monthlyIncome.values.fold<double>(0, (sum, amount) => sum + amount);
      final totalMonthlyExpenses = monthlyExpenses.values.fold<double>(0, (sum, amount) => sum + amount);
      final netMonthlyFlow = totalMonthlyIncome - totalMonthlyExpenses;

      var currentDate = DateTime(startDate.year, startDate.month, 1);
      final endDateNormalized = DateTime(endDate.year, endDate.month, 1);

      while (currentDate.isBefore(endDateNormalized) || currentDate.isAtSameMomentAs(endDateNormalized)) {
        runningBalance += netMonthlyFlow;

        projections.add(CashFlowItem(
          date: currentDate,
          income: totalMonthlyIncome,
          expenses: totalMonthlyExpenses,
          netFlow: netMonthlyFlow,
          runningBalance: runningBalance,
        ));

        currentDate = DateTime(currentDate.year, currentDate.month + 1, 1);
      }

      return CashFlowProjection(
        startDate: startDate,
        endDate: endDate,
        projections: projections,
        totalIncome: totalMonthlyIncome * projections.length,
        totalExpenses: totalMonthlyExpenses * projections.length,
        netCashFlow: netMonthlyFlow * projections.length,
        finalBalance: runningBalance,
      );
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Generate cash flow projection');
      rethrow;
    }
  }

  // FEATURE 20: Loan Alerts and Notifications
  static List<LoanAlert> getLoanAlerts() {
    try {
      final alerts = <LoanAlert>[];
      final now = DateTime.now();

      for (final loan in _loans) {
        if (loan.status != LoanStatus.active) continue;

        // Payment due alerts
        final nextPayment = _getNextPendingPayment(loan.id);
        if (nextPayment != null) {
          final daysUntilDue = nextPayment.dueDate.difference(now).inDays;

          if (daysUntilDue <= 0) {
            alerts.add(LoanAlert(
              type: LoanAlertType.paymentOverdue,
              message: 'Payment for ${loan.name} is overdue',
              severity: LoanAlertSeverity.critical,
              loanId: loan.id,
              dueDate: nextPayment.dueDate,
              amount: nextPayment.amount,
            ));
          } else if (daysUntilDue <= 3) {
            alerts.add(LoanAlert(
              type: LoanAlertType.paymentDue,
              message: 'Payment for ${loan.name} is due in $daysUntilDue days',
              severity: LoanAlertSeverity.warning,
              loanId: loan.id,
              dueDate: nextPayment.dueDate,
              amount: nextPayment.amount,
            ));
          } else if (daysUntilDue <= 7) {
            alerts.add(LoanAlert(
              type: LoanAlertType.paymentReminder,
              message: 'Payment for ${loan.name} is due in $daysUntilDue days',
              severity: LoanAlertSeverity.info,
              loanId: loan.id,
              dueDate: nextPayment.dueDate,
              amount: nextPayment.amount,
            ));
          }
        }

        // Interest rate alerts (for variable rate loans)
        if (loan.metadata.containsKey('variable_rate') && loan.metadata['variable_rate'] == true) {
          alerts.add(LoanAlert(
            type: LoanAlertType.rateChange,
            message: 'Interest rate for ${loan.name} may change soon',
            severity: LoanAlertSeverity.info,
            loanId: loan.id,
          ));
        }

        // Payoff alerts
        if (loan.remainingPayments <= 12) {
          alerts.add(LoanAlert(
            type: LoanAlertType.nearPayoff,
            message: '${loan.name} will be paid off in ${loan.remainingPayments} payments',
            severity: LoanAlertSeverity.info,
            loanId: loan.id,
          ));
        }
      }

      return alerts..sort((a, b) => a.severity.index.compareTo(b.severity.index));
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Get loan alerts');
      return [];
    }
  }

  // Helper methods
  static Future<void> _loadLoans() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM loans');
      _loans.clear();
      for (final row in results) {
        _loans.add(Loan.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load loans');
    }
  }

  static Future<void> _loadPayments() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM loan_payments');
      _payments.clear();
      for (final row in results) {
        _payments.add(LoanPayment.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load payments');
    }
  }

  static Future<void> _loadForecasts() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM financial_forecasts');
      _forecasts.clear();
      for (final row in results) {
        _forecasts.add(FinancialForecast.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load forecasts');
    }
  }

  static double _calculateMonthlyPayment(double principal, double annualRate, int months) {
    if (annualRate == 0) return principal / months;

    final monthlyRate = annualRate / 100 / 12;
    final denominator = 1 - math.pow(1 + monthlyRate, -months);
    return principal * (monthlyRate / denominator);
  }

  static int _calculateLoanTerm(double principal, double monthlyPayment, double monthlyRate) {
    if (monthlyRate == 0) return (principal / monthlyPayment).round();

    final numerator = math.log(1 + (principal * monthlyRate / monthlyPayment));
    final denominator = math.log(1 + monthlyRate);
    return (-numerator / denominator).round();
  }

  static Future<void> _generatePaymentSchedule(Loan loan) async {
    try {
      final schedule = generateAmortizationSchedule(
        principalAmount: loan.principalAmount,
        interestRate: loan.interestRate,
        termMonths: loan.termMonths,
        startDate: loan.startDate,
      );

      for (final payment in schedule) {
        final paymentWithLoanId = LoanPayment(
          id: payment.id,
          loanId: loan.id,
          paymentNumber: payment.paymentNumber,
          amount: payment.amount,
          principalAmount: payment.principalAmount,
          interestAmount: payment.interestAmount,
          remainingBalance: payment.remainingBalance,
          dueDate: payment.dueDate,
          status: payment.status,
          metadata: payment.metadata,
        );

        await DatabaseService.safeInsert('loan_payments', paymentWithLoanId.toJson());
        _payments.add(paymentWithLoanId);
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Generate payment schedule');
    }
  }

  static LoanPayment? _getNextPendingPayment(String loanId) {
    final pendingPayments = _payments
        .where((p) => p.loanId == loanId && p.status == PaymentStatus.pending)
        .toList()
      ..sort((a, b) => a.dueDate.compareTo(b.dueDate));

    return pendingPayments.isNotEmpty ? pendingPayments.first : null;
  }

  static List<LoanPayment> _calculateScheduleWithExtraPayments(
    Loan loan, double extraPayment, EarlyPaymentType type) {
    // Simplified implementation - in production, use more sophisticated calculation
    final schedule = <LoanPayment>[];
    var remainingBalance = loan.currentBalance;
    final monthlyRate = loan.interestRate / 100 / 12;
    var paymentNumber = 1;

    while (remainingBalance > 0.01) {
      final interestAmount = remainingBalance * monthlyRate;
      var principalAmount = loan.monthlyPayment - interestAmount;

      if (type == EarlyPaymentType.monthly) {
        principalAmount += extraPayment;
      }

      if (principalAmount > remainingBalance) {
        principalAmount = remainingBalance;
      }

      remainingBalance -= principalAmount;

      final dueDate = DateTime(
        loan.startDate.year,
        loan.startDate.month + paymentNumber,
        loan.startDate.day,
      );

      schedule.add(LoanPayment(
        id: 'extra_payment_$paymentNumber',
        loanId: loan.id,
        paymentNumber: paymentNumber,
        amount: interestAmount + principalAmount,
        principalAmount: principalAmount,
        interestAmount: interestAmount,
        remainingBalance: remainingBalance,
        dueDate: dueDate,
        status: PaymentStatus.pending,
        metadata: {},
      ));

      paymentNumber++;
      if (paymentNumber > 600) break; // Safety check
    }

    return schedule;
  }

  static Future<List<ForecastItem>> _generateForecastItems(
    ForecastType type, DateTime startDate, DateTime endDate, Map<String, dynamic> assumptions) async {
    // Simplified forecast generation - in production, use historical data and ML
    final items = <ForecastItem>[];
    var currentDate = DateTime(startDate.year, startDate.month, 1);

    while (currentDate.isBefore(endDate)) {
      // Generate sample forecast items based on type
      switch (type) {
        case ForecastType.cashFlow:
          items.add(ForecastItem(
            id: 'forecast_item_${currentDate.millisecondsSinceEpoch}',
            forecastId: 'temp_forecast_id',
            date: currentDate,
            category: 'Income',
            description: 'Projected monthly income',
            amount: assumptions['monthly_income']?.toDouble() ?? 5000.0,
            type: ForecastItemType.income,
            confidence: 0.85,
            metadata: {},
          ));

          items.add(ForecastItem(
            id: 'forecast_item_exp_${currentDate.millisecondsSinceEpoch}',
            forecastId: 'temp_forecast_id',
            date: currentDate,
            category: 'Expenses',
            description: 'Projected monthly expenses',
            amount: assumptions['monthly_expenses']?.toDouble() ?? 3500.0,
            type: ForecastItemType.expense,
            confidence: 0.80,
            metadata: {},
          ));
          break;
        default:
          break;
      }

      currentDate = DateTime(currentDate.year, currentDate.month + 1, 1);
    }

    return items;
  }

  static ForecastSummary _calculateForecastSummary(List<ForecastItem> items) {
    final income = items.where((i) => i.type == ForecastItemType.income);
    final expenses = items.where((i) => i.type == ForecastItemType.expense);

    final totalIncome = income.fold<double>(0, (sum, item) => sum + item.amount);
    final totalExpenses = expenses.fold<double>(0, (sum, item) => sum + item.amount);
    final netCashFlow = totalIncome - totalExpenses;

    final monthsCount = items.isNotEmpty ?
        items.map((i) => '${i.date.year}-${i.date.month}').toSet().length : 1;

    return ForecastSummary(
      totalIncome: totalIncome,
      totalExpenses: totalExpenses,
      netCashFlow: netCashFlow,
      averageMonthlyIncome: totalIncome / monthsCount,
      averageMonthlyExpenses: totalExpenses / monthsCount,
      projectedSavings: netCashFlow,
      confidenceScore: 0.82,
      keyInsights: [
        'Positive cash flow projected',
        'Savings rate: ${((netCashFlow / totalIncome) * 100).toStringAsFixed(1)}%',
      ],
      recommendations: [
        'Consider increasing emergency fund',
        'Explore investment opportunities',
      ],
    );
  }

  // Getters
  static List<Loan> get loans => List.unmodifiable(_loans);
  static List<LoanPayment> get payments => List.unmodifiable(_payments);
  static List<FinancialForecast> get forecasts => List.unmodifiable(_forecasts);

  // Dispose
  static void dispose() {
    _loans.clear();
    _payments.clear();
    _forecasts.clear();
  }
}

// Additional data classes for loan features
class LoanCalculationResult {
  final double principalAmount;
  final double interestRate;
  final int termMonths;
  final double monthlyPayment;
  final double totalPayments;
  final double totalInterest;

  const LoanCalculationResult({
    required this.principalAmount,
    required this.interestRate,
    required this.termMonths,
    required this.monthlyPayment,
    required this.totalPayments,
    required this.totalInterest,
  });
}

class EarlyPaymentAnalysis {
  final String loanId;
  final double extraPayment;
  final EarlyPaymentType type;
  final double interestSavings;
  final int timeSavingsMonths;
  final DateTime newPayoffDate;
  final DateTime originalPayoffDate;
  final List<LoanPayment> newSchedule;

  const EarlyPaymentAnalysis({
    required this.loanId,
    required this.extraPayment,
    required this.type,
    required this.interestSavings,
    required this.timeSavingsMonths,
    required this.newPayoffDate,
    required this.originalPayoffDate,
    required this.newSchedule,
  });
}

class RefinancingAnalysis {
  final String loanId;
  final double currentMonthlyPayment;
  final double newMonthlyPayment;
  final double monthlySavings;
  final double totalSavings;
  final double closingCosts;
  final double breakEvenMonths;
  final double newInterestRate;
  final int newTermMonths;
  final bool isRecommended;

  const RefinancingAnalysis({
    required this.loanId,
    required this.currentMonthlyPayment,
    required this.newMonthlyPayment,
    required this.monthlySavings,
    required this.totalSavings,
    required this.closingCosts,
    required this.breakEvenMonths,
    required this.newInterestRate,
    required this.newTermMonths,
    required this.isRecommended,
  });
}

class DebtConsolidationAnalysis {
  final List<String> loanIds;
  final double totalCurrentBalance;
  final double totalCurrentPayments;
  final double consolidatedPayment;
  final double monthlySavings;
  final double interestSavings;
  final double consolidationFees;
  final double consolidationRate;
  final int consolidationTerm;
  final bool isRecommended;

  const DebtConsolidationAnalysis({
    required this.loanIds,
    required this.totalCurrentBalance,
    required this.totalCurrentPayments,
    required this.consolidatedPayment,
    required this.monthlySavings,
    required this.interestSavings,
    required this.consolidationFees,
    required this.consolidationRate,
    required this.consolidationTerm,
    required this.isRecommended,
  });
}

class CashFlowProjection {
  final DateTime startDate;
  final DateTime endDate;
  final List<CashFlowItem> projections;
  final double totalIncome;
  final double totalExpenses;
  final double netCashFlow;
  final double finalBalance;

  const CashFlowProjection({
    required this.startDate,
    required this.endDate,
    required this.projections,
    required this.totalIncome,
    required this.totalExpenses,
    required this.netCashFlow,
    required this.finalBalance,
  });
}

class CashFlowItem {
  final DateTime date;
  final double income;
  final double expenses;
  final double netFlow;
  final double runningBalance;

  const CashFlowItem({
    required this.date,
    required this.income,
    required this.expenses,
    required this.netFlow,
    required this.runningBalance,
  });
}

class LoanAlert {
  final LoanAlertType type;
  final String message;
  final LoanAlertSeverity severity;
  final String loanId;
  final DateTime? dueDate;
  final double? amount;

  const LoanAlert({
    required this.type,
    required this.message,
    required this.severity,
    required this.loanId,
    this.dueDate,
    this.amount,
  });
}

// Enums
enum EarlyPaymentType { monthly, lumpSum, biweekly }
enum LoanAlertType { paymentDue, paymentOverdue, paymentReminder, rateChange, nearPayoff }
enum LoanAlertSeverity { info, warning, critical }