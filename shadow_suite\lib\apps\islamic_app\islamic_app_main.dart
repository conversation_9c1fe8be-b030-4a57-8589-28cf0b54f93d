import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'services/islamic_providers.dart';
import 'screens/islamic_dashboard.dart';
import 'screens/athkar/athkar_categories_screen.dart';
import 'screens/athkar/dhikr_counter_screen.dart';
import 'screens/athkar/progress_tracking_screen.dart';
import 'screens/athkar/custom_athkar_creator_screen.dart';
import 'screens/quran/surah_list_screen.dart';
import 'screens/quran/quran_reading_screen.dart';
import 'screens/quran/quran_search_screen.dart';
import 'screens/quran/bookmarks_screen.dart';
import 'screens/hadith/hadith_collections_screen.dart';
import 'screens/hadith/hadith_reading_screen.dart';
import 'screens/tafseer/tafseer_list_screen.dart';
import 'screens/tafseer/tafseer_reading_screen.dart';
import 'screens/prayer_times/prayer_times_screen.dart';
import 'screens/qibla/qibla_compass_screen.dart';

class IslamicAppMain extends ConsumerWidget {
  const IslamicAppMain({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentScreen = ref.watch(islamicAppCurrentScreenProvider);

    return _getScreenForCurrentState(currentScreen);
  }

  Widget _getScreenForCurrentState(IslamicAppScreen screen) {
    switch (screen) {
      case IslamicAppScreen.dashboard:
        return const IslamicDashboard();
      case IslamicAppScreen.athkarCategories:
        return const AthkarCategoriesScreen();
      case IslamicAppScreen.dhikrCounter:
        return const DhikrCounterScreen();
      case IslamicAppScreen.progressTracking:
        return const ProgressTrackingScreen();
      case IslamicAppScreen.customAthkarCreator:
        return const CustomAthkarCreatorScreen();
      case IslamicAppScreen.surahList:
        return const SurahListScreen();
      case IslamicAppScreen.quranReading:
        return const QuranReadingScreen();
      case IslamicAppScreen.quranSearch:
        return const QuranSearchScreen();
      case IslamicAppScreen.bookmarks:
        return const BookmarksScreen();
      case IslamicAppScreen.hadithCollections:
        return const HadithCollectionsScreen();
      case IslamicAppScreen.hadithReading:
        return const HadithReadingScreen();
      case IslamicAppScreen.tafseerList:
        return const TafseerListScreen();
      case IslamicAppScreen.tafseerReading:
        return const TafseerReadingScreen();
      case IslamicAppScreen.prayerTimes:
        return const PrayerTimesScreen();
      case IslamicAppScreen.qiblaCompass:
        return const QiblaCompassScreen();
    }
  }
}
