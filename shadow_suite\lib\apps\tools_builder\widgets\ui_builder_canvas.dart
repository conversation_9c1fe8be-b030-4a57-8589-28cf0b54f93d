import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_component.dart';
import '../services/tools_builder_providers.dart';

class UIBuilderCanvas extends ConsumerStatefulWidget {
  final List<UIComponent> components;
  final Function(List<UIComponent>) onComponentsChanged;
  final Function(String) onComponentSelected;

  const UIBuilderCanvas({
    super.key,
    required this.components,
    required this.onComponentsChanged,
    required this.onComponentSelected,
  });

  @override
  ConsumerState<UIBuilderCanvas> createState() => _UIBuilderCanvasState();
}

class _UIBuilderCanvasState extends ConsumerState<UIBuilderCanvas> {
  Offset? _dragOffset;
  String? _draggedComponentId;

  @override
  Widget build(BuildContext context) {
    final showGrid = ref.watch(showGridProvider);
    final gridSize = ref.watch(gridSizeProvider);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: DragTarget<ComponentType>(
        onAcceptWithDetails: (details) {
          _addComponentFromPalette(details.data, details.offset);
        },
        builder: (context, candidateData, rejectedData) {
          return Stack(
            children: [
              // Grid background
              if (showGrid)
                CustomPaint(
                  size: Size.infinite,
                  painter: GridPainter(gridSize: gridSize),
                ),

              // Drop zone indicator
              if (candidateData.isNotEmpty)
                Container(
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    border: Border.all(color: Colors.blue, width: 2),
                  ),
                ),

              // Components
              ...widget.components.map(
                (component) => _buildDraggableComponent(component),
              ),

              // Empty state
              if (widget.components.isEmpty)
                const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.design_services, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'Drag components here to build your UI',
                        style: TextStyle(fontSize: 18, color: Colors.grey),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Components will automatically bind to spreadsheet cells',
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildDraggableComponent(UIComponent component) {
    final selectedComponentId = ref.watch(selectedComponentProvider);
    final isSelected = selectedComponentId == component.id;

    return Positioned(
      left: component.x,
      top: component.y,
      child: GestureDetector(
        onTap: () {
          widget.onComponentSelected(component.id);
        },
        onPanStart: (details) {
          _draggedComponentId = component.id;
          _dragOffset = details.localPosition;
          widget.onComponentSelected(component.id);
        },
        onPanUpdate: (details) {
          if (_draggedComponentId == component.id) {
            final snapToGrid = ref.read(snapToGridProvider);
            final gridSize = ref.read(gridSizeProvider);

            double newX = details.globalPosition.dx - (_dragOffset?.dx ?? 0);
            double newY = details.globalPosition.dy - (_dragOffset?.dy ?? 0);

            // Snap to grid if enabled
            if (snapToGrid) {
              newX = ((newX / gridSize).round() * gridSize).toDouble();
              newY = ((newY / gridSize).round() * gridSize).toDouble();
            }

            // Ensure component stays within bounds
            newX = newX.clamp(0.0, double.infinity);
            newY = newY.clamp(0.0, double.infinity);

            final updatedComponent = component.copyWith(x: newX, y: newY);
            _updateComponent(updatedComponent);
          }
        },
        onPanEnd: (details) {
          _draggedComponentId = null;
          _dragOffset = null;
        },
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? Colors.blue : Colors.transparent,
              width: 2,
            ),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Stack(
            children: [
              _buildComponentWidget(component),

              // Selection handles
              if (isSelected) ...[
                _buildResizeHandle(component, Alignment.topLeft),
                _buildResizeHandle(component, Alignment.topRight),
                _buildResizeHandle(component, Alignment.bottomLeft),
                _buildResizeHandle(component, Alignment.bottomRight),
              ],

              // Delete button
              if (isSelected)
                Positioned(
                  top: -10,
                  right: -10,
                  child: GestureDetector(
                    onTap: () => _deleteComponent(component.id),
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        size: 12,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildComponentWidget(UIComponent component) {
    switch (component.type) {
      case ComponentType.textInput:
        return SizedBox(
          width: component.style.width ?? 200,
          height: component.style.height ?? 40,
          child: TextField(
            decoration: InputDecoration(
              labelText: component.label,
              hintText: component.placeholder,
              border: const OutlineInputBorder(),
            ),
            enabled: false, // Disabled in design mode
          ),
        );

      case ComponentType.numberInput:
        return SizedBox(
          width: component.style.width ?? 200,
          height: component.style.height ?? 40,
          child: TextField(
            decoration: InputDecoration(
              labelText: component.label,
              hintText: component.placeholder,
              border: const OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
            enabled: false,
          ),
        );

      case ComponentType.dropdown:
        return SizedBox(
          width: component.style.width ?? 200,
          height: component.style.height ?? 40,
          child: DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: component.label,
              border: const OutlineInputBorder(),
            ),
            items:
                (component.properties['options'] as List<String>? ??
                        ['Option 1', 'Option 2'])
                    .map(
                      (option) =>
                          DropdownMenuItem(value: option, child: Text(option)),
                    )
                    .toList(),
            onChanged: null, // Disabled in design mode
          ),
        );

      case ComponentType.checkbox:
        return SizedBox(
          width: component.style.width ?? 150,
          height: component.style.height ?? 40,
          child: CheckboxListTile(
            title: Text(component.label),
            value: false,
            onChanged: null, // Disabled in design mode
            dense: true,
          ),
        );

      case ComponentType.radioButton:
        return SizedBox(
          width: component.style.width ?? 150,
          height: component.style.height ?? 40,
          child: RadioListTile<bool>(
            title: Text(component.label),
            value: true,
            groupValue: false,
            onChanged: null, // Disabled in design mode
            dense: true,
          ),
        );

      case ComponentType.button:
        return SizedBox(
          width: component.style.width ?? 120,
          height: component.style.height ?? 40,
          child: ElevatedButton(
            onPressed: null, // Disabled in design mode
            child: Text(component.label),
          ),
        );

      case ComponentType.label:
        return Container(
          width: component.style.width ?? 100,
          height: component.style.height ?? 30,
          alignment: Alignment.centerLeft,
          child: Text(
            component.label,
            style: TextStyle(
              fontSize: component.style.fontSize ?? 16,
              fontWeight: component.style.isBold
                  ? FontWeight.bold
                  : FontWeight.normal,
              fontStyle: component.style.isItalic
                  ? FontStyle.italic
                  : FontStyle.normal,
              color: component.style.textColor != null
                  ? Color(
                      int.parse(
                        component.style.textColor!.replaceFirst('#', '0xFF'),
                      ),
                    )
                  : null,
            ),
          ),
        );

      case ComponentType.slider:
        return SizedBox(
          width: component.style.width ?? 200,
          height: component.style.height ?? 60,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(component.label),
              Slider(
                value: 0.5,
                onChanged: null, // Disabled in design mode
              ),
            ],
          ),
        );

      case ComponentType.dateInput:
        return SizedBox(
          width: component.style.width ?? 200,
          height: component.style.height ?? 40,
          child: TextField(
            decoration: InputDecoration(
              labelText: component.label,
              hintText: 'Select date',
              border: const OutlineInputBorder(),
              suffixIcon: const Icon(Icons.calendar_today),
            ),
            enabled: false,
          ),
        );

      case ComponentType.textArea:
        return SizedBox(
          width: component.style.width ?? 300,
          height: component.style.height ?? 100,
          child: TextField(
            decoration: InputDecoration(
              labelText: component.label,
              hintText: component.placeholder,
              border: const OutlineInputBorder(),
            ),
            maxLines: 4,
            enabled: false,
          ),
        );

      case ComponentType.container:
        return Container(
          width: component.style.width ?? 200,
          height: component.style.height ?? 100,
          decoration: BoxDecoration(
            color: component.style.backgroundColor != null
                ? Color(
                    int.parse(
                      component.style.backgroundColor!.replaceFirst(
                        '#',
                        '0xFF',
                      ),
                    ),
                  )
                : Colors.grey[100],
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(
              component.style.borderRadius ?? 4,
            ),
          ),
          child: Center(
            child: Text(
              component.label.isEmpty ? 'Container' : component.label,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        );

      case ComponentType.divider:
        return Container(
          width: component.style.width ?? 200,
          height: component.style.height ?? 1,
          color: Colors.grey,
        );

      case ComponentType.spacer:
        return Container(
          width: component.style.width ?? 50,
          height: component.style.height ?? 20,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey, style: BorderStyle.solid),
          ),
          child: const Center(
            child: Text(
              'SPACER',
              style: TextStyle(fontSize: 8, color: Colors.grey),
            ),
          ),
        );

      default:
        return Container(
          width: component.style.width ?? 100,
          height: component.style.height ?? 40,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Center(
            child: Text(
              component.type.name.toUpperCase(),
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ),
        );
    }
  }

  Widget _buildResizeHandle(UIComponent component, Alignment alignment) {
    return Positioned(
      left: alignment.x == -1
          ? -4
          : (alignment.x == 1 ? (component.style.width ?? 100) - 4 : null),
      top: alignment.y == -1
          ? -4
          : (alignment.y == 1 ? (component.style.height ?? 40) - 4 : null),
      right: alignment.x == 1 ? -4 : null,
      bottom: alignment.y == 1 ? -4 : null,
      child: GestureDetector(
        onPanUpdate: (details) {
          _resizeComponent(component, alignment, details.delta);
        },
        child: Container(
          width: 8,
          height: 8,
          decoration: const BoxDecoration(
            color: Colors.blue,
            shape: BoxShape.circle,
          ),
        ),
      ),
    );
  }

  void _addComponentFromPalette(ComponentType type, Offset position) {
    final newComponent = UIComponent(
      type: type,
      label: type.name.toUpperCase(),
      x: position.dx,
      y: position.dy,
    );

    final updatedComponents = [...widget.components, newComponent];
    widget.onComponentsChanged(updatedComponents);
    widget.onComponentSelected(newComponent.id);
  }

  void _updateComponent(UIComponent component) {
    final updatedComponents = widget.components.map((c) {
      return c.id == component.id ? component : c;
    }).toList();

    widget.onComponentsChanged(updatedComponents);
  }

  void _deleteComponent(String componentId) {
    final updatedComponents = widget.components
        .where((c) => c.id != componentId)
        .toList();
    widget.onComponentsChanged(updatedComponents);

    // Clear selection if deleted component was selected
    final selectedComponentId = ref.read(selectedComponentProvider);
    if (selectedComponentId == componentId) {
      ref.read(selectedComponentProvider.notifier).state = null;
    }
  }

  void _resizeComponent(
    UIComponent component,
    Alignment alignment,
    Offset delta,
  ) {
    double newWidth = component.style.width ?? 100;
    double newHeight = component.style.height ?? 40;
    double newX = component.x;
    double newY = component.y;

    if (alignment.x == 1) {
      // Right side
      newWidth += delta.dx;
    } else if (alignment.x == -1) {
      // Left side
      newWidth -= delta.dx;
      newX += delta.dx;
    }

    if (alignment.y == 1) {
      // Bottom side
      newHeight += delta.dy;
    } else if (alignment.y == -1) {
      // Top side
      newHeight -= delta.dy;
      newY += delta.dy;
    }

    // Minimum size constraints
    newWidth = newWidth.clamp(20.0, double.infinity);
    newHeight = newHeight.clamp(20.0, double.infinity);

    final updatedComponent = component.copyWith(
      x: newX,
      y: newY,
      style: component.style.copyWith(width: newWidth, height: newHeight),
    );

    _updateComponent(updatedComponent);
  }
}

class GridPainter extends CustomPainter {
  final double gridSize;

  GridPainter({required this.gridSize});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey[300]!
      ..strokeWidth = 0.5;

    // Draw vertical lines
    for (double x = 0; x < size.width; x += gridSize) {
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }

    // Draw horizontal lines
    for (double y = 0; y < size.height; y += gridSize) {
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
