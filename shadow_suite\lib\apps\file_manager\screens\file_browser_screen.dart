import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../file_manager_main.dart';
import '../services/file_manager_service.dart';
import '../models/file_manager_models.dart';

class FileBrowserScreen extends ConsumerStatefulWidget {
  const FileBrowserScreen({super.key});

  @override
  ConsumerState<FileBrowserScreen> createState() => _FileBrowserScreenState();
}

class _FileBrowserScreenState extends ConsumerState<FileBrowserScreen> {
  List<FileSystemItem> _currentItems = [];
  String _currentPath = '';
  bool _isLoading = false;
  String _searchQuery = '';
  bool _isGridView = false;
  bool _isSelectionMode = false;
  final Set<String> _selectedItems = <String>{};

  @override
  void initState() {
    super.initState();
    _initializeFileManager();
  }

  Future<void> _initializeFileManager() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Initialize the file manager service to load actual file system contents
      await FileManagerService.initialize();
      await _loadCurrentDirectory();
    } catch (error) {
      _showErrorSnackBar('Error initializing file manager: $error');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadCurrentDirectory() async {
    try {
      _currentItems = FileManagerService.getCurrentItems();
      _currentPath = FileManagerService.getCurrentDirectory();
      setState(() {});
    } catch (error) {
      _showErrorSnackBar('Error loading directory: $error');
    }
  }

  @override
  Widget build(BuildContext context) {
    final filteredItems = _searchQuery.isEmpty
        ? _currentItems
        : _currentItems
              .where(
                (item) => item.name.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ),
              )
              .toList();

    return Scaffold(
      body: Column(
        children: [
          FileManagerHeader(
            title: _isSelectionMode
                ? '${_selectedItems.length} selected'
                : 'File Browser',
            subtitle: _currentPath.isEmpty
                ? 'Browse your files and folders'
                : _currentPath,
            actions: _isSelectionMode
                ? _buildSelectionActions()
                : _buildNormalActions(),
          ),
          _buildNavigationBar(),
          _buildSearchBar(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildFileList(filteredItems),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Colors.grey, width: 0.5)),
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: FileManagerService.canNavigateBack()
                ? () async {
                    await FileManagerService.navigateBack();
                    _loadCurrentDirectory();
                  }
                : null,
            color: const Color(0xFFE67E22),
          ),
          IconButton(
            icon: const Icon(Icons.arrow_forward),
            onPressed: FileManagerService.canNavigateForward()
                ? () async {
                    await FileManagerService.navigateForward();
                    _loadCurrentDirectory();
                  }
                : null,
            color: const Color(0xFFE67E22),
          ),
          IconButton(
            icon: const Icon(Icons.arrow_upward),
            onPressed: () async {
              await FileManagerService.navigateToParent();
              _loadCurrentDirectory();
            },
            color: const Color(0xFFE67E22),
          ),
          IconButton(
            icon: const Icon(Icons.home),
            onPressed: () async {
              await FileManagerService.navigateToHome();
              _loadCurrentDirectory();
            },
            color: const Color(0xFFE67E22),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              _currentPath,
              style: const TextStyle(fontSize: 14, color: Color(0xFF7F8C8D)),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        decoration: const InputDecoration(
          hintText: 'Search files and folders...',
          prefixIcon: Icon(Icons.search),
          border: OutlineInputBorder(),
          contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildFileList(List<FileSystemItem> items) {
    if (items.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.folder_open, size: 64, color: Color(0xFF7F8C8D)),
            SizedBox(height: 16),
            Text(
              'No files or folders found',
              style: TextStyle(fontSize: 16, color: Color(0xFF7F8C8D)),
            ),
          ],
        ),
      );
    }

    if (_isGridView) {
      return GridView.builder(
        padding: const EdgeInsets.all(16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 0.8,
        ),
        itemCount: items.length,
        itemBuilder: (context, index) => _buildGridItem(items[index]),
      );
    } else {
      return ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 8),
        itemCount: items.length,
        itemBuilder: (context, index) => _buildListItem(items[index]),
      );
    }
  }

  Widget _buildListItem(FileSystemItem item) {
    final isSelected = _selectedItems.contains(item.path);

    return FileItemWidget(
      name: item.name,
      subtitle:
          '${_formatFileSize(item.size)} • ${_formatDate(item.lastModified)}',
      icon: item.isDirectory ? Icons.folder : _getFileIcon(item.name),
      onTap: () =>
          _isSelectionMode ? _toggleItemSelection(item) : _handleItemTap(item),
      onLongPress: () => _isSelectionMode ? null : _enterSelectionMode(),
      isSelected: isSelected,
      showCheckbox: _isSelectionMode,
      actions: _isSelectionMode
          ? []
          : [
              PopupMenuButton<String>(
                onSelected: (action) => _handleItemAction(action, item),
                itemBuilder: (context) => [
                  const PopupMenuItem(value: 'open', child: Text('Open')),
                  const PopupMenuItem(value: 'rename', child: Text('Rename')),
                  const PopupMenuItem(value: 'copy', child: Text('Copy')),
                  const PopupMenuItem(value: 'move', child: Text('Move')),
                  const PopupMenuItem(value: 'delete', child: Text('Delete')),
                  const PopupMenuItem(
                    value: 'properties',
                    child: Text('Properties'),
                  ),
                ],
              ),
            ],
    );
  }

  Widget _buildGridItem(FileSystemItem item) {
    return InkWell(
      onTap: () => _handleItemTap(item),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: const [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 2,
              offset: Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              item.isDirectory ? Icons.folder : _getFileIcon(item.name),
              size: 48,
              color: const Color(0xFFE67E22),
            ),
            const SizedBox(height: 8),
            Text(
              item.name,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Color(0xFF2C3E50),
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              _formatFileSize(item.size),
              style: const TextStyle(fontSize: 10, color: Color(0xFF7F8C8D)),
            ),
          ],
        ),
      ),
    );
  }

  void _handleItemTap(FileSystemItem item) async {
    if (item.isDirectory) {
      await FileManagerService.navigateToDirectory(item.path);
      _loadCurrentDirectory();
    } else {
      _openFile(item);
    }
  }

  void _handleItemAction(String action, FileSystemItem item) async {
    switch (action) {
      case 'open':
        _openFile(item);
        break;
      case 'rename':
        _showRenameDialog(item);
        break;
      case 'copy':
        _copyItem(item);
        break;
      case 'move':
        _moveItem(item);
        break;
      case 'delete':
        _deleteItem(item);
        break;
      case 'properties':
        _showPropertiesDialog(item);
        break;
    }
  }

  void _openFile(FileSystemItem item) {
    // Implement file opening logic
    _showInfoSnackBar('Opening ${item.name}');
  }

  List<Widget> _buildNormalActions() {
    return [
      IconButton(
        icon: Icon(_isGridView ? Icons.list : Icons.grid_view),
        onPressed: () => setState(() => _isGridView = !_isGridView),
        color: const Color(0xFFE67E22),
        tooltip: _isGridView ? 'List View' : 'Grid View',
      ),
      IconButton(
        icon: const Icon(Icons.refresh),
        onPressed: _loadCurrentDirectory,
        color: const Color(0xFFE67E22),
        tooltip: 'Refresh',
      ),
      IconButton(
        icon: const Icon(Icons.create_new_folder),
        onPressed: _showCreateFolderDialog,
        color: const Color(0xFFE67E22),
        tooltip: 'New Folder',
      ),
      IconButton(
        icon: const Icon(Icons.checklist),
        onPressed: _enterSelectionMode,
        color: const Color(0xFFE67E22),
        tooltip: 'Select Items',
      ),
    ];
  }

  List<Widget> _buildSelectionActions() {
    return [
      IconButton(
        icon: const Icon(Icons.select_all),
        onPressed: _selectAll,
        color: const Color(0xFFE67E22),
        tooltip: 'Select All',
      ),
      IconButton(
        icon: const Icon(Icons.copy),
        onPressed: _selectedItems.isNotEmpty ? _copySelectedItems : null,
        color: const Color(0xFFE67E22),
        tooltip: 'Copy',
      ),
      IconButton(
        icon: const Icon(Icons.cut),
        onPressed: _selectedItems.isNotEmpty ? _cutSelectedItems : null,
        color: const Color(0xFFE67E22),
        tooltip: 'Cut',
      ),
      IconButton(
        icon: const Icon(Icons.delete),
        onPressed: _selectedItems.isNotEmpty ? _deleteSelectedItems : null,
        color: const Color(0xFFE67E22),
        tooltip: 'Delete',
      ),
      IconButton(
        icon: const Icon(Icons.archive),
        onPressed: _selectedItems.isNotEmpty ? _createZipFromSelected : null,
        color: const Color(0xFFE67E22),
        tooltip: 'Create ZIP',
      ),
      IconButton(
        icon: const Icon(Icons.close),
        onPressed: _exitSelectionMode,
        color: const Color(0xFFE67E22),
        tooltip: 'Exit Selection',
      ),
    ];
  }

  // Selection mode methods
  void _enterSelectionMode() {
    setState(() {
      _isSelectionMode = true;
      _selectedItems.clear();
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedItems.clear();
    });
  }

  void _selectAll() {
    setState(() {
      _selectedItems.clear();
      for (final item in _currentItems) {
        _selectedItems.add(item.path);
      }
    });
  }

  void _toggleItemSelection(FileSystemItem item) {
    setState(() {
      if (_selectedItems.contains(item.path)) {
        _selectedItems.remove(item.path);
      } else {
        _selectedItems.add(item.path);
      }
    });
  }

  void _copySelectedItems() async {
    if (_selectedItems.isEmpty) return;

    try {
      for (final itemPath in _selectedItems) {
        final fileName = itemPath.split('/').last;
        final destinationPath = '$_currentPath/Copy of $fileName';
        await FileManagerService.copyItem(itemPath, destinationPath);
      }
      _loadCurrentDirectory();
      _showInfoSnackBar('${_selectedItems.length} items copied');
      _exitSelectionMode();
    } catch (error) {
      _showErrorSnackBar('Error copying items: $error');
    }
  }

  void _cutSelectedItems() async {
    if (_selectedItems.isEmpty) return;

    try {
      for (final itemPath in _selectedItems) {
        final fileName = itemPath.split('/').last;
        final destinationPath = '$_currentPath/Moved $fileName';
        await FileManagerService.moveItem(itemPath, destinationPath);
      }
      _loadCurrentDirectory();
      _showInfoSnackBar('${_selectedItems.length} items moved');
      _exitSelectionMode();
    } catch (error) {
      _showErrorSnackBar('Error moving items: $error');
    }
  }

  void _deleteSelectedItems() async {
    if (_selectedItems.isEmpty) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Items'),
        content: Text(
          'Are you sure you want to delete ${_selectedItems.length} items?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        for (final itemPath in _selectedItems) {
          await FileManagerService.deleteItem(itemPath);
        }
        _loadCurrentDirectory();
        _showInfoSnackBar('${_selectedItems.length} items deleted');
        _exitSelectionMode();
      } catch (error) {
        _showErrorSnackBar('Error deleting items: $error');
      }
    }
  }

  void _createZipFromSelected() async {
    if (_selectedItems.isEmpty) return;

    final zipName = 'Archive_${DateTime.now().millisecondsSinceEpoch}.zip';

    _showInfoSnackBar('Creating ZIP archive: $zipName');

    try {
      // Create a simple archive by copying files to a new directory
      // This is a basic implementation - in production, you'd use a proper ZIP library
      final archiveDir = Directory(
        '$_currentPath/Archive_${DateTime.now().millisecondsSinceEpoch}',
      );
      await archiveDir.create();

      int copiedFiles = 0;
      for (final itemPath in _selectedItems) {
        final item = File(itemPath);
        final fileName = itemPath.split('/').last;
        final destinationPath = '${archiveDir.path}/$fileName';

        if (await item.exists()) {
          await item.copy(destinationPath);
          copiedFiles++;
        } else {
          // Handle directories by copying recursively
          final dir = Directory(itemPath);
          if (await dir.exists()) {
            await _copyDirectoryRecursively(
              dir,
              Directory('${archiveDir.path}/$fileName'),
            );
            copiedFiles++;
          }
        }
      }

      _loadCurrentDirectory();
      _showInfoSnackBar(
        'Archive created with $copiedFiles items: ${archiveDir.path.split('/').last}',
      );
      _exitSelectionMode();
    } catch (error) {
      _showErrorSnackBar('Error creating archive: $error');
    }
  }

  Future<void> _copyDirectoryRecursively(
    Directory source,
    Directory destination,
  ) async {
    await destination.create(recursive: true);

    await for (final entity in source.list()) {
      final fileName = entity.path.split('/').last;
      final destinationPath = '${destination.path}/$fileName';

      if (entity is File) {
        await entity.copy(destinationPath);
      } else if (entity is Directory) {
        await _copyDirectoryRecursively(entity, Directory(destinationPath));
      }
    }
  }

  void _showCreateFolderDialog() {
    showDialog(
      context: context,
      builder: (context) {
        String folderName = '';
        return AlertDialog(
          title: const Text('Create New Folder'),
          content: TextField(
            decoration: const InputDecoration(
              hintText: 'Folder name',
              border: OutlineInputBorder(),
            ),
            onChanged: (value) => folderName = value,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (folderName.isNotEmpty) {
                  try {
                    final navigator = Navigator.of(context);
                    await FileManagerService.createDirectory(folderName);
                    _loadCurrentDirectory();
                    if (mounted) {
                      navigator.pop();
                      _showInfoSnackBar('Folder created successfully');
                    }
                  } catch (error) {
                    if (mounted) {
                      _showErrorSnackBar('Error creating folder: $error');
                    }
                  }
                }
              },
              child: const Text('Create'),
            ),
          ],
        );
      },
    );
  }

  void _showRenameDialog(FileSystemItem item) {
    showDialog(
      context: context,
      builder: (context) {
        String newName = item.name;
        return AlertDialog(
          title: const Text('Rename Item'),
          content: TextField(
            decoration: const InputDecoration(
              hintText: 'New name',
              border: OutlineInputBorder(),
            ),
            controller: TextEditingController(text: newName),
            onChanged: (value) => newName = value,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (newName.isNotEmpty && newName != item.name) {
                  try {
                    final navigator = Navigator.of(context);
                    await FileManagerService.renameItem(item.path, newName);
                    _loadCurrentDirectory();
                    if (mounted) {
                      navigator.pop();
                      _showInfoSnackBar('Item renamed successfully');
                    }
                  } catch (error) {
                    if (mounted) {
                      _showErrorSnackBar('Error renaming item: $error');
                    }
                  }
                }
              },
              child: const Text('Rename'),
            ),
          ],
        );
      },
    );
  }

  void _copyItem(FileSystemItem item) {
    // Implement copy functionality
    _showInfoSnackBar('Copy functionality not yet implemented');
  }

  void _moveItem(FileSystemItem item) {
    // Implement move functionality
    _showInfoSnackBar('Move functionality not yet implemented');
  }

  void _deleteItem(FileSystemItem item) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Item'),
        content: Text('Are you sure you want to delete "${item.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await FileManagerService.deleteItem(item.path);
        _loadCurrentDirectory();
        _showInfoSnackBar('Item deleted successfully');
      } catch (error) {
        _showErrorSnackBar('Error deleting item: $error');
      }
    }
  }

  void _showPropertiesDialog(FileSystemItem item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Properties: ${item.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Type: ${item.isDirectory ? 'Folder' : 'File'}'),
            Text('Size: ${_formatFileSize(item.size)}'),
            Text('Modified: ${_formatDate(item.lastModified)}'),
            Text('Path: ${item.path}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  IconData _getFileIcon(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return Icons.image;
      case 'mp4':
      case 'avi':
      case 'mov':
        return Icons.video_file;
      case 'mp3':
      case 'wav':
      case 'flac':
        return Icons.audio_file;
      case 'zip':
      case 'rar':
      case '7z':
        return Icons.archive;
      case 'txt':
        return Icons.text_snippet;
      default:
        return Icons.insert_drive_file;
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF27AE60),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}
