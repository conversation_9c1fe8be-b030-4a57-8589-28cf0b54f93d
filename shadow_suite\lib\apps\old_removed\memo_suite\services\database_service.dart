import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../../../core/database/database_initializer.dart';
import '../models/note.dart';
import '../models/todo.dart';
import '../models/voice_memo.dart';

class MemoSuiteDatabaseService {
  static Database? _database;
  static const String _databaseName = 'memo_suite.db';
  static const int _databaseVersion = 2;

  // Table names
  static const String _notesTable = 'notes';
  static const String _todosTable = 'todos';
  static const String _voiceMemosTable = 'voice_memos';

  static Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  static Future<Database> _initDatabase() async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, _databaseName);

    // Use safe database opening with proper initialization
    final db = await DatabaseInitializer.safeOpenDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );

    if (db == null) {
      throw Exception('Failed to initialize Memo Suite database');
    }

    return db;
  }

  static Future<void> _onCreate(Database db, int version) async {
    // Create notes table
    await db.execute('''
      CREATE TABLE $_notesTable (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        category TEXT NOT NULL,
        tags TEXT NOT NULL,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        isPinned INTEGER NOT NULL DEFAULT 0,
        scheduledDate INTEGER,
        color INTEGER
      )
    ''');

    // Create todos table
    await db.execute('''
      CREATE TABLE $_todosTable (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        priority TEXT NOT NULL,
        status TEXT NOT NULL,
        category TEXT NOT NULL,
        dueDate INTEGER,
        subTasks TEXT NOT NULL,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL
      )
    ''');

    // Create voice memos table
    await db.execute('''
      CREATE TABLE $_voiceMemosTable (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        filePath TEXT NOT NULL,
        duration INTEGER NOT NULL,
        category TEXT NOT NULL,
        tags TEXT NOT NULL,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        transcription TEXT,
        fileSize REAL NOT NULL DEFAULT 0.0
      )
    ''');
  }

  static Future<void> _onUpgrade(
    Database db,
    int oldVersion,
    int newVersion,
  ) async {
    if (oldVersion < 2) {
      // Add new columns to notes table
      await db.execute(
        'ALTER TABLE $_notesTable ADD COLUMN scheduledDate INTEGER',
      );
      await db.execute('ALTER TABLE $_notesTable ADD COLUMN color INTEGER');
    }
  }

  // Notes CRUD operations
  static Future<String> insertNote(Note note) async {
    final db = await database;
    await db.insert(_notesTable, note.toMap());
    return note.id;
  }

  static Future<List<Note>> getAllNotes() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _notesTable,
      orderBy: 'isPinned DESC, updatedAt DESC',
    );
    return List.generate(maps.length, (i) => Note.fromMap(maps[i]));
  }

  static Future<Note?> getNoteById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _notesTable,
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Note.fromMap(maps.first);
    }
    return null;
  }

  static Future<void> updateNote(Note note) async {
    final db = await database;
    await db.update(
      _notesTable,
      note.toMap(),
      where: 'id = ?',
      whereArgs: [note.id],
    );
  }

  static Future<void> deleteNote(String id) async {
    final db = await database;
    await db.delete(_notesTable, where: 'id = ?', whereArgs: [id]);
  }

  static Future<List<Note>> searchNotes(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _notesTable,
      where: 'title LIKE ? OR content LIKE ? OR tags LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'isPinned DESC, updatedAt DESC',
    );
    return List.generate(maps.length, (i) => Note.fromMap(maps[i]));
  }

  static Future<List<Note>> getNotesByCategory(String category) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _notesTable,
      where: 'category = ?',
      whereArgs: [category],
      orderBy: 'isPinned DESC, updatedAt DESC',
    );
    return List.generate(maps.length, (i) => Note.fromMap(maps[i]));
  }

  // Todos CRUD operations
  static Future<String> insertTodo(Todo todo) async {
    final db = await database;
    final todoMap = todo.toMap();
    todoMap['subTasks'] = jsonEncode(todoMap['subTasks']);
    await db.insert(_todosTable, todoMap);
    return todo.id;
  }

  static Future<List<Todo>> getAllTodos() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _todosTable,
      orderBy: 'updatedAt DESC',
    );
    return List.generate(maps.length, (i) {
      final map = Map<String, dynamic>.from(maps[i]);
      map['subTasks'] = jsonDecode(map['subTasks']);
      return Todo.fromMap(map);
    });
  }

  static Future<Todo?> getTodoById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _todosTable,
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      final map = Map<String, dynamic>.from(maps.first);
      map['subTasks'] = jsonDecode(map['subTasks']);
      return Todo.fromMap(map);
    }
    return null;
  }

  static Future<void> updateTodo(Todo todo) async {
    final db = await database;
    final todoMap = todo.toMap();
    todoMap['subTasks'] = jsonEncode(todoMap['subTasks']);
    await db.update(
      _todosTable,
      todoMap,
      where: 'id = ?',
      whereArgs: [todo.id],
    );
  }

  static Future<void> deleteTodo(String id) async {
    final db = await database;
    await db.delete(_todosTable, where: 'id = ?', whereArgs: [id]);
  }

  static Future<List<Todo>> getTodosByStatus(TodoStatus status) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _todosTable,
      where: 'status = ?',
      whereArgs: [status.name],
      orderBy: 'updatedAt DESC',
    );
    return List.generate(maps.length, (i) {
      final map = Map<String, dynamic>.from(maps[i]);
      map['subTasks'] = jsonDecode(map['subTasks']);
      return Todo.fromMap(map);
    });
  }

  // Voice Memos CRUD operations
  static Future<String> insertVoiceMemo(VoiceMemo voiceMemo) async {
    final db = await database;
    await db.insert(_voiceMemosTable, voiceMemo.toMap());
    return voiceMemo.id;
  }

  static Future<List<VoiceMemo>> getAllVoiceMemos() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _voiceMemosTable,
      orderBy: 'updatedAt DESC',
    );
    return List.generate(maps.length, (i) => VoiceMemo.fromMap(maps[i]));
  }

  static Future<VoiceMemo?> getVoiceMemoById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _voiceMemosTable,
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return VoiceMemo.fromMap(maps.first);
    }
    return null;
  }

  static Future<void> updateVoiceMemo(VoiceMemo voiceMemo) async {
    final db = await database;
    await db.update(
      _voiceMemosTable,
      voiceMemo.toMap(),
      where: 'id = ?',
      whereArgs: [voiceMemo.id],
    );
  }

  static Future<void> deleteVoiceMemo(String id) async {
    final db = await database;
    await db.delete(_voiceMemosTable, where: 'id = ?', whereArgs: [id]);
  }

  // Statistics
  static Future<Map<String, int>> getStatistics() async {
    final db = await database;

    final notesCount =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM $_notesTable'),
        ) ??
        0;

    final todosCount =
        Sqflite.firstIntValue(
          await db.rawQuery(
            'SELECT COUNT(*) FROM $_todosTable WHERE status != ?',
            [TodoStatus.done.name],
          ),
        ) ??
        0;

    final voiceMemosCount =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM $_voiceMemosTable'),
        ) ??
        0;

    return {
      'notes': notesCount,
      'todos': todosCount,
      'voiceMemos': voiceMemosCount,
    };
  }

  // Recent items
  static Future<List<Map<String, dynamic>>> getRecentItems({
    int limit = 10,
  }) async {
    final db = await database;

    final recentNotes = await db.query(
      _notesTable,
      orderBy: 'updatedAt DESC',
      limit: limit,
    );

    final recentTodos = await db.query(
      _todosTable,
      orderBy: 'updatedAt DESC',
      limit: limit,
    );

    final recentVoiceMemos = await db.query(
      _voiceMemosTable,
      orderBy: 'updatedAt DESC',
      limit: limit,
    );

    final allItems = <Map<String, dynamic>>[];

    for (final note in recentNotes) {
      allItems.add({
        'type': 'note',
        'id': note['id'],
        'title': note['title'],
        'updatedAt': note['updatedAt'],
      });
    }

    for (final todo in recentTodos) {
      allItems.add({
        'type': 'todo',
        'id': todo['id'],
        'title': todo['title'],
        'updatedAt': todo['updatedAt'],
      });
    }

    for (final voiceMemo in recentVoiceMemos) {
      allItems.add({
        'type': 'voiceMemo',
        'id': voiceMemo['id'],
        'title': voiceMemo['title'],
        'updatedAt': voiceMemo['updatedAt'],
      });
    }

    allItems.sort(
      (a, b) => (b['updatedAt'] as int).compareTo(a['updatedAt'] as int),
    );

    return allItems.take(limit).toList();
  }
}
