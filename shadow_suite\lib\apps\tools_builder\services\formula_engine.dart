import 'dart:math' as math;
import '../models/spreadsheet.dart';
import '../models/spreadsheet_cell.dart';

class FormulaError {
  final String type;
  final String message;

  const FormulaError(this.type, this.message);

  @override
  String toString() => '#$type: $message';
}

class FormulaEngine {
  final Map<String, Function> _functions = {};

  FormulaEngine() {
    _initializeFunctions();
  }

  void _initializeFunctions() {
    // Mathematical Functions
    _functions['SUM'] = _sum;
    _functions['AVERAGE'] = _average;
    _functions['COUNT'] = _count;
    _functions['COUNTA'] = _countA;
    _functions['COUNTBLANK'] = _countBlank;
    _functions['MIN'] = _min;
    _functions['MAX'] = _max;
    _functions['ABS'] = _abs;
    _functions['SQRT'] = _sqrt;
    _functions['POWER'] = _power;
    _functions['ROUND'] = _round;
    _functions['ROUNDUP'] = _roundUp;
    _functions['ROUNDDOWN'] = _roundDown;
    _functions['CEILING'] = _ceiling;
    _functions['FLOOR'] = _floor;
    _functions['INT'] = _int;
    _functions['MOD'] = _mod;
    _functions['PI'] = _pi;
    _functions['RAND'] = _rand;
    _functions['RANDBETWEEN'] = _randBetween;

    // Trigonometric Functions
    _functions['SIN'] = _sin;
    _functions['COS'] = _cos;
    _functions['TAN'] = _tan;
    _functions['ASIN'] = _asin;
    _functions['ACOS'] = _acos;
    _functions['ATAN'] = _atan;
    _functions['RADIANS'] = _radians;
    _functions['DEGREES'] = _degrees;

    // Logical Functions
    _functions['IF'] = _if;
    _functions['AND'] = _and;
    _functions['OR'] = _or;
    _functions['NOT'] = _not;
    _functions['TRUE'] = _true;
    _functions['FALSE'] = _false;

    // Text Functions
    _functions['CONCATENATE'] = _concatenate;
    _functions['LEFT'] = _left;
    _functions['RIGHT'] = _right;
    _functions['MID'] = _mid;
    _functions['LEN'] = _len;
    _functions['UPPER'] = _upper;
    _functions['LOWER'] = _lower;
    _functions['TRIM'] = _trim;
    _functions['SUBSTITUTE'] = _substitute;
    _functions['FIND'] = _find;
    _functions['SEARCH'] = _search;

    // Date Functions
    _functions['TODAY'] = _today;
    _functions['NOW'] = _now;
    _functions['DATE'] = _date;
    _functions['YEAR'] = _year;
    _functions['MONTH'] = _month;
    _functions['DAY'] = _day;
    _functions['WEEKDAY'] = _weekday;
    _functions['DATEDIF'] = _dateDif;

    // Lookup Functions
    _functions['VLOOKUP'] = _vlookup;
    _functions['HLOOKUP'] = _hlookup;
    _functions['INDEX'] = _index;
    _functions['MATCH'] = _match;

    // Financial Functions
    _functions['PMT'] = _pmt;
    _functions['PV'] = _pv;
    _functions['FV'] = _fv;
    _functions['RATE'] = _rate;
    _functions['NPER'] = _nper;

    // Information Functions
    _functions['ISBLANK'] = _isBlank;
    _functions['ISNUMBER'] = _isNumber;
    _functions['ISTEXT'] = _isText;
    _functions['ISERROR'] = _isError;
    _functions['TYPE'] = _type;
  }

  dynamic evaluateFormula(
    String formula,
    Spreadsheet spreadsheet, [
    String? currentCellAddress,
  ]) {
    try {
      if (!formula.startsWith('=')) {
        return formula;
      }

      final expression = formula.substring(1);
      return _parseExpression(expression, spreadsheet, currentCellAddress);
    } catch (e) {
      return FormulaError('ERROR', e.toString());
    }
  }

  dynamic _parseExpression(
    String expression,
    Spreadsheet spreadsheet, [
    String? currentCellAddress,
  ]) {
    // Remove whitespace
    expression = expression.trim();

    // Handle parentheses
    if (expression.startsWith('(') && expression.endsWith(')')) {
      return _parseExpression(
        expression.substring(1, expression.length - 1),
        spreadsheet,
        currentCellAddress,
      );
    }

    // Handle function calls
    final functionMatch = RegExp(r'^([A-Z]+)\((.*)\)$').firstMatch(expression);
    if (functionMatch != null) {
      final functionName = functionMatch.group(1)!;
      final argsString = functionMatch.group(2)!;
      return _callFunction(
        functionName,
        argsString,
        spreadsheet,
        currentCellAddress,
      );
    }

    // Handle cell references
    final cellMatch = RegExp(r'^[A-Z]+\d+$').firstMatch(expression);
    if (cellMatch != null) {
      return _getCellValue(expression, spreadsheet);
    }

    // Handle range references
    final rangeMatch = RegExp(r'^[A-Z]+\d+:[A-Z]+\d+$').firstMatch(expression);
    if (rangeMatch != null) {
      return _getRangeValues(expression, spreadsheet);
    }

    // Handle arithmetic operations
    if (expression.contains('+') ||
        expression.contains('-') ||
        expression.contains('*') ||
        expression.contains('/')) {
      return _evaluateArithmetic(expression, spreadsheet, currentCellAddress);
    }

    // Handle comparison operations
    if (expression.contains('=') ||
        expression.contains('>') ||
        expression.contains('<') ||
        expression.contains('>=') ||
        expression.contains('<=') ||
        expression.contains('<>')) {
      return _evaluateComparison(expression, spreadsheet, currentCellAddress);
    }

    // Handle string literals
    if (expression.startsWith('"') && expression.endsWith('"')) {
      return expression.substring(1, expression.length - 1);
    }

    // Handle numbers
    final number = double.tryParse(expression);
    if (number != null) {
      return number;
    }

    // Handle boolean literals
    if (expression.toUpperCase() == 'TRUE') return true;
    if (expression.toUpperCase() == 'FALSE') return false;

    return FormulaError('VALUE', 'Invalid expression: $expression');
  }

  dynamic _callFunction(
    String functionName,
    String argsString,
    Spreadsheet spreadsheet, [
    String? currentCellAddress,
  ]) {
    final function = _functions[functionName];
    if (function == null) {
      return FormulaError('NAME', 'Unknown function: $functionName');
    }

    final args = _parseArguments(argsString, spreadsheet, currentCellAddress);

    // Handle functions that expect a list vs individual arguments
    if ([
      'SUM',
      'AVERAGE',
      'COUNT',
      'COUNTA',
      'COUNTBLANK',
      'MIN',
      'MAX',
      'AND',
      'OR',
      'CONCATENATE',
    ].contains(functionName)) {
      return Function.apply(function, [args]);
    } else {
      return Function.apply(function, args);
    }
  }

  List<dynamic> _parseArguments(
    String argsString,
    Spreadsheet spreadsheet, [
    String? currentCellAddress,
  ]) {
    if (argsString.trim().isEmpty) return [];

    final args = <dynamic>[];
    final parts = _splitArguments(argsString);

    for (final part in parts) {
      args.add(_parseExpression(part.trim(), spreadsheet, currentCellAddress));
    }

    return args;
  }

  List<String> _splitArguments(String argsString) {
    final args = <String>[];
    int level = 0;
    int start = 0;

    for (int i = 0; i < argsString.length; i++) {
      final char = argsString[i];
      if (char == '(') {
        level++;
      } else if (char == ')') {
        level--;
      } else if (char == ',' && level == 0) {
        args.add(argsString.substring(start, i));
        start = i + 1;
      }
    }

    if (start < argsString.length) {
      args.add(argsString.substring(start));
    }

    return args;
  }

  dynamic _getCellValue(String cellAddress, Spreadsheet spreadsheet) {
    final sheet = spreadsheet.activeSheet;
    if (sheet == null) return 0;

    final cell = sheet.cells[cellAddress];
    if (cell == null) return 0;

    return cell.calculatedValue ?? cell.rawValue;
  }

  List<dynamic> _getRangeValues(String range, Spreadsheet spreadsheet) {
    final parts = range.split(':');
    if (parts.length != 2) return [];

    final startCell = parts[0];
    final endCell = parts[1];

    final startCol = SpreadsheetCell.letterToColumn(
      RegExp(r'[A-Z]+').firstMatch(startCell)!.group(0)!,
    );
    final startRow = int.parse(RegExp(r'\d+').firstMatch(startCell)!.group(0)!);
    final endCol = SpreadsheetCell.letterToColumn(
      RegExp(r'[A-Z]+').firstMatch(endCell)!.group(0)!,
    );
    final endRow = int.parse(RegExp(r'\d+').firstMatch(endCell)!.group(0)!);

    final values = <dynamic>[];
    for (int row = startRow; row <= endRow; row++) {
      for (int col = startCol; col <= endCol; col++) {
        final cellAddress = '${SpreadsheetCell.columnToLetter(col)}$row';
        values.add(_getCellValue(cellAddress, spreadsheet));
      }
    }

    return values;
  }

  dynamic _evaluateArithmetic(
    String expression,
    Spreadsheet spreadsheet, [
    String? currentCellAddress,
  ]) {
    // Simple arithmetic evaluation - would need more sophisticated parsing for complex expressions
    // This is a simplified version

    // Handle multiplication and division first
    if (expression.contains('*') || expression.contains('/')) {
      final operators = ['*', '/'];
      for (final op in operators) {
        final parts = expression.split(op);
        if (parts.length == 2) {
          final left = _parseExpression(
            parts[0].trim(),
            spreadsheet,
            currentCellAddress,
          );
          final right = _parseExpression(
            parts[1].trim(),
            spreadsheet,
            currentCellAddress,
          );

          if (left is num && right is num) {
            switch (op) {
              case '*':
                return left * right;
              case '/':
                return right != 0
                    ? left / right
                    : FormulaError('DIV/0', 'Division by zero');
            }
          }
        }
      }
    }

    // Handle addition and subtraction
    if (expression.contains('+') || expression.contains('-')) {
      final operators = ['+', '-'];
      for (final op in operators) {
        final parts = expression.split(op);
        if (parts.length == 2) {
          final left = _parseExpression(
            parts[0].trim(),
            spreadsheet,
            currentCellAddress,
          );
          final right = _parseExpression(
            parts[1].trim(),
            spreadsheet,
            currentCellAddress,
          );

          if (left is num && right is num) {
            switch (op) {
              case '+':
                return left + right;
              case '-':
                return left - right;
            }
          }
        }
      }
    }

    return FormulaError('VALUE', 'Invalid arithmetic expression');
  }

  dynamic _evaluateComparison(
    String expression,
    Spreadsheet spreadsheet, [
    String? currentCellAddress,
  ]) {
    final operators = ['>=', '<=', '<>', '=', '>', '<'];

    for (final op in operators) {
      if (expression.contains(op)) {
        final parts = expression.split(op);
        if (parts.length == 2) {
          final left = _parseExpression(
            parts[0].trim(),
            spreadsheet,
            currentCellAddress,
          );
          final right = _parseExpression(
            parts[1].trim(),
            spreadsheet,
            currentCellAddress,
          );

          switch (op) {
            case '=':
              return left == right;
            case '<>':
              return left != right;
            case '>':
              return (left is num && right is num) ? left > right : false;
            case '<':
              return (left is num && right is num) ? left < right : false;
            case '>=':
              return (left is num && right is num) ? left >= right : false;
            case '<=':
              return (left is num && right is num) ? left <= right : false;
          }
        }
      }
    }

    return FormulaError('VALUE', 'Invalid comparison expression');
  }

  // Mathematical Functions Implementation
  dynamic _sum(List<dynamic> args) {
    double result = 0;
    for (final arg in args) {
      if (arg is List) {
        result += _sum(arg);
      } else if (arg is num) {
        result += arg;
      }
    }
    return result;
  }

  dynamic _average(List<dynamic> args) {
    final values = <num>[];
    _collectNumbers(args, values);
    if (values.isEmpty) return 0;
    return values.reduce((a, b) => a + b) / values.length;
  }

  dynamic _count(List<dynamic> args) {
    final values = <num>[];
    _collectNumbers(args, values);
    return values.length;
  }

  dynamic _countA(List<dynamic> args) {
    int count = 0;
    for (final arg in args) {
      if (arg is List) {
        count += _countA(arg) as int;
      } else if (arg != null && arg.toString().isNotEmpty) {
        count++;
      }
    }
    return count;
  }

  dynamic _countBlank(List<dynamic> args) {
    int count = 0;
    for (final arg in args) {
      if (arg is List) {
        count += _countBlank(arg) as int;
      } else if (arg == null || arg.toString().isEmpty) {
        count++;
      }
    }
    return count;
  }

  dynamic _min(List<dynamic> args) {
    final values = <num>[];
    _collectNumbers(args, values);
    if (values.isEmpty) return 0;
    return values.reduce(math.min);
  }

  dynamic _max(List<dynamic> args) {
    final values = <num>[];
    _collectNumbers(args, values);
    if (values.isEmpty) return 0;
    return values.reduce(math.max);
  }

  void _collectNumbers(List<dynamic> args, List<num> values) {
    for (final arg in args) {
      if (arg is List) {
        _collectNumbers(arg, values);
      } else if (arg is num) {
        values.add(arg);
      }
    }
  }

  // More function implementations would continue here...
  // Due to space constraints, I'm showing the pattern for the key functions

  dynamic _abs(num value) => value.abs();
  dynamic _sqrt(num value) => math.sqrt(value);
  dynamic _power(num base, num exponent) => math.pow(base, exponent);
  dynamic _round(num value, [int digits = 0]) {
    final factor = math.pow(10, digits);
    return (value * factor).round() / factor;
  }

  dynamic _roundUp(num value, [int digits = 0]) {
    final factor = math.pow(10, digits);
    return (value * factor).ceil() / factor;
  }

  dynamic _roundDown(num value, [int digits = 0]) {
    final factor = math.pow(10, digits);
    return (value * factor).floor() / factor;
  }

  dynamic _ceiling(num value, num significance) =>
      (value / significance).ceil() * significance;
  dynamic _floor(num value, num significance) =>
      (value / significance).floor() * significance;
  dynamic _int(num value) => value.floor();
  dynamic _mod(num dividend, num divisor) => dividend % divisor;
  dynamic _pi() => math.pi;
  dynamic _rand() => math.Random().nextDouble();
  dynamic _randBetween(int min, int max) =>
      math.Random().nextInt(max - min + 1) + min;

  // Trigonometric functions
  dynamic _sin(num angle) => math.sin(angle);
  dynamic _cos(num angle) => math.cos(angle);
  dynamic _tan(num angle) => math.tan(angle);
  dynamic _asin(num value) => math.asin(value);
  dynamic _acos(num value) => math.acos(value);
  dynamic _atan(num value) => math.atan(value);
  dynamic _radians(num degrees) => degrees * math.pi / 180;
  dynamic _degrees(num radians) => radians * 180 / math.pi;

  // Logical functions
  dynamic _if(bool condition, dynamic valueIfTrue, dynamic valueIfFalse) {
    return condition ? valueIfTrue : valueIfFalse;
  }

  dynamic _and(List<dynamic> args) => args.every((arg) => arg == true);
  dynamic _or(List<dynamic> args) => args.any((arg) => arg == true);
  dynamic _not(bool value) => !value;
  dynamic _true() => true;
  dynamic _false() => false;

  // Text functions
  dynamic _concatenate(List<dynamic> args) => args.join('');
  dynamic _left(String text, dynamic numChars) =>
      text.substring(0, math.min((numChars as num).toInt(), text.length));
  dynamic _right(String text, dynamic numChars) =>
      text.substring(math.max(0, text.length - (numChars as num).toInt()));
  dynamic _mid(String text, dynamic start, dynamic numChars) => text.substring(
    (start as num).toInt() - 1,
    math.min((start).toInt() - 1 + (numChars as num).toInt(), text.length),
  );
  dynamic _len(String text) => text.length;
  dynamic _upper(String text) => text.toUpperCase();
  dynamic _lower(String text) => text.toLowerCase();
  dynamic _trim(String text) => text.trim();
  dynamic _substitute(
    String text,
    String oldText,
    String newText, [
    int? instanceNum,
  ]) {
    if (instanceNum != null) {
      // Replace specific instance
      int count = 0;
      int index = 0;
      while ((index = text.indexOf(oldText, index)) != -1) {
        count++;
        if (count == instanceNum) {
          return text.replaceRange(index, index + oldText.length, newText);
        }
        index += oldText.length;
      }
      return text;
    } else {
      return text.replaceAll(oldText, newText);
    }
  }

  dynamic _find(String findText, String withinText, [int startNum = 1]) {
    final index = withinText.indexOf(findText, startNum - 1);
    return index == -1 ? FormulaError('VALUE', 'Text not found') : index + 1;
  }

  dynamic _search(String findText, String withinText, [int startNum = 1]) {
    final index = withinText.toLowerCase().indexOf(
      findText.toLowerCase(),
      startNum - 1,
    );
    return index == -1 ? FormulaError('VALUE', 'Text not found') : index + 1;
  }

  // Date functions
  dynamic _today() => DateTime.now();
  dynamic _now() => DateTime.now();
  dynamic _date(int year, int month, int day) => DateTime(year, month, day);
  dynamic _year(DateTime date) => date.year;
  dynamic _month(DateTime date) => date.month;
  dynamic _day(DateTime date) => date.day;
  dynamic _weekday(DateTime date, [int type = 1]) {
    // Type 1: Sunday = 1, Monday = 2, etc.
    // Type 2: Monday = 1, Tuesday = 2, etc.
    final weekday = date.weekday;
    return type == 1 ? (weekday == 7 ? 1 : weekday + 1) : weekday;
  }

  dynamic _dateDif(DateTime startDate, DateTime endDate, String unit) {
    final difference = endDate.difference(startDate);
    switch (unit.toUpperCase()) {
      case 'D':
        return difference.inDays;
      case 'M':
        return (endDate.year - startDate.year) * 12 +
            (endDate.month - startDate.month);
      case 'Y':
        return endDate.year - startDate.year;
      default:
        return FormulaError('VALUE', 'Invalid unit');
    }
  }

  // Financial functions (simplified implementations)
  dynamic _pmt(
    double rate,
    double nper,
    double pv, [
    double fv = 0,
    double type = 0,
  ]) {
    if (rate == 0) return -(pv + fv) / nper;
    final pvif = math.pow(1 + rate, nper);
    return -(rate * (pv * pvif + fv)) / ((pvif - 1) * (1 + rate * type));
  }

  dynamic _pv(
    double rate,
    double nper,
    double pmt, [
    double fv = 0,
    double type = 0,
  ]) {
    if (rate == 0) return -(pmt * nper + fv);
    final pvif = math.pow(1 + rate, nper);
    return -(pmt * (1 + rate * type) * (pvif - 1) / rate + fv) / pvif;
  }

  dynamic _fv(
    double rate,
    double nper,
    double pmt, [
    double pv = 0,
    double type = 0,
  ]) {
    if (rate == 0) return -(pv + pmt * nper);
    final fvif = math.pow(1 + rate, nper);
    return -(pv * fvif + pmt * (1 + rate * type) * (fvif - 1) / rate);
  }

  dynamic _rate(
    double nper,
    double pmt,
    double pv, [
    double fv = 0,
    double type = 0,
    double guess = 0.1,
  ]) {
    // Simplified implementation - would need iterative solving in practice
    return guess; // Placeholder
  }

  dynamic _nper(
    double rate,
    double pmt,
    double pv, [
    double fv = 0,
    double type = 0,
  ]) {
    if (rate == 0) return -(pv + fv) / pmt;
    return math.log(
          (pmt * (1 + rate * type) - fv * rate) /
              (pv * rate + pmt * (1 + rate * type)),
        ) /
        math.log(1 + rate);
  }

  // Lookup functions (simplified)
  dynamic _vlookup(
    dynamic lookupValue,
    List<List<dynamic>> tableArray,
    int colIndex, [
    bool rangeLookup = true,
  ]) {
    // Simplified implementation
    for (final row in tableArray) {
      if (row.isNotEmpty && row[0] == lookupValue) {
        return colIndex <= row.length
            ? row[colIndex - 1]
            : FormulaError('REF', 'Column index out of range');
      }
    }
    return FormulaError('N/A', 'Value not found');
  }

  dynamic _hlookup(
    dynamic lookupValue,
    List<List<dynamic>> tableArray,
    int rowIndex, [
    bool rangeLookup = true,
  ]) {
    // Simplified implementation
    if (tableArray.isNotEmpty && rowIndex <= tableArray.length) {
      final headerRow = tableArray[0];
      for (int i = 0; i < headerRow.length; i++) {
        if (headerRow[i] == lookupValue) {
          return tableArray[rowIndex - 1][i];
        }
      }
    }
    return FormulaError('N/A', 'Value not found');
  }

  dynamic _index(List<dynamic> array, int rowNum, [int? colNum]) {
    if (array.isEmpty) return FormulaError('REF', 'Empty array');
    if (array[0] is List) {
      // 2D array
      if (rowNum <= array.length &&
          (colNum == null || colNum <= array[0].length)) {
        return colNum != null
            ? array[rowNum - 1][colNum - 1]
            : array[rowNum - 1];
      }
    } else {
      // 1D array
      if (rowNum <= array.length) {
        return array[rowNum - 1];
      }
    }
    return FormulaError('REF', 'Index out of range');
  }

  dynamic _match(
    dynamic lookupValue,
    List<dynamic> lookupArray, [
    int matchType = 1,
  ]) {
    for (int i = 0; i < lookupArray.length; i++) {
      if (lookupArray[i] == lookupValue) {
        return i + 1;
      }
    }
    return FormulaError('N/A', 'Value not found');
  }

  // Information functions
  dynamic _isBlank(dynamic value) => value == null || value.toString().isEmpty;
  dynamic _isNumber(dynamic value) => value is num;
  dynamic _isText(dynamic value) => value is String;
  dynamic _isError(dynamic value) => value is FormulaError;
  dynamic _type(dynamic value) {
    if (value is num) return 1;
    if (value is String) return 2;
    if (value is bool) return 4;
    if (value is FormulaError) return 16;
    if (value is List) return 64;
    return 1;
  }
}
