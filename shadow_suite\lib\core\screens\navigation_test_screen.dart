import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/navigation_verification_service.dart';
import '../theme/app_theme.dart';

/// Screen for testing and verifying navigation routes
class NavigationTestScreen extends ConsumerStatefulWidget {
  const NavigationTestScreen({super.key});

  @override
  ConsumerState<NavigationTestScreen> createState() => _NavigationTestScreenState();
}

class _NavigationTestScreenState extends ConsumerState<NavigationTestScreen> {
  NavigationVerificationResult? _verificationResult;
  bool _isVerifying = false;
  String _reportText = '';

  @override
  void initState() {
    super.initState();
    _runVerification();
  }

  Future<void> _runVerification() async {
    setState(() {
      _isVerifying = true;
    });

    try {
      final result = await NavigationVerificationService.verifyAllRoutes(context);
      final report = NavigationVerificationService.generateNavigationReport(result);
      
      setState(() {
        _verificationResult = result;
        _reportText = report;
        _isVerifying = false;
      });
    } catch (e) {
      setState(() {
        _isVerifying = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Verification failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Navigation System Test'),
        backgroundColor: AppTheme.primaryColor,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isVerifying ? null : _runVerification,
            tooltip: 'Re-run verification',
          ),
        ],
      ),
      body: _isVerifying
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Verifying navigation routes...'),
                ],
              ),
            )
          : _verificationResult != null
              ? _buildVerificationResults()
              : const Center(child: Text('No verification results')),
    );
  }

  Widget _buildVerificationResults() {
    final result = _verificationResult!;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSummaryCard(result),
          const SizedBox(height: 16),
          _buildHealthIndicator(result),
          const SizedBox(height: 16),
          _buildRoutesList(result),
          const SizedBox(height: 16),
          _buildReportCard(),
          const SizedBox(height: 16),
          _buildFixSuggestions(result),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(NavigationVerificationResult result) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.assessment,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Navigation Summary',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'Total Routes',
                    result.totalRoutes.toString(),
                    Icons.route,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Working',
                    result.workingRoutes.toString(),
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Broken',
                    result.brokenRoutes.toString(),
                    Icons.error,
                    Colors.red,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Success Rate',
                    '${result.successRate.toStringAsFixed(1)}%',
                    Icons.trending_up,
                    result.healthColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildHealthIndicator(NavigationVerificationResult result) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Navigation Health',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: result.healthColor,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    result.healthDescription,
                    style: TextStyle(
                      color: result.healthColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: result.successRate / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(result.healthColor),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoutesList(NavigationVerificationResult result) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Route Status',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...result.routeStatus.entries.map((entry) {
              final route = entry.key;
              final isWorking = entry.value;
              
              return ListTile(
                leading: Icon(
                  isWorking ? Icons.check_circle : Icons.error,
                  color: isWorking ? Colors.green : Colors.red,
                ),
                title: Text(route),
                subtitle: Text(isWorking ? 'Working' : 'Broken'),
                dense: true,
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildReportCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Detailed Report',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    // Copy report to clipboard
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Report copied to clipboard')),
                    );
                  },
                  icon: const Icon(Icons.copy),
                  label: const Text('Copy'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Text(
                _reportText,
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFixSuggestions(NavigationVerificationResult result) {
    final suggestions = NavigationVerificationService.getNavigationFixSuggestions(result);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Fix Suggestions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...suggestions.map((suggestion) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      result.hasAllRoutesWorking ? Icons.check : Icons.lightbulb,
                      color: result.hasAllRoutesWorking ? Colors.green : Colors.orange,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(suggestion),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}
