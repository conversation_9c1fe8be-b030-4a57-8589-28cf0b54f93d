import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../../../core/database/database_initializer.dart';
import '../models/surah.dart';
import '../models/athkar.dart';
import '../models/tafseer.dart';
import '../models/verse.dart' as verse_model;
import '../models/bookmark.dart' as bookmark_model;

class IslamicDatabaseService {
  static Database? _database;
  static const String _databaseName = 'islamic_app.db';
  static const int _databaseVersion = 1;

  // Table names
  static const String _surahsTable = 'surahs';
  static const String _versesTable = 'verses';
  static const String _bookmarksTable = 'bookmarks';
  static const String _dhikrTable = 'dhikr';
  static const String _dhikrSessionsTable = 'dhikr_sessions';
  static const String _customAthkarTable = 'custom_athkar';
  static const String _tafseerTable = 'tafseer';

  static Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  static Future<Database> _initDatabase() async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, _databaseName);

    // Use safe database opening with proper initialization
    final db = await DatabaseInitializer.safeOpenDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onOpen: _onOpen,
    );

    if (db == null) {
      throw Exception('Failed to initialize Islamic database');
    }

    return db;
  }

  static Future<void> _onCreate(Database db, int version) async {
    // Create surahs table
    await db.execute('''
      CREATE TABLE $_surahsTable (
        number INTEGER PRIMARY KEY,
        nameArabic TEXT NOT NULL,
        nameEnglish TEXT NOT NULL,
        nameTransliteration TEXT NOT NULL,
        verseCount INTEGER NOT NULL,
        revelationType TEXT NOT NULL
      )
    ''');

    // Create verses table
    await db.execute('''
      CREATE TABLE $_versesTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        surahNumber INTEGER NOT NULL,
        verseNumber INTEGER NOT NULL,
        textArabic TEXT NOT NULL,
        textEnglish TEXT NOT NULL,
        textTransliteration TEXT NOT NULL,
        juzNumber INTEGER DEFAULT 1,
        hizbNumber INTEGER DEFAULT 1,
        rukuNumber INTEGER DEFAULT 1,
        FOREIGN KEY (surahNumber) REFERENCES $_surahsTable (number),
        UNIQUE(surahNumber, verseNumber)
      )
    ''');

    // Create bookmarks table
    await db.execute('''
      CREATE TABLE $_bookmarksTable (
        id TEXT PRIMARY KEY,
        surahNumber INTEGER NOT NULL,
        verseNumber INTEGER NOT NULL,
        title TEXT NOT NULL,
        notes TEXT NOT NULL,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        tags TEXT NOT NULL,
        FOREIGN KEY (surahNumber) REFERENCES $_surahsTable (number)
      )
    ''');

    // Create dhikr table
    await db.execute('''
      CREATE TABLE $_dhikrTable (
        id TEXT PRIMARY KEY,
        textArabic TEXT NOT NULL,
        textEnglish TEXT NOT NULL,
        textTransliteration TEXT NOT NULL,
        meaning TEXT NOT NULL,
        recommendedCount INTEGER NOT NULL,
        source TEXT NOT NULL,
        category TEXT NOT NULL,
        benefits TEXT NOT NULL
      )
    ''');

    // Create dhikr sessions table
    await db.execute('''
      CREATE TABLE $_dhikrSessionsTable (
        id TEXT PRIMARY KEY,
        dhikrId TEXT NOT NULL,
        targetCount INTEGER NOT NULL,
        currentCount INTEGER NOT NULL,
        startTime INTEGER NOT NULL,
        endTime INTEGER,
        isCompleted INTEGER NOT NULL DEFAULT 0,
        category TEXT NOT NULL,
        FOREIGN KEY (dhikrId) REFERENCES $_dhikrTable (id)
      )
    ''');

    // Create custom athkar table
    await db.execute('''
      CREATE TABLE $_customAthkarTable (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        dhikrItems TEXT NOT NULL,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        color TEXT NOT NULL,
        isActive INTEGER NOT NULL DEFAULT 1
      )
    ''');

    // Insert initial data
    await _insertInitialData(db);
  }

  static Future<void> _onOpen(Database db) async {
    // Check if we need to populate initial data
    final surahCount =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM $_surahsTable'),
        ) ??
        0;

    if (surahCount == 0) {
      await _insertInitialData(db);
    }
  }

  static Future<void> _insertInitialData(Database db) async {
    // Insert sample surahs
    for (final surahData in QuranData.surahs) {
      await db.insert(_surahsTable, surahData);
    }

    // Insert sample verses for Al-Fatihah
    if (QuranData.verses.containsKey(1)) {
      for (final verseData in QuranData.verses[1]!) {
        await db.insert(_versesTable, {
          'surahNumber': 1,
          'verseNumber': verseData['verseNumber'],
          'textArabic': verseData['textArabic'],
          'textEnglish': verseData['textEnglish'],
          'textTransliteration': verseData['textTransliteration'],
          'juzNumber': 1,
          'hizbNumber': 1,
          'rukuNumber': 1,
        });
      }
    }

    // Insert morning athkar
    for (final athkarData in AthkarData.morningAthkar) {
      final dhikr = Dhikr(
        textArabic: athkarData['textArabic'],
        textEnglish: athkarData['textEnglish'],
        textTransliteration: athkarData['textTransliteration'],
        meaning: athkarData['meaning'],
        recommendedCount: athkarData['recommendedCount'],
        source: athkarData['source'],
        category: AthkarCategory.morning,
        benefits: List<String>.from(athkarData['benefits']),
      );
      await db.insert(_dhikrTable, dhikr.toMap());
    }

    // Insert evening athkar
    for (final athkarData in AthkarData.eveningAthkar) {
      final dhikr = Dhikr(
        textArabic: athkarData['textArabic'],
        textEnglish: athkarData['textEnglish'],
        textTransliteration: athkarData['textTransliteration'],
        meaning: athkarData['meaning'],
        recommendedCount: athkarData['recommendedCount'],
        source: athkarData['source'],
        category: AthkarCategory.evening,
        benefits: List<String>.from(athkarData['benefits']),
      );
      await db.insert(_dhikrTable, dhikr.toMap());
    }

    // Insert after prayer athkar
    for (final athkarData in AthkarData.afterPrayerAthkar) {
      final dhikr = Dhikr(
        textArabic: athkarData['textArabic'],
        textEnglish: athkarData['textEnglish'],
        textTransliteration: athkarData['textTransliteration'],
        meaning: athkarData['meaning'],
        recommendedCount: athkarData['recommendedCount'],
        source: athkarData['source'],
        category: AthkarCategory.afterPrayer,
        benefits: List<String>.from(athkarData['benefits']),
      );
      await db.insert(_dhikrTable, dhikr.toMap());
    }

    // Insert sleeping athkar
    for (final athkarData in AthkarData.sleepingAthkar) {
      final dhikr = Dhikr(
        textArabic: athkarData['textArabic'],
        textEnglish: athkarData['textEnglish'],
        textTransliteration: athkarData['textTransliteration'],
        meaning: athkarData['meaning'],
        recommendedCount: athkarData['recommendedCount'],
        source: athkarData['source'],
        category: AthkarCategory.sleeping,
        benefits: List<String>.from(athkarData['benefits']),
      );
      await db.insert(_dhikrTable, dhikr.toMap());
    }

    // Insert eating athkar
    for (final athkarData in AthkarData.eatingAthkar) {
      final dhikr = Dhikr(
        textArabic: athkarData['textArabic'],
        textEnglish: athkarData['textEnglish'],
        textTransliteration: athkarData['textTransliteration'],
        meaning: athkarData['meaning'],
        recommendedCount: athkarData['recommendedCount'],
        source: athkarData['source'],
        category: AthkarCategory.eating,
        benefits: List<String>.from(athkarData['benefits']),
      );
      await db.insert(_dhikrTable, dhikr.toMap());
    }

    // Insert travel athkar
    for (final athkarData in AthkarData.travelAthkar) {
      final dhikr = Dhikr(
        textArabic: athkarData['textArabic'],
        textEnglish: athkarData['textEnglish'],
        textTransliteration: athkarData['textTransliteration'],
        meaning: athkarData['meaning'],
        recommendedCount: athkarData['recommendedCount'],
        source: athkarData['source'],
        category: AthkarCategory.travel,
        benefits: List<String>.from(athkarData['benefits']),
      );
      await db.insert(_dhikrTable, dhikr.toMap());
    }
  }

  // Surah CRUD operations
  static Future<void> insertSurah(Surah surah) async {
    final db = await database;
    await db.insert(
      _surahsTable,
      surah.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  static Future<List<Surah>> getAllSurahs() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _surahsTable,
      orderBy: 'number ASC',
    );
    return List.generate(maps.length, (i) => Surah.fromMap(maps[i]));
  }

  static Future<Surah?> getSurahByNumber(int number) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _surahsTable,
      where: 'number = ?',
      whereArgs: [number],
    );
    if (maps.isNotEmpty) {
      return Surah.fromMap(maps.first);
    }
    return null;
  }

  // Verse CRUD operations
  static Future<void> insertVerse(verse_model.Verse verse) async {
    final db = await database;
    await db.insert(
      _versesTable,
      verse.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  static Future<List<verse_model.Verse>> getVersesBySurah(
    int surahNumber,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _versesTable,
      where: 'surahNumber = ?',
      whereArgs: [surahNumber],
      orderBy: 'verseNumber ASC',
    );
    return List.generate(
      maps.length,
      (i) => verse_model.Verse.fromMap(maps[i]),
    );
  }

  static Future<verse_model.Verse?> getVerse(
    int surahNumber,
    int verseNumber,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _versesTable,
      where: 'surahNumber = ? AND verseNumber = ?',
      whereArgs: [surahNumber, verseNumber],
    );
    if (maps.isNotEmpty) {
      return verse_model.Verse.fromMap(maps.first);
    }
    return null;
  }

  static Future<List<verse_model.Verse>> searchVerses(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _versesTable,
      where:
          'textArabic LIKE ? OR textEnglish LIKE ? OR textTransliteration LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'surahNumber ASC, verseNumber ASC',
    );
    return List.generate(
      maps.length,
      (i) => verse_model.Verse.fromMap(maps[i]),
    );
  }

  // Bookmark CRUD operations
  static Future<String> insertBookmark(bookmark_model.Bookmark bookmark) async {
    final db = await database;
    await db.insert(_bookmarksTable, bookmark.toMap());
    return bookmark.id;
  }

  static Future<List<bookmark_model.Bookmark>> getAllBookmarks() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _bookmarksTable,
      orderBy: 'updatedAt DESC',
    );
    return List.generate(
      maps.length,
      (i) => bookmark_model.Bookmark.fromMap(maps[i]),
    );
  }

  static Future<void> updateBookmark(bookmark_model.Bookmark bookmark) async {
    final db = await database;
    await db.update(
      _bookmarksTable,
      bookmark.toMap(),
      where: 'id = ?',
      whereArgs: [bookmark.id],
    );
  }

  static Future<void> deleteBookmark(String id) async {
    final db = await database;
    await db.delete(_bookmarksTable, where: 'id = ?', whereArgs: [id]);
  }

  // Tafseer CRUD operations
  static Future<String> insertTafseer(Tafseer tafseer) async {
    final db = await database;
    await db.insert(
      _tafseerTable,
      tafseer.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    return tafseer.id;
  }

  static Future<Tafseer?> getTafseerByVerse(
    int surahNumber,
    int verseNumber,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tafseerTable,
      where: 'surahNumber = ? AND verseNumber = ?',
      whereArgs: [surahNumber, verseNumber],
      limit: 1,
    );
    if (maps.isNotEmpty) {
      return Tafseer.fromMap(maps.first);
    }
    return null;
  }

  static Future<List<Tafseer>> getTafseerBySurah(int surahNumber) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tafseerTable,
      where: 'surahNumber = ?',
      whereArgs: [surahNumber],
      orderBy: 'verseNumber ASC',
    );
    return List.generate(maps.length, (i) => Tafseer.fromMap(maps[i]));
  }

  static Future<List<Tafseer>> searchTafseer(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tafseerTable,
      where: 'text LIKE ?',
      whereArgs: ['%$query%'],
      orderBy: 'surahNumber ASC, verseNumber ASC',
    );
    return List.generate(maps.length, (i) => Tafseer.fromMap(maps[i]));
  }

  static Future<void> updateTafseer(Tafseer tafseer) async {
    final db = await database;
    await db.update(
      _tafseerTable,
      tafseer.toMap(),
      where: 'id = ?',
      whereArgs: [tafseer.id],
    );
  }

  static Future<void> deleteTafseer(String id) async {
    final db = await database;
    await db.delete(_tafseerTable, where: 'id = ?', whereArgs: [id]);
  }

  static Future<Tafseer?> getTafseerById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tafseerTable,
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );
    if (maps.isNotEmpty) {
      return Tafseer.fromMap(maps.first);
    }
    return null;
  }

  static Future<List<Tafseer>> getAllTafseer() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tafseerTable,
      orderBy: 'surahNumber ASC, verseNumber ASC',
    );
    return List.generate(maps.length, (i) => Tafseer.fromMap(maps[i]));
  }

  static Future<List<Tafseer>> getCustomTafseer() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tafseerTable,
      where: 'isCustom = 1',
      orderBy: 'updatedAt DESC',
    );
    return List.generate(maps.length, (i) => Tafseer.fromMap(maps[i]));
  }

  static Future<List<Tafseer>> getTafseerBySource(String source) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tafseerTable,
      where: 'source = ?',
      whereArgs: [source],
      orderBy: 'surahNumber ASC, verseNumber ASC',
    );
    return List.generate(maps.length, (i) => Tafseer.fromMap(maps[i]));
  }

  static Future<Tafseer?> getRandomTafseer() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      'SELECT * FROM $_tafseerTable ORDER BY RANDOM() LIMIT 1',
    );
    if (maps.isNotEmpty) {
      return Tafseer.fromMap(maps.first);
    }
    return null;
  }

  static Future<void> toggleTafseerFavorite(String tafseerID) async {
    final db = await database;
    await db.rawUpdate(
      'UPDATE $_tafseerTable SET isFavorite = NOT isFavorite WHERE id = ?',
      [tafseerID],
    );
  }

  static Future<List<Tafseer>> getFavoriteTafseer() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tafseerTable,
      where: 'isFavorite = 1',
      orderBy: 'updatedAt DESC',
    );
    return List.generate(maps.length, (i) => Tafseer.fromMap(maps[i]));
  }

  static Future<List<Tafseer>> getReadTafseer() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tafseerTable,
      where: 'isRead = 1',
      orderBy: 'readAt DESC',
    );
    return List.generate(maps.length, (i) => Tafseer.fromMap(maps[i]));
  }

  static Future<void> markTafseerAsRead(String tafseerID) async {
    final db = await database;
    await db.update(
      _tafseerTable,
      {
        'isRead': 1,
        'readAt': DateTime.now().millisecondsSinceEpoch,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      },
      where: 'id = ?',
      whereArgs: [tafseerID],
    );
  }

  static Future<List<Tafseer>> getRecentlyReadTafseer({int limit = 10}) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tafseerTable,
      where: 'isRead = 1',
      orderBy: 'readAt DESC',
      limit: limit,
    );
    return List.generate(maps.length, (i) => Tafseer.fromMap(maps[i]));
  }

  // Dhikr CRUD operations
  static Future<String> insertDhikr(Dhikr dhikr) async {
    final db = await database;
    await db.insert(
      _dhikrTable,
      dhikr.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    return dhikr.id;
  }

  static Future<List<Dhikr>> getAllDhikr() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(_dhikrTable);
    return List.generate(maps.length, (i) => Dhikr.fromMap(maps[i]));
  }

  static Future<List<Dhikr>> getDhikrByCategory(AthkarCategory category) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _dhikrTable,
      where: 'category = ?',
      whereArgs: [category.name],
    );
    return List.generate(maps.length, (i) => Dhikr.fromMap(maps[i]));
  }

  static Future<Dhikr?> getDhikrById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _dhikrTable,
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Dhikr.fromMap(maps.first);
    }
    return null;
  }

  // Dhikr Session CRUD operations
  static Future<String> insertDhikrSession(DhikrSession session) async {
    final db = await database;
    await db.insert(_dhikrSessionsTable, session.toMap());
    return session.id;
  }

  static Future<List<DhikrSession>> getAllDhikrSessions() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _dhikrSessionsTable,
      orderBy: 'startTime DESC',
    );
    return List.generate(maps.length, (i) => DhikrSession.fromMap(maps[i]));
  }

  static Future<void> updateDhikrSession(DhikrSession session) async {
    final db = await database;
    await db.update(
      _dhikrSessionsTable,
      session.toMap(),
      where: 'id = ?',
      whereArgs: [session.id],
    );
  }

  static Future<void> deleteDhikrSession(String id) async {
    final db = await database;
    await db.delete(_dhikrSessionsTable, where: 'id = ?', whereArgs: [id]);
  }

  // Custom Athkar CRUD operations
  static Future<String> insertCustomAthkar(CustomAthkarRoutine routine) async {
    final db = await database;
    final routineMap = routine.toMap();
    routineMap['dhikrItems'] = jsonEncode(routineMap['dhikrItems']);
    await db.insert(_customAthkarTable, routineMap);
    return routine.id;
  }

  static Future<List<CustomAthkarRoutine>> getAllCustomAthkar() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _customAthkarTable,
      orderBy: 'updatedAt DESC',
    );
    return List.generate(maps.length, (i) {
      final map = Map<String, dynamic>.from(maps[i]);
      map['dhikrItems'] = jsonDecode(map['dhikrItems']);
      return CustomAthkarRoutine.fromMap(map);
    });
  }

  static Future<void> updateCustomAthkar(CustomAthkarRoutine routine) async {
    final db = await database;
    final routineMap = routine.toMap();
    routineMap['dhikrItems'] = jsonEncode(routineMap['dhikrItems']);
    await db.update(
      _customAthkarTable,
      routineMap,
      where: 'id = ?',
      whereArgs: [routine.id],
    );
  }

  static Future<void> deleteCustomAthkar(String id) async {
    final db = await database;
    await db.delete(_customAthkarTable, where: 'id = ?', whereArgs: [id]);
  }

  // Statistics
  static Future<Map<String, int>> getStatistics() async {
    final db = await database;

    final bookmarksCount =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM $_bookmarksTable'),
        ) ??
        0;

    final completedSessionsCount =
        Sqflite.firstIntValue(
          await db.rawQuery(
            'SELECT COUNT(*) FROM $_dhikrSessionsTable WHERE isCompleted = 1',
          ),
        ) ??
        0;

    final customAthkarCount =
        Sqflite.firstIntValue(
          await db.rawQuery(
            'SELECT COUNT(*) FROM $_customAthkarTable WHERE isActive = 1',
          ),
        ) ??
        0;

    return {
      'bookmarks': bookmarksCount,
      'completedSessions': completedSessionsCount,
      'customAthkar': customAthkarCount,
    };
  }

  // Daily progress
  static Future<Map<AthkarCategory, int>> getTodayProgress() async {
    final db = await database;
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    final Map<AthkarCategory, int> progress = {};

    for (final category in AthkarCategory.values) {
      if (category == AthkarCategory.custom) continue;

      final count =
          Sqflite.firstIntValue(
            await db.rawQuery(
              'SELECT COUNT(*) FROM $_dhikrSessionsTable WHERE category = ? AND isCompleted = 1 AND startTime >= ? AND startTime < ?',
              [
                category.name,
                startOfDay.millisecondsSinceEpoch,
                endOfDay.millisecondsSinceEpoch,
              ],
            ),
          ) ??
          0;

      progress[category] = count;
    }

    return progress;
  }
}
