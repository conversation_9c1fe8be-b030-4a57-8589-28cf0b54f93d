import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/media_providers.dart';
import '../models/media_models.dart';

/// Screen showing playlist details and tracks
class PlaylistDetailScreen extends ConsumerStatefulWidget {
  final Playlist playlist;

  const PlaylistDetailScreen({super.key, required this.playlist});

  @override
  ConsumerState<PlaylistDetailScreen> createState() =>
      _PlaylistDetailScreenState();
}

class _PlaylistDetailScreenState extends ConsumerState<PlaylistDetailScreen> {
  @override
  Widget build(BuildContext context) {
    final mediaFiles = ref.watch(mediaLibraryProvider);
    final playlistTracks = mediaFiles
        .where((file) => widget.playlist.mediaIds.contains(file.id))
        .toList();

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.playlist.name),
        actions: [
          IconButton(icon: const Icon(Icons.edit), onPressed: _editPlaylist),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'shuffle',
                child: ListTile(
                  leading: Icon(Icons.shuffle),
                  title: Text('Shuffle Play'),
                ),
              ),
              const PopupMenuItem(
                value: 'share',
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text('Share Playlist'),
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('Export Playlist'),
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Playlist header
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Playlist artwork
                Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.playlist_play,
                    size: 80,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                const SizedBox(height: 16),

                // Playlist info
                Text(
                  widget.playlist.name,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  widget.playlist.description,
                  style: TextStyle(color: Colors.grey[600]),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  '${widget.playlist.mediaIds.length} songs',
                  style: TextStyle(color: Colors.grey[600]),
                ),
                const SizedBox(height: 16),

                // Play button
                ElevatedButton.icon(
                  onPressed: playlistTracks.isNotEmpty ? _playPlaylist : null,
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('Play All'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const Divider(),

          // Track list
          Expanded(
            child: playlistTracks.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    itemCount: playlistTracks.length,
                    itemBuilder: (context, index) {
                      return _buildTrackItem(playlistTracks[index], index);
                    },
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addTracks,
        tooltip: 'Add Tracks',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.music_off, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No tracks in this playlist',
            style: TextStyle(fontSize: 18, color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'Add some tracks to get started',
            style: TextStyle(color: Colors.grey[500]),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _addTracks,
            icon: const Icon(Icons.add),
            label: const Text('Add Tracks'),
          ),
        ],
      ),
    );
  }

  Widget _buildTrackItem(MediaFile track, int index) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Theme.of(context).primaryColor,
        child: Text(
          '${index + 1}',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      title: Text(
        track.metadata.title ?? track.displayName,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Text(
        track.metadata.artist ?? 'Unknown Artist',
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: PopupMenuButton<String>(
        onSelected: (action) => _handleTrackAction(action, track),
        itemBuilder: (context) => [
          const PopupMenuItem(value: 'play', child: Text('Play')),
          const PopupMenuItem(
            value: 'remove',
            child: Text('Remove from Playlist'),
          ),
          const PopupMenuItem(value: 'info', child: Text('Track Info')),
        ],
      ),
      onTap: () => _playTrack(track),
    );
  }

  void _editPlaylist() {
    // TODO: Show edit playlist dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit playlist functionality coming soon')),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'shuffle':
        _shufflePlay();
        break;
      case 'share':
        _sharePlaylist();
        break;
      case 'export':
        _exportPlaylist();
        break;
    }
  }

  void _playPlaylist() {
    // TODO: Implement playlist playback
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Playing ${widget.playlist.name}')));
  }

  void _shufflePlay() {
    // TODO: Implement shuffle play
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Shuffle play coming soon')));
  }

  void _sharePlaylist() {
    // TODO: Implement playlist sharing
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Playlist sharing coming soon')),
    );
  }

  void _exportPlaylist() {
    // TODO: Implement playlist export
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Playlist export coming soon')),
    );
  }

  void _addTracks() {
    // TODO: Show track selection dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add tracks functionality coming soon')),
    );
  }

  void _playTrack(MediaFile track) {
    // Set the current playing track
    ref.read(currentPlayingMediaProvider.notifier).state = track;
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Playing ${track.displayName}')));
  }

  void _handleTrackAction(String action, MediaFile track) {
    switch (action) {
      case 'play':
        _playTrack(track);
        break;
      case 'remove':
        _removeTrack(track);
        break;
      case 'info':
        _showTrackInfo(track);
        break;
    }
  }

  void _removeTrack(MediaFile track) {
    ref
        .read(playlistsProvider.notifier)
        .removeTrackFromPlaylist(widget.playlist.id, track.id);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Removed ${track.displayName} from playlist')),
    );
  }

  void _showTrackInfo(MediaFile track) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(track.displayName),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Title: ${track.metadata.title ?? 'Unknown'}'),
            Text('Artist: ${track.metadata.artist ?? 'Unknown'}'),
            Text('Album: ${track.metadata.album ?? 'Unknown'}'),
            Text('Duration: ${_formatDuration(track.duration)}'),
            Text('File Size: ${_formatFileSize(track.size)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration? duration) {
    if (duration == null) return '--:--';
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
