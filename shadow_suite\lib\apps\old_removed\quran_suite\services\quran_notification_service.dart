import 'dart:async';
import '../../../core/services/notification_service.dart';
import '../models/prayer_models.dart';
import '../services/prayer_time_service.dart';

/// Quran Suite specific notification service
/// Handles prayer time alerts, daily reading reminders, Islamic event notifications
class QuranNotificationService {
  static final QuranNotificationService _instance = QuranNotificationService._internal();
  factory QuranNotificationService() => _instance;
  QuranNotificationService._internal();

  final NotificationService _notificationService = NotificationService();
  final PrayerTimeService _prayerTimeService = PrayerTimeService();
  static const String _appId = 'quran_suite';

  /// Initialize Quran Suite notifications
  Future<void> initialize() async {
    await _notificationService.initialize();
    await _prayerTimeService.initialize();
  }

  /// Schedule prayer time notifications for today and upcoming days
  Future<void> schedulePrayerTimeNotifications({
    int daysAhead = 7,
    PrayerNotificationSettings? settings,
  }) async {
    final notificationSettings = settings ?? PrayerNotificationSettings.defaultSettings();
    
    if (!notificationSettings.enabled) return;

    final today = DateTime.now();
    
    for (int i = 0; i < daysAhead; i++) {
      final date = today.add(Duration(days: i));
      final prayerTimes = _prayerTimeService.getPrayerTimes(date);
      
      for (final entry in prayerTimes.entries) {
        final prayer = entry.key;
        final time = entry.value;
        
        // Skip sunrise as it's not a prayer
        if (prayer == PrayerType.sunrise) continue;
        
        // Check if notifications are enabled for this prayer
        if (!notificationSettings.prayerNotifications[prayer]!) continue;
        
        // Schedule main prayer notification
        if (time.isAfter(DateTime.now())) {
          await _schedulePrayerNotification(
            prayer: prayer,
            time: time,
            settings: notificationSettings,
          );
        }
        
        // Schedule reminder notification
        final reminderMinutes = notificationSettings.reminderMinutes[prayer] ?? 5;
        if (reminderMinutes > 0) {
          final reminderTime = time.subtract(Duration(minutes: reminderMinutes));
          if (reminderTime.isAfter(DateTime.now())) {
            await _schedulePrayerReminderNotification(
              prayer: prayer,
              time: reminderTime,
              prayerTime: time,
              reminderMinutes: reminderMinutes,
            );
          }
        }
      }
    }
  }

  /// Schedule individual prayer notification
  Future<void> _schedulePrayerNotification({
    required PrayerType prayer,
    required DateTime time,
    required PrayerNotificationSettings settings,
  }) async {
    final prayerInfo = PrayerTimeInfo(
      prayer: prayer,
      time: time,
      timeRemaining: time.difference(DateTime.now()),
    );

    await _notificationService.scheduleNotification(
      appId: _appId,
      title: 'Prayer Time: ${prayerInfo.prayerName}',
      body: '${prayerInfo.prayerNameArabic} - It\'s time for ${prayerInfo.prayerName} prayer',
      scheduledTime: time,
      type: NotificationType.prayer,
      payload: 'prayer_time:${prayer.name}:${time.millisecondsSinceEpoch}',
    );
  }

  /// Schedule prayer reminder notification
  Future<void> _schedulePrayerReminderNotification({
    required PrayerType prayer,
    required DateTime time,
    required DateTime prayerTime,
    required int reminderMinutes,
  }) async {
    final prayerInfo = PrayerTimeInfo(
      prayer: prayer,
      time: prayerTime,
      timeRemaining: prayerTime.difference(time),
    );

    await _notificationService.scheduleNotification(
      appId: _appId,
      title: 'Prayer Reminder: ${prayerInfo.prayerName}',
      body: '${prayerInfo.prayerNameArabic} prayer is in $reminderMinutes minutes',
      scheduledTime: time,
      type: NotificationType.reminder,
      payload: 'prayer_reminder:${prayer.name}:${time.millisecondsSinceEpoch}',
    );
  }

  /// Schedule daily Quran reading reminder
  Future<void> scheduleDailyQuranReminder({
    required TimeOfDay reminderTime,
    String? customMessage,
  }) async {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day, reminderTime.hour, reminderTime.minute);
    final scheduledTime = today.isAfter(now) ? today : today.add(const Duration(days: 1));

    final message = customMessage ?? 'Time for your daily Quran reading. May Allah bless your recitation.';

    await _notificationService.scheduleNotification(
      appId: _appId,
      title: 'Daily Quran Reading 📖',
      body: message,
      scheduledTime: scheduledTime,
      type: NotificationType.reminder,
      recurring: true,
      recurringInterval: const Duration(days: 1),
      payload: 'daily_quran_reminder',
    );
  }

  /// Schedule weekly Hadith reminder
  Future<void> scheduleWeeklyHadithReminder({
    required int weekday, // 1 = Monday, 7 = Sunday
    required TimeOfDay reminderTime,
  }) async {
    final now = DateTime.now();
    final daysUntilWeekday = (weekday - now.weekday) % 7;
    final nextWeekday = now.add(Duration(days: daysUntilWeekday == 0 ? 7 : daysUntilWeekday));
    final scheduledTime = DateTime(nextWeekday.year, nextWeekday.month, nextWeekday.day, reminderTime.hour, reminderTime.minute);

    await _notificationService.scheduleNotification(
      appId: _appId,
      title: 'Weekly Hadith Study 📚',
      body: 'Time to read and reflect on the sayings of Prophet Muhammad (ﷺ)',
      scheduledTime: scheduledTime,
      type: NotificationType.reminder,
      recurring: true,
      recurringInterval: const Duration(days: 7),
      payload: 'weekly_hadith_reminder',
    );
  }

  /// Schedule Islamic event notifications
  Future<void> scheduleIslamicEventNotifications() async {
    final currentYear = DateTime.now().year;
    final islamicEvents = _getIslamicEvents(currentYear);

    for (final event in islamicEvents) {
      if (event.date.isAfter(DateTime.now())) {
        // Schedule notification 1 day before
        final reminderDate = event.date.subtract(const Duration(days: 1));
        if (reminderDate.isAfter(DateTime.now())) {
          await _notificationService.scheduleNotification(
            appId: _appId,
            title: 'Islamic Event Tomorrow',
            body: '${event.name} is tomorrow. ${event.description}',
            scheduledTime: reminderDate,
            type: NotificationType.general,
            payload: 'islamic_event_reminder:${event.id}',
          );
        }

        // Schedule notification on the day
        await _notificationService.scheduleNotification(
          appId: _appId,
          title: event.name,
          body: '${event.description} May Allah bless this sacred day.',
          scheduledTime: event.date,
          type: NotificationType.general,
          payload: 'islamic_event:${event.id}',
        );
      }
    }
  }

  /// Schedule Ramadan specific notifications
  Future<void> scheduleRamadanNotifications({
    required DateTime ramadanStart,
    required DateTime ramadanEnd,
  }) async {
    // Pre-Ramadan preparation reminder
    final preparationDate = ramadanStart.subtract(const Duration(days: 7));
    if (preparationDate.isAfter(DateTime.now())) {
      await _notificationService.scheduleNotification(
        appId: _appId,
        title: 'Ramadan Preparation 🌙',
        body: 'Ramadan starts in 7 days. Prepare your heart and mind for this blessed month.',
        scheduledTime: preparationDate,
        type: NotificationType.general,
        payload: 'ramadan_preparation',
      );
    }

    // Ramadan start notification
    if (ramadanStart.isAfter(DateTime.now())) {
      await _notificationService.scheduleNotification(
        appId: _appId,
        title: 'Ramadan Mubarak! 🌙✨',
        body: 'The blessed month of Ramadan has begun. May Allah accept your fasting and prayers.',
        scheduledTime: ramadanStart,
        type: NotificationType.general,
        payload: 'ramadan_start',
      );
    }

    // Daily Iftar reminders during Ramadan
    DateTime currentDate = ramadanStart;
    while (currentDate.isBefore(ramadanEnd)) {
      final prayerTimes = _prayerTimeService.getPrayerTimes(currentDate);
      final maghribTime = prayerTimes[PrayerType.maghrib];
      
      if (maghribTime != null && maghribTime.isAfter(DateTime.now())) {
        final iftarReminderTime = maghribTime.subtract(const Duration(minutes: 10));
        
        await _notificationService.scheduleNotification(
          appId: _appId,
          title: 'Iftar Time Approaching 🥄',
          body: 'Maghrib prayer and Iftar time is in 10 minutes. Prepare to break your fast.',
          scheduledTime: iftarReminderTime,
          type: NotificationType.reminder,
          payload: 'iftar_reminder:${currentDate.millisecondsSinceEpoch}',
        );
      }
      
      currentDate = currentDate.add(const Duration(days: 1));
    }

    // Laylat al-Qadr reminders (last 10 nights)
    final lastTenNights = ramadanEnd.subtract(const Duration(days: 10));
    if (lastTenNights.isAfter(DateTime.now())) {
      await _notificationService.scheduleNotification(
        appId: _appId,
        title: 'Last 10 Nights of Ramadan 🌟',
        body: 'The last 10 nights have begun. Seek Laylat al-Qadr through extra prayers and remembrance.',
        scheduledTime: lastTenNights,
        type: NotificationType.general,
        payload: 'last_ten_nights',
      );
    }
  }

  /// Schedule Dhikr (remembrance) reminders
  Future<void> scheduleDhikrReminders({
    required List<TimeOfDay> reminderTimes,
    String? customDhikr,
  }) async {
    final dhikrTexts = [
      'SubhanAllah (Glory be to Allah)',
      'Alhamdulillah (All praise is due to Allah)',
      'Allahu Akbar (Allah is the Greatest)',
      'La ilaha illa Allah (There is no god but Allah)',
      'Astaghfirullah (I seek forgiveness from Allah)',
    ];

    for (int i = 0; i < reminderTimes.length; i++) {
      final reminderTime = reminderTimes[i];
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day, reminderTime.hour, reminderTime.minute);
      final scheduledTime = today.isAfter(now) ? today : today.add(const Duration(days: 1));

      final dhikrText = customDhikr ?? dhikrTexts[i % dhikrTexts.length];

      await _notificationService.scheduleNotification(
        appId: _appId,
        title: 'Dhikr Reminder 🤲',
        body: 'Take a moment to remember Allah: $dhikrText',
        scheduledTime: scheduledTime,
        type: NotificationType.reminder,
        recurring: true,
        recurringInterval: const Duration(days: 1),
        payload: 'dhikr_reminder:$i',
      );
    }
  }

  /// Get Islamic events for a given year
  List<IslamicEvent> _getIslamicEvents(int year) {
    // Note: In a real implementation, these would be calculated based on Islamic calendar
    return [
      IslamicEvent(
        id: 'muharram_$year',
        name: 'Islamic New Year',
        date: DateTime(year, 8, 20), // Approximate
        description: 'The beginning of the Islamic calendar year',
      ),
      IslamicEvent(
        id: 'ashura_$year',
        name: 'Day of Ashura',
        date: DateTime(year, 8, 29), // Approximate
        description: 'A day of fasting and remembrance',
      ),
      IslamicEvent(
        id: 'mawlid_$year',
        name: 'Mawlid an-Nabi',
        date: DateTime(year, 10, 28), // Approximate
        description: 'Celebration of the birth of Prophet Muhammad (ﷺ)',
      ),
      IslamicEvent(
        id: 'isra_miraj_$year',
        name: 'Isra and Mi\'raj',
        date: DateTime(year, 2, 18), // Approximate
        description: 'The night journey and ascension of Prophet Muhammad (ﷺ)',
      ),
      IslamicEvent(
        id: 'laylat_qadr_$year',
        name: 'Laylat al-Qadr',
        date: DateTime(year, 4, 25), // Approximate (27th night of Ramadan)
        description: 'The Night of Power, better than a thousand months',
      ),
      IslamicEvent(
        id: 'eid_fitr_$year',
        name: 'Eid al-Fitr',
        date: DateTime(year, 5, 2), // Approximate
        description: 'Festival of breaking the fast after Ramadan',
      ),
      IslamicEvent(
        id: 'eid_adha_$year',
        name: 'Eid al-Adha',
        date: DateTime(year, 7, 9), // Approximate
        description: 'Festival of sacrifice during Hajj season',
      ),
    ];
  }

  /// Cancel all Quran Suite notifications
  Future<void> cancelAllNotifications() async {
    await _notificationService.cancelAllNotifications(_appId);
  }

  /// Cancel specific prayer notifications
  Future<void> cancelPrayerNotifications(PrayerType prayer) async {
    final notifications = _notificationService.getScheduledNotifications(_appId);
    for (final notification in notifications) {
      if (notification.payload?.contains('prayer') == true && 
          notification.payload?.contains(prayer.name) == true) {
        await _notificationService.cancelNotification(notification.id);
      }
    }
  }

  /// Get Quran Suite notification settings
  NotificationSettings getNotificationSettings() {
    return _notificationService.getNotificationSettings(_appId);
  }

  /// Update Quran Suite notification settings
  void updateNotificationSettings(NotificationSettings settings) {
    _notificationService.updateNotificationSettings(_appId, settings);
  }
}

/// Islamic event model
class IslamicEvent {
  final String id;
  final String name;
  final DateTime date;
  final String description;

  const IslamicEvent({
    required this.id,
    required this.name,
    required this.date,
    required this.description,
  });
}

/// Time of day helper class
class TimeOfDay {
  final int hour;
  final int minute;

  const TimeOfDay({required this.hour, required this.minute});

  @override
  String toString() => '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
}
