

// Islamic Art Model
class IslamicArt {
  final String id;
  final String title;
  final String artist;
  final IslamicArtType type;
  final String imagePath;
  final String description;
  final String? arabicText;
  final String? meaning;
  final List<String> tags;
  final double rating;
  final int viewCount;
  final bool isFavorite;
  final DateTime createdAt;

  const IslamicArt({
    required this.id,
    required this.title,
    required this.artist,
    required this.type,
    required this.imagePath,
    required this.description,
    this.arabicText,
    this.meaning,
    required this.tags,
    required this.rating,
    required this.viewCount,
    required this.isFavorite,
    required this.createdAt,
  });

  factory IslamicArt.fromJson(Map<String, dynamic> json) {
    return IslamicArt(
      id: json['id'] as String,
      title: json['title'] as String,
      artist: json['artist'] as String,
      type: IslamicArtType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => IslamicArtType.calligraphy,
      ),
      imagePath: json['image_path'] as String,
      description: json['description'] as String,
      arabicText: json['arabic_text'] as String?,
      meaning: json['meaning'] as String?,
      tags: List<String>.from(json['tags'] as List? ?? []),
      rating: (json['rating'] as num).toDouble(),
      viewCount: json['view_count'] as int,
      isFavorite: json['is_favorite'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'artist': artist,
      'type': type.name,
      'image_path': imagePath,
      'description': description,
      'arabic_text': arabicText,
      'meaning': meaning,
      'tags': tags,
      'rating': rating,
      'view_count': viewCount,
      'is_favorite': isFavorite,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Islamic History Model
class IslamicHistory {
  final String id;
  final String title;
  final String description;
  final DateTime date;
  final IslamicHistoryPeriod period;
  final String location;
  final List<String> keyFigures;
  final String significance;
  final List<String> sources;
  final bool isVerified;
  final DateTime addedAt;

  const IslamicHistory({
    required this.id,
    required this.title,
    required this.description,
    required this.date,
    required this.period,
    required this.location,
    required this.keyFigures,
    required this.significance,
    required this.sources,
    required this.isVerified,
    required this.addedAt,
  });

  factory IslamicHistory.fromJson(Map<String, dynamic> json) {
    return IslamicHistory(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      date: DateTime.parse(json['date'] as String),
      period: IslamicHistoryPeriod.values.firstWhere(
        (e) => e.name == json['period'],
        orElse: () => IslamicHistoryPeriod.classical,
      ),
      location: json['location'] as String,
      keyFigures: List<String>.from(json['key_figures'] as List? ?? []),
      significance: json['significance'] as String,
      sources: List<String>.from(json['sources'] as List? ?? []),
      isVerified: json['is_verified'] as bool,
      addedAt: DateTime.parse(json['added_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'date': date.toIso8601String(),
      'period': period.name,
      'location': location,
      'key_figures': keyFigures,
      'significance': significance,
      'sources': sources,
      'is_verified': isVerified,
      'added_at': addedAt.toIso8601String(),
    };
  }
}

// Quran Memorization Model
class QuranMemorization {
  final String id;
  final int surahNumber;
  final int startVerse;
  final int endVerse;
  final MemorizationMethod method;
  final double progress;
  final List<int> completedVerses;
  final Map<int, DateTime> reviewSchedule;
  final Map<String, dynamic> settings;
  final DateTime startedAt;
  final DateTime? lastReviewAt;
  final bool isCompleted;

  const QuranMemorization({
    required this.id,
    required this.surahNumber,
    required this.startVerse,
    required this.endVerse,
    required this.method,
    required this.progress,
    required this.completedVerses,
    required this.reviewSchedule,
    required this.settings,
    required this.startedAt,
    this.lastReviewAt,
    required this.isCompleted,
  });

  factory QuranMemorization.fromJson(Map<String, dynamic> json) {
    return QuranMemorization(
      id: json['id'] as String,
      surahNumber: json['surah_number'] as int,
      startVerse: json['start_verse'] as int,
      endVerse: json['end_verse'] as int,
      method: MemorizationMethod.values.firstWhere(
        (e) => e.name == json['method'],
        orElse: () => MemorizationMethod.repetition,
      ),
      progress: (json['progress'] as num).toDouble(),
      completedVerses: List<int>.from(json['completed_verses'] as List? ?? []),
      reviewSchedule: Map<int, DateTime>.from(
        (json['review_schedule'] as Map? ?? {}).map(
          (key, value) => MapEntry(int.parse(key), DateTime.parse(value)),
        ),
      ),
      settings: Map<String, dynamic>.from(json['settings'] as Map? ?? {}),
      startedAt: DateTime.parse(json['started_at'] as String),
      lastReviewAt: json['last_review_at'] != null 
          ? DateTime.parse(json['last_review_at'] as String) 
          : null,
      isCompleted: json['is_completed'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'surah_number': surahNumber,
      'start_verse': startVerse,
      'end_verse': endVerse,
      'method': method.name,
      'progress': progress,
      'completed_verses': completedVerses,
      'review_schedule': reviewSchedule.map(
        (key, value) => MapEntry(key.toString(), value.toIso8601String()),
      ),
      'settings': settings,
      'started_at': startedAt.toIso8601String(),
      'last_review_at': lastReviewAt?.toIso8601String(),
      'is_completed': isCompleted,
    };
  }
}

// Islamic Podcast Model
class IslamicPodcast {
  final String id;
  final String title;
  final String speaker;
  final String description;
  final String audioPath;
  final Duration duration;
  final PodcastCategory category;
  final String? transcript;
  final List<String> tags;
  final int playCount;
  final double rating;
  final bool isDownloaded;
  final DateTime? downloadedAt;
  final DateTime createdAt;

  const IslamicPodcast({
    required this.id,
    required this.title,
    required this.speaker,
    required this.description,
    required this.audioPath,
    required this.duration,
    required this.category,
    this.transcript,
    required this.tags,
    required this.playCount,
    required this.rating,
    required this.isDownloaded,
    this.downloadedAt,
    required this.createdAt,
  });

  factory IslamicPodcast.fromJson(Map<String, dynamic> json) {
    return IslamicPodcast(
      id: json['id'] as String,
      title: json['title'] as String,
      speaker: json['speaker'] as String,
      description: json['description'] as String,
      audioPath: json['audio_path'] as String,
      duration: Duration(milliseconds: json['duration_ms'] as int),
      category: PodcastCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => PodcastCategory.general,
      ),
      transcript: json['transcript'] as String?,
      tags: List<String>.from(json['tags'] as List? ?? []),
      playCount: json['play_count'] as int,
      rating: (json['rating'] as num).toDouble(),
      isDownloaded: json['is_downloaded'] as bool,
      downloadedAt: json['downloaded_at'] != null 
          ? DateTime.parse(json['downloaded_at'] as String) 
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'speaker': speaker,
      'description': description,
      'audio_path': audioPath,
      'duration_ms': duration.inMilliseconds,
      'category': category.name,
      'transcript': transcript,
      'tags': tags,
      'play_count': playCount,
      'rating': rating,
      'is_downloaded': isDownloaded,
      'downloaded_at': downloadedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Virtual Mosque Tour Model
class VirtualMosqueTour {
  final String id;
  final String mosqueName;
  final String location;
  final String description;
  final List<TourStop> stops;
  final String? audioGuidePath;
  final List<String> imagePaths;
  final Duration duration;
  final double rating;
  final int viewCount;
  final bool isOfflineAvailable;
  final DateTime createdAt;

  const VirtualMosqueTour({
    required this.id,
    required this.mosqueName,
    required this.location,
    required this.description,
    required this.stops,
    this.audioGuidePath,
    required this.imagePaths,
    required this.duration,
    required this.rating,
    required this.viewCount,
    required this.isOfflineAvailable,
    required this.createdAt,
  });

  factory VirtualMosqueTour.fromJson(Map<String, dynamic> json) {
    return VirtualMosqueTour(
      id: json['id'] as String,
      mosqueName: json['mosque_name'] as String,
      location: json['location'] as String,
      description: json['description'] as String,
      stops: (json['stops'] as List<dynamic>?)
          ?.map((e) => TourStop.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      audioGuidePath: json['audio_guide_path'] as String?,
      imagePaths: List<String>.from(json['image_paths'] as List? ?? []),
      duration: Duration(milliseconds: json['duration_ms'] as int),
      rating: (json['rating'] as num).toDouble(),
      viewCount: json['view_count'] as int,
      isOfflineAvailable: json['is_offline_available'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'mosque_name': mosqueName,
      'location': location,
      'description': description,
      'stops': stops.map((e) => e.toJson()).toList(),
      'audio_guide_path': audioGuidePath,
      'image_paths': imagePaths,
      'duration_ms': duration.inMilliseconds,
      'rating': rating,
      'view_count': viewCount,
      'is_offline_available': isOfflineAvailable,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Tour Stop Model
class TourStop {
  final String id;
  final String title;
  final String description;
  final String imagePath;
  final String? audioPath;
  final int order;
  final Duration? duration;

  const TourStop({
    required this.id,
    required this.title,
    required this.description,
    required this.imagePath,
    this.audioPath,
    required this.order,
    this.duration,
  });

  factory TourStop.fromJson(Map<String, dynamic> json) {
    return TourStop(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      imagePath: json['image_path'] as String,
      audioPath: json['audio_path'] as String?,
      order: json['order'] as int,
      duration: json['duration_ms'] != null 
          ? Duration(milliseconds: json['duration_ms'] as int) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'image_path': imagePath,
      'audio_path': audioPath,
      'order': order,
      'duration_ms': duration?.inMilliseconds,
    };
  }
}

// Islamic Name Model
class IslamicName {
  final String id;
  final String name;
  final String meaning;
  final NameGender gender;
  final String origin;
  final String? pronunciation;
  final List<String> variations;
  final List<String> famousPersons;
  final String? significance;
  final int popularity;
  final bool isFavorite;
  final DateTime addedAt;

  const IslamicName({
    required this.id,
    required this.name,
    required this.meaning,
    required this.gender,
    required this.origin,
    this.pronunciation,
    required this.variations,
    required this.famousPersons,
    this.significance,
    required this.popularity,
    required this.isFavorite,
    required this.addedAt,
  });

  factory IslamicName.fromJson(Map<String, dynamic> json) {
    return IslamicName(
      id: json['id'] as String,
      name: json['name'] as String,
      meaning: json['meaning'] as String,
      gender: NameGender.values.firstWhere(
        (e) => e.name == json['gender'],
        orElse: () => NameGender.unisex,
      ),
      origin: json['origin'] as String,
      pronunciation: json['pronunciation'] as String?,
      variations: List<String>.from(json['variations'] as List? ?? []),
      famousPersons: List<String>.from(json['famous_persons'] as List? ?? []),
      significance: json['significance'] as String?,
      popularity: json['popularity'] as int,
      isFavorite: json['is_favorite'] as bool,
      addedAt: DateTime.parse(json['added_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'meaning': meaning,
      'gender': gender.name,
      'origin': origin,
      'pronunciation': pronunciation,
      'variations': variations,
      'famous_persons': famousPersons,
      'significance': significance,
      'popularity': popularity,
      'is_favorite': isFavorite,
      'added_at': addedAt.toIso8601String(),
    };
  }
}

// Dream Interpretation Model
class DreamInterpretation {
  final String id;
  final String symbol;
  final String interpretation;
  final DreamCategory category;
  final String? context;
  final List<String> relatedSymbols;
  final String source;
  final int searchCount;
  final bool isVerified;
  final DateTime addedAt;

  const DreamInterpretation({
    required this.id,
    required this.symbol,
    required this.interpretation,
    required this.category,
    this.context,
    required this.relatedSymbols,
    required this.source,
    required this.searchCount,
    required this.isVerified,
    required this.addedAt,
  });

  factory DreamInterpretation.fromJson(Map<String, dynamic> json) {
    return DreamInterpretation(
      id: json['id'] as String,
      symbol: json['symbol'] as String,
      interpretation: json['interpretation'] as String,
      category: DreamCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => DreamCategory.general,
      ),
      context: json['context'] as String?,
      relatedSymbols: List<String>.from(json['related_symbols'] as List? ?? []),
      source: json['source'] as String,
      searchCount: json['search_count'] as int,
      isVerified: json['is_verified'] as bool,
      addedAt: DateTime.parse(json['added_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'symbol': symbol,
      'interpretation': interpretation,
      'category': category.name,
      'context': context,
      'related_symbols': relatedSymbols,
      'source': source,
      'search_count': searchCount,
      'is_verified': isVerified,
      'added_at': addedAt.toIso8601String(),
    };
  }
}

// Charity Record Model
class CharityRecord {
  final String id;
  final String recipient;
  final double amount;
  final String currency;
  final CharityType type;
  final String description;
  final DateTime date;
  final bool isZakat;
  final String? receiptPath;
  final bool isVerified;
  final DateTime createdAt;

  const CharityRecord({
    required this.id,
    required this.recipient,
    required this.amount,
    required this.currency,
    required this.type,
    required this.description,
    required this.date,
    required this.isZakat,
    this.receiptPath,
    required this.isVerified,
    required this.createdAt,
  });

  factory CharityRecord.fromJson(Map<String, dynamic> json) {
    return CharityRecord(
      id: json['id'] as String,
      recipient: json['recipient'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      type: CharityType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => CharityType.general,
      ),
      description: json['description'] as String,
      date: DateTime.parse(json['date'] as String),
      isZakat: json['is_zakat'] as bool,
      receiptPath: json['receipt_path'] as String?,
      isVerified: json['is_verified'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'recipient': recipient,
      'amount': amount,
      'currency': currency,
      'type': type.name,
      'description': description,
      'date': date.toIso8601String(),
      'is_zakat': isZakat,
      'receipt_path': receiptPath,
      'is_verified': isVerified,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Islamic Event Plan Model
class IslamicEventPlan {
  final String id;
  final String eventName;
  final IslamicEventType eventType;
  final DateTime startDate;
  final DateTime endDate;
  final String venue;
  final String description;
  final List<EventTask> tasks;
  final double? budget;
  final int? expectedAttendees;
  final int actualAttendees;
  final EventStatus status;
  final DateTime createdAt;
  final DateTime lastModified;

  const IslamicEventPlan({
    required this.id,
    required this.eventName,
    required this.eventType,
    required this.startDate,
    required this.endDate,
    required this.venue,
    required this.description,
    required this.tasks,
    this.budget,
    this.expectedAttendees,
    required this.actualAttendees,
    required this.status,
    required this.createdAt,
    required this.lastModified,
  });

  factory IslamicEventPlan.fromJson(Map<String, dynamic> json) {
    return IslamicEventPlan(
      id: json['id'] as String,
      eventName: json['event_name'] as String,
      eventType: IslamicEventType.values.firstWhere(
        (e) => e.name == json['event_type'],
        orElse: () => IslamicEventType.general,
      ),
      startDate: DateTime.parse(json['start_date'] as String),
      endDate: DateTime.parse(json['end_date'] as String),
      venue: json['venue'] as String,
      description: json['description'] as String,
      tasks: (json['tasks'] as List<dynamic>?)
          ?.map((e) => EventTask.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      budget: (json['budget'] as num?)?.toDouble(),
      expectedAttendees: json['expected_attendees'] as int?,
      actualAttendees: json['actual_attendees'] as int,
      status: EventStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => EventStatus.planning,
      ),
      createdAt: DateTime.parse(json['created_at'] as String),
      lastModified: DateTime.parse(json['last_modified'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'event_name': eventName,
      'event_type': eventType.name,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'venue': venue,
      'description': description,
      'tasks': tasks.map((e) => e.toJson()).toList(),
      'budget': budget,
      'expected_attendees': expectedAttendees,
      'actual_attendees': actualAttendees,
      'status': status.name,
      'created_at': createdAt.toIso8601String(),
      'last_modified': lastModified.toIso8601String(),
    };
  }
}

// Event Task Model
class EventTask {
  final String id;
  final String title;
  final String description;
  final DateTime dueDate;
  final bool isCompleted;
  final String? assignedTo;

  const EventTask({
    required this.id,
    required this.title,
    required this.description,
    required this.dueDate,
    required this.isCompleted,
    this.assignedTo,
  });

  factory EventTask.fromJson(Map<String, dynamic> json) {
    return EventTask(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      dueDate: DateTime.parse(json['due_date'] as String),
      isCompleted: json['is_completed'] as bool,
      assignedTo: json['assigned_to'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'due_date': dueDate.toIso8601String(),
      'is_completed': isCompleted,
      'assigned_to': assignedTo,
    };
  }
}

// Halal Product Model
class HalalProduct {
  final String id;
  final String name;
  final String brand;
  final String barcode;
  final HalalStatus status;
  final String certifyingBody;
  final String? ingredients;
  final DateTime? certificationExpiry;
  final List<String> allergens;
  final DateTime lastVerified;
  final int reportCount;
  final bool isVerified;
  final DateTime addedAt;

  const HalalProduct({
    required this.id,
    required this.name,
    required this.brand,
    required this.barcode,
    required this.status,
    required this.certifyingBody,
    this.ingredients,
    this.certificationExpiry,
    required this.allergens,
    required this.lastVerified,
    required this.reportCount,
    required this.isVerified,
    required this.addedAt,
  });

  factory HalalProduct.fromJson(Map<String, dynamic> json) {
    return HalalProduct(
      id: json['id'] as String,
      name: json['name'] as String,
      brand: json['brand'] as String,
      barcode: json['barcode'] as String,
      status: HalalStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => HalalStatus.unknown,
      ),
      certifyingBody: json['certifying_body'] as String,
      ingredients: json['ingredients'] as String?,
      certificationExpiry: json['certification_expiry'] != null
          ? DateTime.parse(json['certification_expiry'] as String)
          : null,
      allergens: List<String>.from(json['allergens'] as List? ?? []),
      lastVerified: DateTime.parse(json['last_verified'] as String),
      reportCount: json['report_count'] as int,
      isVerified: json['is_verified'] as bool,
      addedAt: DateTime.parse(json['added_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'brand': brand,
      'barcode': barcode,
      'status': status.name,
      'certifying_body': certifyingBody,
      'ingredients': ingredients,
      'certification_expiry': certificationExpiry?.toIso8601String(),
      'allergens': allergens,
      'last_verified': lastVerified.toIso8601String(),
      'report_count': reportCount,
      'is_verified': isVerified,
      'added_at': addedAt.toIso8601String(),
    };
  }
}

// Enums
enum IslamicArtType { calligraphy, geometric, architecture, manuscript, textile }
enum IslamicHistoryPeriod { prophetic, rashidun, umayyad, abbasid, classical, modern }
enum MemorizationMethod { repetition, spaced, visual, audio, combined }
enum PodcastCategory { quran, hadith, fiqh, history, contemporary, youth, general }
enum NameGender { male, female, unisex }
enum DreamCategory { general, animals, nature, people, objects, emotions, spiritual }
enum CharityType { general, zakat, sadaqah, waqf, emergency, education, healthcare }
enum IslamicEventType { general, educational, charity, social, religious, youth, family }
enum EventStatus { planning, active, completed, cancelled }
enum HalalStatus { halal, haram, doubtful, unknown }
