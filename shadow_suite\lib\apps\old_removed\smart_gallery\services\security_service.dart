import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:flutter/services.dart';
// Removed unused import
import '../../../core/database/database_service.dart';
import '../../../core/services/error_handler.dart' as error_handler;

/// Security service for SmartGallery+ with PIN/fingerprint authentication and encryption
class SmartGallerySecurityService {
  static bool _isInitialized = false;
  static bool _isAuthenticated = false;
  static String? _currentSessionKey;
  static DateTime? _lastAuthTime;
  static const Duration _sessionTimeout = Duration(minutes: 30);

  // Security settings
  static bool _biometricEnabled = false;
  static bool _pinEnabled = false;
  static String? _pinHash;
  static int _maxFailedAttempts = 5;
  static int _currentFailedAttempts = 0;
  static DateTime? _lockoutUntil;

  /// Initialize security service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Load security settings
      await _loadSecuritySettings();

      // Initialize biometric authentication
      await _initializeBiometrics();

      // Start session monitoring
      _startSessionMonitoring();

      _isInitialized = true;
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.security,
        context: 'Initialize security service',
      );
    }
  }

  /// Load security settings from database
  static Future<void> _loadSecuritySettings() async {
    try {
      final settings = await DatabaseService.safeQuery(
        'SELECT key, value FROM app_settings WHERE key LIKE ?',
        ['security_%'],
      );

      for (final setting in settings) {
        final key = setting['key'] as String;
        final value = setting['value'] as String;

        switch (key) {
          case 'security_biometric_enabled':
            _biometricEnabled = value == 'true';
            break;
          case 'security_pin_enabled':
            _pinEnabled = value == 'true';
            break;
          case 'security_pin_hash':
            _pinHash = value;
            break;
          case 'security_max_failed_attempts':
            _maxFailedAttempts = int.tryParse(value) ?? 5;
            break;
        }
      }
    } catch (e) {
      // Use default settings
    }
  }

  /// Initialize biometric authentication
  static Future<void> _initializeBiometrics() async {
    try {
      // Check if biometrics are available
      final isAvailable = await _checkBiometricAvailability();
      if (!isAvailable) {
        _biometricEnabled = false;
        await _saveSetting('security_biometric_enabled', 'false');
      }
    } catch (e) {
      _biometricEnabled = false;
    }
  }

  /// Check biometric availability
  static Future<bool> _checkBiometricAvailability() async {
    try {
      // Use local_auth plugin to check biometric availability
      // For now, simulate availability check
      return Platform.isAndroid || Platform.isIOS;
    } catch (e) {
      return false;
    }
  }

  /// Start session monitoring
  static void _startSessionMonitoring() {
    Timer.periodic(const Duration(minutes: 1), (timer) {
      _checkSessionTimeout();
    });
  }

  /// Check session timeout
  static void _checkSessionTimeout() {
    if (_isAuthenticated && _lastAuthTime != null) {
      final timeSinceAuth = DateTime.now().difference(_lastAuthTime!);
      if (timeSinceAuth > _sessionTimeout) {
        _isAuthenticated = false;
        _currentSessionKey = null;
        _lastAuthTime = null;
      }
    }
  }

  /// Authenticate with PIN
  static Future<bool> authenticateWithPIN(String pin) async {
    try {
      // Check if locked out
      if (_isLockedOut()) {
        throw SecurityException(
          'Account locked due to too many failed attempts',
        );
      }

      if (!_pinEnabled || _pinHash == null) {
        throw SecurityException('PIN authentication not enabled');
      }

      // Hash the provided PIN
      final hashedPin = _hashPIN(pin);

      // Verify PIN
      if (hashedPin == _pinHash) {
        await _onSuccessfulAuth();
        return true;
      } else {
        await _onFailedAuth();
        return false;
      }
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.security,
        context: 'PIN authentication failed',
      );
      return false;
    }
  }

  /// Authenticate with biometrics
  static Future<bool> authenticateWithBiometrics() async {
    try {
      if (!_biometricEnabled) {
        throw SecurityException('Biometric authentication not enabled');
      }

      // Check if locked out
      if (_isLockedOut()) {
        throw SecurityException(
          'Account locked due to too many failed attempts',
        );
      }

      // Perform biometric authentication
      final isAuthenticated = await _performBiometricAuth();

      if (isAuthenticated) {
        await _onSuccessfulAuth();
        return true;
      } else {
        await _onFailedAuth();
        return false;
      }
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.security,
        context: 'Biometric authentication failed',
      );
      return false;
    }
  }

  /// Perform biometric authentication
  static Future<bool> _performBiometricAuth() async {
    try {
      // Use local_auth plugin for biometric authentication
      // For now, simulate biometric authentication
      await Future.delayed(const Duration(seconds: 1));
      return true; // Simulated success
    } catch (e) {
      return false;
    }
  }

  /// Handle successful authentication
  static Future<void> _onSuccessfulAuth() async {
    _isAuthenticated = true;
    _lastAuthTime = DateTime.now();
    _currentFailedAttempts = 0;
    _lockoutUntil = null;
    _currentSessionKey = _generateSessionKey();

    // Save authentication state
    await _saveSetting(
      'security_last_auth',
      _lastAuthTime!.millisecondsSinceEpoch.toString(),
    );
    await _saveSetting('security_failed_attempts', '0');
  }

  /// Handle failed authentication
  static Future<void> _onFailedAuth() async {
    _currentFailedAttempts++;

    if (_currentFailedAttempts >= _maxFailedAttempts) {
      _lockoutUntil = DateTime.now().add(const Duration(minutes: 30));
      await _saveSetting(
        'security_lockout_until',
        _lockoutUntil!.millisecondsSinceEpoch.toString(),
      );
    }

    await _saveSetting(
      'security_failed_attempts',
      _currentFailedAttempts.toString(),
    );
  }

  /// Check if account is locked out
  static bool _isLockedOut() {
    if (_lockoutUntil == null) return false;
    return DateTime.now().isBefore(_lockoutUntil!);
  }

  /// Generate session key
  static String _generateSessionKey() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = List.generate(16, (index) => timestamp + index);
    return sha256.convert(random).toString();
  }

  /// Hash PIN with salt
  static String _hashPIN(String pin) {
    const salt = 'SmartGallery_Salt_2024';
    final bytes = utf8.encode(pin + salt);
    return sha256.convert(bytes).toString();
  }

  /// Set up PIN authentication
  static Future<bool> setupPIN(String pin) async {
    try {
      if (pin.length < 4) {
        throw SecurityException('PIN must be at least 4 digits');
      }

      _pinHash = _hashPIN(pin);
      _pinEnabled = true;

      await _saveSetting('security_pin_hash', _pinHash!);
      await _saveSetting('security_pin_enabled', 'true');

      return true;
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.security,
        context: 'PIN setup failed',
      );
      return false;
    }
  }

  /// Enable biometric authentication
  static Future<bool> enableBiometrics() async {
    try {
      final isAvailable = await _checkBiometricAvailability();
      if (!isAvailable) {
        throw SecurityException('Biometric authentication not available');
      }

      // Test biometric authentication
      final testAuth = await _performBiometricAuth();
      if (!testAuth) {
        throw SecurityException('Biometric authentication test failed');
      }

      _biometricEnabled = true;
      await _saveSetting('security_biometric_enabled', 'true');

      return true;
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.security,
        context: 'Biometric setup failed',
      );
      return false;
    }
  }

  /// Lock media item
  static Future<bool> lockMediaItem(String mediaId) async {
    try {
      if (!_isAuthenticated) {
        throw SecurityException('Authentication required');
      }

      // Update database to mark item as locked
      await DatabaseService.safeUpdate(
        'smart_gallery_items',
        {'is_locked': 1},
        where: 'id = ?',
        whereArgs: [mediaId],
      );

      // Encrypt media file if needed
      await _encryptMediaFile(mediaId);

      return true;
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.security,
        context: 'Lock media item failed',
      );
      return false;
    }
  }

  /// Unlock media item
  static Future<bool> unlockMediaItem(String mediaId) async {
    try {
      if (!_isAuthenticated) {
        throw SecurityException('Authentication required');
      }

      // Update database to mark item as unlocked
      await DatabaseService.safeUpdate(
        'smart_gallery_items',
        {'is_locked': 0},
        where: 'id = ?',
        whereArgs: [mediaId],
      );

      // Decrypt media file if needed
      await _decryptMediaFile(mediaId);

      return true;
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.security,
        context: 'Unlock media item failed',
      );
      return false;
    }
  }

  /// Encrypt media file
  static Future<void> _encryptMediaFile(String mediaId) async {
    try {
      // Get media file path
      final results = await DatabaseService.safeQuery(
        'SELECT path FROM smart_gallery_items WHERE id = ?',
        [mediaId],
      );

      if (results.isEmpty) return;

      final filePath = results.first['path'] as String;
      final file = File(filePath);

      if (!await file.exists()) return;

      // Read file content
      final content = await file.readAsBytes();

      // Encrypt content
      final encryptedContent = await _encryptData(content);

      // Write encrypted content to secure location
      final secureDir = await _getSecureDirectory();
      final encryptedFile = File('$secureDir/$mediaId.enc');
      await encryptedFile.writeAsBytes(encryptedContent);

      // Update database with encrypted path
      await DatabaseService.safeUpdate(
        'smart_gallery_items',
        {'path': encryptedFile.path},
        where: 'id = ?',
        whereArgs: [mediaId],
      );

      // Delete original file
      await file.delete();
    } catch (e) {
      // Keep original file if encryption fails
    }
  }

  /// Decrypt media file
  static Future<void> _decryptMediaFile(String mediaId) async {
    try {
      // Get encrypted file path
      final results = await DatabaseService.safeQuery(
        'SELECT path FROM smart_gallery_items WHERE id = ?',
        [mediaId],
      );

      if (results.isEmpty) return;

      final encryptedPath = results.first['path'] as String;
      final encryptedFile = File(encryptedPath);

      if (!await encryptedFile.exists()) return;

      // Read encrypted content
      final encryptedContent = await encryptedFile.readAsBytes();

      // Decrypt content
      final decryptedContent = await _decryptData(encryptedContent);

      // Restore original file
      final originalPath = encryptedPath.replaceAll('.enc', '');
      final originalFile = File(originalPath);
      await originalFile.writeAsBytes(decryptedContent);

      // Update database with original path
      await DatabaseService.safeUpdate(
        'smart_gallery_items',
        {'path': originalPath},
        where: 'id = ?',
        whereArgs: [mediaId],
      );

      // Delete encrypted file
      await encryptedFile.delete();
    } catch (e) {
      // Keep encrypted file if decryption fails
    }
  }

  /// Encrypt data using AES
  static Future<Uint8List> _encryptData(Uint8List data) async {
    // Implement AES encryption
    // For now, return XOR encrypted data (not secure, for demo only)
    final key = _currentSessionKey?.codeUnits ?? [1, 2, 3, 4];
    final encrypted = Uint8List(data.length);

    for (int i = 0; i < data.length; i++) {
      encrypted[i] = data[i] ^ key[i % key.length];
    }

    return encrypted;
  }

  /// Decrypt data using AES
  static Future<Uint8List> _decryptData(Uint8List encryptedData) async {
    // Implement AES decryption
    // For now, return XOR decrypted data (not secure, for demo only)
    return _encryptData(encryptedData); // XOR is its own inverse
  }

  /// Get secure directory for encrypted files
  static Future<String> _getSecureDirectory() async {
    // Create secure directory for encrypted files
    final appDir = Directory.current.path;
    final secureDir = Directory('$appDir/.secure');

    if (!await secureDir.exists()) {
      await secureDir.create(recursive: true);
    }

    return secureDir.path;
  }

  /// Save security setting
  static Future<void> _saveSetting(String key, String value) async {
    // Try to update first, then insert if no rows affected
    final updateResult = await DatabaseService.safeUpdate(
      'app_settings',
      {'value': value},
      where: 'key = ?',
      whereArgs: [key],
    );

    if (updateResult == null || updateResult == 0) {
      // No rows updated, insert new setting
      await DatabaseService.safeInsert('app_settings', {
        'key': key,
        'value': value,
      });
    }
  }

  /// Check if authenticated
  static bool get isAuthenticated => _isAuthenticated;

  /// Check if biometric enabled
  static bool get isBiometricEnabled => _biometricEnabled;

  /// Check if PIN enabled
  static bool get isPINEnabled => _pinEnabled;

  /// Get failed attempts count
  static int get failedAttempts => _currentFailedAttempts;

  /// Get lockout time remaining
  static Duration? get lockoutTimeRemaining {
    if (_lockoutUntil == null) return null;
    final remaining = _lockoutUntil!.difference(DateTime.now());
    return remaining.isNegative ? null : remaining;
  }

  /// Logout
  static void logout() {
    _isAuthenticated = false;
    _currentSessionKey = null;
    _lastAuthTime = null;
  }

  /// Dispose resources
  static void dispose() {
    logout();
  }
}

/// Security exception
class SecurityException implements Exception {
  final String message;

  const SecurityException(this.message);

  @override
  String toString() => 'SecurityException: $message';
}
