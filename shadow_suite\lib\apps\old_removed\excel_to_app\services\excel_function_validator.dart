import 'dart:math' as math;
import 'advanced_formula_engine.dart';

/// Comprehensive Excel function validation and testing service
class ExcelFunctionValidator {
  static bool _isInitialized = false;
  static final Map<String, List<FunctionTest>> _testSuites = {};
  static final List<ValidationResult> _lastResults = [];

  /// Initialize the validator with comprehensive test suites
  static void initialize() {
    if (_isInitialized) return;
    
    AdvancedFormulaEngine.initialize();
    _createTestSuites();
    _isInitialized = true;
  }

  /// Run all function tests and return results
  static Future<ValidationReport> runAllTests() async {
    await _ensureInitialized();
    
    final results = <ValidationResult>[];
    final startTime = DateTime.now();
    
    for (final entry in _testSuites.entries) {
      final functionName = entry.key;
      final tests = entry.value;
      
      for (final test in tests) {
        final result = await _runSingleTest(functionName, test);
        results.add(result);
      }
    }
    
    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);
    
    _lastResults.clear();
    _lastResults.addAll(results);
    
    return ValidationReport(
      results: results,
      totalTests: results.length,
      passedTests: results.where((r) => r.passed).length,
      failedTests: results.where((r) => !r.passed).length,
      duration: duration,
      timestamp: endTime,
    );
  }

  /// Run tests for a specific function
  static Future<List<ValidationResult>> runFunctionTests(String functionName) async {
    await _ensureInitialized();
    
    final tests = _testSuites[functionName.toUpperCase()] ?? [];
    final results = <ValidationResult>[];
    
    for (final test in tests) {
      final result = await _runSingleTest(functionName, test);
      results.add(result);
    }
    
    return results;
  }

  /// Validate a custom formula
  static Future<ValidationResult> validateFormula(String formula, dynamic expectedResult) async {
    await _ensureInitialized();
    
    try {
      final startTime = DateTime.now();
      final result = AdvancedFormulaEngine.evaluate(formula);
      final endTime = DateTime.now();
      
      final passed = _compareResults(result, expectedResult);
      
      return ValidationResult(
        functionName: 'CUSTOM',
        testName: 'Custom Formula',
        formula: formula,
        expectedResult: expectedResult,
        actualResult: result,
        passed: passed,
        executionTime: endTime.difference(startTime),
        errorMessage: passed ? null : 'Expected $expectedResult, got $result',
      );
    } catch (e) {
      return ValidationResult(
        functionName: 'CUSTOM',
        testName: 'Custom Formula',
        formula: formula,
        expectedResult: expectedResult,
        actualResult: null,
        passed: false,
        executionTime: Duration.zero,
        errorMessage: e.toString(),
      );
    }
  }

  /// Get performance benchmarks for all functions
  static Future<Map<String, PerformanceBenchmark>> getPerformanceBenchmarks() async {
    await _ensureInitialized();
    
    final benchmarks = <String, PerformanceBenchmark>{};
    
    for (final functionName in _testSuites.keys) {
      final benchmark = await _benchmarkFunction(functionName);
      benchmarks[functionName] = benchmark;
    }
    
    return benchmarks;
  }

  /// Get last validation results
  static List<ValidationResult> get lastResults => List.unmodifiable(_lastResults);

  /// Check if all core functions are working
  static Future<bool> validateCoreIntegrity() async {
    final coreFunctions = ['SUM', 'AVERAGE', 'IF', 'VLOOKUP', 'COUNT'];
    
    for (final func in coreFunctions) {
      final results = await runFunctionTests(func);
      if (results.any((r) => !r.passed)) {
        return false;
      }
    }
    
    return true;
  }

  // Private methods
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      initialize();
    }
  }

  static Future<ValidationResult> _runSingleTest(String functionName, FunctionTest test) async {
    try {
      final startTime = DateTime.now();
      final result = AdvancedFormulaEngine.evaluate(test.formula);
      final endTime = DateTime.now();
      
      final passed = _compareResults(result, test.expectedResult);
      
      return ValidationResult(
        functionName: functionName,
        testName: test.name,
        formula: test.formula,
        expectedResult: test.expectedResult,
        actualResult: result,
        passed: passed,
        executionTime: endTime.difference(startTime),
        errorMessage: passed ? null : test.errorMessage,
      );
    } catch (e) {
      return ValidationResult(
        functionName: functionName,
        testName: test.name,
        formula: test.formula,
        expectedResult: test.expectedResult,
        actualResult: null,
        passed: false,
        executionTime: Duration.zero,
        errorMessage: e.toString(),
      );
    }
  }

  static bool _compareResults(dynamic actual, dynamic expected) {
    if (actual == null && expected == null) return true;
    if (actual == null || expected == null) return false;
    
    // Handle floating point comparisons
    if (actual is num && expected is num) {
      return (actual - expected).abs() < 0.0001;
    }
    
    // Handle string comparisons
    if (actual is String && expected is String) {
      return actual.trim() == expected.trim();
    }
    
    // Handle list comparisons
    if (actual is List && expected is List) {
      if (actual.length != expected.length) return false;
      for (int i = 0; i < actual.length; i++) {
        if (!_compareResults(actual[i], expected[i])) return false;
      }
      return true;
    }
    
    return actual == expected;
  }

  static Future<PerformanceBenchmark> _benchmarkFunction(String functionName) async {
    final tests = _testSuites[functionName] ?? [];
    if (tests.isEmpty) {
      return PerformanceBenchmark(
        functionName: functionName,
        averageExecutionTime: Duration.zero,
        minExecutionTime: Duration.zero,
        maxExecutionTime: Duration.zero,
        totalExecutions: 0,
      );
    }
    
    final executionTimes = <Duration>[];
    
    // Run each test multiple times for accurate benchmarking
    for (final test in tests) {
      for (int i = 0; i < 10; i++) {
        final startTime = DateTime.now();
        try {
          AdvancedFormulaEngine.evaluate(test.formula);
        } catch (e) {
          // Ignore errors for benchmarking
        }
        final endTime = DateTime.now();
        executionTimes.add(endTime.difference(startTime));
      }
    }
    
    if (executionTimes.isEmpty) {
      return PerformanceBenchmark(
        functionName: functionName,
        averageExecutionTime: Duration.zero,
        minExecutionTime: Duration.zero,
        maxExecutionTime: Duration.zero,
        totalExecutions: 0,
      );
    }
    
    final totalMicroseconds = executionTimes.fold<int>(
      0, (sum, duration) => sum + duration.inMicroseconds,
    );
    final averageMicroseconds = totalMicroseconds ~/ executionTimes.length;
    
    return PerformanceBenchmark(
      functionName: functionName,
      averageExecutionTime: Duration(microseconds: averageMicroseconds),
      minExecutionTime: executionTimes.reduce((a, b) => a < b ? a : b),
      maxExecutionTime: executionTimes.reduce((a, b) => a > b ? a : b),
      totalExecutions: executionTimes.length,
    );
  }

  static void _createTestSuites() {
    // Mathematical Functions Tests
    _testSuites['SUM'] = [
      FunctionTest('Basic Sum', 'SUM(1,2,3,4,5)', 15),
      FunctionTest('Sum with Decimals', 'SUM(1.5,2.5,3.0)', 7.0),
      FunctionTest('Sum with Negatives', 'SUM(-1,2,-3,4)', 2),
      FunctionTest('Single Value', 'SUM(42)', 42),
      FunctionTest('Empty Sum', 'SUM()', 0),
    ];
    
    _testSuites['AVERAGE'] = [
      FunctionTest('Basic Average', 'AVERAGE(1,2,3,4,5)', 3.0),
      FunctionTest('Average with Decimals', 'AVERAGE(1.0,2.0,3.0)', 2.0),
      FunctionTest('Average with Negatives', 'AVERAGE(-2,0,2)', 0.0),
      FunctionTest('Single Value Average', 'AVERAGE(10)', 10.0),
    ];
    
    _testSuites['COUNT'] = [
      FunctionTest('Count Numbers', 'COUNT(1,2,3,"text",4)', 4),
      FunctionTest('Count Mixed', 'COUNT(1,"a",2,"b",3)', 3),
      FunctionTest('Count Empty', 'COUNT()', 0),
    ];
    
    _testSuites['MAX'] = [
      FunctionTest('Basic Max', 'MAX(1,5,3,9,2)', 9),
      FunctionTest('Max with Negatives', 'MAX(-5,-1,-10)', -1),
      FunctionTest('Max Single', 'MAX(42)', 42),
    ];
    
    _testSuites['MIN'] = [
      FunctionTest('Basic Min', 'MIN(1,5,3,9,2)', 1),
      FunctionTest('Min with Negatives', 'MIN(-5,-1,-10)', -10),
      FunctionTest('Min Single', 'MIN(42)', 42),
    ];
    
    // Logical Functions Tests
    _testSuites['IF'] = [
      FunctionTest('IF True', 'IF(TRUE,"Yes","No")', 'Yes'),
      FunctionTest('IF False', 'IF(FALSE,"Yes","No")', 'No'),
      FunctionTest('IF Comparison True', 'IF(5>3,"Greater","Less")', 'Greater'),
      FunctionTest('IF Comparison False', 'IF(2>5,"Greater","Less")', 'Less'),
    ];
    
    _testSuites['AND'] = [
      FunctionTest('AND True', 'AND(TRUE,TRUE)', true),
      FunctionTest('AND False', 'AND(TRUE,FALSE)', false),
      FunctionTest('AND Multiple True', 'AND(TRUE,TRUE,TRUE)', true),
      FunctionTest('AND Multiple False', 'AND(TRUE,FALSE,TRUE)', false),
    ];
    
    _testSuites['OR'] = [
      FunctionTest('OR True', 'OR(TRUE,FALSE)', true),
      FunctionTest('OR False', 'OR(FALSE,FALSE)', false),
      FunctionTest('OR Multiple True', 'OR(FALSE,TRUE,FALSE)', true),
      FunctionTest('OR Multiple False', 'OR(FALSE,FALSE,FALSE)', false),
    ];
    
    // Text Functions Tests
    _testSuites['CONCATENATE'] = [
      FunctionTest('Basic Concat', 'CONCATENATE("Hello"," ","World")', 'Hello World'),
      FunctionTest('Concat Numbers', 'CONCATENATE("Value: ",42)', 'Value: 42'),
      FunctionTest('Concat Empty', 'CONCATENATE("","Test","")', 'Test'),
    ];
    
    _testSuites['LEFT'] = [
      FunctionTest('Left Basic', 'LEFT("Hello",3)', 'Hel'),
      FunctionTest('Left Full', 'LEFT("Test",10)', 'Test'),
      FunctionTest('Left Zero', 'LEFT("Hello",0)', ''),
    ];
    
    _testSuites['RIGHT'] = [
      FunctionTest('Right Basic', 'RIGHT("Hello",3)', 'llo'),
      FunctionTest('Right Full', 'RIGHT("Test",10)', 'Test'),
      FunctionTest('Right Zero', 'RIGHT("Hello",0)', ''),
    ];
    
    _testSuites['LEN'] = [
      FunctionTest('Length Basic', 'LEN("Hello")', 5),
      FunctionTest('Length Empty', 'LEN("")', 0),
      FunctionTest('Length Spaces', 'LEN("  ")', 2),
    ];
    
    // Date Functions Tests
    _testSuites['YEAR'] = [
      FunctionTest('Year from Date', 'YEAR(DATE(2023,12,25))', 2023),
    ];
    
    _testSuites['MONTH'] = [
      FunctionTest('Month from Date', 'MONTH(DATE(2023,12,25))', 12),
    ];
    
    _testSuites['DAY'] = [
      FunctionTest('Day from Date', 'DAY(DATE(2023,12,25))', 25),
    ];
    
    // Advanced Mathematical Functions Tests
    _testSuites['SQRT'] = [
      FunctionTest('Square Root', 'SQRT(16)', 4.0),
      FunctionTest('Square Root Decimal', 'SQRT(2)', math.sqrt(2)),
      FunctionTest('Square Root Zero', 'SQRT(0)', 0.0),
    ];
    
    _testSuites['POWER'] = [
      FunctionTest('Power Basic', 'POWER(2,3)', 8.0),
      FunctionTest('Power Decimal', 'POWER(4,0.5)', 2.0),
      FunctionTest('Power Zero', 'POWER(5,0)', 1.0),
    ];
    
    _testSuites['ABS'] = [
      FunctionTest('Absolute Positive', 'ABS(5)', 5),
      FunctionTest('Absolute Negative', 'ABS(-5)', 5),
      FunctionTest('Absolute Zero', 'ABS(0)', 0),
    ];
    
    _testSuites['ROUND'] = [
      FunctionTest('Round Up', 'ROUND(3.7,0)', 4.0),
      FunctionTest('Round Down', 'ROUND(3.2,0)', 3.0),
      FunctionTest('Round Decimal', 'ROUND(3.14159,2)', 3.14),
    ];
  }
}

/// Test case for a specific function
class FunctionTest {
  final String name;
  final String formula;
  final dynamic expectedResult;
  final String? errorMessage;

  const FunctionTest(
    this.name,
    this.formula,
    this.expectedResult, [
    this.errorMessage,
  ]);
}

/// Result of a single validation test
class ValidationResult {
  final String functionName;
  final String testName;
  final String formula;
  final dynamic expectedResult;
  final dynamic actualResult;
  final bool passed;
  final Duration executionTime;
  final String? errorMessage;

  const ValidationResult({
    required this.functionName,
    required this.testName,
    required this.formula,
    required this.expectedResult,
    required this.actualResult,
    required this.passed,
    required this.executionTime,
    this.errorMessage,
  });
}

/// Complete validation report
class ValidationReport {
  final List<ValidationResult> results;
  final int totalTests;
  final int passedTests;
  final int failedTests;
  final Duration duration;
  final DateTime timestamp;

  const ValidationReport({
    required this.results,
    required this.totalTests,
    required this.passedTests,
    required this.failedTests,
    required this.duration,
    required this.timestamp,
  });

  double get successRate => totalTests > 0 ? passedTests / totalTests : 0.0;
  
  List<ValidationResult> get failedResults => 
      results.where((r) => !r.passed).toList();
      
  List<ValidationResult> get passedResults => 
      results.where((r) => r.passed).toList();
}

/// Performance benchmark for a function
class PerformanceBenchmark {
  final String functionName;
  final Duration averageExecutionTime;
  final Duration minExecutionTime;
  final Duration maxExecutionTime;
  final int totalExecutions;

  const PerformanceBenchmark({
    required this.functionName,
    required this.averageExecutionTime,
    required this.minExecutionTime,
    required this.maxExecutionTime,
    required this.totalExecutions,
  });
  
  bool get meetsPerformanceTarget => averageExecutionTime.inMicroseconds < 100000; // <100ms
}
