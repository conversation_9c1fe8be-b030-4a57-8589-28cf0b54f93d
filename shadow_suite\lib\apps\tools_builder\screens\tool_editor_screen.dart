import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/app_theme.dart';
import '../models/tool.dart';
import '../models/spreadsheet.dart';
import '../models/ui_component.dart';
import '../services/tools_builder_providers.dart';
import '../services/excel_service.dart';
import '../widgets/spreadsheet_editor.dart';
import '../widgets/ui_builder_canvas.dart';
import '../widgets/component_palette.dart';
import '../widgets/properties_panel.dart';

// Intent classes for keyboard shortcuts
class SaveIntent extends Intent {
  const SaveIntent();
}

class UndoIntent extends Intent {
  const UndoIntent();
}

class RedoIntent extends Intent {
  const RedoIntent();
}

class NewIntent extends Intent {
  const NewIntent();
}

class RunIntent extends Intent {
  const RunIntent();
}

class ToolEditorScreen extends ConsumerStatefulWidget {
  final Tool? tool;

  const ToolEditorScreen({super.key, this.tool});

  @override
  ConsumerState<ToolEditorScreen> createState() => _ToolEditorScreenState();
}

class _ToolEditorScreenState extends ConsumerState<ToolEditorScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late Tool _currentTool;
  bool _hasUnsavedChanges = false;

  // Undo/Redo functionality
  final List<Tool> _undoStack = [];
  final List<Tool> _redoStack = [];
  static const int _maxUndoSteps = 50;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Initialize tool
    if (widget.tool != null) {
      _currentTool = widget.tool!;
    } else {
      _createNewTool();
    }

    // Defer provider updates until after the widget is built
    Future.microtask(() {
      if (mounted) {
        try {
          ref.read(currentToolProvider.notifier).setTool(_currentTool);

          // Set up spreadsheet calculation
          if (_currentTool.spreadsheet != null) {
            ref
                .read(spreadsheetCalculationProvider.notifier)
                .setSpreadsheet(_currentTool.spreadsheet!);
          }
        } catch (e) {
          // Handle state modification errors gracefully
          debugPrint('Error setting tool state: $e');
        }
      }
    });
  }

  void _createNewTool() {
    final defaultSpreadsheet = Spreadsheet(
      name: 'New Tool Spreadsheet',
      sheets: [SpreadsheetSheet(name: 'Sheet1', cells: {})],
    );

    _currentTool = Tool(
      name: 'New Tool',
      description: 'A new tool created with Tools Builder',
      type: ToolType.custom,
      category: ToolCategory.utilities,
      spreadsheet: defaultSpreadsheet,
      components: [],
      creatorId: 'current-user', // In real app, get from auth
    );

    ref.read(currentToolProvider.notifier).setTool(_currentTool);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Shortcuts(
      shortcuts: <LogicalKeySet, Intent>{
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyS):
            const SaveIntent(),
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyZ):
            const UndoIntent(),
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyY):
            const RedoIntent(),
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyN):
            const NewIntent(),
        LogicalKeySet(LogicalKeyboardKey.f5): const RunIntent(),
      },
      child: Actions(
        actions: <Type, Action<Intent>>{
          SaveIntent: CallbackAction<SaveIntent>(onInvoke: (_) => _saveTool()),
          UndoIntent: CallbackAction<UndoIntent>(onInvoke: (_) => _undo()),
          RedoIntent: CallbackAction<RedoIntent>(onInvoke: (_) => _redo()),
          NewIntent: CallbackAction<NewIntent>(
            onInvoke: (_) => _createNewTool(),
          ),
          RunIntent: CallbackAction<RunIntent>(onInvoke: (_) => _runTool()),
        },
        child: Scaffold(
          backgroundColor: AppTheme.backgroundColor,
          body: Column(
            children: [
              _buildHeader(),
              _buildTabBar(),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildSpreadsheetTab(),
                    _buildUIBuilderTab(),
                    _buildPreviewTab(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => _handleBack(),
            icon: const Icon(Icons.arrow_back),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextField(
                  controller: TextEditingController(text: _currentTool.name),
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    hintText: 'Tool Name',
                  ),
                  onChanged: (value) {
                    _updateTool(_currentTool.copyWith(name: value));
                  },
                ),
                TextField(
                  controller: TextEditingController(
                    text: _currentTool.description,
                  ),
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    hintText: 'Tool Description',
                  ),
                  onChanged: (value) {
                    _updateTool(_currentTool.copyWith(description: value));
                  },
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          _buildToolActions(),
        ],
      ),
    );
  }

  Widget _buildToolActions() {
    return Row(
      children: [
        if (_hasUnsavedChanges)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              'UNSAVED',
              style: TextStyle(
                color: Colors.orange,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        const SizedBox(width: 12),
        ElevatedButton.icon(
          onPressed: () => _importFromExcel(),
          icon: const Icon(Icons.upload_file, size: 16),
          label: const Text('Import Excel'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
          ),
        ),
        const SizedBox(width: 8),
        ElevatedButton.icon(
          onPressed: () => _exportToExcel(),
          icon: const Icon(Icons.download, size: 16),
          label: const Text('Export'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
        ),
        const SizedBox(width: 8),
        ElevatedButton.icon(
          onPressed: () => _saveTool(),
          icon: const Icon(Icons.save, size: 16),
          label: const Text('Save'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.toolsBuilderColor,
            foregroundColor: Colors.white,
          ),
        ),
        const SizedBox(width: 8),
        ElevatedButton.icon(
          onPressed: () => _runTool(),
          icon: const Icon(Icons.play_arrow, size: 16),
          label: const Text('Run'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: AppTheme.toolsBuilderColor,
        unselectedLabelColor: Colors.grey,
        indicatorColor: AppTheme.toolsBuilderColor,
        tabs: const [
          Tab(icon: Icon(Icons.table_chart), text: 'Spreadsheet'),
          Tab(icon: Icon(Icons.design_services), text: 'UI Builder'),
          Tab(icon: Icon(Icons.preview), text: 'Preview'),
        ],
      ),
    );
  }

  Widget _buildSpreadsheetTab() {
    return Consumer(
      builder: (context, ref, child) {
        final currentTool = ref.watch(currentToolProvider);
        if (currentTool == null) {
          return const Center(child: CircularProgressIndicator());
        }

        return SpreadsheetEditor(
          spreadsheet: currentTool.spreadsheet ?? _createDefaultSpreadsheet(),
          onSpreadsheetChanged: (spreadsheet) {
            _updateTool(currentTool.copyWith(spreadsheet: spreadsheet));
          },
        );
      },
    );
  }

  Widget _buildUIBuilderTab() {
    return Consumer(
      builder: (context, ref, child) {
        final currentTool = ref.watch(currentToolProvider);
        if (currentTool == null) {
          return const Center(child: CircularProgressIndicator());
        }

        return Row(
          children: [
            // Component Palette
            SizedBox(
              width: 250,
              child: ComponentPalette(
                onComponentSelected: (componentType) {
                  _addComponent(componentType);
                },
              ),
            ),
            // Canvas
            Expanded(
              child: UIBuilderCanvas(
                components: currentTool.components,
                onComponentsChanged: (components) {
                  _updateTool(currentTool.copyWith(components: components));
                },
                onComponentSelected: (componentId) {
                  ref.read(selectedComponentProvider.notifier).state =
                      componentId;
                },
              ),
            ),
            // Properties Panel
            SizedBox(
              width: 300,
              child: PropertiesPanel(
                selectedComponentId: ref.watch(selectedComponentProvider),
                components: currentTool.components,
                spreadsheet:
                    currentTool.spreadsheet ?? _createDefaultSpreadsheet(),
                onComponentChanged: (component) {
                  _updateComponent(component);
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPreviewTab() {
    return Consumer(
      builder: (context, ref, child) {
        final currentTool = ref.watch(currentToolProvider);
        if (currentTool == null) {
          return const Center(child: CircularProgressIndicator());
        }

        return Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'This is a preview of how your tool will look when users run it. '
                        'All formulas and data bindings are active.',
                        style: TextStyle(color: Colors.blue[700]),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: _buildToolPreview(currentTool),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildToolPreview(Tool tool) {
    return Stack(
      children: tool.components.map((component) {
        return Positioned(
          left: component.x,
          top: component.y,
          child: _buildPreviewComponent(component),
        );
      }).toList(),
    );
  }

  Widget _buildPreviewComponent(UIComponent component) {
    switch (component.type) {
      case ComponentType.textInput:
        return SizedBox(
          width: component.style.width ?? 200,
          child: TextField(
            decoration: InputDecoration(
              labelText: component.label,
              hintText: component.placeholder,
              border: const OutlineInputBorder(),
            ),
            onChanged: (value) {
              ref
                  .read(toolRuntimeProvider.notifier)
                  .updateComponentValue(component.id, value);
            },
          ),
        );

      case ComponentType.numberInput:
        return SizedBox(
          width: component.style.width ?? 200,
          child: TextField(
            decoration: InputDecoration(
              labelText: component.label,
              hintText: component.placeholder,
              border: const OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
            onChanged: (value) {
              final numValue = double.tryParse(value) ?? 0;
              ref
                  .read(toolRuntimeProvider.notifier)
                  .updateComponentValue(component.id, numValue);
            },
          ),
        );

      case ComponentType.label:
        return Consumer(
          builder: (context, ref, child) {
            final runtimeValue = ref.watch(toolRuntimeProvider)[component.id];
            final displayValue = runtimeValue?.toString() ?? component.label;

            return Text(
              displayValue,
              style: TextStyle(
                fontSize: component.style.fontSize ?? 16,
                fontWeight: component.style.isBold
                    ? FontWeight.bold
                    : FontWeight.normal,
                fontStyle: component.style.isItalic
                    ? FontStyle.italic
                    : FontStyle.normal,
                color: component.style.textColor != null
                    ? Color(
                        int.parse(
                          component.style.textColor!.replaceFirst('#', '0xFF'),
                        ),
                      )
                    : null,
              ),
            );
          },
        );

      case ComponentType.button:
        return ElevatedButton(
          onPressed: () {
            // Handle button press
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('${component.label} pressed')),
            );
          },
          child: Text(component.label),
        );

      default:
        return Container(
          width: component.style.width ?? 100,
          height: component.style.height ?? 40,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Center(
            child: Text(
              component.type.name.toUpperCase(),
              style: const TextStyle(fontSize: 12),
            ),
          ),
        );
    }
  }

  void _updateTool(Tool tool) {
    // Add current tool to undo stack before updating
    _addToUndoStack(_currentTool);

    setState(() {
      _currentTool = tool;
      _hasUnsavedChanges = true;
    });
    ref.read(currentToolProvider.notifier).setTool(tool);
    if (tool.spreadsheet != null) {
      ref
          .read(spreadsheetCalculationProvider.notifier)
          .setSpreadsheet(tool.spreadsheet!);
    }
  }

  void _addToUndoStack(Tool tool) {
    _undoStack.add(tool);
    if (_undoStack.length > _maxUndoSteps) {
      _undoStack.removeAt(0);
    }
    _redoStack.clear(); // Clear redo stack when new action is performed
  }

  void _undo() {
    if (_undoStack.isNotEmpty) {
      _redoStack.add(_currentTool);
      final previousTool = _undoStack.removeLast();
      setState(() {
        _currentTool = previousTool;
        _hasUnsavedChanges = true;
      });
      ref.read(currentToolProvider.notifier).setTool(previousTool);
      if (previousTool.spreadsheet != null) {
        ref
            .read(spreadsheetCalculationProvider.notifier)
            .setSpreadsheet(previousTool.spreadsheet!);
      }
    }
  }

  void _redo() {
    if (_redoStack.isNotEmpty) {
      _undoStack.add(_currentTool);
      final nextTool = _redoStack.removeLast();
      setState(() {
        _currentTool = nextTool;
        _hasUnsavedChanges = true;
      });
      ref.read(currentToolProvider.notifier).setTool(nextTool);
      if (nextTool.spreadsheet != null) {
        ref
            .read(spreadsheetCalculationProvider.notifier)
            .setSpreadsheet(nextTool.spreadsheet!);
      }
    }
  }

  void _addComponent(ComponentType type) {
    final newComponent = UIComponent(
      type: type,
      label: type.name.toUpperCase(),
      x: 50,
      y: 50,
    );

    final updatedComponents = [..._currentTool.components, newComponent];
    _updateTool(_currentTool.copyWith(components: updatedComponents));
  }

  void _updateComponent(UIComponent component) {
    final updatedComponents = _currentTool.components.map((c) {
      return c.id == component.id ? component : c;
    }).toList();

    _updateTool(_currentTool.copyWith(components: updatedComponents));
  }

  Future<void> _importFromExcel() async {
    final result = await ExcelService.importFromExcel();
    if (result.isSuccess && result.spreadsheet != null && mounted) {
      _updateTool(_currentTool.copyWith(spreadsheet: result.spreadsheet));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Excel file imported successfully')),
      );
    }
  }

  Future<void> _exportToExcel() async {
    if (_currentTool.spreadsheet == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No spreadsheet data to export')),
      );
      return;
    }
    final success = await ExcelService.exportToExcel(
      _currentTool.spreadsheet!,
      _currentTool.name,
    );
    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Tool exported to Excel successfully')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to export to Excel')),
        );
      }
    }
  }

  Future<void> _saveTool() async {
    try {
      if (widget.tool != null) {
        ref.read(toolsProvider.notifier).updateTool(_currentTool);
      } else {
        ref.read(toolsProvider.notifier).addTool(_currentTool);
      }

      setState(() {
        _hasUnsavedChanges = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Tool saved successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to save tool: $e')));
      }
    }
  }

  void _runTool() {
    // Navigate to tool runtime
    Navigator.of(context).pushNamed('/tool-runtime', arguments: _currentTool);
  }

  void _handleBack() {
    if (_hasUnsavedChanges) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Unsaved Changes'),
          content: const Text(
            'You have unsaved changes. Do you want to save before leaving?',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pop();
              },
              child: const Text('Discard'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                final navigator = Navigator.of(context);
                navigator.pop();
                await _saveTool();
                if (mounted) {
                  navigator.pop();
                }
              },
              child: const Text('Save'),
            ),
          ],
        ),
      );
    } else {
      Navigator.of(context).pop();
    }
  }

  Spreadsheet _createDefaultSpreadsheet() {
    return Spreadsheet(
      name: 'Default Spreadsheet',
      sheets: [SpreadsheetSheet(name: 'Sheet1', cells: {})],
    );
  }
}
