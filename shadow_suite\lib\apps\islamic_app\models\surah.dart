import 'package:uuid/uuid.dart';

class Surah {
  final int number;
  final String nameArabic;
  final String nameEnglish;
  final String nameTransliteration;
  final int verseCount;
  final String revelationType; // 'Meccan' or 'Medinan'
  final List<Verse> verses;

  const Surah({
    required this.number,
    required this.nameArabic,
    required this.nameEnglish,
    required this.nameTransliteration,
    required this.verseCount,
    required this.revelationType,
    required this.verses,
  });

  Map<String, dynamic> toMap() {
    return {
      'number': number,
      'nameArabic': nameArabic,
      'nameEnglish': nameEnglish,
      'nameTransliteration': nameTransliteration,
      'verseCount': verseCount,
      'revelationType': revelationType,
    };
  }

  factory Surah.fromMap(Map<String, dynamic> map) {
    return Surah(
      number: map['number'],
      nameArabic: map['nameArabic'],
      nameEnglish: map['nameEnglish'],
      nameTransliteration: map['nameTransliteration'],
      verseCount: map['verseCount'],
      revelationType: map['revelationType'],
      verses: [], // Verses loaded separately
    );
  }

  @override
  String toString() {
    return 'Surah(number: $number, nameEnglish: $nameEnglish, verseCount: $verseCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Surah && other.number == number;
  }

  @override
  int get hashCode => number.hashCode;
}

class Verse {
  final int surahNumber;
  final int verseNumber;
  final String textArabic;
  final String textEnglish;
  final String textTransliteration;
  final int juzNumber;
  final int hizbNumber;
  final int rukuNumber;

  const Verse({
    required this.surahNumber,
    required this.verseNumber,
    required this.textArabic,
    required this.textEnglish,
    required this.textTransliteration,
    required this.juzNumber,
    required this.hizbNumber,
    required this.rukuNumber,
  });

  String get reference => '$surahNumber:$verseNumber';

  Map<String, dynamic> toMap() {
    return {
      'surahNumber': surahNumber,
      'verseNumber': verseNumber,
      'textArabic': textArabic,
      'textEnglish': textEnglish,
      'textTransliteration': textTransliteration,
      'juzNumber': juzNumber,
      'hizbNumber': hizbNumber,
      'rukuNumber': rukuNumber,
    };
  }

  factory Verse.fromMap(Map<String, dynamic> map) {
    return Verse(
      surahNumber: map['surahNumber'],
      verseNumber: map['verseNumber'],
      textArabic: map['textArabic'],
      textEnglish: map['textEnglish'],
      textTransliteration: map['textTransliteration'],
      juzNumber: map['juzNumber'],
      hizbNumber: map['hizbNumber'],
      rukuNumber: map['rukuNumber'],
    );
  }

  @override
  String toString() {
    return 'Verse($reference: ${textEnglish.substring(0, 50)}...)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Verse && 
           other.surahNumber == surahNumber && 
           other.verseNumber == verseNumber;
  }

  @override
  int get hashCode => reference.hashCode;
}

class Bookmark {
  final String id;
  final int surahNumber;
  final int verseNumber;
  final String title;
  final String notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<String> tags;

  Bookmark({
    String? id,
    required this.surahNumber,
    required this.verseNumber,
    required this.title,
    required this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    required this.tags,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  String get reference => '$surahNumber:$verseNumber';

  Bookmark copyWith({
    String? title,
    String? notes,
    DateTime? updatedAt,
    List<String>? tags,
  }) {
    return Bookmark(
      id: id,
      surahNumber: surahNumber,
      verseNumber: verseNumber,
      title: title ?? this.title,
      notes: notes ?? this.notes,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      tags: tags ?? this.tags,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'surahNumber': surahNumber,
      'verseNumber': verseNumber,
      'title': title,
      'notes': notes,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'tags': tags.join(','),
    };
  }

  factory Bookmark.fromMap(Map<String, dynamic> map) {
    return Bookmark(
      id: map['id'],
      surahNumber: map['surahNumber'],
      verseNumber: map['verseNumber'],
      title: map['title'],
      notes: map['notes'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
      tags: map['tags'].toString().split(',').where((tag) => tag.isNotEmpty).toList(),
    );
  }

  @override
  String toString() {
    return 'Bookmark(id: $id, reference: $reference, title: $title)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Bookmark && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Sample Quran data - first few surahs for demonstration
class QuranData {
  static const List<Map<String, dynamic>> surahs = [
    {
      'number': 1,
      'nameArabic': 'الفاتحة',
      'nameEnglish': 'Al-Fatihah',
      'nameTransliteration': 'Al-Faatiha',
      'verseCount': 7,
      'revelationType': 'Meccan',
    },
    {
      'number': 2,
      'nameArabic': 'البقرة',
      'nameEnglish': 'Al-Baqarah',
      'nameTransliteration': 'Al-Baqara',
      'verseCount': 286,
      'revelationType': 'Medinan',
    },
    {
      'number': 3,
      'nameArabic': 'آل عمران',
      'nameEnglish': 'Ali \'Imran',
      'nameTransliteration': 'Aal-i-Imraan',
      'verseCount': 200,
      'revelationType': 'Medinan',
    },
    {
      'number': 4,
      'nameArabic': 'النساء',
      'nameEnglish': 'An-Nisa',
      'nameTransliteration': 'An-Nisaa',
      'verseCount': 176,
      'revelationType': 'Medinan',
    },
    {
      'number': 5,
      'nameArabic': 'المائدة',
      'nameEnglish': 'Al-Ma\'idah',
      'nameTransliteration': 'Al-Maaida',
      'verseCount': 120,
      'revelationType': 'Medinan',
    },
    {'number': 6, 'nameArabic': 'الأنعام', 'nameEnglish': 'Al-An\'am', 'nameTransliteration': 'Al-An\'aam', 'verseCount': 165, 'revelationType': 'Meccan'},
    {'number': 7, 'nameArabic': 'الأعراف', 'nameEnglish': 'Al-A\'raf', 'nameTransliteration': 'Al-A\'raaf', 'verseCount': 206, 'revelationType': 'Meccan'},
    {'number': 8, 'nameArabic': 'الأنفال', 'nameEnglish': 'Al-Anfal', 'nameTransliteration': 'Al-Anfaal', 'verseCount': 75, 'revelationType': 'Medinan'},
    {'number': 9, 'nameArabic': 'التوبة', 'nameEnglish': 'At-Tawbah', 'nameTransliteration': 'At-Tawba', 'verseCount': 129, 'revelationType': 'Medinan'},
    {'number': 10, 'nameArabic': 'يونس', 'nameEnglish': 'Yunus', 'nameTransliteration': 'Yunus', 'verseCount': 109, 'revelationType': 'Meccan'},
    {'number': 11, 'nameArabic': 'هود', 'nameEnglish': 'Hud', 'nameTransliteration': 'Hud', 'verseCount': 123, 'revelationType': 'Meccan'},
    {'number': 12, 'nameArabic': 'يوسف', 'nameEnglish': 'Yusuf', 'nameTransliteration': 'Yusuf', 'verseCount': 111, 'revelationType': 'Meccan'},
    {'number': 13, 'nameArabic': 'الرعد', 'nameEnglish': 'Ar-Ra\'d', 'nameTransliteration': 'Ar-Ra\'d', 'verseCount': 43, 'revelationType': 'Medinan'},
    {'number': 14, 'nameArabic': 'إبراهيم', 'nameEnglish': 'Ibrahim', 'nameTransliteration': 'Ibrahim', 'verseCount': 52, 'revelationType': 'Meccan'},
    {'number': 15, 'nameArabic': 'الحجر', 'nameEnglish': 'Al-Hijr', 'nameTransliteration': 'Al-Hijr', 'verseCount': 99, 'revelationType': 'Meccan'},
    {'number': 16, 'nameArabic': 'النحل', 'nameEnglish': 'An-Nahl', 'nameTransliteration': 'An-Nahl', 'verseCount': 128, 'revelationType': 'Meccan'},
    {'number': 17, 'nameArabic': 'الإسراء', 'nameEnglish': 'Al-Isra', 'nameTransliteration': 'Al-Isra', 'verseCount': 111, 'revelationType': 'Meccan'},
    {'number': 18, 'nameArabic': 'الكهف', 'nameEnglish': 'Al-Kahf', 'nameTransliteration': 'Al-Kahf', 'verseCount': 110, 'revelationType': 'Meccan'},
    {'number': 19, 'nameArabic': 'مريم', 'nameEnglish': 'Maryam', 'nameTransliteration': 'Maryam', 'verseCount': 98, 'revelationType': 'Meccan'},
    {'number': 20, 'nameArabic': 'طه', 'nameEnglish': 'Taha', 'nameTransliteration': 'Taha', 'verseCount': 135, 'revelationType': 'Meccan'},
    {'number': 21, 'nameArabic': 'الأنبياء', 'nameEnglish': 'Al-Anbya', 'nameTransliteration': 'Al-Anbiyaa', 'verseCount': 112, 'revelationType': 'Meccan'},
    {'number': 22, 'nameArabic': 'الحج', 'nameEnglish': 'Al-Hajj', 'nameTransliteration': 'Al-Hajj', 'verseCount': 78, 'revelationType': 'Medinan'},
    {'number': 23, 'nameArabic': 'المؤمنون', 'nameEnglish': 'Al-Mu\'minun', 'nameTransliteration': 'Al-Muminoon', 'verseCount': 118, 'revelationType': 'Meccan'},
    {'number': 24, 'nameArabic': 'النور', 'nameEnglish': 'An-Nur', 'nameTransliteration': 'An-Noor', 'verseCount': 64, 'revelationType': 'Medinan'},
    {'number': 25, 'nameArabic': 'الفرقان', 'nameEnglish': 'Al-Furqan', 'nameTransliteration': 'Al-Furqaan', 'verseCount': 77, 'revelationType': 'Meccan'},
    {'number': 26, 'nameArabic': 'الشعراء', 'nameEnglish': 'Ash-Shu\'ara', 'nameTransliteration': 'Ash-Shu\'araa', 'verseCount': 227, 'revelationType': 'Meccan'},
    {'number': 27, 'nameArabic': 'النمل', 'nameEnglish': 'An-Naml', 'nameTransliteration': 'An-Naml', 'verseCount': 93, 'revelationType': 'Meccan'},
    {'number': 28, 'nameArabic': 'القصص', 'nameEnglish': 'Al-Qasas', 'nameTransliteration': 'Al-Qasas', 'verseCount': 88, 'revelationType': 'Meccan'},
    {'number': 29, 'nameArabic': 'العنكبوت', 'nameEnglish': 'Al-\'Ankabut', 'nameTransliteration': 'Al-Ankaboot', 'verseCount': 69, 'revelationType': 'Meccan'},
    {'number': 30, 'nameArabic': 'الروم', 'nameEnglish': 'Ar-Rum', 'nameTransliteration': 'Ar-Room', 'verseCount': 60, 'revelationType': 'Meccan'},
    {'number': 31, 'nameArabic': 'لقمان', 'nameEnglish': 'Luqman', 'nameTransliteration': 'Luqman', 'verseCount': 34, 'revelationType': 'Meccan'},
    {'number': 32, 'nameArabic': 'السجدة', 'nameEnglish': 'As-Sajdah', 'nameTransliteration': 'As-Sajda', 'verseCount': 30, 'revelationType': 'Meccan'},
    {'number': 33, 'nameArabic': 'الأحزاب', 'nameEnglish': 'Al-Ahzab', 'nameTransliteration': 'Al-Ahzaab', 'verseCount': 73, 'revelationType': 'Medinan'},
    {'number': 34, 'nameArabic': 'سبأ', 'nameEnglish': 'Saba', 'nameTransliteration': 'Saba', 'verseCount': 54, 'revelationType': 'Meccan'},
    {'number': 35, 'nameArabic': 'فاطر', 'nameEnglish': 'Fatir', 'nameTransliteration': 'Faatir', 'verseCount': 45, 'revelationType': 'Meccan'},
    {'number': 36, 'nameArabic': 'يس', 'nameEnglish': 'Ya-Sin', 'nameTransliteration': 'Yaseen', 'verseCount': 83, 'revelationType': 'Meccan'},
    {'number': 37, 'nameArabic': 'الصافات', 'nameEnglish': 'As-Saffat', 'nameTransliteration': 'As-Saaffaat', 'verseCount': 182, 'revelationType': 'Meccan'},
    {'number': 38, 'nameArabic': 'ص', 'nameEnglish': 'Sad', 'nameTransliteration': 'Saad', 'verseCount': 88, 'revelationType': 'Meccan'},
    {'number': 39, 'nameArabic': 'الزمر', 'nameEnglish': 'Az-Zumar', 'nameTransliteration': 'Az-Zumar', 'verseCount': 75, 'revelationType': 'Meccan'},
    {'number': 40, 'nameArabic': 'غافر', 'nameEnglish': 'Ghafir', 'nameTransliteration': 'Ghaafir', 'verseCount': 85, 'revelationType': 'Meccan'},
    {'number': 41, 'nameArabic': 'فصلت', 'nameEnglish': 'Fussilat', 'nameTransliteration': 'Fussilat', 'verseCount': 54, 'revelationType': 'Meccan'},
    {'number': 42, 'nameArabic': 'الشورى', 'nameEnglish': 'Ash-Shuraa', 'nameTransliteration': 'Ash-Shura', 'verseCount': 53, 'revelationType': 'Meccan'},
    {'number': 43, 'nameArabic': 'الزخرف', 'nameEnglish': 'Az-Zukhruf', 'nameTransliteration': 'Az-Zukhruf', 'verseCount': 89, 'revelationType': 'Meccan'},
    {'number': 44, 'nameArabic': 'الدخان', 'nameEnglish': 'Ad-Dukhan', 'nameTransliteration': 'Ad-Dukhaan', 'verseCount': 59, 'revelationType': 'Meccan'},
    {'number': 45, 'nameArabic': 'الجاثية', 'nameEnglish': 'Al-Jathiyah', 'nameTransliteration': 'Al-Jaathiya', 'verseCount': 37, 'revelationType': 'Meccan'},
    {'number': 46, 'nameArabic': 'الأحقاف', 'nameEnglish': 'Al-Ahqaf', 'nameTransliteration': 'Al-Ahqaaf', 'verseCount': 35, 'revelationType': 'Meccan'},
    {'number': 47, 'nameArabic': 'محمد', 'nameEnglish': 'Muhammad', 'nameTransliteration': 'Muhammad', 'verseCount': 38, 'revelationType': 'Medinan'},
    {'number': 48, 'nameArabic': 'الفتح', 'nameEnglish': 'Al-Fath', 'nameTransliteration': 'Al-Fath', 'verseCount': 29, 'revelationType': 'Medinan'},
    {'number': 49, 'nameArabic': 'الحجرات', 'nameEnglish': 'Al-Hujurat', 'nameTransliteration': 'Al-Hujuraat', 'verseCount': 18, 'revelationType': 'Medinan'},
    {'number': 50, 'nameArabic': 'ق', 'nameEnglish': 'Qaf', 'nameTransliteration': 'Qaaf', 'verseCount': 45, 'revelationType': 'Meccan'},
    {'number': 51, 'nameArabic': 'الذاريات', 'nameEnglish': 'Adh-Dhariyat', 'nameTransliteration': 'Adh-Dhaariyat', 'verseCount': 60, 'revelationType': 'Meccan'},
    {'number': 52, 'nameArabic': 'الطور', 'nameEnglish': 'At-Tur', 'nameTransliteration': 'At-Toor', 'verseCount': 49, 'revelationType': 'Meccan'},
    {'number': 53, 'nameArabic': 'النجم', 'nameEnglish': 'An-Najm', 'nameTransliteration': 'An-Najm', 'verseCount': 62, 'revelationType': 'Meccan'},
    {'number': 54, 'nameArabic': 'القمر', 'nameEnglish': 'Al-Qamar', 'nameTransliteration': 'Al-Qamar', 'verseCount': 55, 'revelationType': 'Meccan'},
    {'number': 55, 'nameArabic': 'الرحمن', 'nameEnglish': 'Ar-Rahman', 'nameTransliteration': 'Ar-Rahmaan', 'verseCount': 78, 'revelationType': 'Meccan'},
    {'number': 56, 'nameArabic': 'الواقعة', 'nameEnglish': 'Al-Waqi\'ah', 'nameTransliteration': 'Al-Waaqi\'a', 'verseCount': 96, 'revelationType': 'Meccan'},
    {'number': 57, 'nameArabic': 'الحديد', 'nameEnglish': 'Al-Hadid', 'nameTransliteration': 'Al-Hadeed', 'verseCount': 29, 'revelationType': 'Medinan'},
    {'number': 58, 'nameArabic': 'المجادلة', 'nameEnglish': 'Al-Mujadila', 'nameTransliteration': 'Al-Mujaadila', 'verseCount': 22, 'revelationType': 'Medinan'},
    {'number': 59, 'nameArabic': 'الحشر', 'nameEnglish': 'Al-Hashr', 'nameTransliteration': 'Al-Hashr', 'verseCount': 24, 'revelationType': 'Medinan'},
    {'number': 60, 'nameArabic': 'الممتحنة', 'nameEnglish': 'Al-Mumtahanah', 'nameTransliteration': 'Al-Mumtahana', 'verseCount': 13, 'revelationType': 'Medinan'},
    {'number': 61, 'nameArabic': 'الصف', 'nameEnglish': 'As-Saff', 'nameTransliteration': 'As-Saff', 'verseCount': 14, 'revelationType': 'Medinan'},
    {'number': 62, 'nameArabic': 'الجمعة', 'nameEnglish': 'Al-Jumu\'ah', 'nameTransliteration': 'Al-Jumu\'a', 'verseCount': 11, 'revelationType': 'Medinan'},
    {'number': 63, 'nameArabic': 'المنافقون', 'nameEnglish': 'Al-Munafiqun', 'nameTransliteration': 'Al-Munaafiqoon', 'verseCount': 11, 'revelationType': 'Medinan'},
    {'number': 64, 'nameArabic': 'التغابن', 'nameEnglish': 'At-Taghabun', 'nameTransliteration': 'At-Taghaabun', 'verseCount': 18, 'revelationType': 'Medinan'},
    {'number': 65, 'nameArabic': 'الطلاق', 'nameEnglish': 'At-Talaq', 'nameTransliteration': 'At-Talaaq', 'verseCount': 12, 'revelationType': 'Medinan'},
    {'number': 66, 'nameArabic': 'التحريم', 'nameEnglish': 'At-Tahrim', 'nameTransliteration': 'At-Tahreem', 'verseCount': 12, 'revelationType': 'Medinan'},
    {'number': 67, 'nameArabic': 'الملك', 'nameEnglish': 'Al-Mulk', 'nameTransliteration': 'Al-Mulk', 'verseCount': 30, 'revelationType': 'Meccan'},
    {'number': 68, 'nameArabic': 'القلم', 'nameEnglish': 'Al-Qalam', 'nameTransliteration': 'Al-Qalam', 'verseCount': 52, 'revelationType': 'Meccan'},
    {'number': 69, 'nameArabic': 'الحاقة', 'nameEnglish': 'Al-Haqqah', 'nameTransliteration': 'Al-Haaqqah', 'verseCount': 52, 'revelationType': 'Meccan'},
    {'number': 70, 'nameArabic': 'المعارج', 'nameEnglish': 'Al-Ma\'arij', 'nameTransliteration': 'Al-Ma\'aarij', 'verseCount': 44, 'revelationType': 'Meccan'},
    {'number': 71, 'nameArabic': 'نوح', 'nameEnglish': 'Nuh', 'nameTransliteration': 'Nooh', 'verseCount': 28, 'revelationType': 'Meccan'},
    {'number': 72, 'nameArabic': 'الجن', 'nameEnglish': 'Al-Jinn', 'nameTransliteration': 'Al-Jinn', 'verseCount': 28, 'revelationType': 'Meccan'},
    {'number': 73, 'nameArabic': 'المزمل', 'nameEnglish': 'Al-Muzzammil', 'nameTransliteration': 'Al-Muzzammil', 'verseCount': 20, 'revelationType': 'Meccan'},
    {'number': 74, 'nameArabic': 'المدثر', 'nameEnglish': 'Al-Muddaththir', 'nameTransliteration': 'Al-Muddaththir', 'verseCount': 56, 'revelationType': 'Meccan'},
    {'number': 75, 'nameArabic': 'القيامة', 'nameEnglish': 'Al-Qiyamah', 'nameTransliteration': 'Al-Qiyaamah', 'verseCount': 40, 'revelationType': 'Meccan'},
    {'number': 76, 'nameArabic': 'الإنسان', 'nameEnglish': 'Al-Insan', 'nameTransliteration': 'Al-Insaan', 'verseCount': 31, 'revelationType': 'Medinan'},
    {'number': 77, 'nameArabic': 'المرسلات', 'nameEnglish': 'Al-Mursalat', 'nameTransliteration': 'Al-Mursalaat', 'verseCount': 50, 'revelationType': 'Meccan'},
    {'number': 78, 'nameArabic': 'النبأ', 'nameEnglish': 'An-Naba', 'nameTransliteration': 'An-Naba', 'verseCount': 40, 'revelationType': 'Meccan'},
    {'number': 79, 'nameArabic': 'النازعات', 'nameEnglish': 'An-Nazi\'at', 'nameTransliteration': 'An-Naazi\'aat', 'verseCount': 46, 'revelationType': 'Meccan'},
    {'number': 80, 'nameArabic': 'عبس', 'nameEnglish': 'Abasa', 'nameTransliteration': 'Abasa', 'verseCount': 42, 'revelationType': 'Meccan'},
    {'number': 81, 'nameArabic': 'التكوير', 'nameEnglish': 'At-Takwir', 'nameTransliteration': 'At-Takweer', 'verseCount': 29, 'revelationType': 'Meccan'},
    {'number': 82, 'nameArabic': 'الانفطار', 'nameEnglish': 'Al-Infitar', 'nameTransliteration': 'Al-Infitaar', 'verseCount': 19, 'revelationType': 'Meccan'},
    {'number': 83, 'nameArabic': 'المطففين', 'nameEnglish': 'Al-Mutaffifin', 'nameTransliteration': 'Al-Mutaffifeen', 'verseCount': 36, 'revelationType': 'Meccan'},
    {'number': 84, 'nameArabic': 'الانشقاق', 'nameEnglish': 'Al-Inshiqaq', 'nameTransliteration': 'Al-Inshiqaaq', 'verseCount': 25, 'revelationType': 'Meccan'},
    {'number': 85, 'nameArabic': 'البروج', 'nameEnglish': 'Al-Buruj', 'nameTransliteration': 'Al-Burooj', 'verseCount': 22, 'revelationType': 'Meccan'},
    {'number': 86, 'nameArabic': 'الطارق', 'nameEnglish': 'At-Tariq', 'nameTransliteration': 'At-Taariq', 'verseCount': 17, 'revelationType': 'Meccan'},
    {'number': 87, 'nameArabic': 'الأعلى', 'nameEnglish': 'Al-A\'la', 'nameTransliteration': 'Al-A\'laa', 'verseCount': 19, 'revelationType': 'Meccan'},
    {'number': 88, 'nameArabic': 'الغاشية', 'nameEnglish': 'Al-Ghashiyah', 'nameTransliteration': 'Al-Ghaashiya', 'verseCount': 26, 'revelationType': 'Meccan'},
    {'number': 89, 'nameArabic': 'الفجر', 'nameEnglish': 'Al-Fajr', 'nameTransliteration': 'Al-Fajr', 'verseCount': 30, 'revelationType': 'Meccan'},
    {'number': 90, 'nameArabic': 'البلد', 'nameEnglish': 'Al-Balad', 'nameTransliteration': 'Al-Balad', 'verseCount': 20, 'revelationType': 'Meccan'},
    {'number': 91, 'nameArabic': 'الشمس', 'nameEnglish': 'Ash-Shams', 'nameTransliteration': 'Ash-Shams', 'verseCount': 15, 'revelationType': 'Meccan'},
    {'number': 92, 'nameArabic': 'الليل', 'nameEnglish': 'Al-Layl', 'nameTransliteration': 'Al-Layl', 'verseCount': 21, 'revelationType': 'Meccan'},
    {'number': 93, 'nameArabic': 'الضحى', 'nameEnglish': 'Ad-Duhaa', 'nameTransliteration': 'Ad-Duhaa', 'verseCount': 11, 'revelationType': 'Meccan'},
    {'number': 94, 'nameArabic': 'الشرح', 'nameEnglish': 'Ash-Sharh', 'nameTransliteration': 'Ash-Sharh', 'verseCount': 8, 'revelationType': 'Meccan'},
    {'number': 95, 'nameArabic': 'التين', 'nameEnglish': 'At-Tin', 'nameTransliteration': 'At-Teen', 'verseCount': 8, 'revelationType': 'Meccan'},
    {'number': 96, 'nameArabic': 'العلق', 'nameEnglish': 'Al-\'Alaq', 'nameTransliteration': 'Al-\'Alaq', 'verseCount': 19, 'revelationType': 'Meccan'},
    {'number': 97, 'nameArabic': 'القدر', 'nameEnglish': 'Al-Qadr', 'nameTransliteration': 'Al-Qadr', 'verseCount': 5, 'revelationType': 'Meccan'},
    {'number': 98, 'nameArabic': 'البينة', 'nameEnglish': 'Al-Bayyinah', 'nameTransliteration': 'Al-Bayyina', 'verseCount': 8, 'revelationType': 'Medinan'},
    {'number': 99, 'nameArabic': 'الزلزلة', 'nameEnglish': 'Az-Zalzalah', 'nameTransliteration': 'Az-Zalzala', 'verseCount': 8, 'revelationType': 'Medinan'},
    {'number': 100, 'nameArabic': 'العاديات', 'nameEnglish': 'Al-\'Adiyat', 'nameTransliteration': 'Al-\'Aadiyaat', 'verseCount': 11, 'revelationType': 'Meccan'},
    {'number': 101, 'nameArabic': 'القارعة', 'nameEnglish': 'Al-Qari\'ah', 'nameTransliteration': 'Al-Qaari\'a', 'verseCount': 11, 'revelationType': 'Meccan'},
    {'number': 102, 'nameArabic': 'التكاثر', 'nameEnglish': 'At-Takathur', 'nameTransliteration': 'At-Takaathur', 'verseCount': 8, 'revelationType': 'Meccan'},
    {'number': 103, 'nameArabic': 'العصر', 'nameEnglish': 'Al-\'Asr', 'nameTransliteration': 'Al-\'Asr', 'verseCount': 3, 'revelationType': 'Meccan'},
    {'number': 104, 'nameArabic': 'الهمزة', 'nameEnglish': 'Al-Humazah', 'nameTransliteration': 'Al-Humaza', 'verseCount': 9, 'revelationType': 'Meccan'},
    {'number': 105, 'nameArabic': 'الفيل', 'nameEnglish': 'Al-Fil', 'nameTransliteration': 'Al-Feel', 'verseCount': 5, 'revelationType': 'Meccan'},
    {'number': 106, 'nameArabic': 'قريش', 'nameEnglish': 'Quraysh', 'nameTransliteration': 'Quraysh', 'verseCount': 4, 'revelationType': 'Meccan'},
    {'number': 107, 'nameArabic': 'الماعون', 'nameEnglish': 'Al-Ma\'un', 'nameTransliteration': 'Al-Maa\'oon', 'verseCount': 7, 'revelationType': 'Meccan'},
    {'number': 108, 'nameArabic': 'الكوثر', 'nameEnglish': 'Al-Kawthar', 'nameTransliteration': 'Al-Kawthar', 'verseCount': 3, 'revelationType': 'Meccan'},
    {'number': 109, 'nameArabic': 'الكافرون', 'nameEnglish': 'Al-Kafirun', 'nameTransliteration': 'Al-Kaafiroon', 'verseCount': 6, 'revelationType': 'Meccan'},
    {'number': 110, 'nameArabic': 'النصر', 'nameEnglish': 'An-Nasr', 'nameTransliteration': 'An-Nasr', 'verseCount': 3, 'revelationType': 'Medinan'},
    {'number': 111, 'nameArabic': 'المسد', 'nameEnglish': 'Al-Masad', 'nameTransliteration': 'Al-Masad', 'verseCount': 5, 'revelationType': 'Meccan'},
    {'number': 112, 'nameArabic': 'الإخلاص', 'nameEnglish': 'Al-Ikhlas', 'nameTransliteration': 'Al-Ikhlaas', 'verseCount': 4, 'revelationType': 'Meccan'},
    {'number': 113, 'nameArabic': 'الفلق', 'nameEnglish': 'Al-Falaq', 'nameTransliteration': 'Al-Falaq', 'verseCount': 5, 'revelationType': 'Meccan'},
    {'number': 114, 'nameArabic': 'الناس', 'nameEnglish': 'An-Nas', 'nameTransliteration': 'An-Naas', 'verseCount': 6, 'revelationType': 'Meccan'},
  ];

  static const Map<int, List<Map<String, dynamic>>> verses = {
    1: [
      {
        'verseNumber': 1,
        'textArabic': 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        'textEnglish': 'In the name of Allah, the Entirely Merciful, the Especially Merciful.',
        'textTransliteration': 'Bismillaahir Rahmaanir Raheem',
      },
      {
        'verseNumber': 2,
        'textArabic': 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
        'textEnglish': '[All] praise is [due] to Allah, Lord of the worlds -',
        'textTransliteration': 'Alhamdu lillaahi Rabbil aalameen',
      },
      {
        'verseNumber': 3,
        'textArabic': 'الرَّحْمَٰنِ الرَّحِيمِ',
        'textEnglish': 'The Entirely Merciful, the Especially Merciful,',
        'textTransliteration': 'Ar-Rahmaanir-Raheem',
      },
      {
        'verseNumber': 4,
        'textArabic': 'مَالِكِ يَوْمِ الدِّينِ',
        'textEnglish': 'Sovereign of the Day of Recompense.',
        'textTransliteration': 'Maaliki Yawmid-Deen',
      },
      {
        'verseNumber': 5,
        'textArabic': 'إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ',
        'textEnglish': 'It is You we worship and You we ask for help.',
        'textTransliteration': 'Iyyaaka na\'budu wa iyyaaka nasta\'een',
      },
      {
        'verseNumber': 6,
        'textArabic': 'اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ',
        'textEnglish': 'Guide us to the straight path -',
        'textTransliteration': 'Ihdinas-Siraatal-Mustaqeem',
      },
      {
        'verseNumber': 7,
        'textArabic': 'صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ',
        'textEnglish': 'The path of those upon whom You have bestowed favor, not of those who have evoked [Your] anger or of those who are astray.',
        'textTransliteration': 'Siraatal-lazeena an\'amta \'alayhim ghayril-maghdoobi \'alayhim wa lad-daaalleen',
      },
    ],
  };
}
