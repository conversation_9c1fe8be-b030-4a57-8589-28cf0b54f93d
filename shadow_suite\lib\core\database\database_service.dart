import 'dart:async';
import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../services/error_handler.dart' as error_handler;
import 'database_initializer.dart';

class DatabaseService {
  static Database? _database;
  static const String _databaseName = 'shadow_suite.db';
  static const int _databaseVersion = 1;

  // Initialize database with error handling
  static Future<Database> get database async {
    if (_database != null) return _database!;

    try {
      _database = await _initDatabase();
      return _database!;
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Initialize database',
      );
      rethrow;
    }
  }

  // Initialize database
  static Future<Database> _initDatabase() async {
    try {
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, _databaseName);

      // Ensure directory exists
      final directory = Directory(dirname(path));
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      // Use safe database opening with proper initialization
      final db = await DatabaseInitializer.safeOpenDatabase(
        path,
        version: _databaseVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
        onOpen: _onOpen,
        onConfigure: _onConfigure,
      );

      if (db == null) {
        throw Exception('Failed to initialize Shadow Suite database');
      }

      return db;
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Open database',
      );
      rethrow;
    }
  }

  // Configure database settings
  static Future<void> _onConfigure(Database db) async {
    try {
      // Enable foreign key constraints
      await db.execute('PRAGMA foreign_keys = ON');

      // Set journal mode for better performance
      await db.execute('PRAGMA journal_mode = WAL');

      // Set synchronous mode for better performance
      await db.execute('PRAGMA synchronous = NORMAL');
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Configure database',
      );
      // Don't rethrow here as this is not critical
    }
  }

  // Create database tables
  static Future<void> _onCreate(Database db, int version) async {
    try {
      await _createTables(db);
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Create database tables',
      );
      rethrow;
    }
  }

  // Upgrade database schema
  static Future<void> _onUpgrade(
    Database db,
    int oldVersion,
    int newVersion,
  ) async {
    try {
      // Handle database migrations here
      for (int version = oldVersion + 1; version <= newVersion; version++) {
        await _migrateToVersion(db, version);
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Upgrade database',
      );
      rethrow;
    }
  }

  // Database opened callback
  static Future<void> _onOpen(Database db) async {
    try {
      // Verify database integrity
      final result = await db.rawQuery('PRAGMA integrity_check');
      if (result.isNotEmpty && result.first['integrity_check'] != 'ok') {
        throw error_handler.DatabaseException(
          'Database integrity check failed',
          null,
          result,
        );
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Verify database integrity',
      );
      // Don't rethrow here as this is not critical for app startup
    }
  }

  // Create all tables
  static Future<void> _createTables(Database db) async {
    // Money Manager tables
    await db.execute('''
      CREATE TABLE accounts (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        initial_balance REAL NOT NULL DEFAULT 0,
        current_balance REAL NOT NULL DEFAULT 0,
        currency TEXT NOT NULL DEFAULT 'USD',
        color TEXT NOT NULL,
        icon TEXT NOT NULL,
        show_in_total INTEGER NOT NULL DEFAULT 1,
        allow_negative INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        last_modified TEXT NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE categories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        color TEXT NOT NULL,
        icon TEXT NOT NULL,
        parent_id TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (parent_id) REFERENCES categories (id) ON DELETE SET NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE transactions (
        id TEXT PRIMARY KEY,
        account_id TEXT NOT NULL,
        category_id TEXT NOT NULL,
        amount REAL NOT NULL,
        description TEXT NOT NULL,
        date TEXT NOT NULL,
        type TEXT NOT NULL,
        created_at TEXT NOT NULL,
        last_modified TEXT NOT NULL,
        FOREIGN KEY (account_id) REFERENCES accounts (id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE RESTRICT
      )
    ''');

    // Islamic App tables
    await db.execute('''
      CREATE TABLE athkar (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        arabic_text TEXT NOT NULL,
        transliteration TEXT,
        translation TEXT NOT NULL,
        category TEXT NOT NULL,
        count INTEGER NOT NULL DEFAULT 1,
        source TEXT,
        created_at TEXT NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE dhikr_sessions (
        id TEXT PRIMARY KEY,
        athkar_id TEXT NOT NULL,
        count INTEGER NOT NULL,
        target_count INTEGER NOT NULL,
        started_at TEXT NOT NULL,
        completed_at TEXT,
        FOREIGN KEY (athkar_id) REFERENCES athkar (id) ON DELETE CASCADE
      )
    ''');

    // Memo Suite tables
    await db.execute('''
      CREATE TABLE voice_memos (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        file_path TEXT NOT NULL,
        duration INTEGER NOT NULL,
        category TEXT,
        tags TEXT,
        transcription TEXT,
        created_at TEXT NOT NULL,
        last_modified TEXT NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE text_notes (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        category TEXT,
        tags TEXT,
        created_at TEXT NOT NULL,
        last_modified TEXT NOT NULL
      )
    ''');

    // Excel to App tables
    await db.execute('''
      CREATE TABLE excel_tools (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        spreadsheet_data TEXT,
        ui_components TEXT,
        created_at TEXT NOT NULL,
        last_modified TEXT NOT NULL
      )
    ''');

    // Settings table
    await db.execute('''
      CREATE TABLE settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        category TEXT NOT NULL,
        last_modified TEXT NOT NULL
      )
    ''');

    // Create indexes for better performance
    await _createIndexes(db);
  }

  // Create database indexes
  static Future<void> _createIndexes(Database db) async {
    await db.execute(
      'CREATE INDEX idx_transactions_account_id ON transactions(account_id)',
    );
    await db.execute(
      'CREATE INDEX idx_transactions_category_id ON transactions(category_id)',
    );
    await db.execute(
      'CREATE INDEX idx_transactions_date ON transactions(date)',
    );
    await db.execute(
      'CREATE INDEX idx_voice_memos_category ON voice_memos(category)',
    );
    await db.execute(
      'CREATE INDEX idx_text_notes_category ON text_notes(category)',
    );
    await db.execute(
      'CREATE INDEX idx_settings_category ON settings(category)',
    );
  }

  // Handle database migrations
  static Future<void> _migrateToVersion(Database db, int version) async {
    switch (version) {
      case 2:
        // Example migration for version 2
        // await db.execute('ALTER TABLE accounts ADD COLUMN new_field TEXT');
        break;
      // Add more migration cases as needed
    }
  }

  // Safe query execution with error handling
  static Future<List<Map<String, dynamic>>> safeQuery(
    String sql, [
    List<dynamic>? arguments,
  ]) async {
    try {
      final db = await database;
      return await db.rawQuery(sql, arguments);
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Query: $sql',
      );
      return [];
    }
  }

  // Safe insert with error handling
  static Future<int?> safeInsert(
    String table,
    Map<String, dynamic> values, {
    String? nullColumnHack,
    ConflictAlgorithm? conflictAlgorithm,
  }) async {
    try {
      final db = await database;
      return await db.insert(
        table,
        values,
        nullColumnHack: nullColumnHack,
        conflictAlgorithm: conflictAlgorithm,
      );
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Insert into $table',
        table: table,
      );
      return null;
    }
  }

  // Safe update with error handling
  static Future<int?> safeUpdate(
    String table,
    Map<String, dynamic> values, {
    String? where,
    List<dynamic>? whereArgs,
    ConflictAlgorithm? conflictAlgorithm,
  }) async {
    try {
      final db = await database;
      return await db.update(
        table,
        values,
        where: where,
        whereArgs: whereArgs,
        conflictAlgorithm: conflictAlgorithm,
      );
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Update $table',
        table: table,
      );
      return null;
    }
  }

  // Safe delete with error handling
  static Future<int?> safeDelete(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    try {
      final db = await database;
      return await db.delete(table, where: where, whereArgs: whereArgs);
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Delete from $table',
        table: table,
      );
      return null;
    }
  }

  // Safe execute with error handling
  static Future<void> safeExecute(
    String sql, [
    List<dynamic>? arguments,
  ]) async {
    try {
      final db = await database;
      await db.execute(sql, arguments);
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Execute SQL',
      );
    }
  }

  // Execute transaction with error handling
  static Future<T?> safeTransaction<T>(
    Future<T> Function(Transaction txn) action,
  ) async {
    try {
      final db = await database;
      return await db.transaction(action);
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Database transaction',
      );
      return null;
    }
  }

  // Backup database
  static Future<bool> backupDatabase(String backupPath) async {
    try {
      final db = await database;
      final databasePath = db.path;

      final sourceFile = File(databasePath);
      final backupFile = File(backupPath);

      await sourceFile.copy(backupPath);

      return await backupFile.exists();
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Backup database',
      );
      return false;
    }
  }

  // Restore database from backup
  static Future<bool> restoreDatabase(String backupPath) async {
    try {
      final backupFile = File(backupPath);
      if (!await backupFile.exists()) {
        throw error_handler.DatabaseException(
          'Backup file not found',
          null,
          backupPath,
        );
      }

      // Close current database
      await _database?.close();
      _database = null;

      final databasesPath = await getDatabasesPath();
      final databasePath = join(databasesPath, _databaseName);

      await backupFile.copy(databasePath);

      // Reinitialize database
      _database = await _initDatabase();

      return true;
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Restore database',
      );
      return false;
    }
  }

  // Get database size
  static Future<int> getDatabaseSize() async {
    try {
      final db = await database;
      final file = File(db.path);
      return await file.length();
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Get database size',
      );
      return 0;
    }
  }

  // Vacuum database to reclaim space
  static Future<bool> vacuumDatabase() async {
    try {
      final db = await database;
      await db.execute('VACUUM');
      return true;
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Vacuum database',
      );
      return false;
    }
  }

  // Close database
  static Future<void> closeDatabase() async {
    try {
      await _database?.close();
      _database = null;
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Close database',
      );
    }
  }

  // Delete database
  static Future<bool> deleteDatabase() async {
    try {
      await closeDatabase();

      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, _databaseName);

      final file = File(path);
      if (await file.exists()) {
        await file.delete();
      }

      return true;
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Delete database',
      );
      return false;
    }
  }

  // Optimize database
  static Future<void> optimizeDatabase() async {
    try {
      final db = await database;

      // Vacuum the database to reclaim space
      await db.execute('VACUUM');

      // Analyze tables for query optimization
      await db.execute('ANALYZE');

      // Reindex all tables
      await db.execute('REINDEX');
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(
        error,
        operation: 'Optimize database',
      );
    }
  }
}
