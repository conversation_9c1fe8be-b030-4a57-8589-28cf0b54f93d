// Verse Model for Quran verses
class Verse {
  final int surahNumber;
  final int verseNumber;
  final String textArabic;
  final String textEnglish;
  final String textTransliteration;
  final int juzNumber;
  final int hizbNumber;
  final int rukuNumber;

  Verse({
    required this.surahNumber,
    required this.verseNumber,
    required this.textArabic,
    required this.textEnglish,
    required this.textTransliteration,
    this.juzNumber = 1,
    this.hizbNumber = 1,
    this.rukuNumber = 1,
  });

  Map<String, dynamic> toMap() {
    return {
      'surahNumber': surahNumber,
      'verseNumber': verseNumber,
      'textArabic': textArabic,
      'textEnglish': textEnglish,
      'textTransliteration': textTransliteration,
      'juzNumber': juzNumber,
      'hizbNumber': hizbNumber,
      'rukuNumber': rukuNumber,
    };
  }

  factory Verse.fromMap(Map<String, dynamic> map) {
    return Verse(
      surahNumber: map['surahNumber'],
      verseNumber: map['verseNumber'],
      textArabic: map['textArabic'],
      textEnglish: map['textEnglish'],
      textTransliteration: map['textTransliteration'],
      juzNumber: map['juzNumber'] ?? 1,
      hizbNumber: map['hizbNumber'] ?? 1,
      rukuNumber: map['rukuNumber'] ?? 1,
    );
  }

  String get verseReference => '$surahNumber:$verseNumber';

  @override
  String toString() {
    return 'Verse($verseReference: ${textArabic.substring(0, 20)}...)';
  }
}
