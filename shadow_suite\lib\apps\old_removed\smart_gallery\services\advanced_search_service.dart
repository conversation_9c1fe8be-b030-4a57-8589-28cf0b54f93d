import 'dart:async';
import 'dart:math';
import '../models/smart_gallery_models.dart';
import '../../../core/database/database_service.dart';
import '../../../core/services/error_handler.dart' as error_handler;

/// Advanced search service with semantic AI-powered queries for SmartGallery+
class AdvancedSearchService {
  static bool _isInitialized = false;
  static final Map<String, List<String>> _searchIndex = {};
  static final Map<String, double> _termFrequency = {};
  static final Map<String, Set<String>> _invertedIndex = {};
  static Timer? _indexUpdateTimer;

  /// Initialize advanced search service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Build search index from existing media
      await _buildSearchIndex();

      // Start periodic index updates
      _startIndexUpdates();

      _isInitialized = true;
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.operation,
        context: 'Initialize advanced search service',
      );
    }
  }

  /// Build search index from media items
  static Future<void> _buildSearchIndex() async {
    try {
      // Clear existing index
      _searchIndex.clear();
      _termFrequency.clear();
      _invertedIndex.clear();

      // Load all media items
      final results = await DatabaseService.safeQuery(
        'SELECT id, name, ai_tags, custom_tags, ocr_text FROM smart_gallery_items',
      );

      for (final row in results) {
        final mediaId = row['id'] as String;
        await _indexMediaItem(mediaId, row);
      }

      // Calculate TF-IDF scores
      await _calculateTFIDFScores();
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.operation,
        context: 'Build search index',
      );
    }
  }

  /// Index individual media item
  static Future<void> _indexMediaItem(
    String mediaId,
    Map<String, dynamic> data,
  ) async {
    final searchableText = <String>[];

    // Add filename (without extension)
    final name = data['name'] as String? ?? '';
    searchableText.addAll(_extractKeywords(name));

    // Add AI tags
    final aiTags = data['ai_tags'] as String? ?? '';
    if (aiTags.isNotEmpty) {
      searchableText.addAll(
        aiTags.split(',').where((tag) => tag.trim().isNotEmpty),
      );
    }

    // Add custom tags
    final customTags = data['custom_tags'] as String? ?? '';
    if (customTags.isNotEmpty) {
      searchableText.addAll(
        customTags.split(',').where((tag) => tag.trim().isNotEmpty),
      );
    }

    // Add OCR text
    final ocrText = data['ocr_text'] as String? ?? '';
    if (ocrText.isNotEmpty) {
      searchableText.addAll(_extractKeywords(ocrText));
    }

    // Store in search index
    _searchIndex[mediaId] = searchableText
        .map((term) => term.toLowerCase().trim())
        .toList();

    // Update inverted index
    for (final term in _searchIndex[mediaId]!) {
      _invertedIndex.putIfAbsent(term, () => {}).add(mediaId);
    }
  }

  /// Extract keywords from text
  static List<String> _extractKeywords(String text) {
    // Remove special characters and split by whitespace
    final words = text
        .replaceAll(RegExp(r'[^\w\s]'), ' ')
        .split(RegExp(r'\s+'))
        .where((word) => word.length > 2)
        .toList();

    // Remove common stop words
    final stopWords = {
      'the',
      'and',
      'or',
      'but',
      'in',
      'on',
      'at',
      'to',
      'for',
      'of',
      'with',
      'by',
      'from',
      'up',
      'about',
      'into',
      'through',
      'during',
      'before',
      'after',
      'above',
      'below',
      'between',
      'among',
      'this',
      'that',
      'these',
      'those',
      'is',
      'are',
      'was',
      'were',
      'be',
      'been',
      'being',
      'have',
      'has',
      'had',
      'do',
      'does',
      'did',
      'will',
      'would',
      'could',
      'should',
    };

    return words
        .where((word) => !stopWords.contains(word.toLowerCase()))
        .toList();
  }

  /// Calculate TF-IDF scores
  static Future<void> _calculateTFIDFScores() async {
    final totalDocuments = _searchIndex.length;

    for (final entry in _searchIndex.entries) {
      final mediaId = entry.key;
      final terms = entry.value;

      // Calculate term frequency for this document
      final termCount = <String, int>{};
      for (final term in terms) {
        termCount[term] = (termCount[term] ?? 0) + 1;
      }

      // Calculate TF-IDF for each term
      for (final termEntry in termCount.entries) {
        final term = termEntry.key;
        final count = termEntry.value;

        // Term Frequency (TF)
        final tf = count / terms.length;

        // Inverse Document Frequency (IDF)
        final documentsWithTerm = _invertedIndex[term]?.length ?? 1;
        final idf = log(totalDocuments / documentsWithTerm);

        // TF-IDF Score
        final tfidf = tf * idf;
        _termFrequency['${mediaId}_$term'] = tfidf;
      }
    }
  }

  /// Start periodic index updates
  static void _startIndexUpdates() {
    _indexUpdateTimer?.cancel();
    _indexUpdateTimer = Timer.periodic(const Duration(minutes: 10), (
      timer,
    ) async {
      await _updateSearchIndex();
    });
  }

  /// Update search index with new items
  static Future<void> _updateSearchIndex() async {
    try {
      // Get recently added items
      final recentItems = await DatabaseService.safeQuery(
        'SELECT id, name, ai_tags, custom_tags, ocr_text FROM smart_gallery_items WHERE id NOT IN (${_searchIndex.keys.map((_) => '?').join(',')})',
        _searchIndex.keys.toList(),
      );

      // Index new items
      for (final row in recentItems) {
        final mediaId = row['id'] as String;
        await _indexMediaItem(mediaId, row);
      }

      // Recalculate TF-IDF if new items were added
      if (recentItems.isNotEmpty) {
        await _calculateTFIDFScores();
      }
    } catch (e) {
      // Continue with existing index
    }
  }

  /// Perform semantic search
  static Future<List<SmartGallerySearchResult>> semanticSearch(
    String query, {
    SmartGalleryFilter? filter,
    int limit = 100,
  }) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // Extract query terms
      final queryTerms = _extractKeywords(
        query,
      ).map((term) => term.toLowerCase()).toList();
      if (queryTerms.isEmpty) return [];

      // Calculate relevance scores
      final scores = <String, double>{};

      for (final term in queryTerms) {
        final matchingItems = _invertedIndex[term] ?? {};

        for (final mediaId in matchingItems) {
          final tfidfKey = '${mediaId}_$term';
          final tfidfScore = _termFrequency[tfidfKey] ?? 0.0;
          scores[mediaId] = (scores[mediaId] ?? 0.0) + tfidfScore;
        }
      }

      // Apply semantic similarity boost
      await _applySemanticBoost(queryTerms, scores);

      // Sort by relevance score
      final sortedResults = scores.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      // Get media items and apply filters
      final results = <SmartGallerySearchResult>[];

      for (final entry in sortedResults.take(limit)) {
        final mediaId = entry.key;
        final score = entry.value;

        final mediaItem = await _getMediaItem(mediaId);
        if (mediaItem != null && _matchesFilter(mediaItem, filter)) {
          results.add(
            SmartGallerySearchResult(
              item: mediaItem,
              relevanceScore: score,
              matchedTerms: _getMatchedTerms(mediaItem, queryTerms),
              searchQuery: query,
            ),
          );
        }
      }

      return results;
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.operation,
        context: 'Semantic search failed',
      );
      return [];
    }
  }

  /// Apply semantic similarity boost using AI
  static Future<void> _applySemanticBoost(
    List<String> queryTerms,
    Map<String, double> scores,
  ) async {
    // Use AI to find semantically similar terms
    for (final mediaId in scores.keys) {
      final mediaTerms = _searchIndex[mediaId] ?? [];

      // Calculate semantic similarity between query and media terms
      final semanticScore = await _calculateSemanticSimilarity(
        queryTerms,
        mediaTerms,
      );

      // Boost score based on semantic similarity
      scores[mediaId] = (scores[mediaId] ?? 0.0) * (1.0 + semanticScore);
    }
  }

  /// Calculate semantic similarity between term sets
  static Future<double> _calculateSemanticSimilarity(
    List<String> queryTerms,
    List<String> mediaTerms,
  ) async {
    // Simple word embedding similarity (in production, use word2vec or similar)
    double totalSimilarity = 0.0;
    int comparisons = 0;

    for (final queryTerm in queryTerms) {
      for (final mediaTerm in mediaTerms) {
        final similarity = _calculateWordSimilarity(queryTerm, mediaTerm);
        totalSimilarity += similarity;
        comparisons++;
      }
    }

    return comparisons > 0 ? totalSimilarity / comparisons : 0.0;
  }

  /// Calculate word similarity using edit distance
  static double _calculateWordSimilarity(String word1, String word2) {
    if (word1 == word2) return 1.0;

    final distance = _levenshteinDistance(word1, word2);
    final maxLength = max(word1.length, word2.length);

    return maxLength > 0 ? 1.0 - (distance / maxLength) : 0.0;
  }

  /// Calculate Levenshtein distance
  static int _levenshteinDistance(String s1, String s2) {
    final matrix = List.generate(
      s1.length + 1,
      (i) => List.generate(s2.length + 1, (j) => 0),
    );

    for (int i = 0; i <= s1.length; i++) {
      matrix[i][0] = i;
    }

    for (int j = 0; j <= s2.length; j++) {
      matrix[0][j] = j;
    }

    for (int i = 1; i <= s1.length; i++) {
      for (int j = 1; j <= s2.length; j++) {
        final cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost,
        ].reduce(min);
      }
    }

    return matrix[s1.length][s2.length];
  }

  /// Get media item by ID
  static Future<SmartGalleryItem?> _getMediaItem(String mediaId) async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM smart_gallery_items WHERE id = ?',
        [mediaId],
      );

      if (results.isEmpty) return null;

      final row = results.first;
      return SmartGalleryItem(
        id: row['id'],
        path: row['path'],
        name: row['name'],
        type: MediaType.values[row['type']],
        size: row['size'],
        dateCreated: DateTime.fromMillisecondsSinceEpoch(row['date_created']),
        dateModified: DateTime.fromMillisecondsSinceEpoch(row['date_modified']),
        width: row['width'],
        height: row['height'],
        duration: row['duration'] != null
            ? Duration(milliseconds: row['duration'])
            : null,
        thumbnailPath: row['thumbnail_path'],
        isHidden: row['is_hidden'] == 1,
        isFavorite: row['is_favorite'] == 1,
        isLocked: row['is_locked'] == 1,
        aiTags:
            row['ai_tags']
                ?.split(',')
                .where((tag) => tag.isNotEmpty)
                .toList() ??
            [],
        customTags:
            row['custom_tags']
                ?.split(',')
                .where((tag) => tag.isNotEmpty)
                .toList() ??
            [],
        ocrText: row['ocr_text'],
      );
    } catch (e) {
      return null;
    }
  }

  /// Check if media item matches filter
  static bool _matchesFilter(
    SmartGalleryItem item,
    SmartGalleryFilter? filter,
  ) {
    if (filter == null) return true;

    // Media type filter
    if (filter.mediaType != null && item.type != filter.mediaType) {
      return false;
    }

    // Date range filter
    if (filter.dateRange != null) {
      if (item.dateModified.isBefore(filter.dateRange!.start) ||
          item.dateModified.isAfter(filter.dateRange!.end)) {
        return false;
      }
    }

    // Size range filter
    if (filter.sizeRange != null) {
      if (item.size < filter.sizeRange!.minSize ||
          item.size > filter.sizeRange!.maxSize) {
        return false;
      }
    }

    // Tags filter
    if (filter.tags != null && filter.tags!.isNotEmpty) {
      final allTags = [...item.aiTags, ...item.customTags];
      final hasMatchingTag = filter.tags!.any(
        (tag) => allTags.any(
          (itemTag) => itemTag.toLowerCase().contains(tag.toLowerCase()),
        ),
      );
      if (!hasMatchingTag) return false;
    }

    return true;
  }

  /// Get matched terms for highlighting
  static List<String> _getMatchedTerms(
    SmartGalleryItem item,
    List<String> queryTerms,
  ) {
    final allTerms = [
      ...item.aiTags,
      ...item.customTags,
      ..._extractKeywords(item.name),
      if (item.ocrText != null) ..._extractKeywords(item.ocrText!),
    ].map((term) => term.toLowerCase()).toList();

    return queryTerms
        .where(
          (queryTerm) =>
              allTerms.any((term) => term.contains(queryTerm.toLowerCase())),
        )
        .toList();
  }

  /// Search by visual similarity
  static Future<List<SmartGallerySearchResult>> visualSimilaritySearch(
    String referenceImageId, {
    double threshold = 0.7,
    int limit = 50,
  }) async {
    try {
      // Get reference image features
      final referenceFeatures = await _getImageFeatures(referenceImageId);
      if (referenceFeatures == null) return [];

      // Compare with all other images
      final similarities = <String, double>{};

      final allImages = await DatabaseService.safeQuery(
        'SELECT id FROM smart_gallery_items WHERE type = ? AND id != ?',
        [MediaType.image.index, referenceImageId],
      );

      for (final row in allImages) {
        final imageId = row['id'] as String;
        final features = await _getImageFeatures(imageId);

        if (features != null) {
          final similarity = _calculateFeatureSimilarity(
            referenceFeatures,
            features,
          );
          if (similarity >= threshold) {
            similarities[imageId] = similarity;
          }
        }
      }

      // Sort by similarity and return results
      final sortedResults = similarities.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      final results = <SmartGallerySearchResult>[];

      for (final entry in sortedResults.take(limit)) {
        final mediaItem = await _getMediaItem(entry.key);
        if (mediaItem != null) {
          results.add(
            SmartGallerySearchResult(
              item: mediaItem,
              relevanceScore: entry.value,
              matchedTerms: [],
              searchQuery: 'Visual similarity to $referenceImageId',
            ),
          );
        }
      }

      return results;
    } catch (e) {
      return [];
    }
  }

  /// Get image features for similarity comparison
  static Future<List<double>?> _getImageFeatures(String imageId) async {
    // In production, extract features using CNN (e.g., ResNet, VGG)
    // For now, return simulated features
    return List.generate(
      512,
      (index) => (imageId.hashCode + index) % 100 / 100.0,
    );
  }

  /// Calculate feature similarity
  static double _calculateFeatureSimilarity(
    List<double> features1,
    List<double> features2,
  ) {
    if (features1.length != features2.length) return 0.0;

    // Calculate cosine similarity
    double dotProduct = 0.0;
    double norm1 = 0.0;
    double norm2 = 0.0;

    for (int i = 0; i < features1.length; i++) {
      dotProduct += features1[i] * features2[i];
      norm1 += features1[i] * features1[i];
      norm2 += features2[i] * features2[i];
    }

    if (norm1 == 0.0 || norm2 == 0.0) return 0.0;

    return dotProduct / sqrt(norm1 * norm2);
  }

  /// Dispose resources
  static void dispose() {
    _indexUpdateTimer?.cancel();
    _searchIndex.clear();
    _termFrequency.clear();
    _invertedIndex.clear();
  }
}

/// Search result with relevance scoring
class SmartGallerySearchResult {
  final SmartGalleryItem item;
  final double relevanceScore;
  final List<String> matchedTerms;
  final String searchQuery;

  const SmartGallerySearchResult({
    required this.item,
    required this.relevanceScore,
    required this.matchedTerms,
    required this.searchQuery,
  });
}
