import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/voice_recording_models.dart';
import '../services/voice_recorder_service.dart';

class VoiceRecorderDashboard extends ConsumerWidget {
  const VoiceRecorderDashboard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final recordings = ref.watch(recordingsProvider);
    final recentRecordings = ref.watch(recentRecordingsProvider);
    final favoriteRecordings = ref.watch(favoriteRecordingsProvider);
    final currentSession = ref.watch(currentRecordingSessionProvider);
    final currentPlayback = ref.watch(currentPlaybackSessionProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Voice Recorder'),
        backgroundColor: Colors.blueGrey,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context, ref),
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showSettingsDialog(context, ref),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Recording Controls
            _buildRecordingControls(context, ref, currentSession),
            const SizedBox(height: 24),

            // Statistics Cards
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Recordings',
                    recordings.length.toString(),
                    Icons.library_music,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Favorites',
                    favoriteRecordings.length.toString(),
                    Icons.favorite,
                    Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Duration',
                    _formatTotalDuration(recordings),
                    Icons.access_time,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Storage Used',
                    _formatTotalSize(recordings),
                    Icons.storage,
                    Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Current Playback
            if (currentPlayback != null) ...[
              _buildCurrentPlayback(context, ref, currentPlayback),
              const SizedBox(height: 24),
            ],

            // Recent Recordings
            Text(
              'Recent Recordings',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (recentRecordings.isEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Icon(Icons.mic_none, size: 48, color: Colors.grey[400]),
                    const SizedBox(height: 8),
                    Text(
                      'No recordings yet',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Tap the record button to start',
                      style: TextStyle(color: Colors.grey[500], fontSize: 12),
                    ),
                  ],
                ),
              )
            else
              ...recentRecordings
                  .take(5)
                  .map((recording) => _buildRecordingCard(recording, ref)),

            const SizedBox(height: 24),

            // Favorite Recordings
            if (favoriteRecordings.isNotEmpty) ...[
              Text(
                'Favorite Recordings',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              ...favoriteRecordings
                  .take(3)
                  .map((recording) => _buildRecordingCard(recording, ref)),
            ],
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.large(
        onPressed: () => _toggleRecording(context, ref),
        backgroundColor: currentSession?.status == RecordingStatus.recording
            ? Colors.red
            : Colors.blueGrey,
        child: Icon(
          currentSession?.status == RecordingStatus.recording
              ? Icons.stop
              : Icons.mic,
          size: 32,
        ),
      ),
    );
  }

  Widget _buildRecordingControls(
    BuildContext context,
    WidgetRef ref,
    RecordingSession? session,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: session?.status == RecordingStatus.recording
              ? [Colors.red, Colors.redAccent]
              : [Colors.blueGrey, Colors.blue],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Icon(
            session?.status == RecordingStatus.recording
                ? Icons.mic
                : Icons.mic_none,
            size: 64,
            color: Colors.white,
          ),
          const SizedBox(height: 16),
          Text(
            session?.status == RecordingStatus.recording
                ? 'Recording...'
                : 'Ready to Record',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            session?.formattedDuration ?? '00:00',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.white70,
              fontFamily: 'monospace',
            ),
          ),
          if (session?.status == RecordingStatus.recording) ...[
            const SizedBox(height: 16),
            _buildWaveform(session),
          ],
        ],
      ),
    );
  }

  Widget _buildWaveform(RecordingSession? session) {
    return Container(
      height: 40,
      width: double.infinity,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: List.generate(20, (index) {
          final height =
              (session?.currentAmplitude ?? 0.0) *
              40 *
              (0.5 + 0.5 * (index % 3 == 0 ? 1 : 0.7));
          return Container(
            width: 3,
            height: height.clamp(2.0, 40.0),
            margin: const EdgeInsets.symmetric(horizontal: 1),
            decoration: BoxDecoration(
              color: Colors.white70,
              borderRadius: BorderRadius.circular(1.5),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(color: Colors.grey[600], fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentPlayback(
    BuildContext context,
    WidgetRef ref,
    PlaybackSession playback,
  ) {
    final recordings = ref.watch(recordingsProvider);
    final recording = recordings.firstWhere(
      (r) => r.id == playback.recordingId,
    );

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.play_circle, color: Colors.green, size: 24),
              const SizedBox(width: 8),
              Text(
                'Now Playing',
                style: TextStyle(
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            recording.title,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                playback.formattedPosition,
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
              ),
              Expanded(
                child: Slider(
                  value: playback.progress,
                  onChanged: (value) {
                    final position = Duration(
                      milliseconds:
                          (value * playback.totalDuration.inMilliseconds)
                              .round(),
                    );
                    ref.read(voiceRecorderServiceProvider).seekTo(position);
                  },
                  activeColor: Colors.green,
                ),
              ),
              Text(
                playback.formattedTotal,
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRecordingCard(VoiceRecording recording, WidgetRef ref) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 40,
            decoration: BoxDecoration(
              color: recording.color,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  recording.title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                if (recording.description.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    recording.description,
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      recording.formattedDuration,
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                    const SizedBox(width: 16),
                    Icon(Icons.storage, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      recording.formattedFileSize,
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: Icon(
                  recording.isFavorite ? Icons.favorite : Icons.favorite_border,
                  color: recording.isFavorite ? Colors.red : Colors.grey,
                ),
                onPressed: () {
                  ref
                      .read(voiceRecorderServiceProvider)
                      .toggleFavorite(recording.id);
                },
              ),
              IconButton(
                icon: const Icon(Icons.play_arrow),
                onPressed: () {
                  ref
                      .read(voiceRecorderServiceProvider)
                      .startPlayback(recording.id);
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatTotalDuration(List<VoiceRecording> recordings) {
    final totalSeconds = recordings.fold<int>(
      0,
      (sum, recording) => sum + recording.duration.inSeconds,
    );
    final hours = totalSeconds ~/ 3600;
    final minutes = (totalSeconds % 3600) ~/ 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  String _formatTotalSize(List<VoiceRecording> recordings) {
    final totalBytes = recordings.fold<int>(
      0,
      (sum, recording) => sum + recording.fileSize,
    );

    if (totalBytes < 1024 * 1024) {
      return '${(totalBytes / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(totalBytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }

  void _toggleRecording(BuildContext context, WidgetRef ref) async {
    final service = ref.read(voiceRecorderServiceProvider);
    final currentSession = service.currentSession;

    try {
      if (currentSession?.status == RecordingStatus.recording) {
        await service.stopRecording();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Recording saved'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        await service.startRecording();
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
      );
    }
  }

  void _showSearchDialog(BuildContext context, WidgetRef ref) {
    // Implementation for search dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Recordings'),
        content: const Text('Search functionality will be implemented here'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSettingsDialog(BuildContext context, WidgetRef ref) {
    // Implementation for settings dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Voice Recorder Settings'),
        content: const Text('Settings will be implemented here'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
