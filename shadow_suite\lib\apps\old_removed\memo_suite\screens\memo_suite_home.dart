import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'memo_suite_dashboard.dart';
import 'notes/note_view_screen.dart';
import 'todos/todo_view_screen.dart';
import 'voice_memos/voice_memo_playback_screen.dart';
import 'calendar_view_screen.dart';

/// Main home screen for Memo Suite application
class MemoSuiteHome extends ConsumerStatefulWidget {
  const MemoSuiteHome({super.key});

  @override
  ConsumerState<MemoSuiteHome> createState() => _MemoSuiteHomeState();
}

class _MemoSuiteHomeState extends ConsumerState<MemoSuiteHome> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const MemoSuiteDashboard(),
    const NoteViewScreen(),
    const TodoViewScreen(),
    const VoiceMemoPlaybackScreen(),
    const CalendarViewScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        selectedItemColor: Colors.blue,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.note), label: 'Notes'),
          BottomNavigationBarItem(
            icon: Icon(Icons.check_circle),
            label: 'Todos',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.mic), label: 'Voice'),
          BottomNavigationBarItem(
            icon: Icon(Icons.calendar_today),
            label: 'Calendar',
          ),
        ],
      ),
    );
  }
}
