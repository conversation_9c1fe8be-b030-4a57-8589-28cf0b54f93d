import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/money_manager_models.dart';
import '../services/money_manager_providers.dart';
import '../widgets/account_overview_card.dart';
import '../widgets/recent_transactions_card.dart';
import '../widgets/budget_status_card.dart';
import '../widgets/quick_actions_card.dart';
import '../widgets/financial_summary_card.dart';

class MoneyManagerDashboard extends ConsumerWidget {
  const MoneyManagerDashboard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue.shade50,
            Colors.white,
          ],
        ),
      ),
      child: Column(
        children: [
          _buildHeader(context),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Financial Summary Row
                  _buildFinancialSummaryRow(ref),
                  const SizedBox(height: 24),
                  
                  // Main Content Grid
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Left Column
                      Expanded(
                        flex: 2,
                        child: Column(
                          children: [
                            const AccountOverviewCard(),
                            const SizedBox(height: 24),
                            const RecentTransactionsCard(),
                          ],
                        ),
                      ),
                      const SizedBox(width: 24),
                      
                      // Right Column
                      Expanded(
                        flex: 1,
                        child: Column(
                          children: [
                            const QuickActionsCard(),
                            const SizedBox(height: 24),
                            const BudgetStatusCard(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE9ECEF)),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF3498DB).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.dashboard,
              color: Color(0xFF3498DB),
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Financial Dashboard',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                Text(
                  'Overview of your financial status • ${_getCurrentDate()}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF7F8C8D),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: const Color(0xFF27AE60).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.circle,
                  color: Color(0xFF27AE60),
                  size: 8,
                ),
                SizedBox(width: 8),
                Text(
                  'Live Data',
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0xFF27AE60),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialSummaryRow(WidgetRef ref) {
    return Row(
      children: [
        Expanded(
          child: FinancialSummaryCard(
            title: 'Total Balance',
            provider: totalBalanceProvider,
            icon: Icons.account_balance_wallet,
            color: const Color(0xFF3498DB),
            format: 'currency',
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildMonthlyIncomeCard(ref),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildMonthlyExpensesCard(ref),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildNetIncomeCard(ref),
        ),
      ],
    );
  }

  Widget _buildMonthlyIncomeCard(WidgetRef ref) {
    return Consumer(
      builder: (context, ref, child) {
        final transactionsAsync = ref.watch(transactionsProvider);
        
        return transactionsAsync.when(
          data: (transactions) {
            final now = DateTime.now();
            final startOfMonth = DateTime(now.year, now.month, 1);
            final endOfMonth = DateTime(now.year, now.month + 1, 0);
            
            final monthlyIncome = transactions
                .where((t) => 
                  t.type == TransactionType.income &&
                  t.date.isAfter(startOfMonth.subtract(const Duration(days: 1))) &&
                  t.date.isBefore(endOfMonth.add(const Duration(days: 1)))
                )
                .fold(0.0, (sum, t) => sum + t.amount);
            
            return _buildSummaryCard(
              title: 'Monthly Income',
              value: '\$${monthlyIncome.toStringAsFixed(2)}',
              icon: Icons.trending_up,
              color: const Color(0xFF27AE60),
            );
          },
          loading: () => _buildSummaryCard(
            title: 'Monthly Income',
            value: 'Loading...',
            icon: Icons.trending_up,
            color: const Color(0xFF27AE60),
          ),
          error: (error, stack) => _buildSummaryCard(
            title: 'Monthly Income',
            value: 'Error',
            icon: Icons.trending_up,
            color: const Color(0xFF27AE60),
          ),
        );
      },
    );
  }

  Widget _buildMonthlyExpensesCard(WidgetRef ref) {
    return Consumer(
      builder: (context, ref, child) {
        final transactionsAsync = ref.watch(transactionsProvider);
        
        return transactionsAsync.when(
          data: (transactions) {
            final now = DateTime.now();
            final startOfMonth = DateTime(now.year, now.month, 1);
            final endOfMonth = DateTime(now.year, now.month + 1, 0);
            
            final monthlyExpenses = transactions
                .where((t) => 
                  t.type == TransactionType.expense &&
                  t.date.isAfter(startOfMonth.subtract(const Duration(days: 1))) &&
                  t.date.isBefore(endOfMonth.add(const Duration(days: 1)))
                )
                .fold(0.0, (sum, t) => sum + t.amount);
            
            return _buildSummaryCard(
              title: 'Monthly Expenses',
              value: '\$${monthlyExpenses.toStringAsFixed(2)}',
              icon: Icons.trending_down,
              color: const Color(0xFFE74C3C),
            );
          },
          loading: () => _buildSummaryCard(
            title: 'Monthly Expenses',
            value: 'Loading...',
            icon: Icons.trending_down,
            color: const Color(0xFFE74C3C),
          ),
          error: (error, stack) => _buildSummaryCard(
            title: 'Monthly Expenses',
            value: 'Error',
            icon: Icons.trending_down,
            color: const Color(0xFFE74C3C),
          ),
        );
      },
    );
  }

  Widget _buildNetIncomeCard(WidgetRef ref) {
    return Consumer(
      builder: (context, ref, child) {
        final transactionsAsync = ref.watch(transactionsProvider);
        
        return transactionsAsync.when(
          data: (transactions) {
            final now = DateTime.now();
            final startOfMonth = DateTime(now.year, now.month, 1);
            final endOfMonth = DateTime(now.year, now.month + 1, 0);
            
            final monthlyIncome = transactions
                .where((t) => 
                  t.type == TransactionType.income &&
                  t.date.isAfter(startOfMonth.subtract(const Duration(days: 1))) &&
                  t.date.isBefore(endOfMonth.add(const Duration(days: 1)))
                )
                .fold(0.0, (sum, t) => sum + t.amount);
                
            final monthlyExpenses = transactions
                .where((t) => 
                  t.type == TransactionType.expense &&
                  t.date.isAfter(startOfMonth.subtract(const Duration(days: 1))) &&
                  t.date.isBefore(endOfMonth.add(const Duration(days: 1)))
                )
                .fold(0.0, (sum, t) => sum + t.amount);
            
            final netIncome = monthlyIncome - monthlyExpenses;
            final isPositive = netIncome >= 0;
            
            return _buildSummaryCard(
              title: 'Net Income',
              value: '${isPositive ? '+' : ''}\$${netIncome.toStringAsFixed(2)}',
              icon: isPositive ? Icons.arrow_upward : Icons.arrow_downward,
              color: isPositive ? const Color(0xFF27AE60) : const Color(0xFFE74C3C),
            );
          },
          loading: () => _buildSummaryCard(
            title: 'Net Income',
            value: 'Loading...',
            icon: Icons.trending_flat,
            color: const Color(0xFF95A5A6),
          ),
          error: (error, stack) => _buildSummaryCard(
            title: 'Net Income',
            value: 'Error',
            icon: Icons.trending_flat,
            color: const Color(0xFF95A5A6),
          ),
        );
      },
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
              const Spacer(),
              Icon(
                Icons.more_vert,
                color: Colors.grey.shade400,
                size: 16,
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF7F8C8D),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
        ],
      ),
    );
  }

  String _getCurrentDate() {
    final now = DateTime.now();
    final months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return '${months[now.month - 1]} ${now.day}, ${now.year}';
  }
}
