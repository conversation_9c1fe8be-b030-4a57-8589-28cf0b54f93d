import 'package:flutter_riverpod/flutter_riverpod.dart';

// Analytics Data Models
class AnalyticsData {
  final int totalTools;
  final int activeUsers;
  final int totalRuns;
  final double avgPerformance;
  final double toolsGrowth;
  final double usersGrowth;
  final double runsGrowth;
  final double performanceChange;
  final String mostUsedTool;
  final int mostUsedToolRuns;
  final String fastestTool;
  final double fastestToolTime;
  final String mostComplexTool;
  final int mostComplexToolComponents;
  final int peakHour;
  final int peakHourRuns;
  final int totalCalculations;

  const AnalyticsData({
    required this.totalTools,
    required this.activeUsers,
    required this.totalRuns,
    required this.avgPerformance,
    required this.toolsGrowth,
    required this.usersGrowth,
    required this.runsGrowth,
    required this.performanceChange,
    required this.mostUsedTool,
    required this.mostUsedToolRuns,
    required this.fastestTool,
    required this.fastestToolTime,
    required this.mostComplexTool,
    required this.mostComplexToolComponents,
    required this.peakHour,
    required this.peakHourRuns,
    required this.totalCalculations,
  });
}

class ActivityData {
  final String type;
  final String description;
  final DateTime timestamp;
  final String? metadata;

  const ActivityData({
    required this.type,
    required this.description,
    required this.timestamp,
    this.metadata,
  });
}

class ToolUsageData {
  final String name;
  final int usageCount;
  final double usagePercentage;

  const ToolUsageData({
    required this.name,
    required this.usageCount,
    required this.usagePercentage,
  });
}

class PerformanceMetrics {
  final double avgLoadTime;
  final double avgCalculationTime;
  final double avgMemoryUsage;
  final double errorRate;
  final double successRate;

  const PerformanceMetrics({
    required this.avgLoadTime,
    required this.avgCalculationTime,
    required this.avgMemoryUsage,
    required this.errorRate,
    required this.successRate,
  });
}

// Analytics Service
class AnalyticsService {
  static Future<AnalyticsData> getAnalytics(String timeRange) async {
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Mock data based on time range
    switch (timeRange) {
      case '1d':
        return const AnalyticsData(
          totalTools: 12,
          activeUsers: 8,
          totalRuns: 45,
          avgPerformance: 125.5,
          toolsGrowth: 8.3,
          usersGrowth: 12.5,
          runsGrowth: 25.0,
          performanceChange: -5.2,
          mostUsedTool: 'Loan Calculator',
          mostUsedToolRuns: 15,
          fastestTool: 'Unit Converter',
          fastestToolTime: 45.2,
          mostComplexTool: 'Financial Dashboard',
          mostComplexToolComponents: 24,
          peakHour: 14,
          peakHourRuns: 12,
          totalCalculations: 1250,
        );
      case '7d':
        return const AnalyticsData(
          totalTools: 18,
          activeUsers: 25,
          totalRuns: 342,
          avgPerformance: 118.3,
          toolsGrowth: 12.5,
          usersGrowth: 18.7,
          runsGrowth: 45.2,
          performanceChange: -8.1,
          mostUsedTool: 'BMI Calculator',
          mostUsedToolRuns: 89,
          fastestTool: 'Unit Converter',
          fastestToolTime: 42.8,
          mostComplexTool: 'Project Planner',
          mostComplexToolComponents: 32,
          peakHour: 15,
          peakHourRuns: 78,
          totalCalculations: 8750,
        );
      case '30d':
        return const AnalyticsData(
          totalTools: 35,
          activeUsers: 67,
          totalRuns: 1456,
          avgPerformance: 112.7,
          toolsGrowth: 25.8,
          usersGrowth: 34.2,
          runsGrowth: 78.5,
          performanceChange: -12.3,
          mostUsedTool: 'Expense Tracker',
          mostUsedToolRuns: 234,
          fastestTool: 'Currency Converter',
          fastestToolTime: 38.5,
          mostComplexTool: 'Business Analytics',
          mostComplexToolComponents: 45,
          peakHour: 14,
          peakHourRuns: 156,
          totalCalculations: 34500,
        );
      case '90d':
        return const AnalyticsData(
          totalTools: 52,
          activeUsers: 124,
          totalRuns: 4567,
          avgPerformance: 108.2,
          toolsGrowth: 44.4,
          usersGrowth: 67.8,
          runsGrowth: 125.3,
          performanceChange: -18.7,
          mostUsedTool: 'Budget Planner',
          mostUsedToolRuns: 567,
          fastestTool: 'Quick Calculator',
          fastestToolTime: 35.1,
          mostComplexTool: 'Enterprise Dashboard',
          mostComplexToolComponents: 67,
          peakHour: 13,
          peakHourRuns: 345,
          totalCalculations: 125000,
        );
      case '1y':
        return const AnalyticsData(
          totalTools: 89,
          activeUsers: 256,
          totalRuns: 15678,
          avgPerformance: 95.8,
          toolsGrowth: 78.9,
          usersGrowth: 145.6,
          runsGrowth: 234.7,
          performanceChange: -25.4,
          mostUsedTool: 'Investment Calculator',
          mostUsedToolRuns: 1234,
          fastestTool: 'Simple Calculator',
          fastestToolTime: 28.3,
          mostComplexTool: 'AI Analytics Suite',
          mostComplexToolComponents: 89,
          peakHour: 14,
          peakHourRuns: 1456,
          totalCalculations: 567890,
        );
      default:
        return const AnalyticsData(
          totalTools: 18,
          activeUsers: 25,
          totalRuns: 342,
          avgPerformance: 118.3,
          toolsGrowth: 12.5,
          usersGrowth: 18.7,
          runsGrowth: 45.2,
          performanceChange: -8.1,
          mostUsedTool: 'BMI Calculator',
          mostUsedToolRuns: 89,
          fastestTool: 'Unit Converter',
          fastestToolTime: 42.8,
          mostComplexTool: 'Project Planner',
          mostComplexToolComponents: 32,
          peakHour: 15,
          peakHourRuns: 78,
          totalCalculations: 8750,
        );
    }
  }

  static Future<List<ActivityData>> getRecentActivities() async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    return [
      ActivityData(
        type: 'tool_run',
        description: 'Loan Calculator executed',
        timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
        metadata: '2.3s',
      ),
      ActivityData(
        type: 'tool_created',
        description: 'New BMI Calculator created',
        timestamp: DateTime.now().subtract(const Duration(hours: 1)),
        metadata: 'v1.0',
      ),
      ActivityData(
        type: 'tool_edited',
        description: 'Budget Planner updated',
        timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        metadata: 'v2.1',
      ),
      ActivityData(
        type: 'tool_shared',
        description: 'Investment Calculator shared',
        timestamp: DateTime.now().subtract(const Duration(hours: 4)),
        metadata: '3 users',
      ),
      ActivityData(
        type: 'tool_run',
        description: 'Unit Converter executed',
        timestamp: DateTime.now().subtract(const Duration(hours: 6)),
        metadata: '1.1s',
      ),
    ];
  }

  static Future<List<ToolUsageData>> getTopTools(String timeRange) async {
    await Future.delayed(const Duration(milliseconds: 400));
    
    switch (timeRange) {
      case '1d':
        return [
          const ToolUsageData(name: 'Loan Calculator', usageCount: 15, usagePercentage: 33.3),
          const ToolUsageData(name: 'Unit Converter', usageCount: 12, usagePercentage: 26.7),
          const ToolUsageData(name: 'BMI Calculator', usageCount: 8, usagePercentage: 17.8),
          const ToolUsageData(name: 'Currency Converter', usageCount: 6, usagePercentage: 13.3),
          const ToolUsageData(name: 'Budget Planner', usageCount: 4, usagePercentage: 8.9),
        ];
      case '7d':
        return [
          const ToolUsageData(name: 'BMI Calculator', usageCount: 89, usagePercentage: 26.0),
          const ToolUsageData(name: 'Loan Calculator', usageCount: 67, usagePercentage: 19.6),
          const ToolUsageData(name: 'Unit Converter', usageCount: 54, usagePercentage: 15.8),
          const ToolUsageData(name: 'Budget Planner', usageCount: 45, usagePercentage: 13.2),
          const ToolUsageData(name: 'Currency Converter', usageCount: 32, usagePercentage: 9.4),
        ];
      default:
        return [
          const ToolUsageData(name: 'Expense Tracker', usageCount: 234, usagePercentage: 16.1),
          const ToolUsageData(name: 'BMI Calculator', usageCount: 198, usagePercentage: 13.6),
          const ToolUsageData(name: 'Loan Calculator', usageCount: 167, usagePercentage: 11.5),
          const ToolUsageData(name: 'Budget Planner', usageCount: 145, usagePercentage: 10.0),
          const ToolUsageData(name: 'Unit Converter', usageCount: 123, usagePercentage: 8.4),
        ];
    }
  }

  static Future<PerformanceMetrics> getPerformanceMetrics(String timeRange) async {
    await Future.delayed(const Duration(milliseconds: 350));
    
    switch (timeRange) {
      case '1d':
        return const PerformanceMetrics(
          avgLoadTime: 125.5,
          avgCalculationTime: 45.2,
          avgMemoryUsage: 12.8,
          errorRate: 2.1,
          successRate: 97.9,
        );
      case '7d':
        return const PerformanceMetrics(
          avgLoadTime: 118.3,
          avgCalculationTime: 42.8,
          avgMemoryUsage: 11.5,
          errorRate: 1.8,
          successRate: 98.2,
        );
      case '30d':
        return const PerformanceMetrics(
          avgLoadTime: 112.7,
          avgCalculationTime: 38.5,
          avgMemoryUsage: 10.2,
          errorRate: 1.5,
          successRate: 98.5,
        );
      default:
        return const PerformanceMetrics(
          avgLoadTime: 108.2,
          avgCalculationTime: 35.1,
          avgMemoryUsage: 9.8,
          errorRate: 1.2,
          successRate: 98.8,
        );
    }
  }
}

// Providers
final analyticsProvider = FutureProvider.family<AnalyticsData, String>((ref, timeRange) {
  return AnalyticsService.getAnalytics(timeRange);
});

final recentActivitiesProvider = FutureProvider<List<ActivityData>>((ref) {
  return AnalyticsService.getRecentActivities();
});

final topToolsProvider = FutureProvider.family<List<ToolUsageData>, String>((ref, timeRange) {
  return AnalyticsService.getTopTools(timeRange);
});

final performanceMetricsProvider = FutureProvider.family<PerformanceMetrics, String>((ref, timeRange) {
  return AnalyticsService.getPerformanceMetrics(timeRange);
});
