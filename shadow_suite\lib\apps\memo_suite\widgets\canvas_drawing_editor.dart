import 'dart:convert';
import 'package:flutter/material.dart';

class DrawingPoint {
  final Offset point;
  final Paint paint;

  DrawingPoint({required this.point, required this.paint});

  Map<String, dynamic> toJson() {
    return {
      'x': point.dx,
      'y': point.dy,
      'color': paint.color.value,
      'strokeWidth': paint.strokeWidth,
    };
  }

  factory DrawingPoint.fromJson(Map<String, dynamic> json) {
    return DrawingPoint(
      point: Offset(json['x'], json['y']),
      paint: Paint()
        ..color = Color(json['color'])
        ..strokeWidth = json['strokeWidth']
        ..strokeCap = StrokeCap.round,
    );
  }
}

class CanvasDrawingEditor extends StatefulWidget {
  final String? initialData;
  final Function(String) onDataChanged;

  const CanvasDrawingEditor({
    super.key,
    this.initialData,
    required this.onDataChanged,
  });

  @override
  State<CanvasDrawingEditor> createState() => _CanvasDrawingEditorState();
}

class _CanvasDrawingEditorState extends State<CanvasDrawingEditor> {
  List<List<DrawingPoint>> _paths = [];
  List<DrawingPoint> _currentPath = [];
  Color _selectedColor = Colors.black;
  double _strokeWidth = 3.0;

  final List<Color> _colors = [
    Colors.black,
    Colors.red,
    Colors.blue,
    Colors.green,
    Colors.orange,
    Colors.purple,
    Colors.brown,
    Colors.pink,
  ];

  @override
  void initState() {
    super.initState();
    if (widget.initialData != null && widget.initialData!.isNotEmpty) {
      _loadDrawingData(widget.initialData!);
    }
  }

  void _loadDrawingData(String data) {
    try {
      final Map<String, dynamic> jsonData = json.decode(data);
      final List<dynamic> pathsData = jsonData['paths'] ?? [];
      
      _paths = pathsData.map((pathData) {
        final List<dynamic> pointsData = pathData['points'] ?? [];
        return pointsData.map((pointData) => DrawingPoint.fromJson(pointData)).toList();
      }).toList();
      
      setState(() {});
    } catch (e) {
      // Handle invalid data gracefully
      _paths = [];
    }
  }

  void _saveDrawingData() {
    final data = {
      'paths': _paths.map((path) => {
        'points': path.map((point) => point.toJson()).toList(),
      }).toList(),
    };
    widget.onDataChanged(json.encode(data));
  }

  void _onPanStart(DragStartDetails details) {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final localPosition = renderBox.globalToLocal(details.globalPosition);
    
    _currentPath = [
      DrawingPoint(
        point: localPosition,
        paint: Paint()
          ..color = _selectedColor
          ..strokeWidth = _strokeWidth
          ..strokeCap = StrokeCap.round,
      ),
    ];
  }

  void _onPanUpdate(DragUpdateDetails details) {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final localPosition = renderBox.globalToLocal(details.globalPosition);
    
    setState(() {
      _currentPath.add(
        DrawingPoint(
          point: localPosition,
          paint: Paint()
            ..color = _selectedColor
            ..strokeWidth = _strokeWidth
            ..strokeCap = StrokeCap.round,
        ),
      );
    });
  }

  void _onPanEnd(DragEndDetails details) {
    if (_currentPath.isNotEmpty) {
      setState(() {
        _paths.add(List.from(_currentPath));
        _currentPath = [];
      });
      _saveDrawingData();
    }
  }

  void _clearCanvas() {
    setState(() {
      _paths.clear();
      _currentPath.clear();
    });
    _saveDrawingData();
  }

  void _undoLastPath() {
    if (_paths.isNotEmpty) {
      setState(() {
        _paths.removeLast();
      });
      _saveDrawingData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Toolbar
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            border: const Border(bottom: BorderSide(color: Colors.grey)),
          ),
          child: Column(
            children: [
              // Color picker
              Row(
                children: [
                  const Text('Color: '),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Wrap(
                      spacing: 8,
                      children: _colors.map((color) {
                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedColor = color;
                            });
                          },
                          child: Container(
                            width: 30,
                            height: 30,
                            decoration: BoxDecoration(
                              color: color,
                              shape: BoxShape.circle,
                              border: _selectedColor == color
                                  ? Border.all(color: Colors.black, width: 2)
                                  : null,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Stroke width and actions
              Row(
                children: [
                  const Text('Size: '),
                  Expanded(
                    child: Slider(
                      value: _strokeWidth,
                      min: 1.0,
                      max: 10.0,
                      divisions: 9,
                      label: _strokeWidth.round().toString(),
                      onChanged: (value) {
                        setState(() {
                          _strokeWidth = value;
                        });
                      },
                    ),
                  ),
                  IconButton(
                    onPressed: _undoLastPath,
                    icon: const Icon(Icons.undo),
                    tooltip: 'Undo',
                  ),
                  IconButton(
                    onPressed: _clearCanvas,
                    icon: const Icon(Icons.clear),
                    tooltip: 'Clear',
                  ),
                ],
              ),
            ],
          ),
        ),
        // Drawing canvas
        Expanded(
          child: Container(
            width: double.infinity,
            color: Colors.white,
            child: GestureDetector(
              onPanStart: _onPanStart,
              onPanUpdate: _onPanUpdate,
              onPanEnd: _onPanEnd,
              child: CustomPaint(
                painter: DrawingPainter(
                  paths: _paths,
                  currentPath: _currentPath,
                ),
                size: Size.infinite,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class DrawingPainter extends CustomPainter {
  final List<List<DrawingPoint>> paths;
  final List<DrawingPoint> currentPath;

  DrawingPainter({required this.paths, required this.currentPath});

  @override
  void paint(Canvas canvas, Size size) {
    // Draw completed paths
    for (final path in paths) {
      _drawPath(canvas, path);
    }
    
    // Draw current path
    if (currentPath.isNotEmpty) {
      _drawPath(canvas, currentPath);
    }
  }

  void _drawPath(Canvas canvas, List<DrawingPoint> path) {
    if (path.length < 2) return;
    
    for (int i = 0; i < path.length - 1; i++) {
      canvas.drawLine(
        path[i].point,
        path[i + 1].point,
        path[i].paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
