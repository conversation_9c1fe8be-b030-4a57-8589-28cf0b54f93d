import 'package:uuid/uuid.dart';

enum TransactionType {
  income('Income'),
  expense('Expense'),
  transfer('Transfer');

  const TransactionType(this.displayName);
  final String displayName;

  static List<String> get allTypes => TransactionType.values.map((e) => e.displayName).toList();
}

class TransactionCategoryHelper {
  static const List<String> incomeCategories = [
    'Salary',
    'Freelance',
    'Investment',
    'Business',
    'Gift',
    'Refund',
    'Other Income',
  ];

  static const List<String> expenseCategories = [
    'Food & Dining',
    'Transportation',
    'Shopping',
    'Entertainment',
    'Bills & Utilities',
    'Healthcare',
    'Education',
    'Travel',
    'Insurance',
    'Taxes',
    'Investment',
    'Other Expense',
  ];

  static List<String> getAllCategories() {
    return [...incomeCategories, ...expenseCategories];
  }

  static List<String> getCategoriesForType(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return incomeCategories;
      case TransactionType.expense:
        return expenseCategories;
      case TransactionType.transfer:
        return ['Transfer'];
    }
  }
}

class MoneyTransaction {
  final String id;
  final String accountId;
  final String? toAccountId; // For transfers
  final TransactionType type;
  final double amount;
  final String category;
  final String description;
  final String? notes;
  final List<String> tags;
  final String? receiptPath;
  final DateTime date;
  final DateTime createdAt;
  final DateTime updatedAt;

  MoneyTransaction({
    String? id,
    required this.accountId,
    this.toAccountId,
    required this.type,
    required this.amount,
    required this.category,
    required this.description,
    this.notes,
    this.tags = const [],
    this.receiptPath,
    DateTime? date,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        date = date ?? DateTime.now(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'accountId': accountId,
      'toAccountId': toAccountId,
      'type': type.name,
      'amount': amount,
      'category': category,
      'description': description,
      'notes': notes,
      'tags': tags.join(','),
      'receiptPath': receiptPath,
      'date': date.millisecondsSinceEpoch,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory MoneyTransaction.fromMap(Map<String, dynamic> map) {
    return MoneyTransaction(
      id: map['id'],
      accountId: map['accountId'],
      toAccountId: map['toAccountId'],
      type: TransactionType.values.firstWhere((e) => e.name == map['type']),
      amount: map['amount'].toDouble(),
      category: map['category'],
      description: map['description'],
      notes: map['notes'],
      tags: map['tags'] != null && map['tags'].isNotEmpty 
          ? map['tags'].split(',') 
          : <String>[],
      receiptPath: map['receiptPath'],
      date: DateTime.fromMillisecondsSinceEpoch(map['date']),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
    );
  }

  MoneyTransaction copyWith({
    String? accountId,
    String? toAccountId,
    TransactionType? type,
    double? amount,
    String? category,
    String? description,
    String? notes,
    List<String>? tags,
    String? receiptPath,
    DateTime? date,
    DateTime? updatedAt,
  }) {
    return MoneyTransaction(
      id: id,
      accountId: accountId ?? this.accountId,
      toAccountId: toAccountId ?? this.toAccountId,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      category: category ?? this.category,
      description: description ?? this.description,
      notes: notes ?? this.notes,
      tags: tags ?? this.tags,
      receiptPath: receiptPath ?? this.receiptPath,
      date: date ?? this.date,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  String get formattedAmount {
    final sign = type == TransactionType.income ? '+' : '-';
    return '$sign\$${amount.toStringAsFixed(2)}';
  }

  String get formattedDate {
    return '${date.day}/${date.month}/${date.year}';
  }
}
