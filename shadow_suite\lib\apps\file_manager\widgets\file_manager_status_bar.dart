import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/file_manager_service.dart';

class FileManagerStatusBar extends ConsumerWidget {
  const FileManagerStatusBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      height: 24,
      decoration: const BoxDecoration(
        color: Color(0xFFF8F9FA),
        border: Border(
          top: BorderSide(color: Color(0xFFE9ECEF)),
        ),
      ),
      child: Row(
        children: [
          const SizedBox(width: 8),
          
          // File count and selection info
          Text(
            _getFileCountText(),
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF495057),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Selected items info
          Text(
            _getSelectionText(),
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF495057),
            ),
          ),
          
          const Spacer(),
          
          // Storage info
          Text(
            _getStorageText(),
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF495057),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Network status
          _buildNetworkStatus(),
          
          const SizedBox(width: 8),
        ],
      ),
    );
  }

  String _getFileCountText() {
    final items = FileManagerService.getCurrentItems();
    final fileCount = items.where((item) => !item.isDirectory).length;
    final folderCount = items.where((item) => item.isDirectory).length;
    
    if (fileCount == 0 && folderCount == 0) {
      return 'Empty folder';
    } else if (fileCount == 0) {
      return '$folderCount folder${folderCount == 1 ? '' : 's'}';
    } else if (folderCount == 0) {
      return '$fileCount file${fileCount == 1 ? '' : 's'}';
    } else {
      return '$fileCount file${fileCount == 1 ? '' : 's'}, $folderCount folder${folderCount == 1 ? '' : 's'}';
    }
  }

  String _getSelectionText() {
    // This would be updated based on actual selection
    // For now, return empty or placeholder
    return '';
  }

  String _getStorageText() {
    // This would show actual storage information
    // For now, return placeholder
    return 'Free: 2.5 GB';
  }

  Widget _buildNetworkStatus() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.wifi,
          size: 14,
          color: _isNetworkConnected() ? const Color(0xFF27AE60) : const Color(0xFF95A5A6),
        ),
        const SizedBox(width: 4),
        Text(
          _isNetworkConnected() ? 'Online' : 'Offline',
          style: TextStyle(
            fontSize: 12,
            color: _isNetworkConnected() ? const Color(0xFF27AE60) : const Color(0xFF95A5A6),
          ),
        ),
      ],
    );
  }

  bool _isNetworkConnected() {
    // This would check actual network connectivity
    // For now, return true as placeholder
    return true;
  }
}
