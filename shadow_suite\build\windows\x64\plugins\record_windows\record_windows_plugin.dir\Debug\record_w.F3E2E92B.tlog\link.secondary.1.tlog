^D:\PROJECTS\T2 - COPY\SHADOW_SUITE\BUILD\WINDOWS\X64\PLUGINS\RECORD_WINDOWS\RECORD_WINDOWS_PLUGIN.DIR\DEBUG\RECORD.OBJ|D:\PROJECTS\T2 - COPY\SHADOW_SUITE\BUILD\WINDOWS\X64\PLUGINS\RECORD_WINDOWS\RECORD_WINDOWS_PLUGIN.DIR\DEBUG\RECORD_IUNKNOWN.OBJ|D:\PROJECTS\T2 - COPY\SHADOW_SUITE\BUILD\WINDOWS\X64\PLUGINS\RECORD_WINDOWS\RECORD_WINDOWS_PLUGIN.DIR\DEBUG\RECORD_MEDIATYPE.OBJ|D:\PROJECTS\T2 - COPY\SHADOW_SUITE\BUILD\WINDOWS\X64\PLUGINS\RECORD_WINDOWS\RECORD_WINDOWS_PLUGIN.DIR\DEBUG\RECORD_READERCALLBACK.OBJ|D:\PROJECTS\T2 - COPY\SHADOW_SUITE\BUILD\WINDOWS\X64\PLUGINS\RECORD_WINDOWS\RECORD_WINDOWS_PLUGIN.DIR\DEBUG\RECORD_WINDOWS_PLUGIN.OBJ|D:\PROJECTS\T2 - COPY\SHADOW_SUITE\BUILD\WINDOWS\X64\PLUGINS\RECORD_WINDOWS\RECORD_WINDOWS_PLUGIN.DIR\DEBUG\RECORD_WINDOWS_PLUGIN_C_API.OBJ
D:\projects\t2 - Copy\shadow_suite\build\windows\x64\plugins\record_windows\Debug\record_windows_plugin.lib
D:\projects\t2 - Copy\shadow_suite\build\windows\x64\plugins\record_windows\Debug\record_windows_plugin.EXP
D:\projects\t2 - Copy\shadow_suite\build\windows\x64\plugins\record_windows\record_windows_plugin.dir\Debug\record_windows_plugin.ilk
