import 'package:flutter/material.dart';
import '../services/mobile_touch_interface_service.dart';

/// Mobile-optimized UI widget for Excel-to-App builder
class MobileOptimizedUIWidget extends StatefulWidget {
  final Widget child;
  final bool enableMobileOptimizations;

  const MobileOptimizedUIWidget({
    super.key,
    required this.child,
    this.enableMobileOptimizations = true,
  });

  @override
  State<MobileOptimizedUIWidget> createState() =>
      _MobileOptimizedUIWidgetState();
}

class _MobileOptimizedUIWidgetState extends State<MobileOptimizedUIWidget> {
  bool _showMobileSettings = false;
  TouchConfiguration _config = const TouchConfiguration();

  @override
  void initState() {
    super.initState();
    MobileTouchInterfaceService.initialize();
    _config = MobileTouchInterfaceService.configuration;
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enableMobileOptimizations ||
        !MobileTouchInterfaceService.isMobileDevice) {
      return widget.child;
    }

    return Stack(
      children: [
        MobileTouchInterfaceService.optimizeForMobile(
          widget.child,
          enableSwipeGestures: _config.enableSwipeGestures,
          enablePinchZoom: _config.enablePinchZoom,
          enableDoubleTap: true,
          enableLongPress: true,
        ),
        if (_showMobileSettings) _buildMobileSettingsOverlay(),
        _buildMobileToolbar(),
      ],
    );
  }

  Widget _buildMobileToolbar() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 8,
      right: 8,
      child: Column(
        children: [
          FloatingActionButton.small(
            onPressed: _toggleMobileSettings,
            backgroundColor: Colors.black54,
            heroTag: "mobile_settings",
            child: const Icon(Icons.phone_android, color: Colors.white),
          ),
          const SizedBox(height: 8),
          FloatingActionButton.small(
            onPressed: _showTouchStatistics,
            backgroundColor: Colors.black54,
            heroTag: "touch_stats",
            child: const Icon(Icons.analytics, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileSettingsOverlay() {
    return Positioned.fill(
      child: Container(
        color: Colors.black54,
        child: Center(
          child: Container(
            margin: const EdgeInsets.all(24),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    const Icon(Icons.phone_android),
                    const SizedBox(width: 8),
                    const Text(
                      'Mobile Optimizations',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: _toggleMobileSettings,
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                _buildSettingsContent(),
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _applySettings,
                        child: const Text('Apply'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: OutlinedButton(
                        onPressed: _resetSettings,
                        child: const Text('Reset'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsContent() {
    return Column(
      children: [
        _buildSettingsSection('Touch Targets', [
          _buildSliderSetting(
            'Minimum Touch Size',
            _config.minimumTouchTargetSize,
            32.0,
            64.0,
            (value) => _updateConfig(
              _config = TouchConfiguration(
                minimumTouchTargetSize: value,
                fontSize: _config.fontSize,
                buttonPadding: _config.buttonPadding,
                inputPadding: _config.inputPadding,
                listItemPadding: _config.listItemPadding,
                cardMargin: _config.cardMargin,
                cardPadding: _config.cardPadding,
                appBarHeight: _config.appBarHeight,
                appBarTitleSize: _config.appBarTitleSize,
                appBarActionPadding: _config.appBarActionPadding,
                bottomNavFontSize: _config.bottomNavFontSize,
                bottomNavIconSize: _config.bottomNavIconSize,
                fabTextSize: _config.fabTextSize,
                dialogTitleSize: _config.dialogTitleSize,
                dialogPadding: _config.dialogPadding,
                snackBarFontSize: _config.snackBarFontSize,
                snackBarMargin: _config.snackBarMargin,
                enableHapticFeedback: _config.enableHapticFeedback,
                enableSwipeGestures: _config.enableSwipeGestures,
                enablePinchZoom: _config.enablePinchZoom,
              ),
            ),
          ),
          _buildSliderSetting(
            'Font Size',
            _config.fontSize,
            12.0,
            24.0,
            (value) => _updateConfig(
              _config = TouchConfiguration(
                minimumTouchTargetSize: _config.minimumTouchTargetSize,
                fontSize: value,
                buttonPadding: _config.buttonPadding,
                inputPadding: _config.inputPadding,
                listItemPadding: _config.listItemPadding,
                cardMargin: _config.cardMargin,
                cardPadding: _config.cardPadding,
                appBarHeight: _config.appBarHeight,
                appBarTitleSize: _config.appBarTitleSize,
                appBarActionPadding: _config.appBarActionPadding,
                bottomNavFontSize: _config.bottomNavFontSize,
                bottomNavIconSize: _config.bottomNavIconSize,
                fabTextSize: _config.fabTextSize,
                dialogTitleSize: _config.dialogTitleSize,
                dialogPadding: _config.dialogPadding,
                snackBarFontSize: _config.snackBarFontSize,
                snackBarMargin: _config.snackBarMargin,
                enableHapticFeedback: _config.enableHapticFeedback,
                enableSwipeGestures: _config.enableSwipeGestures,
                enablePinchZoom: _config.enablePinchZoom,
              ),
            ),
          ),
        ]),
        const SizedBox(height: 16),
        _buildSettingsSection('Gestures', [
          _buildSwitchSetting(
            'Haptic Feedback',
            _config.enableHapticFeedback,
            (value) => _updateConfig(
              _config = TouchConfiguration(
                minimumTouchTargetSize: _config.minimumTouchTargetSize,
                fontSize: _config.fontSize,
                buttonPadding: _config.buttonPadding,
                inputPadding: _config.inputPadding,
                listItemPadding: _config.listItemPadding,
                cardMargin: _config.cardMargin,
                cardPadding: _config.cardPadding,
                appBarHeight: _config.appBarHeight,
                appBarTitleSize: _config.appBarTitleSize,
                appBarActionPadding: _config.appBarActionPadding,
                bottomNavFontSize: _config.bottomNavFontSize,
                bottomNavIconSize: _config.bottomNavIconSize,
                fabTextSize: _config.fabTextSize,
                dialogTitleSize: _config.dialogTitleSize,
                dialogPadding: _config.dialogPadding,
                snackBarFontSize: _config.snackBarFontSize,
                snackBarMargin: _config.snackBarMargin,
                enableHapticFeedback: value,
                enableSwipeGestures: _config.enableSwipeGestures,
                enablePinchZoom: _config.enablePinchZoom,
              ),
            ),
          ),
          _buildSwitchSetting(
            'Swipe Gestures',
            _config.enableSwipeGestures,
            (value) => _updateConfig(
              _config = TouchConfiguration(
                minimumTouchTargetSize: _config.minimumTouchTargetSize,
                fontSize: _config.fontSize,
                buttonPadding: _config.buttonPadding,
                inputPadding: _config.inputPadding,
                listItemPadding: _config.listItemPadding,
                cardMargin: _config.cardMargin,
                cardPadding: _config.cardPadding,
                appBarHeight: _config.appBarHeight,
                appBarTitleSize: _config.appBarTitleSize,
                appBarActionPadding: _config.appBarActionPadding,
                bottomNavFontSize: _config.bottomNavFontSize,
                bottomNavIconSize: _config.bottomNavIconSize,
                fabTextSize: _config.fabTextSize,
                dialogTitleSize: _config.dialogTitleSize,
                dialogPadding: _config.dialogPadding,
                snackBarFontSize: _config.snackBarFontSize,
                snackBarMargin: _config.snackBarMargin,
                enableHapticFeedback: _config.enableHapticFeedback,
                enableSwipeGestures: value,
                enablePinchZoom: _config.enablePinchZoom,
              ),
            ),
          ),
          _buildSwitchSetting(
            'Pinch to Zoom',
            _config.enablePinchZoom,
            (value) => _updateConfig(
              _config = TouchConfiguration(
                minimumTouchTargetSize: _config.minimumTouchTargetSize,
                fontSize: _config.fontSize,
                buttonPadding: _config.buttonPadding,
                inputPadding: _config.inputPadding,
                listItemPadding: _config.listItemPadding,
                cardMargin: _config.cardMargin,
                cardPadding: _config.cardPadding,
                appBarHeight: _config.appBarHeight,
                appBarTitleSize: _config.appBarTitleSize,
                appBarActionPadding: _config.appBarActionPadding,
                bottomNavFontSize: _config.bottomNavFontSize,
                bottomNavIconSize: _config.bottomNavIconSize,
                fabTextSize: _config.fabTextSize,
                dialogTitleSize: _config.dialogTitleSize,
                dialogPadding: _config.dialogPadding,
                snackBarFontSize: _config.snackBarFontSize,
                snackBarMargin: _config.snackBarMargin,
                enableHapticFeedback: _config.enableHapticFeedback,
                enableSwipeGestures: _config.enableSwipeGestures,
                enablePinchZoom: value,
              ),
            ),
          ),
        ]),
      ],
    );
  }

  Widget _buildSettingsSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        ...children,
      ],
    );
  }

  Widget _buildSliderSetting(
    String label,
    double value,
    double min,
    double max,
    Function(double) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('$label: ${value.toStringAsFixed(0)}'),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: ((max - min) / 2).round(),
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildSwitchSetting(
    String label,
    bool value,
    Function(bool) onChanged,
  ) {
    return SwitchListTile(
      title: Text(label),
      value: value,
      onChanged: onChanged,
      contentPadding: EdgeInsets.zero,
    );
  }

  void _updateConfig(TouchConfiguration newConfig) {
    setState(() {
      _config = newConfig;
    });
  }

  void _toggleMobileSettings() {
    setState(() {
      _showMobileSettings = !_showMobileSettings;
    });

    if (_showMobileSettings) {
      MobileTouchInterfaceService.hapticFeedback(HapticFeedbackType.light);
    }
  }

  void _applySettings() {
    MobileTouchInterfaceService.updateConfiguration(_config);
    _toggleMobileSettings();

    ScaffoldMessenger.of(context).showSnackBar(
      MobileTouchInterfaceService.getMobileSnackBar(
        message: 'Mobile settings applied',
        backgroundColor: Colors.green,
      ),
    );
  }

  void _resetSettings() {
    setState(() {
      _config = const TouchConfiguration();
    });

    MobileTouchInterfaceService.updateConfiguration(_config);

    ScaffoldMessenger.of(context).showSnackBar(
      MobileTouchInterfaceService.getMobileSnackBar(
        message: 'Settings reset to defaults',
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showTouchStatistics() {
    final stats = MobileTouchInterfaceService.getTouchStatistics();

    showDialog(
      context: context,
      builder: (context) => MobileTouchInterfaceService.getMobileDialog(
        title: 'Touch Statistics',
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatRow('Total Events', '${stats.totalEvents}'),
            _buildStatRow(
              'Average Response',
              '${stats.averageResponseTime.inMilliseconds}ms',
            ),
            _buildStatRow(
              'Mobile Optimized',
              stats.isMobileOptimized ? 'Yes' : 'No',
            ),
            const SizedBox(height: 16),
            const Text(
              'Events by Type:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            ...stats.eventsByType.entries.map((entry) {
              return _buildStatRow(
                entry.key.toString().split('.').last,
                '${entry.value}',
              );
            }),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              MobileTouchInterfaceService.clearTouchHistory();
              Navigator.of(context).pop();
            },
            child: const Text('Clear'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }
}

/// Mobile-optimized components showcase
class MobileComponentsShowcase extends StatelessWidget {
  const MobileComponentsShowcase({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MobileTouchInterfaceService.getMobileAppBar(
        title: 'Mobile Components',
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(onPressed: () {}, icon: const Icon(Icons.settings)),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Mobile-Optimized Components',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 24),

            // Buttons
            const Text(
              'Buttons:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: MobileTouchInterfaceService.getMobileButton(
                    text: 'Normal',
                    onPressed: () {},
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: MobileTouchInterfaceService.getMobileButton(
                    text: 'Large',
                    onPressed: () {},
                    icon: Icons.star,
                    isLarge: true,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Text Fields
            const Text(
              'Text Fields:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            MobileTouchInterfaceService.getMobileTextField(
              label: 'Name',
              hintText: 'Enter your name',
            ),
            const SizedBox(height: 16),
            MobileTouchInterfaceService.getMobileTextField(
              label: 'Email',
              hintText: 'Enter your email',
              keyboardType: TextInputType.emailAddress,
              suffixIcon: const Icon(Icons.email),
            ),
            const SizedBox(height: 24),

            // Cards
            const Text(
              'Cards:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            MobileTouchInterfaceService.getMobileCard(
              onTap: () {},
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Card Title',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'This is a mobile-optimized card with proper touch targets and spacing.',
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // List Tiles
            const Text(
              'List Items:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            MobileTouchInterfaceService.getMobileListTile(
              title: 'List Item 1',
              subtitle: 'This is a subtitle',
              leading: const Icon(Icons.person),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {},
            ),
            MobileTouchInterfaceService.getMobileListTile(
              title: 'List Item 2',
              subtitle: 'Another subtitle with more text to show how it wraps',
              leading: const Icon(Icons.settings),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {},
              isThreeLine: true,
            ),
          ],
        ),
      ),
      floatingActionButton: MobileTouchInterfaceService.getMobileFAB(
        onPressed: () {},
        isExtended: true,
        label: 'Add Item',
        child: const Icon(Icons.add),
      ),
      bottomNavigationBar:
          MobileTouchInterfaceService.getMobileBottomNavigation(
            items: const [
              BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
              BottomNavigationBarItem(
                icon: Icon(Icons.search),
                label: 'Search',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.person),
                label: 'Profile',
              ),
            ],
            currentIndex: 0,
            onTap: (index) {},
          ),
    );
  }
}
