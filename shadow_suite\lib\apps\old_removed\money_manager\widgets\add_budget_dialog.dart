import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/money_manager_models.dart';
import '../services/money_manager_providers.dart';

class AddBudgetDialog extends ConsumerStatefulWidget {
  final Budget? budget;

  const AddBudgetDialog({super.key, this.budget});

  @override
  ConsumerState<AddBudgetDialog> createState() => _AddBudgetDialogState();
}

class _AddBudgetDialogState extends ConsumerState<AddBudgetDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _amountController = TextEditingController();
  final _alertThresholdController = TextEditingController();

  String? _selectedCategoryId;
  BudgetPeriod _selectedPeriod = BudgetPeriod.monthly;
  DateTime _startDate = DateTime.now();
  bool _alertEnabled = true;

  @override
  void initState() {
    super.initState();
    if (widget.budget != null) {
      _initializeFromBudget();
    } else {
      _alertThresholdController.text = '80';
    }
  }

  void _initializeFromBudget() {
    final budget = widget.budget!;
    _nameController.text = budget.name;
    _amountController.text = budget.amount.toString();
    _alertThresholdController.text = (budget.alertThreshold * 100).toString();
    _selectedCategoryId = budget.categoryId;
    _selectedPeriod = budget.period;
    _startDate = budget.startDate;
    _alertEnabled = budget.alertEnabled;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _amountController.dispose();
    _alertThresholdController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final categoriesAsync = ref.watch(categoriesProvider);

    return Dialog(
      child: Container(
        width: 600,
        constraints: const BoxConstraints(maxHeight: 700),
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildNameField(),
                      const SizedBox(height: 20),
                      _buildCategorySelector(categoriesAsync),
                      const SizedBox(height: 20),
                      _buildAmountField(),
                      const SizedBox(height: 20),
                      _buildPeriodSelector(),
                      const SizedBox(height: 20),
                      _buildDatePicker(),
                      const SizedBox(height: 20),
                      _buildAlertSettings(),
                    ],
                  ),
                ),
              ),
            ),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Color(0xFF9B59B6),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Row(
        children: [
          const Icon(Icons.pie_chart, color: Colors.white, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              widget.budget == null ? 'Create Budget' : 'Edit Budget',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildNameField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Budget Name',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _nameController,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            hintText: 'e.g., Monthly Groceries, Entertainment Budget',
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter a budget name';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildCategorySelector(AsyncValue<List<Category>> categoriesAsync) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Category',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 8),
        categoriesAsync.when(
          data: (categories) => DropdownButtonFormField<String>(
            value: _selectedCategoryId,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'Select category',
            ),
            items: categories.map((category) => DropdownMenuItem(
              value: category.id,
              child: Row(
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Color(int.parse(category.color.replaceFirst('#', '0xFF'))).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Icon(
                      _getCategoryIcon(category.icon),
                      size: 14,
                      color: Color(int.parse(category.color.replaceFirst('#', '0xFF'))),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(category.name),
                ],
              ),
            )).toList(),
            onChanged: (value) => setState(() => _selectedCategoryId = value),
            validator: (value) => value == null ? 'Please select a category' : null,
          ),
          loading: () => const CircularProgressIndicator(),
          error: (error, stack) => Text('Error: $error'),
        ),
      ],
    );
  }

  Widget _buildAmountField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Budget Amount',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _amountController,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            prefixText: '\$ ',
            border: OutlineInputBorder(),
            hintText: '0.00',
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter a budget amount';
            }
            if (double.tryParse(value) == null) {
              return 'Please enter a valid number';
            }
            if (double.parse(value) <= 0) {
              return 'Amount must be greater than 0';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildPeriodSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Budget Period',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: BudgetPeriod.values.map((period) {
            final isSelected = _selectedPeriod == period;
            return GestureDetector(
              onTap: () => setState(() => _selectedPeriod = period),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? const Color(0xFF9B59B6) : Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected ? const Color(0xFF9B59B6) : Colors.grey.shade300,
                  ),
                ),
                child: Text(
                  period.name.toUpperCase(),
                  style: TextStyle(
                    color: isSelected ? Colors.white : const Color(0xFF7F8C8D),
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                    fontSize: 12,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildDatePicker() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Start Date',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: _selectStartDate,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade400),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              children: [
                const Icon(Icons.calendar_today, size: 20),
                const SizedBox(width: 12),
                Text(
                  '${_startDate.month}/${_startDate.day}/${_startDate.year}',
                  style: const TextStyle(fontSize: 16),
                ),
                const Spacer(),
                const Icon(Icons.arrow_drop_down),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAlertSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Alert Settings',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 12),
        CheckboxListTile(
          title: const Text('Enable budget alerts'),
          subtitle: const Text('Get notified when approaching budget limit'),
          value: _alertEnabled,
          onChanged: (value) => setState(() => _alertEnabled = value!),
          controlAffinity: ListTileControlAffinity.leading,
        ),
        if (_alertEnabled) ...[
          const SizedBox(height: 12),
          TextFormField(
            controller: _alertThresholdController,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: 'Alert threshold (%)',
              hintText: '80',
              suffixText: '%',
            ),
            validator: (value) {
              if (_alertEnabled && (value == null || value.isEmpty)) {
                return 'Please enter alert threshold';
              }
              if (_alertEnabled && value != null && double.tryParse(value) == null) {
                return 'Please enter a valid number';
              }
              if (_alertEnabled && value != null && (double.parse(value) < 1 || double.parse(value) > 100)) {
                return 'Threshold must be between 1 and 100';
              }
              return null;
            },
          ),
        ],
      ],
    );
  }

  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Color(0xFFF8F9FA),
        border: Border(
          top: BorderSide(color: Color(0xFFE9ECEF)),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _saveBudget,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF27AE60),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(widget.budget == null ? 'Create Budget' : 'Update Budget'),
            ),
          ),
        ],
      ),
    );
  }

  void _selectStartDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (date != null) {
      setState(() => _startDate = date);
    }
  }

  void _saveBudget() {
    if (!_formKey.currentState!.validate()) return;

    final amount = double.parse(_amountController.text);
    final alertThreshold = _alertEnabled ? double.parse(_alertThresholdController.text) / 100 : 0.8;
    
    // Calculate end date based on period
    DateTime endDate;
    switch (_selectedPeriod) {
      case BudgetPeriod.weekly:
        endDate = _startDate.add(const Duration(days: 7));
        break;
      case BudgetPeriod.monthly:
        endDate = DateTime(_startDate.year, _startDate.month + 1, _startDate.day);
        break;
      case BudgetPeriod.quarterly:
        endDate = DateTime(_startDate.year, _startDate.month + 3, _startDate.day);
        break;
      case BudgetPeriod.yearly:
        endDate = DateTime(_startDate.year + 1, _startDate.month, _startDate.day);
        break;
    }

    final budget = Budget(
      id: widget.budget?.id ?? 'bud_${DateTime.now().millisecondsSinceEpoch}',
      name: _nameController.text,
      categoryId: _selectedCategoryId!,
      amount: amount,
      spent: widget.budget?.spent ?? 0.0,
      period: _selectedPeriod,
      startDate: _startDate,
      endDate: endDate,
      alertEnabled: _alertEnabled,
      alertThreshold: alertThreshold,
      createdAt: widget.budget?.createdAt ?? DateTime.now(),
    );

    if (widget.budget == null) {
      ref.read(budgetsProvider.notifier).addBudget(budget);
    } else {
      ref.read(budgetsProvider.notifier).updateBudget(budget);
    }

    Navigator.of(context).pop();
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(widget.budget == null 
            ? 'Budget created successfully' 
            : 'Budget updated successfully'),
        backgroundColor: const Color(0xFF27AE60),
      ),
    );
  }

  IconData _getCategoryIcon(String iconName) {
    switch (iconName) {
      case 'restaurant':
        return Icons.restaurant;
      case 'directions_car':
        return Icons.directions_car;
      case 'shopping_bag':
        return Icons.shopping_bag;
      case 'electrical_services':
        return Icons.electrical_services;
      case 'movie':
        return Icons.movie;
      case 'local_hospital':
        return Icons.local_hospital;
      case 'work':
        return Icons.work;
      case 'laptop':
        return Icons.laptop;
      case 'trending_up':
        return Icons.trending_up;
      default:
        return Icons.category;
    }
  }
}
