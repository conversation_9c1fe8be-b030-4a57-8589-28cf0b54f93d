^D:\PROJECTS\T2 - COPY\SHADOW_SUITE\BUILD\WINDOWS\X64\CMAKEFILES\4478B30B68D9BCCC86A803D770162C1F\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SD:/projects/t2 - Copy/shadow_suite/windows" "-BD:/projects/t2 - Copy/shadow_suite/build/windows/x64" --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file "D:/projects/t2 - Copy/shadow_suite/build/windows/x64/shadow_suite.sln"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
