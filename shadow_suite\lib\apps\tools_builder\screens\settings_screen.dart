import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/app_theme.dart';
import '../services/settings_providers.dart';
import '../services/tools_builder_providers.dart';

class ToolsBuilderSettingsScreen extends ConsumerStatefulWidget {
  const ToolsBuilderSettingsScreen({super.key});

  @override
  ConsumerState<ToolsBuilderSettingsScreen> createState() =>
      _ToolsBuilderSettingsScreenState();
}

class _ToolsBuilderSettingsScreenState
    extends ConsumerState<ToolsBuilderSettingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String? _livePreviewMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _showLivePreview(String message) {
    setState(() {
      _livePreviewMessage = message;
    });

    // Clear the message after 2 seconds
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _livePreviewMessage = null;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Tools Builder Settings'),
        backgroundColor: AppTheme.toolsBuilderColor,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.settings), text: 'General'),
            Tab(icon: Icon(Icons.table_chart), text: 'Spreadsheet'),
            Tab(icon: Icon(Icons.design_services), text: 'UI Builder'),
            Tab(icon: Icon(Icons.calculate), text: 'Formulas'),
            Tab(icon: Icon(Icons.import_export), text: 'Import/Export'),
            Tab(icon: Icon(Icons.security), text: 'Advanced'),
          ],
        ),
      ),
      body: Stack(
        children: [
          TabBarView(
            controller: _tabController,
            children: [
              _buildGeneralSettings(),
              _buildSpreadsheetSettings(),
              _buildUIBuilderSettings(),
              _buildFormulaSettings(),
              _buildImportExportSettings(),
              _buildAdvancedSettings(),
            ],
          ),

          // Live preview notification
          if (_livePreviewMessage != null)
            Positioned(
              top: 16,
              right: 16,
              child: Material(
                elevation: 4,
                borderRadius: BorderRadius.circular(8),
                color: Colors.green,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.check_circle,
                        color: Colors.white,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _livePreviewMessage!,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildGeneralSettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSettingsSection('Application Preferences', [
            Consumer(
              builder: (context, ref, child) {
                final autoSave = ref.watch(autoSaveEnabledProvider);
                return SwitchListTile(
                  title: const Text('Auto-save'),
                  subtitle: const Text(
                    'Automatically save changes every 30 seconds',
                  ),
                  value: autoSave,
                  onChanged: (value) {
                    updateSetting(
                      ref,
                      (settings) => settings.copyWith(autoSaveEnabled: value),
                    );
                    _showLivePreview(
                      'Auto-save ${value ? 'enabled' : 'disabled'}',
                    );
                  },
                );
              },
            ),
            Consumer(
              builder: (context, ref, child) {
                final confirmDelete = ref.watch(confirmDeleteProvider);
                return SwitchListTile(
                  title: const Text('Confirm deletions'),
                  subtitle: const Text(
                    'Show confirmation dialog before deleting items',
                  ),
                  value: confirmDelete,
                  onChanged: (value) {
                    updateSetting(
                      ref,
                      (settings) => settings.copyWith(confirmDelete: value),
                    );
                  },
                );
              },
            ),
            Consumer(
              builder: (context, ref, child) {
                final showTips = ref.watch(showTipsProvider);
                return SwitchListTile(
                  title: const Text('Show tips'),
                  subtitle: const Text('Display helpful tips and tutorials'),
                  value: showTips,
                  onChanged: (value) {
                    updateSetting(
                      ref,
                      (settings) => settings.copyWith(showTips: value),
                    );
                  },
                );
              },
            ),
          ]),
          const SizedBox(height: 24),
          _buildSettingsSection('Interface', [
            Consumer(
              builder: (context, ref, child) {
                final theme = ref.watch(themeProvider);
                return ListTile(
                  title: const Text('Theme'),
                  subtitle: Text('Current: ${theme.name}'),
                  trailing: DropdownButton<AppThemeMode>(
                    value: theme,
                    items: AppThemeMode.values.map((mode) {
                      return DropdownMenuItem(
                        value: mode,
                        child: Text(mode.name.toUpperCase()),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        updateSetting(
                          ref,
                          (settings) => settings.copyWith(theme: value),
                        );
                        _showLivePreview('Theme changed to ${value.name}');
                      }
                    },
                  ),
                );
              },
            ),
            Consumer(
              builder: (context, ref, child) {
                final language = ref.watch(languageProvider);
                return ListTile(
                  title: const Text('Language'),
                  subtitle: Text('Current: $language'),
                  trailing: DropdownButton<String>(
                    value: language,
                    items: const [
                      DropdownMenuItem(
                        value: 'English',
                        child: Text('English'),
                      ),
                      DropdownMenuItem(value: 'Arabic', child: Text('العربية')),
                      DropdownMenuItem(
                        value: 'French',
                        child: Text('Français'),
                      ),
                      DropdownMenuItem(
                        value: 'Spanish',
                        child: Text('Español'),
                      ),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        updateSetting(
                          ref,
                          (settings) => settings.copyWith(language: value),
                        );
                      }
                    },
                  ),
                );
              },
            ),
          ]),
          const SizedBox(height: 24),
          _buildSettingsSection('Performance', [
            Consumer(
              builder: (context, ref, child) {
                final maxUndoSteps = ref.watch(maxUndoStepsProvider);
                return ListTile(
                  title: const Text('Undo history'),
                  subtitle: Text('Maximum undo steps: $maxUndoSteps'),
                  trailing: SizedBox(
                    width: 100,
                    child: Slider(
                      value: maxUndoSteps.toDouble(),
                      min: 10,
                      max: 100,
                      divisions: 9,
                      label: maxUndoSteps.toString(),
                      onChanged: (value) {
                        updateSetting(
                          ref,
                          (settings) =>
                              settings.copyWith(maxUndoSteps: value.toInt()),
                        );
                      },
                    ),
                  ),
                );
              },
            ),
            Consumer(
              builder: (context, ref, child) {
                final cacheSize = ref.watch(cacheSizeProvider);
                return ListTile(
                  title: const Text('Cache size'),
                  subtitle: Text('${cacheSize}MB cache limit'),
                  trailing: SizedBox(
                    width: 100,
                    child: Slider(
                      value: cacheSize.toDouble(),
                      min: 50,
                      max: 500,
                      divisions: 9,
                      label: '${cacheSize}MB',
                      onChanged: (value) {
                        updateSetting(
                          ref,
                          (settings) =>
                              settings.copyWith(cacheSize: value.toInt()),
                        );
                      },
                    ),
                  ),
                );
              },
            ),
          ]),
        ],
      ),
    );
  }

  Widget _buildSpreadsheetSettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSettingsSection('Grid Display', [
            Consumer(
              builder: (context, ref, child) {
                final showGridLines = ref.watch(showGridLinesProvider);
                return SwitchListTile(
                  title: const Text('Show grid lines'),
                  subtitle: const Text('Display cell borders in spreadsheet'),
                  value: showGridLines,
                  onChanged: (value) {
                    updateSetting(
                      ref,
                      (settings) => settings.copyWith(showGridLines: value),
                    );
                  },
                );
              },
            ),
            Consumer(
              builder: (context, ref, child) {
                final showRowNumbers = ref.watch(showRowNumbersProvider);
                return SwitchListTile(
                  title: const Text('Show row numbers'),
                  subtitle: const Text('Display row numbers on the left'),
                  value: showRowNumbers,
                  onChanged: (value) {
                    updateSetting(
                      ref,
                      (settings) => settings.copyWith(showRowNumbers: value),
                    );
                  },
                );
              },
            ),
            Consumer(
              builder: (context, ref, child) {
                final showColumnHeaders = ref.watch(showColumnHeadersProvider);
                return SwitchListTile(
                  title: const Text('Show column headers'),
                  subtitle: const Text('Display column letters at the top'),
                  value: showColumnHeaders,
                  onChanged: (value) {
                    updateSetting(
                      ref,
                      (settings) => settings.copyWith(showColumnHeaders: value),
                    );
                  },
                );
              },
            ),
          ]),
          const SizedBox(height: 24),
          _buildSettingsSection('Cell Behavior', [
            Consumer(
              builder: (context, ref, child) {
                final autoComplete = ref.watch(autoCompleteProvider);
                return SwitchListTile(
                  title: const Text('Auto-complete'),
                  subtitle: const Text('Suggest values based on column data'),
                  value: autoComplete,
                  onChanged: (value) {
                    updateSetting(
                      ref,
                      (settings) => settings.copyWith(autoComplete: value),
                    );
                  },
                );
              },
            ),
            Consumer(
              builder: (context, ref, child) {
                final autoFill = ref.watch(autoFillProvider);
                return SwitchListTile(
                  title: const Text('Auto-fill'),
                  subtitle: const Text(
                    'Automatically fill series when dragging',
                  ),
                  value: autoFill,
                  onChanged: (value) {
                    updateSetting(
                      ref,
                      (settings) => settings.copyWith(autoFill: value),
                    );
                  },
                );
              },
            ),
            Consumer(
              builder: (context, ref, child) {
                final defaultCellWidth = ref.watch(defaultCellWidthProvider);
                return ListTile(
                  title: const Text('Default cell width'),
                  subtitle: Text('${defaultCellWidth}px'),
                  trailing: SizedBox(
                    width: 100,
                    child: Slider(
                      value: defaultCellWidth,
                      min: 50,
                      max: 200,
                      divisions: 15,
                      label: '${defaultCellWidth.toInt()}px',
                      onChanged: (value) {
                        updateSetting(
                          ref,
                          (settings) =>
                              settings.copyWith(defaultCellWidth: value),
                        );
                      },
                    ),
                  ),
                );
              },
            ),
            Consumer(
              builder: (context, ref, child) {
                final defaultCellHeight = ref.watch(defaultCellHeightProvider);
                return ListTile(
                  title: const Text('Default cell height'),
                  subtitle: Text('${defaultCellHeight}px'),
                  trailing: SizedBox(
                    width: 100,
                    child: Slider(
                      value: defaultCellHeight,
                      min: 20,
                      max: 60,
                      divisions: 8,
                      label: '${defaultCellHeight.toInt()}px',
                      onChanged: (value) {
                        updateSetting(
                          ref,
                          (settings) =>
                              settings.copyWith(defaultCellHeight: value),
                        );
                      },
                    ),
                  ),
                );
              },
            ),
          ]),
        ],
      ),
    );
  }

  Widget _buildUIBuilderSettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSettingsSection('Canvas Settings', [
            Consumer(
              builder: (context, ref, child) {
                final showGrid = ref.watch(showGridProvider);
                return SwitchListTile(
                  title: const Text('Show grid'),
                  subtitle: const Text('Display grid on design canvas'),
                  value: showGrid,
                  onChanged: (value) {
                    ref.read(showGridProvider.notifier).state = value;
                    _showLivePreview('Grid ${value ? 'enabled' : 'disabled'}');
                  },
                );
              },
            ),
            Consumer(
              builder: (context, ref, child) {
                final snapToGrid = ref.watch(snapToGridProvider);
                return SwitchListTile(
                  title: const Text('Snap to grid'),
                  subtitle: const Text(
                    'Automatically align components to grid',
                  ),
                  value: snapToGrid,
                  onChanged: (value) {
                    ref.read(snapToGridProvider.notifier).state = value;
                    _showLivePreview(
                      'Snap to grid ${value ? 'enabled' : 'disabled'}',
                    );
                  },
                );
              },
            ),
            Consumer(
              builder: (context, ref, child) {
                final gridSize = ref.watch(gridSizeProvider);
                return ListTile(
                  title: const Text('Grid size'),
                  subtitle: Text('${gridSize.toInt()}px spacing'),
                  trailing: SizedBox(
                    width: 100,
                    child: Slider(
                      value: gridSize,
                      min: 5,
                      max: 50,
                      divisions: 9,
                      label: '${gridSize.toInt()}px',
                      onChanged: (value) {
                        ref.read(gridSizeProvider.notifier).state = value;
                        _showLivePreview('Grid size set to ${value.toInt()}px');
                      },
                    ),
                  ),
                );
              },
            ),
          ]),
          const SizedBox(height: 24),
          _buildSettingsSection('Component Behavior', [
            Consumer(
              builder: (context, ref, child) {
                final showComponentOutlines = ref.watch(
                  showComponentOutlinesProvider,
                );
                return SwitchListTile(
                  title: const Text('Show component outlines'),
                  subtitle: const Text('Display borders around components'),
                  value: showComponentOutlines,
                  onChanged: (value) {
                    ref.read(showComponentOutlinesProvider.notifier).state =
                        value;
                  },
                );
              },
            ),
            Consumer(
              builder: (context, ref, child) {
                final showComponentLabels = ref.watch(
                  showComponentLabelsProvider,
                );
                return SwitchListTile(
                  title: const Text('Show component labels'),
                  subtitle: const Text('Display component type labels'),
                  value: showComponentLabels,
                  onChanged: (value) {
                    ref.read(showComponentLabelsProvider.notifier).state =
                        value;
                  },
                );
              },
            ),
            Consumer(
              builder: (context, ref, child) {
                final autoSelectOnDrop = ref.watch(autoSelectOnDropProvider);
                return SwitchListTile(
                  title: const Text('Auto-select on drop'),
                  subtitle: const Text(
                    'Automatically select components when dropped',
                  ),
                  value: autoSelectOnDrop,
                  onChanged: (value) {
                    ref.read(autoSelectOnDropProvider.notifier).state = value;
                  },
                );
              },
            ),
          ]),
        ],
      ),
    );
  }

  Widget _buildFormulaSettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSettingsSection('Calculation Settings', [
            Consumer(
              builder: (context, ref, child) {
                final autoCalculate = ref.watch(autoCalculateProvider);
                return SwitchListTile(
                  title: const Text('Automatic calculation'),
                  subtitle: const Text(
                    'Recalculate formulas when values change',
                  ),
                  value: autoCalculate,
                  onChanged: (value) {
                    // Note: autoCalculateProvider is a Provider, not StateProvider
                    // This setting would need to be implemented in the settings system
                    _showLivePreview(
                      'Auto-calculate ${value ? 'enabled' : 'disabled'}',
                    );
                  },
                );
              },
            ),
            Consumer(
              builder: (context, ref, child) {
                final showFormulaBar = ref.watch(showFormulaBarProvider);
                return SwitchListTile(
                  title: const Text('Show formula bar'),
                  subtitle: const Text('Display formula editing bar'),
                  value: showFormulaBar,
                  onChanged: (value) {
                    ref.read(showFormulaBarProvider.notifier).state = value;
                    _showLivePreview(
                      'Formula bar ${value ? 'enabled' : 'disabled'}',
                    );
                  },
                );
              },
            ),
            Consumer(
              builder: (context, ref, child) {
                final highlightDependencies = ref.watch(
                  highlightDependenciesProvider,
                );
                return SwitchListTile(
                  title: const Text('Highlight dependencies'),
                  subtitle: const Text(
                    'Show cell dependencies when editing formulas',
                  ),
                  value: highlightDependencies,
                  onChanged: (value) {
                    ref.read(highlightDependenciesProvider.notifier).state =
                        value;
                    _showLivePreview(
                      'Dependencies ${value ? 'enabled' : 'disabled'}',
                    );
                  },
                );
              },
            ),
          ]),
          const SizedBox(height: 24),
          _buildSettingsSection('Error Handling', [
            Consumer(
              builder: (context, ref, child) {
                final showFormulaErrors = ref.watch(showFormulaErrorsProvider);
                return SwitchListTile(
                  title: const Text('Show formula errors'),
                  subtitle: const Text(
                    'Display error indicators for invalid formulas',
                  ),
                  value: showFormulaErrors,
                  onChanged: (value) {
                    ref.read(showFormulaErrorsProvider.notifier).state = value;
                    _showLivePreview(
                      'Formula errors ${value ? 'enabled' : 'disabled'}',
                    );
                  },
                );
              },
            ),
            Consumer(
              builder: (context, ref, child) {
                final errorNotifications = ref.watch(
                  errorNotificationsProvider,
                );
                return SwitchListTile(
                  title: const Text('Error notifications'),
                  subtitle: const Text(
                    'Show popup notifications for calculation errors',
                  ),
                  value: errorNotifications,
                  onChanged: (value) {
                    ref.read(errorNotificationsProvider.notifier).state = value;
                    _showLivePreview(
                      'Error notifications ${value ? 'enabled' : 'disabled'}',
                    );
                  },
                );
              },
            ),
          ]),
        ],
      ),
    );
  }

  Widget _buildImportExportSettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSettingsSection('Excel Compatibility', [
            Consumer(
              builder: (context, ref, child) {
                final preserveFormatting = ref.watch(
                  preserveFormattingProvider,
                );
                return SwitchListTile(
                  title: const Text('Preserve formatting'),
                  subtitle: const Text(
                    'Keep cell formatting when importing/exporting',
                  ),
                  value: preserveFormatting,
                  onChanged: (value) {
                    ref.read(preserveFormattingProvider.notifier).state = value;
                    _showLivePreview(
                      'Preserve formatting ${value ? 'enabled' : 'disabled'}',
                    );
                  },
                );
              },
            ),
            Consumer(
              builder: (context, ref, child) {
                final preserveFormulas = ref.watch(preserveFormulasProvider);
                return SwitchListTile(
                  title: const Text('Preserve formulas'),
                  subtitle: const Text(
                    'Keep formulas when importing/exporting',
                  ),
                  value: preserveFormulas,
                  onChanged: (value) {
                    ref.read(preserveFormulasProvider.notifier).state = value;
                    _showLivePreview(
                      'Preserve formulas ${value ? 'enabled' : 'disabled'}',
                    );
                  },
                );
              },
            ),
            Consumer(
              builder: (context, ref, child) {
                final autoDetectDataTypes = ref.watch(
                  autoDetectDataTypesProvider,
                );
                return SwitchListTile(
                  title: const Text('Auto-detect data types'),
                  subtitle: const Text(
                    'Automatically detect numbers, dates, etc.',
                  ),
                  value: autoDetectDataTypes,
                  onChanged: (value) {
                    ref.read(autoDetectDataTypesProvider.notifier).state =
                        value;
                    _showLivePreview(
                      'Auto-detect data types ${value ? 'enabled' : 'disabled'}',
                    );
                  },
                );
              },
            ),
          ]),
          const SizedBox(height: 24),
          _buildSettingsSection('File Handling', [
            Consumer(
              builder: (context, ref, child) {
                final defaultExportFormat = ref.watch(
                  defaultExportFormatProvider,
                );
                return ListTile(
                  title: const Text('Default export format'),
                  subtitle: Text('Current: $defaultExportFormat'),
                  trailing: DropdownButton<String>(
                    value: defaultExportFormat,
                    items: const [
                      DropdownMenuItem(
                        value: 'xlsx',
                        child: Text('Excel (.xlsx)'),
                      ),
                      DropdownMenuItem(value: 'csv', child: Text('CSV (.csv)')),
                      DropdownMenuItem(
                        value: 'json',
                        child: Text('JSON (.json)'),
                      ),
                      DropdownMenuItem(value: 'pdf', child: Text('PDF (.pdf)')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        ref.read(defaultExportFormatProvider.notifier).state =
                            value;
                        _showLivePreview('Default export format: $value');
                      }
                    },
                  ),
                );
              },
            ),
            Consumer(
              builder: (context, ref, child) {
                final compressionLevel = ref.watch(compressionLevelProvider);
                return ListTile(
                  title: const Text('Compression level'),
                  subtitle: Text(
                    'Level $compressionLevel (${_getCompressionDescription(compressionLevel)})',
                  ),
                  trailing: SizedBox(
                    width: 100,
                    child: Slider(
                      value: compressionLevel.toDouble(),
                      min: 1,
                      max: 9,
                      divisions: 8,
                      label: compressionLevel.toString(),
                      onChanged: (value) {
                        ref.read(compressionLevelProvider.notifier).state =
                            value.round();
                        _showLivePreview('Compression level: ${value.round()}');
                      },
                    ),
                  ),
                );
              },
            ),
          ]),
        ],
      ),
    );
  }

  Widget _buildAdvancedSettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSettingsSection('Security', [
            Consumer(
              builder: (context, ref, child) {
                final enableBackups = ref.watch(enableBackupsProvider);
                return SwitchListTile(
                  title: const Text('Enable backups'),
                  subtitle: const Text('Automatically backup tools and data'),
                  value: enableBackups,
                  onChanged: (value) {
                    ref.read(enableBackupsProvider.notifier).state = value;
                    _showLivePreview(
                      'Backups ${value ? 'enabled' : 'disabled'}',
                    );
                  },
                );
              },
            ),
            Consumer(
              builder: (context, ref, child) {
                final encryptData = ref.watch(encryptDataProvider);
                return SwitchListTile(
                  title: const Text('Encrypt data'),
                  subtitle: const Text('Encrypt sensitive tool data'),
                  value: encryptData,
                  onChanged: (value) {
                    ref.read(encryptDataProvider.notifier).state = value;
                    _showLivePreview(
                      'Data encryption ${value ? 'enabled' : 'disabled'}',
                    );
                  },
                );
              },
            ),
          ]),
          const SizedBox(height: 24),
          _buildSettingsSection('Developer Options', [
            Consumer(
              builder: (context, ref, child) {
                final debugMode = ref.watch(debugModeProvider);
                return SwitchListTile(
                  title: const Text('Debug mode'),
                  subtitle: const Text('Enable debugging features'),
                  value: debugMode,
                  onChanged: (value) {
                    ref.read(debugModeProvider.notifier).state = value;
                    _showLivePreview(
                      'Debug mode ${value ? 'enabled' : 'disabled'}',
                    );
                  },
                );
              },
            ),
            Consumer(
              builder: (context, ref, child) {
                final showPerformanceMetrics = ref.watch(
                  showPerformanceMetricsProvider,
                );
                return SwitchListTile(
                  title: const Text('Show performance metrics'),
                  subtitle: const Text(
                    'Display calculation and rendering times',
                  ),
                  value: showPerformanceMetrics,
                  onChanged: (value) {
                    ref.read(showPerformanceMetricsProvider.notifier).state =
                        value;
                    _showLivePreview(
                      'Performance metrics ${value ? 'enabled' : 'disabled'}',
                    );
                  },
                );
              },
            ),
            ListTile(
              title: const Text('Clear cache'),
              subtitle: const Text('Clear all cached data and temporary files'),
              trailing: ElevatedButton(
                onPressed: () => _clearCache(),
                child: const Text('Clear'),
              ),
            ),
            ListTile(
              title: const Text('Reset settings'),
              subtitle: const Text('Reset all settings to default values'),
              trailing: ElevatedButton(
                onPressed: () => _resetSettings(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Reset'),
              ),
            ),
          ]),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(String title, List<Widget> children) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.toolsBuilderColor,
              ),
            ),
            const SizedBox(height: 8),
            ...children,
          ],
        ),
      ),
    );
  }

  String _getCompressionDescription(int level) {
    if (level <= 3) return 'Fast';
    if (level <= 6) return 'Balanced';
    return 'Maximum';
  }

  void _clearCache() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cache'),
        content: const Text(
          'Are you sure you want to clear all cached data? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Implement cache clearing
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Cache cleared successfully')),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _resetSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text(
          'Are you sure you want to reset all settings to their default values? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Reset all settings to defaults
              ref.read(settingsProvider.notifier).resetToDefaults();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Settings reset to defaults')),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}
