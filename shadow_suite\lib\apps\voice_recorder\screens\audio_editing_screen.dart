import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:just_audio/just_audio.dart';

/// Audio Editing Screen with waveform visualization and editing tools
class AudioEditingScreen extends ConsumerStatefulWidget {
  final String recordingId;
  final String recordingTitle;
  final String filePath;

  const AudioEditingScreen({
    super.key,
    required this.recordingId,
    required this.recordingTitle,
    required this.filePath,
  });

  @override
  ConsumerState<AudioEditingScreen> createState() => _AudioEditingScreenState();
}

class _AudioEditingScreenState extends ConsumerState<AudioEditingScreen> {
  late AudioPlayer _audioPlayer;
  bool _isPlaying = false;
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  
  // Editing state
  double _startTrim = 0.0;
  double _endTrim = 1.0;
  double _volume = 1.0;
  double _speed = 1.0;
  bool _noiseReduction = false;
  bool _hasUnsavedChanges = false;
  
  // Waveform data (simulated)
  List<double> _waveformData = [];

  @override
  void initState() {
    super.initState();
    _audioPlayer = AudioPlayer();
    _initializePlayer();
    _generateWaveformData();
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Audio Editor'),
        backgroundColor: Colors.blueGrey,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.undo),
            onPressed: _hasUnsavedChanges ? _undoChanges : null,
          ),
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _hasUnsavedChanges ? _saveChanges : null,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFileInfo(),
          _buildWaveformViewer(),
          _buildPlaybackControls(),
          _buildEditingTools(),
          _buildEffectsPanel(),
        ],
      ),
    );
  }

  Widget _buildFileInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blueGrey.withValues(alpha: 0.1),
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.recordingTitle,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Text(
                'Duration: ${_formatDuration(_duration)}',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
              const SizedBox(width: 16),
              Text(
                'File: ${widget.filePath.split('/').last}',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWaveformViewer() {
    return Container(
      height: 150,
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Waveform display
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(8),
              child: CustomPaint(
                painter: WaveformPainter(
                  waveformData: _waveformData,
                  progress: _duration.inMilliseconds > 0 
                    ? _position.inMilliseconds / _duration.inMilliseconds 
                    : 0.0,
                  startTrim: _startTrim,
                  endTrim: _endTrim,
                ),
                size: Size.infinite,
              ),
            ),
          ),
          
          // Trim controls
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                const Text('Trim:', style: TextStyle(fontSize: 12)),
                Expanded(
                  child: RangeSlider(
                    values: RangeValues(_startTrim, _endTrim),
                    onChanged: (values) {
                      setState(() {
                        _startTrim = values.start;
                        _endTrim = values.end;
                        _hasUnsavedChanges = true;
                      });
                    },
                    divisions: 100,
                    labels: RangeLabels(
                      _formatDuration(Duration(milliseconds: (_startTrim * _duration.inMilliseconds).round())),
                      _formatDuration(Duration(milliseconds: (_endTrim * _duration.inMilliseconds).round())),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaybackControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Progress slider
          Slider(
            value: _duration.inMilliseconds > 0 
              ? _position.inMilliseconds / _duration.inMilliseconds 
              : 0.0,
            onChanged: (value) {
              final newPosition = Duration(
                milliseconds: (value * _duration.inMilliseconds).round(),
              );
              _audioPlayer.seek(newPosition);
            },
          ),
          
          // Time display
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(_formatDuration(_position)),
              Text(_formatDuration(_duration)),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Control buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                onPressed: () => _seekRelative(const Duration(seconds: -10)),
                icon: const Icon(Icons.replay_10),
                iconSize: 32,
              ),
              const SizedBox(width: 16),
              Container(
                decoration: BoxDecoration(
                  color: Colors.blueGrey,
                  borderRadius: BorderRadius.circular(30),
                ),
                child: IconButton(
                  onPressed: _togglePlayback,
                  icon: Icon(
                    _isPlaying ? Icons.pause : Icons.play_arrow,
                    color: Colors.white,
                  ),
                  iconSize: 32,
                ),
              ),
              const SizedBox(width: 16),
              IconButton(
                onPressed: () => _seekRelative(const Duration(seconds: 10)),
                icon: const Icon(Icons.forward_10),
                iconSize: 32,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEditingTools() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Editing Tools',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: _buildEditButton(
                  'Cut',
                  Icons.content_cut,
                  () => _cutAudio(),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildEditButton(
                  'Copy',
                  Icons.copy,
                  () => _copyAudio(),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildEditButton(
                  'Paste',
                  Icons.paste,
                  () => _pasteAudio(),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildEditButton(
                  'Delete',
                  Icons.delete,
                  () => _deleteSelection(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEffectsPanel() {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Audio Effects',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            
            // Volume control
            Row(
              children: [
                const Icon(Icons.volume_up, size: 20),
                const SizedBox(width: 8),
                const Text('Volume:', style: TextStyle(fontSize: 14)),
                Expanded(
                  child: Slider(
                    value: _volume,
                    min: 0.0,
                    max: 2.0,
                    divisions: 20,
                    label: '${(_volume * 100).round()}%',
                    onChanged: (value) {
                      setState(() {
                        _volume = value;
                        _hasUnsavedChanges = true;
                      });
                      _audioPlayer.setVolume(value);
                    },
                  ),
                ),
              ],
            ),
            
            // Speed control
            Row(
              children: [
                const Icon(Icons.speed, size: 20),
                const SizedBox(width: 8),
                const Text('Speed:', style: TextStyle(fontSize: 14)),
                Expanded(
                  child: Slider(
                    value: _speed,
                    min: 0.5,
                    max: 2.0,
                    divisions: 15,
                    label: '${_speed.toStringAsFixed(1)}x',
                    onChanged: (value) {
                      setState(() {
                        _speed = value;
                        _hasUnsavedChanges = true;
                      });
                      _audioPlayer.setSpeed(value);
                    },
                  ),
                ),
              ],
            ),
            
            // Noise reduction
            CheckboxListTile(
              title: const Text('Noise Reduction'),
              subtitle: const Text('Remove background noise'),
              value: _noiseReduction,
              onChanged: (value) {
                setState(() {
                  _noiseReduction = value ?? false;
                  _hasUnsavedChanges = true;
                });
              },
            ),
            
            const SizedBox(height: 16),
            
            // Effect buttons
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildEffectButton('Normalize', () => _applyNormalization()),
                _buildEffectButton('Fade In', () => _applyFadeIn()),
                _buildEffectButton('Fade Out', () => _applyFadeOut()),
                _buildEffectButton('Reverse', () => _applyReverse()),
                _buildEffectButton('Echo', () => _applyEcho()),
                _buildEffectButton('Amplify', () => _applyAmplify()),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEditButton(String label, IconData icon, VoidCallback onPressed) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(label, style: const TextStyle(fontSize: 12)),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      ),
    );
  }

  Widget _buildEffectButton(String label, VoidCallback onPressed) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blueGrey.withValues(alpha: 0.1),
        foregroundColor: Colors.blueGrey,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      child: Text(label, style: const TextStyle(fontSize: 12)),
    );
  }

  Future<void> _initializePlayer() async {
    try {
      await _audioPlayer.setFilePath(widget.filePath);
      
      _audioPlayer.durationStream.listen((duration) {
        if (mounted) {
          setState(() => _duration = duration ?? Duration.zero);
        }
      });

      _audioPlayer.positionStream.listen((position) {
        if (mounted) {
          setState(() => _position = position);
        }
      });

      _audioPlayer.playerStateStream.listen((state) {
        if (mounted) {
          setState(() => _isPlaying = state.playing);
        }
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading audio: $e')),
        );
      }
    }
  }

  void _generateWaveformData() {
    // Generate simulated waveform data
    _waveformData = List.generate(200, (index) {
      return (0.5 + 0.5 * (index % 10 / 10)) * (1 - (index - 100).abs() / 100);
    });
  }

  void _togglePlayback() {
    if (_isPlaying) {
      _audioPlayer.pause();
    } else {
      _audioPlayer.play();
    }
  }

  void _seekRelative(Duration offset) {
    final newPosition = _position + offset;
    final clampedPosition = newPosition < Duration.zero 
      ? Duration.zero 
      : newPosition > _duration 
        ? _duration 
        : newPosition;
    _audioPlayer.seek(clampedPosition);
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  // Editing operations
  void _cutAudio() {
    setState(() => _hasUnsavedChanges = true);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Audio cut operation applied')),
    );
  }

  void _copyAudio() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Audio copied to clipboard')),
    );
  }

  void _pasteAudio() {
    setState(() => _hasUnsavedChanges = true);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Audio pasted')),
    );
  }

  void _deleteSelection() {
    setState(() => _hasUnsavedChanges = true);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Selection deleted')),
    );
  }

  // Effect operations
  void _applyNormalization() {
    setState(() => _hasUnsavedChanges = true);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Normalization applied')),
    );
  }

  void _applyFadeIn() {
    setState(() => _hasUnsavedChanges = true);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Fade in effect applied')),
    );
  }

  void _applyFadeOut() {
    setState(() => _hasUnsavedChanges = true);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Fade out effect applied')),
    );
  }

  void _applyReverse() {
    setState(() => _hasUnsavedChanges = true);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Reverse effect applied')),
    );
  }

  void _applyEcho() {
    setState(() => _hasUnsavedChanges = true);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Echo effect applied')),
    );
  }

  void _applyAmplify() {
    setState(() => _hasUnsavedChanges = true);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Amplify effect applied')),
    );
  }

  void _undoChanges() {
    setState(() {
      _startTrim = 0.0;
      _endTrim = 1.0;
      _volume = 1.0;
      _speed = 1.0;
      _noiseReduction = false;
      _hasUnsavedChanges = false;
    });
    _audioPlayer.setVolume(1.0);
    _audioPlayer.setSpeed(1.0);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Changes undone')),
    );
  }

  void _saveChanges() {
    setState(() => _hasUnsavedChanges = false);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Changes saved successfully'),
        backgroundColor: Colors.green,
      ),
    );
  }
}

/// Custom painter for waveform visualization
class WaveformPainter extends CustomPainter {
  final List<double> waveformData;
  final double progress;
  final double startTrim;
  final double endTrim;

  WaveformPainter({
    required this.waveformData,
    required this.progress,
    required this.startTrim,
    required this.endTrim,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blueGrey
      ..strokeWidth = 1;

    final progressPaint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 1;

    final trimPaint = Paint()
      ..color = Colors.red.withValues(alpha: 0.3)
      ..style = PaintingStyle.fill;

    // Draw trim areas
    final startTrimX = startTrim * size.width;
    final endTrimX = endTrim * size.width;
    
    // Left trim area
    canvas.drawRect(
      Rect.fromLTWH(0, 0, startTrimX, size.height),
      trimPaint,
    );
    
    // Right trim area
    canvas.drawRect(
      Rect.fromLTWH(endTrimX, 0, size.width - endTrimX, size.height),
      trimPaint,
    );

    // Draw waveform
    final centerY = size.height / 2;
    final progressX = progress * size.width;

    for (int i = 0; i < waveformData.length; i++) {
      final x = (i / waveformData.length) * size.width;
      final amplitude = waveformData[i] * centerY;
      
      final currentPaint = x <= progressX ? progressPaint : paint;
      
      canvas.drawLine(
        Offset(x, centerY - amplitude),
        Offset(x, centerY + amplitude),
        currentPaint,
      );
    }

    // Draw progress line
    final progressLinePaint = Paint()
      ..color = Colors.red
      ..strokeWidth = 2;
    
    canvas.drawLine(
      Offset(progressX, 0),
      Offset(progressX, size.height),
      progressLinePaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
