import 'dart:math' as math;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/spreadsheet.dart';
import '../models/spreadsheet_cell.dart';
import '../services/enhanced_formula_engine.dart';

// Dependency tracking and circular reference detection
class DependencyTracker {
  final Map<String, Set<String>> _dependencies = {}; // cell -> dependencies
  final Map<String, Set<String>> _dependents = {}; // cell -> dependents
  final Map<String, int> _calculationOrder = {};
  
  // Clear all dependencies
  void clear() {
    _dependencies.clear();
    _dependents.clear();
    _calculationOrder.clear();
  }
  
  // Add dependency relationship
  void addDependency(String cell, String dependsOn) {
    _dependencies.putIfAbsent(cell, () => <String>{}).add(dependsOn);
    _dependents.putIfAbsent(dependsOn, () => <String>{}).add(cell);
  }
  
  // Remove all dependencies for a cell
  void removeDependencies(String cell) {
    final deps = _dependencies[cell];
    if (deps != null) {
      for (final dep in deps) {
        _dependents[dep]?.remove(cell);
      }
      _dependencies.remove(cell);
    }
    
    // Remove as dependent
    final dependents = _dependents[cell];
    if (dependents != null) {
      for (final dependent in dependents) {
        _dependencies[dependent]?.remove(cell);
      }
      _dependents.remove(cell);
    }
  }
  
  // Extract cell references from formula
  Set<String> extractCellReferences(String formula) {
    final references = <String>{};
    
    // Match cell references like A1, B2, etc.
    final cellPattern = RegExp(r'\b([A-Z]+)(\d+)\b');
    final matches = cellPattern.allMatches(formula);
    
    for (final match in matches) {
      references.add(match.group(0)!);
    }
    
    // Match range references like A1:A10
    final rangePattern = RegExp(r'\b([A-Z]+)(\d+):([A-Z]+)(\d+)\b');
    final rangeMatches = rangePattern.allMatches(formula);
    
    for (final match in rangeMatches) {
      final startCol = match.group(1)!;
      final startRow = int.parse(match.group(2)!);
      final endCol = match.group(3)!;
      final endRow = int.parse(match.group(4)!);
      
      // Add all cells in range
      for (int row = startRow; row <= endRow; row++) {
        for (int col = _columnToNumber(startCol); col <= _columnToNumber(endCol); col++) {
          references.add('${_numberToColumn(col)}$row');
        }
      }
    }
    
    return references;
  }
  
  // Update dependencies for a cell
  void updateCellDependencies(String cell, String formula) {
    // Remove existing dependencies
    removeDependencies(cell);
    
    if (formula.startsWith('=')) {
      final references = extractCellReferences(formula);
      for (final ref in references) {
        addDependency(cell, ref);
      }
    }
  }
  
  // Check for circular references
  CircularReferenceResult checkCircularReference(String cell, String formula) {
    final tempTracker = DependencyTracker();
    
    // Copy current dependencies
    for (final entry in _dependencies.entries) {
      for (final dep in entry.value) {
        tempTracker.addDependency(entry.key, dep);
      }
    }
    
    // Add new dependencies
    tempTracker.updateCellDependencies(cell, formula);
    
    // Check for cycles
    final visited = <String>{};
    final recursionStack = <String>{};
    final path = <String>[];
    
    bool hasCycle = _detectCycle(cell, tempTracker._dependencies, visited, recursionStack, path);
    
    if (hasCycle) {
      // Find the cycle path
      final cycleStart = path.indexOf(path.last);
      final cyclePath = path.sublist(cycleStart);
      
      return CircularReferenceResult(
        hasCircularReference: true,
        cyclePath: cyclePath,
        description: 'Circular reference detected: ${cyclePath.join(' → ')} → ${cyclePath.first}',
      );
    }
    
    return const CircularReferenceResult(hasCircularReference: false);
  }
  
  bool _detectCycle(
    String cell,
    Map<String, Set<String>> dependencies,
    Set<String> visited,
    Set<String> recursionStack,
    List<String> path,
  ) {
    visited.add(cell);
    recursionStack.add(cell);
    path.add(cell);
    
    final deps = dependencies[cell] ?? <String>{};
    for (final dep in deps) {
      if (!visited.contains(dep)) {
        if (_detectCycle(dep, dependencies, visited, recursionStack, path)) {
          return true;
        }
      } else if (recursionStack.contains(dep)) {
        path.add(dep);
        return true;
      }
    }
    
    recursionStack.remove(cell);
    if (path.isNotEmpty && path.last == cell) {
      path.removeLast();
    }
    return false;
  }
  
  // Get calculation order (topological sort)
  List<String> getCalculationOrder() {
    final result = <String>[];
    final visited = <String>{};
    final temp = <String>{};
    
    void visit(String cell) {
      if (temp.contains(cell)) {
        // Circular reference detected during sort
        return;
      }
      if (visited.contains(cell)) {
        return;
      }
      
      temp.add(cell);
      
      final deps = _dependencies[cell] ?? <String>{};
      for (final dep in deps) {
        visit(dep);
      }
      
      temp.remove(cell);
      visited.add(cell);
      result.add(cell);
    }
    
    // Visit all cells
    for (final cell in _dependencies.keys) {
      if (!visited.contains(cell)) {
        visit(cell);
      }
    }
    
    return result.reversed.toList();
  }
  
  // Get cells that depend on a given cell
  Set<String> getDependents(String cell) {
    return _dependents[cell] ?? <String>{};
  }
  
  // Get cells that a given cell depends on
  Set<String> getDependencies(String cell) {
    return _dependencies[cell] ?? <String>{};
  }
  
  // Get all cells that need recalculation when a cell changes
  Set<String> getCellsToRecalculate(String changedCell) {
    final toRecalculate = <String>{};
    final queue = <String>[changedCell];
    
    while (queue.isNotEmpty) {
      final cell = queue.removeAt(0);
      final dependents = getDependents(cell);
      
      for (final dependent in dependents) {
        if (!toRecalculate.contains(dependent)) {
          toRecalculate.add(dependent);
          queue.add(dependent);
        }
      }
    }
    
    return toRecalculate;
  }
  
  // Utility methods for column conversion
  int _columnToNumber(String column) {
    int result = 0;
    for (int i = 0; i < column.length; i++) {
      result = result * 26 + (column.codeUnitAt(i) - 'A'.codeUnitAt(0) + 1);
    }
    return result;
  }
  
  String _numberToColumn(int number) {
    String result = '';
    while (number > 0) {
      number--;
      result = String.fromCharCode('A'.codeUnitAt(0) + (number % 26)) + result;
      number ~/= 26;
    }
    return result;
  }
  
  // Get dependency graph for visualization
  DependencyGraph getDependencyGraph() {
    return DependencyGraph(
      dependencies: Map.from(_dependencies),
      dependents: Map.from(_dependents),
    );
  }
}

// Result classes
class CircularReferenceResult {
  final bool hasCircularReference;
  final List<String> cyclePath;
  final String description;
  
  const CircularReferenceResult({
    required this.hasCircularReference,
    this.cyclePath = const [],
    this.description = '',
  });
}

class DependencyGraph {
  final Map<String, Set<String>> dependencies;
  final Map<String, Set<String>> dependents;
  
  const DependencyGraph({
    required this.dependencies,
    required this.dependents,
  });
  
  // Get all cells in the graph
  Set<String> getAllCells() {
    final cells = <String>{};
    cells.addAll(dependencies.keys);
    cells.addAll(dependents.keys);
    return cells;
  }
  
  // Get depth of dependency chain for a cell
  int getDependencyDepth(String cell) {
    final visited = <String>{};
    
    int getDepth(String currentCell) {
      if (visited.contains(currentCell)) return 0;
      visited.add(currentCell);
      
      final deps = dependencies[currentCell] ?? <String>{};
      if (deps.isEmpty) return 0;
      
      int maxDepth = 0;
      for (final dep in deps) {
        maxDepth = math.max(maxDepth, getDepth(dep));
      }
      
      return maxDepth + 1;
    }
    
    return getDepth(cell);
  }
}

// Enhanced spreadsheet calculation provider with dependency tracking
class EnhancedSpreadsheetCalculationNotifier extends StateNotifier<Spreadsheet?> {
  final DependencyTracker _dependencyTracker = DependencyTracker();
  
  EnhancedSpreadsheetCalculationNotifier() : super(null);
  
  void setSpreadsheet(Spreadsheet spreadsheet) {
    state = spreadsheet;
    _rebuildDependencies();
  }
  
  void _rebuildDependencies() {
    _dependencyTracker.clear();
    
    if (state?.activeSheet == null) return;
    
    final sheet = state!.activeSheet!;
    for (final entry in sheet.cells.entries) {
      final cell = entry.value;
      if (cell.rawValue.startsWith('=')) {
        _dependencyTracker.updateCellDependencies(entry.key, cell.rawValue);
      }
    }
  }
  
  void updateCellValue(String cellAddress, dynamic value) {
    if (state?.activeSheet == null) return;
    
    final sheet = state!.activeSheet!;
    final valueStr = value.toString();
    
    // Check for circular reference if it's a formula
    if (valueStr.startsWith('=')) {
      final circularCheck = _dependencyTracker.checkCircularReference(cellAddress, valueStr);
      if (circularCheck.hasCircularReference) {
        // Handle circular reference error
        _updateCellWithError(cellAddress, '#CIRCULAR!', circularCheck.description);
        return;
      }
    }
    
    // Update the cell
    final cell = sheet.cells[cellAddress] ?? SpreadsheetCell(
      row: _getRowFromAddress(cellAddress),
      column: _getColumnFromAddress(cellAddress),
      rawValue: '',
      dataType: CellDataType.text,
      format: const CellFormat(),
    );
    
    final updatedCell = cell.copyWith(
      rawValue: valueStr,
      dataType: _inferDataType(valueStr),
    );
    
    sheet.cells[cellAddress] = updatedCell;
    
    // Update dependencies
    _dependencyTracker.updateCellDependencies(cellAddress, valueStr);
    
    // Recalculate dependent cells
    _recalculateDependents(cellAddress);
    
    // Update state
    state = state!.copyWith();
  }
  
  void _updateCellWithError(String cellAddress, String errorValue, String errorDescription) {
    if (state?.activeSheet == null) return;
    
    final sheet = state!.activeSheet!;
    final cell = sheet.cells[cellAddress] ?? SpreadsheetCell(
      row: _getRowFromAddress(cellAddress),
      column: _getColumnFromAddress(cellAddress),
      rawValue: '',
      dataType: CellDataType.text,
      format: const CellFormat(),
    );
    
    sheet.cells[cellAddress] = cell.copyWith(
      rawValue: errorValue,
      calculatedValue: errorValue,
      dataType: CellDataType.error,
    );
    
    state = state!.copyWith();
  }
  
  void _recalculateDependents(String changedCell) {
    final cellsToRecalculate = _dependencyTracker.getCellsToRecalculate(changedCell);
    final calculationOrder = _dependencyTracker.getCalculationOrder();
    
    // Recalculate in dependency order
    for (final cellAddress in calculationOrder) {
      if (cellsToRecalculate.contains(cellAddress)) {
        _calculateCell(cellAddress);
      }
    }
  }
  
  void _calculateCell(String cellAddress) {
    if (state?.activeSheet == null) return;
    
    final sheet = state!.activeSheet!;
    final cell = sheet.cells[cellAddress];
    
    if (cell == null || !cell.rawValue.startsWith('=')) return;
    
    try {
      // Calculate using enhanced formula engine
      final result = EnhancedFormulaEngine.calculateFormula(cell.rawValue, state!);
      
      final updatedCell = cell.copyWith(
        calculatedValue: result,
        dataType: _inferDataType(result.toString()),
      );
      
      sheet.cells[cellAddress] = updatedCell;
    } catch (e) {
      final updatedCell = cell.copyWith(
        calculatedValue: '#ERROR!',
        dataType: CellDataType.error,
      );
      
      sheet.cells[cellAddress] = updatedCell;
    }
  }
  
  CellDataType _inferDataType(String value) {
    if (value.startsWith('=')) return CellDataType.formula;
    if (value.startsWith('#')) return CellDataType.error;
    if (double.tryParse(value) != null) return CellDataType.number;
    if (DateTime.tryParse(value) != null) return CellDataType.date;
    return CellDataType.text;
  }
  
  // Get dependency information
  DependencyGraph getDependencyGraph() {
    return _dependencyTracker.getDependencyGraph();
  }
  
  Set<String> getCellDependencies(String cellAddress) {
    return _dependencyTracker.getDependencies(cellAddress);
  }
  
  Set<String> getCellDependents(String cellAddress) {
    return _dependencyTracker.getDependents(cellAddress);
  }

  // Helper methods to parse cell addresses
  int _getRowFromAddress(String cellAddress) {
    final match = RegExp(r'([A-Z]+)(\d+)').firstMatch(cellAddress);
    if (match != null) {
      return int.parse(match.group(2)!) - 1; // Convert to 0-based index
    }
    return 0;
  }

  int _getColumnFromAddress(String cellAddress) {
    final match = RegExp(r'([A-Z]+)(\d+)').firstMatch(cellAddress);
    if (match != null) {
      final columnStr = match.group(1)!;
      int column = 0;
      for (int i = 0; i < columnStr.length; i++) {
        column = column * 26 + (columnStr.codeUnitAt(i) - 'A'.codeUnitAt(0) + 1);
      }
      return column - 1; // Convert to 0-based index
    }
    return 0;
  }
}

// Provider
final enhancedSpreadsheetCalculationProvider = 
    StateNotifierProvider<EnhancedSpreadsheetCalculationNotifier, Spreadsheet?>((ref) {
  return EnhancedSpreadsheetCalculationNotifier();
});

// Dependency visualization provider
final dependencyGraphProvider = Provider<DependencyGraph?>((ref) {
  final notifier = ref.read(enhancedSpreadsheetCalculationProvider.notifier);
  return notifier.getDependencyGraph();
});


