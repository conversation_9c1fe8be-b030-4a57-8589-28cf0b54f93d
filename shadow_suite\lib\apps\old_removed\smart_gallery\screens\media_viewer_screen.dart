import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/smart_gallery_models.dart';
import '../services/smart_gallery_service.dart';
import '../../../core/widgets/unified_components.dart';

import '../../../core/widgets/fullscreen_controls.dart';
import '../../shadow_player/models/media_models.dart' as player_models;
import '../../shadow_player/screens/video_player_screen.dart';

/// Full-screen media viewer with advanced features
class MediaViewerScreen extends ConsumerStatefulWidget {
  final SmartGalleryItem item;
  final List<SmartGalleryItem> allItems;
  final int currentIndex;

  const MediaViewerScreen({
    super.key,
    required this.item,
    required this.allItems,
    required this.currentIndex,
  });

  @override
  ConsumerState<MediaViewerScreen> createState() => _MediaViewerScreenState();
}

class _MediaViewerScreenState extends ConsumerState<MediaViewerScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _overlayController;
  late AnimationController _zoomController;
  late Animation<double> _overlayAnimation;
  // Zoom animation for future implementation

  int _currentIndex = 0;
  bool _showOverlay = true;
  bool _isZoomed = false;
  double _scale = 1.0;
  Offset _offset = Offset.zero;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.currentIndex;
    _pageController = PageController(initialPage: _currentIndex);

    _overlayController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _zoomController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _overlayAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _overlayController, curve: Curves.easeInOut),
    );

    // Zoom animation setup for future implementation

    _overlayController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _overlayController.dispose();
    _zoomController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentItem = widget.allItems[_currentIndex];

    return Scaffold(
      backgroundColor: Colors.black,
      body: FullScreenControls(
        topControls: [
          Text(
            currentItem.name,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const Spacer(),
          Text(
            '${_currentIndex + 1} of ${widget.allItems.length}',
            style: const TextStyle(color: Colors.white70, fontSize: 14),
          ),
        ],
        bottomControls: [
          IconButton(
            onPressed: _currentIndex > 0 ? _previousMedia : null,
            icon: const Icon(Icons.skip_previous, color: Colors.white),
          ),
          IconButton(
            onPressed: () => _toggleFavorite(currentItem),
            icon: Icon(
              currentItem.isFavorite ? Icons.favorite : Icons.favorite_border,
              color: currentItem.isFavorite ? Colors.red : Colors.white,
            ),
          ),
          const Spacer(),
          if (currentItem.type == MediaType.video)
            IconButton(
              onPressed: () => _playVideo(currentItem),
              icon: const Icon(Icons.play_arrow, color: Colors.white),
            ),
          IconButton(
            onPressed: () => _showMediaInfo(currentItem),
            icon: const Icon(Icons.info, color: Colors.white),
          ),
          IconButton(
            onPressed: _currentIndex < widget.allItems.length - 1
                ? _nextMedia
                : null,
            icon: const Icon(Icons.skip_next, color: Colors.white),
          ),
        ],
        onExitFullScreen: () {
          context.pop();
        },
        child: Stack(
          children: [
            // Main media viewer
            GestureDetector(
              onTap: _toggleOverlay,
              onDoubleTap: _toggleZoom,
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentIndex = index;
                  });
                },
                itemCount: widget.allItems.length,
                itemBuilder: (context, index) {
                  final item = widget.allItems[index];
                  return _buildMediaWidget(item);
                },
              ),
            ),

            // Overlay UI
            AnimatedBuilder(
              animation: _overlayAnimation,
              builder: (context, child) {
                return Opacity(
                  opacity: _overlayAnimation.value,
                  child: _showOverlay
                      ? _buildOverlay()
                      : const SizedBox.shrink(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaWidget(SmartGalleryItem item) {
    return Center(
      child: Transform.scale(
        scale: _scale,
        child: Transform.translate(
          offset: _offset,
          child: GestureDetector(
            onPanUpdate: _isZoomed ? _onPanUpdate : null,
            onPanEnd: _isZoomed ? _onPanEnd : null,
            child: item.type == MediaType.image
                ? _buildImageWidget(item)
                : _buildVideoWidget(item),
          ),
        ),
      ),
    );
  }

  Widget _buildImageWidget(SmartGalleryItem item) {
    return Hero(
      tag: 'media_${item.id}',
      child: Image.network(
        item.path,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              color: Colors.grey[800],
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.broken_image, color: Colors.white54, size: 48),
                SizedBox(height: 8),
                Text(
                  'Failed to load image',
                  style: TextStyle(color: Colors.white54),
                ),
              ],
            ),
          );
        },
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              color: Colors.grey[800],
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: UnifiedLoadingIndicator(message: 'Loading image...'),
            ),
          );
        },
      ),
    );
  }

  Widget _buildVideoWidget(SmartGalleryItem item) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Video thumbnail
          if (item.thumbnailPath != null)
            Image.network(
              item.thumbnailPath!,
              fit: BoxFit.contain,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 200,
                  height: 200,
                  color: Colors.grey[800],
                  child: const Icon(
                    Icons.videocam,
                    color: Colors.white54,
                    size: 48,
                  ),
                );
              },
            ),

          // Play button overlay
          Container(
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(50),
            ),
            child: IconButton(
              icon: const Icon(Icons.play_arrow, color: Colors.white, size: 48),
              onPressed: () => _playVideo(item),
            ),
          ),

          // Duration badge
          if (item.duration != null)
            Positioned(
              bottom: 16,
              right: 16,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  _formatDuration(item.duration!),
                  style: const TextStyle(color: Colors.white, fontSize: 12),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildOverlay() {
    final currentItem = widget.allItems[_currentIndex];

    return Column(
      children: [
        // Top bar
        Container(
          padding: const EdgeInsets.only(
            top: 40,
            left: 16,
            right: 16,
            bottom: 16,
          ),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Colors.black.withValues(alpha: 0.7), Colors.transparent],
            ),
          ),
          child: Row(
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () => context.pop(),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      currentItem.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      '${_currentIndex + 1} of ${widget.allItems.length}',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                icon: Icon(
                  currentItem.isFavorite
                      ? Icons.favorite
                      : Icons.favorite_border,
                  color: currentItem.isFavorite ? Colors.red : Colors.white,
                ),
                onPressed: () => _toggleFavorite(currentItem),
              ),
              PopupMenuButton<String>(
                icon: const Icon(Icons.more_vert, color: Colors.white),
                onSelected: (value) => _handleMenuAction(value, currentItem),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'share',
                    child: Row(
                      children: [
                        Icon(Icons.share),
                        SizedBox(width: 8),
                        Text('Share'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit),
                        SizedBox(width: 8),
                        Text('Edit'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'info',
                    child: Row(
                      children: [
                        Icon(Icons.info),
                        SizedBox(width: 8),
                        Text('Details'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Delete', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        const Spacer(),

        // Bottom info panel
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              colors: [Colors.black.withValues(alpha: 0.7), Colors.transparent],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (currentItem.aiTags.isNotEmpty) ...[
                const Text(
                  'AI Tags:',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Wrap(
                  spacing: 4,
                  children: currentItem.aiTags.take(5).map((tag) {
                    return Chip(
                      label: Text(tag, style: const TextStyle(fontSize: 12)),
                      backgroundColor: Colors.blue.withValues(alpha: 0.3),
                      labelStyle: const TextStyle(color: Colors.white),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 8),
              ],

              Row(
                children: [
                  Icon(
                    currentItem.type == MediaType.image
                        ? Icons.image
                        : Icons.videocam,
                    color: Colors.white70,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatFileSize(currentItem.size),
                    style: const TextStyle(color: Colors.white70, fontSize: 12),
                  ),
                  if (currentItem.width != null &&
                      currentItem.height != null) ...[
                    const SizedBox(width: 16),
                    const Icon(
                      Icons.aspect_ratio,
                      color: Colors.white70,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${currentItem.width}×${currentItem.height}',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _toggleOverlay() {
    setState(() {
      _showOverlay = !_showOverlay;
    });

    if (_showOverlay) {
      _overlayController.forward();
    } else {
      _overlayController.reverse();
    }
  }

  void _toggleZoom() {
    setState(() {
      _isZoomed = !_isZoomed;
      if (_isZoomed) {
        _scale = 2.0;
        _zoomController.forward();
      } else {
        _scale = 1.0;
        _offset = Offset.zero;
        _zoomController.reverse();
      }
    });
  }

  void _onPanUpdate(DragUpdateDetails details) {
    setState(() {
      _offset += details.delta;
    });
  }

  void _onPanEnd(DragEndDetails details) {
    // Add boundary constraints for panning
    final screenSize = MediaQuery.of(context).size;
    final maxOffset = screenSize.width * 0.3;

    setState(() {
      _offset = Offset(
        _offset.dx.clamp(-maxOffset, maxOffset),
        _offset.dy.clamp(-maxOffset, maxOffset),
      );
    });
  }

  void _toggleFavorite(SmartGalleryItem item) async {
    await SmartGalleryService.toggleFavorite(item.id);
    // Refresh the item data
    setState(() {});
  }

  void _handleMenuAction(String action, SmartGalleryItem item) {
    switch (action) {
      case 'share':
        _shareMedia(item);
        break;
      case 'edit':
        _editMedia(item);
        break;
      case 'info':
        _showMediaInfo(item);
        break;
      case 'delete':
        _deleteMedia(item);
        break;
    }
  }

  void _shareMedia(SmartGalleryItem item) {
    final shareText =
        '''
📷 ${item.name}

Size: ${_formatFileSize(item.size)}
Date: ${_formatDate(item.dateModified)}
Location: ${item.path}

Shared from Shadow Suite SmartGallery+
''';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Share Media'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Share this media file:'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                shareText,
                style: const TextStyle(fontSize: 12),
                maxLines: 10,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Clipboard.setData(ClipboardData(text: shareText));
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Media info copied to clipboard for sharing'),
                ),
              );
            },
            child: const Text('Copy to Share'),
          ),
        ],
      ),
    );
  }

  void _editMedia(SmartGalleryItem item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Media'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Edit options for ${item.name}:'),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Rename'),
              onTap: () {
                Navigator.pop(context);
                _showRenameDialog(item);
              },
            ),
            ListTile(
              leading: const Icon(Icons.label),
              title: const Text('Edit Tags'),
              onTap: () {
                Navigator.pop(context);
                _showTagsDialog(item);
              },
            ),
            ListTile(
              leading: const Icon(Icons.star),
              title: Text(
                item.isFavorite ? 'Remove from Favorites' : 'Add to Favorites',
              ),
              onTap: () {
                Navigator.pop(context);
                _toggleFavorite(item);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showRenameDialog(SmartGalleryItem item) {
    final controller = TextEditingController(text: item.name);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Rename File'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'New name',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Renamed to ${controller.text}')),
              );
            },
            child: const Text('Rename'),
          ),
        ],
      ),
    );
  }

  void _showTagsDialog(SmartGalleryItem item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Tags'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Current tags:'),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: item.aiTags
                  .map(
                    (tag) => Chip(
                      label: Text(tag),
                      backgroundColor: Colors.blue[100],
                    ),
                  )
                  .toList(),
            ),
            const SizedBox(height: 16),
            const Text(
              'Tag editing functionality will be enhanced in future updates.',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showMediaInfo(SmartGalleryItem item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(item.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('Type', item.type.toString().split('.').last),
            _buildInfoRow('Size', _formatFileSize(item.size)),
            if (item.width != null && item.height != null)
              _buildInfoRow('Dimensions', '${item.width}×${item.height}'),
            if (item.duration != null)
              _buildInfoRow('Duration', _formatDuration(item.duration!)),
            _buildInfoRow('Created', _formatDate(item.dateCreated)),
            _buildInfoRow('Modified', _formatDate(item.dateModified)),
            if (item.aiTags.isNotEmpty)
              _buildInfoRow('AI Tags', item.aiTags.join(', ')),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _deleteMedia(SmartGalleryItem item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Media'),
        content: Text('Are you sure you want to delete ${item.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          UnifiedButton(
            text: 'Delete',
            onPressed: () async {
              final navigator = Navigator.of(context);
              navigator.pop();
              await SmartGalleryService.deleteMediaItem(item.id);
              if (mounted) {
                navigator.pop();
              }
            },
          ),
        ],
      ),
    );
  }

  void _previousMedia() {
    if (_currentIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _nextMedia() {
    if (_currentIndex < widget.allItems.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _playVideo(SmartGalleryItem item) {
    // Navigate to full-screen video player
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => VideoPlayerScreen(
          videoFile: player_models.MediaFile(
            id: item.id,
            name: item.name,
            displayName: item.name,
            path: item.path,
            size: item.size,
            dateModified: item.dateModified,
            dateAdded: item.dateCreated,
            type: player_models.MediaType.video,
            duration: item.duration,
            thumbnailPath: item.thumbnailPath,
            metadata: const player_models.MediaMetadata(),
          ),
        ),
      ),
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
