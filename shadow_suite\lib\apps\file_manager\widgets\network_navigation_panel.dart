import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../services/network_sharing_service.dart';
import '../services/network_drive_service.dart';
import '../models/file_manager_models.dart';

// Network Navigation Panel for File Manager
class NetworkNavigationPanel extends ConsumerStatefulWidget {
  const NetworkNavigationPanel({super.key});

  @override
  ConsumerState<NetworkNavigationPanel> createState() =>
      _NetworkNavigationPanelState();
}

class _NetworkNavigationPanelState
    extends ConsumerState<NetworkNavigationPanel> {
  bool _isExpanded = false;
  List<NetworkShare> _activeShares = [];
  List<NetworkDrive> _mountedDrives = [];

  @override
  void initState() {
    super.initState();
    _loadNetworkStatus();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8),
      child: Column(
        children: [
          // Network Panel Header
          ListTile(
            leading: const Icon(Icons.wifi, color: Color(0xFF3498DB)),
            title: const Text(
              'Network Services',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Color(0xFF2C3E50),
              ),
            ),
            subtitle: Text(
              '${_activeShares.length} shares, ${_mountedDrives.length} drives',
              style: const TextStyle(color: Color(0xFF7F8C8D)),
            ),
            trailing: IconButton(
              icon: Icon(
                _isExpanded ? Icons.expand_less : Icons.expand_more,
                color: const Color(0xFF3498DB),
              ),
              onPressed: () {
                setState(() {
                  _isExpanded = !_isExpanded;
                });
              },
            ),
          ),

          // Network Services Grid
          if (_isExpanded) ...[
            const Divider(),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Primary Network Services
                  Row(
                    children: [
                      Expanded(
                        child: _buildNetworkServiceCard(
                          icon: Icons.router,
                          title: 'LAN Discovery',
                          subtitle: 'Find network devices',
                          color: const Color(0xFF27AE60),
                          onTap: () =>
                              context.go('/file-manager/lan-discovery'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildNetworkServiceCard(
                          icon: Icons.wifi_tethering,
                          title: 'WiFi Share',
                          subtitle: 'Share files via WiFi',
                          color: const Color(0xFF3498DB),
                          onTap: () => context.go('/file-manager/wifi-share'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // FTP Services
                  Row(
                    children: [
                      Expanded(
                        child: _buildNetworkServiceCard(
                          icon: Icons.cloud_upload,
                          title: 'FTP Server',
                          subtitle: 'Start FTP server',
                          color: const Color(0xFFE67E22),
                          onTap: () => context.go('/file-manager/ftp-server'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildNetworkServiceCard(
                          icon: Icons.cloud_download,
                          title: 'FTP Client',
                          subtitle: 'Connect to FTP',
                          color: const Color(0xFF9B59B6),
                          onTap: () => context.go('/file-manager/ftp-client'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Network Drives
                  Row(
                    children: [
                      Expanded(
                        child: _buildNetworkServiceCard(
                          icon: Icons.folder_shared,
                          title: 'Network Drives',
                          subtitle: 'Mount network shares',
                          color: const Color(0xFFE74C3C),
                          onTap: () => context.go('/file-manager/network'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildNetworkServiceCard(
                          icon: Icons.settings_ethernet,
                          title: 'Network Settings',
                          subtitle: 'Configure network',
                          color: const Color(0xFF95A5A6),
                          onTap: () => _showNetworkSettings(),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Active Network Status
            if (_activeShares.isNotEmpty || _mountedDrives.isNotEmpty) ...[
              const Divider(),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Active Connections',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2C3E50),
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Active Shares
                    if (_activeShares.isNotEmpty) ...[
                      ...(_activeShares
                          .take(3)
                          .map((share) => _buildActiveShareItem(share))),
                      if (_activeShares.length > 3)
                        TextButton(
                          onPressed: () => context.go('/file-manager/network'),
                          child: Text(
                            'View all ${_activeShares.length} shares',
                          ),
                        ),
                    ],

                    // Mounted Drives
                    if (_mountedDrives.isNotEmpty) ...[
                      ...(_mountedDrives
                          .take(3)
                          .map((drive) => _buildMountedDriveItem(drive))),
                      if (_mountedDrives.length > 3)
                        TextButton(
                          onPressed: () => context.go('/file-manager/network'),
                          child: Text(
                            'View all ${_mountedDrives.length} drives',
                          ),
                        ),
                    ],
                  ],
                ),
              ),
            ],
          ],
        ],
      ),
    );
  }

  Widget _buildNetworkServiceCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: color,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: const TextStyle(fontSize: 10, color: Color(0xFF7F8C8D)),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveShareItem(NetworkShare share) {
    return ListTile(
      dense: true,
      leading: Icon(
        Icons.share,
        color: share.isActive
            ? const Color(0xFF27AE60)
            : const Color(0xFF95A5A6),
        size: 20,
      ),
      title: Text(share.name, style: const TextStyle(fontSize: 12)),
      subtitle: Text(
        '${share.protocol.name.toUpperCase()} - ${share.connectedClients} clients',
        style: const TextStyle(fontSize: 10),
      ),
      trailing: IconButton(
        icon: const Icon(Icons.stop, size: 16),
        onPressed: () => _stopShare(share),
      ),
    );
  }

  Widget _buildMountedDriveItem(NetworkDrive drive) {
    return ListTile(
      dense: true,
      leading: Icon(
        Icons.folder_shared,
        color: drive.isConnected
            ? const Color(0xFF3498DB)
            : const Color(0xFF95A5A6),
        size: 20,
      ),
      title: Text(drive.displayName, style: const TextStyle(fontSize: 12)),
      subtitle: Text(
        '${drive.host}:${drive.port}',
        style: const TextStyle(fontSize: 10),
      ),
      trailing: IconButton(
        icon: const Icon(Icons.eject, size: 16),
        onPressed: () => _unmountDrive(drive),
      ),
    );
  }

  Future<void> _loadNetworkStatus() async {
    try {
      final shares = await NetworkSharingService.getActiveShares();
      final drives = await NetworkDriveService.getMountedDrives();

      if (mounted) {
        setState(() {
          _activeShares = shares;
          _mountedDrives = drives;
        });
      }
    } catch (e) {
      // Handle error silently for now
    }
  }

  void _showNetworkSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Network Settings'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.wifi),
              title: Text('WiFi Configuration'),
              subtitle: Text('Configure WiFi sharing settings'),
            ),
            ListTile(
              leading: Icon(Icons.security),
              title: Text('Security Settings'),
              subtitle: Text('Configure authentication and encryption'),
            ),
            ListTile(
              leading: Icon(Icons.speed),
              title: Text('Performance Settings'),
              subtitle: Text('Configure transfer speeds and limits'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.go('/file-manager/network');
            },
            child: const Text('Advanced Settings'),
          ),
        ],
      ),
    );
  }

  Future<void> _stopShare(NetworkShare share) async {
    try {
      await NetworkSharingService.stopShare(share.id);
      await _loadNetworkStatus();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Stopped sharing ${share.name}'),
            backgroundColor: const Color(0xFF27AE60),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to stop share: $e'),
            backgroundColor: const Color(0xFFE74C3C),
          ),
        );
      }
    }
  }

  Future<void> _unmountDrive(NetworkDrive drive) async {
    try {
      await NetworkDriveService.unmountDrive(drive.id);
      await _loadNetworkStatus();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Unmounted ${drive.displayName}'),
            backgroundColor: const Color(0xFF27AE60),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to unmount drive: $e'),
            backgroundColor: const Color(0xFFE74C3C),
          ),
        );
      }
    }
  }
}
