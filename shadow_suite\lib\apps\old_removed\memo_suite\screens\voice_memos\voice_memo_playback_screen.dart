import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:just_audio/just_audio.dart';
import '../../../../core/theme/app_theme.dart';
import '../../services/memo_providers.dart';
import '../../models/voice_memo.dart';

enum PlaybackState { stopped, playing, paused }

class VoiceMemoPlaybackScreen extends ConsumerStatefulWidget {
  const VoiceMemoPlaybackScreen({super.key});

  @override
  ConsumerState<VoiceMemoPlaybackScreen> createState() =>
      _VoiceMemoPlaybackScreenState();
}

class _VoiceMemoPlaybackScreenState
    extends ConsumerState<VoiceMemoPlaybackScreen>
    with TickerProviderStateMixin {
  PlaybackState _playbackState = PlaybackState.stopped;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  double _playbackSpeed = 1.0;
  late AnimationController _waveformController;
  Timer? _positionTimer;

  // Audio player
  late AudioPlayer _audioPlayer;
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<Duration?>? _durationSubscription;
  StreamSubscription<PlayerState>? _playerStateSubscription;

  final List<double> _playbackSpeeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];

  @override
  void initState() {
    super.initState();
    _waveformController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _audioPlayer = AudioPlayer();
    _initializeAudioPlayer();
  }

  @override
  void dispose() {
    _positionTimer?.cancel();
    _positionSubscription?.cancel();
    _durationSubscription?.cancel();
    _playerStateSubscription?.cancel();
    _audioPlayer.dispose();
    _waveformController.dispose();
    super.dispose();
  }

  void _initializeAudioPlayer() {
    // Listen to position changes
    _positionSubscription = _audioPlayer.positionStream.listen((position) {
      if (mounted) {
        setState(() {
          _currentPosition = position;
        });
      }
    });

    // Listen to duration changes
    _durationSubscription = _audioPlayer.durationStream.listen((duration) {
      if (mounted && duration != null) {
        setState(() {
          _totalDuration = duration;
        });
      }
    });

    // Listen to player state changes
    _playerStateSubscription = _audioPlayer.playerStateStream.listen((state) {
      if (mounted) {
        setState(() {
          switch (state.processingState) {
            case ProcessingState.idle:
            case ProcessingState.completed:
              _playbackState = PlaybackState.stopped;
              break;
            case ProcessingState.loading:
            case ProcessingState.buffering:
              // Keep current state during loading
              break;
            case ProcessingState.ready:
              _playbackState = state.playing
                  ? PlaybackState.playing
                  : PlaybackState.paused;
              break;
          }
        });

        // Update waveform animation
        if (_playbackState == PlaybackState.playing) {
          _waveformController.repeat();
        } else {
          _waveformController.stop();
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final selectedVoiceMemo = ref.watch(selectedVoiceMemoProvider);

    if (selectedVoiceMemo == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Voice Memo'),
          backgroundColor: AppTheme.memoSuiteColor,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              ref.read(memoSuiteCurrentScreenProvider.notifier).state =
                  MemoSuiteScreen.voiceMemosList;
            },
          ),
        ),
        body: const Center(child: Text('No voice memo selected')),
      );
    }

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Voice Memo Player'),
        backgroundColor: AppTheme.memoSuiteColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            _stopPlayback();
            ref.read(memoSuiteCurrentScreenProvider.notifier).state =
                MemoSuiteScreen.voiceMemosList;
          },
        ),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) =>
                _handleMenuAction(context, ref, selectedVoiceMemo, value),
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'rename', child: Text('Rename')),
              const PopupMenuItem(value: 'share', child: Text('Share')),
              const PopupMenuItem(value: 'export', child: Text('Export')),
              const PopupMenuItem(value: 'delete', child: Text('Delete')),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildVoiceMemoHeader(context, selectedVoiceMemo),
            const SizedBox(height: 32),
            _buildWaveformVisualizer(context, selectedVoiceMemo),
            const SizedBox(height: 32),
            _buildPlaybackControls(context, selectedVoiceMemo),
            const SizedBox(height: 32),
            _buildPlaybackSettings(context),
            const SizedBox(height: 32),
            if (selectedVoiceMemo.transcription != null &&
                selectedVoiceMemo.transcription!.isNotEmpty)
              _buildTranscription(context, selectedVoiceMemo),
            _buildVoiceMemoInfo(context, selectedVoiceMemo),
          ],
        ),
      ),
    );
  }

  Widget _buildVoiceMemoHeader(BuildContext context, VoiceMemo voiceMemo) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppTheme.memoSuiteColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.mic,
                    color: AppTheme.memoSuiteColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        voiceMemo.title,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Duration: ${voiceMemo.formattedDuration} • Size: ${voiceMemo.formattedFileSize}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.memoSuiteColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppTheme.memoSuiteColor.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    voiceMemo.category,
                    style: TextStyle(
                      color: AppTheme.memoSuiteColor,
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  _formatDate(voiceMemo.updatedAt),
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
              ],
            ),
            if (voiceMemo.tags.isNotEmpty) ...[
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                children: voiceMemo.tags
                    .map(
                      (tag) => Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: Text(
                          tag,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: Colors.grey[700]),
                        ),
                      ),
                    )
                    .toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildWaveformVisualizer(BuildContext context, VoiceMemo voiceMemo) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.graphic_eq,
                  color: AppTheme.memoSuiteColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Waveform',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.memoSuiteColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Container(
              height: 80,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: CustomPaint(
                painter: WaveformPainter(
                  progress:
                      _currentPosition.inMilliseconds /
                      voiceMemo.duration.inMilliseconds,
                  isPlaying: _playbackState == PlaybackState.playing,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Text(
                  _formatDuration(_currentPosition),
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
                Expanded(
                  child: Slider(
                    value: voiceMemo.duration.inMilliseconds > 0
                        ? _currentPosition.inMilliseconds /
                              voiceMemo.duration.inMilliseconds
                        : 0.0,
                    onChanged: (value) {
                      setState(() {
                        _currentPosition = Duration(
                          milliseconds:
                              (value * voiceMemo.duration.inMilliseconds)
                                  .round(),
                        );
                      });
                      // Simulate seeking to position - in a real app, you would use audio player packages
                    },
                    activeColor: AppTheme.memoSuiteColor,
                  ),
                ),
                Text(
                  voiceMemo.formattedDuration,
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaybackControls(BuildContext context, VoiceMemo voiceMemo) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildControlButton(
              icon: Icons.replay_10,
              onPressed: () => _seekRelative(-10),
              size: 40,
            ),
            _buildControlButton(
              icon: _playbackState == PlaybackState.playing
                  ? Icons.pause
                  : Icons.play_arrow,
              onPressed: _togglePlayback,
              size: 60,
              isPrimary: true,
            ),
            _buildControlButton(
              icon: Icons.forward_10,
              onPressed: () => _seekRelative(10),
              size: 40,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaybackSettings(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.settings, color: AppTheme.memoSuiteColor, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Playback Settings',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.memoSuiteColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Text(
                  'Speed: ',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                ),
                const SizedBox(width: 8),
                ...List.generate(_playbackSpeeds.length, (index) {
                  final speed = _playbackSpeeds[index];
                  final isSelected = _playbackSpeed == speed;

                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text('${speed}x'),
                      selected: isSelected,
                      onSelected: (selected) {
                        if (selected) {
                          setState(() {
                            _playbackSpeed = speed;
                          });
                          // Simulate setting playback speed - in a real app, you would use audio player packages
                        }
                      },
                      selectedColor: AppTheme.memoSuiteColor.withValues(
                        alpha: 0.2,
                      ),
                      checkmarkColor: AppTheme.memoSuiteColor,
                    ),
                  );
                }),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTranscription(BuildContext context, VoiceMemo voiceMemo) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.text_fields,
                  color: AppTheme.memoSuiteColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Transcription',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.memoSuiteColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SelectableText(
              voiceMemo.transcription!,
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(height: 1.6),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVoiceMemoInfo(BuildContext context, VoiceMemo voiceMemo) {
    return Card(
      color: Colors.grey[50],
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.grey[600], size: 20),
                const SizedBox(width: 8),
                Text(
                  'Voice Memo Information',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              context,
              'Created',
              _formatFullDate(voiceMemo.createdAt),
            ),
            const SizedBox(height: 8),
            _buildInfoRow(
              context,
              'Last Modified',
              _formatFullDate(voiceMemo.updatedAt),
            ),
            const SizedBox(height: 8),
            _buildInfoRow(context, 'File Size', voiceMemo.formattedFileSize),
            const SizedBox(height: 8),
            _buildInfoRow(context, 'Tags', voiceMemo.tags.length.toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required double size,
    bool isPrimary = false,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: isPrimary ? AppTheme.memoSuiteColor : Colors.grey[200],
        shape: BoxShape.circle,
        boxShadow: isPrimary
            ? [
                BoxShadow(
                  color: AppTheme.memoSuiteColor.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ]
            : null,
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          color: isPrimary ? Colors.white : Colors.grey[700],
          size: size * 0.4,
        ),
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Row(
      children: [
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: Colors.grey[800]),
        ),
      ],
    );
  }

  Future<void> _togglePlayback() async {
    if (_playbackState == PlaybackState.playing) {
      await _pausePlayback();
    } else {
      await _startPlayback();
    }
  }

  Future<void> _startPlayback() async {
    final voiceMemo = ref.read(selectedVoiceMemoProvider);
    if (voiceMemo == null) return;

    try {
      // Load audio file if not already loaded
      if (_audioPlayer.audioSource == null) {
        await _loadAudioFile(voiceMemo);
      }

      // Set playback speed
      await _audioPlayer.setSpeed(_playbackSpeed);

      // Start playback
      await _audioPlayer.play();

      // Visual feedback
      _waveformController.repeat();
    } catch (e) {
      // Handle audio playback error
      _showErrorSnackBar('Failed to play audio: ${e.toString()}');
    }
  }

  Future<void> _pausePlayback() async {
    try {
      await _audioPlayer.pause();
      _waveformController.stop();
    } catch (e) {
      _showErrorSnackBar('Failed to pause audio: ${e.toString()}');
    }
  }

  Future<void> _stopPlayback() async {
    try {
      await _audioPlayer.stop();
      await _audioPlayer.seek(Duration.zero);
      _waveformController.stop();
    } catch (e) {
      _showErrorSnackBar('Failed to stop audio: ${e.toString()}');
    }
  }

  Future<void> _seekRelative(int seconds) async {
    try {
      final newPosition = Duration(
        milliseconds: (_currentPosition.inMilliseconds + (seconds * 1000))
            .clamp(0, _totalDuration.inMilliseconds),
      );
      await _audioPlayer.seek(newPosition);
    } catch (e) {
      _showErrorSnackBar('Failed to seek audio: ${e.toString()}');
    }
  }

  Future<void> _loadAudioFile(VoiceMemo voiceMemo) async {
    try {
      // Load audio file from the actual file path
      if (voiceMemo.filePath.isNotEmpty &&
          File(voiceMemo.filePath).existsSync()) {
        await _audioPlayer.setFilePath(voiceMemo.filePath);
      } else {
        // Handle missing audio file
        throw Exception('Audio file not found: ${voiceMemo.filePath}');
      }
    } catch (e) {
      throw Exception('Failed to load audio file: ${e.toString()}');
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
          action: SnackBarAction(
            label: 'Dismiss',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    }
  }

  void _handleMenuAction(
    BuildContext context,
    WidgetRef ref,
    VoiceMemo voiceMemo,
    String action,
  ) {
    switch (action) {
      case 'rename':
        _showRenameDialog(context, ref, voiceMemo);
        break;
      case 'share':
        _shareVoiceMemo(context, voiceMemo);
        break;
      case 'export':
        _exportVoiceMemo(context, voiceMemo);
        break;
      case 'delete':
        _showDeleteDialog(context, ref, voiceMemo);
        break;
    }
  }

  void _showRenameDialog(
    BuildContext context,
    WidgetRef ref,
    VoiceMemo voiceMemo,
  ) {
    final controller = TextEditingController(text: voiceMemo.title);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Rename Voice Memo'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Title',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (controller.text.trim().isNotEmpty) {
                final updatedMemo = voiceMemo.copyWith(
                  title: controller.text.trim(),
                );
                ref
                    .read(voiceMemosProvider.notifier)
                    .updateVoiceMemo(updatedMemo);
                ref.read(selectedVoiceMemoProvider.notifier).state =
                    updatedMemo;
              }
              Navigator.of(context).pop();
            },
            child: const Text('Rename'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(
    BuildContext context,
    WidgetRef ref,
    VoiceMemo voiceMemo,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Voice Memo'),
        content: Text('Are you sure you want to delete "${voiceMemo.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              ref
                  .read(voiceMemosProvider.notifier)
                  .deleteVoiceMemo(voiceMemo.id);
              Navigator.of(context).pop();
              ref.read(memoSuiteCurrentScreenProvider.notifier).state =
                  MemoSuiteScreen.voiceMemosList;
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String _formatFullDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} at ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _shareVoiceMemo(BuildContext context, VoiceMemo voiceMemo) {
    final shareText =
        '''
Voice Memo: ${voiceMemo.title}
Duration: ${voiceMemo.formattedDuration}
Category: ${voiceMemo.category}
Created: ${_formatFullDate(voiceMemo.createdAt)}
${voiceMemo.tags.isNotEmpty ? 'Tags: ${voiceMemo.tags.join(', ')}' : ''}
    ''';

    // In a real app, you would use share_plus package
    Clipboard.setData(ClipboardData(text: shareText));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Voice memo details copied to clipboard')),
    );
  }

  void _exportVoiceMemo(BuildContext context, VoiceMemo voiceMemo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Voice Memo'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.audio_file),
              title: const Text('Export as Audio File'),
              subtitle: const Text('Save the original audio file'),
              onTap: () {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text(
                      'Audio export functionality will be implemented',
                    ),
                  ),
                );
              },
            ),
            if (voiceMemo.transcription?.isNotEmpty == true)
              ListTile(
                leading: const Icon(Icons.text_snippet),
                title: const Text('Export as Text'),
                subtitle: const Text('Save transcription as text file'),
                onTap: () {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                        'Text export functionality will be implemented',
                      ),
                    ),
                  );
                },
              ),
            ListTile(
              leading: const Icon(Icons.picture_as_pdf),
              title: const Text('Export as PDF'),
              subtitle: const Text('Create a PDF with memo details'),
              onTap: () {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text(
                      'PDF export functionality will be implemented',
                    ),
                  ),
                );
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}

class WaveformPainter extends CustomPainter {
  final double progress;
  final bool isPlaying;

  WaveformPainter({required this.progress, required this.isPlaying});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey[400]!
      ..strokeWidth = 2;

    final activePaint = Paint()
      ..color = AppTheme.memoSuiteColor
      ..strokeWidth = 2;

    const barCount = 50;
    final barWidth = size.width / barCount;

    for (int i = 0; i < barCount; i++) {
      final x = i * barWidth + barWidth / 2;
      final height = (20 + (i % 5) * 10).toDouble();
      final y1 = (size.height - height) / 2;
      final y2 = y1 + height;

      final currentPaint = (i / barCount) <= progress ? activePaint : paint;

      canvas.drawLine(Offset(x, y1), Offset(x, y2), currentPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
