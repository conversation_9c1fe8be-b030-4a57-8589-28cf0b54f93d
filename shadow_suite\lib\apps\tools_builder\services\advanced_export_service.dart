import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'dart:math' as math;
import 'package:excel/excel.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import '../models/spreadsheet.dart';
import '../models/spreadsheet_cell.dart';
import '../models/ui_component.dart';
import '../models/tool.dart';

// Advanced Export Service with multiple format support
class AdvancedExportService {
  /// Export tool to Excel format
  static Future<ExportResult> exportToExcel(
    Tool tool, {
    String? customPath,
    ExcelExportOptions? options,
  }) async {
    try {
      final excel = Excel.createExcel();
      final defaultSheet = excel.getDefaultSheet();
      if (defaultSheet != null) {
        excel.delete(defaultSheet);
      }

      // Export spreadsheet data
      if (tool.spreadsheet != null) {
        await _exportSpreadsheetToExcel(excel, tool.spreadsheet!, options);
      }

      // Export UI components as metadata sheet
      if (tool.components.isNotEmpty) {
        await _exportComponentsToExcel(excel, tool.components, options);
      }

      // Save file
      final bytes = excel.encode();
      if (bytes == null) {
        return ExportResult(
          success: false,
          error: 'Failed to encode Excel file',
        );
      }

      final filePath = await _saveFile(bytes, '${tool.name}.xlsx', customPath);

      return ExportResult(
        success: true,
        filePath: filePath,
        format: ExportFormat.excel,
        fileSize: bytes.length,
      );
    } catch (e) {
      return ExportResult(success: false, error: e.toString());
    }
  }

  /// Export tool to PDF format
  static Future<ExportResult> exportToPdf(
    Tool tool, {
    String? customPath,
    PdfExportOptions? options,
  }) async {
    try {
      final pdf = pw.Document();

      // Add title page
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Header(
                  level: 0,
                  child: pw.Text(
                    tool.name,
                    style: const pw.TextStyle(fontSize: 24),
                  ),
                ),
                pw.SizedBox(height: 20),
                pw.Text(
                  tool.description,
                  style: const pw.TextStyle(fontSize: 14),
                ),
                pw.SizedBox(height: 20),
                pw.Text(
                  'Created: ${DateTime.now().toIso8601String().split('T')[0]}',
                ),
                pw.Text('Type: ${tool.type.name}'),
                pw.Text('Category: ${tool.category.name}'),
              ],
            );
          },
        ),
      );

      // Export spreadsheet data
      if (tool.spreadsheet != null) {
        await _exportSpreadsheetToPdf(pdf, tool.spreadsheet!, options);
      }

      // Export UI components
      if (tool.components.isNotEmpty) {
        await _exportComponentsToPdf(pdf, tool.components, options);
      }

      // Save file
      final bytes = await pdf.save();
      final filePath = await _saveFile(bytes, '${tool.name}.pdf', customPath);

      return ExportResult(
        success: true,
        filePath: filePath,
        format: ExportFormat.pdf,
        fileSize: bytes.length,
      );
    } catch (e) {
      return ExportResult(success: false, error: e.toString());
    }
  }

  /// Export tool to JSON format
  static Future<ExportResult> exportToJson(
    Tool tool, {
    String? customPath,
    JsonExportOptions? options,
  }) async {
    try {
      final data = {
        'tool': tool.toMap(),
        'exportInfo': {
          'version': '1.0',
          'exportedAt': DateTime.now().toIso8601String(),
          'format': 'json',
        },
      };

      final jsonString = const JsonEncoder.withIndent('  ').convert(data);
      final bytes = utf8.encode(jsonString);

      final filePath = await _saveFile(bytes, '${tool.name}.json', customPath);

      return ExportResult(
        success: true,
        filePath: filePath,
        format: ExportFormat.json,
        fileSize: bytes.length,
      );
    } catch (e) {
      return ExportResult(success: false, error: e.toString());
    }
  }

  /// Export tool to CSV format
  static Future<ExportResult> exportToCsv(
    Tool tool, {
    String? customPath,
    CsvExportOptions? options,
  }) async {
    try {
      if (tool.spreadsheet == null) {
        return ExportResult(
          success: false,
          error: 'No spreadsheet data to export',
        );
      }

      final csv = _generateCsv(tool.spreadsheet!, options);
      final bytes = utf8.encode(csv);

      final filePath = await _saveFile(bytes, '${tool.name}.csv', customPath);

      return ExportResult(
        success: true,
        filePath: filePath,
        format: ExportFormat.csv,
        fileSize: bytes.length,
      );
    } catch (e) {
      return ExportResult(success: false, error: e.toString());
    }
  }

  /// Export tool to HTML format
  static Future<ExportResult> exportToHtml(
    Tool tool, {
    String? customPath,
    HtmlExportOptions? options,
  }) async {
    try {
      final html = _generateHtml(tool, options);
      final bytes = utf8.encode(html);

      final filePath = await _saveFile(bytes, '${tool.name}.html', customPath);

      return ExportResult(
        success: true,
        filePath: filePath,
        format: ExportFormat.html,
        fileSize: bytes.length,
      );
    } catch (e) {
      return ExportResult(success: false, error: e.toString());
    }
  }

  // Helper methods
  static Future<void> _exportSpreadsheetToExcel(
    Excel excel,
    Spreadsheet spreadsheet,
    ExcelExportOptions? options,
  ) async {
    for (final sheet in spreadsheet.sheets) {
      final excelSheet = excel[sheet.name];

      for (final cell in sheet.cells.values) {
        final cellRef = CellIndex.indexByString(
          '${_getColumnLetter(cell.column)}${cell.row + 1}',
        );

        // Set cell value
        if (cell.dataType == CellDataType.number) {
          excelSheet.cell(cellRef).value = TextCellValue(
            (double.tryParse(cell.rawValue) ?? 0).toString(),
          );
        } else if (cell.dataType == CellDataType.boolean) {
          excelSheet.cell(cellRef).value = TextCellValue(
            cell.rawValue.toLowerCase() == 'true' ? 'TRUE' : 'FALSE',
          );
        } else {
          excelSheet.cell(cellRef).value = TextCellValue(cell.rawValue);
        }

        // Apply formatting
        if (cell.format.isBold || cell.format.isItalic) {
          excelSheet.cell(cellRef).cellStyle = CellStyle(
            bold: cell.format.isBold,
            italic: cell.format.isItalic,
          );
        }
      }
    }
  }

  static Future<void> _exportComponentsToExcel(
    Excel excel,
    List<UIComponent> components,
    ExcelExportOptions? options,
  ) async {
    final sheet = excel['Components'];

    // Headers
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('ID');
    sheet.cell(CellIndex.indexByString('B1')).value = TextCellValue('Type');
    sheet.cell(CellIndex.indexByString('C1')).value = TextCellValue('Label');
    sheet.cell(CellIndex.indexByString('D1')).value = TextCellValue('X');
    sheet.cell(CellIndex.indexByString('E1')).value = TextCellValue('Y');

    // Data
    for (int i = 0; i < components.length; i++) {
      final component = components[i];
      final row = i + 2;

      sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue(
        component.id,
      );
      sheet.cell(CellIndex.indexByString('B$row')).value = TextCellValue(
        component.type.name,
      );
      sheet.cell(CellIndex.indexByString('C$row')).value = TextCellValue(
        component.label,
      );
      sheet.cell(CellIndex.indexByString('D$row')).value = TextCellValue(
        component.x.toString(),
      );
      sheet.cell(CellIndex.indexByString('E$row')).value = TextCellValue(
        component.y.toString(),
      );
    }
  }

  static Future<void> _exportSpreadsheetToPdf(
    pw.Document pdf,
    Spreadsheet spreadsheet,
    PdfExportOptions? options,
  ) async {
    for (final sheet in spreadsheet.sheets) {
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Header(level: 1, child: pw.Text('Sheet: ${sheet.name}')),
                pw.SizedBox(height: 10),
                _buildPdfTable(sheet),
              ],
            );
          },
        ),
      );
    }
  }

  static pw.Widget _buildPdfTable(SpreadsheetSheet sheet) {
    // Find the bounds of the data
    int maxRow = 0;
    int maxCol = 0;

    for (final cell in sheet.cells.values) {
      maxRow = math.max(maxRow, cell.row);
      maxCol = math.max(maxCol, cell.column);
    }

    // Build table data
    final tableData = <List<String>>[];

    for (int row = 0; row <= maxRow; row++) {
      final rowData = <String>[];
      for (int col = 0; col <= maxCol; col++) {
        final cell = sheet.getCell(row, col);
        rowData.add(cell?.rawValue ?? '');
      }
      tableData.add(rowData);
    }

    return pw.TableHelper.fromTextArray(
      data: tableData,
      border: pw.TableBorder.all(),
      cellAlignment: pw.Alignment.centerLeft,
      cellPadding: const pw.EdgeInsets.all(4),
    );
  }

  static Future<void> _exportComponentsToPdf(
    pw.Document pdf,
    List<UIComponent> components,
    PdfExportOptions? options,
  ) async {
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Header(level: 1, child: pw.Text('UI Components')),
              pw.SizedBox(height: 10),
              pw.TableHelper.fromTextArray(
                data: [
                  ['Type', 'Label', 'Position', 'Properties'],
                  ...components.map(
                    (c) => [
                      c.type.name,
                      c.label,
                      '(${c.x.toInt()}, ${c.y.toInt()})',
                      c.properties.length.toString(),
                    ],
                  ),
                ],
                border: pw.TableBorder.all(),
                cellAlignment: pw.Alignment.centerLeft,
                cellPadding: const pw.EdgeInsets.all(4),
              ),
            ],
          );
        },
      ),
    );
  }

  static String _generateCsv(
    Spreadsheet spreadsheet,
    CsvExportOptions? options,
  ) {
    final buffer = StringBuffer();
    final separator = options?.separator ?? ',';

    for (final sheet in spreadsheet.sheets) {
      // Find bounds
      int maxRow = 0;
      int maxCol = 0;

      for (final cell in sheet.cells.values) {
        maxRow = math.max(maxRow, cell.row);
        maxCol = math.max(maxCol, cell.column);
      }

      // Generate CSV
      for (int row = 0; row <= maxRow; row++) {
        final rowData = <String>[];
        for (int col = 0; col <= maxCol; col++) {
          final cell = sheet.getCell(row, col);
          final value = cell?.rawValue ?? '';

          // Escape quotes and wrap in quotes if contains separator
          if (value.contains(separator) ||
              value.contains('"') ||
              value.contains('\n')) {
            rowData.add('"${value.replaceAll('"', '""')}"');
          } else {
            rowData.add(value);
          }
        }
        buffer.writeln(rowData.join(separator));
      }
    }

    return buffer.toString();
  }

  static String _generateHtml(Tool tool, HtmlExportOptions? options) {
    final buffer = StringBuffer();

    buffer.writeln('<!DOCTYPE html>');
    buffer.writeln('<html>');
    buffer.writeln('<head>');
    buffer.writeln('<title>${tool.name}</title>');
    buffer.writeln('<style>');
    buffer.writeln('table { border-collapse: collapse; width: 100%; }');
    buffer.writeln(
      'th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }',
    );
    buffer.writeln('th { background-color: #f2f2f2; }');
    buffer.writeln('</style>');
    buffer.writeln('</head>');
    buffer.writeln('<body>');

    buffer.writeln('<h1>${tool.name}</h1>');
    buffer.writeln('<p>${tool.description}</p>');

    if (tool.spreadsheet != null) {
      for (final sheet in tool.spreadsheet!.sheets) {
        buffer.writeln('<h2>Sheet: ${sheet.name}</h2>');
        buffer.writeln(_generateHtmlTable(sheet));
      }
    }

    buffer.writeln('</body>');
    buffer.writeln('</html>');

    return buffer.toString();
  }

  static String _generateHtmlTable(SpreadsheetSheet sheet) {
    final buffer = StringBuffer();

    // Find bounds
    int maxRow = 0;
    int maxCol = 0;

    for (final cell in sheet.cells.values) {
      maxRow = math.max(maxRow, cell.row);
      maxCol = math.max(maxCol, cell.column);
    }

    buffer.writeln('<table>');

    for (int row = 0; row <= maxRow; row++) {
      buffer.writeln('<tr>');
      for (int col = 0; col <= maxCol; col++) {
        final cell = sheet.getCell(row, col);
        final value = cell?.rawValue ?? '';

        if (row == 0) {
          buffer.writeln('<th>$value</th>');
        } else {
          buffer.writeln('<td>$value</td>');
        }
      }
      buffer.writeln('</tr>');
    }

    buffer.writeln('</table>');

    return buffer.toString();
  }

  static String _getColumnLetter(int column) {
    String result = '';
    int col = column;
    while (col >= 0) {
      result = String.fromCharCode('A'.codeUnitAt(0) + (col % 26)) + result;
      col = (col ~/ 26) - 1;
      if (col < 0) break;
    }
    return result;
  }

  static Future<String> _saveFile(
    List<int> bytes,
    String fileName,
    String? customPath,
  ) async {
    if (customPath != null) {
      final file = File('$customPath/$fileName');
      await file.writeAsBytes(bytes);
      return file.path;
    }

    // Use file picker to let user choose location
    final result = await FilePicker.platform.saveFile(
      dialogTitle: 'Save $fileName',
      fileName: fileName,
      bytes: Uint8List.fromList(bytes),
    );

    if (result != null) {
      return result;
    }

    // Fallback to documents directory
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName');
    await file.writeAsBytes(bytes);
    return file.path;
  }
}

// Export options classes
class ExcelExportOptions {
  final bool includeFormulas;
  final bool includeFormatting;
  final bool includeComponents;

  ExcelExportOptions({
    this.includeFormulas = true,
    this.includeFormatting = true,
    this.includeComponents = true,
  });
}

class PdfExportOptions {
  final PdfPageFormat pageFormat;
  final bool includeComponents;
  final bool includeCharts;

  PdfExportOptions({
    this.pageFormat = PdfPageFormat.a4,
    this.includeComponents = true,
    this.includeCharts = true,
  });
}

class JsonExportOptions {
  final bool prettyPrint;
  final bool includeMetadata;

  JsonExportOptions({this.prettyPrint = true, this.includeMetadata = true});
}

class CsvExportOptions {
  final String separator;
  final bool includeHeaders;

  CsvExportOptions({this.separator = ',', this.includeHeaders = true});
}

class HtmlExportOptions {
  final bool includeStyles;
  final bool includeComponents;

  HtmlExportOptions({this.includeStyles = true, this.includeComponents = true});
}

// Export result
class ExportResult {
  final bool success;
  final String? filePath;
  final ExportFormat? format;
  final int? fileSize;
  final String? error;

  ExportResult({
    required this.success,
    this.filePath,
    this.format,
    this.fileSize,
    this.error,
  });
}

enum ExportFormat { excel, pdf, json, csv, html }
