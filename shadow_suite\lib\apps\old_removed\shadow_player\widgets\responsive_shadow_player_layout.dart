import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/services/layout_service.dart';
import '../../../core/themes/theme_models_base.dart';
import '../screens/video_tab_screen.dart';
import '../screens/music_tab_screen.dart';
import '../screens/player_settings_screen.dart';

class ResponsiveShadowPlayerLayout extends ConsumerStatefulWidget {
  const ResponsiveShadowPlayerLayout({super.key});

  @override
  ConsumerState<ResponsiveShadowPlayerLayout> createState() =>
      _ResponsiveShadowPlayerLayoutState();
}

class _ResponsiveShadowPlayerLayoutState
    extends ConsumerState<ResponsiveShadowPlayerLayout>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int _currentIndex = 0;

  final List<ShadowPlayerTab> _tabs = [
    ShadowPlayerTab(
      title: 'Videos',
      icon: Icons.video_library,
      activeIcon: Icons.video_library,
      screen: const VideoTabScreen(),
    ),
    ShadowPlayerTab(
      title: 'Music',
      icon: Icons.library_music,
      activeIcon: Icons.library_music,
      screen: const MusicTabScreen(),
    ),
    ShadowPlayerTab(
      title: 'Settings',
      icon: Icons.settings,
      activeIcon: Icons.settings,
      screen: const PlayerSettingsScreen(),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {
          _currentIndex = _tabController.index;
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final layoutConfig = ref.watch(currentLayoutProvider);
    final screenSize = MediaQuery.of(context).size;
    final layoutService = ref.read(layoutServiceProvider);
    final deviceType = layoutService.getDeviceType(screenSize);

    return _buildLayoutSystem(layoutConfig, deviceType, screenSize);
  }

  Widget _buildLayoutSystem(
    LayoutConfiguration config,
    DeviceType deviceType,
    Size screenSize,
  ) {
    switch (config.layoutSystem) {
      case LayoutSystem.desktopOptimized:
        return _buildDesktopOptimizedLayout(config, deviceType);
      case LayoutSystem.materialDesignMobile:
        return _buildMaterialDesignLayout(config, deviceType);
      case LayoutSystem.androidNativeSmall:
        return _buildAndroidNativeLayout(config, deviceType);
    }
  }

  // Desktop-Optimized Layout (current enhanced)
  Widget _buildDesktopOptimizedLayout(
    LayoutConfiguration config,
    DeviceType deviceType,
  ) {
    final isMobile = deviceType == DeviceType.mobile;

    return Column(
      children: [
        // Enhanced Header with Tab Navigation
        Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFF2C3E50), Color(0xFF34495E)],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black26,
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.all(isMobile ? 12.0 : 24.0),
              child: Column(
                children: [
                  // Header Row
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: const Color(0xFFE74C3C),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.play_circle_filled,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      const Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'ShadowPlayer',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'Media Player & Library',
                            style: TextStyle(
                              color: Color(0xFFBDC3C7),
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                      const Spacer(),
                      if (!isMobile) ...[
                        IconButton(
                          onPressed: () {},
                          icon: const Icon(Icons.search, color: Colors.white),
                          tooltip: 'Search Media',
                        ),
                        IconButton(
                          onPressed: () {},
                          icon: const Icon(Icons.refresh, color: Colors.white),
                          tooltip: 'Refresh Library',
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Tab Bar
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: TabBar(
                      controller: _tabController,
                      indicator: BoxDecoration(
                        color: const Color(0xFFE74C3C),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      indicatorSize: TabBarIndicatorSize.tab,
                      dividerColor: Colors.transparent,
                      labelColor: Colors.white,
                      unselectedLabelColor: const Color(0xFFBDC3C7),
                      labelStyle: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                      tabs: _tabs
                          .map(
                            (tab) => Tab(
                              icon: Icon(tab.icon, size: 20),
                              text: tab.title,
                            ),
                          )
                          .toList(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Tab Content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: _tabs.map((tab) => tab.screen).toList(),
          ),
        ),
      ],
    );
  }

  // Material Design Mobile-First Layout
  Widget _buildMaterialDesignLayout(
    LayoutConfiguration config,
    DeviceType deviceType,
  ) {
    return Column(
      children: [
        // Header Bar
        Container(
          height: 64,
          decoration: const BoxDecoration(
            color: Color(0xFFE74C3C),
            boxShadow: [
              BoxShadow(
                color: Colors.black26,
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  const Text(
                    'ShadowPlayer',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () {},
                    icon: const Icon(Icons.search, color: Colors.white),
                    tooltip: 'Search Media',
                  ),
                  IconButton(
                    onPressed: () {},
                    icon: const Icon(Icons.more_vert, color: Colors.white),
                    tooltip: 'More Options',
                  ),
                ],
              ),
            ),
          ),
        ),

        // Content
        Expanded(
          child: IndexedStack(
            index: _currentIndex,
            children: _tabs.map((tab) => tab.screen).toList(),
          ),
        ),

        // Bottom Navigation
        if (config.showBottomNavigation)
          Container(
            height: config.bottomNavHeight,
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: BottomNavigationBar(
              currentIndex: _currentIndex,
              onTap: (index) {
                setState(() {
                  _currentIndex = index;
                });
                _tabController.animateTo(index);
              },
              type: BottomNavigationBarType.fixed,
              backgroundColor: Colors.white,
              selectedItemColor: const Color(0xFFE74C3C),
              unselectedItemColor: const Color(0xFF7F8C8D),
              elevation: 0,
              items: _tabs
                  .map(
                    (tab) => BottomNavigationBarItem(
                      icon: Icon(tab.icon),
                      activeIcon: Icon(tab.activeIcon),
                      label: tab.title,
                    ),
                  )
                  .toList(),
            ),
          ),
      ],
    );
  }

  // Android Native Small Screen Layout
  Widget _buildAndroidNativeLayout(
    LayoutConfiguration config,
    DeviceType deviceType,
  ) {
    return Column(
      children: [
        // Compact Header Bar
        Container(
          height: 56,
          decoration: const BoxDecoration(
            color: Color(0xFF2C3E50),
            border: Border(
              bottom: BorderSide(color: Color(0xFF34495E), width: 1),
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: const Color(0xFFE74C3C),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Icon(
                      Icons.play_circle_filled,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'ShadowPlayer',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () {},
                    icon: const Icon(
                      Icons.search,
                      color: Colors.white,
                      size: 20,
                    ),
                    tooltip: 'Search',
                    constraints: const BoxConstraints(
                      minWidth: 48,
                      minHeight: 48,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Content
        Expanded(
          child: IndexedStack(
            index: _currentIndex,
            children: _tabs.map((tab) => tab.screen).toList(),
          ),
        ),

        // Bottom Navigation
        Container(
          height: config.bottomNavHeight,
          decoration: const BoxDecoration(
            color: Color(0xFF2C3E50),
            border: Border(top: BorderSide(color: Color(0xFF34495E), width: 1)),
          ),
          child: BottomNavigationBar(
            currentIndex: _currentIndex,
            onTap: (index) {
              setState(() {
                _currentIndex = index;
              });
              _tabController.animateTo(index);
            },
            type: BottomNavigationBarType.fixed,
            backgroundColor: const Color(0xFF2C3E50),
            selectedItemColor: const Color(0xFFE74C3C),
            unselectedItemColor: const Color(0xFF7F8C8D),
            elevation: 0,
            selectedFontSize: 12,
            unselectedFontSize: 10,
            iconSize: 24,
            items: _tabs
                .map(
                  (tab) => BottomNavigationBarItem(
                    icon: Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Icon(tab.icon),
                    ),
                    activeIcon: Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Icon(tab.activeIcon),
                    ),
                    label: tab.title,
                  ),
                )
                .toList(),
          ),
        ),
      ],
    );
  }
}

class ShadowPlayerTab {
  final String title;
  final IconData icon;
  final IconData activeIcon;
  final Widget screen;

  const ShadowPlayerTab({
    required this.title,
    required this.icon,
    required this.activeIcon,
    required this.screen,
  });
}
