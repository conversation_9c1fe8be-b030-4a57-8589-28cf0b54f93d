import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/excel_app_tool.dart';

// Advanced Chart Generation Service with Real-time Data Binding
class AdvancedChartService {
  static final AdvancedChartService _instance =
      AdvancedChartService._internal();
  factory AdvancedChartService() => _instance;
  AdvancedChartService._internal();

  // Chart data cache for performance
  final Map<String, ChartData> _chartCache = {};
  final StreamController<ChartUpdateEvent> _updateController =
      StreamController.broadcast();

  // Comprehensive chart type support
  static const List<ChartType> supportedChartTypes = [
    ChartType.line,
    ChartType.bar,
    ChartType.pie,
    ChartType.scatter,
    ChartType.area,
    ChartType.radar,
    ChartType.bubble,
    ChartType.candlestick,
    ChartType.histogram,
    ChartType.heatmap,
    ChartType.treemap,
    ChartType.waterfall,
    ChartType.funnel,
    ChartType.gauge,
    ChartType.sparkline,
  ];

  // Generate chart from spreadsheet data with real-time binding
  Future<Widget> generateChart({
    required ExcelSpreadsheet spreadsheet,
    required ChartConfiguration config,
    required BuildContext context,
  }) async {
    try {
      final startTime = DateTime.now();

      // Extract and validate data
      final chartData = await _extractChartData(spreadsheet, config);

      // Generate chart widget based on type
      if (!context.mounted) {
        return const SizedBox.shrink(); // Return empty widget if context is not mounted
      }
      final chartWidget = await _buildChartWidget(chartData, config, context);

      // Cache for performance
      _chartCache[config.id] = chartData;

      // Ensure <100ms response time
      final duration = DateTime.now().difference(startTime);
      if (duration.inMilliseconds > 100) {
        debugPrint(
          'Warning: Chart generation took ${duration.inMilliseconds}ms',
        );
      }

      // Emit update event
      _updateController.add(
        ChartUpdateEvent(
          chartId: config.id,
          chartType: config.type,
          dataPoints: chartData.dataPoints.length,
          timestamp: DateTime.now(),
        ),
      );

      return chartWidget;
    } catch (error) {
      return _buildErrorChart(error.toString(), null);
    }
  }

  // Extract data from spreadsheet with comprehensive validation
  Future<ChartData> _extractChartData(
    ExcelSpreadsheet spreadsheet,
    ChartConfiguration config,
  ) async {
    final dataPoints = <ChartDataPoint>[];
    final categories = <String>[];
    final series = <ChartSeries>[];

    // Parse data range
    final dataRange = _parseRange(config.dataRange);

    for (int row = dataRange.startRow; row <= dataRange.endRow; row++) {
      for (int col = dataRange.startCol; col <= dataRange.endCol; col++) {
        final cellAddress = _getCellAddress(row, col);
        final cell = spreadsheet.cells[cellAddress];

        if (cell != null && cell.value != null) {
          final value = _parseNumericValue(cell.value);
          if (value != null) {
            dataPoints.add(
              ChartDataPoint(
                x: (col - dataRange.startCol).toDouble(),
                y: value,
                label: cell.value.toString(),
                category: _getCategoryForCell(row, col, config),
              ),
            );
          }
        }
      }
    }

    // Group data into series for multi-series charts
    if (config.hasMultipleSeries) {
      final groupedData = _groupDataIntoSeries(dataPoints, config);
      series.addAll(groupedData);
    } else {
      series.add(
        ChartSeries(
          name: config.title,
          data: dataPoints,
          color: config.primaryColor,
        ),
      );
    }

    return ChartData(
      dataPoints: dataPoints,
      categories: categories,
      series: series,
      title: config.title,
      xAxisLabel: config.xAxisLabel,
      yAxisLabel: config.yAxisLabel,
    );
  }

  // Build chart widget based on type with comprehensive styling
  Future<Widget> _buildChartWidget(
    ChartData data,
    ChartConfiguration config,
    BuildContext context,
  ) async {
    switch (config.type) {
      case ChartType.line:
        return _buildLineChart(data, config, context);
      case ChartType.bar:
        return _buildBarChart(data, config, context);
      case ChartType.pie:
        return _buildPieChart(data, config, context);
      case ChartType.scatter:
        return _buildScatterChart(data, config, context);
      case ChartType.area:
        return _buildAreaChart(data, config, context);
      case ChartType.radar:
        return _buildUnsupportedChart(config.type, context);
      case ChartType.bubble:
        return _buildUnsupportedChart(config.type, context);
      case ChartType.gauge:
        return _buildGaugeChart(data, config, context);
      case ChartType.sparkline:
        return _buildSparklineChart(data, config, context);
      default:
        return _buildUnsupportedChart(config.type, context);
    }
  }

  // Line Chart with advanced features
  Widget _buildLineChart(
    ChartData data,
    ChartConfiguration config,
    BuildContext context,
  ) {
    return Container(
      height: config.height,
      width: config.width,
      padding: const EdgeInsets.all(16),
      child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: config.showGrid,
            drawVerticalLine: true,
            drawHorizontalLine: true,
            getDrawingHorizontalLine: (value) =>
                FlLine(color: Colors.grey[300]!, strokeWidth: 1),
            getDrawingVerticalLine: (value) =>
                FlLine(color: Colors.grey[300]!, strokeWidth: 1),
          ),
          titlesData: FlTitlesData(
            show: config.showLabels,
            rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
            topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
            bottomTitles: AxisTitles(
              axisNameWidget: Text(data.xAxisLabel),
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                interval: _calculateInterval(data.dataPoints.length),
                getTitlesWidget: (value, meta) {
                  return Text(
                    value.toInt().toString(),
                    style: const TextStyle(fontSize: 12),
                  );
                },
              ),
            ),
            leftTitles: AxisTitles(
              axisNameWidget: Text(data.yAxisLabel),
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40,
                getTitlesWidget: (value, meta) {
                  return Text(
                    _formatValue(value),
                    style: const TextStyle(fontSize: 12),
                  );
                },
              ),
            ),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border.all(color: Colors.grey[400]!, width: 1),
          ),
          lineBarsData: data.series
              .map(
                (series) => LineChartBarData(
                  spots: series.data
                      .map((point) => FlSpot(point.x, point.y))
                      .toList(),
                  isCurved: config.smoothLines,
                  color: series.color,
                  barWidth: config.lineWidth,
                  isStrokeCapRound: true,
                  dotData: FlDotData(
                    show: config.showDataPoints,
                    getDotPainter: (spot, percent, barData, index) =>
                        FlDotCirclePainter(
                          radius: 4,
                          color: series.color,
                          strokeWidth: 2,
                          strokeColor: Colors.white,
                        ),
                  ),
                  belowBarData: BarAreaData(
                    show: config.fillArea,
                    color: series.color.withValues(alpha: 0.3),
                  ),
                ),
              )
              .toList(),
          lineTouchData: LineTouchData(
            enabled: config.enableInteraction,
            touchTooltipData: LineTouchTooltipData(
              tooltipBgColor: Colors.blueGrey.withValues(alpha: 0.8),
              getTooltipItems: (touchedSpots) {
                return touchedSpots.map((spot) {
                  return LineTooltipItem(
                    '${data.yAxisLabel}: ${_formatValue(spot.y)}',
                    const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  );
                }).toList();
              },
            ),
          ),
        ),
      ),
    );
  }

  // Bar Chart with comprehensive styling
  Widget _buildBarChart(
    ChartData data,
    ChartConfiguration config,
    BuildContext context,
  ) {
    return Container(
      height: config.height,
      width: config.width,
      padding: const EdgeInsets.all(16),
      child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: _getMaxValue(data.dataPoints) * 1.2,
          barTouchData: BarTouchData(
            enabled: config.enableInteraction,
            touchTooltipData: BarTouchTooltipData(
              tooltipBgColor: Colors.blueGrey.withValues(alpha: 0.8),
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                return BarTooltipItem(
                  '${data.yAxisLabel}: ${_formatValue(rod.toY)}',
                  const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                );
              },
            ),
          ),
          titlesData: FlTitlesData(
            show: config.showLabels,
            rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
            topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
            bottomTitles: AxisTitles(
              axisNameWidget: Text(data.xAxisLabel),
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  final index = value.toInt();
                  if (index >= 0 && index < data.categories.length) {
                    return Text(
                      data.categories[index],
                      style: const TextStyle(fontSize: 12),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            leftTitles: AxisTitles(
              axisNameWidget: Text(data.yAxisLabel),
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40,
                getTitlesWidget: (value, meta) {
                  return Text(
                    _formatValue(value),
                    style: const TextStyle(fontSize: 12),
                  );
                },
              ),
            ),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border.all(color: Colors.grey[400]!, width: 1),
          ),
          barGroups: data.dataPoints.asMap().entries.map((entry) {
            final index = entry.key;
            final point = entry.value;
            return BarChartGroupData(
              x: index,
              barRods: [
                BarChartRodData(
                  toY: point.y,
                  color: _getBarColor(index, config),
                  width: config.barWidth,
                  borderRadius: BorderRadius.circular(config.borderRadius),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  // Pie Chart with advanced features
  Widget _buildPieChart(
    ChartData data,
    ChartConfiguration config,
    BuildContext context,
  ) {
    return Container(
      height: config.height,
      width: config.width,
      padding: const EdgeInsets.all(16),
      child: PieChart(
        PieChartData(
          sections: data.dataPoints.asMap().entries.map((entry) {
            final index = entry.key;
            final point = entry.value;
            final percentage =
                (point.y / _getTotalValue(data.dataPoints)) * 100;

            return PieChartSectionData(
              color: _getPieColor(index, config),
              value: point.y,
              title: config.showPercentages
                  ? '${percentage.toStringAsFixed(1)}%'
                  : point.label,
              radius: config.pieRadius,
              titleStyle: TextStyle(
                fontSize: config.labelFontSize,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            );
          }).toList(),
          borderData: FlBorderData(show: false),
          sectionsSpace: config.sectionSpacing,
          centerSpaceRadius: config.centerSpaceRadius,
          pieTouchData: PieTouchData(
            enabled: config.enableInteraction,
            touchCallback: (event, response) {
              // Handle pie chart interactions
            },
          ),
        ),
      ),
    );
  }

  // Scatter Chart implementation
  Widget _buildScatterChart(
    ChartData data,
    ChartConfiguration config,
    BuildContext context,
  ) {
    return Container(
      height: config.height,
      width: config.width,
      padding: const EdgeInsets.all(16),
      child: LineChart(
        LineChartData(
          gridData: FlGridData(show: config.showGrid),
          titlesData: FlTitlesData(show: config.showLabels),
          borderData: FlBorderData(show: true),
          lineBarsData: data.series
              .map(
                (series) => LineChartBarData(
                  spots: series.data
                      .map((point) => FlSpot(point.x, point.y))
                      .toList(),
                  isCurved: false,
                  color: series.color,
                  barWidth: 0, // No line, just dots
                  dotData: FlDotData(
                    show: true,
                    getDotPainter: (spot, percent, barData, index) =>
                        FlDotCirclePainter(
                          radius: 6,
                          color: series.color,
                          strokeWidth: 2,
                          strokeColor: Colors.white,
                        ),
                  ),
                ),
              )
              .toList(),
        ),
      ),
    );
  }

  // Area Chart implementation
  Widget _buildAreaChart(
    ChartData data,
    ChartConfiguration config,
    BuildContext context,
  ) {
    return Container(
      height: config.height,
      width: config.width,
      padding: const EdgeInsets.all(16),
      child: LineChart(
        LineChartData(
          gridData: FlGridData(show: config.showGrid),
          titlesData: FlTitlesData(show: config.showLabels),
          borderData: FlBorderData(show: true),
          lineBarsData: data.series
              .map(
                (series) => LineChartBarData(
                  spots: series.data
                      .map((point) => FlSpot(point.x, point.y))
                      .toList(),
                  isCurved: config.smoothLines,
                  color: series.color,
                  barWidth: config.lineWidth,
                  belowBarData: BarAreaData(
                    show: true,
                    color: series.color.withValues(alpha: 0.3),
                  ),
                ),
              )
              .toList(),
        ),
      ),
    );
  }

  // Gauge Chart implementation
  Widget _buildGaugeChart(
    ChartData data,
    ChartConfiguration config,
    BuildContext context,
  ) {
    final value = data.dataPoints.isNotEmpty ? data.dataPoints.first.y : 0;
    final maxValue = _getMaxValue(data.dataPoints);
    final percentage = maxValue > 0 ? (value / maxValue) : 0;

    return Container(
      height: config.height,
      width: config.width,
      padding: const EdgeInsets.all(16),
      child: Center(
        child: SizedBox(
          width: 200,
          height: 200,
          child: CircularProgressIndicator(
            value: percentage.toDouble(),
            strokeWidth: 20,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(config.primaryColor),
          ),
        ),
      ),
    );
  }

  // Sparkline Chart implementation
  Widget _buildSparklineChart(
    ChartData data,
    ChartConfiguration config,
    BuildContext context,
  ) {
    return Container(
      height: 50,
      width: config.width,
      padding: const EdgeInsets.all(4),
      child: LineChart(
        LineChartData(
          gridData: FlGridData(show: false),
          titlesData: FlTitlesData(show: false),
          borderData: FlBorderData(show: false),
          lineBarsData: [
            LineChartBarData(
              spots: data.dataPoints
                  .map((point) => FlSpot(point.x, point.y))
                  .toList(),
              isCurved: true,
              color: config.primaryColor,
              barWidth: 2,
              dotData: FlDotData(show: false),
            ),
          ],
        ),
      ),
    );
  }

  // Utility methods for chart generation
  double _calculateInterval(int dataPointCount) {
    if (dataPointCount <= 5) return 1;
    if (dataPointCount <= 10) return 2;
    if (dataPointCount <= 20) return 5;
    return (dataPointCount / 10).ceil().toDouble();
  }

  String _formatValue(double value) {
    if (value.abs() >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value.abs() >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return value.toStringAsFixed(1);
    }
  }

  double _getMaxValue(List<ChartDataPoint> points) {
    return points.isEmpty ? 0 : points.map((p) => p.y).reduce(math.max);
  }

  double _getTotalValue(List<ChartDataPoint> points) {
    return points.isEmpty ? 0 : points.map((p) => p.y).reduce((a, b) => a + b);
  }

  Color _getBarColor(int index, ChartConfiguration config) {
    final colors = config.colorPalette.isNotEmpty
        ? config.colorPalette
        : [Colors.blue, Colors.red, Colors.green, Colors.orange, Colors.purple];
    return colors[index % colors.length];
  }

  Color _getPieColor(int index, ChartConfiguration config) {
    final colors = config.colorPalette.isNotEmpty
        ? config.colorPalette
        : [
            Colors.blue,
            Colors.red,
            Colors.green,
            Colors.orange,
            Colors.purple,
            Colors.teal,
          ];
    return colors[index % colors.length];
  }

  // Error handling
  Widget _buildErrorChart(String error, BuildContext? context) {
    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.red),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error, color: Colors.red, size: 48),
          const SizedBox(height: 8),
          Text(
            'Chart Generation Error',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            error,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildUnsupportedChart(ChartType type, BuildContext context) {
    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.orange),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.warning, color: Colors.orange, size: 48),
          const SizedBox(height: 8),
          Text(
            'Unsupported Chart Type',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.orange,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            type.toString(),
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  // Additional utility methods will be added in the next edit
  DataRange _parseRange(String range) {
    // Parse Excel-style range (e.g., "A1:C10")
    final parts = range.split(':');
    final start = _parseCellAddress(parts[0]);
    final end = parts.length > 1 ? _parseCellAddress(parts[1]) : start;

    return DataRange(
      startRow: start.row,
      startCol: start.col,
      endRow: end.row,
      endCol: end.col,
    );
  }

  CellPosition _parseCellAddress(String address) {
    final match = RegExp(r'^([A-Z]+)(\d+)$').firstMatch(address.toUpperCase());
    if (match == null) throw ArgumentError('Invalid cell address: $address');

    final colStr = match.group(1)!;
    final rowStr = match.group(2)!;

    int col = 0;
    for (int i = 0; i < colStr.length; i++) {
      col = col * 26 + (colStr.codeUnitAt(i) - 'A'.codeUnitAt(0) + 1);
    }

    return CellPosition(row: int.parse(rowStr), col: col);
  }

  String _getCellAddress(int row, int col) {
    String colStr = '';
    int tempCol = col;
    while (tempCol > 0) {
      tempCol--;
      colStr = String.fromCharCode('A'.codeUnitAt(0) + (tempCol % 26)) + colStr;
      tempCol ~/= 26;
    }
    return '$colStr$row';
  }

  double? _parseNumericValue(dynamic value) {
    if (value is num) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  String _getCategoryForCell(int row, int col, ChartConfiguration config) {
    // Implementation for category extraction
    return 'Category ${col - config.dataRange.split(':')[0].replaceAll(RegExp(r'\d'), '').length + 1}';
  }

  List<ChartSeries> _groupDataIntoSeries(
    List<ChartDataPoint> dataPoints,
    ChartConfiguration config,
  ) {
    // Implementation for grouping data into multiple series
    return [
      ChartSeries(
        name: 'Series 1',
        data: dataPoints,
        color: config.primaryColor,
      ),
    ];
  }

  // Cleanup
  void dispose() {
    _updateController.close();
    _chartCache.clear();
  }

  // Getters
  Stream<ChartUpdateEvent> get updateStream => _updateController.stream;
  Map<String, ChartData> get chartCache => Map.unmodifiable(_chartCache);
}

// Chart Models and Enums
enum ChartType {
  line,
  bar,
  pie,
  scatter,
  area,
  radar,
  bubble,
  candlestick,
  histogram,
  heatmap,
  treemap,
  waterfall,
  funnel,
  gauge,
  sparkline,
}

class ChartConfiguration {
  final String id;
  final ChartType type;
  final String title;
  final String dataRange;
  final String xAxisLabel;
  final String yAxisLabel;
  final double height;
  final double width;
  final bool showGrid;
  final bool showLabels;
  final bool enableInteraction;
  final bool smoothLines;
  final bool showDataPoints;
  final bool fillArea;
  final bool showPercentages;
  final bool hasMultipleSeries;
  final double lineWidth;
  final double barWidth;
  final double pieRadius;
  final double centerSpaceRadius;
  final double sectionSpacing;
  final double borderRadius;
  final double labelFontSize;
  final Color primaryColor;
  final List<Color> colorPalette;

  const ChartConfiguration({
    required this.id,
    required this.type,
    required this.title,
    required this.dataRange,
    this.xAxisLabel = 'X Axis',
    this.yAxisLabel = 'Y Axis',
    this.height = 300,
    this.width = 400,
    this.showGrid = true,
    this.showLabels = true,
    this.enableInteraction = true,
    this.smoothLines = false,
    this.showDataPoints = true,
    this.fillArea = false,
    this.showPercentages = true,
    this.hasMultipleSeries = false,
    this.lineWidth = 2.0,
    this.barWidth = 20.0,
    this.pieRadius = 100.0,
    this.centerSpaceRadius = 40.0,
    this.sectionSpacing = 2.0,
    this.borderRadius = 4.0,
    this.labelFontSize = 12.0,
    this.primaryColor = Colors.blue,
    this.colorPalette = const [],
  });
}

class ChartData {
  final List<ChartDataPoint> dataPoints;
  final List<String> categories;
  final List<ChartSeries> series;
  final String title;
  final String xAxisLabel;
  final String yAxisLabel;

  const ChartData({
    required this.dataPoints,
    required this.categories,
    required this.series,
    required this.title,
    required this.xAxisLabel,
    required this.yAxisLabel,
  });
}

class ChartDataPoint {
  final double x;
  final double y;
  final String label;
  final String category;

  const ChartDataPoint({
    required this.x,
    required this.y,
    required this.label,
    required this.category,
  });
}

class ChartSeries {
  final String name;
  final List<ChartDataPoint> data;
  final Color color;

  const ChartSeries({
    required this.name,
    required this.data,
    required this.color,
  });
}

class ChartUpdateEvent {
  final String chartId;
  final ChartType chartType;
  final int dataPoints;
  final DateTime timestamp;

  const ChartUpdateEvent({
    required this.chartId,
    required this.chartType,
    required this.dataPoints,
    required this.timestamp,
  });
}

class DataRange {
  final int startRow;
  final int startCol;
  final int endRow;
  final int endCol;

  const DataRange({
    required this.startRow,
    required this.startCol,
    required this.endRow,
    required this.endCol,
  });
}

class CellPosition {
  final int row;
  final int col;

  const CellPosition({required this.row, required this.col});
}
