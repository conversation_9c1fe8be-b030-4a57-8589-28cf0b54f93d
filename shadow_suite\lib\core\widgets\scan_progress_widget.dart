import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/storage_scanner_service.dart';

/// Widget to display storage scanning progress
class ScanProgressWidget extends ConsumerWidget {
  final String title;
  final VoidCallback? onCancel;
  final bool showDetails;

  const ScanProgressWidget({
    super.key,
    this.title = 'Scanning Storage',
    this.onCancel,
    this.showDetails = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final scanProgressAsync = ref.watch(scanProgressProvider);
    final isScanning = ref.watch(isScanningProvider);

    if (!isScanning) {
      return const SizedBox.shrink();
    }

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                const Icon(Icons.search, color: Colors.blue),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (onCancel != null)
                  IconButton(
                    onPressed: onCancel,
                    icon: const Icon(Icons.close),
                    tooltip: 'Cancel Scan',
                  ),
              ],
            ),
            const SizedBox(height: 16),
            scanProgressAsync.when(
              data: (progress) => _buildProgressContent(context, progress),
              loading: () => _buildLoadingContent(context),
              error: (error, stack) => _buildErrorContent(context, error),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressContent(BuildContext context, ScanProgress progress) {
    return Column(
      children: [
        LinearProgressIndicator(
          value: progress.progress,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            _getProgressColor(progress.phase),
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Icon(
              _getPhaseIcon(progress.phase),
              size: 16,
              color: _getProgressColor(progress.phase),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                progress.message,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
            Text(
              '${(progress.progress * 100).toInt()}%',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        if (showDetails) ...[
          const SizedBox(height: 8),
          _buildPhaseIndicator(context, progress.phase),
        ],
      ],
    );
  }

  Widget _buildLoadingContent(BuildContext context) {
    return Column(
      children: [
        const LinearProgressIndicator(),
        const SizedBox(height: 12),
        Row(
          children: [
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Initializing scan...',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildErrorContent(BuildContext context, Object error) {
    return Column(
      children: [
        LinearProgressIndicator(
          value: 0,
          backgroundColor: Colors.grey[300],
          valueColor: const AlwaysStoppedAnimation<Color>(Colors.red),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            const Icon(Icons.error, size: 16, color: Colors.red),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Scan failed: ${error.toString()}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.red,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPhaseIndicator(BuildContext context, ScanPhase phase) {
    final phases = [
      ScanPhase.starting,
      ScanPhase.scanning,
      ScanPhase.organizing,
      ScanPhase.completed,
    ];

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: phases.map((p) {
        final isActive = p.index <= phase.index;
        final isCurrent = p == phase;
        
        return Column(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isActive 
                    ? _getProgressColor(phase)
                    : Colors.grey[300],
                border: isCurrent 
                    ? Border.all(color: _getProgressColor(phase), width: 2)
                    : null,
              ),
              child: Icon(
                _getPhaseIcon(p),
                size: 12,
                color: isActive ? Colors.white : Colors.grey[600],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              _getPhaseLabel(p),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: isActive ? _getProgressColor(phase) : Colors.grey[600],
                fontWeight: isCurrent ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        );
      }).toList(),
    );
  }

  Color _getProgressColor(ScanPhase phase) {
    switch (phase) {
      case ScanPhase.starting:
        return Colors.blue;
      case ScanPhase.scanning:
        return Colors.orange;
      case ScanPhase.organizing:
        return Colors.purple;
      case ScanPhase.completed:
        return Colors.green;
      case ScanPhase.error:
        return Colors.red;
    }
  }

  IconData _getPhaseIcon(ScanPhase phase) {
    switch (phase) {
      case ScanPhase.starting:
        return Icons.play_arrow;
      case ScanPhase.scanning:
        return Icons.search;
      case ScanPhase.organizing:
        return Icons.sort;
      case ScanPhase.completed:
        return Icons.check;
      case ScanPhase.error:
        return Icons.error;
    }
  }

  String _getPhaseLabel(ScanPhase phase) {
    switch (phase) {
      case ScanPhase.starting:
        return 'Start';
      case ScanPhase.scanning:
        return 'Scan';
      case ScanPhase.organizing:
        return 'Organize';
      case ScanPhase.completed:
        return 'Done';
      case ScanPhase.error:
        return 'Error';
    }
  }
}

/// Compact scan progress indicator for app bars
class CompactScanIndicator extends ConsumerWidget {
  const CompactScanIndicator({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isScanning = ref.watch(isScanningProvider);
    final scanProgressAsync = ref.watch(scanProgressProvider);

    if (!isScanning) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 12,
            height: 12,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          const SizedBox(width: 6),
          scanProgressAsync.when(
            data: (progress) => Text(
              '${(progress.progress * 100).toInt()}%',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
            loading: () => Text(
              'Scanning...',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            error: (_, __) => Icon(
              Icons.error,
              size: 12,
              color: Colors.red,
            ),
          ),
        ],
      ),
    );
  }
}

/// Scan results summary widget
class ScanResultsSummary extends StatelessWidget {
  final StorageScanResult results;
  final VoidCallback? onViewDetails;

  const ScanResultsSummary({
    super.key,
    required this.results,
    this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.analytics, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Scan Results',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                if (onViewDetails != null)
                  TextButton(
                    onPressed: onViewDetails,
                    child: const Text('View Details'),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            _buildResultRow(context, Icons.image, 'Images', results.images.length, Colors.blue),
            _buildResultRow(context, Icons.videocam, 'Videos', results.videos.length, Colors.red),
            _buildResultRow(context, Icons.audiotrack, 'Audio', results.audio.length, Colors.orange),
            _buildResultRow(context, Icons.description, 'Documents', results.documents.length, Colors.green),
            if (results.others.isNotEmpty)
              _buildResultRow(context, Icons.insert_drive_file, 'Others', results.others.length, Colors.grey),
            const Divider(),
            _buildResultRow(context, Icons.folder, 'Total Files', results.totalFiles, Colors.purple),
          ],
        ),
      ),
    );
  }

  Widget _buildResultRow(BuildContext context, IconData icon, String label, int count, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 20, color: color),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              count.toString(),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
