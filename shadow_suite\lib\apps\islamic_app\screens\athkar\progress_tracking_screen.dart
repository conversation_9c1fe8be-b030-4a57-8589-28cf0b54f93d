import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../../core/theme/app_theme.dart';
import '../../services/islamic_providers.dart';
import '../../models/athkar.dart';

class ProgressTrackingScreen extends ConsumerStatefulWidget {
  const ProgressTrackingScreen({super.key});

  @override
  ConsumerState<ProgressTrackingScreen> createState() =>
      _ProgressTrackingScreenState();
}

class _ProgressTrackingScreenState extends ConsumerState<ProgressTrackingScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedPeriod = 'Week';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final sessionsAsync = ref.watch(dhikrSessionsProvider);
    final dailyProgressAsync = ref.watch(dailyProgressProvider);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Progress Tracking'),
        backgroundColor: AppTheme.islamicAppColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            ref.read(islamicAppCurrentScreenProvider.notifier).state =
                IslamicAppScreen.dashboard;
          },
        ),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              setState(() {
                _selectedPeriod = value;
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'Day', child: Text('Today')),
              const PopupMenuItem(value: 'Week', child: Text('This Week')),
              const PopupMenuItem(value: 'Month', child: Text('This Month')),
            ],
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(_selectedPeriod),
                  const Icon(Icons.arrow_drop_down),
                ],
              ),
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Overview', icon: Icon(Icons.dashboard)),
            Tab(text: 'Charts', icon: Icon(Icons.bar_chart)),
            Tab(text: 'History', icon: Icon(Icons.history)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(dailyProgressAsync, sessionsAsync),
          _buildChartsTab(sessionsAsync),
          _buildHistoryTab(sessionsAsync),
        ],
      ),
    );
  }

  Widget _buildOverviewTab(
    AsyncValue<Map<AthkarCategory, int>> dailyProgressAsync,
    AsyncValue<List<DhikrSession>> sessionsAsync,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTodayProgress(dailyProgressAsync),
          const SizedBox(height: 24),
          _buildWeeklyStats(sessionsAsync),
          const SizedBox(height: 24),
          _buildStreakCard(sessionsAsync),
          const SizedBox(height: 24),
          _buildGoalsSection(),
        ],
      ),
    );
  }

  Widget _buildTodayProgress(
    AsyncValue<Map<AthkarCategory, int>> dailyProgressAsync,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.today, color: AppTheme.islamicAppColor),
                const SizedBox(width: 8),
                Text(
                  'Today\'s Progress',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.islamicAppColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            dailyProgressAsync.when(
              data: (progress) {
                final categories = [
                  AthkarCategory.morning,
                  AthkarCategory.evening,
                  AthkarCategory.afterPrayer,
                ];

                return Column(
                  children: categories.map((category) {
                    final completed = progress[category] ?? 0;
                    final target = _getTargetForCategory(category);
                    final progressValue = target > 0 ? completed / target : 0.0;

                    return Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                category.displayName,
                                style: Theme.of(context).textTheme.bodyMedium
                                    ?.copyWith(fontWeight: FontWeight.w500),
                              ),
                              Text(
                                '$completed/$target',
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(color: Colors.grey[600]),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          LinearProgressIndicator(
                            value: progressValue.clamp(0.0, 1.0),
                            backgroundColor: Colors.grey[300],
                            valueColor: AlwaysStoppedAnimation<Color>(
                              _getCategoryColor(category),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text('Error: $error'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWeeklyStats(AsyncValue<List<DhikrSession>> sessionsAsync) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.calendar_view_week, color: AppTheme.islamicAppColor),
                const SizedBox(width: 8),
                Text(
                  'Weekly Statistics',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.islamicAppColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            sessionsAsync.when(
              data: (sessions) {
                final weekSessions = _getSessionsForPeriod(sessions, 'Week');
                final completedSessions = weekSessions
                    .where((s) => s.isCompleted)
                    .length;
                final totalDhikrCount = weekSessions
                    .where((s) => s.isCompleted)
                    .fold(0, (sum, s) => sum + s.currentCount);
                final avgSessionTime = _calculateAverageSessionTime(
                  weekSessions,
                );

                return Row(
                  children: [
                    Expanded(
                      child: _buildStatItem(
                        'Sessions',
                        completedSessions.toString(),
                        Icons.play_circle,
                        Colors.blue,
                      ),
                    ),
                    Expanded(
                      child: _buildStatItem(
                        'Total Dhikr',
                        totalDhikrCount.toString(),
                        Icons.repeat,
                        Colors.green,
                      ),
                    ),
                    Expanded(
                      child: _buildStatItem(
                        'Avg Time',
                        avgSessionTime,
                        Icons.timer,
                        Colors.orange,
                      ),
                    ),
                  ],
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text('Error: $error'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildStreakCard(AsyncValue<List<DhikrSession>> sessionsAsync) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.local_fire_department, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  'Current Streak',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.islamicAppColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            sessionsAsync.when(
              data: (sessions) {
                final streak = _calculateCurrentStreak(sessions);
                return Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '$streak days',
                            style: Theme.of(context).textTheme.headlineLarge
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.orange,
                                ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Keep it up! Consistency is key to spiritual growth.',
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(color: Colors.grey[600]),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.local_fire_department,
                      size: 48,
                      color: Colors.orange.withValues(alpha: 0.3),
                    ),
                  ],
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text('Error: $error'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(Icons.flag, color: AppTheme.islamicAppColor),
                    const SizedBox(width: 8),
                    Text(
                      'Goals',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.islamicAppColor,
                      ),
                    ),
                  ],
                ),
                TextButton(
                  onPressed: () => _showGoalDialog(),
                  child: const Text('Set Goal'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildGoalItem('Daily Dhikr Sessions', 3, 2),
            const SizedBox(height: 12),
            _buildGoalItem('Weekly Athkar Completion', 7, 5),
            const SizedBox(height: 12),
            _buildGoalItem('Monthly Streak Days', 30, 15),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalItem(String title, int target, int current) {
    final progress = target > 0 ? current / target : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
            ),
            Text(
              '$current/$target',
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: progress.clamp(0.0, 1.0),
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            progress >= 1.0 ? Colors.green : AppTheme.islamicAppColor,
          ),
        ),
      ],
    );
  }

  Widget _buildChartsTab(AsyncValue<List<DhikrSession>> sessionsAsync) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSessionsChart(sessionsAsync),
          const SizedBox(height: 24),
          _buildCategoryChart(sessionsAsync),
          const SizedBox(height: 24),
          _buildTimeChart(sessionsAsync),
        ],
      ),
    );
  }

  Widget _buildSessionsChart(AsyncValue<List<DhikrSession>> sessionsAsync) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sessions Over Time',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.islamicAppColor,
              ),
            ),
            const SizedBox(height: 16),
            sessionsAsync.when(
              data: (sessions) {
                final chartData = _getChartData(sessions);
                return SizedBox(
                  height: 200,
                  child: LineChart(
                    LineChartData(
                      gridData: FlGridData(show: true),
                      titlesData: FlTitlesData(
                        leftTitles: AxisTitles(
                          sideTitles: SideTitles(showTitles: true),
                        ),
                        bottomTitles: AxisTitles(
                          sideTitles: SideTitles(showTitles: true),
                        ),
                        rightTitles: AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                        topTitles: AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                      ),
                      borderData: FlBorderData(show: true),
                      lineBarsData: [
                        LineChartBarData(
                          spots: chartData,
                          isCurved: true,
                          color: AppTheme.islamicAppColor,
                          barWidth: 3,
                          dotData: FlDotData(show: true),
                        ),
                      ],
                    ),
                  ),
                );
              },
              loading: () => const SizedBox(
                height: 200,
                child: Center(child: CircularProgressIndicator()),
              ),
              error: (error, stack) => SizedBox(
                height: 200,
                child: Center(child: Text('Error: $error')),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryChart(AsyncValue<List<DhikrSession>> sessionsAsync) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sessions by Category',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.islamicAppColor,
              ),
            ),
            const SizedBox(height: 16),
            sessionsAsync.when(
              data: (sessions) {
                final categoryData = _getCategoryData(sessions);
                return SizedBox(
                  height: 200,
                  child: PieChart(
                    PieChartData(
                      sections: categoryData.map((data) {
                        return PieChartSectionData(
                          value: data['value'],
                          title: '${data['percentage']}%',
                          color: data['color'],
                          radius: 60,
                        );
                      }).toList(),
                      centerSpaceRadius: 40,
                    ),
                  ),
                );
              },
              loading: () => const SizedBox(
                height: 200,
                child: Center(child: CircularProgressIndicator()),
              ),
              error: (error, stack) => SizedBox(
                height: 200,
                child: Center(child: Text('Error: $error')),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeChart(AsyncValue<List<DhikrSession>> sessionsAsync) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Session Duration',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.islamicAppColor,
              ),
            ),
            const SizedBox(height: 16),
            sessionsAsync.when(
              data: (sessions) {
                final timeData = _getTimeData(sessions);
                return SizedBox(
                  height: 200,
                  child: BarChart(
                    BarChartData(
                      alignment: BarChartAlignment.spaceAround,
                      maxY: timeData.isNotEmpty
                          ? timeData
                                    .map((e) => e.y)
                                    .reduce((a, b) => a > b ? a : b) *
                                1.2
                          : 10,
                      barTouchData: BarTouchData(enabled: false),
                      titlesData: FlTitlesData(
                        show: true,
                        bottomTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            getTitlesWidget: (value, meta) {
                              const days = [
                                'Mon',
                                'Tue',
                                'Wed',
                                'Thu',
                                'Fri',
                                'Sat',
                                'Sun',
                              ];
                              if (value.toInt() >= 0 &&
                                  value.toInt() < days.length) {
                                return Text(days[value.toInt()]);
                              }
                              return const Text('');
                            },
                          ),
                        ),
                        leftTitles: AxisTitles(
                          sideTitles: SideTitles(showTitles: true),
                        ),
                        topTitles: AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                        rightTitles: AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                      ),
                      borderData: FlBorderData(show: false),
                      barGroups: timeData.map((data) {
                        return BarChartGroupData(
                          x: data.x.toInt(),
                          barRods: [
                            BarChartRodData(
                              toY: data.y,
                              color: AppTheme.islamicAppColor,
                              width: 16,
                            ),
                          ],
                        );
                      }).toList(),
                    ),
                  ),
                );
              },
              loading: () => const SizedBox(
                height: 200,
                child: Center(child: CircularProgressIndicator()),
              ),
              error: (error, stack) => SizedBox(
                height: 200,
                child: Center(child: Text('Error: $error')),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryTab(AsyncValue<List<DhikrSession>> sessionsAsync) {
    return sessionsAsync.when(
      data: (sessions) {
        final filteredSessions = _getSessionsForPeriod(
          sessions,
          _selectedPeriod,
        );

        if (filteredSessions.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.history, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'No sessions found',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(color: Colors.grey[600]),
                ),
                const SizedBox(height: 8),
                Text(
                  'Start practicing dhikr to see your history',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(24),
          itemCount: filteredSessions.length,
          itemBuilder: (context, index) {
            final session = filteredSessions[index];
            return _buildSessionHistoryItem(session);
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(child: Text('Error: $error')),
    );
  }

  Widget _buildSessionHistoryItem(DhikrSession session) {
    final dhikrAsync = ref.watch(dhikrProvider);

    return dhikrAsync.when(
      data: (allDhikr) {
        final dhikr = allDhikr.firstWhere((d) => d.id == session.dhikrId);

        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: _getCategoryColor(
                          session.category,
                        ).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        _getCategoryIcon(session.category),
                        color: _getCategoryColor(session.category),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            dhikr.textEnglish,
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(fontWeight: FontWeight.w500),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            session.category.displayName,
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(color: Colors.grey[600]),
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '${session.currentCount}/${session.targetCount}',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: session.isCompleted
                                    ? Colors.green
                                    : Colors.orange,
                              ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _formatDate(session.startTime),
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ],
                ),
                if (session.duration != null) ...[
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Icon(Icons.timer, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Text(
                        'Duration: ${_formatDuration(session.duration!)}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      const Spacer(),
                      if (session.isCompleted)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.green.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'Completed',
                            style: TextStyle(
                              color: Colors.green,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        );
      },
      loading: () => const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Center(child: CircularProgressIndicator()),
        ),
      ),
      error: (error, stack) => Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Text('Error loading dhikr: $error'),
        ),
      ),
    );
  }

  // Helper methods
  int _getTargetForCategory(AthkarCategory category) {
    switch (category) {
      case AthkarCategory.morning:
      case AthkarCategory.evening:
        return 1;
      case AthkarCategory.afterPrayer:
        return 5; // 5 prayers per day
      default:
        return 1;
    }
  }

  Color _getCategoryColor(AthkarCategory category) {
    switch (category) {
      case AthkarCategory.morning:
        return Colors.orange;
      case AthkarCategory.evening:
        return Colors.indigo;
      case AthkarCategory.afterPrayer:
        return Colors.green;
      case AthkarCategory.sleeping:
        return Colors.purple;
      case AthkarCategory.eating:
        return Colors.brown;
      case AthkarCategory.travel:
        return Colors.blue;
      default:
        return AppTheme.islamicAppColor;
    }
  }

  IconData _getCategoryIcon(AthkarCategory category) {
    switch (category) {
      case AthkarCategory.morning:
        return Icons.wb_sunny;
      case AthkarCategory.evening:
        return Icons.nights_stay;
      case AthkarCategory.afterPrayer:
        return Icons.mosque;
      case AthkarCategory.sleeping:
        return Icons.bedtime;
      case AthkarCategory.eating:
        return Icons.restaurant;
      case AthkarCategory.travel:
        return Icons.directions_car;
      default:
        return Icons.auto_awesome;
    }
  }

  List<DhikrSession> _getSessionsForPeriod(
    List<DhikrSession> sessions,
    String period,
  ) {
    final now = DateTime.now();
    DateTime startDate;

    switch (period) {
      case 'Day':
        startDate = DateTime(now.year, now.month, now.day);
        break;
      case 'Week':
        startDate = now.subtract(Duration(days: now.weekday - 1));
        startDate = DateTime(startDate.year, startDate.month, startDate.day);
        break;
      case 'Month':
        startDate = DateTime(now.year, now.month, 1);
        break;
      default:
        startDate = DateTime(now.year, now.month, now.day);
    }

    return sessions
        .where((session) => session.startTime.isAfter(startDate))
        .toList();
  }

  String _calculateAverageSessionTime(List<DhikrSession> sessions) {
    final completedSessions = sessions
        .where((s) => s.isCompleted && s.duration != null)
        .toList();

    if (completedSessions.isEmpty) return '0m';

    final totalMinutes = completedSessions
        .map((s) => s.duration!.inMinutes)
        .fold(0, (sum, minutes) => sum + minutes);

    final avgMinutes = totalMinutes / completedSessions.length;

    if (avgMinutes < 60) {
      return '${avgMinutes.round()}m';
    } else {
      final hours = avgMinutes ~/ 60;
      final minutes = (avgMinutes % 60).round();
      return '${hours}h ${minutes}m';
    }
  }

  int _calculateCurrentStreak(List<DhikrSession> sessions) {
    final completedSessions = sessions.where((s) => s.isCompleted).toList()
      ..sort((a, b) => b.startTime.compareTo(a.startTime));

    if (completedSessions.isEmpty) return 0;

    int streak = 0;
    DateTime currentDate = DateTime.now();

    for (final session in completedSessions) {
      final sessionDate = DateTime(
        session.startTime.year,
        session.startTime.month,
        session.startTime.day,
      );

      final checkDate = DateTime(
        currentDate.year,
        currentDate.month,
        currentDate.day,
      );

      if (sessionDate.isAtSameMomentAs(checkDate) ||
          sessionDate.isAtSameMomentAs(
            checkDate.subtract(const Duration(days: 1)),
          )) {
        streak++;
        currentDate = currentDate.subtract(const Duration(days: 1));
      } else {
        break;
      }
    }

    return streak;
  }

  List<FlSpot> _getChartData(List<DhikrSession> sessions) {
    final now = DateTime.now();
    final data = <FlSpot>[];

    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dayStart = DateTime(date.year, date.month, date.day);
      final dayEnd = dayStart.add(const Duration(days: 1));

      final daySessionsCount = sessions
          .where(
            (s) =>
                s.startTime.isAfter(dayStart) &&
                s.startTime.isBefore(dayEnd) &&
                s.isCompleted,
          )
          .length;

      data.add(FlSpot((6 - i).toDouble(), daySessionsCount.toDouble()));
    }

    return data;
  }

  List<Map<String, dynamic>> _getCategoryData(List<DhikrSession> sessions) {
    final completedSessions = sessions.where((s) => s.isCompleted).toList();
    final categoryCount = <AthkarCategory, int>{};

    for (final session in completedSessions) {
      categoryCount[session.category] =
          (categoryCount[session.category] ?? 0) + 1;
    }

    final total = categoryCount.values.fold(0, (sum, count) => sum + count);
    if (total == 0) return [];

    return categoryCount.entries.map((entry) {
      final percentage = ((entry.value / total) * 100).round();
      return {
        'category': entry.key,
        'value': entry.value.toDouble(),
        'percentage': percentage,
        'color': _getCategoryColor(entry.key),
      };
    }).toList();
  }

  List<FlSpot> _getTimeData(List<DhikrSession> sessions) {
    final now = DateTime.now();
    final data = <FlSpot>[];

    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dayStart = DateTime(date.year, date.month, date.day);
      final dayEnd = dayStart.add(const Duration(days: 1));

      final daySessions = sessions
          .where(
            (s) =>
                s.startTime.isAfter(dayStart) &&
                s.startTime.isBefore(dayEnd) &&
                s.isCompleted &&
                s.duration != null,
          )
          .toList();

      final totalMinutes = daySessions
          .map((s) => s.duration!.inMinutes)
          .fold(0, (sum, minutes) => sum + minutes);

      data.add(FlSpot((6 - i).toDouble(), totalMinutes.toDouble()));
    }

    return data;
  }

  void _showGoalDialog() {
    final TextEditingController dailyGoalController = TextEditingController();
    final TextEditingController weeklyGoalController = TextEditingController();
    final TextEditingController monthlyGoalController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Set Dhikr Goals'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Set your dhikr goals to track your progress:'),
              const SizedBox(height: 16),
              TextField(
                controller: dailyGoalController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Daily Goal',
                  hintText: 'e.g., 100',
                  border: OutlineInputBorder(),
                  suffixText: 'dhikr/day',
                ),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: weeklyGoalController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Weekly Goal',
                  hintText: 'e.g., 700',
                  border: OutlineInputBorder(),
                  suffixText: 'dhikr/week',
                ),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: monthlyGoalController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Monthly Goal',
                  hintText: 'e.g., 3000',
                  border: OutlineInputBorder(),
                  suffixText: 'dhikr/month',
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Column(
                  children: [
                    Icon(Icons.lightbulb, color: Colors.blue),
                    SizedBox(height: 8),
                    Text(
                      'Tip: Start with achievable goals and gradually increase them. Consistency is more important than quantity.',
                      style: TextStyle(fontSize: 12, color: Colors.blue),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final dailyGoal = int.tryParse(dailyGoalController.text) ?? 0;
              final weeklyGoal = int.tryParse(weeklyGoalController.text) ?? 0;
              final monthlyGoal = int.tryParse(monthlyGoalController.text) ?? 0;

              if (dailyGoal > 0 || weeklyGoal > 0 || monthlyGoal > 0) {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Goals set! Daily: $dailyGoal, Weekly: $weeklyGoal, Monthly: $monthlyGoal',
                    ),
                    backgroundColor: Colors.green,
                  ),
                );

                // In a real app, you would save these goals to storage
                // For now, we'll just show a success message
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please set at least one goal'),
                    backgroundColor: Colors.orange,
                  ),
                );
              }
            },
            child: const Text('Set Goals'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);

    if (dateOnly.isAtSameMomentAs(today)) {
      return 'Today ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } else if (dateOnly.isAtSameMomentAs(yesterday)) {
      return 'Yesterday ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } else {
      return '${date.day}/${date.month} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    }
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;

    if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }
}
