import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../themes/theme_models_base.dart';

// Layout Service Provider
final layoutServiceProvider = Provider<LayoutService>((ref) {
  return LayoutService();
});

// Current Layout Provider
final currentLayoutProvider =
    StateNotifierProvider<LayoutNotifier, LayoutConfiguration>((ref) {
      return LayoutNotifier();
    });

// Screen Size Provider
final screenSizeProvider = StateProvider<Size>((ref) {
  return const Size(1024, 768); // Default desktop size
});

// Layout Service
class LayoutService {
  // Get current device type based on screen size
  DeviceType getDeviceType(Size screenSize) {
    if (screenSize.width < 480) {
      return DeviceType.mobile;
    } else if (screenSize.width < 768) {
      return DeviceType.tablet;
    } else if (screenSize.width < 1024) {
      return DeviceType.desktop;
    } else {
      return DeviceType.largeDesktop;
    }
  }

  // Get recommended layout system for device
  LayoutSystem getRecommendedLayout(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return LayoutSystem.androidNativeSmall;
      case DeviceType.tablet:
        return LayoutSystem.materialDesignMobile;
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        return LayoutSystem.desktopOptimized;
    }
  }

  // Get layout configuration for specific layout system
  LayoutConfiguration getLayoutConfig(
    LayoutSystem layoutSystem,
    DeviceType deviceType,
  ) {
    switch (layoutSystem) {
      case LayoutSystem.desktopOptimized:
        return _getDesktopOptimizedConfig(deviceType);
      case LayoutSystem.materialDesignMobile:
        return _getMaterialDesignConfig(deviceType);
      case LayoutSystem.androidNativeSmall:
        return _getAndroidNativeConfig(deviceType);
    }
  }

  // Desktop Optimized Layout Configuration
  LayoutConfiguration _getDesktopOptimizedConfig(DeviceType deviceType) {
    return LayoutConfiguration(
      layoutSystem: LayoutSystem.desktopOptimized,
      breakpoints: const ResponsiveBreakpoints(
        mobile: 480,
        tablet: 768,
        desktop: 1024,
        largeDesktop: 1440,
      ),
      touchTargets: const TouchTargetConfig(
        minimumSize: 32.0, // Smaller for mouse interaction
        preferredSize: 40.0,
        spacing: 4.0,
        enableHapticFeedback: false,
      ),
      enableSidebarCollapse: deviceType == DeviceType.mobile,
      showBottomNavigation: false,
      enableSwipeGestures: deviceType == DeviceType.mobile,
      sidebarWidth: deviceType == DeviceType.mobile ? 240.0 : 280.0,
      bottomNavHeight: 0.0,
    );
  }

  // Material Design Mobile Layout Configuration
  LayoutConfiguration _getMaterialDesignConfig(DeviceType deviceType) {
    return LayoutConfiguration(
      layoutSystem: LayoutSystem.materialDesignMobile,
      breakpoints: const ResponsiveBreakpoints(
        mobile: 600,
        tablet: 905,
        desktop: 1240,
        largeDesktop: 1440,
      ),
      touchTargets: const TouchTargetConfig(
        minimumSize: 48.0, // Material Design standard
        preferredSize: 56.0,
        spacing: 8.0,
        enableHapticFeedback: true,
      ),
      enableSidebarCollapse: true,
      showBottomNavigation: deviceType == DeviceType.mobile,
      enableSwipeGestures: true,
      sidebarWidth: 256.0, // Material Design standard
      bottomNavHeight: 80.0,
    );
  }

  // Android Native Small Screen Layout Configuration
  LayoutConfiguration _getAndroidNativeConfig(DeviceType deviceType) {
    return LayoutConfiguration(
      layoutSystem: LayoutSystem.androidNativeSmall,
      breakpoints: const ResponsiveBreakpoints(
        mobile: 360,
        tablet: 600,
        desktop: 840,
        largeDesktop: 1024,
      ),
      touchTargets: const TouchTargetConfig(
        minimumSize: 48.0,
        preferredSize: 64.0, // Larger for small screens
        spacing: 12.0,
        padding: EdgeInsets.all(12.0),
        enableHapticFeedback: true,
      ),
      enableSidebarCollapse: true,
      showBottomNavigation: true,
      enableSwipeGestures: true,
      sidebarWidth: 280.0,
      bottomNavHeight: 88.0, // Larger for easier touch
    );
  }

  // Check if current screen size requires layout adaptation
  bool shouldAdaptLayout(Size currentSize, LayoutConfiguration config) {
    final deviceType = getDeviceType(currentSize);
    final recommendedLayout = getRecommendedLayout(deviceType);
    return config.layoutSystem != recommendedLayout;
  }

  // Get adaptive margins based on screen size
  EdgeInsets getAdaptiveMargins(Size screenSize, LayoutConfiguration config) {
    final deviceType = getDeviceType(screenSize);

    switch (deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.all(8.0);
      case DeviceType.tablet:
        return const EdgeInsets.all(16.0);
      case DeviceType.desktop:
        return const EdgeInsets.all(24.0);
      case DeviceType.largeDesktop:
        return const EdgeInsets.all(32.0);
    }
  }

  // Get adaptive font scaling
  double getAdaptiveFontScale(Size screenSize, LayoutConfiguration config) {
    final deviceType = getDeviceType(screenSize);

    switch (config.layoutSystem) {
      case LayoutSystem.desktopOptimized:
        return deviceType == DeviceType.mobile ? 0.9 : 1.0;
      case LayoutSystem.materialDesignMobile:
        return 1.0;
      case LayoutSystem.androidNativeSmall:
        return deviceType == DeviceType.mobile ? 1.1 : 1.0;
    }
  }
}

// Layout State Notifier
class LayoutNotifier extends StateNotifier<LayoutConfiguration> {
  LayoutNotifier() : super(const LayoutConfiguration());

  void updateLayout(LayoutConfiguration newConfig) {
    state = newConfig;
  }

  void updateLayoutSystem(LayoutSystem layoutSystem) {
    final layoutService = LayoutService();
    final deviceType = layoutService.getDeviceType(
      const Size(1024, 768),
    ); // Default
    final newConfig = layoutService.getLayoutConfig(layoutSystem, deviceType);
    state = newConfig;
  }

  void toggleSidebarCollapse() {
    state = LayoutConfiguration(
      layoutSystem: state.layoutSystem,
      breakpoints: state.breakpoints,
      touchTargets: state.touchTargets,
      enableSidebarCollapse: !state.enableSidebarCollapse,
      showBottomNavigation: state.showBottomNavigation,
      enableSwipeGestures: state.enableSwipeGestures,
      sidebarWidth: state.sidebarWidth,
      bottomNavHeight: state.bottomNavHeight,
    );
  }

  void toggleBottomNavigation() {
    state = LayoutConfiguration(
      layoutSystem: state.layoutSystem,
      breakpoints: state.breakpoints,
      touchTargets: state.touchTargets,
      enableSidebarCollapse: state.enableSidebarCollapse,
      showBottomNavigation: !state.showBottomNavigation,
      enableSwipeGestures: state.enableSwipeGestures,
      sidebarWidth: state.sidebarWidth,
      bottomNavHeight: state.bottomNavHeight,
    );
  }

  void updateSidebarWidth(double width) {
    state = LayoutConfiguration(
      layoutSystem: state.layoutSystem,
      breakpoints: state.breakpoints,
      touchTargets: state.touchTargets,
      enableSidebarCollapse: state.enableSidebarCollapse,
      showBottomNavigation: state.showBottomNavigation,
      enableSwipeGestures: state.enableSwipeGestures,
      sidebarWidth: width,
      bottomNavHeight: state.bottomNavHeight,
    );
  }

  void updateTouchTargets(TouchTargetConfig touchTargets) {
    state = LayoutConfiguration(
      layoutSystem: state.layoutSystem,
      breakpoints: state.breakpoints,
      touchTargets: touchTargets,
      enableSidebarCollapse: state.enableSidebarCollapse,
      showBottomNavigation: state.showBottomNavigation,
      enableSwipeGestures: state.enableSwipeGestures,
      sidebarWidth: state.sidebarWidth,
      bottomNavHeight: state.bottomNavHeight,
    );
  }

  void toggleHapticFeedback() {
    final newTouchTargets = TouchTargetConfig(
      minimumSize: state.touchTargets.minimumSize,
      preferredSize: state.touchTargets.preferredSize,
      spacing: state.touchTargets.spacing,
      padding: state.touchTargets.padding,
      enableHapticFeedback: !state.touchTargets.enableHapticFeedback,
    );
    updateTouchTargets(newTouchTargets);
  }

  void updateBreakpoints(ResponsiveBreakpoints breakpoints) {
    state = LayoutConfiguration(
      layoutSystem: state.layoutSystem,
      breakpoints: breakpoints,
      touchTargets: state.touchTargets,
      enableSidebarCollapse: state.enableSidebarCollapse,
      showBottomNavigation: state.showBottomNavigation,
      enableSwipeGestures: state.enableSwipeGestures,
      sidebarWidth: state.sidebarWidth,
      bottomNavHeight: state.bottomNavHeight,
    );
  }
}

// Device Type Enum
enum DeviceType { mobile, tablet, desktop, largeDesktop }
