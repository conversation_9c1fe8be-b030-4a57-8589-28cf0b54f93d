﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\projects\t2 - Copy\shadow_suite\windows\flutter\ephemeral\.plugin_symlinks\record_windows\windows\record_windows_plugin_c_api.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\projects\t2 - Copy\shadow_suite\windows\flutter\ephemeral\.plugin_symlinks\record_windows\windows\record_windows_plugin.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\projects\t2 - Copy\shadow_suite\windows\flutter\ephemeral\.plugin_symlinks\record_windows\windows\record.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\projects\t2 - Copy\shadow_suite\windows\flutter\ephemeral\.plugin_symlinks\record_windows\windows\record_readercallback.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\projects\t2 - Copy\shadow_suite\windows\flutter\ephemeral\.plugin_symlinks\record_windows\windows\record_iunknown.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\projects\t2 - Copy\shadow_suite\windows\flutter\ephemeral\.plugin_symlinks\record_windows\windows\record_mediatype.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\projects\t2 - Copy\shadow_suite\windows\flutter\ephemeral\.plugin_symlinks\record_windows\windows\include\record_windows\record_windows_plugin_c_api.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\projects\t2 - Copy\shadow_suite\windows\flutter\ephemeral\.plugin_symlinks\record_windows\windows\record_windows_plugin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\projects\t2 - Copy\shadow_suite\windows\flutter\ephemeral\.plugin_symlinks\record_windows\windows\record_config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\projects\t2 - Copy\shadow_suite\windows\flutter\ephemeral\.plugin_symlinks\record_windows\windows\record.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\projects\t2 - Copy\shadow_suite\windows\flutter\ephemeral\.plugin_symlinks\record_windows\windows\utils.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\projects\t2 - Copy\shadow_suite\windows\flutter\ephemeral\.plugin_symlinks\record_windows\windows\event_stream_handler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\projects\t2 - Copy\shadow_suite\windows\flutter\ephemeral\.plugin_symlinks\record_windows\windows\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{C454D3DF-919A-37A6-944D-7BBCEA7907D6}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{63AF7B70-3ABA-33F7-BD09-2C55F7373CD1}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
