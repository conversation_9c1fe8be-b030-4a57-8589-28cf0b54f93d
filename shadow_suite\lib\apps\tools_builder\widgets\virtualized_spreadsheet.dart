import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/app_theme.dart';
import '../models/spreadsheet.dart';
import '../models/spreadsheet_cell.dart';
import '../models/tool.dart';
import '../services/tools_providers.dart';

// High-performance virtualized spreadsheet for large datasets
class VirtualizedSpreadsheet extends ConsumerStatefulWidget {
  final Spreadsheet spreadsheet;
  final Function(String, String)? onCellChanged;
  final Function(String)? onCellSelected;

  const VirtualizedSpreadsheet({
    super.key,
    required this.spreadsheet,
    this.onCellChanged,
    this.onCellSelected,
  });

  @override
  ConsumerState<VirtualizedSpreadsheet> createState() =>
      _VirtualizedSpreadsheetState();
}

class _VirtualizedSpreadsheetState
    extends ConsumerState<VirtualizedSpreadsheet> {
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();

  // Viewport configuration
  static const double cellWidth = 100.0;
  static const double cellHeight = 30.0;
  static const double headerHeight = 35.0;
  static const double headerWidth = 60.0;

  // Visible range
  int _startRow = 0;
  int _endRow = 50;
  int _startCol = 0;
  int _endCol = 20;

  // Cache for rendered cells
  final Map<String, Widget> _cellCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration cacheExpiry = Duration(minutes: 5);

  @override
  void initState() {
    super.initState();
    _horizontalController.addListener(_onHorizontalScroll);
    _verticalController.addListener(_onVerticalScroll);

    // Preload initial viewport
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateVisibleRange();
    });
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    super.dispose();
  }

  void _onHorizontalScroll() {
    _updateVisibleRange();
  }

  void _onVerticalScroll() {
    _updateVisibleRange();
  }

  void _updateVisibleRange() {
    final viewportWidth = MediaQuery.of(context).size.width - headerWidth;
    final viewportHeight =
        MediaQuery.of(context).size.height -
        headerHeight -
        200; // Account for other UI

    final newStartCol = (_horizontalController.offset / cellWidth).floor();
    final newEndCol =
        newStartCol + (viewportWidth / cellWidth).ceil() + 5; // Buffer

    final newStartRow = (_verticalController.offset / cellHeight).floor();
    final newEndRow =
        newStartRow + (viewportHeight / cellHeight).ceil() + 10; // Buffer

    if (newStartRow != _startRow ||
        newEndRow != _endRow ||
        newStartCol != _startCol ||
        newEndCol != _endCol) {
      setState(() {
        _startRow = newStartRow;
        _endRow = newEndRow.clamp(0, 1000); // Max 1000 rows for performance
        _startCol = newStartCol;
        _endCol = newEndCol.clamp(0, 50); // Max 50 columns
      });

      // Clean old cache entries
      _cleanCache();
    }
  }

  void _cleanCache() {
    final now = DateTime.now();
    final keysToRemove = <String>[];

    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) > cacheExpiry) {
        keysToRemove.add(entry.key);
      }
    }

    for (final key in keysToRemove) {
      _cellCache.remove(key);
      _cacheTimestamps.remove(key);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          _buildColumnHeaders(),
          Expanded(
            child: Row(
              children: [
                _buildRowHeaders(),
                Expanded(child: _buildCellGrid()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColumnHeaders() {
    return SizedBox(
      height: headerHeight,
      child: Row(
        children: [
          // Corner cell
          Container(
            width: headerWidth,
            height: headerHeight,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              border: Border.all(color: Colors.grey[300]!),
            ),
          ),
          // Column headers
          Expanded(
            child: SingleChildScrollView(
              controller: _horizontalController,
              scrollDirection: Axis.horizontal,
              child: Row(
                children: List.generate(
                  _endCol - _startCol,
                  (index) => _buildColumnHeader(_startCol + index),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColumnHeader(int colIndex) {
    final columnName = _numberToColumn(colIndex + 1);

    return Container(
      width: cellWidth,
      height: headerHeight,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Center(
        child: Text(
          columnName,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
        ),
      ),
    );
  }

  Widget _buildRowHeaders() {
    return SizedBox(
      width: headerWidth,
      child: SingleChildScrollView(
        controller: _verticalController,
        child: Column(
          children: List.generate(
            _endRow - _startRow,
            (index) => _buildRowHeader(_startRow + index),
          ),
        ),
      ),
    );
  }

  Widget _buildRowHeader(int rowIndex) {
    return Container(
      width: headerWidth,
      height: cellHeight,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Center(
        child: Text(
          '${rowIndex + 1}',
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
        ),
      ),
    );
  }

  Widget _buildCellGrid() {
    return SingleChildScrollView(
      controller: _horizontalController,
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        controller: _verticalController,
        child: SizedBox(
          width: (_endCol - _startCol) * cellWidth,
          height: (_endRow - _startRow) * cellHeight,
          child: Stack(children: _buildVisibleCells()),
        ),
      ),
    );
  }

  List<Widget> _buildVisibleCells() {
    final cells = <Widget>[];

    for (int row = _startRow; row < _endRow; row++) {
      for (int col = _startCol; col < _endCol; col++) {
        final cellAddress = '${_numberToColumn(col + 1)}${row + 1}';
        final cell = _getCachedCell(cellAddress, row, col);
        cells.add(cell);
      }
    }

    return cells;
  }

  Widget _getCachedCell(String cellAddress, int row, int col) {
    final cacheKey = '$cellAddress-${widget.spreadsheet.activeSheet?.name}';

    // Check cache first
    if (_cellCache.containsKey(cacheKey)) {
      final timestamp = _cacheTimestamps[cacheKey];
      if (timestamp != null &&
          DateTime.now().difference(timestamp) < cacheExpiry) {
        return _cellCache[cacheKey]!;
      }
    }

    // Build new cell
    final cell = _buildCell(cellAddress, row, col);

    // Cache it
    _cellCache[cacheKey] = cell;
    _cacheTimestamps[cacheKey] = DateTime.now();

    return cell;
  }

  Widget _buildCell(String cellAddress, int row, int col) {
    return Positioned(
      left: (col - _startCol) * cellWidth,
      top: (row - _startRow) * cellHeight,
      child: Consumer(
        builder: (context, ref, child) {
          final selectedCell = ref.watch(selectedCellProvider);
          final isSelected = selectedCell == cellAddress;

          final activeSheet = widget.spreadsheet.activeSheet;
          final cellData = activeSheet?.cells[cellAddress];

          return GestureDetector(
            onTap: () {
              try {
                ref.read(selectedCellProvider.notifier).state =
                    CellPosition.fromAddress(cellAddress);
              } catch (e) {
                // Invalid cell address, ignore
              }
              widget.onCellSelected?.call(cellAddress);
            },
            child: Container(
              width: cellWidth,
              height: cellHeight,
              decoration: BoxDecoration(
                color: isSelected
                    ? AppTheme.toolsBuilderColor.withValues(alpha: 0.1)
                    : Colors.white,
                border: Border.all(
                  color: isSelected
                      ? AppTheme.toolsBuilderColor
                      : Colors.grey[300]!,
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: _buildCellContent(cellData, cellAddress),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCellContent(SpreadsheetCell? cellData, String cellAddress) {
    if (cellData == null) {
      return const SizedBox.shrink();
    }

    final displayValue =
        cellData.calculatedValue?.toString() ?? cellData.rawValue;

    Color? textColor;
    FontWeight? fontWeight;
    FontStyle? fontStyle;

    // Apply formatting
    if (cellData.format.isBold) fontWeight = FontWeight.bold;
    if (cellData.format.isItalic) fontStyle = FontStyle.italic;
    if (cellData.format.textColor != null) {
      try {
        textColor = Color(
          int.parse(cellData.format.textColor!.replaceFirst('#', '0xFF')),
        );
      } catch (e) {
        textColor = Colors.black;
      }
    }

    // Handle different data types
    Widget content;
    switch (cellData.dataType) {
      case CellDataType.error:
        content = Text(
          displayValue,
          style: TextStyle(
            color: Colors.red,
            fontWeight: fontWeight,
            fontStyle: fontStyle,
            fontSize: 12,
          ),
          overflow: TextOverflow.ellipsis,
        );
        break;
      case CellDataType.number:
        content = Text(
          displayValue,
          style: TextStyle(
            color: textColor ?? Colors.black,
            fontWeight: fontWeight,
            fontStyle: fontStyle,
            fontSize: 12,
          ),
          textAlign: TextAlign.right,
          overflow: TextOverflow.ellipsis,
        );
        break;
      default:
        content = Text(
          displayValue,
          style: TextStyle(
            color: textColor ?? Colors.black,
            fontWeight: fontWeight,
            fontStyle: fontStyle,
            fontSize: 12,
          ),
          overflow: TextOverflow.ellipsis,
        );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      child: Align(
        alignment: cellData.dataType == CellDataType.number
            ? Alignment.centerRight
            : Alignment.centerLeft,
        child: content,
      ),
    );
  }

  String _numberToColumn(int number) {
    String result = '';
    while (number > 0) {
      number--;
      result = String.fromCharCode('A'.codeUnitAt(0) + (number % 26)) + result;
      number ~/= 26;
    }
    return result;
  }
}

// Performance monitoring widget
class SpreadsheetPerformanceMonitor extends ConsumerWidget {
  const SpreadsheetPerformanceMonitor({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Consumer(
      builder: (context, ref, child) {
        final performanceMetrics = ref.watch(spreadsheetPerformanceProvider);

        return Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(4),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.speed, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Text(
                'Render: ${performanceMetrics.renderTime}ms',
                style: TextStyle(fontSize: 10, color: Colors.grey[600]),
              ),
              const SizedBox(width: 8),
              Text(
                'Calc: ${performanceMetrics.calculationTime}ms',
                style: TextStyle(fontSize: 10, color: Colors.grey[600]),
              ),
              const SizedBox(width: 8),
              Text(
                'Cells: ${performanceMetrics.visibleCells}',
                style: TextStyle(fontSize: 10, color: Colors.grey[600]),
              ),
            ],
          ),
        );
      },
    );
  }
}

// Performance metrics
class SpreadsheetPerformanceMetrics {
  final int renderTime;
  final int calculationTime;
  final int visibleCells;
  final int cachedCells;
  final double memoryUsage;

  const SpreadsheetPerformanceMetrics({
    required this.renderTime,
    required this.calculationTime,
    required this.visibleCells,
    required this.cachedCells,
    required this.memoryUsage,
  });
}

// Performance provider
final spreadsheetPerformanceProvider =
    StateProvider<SpreadsheetPerformanceMetrics>((ref) {
      return const SpreadsheetPerformanceMetrics(
        renderTime: 0,
        calculationTime: 0,
        visibleCells: 0,
        cachedCells: 0,
        memoryUsage: 0.0,
      );
    });

// Lazy loading provider for templates
final lazyTemplateProvider = FutureProvider.family<List<ToolTemplate>, int>((
  ref,
  page,
) async {
  // Simulate lazy loading with pagination
  await Future.delayed(const Duration(milliseconds: 200));

  final startIndex = page * 10;
  final templates = <ToolTemplate>[];

  for (int i = startIndex; i < startIndex + 10; i++) {
    templates.add(
      ToolTemplate(
        id: 'template_$i',
        name: 'Template ${i + 1}',
        description: 'Description for template ${i + 1}',
        category: ToolCategory.business,
        type: ToolType.custom,
        components: [],
        tags: ['tag${i % 3}', 'category${i % 5}'],
        isPremium: i % 4 == 0,
      ),
    );
  }

  return templates;
});

// Cache provider for frequently used formulas
final formulaCacheProvider = StateProvider<Map<String, dynamic>>((ref) => {});

class FormulaCacheNotifier extends StateNotifier<Map<String, dynamic>> {
  FormulaCacheNotifier() : super({});

  void cacheResult(String formula, dynamic result) {
    state = {...state, formula: result};

    // Limit cache size
    if (state.length > 1000) {
      final keys = state.keys.toList();
      for (int i = 0; i < 100; i++) {
        state.remove(keys[i]);
      }
    }
  }

  dynamic getCachedResult(String formula) {
    return state[formula];
  }

  void clearCache() {
    state = {};
  }
}
