D:\projects\t2 - Copy\shadow_suite\windows\flutter\ephemeral\.plugin_symlinks\record_windows\windows\record_windows_plugin_c_api.cpp;D:\projects\t2 - Copy\shadow_suite\build\windows\x64\plugins\record_windows\record_windows_plugin.dir\Debug\record_windows_plugin_c_api.obj
D:\projects\t2 - Copy\shadow_suite\windows\flutter\ephemeral\.plugin_symlinks\record_windows\windows\record_windows_plugin.cpp;D:\projects\t2 - Copy\shadow_suite\build\windows\x64\plugins\record_windows\record_windows_plugin.dir\Debug\record_windows_plugin.obj
D:\projects\t2 - Copy\shadow_suite\windows\flutter\ephemeral\.plugin_symlinks\record_windows\windows\record.cpp;D:\projects\t2 - Copy\shadow_suite\build\windows\x64\plugins\record_windows\record_windows_plugin.dir\Debug\record.obj
D:\projects\t2 - Copy\shadow_suite\windows\flutter\ephemeral\.plugin_symlinks\record_windows\windows\record_readercallback.cpp;D:\projects\t2 - Copy\shadow_suite\build\windows\x64\plugins\record_windows\record_windows_plugin.dir\Debug\record_readercallback.obj
D:\projects\t2 - Copy\shadow_suite\windows\flutter\ephemeral\.plugin_symlinks\record_windows\windows\record_iunknown.cpp;D:\projects\t2 - Copy\shadow_suite\build\windows\x64\plugins\record_windows\record_windows_plugin.dir\Debug\record_iunknown.obj
D:\projects\t2 - Copy\shadow_suite\windows\flutter\ephemeral\.plugin_symlinks\record_windows\windows\record_mediatype.cpp;D:\projects\t2 - Copy\shadow_suite\build\windows\x64\plugins\record_windows\record_windows_plugin.dir\Debug\record_mediatype.obj
