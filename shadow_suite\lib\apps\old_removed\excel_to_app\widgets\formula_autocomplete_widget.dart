import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/excel_formula_engine.dart';
import '../services/excel_app_providers.dart';

class FormulaAutocompleteWidget extends ConsumerStatefulWidget {
  final TextEditingController controller;
  final Function(String) onSuggestionSelected;
  final VoidCallback? onDismiss;

  const FormulaAutocompleteWidget({
    super.key,
    required this.controller,
    required this.onSuggestionSelected,
    this.onDismiss,
  });

  @override
  ConsumerState<FormulaAutocompleteWidget> createState() => _FormulaAutocompleteWidgetState();
}

class _FormulaAutocompleteWidgetState extends ConsumerState<FormulaAutocompleteWidget> {
  List<String> _suggestions = [];
  // String _currentInput = ''; // Reserved for future use
  bool _isVisible = false;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    super.dispose();
  }

  void _onTextChanged() {
    final text = widget.controller.text;
    final cursorPosition = widget.controller.selection.baseOffset;
    
    if (cursorPosition < 0) return;
    
    // Find the current function being typed
    final beforeCursor = text.substring(0, cursorPosition);
    final functionMatch = RegExp(r'([A-Z]+)$').firstMatch(beforeCursor);
    
    if (functionMatch != null && beforeCursor.contains('=')) {
      final partial = functionMatch.group(1)!;
      if (partial.isNotEmpty) {
        final formulaEngine = ref.read(excelFormulaEngineProvider);
        final suggestions = formulaEngine.getFunctionSuggestions(partial);
        
        setState(() {
          _suggestions = suggestions;
          // _currentInput = partial; // Reserved for future use
          _isVisible = suggestions.isNotEmpty;
        });
        return;
      }
    }
    
    setState(() {
      _isVisible = false;
      _suggestions = [];
    });
  }

  void _insertSuggestion(String suggestion) {
    final text = widget.controller.text;
    final cursorPosition = widget.controller.selection.baseOffset;
    
    if (cursorPosition < 0) return;
    
    final beforeCursor = text.substring(0, cursorPosition);
    final afterCursor = text.substring(cursorPosition);
    
    // Replace the partial function name with the complete one
    final newBeforeCursor = beforeCursor.replaceAll(RegExp(r'([A-Z]+)$'), '$suggestion(');
    final newText = newBeforeCursor + afterCursor;
    
    widget.controller.text = newText;
    widget.controller.selection = TextSelection.collapsed(offset: newBeforeCursor.length);
    
    widget.onSuggestionSelected(suggestion);
    
    setState(() {
      _isVisible = false;
    });
  }

  String _getFunctionCategory(String functionName) {
    for (final entry in ExcelFormulaEngine.excelFunctionsByCategory.entries) {
      if (entry.value.contains(functionName)) {
        return entry.key;
      }
    }
    return 'Other';
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Math & Trig':
        return const Color(0xFF3498DB);
      case 'Statistical':
        return const Color(0xFF9B59B6);
      case 'Logical':
        return const Color(0xFF27AE60);
      case 'Text':
        return const Color(0xFFE67E22);
      case 'Date & Time':
        return const Color(0xFFE74C3C);
      case 'Lookup & Reference':
        return const Color(0xFF34495E);
      case 'Information':
        return const Color(0xFF95A5A6);
      default:
        return const Color(0xFF7F8C8D);
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'Math & Trig':
        return Icons.calculate;
      case 'Statistical':
        return Icons.analytics;
      case 'Logical':
        return Icons.psychology;
      case 'Text':
        return Icons.text_fields;
      case 'Date & Time':
        return Icons.access_time;
      case 'Lookup & Reference':
        return Icons.search;
      case 'Information':
        return Icons.info;
      default:
        return Icons.functions;
    }
  }

  String _getFunctionDescription(String functionName) {
    switch (functionName) {
      case 'SUM':
        return 'Adds all numbers in a range of cells';
      case 'AVERAGE':
        return 'Returns the average of numbers';
      case 'COUNT':
        return 'Counts the number of cells that contain numbers';
      case 'IF':
        return 'Returns one value if condition is true, another if false';
      case 'VLOOKUP':
        return 'Looks up a value in the first column and returns a value in the same row';
      case 'CONCATENATE':
        return 'Joins several text strings into one text string';
      case 'TODAY':
        return 'Returns the current date';
      case 'NOW':
        return 'Returns the current date and time';
      case 'ROUND':
        return 'Rounds a number to a specified number of digits';
      case 'MAX':
        return 'Returns the largest value in a set of values';
      case 'MIN':
        return 'Returns the smallest value in a set of values';
      case 'ABS':
        return 'Returns the absolute value of a number';
      case 'SQRT':
        return 'Returns the square root of a number';
      case 'POWER':
        return 'Returns the result of a number raised to a power';
      case 'AND':
        return 'Returns TRUE if all arguments are TRUE';
      case 'OR':
        return 'Returns TRUE if any argument is TRUE';
      case 'NOT':
        return 'Reverses the logic of its argument';
      case 'LEN':
        return 'Returns the number of characters in a text string';
      case 'LEFT':
        return 'Returns the leftmost characters from a text value';
      case 'RIGHT':
        return 'Returns the rightmost characters from a text value';
      case 'MID':
        return 'Returns a specific number of characters from a text string';
      case 'UPPER':
        return 'Converts text to uppercase';
      case 'LOWER':
        return 'Converts text to lowercase';
      case 'TRIM':
        return 'Removes spaces from text';
      default:
        return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isVisible || _suggestions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      constraints: const BoxConstraints(maxHeight: 300),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF2C3E50),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.functions, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Excel Functions',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const Spacer(),
                if (widget.onDismiss != null)
                  GestureDetector(
                    onTap: widget.onDismiss,
                    child: const Icon(Icons.close, color: Colors.white, size: 18),
                  ),
              ],
            ),
          ),
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              padding: const EdgeInsets.all(8),
              itemCount: _suggestions.length,
              itemBuilder: (context, index) {
                return _buildSuggestionItem(_suggestions[index]);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestionItem(String suggestion) {
    final category = _getFunctionCategory(suggestion);
    final formulaEngine = ref.read(excelFormulaEngineProvider);
    final syntax = formulaEngine.getFunctionSyntax(suggestion);
    final description = _getFunctionDescription(suggestion);
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: ListTile(
        dense: true,
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: _getCategoryColor(category),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            _getCategoryIcon(category),
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Row(
          children: [
            Text(
              suggestion,
              style: const TextStyle(
                fontFamily: 'monospace',
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: _getCategoryColor(category).withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                category,
                style: TextStyle(
                  fontSize: 10,
                  color: _getCategoryColor(category),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              syntax,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontFamily: 'monospace',
              ),
            ),
            if (description.isNotEmpty) ...[
              const SizedBox(height: 2),
              Text(
                description,
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[500],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
        onTap: () => _insertSuggestion(suggestion),
      ),
    );
  }
}
