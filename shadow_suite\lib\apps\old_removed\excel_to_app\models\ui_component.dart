import 'package:flutter/material.dart';

enum UIComponentType {
  text,
  button,
  textField,
  image,
  chart,
  table,
  container,
  list,
  card,
  slider,
  switch_,
  checkbox,
  radio,
  dropdown,
  progressBar,
  divider,
}

enum ResizeDirection {
  topLeft,
  topRight,
  bottomLeft,
  bottomRight,
  top,
  bottom,
  left,
  right,
}

class UIComponent {
  final String id;
  final UIComponentType type;
  final Offset position;
  final Size size;
  final Map<String, dynamic> properties;
  final String? cellBinding;
  final bool isVisible;
  final bool isLocked;

  const UIComponent({
    required this.id,
    required this.type,
    required this.position,
    required this.size,
    required this.properties,
    this.cellBinding,
    this.isVisible = true,
    this.isLocked = false,
  });

  UIComponent copyWith({
    String? id,
    UIComponentType? type,
    Offset? position,
    Size? size,
    Map<String, dynamic>? properties,
    String? cellBinding,
    bool? isVisible,
    bool? isLocked,
  }) {
    return UIComponent(
      id: id ?? this.id,
      type: type ?? this.type,
      position: position ?? this.position,
      size: size ?? this.size,
      properties: properties ?? this.properties,
      cellBinding: cellBinding ?? this.cellBinding,
      isVisible: isVisible ?? this.isVisible,
      isLocked: isLocked ?? this.isLocked,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString(),
      'position': {'dx': position.dx, 'dy': position.dy},
      'size': {'width': size.width, 'height': size.height},
      'properties': properties,
      'cellBinding': cellBinding,
      'isVisible': isVisible,
      'isLocked': isLocked,
    };
  }

  factory UIComponent.fromJson(Map<String, dynamic> json) {
    return UIComponent(
      id: json['id'] as String,
      type: UIComponentType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => UIComponentType.text,
      ),
      position: Offset(
        (json['position']['dx'] as num).toDouble(),
        (json['position']['dy'] as num).toDouble(),
      ),
      size: Size(
        (json['size']['width'] as num).toDouble(),
        (json['size']['height'] as num).toDouble(),
      ),
      properties: Map<String, dynamic>.from(json['properties'] as Map),
      cellBinding: json['cellBinding'] as String?,
      isVisible: json['isVisible'] as bool? ?? true,
      isLocked: json['isLocked'] as bool? ?? false,
    );
  }
}
