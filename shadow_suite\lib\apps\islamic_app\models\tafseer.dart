import 'package:uuid/uuid.dart';

// Tafseer (Quran Commentary) Model
class Tafseer {
  final String id;
  final int surahNumber;
  final int verseNumber;
  final String text;
  final String source;
  final String language;
  final bool isCustom;
  final bool isFavorite;
  final bool isRead;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? readAt;

  Tafseer({
    String? id,
    required this.surahNumber,
    required this.verseNumber,
    required this.text,
    required this.source,
    this.language = 'en',
    this.isCustom = false,
    this.isFavorite = false,
    this.isRead = false,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.readAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Tafseer copyWith({
    int? surahNumber,
    int? verseNumber,
    String? text,
    String? source,
    String? language,
    bool? isCustom,
    bool? isFavorite,
    bool? isRead,
    DateTime? updatedAt,
    DateTime? readAt,
  }) {
    return Tafseer(
      id: id,
      surahNumber: surahNumber ?? this.surahNumber,
      verseNumber: verseNumber ?? this.verseNumber,
      text: text ?? this.text,
      source: source ?? this.source,
      language: language ?? this.language,
      isCustom: isCustom ?? this.isCustom,
      isFavorite: isFavorite ?? this.isFavorite,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      readAt: readAt ?? this.readAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'surahNumber': surahNumber,
      'verseNumber': verseNumber,
      'text': text,
      'source': source,
      'language': language,
      'isCustom': isCustom ? 1 : 0,
      'isFavorite': isFavorite ? 1 : 0,
      'isRead': isRead ? 1 : 0,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'readAt': readAt?.millisecondsSinceEpoch,
    };
  }

  factory Tafseer.fromMap(Map<String, dynamic> map) {
    return Tafseer(
      id: map['id'],
      surahNumber: map['surahNumber'],
      verseNumber: map['verseNumber'],
      text: map['text'],
      source: map['source'],
      language: map['language'] ?? 'en',
      isCustom: map['isCustom'] == 1,
      isFavorite: map['isFavorite'] == 1,
      isRead: map['isRead'] == 1,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
      readAt: map['readAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['readAt'])
          : null,
    );
  }

  // Get verse reference as string
  String get verseReference => '$surahNumber:$verseNumber';

  // Get formatted source
  String get formattedSource {
    if (isCustom) {
      return '$source (Custom)';
    }
    return source;
  }

  // Get text preview (first 100 characters)
  String get textPreview {
    if (text.length <= 100) return text;
    return '${text.substring(0, 97)}...';
  }

  // Check if tafseer is recent (within last 7 days)
  bool get isRecent {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    return difference.inDays <= 7;
  }

  // Get reading status
  String get readingStatus {
    if (isRead) {
      if (readAt != null) {
        final now = DateTime.now();
        final difference = now.difference(readAt!);
        
        if (difference.inDays == 0) {
          return 'Read today';
        } else if (difference.inDays == 1) {
          return 'Read yesterday';
        } else if (difference.inDays <= 7) {
          return 'Read ${difference.inDays} days ago';
        } else {
          return 'Read on ${readAt!.day}/${readAt!.month}/${readAt!.year}';
        }
      }
      return 'Read';
    }
    return 'Unread';
  }

  // Get word count
  int get wordCount => text.split(' ').length;

  // Get estimated reading time in minutes
  int get estimatedReadingTime {
    const wordsPerMinute = 200; // Average reading speed
    return (wordCount / wordsPerMinute).ceil();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Tafseer && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Tafseer(id: $id, verse: $verseReference, source: $source, isCustom: $isCustom)';
  }
}

// Tafseer Search Result
class TafseerSearchResult {
  final Tafseer tafseer;
  final List<String> matchedTerms;
  final double relevanceScore;

  TafseerSearchResult({
    required this.tafseer,
    required this.matchedTerms,
    required this.relevanceScore,
  });

  // Get highlighted text with matched terms
  String getHighlightedText(String highlightStart, String highlightEnd) {
    String highlightedText = tafseer.text;
    
    for (final term in matchedTerms) {
      final regex = RegExp(term, caseSensitive: false);
      highlightedText = highlightedText.replaceAllMapped(
        regex,
        (match) => '$highlightStart${match.group(0)}$highlightEnd',
      );
    }
    
    return highlightedText;
  }
}

// Tafseer Statistics
class TafseerStatistics {
  final int totalTafseer;
  final int defaultTafseer;
  final int customTafseer;
  final int readTafseer;
  final int favoriteTafseer;
  final Map<String, int> sourceDistribution;
  final Map<String, int> languageDistribution;
  final double readingProgress;

  TafseerStatistics({
    required this.totalTafseer,
    required this.defaultTafseer,
    required this.customTafseer,
    required this.readTafseer,
    required this.favoriteTafseer,
    required this.sourceDistribution,
    required this.languageDistribution,
    required this.readingProgress,
  });

  factory TafseerStatistics.fromTafseerList(List<Tafseer> tafseerList) {
    final total = tafseerList.length;
    final defaultCount = tafseerList.where((t) => !t.isCustom).length;
    final customCount = tafseerList.where((t) => t.isCustom).length;
    final readCount = tafseerList.where((t) => t.isRead).length;
    final favoriteCount = tafseerList.where((t) => t.isFavorite).length;

    final sourceDistribution = <String, int>{};
    final languageDistribution = <String, int>{};

    for (final tafseer in tafseerList) {
      sourceDistribution[tafseer.source] = (sourceDistribution[tafseer.source] ?? 0) + 1;
      languageDistribution[tafseer.language] = (languageDistribution[tafseer.language] ?? 0) + 1;
    }

    final readingProgress = total > 0 ? (readCount / total) * 100 : 0.0;

    return TafseerStatistics(
      totalTafseer: total,
      defaultTafseer: defaultCount,
      customTafseer: customCount,
      readTafseer: readCount,
      favoriteTafseer: favoriteCount,
      sourceDistribution: sourceDistribution,
      languageDistribution: languageDistribution,
      readingProgress: readingProgress,
    );
  }

  // Get most popular source
  String get mostPopularSource {
    if (sourceDistribution.isEmpty) return 'None';
    
    return sourceDistribution.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  // Get completion percentage
  String get completionPercentage => '${readingProgress.toStringAsFixed(1)}%';
}
