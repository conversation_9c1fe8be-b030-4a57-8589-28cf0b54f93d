import 'package:uuid/uuid.dart';

enum CellDataType {
  text,
  number,
  formula,
  date,
  boolean,
  error,
}

enum CellAlignment {
  left,
  center,
  right,
  justify,
}

enum CellVerticalAlignment {
  top,
  middle,
  bottom,
}

class CellFormat {
  final String? fontFamily;
  final double? fontSize;
  final bool isBold;
  final bool isItalic;
  final bool isUnderline;
  final bool isStrikethrough;
  final String? textColor;
  final String? backgroundColor;
  final CellAlignment alignment;
  final CellVerticalAlignment verticalAlignment;
  final String? numberFormat;
  final bool wrapText;
  final int? indentLevel;
  final double? rotation;
  final BorderStyle? topBorder;
  final BorderStyle? bottomBorder;
  final BorderStyle? leftBorder;
  final BorderStyle? rightBorder;

  const CellFormat({
    this.fontFamily,
    this.fontSize,
    this.isBold = false,
    this.isItalic = false,
    this.isUnderline = false,
    this.isStrikethrough = false,
    this.textColor,
    this.backgroundColor,
    this.alignment = CellAlignment.left,
    this.verticalAlignment = CellVerticalAlignment.middle,
    this.numberFormat,
    this.wrapText = false,
    this.indentLevel,
    this.rotation,
    this.topBorder,
    this.bottomBorder,
    this.leftBorder,
    this.rightBorder,
  });

  CellFormat copyWith({
    String? fontFamily,
    double? fontSize,
    bool? isBold,
    bool? isItalic,
    bool? isUnderline,
    bool? isStrikethrough,
    String? textColor,
    String? backgroundColor,
    CellAlignment? alignment,
    CellVerticalAlignment? verticalAlignment,
    String? numberFormat,
    bool? wrapText,
    int? indentLevel,
    double? rotation,
    BorderStyle? topBorder,
    BorderStyle? bottomBorder,
    BorderStyle? leftBorder,
    BorderStyle? rightBorder,
  }) {
    return CellFormat(
      fontFamily: fontFamily ?? this.fontFamily,
      fontSize: fontSize ?? this.fontSize,
      isBold: isBold ?? this.isBold,
      isItalic: isItalic ?? this.isItalic,
      isUnderline: isUnderline ?? this.isUnderline,
      isStrikethrough: isStrikethrough ?? this.isStrikethrough,
      textColor: textColor ?? this.textColor,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      alignment: alignment ?? this.alignment,
      verticalAlignment: verticalAlignment ?? this.verticalAlignment,
      numberFormat: numberFormat ?? this.numberFormat,
      wrapText: wrapText ?? this.wrapText,
      indentLevel: indentLevel ?? this.indentLevel,
      rotation: rotation ?? this.rotation,
      topBorder: topBorder ?? this.topBorder,
      bottomBorder: bottomBorder ?? this.bottomBorder,
      leftBorder: leftBorder ?? this.leftBorder,
      rightBorder: rightBorder ?? this.rightBorder,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'fontFamily': fontFamily,
      'fontSize': fontSize,
      'isBold': isBold,
      'isItalic': isItalic,
      'isUnderline': isUnderline,
      'isStrikethrough': isStrikethrough,
      'textColor': textColor,
      'backgroundColor': backgroundColor,
      'alignment': alignment.name,
      'verticalAlignment': verticalAlignment.name,
      'numberFormat': numberFormat,
      'wrapText': wrapText,
      'indentLevel': indentLevel,
      'rotation': rotation,
      'topBorder': topBorder?.toMap(),
      'bottomBorder': bottomBorder?.toMap(),
      'leftBorder': leftBorder?.toMap(),
      'rightBorder': rightBorder?.toMap(),
    };
  }

  factory CellFormat.fromMap(Map<String, dynamic> map) {
    return CellFormat(
      fontFamily: map['fontFamily'],
      fontSize: map['fontSize']?.toDouble(),
      isBold: map['isBold'] ?? false,
      isItalic: map['isItalic'] ?? false,
      isUnderline: map['isUnderline'] ?? false,
      isStrikethrough: map['isStrikethrough'] ?? false,
      textColor: map['textColor'],
      backgroundColor: map['backgroundColor'],
      alignment: CellAlignment.values.firstWhere(
        (e) => e.name == map['alignment'],
        orElse: () => CellAlignment.left,
      ),
      verticalAlignment: CellVerticalAlignment.values.firstWhere(
        (e) => e.name == map['verticalAlignment'],
        orElse: () => CellVerticalAlignment.middle,
      ),
      numberFormat: map['numberFormat'],
      wrapText: map['wrapText'] ?? false,
      indentLevel: map['indentLevel'],
      rotation: map['rotation']?.toDouble(),
      topBorder: map['topBorder'] != null ? BorderStyle.fromMap(map['topBorder']) : null,
      bottomBorder: map['bottomBorder'] != null ? BorderStyle.fromMap(map['bottomBorder']) : null,
      leftBorder: map['leftBorder'] != null ? BorderStyle.fromMap(map['leftBorder']) : null,
      rightBorder: map['rightBorder'] != null ? BorderStyle.fromMap(map['rightBorder']) : null,
    );
  }
}

class BorderStyle {
  final String color;
  final double width;
  final String style; // solid, dashed, dotted, double

  const BorderStyle({
    this.color = '#000000',
    this.width = 1.0,
    this.style = 'solid',
  });

  Map<String, dynamic> toMap() {
    return {
      'color': color,
      'width': width,
      'style': style,
    };
  }

  factory BorderStyle.fromMap(Map<String, dynamic> map) {
    return BorderStyle(
      color: map['color'] ?? '#000000',
      width: map['width']?.toDouble() ?? 1.0,
      style: map['style'] ?? 'solid',
    );
  }
}

class SpreadsheetCell {
  final String id;
  final int row;
  final int column;
  final String rawValue;
  final dynamic calculatedValue;
  final String? formula;
  final CellDataType dataType;
  final CellFormat format;
  final String? comment;
  final bool isLocked;
  final bool isHidden;
  final List<String> dependentCells;
  final List<String> precedentCells;
  final DateTime lastModified;

  SpreadsheetCell({
    String? id,
    required this.row,
    required this.column,
    this.rawValue = '',
    this.calculatedValue,
    this.formula,
    this.dataType = CellDataType.text,
    this.format = const CellFormat(),
    this.comment,
    this.isLocked = false,
    this.isHidden = false,
    this.dependentCells = const [],
    this.precedentCells = const [],
    DateTime? lastModified,
  }) : id = id ?? const Uuid().v4(),
       lastModified = lastModified ?? DateTime.now();

  String get cellAddress => '${columnToLetter(column)}$row';

  static String columnToLetter(int column) {
    String result = '';
    while (column > 0) {
      column--;
      result = String.fromCharCode(65 + (column % 26)) + result;
      column ~/= 26;
    }
    return result;
  }

  static int letterToColumn(String letter) {
    int result = 0;
    for (int i = 0; i < letter.length; i++) {
      result = result * 26 + (letter.codeUnitAt(i) - 64);
    }
    return result;
  }

  SpreadsheetCell copyWith({
    String? rawValue,
    dynamic calculatedValue,
    String? formula,
    CellDataType? dataType,
    CellFormat? format,
    String? comment,
    bool? isLocked,
    bool? isHidden,
    List<String>? dependentCells,
    List<String>? precedentCells,
    DateTime? lastModified,
  }) {
    return SpreadsheetCell(
      id: id,
      row: row,
      column: column,
      rawValue: rawValue ?? this.rawValue,
      calculatedValue: calculatedValue ?? this.calculatedValue,
      formula: formula ?? this.formula,
      dataType: dataType ?? this.dataType,
      format: format ?? this.format,
      comment: comment ?? this.comment,
      isLocked: isLocked ?? this.isLocked,
      isHidden: isHidden ?? this.isHidden,
      dependentCells: dependentCells ?? this.dependentCells,
      precedentCells: precedentCells ?? this.precedentCells,
      lastModified: lastModified ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'row': row,
      'column': column,
      'rawValue': rawValue,
      'calculatedValue': calculatedValue,
      'formula': formula,
      'dataType': dataType.name,
      'format': format.toMap(),
      'comment': comment,
      'isLocked': isLocked,
      'isHidden': isHidden,
      'dependentCells': dependentCells,
      'precedentCells': precedentCells,
      'lastModified': lastModified.toIso8601String(),
    };
  }

  factory SpreadsheetCell.fromMap(Map<String, dynamic> map) {
    return SpreadsheetCell(
      id: map['id'],
      row: map['row'],
      column: map['column'],
      rawValue: map['rawValue'] ?? '',
      calculatedValue: map['calculatedValue'],
      formula: map['formula'],
      dataType: CellDataType.values.firstWhere(
        (e) => e.name == map['dataType'],
        orElse: () => CellDataType.text,
      ),
      format: CellFormat.fromMap(map['format'] ?? {}),
      comment: map['comment'],
      isLocked: map['isLocked'] ?? false,
      isHidden: map['isHidden'] ?? false,
      dependentCells: List<String>.from(map['dependentCells'] ?? []),
      precedentCells: List<String>.from(map['precedentCells'] ?? []),
      lastModified: DateTime.parse(map['lastModified']),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SpreadsheetCell && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
