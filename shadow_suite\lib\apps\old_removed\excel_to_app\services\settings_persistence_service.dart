import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'advanced_layout_service.dart';

/// Real-time settings persistence service with auto-save functionality
class SettingsPersistenceService {
  static const String _layoutConfigKey = 'excel_app_layout_config';
  static const String _presetsKey = 'excel_app_presets';
  static const String _autoSaveKey = 'excel_app_auto_save_enabled';
  static const String _autoSaveIntervalKey = 'excel_app_auto_save_interval';
  static const String _lastSaveKey = 'excel_app_last_save';
  
  static SharedPreferences? _prefs;
  static Timer? _autoSaveTimer;
  static bool _isInitialized = false;
  static bool _autoSaveEnabled = true;
  static int _autoSaveIntervalSeconds = 30;
  static LayoutConfiguration? _lastSavedConfig;
  static final List<VoidCallback> _changeListeners = [];

  /// Initialize the persistence service
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    _prefs = await SharedPreferences.getInstance();
    await _loadSettings();
    _startAutoSave();
    _isInitialized = true;
  }

  /// Save layout configuration immediately
  static Future<bool> saveLayoutConfiguration(LayoutConfiguration config) async {
    try {
      await _ensureInitialized();
      final json = config.toJson();
      final success = await _prefs!.setString(_layoutConfigKey, jsonEncode(json));
      
      if (success) {
        _lastSavedConfig = config;
        await _prefs!.setString(_lastSaveKey, DateTime.now().toIso8601String());
        _notifyListeners();
      }
      
      return success;
    } catch (e) {
      debugPrint('Error saving layout configuration: $e');
      return false;
    }
  }

  /// Load layout configuration
  static Future<LayoutConfiguration?> loadLayoutConfiguration() async {
    try {
      await _ensureInitialized();
      final jsonString = _prefs!.getString(_layoutConfigKey);
      
      if (jsonString != null) {
        final json = jsonDecode(jsonString) as Map<String, dynamic>;
        final config = LayoutConfiguration.fromJson(json);
        _lastSavedConfig = config;
        return config;
      }
    } catch (e) {
      debugPrint('Error loading layout configuration: $e');
    }
    return null;
  }

  /// Save presets
  static Future<bool> savePresets(List<LayoutPreset> presets) async {
    try {
      await _ensureInitialized();
      final jsonList = presets.map((preset) => preset.toJson()).toList();
      return await _prefs!.setString(_presetsKey, jsonEncode(jsonList));
    } catch (e) {
      debugPrint('Error saving presets: $e');
      return false;
    }
  }

  /// Load presets
  static Future<List<LayoutPreset>> loadPresets() async {
    try {
      await _ensureInitialized();
      final jsonString = _prefs!.getString(_presetsKey);
      
      if (jsonString != null) {
        final jsonList = jsonDecode(jsonString) as List<dynamic>;
        return jsonList
            .map((json) => LayoutPreset.fromJson(json as Map<String, dynamic>))
            .toList();
      }
    } catch (e) {
      debugPrint('Error loading presets: $e');
    }
    return [];
  }

  /// Export configuration to JSON string
  static Future<String> exportConfiguration() async {
    try {
      await _ensureInitialized();
      final config = await loadLayoutConfiguration();
      final presets = await loadPresets();
      
      final exportData = {
        'version': '1.0',
        'timestamp': DateTime.now().toIso8601String(),
        'configuration': config?.toJson(),
        'presets': presets.map((p) => p.toJson()).toList(),
        'settings': {
          'autoSaveEnabled': _autoSaveEnabled,
          'autoSaveInterval': _autoSaveIntervalSeconds,
        },
      };
      
      return jsonEncode(exportData);
    } catch (e) {
      debugPrint('Error exporting configuration: $e');
      rethrow;
    }
  }

  /// Import configuration from JSON string
  static Future<bool> importConfiguration(String jsonString) async {
    try {
      await _ensureInitialized();
      final data = jsonDecode(jsonString) as Map<String, dynamic>;
      
      // Validate version compatibility
      final version = data['version'] as String?;
      if (version != '1.0') {
        throw Exception('Unsupported configuration version: $version');
      }
      
      // Import configuration
      if (data['configuration'] != null) {
        final config = LayoutConfiguration.fromJson(
          data['configuration'] as Map<String, dynamic>,
        );
        await saveLayoutConfiguration(config);
        AdvancedLayoutService.updateConfig(config);
      }
      
      // Import presets
      if (data['presets'] != null) {
        final presetsList = data['presets'] as List<dynamic>;
        final presets = presetsList
            .map((json) => LayoutPreset.fromJson(json as Map<String, dynamic>))
            .toList();
        await savePresets(presets);
      }
      
      // Import settings
      if (data['settings'] != null) {
        final settings = data['settings'] as Map<String, dynamic>;
        if (settings['autoSaveEnabled'] != null) {
          await setAutoSaveEnabled(settings['autoSaveEnabled'] as bool);
        }
        if (settings['autoSaveInterval'] != null) {
          await setAutoSaveInterval(settings['autoSaveInterval'] as int);
        }
      }
      
      return true;
    } catch (e) {
      debugPrint('Error importing configuration: $e');
      return false;
    }
  }

  /// Enable/disable auto-save
  static Future<void> setAutoSaveEnabled(bool enabled) async {
    await _ensureInitialized();
    _autoSaveEnabled = enabled;
    await _prefs!.setBool(_autoSaveKey, enabled);
    
    if (enabled) {
      _startAutoSave();
    } else {
      _stopAutoSave();
    }
    _notifyListeners();
  }

  /// Set auto-save interval in seconds
  static Future<void> setAutoSaveInterval(int seconds) async {
    await _ensureInitialized();
    _autoSaveIntervalSeconds = seconds;
    await _prefs!.setInt(_autoSaveIntervalKey, seconds);
    
    if (_autoSaveEnabled) {
      _startAutoSave(); // Restart with new interval
    }
    _notifyListeners();
  }

  /// Get auto-save status
  static bool get isAutoSaveEnabled => _autoSaveEnabled;
  
  /// Get auto-save interval
  static int get autoSaveIntervalSeconds => _autoSaveIntervalSeconds;
  
  /// Get last save time
  static Future<DateTime?> getLastSaveTime() async {
    await _ensureInitialized();
    final timeString = _prefs!.getString(_lastSaveKey);
    return timeString != null ? DateTime.parse(timeString) : null;
  }

  /// Check if configuration has unsaved changes
  static bool hasUnsavedChanges(LayoutConfiguration currentConfig) {
    if (_lastSavedConfig == null) return true;
    
    // Compare JSON representations for deep equality
    final currentJson = jsonEncode(currentConfig.toJson());
    final savedJson = jsonEncode(_lastSavedConfig!.toJson());
    return currentJson != savedJson;
  }

  /// Add change listener
  static void addChangeListener(VoidCallback listener) {
    _changeListeners.add(listener);
  }

  /// Remove change listener
  static void removeChangeListener(VoidCallback listener) {
    _changeListeners.remove(listener);
  }

  /// Clear all saved data
  static Future<void> clearAllData() async {
    await _ensureInitialized();
    await _prefs!.remove(_layoutConfigKey);
    await _prefs!.remove(_presetsKey);
    await _prefs!.remove(_lastSaveKey);
    _lastSavedConfig = null;
    _notifyListeners();
  }

  /// Get storage usage statistics
  static Future<Map<String, dynamic>> getStorageStats() async {
    await _ensureInitialized();
    
    final configSize = _prefs!.getString(_layoutConfigKey)?.length ?? 0;
    final presetsSize = _prefs!.getString(_presetsKey)?.length ?? 0;
    final lastSave = await getLastSaveTime();
    
    return {
      'configurationSize': configSize,
      'presetsSize': presetsSize,
      'totalSize': configSize + presetsSize,
      'lastSaveTime': lastSave?.toIso8601String(),
      'autoSaveEnabled': _autoSaveEnabled,
      'autoSaveInterval': _autoSaveIntervalSeconds,
    };
  }

  // Private methods
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  static Future<void> _loadSettings() async {
    _autoSaveEnabled = _prefs!.getBool(_autoSaveKey) ?? true;
    _autoSaveIntervalSeconds = _prefs!.getInt(_autoSaveIntervalKey) ?? 30;
  }

  static void _startAutoSave() {
    _stopAutoSave();
    
    if (_autoSaveEnabled) {
      _autoSaveTimer = Timer.periodic(
        Duration(seconds: _autoSaveIntervalSeconds),
        (_) => _performAutoSave(),
      );
    }
  }

  static void _stopAutoSave() {
    _autoSaveTimer?.cancel();
    _autoSaveTimer = null;
  }

  static Future<void> _performAutoSave() async {
    try {
      final currentConfig = AdvancedLayoutService.currentConfig;
      if (hasUnsavedChanges(currentConfig)) {
        await saveLayoutConfiguration(currentConfig);
        debugPrint('Auto-save completed at ${DateTime.now()}');
      }
    } catch (e) {
      debugPrint('Auto-save failed: $e');
    }
  }

  static void _notifyListeners() {
    for (final listener in _changeListeners) {
      try {
        listener();
      } catch (e) {
        debugPrint('Error notifying listener: $e');
      }
    }
  }

  /// Dispose resources
  static void dispose() {
    _stopAutoSave();
    _changeListeners.clear();
    _isInitialized = false;
  }
}
