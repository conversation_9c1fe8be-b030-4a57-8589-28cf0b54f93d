import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../core/theme/app_theme.dart';
import '../../core/services/storage_scanner_service.dart';
// Removed imports for deleted apps - using simplified dashboard
import '../../apps/unified_finance/services/finance_service.dart';
import '../../apps/unified_finance/models/finance_models.dart';

/// Mobile-optimized dashboard with responsive grid layout
class DashboardScreen extends ConsumerWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: RefreshIndicator(
        onRefresh: () => _refreshDashboard(ref),
        child: SingleChildScrollView(
          padding: EdgeInsets.all(isMobile ? 16 : 24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildWelcomeSection(context, isMobile),
              const SizedBox(height: 24),
              _buildQuickActions(context, ref, isMobile),
              const SizedBox(height: 24),
              _buildQuickStats(context, ref, isMobile),
              const SizedBox(height: 24),
              _buildMiniAppGrid(context, ref, isMobile),
              const SizedBox(height: 24),
              _buildRecentActivity(context, ref, isMobile),
              const SizedBox(height: 24),
              _buildSystemStatus(context, ref, isMobile),
            ],
          ),
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(context, ref),
    );
  }

  Widget _buildWelcomeSection(BuildContext context, bool isMobile) {
    final hour = DateTime.now().hour;
    String greeting;
    if (hour < 12) {
      greeting = 'Good Morning';
    } else if (hour < 17) {
      greeting = 'Good Afternoon';
    } else {
      greeting = 'Good Evening';
    }

    return Container(
      padding: EdgeInsets.all(isMobile ? 20 : 24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(isMobile ? 12 : 16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: isMobile
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  greeting,
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Welcome back to Shadow Suite',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    const Icon(Icons.today, color: Colors.white70, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      _formatDate(DateTime.now()),
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ],
            )
          : Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '$greeting, Welcome to Shadow Suite',
                        style: Theme.of(context).textTheme.headlineMedium
                            ?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Your complete productivity companion with multiple specialized apps',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                    ],
                  ),
                ),
                const Icon(Icons.apps, size: 64, color: Colors.white),
              ],
            ),
    );
  }

  Widget _buildQuickStats(BuildContext context, WidgetRef ref, bool isMobile) {
    // Simplified stats - providers will be implemented later
    final accountsAsync = <FinanceAccount>[];
    final notesAsync = <dynamic>[];
    final todosAsync = <dynamic>[];
    final toolsAsync = <dynamic>[];

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            context,
            'Total Notes',
            '${notesAsync.length}',
            Icons.note_alt,
            AppTheme.memoSuiteColor,
            onTap: () => context.go('/notes-app'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            context,
            'Pending Todos',
            '${todosAsync.length}',
            Icons.check_box,
            AppTheme.memoSuiteColor,
            onTap: () => context.go('/todo-app'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            context,
            'Accounts',
            '${accountsAsync.length}',
            Icons.account_balance,
            AppTheme.moneyFlowColor,
            onTap: () => context.go('/unified-finance'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            context,
            'Tools Created',
            '${toolsAsync.length}',
            Icons.build,
            AppTheme.toolsBuilderColor,
            onTap: () => context.go('/tools-builder'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Consumer(
            builder: (context, ref, child) {
              final cachedResults = StorageScannerService.getCachedResults();
              final mediaCount =
                  (cachedResults['images']?.length ?? 0) +
                  (cachedResults['videos']?.length ?? 0);

              return _buildStatCard(
                context,
                'Media Files',
                mediaCount > 0 ? '$mediaCount' : 'Scan',
                Icons.photo_library,
                const Color(0xFF8E44AD),
                onTap: () => context.go('/smart-gallery'),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color, {
    VoidCallback? onTap,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Icon(icon, size: 32, color: color),
              const SizedBox(height: 12),
              Text(
                value,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingStatCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color.withValues(alpha: 0.5)),
            const SizedBox(height: 12),
            SizedBox(
              width: 40,
              height: 20,
              child: LinearProgressIndicator(
                backgroundColor: Colors.grey.shade200,
                valueColor: AlwaysStoppedAnimation<Color>(color),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivity(
    BuildContext context,
    WidgetRef ref,
    bool isMobile,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Recent Activity',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const Spacer(),
            _buildQuickActionButtons(context, ref),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(flex: 2, child: _buildRecentTransactions(context, ref)),
            const SizedBox(width: 16),
            Expanded(child: _buildAccountOverview(context, ref)),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionButtons(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        ElevatedButton.icon(
          onPressed: () => _showAddTransactionDialog(context),
          icon: const Icon(Icons.add, size: 16),
          label: const Text('Add Transaction'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.moneyFlowColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
        ),
        const SizedBox(width: 8),
        ElevatedButton.icon(
          onPressed: () => _showAddAccountDialog(context),
          icon: const Icon(Icons.account_balance, size: 16),
          label: const Text('Add Account'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.moneyFlowColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
        ),
        const SizedBox(width: 8),
        ElevatedButton.icon(
          onPressed: () => context.go('/money-manager'),
          icon: const Icon(Icons.analytics, size: 16),
          label: const Text('View Reports'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
        ),
        const SizedBox(width: 8),
        ElevatedButton.icon(
          onPressed: () => context.go('/smart-gallery'),
          icon: const Icon(Icons.photo_camera, size: 16),
          label: const Text('Scan Media'),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF8E44AD),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
        ),
      ],
    );
  }

  Widget _buildRecentTransactions(BuildContext context, WidgetRef ref) {
    // Simplified - providers will be implemented later
    final recentTransactionsAsync = <FinanceTransaction>[];
    final accountsAsync = <FinanceAccount>[];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Transactions',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            recentTransactionsAsync.isEmpty
                ? _buildEmptyTransactions(context)
                : ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: recentTransactionsAsync.take(5).length,
                    separatorBuilder: (context, index) =>
                        const Divider(height: 1),
                    itemBuilder: (context, index) {
                      final transaction = recentTransactionsAsync[index];
                      final account = accountsAsync.firstWhere(
                        (acc) => acc.id == transaction.accountId,
                        orElse: () => FinanceAccount(
                          id: '',
                          name: 'Unknown Account',
                          type: AccountType.checking,
                          balance: 0,
                          color: Colors.grey,
                          institution: 'Unknown',
                          createdAt: DateTime.now(),
                          updatedAt: DateTime.now(),
                        ),
                      );
                      final category = TransactionCategory.otherExpense;

                      return _buildTransactionTile(
                        context,
                        ref,
                        transaction,
                        account,
                        category,
                      );
                    },
                  ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyTransactions(BuildContext context) {
    return Center(
      child: Column(
        children: [
          Icon(Icons.receipt_long, size: 48, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'No transactions yet',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(color: Colors.grey.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            'Add your first transaction to get started',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionTile(
    BuildContext context,
    WidgetRef ref,
    FinanceTransaction transaction,
    FinanceAccount account,
    TransactionCategory category,
  ) {
    final transactionColor = _getTransactionColor(transaction.type);

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: transactionColor.withValues(alpha: 0.1),
        child: Icon(
          _getTransactionIcon(transaction.type),
          color: transactionColor,
          size: 20,
        ),
      ),
      title: Text(
        transaction.description.isNotEmpty
            ? transaction.description
            : category.name,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text('${account.name} • ${category.name}'),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            '${transaction.type == TransactionType.income
                ? '+'
                : transaction.type == TransactionType.transfer
                ? ''
                : '-'}\$${transaction.amount.toStringAsFixed(2)}',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: transactionColor,
            ),
          ),
          Text(
            _formatDate(transaction.date),
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade600),
          ),
        ],
      ),
      onTap: () =>
          _showTransactionDetails(context, ref, transaction, account, category),
    );
  }

  Widget _buildAccountOverview(BuildContext context, WidgetRef ref) {
    final accountsAsync = ref.watch(accountsProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Account Overview',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            accountsAsync.isEmpty
                ? _buildEmptyAccounts(context)
                : Builder(
                    builder: (context) {
                      final accounts = accountsAsync;
                      final totalBalance = accounts.fold(
                        0.0,
                        (sum, account) => sum + account.balance,
                      );

                      return Column(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  AppTheme.moneyFlowColor,
                                  AppTheme.moneyFlowColor.withValues(
                                    alpha: 0.7,
                                  ),
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.account_balance_wallet,
                                  color: Colors.white,
                                  size: 24,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        'Total Balance',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 14,
                                        ),
                                      ),
                                      Text(
                                        '\$${totalBalance.toStringAsFixed(2)}',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),
                          ...accounts
                              .take(3)
                              .map(
                                (account) => Padding(
                                  padding: const EdgeInsets.only(bottom: 8),
                                  child: _buildAccountTile(
                                    context,
                                    ref,
                                    account,
                                  ),
                                ),
                              ),
                          if (accounts.length > 3)
                            TextButton(
                              onPressed: () => context.go('/money-manager'),
                              child: Text(
                                'View all ${accounts.length} accounts',
                              ),
                            ),
                        ],
                      );
                    },
                  ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyAccounts(BuildContext context) {
    return Center(
      child: Column(
        children: [
          Icon(Icons.account_balance, size: 48, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'No accounts yet',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(color: Colors.grey.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            'Add your first account to get started',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountTile(
    BuildContext context,
    WidgetRef ref,
    FinanceAccount account,
  ) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: account.color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          _getAccountIcon(account.type),
          color: account.color,
          size: 20,
        ),
      ),
      title: Text(
        account.name,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(account.type.name.toUpperCase()),
      trailing: Text(
        '\$${account.balance.toStringAsFixed(2)}',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: account.balance >= 0
              ? const Color(0xFF27AE60)
              : const Color(0xFFE74C3C),
        ),
      ),
      onTap: () => _showAccountDetails(context, ref, account),
    );
  }

  // Helper methods
  Color _getTransactionColor(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return const Color(0xFF27AE60);
      case TransactionType.expense:
        return const Color(0xFFE74C3C);
      case TransactionType.transfer:
        return const Color(0xFF3498DB);
    }
  }

  IconData _getTransactionIcon(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return Icons.arrow_downward;
      case TransactionType.expense:
        return Icons.arrow_upward;
      case TransactionType.transfer:
        return Icons.swap_horiz;
    }
  }

  IconData _getAccountIcon(AccountType type) {
    switch (type) {
      case AccountType.checking:
        return Icons.account_balance;
      case AccountType.savings:
        return Icons.savings;
      case AccountType.credit:
        return Icons.credit_card;
      case AccountType.cash:
        return Icons.account_balance_wallet;
      case AccountType.investment:
        return Icons.trending_up;
      case AccountType.loan:
        return Icons.money_off;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.month}/${date.day}/${date.year}';
    }
  }

  // Dialog methods
  void _showAddTransactionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Transaction'),
        content: const Text('Transaction creation will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _showAddAccountDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Account'),
        content: const Text('Account creation will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _showTransactionDetails(
    BuildContext context,
    WidgetRef ref,
    FinanceTransaction transaction,
    FinanceAccount account,
    TransactionCategory category,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Transaction Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow(
              'Description',
              transaction.description.isNotEmpty
                  ? transaction.description
                  : category.name,
            ),
            _buildDetailRow(
              'Amount',
              '\$${transaction.amount.toStringAsFixed(2)}',
            ),
            _buildDetailRow('Type', transaction.type.name.toUpperCase()),
            _buildDetailRow('Account', account.name),
            _buildDetailRow('Category', category.name),
            _buildDetailRow(
              'Date',
              '${transaction.date.month}/${transaction.date.day}/${transaction.date.year}',
            ),
            if (transaction.notes != null && transaction.notes!.isNotEmpty)
              _buildDetailRow('Notes', transaction.notes!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showEditTransactionDialog(context, transaction);
            },
            child: const Text('Edit'),
          ),
        ],
      ),
    );
  }

  void _showAccountDetails(
    BuildContext context,
    WidgetRef ref,
    FinanceAccount account,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Account Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Name', account.name),
            _buildDetailRow('Type', account.type.name.toUpperCase()),
            _buildDetailRow(
              'Current Balance',
              '\$${account.balance.toStringAsFixed(2)}',
            ),
            _buildDetailRow('Institution', account.institution),
            _buildDetailRow(
              'Created',
              '${account.createdAt.month}/${account.createdAt.day}/${account.createdAt.year}',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Edit Account'),
                  content: const Text(
                    'Account editing will be implemented here.',
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Cancel'),
                    ),
                    ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Save'),
                    ),
                  ],
                ),
              );
            },
            child: const Text('Edit'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  // New mobile-optimized methods
  Future<void> _refreshDashboard(WidgetRef ref) async {
    // Refresh all providers (simplified)
    // ref.invalidate(accountsProvider); // Will be implemented with proper providers

    // Add a small delay for better UX
    await Future.delayed(const Duration(milliseconds: 500));
  }

  Widget _buildQuickActions(
    BuildContext context,
    WidgetRef ref,
    bool isMobile,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Actions',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          isMobile
              ? Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: _buildQuickActionButton(
                            context,
                            'Add Transaction',
                            Icons.add_circle,
                            AppTheme.moneyFlowColor,
                            () => _showAddTransaction(context, ref),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildQuickActionButton(
                            context,
                            'Quick Note',
                            Icons.note_add,
                            AppTheme.memoSuiteColor,
                            () => _showQuickNote(context, ref),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: _buildQuickActionButton(
                            context,
                            'Voice Memo',
                            Icons.mic,
                            Colors.red,
                            () => _showVoiceMemo(context, ref),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildQuickActionButton(
                            context,
                            'Global Search',
                            Icons.search,
                            Colors.purple,
                            () => _showGlobalSearch(context),
                          ),
                        ),
                      ],
                    ),
                  ],
                )
              : Row(
                  children: [
                    Expanded(
                      child: _buildQuickActionButton(
                        context,
                        'Add Transaction',
                        Icons.add_circle,
                        AppTheme.moneyFlowColor,
                        () => _showAddTransaction(context, ref),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildQuickActionButton(
                        context,
                        'Quick Note',
                        Icons.note_add,
                        AppTheme.memoSuiteColor,
                        () => _showQuickNote(context, ref),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildQuickActionButton(
                        context,
                        'Voice Memo',
                        Icons.mic,
                        Colors.red,
                        () => _showVoiceMemo(context, ref),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildQuickActionButton(
                        context,
                        'Global Search',
                        Icons.search,
                        Colors.purple,
                        () => _showGlobalSearch(context),
                      ),
                    ),
                  ],
                ),
        ],
      ),
    );
  }

  Widget _buildQuickActionButton(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Material(
      color: color.withValues(alpha: 0.1),
      borderRadius: BorderRadius.circular(8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(height: 4),
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: color,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMiniAppGrid(BuildContext context, WidgetRef ref, bool isMobile) {
    final miniApps = [
      MiniAppInfo(
        'Money Manager',
        'Financial tracking & budgets',
        Icons.account_balance_wallet,
        AppTheme.moneyFlowColor,
        '/money-manager',
      ),
      MiniAppInfo(
        'File Manager',
        'File operations & cloud sync',
        Icons.folder,
        Colors.orange,
        '/file-manager',
      ),
      MiniAppInfo(
        'Tools Builder',
        'Excel to app converter',
        Icons.build,
        AppTheme.toolsBuilderColor,
        '/excel-to-app',
      ),
      MiniAppInfo(
        'Islamic App',
        'Quran, Hadith & Prayer times',
        Icons.book,
        Colors.green,
        '/islamic-app',
      ),
      MiniAppInfo(
        'Memo Suite',
        'Notes, todos & voice memos',
        Icons.note,
        AppTheme.memoSuiteColor,
        '/memo-suite',
      ),
      MiniAppInfo(
        'Shadow Player',
        'Media player & library',
        Icons.play_circle,
        Colors.deepPurple,
        '/shadow-player',
      ),
      MiniAppInfo(
        'Smart Gallery+',
        'AI-powered photo management',
        Icons.photo_library,
        Colors.pink,
        '/smart-gallery',
      ),
    ];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Mini Apps',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: isMobile ? 2 : 4,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: isMobile ? 1.1 : 1.2,
            ),
            itemCount: miniApps.length,
            itemBuilder: (context, index) {
              final app = miniApps[index];
              return _buildMiniAppCardNew(context, app, isMobile);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMiniAppCardNew(
    BuildContext context,
    MiniAppInfo app,
    bool isMobile,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => context.go(app.route),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: EdgeInsets.all(isMobile ? 12 : 16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              colors: [
                app.color.withValues(alpha: 0.1),
                app.color.withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: app.color.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  app.icon,
                  color: app.color,
                  size: isMobile ? 24 : 28,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                app.name,
                style: TextStyle(
                  fontSize: isMobile ? 12 : 14,
                  fontWeight: FontWeight.bold,
                  color: app.color,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                app.description,
                style: TextStyle(
                  fontSize: isMobile ? 10 : 12,
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSystemStatus(
    BuildContext context,
    WidgetRef ref,
    bool isMobile,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'System Status',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatusIndicator(
                  context,
                  'Storage',
                  '75%',
                  Colors.blue,
                  isMobile,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatusIndicator(
                  context,
                  'Memory',
                  '45%',
                  Colors.green,
                  isMobile,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatusIndicator(
                  context,
                  'Network',
                  'Online',
                  Colors.green,
                  isMobile,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusIndicator(
    BuildContext context,
    String label,
    String value,
    Color color,
    bool isMobile,
  ) {
    return Container(
      padding: EdgeInsets.all(isMobile ? 8 : 12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(Icons.circle, color: color, size: isMobile ? 12 : 16),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: isMobile ? 10 : 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: TextStyle(fontSize: isMobile ? 8 : 10, color: color),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton(BuildContext context, WidgetRef ref) {
    return FloatingActionButton(
      onPressed: () => _showQuickActions(context, ref),
      child: const Icon(Icons.add),
    );
  }

  void _showQuickActions(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.add_circle),
              title: const Text('Add Transaction'),
              onTap: () {
                Navigator.of(context).pop();
                _showAddTransaction(context, ref);
              },
            ),
            ListTile(
              leading: const Icon(Icons.note_add),
              title: const Text('Quick Note'),
              onTap: () {
                Navigator.of(context).pop();
                _showQuickNote(context, ref);
              },
            ),
            ListTile(
              leading: const Icon(Icons.mic),
              title: const Text('Voice Memo'),
              onTap: () {
                Navigator.of(context).pop();
                _showVoiceMemo(context, ref);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showAddTransaction(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Transaction'),
        content: const Text('Transaction creation will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _showQuickNote(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: Text('Quick note functionality will be implemented'),
        ),
      ),
    );
  }

  void _showVoiceMemo(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.4,
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: Text('Voice memo functionality will be implemented'),
        ),
      ),
    );
  }

  void _showGlobalSearch(BuildContext context) {
    showSearch(context: context, delegate: GlobalSearchDelegate());
  }

  void _showEditTransactionDialog(
    BuildContext context,
    FinanceTransaction transaction,
  ) {
    final titleController = TextEditingController(
      text: transaction.description,
    );
    final amountController = TextEditingController(
      text: transaction.amount.abs().toString(),
    );
    final descriptionController = TextEditingController(
      text: transaction.description,
    );
    TransactionType selectedType = transaction.type;
    TransactionCategory selectedCategory = transaction.category;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Transaction'),
        content: SizedBox(
          width: 400,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: titleController,
                  decoration: const InputDecoration(
                    labelText: 'Title',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: amountController,
                  decoration: const InputDecoration(
                    labelText: 'Amount',
                    border: OutlineInputBorder(),
                    prefixText: '\$',
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                StatefulBuilder(
                  builder: (context, setState) => Column(
                    children: [
                      DropdownButtonFormField<TransactionType>(
                        value: selectedType,
                        decoration: const InputDecoration(
                          labelText: 'Type',
                          border: OutlineInputBorder(),
                        ),
                        items: TransactionType.values.map((type) {
                          return DropdownMenuItem(
                            value: type,
                            child: Text(type.name.toUpperCase()),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() => selectedType = value);
                          }
                        },
                      ),
                      const SizedBox(height: 16),
                      DropdownButtonFormField<TransactionCategory>(
                        value: selectedCategory,
                        decoration: const InputDecoration(
                          labelText: 'Category',
                          border: OutlineInputBorder(),
                        ),
                        items: TransactionCategory.values.map((category) {
                          return DropdownMenuItem(
                            value: category,
                            child: Text(category.name.toUpperCase()),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() => selectedCategory = value);
                          }
                        },
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (titleController.text.trim().isEmpty ||
                  amountController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please fill in all required fields'),
                  ),
                );
                return;
              }

              final amount = double.tryParse(amountController.text.trim());
              if (amount == null || amount <= 0) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Please enter a valid amount')),
                );
                return;
              }

              // Create updated transaction
              final updatedTransaction = transaction.copyWith(
                description: titleController.text.trim(),
                amount: selectedType == TransactionType.expense
                    ? -amount
                    : amount,
                type: selectedType,
                category: selectedCategory,
                notes: descriptionController.text.trim(),
                updatedAt: DateTime.now(),
              );

              // Update transaction in the finance service
              // Note: This would typically call a service method to update the transaction
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Transaction updated successfully'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }
}

// Global Search Delegate
class GlobalSearchDelegate extends SearchDelegate<String> {
  @override
  List<Widget> buildActions(BuildContext context) => [
    IconButton(icon: const Icon(Icons.clear), onPressed: () => query = ''),
  ];

  @override
  Widget buildLeading(BuildContext context) => IconButton(
    icon: const Icon(Icons.arrow_back),
    onPressed: () => close(context, ''),
  );

  @override
  Widget buildResults(BuildContext context) =>
      const Center(child: Text('Search results will be implemented'));

  @override
  Widget buildSuggestions(BuildContext context) =>
      const Center(child: Text('Search suggestions will be implemented'));
}

// Helper class for mini app information
class MiniAppInfo {
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final String route;

  const MiniAppInfo(
    this.name,
    this.description,
    this.icon,
    this.color,
    this.route,
  );
}
