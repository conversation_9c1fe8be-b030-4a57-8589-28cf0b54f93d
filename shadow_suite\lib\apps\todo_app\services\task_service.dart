import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sqflite/sqflite.dart';
import '../../../core/database/database_service.dart';
import '../models/task_models.dart';

/// Advanced Task Management Service
class TaskService {
  static final TaskService _instance = TaskService._internal();
  factory TaskService() => _instance;
  TaskService._internal();

  // In-memory storage with database persistence
  final List<Task> _tasks = [];
  final List<Project> _projects = [];
  final ValueNotifier<int> _stateNotifier = ValueNotifier<int>(0);

  // Database instance
  Database? _database;

  /// Initialize task service
  Future<void> initialize() async {
    await _initializeDatabase();
    await _loadTasksFromDatabase();
    await _loadProjectsFromDatabase();
    if (_tasks.isEmpty) {
      _loadSampleTasks();
    }
  }

  Future<void> _initializeDatabase() async {
    _database = await DatabaseService.database;

    // Create tasks table
    await _database!.execute('''
      CREATE TABLE IF NOT EXISTS tasks (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        priority TEXT NOT NULL,
        status TEXT NOT NULL,
        category TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        due_date TEXT,
        start_date TEXT,
        completed_at TEXT,
        tags TEXT,
        assigned_to TEXT,
        project_id TEXT,
        estimated_minutes INTEGER DEFAULT 0,
        actual_minutes INTEGER DEFAULT 0,
        recurrence TEXT NOT NULL,
        custom_fields TEXT,
        color INTEGER NOT NULL,
        is_archived INTEGER DEFAULT 0,
        progress REAL DEFAULT 0.0
      )
    ''');

    // Create subtasks table
    await _database!.execute('''
      CREATE TABLE IF NOT EXISTS subtasks (
        id TEXT PRIMARY KEY,
        task_id TEXT NOT NULL,
        title TEXT NOT NULL,
        is_completed INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        completed_at TEXT,
        FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE
      )
    ''');

    // Create task_comments table
    await _database!.execute('''
      CREATE TABLE IF NOT EXISTS task_comments (
        id TEXT PRIMARY KEY,
        task_id TEXT NOT NULL,
        content TEXT NOT NULL,
        author TEXT NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE
      )
    ''');

    // Create task_attachments table
    await _database!.execute('''
      CREATE TABLE IF NOT EXISTS task_attachments (
        id TEXT PRIMARY KEY,
        task_id TEXT NOT NULL,
        name TEXT NOT NULL,
        path TEXT NOT NULL,
        type TEXT NOT NULL,
        size INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE
      )
    ''');

    // Create projects table
    await _database!.execute('''
      CREATE TABLE IF NOT EXISTS projects (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        color INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_archived INTEGER DEFAULT 0
      )
    ''');
  }

  void _loadSampleTasks() {
    final now = DateTime.now();
    _tasks.addAll([
      Task(
        id: '1',
        title: 'Complete project proposal',
        description: 'Write and submit the Q4 project proposal',
        priority: TaskPriority.high,
        status: TaskStatus.inProgress,
        category: TaskCategory.work,
        createdAt: now.subtract(const Duration(days: 2)),
        updatedAt: now,
        dueDate: now.add(const Duration(days: 3)),
        tags: ['urgent', 'proposal'],
        estimatedMinutes: 240,
        actualMinutes: 120,
        progress: 0.5,
        color: Colors.red,
      ),
      Task(
        id: '2',
        title: 'Buy groceries',
        description: 'Weekly grocery shopping',
        priority: TaskPriority.medium,
        status: TaskStatus.todo,
        category: TaskCategory.shopping,
        createdAt: now.subtract(const Duration(hours: 6)),
        updatedAt: now,
        dueDate: now.add(const Duration(days: 1)),
        tags: ['weekly', 'food'],
        estimatedMinutes: 60,
        color: Colors.green,
      ),
      Task(
        id: '3',
        title: 'Exercise routine',
        description: '30 minutes cardio workout',
        priority: TaskPriority.medium,
        status: TaskStatus.completed,
        category: TaskCategory.health,
        createdAt: now.subtract(const Duration(days: 1)),
        updatedAt: now,
        completedAt: now.subtract(const Duration(hours: 2)),
        recurrence: RecurrencePattern.daily,
        tags: ['health', 'daily'],
        estimatedMinutes: 30,
        actualMinutes: 35,
        progress: 1.0,
        color: Colors.blue,
      ),
    ]);
    _notifyChange();
  }

  // Task CRUD operations
  List<Task> getTasks() => List.unmodifiable(_tasks);

  List<Task> getTasksByStatus(TaskStatus status) {
    return _tasks
        .where((task) => task.status == status && !task.isArchived)
        .toList();
  }

  List<Task> getTasksByCategory(TaskCategory category) {
    return _tasks
        .where((task) => task.category == category && !task.isArchived)
        .toList();
  }

  List<Task> getTasksByPriority(TaskPriority priority) {
    return _tasks
        .where((task) => task.priority == priority && !task.isArchived)
        .toList();
  }

  List<Task> getTasksDueToday() {
    final today = DateTime.now();
    return _tasks.where((task) {
      if (task.dueDate == null || task.isArchived) return false;
      return task.dueDate!.year == today.year &&
          task.dueDate!.month == today.month &&
          task.dueDate!.day == today.day;
    }).toList();
  }

  List<Task> getOverdueTasks() {
    final now = DateTime.now();
    return _tasks.where((task) {
      if (task.dueDate == null ||
          task.isArchived ||
          task.status == TaskStatus.completed)
        return false;
      return task.dueDate!.isBefore(now);
    }).toList();
  }

  Future<void> addTask(Task task) async {
    _tasks.add(task);
    await _saveTaskToDatabase(task);
    _notifyChange();
  }

  Future<void> updateTask(Task task) async {
    final index = _tasks.indexWhere((t) => t.id == task.id);
    if (index != -1) {
      _tasks[index] = task;
      await _saveTaskToDatabase(task);
      _notifyChange();
    }
  }

  Future<void> deleteTask(String taskId) async {
    _tasks.removeWhere((task) => task.id == taskId);
    await _deleteTaskFromDatabase(taskId);
    _notifyChange();
  }

  Future<void> completeTask(String taskId) async {
    final index = _tasks.indexWhere((t) => t.id == taskId);
    if (index != -1) {
      final task = _tasks[index].copyWith(
        status: TaskStatus.completed,
        completedAt: DateTime.now(),
        progress: 1.0,
        updatedAt: DateTime.now(),
      );
      _tasks[index] = task;
      await _saveTaskToDatabase(task);
      _notifyChange();
    }
  }

  Future<void> archiveTask(String taskId) async {
    final index = _tasks.indexWhere((t) => t.id == taskId);
    if (index != -1) {
      final task = _tasks[index].copyWith(
        isArchived: true,
        updatedAt: DateTime.now(),
      );
      _tasks[index] = task;
      await _saveTaskToDatabase(task);
      _notifyChange();
    }
  }

  // Subtask operations
  Future<void> addSubtask(String taskId, Subtask subtask) async {
    final index = _tasks.indexWhere((t) => t.id == taskId);
    if (index != -1) {
      final task = _tasks[index];
      final updatedSubtasks = List<Subtask>.from(task.subtasks)..add(subtask);
      final updatedTask = task.copyWith(
        subtasks: updatedSubtasks,
        updatedAt: DateTime.now(),
      );
      _tasks[index] = updatedTask;
      await _saveTaskToDatabase(updatedTask);
      await _saveSubtaskToDatabase(taskId, subtask);
      _notifyChange();
    }
  }

  Future<void> toggleSubtask(String taskId, String subtaskId) async {
    final index = _tasks.indexWhere((t) => t.id == taskId);
    if (index != -1) {
      final task = _tasks[index];
      final subtasks = task.subtasks.map((s) {
        if (s.id == subtaskId) {
          return s.copyWith(
            isCompleted: !s.isCompleted,
            completedAt: !s.isCompleted ? DateTime.now() : null,
          );
        }
        return s;
      }).toList();

      final updatedTask = task.copyWith(
        subtasks: subtasks,
        updatedAt: DateTime.now(),
      );
      _tasks[index] = updatedTask;
      await _saveTaskToDatabase(updatedTask);
      _notifyChange();
    }
  }

  // Database operations
  Future<void> _saveTaskToDatabase(Task task) async {
    if (_database == null) return;

    await _database!.insert('tasks', {
      'id': task.id,
      'title': task.title,
      'description': task.description,
      'priority': task.priority.name,
      'status': task.status.name,
      'category': task.category.name,
      'created_at': task.createdAt.toIso8601String(),
      'updated_at': task.updatedAt.toIso8601String(),
      'due_date': task.dueDate?.toIso8601String(),
      'start_date': task.startDate?.toIso8601String(),
      'completed_at': task.completedAt?.toIso8601String(),
      'tags': task.tags.join(','),
      'assigned_to': task.assignedTo,
      'project_id': task.projectId,
      'estimated_minutes': task.estimatedMinutes,
      'actual_minutes': task.actualMinutes,
      'recurrence': task.recurrence.name,
      'custom_fields': task.customFields.toString(),
      'color': task.color.value,
      'is_archived': task.isArchived ? 1 : 0,
      'progress': task.progress,
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<void> _saveSubtaskToDatabase(String taskId, Subtask subtask) async {
    if (_database == null) return;

    await _database!.insert('subtasks', {
      'id': subtask.id,
      'task_id': taskId,
      'title': subtask.title,
      'is_completed': subtask.isCompleted ? 1 : 0,
      'created_at': subtask.createdAt.toIso8601String(),
      'completed_at': subtask.completedAt?.toIso8601String(),
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<void> _loadTasksFromDatabase() async {
    if (_database == null) return;

    final List<Map<String, dynamic>> maps = await _database!.query('tasks');
    _tasks.clear();

    for (final map in maps) {
      // Load subtasks for this task
      final subtaskMaps = await _database!.query(
        'subtasks',
        where: 'task_id = ?',
        whereArgs: [map['id']],
      );

      final subtasks = subtaskMaps
          .map(
            (subtaskMap) => Subtask.fromJson({
              'id': subtaskMap['id'],
              'title': subtaskMap['title'],
              'isCompleted': subtaskMap['is_completed'] == 1,
              'createdAt': subtaskMap['created_at'],
              'completedAt': subtaskMap['completed_at'],
            }),
          )
          .toList();

      final task = Task.fromJson({
        'id': map['id'],
        'title': map['title'],
        'description': map['description'],
        'priority': map['priority'],
        'status': map['status'],
        'category': map['category'],
        'createdAt': map['created_at'],
        'updatedAt': map['updated_at'],
        'dueDate': map['due_date'],
        'startDate': map['start_date'],
        'completedAt': map['completed_at'],
        'tags': map['tags']?.split(',') ?? [],
        'assignedTo': map['assigned_to'],
        'projectId': map['project_id'],
        'estimatedMinutes': map['estimated_minutes'],
        'actualMinutes': map['actual_minutes'],
        'recurrence': map['recurrence'],
        'customFields': {},
        'color': map['color'],
        'isArchived': map['is_archived'] == 1,
        'progress': map['progress'],
        'subtasks': subtasks,
        'attachments': [],
        'comments': [],
      });

      _tasks.add(task);
    }
  }

  Future<void> _loadProjectsFromDatabase() async {
    // Implementation for projects loading
  }

  Future<void> _deleteTaskFromDatabase(String taskId) async {
    if (_database == null) return;

    await _database!.delete('tasks', where: 'id = ?', whereArgs: [taskId]);
  }

  void _notifyChange() {
    _stateNotifier.value++;
  }

  ValueNotifier<int> get stateNotifier => _stateNotifier;
}

/// Project model for task organization
class Project {
  final String id;
  final String name;
  final String description;
  final Color color;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isArchived;

  const Project({
    required this.id,
    required this.name,
    this.description = '',
    this.color = Colors.blue,
    required this.createdAt,
    required this.updatedAt,
    this.isArchived = false,
  });
}

// Providers
final taskServiceProvider = Provider<TaskService>((ref) {
  final service = TaskService();
  service.initialize();
  return service;
});

final tasksProvider = Provider<List<Task>>((ref) {
  final service = ref.watch(taskServiceProvider);
  service.stateNotifier.value; // Trigger rebuild
  return service.getTasks();
});

final todoTasksProvider = Provider<List<Task>>((ref) {
  final service = ref.watch(taskServiceProvider);
  service.stateNotifier.value; // Trigger rebuild
  return service.getTasksByStatus(TaskStatus.todo);
});

final inProgressTasksProvider = Provider<List<Task>>((ref) {
  final service = ref.watch(taskServiceProvider);
  service.stateNotifier.value; // Trigger rebuild
  return service.getTasksByStatus(TaskStatus.inProgress);
});

final completedTasksProvider = Provider<List<Task>>((ref) {
  final service = ref.watch(taskServiceProvider);
  service.stateNotifier.value; // Trigger rebuild
  return service.getTasksByStatus(TaskStatus.completed);
});

final todayTasksProvider = Provider<List<Task>>((ref) {
  final service = ref.watch(taskServiceProvider);
  service.stateNotifier.value; // Trigger rebuild
  return service.getTasksDueToday();
});

final overdueTasksProvider = Provider<List<Task>>((ref) {
  final service = ref.watch(taskServiceProvider);
  service.stateNotifier.value; // Trigger rebuild
  return service.getOverdueTasks();
});
