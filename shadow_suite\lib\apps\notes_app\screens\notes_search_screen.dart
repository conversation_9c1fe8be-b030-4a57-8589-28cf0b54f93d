import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class NotesSearchScreen extends ConsumerStatefulWidget {
  const NotesSearchScreen({super.key});

  @override
  ConsumerState<NotesSearchScreen> createState() => _NotesSearchScreenState();
}

class _NotesSearchScreenState extends ConsumerState<NotesSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Search Notes'),
        backgroundColor: Colors.amber,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          _buildSearchHeader(),
          Expanded(
            child: _isSearching ? _buildSearchResults() : _buildSearchSuggestions(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search notes by title, content, or tags...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                    setState(() => _isSearching = false);
                  },
                  icon: const Icon(Icons.clear),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        onChanged: (value) {
          setState(() {
            _isSearching = value.isNotEmpty;
          });
        },
      ),
    );
  }

  Widget _buildSearchSuggestions() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        Text(
          'Recent Searches',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        _buildRecentSearchItem('meeting notes'),
        _buildRecentSearchItem('shopping list'),
        _buildRecentSearchItem('travel plans'),
        const SizedBox(height: 24),
        Text(
          'Popular Tags',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildTagChip('work'),
            _buildTagChip('personal'),
            _buildTagChip('ideas'),
            _buildTagChip('recipes'),
            _buildTagChip('travel'),
            _buildTagChip('learning'),
          ],
        ),
        const SizedBox(height: 24),
        Text(
          'Categories',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        _buildCategoryItem('Work', Icons.work, Colors.blue, 12),
        _buildCategoryItem('Personal', Icons.person, Colors.green, 8),
        _buildCategoryItem('Learning', Icons.school, Colors.purple, 5),
        _buildCategoryItem('Travel', Icons.flight, Colors.orange, 3),
      ],
    );
  }

  Widget _buildRecentSearchItem(String search) {
    return ListTile(
      leading: const Icon(Icons.history, color: Colors.grey),
      title: Text(search),
      trailing: const Icon(Icons.arrow_outward),
      onTap: () {
        _searchController.text = search;
        setState(() => _isSearching = true);
      },
    );
  }

  Widget _buildTagChip(String tag) {
    return ActionChip(
      label: Text(tag),
      onPressed: () {
        _searchController.text = '#$tag';
        setState(() => _isSearching = true);
      },
      backgroundColor: Colors.amber.withValues(alpha: 0.1),
      labelStyle: const TextStyle(color: Colors.amber),
    );
  }

  Widget _buildCategoryItem(String category, IconData icon, Color color, int count) {
    return ListTile(
      leading: Icon(icon, color: color),
      title: Text(category),
      trailing: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          '$count',
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
        ),
      ),
      onTap: () {
        _searchController.text = 'category:$category';
        setState(() => _isSearching = true);
      },
    );
  }

  Widget _buildSearchResults() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 10,
      itemBuilder: (context, index) {
        return _buildSearchResultItem(index);
      },
    );
  }

  Widget _buildSearchResultItem(int index) {
    final results = [
      {'title': 'Meeting Notes', 'content': 'Project discussion points and action items...', 'category': 'Work'},
      {'title': 'Shopping List', 'content': 'Groceries for this week including fruits...', 'category': 'Personal'},
      {'title': 'Book Ideas', 'content': 'Interesting concepts to explore in future reading...', 'category': 'Learning'},
    ];
    
    final result = results[index % results.length];
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    result['title'] as String,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.amber.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    result['category'] as String,
                    style: const TextStyle(
                      color: Colors.amber,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            RichText(
              text: TextSpan(
                style: Theme.of(context).textTheme.bodyMedium,
                children: [
                  TextSpan(text: result['content'] as String),
                  // Highlight search terms
                  if (_searchController.text.isNotEmpty)
                    TextSpan(
                      text: ' ${_searchController.text}',
                      style: const TextStyle(
                        backgroundColor: Colors.yellow,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Text(
                  'Modified 2 days ago',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {},
                  child: const Text('Open'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
