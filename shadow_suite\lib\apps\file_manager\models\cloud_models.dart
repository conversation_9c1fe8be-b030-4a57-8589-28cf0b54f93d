// Cloud Account Model
class CloudAccount {
  final String id;
  final CloudProviderType providerType;
  final String accountName;
  final String displayName;
  final bool isActive;
  final int totalStorage;
  final int usedStorage;
  final DateTime? lastSyncTime;
  final Map<String, String> credentials;
  final DateTime createdAt;

  const CloudAccount({
    required this.id,
    required this.providerType,
    required this.accountName,
    required this.displayName,
    required this.isActive,
    required this.totalStorage,
    required this.usedStorage,
    this.lastSyncTime,
    required this.credentials,
    required this.createdAt,
  });

  factory CloudAccount.fromJson(Map<String, dynamic> json) {
    return CloudAccount(
      id: json['id'] as String,
      providerType: CloudProviderType.values.firstWhere(
        (e) => e.name == json['provider_type'],
        orElse: () => CloudProviderType.googleDrive,
      ),
      accountName: json['account_name'] as String,
      displayName: json['display_name'] as String,
      isActive: json['is_active'] as bool,
      totalStorage: json['total_storage'] as int,
      usedStorage: json['used_storage'] as int,
      lastSyncTime: json['last_sync_time'] != null 
          ? DateTime.parse(json['last_sync_time'] as String) 
          : null,
      credentials: Map<String, String>.from(json['credentials'] as Map? ?? {}),
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'provider_type': providerType.name,
      'account_name': accountName,
      'display_name': displayName,
      'is_active': isActive,
      'total_storage': totalStorage,
      'used_storage': usedStorage,
      'last_sync_time': lastSyncTime?.toIso8601String(),
      'credentials': credentials,
      'created_at': createdAt.toIso8601String(),
    };
  }

  double get storageUsagePercentage {
    if (totalStorage == 0) return 0.0;
    return (usedStorage / totalStorage) * 100.0;
  }

  int get availableStorage => totalStorage - usedStorage;
}

// Cloud Sync Task Model
class CloudSyncTask {
  final String id;
  final String accountId;
  final String localPath;
  final String remotePath;
  final SyncDirection direction;
  final SyncSettings settings;
  final SyncStatus status;
  final DateTime? lastSyncTime;
  final int filesUploaded;
  final int filesDownloaded;
  final int bytesTransferred;
  final int conflictsResolved;
  final DateTime createdAt;

  const CloudSyncTask({
    required this.id,
    required this.accountId,
    required this.localPath,
    required this.remotePath,
    required this.direction,
    required this.settings,
    required this.status,
    this.lastSyncTime,
    required this.filesUploaded,
    required this.filesDownloaded,
    required this.bytesTransferred,
    required this.conflictsResolved,
    required this.createdAt,
  });

  CloudSyncTask copyWith({
    SyncSettings? settings,
    SyncStatus? status,
    DateTime? lastSyncTime,
    int? filesUploaded,
    int? filesDownloaded,
    int? bytesTransferred,
    int? conflictsResolved,
  }) {
    return CloudSyncTask(
      id: id,
      accountId: accountId,
      localPath: localPath,
      remotePath: remotePath,
      direction: direction,
      settings: settings ?? this.settings,
      status: status ?? this.status,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
      filesUploaded: filesUploaded ?? this.filesUploaded,
      filesDownloaded: filesDownloaded ?? this.filesDownloaded,
      bytesTransferred: bytesTransferred ?? this.bytesTransferred,
      conflictsResolved: conflictsResolved ?? this.conflictsResolved,
      createdAt: createdAt,
    );
  }
}

// Sync Settings Model
class SyncSettings {
  final bool enableRealTime;
  final Duration syncInterval;
  final List<String> includePaths;
  final List<String> excludePaths;
  final List<String> excludeExtensions;
  final int maxFileSize;
  final bool deleteRemoteFiles;
  final bool deleteLocalFiles;
  final ConflictResolution defaultConflictResolution;
  final bool enableCompression;
  final bool enableEncryption;

  const SyncSettings({
    required this.enableRealTime,
    required this.syncInterval,
    required this.includePaths,
    required this.excludePaths,
    required this.excludeExtensions,
    required this.maxFileSize,
    required this.deleteRemoteFiles,
    required this.deleteLocalFiles,
    required this.defaultConflictResolution,
    required this.enableCompression,
    required this.enableEncryption,
  });

  factory SyncSettings.defaultSettings() {
    return const SyncSettings(
      enableRealTime: true,
      syncInterval: Duration(minutes: 15),
      includePaths: [],
      excludePaths: [],
      excludeExtensions: ['.tmp', '.log', '.cache'],
      maxFileSize: 100 * 1024 * 1024, // 100 MB
      deleteRemoteFiles: false,
      deleteLocalFiles: false,
      defaultConflictResolution: ConflictResolution.useLocal,
      enableCompression: true,
      enableEncryption: false,
    );
  }

  SyncSettings copyWith({
    List<String>? includePaths,
    List<String>? excludePaths,
    ConflictResolution? defaultConflictResolution,
  }) {
    return SyncSettings(
      enableRealTime: enableRealTime,
      syncInterval: syncInterval,
      includePaths: includePaths ?? this.includePaths,
      excludePaths: excludePaths ?? this.excludePaths,
      excludeExtensions: excludeExtensions,
      maxFileSize: maxFileSize,
      deleteRemoteFiles: deleteRemoteFiles,
      deleteLocalFiles: deleteLocalFiles,
      defaultConflictResolution: defaultConflictResolution ?? this.defaultConflictResolution,
      enableCompression: enableCompression,
      enableEncryption: enableEncryption,
    );
  }
}

// Cloud File Model
class CloudFile {
  final String id;
  final String accountId;
  final String name;
  final String path;
  final String? parentId;
  final CloudFileType type;
  final int size;
  final String? mimeType;
  final DateTime createdAt;
  final DateTime modifiedAt;
  final String? downloadUrl;
  final Map<String, dynamic> metadata;

  const CloudFile({
    required this.id,
    required this.accountId,
    required this.name,
    required this.path,
    this.parentId,
    required this.type,
    required this.size,
    this.mimeType,
    required this.createdAt,
    required this.modifiedAt,
    this.downloadUrl,
    required this.metadata,
  });
}

// Cloud Cache Entry Model
class CloudCacheEntry {
  final String id;
  final String accountId;
  final String remotePath;
  final String localCachePath;
  final CacheStrategy strategy;
  final int priority;
  final DateTime lastAccessed;
  final DateTime? expiresAt;
  final bool isDownloaded;
  final int fileSize;
  final DateTime createdAt;

  const CloudCacheEntry({
    required this.id,
    required this.accountId,
    required this.remotePath,
    required this.localCachePath,
    required this.strategy,
    required this.priority,
    required this.lastAccessed,
    this.expiresAt,
    required this.isDownloaded,
    required this.fileSize,
    required this.createdAt,
  });

  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }
}

// Cloud Storage Analytics Model
class CloudStorageAnalytics {
  final String accountId;
  final int totalStorage;
  final int usedStorage;
  final int availableStorage;
  final Map<String, int> storageByType;
  final List<CloudStorageUsage> usageHistory;
  final List<CloudFileStats> largestFiles;
  final DateTime generatedAt;

  const CloudStorageAnalytics({
    required this.accountId,
    required this.totalStorage,
    required this.usedStorage,
    required this.availableStorage,
    required this.storageByType,
    required this.usageHistory,
    required this.largestFiles,
    required this.generatedAt,
  });

  double get usagePercentage {
    if (totalStorage == 0) return 0.0;
    return (usedStorage / totalStorage) * 100.0;
  }
}

// Cloud Storage Usage Model
class CloudStorageUsage {
  final DateTime date;
  final int usedStorage;
  final int filesCount;

  const CloudStorageUsage({
    required this.date,
    required this.usedStorage,
    required this.filesCount,
  });
}

// Cloud File Stats Model
class CloudFileStats {
  final String fileName;
  final String filePath;
  final int fileSize;
  final DateTime lastModified;

  const CloudFileStats({
    required this.fileName,
    required this.filePath,
    required this.fileSize,
    required this.lastModified,
  });
}

// Cloud Conflict Model
class CloudConflict {
  final String id;
  final String accountId;
  final String localPath;
  final String remotePath;
  final ConflictType type;
  final DateTime localModified;
  final DateTime remoteModified;
  final int localSize;
  final int remoteSize;
  final ConflictResolution? suggestedResolution;
  final DateTime detectedAt;

  const CloudConflict({
    required this.id,
    required this.accountId,
    required this.localPath,
    required this.remotePath,
    required this.type,
    required this.localModified,
    required this.remoteModified,
    required this.localSize,
    required this.remoteSize,
    this.suggestedResolution,
    required this.detectedAt,
  });
}

// Cloud Backup Schedule Model
class CloudBackupSchedule {
  final String id;
  final String accountId;
  final String localPath;
  final String remotePath;
  final BackupFrequency frequency;
  final BackupSettings settings;
  final bool isActive;
  final DateTime? lastBackupTime;
  final DateTime? nextBackupTime;
  final int totalBackups;
  final int totalSize;
  final DateTime createdAt;

  const CloudBackupSchedule({
    required this.id,
    required this.accountId,
    required this.localPath,
    required this.remotePath,
    required this.frequency,
    required this.settings,
    required this.isActive,
    this.lastBackupTime,
    this.nextBackupTime,
    required this.totalBackups,
    required this.totalSize,
    required this.createdAt,
  });
}

// Backup Settings Model
class BackupSettings {
  final bool enableCompression;
  final bool enableEncryption;
  final bool enableVersioning;
  final int maxVersions;
  final List<String> excludePatterns;
  final bool deleteOldBackups;
  final Duration retentionPeriod;

  const BackupSettings({
    required this.enableCompression,
    required this.enableEncryption,
    required this.enableVersioning,
    required this.maxVersions,
    required this.excludePatterns,
    required this.deleteOldBackups,
    required this.retentionPeriod,
  });

  factory BackupSettings.defaultSettings() {
    return const BackupSettings(
      enableCompression: true,
      enableEncryption: false,
      enableVersioning: true,
      maxVersions: 10,
      excludePatterns: ['*.tmp', '*.log', '*.cache'],
      deleteOldBackups: true,
      retentionPeriod: Duration(days: 90),
    );
  }
}

// Cloud Share Link Model
class CloudShareLink {
  final String id;
  final String accountId;
  final String filePath;
  final String shareUrl;
  final SharePermissions permissions;
  final DateTime? expirationDate;
  final String? password;
  final int accessCount;
  final DateTime createdAt;

  const CloudShareLink({
    required this.id,
    required this.accountId,
    required this.filePath,
    required this.shareUrl,
    required this.permissions,
    this.expirationDate,
    this.password,
    required this.accessCount,
    required this.createdAt,
  });

  bool get isExpired {
    if (expirationDate == null) return false;
    return DateTime.now().isAfter(expirationDate!);
  }

  bool get hasPassword => password != null && password!.isNotEmpty;
}

// Cloud Provider Model
class CloudProvider {
  final CloudProviderType type;
  final String name;
  final String baseUrl;
  final Map<String, String> endpoints;
  final List<String> supportedFeatures;

  const CloudProvider({
    required this.type,
    required this.name,
    required this.baseUrl,
    required this.endpoints,
    required this.supportedFeatures,
  });
}

// Cloud Authentication Result Model
class CloudAuthResult {
  final bool isSuccess;
  final String? accessToken;
  final String? refreshToken;
  final DateTime? expiresAt;
  final int? totalStorage;
  final int? usedStorage;
  final String? errorMessage;

  const CloudAuthResult({
    required this.isSuccess,
    this.accessToken,
    this.refreshToken,
    this.expiresAt,
    this.totalStorage,
    this.usedStorage,
    this.errorMessage,
  });
}

// Cloud Event Model
class CloudEvent {
  final CloudEventType type;
  final String? accountId;
  final String? syncTaskId;
  final String message;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  const CloudEvent({
    required this.type,
    this.accountId,
    this.syncTaskId,
    required this.message,
    required this.timestamp,
    this.metadata,
  });
}

// Enums
enum CloudProviderType { googleDrive, dropbox, oneDrive, iCloud, box, mega }
enum SyncDirection { upload, download, bidirectional }
enum SyncStatus { active, paused, stopped, error }
enum CloudFileType { file, folder }
enum CacheStrategy { intelligent, aggressive, conservative, manual }
enum ConflictType { modified, deleted, renamed, moved }
enum ConflictResolution { useLocal, useRemote, createCopy, merge, skip }
enum BackupFrequency { hourly, daily, weekly, monthly }
enum SharePermissions { read, write, admin }
enum CloudEventType {
  accountAdded,
  accountRemoved,
  syncStarted,
  syncCompleted,
  syncPaused,
  syncError,
  cachingEnabled,
  cachingDisabled,
  selectiveSyncConfigured,
  conflictDetected,
  conflictResolved,
  backupScheduleCreated,
  backupCompleted,
  crossAccountSyncCompleted,
  bandwidthThrottlingSet,
  shareLinkCreated,
  shareLinkExpired,
}
