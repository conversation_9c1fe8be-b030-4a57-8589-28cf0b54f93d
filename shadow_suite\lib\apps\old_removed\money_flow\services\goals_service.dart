import 'package:sqflite/sqflite.dart';
import '../models/goal.dart';
import 'money_flow_database_service.dart';

class GoalsService {
  static const String _tableName = 'goals';
  static const String _milestonesTableName = 'goal_milestones';

  static Future<void> createTables(Database db) async {
    await db.execute('''
      CREATE TABLE $_tableName (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        targetAmount REAL NOT NULL,
        currentAmount REAL NOT NULL DEFAULT 0.0,
        targetDate TEXT NOT NULL,
        category TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'active',
        color TEXT NOT NULL DEFAULT '#4CAF50',
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE $_milestonesTableName (
        id TEXT PRIMARY KEY,
        goalId TEXT NOT NULL,
        title TEXT NOT NULL,
        targetAmount REAL NOT NULL,
        targetDate TEXT NOT NULL,
        isCompleted INTEGER NOT NULL DEFAULT 0,
        completedAt TEXT,
        createdAt TEXT NOT NULL,
        FOREIGN KEY (goalId) REFERENCES $_tableName (id) ON DELETE CASCADE
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_goals_status ON $_tableName (status)');
    await db.execute('CREATE INDEX idx_goals_category ON $_tableName (category)');
    await db.execute('CREATE INDEX idx_goals_target_date ON $_tableName (targetDate)');
    await db.execute('CREATE INDEX idx_milestones_goal_id ON $_milestonesTableName (goalId)');
  }

  static Future<List<Goal>> getAllGoals() async {
    final db = await MoneyFlowDatabaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      orderBy: 'createdAt DESC',
    );

    final goals = <Goal>[];
    for (final map in maps) {
      final goal = Goal.fromMap(map);
      final milestones = await getMilestonesByGoalId(goal.id);
      goals.add(goal.copyWith(milestones: milestones));
    }

    return goals;
  }

  static Future<Goal?> getGoalById(String id) async {
    final db = await MoneyFlowDatabaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;

    final goal = Goal.fromMap(maps.first);
    final milestones = await getMilestonesByGoalId(goal.id);
    return goal.copyWith(milestones: milestones);
  }

  static Future<List<Goal>> getGoalsByCategory(GoalCategory category) async {
    final db = await MoneyFlowDatabaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      where: 'category = ?',
      whereArgs: [category.name],
      orderBy: 'createdAt DESC',
    );

    final goals = <Goal>[];
    for (final map in maps) {
      final goal = Goal.fromMap(map);
      final milestones = await getMilestonesByGoalId(goal.id);
      goals.add(goal.copyWith(milestones: milestones));
    }

    return goals;
  }

  static Future<List<Goal>> getGoalsByStatus(GoalStatus status) async {
    final db = await MoneyFlowDatabaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      where: 'status = ?',
      whereArgs: [status.name],
      orderBy: 'createdAt DESC',
    );

    final goals = <Goal>[];
    for (final map in maps) {
      final goal = Goal.fromMap(map);
      final milestones = await getMilestonesByGoalId(goal.id);
      goals.add(goal.copyWith(milestones: milestones));
    }

    return goals;
  }

  static Future<void> insertGoal(Goal goal) async {
    final db = await MoneyFlowDatabaseService.database;
    await db.insert(_tableName, goal.toMap());

    // Insert milestones if any
    for (final milestone in goal.milestones) {
      await insertMilestone(milestone);
    }
  }

  static Future<void> updateGoal(Goal goal) async {
    final db = await MoneyFlowDatabaseService.database;
    await db.update(
      _tableName,
      goal.copyWith(updatedAt: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [goal.id],
    );
  }

  static Future<void> deleteGoal(String id) async {
    final db = await MoneyFlowDatabaseService.database;
    // Delete milestones first (cascade should handle this, but being explicit)
    await db.delete(_milestonesTableName, where: 'goalId = ?', whereArgs: [id]);
    // Delete the goal
    await db.delete(_tableName, where: 'id = ?', whereArgs: [id]);
  }

  static Future<void> updateGoalProgress(String goalId, double amount) async {
    final db = await MoneyFlowDatabaseService.database;
    await db.update(
      _tableName,
      {
        'currentAmount': amount,
        'updatedAt': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [goalId],
    );

    // Check if goal is completed and update status
    final goal = await getGoalById(goalId);
    if (goal != null && goal.currentAmount >= goal.targetAmount && goal.status != GoalStatus.completed) {
      await db.update(
        _tableName,
        {
          'status': GoalStatus.completed.name,
          'updatedAt': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [goalId],
      );
    }
  }

  static Future<void> addToGoalProgress(String goalId, double amount) async {
    final goal = await getGoalById(goalId);
    if (goal != null) {
      final newAmount = goal.currentAmount + amount;
      await updateGoalProgress(goalId, newAmount);
    }
  }

  // Milestone methods
  static Future<List<GoalMilestone>> getMilestonesByGoalId(String goalId) async {
    final db = await MoneyFlowDatabaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      _milestonesTableName,
      where: 'goalId = ?',
      whereArgs: [goalId],
      orderBy: 'targetDate ASC',
    );

    return maps.map((map) => GoalMilestone.fromMap(map)).toList();
  }

  static Future<void> insertMilestone(GoalMilestone milestone) async {
    final db = await MoneyFlowDatabaseService.database;
    await db.insert(_milestonesTableName, milestone.toMap());
  }

  static Future<void> updateMilestone(GoalMilestone milestone) async {
    final db = await MoneyFlowDatabaseService.database;
    await db.update(
      _milestonesTableName,
      milestone.toMap(),
      where: 'id = ?',
      whereArgs: [milestone.id],
    );
  }

  static Future<void> deleteMilestone(String id) async {
    final db = await MoneyFlowDatabaseService.database;
    await db.delete(_milestonesTableName, where: 'id = ?', whereArgs: [id]);
  }

  static Future<void> completeMilestone(String milestoneId) async {
    final db = await MoneyFlowDatabaseService.database;
    await db.update(
      _milestonesTableName,
      {
        'isCompleted': 1,
        'completedAt': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [milestoneId],
    );
  }

  // Statistics and analytics
  static Future<Map<String, dynamic>> getGoalStatistics() async {
    final db = await MoneyFlowDatabaseService.database;
    
    final totalGoalsResult = await db.rawQuery('SELECT COUNT(*) as count FROM $_tableName');
    final totalGoals = totalGoalsResult.first['count'] as int;

    final activeGoalsResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $_tableName WHERE status = ?',
      [GoalStatus.active.name],
    );
    final activeGoals = activeGoalsResult.first['count'] as int;

    final completedGoalsResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $_tableName WHERE status = ?',
      [GoalStatus.completed.name],
    );
    final completedGoals = completedGoalsResult.first['count'] as int;

    final totalTargetResult = await db.rawQuery(
      'SELECT SUM(targetAmount) as total FROM $_tableName WHERE status = ?',
      [GoalStatus.active.name],
    );
    final totalTarget = (totalTargetResult.first['total'] as double?) ?? 0.0;

    final totalCurrentResult = await db.rawQuery(
      'SELECT SUM(currentAmount) as total FROM $_tableName WHERE status = ?',
      [GoalStatus.active.name],
    );
    final totalCurrent = (totalCurrentResult.first['total'] as double?) ?? 0.0;

    return {
      'totalGoals': totalGoals,
      'activeGoals': activeGoals,
      'completedGoals': completedGoals,
      'totalTargetAmount': totalTarget,
      'totalCurrentAmount': totalCurrent,
      'overallProgress': totalTarget > 0 ? (totalCurrent / totalTarget * 100) : 0.0,
    };
  }

  static Future<List<Goal>> getOverdueGoals() async {
    final db = await MoneyFlowDatabaseService.database;
    final now = DateTime.now().toIso8601String();
    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      where: 'targetDate < ? AND status = ?',
      whereArgs: [now, GoalStatus.active.name],
      orderBy: 'targetDate ASC',
    );

    final goals = <Goal>[];
    for (final map in maps) {
      final goal = Goal.fromMap(map);
      final milestones = await getMilestonesByGoalId(goal.id);
      goals.add(goal.copyWith(milestones: milestones));
    }

    return goals;
  }

  static Future<List<Goal>> getGoalsNearDeadline({int daysAhead = 30}) async {
    final db = await MoneyFlowDatabaseService.database;
    final now = DateTime.now();
    final deadline = now.add(Duration(days: daysAhead)).toIso8601String();
    
    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      where: 'targetDate <= ? AND targetDate >= ? AND status = ?',
      whereArgs: [deadline, now.toIso8601String(), GoalStatus.active.name],
      orderBy: 'targetDate ASC',
    );

    final goals = <Goal>[];
    for (final map in maps) {
      final goal = Goal.fromMap(map);
      final milestones = await getMilestonesByGoalId(goal.id);
      goals.add(goal.copyWith(milestones: milestones));
    }

    return goals;
  }
}
