import 'dart:io';
import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import '../models/spreadsheet.dart';
import '../models/spreadsheet_cell.dart';

class ExcelService {
  static Future<ExcelImportResult> importFromExcel() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.bytes != null) {
        final bytes = result.files.single.bytes!;
        final excel = Excel.decodeBytes(bytes);

        final spreadsheet = _convertExcelToSpreadsheet(excel, result.files.single.name);
        return ExcelImportResult.success(spreadsheet);
      } else {
        return ExcelImportResult.failure('No file selected');
      }
    } catch (e) {
      return ExcelImportResult.failure('Failed to import Excel file: ${e.toString()}');
    }
  }

  static Future<ExcelImportResult> importFromFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        return ExcelImportResult.failure('File does not exist: $filePath');
      }

      final bytes = await file.readAsBytes();
      final excel = Excel.decodeBytes(bytes);

      final fileName = filePath.split('/').last;
      final spreadsheet = _convertExcelToSpreadsheet(excel, fileName);
      return ExcelImportResult.success(spreadsheet);
    } catch (e) {
      return ExcelImportResult.failure('Failed to import file: ${e.toString()}');
    }
  }

  static Future<bool> exportToExcel(Spreadsheet spreadsheet, String fileName) async {
    try {
      final excel = Excel.createExcel();
      
      // Remove default sheet
      excel.delete('Sheet1');
      
      for (final sheet in spreadsheet.sheets) {
        final excelSheet = excel[sheet.name];
        
        // Convert cells
        for (final cell in sheet.cells.values) {
          final cellIndex = CellIndex.indexByString(cell.cellAddress);
          
          dynamic value = cell.calculatedValue ?? cell.rawValue;
          
          // Handle different data types
          if (cell.dataType == CellDataType.number && value is String) {
            value = double.tryParse(value) ?? value;
          } else if (cell.dataType == CellDataType.boolean && value is String) {
            value = value.toLowerCase() == 'true';
          } else if (cell.dataType == CellDataType.date && value is DateTime) {
            value = value.toIso8601String();
          }
          
          excelSheet.cell(cellIndex).value = TextCellValue(value.toString());
          
          // Apply basic formatting (simplified for compatibility)
          if (cell.format.isBold || cell.format.isItalic) {
            // Note: Excel package formatting may vary by version
            // This is a simplified implementation
            try {
              excelSheet.cell(cellIndex).cellStyle = CellStyle(
                bold: cell.format.isBold,
                italic: cell.format.isItalic,
              );
            } catch (e) {
              // Ignore formatting errors for compatibility
            }
          }
        }
        
        // Set column widths
        for (final entry in sheet.columnWidths.entries) {
          excelSheet.setColumnWidth(entry.key, entry.value);
        }
        
        // Set row heights
        for (final entry in sheet.rowHeights.entries) {
          excelSheet.setRowHeight(entry.key, entry.value);
        }
      }
      
      // Save file
      final directory = await getApplicationDocumentsDirectory();
      final filePath = '${directory.path}/$fileName.xlsx';
      final file = File(filePath);
      
      final bytes = excel.encode();
      if (bytes != null) {
        await file.writeAsBytes(bytes);
        return true;
      }
    } catch (e) {
      // Error exporting to Excel
      return false;
    }
    return false;
  }

  static Spreadsheet _convertExcelToSpreadsheet(Excel excel, String fileName) {
    final sheets = <SpreadsheetSheet>[];
    
    for (final tableName in excel.tables.keys) {
      final table = excel.tables[tableName];
      if (table == null) continue;
      
      final cells = <String, SpreadsheetCell>{};
      final columnWidths = <int, double>{};
      final rowHeights = <int, double>{};
      
      // Process cells
      for (final row in table.rows) {
        for (int colIndex = 0; colIndex < row.length; colIndex++) {
          final cell = row[colIndex];
          if (cell == null) continue;
          
          final rowIndex = table.rows.indexOf(row) + 1;
          final columnIndex = colIndex + 1;
          final cellAddress = '${SpreadsheetCell.columnToLetter(columnIndex)}$rowIndex';
          
          // Determine data type and value
          String rawValue = '';
          dynamic calculatedValue;
          CellDataType dataType = CellDataType.text;
          
          if (cell.value != null) {
            if (cell.value is TextCellValue) {
              rawValue = (cell.value as TextCellValue).value.toString();
              calculatedValue = rawValue;
              
              // Try to parse as number
              final numValue = double.tryParse(rawValue);
              if (numValue != null) {
                dataType = CellDataType.number;
                calculatedValue = numValue;
              } else if (rawValue.toLowerCase() == 'true' || rawValue.toLowerCase() == 'false') {
                dataType = CellDataType.boolean;
                calculatedValue = rawValue.toLowerCase() == 'true';
              } else if (DateTime.tryParse(rawValue) != null) {
                dataType = CellDataType.date;
                calculatedValue = DateTime.tryParse(rawValue);
              }
            } else if (cell.value is IntCellValue) {
              final intValue = (cell.value as IntCellValue).value;
              rawValue = intValue.toString();
              calculatedValue = intValue.toDouble();
              dataType = CellDataType.number;
            } else if (cell.value is DoubleCellValue) {
              final doubleValue = (cell.value as DoubleCellValue).value;
              rawValue = doubleValue.toString();
              calculatedValue = doubleValue;
              dataType = CellDataType.number;
            } else if (cell.value is BoolCellValue) {
              final boolValue = (cell.value as BoolCellValue).value;
              rawValue = boolValue.toString();
              calculatedValue = boolValue;
              dataType = CellDataType.boolean;
            } else if (cell.value is DateCellValue) {
              final dateValue = DateTime.now(); // Simplified date handling
              rawValue = dateValue.toIso8601String();
              calculatedValue = dateValue;
              dataType = CellDataType.date;
            } else if (cell.value is FormulaCellValue) {
              final formulaValue = (cell.value as FormulaCellValue).formula;
              rawValue = '=$formulaValue';
              dataType = CellDataType.formula;
              // calculatedValue will be set by formula engine
            }
          }
          
          // Use default format for compatibility
          const CellFormat format = CellFormat();
          
          cells[cellAddress] = SpreadsheetCell(
            row: rowIndex,
            column: columnIndex,
            rawValue: rawValue,
            calculatedValue: calculatedValue,
            formula: dataType == CellDataType.formula ? rawValue : null,
            dataType: dataType,
            format: format,
          );
        }
      }
      
      // Get column widths and row heights from Excel (simplified)
      for (int i = 0; i < table.maxColumns; i++) {
        try {
          final width = table.getColumnWidth(i);
          columnWidths[i + 1] = width;
        } catch (e) {
          // Use default width if not available
        }
      }

      for (int i = 0; i < table.maxRows; i++) {
        try {
          final height = table.getRowHeight(i);
          rowHeights[i + 1] = height;
        } catch (e) {
          // Use default height if not available
        }
      }
      
      sheets.add(SpreadsheetSheet(
        name: tableName,
        cells: cells,
        columnWidths: columnWidths,
        rowHeights: rowHeights,
      ));
    }
    
    return Spreadsheet(
      name: fileName.replaceAll(RegExp(r'\.(xlsx|xls)$'), ''),
      sheets: sheets.isNotEmpty ? sheets : [
        SpreadsheetSheet(name: 'Sheet1', cells: {}),
      ],
    );
  }

  static Future<String?> saveSpreadsheetToFile(Spreadsheet spreadsheet) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName = '${spreadsheet.name}_${DateTime.now().millisecondsSinceEpoch}';
      final filePath = '${directory.path}/$fileName.xlsx';
      
      final success = await exportToExcel(spreadsheet, fileName);
      return success ? filePath : null;
    } catch (e) {
      return null;
    }
  }

  static Future<List<String>> getRecentFiles() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final files = directory.listSync()
          .where((file) => file.path.endsWith('.xlsx'))
          .map((file) => file.path)
          .toList();
      
      // Sort by modification time (most recent first)
      files.sort((a, b) {
        final fileA = File(a);
        final fileB = File(b);
        return fileB.lastModifiedSync().compareTo(fileA.lastModifiedSync());
      });
      
      return files.take(10).toList(); // Return last 10 files
    } catch (e) {
      return [];
    }
  }

  static Future<Spreadsheet?> openRecentFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!file.existsSync()) return null;

      final bytes = await file.readAsBytes();
      final excel = Excel.decodeBytes(bytes);

      final fileName = filePath.split('/').last;
      return _convertExcelToSpreadsheet(excel, fileName);
    } catch (e) {
      return null;
    }
  }
}

// Excel Import Result
class ExcelImportResult {
  final bool success;
  final Spreadsheet? spreadsheet;
  final String? errorMessage;

  const ExcelImportResult.success(this.spreadsheet)
      : success = true, errorMessage = null;

  const ExcelImportResult.failure(this.errorMessage)
      : success = false, spreadsheet = null;

  bool get isSuccess => success;
  bool get isFailure => !success;
}

// Excel Export Result
class ExcelExportResult {
  final bool success;
  final String? filePath;
  final String? errorMessage;

  const ExcelExportResult.success(this.filePath)
      : success = true, errorMessage = null;

  const ExcelExportResult.failure(this.errorMessage)
      : success = false, filePath = null;

  bool get isSuccess => success;
  bool get isFailure => !success;
}
