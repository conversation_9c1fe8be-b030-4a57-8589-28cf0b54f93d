// import 'package:flutter/foundation.dart'; // Reserved for future debugging

// Tax Calculation Model
class TaxCalculation {
  final String id;
  final int taxYear;
  final double grossIncome;
  final double adjustedGrossIncome;
  final FilingStatus filingStatus;
  final double standardDeduction;
  final double itemizedDeductions;
  final double totalDeductions;
  final double federalTax;
  final double stateTax;
  final double totalCredits;
  final double totalTax;
  final double effectiveTaxRate;
  final double marginalTaxRate;
  final double refundOwed;
  final List<TaxDeduction> deductions;
  final List<TaxCredit> credits;
  final String? state;
  final DateTime calculatedAt;

  const TaxCalculation({
    required this.id,
    required this.taxYear,
    required this.grossIncome,
    required this.adjustedGrossIncome,
    required this.filingStatus,
    required this.standardDeduction,
    required this.itemizedDeductions,
    required this.totalDeductions,
    required this.federalTax,
    required this.stateTax,
    required this.totalCredits,
    required this.totalTax,
    required this.effectiveTaxRate,
    required this.marginalTaxRate,
    required this.refundOwed,
    required this.deductions,
    required this.credits,
    this.state,
    required this.calculatedAt,
  });

  factory TaxCalculation.fromJson(Map<String, dynamic> json) {
    return TaxCalculation(
      id: json['id'] as String,
      taxYear: json['tax_year'] as int,
      grossIncome: (json['gross_income'] as num).toDouble(),
      adjustedGrossIncome: (json['adjusted_gross_income'] as num).toDouble(),
      filingStatus: FilingStatus.values.firstWhere(
        (e) => e.name == json['filing_status'],
        orElse: () => FilingStatus.single,
      ),
      standardDeduction: (json['standard_deduction'] as num).toDouble(),
      itemizedDeductions: (json['itemized_deductions'] as num).toDouble(),
      totalDeductions: (json['total_deductions'] as num).toDouble(),
      federalTax: (json['federal_tax'] as num).toDouble(),
      stateTax: (json['state_tax'] as num).toDouble(),
      totalCredits: (json['total_credits'] as num).toDouble(),
      totalTax: (json['total_tax'] as num).toDouble(),
      effectiveTaxRate: (json['effective_tax_rate'] as num).toDouble(),
      marginalTaxRate: (json['marginal_tax_rate'] as num).toDouble(),
      refundOwed: (json['refund_owed'] as num).toDouble(),
      deductions: (json['deductions'] as List<dynamic>?)
          ?.map((e) => TaxDeduction.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      credits: (json['credits'] as List<dynamic>?)
          ?.map((e) => TaxCredit.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      state: json['state'] as String?,
      calculatedAt: DateTime.parse(json['calculated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tax_year': taxYear,
      'gross_income': grossIncome,
      'adjusted_gross_income': adjustedGrossIncome,
      'filing_status': filingStatus.name,
      'standard_deduction': standardDeduction,
      'itemized_deductions': itemizedDeductions,
      'total_deductions': totalDeductions,
      'federal_tax': federalTax,
      'state_tax': stateTax,
      'total_credits': totalCredits,
      'total_tax': totalTax,
      'effective_tax_rate': effectiveTaxRate,
      'marginal_tax_rate': marginalTaxRate,
      'refund_owed': refundOwed,
      'deductions': deductions.map((e) => e.toJson()).toList(),
      'credits': credits.map((e) => e.toJson()).toList(),
      'state': state,
      'calculated_at': calculatedAt.toIso8601String(),
    };
  }
}

// Tax Deduction Model
class TaxDeduction {
  final String id;
  final String category;
  final String description;
  final double amount;
  final DateTime date;
  final int taxYear;
  final String? receiptPath;
  final bool isVerified;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;

  const TaxDeduction({
    required this.id,
    required this.category,
    required this.description,
    required this.amount,
    required this.date,
    required this.taxYear,
    this.receiptPath,
    required this.isVerified,
    required this.metadata,
    required this.createdAt,
  });

  factory TaxDeduction.fromJson(Map<String, dynamic> json) {
    return TaxDeduction(
      id: json['id'] as String,
      category: json['category'] as String,
      description: json['description'] as String,
      amount: (json['amount'] as num).toDouble(),
      date: DateTime.parse(json['date'] as String),
      taxYear: json['tax_year'] as int,
      receiptPath: json['receipt_path'] as String?,
      isVerified: json['is_verified'] as bool? ?? false,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category': category,
      'description': description,
      'amount': amount,
      'date': date.toIso8601String(),
      'tax_year': taxYear,
      'receipt_path': receiptPath,
      'is_verified': isVerified,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Tax Credit Model
class TaxCredit {
  final String id;
  final String name;
  final double amount;
  final TaxCreditType type;
  final String description;
  final Map<String, dynamic> metadata;

  const TaxCredit({
    required this.id,
    required this.name,
    required this.amount,
    required this.type,
    required this.description,
    required this.metadata,
  });

  factory TaxCredit.fromJson(Map<String, dynamic> json) {
    return TaxCredit(
      id: json['id'] as String,
      name: json['name'] as String,
      amount: (json['amount'] as num).toDouble(),
      type: TaxCreditType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => TaxCreditType.nonRefundable,
      ),
      description: json['description'] as String,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'amount': amount,
      'type': type.name,
      'description': description,
      'metadata': metadata,
    };
  }
}

// Tax Document Model
class TaxDocument {
  final String id;
  final String name;
  final TaxDocumentType type;
  final String filePath;
  final int taxYear;
  final String description;
  final int fileSize;
  final DateTime uploadedAt;
  final Map<String, dynamic> metadata;

  const TaxDocument({
    required this.id,
    required this.name,
    required this.type,
    required this.filePath,
    required this.taxYear,
    required this.description,
    required this.fileSize,
    required this.uploadedAt,
    required this.metadata,
  });

  factory TaxDocument.fromJson(Map<String, dynamic> json) {
    return TaxDocument(
      id: json['id'] as String,
      name: json['name'] as String,
      type: TaxDocumentType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => TaxDocumentType.other,
      ),
      filePath: json['file_path'] as String,
      taxYear: json['tax_year'] as int,
      description: json['description'] as String,
      fileSize: json['file_size'] as int,
      uploadedAt: DateTime.parse(json['uploaded_at'] as String),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'file_path': filePath,
      'tax_year': taxYear,
      'description': description,
      'file_size': fileSize,
      'uploaded_at': uploadedAt.toIso8601String(),
      'metadata': metadata,
    };
  }
}

// Tax Projection Model
class TaxProjection {
  final double projectedIncome;
  final double projectedTax;
  final double effectiveTaxRate;
  final double marginalTaxRate;
  final List<double> quarterlyEstimates;
  final List<TaxStrategy> optimizationStrategies;
  final DateTime projectedAt;

  const TaxProjection({
    required this.projectedIncome,
    required this.projectedTax,
    required this.effectiveTaxRate,
    required this.marginalTaxRate,
    required this.quarterlyEstimates,
    required this.optimizationStrategies,
    required this.projectedAt,
  });
}

// Tax Strategy Model
class TaxStrategy {
  final String name;
  final String description;
  final double potentialSavings;
  final String category;
  final TaxStrategyPriority priority;

  const TaxStrategy({
    required this.name,
    required this.description,
    required this.potentialSavings,
    required this.category,
    required this.priority,
  });
}

// Tax Bracket Model
class TaxBracket {
  final double lowerLimit;
  final double upperLimit;
  final double rate;

  const TaxBracket(this.lowerLimit, this.upperLimit, this.rate);
}

// Expense Analytics Model
class ExpenseAnalytics {
  final DateTime startDate;
  final DateTime endDate;
  final double totalExpenses;
  final double averageDaily;
  final double averageMonthly;
  final Map<String, double> categoryBreakdown;
  final Map<String, double> trends;
  final List<dynamic> taxDeductibleExpenses; // Using dynamic to avoid circular import
  final Map<String, dynamic> spendingPatterns;
  final List<String> recommendations;
  final DateTime analyzedAt;

  const ExpenseAnalytics({
    required this.startDate,
    required this.endDate,
    required this.totalExpenses,
    required this.averageDaily,
    required this.averageMonthly,
    required this.categoryBreakdown,
    required this.trends,
    required this.taxDeductibleExpenses,
    required this.spendingPatterns,
    required this.recommendations,
    required this.analyzedAt,
  });
}

// Enums
enum FilingStatus {
  single,
  marriedFilingJointly,
  marriedFilingSeparately,
  headOfHousehold,
}

enum TaxCreditType {
  refundable,
  nonRefundable,
  partiallyRefundable,
}

enum TaxDocumentType {
  w2,
  w4,
  form1099,
  form1098,
  receipt,
  invoice,
  bankStatement,
  investmentStatement,
  other,
}

enum TaxStrategyPriority {
  low,
  medium,
  high,
  critical,
}
