import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/media_providers.dart';
import 'video_tab_screen.dart';
import 'audio_tab_screen.dart';
import 'playlist_tab_screen.dart';
import 'settings_tab_screen.dart';

/// Main home screen for Shadow Player application
class ShadowPlayerHome extends ConsumerStatefulWidget {
  const ShadowPlayerHome({super.key});

  @override
  ConsumerState<ShadowPlayerHome> createState() => _ShadowPlayerHomeState();
}

class _ShadowPlayerHomeState extends ConsumerState<ShadowPlayerHome>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Shadow Player'),
        centerTitle: true,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.video_library), text: 'Videos'),
            Tab(icon: Icon(Icons.music_note), text: 'Music'),
            Tab(icon: Icon(Icons.playlist_play), text: 'Playlists'),
            Tab(icon: Icon(Icons.settings), text: 'Settings'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearch(),
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'scan',
                child: ListTile(
                  leading: Icon(Icons.refresh),
                  title: Text('Scan for Media'),
                ),
              ),
              const PopupMenuItem(
                value: 'import',
                child: ListTile(
                  leading: Icon(Icons.folder_open),
                  title: Text('Import Folder'),
                ),
              ),
              const PopupMenuItem(
                value: 'about',
                child: ListTile(
                  leading: Icon(Icons.info),
                  title: Text('About'),
                ),
              ),
            ],
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          VideoTabScreen(),
          AudioTabScreen(),
          PlaylistTabScreen(),
          SettingsTabScreen(),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget? _buildFloatingActionButton() {
    final currentIndex = _tabController.index;
    
    switch (currentIndex) {
      case 0: // Videos
      case 1: // Music
        return FloatingActionButton(
          onPressed: () => _scanForMedia(),
          tooltip: 'Scan for Media',
          child: const Icon(Icons.refresh),
        );
      case 2: // Playlists
        return FloatingActionButton(
          onPressed: () => _createPlaylist(),
          tooltip: 'Create Playlist',
          child: const Icon(Icons.add),
        );
      default:
        return null;
    }
  }

  void _showSearch() {
    showSearch(
      context: context,
      delegate: MediaSearchDelegate(),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'scan':
        _scanForMedia();
        break;
      case 'import':
        _importFolder();
        break;
      case 'about':
        _showAbout();
        break;
    }
  }

  void _scanForMedia() {
    ref.read(mediaLibraryProvider.notifier).scanForMedia();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Scanning for media files...')),
    );
  }

  void _importFolder() {
    // TODO: Implement folder import
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Folder import coming soon')),
    );
  }

  void _createPlaylist() {
    // TODO: Implement playlist creation
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Playlist creation coming soon')),
    );
  }

  void _showAbout() {
    showAboutDialog(
      context: context,
      applicationName: 'Shadow Player',
      applicationVersion: '1.0.0',
      applicationIcon: const Icon(Icons.play_circle_filled, size: 64),
      children: const [
        Text('A powerful media player for videos and music.'),
        SizedBox(height: 16),
        Text('Features:'),
        Text('• Video and audio playback'),
        Text('• Playlist management'),
        Text('• Media library organization'),
        Text('• Advanced playback controls'),
      ],
    );
  }
}

/// Search delegate for media files
class MediaSearchDelegate extends SearchDelegate<String> {
  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, '');
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final mediaFiles = ref.watch(mediaLibraryProvider);
        final filteredFiles = mediaFiles
            .where((file) =>
                file.displayName.toLowerCase().contains(query.toLowerCase()) ||
                (file.metadata.title?.toLowerCase().contains(query.toLowerCase()) ?? false) ||
                (file.metadata.artist?.toLowerCase().contains(query.toLowerCase()) ?? false))
            .toList();

        if (filteredFiles.isEmpty) {
          return const Center(
            child: Text('No media files found'),
          );
        }

        return ListView.builder(
          itemCount: filteredFiles.length,
          itemBuilder: (context, index) {
            final file = filteredFiles[index];
            return ListTile(
              leading: Icon(
                file.type.name == 'video' ? Icons.video_file : Icons.audio_file,
              ),
              title: Text(file.displayName),
              subtitle: Text(file.metadata.artist ?? 'Unknown Artist'),
              onTap: () {
                close(context, file.id);
                // TODO: Play the selected file
              },
            );
          },
        );
      },
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    if (query.isEmpty) {
      return const Center(
        child: Text('Enter a search term'),
      );
    }

    return buildResults(context);
  }
}
