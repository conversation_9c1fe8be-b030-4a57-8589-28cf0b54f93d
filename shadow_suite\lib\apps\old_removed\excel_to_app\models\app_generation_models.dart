import 'package:flutter/material.dart';
import '../services/app_generation_service.dart' show AppType;

/// Navigation system types
enum NavigationSystem { sidebar, topTabs, bottomTabs, drawer, rail }

/// Grid layout types
enum GridLayoutType { fixed, responsive, masonry, staggered }

/// App color schemes
enum AppColorScheme { light, dark, auto, custom }

/// Layout configuration for generated apps
class LayoutConfiguration {
  final NavigationSystem navigationSystem;
  final GridLayoutType gridLayout;
  final AppColorScheme colorScheme;
  final Map<String, dynamic> customProperties;

  const LayoutConfiguration({
    this.navigationSystem = NavigationSystem.bottomTabs,
    this.gridLayout = GridLayoutType.responsive,
    this.colorScheme = AppColorScheme.light,
    this.customProperties = const {},
  });

  LayoutConfiguration copyWith({
    NavigationSystem? navigationSystem,
    GridLayoutType? gridLayout,
    AppColorScheme? colorScheme,
    Map<String, dynamic>? customProperties,
  }) {
    return LayoutConfiguration(
      navigationSystem: navigationSystem ?? this.navigationSystem,
      gridLayout: gridLayout ?? this.gridLayout,
      colorScheme: colorScheme ?? this.colorScheme,
      customProperties: customProperties ?? this.customProperties,
    );
  }
}

/// App navigation types
enum NavigationType { sidebar, bottomTabs, topTabs, drawer }

/// Navigation item model
class NavigationItem {
  final String id;
  final String label;
  final IconData icon;
  final String route;

  const NavigationItem({
    required this.id,
    required this.label,
    required this.icon,
    required this.route,
  });
}

/// App navigation configuration
class AppNavigation {
  final NavigationType type;
  final List<NavigationItem> items;

  const AppNavigation({required this.type, required this.items});
}

/// App screen model
class AppScreen {
  final String id;
  final String title;
  final String route;
  final IconData icon;
  final Widget content;

  const AppScreen({
    required this.id,
    required this.title,
    required this.route,
    required this.icon,
    required this.content,
  });
}

/// Deployment targets
enum DeploymentTarget { web, android, ios, desktop, pwa }

/// Export formats
enum ExportFormat { flutter, react, vue, angular, html }

/// Generated app model
class GeneratedApp {
  final String id;
  final String name;
  final AppType type;
  final List<AppScreen> screens;
  final AppNavigation navigation;
  final LayoutConfiguration layout;
  final DateTime createdAt;

  const GeneratedApp({
    required this.id,
    required this.name,
    required this.type,
    required this.screens,
    required this.navigation,
    required this.layout,
    required this.createdAt,
  });
}

/// Deployment result model
class DeploymentResult {
  final bool success;
  final String? error;
  final String? deploymentUrl;
  final Duration deploymentTime;

  const DeploymentResult({
    required this.success,
    this.error,
    this.deploymentUrl,
    required this.deploymentTime,
  });
}

/// Export result model
class ExportResult {
  final bool success;
  final String? error;
  final String? exportPath;
  final int fileCount;
  final Duration exportTime;

  const ExportResult({
    required this.success,
    this.error,
    this.exportPath,
    required this.fileCount,
    required this.exportTime,
  });
}

/// Data analysis result model
class DataAnalysisResult {
  final List<String> columnNames;
  final List<String> dataTypes;
  final int rowCount;
  final Map<String, dynamic> statistics;

  const DataAnalysisResult({
    required this.columnNames,
    required this.dataTypes,
    required this.rowCount,
    required this.statistics,
  });
}

/// App configuration model
class AppConfiguration {
  final AppType appType;
  final LayoutConfiguration layoutConfig;
  final Map<String, dynamic> customSettings;

  const AppConfiguration({
    required this.appType,
    required this.layoutConfig,
    this.customSettings = const {},
  });
}
