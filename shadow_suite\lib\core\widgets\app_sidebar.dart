import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/app_navigation.dart';
import '../theme/app_theme.dart';
import '../services/app_permissions_manager.dart';
import '../services/route_tracker_service.dart';

final sidebarExpandedProvider = StateProvider<Map<AppSection, bool>>(
  (ref) => {},
);
final selectedSectionProvider = StateProvider<AppSection>(
  (ref) => AppSection.dashboard,
);

class AppSidebar extends ConsumerWidget {
  final bool isCollapsed;
  final VoidCallback? onToggle;
  final bool isMobile;

  const AppSidebar({
    super.key,
    this.isCollapsed = false,
    this.onToggle,
    this.isMobile = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final expandedSections = ref.watch(sidebarExpandedProvider);
    final selectedSection = ref.watch(selectedSectionProvider);

    return Container(
      width: isCollapsed ? 70 : 280,
      decoration: const BoxDecoration(
        color: AppTheme.sidebarColor,
        boxShadow: [
          BoxShadow(color: Colors.black12, blurRadius: 4, offset: Offset(2, 0)),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(context),
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(vertical: 8),
              children: [
                _buildSectionTitle('Main', context),
                ...AppNavigation.mainItems.map(
                  (item) => _buildNavigationItem(
                    context,
                    ref,
                    item,
                    selectedSection,
                    expandedSections,
                  ),
                ),
                const SizedBox(height: 16),
                _buildSectionTitle('Apps', context),
                ...AppNavigation.appItems.map(
                  (item) => _buildNavigationItem(
                    context,
                    ref,
                    item,
                    selectedSection,
                    expandedSections,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      height: 80,
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: AppTheme.sidebarSelectedColor,
        border: Border(bottom: BorderSide(color: Colors.white12)),
      ),
      child: Row(
        children: [
          const Icon(Icons.apps, color: AppTheme.sidebarTextColor, size: 32),
          if (!isCollapsed) ...[
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'ShadowSuite',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: AppTheme.sidebarTextColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
          if (onToggle != null)
            IconButton(
              onPressed: onToggle,
              icon: Icon(
                isCollapsed ? Icons.menu_open : Icons.menu,
                color: AppTheme.sidebarTextColor,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title, BuildContext context) {
    if (isCollapsed) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title.toUpperCase(),
        style: Theme.of(context).textTheme.labelSmall?.copyWith(
          color: AppTheme.sidebarTextColor.withValues(alpha: 0.7),
          fontWeight: FontWeight.w600,
          letterSpacing: 1.2,
        ),
      ),
    );
  }

  Widget _buildNavigationItem(
    BuildContext context,
    WidgetRef ref,
    NavigationItem item,
    AppSection selectedSection,
    Map<AppSection, bool> expandedSections,
  ) {
    final routeInfo = ref.watch(routeInfoProvider);
    final isSelected = routeInfo.isSectionActive(item.section);
    final isExpanded = expandedSections[item.section] ?? false;

    return Column(
      children: [
        Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () async {
              ref.read(selectedSectionProvider.notifier).state = item.section;

              if (item.isExpandable) {
                final currentExpanded = ref.read(sidebarExpandedProvider);
                ref.read(sidebarExpandedProvider.notifier).state = {
                  ...currentExpanded,
                  item.section: !isExpanded,
                };
              } else {
                // Check permissions before navigation for file-dependent apps
                final appName = _getAppNameFromSection(item.section);
                if (appName != null) {
                  final hasPermissions =
                      await AppPermissionsManager.ensureAppPermissions(
                        context,
                        appName,
                      );
                  if (!hasPermissions) {
                    // Show permission denied message but don't navigate
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            '${item.title} requires additional permissions to function properly.',
                          ),
                          action: SnackBarAction(
                            label: 'Grant',
                            onPressed: () =>
                                AppPermissionsManager.ensureAppPermissions(
                                  context,
                                  appName,
                                ),
                          ),
                        ),
                      );
                    }
                    return;
                  }
                }

                // Navigate to the main section route
                final route = _getSectionRoute(item.section);
                if (context.mounted) {
                  context.go(route);
                  // Update route tracking
                  ref.read(currentRouteProvider.notifier).state = route;
                  RouteTrackerService.updateRoute(route);

                  // Auto-close drawer on mobile after navigation
                  if (isMobile && Navigator.of(context).canPop()) {
                    Navigator.of(context).pop();
                  }
                }
              }
            },
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: isSelected ? AppTheme.sidebarSelectedColor : null,
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListTile(
                leading: Icon(
                  item.icon,
                  color: item.color ?? AppTheme.sidebarTextColor,
                  size: 24,
                ),
                title: isCollapsed
                    ? null
                    : Text(
                        item.title,
                        style: TextStyle(
                          color: AppTheme.sidebarTextColor,
                          fontWeight: isSelected
                              ? FontWeight.w600
                              : FontWeight.normal,
                        ),
                      ),
                trailing: isCollapsed
                    ? null
                    : (item.isExpandable
                          ? Icon(
                              isExpanded
                                  ? Icons.expand_less
                                  : Icons.expand_more,
                              color: AppTheme.sidebarTextColor,
                            )
                          : null),
                dense:
                    !isMobile, // Use full height on mobile for better touch targets
                contentPadding: EdgeInsets.symmetric(
                  horizontal: isCollapsed ? 8 : 16,
                  vertical: isMobile ? 8 : 4, // Larger touch area on mobile
                ),
                minVerticalPadding: isMobile
                    ? 12
                    : 8, // Ensure minimum touch target
              ),
            ),
          ),
        ),
        if (item.isExpandable && isExpanded && !isCollapsed)
          ...item.subItems!.map(
            (subItem) => _buildSubNavigationItem(context, ref, subItem),
          ),
      ],
    );
  }

  Widget _buildSubNavigationItem(
    BuildContext context,
    WidgetRef ref,
    SubNavigationItem subItem,
  ) {
    final routeInfo = ref.watch(routeInfoProvider);
    final isActive = routeInfo.isRouteActive(subItem.route);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          context.go(subItem.route);
          // Update route tracking
          ref.read(currentRouteProvider.notifier).state = subItem.route;
          RouteTrackerService.updateRoute(subItem.route);

          // Auto-close drawer on mobile after navigation
          if (isMobile && Navigator.of(context).canPop()) {
            Navigator.of(context).pop();
          }
        },
        child: Container(
          margin: const EdgeInsets.only(left: 24, right: 8, top: 2, bottom: 2),
          decoration: BoxDecoration(
            color: isActive
                ? AppTheme.sidebarSelectedColor.withValues(alpha: 0.5)
                : null,
            borderRadius: BorderRadius.circular(6),
          ),
          child: ListTile(
            leading: Icon(
              subItem.icon,
              color: isActive
                  ? AppTheme.sidebarTextColor
                  : AppTheme.sidebarTextColor.withValues(alpha: 0.8),
              size: 20,
            ),
            title: Text(
              subItem.title,
              style: TextStyle(
                color: isActive
                    ? AppTheme.sidebarTextColor
                    : AppTheme.sidebarTextColor.withValues(alpha: 0.8),
                fontSize: 14,
                fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
            dense:
                !isMobile, // Use full height on mobile for better touch targets
            contentPadding: EdgeInsets.symmetric(
              horizontal: 16,
              vertical: isMobile ? 6 : 2, // Larger touch area on mobile
            ),
            minVerticalPadding: isMobile ? 8 : 4, // Ensure minimum touch target
          ),
        ),
      ),
    );
  }

  String _getSectionRoute(AppSection section) {
    switch (section) {
      case AppSection.dashboard:
        return '/dashboard';
      case AppSection.moneyManager:
        return '/money-manager';
      case AppSection.fileManager:
        return '/file-manager';
      case AppSection.excelToApp:
        return '/excel-to-app';
      case AppSection.islamicApp:
        return '/islamic-app';
      case AppSection.memoSuite:
        return '/memo-suite';
      case AppSection.shadowPlayer:
        return '/shadow-player';
      case AppSection.smartGallery:
        return '/smart-gallery';
      case AppSection.settings:
        return '/settings';
      case AppSection.profile:
        return '/profile';
    }
  }

  String? _getAppNameFromSection(AppSection section) {
    switch (section) {
      case AppSection.fileManager:
        return 'file_manager';
      case AppSection.smartGallery:
        return 'smartgallery';
      case AppSection.shadowPlayer:
        return 'shadow_player';
      case AppSection.memoSuite:
        return 'memo_suite';
      case AppSection.islamicApp:
        return 'islamic_app';
      case AppSection.moneyManager:
      case AppSection.excelToApp:
      case AppSection.dashboard:
      case AppSection.settings:
      case AppSection.profile:
        return null; // These apps don't require special permissions
    }
  }
}
