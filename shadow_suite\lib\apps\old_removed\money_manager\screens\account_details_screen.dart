import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/money_manager_models.dart';
import '../services/money_manager_providers.dart';
import '../widgets/add_transaction_dialog.dart';
import '../widgets/add_account_dialog.dart';

class AccountDetailsScreen extends ConsumerStatefulWidget {
  final Account account;

  const AccountDetailsScreen({super.key, required this.account});

  @override
  ConsumerState<AccountDetailsScreen> createState() => _AccountDetailsScreenState();
}

class _AccountDetailsScreenState extends ConsumerState<AccountDetailsScreen> {
  String _selectedPeriod = 'All Time';
  final List<String> _periods = ['This Week', 'This Month', 'This Quarter', 'This Year', 'All Time'];

  @override
  Widget build(BuildContext context) {
    final transactionsAsync = ref.watch(transactionsProvider);
    final categoriesAsync = ref.watch(categoriesProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.account.name),
        backgroundColor: Color(int.parse(widget.account.color.replaceFirst('#', '0xFF'))),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () => _showEditAccountDialog(context),
            icon: const Icon(Icons.edit),
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'transactions', child: Text('View All Transactions')),
              const PopupMenuItem(value: 'transfer', child: Text('Transfer Money')),
              const PopupMenuItem(value: 'delete', child: Text('Delete Account')),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          _buildAccountSummary(),
          _buildPeriodSelector(),
          Expanded(
            child: transactionsAsync.when(
              data: (transactions) => categoriesAsync.when(
                data: (categories) => _buildTransactionsList(
                  _filterTransactionsByAccount(transactions),
                  categories,
                ),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => _buildErrorState(error.toString()),
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _buildErrorState(error.toString()),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddTransactionDialog(context),
        backgroundColor: Color(int.parse(widget.account.color.replaceFirst('#', '0xFF'))),
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildAccountSummary() {
    final isNegative = widget.account.currentBalance < 0;
    
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(int.parse(widget.account.color.replaceFirst('#', '0xFF'))),
            Color(int.parse(widget.account.color.replaceFirst('#', '0xFF'))).withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  _getAccountIcon(widget.account.type),
                  color: Colors.white,
                  size: 32,
                ),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.account.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      widget.account.type.name.toUpperCase(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: _buildBalanceCard(
                  'Current Balance',
                  '${isNegative ? '-' : ''}\$${widget.account.currentBalance.abs().toStringAsFixed(2)}',
                  isNegative ? Icons.trending_down : Icons.account_balance_wallet,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildBalanceCard(
                  'Initial Balance',
                  '\$${widget.account.initialBalance.toStringAsFixed(2)}',
                  Icons.savings,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceCard(String title, String amount, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: Colors.white, size: 24),
          const SizedBox(height: 8),
          Text(
            amount,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE9ECEF)),
        ),
      ),
      child: Row(
        children: [
          const Text(
            'Period:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: _periods.map((period) {
                  final isSelected = _selectedPeriod == period;
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: GestureDetector(
                      onTap: () => setState(() => _selectedPeriod = period),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: isSelected 
                              ? Color(int.parse(widget.account.color.replaceFirst('#', '0xFF')))
                              : Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: isSelected 
                                ? Color(int.parse(widget.account.color.replaceFirst('#', '0xFF')))
                                : Colors.grey.shade300,
                          ),
                        ),
                        child: Text(
                          period,
                          style: TextStyle(
                            color: isSelected ? Colors.white : const Color(0xFF7F8C8D),
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Transaction> _filterTransactionsByAccount(List<Transaction> transactions) {
    final accountTransactions = transactions.where((transaction) => 
      transaction.accountId == widget.account.id || 
      transaction.toAccountId == widget.account.id
    ).toList();

    if (_selectedPeriod == 'All Time') {
      return accountTransactions;
    }

    final now = DateTime.now();
    DateTime startDate;

    switch (_selectedPeriod) {
      case 'This Week':
        startDate = now.subtract(Duration(days: now.weekday - 1));
        break;
      case 'This Month':
        startDate = DateTime(now.year, now.month, 1);
        break;
      case 'This Quarter':
        final quarterStart = ((now.month - 1) ~/ 3) * 3 + 1;
        startDate = DateTime(now.year, quarterStart, 1);
        break;
      case 'This Year':
        startDate = DateTime(now.year, 1, 1);
        break;
      default:
        return accountTransactions;
    }

    return accountTransactions.where((transaction) => 
      transaction.date.isAfter(startDate.subtract(const Duration(days: 1)))
    ).toList();
  }

  Widget _buildTransactionsList(List<Transaction> transactions, List<Category> categories) {
    if (transactions.isEmpty) {
      return _buildEmptyTransactions();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: transactions.length,
      itemBuilder: (context, index) {
        final transaction = transactions[index];
        final category = categories.firstWhere(
          (cat) => cat.id == transaction.categoryId,
          orElse: () => Category(
            id: '',
            name: 'Unknown Category',
            type: CategoryType.expense,
            createdAt: DateTime.now(),
          ),
        );
        
        return _buildTransactionTile(transaction, category);
      },
    );
  }

  Widget _buildEmptyTransactions() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long,
            size: 64,
            color: Color(0xFFBDC3C7),
          ),
          SizedBox(height: 16),
          Text(
            'No Transactions',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF7F8C8D),
            ),
          ),
          SizedBox(height: 8),
          Text(
            'No transactions found for this account in the selected period',
            style: TextStyle(
              color: Color(0xFF95A5A6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionTile(Transaction transaction, Category category) {
    final isIncoming = transaction.accountId == widget.account.id && transaction.type == TransactionType.income;
    final isOutgoing = transaction.accountId == widget.account.id && transaction.type == TransactionType.expense;
    final isTransferIn = transaction.toAccountId == widget.account.id && transaction.type == TransactionType.transfer;
    final isTransferOut = transaction.accountId == widget.account.id && transaction.type == TransactionType.transfer;

    Color transactionColor;
    IconData transactionIcon;
    String prefix;

    if (isIncoming || isTransferIn) {
      transactionColor = const Color(0xFF27AE60);
      transactionIcon = Icons.arrow_downward;
      prefix = '+';
    } else if (isOutgoing || isTransferOut) {
      transactionColor = const Color(0xFFE74C3C);
      transactionIcon = Icons.arrow_upward;
      prefix = '-';
    } else {
      transactionColor = const Color(0xFF3498DB);
      transactionIcon = Icons.swap_horiz;
      prefix = '';
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: transactionColor.withValues(alpha: 0.1),
          child: Icon(
            transactionIcon,
            color: transactionColor,
            size: 20,
          ),
        ),
        title: Text(
          transaction.description.isNotEmpty ? transaction.description : category.name,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(category.name),
            Text(
              _formatDate(transaction.date),
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
        trailing: Text(
          '$prefix\$${transaction.amount.toStringAsFixed(2)}',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: transactionColor,
            fontSize: 16,
          ),
        ),
        onTap: () => _showTransactionDetails(transaction, category),
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Color(0xFFE74C3C),
          ),
          const SizedBox(height: 16),
          const Text(
            'Error Loading Data',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFFE74C3C),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: const TextStyle(
              color: Color(0xFF95A5A6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Helper methods
  IconData _getAccountIcon(AccountType type) {
    switch (type) {
      case AccountType.checking:
        return Icons.account_balance;
      case AccountType.savings:
        return Icons.savings;
      case AccountType.credit:
        return Icons.credit_card;
      case AccountType.cash:
        return Icons.account_balance_wallet;
      case AccountType.investment:
        return Icons.trending_up;
      case AccountType.loan:
        return Icons.money_off;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.month}/${date.day}/${date.year}';
    }
  }

  // Action methods
  void _showAddTransactionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AddTransactionDialog(
        preselectedAccountId: widget.account.id,
      ),
    );
  }

  void _showEditAccountDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AddAccountDialog(account: widget.account),
    );
  }

  void _showTransactionDetails(Transaction transaction, Category category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Transaction Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Description', transaction.description.isNotEmpty ? transaction.description : category.name),
            _buildDetailRow('Amount', '\$${transaction.amount.toStringAsFixed(2)}'),
            _buildDetailRow('Type', transaction.type.name.toUpperCase()),
            _buildDetailRow('Category', category.name),
            _buildDetailRow('Date', '${transaction.date.month}/${transaction.date.day}/${transaction.date.year}'),
            if (transaction.notes != null && transaction.notes!.isNotEmpty)
              _buildDetailRow('Notes', transaction.notes!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              showDialog(
                context: context,
                builder: (context) => AddTransactionDialog(transaction: transaction),
              );
            },
            child: const Text('Edit'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'transactions':
        // Already showing transactions
        break;
      case 'transfer':
        _showTransferDialog();
        break;
      case 'delete':
        _showDeleteAccountDialog();
        break;
    }
  }

  void _showTransferDialog() {
    showDialog(
      context: context,
      builder: (context) => AddTransactionDialog(
        preselectedAccountId: widget.account.id,
        preselectedType: TransactionType.transfer,
      ),
    );
  }

  void _showDeleteAccountDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account'),
        content: Text('Are you sure you want to delete "${widget.account.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(accountsProvider.notifier).deleteAccount(widget.account.id);
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Go back to accounts screen

              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Account deleted successfully'),
                  backgroundColor: Color(0xFFE74C3C),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFE74C3C),
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
