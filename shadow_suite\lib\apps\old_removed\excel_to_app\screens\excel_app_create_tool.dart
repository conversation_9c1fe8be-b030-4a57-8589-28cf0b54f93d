import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/excel_app_providers.dart';
import '../services/excel_file_service.dart';
import '../models/excel_app_tool.dart';
import '../widgets/excel_spreadsheet_editor.dart';
import '../widgets/excel_ui_builder.dart';
import '../widgets/tool_runtime_preview.dart';
import 'excel_to_app_main.dart';

class ExcelAppCreateTool extends ConsumerStatefulWidget {
  const ExcelAppCreateTool({super.key});

  @override
  ConsumerState<ExcelAppCreateTool> createState() => _ExcelAppCreateToolState();
}

class _ExcelAppCreateToolState extends ConsumerState<ExcelAppCreateTool>
    with TickerProviderStateMixin {
  late TabController _tabController;
  // int _currentTabIndex = 0; // Reserved for future tab management

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {
          // _currentTabIndex = _tabController.index; // Reserved for future tab management
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentTool = ref.watch(currentExcelAppToolProvider);

    if (currentTool == null) {
      return Scaffold(
        backgroundColor: Colors.transparent,
        body: Column(
          children: [
            ExcelToAppHeader(
              title: 'Tool Editor',
              subtitle: 'No tool selected',
              leading: IconButton(
                onPressed: () {
                  ref.read(excelToAppCurrentScreenProvider.notifier).state =
                      ExcelToAppScreen.dashboard;
                },
                icon: const Icon(Icons.arrow_back),
              ),
            ),
            const Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Color(0xFFBDC3C7),
                    ),
                    SizedBox(height: 16),
                    Text(
                      'No Tool Selected',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2C3E50),
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Please select a tool from the dashboard to edit',
                      style: TextStyle(color: Color(0xFF7F8C8D)),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Column(
        children: [
          _buildHeader(context, ref, currentTool),
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                ExcelSpreadsheetEditor(tool: currentTool),
                ExcelUIBuilder(tool: currentTool),
                _buildPreviewTab(currentTool),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, WidgetRef ref, currentTool) {
    return ExcelToAppHeader(
      title: currentTool.name,
      subtitle: 'Editing Excel-to-App tool',
      leading: IconButton(
        onPressed: () {
          ref.read(excelToAppCurrentScreenProvider.notifier).state =
              ExcelToAppScreen.dashboard;
        },
        icon: const Icon(Icons.arrow_back),
      ),
      actions: [
        IconButton(
          onPressed: () => _saveTool(ref),
          icon: const Icon(Icons.save),
          tooltip: 'Save Tool',
        ),
        const SizedBox(width: 8),
        PopupMenuButton<String>(
          icon: const Icon(Icons.import_export),
          tooltip: 'Import/Export',
          onSelected: (value) => _handleFileOperation(value, ref),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'import_excel',
              child: Row(
                children: [
                  Icon(Icons.file_upload, size: 18),
                  SizedBox(width: 8),
                  Text('Import Excel File'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'export_excel',
              child: Row(
                children: [
                  Icon(Icons.file_download, size: 18),
                  SizedBox(width: 8),
                  Text('Export as Excel'),
                ],
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'export_template',
              child: Row(
                children: [
                  Icon(Icons.bookmark_add, size: 18),
                  SizedBox(width: 8),
                  Text('Save as Template'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'load_template',
              child: Row(
                children: [
                  Icon(Icons.bookmark, size: 18),
                  SizedBox(width: 8),
                  Text('Load from Template'),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(width: 8),
        IconButton(
          onPressed: () => _previewTool(ref),
          icon: const Icon(Icons.play_arrow),
          tooltip: 'Preview Tool',
        ),
        const SizedBox(width: 8),
        IconButton(
          onPressed: () => _showToolSettings(context, ref),
          icon: const Icon(Icons.settings),
          tooltip: 'Tool Settings',
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: const Color(0xFFF8F9FA),
      child: TabBar(
        controller: _tabController,
        indicatorColor: const Color(0xFF3498DB),
        labelColor: const Color(0xFF3498DB),
        unselectedLabelColor: const Color(0xFF7F8C8D),
        tabs: const [
          Tab(icon: Icon(Icons.table_view), text: 'Spreadsheet'),
          Tab(icon: Icon(Icons.widgets), text: 'UI Builder'),
          Tab(icon: Icon(Icons.preview), text: 'Preview'),
        ],
      ),
    );
  }

  Widget _buildPreviewTab(currentTool) {
    if (currentTool.uiComponents.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.widgets, size: 64, color: Color(0xFFBDC3C7)),
            SizedBox(height: 16),
            Text(
              'No UI Components',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2C3E50),
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Add some UI components in the UI Builder tab to see the preview',
              style: TextStyle(color: Color(0xFF7F8C8D)),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ToolRuntimePreview(tool: currentTool);
  }

  void _saveTool(WidgetRef ref) async {
    final currentTool = ref.read(currentExcelAppToolProvider);
    if (currentTool != null) {
      try {
        await ref.read(excelAppToolsProvider.notifier).saveTool(currentTool);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Tool saved successfully'),
              backgroundColor: Color(0xFF27AE60),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to save tool: $e'),
              backgroundColor: const Color(0xFFE74C3C),
            ),
          );
        }
      }
    }
  }

  void _previewTool(WidgetRef ref) {
    final currentTool = ref.read(currentExcelAppToolProvider);
    if (currentTool != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ToolRuntimePreview(tool: currentTool),
          fullscreenDialog: true,
        ),
      );
    }
  }

  void _handleFileOperation(String operation, WidgetRef ref) async {
    final currentTool = ref.read(currentExcelAppToolProvider);
    if (currentTool == null) return;

    switch (operation) {
      case 'import_excel':
        await _importExcelFile(ref);
        break;
      case 'export_excel':
        await _exportAsExcel(currentTool);
        break;
      case 'export_template':
        await _exportAsTemplate(currentTool);
        break;
      case 'load_template':
        await _loadFromTemplate(ref);
        break;
    }
  }

  Future<void> _importExcelFile(WidgetRef ref) async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      final result = await ExcelFileService.importExcelFile();

      // Hide loading indicator
      if (mounted) Navigator.of(context).pop();

      if (result.isCancelled) {
        return;
      }

      if (!result.isSuccess) {
        _showErrorDialog(
          'Import Failed',
          result.error ?? 'Unknown error occurred',
        );
        return;
      }

      // Update the current tool with imported spreadsheet data
      final currentTool = ref.read(currentExcelAppToolProvider);
      if (currentTool != null && result.spreadsheet != null) {
        final updatedTool = currentTool.copyWith(
          spreadsheet: result.spreadsheet!,
        );
        ref.read(currentExcelAppToolProvider.notifier).setTool(updatedTool);

        _showSuccessDialog(
          'Import Successful',
          'Excel file "${result.fileName}" imported successfully',
        );
      }
    } catch (e) {
      if (mounted) Navigator.of(context).pop();
      _showErrorDialog('Import Error', 'Failed to import Excel file: $e');
    }
  }

  Future<void> _exportAsExcel(ExcelAppTool tool) async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      final result = await ExcelFileService.exportToolAsExcel(tool);

      // Hide loading indicator
      if (mounted) Navigator.of(context).pop();

      if (result.isSuccess) {
        _showSuccessDialog(
          'Export Successful',
          'Tool exported as "${result.fileName}"',
        );
      } else {
        _showErrorDialog(
          'Export Failed',
          result.error ?? 'Unknown error occurred',
        );
      }
    } catch (e) {
      if (mounted) Navigator.of(context).pop();
      _showErrorDialog('Export Error', 'Failed to export Excel file: $e');
    }
  }

  Future<void> _exportAsTemplate(ExcelAppTool tool) async {
    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => _buildTemplateDialog(),
    );

    if (result == null) return;

    try {
      // Show loading indicator
      if (mounted && context.mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) =>
              const Center(child: CircularProgressIndicator()),
        );
      }

      final exportResult = await ExcelFileService.exportAsTemplate(
        tool,
        result['name']!,
        result['description']!,
      );

      // Hide loading indicator
      if (mounted) Navigator.of(context).pop();

      if (exportResult.isSuccess) {
        _showSuccessDialog(
          'Template Created',
          'Template "${result['name']}" created successfully',
        );
      } else {
        _showErrorDialog(
          'Template Creation Failed',
          exportResult.error ?? 'Unknown error occurred',
        );
      }
    } catch (e) {
      if (mounted) Navigator.of(context).pop();
      _showErrorDialog('Template Error', 'Failed to create template: $e');
    }
  }

  Future<void> _loadFromTemplate(WidgetRef ref) async {
    try {
      final templates = await ExcelFileService.getAllTemplates();

      if (templates.isEmpty) {
        _showInfoDialog(
          'No Templates',
          'No templates available. Create a template first by using "Save as Template".',
        );
        return;
      }

      final selectedTemplate = (mounted && context.mounted)
          ? await showDialog<ExcelAppTemplate>(
              context: context,
              builder: (context) => _buildTemplateSelectionDialog(templates),
            )
          : null;

      if (selectedTemplate == null) return;

      final result = await ExcelFileService.importFromTemplate(
        selectedTemplate,
      );

      if (result.isSuccess && result.tool != null) {
        ref.read(currentExcelAppToolProvider.notifier).setTool(result.tool!);
        _showSuccessDialog(
          'Template Loaded',
          'Template "${selectedTemplate.name}" loaded successfully',
        );
      } else {
        _showErrorDialog(
          'Load Failed',
          result.error ?? 'Unknown error occurred',
        );
      }
    } catch (e) {
      _showErrorDialog('Template Error', 'Failed to load template: $e');
    }
  }

  Widget _buildTemplateDialog() {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();

    return AlertDialog(
      title: const Text('Save as Template'),
      content: SizedBox(
        width: 400,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Template Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (nameController.text.trim().isNotEmpty) {
              Navigator.of(context).pop({
                'name': nameController.text.trim(),
                'description': descriptionController.text.trim(),
              });
            }
          },
          child: const Text('Save Template'),
        ),
      ],
    );
  }

  Widget _buildTemplateSelectionDialog(List<ExcelAppTemplate> templates) {
    return AlertDialog(
      title: const Text('Load from Template'),
      content: SizedBox(
        width: 500,
        height: 400,
        child: ListView.builder(
          itemCount: templates.length,
          itemBuilder: (context, index) {
            final template = templates[index];
            return Card(
              child: ListTile(
                leading: const Icon(Icons.bookmark, color: Color(0xFF3498DB)),
                title: Text(template.name),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(template.description),
                    const SizedBox(height: 4),
                    Text(
                      'Created: ${template.createdAt.toString().split(' ')[0]} by ${template.author}',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                ),
                onTap: () => Navigator.of(context).pop(template),
              ),
            );
          },
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
      ],
    );
  }

  void _showSuccessDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.check_circle, color: Color(0xFF27AE60)),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(message),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF27AE60),
              foregroundColor: Colors.white,
            ),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.error, color: Color(0xFFE74C3C)),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(message),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFE74C3C),
              foregroundColor: Colors.white,
            ),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showInfoDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.info, color: Color(0xFF3498DB)),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(message),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF3498DB),
              foregroundColor: Colors.white,
            ),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showToolSettings(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.settings, color: Color(0xFF3498DB)),
            SizedBox(width: 8),
            Text('Tool Settings'),
          ],
        ),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Configure tool behavior and appearance',
                style: TextStyle(color: Color(0xFF7F8C8D)),
              ),
              const SizedBox(height: 20),
              _buildSettingItem(
                'Auto-save changes',
                'Automatically save tool changes',
                true,
                (value) {},
              ),
              _buildSettingItem(
                'Show grid lines',
                'Display grid lines in spreadsheet',
                true,
                (value) {},
              ),
              _buildSettingItem(
                'Enable formulas',
                'Allow formula calculations',
                true,
                (value) {},
              ),
              _buildSettingItem(
                'Real-time binding',
                'Update UI components in real-time',
                true,
                (value) {},
              ),
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),
              const Text(
                'Performance',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                ),
              ),
              const SizedBox(height: 8),
              _buildSettingItem(
                'Optimize for speed',
                'Prioritize performance over features',
                false,
                (value) {},
              ),
              _buildSettingItem(
                'Cache calculations',
                'Store formula results for faster access',
                true,
                (value) {},
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Settings saved successfully'),
                  backgroundColor: Color(0xFF27AE60),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF3498DB),
              foregroundColor: Colors.white,
            ),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingItem(
    String title,
    String description,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF7F8C8D),
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: const Color(0xFF3498DB),
          ),
        ],
      ),
    );
  }
}
