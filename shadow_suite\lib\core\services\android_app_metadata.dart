import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:io';

// Android App Metadata Service Provider
final androidAppMetadataProvider = Provider<AndroidAppMetadata>((ref) {
  return AndroidAppMetadata();
});

// App Info Provider
final appInfoProvider = StateNotifierProvider<AppInfoNotifier, AppInfo>((ref) {
  return AppInfoNotifier();
});

// Android App Metadata Service
class AndroidAppMetadata {
  static const MethodChannel _channel = MethodChannel('shadow_suite/app_metadata');

  // App information
  static const String appName = 'Shadow Suite';
  static const String packageName = 'com.shadowsuite.app';
  static const String versionName = '1.0.0';
  static const int versionCode = 1;
  static const String description = 'Complete productivity suite with Money Manager, File Manager, Excel to App, Islamic App, and Memo Suite';

  // Initialize app metadata
  Future<void> initialize() async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('setAppMetadata', {
        'appName': appName,
        'packageName': packageName,
        'versionName': versionName,
        'versionCode': versionCode,
        'description': description,
      });
    } catch (e) {
      debugPrint('Error setting app metadata: $e');
    }
  }

  // Get app information
  Future<AppInfo> getAppInfo() async {
    if (!Platform.isAndroid) {
      return AppInfo(
        appName: appName,
        packageName: packageName,
        versionName: versionName,
        versionCode: versionCode,
        buildNumber: '1',
        installedDate: DateTime.now(),
        lastUpdated: DateTime.now(),
        appSize: 0,
        dataSize: 0,
      );
    }

    try {
      final Map<String, dynamic> appInfo = await _channel.invokeMethod('getAppInfo');
      return AppInfo.fromMap(appInfo);
    } catch (e) {
      debugPrint('Error getting app info: $e');
      return AppInfo(
        appName: appName,
        packageName: packageName,
        versionName: versionName,
        versionCode: versionCode,
        buildNumber: '1',
        installedDate: DateTime.now(),
        lastUpdated: DateTime.now(),
        appSize: 0,
        dataSize: 0,
      );
    }
  }

  // Set app icon badge (for notifications)
  Future<void> setAppIconBadge(int count) async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('setAppIconBadge', {
        'count': count,
      });
    } catch (e) {
      debugPrint('Error setting app icon badge: $e');
    }
  }

  // Clear app icon badge
  Future<void> clearAppIconBadge() async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('clearAppIconBadge');
    } catch (e) {
      debugPrint('Error clearing app icon badge: $e');
    }
  }

  // Create app shortcuts (Android 7.1+)
  Future<void> createAppShortcuts() async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('createAppShortcuts', {
        'shortcuts': [
          {
            'id': 'money_manager',
            'shortLabel': 'Money Manager',
            'longLabel': 'Open Money Manager',
            'icon': 'ic_money_manager',
            'intent': 'money_manager',
          },
          {
            'id': 'file_manager',
            'shortLabel': 'File Manager',
            'longLabel': 'Open File Manager',
            'icon': 'ic_file_manager',
            'intent': 'file_manager',
          },
          {
            'id': 'excel_to_app',
            'shortLabel': 'Excel to App',
            'longLabel': 'Open Excel to App',
            'icon': 'ic_excel_to_app',
            'intent': 'excel_to_app',
          },
          {
            'id': 'islamic_app',
            'shortLabel': 'Islamic App',
            'longLabel': 'Open Islamic App',
            'icon': 'ic_islamic_app',
            'intent': 'islamic_app',
          },
        ],
      });
    } catch (e) {
      debugPrint('Error creating app shortcuts: $e');
    }
  }

  // Handle app shortcut intent
  Future<String?> getShortcutIntent() async {
    if (!Platform.isAndroid) return null;

    try {
      final String? intent = await _channel.invokeMethod('getShortcutIntent');
      return intent;
    } catch (e) {
      debugPrint('Error getting shortcut intent: $e');
      return null;
    }
  }

  // Set app theme (for Android 12+ dynamic colors)
  Future<void> setAppTheme({
    required Color primaryColor,
    required Color secondaryColor,
    required bool isDarkMode,
  }) async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('setAppTheme', {
        'primaryColor': primaryColor.toARGB32(),
        'secondaryColor': secondaryColor.toARGB32(),
        'isDarkMode': isDarkMode,
      });
    } catch (e) {
      debugPrint('Error setting app theme: $e');
    }
  }

  // Get system theme information
  Future<SystemThemeInfo> getSystemThemeInfo() async {
    if (!Platform.isAndroid) {
      return SystemThemeInfo(
        isDarkMode: false,
        accentColor: const Color(0xFF3498DB),
        supportsDynamicColors: false,
        systemColors: {},
      );
    }

    try {
      final Map<String, dynamic> themeInfo = await _channel.invokeMethod('getSystemThemeInfo');
      return SystemThemeInfo.fromMap(themeInfo);
    } catch (e) {
      debugPrint('Error getting system theme info: $e');
      return SystemThemeInfo(
        isDarkMode: false,
        accentColor: const Color(0xFF3498DB),
        supportsDynamicColors: false,
        systemColors: {},
      );
    }
  }

  // Request app review (Google Play In-App Review)
  Future<void> requestAppReview() async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('requestAppReview');
    } catch (e) {
      debugPrint('Error requesting app review: $e');
    }
  }

  // Open app in Google Play Store
  Future<void> openInPlayStore() async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('openInPlayStore', {
        'packageName': packageName,
      });
    } catch (e) {
      debugPrint('Error opening in Play Store: $e');
    }
  }

  // Check if app is installed from Play Store
  Future<bool> isInstalledFromPlayStore() async {
    if (!Platform.isAndroid) return false;

    try {
      final bool isFromPlayStore = await _channel.invokeMethod('isInstalledFromPlayStore');
      return isFromPlayStore;
    } catch (e) {
      debugPrint('Error checking Play Store installation: $e');
      return false;
    }
  }

  // Get app installation source
  Future<String> getInstallationSource() async {
    if (!Platform.isAndroid) return 'unknown';

    try {
      final String source = await _channel.invokeMethod('getInstallationSource');
      return source;
    } catch (e) {
      debugPrint('Error getting installation source: $e');
      return 'unknown';
    }
  }
}

// App Info Model
class AppInfo {
  final String appName;
  final String packageName;
  final String versionName;
  final int versionCode;
  final String buildNumber;
  final DateTime installedDate;
  final DateTime lastUpdated;
  final int appSize;
  final int dataSize;

  const AppInfo({
    required this.appName,
    required this.packageName,
    required this.versionName,
    required this.versionCode,
    required this.buildNumber,
    required this.installedDate,
    required this.lastUpdated,
    required this.appSize,
    required this.dataSize,
  });

  factory AppInfo.fromMap(Map<String, dynamic> map) {
    return AppInfo(
      appName: map['appName'] as String? ?? 'Shadow Suite',
      packageName: map['packageName'] as String? ?? 'com.shadowsuite.app',
      versionName: map['versionName'] as String? ?? '1.0.0',
      versionCode: map['versionCode'] as int? ?? 1,
      buildNumber: map['buildNumber'] as String? ?? '1',
      installedDate: DateTime.fromMillisecondsSinceEpoch(
        map['installedDate'] as int? ?? DateTime.now().millisecondsSinceEpoch,
      ),
      lastUpdated: DateTime.fromMillisecondsSinceEpoch(
        map['lastUpdated'] as int? ?? DateTime.now().millisecondsSinceEpoch,
      ),
      appSize: map['appSize'] as int? ?? 0,
      dataSize: map['dataSize'] as int? ?? 0,
    );
  }

  String get formattedAppSize {
    if (appSize < 1024) return '${appSize}B';
    if (appSize < 1024 * 1024) return '${(appSize / 1024).toStringAsFixed(1)}KB';
    if (appSize < 1024 * 1024 * 1024) return '${(appSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(appSize / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  String get formattedDataSize {
    if (dataSize < 1024) return '${dataSize}B';
    if (dataSize < 1024 * 1024) return '${(dataSize / 1024).toStringAsFixed(1)}KB';
    if (dataSize < 1024 * 1024 * 1024) return '${(dataSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(dataSize / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  String get totalSize {
    final total = appSize + dataSize;
    if (total < 1024) return '${total}B';
    if (total < 1024 * 1024) return '${(total / 1024).toStringAsFixed(1)}KB';
    if (total < 1024 * 1024 * 1024) return '${(total / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(total / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }
}

// System Theme Info Model
class SystemThemeInfo {
  final bool isDarkMode;
  final Color accentColor;
  final bool supportsDynamicColors;
  final Map<String, Color> systemColors;

  const SystemThemeInfo({
    required this.isDarkMode,
    required this.accentColor,
    required this.supportsDynamicColors,
    required this.systemColors,
  });

  factory SystemThemeInfo.fromMap(Map<String, dynamic> map) {
    final systemColorsMap = <String, Color>{};
    final colorsData = map['systemColors'] as Map<String, dynamic>? ?? {};
    
    for (final entry in colorsData.entries) {
      systemColorsMap[entry.key] = Color(entry.value as int);
    }

    return SystemThemeInfo(
      isDarkMode: map['isDarkMode'] as bool? ?? false,
      accentColor: Color(map['accentColor'] as int? ?? 0xFF3498DB),
      supportsDynamicColors: map['supportsDynamicColors'] as bool? ?? false,
      systemColors: systemColorsMap,
    );
  }
}

// App Info Notifier
class AppInfoNotifier extends StateNotifier<AppInfo> {
  AppInfoNotifier() : super(
    AppInfo(
      appName: AndroidAppMetadata.appName,
      packageName: AndroidAppMetadata.packageName,
      versionName: AndroidAppMetadata.versionName,
      versionCode: AndroidAppMetadata.versionCode,
      buildNumber: '1',
      installedDate: DateTime.now(),
      lastUpdated: DateTime.now(),
      appSize: 0,
      dataSize: 0,
    ),
  );

  Future<void> loadAppInfo() async {
    final metadata = AndroidAppMetadata();
    final appInfo = await metadata.getAppInfo();
    state = appInfo;
  }

  void updateAppInfo(AppInfo newAppInfo) {
    state = newAppInfo;
  }
}
