import 'package:flutter/material.dart';
import 'finance_models.dart';

/// Financial Insight generated by AI analysis
class FinancialInsight {
  final String id;
  final InsightType type;
  final String title;
  final String description;
  final ImpactLevel impact;
  final bool actionable;
  final List<String> recommendations;
  final double confidence;
  final DateTime createdAt;
  final Map<String, dynamic>? metadata;

  const FinancialInsight({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.impact,
    required this.actionable,
    required this.recommendations,
    required this.confidence,
    required this.createdAt,
    this.metadata,
  });

  FinancialInsight copyWith({
    String? id,
    InsightType? type,
    String? title,
    String? description,
    ImpactLevel? impact,
    bool? actionable,
    List<String>? recommendations,
    double? confidence,
    DateTime? createdAt,
    Map<String, dynamic>? metadata,
  }) {
    return FinancialInsight(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      impact: impact ?? this.impact,
      actionable: actionable ?? this.actionable,
      recommendations: recommendations ?? this.recommendations,
      confidence: confidence ?? this.confidence,
      createdAt: createdAt ?? this.createdAt,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'impact': impact.name,
      'actionable': actionable,
      'recommendations': recommendations,
      'confidence': confidence,
      'createdAt': createdAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  factory FinancialInsight.fromJson(Map<String, dynamic> json) {
    return FinancialInsight(
      id: json['id'],
      type: InsightType.values.firstWhere((e) => e.name == json['type']),
      title: json['title'],
      description: json['description'],
      impact: ImpactLevel.values.firstWhere((e) => e.name == json['impact']),
      actionable: json['actionable'],
      recommendations: List<String>.from(json['recommendations']),
      confidence: json['confidence'].toDouble(),
      createdAt: DateTime.parse(json['createdAt']),
      metadata: json['metadata'],
    );
  }
}

/// Types of financial insights
enum InsightType {
  trend,
  warning,
  optimization,
  opportunity,
  prediction,
  anomaly,
  recommendation,
}

/// Impact level of financial insights
enum ImpactLevel { low, medium, high, critical }

/// Cash flow forecast with predictions
class CashFlowForecast {
  final String id;
  final Map<DateTime, double> predictions;
  final double confidence;
  final String methodology;
  final DateTime generatedAt;
  final Map<String, dynamic>? parameters;

  const CashFlowForecast({
    required this.id,
    required this.predictions,
    required this.confidence,
    required this.methodology,
    required this.generatedAt,
    this.parameters,
  });

  CashFlowForecast copyWith({
    String? id,
    Map<DateTime, double>? predictions,
    double? confidence,
    String? methodology,
    DateTime? generatedAt,
    Map<String, dynamic>? parameters,
  }) {
    return CashFlowForecast(
      id: id ?? this.id,
      predictions: predictions ?? this.predictions,
      confidence: confidence ?? this.confidence,
      methodology: methodology ?? this.methodology,
      generatedAt: generatedAt ?? this.generatedAt,
      parameters: parameters ?? this.parameters,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'predictions': predictions.map(
        (k, v) => MapEntry(k.toIso8601String(), v),
      ),
      'confidence': confidence,
      'methodology': methodology,
      'generatedAt': generatedAt.toIso8601String(),
      'parameters': parameters,
    };
  }

  factory CashFlowForecast.fromJson(Map<String, dynamic> json) {
    final predictionsMap = Map<String, dynamic>.from(json['predictions']);
    final predictions = <DateTime, double>{};

    predictionsMap.forEach((key, value) {
      predictions[DateTime.parse(key)] = value.toDouble();
    });

    return CashFlowForecast(
      id: json['id'],
      predictions: predictions,
      confidence: json['confidence'].toDouble(),
      methodology: json['methodology'],
      generatedAt: DateTime.parse(json['generatedAt']),
      parameters: json['parameters'],
    );
  }
}

/// Investment recommendation with portfolio options
class InvestmentRecommendation {
  final String id;
  final List<InvestmentOption> options;
  final double totalAmount;
  final RiskProfile riskProfile;
  final double expectedReturn;
  final String timeHorizon;
  final DateTime generatedAt;
  final Map<String, dynamic>? analysis;

  const InvestmentRecommendation({
    required this.id,
    required this.options,
    required this.totalAmount,
    required this.riskProfile,
    required this.expectedReturn,
    required this.timeHorizon,
    required this.generatedAt,
    this.analysis,
  });

  InvestmentRecommendation copyWith({
    String? id,
    List<InvestmentOption>? options,
    double? totalAmount,
    RiskProfile? riskProfile,
    double? expectedReturn,
    String? timeHorizon,
    DateTime? generatedAt,
    Map<String, dynamic>? analysis,
  }) {
    return InvestmentRecommendation(
      id: id ?? this.id,
      options: options ?? this.options,
      totalAmount: totalAmount ?? this.totalAmount,
      riskProfile: riskProfile ?? this.riskProfile,
      expectedReturn: expectedReturn ?? this.expectedReturn,
      timeHorizon: timeHorizon ?? this.timeHorizon,
      generatedAt: generatedAt ?? this.generatedAt,
      analysis: analysis ?? this.analysis,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'options': options.map((o) => o.toJson()).toList(),
      'totalAmount': totalAmount,
      'riskProfile': riskProfile.name,
      'expectedReturn': expectedReturn,
      'timeHorizon': timeHorizon,
      'generatedAt': generatedAt.toIso8601String(),
      'analysis': analysis,
    };
  }

  factory InvestmentRecommendation.fromJson(Map<String, dynamic> json) {
    return InvestmentRecommendation(
      id: json['id'],
      options: (json['options'] as List)
          .map((o) => InvestmentOption.fromJson(o))
          .toList(),
      totalAmount: json['totalAmount'].toDouble(),
      riskProfile: RiskProfile.values.firstWhere(
        (e) => e.name == json['riskProfile'],
      ),
      expectedReturn: json['expectedReturn'].toDouble(),
      timeHorizon: json['timeHorizon'],
      generatedAt: DateTime.parse(json['generatedAt']),
      analysis: json['analysis'],
    );
  }
}

/// Individual investment option
class InvestmentOption {
  final String name;
  final double allocation;
  final double expectedReturn;
  final RiskLevel risk;
  final LiquidityLevel liquidity;
  final String? description;
  final Map<String, dynamic>? details;

  const InvestmentOption({
    required this.name,
    required this.allocation,
    required this.expectedReturn,
    required this.risk,
    required this.liquidity,
    this.description,
    this.details,
  });

  InvestmentOption copyWith({
    String? name,
    double? allocation,
    double? expectedReturn,
    RiskLevel? risk,
    LiquidityLevel? liquidity,
    String? description,
    Map<String, dynamic>? details,
  }) {
    return InvestmentOption(
      name: name ?? this.name,
      allocation: allocation ?? this.allocation,
      expectedReturn: expectedReturn ?? this.expectedReturn,
      risk: risk ?? this.risk,
      liquidity: liquidity ?? this.liquidity,
      description: description ?? this.description,
      details: details ?? this.details,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'allocation': allocation,
      'expectedReturn': expectedReturn,
      'risk': risk.name,
      'liquidity': liquidity.name,
      'description': description,
      'details': details,
    };
  }

  factory InvestmentOption.fromJson(Map<String, dynamic> json) {
    return InvestmentOption(
      name: json['name'],
      allocation: json['allocation'].toDouble(),
      expectedReturn: json['expectedReturn'].toDouble(),
      risk: RiskLevel.values.firstWhere((e) => e.name == json['risk']),
      liquidity: LiquidityLevel.values.firstWhere(
        (e) => e.name == json['liquidity'],
      ),
      description: json['description'],
      details: json['details'],
    );
  }
}

/// Risk profile for investment recommendations
enum RiskProfile { conservative, moderate, aggressive }

/// Risk level for individual investments
enum RiskLevel { low, medium, high }

/// Liquidity level for investments
enum LiquidityLevel { low, medium, high }

/// Budget optimization suggestion
class BudgetOptimization {
  final String id;
  final String category;
  final double currentAmount;
  final double suggestedAmount;
  final double potentialSavings;
  final String reasoning;
  final List<String> actionSteps;
  final double confidence;
  final DateTime createdAt;

  const BudgetOptimization({
    required this.id,
    required this.category,
    required this.currentAmount,
    required this.suggestedAmount,
    required this.potentialSavings,
    required this.reasoning,
    required this.actionSteps,
    required this.confidence,
    required this.createdAt,
  });

  BudgetOptimization copyWith({
    String? id,
    String? category,
    double? currentAmount,
    double? suggestedAmount,
    double? potentialSavings,
    String? reasoning,
    List<String>? actionSteps,
    double? confidence,
    DateTime? createdAt,
  }) {
    return BudgetOptimization(
      id: id ?? this.id,
      category: category ?? this.category,
      currentAmount: currentAmount ?? this.currentAmount,
      suggestedAmount: suggestedAmount ?? this.suggestedAmount,
      potentialSavings: potentialSavings ?? this.potentialSavings,
      reasoning: reasoning ?? this.reasoning,
      actionSteps: actionSteps ?? this.actionSteps,
      confidence: confidence ?? this.confidence,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category': category,
      'currentAmount': currentAmount,
      'suggestedAmount': suggestedAmount,
      'potentialSavings': potentialSavings,
      'reasoning': reasoning,
      'actionSteps': actionSteps,
      'confidence': confidence,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory BudgetOptimization.fromJson(Map<String, dynamic> json) {
    return BudgetOptimization(
      id: json['id'],
      category: json['category'],
      currentAmount: json['currentAmount'].toDouble(),
      suggestedAmount: json['suggestedAmount'].toDouble(),
      potentialSavings: json['potentialSavings'].toDouble(),
      reasoning: json['reasoning'],
      actionSteps: List<String>.from(json['actionSteps']),
      confidence: json['confidence'].toDouble(),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Automation rule for financial tasks
class AutomationRule {
  final String id;
  final String name;
  final String description;
  final AutomationRuleType type;
  final bool isEnabled;
  final AutomationSchedule schedule;
  final Map<String, dynamic> parameters;
  final List<AutomationCondition> conditions;
  final List<AutomationAction> actions;
  DateTime? lastExecuted;
  final DateTime createdAt;

  AutomationRule({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.isEnabled,
    required this.schedule,
    required this.parameters,
    required this.conditions,
    required this.actions,
    this.lastExecuted,
    required this.createdAt,
  });

  AutomationRule copyWith({
    String? id,
    String? name,
    String? description,
    AutomationRuleType? type,
    bool? isEnabled,
    AutomationSchedule? schedule,
    Map<String, dynamic>? parameters,
    List<AutomationCondition>? conditions,
    List<AutomationAction>? actions,
    DateTime? lastExecuted,
    DateTime? createdAt,
  }) {
    return AutomationRule(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      isEnabled: isEnabled ?? this.isEnabled,
      schedule: schedule ?? this.schedule,
      parameters: parameters ?? this.parameters,
      conditions: conditions ?? this.conditions,
      actions: actions ?? this.actions,
      lastExecuted: lastExecuted ?? this.lastExecuted,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'isEnabled': isEnabled,
      'schedule': schedule.toJson(),
      'parameters': parameters,
      'conditions': conditions.map((c) => c.toJson()).toList(),
      'actions': actions.map((a) => a.toJson()).toList(),
      'lastExecuted': lastExecuted?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory AutomationRule.fromJson(Map<String, dynamic> json) {
    return AutomationRule(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      type: AutomationRuleType.values.firstWhere((e) => e.name == json['type']),
      isEnabled: json['isEnabled'],
      schedule: AutomationSchedule.fromJson(json['schedule']),
      parameters: Map<String, dynamic>.from(json['parameters']),
      conditions: (json['conditions'] as List)
          .map((c) => AutomationCondition.fromJson(c))
          .toList(),
      actions: (json['actions'] as List)
          .map((a) => AutomationAction.fromJson(a))
          .toList(),
      lastExecuted: json['lastExecuted'] != null
          ? DateTime.parse(json['lastExecuted'])
          : null,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Types of automation rules
enum AutomationRuleType {
  categorization,
  billPayment,
  budgetAlert,
  savingsTransfer,
}

/// Automation schedule configuration
class AutomationSchedule {
  final ScheduleFrequency frequency;
  final DateTime? specificTime;
  final List<int>? daysOfWeek;
  final int? dayOfMonth;

  const AutomationSchedule({
    required this.frequency,
    this.specificTime,
    this.daysOfWeek,
    this.dayOfMonth,
  });

  AutomationSchedule copyWith({
    ScheduleFrequency? frequency,
    DateTime? specificTime,
    List<int>? daysOfWeek,
    int? dayOfMonth,
  }) {
    return AutomationSchedule(
      frequency: frequency ?? this.frequency,
      specificTime: specificTime ?? this.specificTime,
      daysOfWeek: daysOfWeek ?? this.daysOfWeek,
      dayOfMonth: dayOfMonth ?? this.dayOfMonth,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'frequency': frequency.name,
      'specificTime': specificTime?.toIso8601String(),
      'daysOfWeek': daysOfWeek,
      'dayOfMonth': dayOfMonth,
    };
  }

  factory AutomationSchedule.fromJson(Map<String, dynamic> json) {
    return AutomationSchedule(
      frequency: ScheduleFrequency.values.firstWhere(
        (e) => e.name == json['frequency'],
      ),
      specificTime: json['specificTime'] != null
          ? DateTime.parse(json['specificTime'])
          : null,
      daysOfWeek: json['daysOfWeek'] != null
          ? List<int>.from(json['daysOfWeek'])
          : null,
      dayOfMonth: json['dayOfMonth'],
    );
  }
}

/// Schedule frequency options
enum ScheduleFrequency { immediate, daily, weekly, monthly }

/// Automation condition
class AutomationCondition {
  final String field;
  final ConditionOperator operator;
  final dynamic value;

  const AutomationCondition({
    required this.field,
    required this.operator,
    required this.value,
  });

  AutomationCondition copyWith({
    String? field,
    ConditionOperator? operator,
    dynamic value,
  }) {
    return AutomationCondition(
      field: field ?? this.field,
      operator: operator ?? this.operator,
      value: value ?? this.value,
    );
  }

  Map<String, dynamic> toJson() {
    return {'field': field, 'operator': operator.name, 'value': value};
  }

  factory AutomationCondition.fromJson(Map<String, dynamic> json) {
    return AutomationCondition(
      field: json['field'],
      operator: ConditionOperator.values.firstWhere(
        (e) => e.name == json['operator'],
      ),
      value: json['value'],
    );
  }
}

/// Condition operators
enum ConditionOperator {
  equals,
  notEquals,
  greaterThan,
  lessThan,
  contains,
  startsWith,
  endsWith,
}

/// Automation action
class AutomationAction {
  final ActionType type;
  final Map<String, dynamic> parameters;

  const AutomationAction({required this.type, required this.parameters});

  AutomationAction copyWith({
    ActionType? type,
    Map<String, dynamic>? parameters,
  }) {
    return AutomationAction(
      type: type ?? this.type,
      parameters: parameters ?? this.parameters,
    );
  }

  Map<String, dynamic> toJson() {
    return {'type': type.name, 'parameters': parameters};
  }

  factory AutomationAction.fromJson(Map<String, dynamic> json) {
    return AutomationAction(
      type: ActionType.values.firstWhere((e) => e.name == json['type']),
      parameters: Map<String, dynamic>.from(json['parameters']),
    );
  }
}

/// Action types
enum ActionType {
  categorize,
  createTransaction,
  sendNotification,
  transferFunds,
  updateBudget,
}

/// Automation event
class AutomationEvent {
  final String id;
  final AutomationEventType type;
  final String message;
  final DateTime timestamp;
  final String? ruleId;
  final String? transactionId;
  final Map<String, dynamic>? metadata;

  const AutomationEvent({
    required this.id,
    required this.type,
    required this.message,
    required this.timestamp,
    this.ruleId,
    this.transactionId,
    this.metadata,
  });

  AutomationEvent copyWith({
    String? id,
    AutomationEventType? type,
    String? message,
    DateTime? timestamp,
    String? ruleId,
    String? transactionId,
    Map<String, dynamic>? metadata,
  }) {
    return AutomationEvent(
      id: id ?? this.id,
      type: type ?? this.type,
      message: message ?? this.message,
      timestamp: timestamp ?? this.timestamp,
      ruleId: ruleId ?? this.ruleId,
      transactionId: transactionId ?? this.transactionId,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'ruleId': ruleId,
      'transactionId': transactionId,
      'metadata': metadata,
    };
  }

  factory AutomationEvent.fromJson(Map<String, dynamic> json) {
    return AutomationEvent(
      id: json['id'],
      type: AutomationEventType.values.firstWhere(
        (e) => e.name == json['type'],
      ),
      message: json['message'],
      timestamp: DateTime.parse(json['timestamp']),
      ruleId: json['ruleId'],
      transactionId: json['transactionId'],
      metadata: json['metadata'],
    );
  }
}

/// Automation event types
enum AutomationEventType {
  ruleAdded,
  ruleRemoved,
  ruleUpdated,
  ruleExecuted,
  ruleError,
  paymentExecuted,
  paymentFailed,
  categoryAssigned,
  budgetAlertSent,
  transferCompleted,
}

/// Recurring transaction pattern
class RecurringTransaction {
  final String id;
  final String payee;
  final double averageAmount;
  final RecurringFrequency frequency;
  final DateTime nextExpectedDate;
  final double confidence;
  final List<Transaction> transactions;
  final String category;
  final bool isActive;
  final DateTime createdAt;

  const RecurringTransaction({
    required this.id,
    required this.payee,
    required this.averageAmount,
    required this.frequency,
    required this.nextExpectedDate,
    required this.confidence,
    required this.transactions,
    required this.category,
    required this.isActive,
    required this.createdAt,
  });

  RecurringTransaction copyWith({
    String? id,
    String? payee,
    double? averageAmount,
    RecurringFrequency? frequency,
    DateTime? nextExpectedDate,
    double? confidence,
    List<Transaction>? transactions,
    String? category,
    bool? isActive,
    DateTime? createdAt,
  }) {
    return RecurringTransaction(
      id: id ?? this.id,
      payee: payee ?? this.payee,
      averageAmount: averageAmount ?? this.averageAmount,
      frequency: frequency ?? this.frequency,
      nextExpectedDate: nextExpectedDate ?? this.nextExpectedDate,
      confidence: confidence ?? this.confidence,
      transactions: transactions ?? this.transactions,
      category: category ?? this.category,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'payee': payee,
      'averageAmount': averageAmount,
      'frequency': frequency.name,
      'nextExpectedDate': nextExpectedDate.toIso8601String(),
      'confidence': confidence,
      'transactions': transactions.map((t) => t.toJson()).toList(),
      'category': category,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory RecurringTransaction.fromJson(Map<String, dynamic> json) {
    return RecurringTransaction(
      id: json['id'],
      payee: json['payee'],
      averageAmount: json['averageAmount'].toDouble(),
      frequency: RecurringFrequency.values.firstWhere(
        (e) => e.name == json['frequency'],
      ),
      nextExpectedDate: DateTime.parse(json['nextExpectedDate']),
      confidence: json['confidence'].toDouble(),
      transactions: (json['transactions'] as List)
          .map((t) => Transaction.fromJson(t))
          .toList(),
      category: json['category'],
      isActive: json['isActive'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Recurring frequency options
enum RecurringFrequency { weekly, biweekly, monthly, quarterly, yearly }

/// Recurring pattern analysis result
class RecurringPattern {
  final double averageAmount;
  final RecurringFrequency frequency;
  final DateTime nextExpectedDate;
  final double confidence;

  const RecurringPattern({
    required this.averageAmount,
    required this.frequency,
    required this.nextExpectedDate,
    required this.confidence,
  });

  RecurringPattern copyWith({
    double? averageAmount,
    RecurringFrequency? frequency,
    DateTime? nextExpectedDate,
    double? confidence,
  }) {
    return RecurringPattern(
      averageAmount: averageAmount ?? this.averageAmount,
      frequency: frequency ?? this.frequency,
      nextExpectedDate: nextExpectedDate ?? this.nextExpectedDate,
      confidence: confidence ?? this.confidence,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'averageAmount': averageAmount,
      'frequency': frequency.name,
      'nextExpectedDate': nextExpectedDate.toIso8601String(),
      'confidence': confidence,
    };
  }

  factory RecurringPattern.fromJson(Map<String, dynamic> json) {
    return RecurringPattern(
      averageAmount: json['averageAmount'].toDouble(),
      frequency: RecurringFrequency.values.firstWhere(
        (e) => e.name == json['frequency'],
      ),
      nextExpectedDate: DateTime.parse(json['nextExpectedDate']),
      confidence: json['confidence'].toDouble(),
    );
  }
}

/// Expense prediction
class ExpensePrediction {
  final String id;
  final String payee;
  final String category;
  final double predictedAmount;
  final DateTime predictedDate;
  final double confidence;
  final PredictionType type;
  final List<String> basedOnTransactions;
  final DateTime createdAt;

  const ExpensePrediction({
    required this.id,
    required this.payee,
    required this.category,
    required this.predictedAmount,
    required this.predictedDate,
    required this.confidence,
    required this.type,
    required this.basedOnTransactions,
    required this.createdAt,
  });

  ExpensePrediction copyWith({
    String? id,
    String? payee,
    String? category,
    double? predictedAmount,
    DateTime? predictedDate,
    double? confidence,
    PredictionType? type,
    List<String>? basedOnTransactions,
    DateTime? createdAt,
  }) {
    return ExpensePrediction(
      id: id ?? this.id,
      payee: payee ?? this.payee,
      category: category ?? this.category,
      predictedAmount: predictedAmount ?? this.predictedAmount,
      predictedDate: predictedDate ?? this.predictedDate,
      confidence: confidence ?? this.confidence,
      type: type ?? this.type,
      basedOnTransactions: basedOnTransactions ?? this.basedOnTransactions,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'payee': payee,
      'category': category,
      'predictedAmount': predictedAmount,
      'predictedDate': predictedDate.toIso8601String(),
      'confidence': confidence,
      'type': type.name,
      'basedOnTransactions': basedOnTransactions,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory ExpensePrediction.fromJson(Map<String, dynamic> json) {
    return ExpensePrediction(
      id: json['id'],
      payee: json['payee'],
      category: json['category'],
      predictedAmount: json['predictedAmount'].toDouble(),
      predictedDate: DateTime.parse(json['predictedDate']),
      confidence: json['confidence'].toDouble(),
      type: PredictionType.values.firstWhere((e) => e.name == json['type']),
      basedOnTransactions: List<String>.from(json['basedOnTransactions']),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Prediction types
enum PredictionType { recurring, seasonal, trend, anomaly }

/// Statistical summary of financial data
class StatisticalSummary {
  final String id;
  final int totalTransactions;
  final double totalExpenses;
  final double totalIncome;
  final double averageExpense;
  final double averageIncome;
  final double medianExpense;
  final double medianIncome;
  final double expenseStandardDeviation;
  final double incomeStandardDeviation;
  final double expenseVariance;
  final double incomeVariance;
  final double expenseSkewness;
  final double incomeSkewness;
  final double expenseKurtosis;
  final double incomeKurtosis;
  final double correlationCoefficient;
  final DateTime generatedAt;

  const StatisticalSummary({
    required this.id,
    required this.totalTransactions,
    required this.totalExpenses,
    required this.totalIncome,
    required this.averageExpense,
    required this.averageIncome,
    required this.medianExpense,
    required this.medianIncome,
    required this.expenseStandardDeviation,
    required this.incomeStandardDeviation,
    required this.expenseVariance,
    required this.incomeVariance,
    required this.expenseSkewness,
    required this.incomeSkewness,
    required this.expenseKurtosis,
    required this.incomeKurtosis,
    required this.correlationCoefficient,
    required this.generatedAt,
  });

  StatisticalSummary copyWith({
    String? id,
    int? totalTransactions,
    double? totalExpenses,
    double? totalIncome,
    double? averageExpense,
    double? averageIncome,
    double? medianExpense,
    double? medianIncome,
    double? expenseStandardDeviation,
    double? incomeStandardDeviation,
    double? expenseVariance,
    double? incomeVariance,
    double? expenseSkewness,
    double? incomeSkewness,
    double? expenseKurtosis,
    double? incomeKurtosis,
    double? correlationCoefficient,
    DateTime? generatedAt,
  }) {
    return StatisticalSummary(
      id: id ?? this.id,
      totalTransactions: totalTransactions ?? this.totalTransactions,
      totalExpenses: totalExpenses ?? this.totalExpenses,
      totalIncome: totalIncome ?? this.totalIncome,
      averageExpense: averageExpense ?? this.averageExpense,
      averageIncome: averageIncome ?? this.averageIncome,
      medianExpense: medianExpense ?? this.medianExpense,
      medianIncome: medianIncome ?? this.medianIncome,
      expenseStandardDeviation:
          expenseStandardDeviation ?? this.expenseStandardDeviation,
      incomeStandardDeviation:
          incomeStandardDeviation ?? this.incomeStandardDeviation,
      expenseVariance: expenseVariance ?? this.expenseVariance,
      incomeVariance: incomeVariance ?? this.incomeVariance,
      expenseSkewness: expenseSkewness ?? this.expenseSkewness,
      incomeSkewness: incomeSkewness ?? this.incomeSkewness,
      expenseKurtosis: expenseKurtosis ?? this.expenseKurtosis,
      incomeKurtosis: incomeKurtosis ?? this.incomeKurtosis,
      correlationCoefficient:
          correlationCoefficient ?? this.correlationCoefficient,
      generatedAt: generatedAt ?? this.generatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'totalTransactions': totalTransactions,
      'totalExpenses': totalExpenses,
      'totalIncome': totalIncome,
      'averageExpense': averageExpense,
      'averageIncome': averageIncome,
      'medianExpense': medianExpense,
      'medianIncome': medianIncome,
      'expenseStandardDeviation': expenseStandardDeviation,
      'incomeStandardDeviation': incomeStandardDeviation,
      'expenseVariance': expenseVariance,
      'incomeVariance': incomeVariance,
      'expenseSkewness': expenseSkewness,
      'incomeSkewness': incomeSkewness,
      'expenseKurtosis': expenseKurtosis,
      'incomeKurtosis': incomeKurtosis,
      'correlationCoefficient': correlationCoefficient,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }

  factory StatisticalSummary.fromJson(Map<String, dynamic> json) {
    return StatisticalSummary(
      id: json['id'],
      totalTransactions: json['totalTransactions'],
      totalExpenses: json['totalExpenses'].toDouble(),
      totalIncome: json['totalIncome'].toDouble(),
      averageExpense: json['averageExpense'].toDouble(),
      averageIncome: json['averageIncome'].toDouble(),
      medianExpense: json['medianExpense'].toDouble(),
      medianIncome: json['medianIncome'].toDouble(),
      expenseStandardDeviation: json['expenseStandardDeviation'].toDouble(),
      incomeStandardDeviation: json['incomeStandardDeviation'].toDouble(),
      expenseVariance: json['expenseVariance'].toDouble(),
      incomeVariance: json['incomeVariance'].toDouble(),
      expenseSkewness: json['expenseSkewness'].toDouble(),
      incomeSkewness: json['incomeSkewness'].toDouble(),
      expenseKurtosis: json['expenseKurtosis'].toDouble(),
      incomeKurtosis: json['incomeKurtosis'].toDouble(),
      correlationCoefficient: json['correlationCoefficient'].toDouble(),
      generatedAt: DateTime.parse(json['generatedAt']),
    );
  }
}

/// Trend analysis result
class TrendAnalysis {
  final String id;
  final int periodDays;
  final List<TrendData> trendData;
  final TrendDirection expenseTrendDirection;
  final double expenseTrendStrength;
  final TrendDirection incomeTrendDirection;
  final double incomeTrendStrength;
  final TrendDirection cashFlowTrendDirection;
  final double cashFlowTrendStrength;
  final double seasonalityScore;
  final double volatilityIndex;
  final DateTime generatedAt;

  const TrendAnalysis({
    required this.id,
    required this.periodDays,
    required this.trendData,
    required this.expenseTrendDirection,
    required this.expenseTrendStrength,
    required this.incomeTrendDirection,
    required this.incomeTrendStrength,
    required this.cashFlowTrendDirection,
    required this.cashFlowTrendStrength,
    required this.seasonalityScore,
    required this.volatilityIndex,
    required this.generatedAt,
  });

  TrendAnalysis copyWith({
    String? id,
    int? periodDays,
    List<TrendData>? trendData,
    TrendDirection? expenseTrendDirection,
    double? expenseTrendStrength,
    TrendDirection? incomeTrendDirection,
    double? incomeTrendStrength,
    TrendDirection? cashFlowTrendDirection,
    double? cashFlowTrendStrength,
    double? seasonalityScore,
    double? volatilityIndex,
    DateTime? generatedAt,
  }) {
    return TrendAnalysis(
      id: id ?? this.id,
      periodDays: periodDays ?? this.periodDays,
      trendData: trendData ?? this.trendData,
      expenseTrendDirection:
          expenseTrendDirection ?? this.expenseTrendDirection,
      expenseTrendStrength: expenseTrendStrength ?? this.expenseTrendStrength,
      incomeTrendDirection: incomeTrendDirection ?? this.incomeTrendDirection,
      incomeTrendStrength: incomeTrendStrength ?? this.incomeTrendStrength,
      cashFlowTrendDirection:
          cashFlowTrendDirection ?? this.cashFlowTrendDirection,
      cashFlowTrendStrength:
          cashFlowTrendStrength ?? this.cashFlowTrendStrength,
      seasonalityScore: seasonalityScore ?? this.seasonalityScore,
      volatilityIndex: volatilityIndex ?? this.volatilityIndex,
      generatedAt: generatedAt ?? this.generatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'periodDays': periodDays,
      'trendData': trendData.map((t) => t.toJson()).toList(),
      'expenseTrendDirection': expenseTrendDirection.name,
      'expenseTrendStrength': expenseTrendStrength,
      'incomeTrendDirection': incomeTrendDirection.name,
      'incomeTrendStrength': incomeTrendStrength,
      'cashFlowTrendDirection': cashFlowTrendDirection.name,
      'cashFlowTrendStrength': cashFlowTrendStrength,
      'seasonalityScore': seasonalityScore,
      'volatilityIndex': volatilityIndex,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }

  factory TrendAnalysis.fromJson(Map<String, dynamic> json) {
    return TrendAnalysis(
      id: json['id'],
      periodDays: json['periodDays'],
      trendData: (json['trendData'] as List)
          .map((t) => TrendData.fromJson(t))
          .toList(),
      expenseTrendDirection: TrendDirection.values.firstWhere(
        (e) => e.name == json['expenseTrendDirection'],
      ),
      expenseTrendStrength: json['expenseTrendStrength'].toDouble(),
      incomeTrendDirection: TrendDirection.values.firstWhere(
        (e) => e.name == json['incomeTrendDirection'],
      ),
      incomeTrendStrength: json['incomeTrendStrength'].toDouble(),
      cashFlowTrendDirection: TrendDirection.values.firstWhere(
        (e) => e.name == json['cashFlowTrendDirection'],
      ),
      cashFlowTrendStrength: json['cashFlowTrendStrength'].toDouble(),
      seasonalityScore: json['seasonalityScore'].toDouble(),
      volatilityIndex: json['volatilityIndex'].toDouble(),
      generatedAt: DateTime.parse(json['generatedAt']),
    );
  }
}

/// Trend direction enumeration
enum TrendDirection { increasing, decreasing, stable }

/// Trend data point
class TrendData {
  final DateTime period;
  final double totalExpenses;
  final double totalIncome;
  final double netCashFlow;
  final int transactionCount;

  const TrendData({
    required this.period,
    required this.totalExpenses,
    required this.totalIncome,
    required this.netCashFlow,
    required this.transactionCount,
  });

  TrendData copyWith({
    DateTime? period,
    double? totalExpenses,
    double? totalIncome,
    double? netCashFlow,
    int? transactionCount,
  }) {
    return TrendData(
      period: period ?? this.period,
      totalExpenses: totalExpenses ?? this.totalExpenses,
      totalIncome: totalIncome ?? this.totalIncome,
      netCashFlow: netCashFlow ?? this.netCashFlow,
      transactionCount: transactionCount ?? this.transactionCount,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'period': period.toIso8601String(),
      'totalExpenses': totalExpenses,
      'totalIncome': totalIncome,
      'netCashFlow': netCashFlow,
      'transactionCount': transactionCount,
    };
  }

  factory TrendData.fromJson(Map<String, dynamic> json) {
    return TrendData(
      period: DateTime.parse(json['period']),
      totalExpenses: json['totalExpenses'].toDouble(),
      totalIncome: json['totalIncome'].toDouble(),
      netCashFlow: json['netCashFlow'].toDouble(),
      transactionCount: json['transactionCount'],
    );
  }
}

/// Trend result with direction and strength
class TrendResult {
  final TrendDirection direction;
  final double strength;

  const TrendResult(this.direction, this.strength);

  TrendResult copyWith({TrendDirection? direction, double? strength}) {
    return TrendResult(direction ?? this.direction, strength ?? this.strength);
  }

  Map<String, dynamic> toJson() {
    return {'direction': direction.name, 'strength': strength};
  }

  factory TrendResult.fromJson(Map<String, dynamic> json) {
    return TrendResult(
      TrendDirection.values.firstWhere((e) => e.name == json['direction']),
      json['strength'].toDouble(),
    );
  }
}

/// Category analysis result
class CategoryAnalysis {
  final String id;
  final Map<String, CategoryMetrics> categoryMetrics;
  final List<String> topCategories;
  final List<String> growingCategories;
  final List<String> decliningCategories;
  final List<String> volatileCategories;
  final List<String> efficientCategories;
  final DateTime generatedAt;

  const CategoryAnalysis({
    required this.id,
    required this.categoryMetrics,
    required this.topCategories,
    required this.growingCategories,
    required this.decliningCategories,
    required this.volatileCategories,
    required this.efficientCategories,
    required this.generatedAt,
  });

  CategoryAnalysis copyWith({
    String? id,
    Map<String, CategoryMetrics>? categoryMetrics,
    List<String>? topCategories,
    List<String>? growingCategories,
    List<String>? decliningCategories,
    List<String>? volatileCategories,
    List<String>? efficientCategories,
    DateTime? generatedAt,
  }) {
    return CategoryAnalysis(
      id: id ?? this.id,
      categoryMetrics: categoryMetrics ?? this.categoryMetrics,
      topCategories: topCategories ?? this.topCategories,
      growingCategories: growingCategories ?? this.growingCategories,
      decliningCategories: decliningCategories ?? this.decliningCategories,
      volatileCategories: volatileCategories ?? this.volatileCategories,
      efficientCategories: efficientCategories ?? this.efficientCategories,
      generatedAt: generatedAt ?? this.generatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'categoryMetrics': categoryMetrics.map((k, v) => MapEntry(k, v.toJson())),
      'topCategories': topCategories,
      'growingCategories': growingCategories,
      'decliningCategories': decliningCategories,
      'volatileCategories': volatileCategories,
      'efficientCategories': efficientCategories,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }

  factory CategoryAnalysis.fromJson(Map<String, dynamic> json) {
    final categoryMetricsMap = Map<String, dynamic>.from(
      json['categoryMetrics'],
    );
    final categoryMetrics = <String, CategoryMetrics>{};

    categoryMetricsMap.forEach((key, value) {
      categoryMetrics[key] = CategoryMetrics.fromJson(value);
    });

    return CategoryAnalysis(
      id: json['id'],
      categoryMetrics: categoryMetrics,
      topCategories: List<String>.from(json['topCategories']),
      growingCategories: List<String>.from(json['growingCategories']),
      decliningCategories: List<String>.from(json['decliningCategories']),
      volatileCategories: List<String>.from(json['volatileCategories']),
      efficientCategories: List<String>.from(json['efficientCategories']),
      generatedAt: DateTime.parse(json['generatedAt']),
    );
  }
}

/// Category metrics
class CategoryMetrics {
  final String category;
  final double totalAmount;
  final double averageAmount;
  final int transactionCount;
  final double frequency;
  final double trend;
  final double seasonality;
  final double volatility;
  final double efficiency;
  final double growthRate;
  final double percentageOfTotal;

  const CategoryMetrics({
    required this.category,
    required this.totalAmount,
    required this.averageAmount,
    required this.transactionCount,
    required this.frequency,
    required this.trend,
    required this.seasonality,
    required this.volatility,
    required this.efficiency,
    required this.growthRate,
    required this.percentageOfTotal,
  });

  CategoryMetrics copyWith({
    String? category,
    double? totalAmount,
    double? averageAmount,
    int? transactionCount,
    double? frequency,
    double? trend,
    double? seasonality,
    double? volatility,
    double? efficiency,
    double? growthRate,
    double? percentageOfTotal,
  }) {
    return CategoryMetrics(
      category: category ?? this.category,
      totalAmount: totalAmount ?? this.totalAmount,
      averageAmount: averageAmount ?? this.averageAmount,
      transactionCount: transactionCount ?? this.transactionCount,
      frequency: frequency ?? this.frequency,
      trend: trend ?? this.trend,
      seasonality: seasonality ?? this.seasonality,
      volatility: volatility ?? this.volatility,
      efficiency: efficiency ?? this.efficiency,
      growthRate: growthRate ?? this.growthRate,
      percentageOfTotal: percentageOfTotal ?? this.percentageOfTotal,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'category': category,
      'totalAmount': totalAmount,
      'averageAmount': averageAmount,
      'transactionCount': transactionCount,
      'frequency': frequency,
      'trend': trend,
      'seasonality': seasonality,
      'volatility': volatility,
      'efficiency': efficiency,
      'growthRate': growthRate,
      'percentageOfTotal': percentageOfTotal,
    };
  }

  factory CategoryMetrics.fromJson(Map<String, dynamic> json) {
    return CategoryMetrics(
      category: json['category'],
      totalAmount: json['totalAmount'].toDouble(),
      averageAmount: json['averageAmount'].toDouble(),
      transactionCount: json['transactionCount'],
      frequency: json['frequency'].toDouble(),
      trend: json['trend'].toDouble(),
      seasonality: json['seasonality'].toDouble(),
      volatility: json['volatility'].toDouble(),
      efficiency: json['efficiency'].toDouble(),
      growthRate: json['growthRate'].toDouble(),
      percentageOfTotal: json['percentageOfTotal'].toDouble(),
    );
  }
}
