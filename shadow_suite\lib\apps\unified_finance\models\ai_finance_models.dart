import 'package:flutter/material.dart';
import 'finance_models.dart';

/// Financial Insight generated by AI analysis
class FinancialInsight {
  final String id;
  final InsightType type;
  final String title;
  final String description;
  final ImpactLevel impact;
  final bool actionable;
  final List<String> recommendations;
  final double confidence;
  final DateTime createdAt;
  final Map<String, dynamic>? metadata;

  const FinancialInsight({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.impact,
    required this.actionable,
    required this.recommendations,
    required this.confidence,
    required this.createdAt,
    this.metadata,
  });

  FinancialInsight copyWith({
    String? id,
    InsightType? type,
    String? title,
    String? description,
    ImpactLevel? impact,
    bool? actionable,
    List<String>? recommendations,
    double? confidence,
    DateTime? createdAt,
    Map<String, dynamic>? metadata,
  }) {
    return FinancialInsight(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      impact: impact ?? this.impact,
      actionable: actionable ?? this.actionable,
      recommendations: recommendations ?? this.recommendations,
      confidence: confidence ?? this.confidence,
      createdAt: createdAt ?? this.createdAt,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'impact': impact.name,
      'actionable': actionable,
      'recommendations': recommendations,
      'confidence': confidence,
      'createdAt': createdAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  factory FinancialInsight.fromJson(Map<String, dynamic> json) {
    return FinancialInsight(
      id: json['id'],
      type: InsightType.values.firstWhere((e) => e.name == json['type']),
      title: json['title'],
      description: json['description'],
      impact: ImpactLevel.values.firstWhere((e) => e.name == json['impact']),
      actionable: json['actionable'],
      recommendations: List<String>.from(json['recommendations']),
      confidence: json['confidence'].toDouble(),
      createdAt: DateTime.parse(json['createdAt']),
      metadata: json['metadata'],
    );
  }
}

/// Types of financial insights
enum InsightType {
  trend,
  warning,
  optimization,
  opportunity,
  prediction,
  anomaly,
  recommendation,
  pattern,
}

/// Impact level of financial insights
enum ImpactLevel { low, medium, high, critical }

/// Cash flow forecast model
class CashFlowForecast {
  final String id;
  final DateTime forecastDate;
  final double predictedIncome;
  final double predictedExpenses;
  final double netCashFlow;
  final double confidence;
  final List<CashFlowPeriod> periods;
  final DateTime generatedAt;

  const CashFlowForecast({
    required this.id,
    required this.forecastDate,
    required this.predictedIncome,
    required this.predictedExpenses,
    required this.netCashFlow,
    required this.confidence,
    required this.periods,
    required this.generatedAt,
  });

  CashFlowForecast copyWith({
    String? id,
    DateTime? forecastDate,
    double? predictedIncome,
    double? predictedExpenses,
    double? netCashFlow,
    double? confidence,
    List<CashFlowPeriod>? periods,
    DateTime? generatedAt,
  }) {
    return CashFlowForecast(
      id: id ?? this.id,
      forecastDate: forecastDate ?? this.forecastDate,
      predictedIncome: predictedIncome ?? this.predictedIncome,
      predictedExpenses: predictedExpenses ?? this.predictedExpenses,
      netCashFlow: netCashFlow ?? this.netCashFlow,
      confidence: confidence ?? this.confidence,
      periods: periods ?? this.periods,
      generatedAt: generatedAt ?? this.generatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'forecastDate': forecastDate.toIso8601String(),
      'predictedIncome': predictedIncome,
      'predictedExpenses': predictedExpenses,
      'netCashFlow': netCashFlow,
      'confidence': confidence,
      'periods': periods.map((p) => p.toJson()).toList(),
      'generatedAt': generatedAt.toIso8601String(),
    };
  }

  factory CashFlowForecast.fromJson(Map<String, dynamic> json) {
    return CashFlowForecast(
      id: json['id'],
      forecastDate: DateTime.parse(json['forecastDate']),
      predictedIncome: json['predictedIncome'].toDouble(),
      predictedExpenses: json['predictedExpenses'].toDouble(),
      netCashFlow: json['netCashFlow'].toDouble(),
      confidence: json['confidence'].toDouble(),
      periods: (json['periods'] as List)
          .map((p) => CashFlowPeriod.fromJson(p))
          .toList(),
      generatedAt: DateTime.parse(json['generatedAt']),
    );
  }
}

/// Cash flow period data
class CashFlowPeriod {
  final DateTime startDate;
  final DateTime endDate;
  final double income;
  final double expenses;
  final double netFlow;

  const CashFlowPeriod({
    required this.startDate,
    required this.endDate,
    required this.income,
    required this.expenses,
    required this.netFlow,
  });

  Map<String, dynamic> toJson() {
    return {
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'income': income,
      'expenses': expenses,
      'netFlow': netFlow,
    };
  }

  factory CashFlowPeriod.fromJson(Map<String, dynamic> json) {
    return CashFlowPeriod(
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      income: json['income'].toDouble(),
      expenses: json['expenses'].toDouble(),
      netFlow: json['netFlow'].toDouble(),
    );
  }
}

/// Investment recommendation model
class InvestmentRecommendation {
  final String id;
  final String title;
  final String description;
  final List<InvestmentOption> options;
  final RiskProfile riskProfile;
  final double expectedReturn;
  final double confidence;
  final DateTime generatedAt;

  const InvestmentRecommendation({
    required this.id,
    required this.title,
    required this.description,
    required this.options,
    required this.riskProfile,
    required this.expectedReturn,
    required this.confidence,
    required this.generatedAt,
  });

  InvestmentRecommendation copyWith({
    String? id,
    String? title,
    String? description,
    List<InvestmentOption>? options,
    RiskProfile? riskProfile,
    double? expectedReturn,
    double? confidence,
    DateTime? generatedAt,
  }) {
    return InvestmentRecommendation(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      options: options ?? this.options,
      riskProfile: riskProfile ?? this.riskProfile,
      expectedReturn: expectedReturn ?? this.expectedReturn,
      confidence: confidence ?? this.confidence,
      generatedAt: generatedAt ?? this.generatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'options': options.map((o) => o.toJson()).toList(),
      'riskProfile': riskProfile.name,
      'expectedReturn': expectedReturn,
      'confidence': confidence,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }

  factory InvestmentRecommendation.fromJson(Map<String, dynamic> json) {
    return InvestmentRecommendation(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      options: (json['options'] as List)
          .map((o) => InvestmentOption.fromJson(o))
          .toList(),
      riskProfile: RiskProfile.values.firstWhere(
        (e) => e.name == json['riskProfile'],
      ),
      expectedReturn: json['expectedReturn'].toDouble(),
      confidence: json['confidence'].toDouble(),
      generatedAt: DateTime.parse(json['generatedAt']),
    );
  }
}

/// Investment option model
class InvestmentOption {
  final String id;
  final String name;
  final String description;
  final String category;
  final RiskLevel riskLevel;
  final LiquidityLevel liquidity;
  final double expectedReturn;
  final double minimumInvestment;
  final List<String> features;

  const InvestmentOption({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.riskLevel,
    required this.liquidity,
    required this.expectedReturn,
    required this.minimumInvestment,
    required this.features,
  });

  InvestmentOption copyWith({
    String? id,
    String? name,
    String? description,
    String? category,
    RiskLevel? riskLevel,
    LiquidityLevel? liquidity,
    double? expectedReturn,
    double? minimumInvestment,
    List<String>? features,
  }) {
    return InvestmentOption(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      riskLevel: riskLevel ?? this.riskLevel,
      liquidity: liquidity ?? this.liquidity,
      expectedReturn: expectedReturn ?? this.expectedReturn,
      minimumInvestment: minimumInvestment ?? this.minimumInvestment,
      features: features ?? this.features,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'riskLevel': riskLevel.name,
      'liquidity': liquidity.name,
      'expectedReturn': expectedReturn,
      'minimumInvestment': minimumInvestment,
      'features': features,
    };
  }

  factory InvestmentOption.fromJson(Map<String, dynamic> json) {
    return InvestmentOption(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      category: json['category'],
      riskLevel: RiskLevel.values.firstWhere(
        (e) => e.name == json['riskLevel'],
      ),
      liquidity: LiquidityLevel.values.firstWhere(
        (e) => e.name == json['liquidity'],
      ),
      expectedReturn: json['expectedReturn'].toDouble(),
      minimumInvestment: json['minimumInvestment'].toDouble(),
      features: List<String>.from(json['features']),
    );
  }
}

/// Risk assessment model
class RiskAssessment {
  final String id;
  final double overallRiskScore;
  final RiskLevel riskLevel;
  final List<RiskFactor> riskFactors;
  final List<String> recommendations;
  final double confidence;
  final DateTime generatedAt;

  const RiskAssessment({
    required this.id,
    required this.overallRiskScore,
    required this.riskLevel,
    required this.riskFactors,
    required this.recommendations,
    required this.confidence,
    required this.generatedAt,
  });

  RiskAssessment copyWith({
    String? id,
    double? overallRiskScore,
    RiskLevel? riskLevel,
    List<RiskFactor>? riskFactors,
    List<String>? recommendations,
    double? confidence,
    DateTime? generatedAt,
  }) {
    return RiskAssessment(
      id: id ?? this.id,
      overallRiskScore: overallRiskScore ?? this.overallRiskScore,
      riskLevel: riskLevel ?? this.riskLevel,
      riskFactors: riskFactors ?? this.riskFactors,
      recommendations: recommendations ?? this.recommendations,
      confidence: confidence ?? this.confidence,
      generatedAt: generatedAt ?? this.generatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'overallRiskScore': overallRiskScore,
      'riskLevel': riskLevel.name,
      'riskFactors': riskFactors.map((r) => r.toJson()).toList(),
      'recommendations': recommendations,
      'confidence': confidence,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }

  factory RiskAssessment.fromJson(Map<String, dynamic> json) {
    return RiskAssessment(
      id: json['id'],
      overallRiskScore: json['overallRiskScore'].toDouble(),
      riskLevel: RiskLevel.values.firstWhere(
        (e) => e.name == json['riskLevel'],
      ),
      riskFactors: (json['riskFactors'] as List)
          .map((r) => RiskFactor.fromJson(r))
          .toList(),
      recommendations: List<String>.from(json['recommendations']),
      confidence: json['confidence'].toDouble(),
      generatedAt: DateTime.parse(json['generatedAt']),
    );
  }
}

/// Risk factor model
class RiskFactor {
  final String id;
  final String name;
  final String description;
  final RiskLevel level;
  final double impact;
  final double probability;
  final String category;

  const RiskFactor({
    required this.id,
    required this.name,
    required this.description,
    required this.level,
    required this.impact,
    required this.probability,
    required this.category,
  });

  RiskFactor copyWith({
    String? id,
    String? name,
    String? description,
    RiskLevel? level,
    double? impact,
    double? probability,
    String? category,
  }) {
    return RiskFactor(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      level: level ?? this.level,
      impact: impact ?? this.impact,
      probability: probability ?? this.probability,
      category: category ?? this.category,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'level': level.name,
      'impact': impact,
      'probability': probability,
      'category': category,
    };
  }

  factory RiskFactor.fromJson(Map<String, dynamic> json) {
    return RiskFactor(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      level: RiskLevel.values.firstWhere((e) => e.name == json['level']),
      impact: json['impact'].toDouble(),
      probability: json['probability'].toDouble(),
      category: json['category'],
    );
  }
}

/// Risk profile for investment recommendations
enum RiskProfile { conservative, moderate, aggressive }

/// Risk level for individual investments
enum RiskLevel { low, medium, high }

/// Liquidity level for investments
enum LiquidityLevel { low, medium, high }

/// Budget optimization suggestion
class BudgetOptimization {
  final String id;
  final String category;
  final double currentAmount;
  final double suggestedAmount;
  final double potentialSavings;
  final String reasoning;
  final List<String> actionSteps;
  final double confidence;
  final DateTime createdAt;

  const BudgetOptimization({
    required this.id,
    required this.category,
    required this.currentAmount,
    required this.suggestedAmount,
    required this.potentialSavings,
    required this.reasoning,
    required this.actionSteps,
    required this.confidence,
    required this.createdAt,
  });

  BudgetOptimization copyWith({
    String? id,
    String? category,
    double? currentAmount,
    double? suggestedAmount,
    double? potentialSavings,
    String? reasoning,
    List<String>? actionSteps,
    double? confidence,
    DateTime? createdAt,
  }) {
    return BudgetOptimization(
      id: id ?? this.id,
      category: category ?? this.category,
      currentAmount: currentAmount ?? this.currentAmount,
      suggestedAmount: suggestedAmount ?? this.suggestedAmount,
      potentialSavings: potentialSavings ?? this.potentialSavings,
      reasoning: reasoning ?? this.reasoning,
      actionSteps: actionSteps ?? this.actionSteps,
      confidence: confidence ?? this.confidence,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category': category,
      'currentAmount': currentAmount,
      'suggestedAmount': suggestedAmount,
      'potentialSavings': potentialSavings,
      'reasoning': reasoning,
      'actionSteps': actionSteps,
      'confidence': confidence,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory BudgetOptimization.fromJson(Map<String, dynamic> json) {
    return BudgetOptimization(
      id: json['id'],
      category: json['category'],
      currentAmount: json['currentAmount'].toDouble(),
      suggestedAmount: json['suggestedAmount'].toDouble(),
      potentialSavings: json['potentialSavings'].toDouble(),
      reasoning: json['reasoning'],
      actionSteps: List<String>.from(json['actionSteps']),
      confidence: json['confidence'].toDouble(),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Automation rule for financial tasks
class AutomationRule {
  final String id;
  final String name;
  final String description;
  final AutomationRuleType type;
  final bool isEnabled;
  final AutomationSchedule schedule;
  final Map<String, dynamic> parameters;
  final List<AutomationCondition> conditions;
  final List<AutomationAction> actions;
  DateTime? lastExecuted;
  final DateTime createdAt;

  AutomationRule({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.isEnabled,
    required this.schedule,
    required this.parameters,
    required this.conditions,
    required this.actions,
    this.lastExecuted,
    required this.createdAt,
  });

  AutomationRule copyWith({
    String? id,
    String? name,
    String? description,
    AutomationRuleType? type,
    bool? isEnabled,
    AutomationSchedule? schedule,
    Map<String, dynamic>? parameters,
    List<AutomationCondition>? conditions,
    List<AutomationAction>? actions,
    DateTime? lastExecuted,
    DateTime? createdAt,
  }) {
    return AutomationRule(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      isEnabled: isEnabled ?? this.isEnabled,
      schedule: schedule ?? this.schedule,
      parameters: parameters ?? this.parameters,
      conditions: conditions ?? this.conditions,
      actions: actions ?? this.actions,
      lastExecuted: lastExecuted ?? this.lastExecuted,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'isEnabled': isEnabled,
      'schedule': schedule.toJson(),
      'parameters': parameters,
      'conditions': conditions.map((c) => c.toJson()).toList(),
      'actions': actions.map((a) => a.toJson()).toList(),
      'lastExecuted': lastExecuted?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory AutomationRule.fromJson(Map<String, dynamic> json) {
    return AutomationRule(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      type: AutomationRuleType.values.firstWhere((e) => e.name == json['type']),
      isEnabled: json['isEnabled'],
      schedule: AutomationSchedule.fromJson(json['schedule']),
      parameters: Map<String, dynamic>.from(json['parameters']),
      conditions: (json['conditions'] as List)
          .map((c) => AutomationCondition.fromJson(c))
          .toList(),
      actions: (json['actions'] as List)
          .map((a) => AutomationAction.fromJson(a))
          .toList(),
      lastExecuted: json['lastExecuted'] != null
          ? DateTime.parse(json['lastExecuted'])
          : null,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Types of automation rules
enum AutomationRuleType {
  categorization,
  billPayment,
  budgetAlert,
  savingsTransfer,
}

/// Automation schedule configuration
class AutomationSchedule {
  final ScheduleFrequency frequency;
  final DateTime? specificTime;
  final List<int>? daysOfWeek;
  final int? dayOfMonth;

  const AutomationSchedule({
    required this.frequency,
    this.specificTime,
    this.daysOfWeek,
    this.dayOfMonth,
  });

  AutomationSchedule copyWith({
    ScheduleFrequency? frequency,
    DateTime? specificTime,
    List<int>? daysOfWeek,
    int? dayOfMonth,
  }) {
    return AutomationSchedule(
      frequency: frequency ?? this.frequency,
      specificTime: specificTime ?? this.specificTime,
      daysOfWeek: daysOfWeek ?? this.daysOfWeek,
      dayOfMonth: dayOfMonth ?? this.dayOfMonth,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'frequency': frequency.name,
      'specificTime': specificTime?.toIso8601String(),
      'daysOfWeek': daysOfWeek,
      'dayOfMonth': dayOfMonth,
    };
  }

  factory AutomationSchedule.fromJson(Map<String, dynamic> json) {
    return AutomationSchedule(
      frequency: ScheduleFrequency.values.firstWhere(
        (e) => e.name == json['frequency'],
      ),
      specificTime: json['specificTime'] != null
          ? DateTime.parse(json['specificTime'])
          : null,
      daysOfWeek: json['daysOfWeek'] != null
          ? List<int>.from(json['daysOfWeek'])
          : null,
      dayOfMonth: json['dayOfMonth'],
    );
  }
}

/// Schedule frequency options
enum ScheduleFrequency { immediate, daily, weekly, monthly }

/// Automation condition
class AutomationCondition {
  final String field;
  final ConditionOperator operator;
  final dynamic value;

  const AutomationCondition({
    required this.field,
    required this.operator,
    required this.value,
  });

  AutomationCondition copyWith({
    String? field,
    ConditionOperator? operator,
    dynamic value,
  }) {
    return AutomationCondition(
      field: field ?? this.field,
      operator: operator ?? this.operator,
      value: value ?? this.value,
    );
  }

  Map<String, dynamic> toJson() {
    return {'field': field, 'operator': operator.name, 'value': value};
  }

  factory AutomationCondition.fromJson(Map<String, dynamic> json) {
    return AutomationCondition(
      field: json['field'],
      operator: ConditionOperator.values.firstWhere(
        (e) => e.name == json['operator'],
      ),
      value: json['value'],
    );
  }
}

/// Condition operators
enum ConditionOperator {
  equals,
  notEquals,
  greaterThan,
  lessThan,
  contains,
  startsWith,
  endsWith,
}

/// Automation action
class AutomationAction {
  final ActionType type;
  final Map<String, dynamic> parameters;

  const AutomationAction({required this.type, required this.parameters});

  AutomationAction copyWith({
    ActionType? type,
    Map<String, dynamic>? parameters,
  }) {
    return AutomationAction(
      type: type ?? this.type,
      parameters: parameters ?? this.parameters,
    );
  }

  Map<String, dynamic> toJson() {
    return {'type': type.name, 'parameters': parameters};
  }

  factory AutomationAction.fromJson(Map<String, dynamic> json) {
    return AutomationAction(
      type: ActionType.values.firstWhere((e) => e.name == json['type']),
      parameters: Map<String, dynamic>.from(json['parameters']),
    );
  }
}

/// Action types
enum ActionType {
  categorize,
  createTransaction,
  sendNotification,
  transferFunds,
  updateBudget,
}

/// Automation event
class AutomationEvent {
  final String id;
  final AutomationEventType type;
  final String message;
  final DateTime timestamp;
  final String? ruleId;
  final String? transactionId;
  final Map<String, dynamic>? metadata;

  const AutomationEvent({
    required this.id,
    required this.type,
    required this.message,
    required this.timestamp,
    this.ruleId,
    this.transactionId,
    this.metadata,
  });

  AutomationEvent copyWith({
    String? id,
    AutomationEventType? type,
    String? message,
    DateTime? timestamp,
    String? ruleId,
    String? transactionId,
    Map<String, dynamic>? metadata,
  }) {
    return AutomationEvent(
      id: id ?? this.id,
      type: type ?? this.type,
      message: message ?? this.message,
      timestamp: timestamp ?? this.timestamp,
      ruleId: ruleId ?? this.ruleId,
      transactionId: transactionId ?? this.transactionId,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'ruleId': ruleId,
      'transactionId': transactionId,
      'metadata': metadata,
    };
  }

  factory AutomationEvent.fromJson(Map<String, dynamic> json) {
    return AutomationEvent(
      id: json['id'],
      type: AutomationEventType.values.firstWhere(
        (e) => e.name == json['type'],
      ),
      message: json['message'],
      timestamp: DateTime.parse(json['timestamp']),
      ruleId: json['ruleId'],
      transactionId: json['transactionId'],
      metadata: json['metadata'],
    );
  }
}

/// Automation event types
enum AutomationEventType {
  ruleAdded,
  ruleRemoved,
  ruleUpdated,
  ruleExecuted,
  ruleError,
  transactionAdded,
  budgetExceeded,
  goalReached,
  billDue,
  paymentExecuted,
  paymentFailed,
  categoryAssigned,
  budgetAlertSent,
  transferCompleted,
}

/// Analytics report model
class AnalyticsReport {
  final String id;
  final String title;
  final String description;
  final Map<String, dynamic> data;
  final List<String> insights;
  final DateTime generatedAt;
  final String reportType;

  const AnalyticsReport({
    required this.id,
    required this.title,
    required this.description,
    required this.data,
    required this.insights,
    required this.generatedAt,
    required this.reportType,
  });

  AnalyticsReport copyWith({
    String? id,
    String? title,
    String? description,
    Map<String, dynamic>? data,
    List<String>? insights,
    DateTime? generatedAt,
    String? reportType,
  }) {
    return AnalyticsReport(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      data: data ?? this.data,
      insights: insights ?? this.insights,
      generatedAt: generatedAt ?? this.generatedAt,
      reportType: reportType ?? this.reportType,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'data': data,
      'insights': insights,
      'generatedAt': generatedAt.toIso8601String(),
      'reportType': reportType,
    };
  }

  factory AnalyticsReport.fromJson(Map<String, dynamic> json) {
    return AnalyticsReport(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      data: Map<String, dynamic>.from(json['data']),
      insights: List<String>.from(json['insights']),
      generatedAt: DateTime.parse(json['generatedAt']),
      reportType: json['reportType'],
    );
  }
}

/// Predictive model for financial forecasting
class PredictiveModel {
  final String id;
  final ModelType modelType;
  final List<String> features;
  final double accuracy;
  final double precision;
  final double recall;
  final double f1Score;
  final double meanAbsoluteError;
  final double rootMeanSquareError;
  final double rSquared;
  final int trainingDataSize;
  final int validationDataSize;
  final DateTime trainedAt;
  final String version;

  const PredictiveModel({
    required this.id,
    required this.modelType,
    required this.features,
    required this.accuracy,
    required this.precision,
    required this.recall,
    required this.f1Score,
    required this.meanAbsoluteError,
    required this.rootMeanSquareError,
    required this.rSquared,
    required this.trainingDataSize,
    required this.validationDataSize,
    required this.trainedAt,
    required this.version,
  });

  PredictiveModel copyWith({
    String? id,
    ModelType? modelType,
    List<String>? features,
    double? accuracy,
    double? precision,
    double? recall,
    double? f1Score,
    double? meanAbsoluteError,
    double? rootMeanSquareError,
    double? rSquared,
    int? trainingDataSize,
    int? validationDataSize,
    DateTime? trainedAt,
    String? version,
  }) {
    return PredictiveModel(
      id: id ?? this.id,
      modelType: modelType ?? this.modelType,
      features: features ?? this.features,
      accuracy: accuracy ?? this.accuracy,
      precision: precision ?? this.precision,
      recall: recall ?? this.recall,
      f1Score: f1Score ?? this.f1Score,
      meanAbsoluteError: meanAbsoluteError ?? this.meanAbsoluteError,
      rootMeanSquareError: rootMeanSquareError ?? this.rootMeanSquareError,
      rSquared: rSquared ?? this.rSquared,
      trainingDataSize: trainingDataSize ?? this.trainingDataSize,
      validationDataSize: validationDataSize ?? this.validationDataSize,
      trainedAt: trainedAt ?? this.trainedAt,
      version: version ?? this.version,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'modelType': modelType.name,
      'features': features,
      'accuracy': accuracy,
      'precision': precision,
      'recall': recall,
      'f1Score': f1Score,
      'meanAbsoluteError': meanAbsoluteError,
      'rootMeanSquareError': rootMeanSquareError,
      'rSquared': rSquared,
      'trainingDataSize': trainingDataSize,
      'validationDataSize': validationDataSize,
      'trainedAt': trainedAt.toIso8601String(),
      'version': version,
    };
  }

  factory PredictiveModel.fromJson(Map<String, dynamic> json) {
    return PredictiveModel(
      id: json['id'],
      modelType: ModelType.values.firstWhere(
        (e) => e.name == json['modelType'],
      ),
      features: List<String>.from(json['features']),
      accuracy: json['accuracy'].toDouble(),
      precision: json['precision'].toDouble(),
      recall: json['recall'].toDouble(),
      f1Score: json['f1Score'].toDouble(),
      meanAbsoluteError: json['meanAbsoluteError'].toDouble(),
      rootMeanSquareError: json['rootMeanSquareError'].toDouble(),
      rSquared: json['rSquared'].toDouble(),
      trainingDataSize: json['trainingDataSize'],
      validationDataSize: json['validationDataSize'],
      trainedAt: DateTime.parse(json['trainedAt']),
      version: json['version'],
    );
  }
}

/// Performance benchmark model
class PerformanceBenchmark {
  final String id;
  final String benchmarkType;
  final Map<String, double> userMetrics;
  final Map<String, double> benchmarkMetrics;
  final double percentileRanking;
  final double performanceScore;
  final List<String> strengths;
  final List<String> weaknesses;
  final List<String> improvementAreas;
  final DateTime generatedAt;

  const PerformanceBenchmark({
    required this.id,
    required this.benchmarkType,
    required this.userMetrics,
    required this.benchmarkMetrics,
    required this.percentileRanking,
    required this.performanceScore,
    required this.strengths,
    required this.weaknesses,
    required this.improvementAreas,
    required this.generatedAt,
  });

  PerformanceBenchmark copyWith({
    String? id,
    String? benchmarkType,
    Map<String, double>? userMetrics,
    Map<String, double>? benchmarkMetrics,
    double? percentileRanking,
    double? performanceScore,
    List<String>? strengths,
    List<String>? weaknesses,
    List<String>? improvementAreas,
    DateTime? generatedAt,
  }) {
    return PerformanceBenchmark(
      id: id ?? this.id,
      benchmarkType: benchmarkType ?? this.benchmarkType,
      userMetrics: userMetrics ?? this.userMetrics,
      benchmarkMetrics: benchmarkMetrics ?? this.benchmarkMetrics,
      percentileRanking: percentileRanking ?? this.percentileRanking,
      performanceScore: performanceScore ?? this.performanceScore,
      strengths: strengths ?? this.strengths,
      weaknesses: weaknesses ?? this.weaknesses,
      improvementAreas: improvementAreas ?? this.improvementAreas,
      generatedAt: generatedAt ?? this.generatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'benchmarkType': benchmarkType,
      'userMetrics': userMetrics,
      'benchmarkMetrics': benchmarkMetrics,
      'percentileRanking': percentileRanking,
      'performanceScore': performanceScore,
      'strengths': strengths,
      'weaknesses': weaknesses,
      'improvementAreas': improvementAreas,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }

  factory PerformanceBenchmark.fromJson(Map<String, dynamic> json) {
    return PerformanceBenchmark(
      id: json['id'],
      benchmarkType: json['benchmarkType'],
      userMetrics: Map<String, double>.from(json['userMetrics']),
      benchmarkMetrics: Map<String, double>.from(json['benchmarkMetrics']),
      percentileRanking: json['percentileRanking'].toDouble(),
      performanceScore: json['performanceScore'].toDouble(),
      strengths: List<String>.from(json['strengths']),
      weaknesses: List<String>.from(json['weaknesses']),
      improvementAreas: List<String>.from(json['improvementAreas']),
      generatedAt: DateTime.parse(json['generatedAt']),
    );
  }
}

/// Model type enum for machine learning
enum ModelType { linearRegression, randomForest, neuralNetwork, svm }

/// Feature vector for machine learning
class FeatureVector {
  final String id;
  final List<double> features;
  final String label;
  final DateTime timestamp;

  const FeatureVector({
    required this.id,
    required this.features,
    required this.label,
    required this.timestamp,
  });

  FeatureVector copyWith({
    String? id,
    List<double>? features,
    String? label,
    DateTime? timestamp,
  }) {
    return FeatureVector(
      id: id ?? this.id,
      features: features ?? this.features,
      label: label ?? this.label,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'features': features,
      'label': label,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory FeatureVector.fromJson(Map<String, dynamic> json) {
    return FeatureVector(
      id: json['id'],
      features: List<double>.from(json['features']),
      label: json['label'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

/// Recurring transaction pattern
class RecurringTransaction {
  final String id;
  final String payee;
  final double averageAmount;
  final RecurringFrequency frequency;
  final DateTime nextExpectedDate;
  final double confidence;
  final List<FinanceTransaction> transactions;
  final String category;
  final bool isActive;
  final DateTime createdAt;

  const RecurringTransaction({
    required this.id,
    required this.payee,
    required this.averageAmount,
    required this.frequency,
    required this.nextExpectedDate,
    required this.confidence,
    required this.transactions,
    required this.category,
    required this.isActive,
    required this.createdAt,
  });

  RecurringTransaction copyWith({
    String? id,
    String? payee,
    double? averageAmount,
    RecurringFrequency? frequency,
    DateTime? nextExpectedDate,
    double? confidence,
    List<FinanceTransaction>? transactions,
    String? category,
    bool? isActive,
    DateTime? createdAt,
  }) {
    return RecurringTransaction(
      id: id ?? this.id,
      payee: payee ?? this.payee,
      averageAmount: averageAmount ?? this.averageAmount,
      frequency: frequency ?? this.frequency,
      nextExpectedDate: nextExpectedDate ?? this.nextExpectedDate,
      confidence: confidence ?? this.confidence,
      transactions: transactions ?? this.transactions,
      category: category ?? this.category,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'payee': payee,
      'averageAmount': averageAmount,
      'frequency': frequency.name,
      'nextExpectedDate': nextExpectedDate.toIso8601String(),
      'confidence': confidence,
      'transactions': transactions.map((t) => t.toJson()).toList(),
      'category': category,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory RecurringTransaction.fromJson(Map<String, dynamic> json) {
    return RecurringTransaction(
      id: json['id'],
      payee: json['payee'],
      averageAmount: json['averageAmount'].toDouble(),
      frequency: RecurringFrequency.values.firstWhere(
        (e) => e.name == json['frequency'],
      ),
      nextExpectedDate: DateTime.parse(json['nextExpectedDate']),
      confidence: json['confidence'].toDouble(),
      transactions: (json['transactions'] as List)
          .map((t) => FinanceTransaction.fromJson(t))
          .toList(),
      category: json['category'],
      isActive: json['isActive'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Recurring frequency options
enum RecurringFrequency { weekly, biweekly, monthly, quarterly, yearly }

/// Recurring pattern analysis result
class RecurringPattern {
  final double averageAmount;
  final RecurringFrequency frequency;
  final DateTime nextExpectedDate;
  final double confidence;

  const RecurringPattern({
    required this.averageAmount,
    required this.frequency,
    required this.nextExpectedDate,
    required this.confidence,
  });

  RecurringPattern copyWith({
    double? averageAmount,
    RecurringFrequency? frequency,
    DateTime? nextExpectedDate,
    double? confidence,
  }) {
    return RecurringPattern(
      averageAmount: averageAmount ?? this.averageAmount,
      frequency: frequency ?? this.frequency,
      nextExpectedDate: nextExpectedDate ?? this.nextExpectedDate,
      confidence: confidence ?? this.confidence,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'averageAmount': averageAmount,
      'frequency': frequency.name,
      'nextExpectedDate': nextExpectedDate.toIso8601String(),
      'confidence': confidence,
    };
  }

  factory RecurringPattern.fromJson(Map<String, dynamic> json) {
    return RecurringPattern(
      averageAmount: json['averageAmount'].toDouble(),
      frequency: RecurringFrequency.values.firstWhere(
        (e) => e.name == json['frequency'],
      ),
      nextExpectedDate: DateTime.parse(json['nextExpectedDate']),
      confidence: json['confidence'].toDouble(),
    );
  }
}

/// Expense prediction
class ExpensePrediction {
  final String id;
  final String payee;
  final String category;
  final double predictedAmount;
  final DateTime predictedDate;
  final double confidence;
  final PredictionType type;
  final List<String> basedOnTransactions;
  final DateTime createdAt;

  const ExpensePrediction({
    required this.id,
    required this.payee,
    required this.category,
    required this.predictedAmount,
    required this.predictedDate,
    required this.confidence,
    required this.type,
    required this.basedOnTransactions,
    required this.createdAt,
  });

  ExpensePrediction copyWith({
    String? id,
    String? payee,
    String? category,
    double? predictedAmount,
    DateTime? predictedDate,
    double? confidence,
    PredictionType? type,
    List<String>? basedOnTransactions,
    DateTime? createdAt,
  }) {
    return ExpensePrediction(
      id: id ?? this.id,
      payee: payee ?? this.payee,
      category: category ?? this.category,
      predictedAmount: predictedAmount ?? this.predictedAmount,
      predictedDate: predictedDate ?? this.predictedDate,
      confidence: confidence ?? this.confidence,
      type: type ?? this.type,
      basedOnTransactions: basedOnTransactions ?? this.basedOnTransactions,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'payee': payee,
      'category': category,
      'predictedAmount': predictedAmount,
      'predictedDate': predictedDate.toIso8601String(),
      'confidence': confidence,
      'type': type.name,
      'basedOnTransactions': basedOnTransactions,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory ExpensePrediction.fromJson(Map<String, dynamic> json) {
    return ExpensePrediction(
      id: json['id'],
      payee: json['payee'],
      category: json['category'],
      predictedAmount: json['predictedAmount'].toDouble(),
      predictedDate: DateTime.parse(json['predictedDate']),
      confidence: json['confidence'].toDouble(),
      type: PredictionType.values.firstWhere((e) => e.name == json['type']),
      basedOnTransactions: List<String>.from(json['basedOnTransactions']),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Prediction types
enum PredictionType { recurring, seasonal, trend, anomaly }

/// Statistical summary of financial data
class StatisticalSummary {
  final String id;
  final int totalTransactions;
  final double totalExpenses;
  final double totalIncome;
  final double averageExpense;
  final double averageIncome;
  final double medianExpense;
  final double medianIncome;
  final double expenseStandardDeviation;
  final double incomeStandardDeviation;
  final double expenseVariance;
  final double incomeVariance;
  final double expenseSkewness;
  final double incomeSkewness;
  final double expenseKurtosis;
  final double incomeKurtosis;
  final double correlationCoefficient;
  final DateTime generatedAt;

  const StatisticalSummary({
    required this.id,
    required this.totalTransactions,
    required this.totalExpenses,
    required this.totalIncome,
    required this.averageExpense,
    required this.averageIncome,
    required this.medianExpense,
    required this.medianIncome,
    required this.expenseStandardDeviation,
    required this.incomeStandardDeviation,
    required this.expenseVariance,
    required this.incomeVariance,
    required this.expenseSkewness,
    required this.incomeSkewness,
    required this.expenseKurtosis,
    required this.incomeKurtosis,
    required this.correlationCoefficient,
    required this.generatedAt,
  });

  StatisticalSummary copyWith({
    String? id,
    int? totalTransactions,
    double? totalExpenses,
    double? totalIncome,
    double? averageExpense,
    double? averageIncome,
    double? medianExpense,
    double? medianIncome,
    double? expenseStandardDeviation,
    double? incomeStandardDeviation,
    double? expenseVariance,
    double? incomeVariance,
    double? expenseSkewness,
    double? incomeSkewness,
    double? expenseKurtosis,
    double? incomeKurtosis,
    double? correlationCoefficient,
    DateTime? generatedAt,
  }) {
    return StatisticalSummary(
      id: id ?? this.id,
      totalTransactions: totalTransactions ?? this.totalTransactions,
      totalExpenses: totalExpenses ?? this.totalExpenses,
      totalIncome: totalIncome ?? this.totalIncome,
      averageExpense: averageExpense ?? this.averageExpense,
      averageIncome: averageIncome ?? this.averageIncome,
      medianExpense: medianExpense ?? this.medianExpense,
      medianIncome: medianIncome ?? this.medianIncome,
      expenseStandardDeviation:
          expenseStandardDeviation ?? this.expenseStandardDeviation,
      incomeStandardDeviation:
          incomeStandardDeviation ?? this.incomeStandardDeviation,
      expenseVariance: expenseVariance ?? this.expenseVariance,
      incomeVariance: incomeVariance ?? this.incomeVariance,
      expenseSkewness: expenseSkewness ?? this.expenseSkewness,
      incomeSkewness: incomeSkewness ?? this.incomeSkewness,
      expenseKurtosis: expenseKurtosis ?? this.expenseKurtosis,
      incomeKurtosis: incomeKurtosis ?? this.incomeKurtosis,
      correlationCoefficient:
          correlationCoefficient ?? this.correlationCoefficient,
      generatedAt: generatedAt ?? this.generatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'totalTransactions': totalTransactions,
      'totalExpenses': totalExpenses,
      'totalIncome': totalIncome,
      'averageExpense': averageExpense,
      'averageIncome': averageIncome,
      'medianExpense': medianExpense,
      'medianIncome': medianIncome,
      'expenseStandardDeviation': expenseStandardDeviation,
      'incomeStandardDeviation': incomeStandardDeviation,
      'expenseVariance': expenseVariance,
      'incomeVariance': incomeVariance,
      'expenseSkewness': expenseSkewness,
      'incomeSkewness': incomeSkewness,
      'expenseKurtosis': expenseKurtosis,
      'incomeKurtosis': incomeKurtosis,
      'correlationCoefficient': correlationCoefficient,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }

  factory StatisticalSummary.fromJson(Map<String, dynamic> json) {
    return StatisticalSummary(
      id: json['id'],
      totalTransactions: json['totalTransactions'],
      totalExpenses: json['totalExpenses'].toDouble(),
      totalIncome: json['totalIncome'].toDouble(),
      averageExpense: json['averageExpense'].toDouble(),
      averageIncome: json['averageIncome'].toDouble(),
      medianExpense: json['medianExpense'].toDouble(),
      medianIncome: json['medianIncome'].toDouble(),
      expenseStandardDeviation: json['expenseStandardDeviation'].toDouble(),
      incomeStandardDeviation: json['incomeStandardDeviation'].toDouble(),
      expenseVariance: json['expenseVariance'].toDouble(),
      incomeVariance: json['incomeVariance'].toDouble(),
      expenseSkewness: json['expenseSkewness'].toDouble(),
      incomeSkewness: json['incomeSkewness'].toDouble(),
      expenseKurtosis: json['expenseKurtosis'].toDouble(),
      incomeKurtosis: json['incomeKurtosis'].toDouble(),
      correlationCoefficient: json['correlationCoefficient'].toDouble(),
      generatedAt: DateTime.parse(json['generatedAt']),
    );
  }
}

/// Trend analysis result
class TrendAnalysis {
  final String id;
  final int periodDays;
  final List<TrendData> trendData;
  final TrendDirection expenseTrendDirection;
  final double expenseTrendStrength;
  final TrendDirection incomeTrendDirection;
  final double incomeTrendStrength;
  final TrendDirection cashFlowTrendDirection;
  final double cashFlowTrendStrength;
  final double seasonalityScore;
  final double volatilityIndex;
  final DateTime generatedAt;

  const TrendAnalysis({
    required this.id,
    required this.periodDays,
    required this.trendData,
    required this.expenseTrendDirection,
    required this.expenseTrendStrength,
    required this.incomeTrendDirection,
    required this.incomeTrendStrength,
    required this.cashFlowTrendDirection,
    required this.cashFlowTrendStrength,
    required this.seasonalityScore,
    required this.volatilityIndex,
    required this.generatedAt,
  });

  TrendAnalysis copyWith({
    String? id,
    int? periodDays,
    List<TrendData>? trendData,
    TrendDirection? expenseTrendDirection,
    double? expenseTrendStrength,
    TrendDirection? incomeTrendDirection,
    double? incomeTrendStrength,
    TrendDirection? cashFlowTrendDirection,
    double? cashFlowTrendStrength,
    double? seasonalityScore,
    double? volatilityIndex,
    DateTime? generatedAt,
  }) {
    return TrendAnalysis(
      id: id ?? this.id,
      periodDays: periodDays ?? this.periodDays,
      trendData: trendData ?? this.trendData,
      expenseTrendDirection:
          expenseTrendDirection ?? this.expenseTrendDirection,
      expenseTrendStrength: expenseTrendStrength ?? this.expenseTrendStrength,
      incomeTrendDirection: incomeTrendDirection ?? this.incomeTrendDirection,
      incomeTrendStrength: incomeTrendStrength ?? this.incomeTrendStrength,
      cashFlowTrendDirection:
          cashFlowTrendDirection ?? this.cashFlowTrendDirection,
      cashFlowTrendStrength:
          cashFlowTrendStrength ?? this.cashFlowTrendStrength,
      seasonalityScore: seasonalityScore ?? this.seasonalityScore,
      volatilityIndex: volatilityIndex ?? this.volatilityIndex,
      generatedAt: generatedAt ?? this.generatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'periodDays': periodDays,
      'trendData': trendData.map((t) => t.toJson()).toList(),
      'expenseTrendDirection': expenseTrendDirection.name,
      'expenseTrendStrength': expenseTrendStrength,
      'incomeTrendDirection': incomeTrendDirection.name,
      'incomeTrendStrength': incomeTrendStrength,
      'cashFlowTrendDirection': cashFlowTrendDirection.name,
      'cashFlowTrendStrength': cashFlowTrendStrength,
      'seasonalityScore': seasonalityScore,
      'volatilityIndex': volatilityIndex,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }

  factory TrendAnalysis.fromJson(Map<String, dynamic> json) {
    return TrendAnalysis(
      id: json['id'],
      periodDays: json['periodDays'],
      trendData: (json['trendData'] as List)
          .map((t) => TrendData.fromJson(t))
          .toList(),
      expenseTrendDirection: TrendDirection.values.firstWhere(
        (e) => e.name == json['expenseTrendDirection'],
      ),
      expenseTrendStrength: json['expenseTrendStrength'].toDouble(),
      incomeTrendDirection: TrendDirection.values.firstWhere(
        (e) => e.name == json['incomeTrendDirection'],
      ),
      incomeTrendStrength: json['incomeTrendStrength'].toDouble(),
      cashFlowTrendDirection: TrendDirection.values.firstWhere(
        (e) => e.name == json['cashFlowTrendDirection'],
      ),
      cashFlowTrendStrength: json['cashFlowTrendStrength'].toDouble(),
      seasonalityScore: json['seasonalityScore'].toDouble(),
      volatilityIndex: json['volatilityIndex'].toDouble(),
      generatedAt: DateTime.parse(json['generatedAt']),
    );
  }
}

/// Trend direction enumeration
enum TrendDirection { increasing, decreasing, stable }

/// Trend data point
class TrendData {
  final DateTime period;
  final double totalExpenses;
  final double totalIncome;
  final double netCashFlow;
  final int transactionCount;

  const TrendData({
    required this.period,
    required this.totalExpenses,
    required this.totalIncome,
    required this.netCashFlow,
    required this.transactionCount,
  });

  TrendData copyWith({
    DateTime? period,
    double? totalExpenses,
    double? totalIncome,
    double? netCashFlow,
    int? transactionCount,
  }) {
    return TrendData(
      period: period ?? this.period,
      totalExpenses: totalExpenses ?? this.totalExpenses,
      totalIncome: totalIncome ?? this.totalIncome,
      netCashFlow: netCashFlow ?? this.netCashFlow,
      transactionCount: transactionCount ?? this.transactionCount,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'period': period.toIso8601String(),
      'totalExpenses': totalExpenses,
      'totalIncome': totalIncome,
      'netCashFlow': netCashFlow,
      'transactionCount': transactionCount,
    };
  }

  factory TrendData.fromJson(Map<String, dynamic> json) {
    return TrendData(
      period: DateTime.parse(json['period']),
      totalExpenses: json['totalExpenses'].toDouble(),
      totalIncome: json['totalIncome'].toDouble(),
      netCashFlow: json['netCashFlow'].toDouble(),
      transactionCount: json['transactionCount'],
    );
  }
}

/// Trend result with direction and strength
class TrendResult {
  final TrendDirection direction;
  final double strength;

  const TrendResult(this.direction, this.strength);

  TrendResult copyWith({TrendDirection? direction, double? strength}) {
    return TrendResult(direction ?? this.direction, strength ?? this.strength);
  }

  Map<String, dynamic> toJson() {
    return {'direction': direction.name, 'strength': strength};
  }

  factory TrendResult.fromJson(Map<String, dynamic> json) {
    return TrendResult(
      TrendDirection.values.firstWhere((e) => e.name == json['direction']),
      json['strength'].toDouble(),
    );
  }
}

/// Category analysis result
class CategoryAnalysis {
  final String id;
  final Map<String, CategoryMetrics> categoryMetrics;
  final List<String> topCategories;
  final List<String> growingCategories;
  final List<String> decliningCategories;
  final List<String> volatileCategories;
  final List<String> efficientCategories;
  final DateTime generatedAt;

  const CategoryAnalysis({
    required this.id,
    required this.categoryMetrics,
    required this.topCategories,
    required this.growingCategories,
    required this.decliningCategories,
    required this.volatileCategories,
    required this.efficientCategories,
    required this.generatedAt,
  });

  CategoryAnalysis copyWith({
    String? id,
    Map<String, CategoryMetrics>? categoryMetrics,
    List<String>? topCategories,
    List<String>? growingCategories,
    List<String>? decliningCategories,
    List<String>? volatileCategories,
    List<String>? efficientCategories,
    DateTime? generatedAt,
  }) {
    return CategoryAnalysis(
      id: id ?? this.id,
      categoryMetrics: categoryMetrics ?? this.categoryMetrics,
      topCategories: topCategories ?? this.topCategories,
      growingCategories: growingCategories ?? this.growingCategories,
      decliningCategories: decliningCategories ?? this.decliningCategories,
      volatileCategories: volatileCategories ?? this.volatileCategories,
      efficientCategories: efficientCategories ?? this.efficientCategories,
      generatedAt: generatedAt ?? this.generatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'categoryMetrics': categoryMetrics.map((k, v) => MapEntry(k, v.toJson())),
      'topCategories': topCategories,
      'growingCategories': growingCategories,
      'decliningCategories': decliningCategories,
      'volatileCategories': volatileCategories,
      'efficientCategories': efficientCategories,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }

  factory CategoryAnalysis.fromJson(Map<String, dynamic> json) {
    final categoryMetricsMap = Map<String, dynamic>.from(
      json['categoryMetrics'],
    );
    final categoryMetrics = <String, CategoryMetrics>{};

    categoryMetricsMap.forEach((key, value) {
      categoryMetrics[key] = CategoryMetrics.fromJson(value);
    });

    return CategoryAnalysis(
      id: json['id'],
      categoryMetrics: categoryMetrics,
      topCategories: List<String>.from(json['topCategories']),
      growingCategories: List<String>.from(json['growingCategories']),
      decliningCategories: List<String>.from(json['decliningCategories']),
      volatileCategories: List<String>.from(json['volatileCategories']),
      efficientCategories: List<String>.from(json['efficientCategories']),
      generatedAt: DateTime.parse(json['generatedAt']),
    );
  }
}

/// Category metrics
class CategoryMetrics {
  final String category;
  final double totalAmount;
  final double averageAmount;
  final int transactionCount;
  final double frequency;
  final double trend;
  final double seasonality;
  final double volatility;
  final double efficiency;
  final double growthRate;
  final double percentageOfTotal;

  const CategoryMetrics({
    required this.category,
    required this.totalAmount,
    required this.averageAmount,
    required this.transactionCount,
    required this.frequency,
    required this.trend,
    required this.seasonality,
    required this.volatility,
    required this.efficiency,
    required this.growthRate,
    required this.percentageOfTotal,
  });

  CategoryMetrics copyWith({
    String? category,
    double? totalAmount,
    double? averageAmount,
    int? transactionCount,
    double? frequency,
    double? trend,
    double? seasonality,
    double? volatility,
    double? efficiency,
    double? growthRate,
    double? percentageOfTotal,
  }) {
    return CategoryMetrics(
      category: category ?? this.category,
      totalAmount: totalAmount ?? this.totalAmount,
      averageAmount: averageAmount ?? this.averageAmount,
      transactionCount: transactionCount ?? this.transactionCount,
      frequency: frequency ?? this.frequency,
      trend: trend ?? this.trend,
      seasonality: seasonality ?? this.seasonality,
      volatility: volatility ?? this.volatility,
      efficiency: efficiency ?? this.efficiency,
      growthRate: growthRate ?? this.growthRate,
      percentageOfTotal: percentageOfTotal ?? this.percentageOfTotal,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'category': category,
      'totalAmount': totalAmount,
      'averageAmount': averageAmount,
      'transactionCount': transactionCount,
      'frequency': frequency,
      'trend': trend,
      'seasonality': seasonality,
      'volatility': volatility,
      'efficiency': efficiency,
      'growthRate': growthRate,
      'percentageOfTotal': percentageOfTotal,
    };
  }

  factory CategoryMetrics.fromJson(Map<String, dynamic> json) {
    return CategoryMetrics(
      category: json['category'],
      totalAmount: json['totalAmount'].toDouble(),
      averageAmount: json['averageAmount'].toDouble(),
      transactionCount: json['transactionCount'],
      frequency: json['frequency'].toDouble(),
      trend: json['trend'].toDouble(),
      seasonality: json['seasonality'].toDouble(),
      volatility: json['volatility'].toDouble(),
      efficiency: json['efficiency'].toDouble(),
      growthRate: json['growthRate'].toDouble(),
      percentageOfTotal: json['percentageOfTotal'].toDouble(),
    );
  }
}
