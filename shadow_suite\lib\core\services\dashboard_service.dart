import 'dart:async';
import 'dart:convert';
import '../database/database_service.dart';
import '../services/error_handler.dart' as error_handler;
import '../widgets/dashboard_widgets.dart';

/// Dashboard service for managing real-time statistics and customizable widgets
class DashboardService {
  static bool _isInitialized = false;
  static final StreamController<DashboardUpdate> _updateController =
      StreamController<DashboardUpdate>.broadcast();
  static Timer? _refreshTimer;
  static DashboardConfiguration _currentConfig =
      DashboardConfiguration.defaultConfig();

  /// Initialize dashboard service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize database tables
      await _initializeDatabaseTables();

      // Load dashboard configuration
      await _loadDashboardConfiguration();

      // Start real-time updates
      _startRealTimeUpdates();

      _isInitialized = true;
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.operation,
        context: 'Initialize dashboard service',
      );
    }
  }

  /// Initialize database tables
  static Future<void> _initializeDatabaseTables() async {
    await DatabaseService.safeExecute('''
      CREATE TABLE IF NOT EXISTS dashboard_config (
        id TEXT PRIMARY KEY,
        widget_type TEXT NOT NULL,
        position_x INTEGER NOT NULL,
        position_y INTEGER NOT NULL,
        width INTEGER NOT NULL,
        height INTEGER NOT NULL,
        settings TEXT DEFAULT '{}',
        is_active INTEGER DEFAULT 1,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    await DatabaseService.safeExecute('''
      CREATE TABLE IF NOT EXISTS dashboard_stats (
        id TEXT PRIMARY KEY,
        app_id TEXT NOT NULL,
        stat_type TEXT NOT NULL,
        value TEXT NOT NULL,
        timestamp INTEGER NOT NULL
      )
    ''');

    await DatabaseService.safeExecute('''
      CREATE TABLE IF NOT EXISTS dashboard_activities (
        id TEXT PRIMARY KEY,
        app_id TEXT NOT NULL,
        activity_type TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT,
        timestamp INTEGER NOT NULL,
        metadata TEXT DEFAULT '{}'
      )
    ''');
  }

  /// Load dashboard configuration
  static Future<void> _loadDashboardConfiguration() async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM dashboard_config WHERE is_active = 1 ORDER BY position_y, position_x',
      );

      final activeWidgets = <DashboardWidget>[];

      for (final row in results) {
        final widget = DashboardWidget(
          id: row['id'],
          type: DashboardWidgetType.values.firstWhere(
            (type) => type.toString().split('.').last == row['widget_type'],
            orElse: () => DashboardWidgetType.quickStats,
          ),
          position: DashboardPosition(
            x: row['position_x'],
            y: row['position_y'],
          ),
          size: DashboardSize(width: row['width'], height: row['height']),
          settings: jsonDecode(row['settings'] ?? '{}'),
          isActive: row['is_active'] == 1,
        );

        activeWidgets.add(widget);
      }

      _currentConfig = DashboardConfiguration(
        activeWidgets: activeWidgets,
        availableWidgets: _getAvailableWidgets(),
        gridSize: const DashboardSize(width: 12, height: 8),
        refreshInterval: const Duration(seconds: 30),
      );
    } catch (e) {
      _currentConfig = DashboardConfiguration.defaultConfig();
    }
  }

  /// Get available widgets
  static List<DashboardWidget> _getAvailableWidgets() {
    return [
      DashboardWidget(
        id: 'quick_stats',
        type: DashboardWidgetType.quickStats,
        position: const DashboardPosition(x: 0, y: 0),
        size: const DashboardSize(width: 12, height: 2),
        settings: {},
        isActive: false,
      ),
      DashboardWidget(
        id: 'recent_activity',
        type: DashboardWidgetType.recentActivity,
        position: const DashboardPosition(x: 0, y: 2),
        size: const DashboardSize(width: 6, height: 3),
        settings: {},
        isActive: false,
      ),
      DashboardWidget(
        id: 'storage_usage',
        type: DashboardWidgetType.storageUsage,
        position: const DashboardPosition(x: 6, y: 2),
        size: const DashboardSize(width: 6, height: 3),
        settings: {},
        isActive: false,
      ),
      DashboardWidget(
        id: 'performance_monitor',
        type: DashboardWidgetType.performanceMonitor,
        position: const DashboardPosition(x: 0, y: 5),
        size: const DashboardSize(width: 4, height: 3),
        settings: {},
        isActive: false,
      ),
      DashboardWidget(
        id: 'ai_processing',
        type: DashboardWidgetType.aiProcessingStatus,
        position: const DashboardPosition(x: 4, y: 5),
        size: const DashboardSize(width: 4, height: 3),
        settings: {},
        isActive: false,
      ),
      DashboardWidget(
        id: 'quick_actions',
        type: DashboardWidgetType.quickActions,
        position: const DashboardPosition(x: 8, y: 5),
        size: const DashboardSize(width: 4, height: 3),
        settings: {},
        isActive: false,
      ),
    ];
  }

  /// Start real-time updates
  static void _startRealTimeUpdates() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(_currentConfig.refreshInterval, (
      timer,
    ) async {
      await _collectAndBroadcastUpdates();
    });
  }

  /// Collect and broadcast updates
  static Future<void> _collectAndBroadcastUpdates() async {
    try {
      // Collect statistics from all apps
      final stats = await _collectAppStatistics();

      // Collect recent activities
      final activities = await _collectRecentActivities();

      // Collect performance metrics
      final performance = await _collectPerformanceMetrics();

      // Broadcast update
      _notifyUpdate(
        DashboardUpdate(
          statistics: stats,
          activities: activities,
          performance: performance,
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      // Continue with next update cycle
    }
  }

  /// Collect app statistics
  static Future<Map<String, Map<String, dynamic>>>
  _collectAppStatistics() async {
    final stats = <String, Map<String, dynamic>>{};

    try {
      // SmartGallery+ statistics
      final galleryResults = await DatabaseService.safeQuery(
        'SELECT COUNT(*) as total_media, SUM(size) as total_size FROM smart_gallery_items',
      );

      if (galleryResults.isNotEmpty) {
        stats['smart_gallery'] = {
          'total_media': galleryResults.first['total_media'] ?? 0,
          'total_size': galleryResults.first['total_size'] ?? 0,
        };
      }

      // Money Manager statistics
      final moneyResults = await DatabaseService.safeQuery(
        'SELECT COUNT(*) as total_transactions, SUM(amount) as total_amount FROM transactions',
      );

      if (moneyResults.isNotEmpty) {
        stats['money_manager'] = {
          'total_transactions': moneyResults.first['total_transactions'] ?? 0,
          'total_amount': moneyResults.first['total_amount'] ?? 0.0,
        };
      }

      // File Manager statistics
      final fileResults = await DatabaseService.safeQuery(
        'SELECT COUNT(*) as total_files FROM file_manager_items',
      );

      if (fileResults.isNotEmpty) {
        stats['file_manager'] = {
          'total_files': fileResults.first['total_files'] ?? 0,
        };
      }

      // Shadow Player statistics
      final playerResults = await DatabaseService.safeQuery(
        'SELECT COUNT(*) as total_media FROM shadow_player_media',
      );

      if (playerResults.isNotEmpty) {
        stats['shadow_player'] = {
          'total_media': playerResults.first['total_media'] ?? 0,
        };
      }
    } catch (e) {
      // Return empty stats on error
    }

    return stats;
  }

  /// Collect recent activities
  static Future<List<DashboardActivity>> _collectRecentActivities() async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM dashboard_activities ORDER BY timestamp DESC LIMIT 10',
      );

      return results
          .map(
            (row) => DashboardActivity(
              id: row['id'],
              appId: row['app_id'],
              type: row['activity_type'],
              title: row['title'],
              description: row['description'],
              timestamp: DateTime.fromMillisecondsSinceEpoch(row['timestamp']),
              metadata: jsonDecode(row['metadata'] ?? '{}'),
            ),
          )
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// Collect performance metrics
  static Future<DashboardPerformance> _collectPerformanceMetrics() async {
    try {
      // Simulate performance metrics collection
      // In production, collect actual system metrics
      return DashboardPerformance(
        cpuUsage: 45.2,
        memoryUsage: 67.8,
        diskUsage: 23.4,
        networkActivity: 12.5,
        responseTime: 89.0,
        activeConnections: 15,
      );
    } catch (e) {
      return DashboardPerformance(
        cpuUsage: 0.0,
        memoryUsage: 0.0,
        diskUsage: 0.0,
        networkActivity: 0.0,
        responseTime: 0.0,
        activeConnections: 0,
      );
    }
  }

  /// Add activity
  static Future<void> addActivity({
    required String appId,
    required String type,
    required String title,
    String? description,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final activityId = 'activity_${DateTime.now().millisecondsSinceEpoch}';

      await DatabaseService.safeInsert('dashboard_activities', {
        'id': activityId,
        'app_id': appId,
        'activity_type': type,
        'title': title,
        'description': description ?? '',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'metadata': jsonEncode(metadata ?? {}),
      });
    } catch (e) {
      // Continue without logging activity
    }
  }

  /// Save widget configuration
  static Future<void> saveWidgetConfiguration(DashboardWidget widget) async {
    try {
      // Try to update first, then insert if no rows affected
      final updateResult = await DatabaseService.safeUpdate(
        'dashboard_config',
        {
          'widget_type': widget.type.toString().split('.').last,
          'position_x': widget.position.x,
          'position_y': widget.position.y,
          'width': widget.size.width,
          'height': widget.size.height,
          'settings': jsonEncode(widget.settings),
          'is_active': widget.isActive ? 1 : 0,
          'updated_at': DateTime.now().millisecondsSinceEpoch,
        },
        where: 'id = ?',
        whereArgs: [widget.id],
      );

      if (updateResult == null || updateResult == 0) {
        // No rows updated, insert new widget
        await DatabaseService.safeInsert('dashboard_config', {
          'id': widget.id,
          'widget_type': widget.type.toString().split('.').last,
          'position_x': widget.position.x,
          'position_y': widget.position.y,
          'width': widget.size.width,
          'height': widget.size.height,
          'settings': jsonEncode(widget.settings),
          'is_active': widget.isActive ? 1 : 0,
          'created_at': DateTime.now().millisecondsSinceEpoch,
          'updated_at': DateTime.now().millisecondsSinceEpoch,
        });
      }

      // Reload configuration
      await _loadDashboardConfiguration();
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.operation,
        context: 'Save widget configuration failed',
      );
    }
  }

  /// Remove widget
  static Future<void> removeWidget(String widgetId) async {
    try {
      await DatabaseService.safeUpdate(
        'dashboard_config',
        {'is_active': 0},
        where: 'id = ?',
        whereArgs: [widgetId],
      );

      // Reload configuration
      await _loadDashboardConfiguration();
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.operation,
        context: 'Remove widget failed',
      );
    }
  }

  /// Get dashboard configuration
  static Future<DashboardConfiguration> getDashboardConfiguration() async {
    if (!_isInitialized) {
      await initialize();
    }
    return _currentConfig;
  }

  /// Get updates stream
  static Stream<DashboardUpdate> get updatesStream => _updateController.stream;

  /// Notify update
  static void _notifyUpdate(DashboardUpdate update) {
    if (!_updateController.isClosed) {
      _updateController.add(update);
    }
  }

  /// Dispose resources
  static void dispose() {
    _refreshTimer?.cancel();
    _updateController.close();
  }
}

/// Dashboard update model
class DashboardUpdate {
  final Map<String, Map<String, dynamic>> statistics;
  final List<DashboardActivity> activities;
  final DashboardPerformance performance;
  final DateTime timestamp;

  const DashboardUpdate({
    required this.statistics,
    required this.activities,
    required this.performance,
    required this.timestamp,
  });
}

/// Dashboard activity model
class DashboardActivity {
  final String id;
  final String appId;
  final String type;
  final String title;
  final String? description;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  const DashboardActivity({
    required this.id,
    required this.appId,
    required this.type,
    required this.title,
    this.description,
    required this.timestamp,
    required this.metadata,
  });
}

/// Dashboard performance model
class DashboardPerformance {
  final double cpuUsage;
  final double memoryUsage;
  final double diskUsage;
  final double networkActivity;
  final double responseTime;
  final int activeConnections;

  const DashboardPerformance({
    required this.cpuUsage,
    required this.memoryUsage,
    required this.diskUsage,
    required this.networkActivity,
    required this.responseTime,
    required this.activeConnections,
  });
}

/// Dashboard configuration model
class DashboardConfiguration {
  final List<DashboardWidget> activeWidgets;
  final List<DashboardWidget> availableWidgets;
  final DashboardSize gridSize;
  final Duration refreshInterval;

  const DashboardConfiguration({
    required this.activeWidgets,
    required this.availableWidgets,
    required this.gridSize,
    required this.refreshInterval,
  });

  static DashboardConfiguration defaultConfig() {
    return DashboardConfiguration(
      activeWidgets: [],
      availableWidgets: [],
      gridSize: const DashboardSize(width: 12, height: 8),
      refreshInterval: const Duration(seconds: 30),
    );
  }
}
