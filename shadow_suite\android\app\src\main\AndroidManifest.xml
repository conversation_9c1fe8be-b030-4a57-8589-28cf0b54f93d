<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Storage Permissions for File Manager, SmartGallery+, Shadow Player -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
                     android:minSdkVersion="30" />

    <!-- Media Permissions for SmartGallery+ and Shadow Player -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"
                     android:minSdkVersion="33" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO"
                     android:minSdkVersion="33" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO"
                     android:minSdkVersion="33" />

    <!-- Camera Permission for SmartGallery+ -->
    <uses-permission android:name="android.permission.CAMERA" />

    <!-- Microphone Permission for Memo Suite voice recording -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <!-- Location Permission for Islamic App (Qibla, Prayer Times) -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- Network Permissions for File Manager network services -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />

    <!-- Notification Permission for reminders and background tasks -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"
                     android:minSdkVersion="33" />

    <!-- Vibration Permission for haptic feedback -->
    <uses-permission android:name="android.permission.VIBRATE" />

    <!-- Wake Lock Permission for background media playback -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- Foreground Service Permission for background operations -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK"
                     android:minSdkVersion="34" />

    <!-- Biometric Permission for security features -->
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />

    <application
        android:label="Shadow Suite"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher"
        android:requestLegacyExternalStorage="true"
        android:preserveLegacyExternalStorage="true"
        android:allowBackup="true"
        android:usesCleartextTraffic="true">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>
    </queries>
</manifest>
