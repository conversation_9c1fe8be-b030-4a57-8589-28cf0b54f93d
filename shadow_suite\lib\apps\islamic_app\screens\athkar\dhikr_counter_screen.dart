import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_theme.dart';
import '../../services/islamic_providers.dart';
import '../../services/islamic_database_service.dart';
import '../../models/athkar.dart';

class DhikrCounterScreen extends ConsumerStatefulWidget {
  const DhikrCounterScreen({super.key});

  @override
  ConsumerState<DhikrCounterScreen> createState() => _DhikrCounterScreenState();
}

class _DhikrCounterScreenState extends ConsumerState<DhikrCounterScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rippleController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rippleAnimation;
  
  List<Dhikr> _currentDhikrList = [];
  int _currentDhikrIndex = 0;
  DhikrSession? _currentSession;
  Timer? _sessionTimer;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _rippleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    _rippleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _rippleController, curve: Curves.easeOut),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDhikrList();
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rippleController.dispose();
    _sessionTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadDhikrList() async {
    final selectedCategory = ref.read(selectedAthkarCategoryProvider);
    final selectedCustomAthkar = ref.read(selectedCustomAthkarProvider);
    
    if (selectedCategory != null) {
      final dhikrNotifier = ref.read(dhikrProvider.notifier);
      _currentDhikrList = await dhikrNotifier.getDhikrByCategory(selectedCategory);
    } else if (selectedCustomAthkar != null) {
      // Load dhikr from custom athkar routine
      final allDhikr = ref.read(dhikrProvider).value ?? [];
      _currentDhikrList = [];

      for (final item in selectedCustomAthkar.dhikrItems) {
        // Try to find existing dhikr first
        final existingDhikr = allDhikr.where((d) => d.id == item.dhikrId).firstOrNull;
        if (existingDhikr != null) {
          _currentDhikrList.add(existingDhikr);
        } else {
          // If not found, it might be a custom dhikr - load from database
          try {
            final customDhikr = await IslamicDatabaseService.getDhikrById(item.dhikrId);
            if (customDhikr != null) {
              _currentDhikrList.add(customDhikr);
            }
          } catch (e) {
            // If still not found, create a placeholder dhikr
            final placeholderDhikr = Dhikr(
              id: item.dhikrId,
              textArabic: 'ذكر مخصص',
              textEnglish: 'Custom dhikr from routine',
              textTransliteration: 'Custom dhikr',
              meaning: 'Custom dhikr created by user',
              category: AthkarCategory.custom,
              recommendedCount: item.count,
              source: 'Custom',
              benefits: ['Custom dhikr benefits'],
            );
            _currentDhikrList.add(placeholderDhikr);
          }
        }
      }
    }

    if (_currentDhikrList.isNotEmpty) {
      _startNewSession();
    }
  }

  void _startNewSession() {
    final currentDhikr = _currentDhikrList[_currentDhikrIndex];
    final selectedCustomAthkar = ref.read(selectedCustomAthkarProvider);
    
    int targetCount = currentDhikr.recommendedCount;
    if (selectedCustomAthkar != null) {
      final customItem = selectedCustomAthkar.dhikrItems
          .firstWhere((item) => item.dhikrId == currentDhikr.id);
      targetCount = customItem.count;
    }

    _currentSession = DhikrSession(
      dhikrId: currentDhikr.id,
      targetCount: targetCount,
      currentCount: 0,
      isCompleted: false,
      category: currentDhikr.category,
    );

    ref.read(dhikrCounterProvider.notifier).state = 0;
    ref.read(dhikrTargetCountProvider.notifier).state = targetCount;
  }

  @override
  Widget build(BuildContext context) {
    final currentCount = ref.watch(dhikrCounterProvider);
    final targetCount = ref.watch(dhikrTargetCountProvider);
    final hapticEnabled = ref.watch(dhikrCounterHapticProvider);
    final soundEnabled = ref.watch(dhikrCounterSoundProvider);

    if (_currentDhikrList.isEmpty) {
      return Scaffold(
        backgroundColor: AppTheme.backgroundColor,
        appBar: AppBar(
          title: const Text('Dhikr Counter'),
          backgroundColor: AppTheme.islamicAppColor,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.athkarCategories;
            },
          ),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final currentDhikr = _currentDhikrList[_currentDhikrIndex];
    final progress = targetCount > 0 ? currentCount / targetCount : 0.0;
    final isCompleted = currentCount >= targetCount;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text('${_currentDhikrIndex + 1}/${_currentDhikrList.length}'),
        backgroundColor: AppTheme.islamicAppColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            _showExitDialog();
          },
        ),
        actions: [
          IconButton(
            icon: Icon(hapticEnabled ? Icons.vibration : Icons.phone_android),
            onPressed: () {
              ref.read(dhikrCounterHapticProvider.notifier).state = !hapticEnabled;
            },
          ),
          IconButton(
            icon: Icon(soundEnabled ? Icons.volume_up : Icons.volume_off),
            onPressed: () {
              ref.read(dhikrCounterSoundProvider.notifier).state = !soundEnabled;
            },
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'reset', child: Text('Reset Counter')),
              const PopupMenuItem(value: 'skip', child: Text('Skip This Dhikr')),
              const PopupMenuItem(value: 'settings', child: Text('Settings')),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          _buildProgressIndicator(progress),
          Expanded(
            child: _buildDhikrContent(context, currentDhikr, currentCount, targetCount),
          ),
          _buildCounterSection(context, currentCount, targetCount, isCompleted),
          _buildNavigationButtons(context, isCompleted),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator(double progress) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.islamicAppColor),
            minHeight: 6,
          ),
          const SizedBox(height: 8),
          Text(
            '${(progress * 100).toInt()}% Complete',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDhikrContent(BuildContext context, Dhikr dhikr, int currentCount, int targetCount) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  Text(
                    dhikr.textArabic,
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontFamily: 'Arabic',
                      height: 2.0,
                      color: AppTheme.islamicAppColor,
                    ),
                    textAlign: TextAlign.center,
                    textDirection: TextDirection.rtl,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    dhikr.textTransliteration,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontStyle: FontStyle.italic,
                      color: Colors.grey[700],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    dhikr.meaning,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      height: 1.6,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),
          Card(
            color: Colors.grey[50],
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.grey[600],
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Source: ${dhikr.source}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                  if (dhikr.benefits.isNotEmpty) ...[
                    const SizedBox(height: 12),
                    Text(
                      'Benefits:',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 4),
                    ...dhikr.benefits.map((benefit) => Padding(
                      padding: const EdgeInsets.only(left: 16, top: 2),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '• ',
                            style: TextStyle(color: AppTheme.islamicAppColor),
                          ),
                          Expanded(
                            child: Text(
                              benefit,
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Colors.grey[600],
                              ),
                            ),
                          ),
                        ],
                      ),
                    )),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCounterSection(BuildContext context, int currentCount, int targetCount, bool isCompleted) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Text(
            '$currentCount / $targetCount',
            style: Theme.of(context).textTheme.headlineLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: isCompleted ? Colors.green : AppTheme.islamicAppColor,
            ),
          ),
          const SizedBox(height: 24),
          GestureDetector(
            onTap: isCompleted ? null : _incrementCounter,
            child: AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _pulseAnimation.value,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // Ripple effect
                      AnimatedBuilder(
                        animation: _rippleAnimation,
                        builder: (context, child) {
                          return Container(
                            width: 200 + (_rippleAnimation.value * 50),
                            height: 200 + (_rippleAnimation.value * 50),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: AppTheme.islamicAppColor.withValues(
                                alpha: 0.3 * (1 - _rippleAnimation.value),
                              ),
                            ),
                          );
                        },
                      ),
                      // Main counter button
                      Container(
                        width: 200,
                        height: 200,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: isCompleted ? Colors.green : AppTheme.islamicAppColor,
                          boxShadow: [
                            BoxShadow(
                              color: (isCompleted ? Colors.green : AppTheme.islamicAppColor).withValues(alpha: 0.3),
                              blurRadius: 20,
                              offset: const Offset(0, 8),
                            ),
                          ],
                        ),
                        child: Icon(
                          isCompleted ? Icons.check : Icons.touch_app,
                          size: 60,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 16),
          Text(
            isCompleted ? 'Completed!' : 'Tap to count',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: isCompleted ? Colors.green : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons(BuildContext context, bool isCompleted) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          if (_currentDhikrIndex > 0)
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _previousDhikr,
                icon: const Icon(Icons.arrow_back),
                label: const Text('Previous'),
              ),
            ),
          if (_currentDhikrIndex > 0 && _currentDhikrIndex < _currentDhikrList.length - 1)
            const SizedBox(width: 16),
          if (_currentDhikrIndex < _currentDhikrList.length - 1)
            Expanded(
              child: ElevatedButton.icon(
                onPressed: isCompleted ? _nextDhikr : null,
                icon: const Icon(Icons.arrow_forward),
                label: const Text('Next'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.islamicAppColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          if (_currentDhikrIndex == _currentDhikrList.length - 1 && isCompleted)
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _completeSession,
                icon: const Icon(Icons.check),
                label: const Text('Complete'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _incrementCounter() {
    final currentCount = ref.read(dhikrCounterProvider);
    final targetCount = ref.read(dhikrTargetCountProvider);
    final hapticEnabled = ref.read(dhikrCounterHapticProvider);
    final soundEnabled = ref.read(dhikrCounterSoundProvider);

    if (currentCount < targetCount) {
      ref.read(dhikrCounterProvider.notifier).state = currentCount + 1;

      // Enhanced haptic feedback
      if (hapticEnabled) {
        if (currentCount + 1 == targetCount) {
          // Stronger haptic for completion
          HapticFeedback.mediumImpact();
        } else if ((currentCount + 1) % 10 == 0) {
          // Medium haptic for every 10th count
          HapticFeedback.lightImpact();
        } else {
          // Light haptic for regular counts
          HapticFeedback.selectionClick();
        }
      }

      // Audio feedback
      if (soundEnabled) {
        _playCountSound(currentCount + 1, targetCount);
      }

      // Visual feedback
      _pulseController.forward().then((_) {
        _pulseController.reverse();
      });

      _rippleController.forward().then((_) {
        _rippleController.reset();
      });

      // Update session
      if (_currentSession != null) {
        _currentSession = _currentSession!.copyWith(
          currentCount: currentCount + 1,
          isCompleted: currentCount + 1 >= targetCount,
          endTime: currentCount + 1 >= targetCount ? DateTime.now() : null,
        );

        // Save session progress
        _saveSessionProgress();
      }

      // Show completion celebration
      if (currentCount + 1 == targetCount) {
        _showCompletionCelebration();
      }
    }
  }

  void _playCountSound(int count, int target) {
    // Play different sounds for different milestones
    if (count == target) {
      // Completion sound
      SystemSound.play(SystemSoundType.alert);
    } else if (count % 10 == 0) {
      // Milestone sound
      SystemSound.play(SystemSoundType.click);
    } else {
      // Regular count sound
      SystemSound.play(SystemSoundType.click);
    }
  }

  void _showCompletionCelebration() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.celebration, color: Colors.green, size: 32),
            const SizedBox(width: 12),
            const Text('Dhikr Completed!'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Alhamdulillah! You have completed this dhikr.'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'May Allah accept your dhikr and grant you His blessings.',
                style: TextStyle(
                  fontStyle: FontStyle.italic,
                  color: Colors.green,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
        actions: [
          if (_currentDhikrIndex < _currentDhikrList.length - 1)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _nextDhikr();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.islamicAppColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Next Dhikr'),
            ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              if (_currentDhikrIndex == _currentDhikrList.length - 1) {
                _completeSession();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: Text(_currentDhikrIndex == _currentDhikrList.length - 1 ? 'Complete Session' : 'Continue'),
          ),
        ],
      ),
    );
  }

  void _saveSessionProgress() async {
    if (_currentSession != null) {
      try {
        // Save session progress to local storage
        // In a real implementation, this would save to SQLite database
        // For now, we'll just update the provider state
        ref.read(dhikrSessionsProvider.notifier).updateSession(_currentSession!);
      } catch (e) {
        // Handle error silently for now
      }
    }
  }

  void _nextDhikr() {
    if (_currentDhikrIndex < _currentDhikrList.length - 1) {
      setState(() {
        _currentDhikrIndex++;
      });
      _startNewSession();
    }
  }

  void _previousDhikr() {
    if (_currentDhikrIndex > 0) {
      setState(() {
        _currentDhikrIndex--;
      });
      _startNewSession();
    }
  }

  void _completeSession() {
    // Save session to database
    if (_currentSession != null) {
      ref.read(dhikrSessionsProvider.notifier).addSession(_currentSession!);
    }
    
    // Show completion dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Session Completed!'),
        content: const Text('May Allah accept your dhikr and grant you His blessings.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.dashboard;
            },
            child: const Text('Return to Dashboard'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.athkarCategories;
            },
            child: const Text('Choose Another'),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'reset':
        ref.read(dhikrCounterProvider.notifier).state = 0;
        if (_currentSession != null) {
          _currentSession = _currentSession!.copyWith(
            currentCount: 0,
            isCompleted: false,
            endTime: null,
          );
        }
        break;
      case 'skip':
        _nextDhikr();
        break;
      case 'settings':
        // Settings functionality can be implemented here
        break;
    }
  }

  void _showExitDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Exit Dhikr Session'),
        content: const Text('Are you sure you want to exit? Your progress will be lost.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Continue'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.athkarCategories;
            },
            child: const Text('Exit'),
          ),
        ],
      ),
    );
  }
}
