import 'dart:io';
import 'dart:async';

class BatchOperationsService {
  static final StreamController<BatchProgress> _progressController =
      StreamController<BatchProgress>.broadcast();

  static Stream<BatchProgress> get progressStream => _progressController.stream;

  static bool _isProcessing = false;
  static String? _currentBatchId;

  /// Batch rename files with pattern
  static Future<BatchResult> batchRename({
    required List<String> filePaths,
    required String pattern,
    bool useRegex = false,
    String replacement = '',
  }) async {
    final batchId = DateTime.now().millisecondsSinceEpoch.toString();
    _currentBatchId = batchId;
    _isProcessing = true;

    final results = <String, String>{}; // old path -> new path
    final errors = <String, String>{}; // path -> error message
    int processed = 0;

    try {
      for (final filePath in filePaths) {
        if (!_isProcessing || _currentBatchId != batchId) break;

        try {
          final fileName = filePath.split('/').last;
          final directory = filePath.substring(0, filePath.lastIndexOf('/'));

          String newName;
          if (useRegex) {
            final regex = RegExp(pattern);
            newName = fileName.replaceAll(regex, replacement);
          } else {
            newName = fileName.replaceAll(pattern, replacement);
          }

          if (newName != fileName) {
            final newPath = '$directory/$newName';
            final file = File(filePath);

            if (await File(newPath).exists()) {
              errors[filePath] = 'Target file already exists: $newName';
            } else {
              await file.rename(newPath);
              results[filePath] = newPath;
            }
          }

          processed++;

          // Update progress
          _progressController.add(
            BatchProgress(
              batchId: batchId,
              operation: 'Renaming files',
              processed: processed,
              total: filePaths.length,
              currentItem: fileName,
              isCompleted: false,
            ),
          );
        } catch (error) {
          errors[filePath] = error.toString();
        }
      }

      // Final progress
      _progressController.add(
        BatchProgress(
          batchId: batchId,
          operation: 'Renaming files',
          processed: processed,
          total: filePaths.length,
          currentItem: '',
          isCompleted: true,
        ),
      );

      return BatchResult(
        operation: 'Batch Rename',
        totalItems: filePaths.length,
        successCount: results.length,
        errorCount: errors.length,
        results: results,
        errors: errors,
      );
    } finally {
      _isProcessing = false;
      _currentBatchId = null;
    }
  }

  /// Batch copy files to destination
  static Future<BatchResult> batchCopy({
    required List<String> sourcePaths,
    required String destinationPath,
    bool overwriteExisting = false,
  }) async {
    final batchId = DateTime.now().millisecondsSinceEpoch.toString();
    _currentBatchId = batchId;
    _isProcessing = true;

    final results = <String, String>{}; // source -> destination
    final errors = <String, String>{};
    int processed = 0;

    try {
      final destDir = Directory(destinationPath);
      if (!await destDir.exists()) {
        await destDir.create(recursive: true);
      }

      for (final sourcePath in sourcePaths) {
        if (!_isProcessing || _currentBatchId != batchId) break;

        try {
          final fileName = sourcePath.split('/').last;
          final destPath = '$destinationPath/$fileName';

          if (!overwriteExisting && await File(destPath).exists()) {
            errors[sourcePath] = 'Destination file already exists';
          } else {
            final sourceFile = File(sourcePath);
            await sourceFile.copy(destPath);
            results[sourcePath] = destPath;
          }

          processed++;

          _progressController.add(
            BatchProgress(
              batchId: batchId,
              operation: 'Copying files',
              processed: processed,
              total: sourcePaths.length,
              currentItem: fileName,
              isCompleted: false,
            ),
          );
        } catch (error) {
          errors[sourcePath] = error.toString();
        }
      }

      _progressController.add(
        BatchProgress(
          batchId: batchId,
          operation: 'Copying files',
          processed: processed,
          total: sourcePaths.length,
          currentItem: '',
          isCompleted: true,
        ),
      );

      return BatchResult(
        operation: 'Batch Copy',
        totalItems: sourcePaths.length,
        successCount: results.length,
        errorCount: errors.length,
        results: results,
        errors: errors,
      );
    } finally {
      _isProcessing = false;
      _currentBatchId = null;
    }
  }

  /// Batch move files to destination
  static Future<BatchResult> batchMove({
    required List<String> sourcePaths,
    required String destinationPath,
    bool overwriteExisting = false,
  }) async {
    final batchId = DateTime.now().millisecondsSinceEpoch.toString();
    _currentBatchId = batchId;
    _isProcessing = true;

    final results = <String, String>{};
    final errors = <String, String>{};
    int processed = 0;

    try {
      final destDir = Directory(destinationPath);
      if (!await destDir.exists()) {
        await destDir.create(recursive: true);
      }

      for (final sourcePath in sourcePaths) {
        if (!_isProcessing || _currentBatchId != batchId) break;

        try {
          final fileName = sourcePath.split('/').last;
          final destPath = '$destinationPath/$fileName';

          if (!overwriteExisting && await File(destPath).exists()) {
            errors[sourcePath] = 'Destination file already exists';
          } else {
            final sourceFile = File(sourcePath);
            await sourceFile.rename(destPath);
            results[sourcePath] = destPath;
          }

          processed++;

          _progressController.add(
            BatchProgress(
              batchId: batchId,
              operation: 'Moving files',
              processed: processed,
              total: sourcePaths.length,
              currentItem: fileName,
              isCompleted: false,
            ),
          );
        } catch (error) {
          errors[sourcePath] = error.toString();
        }
      }

      _progressController.add(
        BatchProgress(
          batchId: batchId,
          operation: 'Moving files',
          processed: processed,
          total: sourcePaths.length,
          currentItem: '',
          isCompleted: true,
        ),
      );

      return BatchResult(
        operation: 'Batch Move',
        totalItems: sourcePaths.length,
        successCount: results.length,
        errorCount: errors.length,
        results: results,
        errors: errors,
      );
    } finally {
      _isProcessing = false;
      _currentBatchId = null;
    }
  }

  /// Batch delete files
  static Future<BatchResult> batchDelete({
    required List<String> filePaths,
    bool moveToTrash = true,
  }) async {
    final batchId = DateTime.now().millisecondsSinceEpoch.toString();
    _currentBatchId = batchId;
    _isProcessing = true;

    final results = <String, String>{};
    final errors = <String, String>{};
    int processed = 0;

    try {
      for (final filePath in filePaths) {
        if (!_isProcessing || _currentBatchId != batchId) break;

        try {
          final file = File(filePath);

          if (moveToTrash) {
            // Move to trash directory (simplified implementation)
            final trashDir = Directory('${Directory.systemTemp.path}/trash');
            if (!await trashDir.exists()) {
              await trashDir.create(recursive: true);
            }

            final fileName = filePath.split('/').last;
            final trashPath = '${trashDir.path}/$fileName';
            await file.rename(trashPath);
            results[filePath] = trashPath;
          } else {
            await file.delete();
            results[filePath] = 'Permanently deleted';
          }

          processed++;

          _progressController.add(
            BatchProgress(
              batchId: batchId,
              operation: 'Deleting files',
              processed: processed,
              total: filePaths.length,
              currentItem: filePath.split('/').last,
              isCompleted: false,
            ),
          );
        } catch (error) {
          errors[filePath] = error.toString();
        }
      }

      _progressController.add(
        BatchProgress(
          batchId: batchId,
          operation: 'Deleting files',
          processed: processed,
          total: filePaths.length,
          currentItem: '',
          isCompleted: true,
        ),
      );

      return BatchResult(
        operation: 'Batch Delete',
        totalItems: filePaths.length,
        successCount: results.length,
        errorCount: errors.length,
        results: results,
        errors: errors,
      );
    } finally {
      _isProcessing = false;
      _currentBatchId = null;
    }
  }

  /// Execute custom script on files
  static Future<BatchResult> executeScript({
    required List<String> filePaths,
    required String script,
    Map<String, String> variables = const {},
  }) async {
    final batchId = DateTime.now().millisecondsSinceEpoch.toString();
    _currentBatchId = batchId;
    _isProcessing = true;

    final results = <String, String>{};
    final errors = <String, String>{};
    int processed = 0;

    try {
      for (final filePath in filePaths) {
        if (!_isProcessing || _currentBatchId != batchId) break;

        try {
          // Simple script execution (placeholder - in production would use proper scripting engine)
          final result = await _executeSimpleScript(
            filePath,
            script,
            variables,
          );
          results[filePath] = result;

          processed++;

          _progressController.add(
            BatchProgress(
              batchId: batchId,
              operation: 'Executing script',
              processed: processed,
              total: filePaths.length,
              currentItem: filePath.split('/').last,
              isCompleted: false,
            ),
          );
        } catch (error) {
          errors[filePath] = error.toString();
        }
      }

      _progressController.add(
        BatchProgress(
          batchId: batchId,
          operation: 'Executing script',
          processed: processed,
          total: filePaths.length,
          currentItem: '',
          isCompleted: true,
        ),
      );

      return BatchResult(
        operation: 'Script Execution',
        totalItems: filePaths.length,
        successCount: results.length,
        errorCount: errors.length,
        results: results,
        errors: errors,
      );
    } finally {
      _isProcessing = false;
      _currentBatchId = null;
    }
  }

  /// Cancel current batch operation
  static void cancelBatch() {
    _isProcessing = false;
    _currentBatchId = null;
  }

  /// Check if batch operation is running
  static bool get isProcessing => _isProcessing;

  static Future<String> _executeSimpleScript(
    String filePath,
    String script,
    Map<String, String> variables,
  ) async {
    // Simplified script execution - replace variables and basic operations
    var processedScript = script;

    // Replace built-in variables
    processedScript = processedScript.replaceAll('{filepath}', filePath);
    processedScript = processedScript.replaceAll(
      '{filename}',
      filePath.split('/').last,
    );
    processedScript = processedScript.replaceAll(
      '{directory}',
      filePath.substring(0, filePath.lastIndexOf('/')),
    );

    // Replace custom variables
    variables.forEach((key, value) {
      processedScript = processedScript.replaceAll('{$key}', value);
    });

    // For now, just return the processed script as result
    // In production, this would execute actual commands
    return 'Script executed: $processedScript';
  }

  static void dispose() {
    _progressController.close();
  }
}

/// Batch operation progress
class BatchProgress {
  final String batchId;
  final String operation;
  final int processed;
  final int total;
  final String currentItem;
  final bool isCompleted;

  const BatchProgress({
    required this.batchId,
    required this.operation,
    required this.processed,
    required this.total,
    required this.currentItem,
    required this.isCompleted,
  });

  double get progress => total > 0 ? processed / total : 0.0;
}

/// Batch operation result
class BatchResult {
  final String operation;
  final int totalItems;
  final int successCount;
  final int errorCount;
  final Map<String, String> results;
  final Map<String, String> errors;

  const BatchResult({
    required this.operation,
    required this.totalItems,
    required this.successCount,
    required this.errorCount,
    required this.results,
    required this.errors,
  });

  bool get hasErrors => errorCount > 0;
  double get successRate => totalItems > 0 ? successCount / totalItems : 0.0;
}
