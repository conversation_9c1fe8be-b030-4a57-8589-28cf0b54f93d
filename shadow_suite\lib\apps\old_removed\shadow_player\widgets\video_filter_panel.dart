import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/shadow_player_providers.dart';

class VideoFilterPanel extends ConsumerStatefulWidget {
  final ScrollController scrollController;

  const VideoFilterPanel({super.key, required this.scrollController});

  @override
  ConsumerState<VideoFilterPanel> createState() => _VideoFilterPanelState();
}

class _VideoFilterPanelState extends ConsumerState<VideoFilterPanel> {
  @override
  Widget build(BuildContext context) {
    final filter = ref.watch(videoFilterProvider);
    final supportedFormats = ref.watch(supportedVideoFormatsProvider);

    return Container(
      decoration: const BoxDecoration(
        color: Color(0xFF2C3E50),
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        children: [
          // Handle Bar
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: const Color(0xFF7F8C8D),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(24),
            child: Row(
              children: [
                const Text(
                  'Filter Videos',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _clearFilters,
                  child: const Text(
                    'Clear All',
                    style: TextStyle(color: Color(0xFFE74C3C), fontSize: 14),
                  ),
                ),
              ],
            ),
          ),

          // Filter Content
          Expanded(
            child: ListView(
              controller: widget.scrollController,
              padding: const EdgeInsets.symmetric(horizontal: 24),
              children: [
                // File Formats
                _buildFilterSection(
                  title: 'File Formats',
                  child: Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: supportedFormats.map((format) {
                      final isSelected = filter.formats.contains(format);
                      return FilterChip(
                        label: Text(format.toUpperCase()),
                        selected: isSelected,
                        onSelected: (selected) {
                          _toggleFormat(format, selected);
                        },
                        backgroundColor: const Color(0xFF34495E),
                        selectedColor: const Color(0xFF3498DB),
                        labelStyle: TextStyle(
                          color: isSelected
                              ? Colors.white
                              : const Color(0xFFBDC3C7),
                          fontSize: 12,
                        ),
                      );
                    }).toList(),
                  ),
                ),

                // Duration Range
                _buildFilterSection(
                  title: 'Duration',
                  child: Column(
                    children: [
                      _buildDurationSlider(
                        'Minimum Duration',
                        filter.minDuration,
                        (duration) => _updateFilter(
                          filter.copyWith(minDuration: duration),
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildDurationSlider(
                        'Maximum Duration',
                        filter.maxDuration,
                        (duration) => _updateFilter(
                          filter.copyWith(maxDuration: duration),
                        ),
                      ),
                    ],
                  ),
                ),

                // Resolution
                _buildFilterSection(
                  title: 'Resolution',
                  child: Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: ['720p', '1080p', '1440p', '4K'].map((
                      resolution,
                    ) {
                      final isSelected = filter.resolutions.contains(
                        resolution,
                      );
                      return FilterChip(
                        label: Text(resolution),
                        selected: isSelected,
                        onSelected: (selected) {
                          _toggleResolution(resolution, selected);
                        },
                        backgroundColor: const Color(0xFF34495E),
                        selectedColor: const Color(0xFF3498DB),
                        labelStyle: TextStyle(
                          color: isSelected
                              ? Colors.white
                              : const Color(0xFFBDC3C7),
                          fontSize: 12,
                        ),
                      );
                    }).toList(),
                  ),
                ),

                // File Size Range
                _buildFilterSection(
                  title: 'File Size',
                  child: Column(
                    children: [
                      _buildSizeSlider(
                        'Minimum Size (MB)',
                        filter.minSize,
                        (size) => _updateFilter(
                          filter.copyWith(minSize: size?.toInt()),
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildSizeSlider(
                        'Maximum Size (GB)',
                        filter.maxSize,
                        (size) => _updateFilter(
                          filter.copyWith(maxSize: size?.toInt()),
                        ),
                      ),
                    ],
                  ),
                ),

                // Date Range
                _buildFilterSection(
                  title: 'Date Modified',
                  child: Column(
                    children: [
                      _buildDatePicker(
                        'From Date',
                        filter.dateFrom,
                        (date) =>
                            _updateFilter(filter.copyWith(dateFrom: date)),
                      ),
                      const SizedBox(height: 16),
                      _buildDatePicker(
                        'To Date',
                        filter.dateTo,
                        (date) => _updateFilter(filter.copyWith(dateTo: date)),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),
              ],
            ),
          ),

          // Apply Button
          Container(
            padding: const EdgeInsets.all(24),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF3498DB),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Apply Filters',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection({required String title, required Widget child}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          child,
        ],
      ),
    );
  }

  Widget _buildDurationSlider(
    String label,
    Duration? value,
    ValueChanged<Duration?> onChanged,
  ) {
    final minutes = value?.inMinutes ?? 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: const TextStyle(color: Color(0xFFBDC3C7), fontSize: 14),
            ),
            const Spacer(),
            Text(
              value != null ? _formatDuration(value) : 'Any',
              style: const TextStyle(
                color: Color(0xFF3498DB),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Slider(
          value: minutes.toDouble(),
          min: 0,
          max: 180, // 3 hours
          divisions: 36,
          activeColor: const Color(0xFF3498DB),
          inactiveColor: const Color(0xFF34495E),
          onChanged: (newValue) {
            if (newValue == 0) {
              onChanged(null);
            } else {
              onChanged(Duration(minutes: newValue.round()));
            }
          },
        ),
      ],
    );
  }

  Widget _buildSizeSlider(
    String label,
    int? value,
    ValueChanged<double?> onChanged,
  ) {
    final sizeMB = value != null ? value / (1024 * 1024) : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: const TextStyle(color: Color(0xFFBDC3C7), fontSize: 14),
            ),
            const Spacer(),
            Text(
              value != null ? _formatFileSize(value) : 'Any',
              style: const TextStyle(
                color: Color(0xFF3498DB),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Slider(
          value: sizeMB,
          min: 0,
          max: label.contains('GB') ? 10240 : 1024, // 10GB or 1GB in MB
          divisions: label.contains('GB') ? 20 : 10,
          activeColor: const Color(0xFF3498DB),
          inactiveColor: const Color(0xFF34495E),
          onChanged: (newValue) {
            if (newValue == 0) {
              onChanged(null);
            } else {
              onChanged(newValue * 1024 * 1024);
            }
          },
        ),
      ],
    );
  }

  Widget _buildDatePicker(
    String label,
    DateTime? value,
    ValueChanged<DateTime?> onChanged,
  ) {
    return InkWell(
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: value ?? DateTime.now(),
          firstDate: DateTime(2000),
          lastDate: DateTime.now(),
          builder: (context, child) {
            return Theme(
              data: Theme.of(context).copyWith(
                colorScheme: const ColorScheme.dark(
                  primary: Color(0xFF3498DB),
                  surface: Color(0xFF2C3E50),
                ),
              ),
              child: child!,
            );
          },
        );
        onChanged(date);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFF34495E)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            const Icon(
              Icons.calendar_today,
              color: Color(0xFF3498DB),
              size: 20,
            ),
            const SizedBox(width: 12),
            Text(
              label,
              style: const TextStyle(color: Color(0xFFBDC3C7), fontSize: 14),
            ),
            const Spacer(),
            Text(
              value != null
                  ? '${value.day}/${value.month}/${value.year}'
                  : 'Select Date',
              style: const TextStyle(color: Colors.white, fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleFormat(String format, bool selected) {
    final filter = ref.read(videoFilterProvider);
    final formats = List<String>.from(filter.formats);

    if (selected) {
      formats.add(format);
    } else {
      formats.remove(format);
    }

    _updateFilter(filter.copyWith(formats: formats));
  }

  void _toggleResolution(String resolution, bool selected) {
    final filter = ref.read(videoFilterProvider);
    final resolutions = List<String>.from(filter.resolutions);

    if (selected) {
      resolutions.add(resolution);
    } else {
      resolutions.remove(resolution);
    }

    _updateFilter(filter.copyWith(resolutions: resolutions));
  }

  void _updateFilter(VideoFilter newFilter) {
    ref.read(videoFilterProvider.notifier).state = newFilter;
  }

  void _clearFilters() {
    ref.read(videoFilterProvider.notifier).state = const VideoFilter();
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }
}

// Extension for VideoFilter copyWith
extension VideoFilterExtension on VideoFilter {
  VideoFilter copyWith({
    List<String>? formats,
    Duration? minDuration,
    Duration? maxDuration,
    List<String>? resolutions,
    DateTime? dateFrom,
    DateTime? dateTo,
    int? minSize,
    int? maxSize,
  }) {
    return VideoFilter(
      formats: formats ?? this.formats,
      minDuration: minDuration ?? this.minDuration,
      maxDuration: maxDuration ?? this.maxDuration,
      resolutions: resolutions ?? this.resolutions,
      dateFrom: dateFrom ?? this.dateFrom,
      dateTo: dateTo ?? this.dateTo,
      minSize: minSize ?? this.minSize,
      maxSize: maxSize ?? this.maxSize,
    );
  }
}
