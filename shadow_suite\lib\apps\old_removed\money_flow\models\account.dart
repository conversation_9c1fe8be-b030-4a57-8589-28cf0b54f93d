import 'package:uuid/uuid.dart';

enum AccountType {
  checking('Checking'),
  savings('Savings'),
  credit('Credit Card'),
  investment('Investment'),
  cash('Cash'),
  loan('Loan');

  const AccountType(this.displayName);
  final String displayName;

  static List<String> get allTypes => AccountType.values.map((e) => e.displayName).toList();
}

class Account {
  final String id;
  final String name;
  final AccountType type;
  final String bankName;
  final String accountNumber;
  final double balance;
  final String currency;
  final String color;
  final bool isActive;
  final String? description;
  final String? routingNumber;
  final String? swiftCode;
  final double? creditLimit;
  final double? interestRate;
  final DateTime? openDate;
  final String? notes;
  final bool includeInNetWorth;
  final bool allowOverdraft;
  final double? overdraftLimit;
  final DateTime createdAt;
  final DateTime updatedAt;

  Account({
    String? id,
    required this.name,
    required this.type,
    required this.bankName,
    required this.accountNumber,
    required this.balance,
    this.currency = 'USD',
    this.color = '#2196F3',
    this.isActive = true,
    this.description,
    this.routingNumber,
    this.swiftCode,
    this.creditLimit,
    this.interestRate,
    this.openDate,
    this.notes,
    this.includeInNetWorth = true,
    this.allowOverdraft = false,
    this.overdraftLimit,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'bankName': bankName,
      'accountNumber': accountNumber,
      'balance': balance,
      'currency': currency,
      'color': color,
      'isActive': isActive ? 1 : 0,
      'description': description,
      'routingNumber': routingNumber,
      'swiftCode': swiftCode,
      'creditLimit': creditLimit,
      'interestRate': interestRate,
      'openDate': openDate?.millisecondsSinceEpoch,
      'notes': notes,
      'includeInNetWorth': includeInNetWorth ? 1 : 0,
      'allowOverdraft': allowOverdraft ? 1 : 0,
      'overdraftLimit': overdraftLimit,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory Account.fromMap(Map<String, dynamic> map) {
    return Account(
      id: map['id'],
      name: map['name'],
      type: AccountType.values.firstWhere((e) => e.name == map['type']),
      bankName: map['bankName'],
      accountNumber: map['accountNumber'],
      balance: map['balance'].toDouble(),
      currency: map['currency'],
      color: map['color'],
      isActive: map['isActive'] == 1,
      description: map['description'],
      routingNumber: map['routingNumber'],
      swiftCode: map['swiftCode'],
      creditLimit: map['creditLimit']?.toDouble(),
      interestRate: map['interestRate']?.toDouble(),
      openDate: map['openDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['openDate'])
          : null,
      notes: map['notes'],
      includeInNetWorth: map['includeInNetWorth'] == 1,
      allowOverdraft: map['allowOverdraft'] == 1,
      overdraftLimit: map['overdraftLimit']?.toDouble(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
    );
  }

  Account copyWith({
    String? name,
    AccountType? type,
    String? bankName,
    String? accountNumber,
    double? balance,
    String? currency,
    String? color,
    bool? isActive,
    String? description,
    String? routingNumber,
    String? swiftCode,
    double? creditLimit,
    double? interestRate,
    DateTime? openDate,
    String? notes,
    bool? includeInNetWorth,
    bool? allowOverdraft,
    double? overdraftLimit,
    DateTime? updatedAt,
  }) {
    return Account(
      id: id,
      name: name ?? this.name,
      type: type ?? this.type,
      bankName: bankName ?? this.bankName,
      accountNumber: accountNumber ?? this.accountNumber,
      balance: balance ?? this.balance,
      currency: currency ?? this.currency,
      color: color ?? this.color,
      isActive: isActive ?? this.isActive,
      description: description ?? this.description,
      routingNumber: routingNumber ?? this.routingNumber,
      swiftCode: swiftCode ?? this.swiftCode,
      creditLimit: creditLimit ?? this.creditLimit,
      interestRate: interestRate ?? this.interestRate,
      openDate: openDate ?? this.openDate,
      notes: notes ?? this.notes,
      includeInNetWorth: includeInNetWorth ?? this.includeInNetWorth,
      allowOverdraft: allowOverdraft ?? this.allowOverdraft,
      overdraftLimit: overdraftLimit ?? this.overdraftLimit,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  String get formattedBalance {
    return '${currency == 'USD' ? '\$' : currency} ${balance.toStringAsFixed(2)}';
  }

  String get maskedAccountNumber {
    if (accountNumber.length <= 4) return accountNumber;
    return '**** ${accountNumber.substring(accountNumber.length - 4)}';
  }
}
