import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/app_theme.dart';
import '../services/analytics_service.dart';

class AnalyticsScreen extends ConsumerStatefulWidget {
  const AnalyticsScreen({super.key});

  @override
  ConsumerState<AnalyticsScreen> createState() => _AnalyticsScreenState();
}

class _AnalyticsScreenState extends ConsumerState<AnalyticsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedTimeRange = '7d';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Tools Analytics'),
        backgroundColor: AppTheme.toolsBuilderColor,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Overview'),
            Tab(icon: Icon(Icons.trending_up), text: 'Usage'),
            Tab(icon: Icon(Icons.speed), text: 'Performance'),
            Tab(icon: Icon(Icons.assessment), text: 'Reports'),
          ],
        ),
        actions: [
          _buildTimeRangeSelector(),
          IconButton(
            onPressed: () => _exportReport(),
            icon: const Icon(Icons.download),
            tooltip: 'Export Report',
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildUsageTab(),
          _buildPerformanceTab(),
          _buildReportsTab(),
        ],
      ),
    );
  }

  Widget _buildTimeRangeSelector() {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: DropdownButton<String>(
        value: _selectedTimeRange,
        dropdownColor: AppTheme.toolsBuilderColor,
        style: const TextStyle(color: Colors.white),
        underline: Container(),
        items: const [
          DropdownMenuItem(value: '1d', child: Text('Last 24h')),
          DropdownMenuItem(value: '7d', child: Text('Last 7 days')),
          DropdownMenuItem(value: '30d', child: Text('Last 30 days')),
          DropdownMenuItem(value: '90d', child: Text('Last 3 months')),
          DropdownMenuItem(value: '1y', child: Text('Last year')),
        ],
        onChanged: (value) {
          if (value != null) {
            setState(() {
              _selectedTimeRange = value;
            });
          }
        },
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMetricsGrid(),
          const SizedBox(height: 24),
          _buildQuickStats(),
          const SizedBox(height: 24),
          _buildRecentActivity(),
        ],
      ),
    );
  }

  Widget _buildMetricsGrid() {
    return Consumer(
      builder: (context, ref, child) {
        final analyticsAsync = ref.watch(analyticsProvider(_selectedTimeRange));
        
        return analyticsAsync.when(
          data: (analytics) => GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 4,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.5,
            children: [
              _buildMetricCard(
                'Total Tools',
                analytics.totalTools.toString(),
                Icons.build_circle,
                Colors.blue,
                '+${analytics.toolsGrowth}%',
              ),
              _buildMetricCard(
                'Active Users',
                analytics.activeUsers.toString(),
                Icons.people,
                Colors.green,
                '+${analytics.usersGrowth}%',
              ),
              _buildMetricCard(
                'Tool Runs',
                analytics.totalRuns.toString(),
                Icons.play_arrow,
                Colors.orange,
                '+${analytics.runsGrowth}%',
              ),
              _buildMetricCard(
                'Avg. Performance',
                '${analytics.avgPerformance.toStringAsFixed(1)}ms',
                Icons.speed,
                Colors.purple,
                '${analytics.performanceChange > 0 ? '+' : ''}${analytics.performanceChange.toStringAsFixed(1)}%',
              ),
            ],
          ),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorWidget(error),
        );
      },
    );
  }

  Widget _buildMetricCard(String title, String value, IconData icon, Color color, String change) {
    final isPositive = change.startsWith('+');
    
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: (isPositive ? Colors.green : Colors.red).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    change,
                    style: TextStyle(
                      color: isPositive ? Colors.green : Colors.red,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Statistics',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Consumer(
              builder: (context, ref, child) {
                final analyticsAsync = ref.watch(analyticsProvider(_selectedTimeRange));
                
                return analyticsAsync.when(
                  data: (analytics) => Column(
                    children: [
                      _buildStatRow('Most Used Tool', analytics.mostUsedTool, '${analytics.mostUsedToolRuns} runs'),
                      _buildStatRow('Fastest Tool', analytics.fastestTool, '${analytics.fastestToolTime}ms avg'),
                      _buildStatRow('Most Complex Tool', analytics.mostComplexTool, '${analytics.mostComplexToolComponents} components'),
                      _buildStatRow('Peak Usage Hour', '${analytics.peakHour}:00', '${analytics.peakHourRuns} runs'),
                      _buildStatRow('Total Calculations', analytics.totalCalculations.toString(), 'formulas executed'),
                    ],
                  ),
                  loading: () => const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => _buildErrorWidget(error),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value, String subtitle) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(value),
          ),
          Expanded(
            child: Text(
              subtitle,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Activity',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Consumer(
              builder: (context, ref, child) {
                final activitiesAsync = ref.watch(recentActivitiesProvider);
                
                return activitiesAsync.when(
                  data: (activities) => ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: activities.length.clamp(0, 5),
                    separatorBuilder: (context, index) => const Divider(),
                    itemBuilder: (context, index) {
                      final activity = activities[index];
                      return ListTile(
                        leading: Icon(
                          _getActivityIcon(activity.type),
                          color: _getActivityColor(activity.type),
                        ),
                        title: Text(activity.description),
                        subtitle: Text(activity.timestamp.toString()),
                        trailing: activity.metadata != null 
                            ? Chip(
                                label: Text(activity.metadata!),
                                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              )
                            : null,
                      );
                    },
                  ),
                  loading: () => const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => _buildErrorWidget(error),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUsageTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildUsageChart(),
          const SizedBox(height: 24),
          _buildTopToolsList(),
        ],
      ),
    );
  }

  Widget _buildUsageChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Usage Trends',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              height: 300,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Text(
                  'Usage Chart\n(Chart implementation would go here)',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopToolsList() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Top Tools by Usage',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Consumer(
              builder: (context, ref, child) {
                final topToolsAsync = ref.watch(topToolsProvider(_selectedTimeRange));
                
                return topToolsAsync.when(
                  data: (tools) => ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: tools.length,
                    itemBuilder: (context, index) {
                      final tool = tools[index];
                      return ListTile(
                        leading: CircleAvatar(
                          backgroundColor: AppTheme.toolsBuilderColor,
                          child: Text('${index + 1}'),
                        ),
                        title: Text(tool.name),
                        subtitle: Text('${tool.usageCount} runs'),
                        trailing: Text('${tool.usagePercentage.toStringAsFixed(1)}%'),
                      );
                    },
                  ),
                  loading: () => const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => _buildErrorWidget(error),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildPerformanceMetrics(),
          const SizedBox(height: 24),
          _buildPerformanceChart(),
        ],
      ),
    );
  }

  Widget _buildPerformanceMetrics() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Performance Metrics',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Consumer(
              builder: (context, ref, child) {
                final performanceAsync = ref.watch(performanceMetricsProvider(_selectedTimeRange));
                
                return performanceAsync.when(
                  data: (metrics) => Column(
                    children: [
                      _buildPerformanceRow('Average Load Time', '${metrics.avgLoadTime}ms'),
                      _buildPerformanceRow('Average Calculation Time', '${metrics.avgCalculationTime}ms'),
                      _buildPerformanceRow('Memory Usage', '${metrics.avgMemoryUsage}MB'),
                      _buildPerformanceRow('Error Rate', '${metrics.errorRate.toStringAsFixed(2)}%'),
                      _buildPerformanceRow('Success Rate', '${metrics.successRate.toStringAsFixed(2)}%'),
                    ],
                  ),
                  loading: () => const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => _buildErrorWidget(error),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Performance Over Time',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              height: 300,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Text(
                  'Performance Chart\n(Chart implementation would go here)',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildReportsList(),
        ],
      ),
    );
  }

  Widget _buildReportsList() {
    final reports = [
      {'name': 'Usage Summary Report', 'description': 'Comprehensive usage statistics', 'icon': Icons.summarize},
      {'name': 'Performance Report', 'description': 'Detailed performance analysis', 'icon': Icons.speed},
      {'name': 'User Activity Report', 'description': 'User engagement metrics', 'icon': Icons.people},
      {'name': 'Tool Efficiency Report', 'description': 'Tool performance comparison', 'icon': Icons.compare_arrows},
      {'name': 'Error Analysis Report', 'description': 'Error patterns and trends', 'icon': Icons.error_outline},
    ];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Available Reports',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: reports.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final report = reports[index];
                return ListTile(
                  leading: Icon(report['icon'] as IconData),
                  title: Text(report['name'] as String),
                  subtitle: Text(report['description'] as String),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        onPressed: () => _generateReport(report['name'] as String),
                        icon: const Icon(Icons.play_arrow),
                        tooltip: 'Generate Report',
                      ),
                      IconButton(
                        onPressed: () => _scheduleReport(report['name'] as String),
                        icon: const Icon(Icons.schedule),
                        tooltip: 'Schedule Report',
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[300],
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading analytics',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  IconData _getActivityIcon(String type) {
    switch (type) {
      case 'tool_created':
        return Icons.add_circle;
      case 'tool_run':
        return Icons.play_arrow;
      case 'tool_edited':
        return Icons.edit;
      case 'tool_shared':
        return Icons.share;
      default:
        return Icons.info;
    }
  }

  Color _getActivityColor(String type) {
    switch (type) {
      case 'tool_created':
        return Colors.green;
      case 'tool_run':
        return Colors.blue;
      case 'tool_edited':
        return Colors.orange;
      case 'tool_shared':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Exporting analytics report...')),
    );
  }

  void _generateReport(String reportName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Generating $reportName...')),
    );
  }

  void _scheduleReport(String reportName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Schedule $reportName'),
        content: const Text('Report scheduling will be implemented'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Schedule'),
          ),
        ],
      ),
    );
  }
}
