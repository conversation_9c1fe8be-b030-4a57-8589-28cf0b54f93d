import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../file_manager_main.dart';
import '../../../core/widgets/scan_button_widget.dart';

class FileManagerDashboardView extends ConsumerWidget {
  const FileManagerDashboardView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          const Text(
            'File Manager Dashboard',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Overview of your file system and recent activity',
            style: TextStyle(color: Color(0xFF7F8C8D)),
          ),
          const SizedBox(height: 32),

          // Quick actions
          Row(
            children: [
              _buildQuickAction(
                context,
                ref,
                Icons.folder_open,
                'Browse Files',
                'Open dual-pane file explorer',
                () => _switchToDualPane(ref),
              ),
              const SizedBox(width: 16),
              _buildQuickAction(
                context,
                ref,
                Icons.view_agenda,
                'Single Pane',
                'Open single-pane file explorer',
                () => _switchToSinglePane(ref),
              ),
              const SizedBox(width: 16),
              _buildQuickAction(
                context,
                ref,
                Icons.search,
                'Search Files',
                'Search across all directories',
                () => _showGlobalSearch(context),
              ),
              const SizedBox(width: 16),
              _buildQuickAction(
                context,
                ref,
                Icons.scanner,
                'Scan Storage',
                'Discover and index all files',
                () => _showStorageScan(context),
              ),
            ],
          ),

          const SizedBox(height: 32),

          // Storage overview
          Expanded(
            child: Row(
              children: [
                // Storage stats
                Expanded(flex: 2, child: _buildStorageOverview()),
                const SizedBox(width: 24),

                // Recent files and network status
                Expanded(
                  flex: 3,
                  child: Column(
                    children: [
                      Expanded(child: _buildRecentFiles()),
                      const SizedBox(height: 16),
                      Expanded(child: _buildNetworkServices()),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAction(
    BuildContext context,
    WidgetRef ref,
    IconData icon,
    String title,
    String subtitle,
    VoidCallback onTap,
  ) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: const [
              BoxShadow(
                color: Colors.black12,
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(icon, size: 32, color: const Color(0xFFE67E22)),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: const TextStyle(fontSize: 12, color: Color(0xFF7F8C8D)),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStorageOverview() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(color: Colors.black12, blurRadius: 4, offset: Offset(0, 2)),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Storage Overview',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 20),

          // Storage chart placeholder
          Container(
            height: 120,
            decoration: BoxDecoration(
              color: const Color(0xFFF8F9FA),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: Text(
                'Storage Chart\n(To be implemented)',
                textAlign: TextAlign.center,
                style: TextStyle(color: Color(0xFF7F8C8D)),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Storage stats
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildStorageStat('Used', '7.5 GB', const Color(0xFFE67E22)),
              _buildStorageStat('Free', '2.5 GB', const Color(0xFF27AE60)),
              _buildStorageStat('Total', '10 GB', const Color(0xFF3498DB)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStorageStat(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Color(0xFF7F8C8D)),
        ),
      ],
    );
  }

  Widget _buildRecentFiles() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(color: Colors.black12, blurRadius: 4, offset: Offset(0, 2)),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Recent Files',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView(
              children: [
                _buildRecentFileItem(
                  'Document.pdf',
                  'Documents',
                  Icons.picture_as_pdf,
                ),
                _buildRecentFileItem('Photo.jpg', 'Pictures', Icons.image),
                _buildRecentFileItem('Music.mp3', 'Music', Icons.audio_file),
                _buildRecentFileItem('Video.mp4', 'Videos', Icons.video_file),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentFileItem(String name, String location, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 16, color: const Color(0xFFE67E22)),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  location,
                  style: const TextStyle(
                    fontSize: 10,
                    color: Color(0xFF7F8C8D),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNetworkServices() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(color: Colors.black12, blurRadius: 4, offset: Offset(0, 2)),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Network Services',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Column(
              children: [
                _buildNetworkServiceItem('FTP Server', 'Stopped', false),
                _buildNetworkServiceItem('WiFi Share', 'Running', true),
                _buildNetworkServiceItem('LAN Discovery', 'Active', true),
                _buildNetworkServiceItem('File Sync', 'Idle', false),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNetworkServiceItem(String name, String status, bool isActive) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: isActive
                  ? const Color(0xFF27AE60)
                  : const Color(0xFF95A5A6),
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              name,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            ),
          ),
          Text(
            status,
            style: TextStyle(
              fontSize: 10,
              color: isActive
                  ? const Color(0xFF27AE60)
                  : const Color(0xFF95A5A6),
            ),
          ),
        ],
      ),
    );
  }

  void _switchToDualPane(WidgetRef ref) {
    ref.read(currentFileManagerViewModeProvider.notifier).state =
        FileManagerViewMode.dualPane;
  }

  void _switchToSinglePane(WidgetRef ref) {
    ref.read(currentFileManagerViewModeProvider.notifier).state =
        FileManagerViewMode.singlePane;
  }

  void _showGlobalSearch(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Global File Search'),
        content: const TextField(
          decoration: InputDecoration(
            hintText: 'Search across all directories...',
            prefixIcon: Icon(Icons.search),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }

  void _showStorageScan(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Storage Scanner'),
        content: SizedBox(
          width: 400,
          child: ScanButtonWidget(
            appName: 'File Manager',
            buttonText: 'Start Full Scan',
            buttonIcon: Icons.scanner,
            onScanComplete: (result) {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Scan completed! Found ${result.totalFiles} files.',
                  ),
                  duration: const Duration(seconds: 3),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
