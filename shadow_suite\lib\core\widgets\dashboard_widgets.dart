import 'package:flutter/material.dart';

/// Dashboard widget model
class DashboardWidget {
  final String id;
  final DashboardWidgetType type;
  final DashboardPosition position;
  final DashboardSize size;
  final Map<String, dynamic> settings;
  final bool isActive;

  const DashboardWidget({
    required this.id,
    required this.type,
    required this.position,
    required this.size,
    required this.settings,
    required this.isActive,
  });

  DashboardWidget copyWith({
    String? id,
    DashboardWidgetType? type,
    DashboardPosition? position,
    DashboardSize? size,
    Map<String, dynamic>? settings,
    bool? isActive,
  }) {
    return DashboardWidget(
      id: id ?? this.id,
      type: type ?? this.type,
      position: position ?? this.position,
      size: size ?? this.size,
      settings: settings ?? this.settings,
      isActive: isActive ?? this.isActive,
    );
  }
}

/// Dashboard widget types
enum DashboardWidgetType {
  quickStats,
  recentActivity,
  storageUsage,
  performanceMonitor,
  aiProcessingStatus,
  quickActions,
  crossAppSearch,
  weatherWidget,
  calendarWidget,
  notesWidget,
}

/// Dashboard position
class DashboardPosition {
  final int x;
  final int y;

  const DashboardPosition({
    required this.x,
    required this.y,
  });

  DashboardPosition copyWith({
    int? x,
    int? y,
  }) {
    return DashboardPosition(
      x: x ?? this.x,
      y: y ?? this.y,
    );
  }
}

/// Dashboard size
class DashboardSize {
  final int width;
  final int height;

  const DashboardSize({
    required this.width,
    required this.height,
  });

  DashboardSize copyWith({
    int? width,
    int? height,
  }) {
    return DashboardSize(
      width: width ?? this.width,
      height: height ?? this.height,
    );
  }
}

/// Quick stats widget
class QuickStatsWidget extends StatelessWidget {
  final Map<String, Map<String, dynamic>> statistics;

  const QuickStatsWidget({
    super.key,
    required this.statistics,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Quick Statistics',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'Media Files',
                      '${statistics['smart_gallery']?['total_media'] ?? 0}',
                      Icons.photo_library,
                      const Color(0xFF8E44AD),
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      'Transactions',
                      '${statistics['money_manager']?['total_transactions'] ?? 0}',
                      Icons.account_balance_wallet,
                      const Color(0xFF27AE60),
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      'Files',
                      '${statistics['file_manager']?['total_files'] ?? 0}',
                      Icons.folder,
                      const Color(0xFF3498DB),
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      'Music/Videos',
                      '${statistics['shadow_player']?['total_media'] ?? 0}',
                      Icons.play_circle,
                      const Color(0xFFE74C3C),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}

/// Recent activity widget
class RecentActivityWidget extends StatelessWidget {
  final List<dynamic> activities;

  const RecentActivityWidget({
    super.key,
    required this.activities,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Recent Activity',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: activities.isEmpty
                  ? const Center(
                      child: Text(
                        'No recent activity',
                        style: TextStyle(color: Colors.grey),
                      ),
                    )
                  : ListView.builder(
                      itemCount: activities.length,
                      itemBuilder: (context, index) {
                        final activity = activities[index];
                        return ListTile(
                          leading: _getActivityIcon(activity['type']),
                          title: Text(activity['title'] ?? 'Unknown Activity'),
                          subtitle: Text(activity['description'] ?? ''),
                          trailing: Text(
                            _formatTime(activity['timestamp']),
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _getActivityIcon(String? type) {
    IconData iconData;
    Color color;

    switch (type) {
      case 'media_added':
        iconData = Icons.add_photo_alternate;
        color = const Color(0xFF8E44AD);
        break;
      case 'transaction_added':
        iconData = Icons.add_circle;
        color = const Color(0xFF27AE60);
        break;
      case 'file_uploaded':
        iconData = Icons.upload_file;
        color = const Color(0xFF3498DB);
        break;
      case 'music_played':
        iconData = Icons.play_arrow;
        color = const Color(0xFFE74C3C);
        break;
      default:
        iconData = Icons.info;
        color = Colors.grey;
    }

    return Icon(iconData, color: color);
  }

  String _formatTime(dynamic timestamp) {
    if (timestamp == null) return '';
    
    try {
      final dateTime = timestamp is DateTime 
          ? timestamp 
          : DateTime.fromMillisecondsSinceEpoch(timestamp);
      
      final now = DateTime.now();
      final difference = now.difference(dateTime);
      
      if (difference.inMinutes < 1) {
        return 'Just now';
      } else if (difference.inHours < 1) {
        return '${difference.inMinutes}m ago';
      } else if (difference.inDays < 1) {
        return '${difference.inHours}h ago';
      } else {
        return '${difference.inDays}d ago';
      }
    } catch (e) {
      return '';
    }
  }
}

/// Storage usage widget
class StorageUsageWidget extends StatelessWidget {
  final Map<String, Map<String, dynamic>> statistics;

  const StorageUsageWidget({
    super.key,
    required this.statistics,
  });

  @override
  Widget build(BuildContext context) {
    final totalSize = statistics['smart_gallery']?['total_size'] ?? 0;
    final usedPercentage = (totalSize / (1024 * 1024 * 1024 * 100)) * 100; // Assume 100GB total

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Storage Usage',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Column(
                children: [
                  CircularProgressIndicator(
                    value: usedPercentage / 100,
                    strokeWidth: 8,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      usedPercentage > 80 ? Colors.red : 
                      usedPercentage > 60 ? Colors.orange : Colors.green,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '${usedPercentage.toStringAsFixed(1)}% Used',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${_formatBytes(totalSize)} / 100 GB',
                    style: const TextStyle(
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}

/// Performance monitor widget
class PerformanceMonitorWidget extends StatelessWidget {
  final Map<String, dynamic> performance;

  const PerformanceMonitorWidget({
    super.key,
    required this.performance,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Performance Monitor',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Column(
                children: [
                  _buildPerformanceItem(
                    'CPU Usage',
                    performance['cpuUsage'] ?? 0.0,
                    '%',
                    Colors.blue,
                  ),
                  const SizedBox(height: 8),
                  _buildPerformanceItem(
                    'Memory Usage',
                    performance['memoryUsage'] ?? 0.0,
                    '%',
                    Colors.green,
                  ),
                  const SizedBox(height: 8),
                  _buildPerformanceItem(
                    'Response Time',
                    performance['responseTime'] ?? 0.0,
                    'ms',
                    Colors.orange,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceItem(String label, double value, String unit, Color color) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(label),
        ),
        Expanded(
          flex: 3,
          child: LinearProgressIndicator(
            value: value / 100,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ),
        Expanded(
          child: Text(
            '${value.toStringAsFixed(1)}$unit',
            textAlign: TextAlign.right,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ),
      ],
    );
  }
}

/// Quick actions widget
class QuickActionsWidget extends StatelessWidget {
  final VoidCallback? onAddTransaction;
  final VoidCallback? onScanMedia;
  final VoidCallback? onCreateNote;
  final VoidCallback? onOpenFileManager;

  const QuickActionsWidget({
    super.key,
    this.onAddTransaction,
    this.onScanMedia,
    this.onCreateNote,
    this.onOpenFileManager,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Quick Actions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
                children: [
                  _buildActionButton(
                    'Add Transaction',
                    Icons.add_circle,
                    const Color(0xFF27AE60),
                    onAddTransaction,
                  ),
                  _buildActionButton(
                    'Scan Media',
                    Icons.photo_camera,
                    const Color(0xFF8E44AD),
                    onScanMedia,
                  ),
                  _buildActionButton(
                    'Create Note',
                    Icons.note_add,
                    const Color(0xFF3498DB),
                    onCreateNote,
                  ),
                  _buildActionButton(
                    'File Manager',
                    Icons.folder_open,
                    const Color(0xFFF39C12),
                    onOpenFileManager,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(String label, IconData icon, Color color, VoidCallback? onPressed) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 24),
          const SizedBox(height: 4),
          Text(
            label,
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 12),
          ),
        ],
      ),
    );
  }
}
