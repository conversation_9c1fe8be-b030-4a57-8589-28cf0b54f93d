import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/file_manager_models.dart';

class FileOperationsService {
  static final Map<String, FileOperation> _activeOperations = {};
  static final StreamController<FileOperationProgress> _progressController = 
      StreamController<FileOperationProgress>.broadcast();
  
  static Stream<FileOperationProgress> get progressStream => _progressController.stream;
  
  // Clipboard for copy/cut operations
  static final List<String> _clipboard = [];
  static bool _isCutOperation = false;
  
  // Multi-selection support
  static final Set<String> _selectedItems = {};
  
  // CRUD Operations
  
  /// Create a new folder
  static Future<bool> createFolder(String parentPath, String folderName) async {
    final startTime = DateTime.now();
    
    try {
      final folderPath = '$parentPath/$folderName';
      final directory = Directory(folderPath);
      
      if (await directory.exists()) {
        throw FileSystemException('Folder already exists', folderPath);
      }
      
      await directory.create(recursive: true);
      
      // Ensure <100ms response time
      final duration = DateTime.now().difference(startTime);
      if (duration.inMilliseconds > 100) {
        debugPrint('Warning: Create folder took ${duration.inMilliseconds}ms');
      }
      
      return true;
    } catch (error) {
      debugPrint('Error creating folder: $error');
      return false;
    }
  }
  
  /// Create a new file
  static Future<bool> createFile(String parentPath, String fileName, {String content = ''}) async {
    final startTime = DateTime.now();
    
    try {
      final filePath = '$parentPath/$fileName';
      final file = File(filePath);
      
      if (await file.exists()) {
        throw FileSystemException('File already exists', filePath);
      }
      
      await file.writeAsString(content);
      
      // Ensure <100ms response time
      final duration = DateTime.now().difference(startTime);
      if (duration.inMilliseconds > 100) {
        debugPrint('Warning: Create file took ${duration.inMilliseconds}ms');
      }
      
      return true;
    } catch (error) {
      debugPrint('Error creating file: $error');
      return false;
    }
  }
  
  /// Copy files/folders
  static Future<String> copyItems(List<String> sourcePaths, String destinationPath) async {
    final operationId = DateTime.now().millisecondsSinceEpoch.toString();
    final operation = FileOperation(
      id: operationId,
      type: FileOperationType.copy,
      sourcePath: sourcePaths.join(', '),
      destinationPath: destinationPath,
      status: FileOperationStatus.inProgress,
      progress: 0.0,
      totalBytes: 0,
      processedBytes: 0,
      startTime: DateTime.now(),
    );
    
    _activeOperations[operationId] = operation;
    
    try {
      for (int i = 0; i < sourcePaths.length; i++) {
        final sourcePath = sourcePaths[i];
        final sourceEntity = await FileSystemEntity.type(sourcePath);
        
        if (sourceEntity == FileSystemEntityType.notFound) {
          throw FileSystemException('Source not found', sourcePath);
        }
        
        final fileName = sourcePath.split('/').last;
        final destPath = '$destinationPath/$fileName';
        
        if (sourceEntity == FileSystemEntityType.directory) {
          await _copyDirectory(sourcePath, destPath);
        } else {
          await _copyFile(sourcePath, destPath);
        }
        
        // Update progress
        final progress = FileOperationProgress(
          operationId: operationId,
          currentItem: fileName,
          itemsCompleted: i + 1,
          totalItems: sourcePaths.length,
          bytesTransferred: 0,
          totalBytes: 0,
        );
        
        _progressController.add(progress);
      }
      
      operation.status = FileOperationStatus.completed;
      operation.endTime = DateTime.now();
      
      return operationId;
    } catch (error) {
      operation.status = FileOperationStatus.failed;
      operation.errorMessage = error.toString();
      operation.endTime = DateTime.now();
      rethrow;
    }
  }
  
  /// Move files/folders
  static Future<String> moveItems(List<String> sourcePaths, String destinationPath) async {
    final operationId = DateTime.now().millisecondsSinceEpoch.toString();
    final operation = FileOperation(
      id: operationId,
      type: FileOperationType.move,
      sourcePath: sourcePaths.join(', '),
      destinationPath: destinationPath,
      status: FileOperationStatus.inProgress,
      progress: 0.0,
      totalBytes: 0,
      processedBytes: 0,
      startTime: DateTime.now(),
    );
    
    _activeOperations[operationId] = operation;
    
    try {
      for (int i = 0; i < sourcePaths.length; i++) {
        final sourcePath = sourcePaths[i];
        final sourceEntity = await FileSystemEntity.type(sourcePath);
        
        if (sourceEntity == FileSystemEntityType.notFound) {
          throw FileSystemException('Source not found', sourcePath);
        }
        
        final fileName = sourcePath.split('/').last;
        final destPath = '$destinationPath/$fileName';
        
        if (sourceEntity == FileSystemEntityType.directory) {
          await Directory(sourcePath).rename(destPath);
        } else {
          await File(sourcePath).rename(destPath);
        }
        
        // Update progress
        final progress = FileOperationProgress(
          operationId: operationId,
          currentItem: fileName,
          itemsCompleted: i + 1,
          totalItems: sourcePaths.length,
          bytesTransferred: 0,
          totalBytes: 0,
        );
        
        _progressController.add(progress);
      }
      
      operation.status = FileOperationStatus.completed;
      operation.endTime = DateTime.now();
      
      return operationId;
    } catch (error) {
      operation.status = FileOperationStatus.failed;
      operation.errorMessage = error.toString();
      operation.endTime = DateTime.now();
      rethrow;
    }
  }
  
  /// Delete files/folders
  static Future<String> deleteItems(List<String> paths) async {
    final operationId = DateTime.now().millisecondsSinceEpoch.toString();
    final operation = FileOperation(
      id: operationId,
      type: FileOperationType.delete,
      sourcePath: paths.join(', '),
      destinationPath: '',
      status: FileOperationStatus.inProgress,
      progress: 0.0,
      totalBytes: 0,
      processedBytes: 0,
      startTime: DateTime.now(),
    );
    
    _activeOperations[operationId] = operation;
    
    try {
      for (int i = 0; i < paths.length; i++) {
        final path = paths[i];
        final entity = await FileSystemEntity.type(path);
        
        if (entity == FileSystemEntityType.notFound) {
          continue; // Skip if already deleted
        }
        
        if (entity == FileSystemEntityType.directory) {
          await Directory(path).delete(recursive: true);
        } else {
          await File(path).delete();
        }
        
        // Update progress
        final progress = FileOperationProgress(
          operationId: operationId,
          currentItem: path.split('/').last,
          itemsCompleted: i + 1,
          totalItems: paths.length,
          bytesTransferred: 0,
          totalBytes: 0,
        );
        
        _progressController.add(progress);
      }
      
      operation.status = FileOperationStatus.completed;
      operation.endTime = DateTime.now();
      
      return operationId;
    } catch (error) {
      operation.status = FileOperationStatus.failed;
      operation.errorMessage = error.toString();
      operation.endTime = DateTime.now();
      rethrow;
    }
  }
  
  /// Rename file/folder
  static Future<bool> renameItem(String oldPath, String newName) async {
    final startTime = DateTime.now();
    
    try {
      final parentPath = oldPath.substring(0, oldPath.lastIndexOf('/'));
      final newPath = '$parentPath/$newName';
      
      final entity = await FileSystemEntity.type(oldPath);
      
      if (entity == FileSystemEntityType.notFound) {
        throw FileSystemException('Source not found', oldPath);
      }
      
      if (entity == FileSystemEntityType.directory) {
        await Directory(oldPath).rename(newPath);
      } else {
        await File(oldPath).rename(newPath);
      }
      
      // Ensure <100ms response time
      final duration = DateTime.now().difference(startTime);
      if (duration.inMilliseconds > 100) {
        debugPrint('Warning: Rename took ${duration.inMilliseconds}ms');
      }
      
      return true;
    } catch (error) {
      debugPrint('Error renaming item: $error');
      return false;
    }
  }
  
  // Clipboard Operations
  
  /// Copy items to clipboard
  static void copyToClipboard(List<String> paths) {
    _clipboard.clear();
    _clipboard.addAll(paths);
    _isCutOperation = false;
  }
  
  /// Cut items to clipboard
  static void cutToClipboard(List<String> paths) {
    _clipboard.clear();
    _clipboard.addAll(paths);
    _isCutOperation = true;
  }
  
  /// Paste items from clipboard
  static Future<String?> pasteFromClipboard(String destinationPath) async {
    if (_clipboard.isEmpty) return null;
    
    try {
      if (_isCutOperation) {
        return await moveItems(_clipboard, destinationPath);
      } else {
        return await copyItems(_clipboard, destinationPath);
      }
    } finally {
      if (_isCutOperation) {
        _clipboard.clear(); // Clear clipboard after cut operation
      }
    }
  }
  
  /// Check if clipboard has items
  static bool get hasClipboardItems => _clipboard.isNotEmpty;
  
  /// Check if clipboard operation is cut
  static bool get isClipboardCutOperation => _isCutOperation;
  
  // Selection Management
  
  /// Add item to selection
  static void addToSelection(String path) {
    _selectedItems.add(path);
  }
  
  /// Remove item from selection
  static void removeFromSelection(String path) {
    _selectedItems.remove(path);
  }
  
  /// Toggle item selection
  static void toggleSelection(String path) {
    if (_selectedItems.contains(path)) {
      _selectedItems.remove(path);
    } else {
      _selectedItems.add(path);
    }
  }
  
  /// Clear all selections
  static void clearSelection() {
    _selectedItems.clear();
  }
  
  /// Get selected items
  static Set<String> get selectedItems => Set.unmodifiable(_selectedItems);
  
  /// Check if item is selected
  static bool isSelected(String path) => _selectedItems.contains(path);
  
  // Operation Management
  
  /// Get active operations
  static Map<String, FileOperation> get activeOperations => Map.unmodifiable(_activeOperations);
  
  /// Cancel operation
  static void cancelOperation(String operationId) {
    final operation = _activeOperations[operationId];
    if (operation != null) {
      operation.status = FileOperationStatus.cancelled;
      operation.endTime = DateTime.now();
    }
  }
  
  // Helper Methods
  
  static Future<void> _copyFile(String sourcePath, String destPath) async {
    final sourceFile = File(sourcePath);
    await sourceFile.copy(destPath);
  }
  
  static Future<void> _copyDirectory(String sourcePath, String destPath) async {
    final sourceDir = Directory(sourcePath);
    final destDir = Directory(destPath);
    
    await destDir.create(recursive: true);
    
    await for (final entity in sourceDir.list(recursive: false)) {
      final fileName = entity.path.split('/').last;
      final newDestPath = '$destPath/$fileName';
      
      if (entity is Directory) {
        await _copyDirectory(entity.path, newDestPath);
      } else if (entity is File) {
        await entity.copy(newDestPath);
      }
    }
  }
  
  /// Dispose resources
  static void dispose() {
    _progressController.close();
    _activeOperations.clear();
    _clipboard.clear();
    _selectedItems.clear();
  }
}

/// Progress information for file operations
class FileOperationProgress {
  final String operationId;
  final String currentItem;
  final int itemsCompleted;
  final int totalItems;
  final int bytesTransferred;
  final int totalBytes;
  
  const FileOperationProgress({
    required this.operationId,
    required this.currentItem,
    required this.itemsCompleted,
    required this.totalItems,
    required this.bytesTransferred,
    required this.totalBytes,
  });
  
  double get itemProgress => totalItems > 0 ? itemsCompleted / totalItems : 0.0;
  double get byteProgress => totalBytes > 0 ? bytesTransferred / totalBytes : 0.0;
}
