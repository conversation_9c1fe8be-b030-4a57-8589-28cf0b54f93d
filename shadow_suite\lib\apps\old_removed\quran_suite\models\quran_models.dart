/// Text versions for Quran display
enum TextVersion { uthmani, simplified, indoPak }

/// Revelation types
enum RevelationType { meccan, medinan }

/// Quran event types
enum QuranEventType {
  initialized,
  textVersionChanged,
  languageChanged,
  settingsChanged,
  bookmarkAdded,
  bookmarkRemoved,
  sessionStarted,
  sessionEnded,
  error,
}

/// Search match types
enum SearchMatchType { arabic, transliteration, translation }

/// Bookmark categories
enum BookmarkCategory { general, study, favorite, memorization, reflection }

/// Verse model
class Verse {
  final int number;
  final String arabicText;
  final String? transliteration;
  final String? translation;
  final int juzNumber;
  final int hizbNumber;
  final int rukuNumber;
  final bool sajdah;

  const Verse({
    required this.number,
    required this.arabicText,
    this.transliteration,
    this.translation,
    required this.juzNumber,
    required this.hizbNumber,
    required this.rukuNumber,
    this.sajdah = false,
  });

  /// Convenience getter for hasSajdah
  bool get hasSajdah => sajdah;
}

/// Surah model
class Surah {
  final int number;
  final String name;
  final String arabicName;
  final String englishName;
  final RevelationType revelationType;
  final int versesCount;
  final int revelationOrder;
  final List<Verse> verses;

  const Surah({
    required this.number,
    required this.name,
    required this.arabicName,
    required this.englishName,
    required this.revelationType,
    required this.versesCount,
    required this.revelationOrder,
    required this.verses,
  });
}

/// Translation model
class Translation {
  final String language;
  final String text;
  final String translator;
  final String? footnote;

  const Translation({
    required this.language,
    required this.text,
    required this.translator,
    this.footnote,
  });
}

/// Tafseer model
class Tafseer {
  final String author;
  final String text;
  final String language;
  final String? source;

  const Tafseer({
    required this.author,
    required this.text,
    required this.language,
    this.source,
  });
}

/// Bookmark data
class BookmarkData {
  final String id;
  final int surahNumber;
  final int verseNumber;
  final String title;
  final String? notes;
  final String? note; // Keep for backward compatibility
  final BookmarkCategory category;
  final DateTime createdAt;
  final DateTime updatedAt;

  const BookmarkData({
    required this.id,
    required this.surahNumber,
    required this.verseNumber,
    required this.title,
    this.notes,
    this.note,
    this.category = BookmarkCategory.general,
    required this.createdAt,
    required this.updatedAt,
  });
}

/// Reading session
class ReadingSession {
  final String id;
  final DateTime startTime;
  DateTime? endTime;
  final int startSurah;
  final int startVerse;
  int currentSurah;
  int currentVerse;
  int versesRead;
  bool isActive;
  Duration? duration;
  DateTime? lastReadTime;

  ReadingSession({
    required this.id,
    required this.startTime,
    this.endTime,
    required this.startSurah,
    required this.startVerse,
    required this.currentSurah,
    required this.currentVerse,
    this.versesRead = 0,
    this.isActive = true,
    this.duration,
    this.lastReadTime,
  });

  static ReadingSession empty() {
    return ReadingSession(
      id: '',
      startTime: DateTime.now(),
      startSurah: 1,
      startVerse: 1,
      currentSurah: 1,
      currentVerse: 1,
    );
  }
}

/// Reading settings
class ReadingSettings {
  final double fontSize;
  final String fontFamily;
  final double lineHeight;
  final bool showTransliteration;
  final bool showTranslation;
  final bool showTafseer;
  final String translationLanguage;
  final bool nightMode;
  final double brightness;

  // Additional properties for VerseWidget
  final double arabicFontSize;
  final String arabicFontFamily;
  final double lineSpacing;
  final double transliterationFontSize;
  final double translationFontSize;

  const ReadingSettings({
    this.fontSize = 18.0,
    this.fontFamily = 'Amiri',
    this.lineHeight = 1.5,
    this.showTransliteration = false,
    this.showTranslation = true,
    this.showTafseer = false,
    this.translationLanguage = 'en',
    this.nightMode = false,
    this.brightness = 1.0,
    this.arabicFontSize = 20.0,
    this.arabicFontFamily = 'Amiri',
    this.lineSpacing = 1.8,
    this.transliterationFontSize = 14.0,
    this.translationFontSize = 16.0,
  });

  static ReadingSettings defaultSettings() => const ReadingSettings();

  ReadingSettings copyWith({
    double? fontSize,
    String? fontFamily,
    double? lineHeight,
    bool? showTransliteration,
    bool? showTranslation,
    bool? showTafseer,
    String? translationLanguage,
    bool? nightMode,
    double? brightness,
    double? arabicFontSize,
    String? arabicFontFamily,
    double? lineSpacing,
    double? transliterationFontSize,
    double? translationFontSize,
  }) {
    return ReadingSettings(
      fontSize: fontSize ?? this.fontSize,
      fontFamily: fontFamily ?? this.fontFamily,
      lineHeight: lineHeight ?? this.lineHeight,
      showTransliteration: showTransliteration ?? this.showTransliteration,
      showTranslation: showTranslation ?? this.showTranslation,
      showTafseer: showTafseer ?? this.showTafseer,
      translationLanguage: translationLanguage ?? this.translationLanguage,
      nightMode: nightMode ?? this.nightMode,
      brightness: brightness ?? this.brightness,
      arabicFontSize: arabicFontSize ?? this.arabicFontSize,
      arabicFontFamily: arabicFontFamily ?? this.arabicFontFamily,
      lineSpacing: lineSpacing ?? this.lineSpacing,
      transliterationFontSize:
          transliterationFontSize ?? this.transliterationFontSize,
      translationFontSize: translationFontSize ?? this.translationFontSize,
    );
  }
}

/// Search options
class SearchOptions {
  final bool searchInArabic;
  final bool searchInTransliteration;
  final bool searchInTranslation;
  final int maxResults;
  final double minRelevanceScore;

  const SearchOptions({
    this.searchInArabic = true,
    this.searchInTransliteration = true,
    this.searchInTranslation = true,
    this.maxResults = 50,
    this.minRelevanceScore = 0.1,
  });

  static SearchOptions defaultOptions() => const SearchOptions();
}

/// Search result
class SearchResult {
  final int surahNumber;
  final int verseNumber;
  final Verse verse;
  final SearchMatchType matchType;
  final double relevanceScore;

  const SearchResult({
    required this.surahNumber,
    required this.verseNumber,
    required this.verse,
    required this.matchType,
    required this.relevanceScore,
  });
}

/// Quran statistics
class QuranStatistics {
  final int totalSurahs;
  final int totalVerses;
  final int totalWords;
  final int totalLetters;
  final TextVersion currentVersion;

  const QuranStatistics({
    required this.totalSurahs,
    required this.totalVerses,
    required this.totalWords,
    required this.totalLetters,
    required this.currentVersion,
  });
}

/// Reading progress
class ReadingProgress {
  final String sessionId;
  final int surahNumber;
  final int verseNumber;
  final int versesRead;
  final Duration timeSpent;
  final double readingSpeed;

  const ReadingProgress({
    required this.sessionId,
    required this.surahNumber,
    required this.verseNumber,
    required this.versesRead,
    required this.timeSpent,
    required this.readingSpeed,
  });
}

/// Reading statistics
class ReadingStatistics {
  final int totalSessions;
  final int totalVersesRead;
  final Duration totalTimeSpent;
  final Duration averageSessionLength;
  final double averageReadingSpeed;
  final int bookmarksCount;
  final String favoriteSuprah;
  final int currentStreak;

  const ReadingStatistics({
    required this.totalSessions,
    required this.totalVersesRead,
    required this.totalTimeSpent,
    required this.averageSessionLength,
    required this.averageReadingSpeed,
    required this.bookmarksCount,
    required this.favoriteSuprah,
    required this.currentStreak,
  });
}

/// Quran event
class QuranEvent {
  final QuranEventType type;
  final String message;
  final DateTime timestamp;

  const QuranEvent({
    required this.type,
    required this.message,
    required this.timestamp,
  });
}
