import 'dart:async';
import 'dart:io';
import 'package:path/path.dart' as path;
import '../models/smart_gallery_models.dart';
import '../../../core/database/database_service.dart';
import '../../../core/services/error_handler.dart' as error_handler;
// Removed unused import

/// Batch operations service for SmartGallery+ with progress tracking
class BatchOperationsService {
  static bool _isProcessing = false;
  static final StreamController<BatchOperationProgress> _progressController =
      StreamController<BatchOperationProgress>.broadcast();
  static final List<BatchOperation> _operationQueue = [];
  static BatchOperation? _currentOperation;

  /// Start batch operation
  static Future<String> startBatchOperation(
    BatchOperationType type,
    List<String> mediaIds, {
    String? targetPath,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      final operationId = 'batch_${DateTime.now().millisecondsSinceEpoch}';

      final operation = BatchOperation(
        id: operationId,
        type: type,
        mediaIds: mediaIds,
        targetPath: targetPath,
        parameters: parameters ?? {},
        status: BatchOperationStatus.queued,
        createdAt: DateTime.now(),
      );

      _operationQueue.add(operation);

      // Start processing if not already running
      if (!_isProcessing) {
        _processNextOperation();
      }

      return operationId;
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.operation,
        context: 'Start batch operation failed',
      );
      rethrow;
    }
  }

  /// Process next operation in queue
  static Future<void> _processNextOperation() async {
    if (_operationQueue.isEmpty || _isProcessing) return;

    _isProcessing = true;
    _currentOperation = _operationQueue.removeAt(0);

    try {
      await _executeBatchOperation(_currentOperation!);
    } catch (e) {
      _updateOperationStatus(
        _currentOperation!,
        BatchOperationStatus.failed,
        error: e.toString(),
      );
    } finally {
      _currentOperation = null;
      _isProcessing = false;

      // Process next operation
      if (_operationQueue.isNotEmpty) {
        _processNextOperation();
      }
    }
  }

  /// Execute batch operation
  static Future<void> _executeBatchOperation(BatchOperation operation) async {
    _updateOperationStatus(operation, BatchOperationStatus.processing);

    switch (operation.type) {
      case BatchOperationType.move:
        await _executeMoveOperation(operation);
        break;
      case BatchOperationType.copy:
        await _executeCopyOperation(operation);
        break;
      case BatchOperationType.delete:
        await _executeDeleteOperation(operation);
        break;
      case BatchOperationType.tag:
        await _executeTagOperation(operation);
        break;
      case BatchOperationType.favorite:
        await _executeFavoriteOperation(operation);
        break;
      case BatchOperationType.lock:
        await _executeLockOperation(operation);
        break;
      case BatchOperationType.unlock:
        await _executeUnlockOperation(operation);
        break;
      case BatchOperationType.compress:
        await _executeCompressOperation(operation);
        break;
      case BatchOperationType.convert:
        await _executeConvertOperation(operation);
        break;
      case BatchOperationType.backup:
        await _executeBackupOperation(operation);
        break;
    }

    _updateOperationStatus(operation, BatchOperationStatus.completed);
  }

  /// Execute move operation
  static Future<void> _executeMoveOperation(BatchOperation operation) async {
    if (operation.targetPath == null) {
      throw Exception('Target path required for move operation');
    }

    final targetDir = Directory(operation.targetPath!);
    if (!await targetDir.exists()) {
      await targetDir.create(recursive: true);
    }

    for (int i = 0; i < operation.mediaIds.length; i++) {
      final mediaId = operation.mediaIds[i];

      try {
        // Get media item
        final mediaItem = await _getMediaItem(mediaId);
        if (mediaItem == null) continue;

        // Move file
        final sourceFile = File(mediaItem.path);
        if (await sourceFile.exists()) {
          final targetPath = path.join(
            operation.targetPath!,
            path.basename(mediaItem.path),
          );
          await sourceFile.rename(targetPath);

          // Update database
          await DatabaseService.safeUpdate(
            'smart_gallery_items',
            {'path': targetPath},
            where: 'id = ?',
            whereArgs: [mediaId],
          );
        }

        // Update progress
        _updateProgress(operation, i + 1, operation.mediaIds.length);
      } catch (e) {
        // Continue with next item
        continue;
      }
    }
  }

  /// Execute copy operation
  static Future<void> _executeCopyOperation(BatchOperation operation) async {
    if (operation.targetPath == null) {
      throw Exception('Target path required for copy operation');
    }

    final targetDir = Directory(operation.targetPath!);
    if (!await targetDir.exists()) {
      await targetDir.create(recursive: true);
    }

    for (int i = 0; i < operation.mediaIds.length; i++) {
      final mediaId = operation.mediaIds[i];

      try {
        // Get media item
        final mediaItem = await _getMediaItem(mediaId);
        if (mediaItem == null) continue;

        // Copy file
        final sourceFile = File(mediaItem.path);
        if (await sourceFile.exists()) {
          final targetPath = path.join(
            operation.targetPath!,
            path.basename(mediaItem.path),
          );
          await sourceFile.copy(targetPath);
        }

        // Update progress
        _updateProgress(operation, i + 1, operation.mediaIds.length);
      } catch (e) {
        // Continue with next item
        continue;
      }
    }
  }

  /// Execute delete operation
  static Future<void> _executeDeleteOperation(BatchOperation operation) async {
    final moveToTrash = operation.parameters['moveToTrash'] as bool? ?? true;

    for (int i = 0; i < operation.mediaIds.length; i++) {
      final mediaId = operation.mediaIds[i];

      try {
        // Get media item
        final mediaItem = await _getMediaItem(mediaId);
        if (mediaItem == null) continue;

        if (moveToTrash) {
          // Move to trash folder
          await _moveToTrash(mediaItem);
        } else {
          // Permanently delete
          final file = File(mediaItem.path);
          if (await file.exists()) {
            await file.delete();
          }
        }

        // Remove from database
        await DatabaseService.safeDelete(
          'smart_gallery_items',
          where: 'id = ?',
          whereArgs: [mediaId],
        );

        // Update progress
        _updateProgress(operation, i + 1, operation.mediaIds.length);
      } catch (e) {
        // Continue with next item
        continue;
      }
    }
  }

  /// Execute tag operation
  static Future<void> _executeTagOperation(BatchOperation operation) async {
    final tags = operation.parameters['tags'] as List<String>? ?? [];
    final addTags = operation.parameters['addTags'] as bool? ?? true;

    for (int i = 0; i < operation.mediaIds.length; i++) {
      final mediaId = operation.mediaIds[i];

      try {
        // Get current tags
        final results = await DatabaseService.safeQuery(
          'SELECT custom_tags FROM smart_gallery_items WHERE id = ?',
          [mediaId],
        );

        if (results.isNotEmpty) {
          final currentTags = (results.first['custom_tags'] as String? ?? '')
              .split(',')
              .where((tag) => tag.trim().isNotEmpty)
              .toSet();

          if (addTags) {
            currentTags.addAll(tags);
          } else {
            currentTags.removeAll(tags);
          }

          // Update database
          await DatabaseService.safeUpdate(
            'smart_gallery_items',
            {'custom_tags': currentTags.join(',')},
            where: 'id = ?',
            whereArgs: [mediaId],
          );
        }

        // Update progress
        _updateProgress(operation, i + 1, operation.mediaIds.length);
      } catch (e) {
        // Continue with next item
        continue;
      }
    }
  }

  /// Execute favorite operation
  static Future<void> _executeFavoriteOperation(
    BatchOperation operation,
  ) async {
    final isFavorite = operation.parameters['isFavorite'] as bool? ?? true;

    for (int i = 0; i < operation.mediaIds.length; i++) {
      final mediaId = operation.mediaIds[i];

      try {
        await DatabaseService.safeUpdate(
          'smart_gallery_items',
          {'is_favorite': isFavorite ? 1 : 0},
          where: 'id = ?',
          whereArgs: [mediaId],
        );

        // Update progress
        _updateProgress(operation, i + 1, operation.mediaIds.length);
      } catch (e) {
        // Continue with next item
        continue;
      }
    }
  }

  /// Execute lock operation
  static Future<void> _executeLockOperation(BatchOperation operation) async {
    for (int i = 0; i < operation.mediaIds.length; i++) {
      final mediaId = operation.mediaIds[i];

      try {
        await DatabaseService.safeUpdate(
          'smart_gallery_items',
          {'is_locked': 1},
          where: 'id = ?',
          whereArgs: [mediaId],
        );

        // Update progress
        _updateProgress(operation, i + 1, operation.mediaIds.length);
      } catch (e) {
        // Continue with next item
        continue;
      }
    }
  }

  /// Execute unlock operation
  static Future<void> _executeUnlockOperation(BatchOperation operation) async {
    for (int i = 0; i < operation.mediaIds.length; i++) {
      final mediaId = operation.mediaIds[i];

      try {
        await DatabaseService.safeUpdate(
          'smart_gallery_items',
          {'is_locked': 0},
          where: 'id = ?',
          whereArgs: [mediaId],
        );

        // Update progress
        _updateProgress(operation, i + 1, operation.mediaIds.length);
      } catch (e) {
        // Continue with next item
        continue;
      }
    }
  }

  /// Execute compress operation
  static Future<void> _executeCompressOperation(
    BatchOperation operation,
  ) async {
    final quality = operation.parameters['quality'] as int? ?? 80;

    for (int i = 0; i < operation.mediaIds.length; i++) {
      final mediaId = operation.mediaIds[i];

      try {
        // Get media item
        final mediaItem = await _getMediaItem(mediaId);
        if (mediaItem == null || mediaItem.type != MediaType.image) continue;

        // Compress image
        await _compressImage(mediaItem.path, quality);

        // Update progress
        _updateProgress(operation, i + 1, operation.mediaIds.length);
      } catch (e) {
        // Continue with next item
        continue;
      }
    }
  }

  /// Execute convert operation
  static Future<void> _executeConvertOperation(BatchOperation operation) async {
    final targetFormat =
        operation.parameters['targetFormat'] as String? ?? 'jpg';

    for (int i = 0; i < operation.mediaIds.length; i++) {
      final mediaId = operation.mediaIds[i];

      try {
        // Get media item
        final mediaItem = await _getMediaItem(mediaId);
        if (mediaItem == null) continue;

        // Convert format
        await _convertFormat(mediaItem.path, targetFormat);

        // Update progress
        _updateProgress(operation, i + 1, operation.mediaIds.length);
      } catch (e) {
        // Continue with next item
        continue;
      }
    }
  }

  /// Execute backup operation
  static Future<void> _executeBackupOperation(BatchOperation operation) async {
    if (operation.targetPath == null) {
      throw Exception('Target path required for backup operation');
    }

    final backupDir = Directory(operation.targetPath!);
    if (!await backupDir.exists()) {
      await backupDir.create(recursive: true);
    }

    for (int i = 0; i < operation.mediaIds.length; i++) {
      final mediaId = operation.mediaIds[i];

      try {
        // Get media item
        final mediaItem = await _getMediaItem(mediaId);
        if (mediaItem == null) continue;

        // Create backup
        final sourceFile = File(mediaItem.path);
        if (await sourceFile.exists()) {
          final backupPath = path.join(
            operation.targetPath!,
            path.basename(mediaItem.path),
          );
          await sourceFile.copy(backupPath);
        }

        // Update progress
        _updateProgress(operation, i + 1, operation.mediaIds.length);
      } catch (e) {
        // Continue with next item
        continue;
      }
    }
  }

  /// Get media item by ID
  static Future<SmartGalleryItem?> _getMediaItem(String mediaId) async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM smart_gallery_items WHERE id = ?',
        [mediaId],
      );

      if (results.isEmpty) return null;

      final row = results.first;
      return SmartGalleryItem(
        id: row['id'],
        path: row['path'],
        name: row['name'],
        type: MediaType.values[row['type']],
        size: row['size'],
        dateCreated: DateTime.fromMillisecondsSinceEpoch(row['date_created']),
        dateModified: DateTime.fromMillisecondsSinceEpoch(row['date_modified']),
        width: row['width'],
        height: row['height'],
        duration: row['duration'] != null
            ? Duration(milliseconds: row['duration'])
            : null,
        thumbnailPath: row['thumbnail_path'],
        isHidden: row['is_hidden'] == 1,
        isFavorite: row['is_favorite'] == 1,
        isLocked: row['is_locked'] == 1,
        aiTags:
            row['ai_tags']
                ?.split(',')
                .where((tag) => tag.isNotEmpty)
                .toList() ??
            [],
        customTags:
            row['custom_tags']
                ?.split(',')
                .where((tag) => tag.isNotEmpty)
                .toList() ??
            [],
        ocrText: row['ocr_text'],
      );
    } catch (e) {
      return null;
    }
  }

  /// Move file to trash
  static Future<void> _moveToTrash(SmartGalleryItem mediaItem) async {
    // Create trash directory
    final trashDir = Directory('${Directory.current.path}/.trash');
    if (!await trashDir.exists()) {
      await trashDir.create(recursive: true);
    }

    // Move file to trash
    final sourceFile = File(mediaItem.path);
    if (await sourceFile.exists()) {
      final trashPath = path.join(trashDir.path, path.basename(mediaItem.path));
      await sourceFile.rename(trashPath);
    }
  }

  /// Compress image
  static Future<void> _compressImage(String imagePath, int quality) async {
    // Implement image compression using image processing library
    // For now, this is a placeholder
    await Future.delayed(const Duration(milliseconds: 100));
  }

  /// Convert format
  static Future<void> _convertFormat(
    String filePath,
    String targetFormat,
  ) async {
    // Implement format conversion using image/video processing library
    // For now, this is a placeholder
    await Future.delayed(const Duration(milliseconds: 200));
  }

  /// Update operation status
  static void _updateOperationStatus(
    BatchOperation operation,
    BatchOperationStatus status, {
    String? error,
  }) {
    operation.status = status;
    operation.error = error;
    operation.updatedAt = DateTime.now();

    _notifyProgress(
      BatchOperationProgress(
        operationId: operation.id,
        type: operation.type,
        status: status,
        processedItems: operation.processedItems,
        totalItems: operation.mediaIds.length,
        currentItem: operation.currentItem,
        error: error,
        startTime: operation.createdAt,
        endTime:
            status == BatchOperationStatus.completed ||
                status == BatchOperationStatus.failed
            ? DateTime.now()
            : null,
      ),
    );
  }

  /// Update progress
  static void _updateProgress(
    BatchOperation operation,
    int processedItems,
    int totalItems,
  ) {
    operation.processedItems = processedItems;
    operation.currentItem = processedItems < totalItems
        ? operation.mediaIds[processedItems]
        : null;

    _notifyProgress(
      BatchOperationProgress(
        operationId: operation.id,
        type: operation.type,
        status: operation.status,
        processedItems: processedItems,
        totalItems: totalItems,
        currentItem: operation.currentItem,
        startTime: operation.createdAt,
      ),
    );
  }

  /// Notify progress
  static void _notifyProgress(BatchOperationProgress progress) {
    if (!_progressController.isClosed) {
      _progressController.add(progress);
    }
  }

  /// Get operation status
  static BatchOperation? getOperationStatus(String operationId) {
    if (_currentOperation?.id == operationId) {
      return _currentOperation;
    }

    try {
      return _operationQueue.firstWhere((op) => op.id == operationId);
    } catch (e) {
      return null;
    }
  }

  /// Cancel operation
  static bool cancelOperation(String operationId) {
    // Remove from queue if not started
    final queueIndex = _operationQueue.indexWhere((op) => op.id == operationId);
    if (queueIndex != -1) {
      _operationQueue.removeAt(queueIndex);
      return true;
    }

    // Cannot cancel currently running operation
    return false;
  }

  /// Get progress stream
  static Stream<BatchOperationProgress> get progressStream =>
      _progressController.stream;

  /// Dispose resources
  static void dispose() {
    _progressController.close();
    _operationQueue.clear();
    _currentOperation = null;
    _isProcessing = false;
  }
}

/// Batch operation model
class BatchOperation {
  final String id;
  final BatchOperationType type;
  final List<String> mediaIds;
  final String? targetPath;
  final Map<String, dynamic> parameters;
  BatchOperationStatus status;
  final DateTime createdAt;
  DateTime? updatedAt;
  int processedItems = 0;
  String? currentItem;
  String? error;

  BatchOperation({
    required this.id,
    required this.type,
    required this.mediaIds,
    this.targetPath,
    required this.parameters,
    required this.status,
    required this.createdAt,
    this.updatedAt,
  });
}

/// Batch operation progress
class BatchOperationProgress {
  final String operationId;
  final BatchOperationType type;
  final BatchOperationStatus status;
  final int processedItems;
  final int totalItems;
  final String? currentItem;
  final String? error;
  final DateTime startTime;
  final DateTime? endTime;

  const BatchOperationProgress({
    required this.operationId,
    required this.type,
    required this.status,
    required this.processedItems,
    required this.totalItems,
    this.currentItem,
    this.error,
    required this.startTime,
    this.endTime,
  });

  double get progress => totalItems > 0 ? processedItems / totalItems : 0.0;

  Duration get elapsed => (endTime ?? DateTime.now()).difference(startTime);

  Duration? get estimatedTimeRemaining {
    if (processedItems == 0 || processedItems == totalItems) return null;

    final avgTimePerItem = elapsed.inMilliseconds / processedItems;
    final remainingItems = totalItems - processedItems;

    return Duration(milliseconds: (avgTimePerItem * remainingItems).round());
  }
}

/// Batch operation types
enum BatchOperationType {
  move,
  copy,
  delete,
  tag,
  favorite,
  lock,
  unlock,
  compress,
  convert,
  backup,
}

/// Batch operation status
enum BatchOperationStatus { queued, processing, completed, failed, cancelled }
