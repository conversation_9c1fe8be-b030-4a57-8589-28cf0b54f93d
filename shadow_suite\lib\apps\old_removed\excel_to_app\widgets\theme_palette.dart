import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import '../models/excel_app_tool.dart';
import '../services/excel_app_providers.dart';

class ThemePalette extends ConsumerStatefulWidget {
  const ThemePalette({super.key});

  @override
  ConsumerState<ThemePalette> createState() => _ThemePaletteState();
}

class _ThemePaletteState extends ConsumerState<ThemePalette> {
  String? _selectedTheme;
  String? _customBackgroundPath;

  final List<ThemeOption> _predefinedThemes = [
    ThemeOption(
      id: 'android_empty',
      name: 'Android App Style',
      description: 'Clean Android material design background',
      previewColor: Colors.grey.shade100,
      backgroundGradient: LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Colors.grey.shade50, Colors.grey.shade100],
      ),
    ),
    ThemeOption(
      id: 'pc_program',
      name: 'PC Program Style',
      description: 'Classic desktop application background',
      previewColor: Colors.blue.shade50,
      backgroundGradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Colors.blue.shade50, Colors.white],
      ),
    ),
    ThemeOption(
      id: 'solid_white',
      name: 'Solid White',
      description: 'Clean white background',
      previewColor: Colors.white,
      backgroundColor: Colors.white,
    ),
    ThemeOption(
      id: 'solid_dark',
      name: 'Dark Theme',
      description: 'Dark background for modern apps',
      previewColor: Colors.grey.shade800,
      backgroundColor: Colors.grey.shade800,
    ),
    ThemeOption(
      id: 'gradient_blue',
      name: 'Blue Gradient',
      description: 'Professional blue gradient',
      previewColor: Colors.blue.shade300,
      backgroundGradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Colors.blue.shade300, Colors.blue.shade600],
      ),
    ),
    ThemeOption(
      id: 'gradient_green',
      name: 'Green Gradient',
      description: 'Fresh green gradient',
      previewColor: Colors.green.shade300,
      backgroundGradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Colors.green.shade300, Colors.green.shade600],
      ),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final currentTool = ref.watch(currentExcelAppToolProvider);
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          _buildPredefinedThemes(),
          const SizedBox(height: 24),
          _buildCustomBackground(),
          const SizedBox(height: 24),
          _buildPreview(),
          const SizedBox(height: 24),
          _buildActions(currentTool),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: const Color(0xFF9B59B6).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.palette,
            color: Color(0xFF9B59B6),
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        const Text(
          'Background Themes',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2C3E50),
          ),
        ),
      ],
    );
  }

  Widget _buildPredefinedThemes() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Predefined Themes',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 12),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.2,
          ),
          itemCount: _predefinedThemes.length,
          itemBuilder: (context, index) {
            final theme = _predefinedThemes[index];
            final isSelected = _selectedTheme == theme.id;
            
            return GestureDetector(
              onTap: () => setState(() {
                _selectedTheme = theme.id;
                _customBackgroundPath = null;
              }),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected ? const Color(0xFF9B59B6) : Colors.grey.shade300,
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Column(
                  children: [
                    Expanded(
                      child: Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: theme.backgroundColor,
                          gradient: theme.backgroundGradient,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(11),
                            topRight: Radius.circular(11),
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.smartphone,
                            color: Colors.grey.shade600,
                            size: 32,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.all(8),
                      child: Column(
                        children: [
                          Text(
                            theme.name,
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF2C3E50),
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 2),
                          Text(
                            theme.description,
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.grey.shade600,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildCustomBackground() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Custom Background',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _pickBackgroundImage,
                icon: const Icon(Icons.upload_file),
                label: const Text('Upload Image'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF3498DB),
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _pickColorBackground,
                icon: const Icon(Icons.color_lens),
                label: const Text('Pick Color'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF27AE60),
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
        if (_customBackgroundPath != null) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green.shade200),
            ),
            child: Row(
              children: [
                const Icon(Icons.check_circle, color: Color(0xFF27AE60), size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Custom background selected: ${_customBackgroundPath!.split('/').last}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFF27AE60),
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => setState(() => _customBackgroundPath = null),
                  icon: const Icon(Icons.close, size: 16),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPreview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Preview',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(11),
            child: _buildPreviewContent(),
          ),
        ),
      ],
    );
  }

  Widget _buildPreviewContent() {
    if (_customBackgroundPath != null) {
      return Image.file(
        File(_customBackgroundPath!),
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => Container(
          color: Colors.grey.shade200,
          child: const Center(
            child: Icon(Icons.error, color: Colors.red),
          ),
        ),
      );
    }

    if (_selectedTheme != null) {
      final theme = _predefinedThemes.firstWhere((t) => t.id == _selectedTheme);
      return Container(
        decoration: BoxDecoration(
          color: theme.backgroundColor,
          gradient: theme.backgroundGradient,
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.smartphone,
                color: Colors.grey.shade600,
                size: 48,
              ),
              const SizedBox(height: 8),
              Text(
                'Preview: ${theme.name}',
                style: TextStyle(
                  color: Colors.grey.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      color: Colors.grey.shade100,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.preview,
              color: Colors.grey.shade400,
              size: 48,
            ),
            const SizedBox(height: 8),
            Text(
              'Select a theme to preview',
              style: TextStyle(
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActions(ExcelAppTool? currentTool) {
    return Row(
      children: [
        Expanded(
          child: TextButton(
            onPressed: () => setState(() {
              _selectedTheme = null;
              _customBackgroundPath = null;
            }),
            child: const Text('Reset'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: (_selectedTheme != null || _customBackgroundPath != null) && currentTool != null
                ? () => _applyTheme(currentTool)
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF9B59B6),
              foregroundColor: Colors.white,
            ),
            child: const Text('Apply Theme'),
          ),
        ),
      ],
    );
  }

  void _pickBackgroundImage() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.image,
      allowMultiple: false,
    );

    if (result != null && result.files.single.path != null) {
      setState(() {
        _customBackgroundPath = result.files.single.path;
        _selectedTheme = null;
      });
    }
  }

  void _pickColorBackground() {
    // This would open a color picker dialog
    // For now, we'll just show a simple dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Color Picker'),
        content: const Text('Color picker functionality will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _applyTheme(ExcelAppTool tool) {
    // Apply the selected theme to the tool
    Map<String, dynamic> themeData = {};
    
    if (_selectedTheme != null) {
      final theme = _predefinedThemes.firstWhere((t) => t.id == _selectedTheme);
      themeData = {
        'type': 'predefined',
        'themeId': theme.id,
        'name': theme.name,
      };
    } else if (_customBackgroundPath != null) {
      themeData = {
        'type': 'custom',
        'backgroundPath': _customBackgroundPath,
      };
    }

    // Update tool with theme data (store in description for now)
    final updatedTool = tool.copyWith(
      description: '${tool.description}\nTheme: ${themeData['name']}',
      lastModified: DateTime.now(),
    );

    ref.read(currentExcelAppToolProvider.notifier).setTool(updatedTool);
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Theme applied successfully'),
        backgroundColor: Color(0xFF27AE60),
      ),
    );
  }
}

class ThemeOption {
  final String id;
  final String name;
  final String description;
  final Color previewColor;
  final Color? backgroundColor;
  final Gradient? backgroundGradient;

  const ThemeOption({
    required this.id,
    required this.name,
    required this.description,
    required this.previewColor,
    this.backgroundColor,
    this.backgroundGradient,
  });
}
