import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_theme.dart';
import '../../services/islamic_providers.dart';
import '../../models/athkar.dart';

class CustomAthkarCreatorScreen extends ConsumerStatefulWidget {
  const CustomAthkarCreatorScreen({super.key});

  @override
  ConsumerState<CustomAthkarCreatorScreen> createState() => _CustomAthkarCreatorScreenState();
}

class _CustomAthkarCreatorScreenState extends ConsumerState<CustomAthkarCreatorScreen> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();

  List<CustomDhikrItem> _selectedDhikrItems = [];
  String _selectedColor = '#4CAF50';
  String _searchQuery = '';
  CustomAthkarRoutine? _editingRoutine;

  final List<String> _colorOptions = [
    '#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336',
    '#795548', '#607D8B', '#E91E63', '#3F51B5', '#009688',
  ];

  @override
  void initState() {
    super.initState();
    _editingRoutine = ref.read(selectedCustomAthkarProvider);

    if (_editingRoutine != null) {
      _nameController.text = _editingRoutine!.name;
      _descriptionController.text = _editingRoutine!.description;
      _selectedColor = _editingRoutine!.color;
      _selectedDhikrItems = List.from(_editingRoutine!.dhikrItems);
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final dhikrAsync = ref.watch(dhikrProvider);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(_editingRoutine != null ? 'Edit Custom Athkar' : 'Create Custom Athkar'),
        backgroundColor: AppTheme.islamicAppColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            _showExitDialog();
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add_circle),
            onPressed: () => _showCreateCustomDhikrDialog(),
          ),
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveRoutine,
          ),
        ],
      ),
      body: dhikrAsync.when(
        data: (allDhikr) => _buildCreatorContent(context, allDhikr),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(child: Text('Error: $error')),
      ),
    );
  }

  Widget _buildCreatorContent(BuildContext context, List<Dhikr> allDhikr) {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildBasicInfoSection(),
                const SizedBox(height: 24),
                _buildColorSelection(),
                const SizedBox(height: 24),
                _buildSelectedDhikrSection(),
                const SizedBox(height: 24),
                _buildDhikrSelectionSection(allDhikr),
              ],
            ),
          ),
        ),
        _buildBottomActions(),
      ],
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Basic Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.islamicAppColor,
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Routine Name *',
                border: OutlineInputBorder(),
                hintText: 'e.g., Morning Routine, Personal Dhikr',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                border: OutlineInputBorder(),
                hintText: 'Describe your custom athkar routine',
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorSelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Routine Color',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.islamicAppColor,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: _colorOptions.map((color) {
                final colorValue = Color(int.parse(color.replaceFirst('#', '0xFF')));
                final isSelected = _selectedColor == color;

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedColor = color;
                    });
                  },
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: colorValue,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isSelected ? Colors.black : Colors.transparent,
                        width: 3,
                      ),
                    ),
                    child: isSelected
                        ? const Icon(Icons.check, color: Colors.white, size: 20)
                        : null,
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedDhikrSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Selected Dhikr (${_selectedDhikrItems.length})',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.islamicAppColor,
                  ),
                ),
                if (_selectedDhikrItems.isNotEmpty)
                  Text(
                    'Total: ${_selectedDhikrItems.fold(0, (sum, item) => sum + item.count)}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            if (_selectedDhikrItems.isEmpty)
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Center(
                  child: Column(
                    children: [
                      Icon(
                        Icons.add_circle_outline,
                        size: 48,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'No dhikr selected',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Add dhikr from the selection below',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              ReorderableListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _selectedDhikrItems.length,
                onReorder: _reorderDhikrItems,
                itemBuilder: (context, index) {
                  final item = _selectedDhikrItems[index];
                  return _buildSelectedDhikrItem(context, item, index);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDhikrSelectionSection(List<Dhikr> allDhikr) {
    final filteredDhikr = _filterDhikr(allDhikr);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Add Dhikr',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.islamicAppColor,
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search dhikr...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: ListView.builder(
                itemCount: filteredDhikr.length,
                itemBuilder: (context, index) {
                  final dhikr = filteredDhikr[index];
                  final isAlreadySelected = _selectedDhikrItems.any((item) => item.dhikrId == dhikr.id);

                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      title: Text(
                        dhikr.textEnglish,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      subtitle: Text(
                        dhikr.textTransliteration,
                        style: const TextStyle(fontStyle: FontStyle.italic),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      trailing: isAlreadySelected
                          ? Icon(Icons.check_circle, color: AppTheme.islamicAppColor)
                          : IconButton(
                              icon: const Icon(Icons.add_circle_outline),
                              onPressed: () => _addDhikrItem(dhikr),
                            ),
                      onTap: isAlreadySelected ? null : () => _addDhikrItem(dhikr),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey, width: 0.2)),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => _showExitDialog(),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _saveRoutine,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.islamicAppColor,
                foregroundColor: Colors.white,
              ),
              child: Text(_editingRoutine != null ? 'Update Routine' : 'Create Routine'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedDhikrItem(BuildContext context, CustomDhikrItem item, int index) {
    final dhikrAsync = ref.watch(dhikrProvider);

    return dhikrAsync.when(
      data: (allDhikr) {
        final dhikr = allDhikr.firstWhere((d) => d.id == item.dhikrId);

        return Card(
          key: ValueKey(item.dhikrId),
          margin: const EdgeInsets.only(bottom: 8),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Icon(
                  Icons.drag_handle,
                  color: Colors.grey[400],
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        dhikr.textEnglish,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        dhikr.textTransliteration,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontStyle: FontStyle.italic,
                          color: Colors.grey[600],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                SizedBox(
                  width: 120,
                  child: Row(
                    children: [
                      IconButton(
                        onPressed: () => _decrementCount(index),
                        icon: const Icon(Icons.remove_circle_outline),
                        iconSize: 20,
                      ),
                      Expanded(
                        child: Text(
                          item.count.toString(),
                          textAlign: TextAlign.center,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: () => _incrementCount(index),
                        icon: const Icon(Icons.add_circle_outline),
                        iconSize: 20,
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => _removeDhikrItem(index),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red[400],
                ),
              ],
            ),
          ),
        );
      },
      loading: () => const SizedBox(height: 60, child: Center(child: CircularProgressIndicator())),
      error: (error, stack) => const SizedBox(height: 60, child: Center(child: Text('Error loading dhikr'))),
    );
  }

  List<Dhikr> _filterDhikr(List<Dhikr> allDhikr) {
    if (_searchQuery.isEmpty) return allDhikr;

    return allDhikr.where((dhikr) {
      final query = _searchQuery.toLowerCase();
      return dhikr.textEnglish.toLowerCase().contains(query) ||
             dhikr.textTransliteration.toLowerCase().contains(query) ||
             dhikr.textArabic.contains(_searchQuery) ||
             dhikr.meaning.toLowerCase().contains(query);
    }).toList();
  }

  void _reorderDhikrItems(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final item = _selectedDhikrItems.removeAt(oldIndex);
      _selectedDhikrItems.insert(newIndex, CustomDhikrItem(
        dhikrId: item.dhikrId,
        count: item.count,
        order: newIndex,
      ));

      // Update order for all items
      for (int i = 0; i < _selectedDhikrItems.length; i++) {
        _selectedDhikrItems[i] = CustomDhikrItem(
          dhikrId: _selectedDhikrItems[i].dhikrId,
          count: _selectedDhikrItems[i].count,
          order: i,
        );
      }
    });
  }

  void _addDhikrItem(Dhikr dhikr) {
    setState(() {
      _selectedDhikrItems.add(CustomDhikrItem(
        dhikrId: dhikr.id,
        count: dhikr.recommendedCount,
        order: _selectedDhikrItems.length,
      ));
    });
  }

  void _removeDhikrItem(int index) {
    setState(() {
      _selectedDhikrItems.removeAt(index);
      // Update order for remaining items
      for (int i = 0; i < _selectedDhikrItems.length; i++) {
        _selectedDhikrItems[i] = CustomDhikrItem(
          dhikrId: _selectedDhikrItems[i].dhikrId,
          count: _selectedDhikrItems[i].count,
          order: i,
        );
      }
    });
  }

  void _incrementCount(int index) {
    setState(() {
      final item = _selectedDhikrItems[index];
      _selectedDhikrItems[index] = CustomDhikrItem(
        dhikrId: item.dhikrId,
        count: item.count + 1,
        order: item.order,
      );
    });
  }

  void _decrementCount(int index) {
    setState(() {
      final item = _selectedDhikrItems[index];
      if (item.count > 1) {
        _selectedDhikrItems[index] = CustomDhikrItem(
          dhikrId: item.dhikrId,
          count: item.count - 1,
          order: item.order,
        );
      }
    });
  }

  void _saveRoutine() {
    if (_nameController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a routine name')),
      );
      return;
    }

    if (_selectedDhikrItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please add at least one dhikr')),
      );
      return;
    }

    final routine = CustomAthkarRoutine(
      id: _editingRoutine?.id,
      name: _nameController.text.trim(),
      description: _descriptionController.text.trim(),
      dhikrItems: _selectedDhikrItems,
      color: _selectedColor,
      createdAt: _editingRoutine?.createdAt,
    );

    if (_editingRoutine != null) {
      ref.read(customAthkarProvider.notifier).updateCustomAthkar(routine);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Custom athkar updated successfully')),
      );
    } else {
      ref.read(customAthkarProvider.notifier).addCustomAthkar(routine);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Custom athkar created successfully')),
      );
    }

    ref.read(selectedCustomAthkarProvider.notifier).state = null;
    ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.athkarCategories;
  }

  void _showExitDialog() {
    final hasChanges = _nameController.text.isNotEmpty ||
                      _descriptionController.text.isNotEmpty ||
                      _selectedDhikrItems.isNotEmpty;

    if (!hasChanges) {
      ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.athkarCategories;
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Discard Changes'),
        content: const Text('You have unsaved changes. Are you sure you want to exit?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Continue Editing'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(selectedCustomAthkarProvider.notifier).state = null;
              ref.read(islamicAppCurrentScreenProvider.notifier).state = IslamicAppScreen.athkarCategories;
            },
            child: const Text('Discard'),
          ),
        ],
      ),
    );
  }

  void _showCreateCustomDhikrDialog() {
    final arabicController = TextEditingController();
    final englishController = TextEditingController();
    final transliterationController = TextEditingController();
    final meaningController = TextEditingController();
    final sourceController = TextEditingController();
    final benefitsController = TextEditingController();
    int customCount = 1;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Create Custom Dhikr'),
          content: SizedBox(
            width: 500,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: arabicController,
                    decoration: const InputDecoration(
                      labelText: 'Arabic Text *',
                      border: OutlineInputBorder(),
                      hintText: 'Enter Arabic dhikr text',
                    ),
                    textDirection: TextDirection.rtl,
                    style: const TextStyle(fontFamily: 'Arabic'),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: englishController,
                    decoration: const InputDecoration(
                      labelText: 'English Meaning *',
                      border: OutlineInputBorder(),
                      hintText: 'Enter English meaning',
                    ),
                    maxLines: 2,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: transliterationController,
                    decoration: const InputDecoration(
                      labelText: 'Transliteration',
                      border: OutlineInputBorder(),
                      hintText: 'Enter transliteration',
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: meaningController,
                    decoration: const InputDecoration(
                      labelText: 'Detailed Meaning',
                      border: OutlineInputBorder(),
                      hintText: 'Enter detailed meaning/explanation',
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: sourceController,
                    decoration: const InputDecoration(
                      labelText: 'Source',
                      border: OutlineInputBorder(),
                      hintText: 'e.g., Personal, Quran, Hadith',
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: benefitsController,
                    decoration: const InputDecoration(
                      labelText: 'Benefits (comma separated)',
                      border: OutlineInputBorder(),
                      hintText: 'Enter benefits separated by commas',
                    ),
                    maxLines: 2,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      const Text('Repetition Count: '),
                      const SizedBox(width: 16),
                      IconButton(
                        onPressed: customCount > 1 ? () {
                          setDialogState(() {
                            customCount--;
                          });
                        } : null,
                        icon: const Icon(Icons.remove_circle_outline),
                      ),
                      Container(
                        width: 60,
                        alignment: Alignment.center,
                        child: Text(
                          customCount.toString(),
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: () {
                          setDialogState(() {
                            customCount++;
                          });
                        },
                        icon: const Icon(Icons.add_circle_outline),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                if (arabicController.text.trim().isEmpty || englishController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Arabic text and English meaning are required')),
                  );
                  return;
                }

                // Create a custom dhikr and add it to the routine
                final customDhikr = Dhikr(
                  textArabic: arabicController.text.trim(),
                  textEnglish: englishController.text.trim(),
                  textTransliteration: transliterationController.text.trim().isEmpty
                      ? englishController.text.trim()
                      : transliterationController.text.trim(),
                  meaning: meaningController.text.trim().isEmpty
                      ? englishController.text.trim()
                      : meaningController.text.trim(),
                  recommendedCount: customCount,
                  source: sourceController.text.trim().isEmpty ? 'Custom' : sourceController.text.trim(),
                  category: AthkarCategory.custom,
                  benefits: benefitsController.text.trim().isEmpty
                      ? ['Personal dhikr']
                      : benefitsController.text.split(',').map((e) => e.trim()).toList(),
                );

                // Add to database and then to current routine
                ref.read(dhikrProvider.notifier).addCustomDhikr(customDhikr).then((dhikrId) {
                  setState(() {
                    _selectedDhikrItems.add(CustomDhikrItem(
                      dhikrId: dhikrId,
                      count: customCount,
                      order: _selectedDhikrItems.length,
                    ));
                  });
                });

                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Custom dhikr created and added to routine')),
                );
              },
              child: const Text('Create & Add'),
            ),
          ],
        ),
      ),
    );
  }
}
