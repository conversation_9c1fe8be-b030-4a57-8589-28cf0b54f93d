import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class AthkarSettingsScreen extends ConsumerWidget {
  const AthkarSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSettingsSection(
            context,
            'Notifications',
            [
              _buildSwitchTile(
                context,
                'Morning Athkar Reminder',
                'Daily reminder for morning athkar',
                Icons.wb_sunny,
                true,
                (value) {},
              ),
              _buildSwitchTile(
                context,
                'Evening Athkar Reminder',
                'Daily reminder for evening athkar',
                Icons.nights_stay,
                true,
                (value) {},
              ),
              _buildSwitchTile(
                context,
                'Prayer Athkar Reminder',
                'Reminder after each prayer',
                Icons.mosque,
                false,
                (value) {},
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSettingsSection(
            context,
            'Audio Settings',
            [
              _buildSettingsTile(
                context,
                'Audio Language',
                'Arabic',
                Icons.language,
                () {},
              ),
              _buildSettingsTile(
                context,
                'Reciter',
                'Mishary Rashid',
                Icons.person,
                () {},
              ),
              _buildSwitchTile(
                context,
                'Auto-play Audio',
                'Automatically play audio when opening athkar',
                Icons.play_arrow,
                false,
                (value) {},
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSettingsSection(
            context,
            'Display Settings',
            [
              _buildSettingsTile(
                context,
                'Font Size',
                'Medium',
                Icons.text_fields,
                () {},
              ),
              _buildSwitchTile(
                context,
                'Show Arabic Text',
                'Display Arabic text for athkar',
                Icons.text_format,
                true,
                (value) {},
              ),
              _buildSwitchTile(
                context,
                'Show Transliteration',
                'Display transliteration for athkar',
                Icons.translate,
                true,
                (value) {},
              ),
              _buildSwitchTile(
                context,
                'Show Translation',
                'Display English translation',
                Icons.language,
                true,
                (value) {},
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSettingsSection(
            context,
            'Progress Tracking',
            [
              _buildSwitchTile(
                context,
                'Track Progress',
                'Keep track of completed athkar',
                Icons.track_changes,
                true,
                (value) {},
              ),
              _buildSettingsTile(
                context,
                'Reset Progress',
                'Clear all progress data',
                Icons.refresh,
                () => _showResetDialog(context),
              ),
              _buildSettingsTile(
                context,
                'Export Progress',
                'Export progress data',
                Icons.download,
                () {},
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(
    BuildContext context,
    String title,
    List<Widget> children,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.green,
          ),
        ),
        const SizedBox(height: 8),
        Card(
          elevation: 2,
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildSettingsTile(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: Colors.green),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  Widget _buildSwitchTile(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return SwitchListTile(
      secondary: Icon(icon, color: Colors.green),
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      activeColor: Colors.green,
    );
  }

  void _showResetDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Progress'),
        content: const Text(
          'Are you sure you want to reset all progress data? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Progress data reset successfully')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}
