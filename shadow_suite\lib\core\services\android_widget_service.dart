import 'dart:async';
import 'dart:convert';
import 'package:flutter/services.dart';

/// Android home screen widget service for Shadow Suite
/// Provides widget functionality for prayer times, quick file access, budget summary, and media controls
class AndroidWidgetService {
  static final AndroidWidgetService _instance = AndroidWidgetService._internal();
  factory AndroidWidgetService() => _instance;
  AndroidWidgetService._internal();

  static const MethodChannel _channel = MethodChannel('shadow_suite/android_widgets');
  
  final Map<String, WidgetConfiguration> _widgetConfigs = {};
  final Map<String, Map<String, dynamic>> _widgetData = {};
  bool _isInitialized = false;

  /// Initialize Android widget service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize widget configurations
      _initializeWidgetConfigs();
      
      // Set up method call handler for widget interactions
      _channel.setMethodCallHandler(_handleMethodCall);
      
      _isInitialized = true;
    } catch (e) {
      throw Exception('Failed to initialize Android widget service: $e');
    }
  }

  /// Initialize default widget configurations
  void _initializeWidgetConfigs() {
    // Prayer times widget
    _widgetConfigs['prayer_times'] = WidgetConfiguration(
      id: 'prayer_times',
      name: 'Prayer Times',
      description: 'Shows current and next prayer times',
      sizes: [WidgetSize.small, WidgetSize.medium, WidgetSize.large],
      updateInterval: const Duration(minutes: 1),
      requiresLocation: true,
      backgroundColor: 0xFF4CAF50,
      textColor: 0xFFFFFFFF,
    );

    // Quick file access widget
    _widgetConfigs['quick_files'] = WidgetConfiguration(
      id: 'quick_files',
      name: 'Quick Files',
      description: 'Quick access to recent and favorite files',
      sizes: [WidgetSize.medium, WidgetSize.large],
      updateInterval: const Duration(minutes: 5),
      requiresLocation: false,
      backgroundColor: 0xFF2196F3,
      textColor: 0xFFFFFFFF,
    );

    // Budget summary widget
    _widgetConfigs['budget_summary'] = WidgetConfiguration(
      id: 'budget_summary',
      name: 'Budget Summary',
      description: 'Shows current month budget and spending',
      sizes: [WidgetSize.small, WidgetSize.medium],
      updateInterval: const Duration(hours: 1),
      requiresLocation: false,
      backgroundColor: 0xFFFF9800,
      textColor: 0xFFFFFFFF,
    );

    // Media player controls widget
    _widgetConfigs['media_controls'] = WidgetConfiguration(
      id: 'media_controls',
      name: 'Media Controls',
      description: 'Control Shadow Player from home screen',
      sizes: [WidgetSize.medium, WidgetSize.large],
      updateInterval: const Duration(seconds: 30),
      requiresLocation: false,
      backgroundColor: 0xFF9C27B0,
      textColor: 0xFFFFFFFF,
    );

    // Quran verse widget
    _widgetConfigs['quran_verse'] = WidgetConfiguration(
      id: 'quran_verse',
      name: 'Daily Verse',
      description: 'Shows daily Quran verse with translation',
      sizes: [WidgetSize.medium, WidgetSize.large],
      updateInterval: const Duration(hours: 24),
      requiresLocation: false,
      backgroundColor: 0xFF009688,
      textColor: 0xFFFFFFFF,
    );

    // File Manager status widget
    _widgetConfigs['file_status'] = WidgetConfiguration(
      id: 'file_status',
      name: 'Storage Status',
      description: 'Shows storage usage and recent activity',
      sizes: [WidgetSize.small, WidgetSize.medium],
      updateInterval: const Duration(minutes: 15),
      requiresLocation: false,
      backgroundColor: 0xFF607D8B,
      textColor: 0xFFFFFFFF,
    );
  }

  /// Handle method calls from Android widgets
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'getWidgetData':
        return await _getWidgetData(call.arguments['widgetId']);
      
      case 'onWidgetTap':
        return await _handleWidgetTap(
          call.arguments['widgetId'],
          call.arguments['action'],
        );
      
      case 'updateWidgetConfig':
        return await _updateWidgetConfig(
          call.arguments['widgetId'],
          call.arguments['config'],
        );
      
      default:
        throw PlatformException(
          code: 'UNIMPLEMENTED',
          message: 'Method ${call.method} not implemented',
        );
    }
  }

  /// Get widget data for Android widget
  Future<Map<String, dynamic>> _getWidgetData(String widgetId) async {
    switch (widgetId) {
      case 'prayer_times':
        return await _getPrayerTimesData();
      
      case 'quick_files':
        return await _getQuickFilesData();
      
      case 'budget_summary':
        return await _getBudgetSummaryData();
      
      case 'media_controls':
        return await _getMediaControlsData();
      
      case 'quran_verse':
        return await _getQuranVerseData();
      
      case 'file_status':
        return await _getFileStatusData();
      
      default:
        return {'error': 'Unknown widget ID: $widgetId'};
    }
  }

  /// Get prayer times data for widget
  Future<Map<String, dynamic>> _getPrayerTimesData() async {
    // In a real implementation, this would get data from PrayerTimeService
    final now = DateTime.now();
    return {
      'current_time': now.toIso8601String(),
      'current_prayer': 'Dhuhr',
      'current_prayer_arabic': 'الظهر',
      'next_prayer': 'Asr',
      'next_prayer_arabic': 'العصر',
      'next_prayer_time': '15:30',
      'time_remaining': '2h 15m',
      'all_prayers': {
        'Fajr': '05:30',
        'Sunrise': '06:45',
        'Dhuhr': '13:15',
        'Asr': '15:30',
        'Maghrib': '18:45',
        'Isha': '20:00',
      },
    };
  }

  /// Get quick files data for widget
  Future<Map<String, dynamic>> _getQuickFilesData() async {
    return {
      'recent_files': [
        {'name': 'Budget_2024.xlsx', 'path': '/Documents/Budget_2024.xlsx', 'type': 'excel'},
        {'name': 'Quran_Notes.pdf', 'path': '/Documents/Quran_Notes.pdf', 'type': 'pdf'},
        {'name': 'Family_Photo.jpg', 'path': '/Pictures/Family_Photo.jpg', 'type': 'image'},
      ],
      'favorite_files': [
        {'name': 'Important_Docs', 'path': '/Documents/Important', 'type': 'folder'},
        {'name': 'Quran_Audio', 'path': '/Music/Quran', 'type': 'folder'},
      ],
      'storage_usage': {
        'used': '45.2 GB',
        'total': '128 GB',
        'percentage': 35.3,
      },
    };
  }

  /// Get budget summary data for widget
  Future<Map<String, dynamic>> _getBudgetSummaryData() async {
    return {
      'current_month': 'December 2024',
      'total_budget': 3000.00,
      'total_spent': 2150.75,
      'remaining': 849.25,
      'percentage_spent': 71.7,
      'top_categories': [
        {'name': 'Food', 'spent': 650.00, 'budget': 800.00},
        {'name': 'Transport', 'spent': 420.50, 'budget': 500.00},
        {'name': 'Entertainment', 'spent': 280.25, 'budget': 300.00},
      ],
      'status': 'on_track', // on_track, over_budget, warning
    };
  }

  /// Get media controls data for widget
  Future<Map<String, dynamic>> _getMediaControlsData() async {
    return {
      'is_playing': true,
      'current_track': {
        'title': 'Surah Al-Fatihah',
        'artist': 'Sheikh Abdul Rahman Al-Sudais',
        'duration': '2:45',
        'position': '1:23',
        'artwork_url': '/assets/quran_cover.jpg',
      },
      'playlist': {
        'name': 'Quran Recitation',
        'track_count': 114,
        'current_index': 1,
      },
      'controls': {
        'can_skip_previous': true,
        'can_skip_next': true,
        'can_seek': true,
        'shuffle_enabled': false,
        'repeat_mode': 'none', // none, one, all
      },
    };
  }

  /// Get Quran verse data for widget
  Future<Map<String, dynamic>> _getQuranVerseData() async {
    return {
      'verse': {
        'surah_number': 2,
        'verse_number': 255,
        'surah_name': 'Al-Baqarah',
        'surah_name_arabic': 'البقرة',
        'arabic_text': 'ٱللَّهُ لَآ إِلَٰهَ إِلَّا هُوَ ٱلْحَىُّ ٱلْقَيُّومُ',
        'translation': 'Allah - there is no deity except Him, the Ever-Living, the Sustainer of existence.',
        'transliteration': 'Allahu la ilaha illa huwa al-hayyu al-qayyum',
      },
      'reference': 'Quran 2:255',
      'date': DateTime.now().toIso8601String(),
    };
  }

  /// Get file status data for widget
  Future<Map<String, dynamic>> _getFileStatusData() async {
    return {
      'storage': {
        'used_gb': 45.2,
        'total_gb': 128.0,
        'percentage': 35.3,
        'available_gb': 82.8,
      },
      'recent_activity': [
        {'action': 'Downloaded', 'file': 'document.pdf', 'time': '2 min ago'},
        {'action': 'Uploaded', 'file': 'photo.jpg', 'time': '15 min ago'},
        {'action': 'Deleted', 'file': 'old_file.txt', 'time': '1 hour ago'},
      ],
      'sync_status': 'synced', // syncing, synced, error
      'last_backup': '2 hours ago',
    };
  }

  /// Handle widget tap actions
  Future<void> _handleWidgetTap(String widgetId, String action) async {
    try {
      await _channel.invokeMethod('handleWidgetAction', {
        'widgetId': widgetId,
        'action': action,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      throw Exception('Failed to handle widget tap: $e');
    }
  }

  /// Update widget configuration
  Future<void> _updateWidgetConfig(String widgetId, Map<String, dynamic> config) async {
    final currentConfig = _widgetConfigs[widgetId];
    if (currentConfig != null) {
      _widgetConfigs[widgetId] = currentConfig.copyWith(
        backgroundColor: config['backgroundColor'],
        textColor: config['textColor'],
        updateInterval: config['updateInterval'] != null 
            ? Duration(seconds: config['updateInterval'])
            : null,
      );
    }
  }

  /// Update widget data
  Future<void> updateWidgetData(String widgetId, Map<String, dynamic> data) async {
    _widgetData[widgetId] = data;
    
    try {
      await _channel.invokeMethod('updateWidget', {
        'widgetId': widgetId,
        'data': jsonEncode(data),
      });
    } catch (e) {
      throw Exception('Failed to update widget data: $e');
    }
  }

  /// Update all widgets
  Future<void> updateAllWidgets() async {
    for (final widgetId in _widgetConfigs.keys) {
      final data = await _getWidgetData(widgetId);
      await updateWidgetData(widgetId, data);
    }
  }

  /// Get available widget configurations
  List<WidgetConfiguration> getAvailableWidgets() {
    return _widgetConfigs.values.toList();
  }

  /// Get widget configuration by ID
  WidgetConfiguration? getWidgetConfig(String widgetId) {
    return _widgetConfigs[widgetId];
  }

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;
}

/// Widget configuration model
class WidgetConfiguration {
  final String id;
  final String name;
  final String description;
  final List<WidgetSize> sizes;
  final Duration updateInterval;
  final bool requiresLocation;
  final int backgroundColor;
  final int textColor;

  const WidgetConfiguration({
    required this.id,
    required this.name,
    required this.description,
    required this.sizes,
    required this.updateInterval,
    required this.requiresLocation,
    required this.backgroundColor,
    required this.textColor,
  });

  WidgetConfiguration copyWith({
    String? id,
    String? name,
    String? description,
    List<WidgetSize>? sizes,
    Duration? updateInterval,
    bool? requiresLocation,
    int? backgroundColor,
    int? textColor,
  }) => WidgetConfiguration(
    id: id ?? this.id,
    name: name ?? this.name,
    description: description ?? this.description,
    sizes: sizes ?? this.sizes,
    updateInterval: updateInterval ?? this.updateInterval,
    requiresLocation: requiresLocation ?? this.requiresLocation,
    backgroundColor: backgroundColor ?? this.backgroundColor,
    textColor: textColor ?? this.textColor,
  );
}

/// Widget size options
enum WidgetSize {
  small,   // 2x1 cells
  medium,  // 4x2 cells
  large,   // 4x4 cells
}
