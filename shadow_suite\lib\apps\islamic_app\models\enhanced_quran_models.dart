// import 'package:flutter/foundation.dart'; // Reserved for future debugging

// Enhanced Quran Translation Model
class QuranTranslation {
  final String id;
  final String name;
  final String language;
  final String translator;
  final String description;
  final bool isDefault;
  final Map<int, Map<int, String>> verses; // surah -> verse -> translation
  final DateTime lastUpdated;
  final Map<String, dynamic> metadata;

  const QuranTranslation({
    required this.id,
    required this.name,
    required this.language,
    required this.translator,
    required this.description,
    required this.isDefault,
    required this.verses,
    required this.lastUpdated,
    required this.metadata,
  });

  factory QuranTranslation.fromJson(Map<String, dynamic> json) {
    return QuranTranslation(
      id: json['id'] as String,
      name: json['name'] as String,
      language: json['language'] as String,
      translator: json['translator'] as String,
      description: json['description'] as String? ?? '',
      isDefault: json['is_default'] as bool? ?? false,
      verses: _parseVersesFromJson(json['verses'] as Map<String, dynamic>? ?? {}),
      lastUpdated: DateTime.parse(json['last_updated'] as String),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'language': language,
      'translator': translator,
      'description': description,
      'is_default': isDefault,
      'verses': _versesToJson(),
      'last_updated': lastUpdated.toIso8601String(),
      'metadata': metadata,
    };
  }

  static Map<int, Map<int, String>> _parseVersesFromJson(Map<String, dynamic> json) {
    final verses = <int, Map<int, String>>{};
    for (final entry in json.entries) {
      final surahNumber = int.parse(entry.key);
      final surahVerses = <int, String>{};
      for (final verseEntry in (entry.value as Map<String, dynamic>).entries) {
        final verseNumber = int.parse(verseEntry.key);
        surahVerses[verseNumber] = verseEntry.value as String;
      }
      verses[surahNumber] = surahVerses;
    }
    return verses;
  }

  Map<String, dynamic> _versesToJson() {
    final json = <String, dynamic>{};
    for (final entry in verses.entries) {
      final surahJson = <String, dynamic>{};
      for (final verseEntry in entry.value.entries) {
        surahJson[verseEntry.key.toString()] = verseEntry.value;
      }
      json[entry.key.toString()] = surahJson;
    }
    return json;
  }

  String? getVerse(int surahNumber, int verseNumber) {
    return verses[surahNumber]?[verseNumber];
  }
}

// Enhanced Tafseer Model
class QuranTafseer {
  final String id;
  final String name;
  final String author;
  final String language;
  final TafseerType type;
  final String description;
  final Map<int, Map<int, String>> commentary; // surah -> verse -> tafseer
  final DateTime lastUpdated;
  final Map<String, dynamic> metadata;

  const QuranTafseer({
    required this.id,
    required this.name,
    required this.author,
    required this.language,
    required this.type,
    required this.description,
    required this.commentary,
    required this.lastUpdated,
    required this.metadata,
  });

  factory QuranTafseer.fromJson(Map<String, dynamic> json) {
    return QuranTafseer(
      id: json['id'] as String,
      name: json['name'] as String,
      author: json['author'] as String,
      language: json['language'] as String,
      type: TafseerType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => TafseerType.classical,
      ),
      description: json['description'] as String? ?? '',
      commentary: QuranTranslation._parseVersesFromJson(
        json['commentary'] as Map<String, dynamic>? ?? {}),
      lastUpdated: DateTime.parse(json['last_updated'] as String),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'author': author,
      'language': language,
      'type': type.name,
      'description': description,
      'commentary': _commentaryToJson(),
      'last_updated': lastUpdated.toIso8601String(),
      'metadata': metadata,
    };
  }

  Map<String, dynamic> _commentaryToJson() {
    final json = <String, dynamic>{};
    for (final entry in commentary.entries) {
      final surahJson = <String, dynamic>{};
      for (final verseEntry in entry.value.entries) {
        surahJson[verseEntry.key.toString()] = verseEntry.value;
      }
      json[entry.key.toString()] = surahJson;
    }
    return json;
  }

  String? getTafseer(int surahNumber, int verseNumber) {
    return commentary[surahNumber]?[verseNumber];
  }
}

// Prayer Customization Model
class PrayerCustomization {
  final String id;
  final String userId;
  final PrayerCalculationMethod calculationMethod;
  final AsrCalculationMethod asrMethod;
  final HighLatitudeRule highLatitudeRule;
  final Map<String, int> timeAdjustments; // prayer name -> minutes adjustment
  final bool enableNotifications;
  final Map<String, bool> notificationSettings; // prayer name -> enabled
  final Map<String, String> customAdhan; // prayer name -> adhan file path
  final bool enableQiblaDirection;
  final double qiblaAdjustment;
  final Map<String, dynamic> customSettings;
  final DateTime lastModified;

  const PrayerCustomization({
    required this.id,
    required this.userId,
    required this.calculationMethod,
    required this.asrMethod,
    required this.highLatitudeRule,
    required this.timeAdjustments,
    required this.enableNotifications,
    required this.notificationSettings,
    required this.customAdhan,
    required this.enableQiblaDirection,
    required this.qiblaAdjustment,
    required this.customSettings,
    required this.lastModified,
  });

  factory PrayerCustomization.fromJson(Map<String, dynamic> json) {
    return PrayerCustomization(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      calculationMethod: PrayerCalculationMethod.values.firstWhere(
        (e) => e.name == json['calculation_method'],
        orElse: () => PrayerCalculationMethod.muslimWorldLeague,
      ),
      asrMethod: AsrCalculationMethod.values.firstWhere(
        (e) => e.name == json['asr_method'],
        orElse: () => AsrCalculationMethod.shafi,
      ),
      highLatitudeRule: HighLatitudeRule.values.firstWhere(
        (e) => e.name == json['high_latitude_rule'],
        orElse: () => HighLatitudeRule.middleOfTheNight,
      ),
      timeAdjustments: Map<String, int>.from(json['time_adjustments'] as Map? ?? {}),
      enableNotifications: json['enable_notifications'] as bool? ?? true,
      notificationSettings: Map<String, bool>.from(json['notification_settings'] as Map? ?? {}),
      customAdhan: Map<String, String>.from(json['custom_adhan'] as Map? ?? {}),
      enableQiblaDirection: json['enable_qibla_direction'] as bool? ?? true,
      qiblaAdjustment: (json['qibla_adjustment'] as num?)?.toDouble() ?? 0.0,
      customSettings: Map<String, dynamic>.from(json['custom_settings'] as Map? ?? {}),
      lastModified: DateTime.parse(json['last_modified'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'calculation_method': calculationMethod.name,
      'asr_method': asrMethod.name,
      'high_latitude_rule': highLatitudeRule.name,
      'time_adjustments': timeAdjustments,
      'enable_notifications': enableNotifications,
      'notification_settings': notificationSettings,
      'custom_adhan': customAdhan,
      'enable_qibla_direction': enableQiblaDirection,
      'qibla_adjustment': qiblaAdjustment,
      'custom_settings': customSettings,
      'last_modified': lastModified.toIso8601String(),
    };
  }

  PrayerCustomization copyWith({
    PrayerCalculationMethod? calculationMethod,
    AsrCalculationMethod? asrMethod,
    HighLatitudeRule? highLatitudeRule,
    Map<String, int>? timeAdjustments,
    bool? enableNotifications,
    Map<String, bool>? notificationSettings,
    Map<String, String>? customAdhan,
    bool? enableQiblaDirection,
    double? qiblaAdjustment,
    Map<String, dynamic>? customSettings,
  }) {
    return PrayerCustomization(
      id: id,
      userId: userId,
      calculationMethod: calculationMethod ?? this.calculationMethod,
      asrMethod: asrMethod ?? this.asrMethod,
      highLatitudeRule: highLatitudeRule ?? this.highLatitudeRule,
      timeAdjustments: timeAdjustments ?? this.timeAdjustments,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      notificationSettings: notificationSettings ?? this.notificationSettings,
      customAdhan: customAdhan ?? this.customAdhan,
      enableQiblaDirection: enableQiblaDirection ?? this.enableQiblaDirection,
      qiblaAdjustment: qiblaAdjustment ?? this.qiblaAdjustment,
      customSettings: customSettings ?? this.customSettings,
      lastModified: DateTime.now(),
    );
  }
}

// Islamic Calendar Event Model
class IslamicCalendarEvent {
  final String id;
  final String name;
  final String description;
  final IslamicEventType type;
  final DateTime gregorianDate;
  final IslamicDate islamicDate;
  final bool isRecurring;
  final EventSignificance significance;
  final List<String> traditions;
  final List<String> supplications;
  final Map<String, dynamic> metadata;

  const IslamicCalendarEvent({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.gregorianDate,
    required this.islamicDate,
    required this.isRecurring,
    required this.significance,
    required this.traditions,
    required this.supplications,
    required this.metadata,
  });

  factory IslamicCalendarEvent.fromJson(Map<String, dynamic> json) {
    return IslamicCalendarEvent(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: IslamicEventType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => IslamicEventType.religious,
      ),
      gregorianDate: DateTime.parse(json['gregorian_date'] as String),
      islamicDate: IslamicDate.fromJson(json['islamic_date'] as Map<String, dynamic>),
      isRecurring: json['is_recurring'] as bool? ?? false,
      significance: EventSignificance.values.firstWhere(
        (e) => e.name == json['significance'],
        orElse: () => EventSignificance.medium,
      ),
      traditions: List<String>.from(json['traditions'] as List? ?? []),
      supplications: List<String>.from(json['supplications'] as List? ?? []),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'gregorian_date': gregorianDate.toIso8601String(),
      'islamic_date': islamicDate.toJson(),
      'is_recurring': isRecurring,
      'significance': significance.name,
      'traditions': traditions,
      'supplications': supplications,
      'metadata': metadata,
    };
  }
}

// Islamic Date Model
class IslamicDate {
  final int day;
  final int month;
  final int year;
  final String monthName;
  final String dayName;

  const IslamicDate({
    required this.day,
    required this.month,
    required this.year,
    required this.monthName,
    required this.dayName,
  });

  factory IslamicDate.fromJson(Map<String, dynamic> json) {
    return IslamicDate(
      day: json['day'] as int,
      month: json['month'] as int,
      year: json['year'] as int,
      monthName: json['month_name'] as String,
      dayName: json['day_name'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'day': day,
      'month': month,
      'year': year,
      'month_name': monthName,
      'day_name': dayName,
    };
  }

  @override
  String toString() {
    return '$day $monthName $year AH';
  }
}

// Hajj/Umrah Guide Model
class HajjUmrahGuide {
  final String id;
  final String title;
  final PilgrimageType type;
  final List<PilgrimageStep> steps;
  final List<String> supplications;
  final List<String> tips;
  final Map<String, String> translations;
  final DateTime lastUpdated;

  const HajjUmrahGuide({
    required this.id,
    required this.title,
    required this.type,
    required this.steps,
    required this.supplications,
    required this.tips,
    required this.translations,
    required this.lastUpdated,
  });

  factory HajjUmrahGuide.fromJson(Map<String, dynamic> json) {
    return HajjUmrahGuide(
      id: json['id'] as String,
      title: json['title'] as String,
      type: PilgrimageType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => PilgrimageType.umrah,
      ),
      steps: (json['steps'] as List<dynamic>?)
          ?.map((e) => PilgrimageStep.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      supplications: List<String>.from(json['supplications'] as List? ?? []),
      tips: List<String>.from(json['tips'] as List? ?? []),
      translations: Map<String, String>.from(json['translations'] as Map? ?? {}),
      lastUpdated: DateTime.parse(json['last_updated'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'type': type.name,
      'steps': steps.map((e) => e.toJson()).toList(),
      'supplications': supplications,
      'tips': tips,
      'translations': translations,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }
}

// Pilgrimage Step Model
class PilgrimageStep {
  final String id;
  final String title;
  final String description;
  final int order;
  final bool isRequired;
  final List<String> supplications;
  final String? audioPath;
  final Map<String, dynamic> metadata;

  const PilgrimageStep({
    required this.id,
    required this.title,
    required this.description,
    required this.order,
    required this.isRequired,
    required this.supplications,
    this.audioPath,
    required this.metadata,
  });

  factory PilgrimageStep.fromJson(Map<String, dynamic> json) {
    return PilgrimageStep(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      order: json['order'] as int,
      isRequired: json['is_required'] as bool? ?? true,
      supplications: List<String>.from(json['supplications'] as List? ?? []),
      audioPath: json['audio_path'] as String?,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'order': order,
      'is_required': isRequired,
      'supplications': supplications,
      'audio_path': audioPath,
      'metadata': metadata,
    };
  }
}

// Enums
enum TafseerType {
  classical,
  contemporary,
  linguistic,
  thematic,
  scholarly,
}

enum PrayerCalculationMethod {
  muslimWorldLeague,
  egyptianGeneralAuthorityOfSurvey,
  universityOfIslamicSciencesKarachi,
  ummAlQuraUniversityMakkah,
  instituteOfGeophysicsUniversityOfTehran,
  shiaIthnaAshariLevaInstituteQum,
  gulfRegion,
  kuwait,
  qatar,
  majlisUgamaIslamSingapura,
  unionOfIslamicOrganisationsOfFrance,
  diyanetIsleriBaskanligiTurkey,
  spiritualAdministrationOfMuslimsOfRussia,
}

enum AsrCalculationMethod {
  shafi, // Standard (shadow length = object length + shadow at noon)
  hanafi, // Shadow length = 2 * object length + shadow at noon
}

enum HighLatitudeRule {
  middleOfTheNight,
  seventhOfTheNight,
  twilightAngle,
}

enum IslamicEventType {
  religious,
  historical,
  cultural,
  personal,
}

enum EventSignificance {
  low,
  medium,
  high,
  critical,
}

enum PilgrimageType {
  hajj,
  umrah,
  both,
}
