import 'package:flutter/material.dart';
import '../services/file_viewer_service.dart';
import 'viewers/text_file_viewer.dart';
import 'viewers/image_file_viewer.dart';

class UniversalFileViewer extends StatelessWidget {
  final String filePath;
  final VoidCallback? onClose;

  const UniversalFileViewer({
    super.key,
    required this.filePath,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    final fileName = filePath.split('/').last;
    final viewerType = FileViewerService.getViewerType(fileName);

    switch (viewerType) {
      case FileViewerType.text:
        return TextFileViewer(
          filePath: filePath,
          isEditable: FileViewerService.isEditable(fileName),
          onClose: onClose,
        );
        
      case FileViewerType.image:
        return ImageFileViewer(
          filePath: filePath,
          onClose: onClose,
        );
        
      case FileViewerType.audio:
        return AudioFileViewer(
          filePath: filePath,
          onClose: onClose,
        );
        
      case FileViewerType.video:
        return VideoFileViewer(
          filePath: filePath,
          onClose: onClose,
        );
        
      case FileViewerType.pdf:
        return PDFFileViewer(
          filePath: filePath,
          onClose: onClose,
        );
        
      case FileViewerType.archive:
        return ArchiveFileViewer(
          filePath: filePath,
          onClose: onClose,
        );
        
      case FileViewerType.document:
        return DocumentFileViewer(
          filePath: filePath,
          onClose: onClose,
        );
        
      case FileViewerType.unsupported:
        return UnsupportedFileViewer(
          filePath: filePath,
          onClose: onClose,
        );
    }
  }
}

// Placeholder viewers for other file types
class AudioFileViewer extends StatelessWidget {
  final String filePath;
  final VoidCallback? onClose;

  const AudioFileViewer({
    super.key,
    required this.filePath,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    final fileName = filePath.split('/').last;
    
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Icon(
              Icons.audio_file,
              color: FileViewerService.getFileColor(fileName),
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(fileName)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              onClose?.call();
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.audio_file, size: 64, color: Color(0xFF17A2B8)),
            SizedBox(height: 16),
            Text('Audio Player'),
            SizedBox(height: 8),
            Text('Audio playback functionality coming soon'),
          ],
        ),
      ),
    );
  }
}

class VideoFileViewer extends StatelessWidget {
  final String filePath;
  final VoidCallback? onClose;

  const VideoFileViewer({
    super.key,
    required this.filePath,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    final fileName = filePath.split('/').last;
    
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Icon(
              Icons.video_file,
              color: FileViewerService.getFileColor(fileName),
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(fileName)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              onClose?.call();
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.video_file, size: 64, color: Color(0xFFDC3545)),
            SizedBox(height: 16),
            Text('Video Player'),
            SizedBox(height: 8),
            Text('Video playback functionality coming soon'),
          ],
        ),
      ),
    );
  }
}

class PDFFileViewer extends StatelessWidget {
  final String filePath;
  final VoidCallback? onClose;

  const PDFFileViewer({
    super.key,
    required this.filePath,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    final fileName = filePath.split('/').last;
    
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Icon(
              Icons.picture_as_pdf,
              color: FileViewerService.getFileColor(fileName),
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(fileName)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              onClose?.call();
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.picture_as_pdf, size: 64, color: Color(0xFFDC3545)),
            SizedBox(height: 16),
            Text('PDF Viewer'),
            SizedBox(height: 8),
            Text('PDF viewing functionality coming soon'),
          ],
        ),
      ),
    );
  }
}

class ArchiveFileViewer extends StatelessWidget {
  final String filePath;
  final VoidCallback? onClose;

  const ArchiveFileViewer({
    super.key,
    required this.filePath,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    final fileName = filePath.split('/').last;
    
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Icon(
              Icons.archive,
              color: FileViewerService.getFileColor(fileName),
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(fileName)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              onClose?.call();
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.archive, size: 64, color: Color(0xFF6F42C1)),
            SizedBox(height: 16),
            Text('Archive Viewer'),
            SizedBox(height: 8),
            Text('Archive extraction functionality coming soon'),
          ],
        ),
      ),
    );
  }
}

class DocumentFileViewer extends StatelessWidget {
  final String filePath;
  final VoidCallback? onClose;

  const DocumentFileViewer({
    super.key,
    required this.filePath,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    final fileName = filePath.split('/').last;
    
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Icon(
              FileViewerService.getFileIcon(fileName),
              color: FileViewerService.getFileColor(fileName),
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(fileName)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              onClose?.call();
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.description, size: 64, color: Color(0xFF007BFF)),
            SizedBox(height: 16),
            Text('Document Viewer'),
            SizedBox(height: 8),
            Text('Document preview functionality coming soon'),
          ],
        ),
      ),
    );
  }
}

class UnsupportedFileViewer extends StatelessWidget {
  final String filePath;
  final VoidCallback? onClose;

  const UnsupportedFileViewer({
    super.key,
    required this.filePath,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    final fileName = filePath.split('/').last;
    
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            const Icon(Icons.insert_drive_file, color: Color(0xFF6C757D)),
            const SizedBox(width: 8),
            Expanded(child: Text(fileName)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              onClose?.call();
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.help_outline, size: 64, color: Color(0xFF6C757D)),
            SizedBox(height: 16),
            Text('Unsupported File Type'),
            SizedBox(height: 8),
            Text('This file type is not supported for preview'),
          ],
        ),
      ),
    );
  }
}
