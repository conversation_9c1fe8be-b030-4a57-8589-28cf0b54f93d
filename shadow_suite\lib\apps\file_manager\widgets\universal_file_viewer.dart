import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:just_audio/just_audio.dart';
import '../services/file_viewer_service.dart';
import 'viewers/text_file_viewer.dart';
import 'viewers/image_file_viewer.dart';

class UniversalFileViewer extends StatelessWidget {
  final String filePath;
  final VoidCallback? onClose;

  const UniversalFileViewer({super.key, required this.filePath, this.onClose});

  @override
  Widget build(BuildContext context) {
    final fileName = filePath.split('/').last;
    final viewerType = FileViewerService.getViewerType(fileName);

    switch (viewerType) {
      case FileViewerType.text:
        return TextFileViewer(
          filePath: filePath,
          isEditable: FileViewerService.isEditable(fileName),
          onClose: onClose,
        );

      case FileViewerType.image:
        return ImageFileViewer(filePath: filePath, onClose: onClose);

      case FileViewerType.audio:
        return AudioFileViewer(filePath: filePath, onClose: onClose);

      case FileViewerType.video:
        return VideoFileViewer(filePath: filePath, onClose: onClose);

      case FileViewerType.pdf:
        return PDFFileViewer(filePath: filePath, onClose: onClose);

      case FileViewerType.archive:
        return ArchiveFileViewer(filePath: filePath, onClose: onClose);

      case FileViewerType.document:
        return DocumentFileViewer(filePath: filePath, onClose: onClose);

      case FileViewerType.unsupported:
        return UnsupportedFileViewer(filePath: filePath, onClose: onClose);
    }
  }
}

// Placeholder viewers for other file types
class AudioFileViewer extends StatelessWidget {
  final String filePath;
  final VoidCallback? onClose;

  const AudioFileViewer({super.key, required this.filePath, this.onClose});

  @override
  Widget build(BuildContext context) {
    final fileName = filePath.split('/').last;

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Icon(
              Icons.audio_file,
              color: FileViewerService.getFileColor(fileName),
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(fileName)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              onClose?.call();
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: _AudioPlayerWidget(filePath: filePath),
    );
  }
}

class VideoFileViewer extends StatelessWidget {
  final String filePath;
  final VoidCallback? onClose;

  const VideoFileViewer({super.key, required this.filePath, this.onClose});

  @override
  Widget build(BuildContext context) {
    final fileName = filePath.split('/').last;

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Icon(
              Icons.video_file,
              color: FileViewerService.getFileColor(fileName),
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(fileName)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              onClose?.call();
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.video_file, size: 64, color: Color(0xFFDC3545)),
            SizedBox(height: 16),
            Text('Video Player'),
            SizedBox(height: 8),
            Text('Video playback functionality coming soon'),
          ],
        ),
      ),
    );
  }
}

class PDFFileViewer extends StatelessWidget {
  final String filePath;
  final VoidCallback? onClose;

  const PDFFileViewer({super.key, required this.filePath, this.onClose});

  @override
  Widget build(BuildContext context) {
    final fileName = filePath.split('/').last;

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Icon(
              Icons.picture_as_pdf,
              color: FileViewerService.getFileColor(fileName),
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(fileName)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              onClose?.call();
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.picture_as_pdf, size: 64, color: Color(0xFFDC3545)),
            SizedBox(height: 16),
            Text('PDF Viewer'),
            SizedBox(height: 8),
            Text('PDF viewing functionality coming soon'),
          ],
        ),
      ),
    );
  }
}

class ArchiveFileViewer extends StatelessWidget {
  final String filePath;
  final VoidCallback? onClose;

  const ArchiveFileViewer({super.key, required this.filePath, this.onClose});

  @override
  Widget build(BuildContext context) {
    final fileName = filePath.split('/').last;

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Icon(
              Icons.archive,
              color: FileViewerService.getFileColor(fileName),
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(fileName)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              onClose?.call();
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.archive, size: 64, color: Color(0xFF6F42C1)),
            SizedBox(height: 16),
            Text('Archive Viewer'),
            SizedBox(height: 8),
            Text('Archive extraction functionality coming soon'),
          ],
        ),
      ),
    );
  }
}

class DocumentFileViewer extends StatelessWidget {
  final String filePath;
  final VoidCallback? onClose;

  const DocumentFileViewer({super.key, required this.filePath, this.onClose});

  @override
  Widget build(BuildContext context) {
    final fileName = filePath.split('/').last;

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Icon(
              FileViewerService.getFileIcon(fileName),
              color: FileViewerService.getFileColor(fileName),
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(fileName)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              onClose?.call();
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: _DocumentViewerWidget(filePath: filePath),
    );
  }
}

class UnsupportedFileViewer extends StatelessWidget {
  final String filePath;
  final VoidCallback? onClose;

  const UnsupportedFileViewer({
    super.key,
    required this.filePath,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    final fileName = filePath.split('/').last;

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            const Icon(Icons.insert_drive_file, color: Color(0xFF6C757D)),
            const SizedBox(width: 8),
            Expanded(child: Text(fileName)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              onClose?.call();
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.help_outline, size: 64, color: Color(0xFF6C757D)),
            SizedBox(height: 16),
            Text('Unsupported File Type'),
            SizedBox(height: 8),
            Text('This file type is not supported for preview'),
          ],
        ),
      ),
    );
  }
}

/// Audio player widget for playing audio files
class _AudioPlayerWidget extends StatefulWidget {
  final String filePath;

  const _AudioPlayerWidget({required this.filePath});

  @override
  State<_AudioPlayerWidget> createState() => _AudioPlayerWidgetState();
}

class _AudioPlayerWidgetState extends State<_AudioPlayerWidget> {
  late AudioPlayer _audioPlayer;
  bool _isPlaying = false;
  bool _isLoading = true;
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  String? _error;

  @override
  void initState() {
    super.initState();
    _audioPlayer = AudioPlayer();
    _initializePlayer();
  }

  Future<void> _initializePlayer() async {
    try {
      await _audioPlayer.setFilePath(widget.filePath);

      _audioPlayer.durationStream.listen((duration) {
        if (mounted) {
          setState(() {
            _duration = duration ?? Duration.zero;
            _isLoading = false;
          });
        }
      });

      _audioPlayer.positionStream.listen((position) {
        if (mounted) {
          setState(() {
            _position = position;
          });
        }
      });

      _audioPlayer.playerStateStream.listen((state) {
        if (mounted) {
          setState(() {
            _isPlaying = state.playing;
          });
        }
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Error loading audio file: $e';
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading audio file...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(_error!, textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            ),
          ],
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Audio file icon
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: const Color(0xFF17A2B8).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(60),
            ),
            child: const Icon(
              Icons.audio_file,
              size: 60,
              color: Color(0xFF17A2B8),
            ),
          ),
          const SizedBox(height: 32),

          // File name
          Text(
            widget.filePath.split('/').last,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),

          // Progress slider
          Slider(
            value: _duration.inMilliseconds > 0
                ? _position.inMilliseconds / _duration.inMilliseconds
                : 0.0,
            onChanged: (value) {
              final newPosition = Duration(
                milliseconds: (value * _duration.inMilliseconds).round(),
              );
              _audioPlayer.seek(newPosition);
            },
          ),

          // Time display
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(_formatDuration(_position)),
                Text(_formatDuration(_duration)),
              ],
            ),
          ),
          const SizedBox(height: 32),

          // Control buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                onPressed: () {
                  final newPosition = _position - const Duration(seconds: 10);
                  _audioPlayer.seek(
                    newPosition < Duration.zero ? Duration.zero : newPosition,
                  );
                },
                icon: const Icon(Icons.replay_10),
                iconSize: 32,
              ),
              const SizedBox(width: 16),
              Container(
                decoration: BoxDecoration(
                  color: const Color(0xFF17A2B8),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: IconButton(
                  onPressed: () {
                    if (_isPlaying) {
                      _audioPlayer.pause();
                    } else {
                      _audioPlayer.play();
                    }
                  },
                  icon: Icon(
                    _isPlaying ? Icons.pause : Icons.play_arrow,
                    color: Colors.white,
                  ),
                  iconSize: 32,
                ),
              ),
              const SizedBox(width: 16),
              IconButton(
                onPressed: () {
                  final newPosition = _position + const Duration(seconds: 10);
                  _audioPlayer.seek(
                    newPosition > _duration ? _duration : newPosition,
                  );
                },
                icon: const Icon(Icons.forward_10),
                iconSize: 32,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Document viewer widget for displaying document files
class _DocumentViewerWidget extends StatefulWidget {
  final String filePath;

  const _DocumentViewerWidget({required this.filePath});

  @override
  State<_DocumentViewerWidget> createState() => _DocumentViewerWidgetState();
}

class _DocumentViewerWidgetState extends State<_DocumentViewerWidget> {
  String? _content;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadDocument();
  }

  Future<void> _loadDocument() async {
    try {
      final file = File(widget.filePath);
      if (await file.exists()) {
        final extension = widget.filePath.split('.').last.toLowerCase();

        if (['txt', 'md', 'json', 'xml', 'csv', 'log'].contains(extension)) {
          // For text-based files, read content directly
          final content = await file.readAsString();
          setState(() {
            _content = content;
            _isLoading = false;
          });
        } else {
          // For other document types, show file info
          final stats = await file.stat();
          final size = _formatFileSize(stats.size);
          final modified = stats.modified;

          setState(() {
            _content =
                '''Document Information:

File: ${widget.filePath.split('/').last}
Size: $size
Modified: ${modified.toString().substring(0, 19)}
Type: ${extension.toUpperCase()} Document

This document type requires an external application to view.
You can open it with your system's default application.''';
            _isLoading = false;
          });
        }
      } else {
        setState(() {
          _error = 'File not found';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading document: $e';
        _isLoading = false;
      });
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading document...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(_error!, textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            ),
          ],
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Document header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF007BFF).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.description,
                  size: 32,
                  color: Color(0xFF007BFF),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.filePath.split('/').last,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Document Preview',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Document content
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: SingleChildScrollView(
                child: Text(
                  _content ?? '',
                  style: const TextStyle(fontFamily: 'monospace', fontSize: 14),
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton.icon(
                onPressed: () {
                  // Copy content to clipboard
                  if (_content != null) {
                    Clipboard.setData(ClipboardData(text: _content!));
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Content copied to clipboard'),
                      ),
                    );
                  }
                },
                icon: const Icon(Icons.copy),
                label: const Text('Copy'),
              ),
              ElevatedButton.icon(
                onPressed: () {
                  // Refresh document
                  setState(() {
                    _isLoading = true;
                    _error = null;
                  });
                  _loadDocument();
                },
                icon: const Icon(Icons.refresh),
                label: const Text('Refresh'),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
