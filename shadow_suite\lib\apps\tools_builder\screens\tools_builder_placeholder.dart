import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/app_theme.dart';
import '../models/tool.dart';
import '../services/excel_service.dart';
import 'tools_builder_dashboard.dart';
import 'tool_editor_screen.dart';
import 'templates_screen.dart';
import 'analytics_screen.dart';

class ToolsBuilderPlaceholder extends ConsumerStatefulWidget {
  const ToolsBuilderPlaceholder({super.key});

  @override
  ConsumerState<ToolsBuilderPlaceholder> createState() => _ToolsBuilderPlaceholderState();
}

class _ToolsBuilderPlaceholderState extends ConsumerState<ToolsBuilderPlaceholder> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                const ToolsBuilderDashboard(),
                _buildSpreadsheetEditor(),
                _buildUIBuilder(),
                _buildMyTools(),
                const AnalyticsScreen(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.build_circle,
            size: 32,
            color: AppTheme.toolsBuilderColor,
          ),
          const SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Tools Builder',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.toolsBuilderColor,
                ),
              ),
              Text(
                'Excel-compatible spreadsheet engine with UI builder',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const Spacer(),
          _buildQuickActions(),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Row(
      children: [
        ElevatedButton.icon(
          onPressed: () => _createNewTool(),
          icon: const Icon(Icons.add, size: 16),
          label: const Text('New Tool'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.toolsBuilderColor,
            foregroundColor: Colors.white,
          ),
        ),
        const SizedBox(width: 12),
        ElevatedButton.icon(
          onPressed: () => _importExcel(),
          icon: const Icon(Icons.upload_file, size: 16),
          label: const Text('Import Excel'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
          ),
        ),
        const SizedBox(width: 12),
        ElevatedButton.icon(
          onPressed: () => _browseTemplates(),
          icon: const Icon(Icons.library_books, size: 16),
          label: const Text('Templates'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: AppTheme.toolsBuilderColor,
        unselectedLabelColor: Colors.grey,
        indicatorColor: AppTheme.toolsBuilderColor,
        tabs: const [
          Tab(
            icon: Icon(Icons.dashboard),
            text: 'Dashboard',
          ),
          Tab(
            icon: Icon(Icons.table_chart),
            text: 'Spreadsheet',
          ),
          Tab(
            icon: Icon(Icons.design_services),
            text: 'UI Builder',
          ),
          Tab(
            icon: Icon(Icons.folder),
            text: 'My Tools',
          ),
          Tab(
            icon: Icon(Icons.analytics),
            text: 'Analytics',
          ),
        ],
      ),
    );
  }

  Widget _buildSpreadsheetEditor() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          _buildSpreadsheetToolbar(),
          const SizedBox(height: 16),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  flex: 4,
                  child: _buildSpreadsheetGrid(),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 1,
                  child: _buildFunctionLibrary(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpreadsheetToolbar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          // Formula bar
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                children: [
                  Text(
                    'fx',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.toolsBuilderColor,
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: TextField(
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        hintText: 'Enter formula or value...',
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 16),
          // Formatting tools
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.format_bold),
            tooltip: 'Bold',
          ),
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.format_italic),
            tooltip: 'Italic',
          ),
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.format_underlined),
            tooltip: 'Underline',
          ),
          const VerticalDivider(),
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.format_align_left),
            tooltip: 'Align Left',
          ),
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.format_align_center),
            tooltip: 'Align Center',
          ),
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.format_align_right),
            tooltip: 'Align Right',
          ),
          const VerticalDivider(),
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.functions),
            tooltip: 'Insert Function',
          ),
        ],
      ),
    );
  }

  Widget _buildSpreadsheetGrid() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          // Column headers
          Container(
            height: 40,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
            ),
            child: Row(
              children: [
                Container(
                  width: 50,
                  decoration: BoxDecoration(
                    border: Border(right: BorderSide(color: Colors.grey[300]!)),
                  ),
                ),
                ...List.generate(10, (index) {
                  final letter = String.fromCharCode(65 + index);
                  return Container(
                    width: 100,
                    decoration: BoxDecoration(
                      border: Border(right: BorderSide(color: Colors.grey[300]!)),
                    ),
                    child: Center(
                      child: Text(
                        letter,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  );
                }),
              ],
            ),
          ),
          // Grid rows
          Expanded(
            child: ListView.builder(
              itemCount: 20,
              itemBuilder: (context, rowIndex) {
                return Container(
                  height: 30,
                  decoration: BoxDecoration(
                    border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
                  ),
                  child: Row(
                    children: [
                      // Row number
                      Container(
                        width: 50,
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          border: Border(right: BorderSide(color: Colors.grey[300]!)),
                        ),
                        child: Center(
                          child: Text(
                            '${rowIndex + 1}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                      ),
                      // Cells
                      ...List.generate(10, (colIndex) {
                        return Container(
                          width: 100,
                          decoration: BoxDecoration(
                            border: Border(right: BorderSide(color: Colors.grey[300]!)),
                          ),
                          child: const TextField(
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                            ),
                            style: TextStyle(fontSize: 12),
                          ),
                        );
                      }),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionLibrary() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Function Library',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            decoration: InputDecoration(
              hintText: 'Search functions...',
              prefixIcon: const Icon(Icons.search, size: 20),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView(
              children: [
                _buildFunctionCategory('Mathematical', [
                  'SUM', 'AVERAGE', 'COUNT', 'MAX', 'MIN', 'ABS', 'ROUND'
                ]),
                _buildFunctionCategory('Logical', [
                  'IF', 'AND', 'OR', 'NOT', 'TRUE', 'FALSE'
                ]),
                _buildFunctionCategory('Text', [
                  'CONCATENATE', 'LEFT', 'RIGHT', 'MID', 'LEN', 'UPPER', 'LOWER'
                ]),
                _buildFunctionCategory('Date & Time', [
                  'TODAY', 'NOW', 'DATE', 'YEAR', 'MONTH', 'DAY'
                ]),
                _buildFunctionCategory('Financial', [
                  'PMT', 'PV', 'FV', 'RATE', 'NPER'
                ]),
                _buildFunctionCategory('Lookup', [
                  'VLOOKUP', 'HLOOKUP', 'INDEX', 'MATCH'
                ]),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionCategory(String title, List<String> functions) {
    return ExpansionTile(
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      children: functions.map((function) {
        return ListTile(
          dense: true,
          title: Text(
            function,
            style: const TextStyle(fontFamily: 'monospace'),
          ),
          onTap: () {
            // Insert function into formula bar
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Inserted $function function')),
            );
          },
        );
      }).toList(),
    );
  }

  Widget _buildUIBuilder() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          // Component palette
          Container(
            width: 250,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Components',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: ListView(
                    children: [
                      _buildComponentCategory('Input', [
                        {'name': 'Text Input', 'icon': Icons.text_fields},
                        {'name': 'Number Input', 'icon': Icons.numbers},
                        {'name': 'Dropdown', 'icon': Icons.arrow_drop_down},
                        {'name': 'Checkbox', 'icon': Icons.check_box},
                        {'name': 'Radio Button', 'icon': Icons.radio_button_checked},
                        {'name': 'Slider', 'icon': Icons.linear_scale},
                        {'name': 'Date Input', 'icon': Icons.date_range},
                      ]),
                      _buildComponentCategory('Display', [
                        {'name': 'Label', 'icon': Icons.label},
                        {'name': 'Image', 'icon': Icons.image},
                        {'name': 'Chart', 'icon': Icons.bar_chart},
                        {'name': 'Table', 'icon': Icons.table_chart},
                      ]),
                      _buildComponentCategory('Layout', [
                        {'name': 'Container', 'icon': Icons.crop_square},
                        {'name': 'Divider', 'icon': Icons.horizontal_rule},
                        {'name': 'Spacer', 'icon': Icons.space_bar},
                      ]),
                      _buildComponentCategory('Action', [
                        {'name': 'Button', 'icon': Icons.smart_button},
                      ]),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          // Design canvas
          Expanded(
            flex: 3,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Stack(
                children: [
                  // Grid background
                  CustomPaint(
                    size: Size.infinite,
                    painter: GridPainter(),
                  ),
                  // Canvas content
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.design_services,
                          size: 64,
                          color: AppTheme.toolsBuilderColor.withValues(alpha: 0.3),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Professional UI Builder',
                          style: TextStyle(
                            color: AppTheme.toolsBuilderColor,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Drag components from the palette to create your interface',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: () => _openFullUIBuilder(),
                          icon: const Icon(Icons.launch, size: 16),
                          label: const Text('Open Full UI Builder'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.toolsBuilderColor,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 16),
          // Properties panel
          Container(
            width: 250,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Properties',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Select a component to edit its properties',
                  style: TextStyle(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComponentCategory(String title, List<Map<String, dynamic>> components) {
    return ExpansionTile(
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      children: components.map((component) {
        return ListTile(
          dense: true,
          leading: Icon(component['icon'], size: 20),
          title: Text(component['name']),
          onTap: () {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Added ${component['name']} to canvas')),
            );
          },
        );
      }).toList(),
    );
  }

  Widget _buildMyTools() {
    return const ToolsBuilderDashboard();
  }

  void _createNewTool() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ToolEditorScreen(),
      ),
    );
  }

  Future<void> _importExcel() async {
    try {
      final result = await ExcelService.importFromExcel();
      if (result.isSuccess && result.spreadsheet != null && mounted) {
        final tool = Tool(
          name: result.spreadsheet!.name,
          description: 'Tool created from imported Excel file',
          type: ToolType.custom,
          category: ToolCategory.utilities,
          spreadsheet: result.spreadsheet!,
          components: [],
          creatorId: 'current-user',
        );

        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ToolEditorScreen(tool: tool),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to import Excel file: $e')),
        );
      }
    }
  }

  void _browseTemplates() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const TemplatesScreen(),
      ),
    );
  }

  void _openFullUIBuilder() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ToolEditorScreen(),
      ),
    );
  }
}

class GridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey[300]!
      ..strokeWidth = 0.5;

    const gridSize = 20.0;

    // Draw vertical lines
    for (double x = 0; x < size.width; x += gridSize) {
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }

    // Draw horizontal lines
    for (double y = 0; y < size.height; y += gridSize) {
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
