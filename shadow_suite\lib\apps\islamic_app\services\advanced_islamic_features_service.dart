import 'dart:async';
import '../models/advanced_islamic_models.dart';
import '../../../core/database/database_service.dart';
import '../../../core/services/error_handler.dart' as error_handler;

class AdvancedIslamicFeaturesService {
  static final List<IslamicArt> _islamicArt = [];
  static final List<IslamicHistory> _historyTimeline = [];
  static final List<QuranMemorization> _memorizationSessions = [];
  static final List<IslamicPodcast> _podcasts = [];
  static final List<VirtualMosqueTour> _mosqueTours = [];
  static final List<IslamicName> _islamicNames = [];
  static final List<DreamInterpretation> _dreamInterpretations = [];
  static final List<CharityRecord> _charityRecords = [];
  static final List<IslamicEventPlan> _eventPlans = [];
  static final List<HalalProduct> _halalProducts = [];

  // Initialize advanced Islamic features service
  static Future<void> initialize() async {
    await _loadAllData();
    await _initializeDefaultContent();
  }

  // FEATURE 16: Islamic Art & Calligraphy (Offline)
  static Future<IslamicArt> addIslamicArt({
    required String title,
    required String artist,
    required IslamicArtType type,
    required String imagePath,
    String? description,
    String? arabicText,
    String? meaning,
    List<String>? tags,
  }) async {
    try {
      final art = IslamicArt(
        id: 'art_${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        artist: artist,
        type: type,
        imagePath: imagePath,
        description: description ?? '',
        arabicText: arabicText,
        meaning: meaning,
        tags: tags ?? [],
        rating: 0.0,
        viewCount: 0,
        isFavorite: false,
        createdAt: DateTime.now(),
      );

      await DatabaseService.safeInsert('islamic_art', art.toJson());
      _islamicArt.add(art);

      return art;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Add Islamic art');
      rethrow;
    }
  }

  // FEATURE 17: Islamic History Timeline (Offline)
  static Future<IslamicHistory> addHistoryEvent({
    required String title,
    required String description,
    required DateTime date,
    required IslamicHistoryPeriod period,
    required String location,
    List<String>? keyFigures,
    String? significance,
    List<String>? sources,
  }) async {
    try {
      final historyEvent = IslamicHistory(
        id: 'history_${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        description: description,
        date: date,
        period: period,
        location: location,
        keyFigures: keyFigures ?? [],
        significance: significance ?? '',
        sources: sources ?? [],
        isVerified: false,
        addedAt: DateTime.now(),
      );

      await DatabaseService.safeInsert('islamic_history', historyEvent.toJson());
      _historyTimeline.add(historyEvent);

      return historyEvent;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Add history event');
      rethrow;
    }
  }

  // FEATURE 18: Quran Memorization Tools (Offline)
  static Future<QuranMemorization> startMemorizationSession({
    required int surahNumber,
    required int startVerse,
    required int endVerse,
    required MemorizationMethod method,
    Map<String, dynamic>? settings,
  }) async {
    try {
      final session = QuranMemorization(
        id: 'memorization_${DateTime.now().millisecondsSinceEpoch}',
        surahNumber: surahNumber,
        startVerse: startVerse,
        endVerse: endVerse,
        method: method,
        progress: 0.0,
        completedVerses: [],
        reviewSchedule: _generateReviewSchedule(startVerse, endVerse),
        settings: settings ?? {},
        startedAt: DateTime.now(),
        lastReviewAt: null,
        isCompleted: false,
      );

      await DatabaseService.safeInsert('quran_memorization', session.toJson());
      _memorizationSessions.add(session);

      return session;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Start memorization session');
      rethrow;
    }
  }

  // FEATURE 19: Islamic Podcast Platform (Offline)
  static Future<IslamicPodcast> addPodcast({
    required String title,
    required String speaker,
    required String description,
    required String audioPath,
    required Duration duration,
    required PodcastCategory category,
    String? transcript,
    List<String>? tags,
  }) async {
    try {
      final podcast = IslamicPodcast(
        id: 'podcast_${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        speaker: speaker,
        description: description,
        audioPath: audioPath,
        duration: duration,
        category: category,
        transcript: transcript,
        tags: tags ?? [],
        playCount: 0,
        rating: 0.0,
        isDownloaded: true, // Offline-first
        downloadedAt: DateTime.now(),
        createdAt: DateTime.now(),
      );

      await DatabaseService.safeInsert('islamic_podcasts', podcast.toJson());
      _podcasts.add(podcast);

      return podcast;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Add podcast');
      rethrow;
    }
  }

  // FEATURE 20: Virtual Mosque Tours (Offline)
  static Future<VirtualMosqueTour> createMosqueTour({
    required String mosqueName,
    required String location,
    required String description,
    required List<TourStop> stops,
    String? audioGuidePath,
    List<String>? imagePaths,
  }) async {
    try {
      final tour = VirtualMosqueTour(
        id: 'tour_${DateTime.now().millisecondsSinceEpoch}',
        mosqueName: mosqueName,
        location: location,
        description: description,
        stops: stops,
        audioGuidePath: audioGuidePath,
        imagePaths: imagePaths ?? [],
        duration: _calculateTourDuration(stops),
        rating: 0.0,
        viewCount: 0,
        isOfflineAvailable: true,
        createdAt: DateTime.now(),
      );

      await DatabaseService.safeInsert('virtual_mosque_tours', tour.toJson());
      _mosqueTours.add(tour);

      return tour;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Create mosque tour');
      rethrow;
    }
  }

  // FEATURE 21: Islamic Name Meanings (Offline)
  static Future<IslamicName> addIslamicName({
    required String name,
    required String meaning,
    required NameGender gender,
    required String origin,
    String? pronunciation,
    List<String>? variations,
    List<String>? famousPersons,
    String? significance,
  }) async {
    try {
      final islamicName = IslamicName(
        id: 'name_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        meaning: meaning,
        gender: gender,
        origin: origin,
        pronunciation: pronunciation,
        variations: variations ?? [],
        famousPersons: famousPersons ?? [],
        significance: significance,
        popularity: 0,
        isFavorite: false,
        addedAt: DateTime.now(),
      );

      await DatabaseService.safeInsert('islamic_names', islamicName.toJson());
      _islamicNames.add(islamicName);

      return islamicName;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Add Islamic name');
      rethrow;
    }
  }

  // FEATURE 22: Islamic Dream Interpretation (Offline)
  static Future<DreamInterpretation> addDreamInterpretation({
    required String symbol,
    required String interpretation,
    required DreamCategory category,
    String? context,
    List<String>? relatedSymbols,
    String? source,
  }) async {
    try {
      final dreamInterpretation = DreamInterpretation(
        id: 'dream_${DateTime.now().millisecondsSinceEpoch}',
        symbol: symbol,
        interpretation: interpretation,
        category: category,
        context: context,
        relatedSymbols: relatedSymbols ?? [],
        source: source ?? 'Traditional Islamic Sources',
        searchCount: 0,
        isVerified: false,
        addedAt: DateTime.now(),
      );

      await DatabaseService.safeInsert('dream_interpretations', dreamInterpretation.toJson());
      _dreamInterpretations.add(dreamInterpretation);

      return dreamInterpretation;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Add dream interpretation');
      rethrow;
    }
  }

  // FEATURE 23: Charity & Donation Tracking (Offline)
  static Future<CharityRecord> recordCharity({
    required String recipient,
    required double amount,
    required String currency,
    required CharityType type,
    String? description,
    DateTime? date,
    bool isZakat = false,
    String? receiptPath,
  }) async {
    try {
      final charityRecord = CharityRecord(
        id: 'charity_${DateTime.now().millisecondsSinceEpoch}',
        recipient: recipient,
        amount: amount,
        currency: currency,
        type: type,
        description: description ?? '',
        date: date ?? DateTime.now(),
        isZakat: isZakat,
        receiptPath: receiptPath,
        isVerified: receiptPath != null,
        createdAt: DateTime.now(),
      );

      await DatabaseService.safeInsert('charity_records', charityRecord.toJson());
      _charityRecords.add(charityRecord);

      return charityRecord;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Record charity');
      rethrow;
    }
  }

  // FEATURE 24: Islamic Event Planning (Offline)
  static Future<IslamicEventPlan> createEventPlan({
    required String eventName,
    required IslamicEventType eventType,
    required DateTime startDate,
    required DateTime endDate,
    required String venue,
    String? description,
    List<EventTask>? tasks,
    double? budget,
    int? expectedAttendees,
  }) async {
    try {
      final eventPlan = IslamicEventPlan(
        id: 'event_plan_${DateTime.now().millisecondsSinceEpoch}',
        eventName: eventName,
        eventType: eventType,
        startDate: startDate,
        endDate: endDate,
        venue: venue,
        description: description ?? '',
        tasks: tasks ?? [],
        budget: budget,
        expectedAttendees: expectedAttendees,
        actualAttendees: 0,
        status: EventStatus.planning,
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      await DatabaseService.safeInsert('islamic_event_plans', eventPlan.toJson());
      _eventPlans.add(eventPlan);

      return eventPlan;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Create event plan');
      rethrow;
    }
  }

  // FEATURE 25: Halal Food Scanner (Offline Database)
  static Future<HalalProduct> addHalalProduct({
    required String name,
    required String brand,
    required String barcode,
    required HalalStatus status,
    required String certifyingBody,
    String? ingredients,
    DateTime? certificationExpiry,
    List<String>? allergens,
  }) async {
    try {
      final product = HalalProduct(
        id: 'halal_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        brand: brand,
        barcode: barcode,
        status: status,
        certifyingBody: certifyingBody,
        ingredients: ingredients,
        certificationExpiry: certificationExpiry,
        allergens: allergens ?? [],
        lastVerified: DateTime.now(),
        reportCount: 0,
        isVerified: true,
        addedAt: DateTime.now(),
      );

      await DatabaseService.safeInsert('halal_products', product.toJson());
      _halalProducts.add(product);

      return product;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.database,
        context: 'Add halal product');
      rethrow;
    }
  }

  // Search and utility methods
  static List<IslamicArt> searchArt({
    String? query,
    IslamicArtType? type,
    List<String>? tags,
  }) {
    try {
      var artworks = _islamicArt.where((art) {
        if (query != null) {
          final queryLower = query.toLowerCase();
          final titleMatch = art.title.toLowerCase().contains(queryLower);
          final artistMatch = art.artist.toLowerCase().contains(queryLower);
          if (!titleMatch && !artistMatch) return false;
        }
        if (type != null && art.type != type) return false;
        if (tags != null && !tags.any((tag) => art.tags.contains(tag))) return false;
        return true;
      }).toList();

      return artworks..sort((a, b) => b.rating.compareTo(a.rating));
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Search Islamic art');
      return [];
    }
  }

  static List<IslamicHistory> getHistoryByPeriod(IslamicHistoryPeriod period) {
    try {
      var events = _historyTimeline.where((event) => event.period == period).toList();
      return events..sort((a, b) => a.date.compareTo(b.date));
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Get history by period');
      return [];
    }
  }

  static List<IslamicName> searchNames({
    String? query,
    NameGender? gender,
    String? origin,
  }) {
    try {
      var names = _islamicNames.where((name) {
        if (query != null) {
          final queryLower = query.toLowerCase();
          final nameMatch = name.name.toLowerCase().contains(queryLower);
          final meaningMatch = name.meaning.toLowerCase().contains(queryLower);
          if (!nameMatch && !meaningMatch) return false;
        }
        if (gender != null && name.gender != gender && name.gender != NameGender.unisex) return false;
        if (origin != null && !name.origin.toLowerCase().contains(origin.toLowerCase())) return false;
        return true;
      }).toList();

      return names..sort((a, b) => b.popularity.compareTo(a.popularity));
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Search Islamic names');
      return [];
    }
  }

  static List<DreamInterpretation> searchDreamInterpretations(String symbol) {
    try {
      final symbolLower = symbol.toLowerCase();
      var interpretations = _dreamInterpretations.where((dream) {
        final symbolMatch = dream.symbol.toLowerCase().contains(symbolLower);
        final relatedMatch = dream.relatedSymbols.any(
          (related) => related.toLowerCase().contains(symbolLower));
        return symbolMatch || relatedMatch;
      }).toList();

      return interpretations..sort((a, b) => b.searchCount.compareTo(a.searchCount));
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Search dream interpretations');
      return [];
    }
  }

  static HalalProduct? scanProduct(String barcode) {
    try {
      return _halalProducts.firstWhere(
        (product) => product.barcode == barcode,
        orElse: () => throw StateError('Product not found'),
      );
    } catch (error) {
      return null;
    }
  }

  static Map<CharityType, double> getCharitySummary({
    DateTime? startDate,
    DateTime? endDate,
    String? currency,
  }) {
    try {
      var records = _charityRecords.where((record) {
        if (startDate != null && record.date.isBefore(startDate)) return false;
        if (endDate != null && record.date.isAfter(endDate)) return false;
        if (currency != null && record.currency != currency) return false;
        return true;
      }).toList();

      final summary = <CharityType, double>{};
      for (final record in records) {
        summary[record.type] = (summary[record.type] ?? 0) + record.amount;
      }

      return summary;
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Get charity summary');
      return {};
    }
  }

  // Helper methods
  static Map<int, DateTime> _generateReviewSchedule(int startVerse, int endVerse) {
    final schedule = <int, DateTime>{};
    final now = DateTime.now();

    for (int verse = startVerse; verse <= endVerse; verse++) {
      // Spaced repetition schedule: 1 day, 3 days, 7 days, 14 days, 30 days
      schedule[verse] = now.add(Duration(days: 1));
    }

    return schedule;
  }

  static Duration _calculateTourDuration(List<TourStop> stops) {
    var totalDuration = Duration.zero;
    for (final stop in stops) {
      if (stop.duration != null) {
        totalDuration += stop.duration!;
      } else {
        totalDuration += const Duration(minutes: 3); // Default 3 minutes per stop
      }
    }
    return totalDuration;
  }

  // Data loading methods
  static Future<void> _loadAllData() async {
    await Future.wait([
      _loadIslamicArt(),
      _loadHistoryTimeline(),
      _loadMemorizationSessions(),
      _loadPodcasts(),
      _loadMosqueTours(),
      _loadIslamicNames(),
      _loadDreamInterpretations(),
      _loadCharityRecords(),
      _loadEventPlans(),
      _loadHalalProducts(),
    ]);
  }

  static Future<void> _loadIslamicArt() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM islamic_art');
      _islamicArt.clear();
      for (final row in results) {
        _islamicArt.add(IslamicArt.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load Islamic art');
    }
  }

  static Future<void> _loadHistoryTimeline() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM islamic_history');
      _historyTimeline.clear();
      for (final row in results) {
        _historyTimeline.add(IslamicHistory.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load history timeline');
    }
  }

  static Future<void> _loadMemorizationSessions() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM quran_memorization');
      _memorizationSessions.clear();
      for (final row in results) {
        _memorizationSessions.add(QuranMemorization.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load memorization sessions');
    }
  }

  static Future<void> _loadPodcasts() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM islamic_podcasts');
      _podcasts.clear();
      for (final row in results) {
        _podcasts.add(IslamicPodcast.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load podcasts');
    }
  }

  static Future<void> _loadMosqueTours() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM virtual_mosque_tours');
      _mosqueTours.clear();
      for (final row in results) {
        _mosqueTours.add(VirtualMosqueTour.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load mosque tours');
    }
  }

  static Future<void> _loadIslamicNames() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM islamic_names');
      _islamicNames.clear();
      for (final row in results) {
        _islamicNames.add(IslamicName.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load Islamic names');
    }
  }

  static Future<void> _loadDreamInterpretations() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM dream_interpretations');
      _dreamInterpretations.clear();
      for (final row in results) {
        _dreamInterpretations.add(DreamInterpretation.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load dream interpretations');
    }
  }

  static Future<void> _loadCharityRecords() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM charity_records');
      _charityRecords.clear();
      for (final row in results) {
        _charityRecords.add(CharityRecord.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load charity records');
    }
  }

  static Future<void> _loadEventPlans() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM islamic_event_plans');
      _eventPlans.clear();
      for (final row in results) {
        _eventPlans.add(IslamicEventPlan.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load event plans');
    }
  }

  static Future<void> _loadHalalProducts() async {
    try {
      final results = await DatabaseService.safeQuery('SELECT * FROM halal_products');
      _halalProducts.clear();
      for (final row in results) {
        _halalProducts.add(HalalProduct.fromJson(row));
      }
    } catch (error) {
      error_handler.ErrorHandler.handleDatabaseError(error, operation: 'Load halal products');
    }
  }

  static Future<void> _initializeDefaultContent() async {
    try {
      // Initialize default content if collections are empty
      if (_islamicNames.isEmpty) {
        await _addDefaultIslamicNames();
      }

      if (_dreamInterpretations.isEmpty) {
        await _addDefaultDreamInterpretations();
      }

      if (_halalProducts.isEmpty) {
        await _addDefaultHalalProducts();
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(error, stackTrace, error_handler.ErrorType.operation,
        context: 'Initialize default content');
    }
  }

  static Future<void> _addDefaultIslamicNames() async {
    final defaultNames = [
      {
        'name': 'Muhammad',
        'meaning': 'Praised, commendable',
        'gender': NameGender.male,
        'origin': 'Arabic',
        'significance': 'Name of the Prophet (PBUH)',
      },
      {
        'name': 'Aisha',
        'meaning': 'Living, alive',
        'gender': NameGender.female,
        'origin': 'Arabic',
        'significance': 'Name of the Prophet\'s wife (RA)',
      },
      {
        'name': 'Ali',
        'meaning': 'High, elevated, sublime',
        'gender': NameGender.male,
        'origin': 'Arabic',
        'significance': 'Name of the fourth Caliph (RA)',
      },
    ];

    for (final nameData in defaultNames) {
      await addIslamicName(
        name: nameData['name'] as String,
        meaning: nameData['meaning'] as String,
        gender: nameData['gender'] as NameGender,
        origin: nameData['origin'] as String,
        significance: nameData['significance'] as String,
      );
    }
  }

  static Future<void> _addDefaultDreamInterpretations() async {
    final defaultInterpretations = [
      {
        'symbol': 'Water',
        'interpretation': 'Knowledge, purity, life, or spiritual cleansing',
        'category': DreamCategory.nature,
      },
      {
        'symbol': 'Light',
        'interpretation': 'Guidance, knowledge, faith, or divine blessing',
        'category': DreamCategory.spiritual,
      },
      {
        'symbol': 'Green',
        'interpretation': 'Islam, paradise, growth, or prosperity',
        'category': DreamCategory.spiritual,
      },
    ];

    for (final interpretation in defaultInterpretations) {
      await addDreamInterpretation(
        symbol: interpretation['symbol'] as String,
        interpretation: interpretation['interpretation'] as String,
        category: interpretation['category'] as DreamCategory,
      );
    }
  }

  static Future<void> _addDefaultHalalProducts() async {
    final defaultProducts = [
      {
        'name': 'Halal Chicken',
        'brand': 'Sample Brand',
        'barcode': '1234567890123',
        'status': HalalStatus.halal,
        'certifyingBody': 'Islamic Food Authority',
      },
      {
        'name': 'Beef Jerky',
        'brand': 'Sample Brand',
        'barcode': '2345678901234',
        'status': HalalStatus.halal,
        'certifyingBody': 'Halal Certification Board',
      },
    ];

    for (final product in defaultProducts) {
      await addHalalProduct(
        name: product['name'] as String,
        brand: product['brand'] as String,
        barcode: product['barcode'] as String,
        status: product['status'] as HalalStatus,
        certifyingBody: product['certifyingBody'] as String,
      );
    }
  }

  // Getters
  static List<IslamicArt> get islamicArt => List.unmodifiable(_islamicArt);
  static List<IslamicHistory> get historyTimeline => List.unmodifiable(_historyTimeline);
  static List<QuranMemorization> get memorizationSessions => List.unmodifiable(_memorizationSessions);
  static List<IslamicPodcast> get podcasts => List.unmodifiable(_podcasts);
  static List<VirtualMosqueTour> get mosqueTours => List.unmodifiable(_mosqueTours);
  static List<IslamicName> get islamicNames => List.unmodifiable(_islamicNames);
  static List<DreamInterpretation> get dreamInterpretations => List.unmodifiable(_dreamInterpretations);
  static List<CharityRecord> get charityRecords => List.unmodifiable(_charityRecords);
  static List<IslamicEventPlan> get eventPlans => List.unmodifiable(_eventPlans);
  static List<HalalProduct> get halalProducts => List.unmodifiable(_halalProducts);

  // Dispose
  static void dispose() {
    _islamicArt.clear();
    _historyTimeline.clear();
    _memorizationSessions.clear();
    _podcasts.clear();
    _mosqueTours.clear();
    _islamicNames.clear();
    _dreamInterpretations.clear();
    _charityRecords.clear();
    _eventPlans.clear();
    _halalProducts.clear();
  }
}