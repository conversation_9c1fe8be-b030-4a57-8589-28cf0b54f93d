import 'package:flutter/material.dart';

/// Athkar session model
class AthkarSession {
  final String id;
  final DateTime startTime;
  final Duration duration;
  final List<CompletedAthkar> completedAthkar;
  final double focusScore;
  final String location;
  final DateTime createdAt;

  const AthkarSession({
    required this.id,
    required this.startTime,
    required this.duration,
    required this.completedAthkar,
    required this.focusScore,
    required this.location,
    required this.createdAt,
  });

  AthkarSession copyWith({
    String? id,
    DateTime? startTime,
    Duration? duration,
    List<CompletedAthkar>? completedAthkar,
    double? focusScore,
    String? location,
    DateTime? createdAt,
  }) {
    return AthkarSession(
      id: id ?? this.id,
      startTime: startTime ?? this.startTime,
      duration: duration ?? this.duration,
      completedAthkar: completedAthkar ?? this.completedAthkar,
      focusScore: focusScore ?? this.focusScore,
      location: location ?? this.location,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'startTime': startTime.toIso8601String(),
      'duration': duration.inMinutes,
      'completedAthkar': completedAthkar.map((a) => a.toJson()).toList(),
      'focusScore': focusScore,
      'location': location,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory AthkarSession.fromJson(Map<String, dynamic> json) {
    return AthkarSession(
      id: json['id'],
      startTime: DateTime.parse(json['startTime']),
      duration: Duration(minutes: json['duration']),
      completedAthkar: (json['completedAthkar'] as List).map((a) => CompletedAthkar.fromJson(a)).toList(),
      focusScore: json['focusScore'].toDouble(),
      location: json['location'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Completed athkar in a session
class CompletedAthkar {
  final String name;
  final int targetCount;
  final int completedCount;
  final Duration timeSpent;
  final double accuracy;

  const CompletedAthkar({
    required this.name,
    required this.targetCount,
    required this.completedCount,
    required this.timeSpent,
    required this.accuracy,
  });

  CompletedAthkar copyWith({
    String? name,
    int? targetCount,
    int? completedCount,
    Duration? timeSpent,
    double? accuracy,
  }) {
    return CompletedAthkar(
      name: name ?? this.name,
      targetCount: targetCount ?? this.targetCount,
      completedCount: completedCount ?? this.completedCount,
      timeSpent: timeSpent ?? this.timeSpent,
      accuracy: accuracy ?? this.accuracy,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'targetCount': targetCount,
      'completedCount': completedCount,
      'timeSpent': timeSpent.inSeconds,
      'accuracy': accuracy,
    };
  }

  factory CompletedAthkar.fromJson(Map<String, dynamic> json) {
    return CompletedAthkar(
      name: json['name'],
      targetCount: json['targetCount'],
      completedCount: json['completedCount'],
      timeSpent: Duration(seconds: json['timeSpent']),
      accuracy: json['accuracy'].toDouble(),
    );
  }
}

/// User preferences for athkar
class UserPreferences {
  final String preferredLanguage;
  final bool enableNotifications;
  final List<int> reminderTimes;
  final AthkarLevel currentLevel;
  final List<String> favoriteAthkar;
  final bool enableVibration;
  final double volume;

  const UserPreferences({
    required this.preferredLanguage,
    required this.enableNotifications,
    required this.reminderTimes,
    required this.currentLevel,
    required this.favoriteAthkar,
    required this.enableVibration,
    required this.volume,
  });

  UserPreferences copyWith({
    String? preferredLanguage,
    bool? enableNotifications,
    List<int>? reminderTimes,
    AthkarLevel? currentLevel,
    List<String>? favoriteAthkar,
    bool? enableVibration,
    double? volume,
  }) {
    return UserPreferences(
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      reminderTimes: reminderTimes ?? this.reminderTimes,
      currentLevel: currentLevel ?? this.currentLevel,
      favoriteAthkar: favoriteAthkar ?? this.favoriteAthkar,
      enableVibration: enableVibration ?? this.enableVibration,
      volume: volume ?? this.volume,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'preferredLanguage': preferredLanguage,
      'enableNotifications': enableNotifications,
      'reminderTimes': reminderTimes,
      'currentLevel': currentLevel.name,
      'favoriteAthkar': favoriteAthkar,
      'enableVibration': enableVibration,
      'volume': volume,
    };
  }

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      preferredLanguage: json['preferredLanguage'],
      enableNotifications: json['enableNotifications'],
      reminderTimes: List<int>.from(json['reminderTimes']),
      currentLevel: AthkarLevel.values.firstWhere((e) => e.name == json['currentLevel']),
      favoriteAthkar: List<String>.from(json['favoriteAthkar']),
      enableVibration: json['enableVibration'],
      volume: json['volume'].toDouble(),
    );
  }
}

/// Custom athkar created by user
class CustomAthkar {
  final String id;
  final String name;
  final String arabicText;
  final String transliteration;
  final String translation;
  final int recommendedCount;
  final AthkarTiming timing;
  final String category;
  final DateTime createdAt;

  const CustomAthkar({
    required this.id,
    required this.name,
    required this.arabicText,
    required this.transliteration,
    required this.translation,
    required this.recommendedCount,
    required this.timing,
    required this.category,
    required this.createdAt,
  });

  CustomAthkar copyWith({
    String? id,
    String? name,
    String? arabicText,
    String? transliteration,
    String? translation,
    int? recommendedCount,
    AthkarTiming? timing,
    String? category,
    DateTime? createdAt,
  }) {
    return CustomAthkar(
      id: id ?? this.id,
      name: name ?? this.name,
      arabicText: arabicText ?? this.arabicText,
      transliteration: transliteration ?? this.transliteration,
      translation: translation ?? this.translation,
      recommendedCount: recommendedCount ?? this.recommendedCount,
      timing: timing ?? this.timing,
      category: category ?? this.category,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'arabicText': arabicText,
      'transliteration': transliteration,
      'translation': translation,
      'recommendedCount': recommendedCount,
      'timing': timing.name,
      'category': category,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory CustomAthkar.fromJson(Map<String, dynamic> json) {
    return CustomAthkar(
      id: json['id'],
      name: json['name'],
      arabicText: json['arabicText'],
      transliteration: json['transliteration'],
      translation: json['translation'],
      recommendedCount: json['recommendedCount'],
      timing: AthkarTiming.values.firstWhere((e) => e.name == json['timing']),
      category: json['category'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Athkar recommendation from AI
class AthkarRecommendation {
  final String id;
  final RecommendationType type;
  final String title;
  final String description;
  final List<String> suggestedAthkar;
  final AthkarTiming timing;
  final Priority priority;
  final double confidence;
  final String estimatedBenefit;
  final DateTime createdAt;

  const AthkarRecommendation({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.suggestedAthkar,
    required this.timing,
    required this.priority,
    required this.confidence,
    required this.estimatedBenefit,
    required this.createdAt,
  });

  AthkarRecommendation copyWith({
    String? id,
    RecommendationType? type,
    String? title,
    String? description,
    List<String>? suggestedAthkar,
    AthkarTiming? timing,
    Priority? priority,
    double? confidence,
    String? estimatedBenefit,
    DateTime? createdAt,
  }) {
    return AthkarRecommendation(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      suggestedAthkar: suggestedAthkar ?? this.suggestedAthkar,
      timing: timing ?? this.timing,
      priority: priority ?? this.priority,
      confidence: confidence ?? this.confidence,
      estimatedBenefit: estimatedBenefit ?? this.estimatedBenefit,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'suggestedAthkar': suggestedAthkar,
      'timing': timing.name,
      'priority': priority.name,
      'confidence': confidence,
      'estimatedBenefit': estimatedBenefit,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory AthkarRecommendation.fromJson(Map<String, dynamic> json) {
    return AthkarRecommendation(
      id: json['id'],
      type: RecommendationType.values.firstWhere((e) => e.name == json['type']),
      title: json['title'],
      description: json['description'],
      suggestedAthkar: List<String>.from(json['suggestedAthkar']),
      timing: AthkarTiming.values.firstWhere((e) => e.name == json['timing']),
      priority: Priority.values.firstWhere((e) => e.name == json['priority']),
      confidence: json['confidence'].toDouble(),
      estimatedBenefit: json['estimatedBenefit'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Athkar insight from AI analysis
class AthkarInsight {
  final String id;
  final InsightType type;
  final String title;
  final String description;
  final double confidence;
  final List<String> recommendations;
  final DateTime createdAt;

  const AthkarInsight({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.confidence,
    required this.recommendations,
    required this.createdAt,
  });

  AthkarInsight copyWith({
    String? id,
    InsightType? type,
    String? title,
    String? description,
    double? confidence,
    List<String>? recommendations,
    DateTime? createdAt,
  }) {
    return AthkarInsight(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      confidence: confidence ?? this.confidence,
      recommendations: recommendations ?? this.recommendations,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'confidence': confidence,
      'recommendations': recommendations,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory AthkarInsight.fromJson(Map<String, dynamic> json) {
    return AthkarInsight(
      id: json['id'],
      type: InsightType.values.firstWhere((e) => e.name == json['type']),
      title: json['title'],
      description: json['description'],
      confidence: json['confidence'].toDouble(),
      recommendations: List<String>.from(json['recommendations']),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Enums
enum AthkarLevel { beginner, intermediate, advanced }
enum AthkarTiming { morning, afternoon, evening, night, flexible }
enum RecommendationType { routine, timing, improvement, combination, thematic, progression, seasonal, weekly }
enum Priority { low, medium, high }
enum InsightType { pattern, consistency, improvement, achievement }
