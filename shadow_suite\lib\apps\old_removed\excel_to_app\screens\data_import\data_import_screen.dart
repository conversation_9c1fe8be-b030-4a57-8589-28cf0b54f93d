import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_theme.dart';
import '../../widgets/data_import_export_widget.dart';
import '../../services/data_import_export_service.dart';

/// Data import screen with comprehensive format support
class DataImportScreen extends ConsumerStatefulWidget {
  const DataImportScreen({super.key});

  @override
  ConsumerState<DataImportScreen> createState() => _DataImportScreenState();
}

class _DataImportScreenState extends ConsumerState<DataImportScreen> {
  List<List<dynamic>> _importedData = [];
  bool _hasData = false;

  @override
  void initState() {
    super.initState();
    DataImportExportService.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Data Import/Export'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showHelp,
            icon: const Icon(Icons.help),
            tooltip: 'Help',
          ),
          IconButton(
            onPressed: _showSettings,
            icon: const Icon(Icons.settings),
            tooltip: 'Settings',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildStatusBar(),
          Expanded(
            child: DataImportExportWidget(
              onDataImported: _onDataImported,
              dataToExport: _hasData ? _importedData : null,
            ),
          ),
          if (_hasData) _buildDataViewer(),
        ],
      ),
      floatingActionButton: _hasData
          ? FloatingActionButton.extended(
              onPressed: _createAppFromData,
              icon: const Icon(Icons.apps),
              label: const Text('Create App'),
              backgroundColor: AppTheme.primaryColor,
            )
          : null,
    );
  }

  Widget _buildStatusBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withValues(alpha: 0.1),
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          Icon(
            _hasData ? Icons.check_circle : Icons.info,
            color: _hasData ? Colors.green : Colors.grey,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _hasData
                  ? 'Data loaded: ${_importedData.length} rows, ${_importedData.isNotEmpty ? _importedData.first.length : 0} columns'
                  : 'No data loaded. Import data to get started.',
              style: TextStyle(
                color: _hasData ? Colors.green[700] : Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (_hasData) ...[
            TextButton.icon(
              onPressed: _clearData,
              icon: const Icon(Icons.clear),
              label: const Text('Clear'),
            ),
            const SizedBox(width: 8),
            TextButton.icon(
              onPressed: _showDataStatistics,
              icon: const Icon(Icons.analytics),
              label: const Text('Stats'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDataViewer() {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
            ),
            child: Row(
              children: [
                const Icon(Icons.table_view, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Data Preview',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Spacer(),
                Text(
                  '${_importedData.length} rows × ${_importedData.isNotEmpty ? _importedData.first.length : 0} columns',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: SingleChildScrollView(
                child: DataTable(
                  columns: _buildDataColumns(),
                  rows: _buildDataRows(),
                  columnSpacing: 20,
                  headingRowColor: WidgetStateProperty.all(Colors.grey[100]),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataColumn> _buildDataColumns() {
    if (_importedData.isEmpty) return [];

    final firstRow = _importedData.first;
    return List.generate(firstRow.length, (index) {
      return DataColumn(
        label: Text(
          'Column ${index + 1}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
      );
    });
  }

  List<DataRow> _buildDataRows() {
    if (_importedData.isEmpty) return [];

    return _importedData.take(50).map((row) {
      return DataRow(
        cells: row.map((cell) {
          return DataCell(
            Container(
              constraints: const BoxConstraints(maxWidth: 150),
              child: Text(cell.toString(), overflow: TextOverflow.ellipsis),
            ),
          );
        }).toList(),
      );
    }).toList();
  }

  void _onDataImported(List<List<dynamic>> data) {
    setState(() {
      _importedData = data;
      _hasData = data.isNotEmpty;
    });

    _showSnackBar(
      'Data imported successfully: ${data.length} rows',
      Colors.green,
    );
  }

  void _clearData() {
    setState(() {
      _importedData = [];
      _hasData = false;
    });

    _showSnackBar('Data cleared', Colors.orange);
  }

  void _showDataStatistics() {
    if (!_hasData) return;

    final rowCount = _importedData.length;
    final columnCount = _importedData.isNotEmpty
        ? _importedData.first.length
        : 0;
    final cellCount = rowCount * columnCount;

    // Calculate data types
    final dataTypes = <String, int>{};
    for (final row in _importedData) {
      for (final cell in row) {
        final type = _getDataType(cell);
        dataTypes[type] = (dataTypes[type] ?? 0) + 1;
      }
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Data Statistics'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatItem('Rows', rowCount.toString()),
            _buildStatItem('Columns', columnCount.toString()),
            _buildStatItem('Total Cells', cellCount.toString()),
            const SizedBox(height: 16),
            const Text(
              'Data Types:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            ...dataTypes.entries.map((entry) {
              final percentage = (entry.value / cellCount * 100)
                  .toStringAsFixed(1);
              return _buildStatItem(entry.key, '${entry.value} ($percentage%)');
            }),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  String _getDataType(dynamic value) {
    if (value == null || value.toString().isEmpty) return 'Empty';
    if (int.tryParse(value.toString()) != null) return 'Integer';
    if (double.tryParse(value.toString()) != null) return 'Number';
    if (DateTime.tryParse(value.toString()) != null) return 'Date';
    if (['true', 'false', '1', '0'].contains(value.toString().toLowerCase())) {
      return 'Boolean';
    }
    return 'Text';
  }

  void _createAppFromData() {
    if (!_hasData) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create App from Data'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Choose how to create your app from the imported data:'),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.table_chart),
              title: const Text('Data Table App'),
              subtitle: const Text('Create a searchable data table'),
              onTap: () {
                Navigator.of(context).pop();
                _createDataTableApp();
              },
            ),
            ListTile(
              leading: const Icon(Icons.dashboard),
              title: const Text('Dashboard App'),
              subtitle: const Text('Create charts and visualizations'),
              onTap: () {
                Navigator.of(context).pop();
                _createDashboardApp();
              },
            ),
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Form App'),
              subtitle: const Text('Create data entry forms'),
              onTap: () {
                Navigator.of(context).pop();
                _createFormApp();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _createDataTableApp() {
    _showSnackBar('Creating data table app...', Colors.blue);
    // Implementation for creating data table app
  }

  void _createDashboardApp() {
    _showSnackBar('Creating dashboard app...', Colors.blue);
    // Implementation for creating dashboard app
  }

  void _createFormApp() {
    _showSnackBar('Creating form app...', Colors.blue);
    // Implementation for creating form app
  }

  void _showHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Data Import/Export Help'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Supported Formats:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• CSV - Comma Separated Values'),
              Text('• JSON - JavaScript Object Notation'),
              Text('• TSV - Tab Separated Values'),
              Text('• XML - Extensible Markup Language'),
              Text('• Excel - Microsoft Excel files'),
              Text('• TXT - Plain text files'),
              SizedBox(height: 16),
              Text(
                'Import Process:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('1. Select the format of your data'),
              Text('2. Configure import options'),
              Text('3. Choose a file or paste data'),
              Text('4. Preview the data'),
              Text('5. Import and create your app'),
              SizedBox(height: 16),
              Text(
                'Export Options:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Export to multiple formats'),
              Text('• Include or exclude headers'),
              Text('• Custom delimiters for CSV/TSV'),
              Text('• Pretty print for JSON'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import/Export Settings'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CheckboxListTile(
              title: Text('Auto-detect format'),
              subtitle: Text('Automatically detect file format'),
              value: true,
              onChanged: null,
            ),
            CheckboxListTile(
              title: Text('Show preview by default'),
              subtitle: Text('Always show data preview before import'),
              value: true,
              onChanged: null,
            ),
            CheckboxListTile(
              title: Text('Validate data on import'),
              subtitle: Text('Check for data consistency'),
              value: false,
              onChanged: null,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
