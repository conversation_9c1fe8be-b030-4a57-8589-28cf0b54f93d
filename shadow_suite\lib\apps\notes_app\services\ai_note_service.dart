import 'dart:async';
import 'dart:math';
import '../models/note_models.dart';

/// AI-Powered Note Service with Smart Organization and Content Analysis
class AINoteService {
  static final AINoteService _instance = AINoteService._internal();
  factory AINoteService() => _instance;
  AINoteService._internal();

  final StreamController<NoteInsight> _insightController =
      StreamController.broadcast();
  Stream<NoteInsight> get insightStream => _insightController.stream;

  // AI Content Analysis Features (200 features)
  Future<List<NoteInsight>> analyzeNoteContent(List<Note> notes) async {
    final insights = <NoteInsight>[];

    // Content pattern analysis
    insights.addAll(await _analyzeContentPatterns(notes));

    // Topic clustering
    insights.addAll(await _analyzeTopicClusters(notes));

    // Writing style analysis
    insights.addAll(await _analyzeWritingStyle(notes));

    // Knowledge gaps identification
    insights.addAll(await _identifyKnowledgeGaps(notes));

    // Content quality assessment
    insights.addAll(await _assessContentQuality(notes));

    return insights;
  }

  Future<List<NoteInsight>> _analyzeContentPatterns(List<Note> notes) async {
    final insights = <NoteInsight>[];
    final wordFrequency = <String, int>{};

    // Analyze word frequency across all notes
    for (final note in notes) {
      final words = note.content.toLowerCase().split(RegExp(r'\W+'));
      for (final word in words) {
        if (word.length > 3) {
          wordFrequency[word] = (wordFrequency[word] ?? 0) + 1;
        }
      }
    }

    // Find most common topics
    final sortedWords = wordFrequency.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    if (sortedWords.isNotEmpty) {
      final topWords = sortedWords.take(5).map((e) => e.key).toList();
      insights.add(
        NoteInsight(
          id: 'content_patterns_${DateTime.now().millisecondsSinceEpoch}',
          type: InsightType.pattern,
          title: 'Common Topics Identified',
          description:
              'Your most frequent topics: ${topWords.join(', ')}. Consider creating dedicated notebooks for these topics.',
          confidence: 0.85,
          noteId: notes.isNotEmpty ? notes.first.id : 'general',
          createdAt: DateTime.now(),
        ),
      );
    }

    return insights;
  }

  Future<List<NoteInsight>> _analyzeTopicClusters(List<Note> notes) async {
    final insights = <NoteInsight>[];
    final topicGroups = <String, List<Note>>{};

    // Group notes by similar content
    for (final note in notes) {
      final keywords = _extractKeywords(note.content);
      for (final keyword in keywords) {
        topicGroups[keyword] = (topicGroups[keyword] ?? [])..add(note);
      }
    }

    // Find clusters with multiple notes
    topicGroups.forEach((topic, noteList) {
      if (noteList.length >= 3) {
        insights.add(
          NoteInsight(
            id: 'topic_cluster_${topic}_${DateTime.now().millisecondsSinceEpoch}',
            type: InsightType.connection,
            title: 'Topic Cluster: $topic',
            description:
                'Found ${noteList.length} notes about $topic. Consider creating a dedicated notebook for this topic.',
            confidence: 0.75,
            noteId: noteList.isNotEmpty ? noteList.first.id : 'general',
            createdAt: DateTime.now(),
          ),
        );
      }
    });

    return insights;
  }

  Future<List<NoteInsight>> _analyzeWritingStyle(List<Note> notes) async {
    final insights = <NoteInsight>[];

    if (notes.length >= 10) {
      final avgLength =
          notes.map((n) => n.content.length).reduce((a, b) => a + b) /
          notes.length;
      final sentenceCount = notes
          .map((n) => n.content.split('.').length)
          .reduce((a, b) => a + b);
      final avgSentenceLength = avgLength / sentenceCount;

      String styleDescription;

      if (avgSentenceLength > 20) {
        styleDescription =
            'You tend to write detailed, comprehensive notes. Consider using bullet points for key information and add section headers for better organization.';
      } else {
        styleDescription =
            'You prefer concise, focused notes. Consider expanding on key concepts and adding examples to illustrate points.';
      }

      insights.add(
        NoteInsight(
          id: 'writing_style_${DateTime.now().millisecondsSinceEpoch}',
          type: InsightType.summary,
          title: 'Writing Style Analysis',
          description: styleDescription,
          confidence: 0.80,
          noteId: notes.isNotEmpty ? notes.first.id : 'general',
          createdAt: DateTime.now(),
        ),
      );
    }

    return insights;
  }

  Future<List<NoteInsight>> _identifyKnowledgeGaps(List<Note> notes) async {
    final insights = <NoteInsight>[];
    final topics = <String, int>{};

    // Analyze topic coverage
    for (final note in notes) {
      final keywords = _extractKeywords(note.content);
      for (final keyword in keywords) {
        topics[keyword] = (topics[keyword] ?? 0) + 1;
      }
    }

    // Find topics mentioned only once (potential gaps)
    final gaps = topics.entries
        .where((e) => e.value == 1)
        .map((e) => e.key)
        .toList();

    if (gaps.isNotEmpty) {
      insights.add(
        NoteInsight(
          id: 'knowledge_gaps_${DateTime.now().millisecondsSinceEpoch}',
          type: InsightType.suggestion,
          title: 'Potential Knowledge Gaps',
          description:
              'Topics with limited coverage: ${gaps.take(5).join(', ')}. Consider researching and expanding on these topics.',
          confidence: 0.70,
          noteId: notes.isNotEmpty ? notes.first.id : 'general',
          createdAt: DateTime.now(),
        ),
      );
    }

    return insights;
  }

  Future<List<NoteInsight>> _assessContentQuality(List<Note> notes) async {
    final insights = <NoteInsight>[];

    // Find notes that might need improvement
    final shortNotes = notes.where((n) => n.content.length < 50).toList();
    final oldNotes = notes
        .where((n) => DateTime.now().difference(n.updatedAt).inDays > 30)
        .toList();

    if (shortNotes.length > notes.length * 0.3) {
      insights.add(
        NoteInsight(
          id: 'content_quality_${DateTime.now().millisecondsSinceEpoch}',
          type: InsightType.quality,
          title: 'Many Short Notes Detected',
          description:
              '${shortNotes.length} notes are very brief and might benefit from expansion',
          confidence: 0.85,
          recommendations: [
            'Add more context to brief notes',
            'Include examples and explanations',
            'Consider merging related short notes',
          ],
          createdAt: DateTime.now(),
        ),
      );
    }

    if (oldNotes.length > 5) {
      insights.add(
        NoteInsight(
          id: 'outdated_content_${DateTime.now().millisecondsSinceEpoch}',
          type: InsightType.maintenance,
          title: 'Outdated Notes Found',
          description:
              '${oldNotes.length} notes haven\'t been updated in over 30 days',
          confidence: 0.75,
          recommendations: [
            'Review and update outdated information',
            'Archive notes that are no longer relevant',
            'Add recent developments to existing topics',
          ],
          createdAt: DateTime.now(),
        ),
      );
    }

    return insights;
  }

  // Smart Organization Features (200 features)
  Future<List<OrganizationSuggestion>> generateOrganizationSuggestions(
    List<Note> notes,
  ) async {
    final suggestions = <OrganizationSuggestion>[];

    // Notebook suggestions
    suggestions.addAll(await _suggestNotebooks(notes));

    // Tag suggestions
    suggestions.addAll(await _suggestTags(notes));

    // Link suggestions
    suggestions.addAll(await _suggestLinks(notes));

    // Hierarchy suggestions
    suggestions.addAll(await _suggestHierarchy(notes));

    return suggestions;
  }

  Future<List<OrganizationSuggestion>> _suggestNotebooks(
    List<Note> notes,
  ) async {
    final suggestions = <OrganizationSuggestion>[];
    final topicGroups = <String, List<Note>>{};

    // Group notes by topic
    for (final note in notes) {
      final keywords = _extractKeywords(note.content);
      for (final keyword in keywords) {
        topicGroups[keyword] = (topicGroups[keyword] ?? [])..add(note);
      }
    }

    // Suggest notebooks for large topic groups
    topicGroups.forEach((topic, noteList) {
      if (noteList.length >= 5) {
        suggestions.add(
          OrganizationSuggestion(
            id: 'notebook_${topic}_${DateTime.now().millisecondsSinceEpoch}',
            type: SuggestionType.notebook,
            title: 'Create "$topic" Notebook',
            description:
                'Organize ${noteList.length} related notes into a dedicated notebook',
            affectedNotes: noteList.map((n) => n.id).toList(),
            confidence: 0.80,
            estimatedBenefit: 'Improved organization and easier navigation',
            createdAt: DateTime.now(),
          ),
        );
      }
    });

    return suggestions;
  }

  Future<List<OrganizationSuggestion>> _suggestTags(List<Note> notes) async {
    final suggestions = <OrganizationSuggestion>[];
    final untaggedNotes = notes.where((n) => n.tags.isEmpty).toList();

    if (untaggedNotes.isNotEmpty) {
      for (final note in untaggedNotes.take(10)) {
        final suggestedTags = _generateTagsForNote(note);
        if (suggestedTags.isNotEmpty) {
          suggestions.add(
            OrganizationSuggestion(
              id: 'tags_${note.id}_${DateTime.now().millisecondsSinceEpoch}',
              type: SuggestionType.tags,
              title: 'Add Tags to "${note.title}"',
              description: 'Suggested tags: ${suggestedTags.join(', ')}',
              affectedNotes: [note.id],
              confidence: 0.75,
              estimatedBenefit: 'Better searchability and categorization',
              createdAt: DateTime.now(),
            ),
          );
        }
      }
    }

    return suggestions;
  }

  Future<List<OrganizationSuggestion>> _suggestLinks(List<Note> notes) async {
    final suggestions = <OrganizationSuggestion>[];

    // Find notes that could be linked
    for (int i = 0; i < notes.length; i++) {
      for (int j = i + 1; j < notes.length; j++) {
        final similarity = _calculateSimilarity(notes[i], notes[j]);
        if (similarity > 0.7) {
          suggestions.add(
            OrganizationSuggestion(
              id: 'link_${notes[i].id}_${notes[j].id}_${DateTime.now().millisecondsSinceEpoch}',
              type: SuggestionType.link,
              title: 'Link Related Notes',
              description:
                  'Connect "${notes[i].title}" and "${notes[j].title}"',
              affectedNotes: [notes[i].id, notes[j].id],
              confidence: similarity,
              estimatedBenefit: 'Enhanced knowledge connections',
              createdAt: DateTime.now(),
            ),
          );
        }
      }
    }

    return suggestions.take(5).toList(); // Limit to top 5 suggestions
  }

  Future<List<OrganizationSuggestion>> _suggestHierarchy(
    List<Note> notes,
  ) async {
    final suggestions = <OrganizationSuggestion>[];

    // Find potential parent-child relationships
    for (final note in notes) {
      final potentialChildren = notes
          .where(
            (n) =>
                n.id != note.id &&
                    n.title.toLowerCase().contains(note.title.toLowerCase()) ||
                _calculateSimilarity(note, n) > 0.8,
          )
          .toList();

      if (potentialChildren.length >= 2) {
        suggestions.add(
          OrganizationSuggestion(
            id: 'hierarchy_${note.id}_${DateTime.now().millisecondsSinceEpoch}',
            type: SuggestionType.hierarchy,
            title: 'Create Note Hierarchy',
            description:
                'Make "${note.title}" a parent of ${potentialChildren.length} related notes',
            affectedNotes: [note.id, ...potentialChildren.map((n) => n.id)],
            confidence: 0.70,
            estimatedBenefit: 'Clearer information structure',
            createdAt: DateTime.now(),
          ),
        );
      }
    }

    return suggestions;
  }

  // Content Enhancement Features (200 features)
  Future<List<ContentSuggestion>> generateContentSuggestions(Note note) async {
    final suggestions = <ContentSuggestion>[];

    // Grammar and style suggestions
    suggestions.addAll(await _analyzeGrammarAndStyle(note));

    // Content expansion suggestions
    suggestions.addAll(await _suggestContentExpansion(note));

    // Formatting suggestions
    suggestions.addAll(await _suggestFormatting(note));

    // Reference suggestions
    suggestions.addAll(await _suggestReferences(note));

    return suggestions;
  }

  Future<List<ContentSuggestion>> _analyzeGrammarAndStyle(Note note) async {
    final suggestions = <ContentSuggestion>[];

    // Check for common issues
    if (note.content.contains('  ')) {
      suggestions.add(
        ContentSuggestion(
          id: 'spacing_${note.id}_${DateTime.now().millisecondsSinceEpoch}',
          type: ContentSuggestionType.formatting,
          title: 'Fix Double Spaces',
          description: 'Remove extra spaces for cleaner formatting',
          originalText: '  ',
          suggestedText: ' ',
          confidence: 0.95,
          createdAt: DateTime.now(),
        ),
      );
    }

    // Check for missing punctuation
    final sentences = note.content
        .split('\n')
        .where((s) => s.trim().isNotEmpty);
    for (final sentence in sentences) {
      if (sentence.length > 10 &&
          !sentence.trim().endsWith('.') &&
          !sentence.trim().endsWith('!') &&
          !sentence.trim().endsWith('?')) {
        suggestions.add(
          ContentSuggestion(
            id: 'punctuation_${note.id}_${DateTime.now().millisecondsSinceEpoch}',
            type: ContentSuggestionType.grammar,
            title: 'Add Missing Punctuation',
            description: 'Consider adding punctuation to complete sentences',
            originalText: sentence,
            suggestedText: '${sentence.trim()}.',
            confidence: 0.80,
            createdAt: DateTime.now(),
          ),
        );
        break; // Only suggest one at a time
      }
    }

    return suggestions;
  }

  Future<List<ContentSuggestion>> _suggestContentExpansion(Note note) async {
    final suggestions = <ContentSuggestion>[];

    // Suggest expansion for short notes
    if (note.content.length < 100) {
      suggestions.add(
        ContentSuggestion(
          id: 'expand_${note.id}_${DateTime.now().millisecondsSinceEpoch}',
          type: ContentSuggestionType.expansion,
          title: 'Expand Content',
          description: 'Consider adding more details, examples, or context',
          originalText: note.content,
          suggestedText: '${note.content}\n\n[Add more details here]',
          confidence: 0.70,
          createdAt: DateTime.now(),
        ),
      );
    }

    // Suggest adding examples
    if (!note.content.toLowerCase().contains('example') &&
        !note.content.toLowerCase().contains('for instance')) {
      suggestions.add(
        ContentSuggestion(
          id: 'examples_${note.id}_${DateTime.now().millisecondsSinceEpoch}',
          type: ContentSuggestionType.enhancement,
          title: 'Add Examples',
          description: 'Include examples to illustrate key points',
          originalText: note.content,
          suggestedText: '${note.content}\n\nExample: [Add relevant example]',
          confidence: 0.65,
          createdAt: DateTime.now(),
        ),
      );
    }

    return suggestions;
  }

  Future<List<ContentSuggestion>> _suggestFormatting(Note note) async {
    final suggestions = <ContentSuggestion>[];

    // Suggest headers for long notes
    if (note.content.length > 500 && !note.content.contains('#')) {
      suggestions.add(
        ContentSuggestion(
          id: 'headers_${note.id}_${DateTime.now().millisecondsSinceEpoch}',
          type: ContentSuggestionType.formatting,
          title: 'Add Section Headers',
          description:
              'Break up long content with headers for better readability',
          originalText: note.content,
          suggestedText: '# ${note.title}\n\n${note.content}',
          confidence: 0.75,
          createdAt: DateTime.now(),
        ),
      );
    }

    // Suggest bullet points for lists
    if (note.content.contains('\n') &&
        !note.content.contains('•') &&
        !note.content.contains('-') &&
        !note.content.contains('*')) {
      final lines = note.content.split('\n');
      if (lines.length >= 3) {
        suggestions.add(
          ContentSuggestion(
            id: 'bullets_${note.id}_${DateTime.now().millisecondsSinceEpoch}',
            type: ContentSuggestionType.formatting,
            title: 'Use Bullet Points',
            description: 'Format list items with bullet points',
            originalText: note.content,
            suggestedText: lines
                .map((line) => line.trim().isEmpty ? line : '• $line')
                .join('\n'),
            confidence: 0.70,
            createdAt: DateTime.now(),
          ),
        );
      }
    }

    return suggestions;
  }

  Future<List<ContentSuggestion>> _suggestReferences(Note note) async {
    final suggestions = <ContentSuggestion>[];

    // Suggest adding sources for factual content
    if (note.content.length > 200 &&
        !note.content.toLowerCase().contains('source') &&
        !note.content.toLowerCase().contains('reference')) {
      suggestions.add(
        ContentSuggestion(
          id: 'references_${note.id}_${DateTime.now().millisecondsSinceEpoch}',
          type: ContentSuggestionType.enhancement,
          title: 'Add References',
          description: 'Include sources or references for factual information',
          originalText: note.content,
          suggestedText: '${note.content}\n\n## References\n[Add sources here]',
          confidence: 0.60,
          createdAt: DateTime.now(),
        ),
      );
    }

    return suggestions;
  }

  // Utility Methods
  List<String> _extractKeywords(String content) {
    final words = content.toLowerCase().split(RegExp(r'\W+'));
    final keywords = words
        .where((word) => word.length > 4 && !_isStopWord(word))
        .toSet()
        .toList();

    return keywords.take(10).toList();
  }

  bool _isStopWord(String word) {
    const stopWords = {
      'the',
      'and',
      'or',
      'but',
      'in',
      'on',
      'at',
      'to',
      'for',
      'of',
      'with',
      'by',
      'from',
      'up',
      'about',
      'into',
      'through',
      'during',
      'before',
      'after',
      'above',
      'below',
      'between',
      'among',
      'this',
      'that',
      'these',
      'those',
      'is',
      'are',
      'was',
      'were',
      'be',
      'been',
      'being',
      'have',
      'has',
      'had',
      'do',
      'does',
      'did',
      'will',
      'would',
      'could',
      'should',
    };
    return stopWords.contains(word);
  }

  List<String> _generateTagsForNote(Note note) {
    final keywords = _extractKeywords(note.content);
    return keywords.take(3).toList();
  }

  double _calculateSimilarity(Note note1, Note note2) {
    final words1 = note1.content.toLowerCase().split(RegExp(r'\W+'));
    final words2 = note2.content.toLowerCase().split(RegExp(r'\W+'));

    final set1 = words1.toSet();
    final set2 = words2.toSet();

    final intersection = set1.intersection(set2).length;
    final union = set1.union(set2).length;

    return union > 0 ? intersection / union : 0.0;
  }

  void dispose() {
    _insightController.close();
  }
}
