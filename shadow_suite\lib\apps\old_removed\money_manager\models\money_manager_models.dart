import 'dart:convert';

// Enums
enum AccountType { checking, savings, credit, cash, investment, loan }

enum TransactionType { income, expense, transfer }

enum CategoryType { income, expense }

enum BudgetPeriod { weekly, monthly, quarterly, yearly }

enum GoalType { saving, debt, investment, purchase }

// Account Model
class Account {
  final String id;
  final String name;
  final AccountType type;
  final double initialBalance;
  final double currentBalance;
  final String currency;
  final String color;
  final String icon;
  final bool showInTotal;
  final bool allowNegative;
  final DateTime createdAt;
  final DateTime lastModified;

  const Account({
    required this.id,
    required this.name,
    required this.type,
    required this.initialBalance,
    required this.currentBalance,
    this.currency = 'USD',
    this.color = '#3498DB',
    this.icon = 'account_balance_wallet',
    this.showInTotal = true,
    this.allowNegative = false,
    required this.createdAt,
    required this.lastModified,
  });

  Account copyWith({
    String? id,
    String? name,
    AccountType? type,
    double? initialBalance,
    double? currentBalance,
    String? currency,
    String? color,
    String? icon,
    bool? showInTotal,
    bool? allowNegative,
    DateTime? createdAt,
    DateTime? lastModified,
  }) {
    return Account(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      initialBalance: initialBalance ?? this.initialBalance,
      currentBalance: currentBalance ?? this.currentBalance,
      currency: currency ?? this.currency,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      showInTotal: showInTotal ?? this.showInTotal,
      allowNegative: allowNegative ?? this.allowNegative,
      createdAt: createdAt ?? this.createdAt,
      lastModified: lastModified ?? this.lastModified,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'initialBalance': initialBalance,
      'currentBalance': currentBalance,
      'currency': currency,
      'color': color,
      'icon': icon,
      'showInTotal': showInTotal,
      'allowNegative': allowNegative,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastModified': lastModified.millisecondsSinceEpoch,
    };
  }

  factory Account.fromMap(Map<String, dynamic> map) {
    return Account(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      type: AccountType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => AccountType.checking,
      ),
      initialBalance: map['initialBalance']?.toDouble() ?? 0.0,
      currentBalance: map['currentBalance']?.toDouble() ?? 0.0,
      currency: map['currency'] ?? 'USD',
      color: map['color'] ?? '#3498DB',
      icon: map['icon'] ?? 'account_balance_wallet',
      showInTotal: map['showInTotal'] ?? true,
      allowNegative: map['allowNegative'] ?? false,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      lastModified: DateTime.fromMillisecondsSinceEpoch(
        map['lastModified'] ?? 0,
      ),
    );
  }

  String toJson() => json.encode(toMap());
  factory Account.fromJson(String source) =>
      Account.fromMap(json.decode(source));
}

// Category Model
class Category {
  final String id;
  final String name;
  final CategoryType type;
  final String color;
  final String icon;
  final DateTime createdAt;

  const Category({
    required this.id,
    required this.name,
    required this.type,
    this.color = '#27AE60',
    this.icon = 'category',
    required this.createdAt,
  });

  Category copyWith({
    String? id,
    String? name,
    CategoryType? type,
    String? color,
    String? icon,
    DateTime? createdAt,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'color': color,
      'icon': icon,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  factory Category.fromMap(Map<String, dynamic> map) {
    return Category(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      type: CategoryType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => CategoryType.expense,
      ),
      color: map['color'] ?? '#27AE60',
      icon: map['icon'] ?? 'category',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
    );
  }

  String toJson() => json.encode(toMap());
  factory Category.fromJson(String source) =>
      Category.fromMap(json.decode(source));
}

// Transaction Model
class Transaction {
  final String id;
  final double amount;
  final TransactionType type;
  final String accountId;
  final String? toAccountId; // For transfers
  final String categoryId;
  final String description;
  final DateTime date;
  final List<String> tags;
  final String? receiptPath;
  final String? notes;
  final DateTime createdAt;

  const Transaction({
    required this.id,
    required this.amount,
    required this.type,
    required this.accountId,
    this.toAccountId,
    required this.categoryId,
    this.description = '',
    required this.date,
    this.tags = const [],
    this.receiptPath,
    this.notes,
    required this.createdAt,
  });

  Transaction copyWith({
    String? id,
    double? amount,
    TransactionType? type,
    String? accountId,
    String? toAccountId,
    String? categoryId,
    String? description,
    DateTime? date,
    List<String>? tags,
    String? receiptPath,
    String? notes,
    DateTime? createdAt,
  }) {
    return Transaction(
      id: id ?? this.id,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      accountId: accountId ?? this.accountId,
      toAccountId: toAccountId ?? this.toAccountId,
      categoryId: categoryId ?? this.categoryId,
      description: description ?? this.description,
      date: date ?? this.date,
      tags: tags ?? this.tags,
      receiptPath: receiptPath ?? this.receiptPath,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'amount': amount,
      'type': type.name,
      'accountId': accountId,
      'toAccountId': toAccountId,
      'categoryId': categoryId,
      'description': description,
      'date': date.millisecondsSinceEpoch,
      'tags': tags,
      'receiptPath': receiptPath,
      'notes': notes,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  factory Transaction.fromMap(Map<String, dynamic> map) {
    return Transaction(
      id: map['id'] ?? '',
      amount: map['amount']?.toDouble() ?? 0.0,
      type: TransactionType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => TransactionType.expense,
      ),
      accountId: map['accountId'] ?? '',
      toAccountId: map['toAccountId'],
      categoryId: map['categoryId'] ?? '',
      description: map['description'] ?? '',
      date: DateTime.fromMillisecondsSinceEpoch(map['date'] ?? 0),
      tags: List<String>.from(map['tags'] ?? []),
      receiptPath: map['receiptPath'],
      notes: map['notes'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
    );
  }

  String toJson() => json.encode(toMap());
  factory Transaction.fromJson(String source) =>
      Transaction.fromMap(json.decode(source));
}

// Budget Model
class Budget {
  final String id;
  final String name;
  final String categoryId;
  final double amount;
  final double spent;
  final BudgetPeriod period;
  final DateTime startDate;
  final DateTime endDate;
  final bool alertEnabled;
  final double alertThreshold; // Percentage (0.0 - 1.0)
  final DateTime createdAt;

  const Budget({
    required this.id,
    required this.name,
    required this.categoryId,
    required this.amount,
    this.spent = 0.0,
    required this.period,
    required this.startDate,
    required this.endDate,
    this.alertEnabled = true,
    this.alertThreshold = 0.8,
    required this.createdAt,
  });

  Budget copyWith({
    String? id,
    String? name,
    String? categoryId,
    double? amount,
    double? spent,
    BudgetPeriod? period,
    DateTime? startDate,
    DateTime? endDate,
    bool? alertEnabled,
    double? alertThreshold,
    DateTime? createdAt,
  }) {
    return Budget(
      id: id ?? this.id,
      name: name ?? this.name,
      categoryId: categoryId ?? this.categoryId,
      amount: amount ?? this.amount,
      spent: spent ?? this.spent,
      period: period ?? this.period,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      alertEnabled: alertEnabled ?? this.alertEnabled,
      alertThreshold: alertThreshold ?? this.alertThreshold,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  double get progress => amount > 0 ? (spent / amount).clamp(0.0, 1.0) : 0.0;
  double get remaining => (amount - spent).clamp(0.0, double.infinity);
  bool get isOverBudget => spent > amount;
  bool get shouldAlert => alertEnabled && progress >= alertThreshold;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'categoryId': categoryId,
      'amount': amount,
      'spent': spent,
      'period': period.name,
      'startDate': startDate.millisecondsSinceEpoch,
      'endDate': endDate.millisecondsSinceEpoch,
      'alertEnabled': alertEnabled,
      'alertThreshold': alertThreshold,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  factory Budget.fromMap(Map<String, dynamic> map) {
    return Budget(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      categoryId: map['categoryId'] ?? '',
      amount: map['amount']?.toDouble() ?? 0.0,
      spent: map['spent']?.toDouble() ?? 0.0,
      period: BudgetPeriod.values.firstWhere(
        (e) => e.name == map['period'],
        orElse: () => BudgetPeriod.monthly,
      ),
      startDate: DateTime.fromMillisecondsSinceEpoch(map['startDate'] ?? 0),
      endDate: DateTime.fromMillisecondsSinceEpoch(map['endDate'] ?? 0),
      alertEnabled: map['alertEnabled'] ?? true,
      alertThreshold: map['alertThreshold']?.toDouble() ?? 0.8,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
    );
  }

  String toJson() => json.encode(toMap());
  factory Budget.fromJson(String source) => Budget.fromMap(json.decode(source));
}

// Goal Model
class Goal {
  final String id;
  final String name;
  final GoalType type;
  final double targetAmount;
  final double currentAmount;
  final String? accountId;
  final String? categoryId;
  final DateTime targetDate;
  final String description;
  final String color;
  final String icon;
  final DateTime createdAt;

  const Goal({
    required this.id,
    required this.name,
    required this.type,
    required this.targetAmount,
    this.currentAmount = 0.0,
    this.accountId,
    this.categoryId,
    required this.targetDate,
    this.description = '',
    this.color = '#E74C3C',
    this.icon = 'flag',
    required this.createdAt,
  });

  Goal copyWith({
    String? id,
    String? name,
    GoalType? type,
    double? targetAmount,
    double? currentAmount,
    String? accountId,
    String? categoryId,
    DateTime? targetDate,
    String? description,
    String? color,
    String? icon,
    DateTime? createdAt,
  }) {
    return Goal(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      targetAmount: targetAmount ?? this.targetAmount,
      currentAmount: currentAmount ?? this.currentAmount,
      accountId: accountId ?? this.accountId,
      categoryId: categoryId ?? this.categoryId,
      targetDate: targetDate ?? this.targetDate,
      description: description ?? this.description,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  double get progress =>
      targetAmount > 0 ? (currentAmount / targetAmount).clamp(0.0, 1.0) : 0.0;
  double get remaining =>
      (targetAmount - currentAmount).clamp(0.0, double.infinity);
  bool get isCompleted => currentAmount >= targetAmount;
  bool get isOverdue => DateTime.now().isAfter(targetDate) && !isCompleted;
  int get daysRemaining => targetDate.difference(DateTime.now()).inDays;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'targetAmount': targetAmount,
      'currentAmount': currentAmount,
      'accountId': accountId,
      'categoryId': categoryId,
      'targetDate': targetDate.millisecondsSinceEpoch,
      'description': description,
      'color': color,
      'icon': icon,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  factory Goal.fromMap(Map<String, dynamic> map) {
    return Goal(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      type: GoalType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => GoalType.saving,
      ),
      targetAmount: map['targetAmount']?.toDouble() ?? 0.0,
      currentAmount: map['currentAmount']?.toDouble() ?? 0.0,
      accountId: map['accountId'],
      categoryId: map['categoryId'],
      targetDate: DateTime.fromMillisecondsSinceEpoch(map['targetDate'] ?? 0),
      description: map['description'] ?? '',
      color: map['color'] ?? '#E74C3C',
      icon: map['icon'] ?? 'flag',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
    );
  }

  String toJson() => json.encode(toMap());
  factory Goal.fromJson(String source) => Goal.fromMap(json.decode(source));
}

// Additional models for financial analytics

/// Analytics event types
enum AnalyticsEventType {
  reportGenerationStarted,
  reportGenerated,
  reportGenerationFailed,
}

/// Trend directions
enum TrendDirection { increasing, decreasing, stable }

/// Insight types
enum InsightType { positive, warning, info, critical }

/// Insight impact levels
enum InsightImpact { low, medium, high, critical }

/// Recommendation types
enum RecommendationType { savings, debt, investment, budget, expense }

/// Recommendation priorities
enum RecommendationPriority { low, medium, high, urgent }

/// Risk levels
enum RiskLevel { low, moderate, high }

/// Report types
enum ReportType { comprehensive, income, expense, budget, cashFlow, investment }

/// Date range model
class DateRange {
  final DateTime startDate;
  final DateTime endDate;

  const DateRange({required this.startDate, required this.endDate});
}

/// Seasonal pattern
class SeasonalPattern {
  final int month;
  final String pattern;
  final double amount;
  final double variance;

  const SeasonalPattern({
    required this.month,
    required this.pattern,
    required this.amount,
    required this.variance,
  });
}

/// Recurring expense
class RecurringExpense {
  final String description;
  final double averageAmount;
  final String frequency;
  final DateTime lastOccurrence;
  final DateTime nextExpected;
  final double confidence;

  const RecurringExpense({
    required this.description,
    required this.averageAmount,
    required this.frequency,
    required this.lastOccurrence,
    required this.nextExpected,
    required this.confidence,
  });
}

/// Expense item
class ExpenseItem {
  final String description;
  final double amount;
  final String category;
  final DateTime date;

  const ExpenseItem({
    required this.description,
    required this.amount,
    required this.category,
    required this.date,
  });
}

/// Budget performance
class BudgetPerformance {
  final Budget budget;
  final double spent;
  final double remaining;
  final double percentageUsed;
  final bool isOnTrack;
  final int daysRemaining;
  final double projectedSpending;

  const BudgetPerformance({
    required this.budget,
    required this.spent,
    required this.remaining,
    required this.percentageUsed,
    required this.isOnTrack,
    required this.daysRemaining,
    required this.projectedSpending,
  });
}

/// Account performance
class AccountPerformance {
  final Account account;
  final double currentBalance;
  final double monthlyChange;
  final int transactionCount;
  final double averageTransactionAmount;
  final DateTime? lastTransactionDate;

  const AccountPerformance({
    required this.account,
    required this.currentBalance,
    required this.monthlyChange,
    required this.transactionCount,
    required this.averageTransactionAmount,
    this.lastTransactionDate,
  });
}

/// Monthly data for trends
class MonthlyData {
  final String month;
  double income;
  double expenses;
  double netFlow;
  int transactionCount;

  MonthlyData({
    required this.month,
    this.income = 0,
    this.expenses = 0,
    this.netFlow = 0,
    this.transactionCount = 0,
  });
}

/// Category data
class CategoryData {
  final String category;
  double totalSpent;
  int transactionCount;
  double averageAmount;
  double percentage;
  TrendDirection trend;

  CategoryData({
    required this.category,
    this.totalSpent = 0,
    this.transactionCount = 0,
    this.averageAmount = 0,
    this.percentage = 0,
    this.trend = TrendDirection.stable,
  });
}

/// Cash flow data
class CashFlowData {
  final DateTime date;
  final double inflow;
  final double outflow;
  final double netFlow;
  final double runningBalance;

  const CashFlowData({
    required this.date,
    required this.inflow,
    required this.outflow,
    required this.netFlow,
    required this.runningBalance,
  });
}

/// Financial insight
class FinancialInsight {
  final InsightType type;
  final String title;
  final String description;
  final InsightImpact impact;
  final bool actionable;
  final String? recommendation;

  const FinancialInsight({
    required this.type,
    required this.title,
    required this.description,
    required this.impact,
    required this.actionable,
    this.recommendation,
  });
}

/// Financial recommendation
class FinancialRecommendation {
  final RecommendationType type;
  final String title;
  final String description;
  final RecommendationPriority priority;
  final String estimatedImpact;
  final List<String> actionSteps;

  const FinancialRecommendation({
    required this.type,
    required this.title,
    required this.description,
    required this.priority,
    required this.estimatedImpact,
    required this.actionSteps,
  });
}

/// Analytics event
class AnalyticsEvent {
  final AnalyticsEventType type;
  final String message;
  final DateTime timestamp;

  const AnalyticsEvent({
    required this.type,
    required this.message,
    required this.timestamp,
  });
}

/// Income analysis result
class IncomeAnalysis {
  final double totalIncome;
  final double averageMonthlyIncome;
  final double incomeGrowthRate;
  final String primaryIncomeSource;
  final double incomeStability;
  final List<SeasonalPattern> seasonalPatterns;
  final Map<String, double> incomeBySource;
  final Map<String, double> monthlyIncome;

  const IncomeAnalysis({
    required this.totalIncome,
    required this.averageMonthlyIncome,
    required this.incomeGrowthRate,
    required this.primaryIncomeSource,
    required this.incomeStability,
    required this.seasonalPatterns,
    required this.incomeBySource,
    required this.monthlyIncome,
  });
}

/// Expense analysis result
class ExpenseAnalysis {
  final double totalExpenses;
  final double averageMonthlyExpenses;
  final double expenseGrowthRate;
  final String largestExpenseCategory;
  final Map<String, double> expensesByCategory;
  final Map<String, double> monthlyExpenses;
  final List<ExpenseItem> topExpenses;
  final List<RecurringExpense> recurringExpenses;

  const ExpenseAnalysis({
    required this.totalExpenses,
    required this.averageMonthlyExpenses,
    required this.expenseGrowthRate,
    required this.largestExpenseCategory,
    required this.expensesByCategory,
    required this.monthlyExpenses,
    required this.topExpenses,
    required this.recurringExpenses,
  });
}

/// Budget analysis result
class BudgetAnalysis {
  final List<BudgetPerformance> budgetPerformance;
  final double totalBudgeted;
  final double totalSpent;
  final int budgetsOnTrack;
  final int budgetsOverspent;
  final double overallBudgetHealth;
  final double budgetVariance;

  const BudgetAnalysis({
    required this.budgetPerformance,
    required this.totalBudgeted,
    required this.totalSpent,
    required this.budgetsOnTrack,
    required this.budgetsOverspent,
    required this.overallBudgetHealth,
    required this.budgetVariance,
  });
}

/// Account analysis result
class AccountAnalysis {
  final List<AccountPerformance> accountPerformance;
  final double totalBalance;
  final double totalAssets;
  final double totalLiabilities;
  final double netWorth;
  final double debtToAssetRatio;
  final int accountCount;

  const AccountAnalysis({
    required this.accountPerformance,
    required this.totalBalance,
    required this.totalAssets,
    required this.totalLiabilities,
    required this.netWorth,
    required this.debtToAssetRatio,
    required this.accountCount,
  });
}

/// Trend analysis result
class TrendAnalysis {
  final List<MonthlyData> monthlyData;
  final TrendDirection incomeTrend;
  final TrendDirection expenseTrend;
  final TrendDirection netFlowTrend;
  final double volatility;
  final bool seasonality;

  const TrendAnalysis({
    required this.monthlyData,
    required this.incomeTrend,
    required this.expenseTrend,
    required this.netFlowTrend,
    required this.volatility,
    required this.seasonality,
  });
}

/// Category analysis result
class CategoryAnalysis {
  final List<CategoryData> categoryData;
  final List<CategoryData> topCategories;
  final int categoryCount;
  final double averageSpendingPerCategory;

  const CategoryAnalysis({
    required this.categoryData,
    required this.topCategories,
    required this.categoryCount,
    required this.averageSpendingPerCategory,
  });
}

/// Cash flow analysis result
class CashFlowAnalysis {
  final List<CashFlowData> dailyCashFlow;
  final double averageDailyFlow;
  final double cashFlowVolatility;
  final int positiveFlowDays;
  final int negativeFlowDays;
  final int longestPositiveStreak;
  final int longestNegativeStreak;

  const CashFlowAnalysis({
    required this.dailyCashFlow,
    required this.averageDailyFlow,
    required this.cashFlowVolatility,
    required this.positiveFlowDays,
    required this.negativeFlowDays,
    required this.longestPositiveStreak,
    required this.longestNegativeStreak,
  });
}

/// Savings analysis result
class SavingsAnalysis {
  final double totalSavings;
  final double savingsRate;
  final double averageMonthlySavings;
  final int savingsAccounts;
  final Map<String, double> monthlySavings;
  final double savingsGrowthRate;

  const SavingsAnalysis({
    required this.totalSavings,
    required this.savingsRate,
    required this.averageMonthlySavings,
    required this.savingsAccounts,
    required this.monthlySavings,
    required this.savingsGrowthRate,
  });
}

/// Investment analysis result
class InvestmentAnalysis {
  final double totalInvestments;
  final int investmentAccounts;
  final double averageReturn;
  final RiskLevel riskLevel;
  final double diversificationScore;
  final Map<String, double> recommendedAllocation;

  const InvestmentAnalysis({
    required this.totalInvestments,
    required this.investmentAccounts,
    required this.averageReturn,
    required this.riskLevel,
    required this.diversificationScore,
    required this.recommendedAllocation,
  });
}

/// Debt analysis result
class DebtAnalysis {
  final double totalDebt;
  final int debtAccounts;
  final double averageInterestRate;
  final double monthlyPayments;
  final Map<String, DateTime> payoffTimeline;
  final double debtToIncomeRatio;

  const DebtAnalysis({
    required this.totalDebt,
    required this.debtAccounts,
    required this.averageInterestRate,
    required this.monthlyPayments,
    required this.payoffTimeline,
    required this.debtToIncomeRatio,
  });
}

/// Financial report
class FinancialReport {
  final String id;
  final ReportType reportType;
  final DateRange dateRange;
  final DateTime generatedAt;
  final IncomeAnalysis incomeAnalysis;
  final ExpenseAnalysis expenseAnalysis;
  final BudgetAnalysis budgetAnalysis;
  final AccountAnalysis accountAnalysis;
  final TrendAnalysis trendAnalysis;
  final CategoryAnalysis categoryAnalysis;
  final CashFlowAnalysis cashFlowAnalysis;
  final SavingsAnalysis savingsAnalysis;
  final InvestmentAnalysis investmentAnalysis;
  final DebtAnalysis debtAnalysis;
  final List<FinancialInsight> insights;
  final List<FinancialRecommendation> recommendations;

  const FinancialReport({
    required this.id,
    required this.reportType,
    required this.dateRange,
    required this.generatedAt,
    required this.incomeAnalysis,
    required this.expenseAnalysis,
    required this.budgetAnalysis,
    required this.accountAnalysis,
    required this.trendAnalysis,
    required this.categoryAnalysis,
    required this.cashFlowAnalysis,
    required this.savingsAnalysis,
    required this.investmentAnalysis,
    required this.debtAnalysis,
    required this.insights,
    required this.recommendations,
  });
}
