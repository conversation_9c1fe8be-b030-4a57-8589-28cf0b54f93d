import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Import all app screens
import '../../apps/tools_builder/screens/tools_builder_home.dart';
import '../../apps/unified_finance/unified_finance_main.dart';
import '../../apps/hadith_app/hadith_app_main.dart';
import '../../apps/todo_app/todo_app_main.dart';
import '../../apps/voice_recorder/voice_recorder_main.dart';
import '../../apps/athkar_app/athkar_app_main.dart';
import '../../apps/notes_app/notes_app_main.dart';
import '../../apps/file_manager/file_manager_main.dart';
import '../../apps/islamic_app/islamic_app_main.dart';
import '../../apps/islamic_app/services/islamic_providers.dart';

class AppInfo {
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final Widget Function(BuildContext) builder;
  final int number;
  final bool isImplemented;

  AppInfo({
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.builder,
    required this.number,
    this.isImplemented = true,
  });
}

/// Modern Shadow Suite Launcher with Sidebar and Dashboard
class NewShadowSuiteLauncher extends ConsumerStatefulWidget {
  const NewShadowSuiteLauncher({super.key});

  @override
  ConsumerState<NewShadowSuiteLauncher> createState() =>
      _NewShadowSuiteLauncherState();
}

class _NewShadowSuiteLauncherState
    extends ConsumerState<NewShadowSuiteLauncher> {
  int _selectedIndex = -1; // -1 means dashboard view

  final List<AppInfo> _apps = [
    AppInfo(
      number: 1,
      name: 'Quran App',
      description: 'Holy Quran reading, search, and study',
      icon: Icons.book,
      color: Colors.green,
      builder: (context) => const QuranAppWrapper(),
    ),
    AppInfo(
      number: 2,
      name: 'Finance Manager',
      description: 'Comprehensive financial management',
      icon: Icons.account_balance,
      color: Colors.blue,
      builder: (context) => const UnifiedFinanceMain(),
    ),
    AppInfo(
      number: 3,
      name: 'Tools Builder',
      description: 'Create custom tools and spreadsheets',
      icon: Icons.build,
      color: Colors.orange,
      builder: (context) => const ToolsBuilderHome(),
    ),
    AppInfo(
      number: 4,
      name: 'File Manager',
      description: 'Advanced file management',
      icon: Icons.folder,
      color: Colors.brown,
      builder: (context) => const FileManagerMain(),
    ),
    AppInfo(
      number: 5,
      name: 'Islamic App',
      description: 'Islamic tools and resources',
      icon: Icons.mosque,
      color: Colors.deepPurple,
      builder: (context) => const IslamicAppMain(),
    ),
    AppInfo(
      number: 6,
      name: 'Hadith App',
      description: 'Hadith collection and study',
      icon: Icons.menu_book,
      color: Colors.deepOrange,
      builder: (context) => const HadithAppMain(),
    ),
    AppInfo(
      number: 10,
      name: 'Todo App',
      description: 'Task management',
      icon: Icons.check_circle,
      color: Colors.red,
      builder: (context) => const TodoAppMain(),
    ),
    AppInfo(
      number: 11,
      name: 'Voice Recorder',
      description: 'Audio recording and playback',
      icon: Icons.mic,
      color: Colors.blueGrey,
      builder: (context) => const VoiceRecorderMain(),
    ),
    AppInfo(
      number: 12,
      name: 'Athkar App',
      description: 'Islamic remembrance and dhikr',
      icon: Icons.beenhere,
      color: Colors.green,
      builder: (context) => const AthkarAppMain(),
    ),
    AppInfo(
      number: 13,
      name: 'Notes App',
      description: 'Note taking and organization',
      icon: Icons.note,
      color: Colors.amber,
      builder: (context) => const NotesAppMain(),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      drawer: _buildSidebar(),
      body: _selectedIndex == -1
          ? _buildDashboard()
          : _apps[_selectedIndex].builder(context),
      floatingActionButton: _selectedIndex != -1
          ? _buildBackToHomeButton()
          : null,
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        _selectedIndex == -1
            ? 'Shadow Suite Dashboard'
            : _apps[_selectedIndex].name,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      backgroundColor: Colors.white,
      foregroundColor: Colors.black87,
      elevation: 1,
      centerTitle: true,
      actions: [
        IconButton(
          icon: const Icon(Icons.settings),
          onPressed: () {
            // TODO: Open settings
          },
        ),
      ],
    );
  }

  Widget _buildSidebar() {
    return Drawer(
      child: Column(
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue, Colors.purple],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Icon(Icons.dashboard, size: 48, color: Colors.white),
                SizedBox(height: 8),
                Text(
                  'Shadow Suite',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '12 Applications',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Colors.white70),
                ),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.dashboard),
            title: const Text('Dashboard'),
            selected: _selectedIndex == -1,
            onTap: () {
              setState(() {
                _selectedIndex = -1;
              });
              Navigator.pop(context);
            },
          ),
          const Divider(),
          Expanded(
            child: ListView.builder(
              itemCount: _apps.length,
              itemBuilder: (context, index) {
                final app = _apps[index];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: app.color.withOpacity(0.1),
                    child: Text(
                      '${app.number}',
                      style: TextStyle(
                        color: app.color,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  title: Text(
                    app.name,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: app.isImplemented
                          ? FontWeight.normal
                          : FontWeight.w300,
                    ),
                  ),
                  subtitle: Text(
                    app.description,
                    style: const TextStyle(fontSize: 12),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  trailing: app.isImplemented
                      ? Icon(
                          Icons.arrow_forward_ios,
                          size: 16,
                          color: Colors.grey[400],
                        )
                      : const Icon(
                          Icons.schedule,
                          size: 16,
                          color: Colors.orange,
                        ),
                  selected: _selectedIndex == index,
                  onTap: () {
                    setState(() {
                      _selectedIndex = index;
                    });
                    Navigator.pop(context);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboard() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(),
          const SizedBox(height: 24),
          _buildStatsSection(),
          const SizedBox(height: 24),
          _buildQuickAccessSection(),
          const SizedBox(height: 24),
          _buildAllAppsSection(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Colors.blue, Colors.purple],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Welcome to Shadow Suite',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your comprehensive productivity and entertainment platform',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.white70),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              const Icon(Icons.apps, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Text(
                '${_apps.length} Applications Available',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection() {
    final implementedApps = _apps.where((app) => app.isImplemented).length;
    final comingSoonApps = _apps.length - implementedApps;

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Ready to Use',
            implementedApps.toString(),
            Icons.check_circle,
            Colors.green,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            'Coming Soon',
            comingSoonApps.toString(),
            Icons.schedule,
            Colors.orange,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            'Total Apps',
            _apps.length.toString(),
            Icons.apps,
            Colors.blue,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAccessSection() {
    final quickAccessApps = _apps
        .where((app) => app.isImplemented)
        .take(4)
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Access',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.5,
          ),
          itemCount: quickAccessApps.length,
          itemBuilder: (context, index) {
            final app = quickAccessApps[index];
            return _buildQuickAccessCard(app, index);
          },
        ),
      ],
    );
  }

  Widget _buildQuickAccessCard(AppInfo app, int index) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedIndex = _apps.indexOf(app);
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: app.color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(app.icon, color: app.color, size: 24),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: app.color,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    '${app.number}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              app.name,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              app.description,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAllAppsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'All Applications',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: MediaQuery.of(context).size.width > 600 ? 4 : 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.1,
          ),
          itemCount: _apps.length,
          itemBuilder: (context, index) {
            final app = _apps[index];
            return _buildAppCard(app, index);
          },
        ),
      ],
    );
  }

  Widget _buildAppCard(AppInfo app, int index) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedIndex = index;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: app.isImplemented
              ? null
              : Border.all(color: Colors.orange.withOpacity(0.3)),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: app.color,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${app.number}',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
            const SizedBox(height: 8),
            Stack(
              children: [
                Icon(
                  app.icon,
                  size: 32,
                  color: app.isImplemented ? app.color : Colors.grey[400],
                ),
                if (!app.isImplemented)
                  Positioned(
                    right: -2,
                    top: -2,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: const BoxDecoration(
                        color: Colors.orange,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.schedule,
                        size: 12,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              app.name,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: app.isImplemented ? Colors.black87 : Colors.grey[600],
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              app.description,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackToHomeButton() {
    return FloatingActionButton(
      onPressed: () {
        setState(() {
          _selectedIndex = -1;
        });
      },
      backgroundColor: Colors.blue,
      child: const Icon(Icons.home, color: Colors.white),
    );
  }

  static Widget _buildPlaceholderApp(
    BuildContext context,
    String appName,
    String message,
  ) {
    return Scaffold(
      appBar: AppBar(
        title: Text(appName),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.schedule, size: 64, color: Colors.orange),
            const SizedBox(height: 16),
            Text(
              message,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'This application is under development',
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}

/// Wrapper class to navigate directly to Quran functionality
class QuranAppWrapper extends ConsumerStatefulWidget {
  const QuranAppWrapper({super.key});

  @override
  ConsumerState<QuranAppWrapper> createState() => _QuranAppWrapperState();
}

class _QuranAppWrapperState extends ConsumerState<QuranAppWrapper> {
  @override
  void initState() {
    super.initState();
    // Navigate to Quran section when this wrapper is created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(islamicAppCurrentScreenProvider.notifier).state =
          IslamicAppScreen.surahList;
    });
  }

  @override
  Widget build(BuildContext context) {
    return const IslamicAppMain();
  }
}
