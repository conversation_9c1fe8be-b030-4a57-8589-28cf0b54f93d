import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/finance_models.dart';

class CategoriesScreen extends ConsumerWidget {
  const CategoriesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Categories'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () => _showAddCategoryDialog(context),
            icon: const Icon(Icons.add),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCategorySection(
              context,
              'Income Categories',
              _getIncomeCategories(),
              Colors.green,
            ),
            const SizedBox(height: 24),
            _buildCategorySection(
              context,
              'Expense Categories',
              _getExpenseCategories(),
              Colors.red,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategorySection(
    BuildContext context,
    String title,
    List<TransactionCategory> categories,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 3,
          ),
          itemCount: categories.length,
          itemBuilder: (context, index) {
            return _buildCategoryCard(context, categories[index], color);
          },
        ),
      ],
    );
  }

  Widget _buildCategoryCard(
    BuildContext context,
    TransactionCategory category,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => _showCategoryDetails(context, category),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(_getCategoryIcon(category), color: color, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _getCategoryDisplayName(category),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      '${_getCategoryTransactionCount(category)} transactions',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                  ],
                ),
              ),
              PopupMenuButton<String>(
                onSelected: (value) =>
                    _handleCategoryAction(context, value, category),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'view',
                    child: Text('View Transactions'),
                  ),
                  const PopupMenuItem(
                    value: 'edit',
                    child: Text('Edit Category'),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Text('Delete Category'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<TransactionCategory> _getIncomeCategories() {
    return [
      TransactionCategory.salary,
      TransactionCategory.freelance,
      TransactionCategory.investment,
      TransactionCategory.gift,
      TransactionCategory.otherIncome,
    ];
  }

  List<TransactionCategory> _getExpenseCategories() {
    return [
      TransactionCategory.food,
      TransactionCategory.transportation,
      TransactionCategory.entertainment,
      TransactionCategory.shopping,
      TransactionCategory.utilities,
      TransactionCategory.healthcare,
      TransactionCategory.education,
      TransactionCategory.travel,
      TransactionCategory.insurance,
      TransactionCategory.taxes,
      TransactionCategory.otherExpense,
    ];
  }

  IconData _getCategoryIcon(TransactionCategory category) {
    switch (category) {
      case TransactionCategory.salary:
        return Icons.work;
      case TransactionCategory.freelance:
        return Icons.laptop;
      case TransactionCategory.investment:
        return Icons.trending_up;
      case TransactionCategory.gift:
        return Icons.card_giftcard;
      case TransactionCategory.food:
        return Icons.restaurant;
      case TransactionCategory.transportation:
        return Icons.directions_car;
      case TransactionCategory.entertainment:
        return Icons.movie;
      case TransactionCategory.shopping:
        return Icons.shopping_bag;
      case TransactionCategory.utilities:
        return Icons.electrical_services;
      case TransactionCategory.healthcare:
        return Icons.local_hospital;
      case TransactionCategory.education:
        return Icons.school;
      case TransactionCategory.travel:
        return Icons.flight;
      case TransactionCategory.insurance:
        return Icons.security;
      case TransactionCategory.taxes:
        return Icons.receipt;
      default:
        return Icons.category;
    }
  }

  String _getCategoryDisplayName(TransactionCategory category) {
    switch (category) {
      case TransactionCategory.otherIncome:
        return 'Other Income';
      case TransactionCategory.otherExpense:
        return 'Other Expense';
      default:
        return category.name[0].toUpperCase() + category.name.substring(1);
    }
  }

  int _getCategoryTransactionCount(TransactionCategory category) {
    // This would normally query the database for transaction count
    // For now, return a mock count
    return (category.index * 3) + 5;
  }

  void _showCategoryDetails(
    BuildContext context,
    TransactionCategory category,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(_getCategoryDisplayName(category)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Total Transactions: ${_getCategoryTransactionCount(category)}',
            ),
            const SizedBox(height: 8),
            Text(
              'Category Type: ${_isIncomeCategory(category) ? 'Income' : 'Expense'}',
            ),
            const SizedBox(height: 16),
            const Text(
              'Recent transactions in this category will be shown here.',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showCategoryTransactions(context, category);
            },
            child: const Text('View All'),
          ),
        ],
      ),
    );
  }

  void _showAddCategoryDialog(BuildContext context) {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    TransactionType selectedType = TransactionType.expense;
    Color selectedColor = Colors.blue;
    IconData selectedIcon = Icons.category;

    final availableColors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
      Colors.amber,
      Colors.cyan,
      Colors.lime,
      Colors.deepOrange,
    ];

    final availableIcons = [
      Icons.category,
      Icons.shopping_cart,
      Icons.restaurant,
      Icons.local_gas_station,
      Icons.home,
      Icons.medical_services,
      Icons.school,
      Icons.work,
      Icons.flight,
      Icons.sports,
      Icons.movie,
      Icons.fitness_center,
      Icons.pets,
      Icons.child_care,
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Custom Category'),
        content: SizedBox(
          width: 400,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Category Name',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.label),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description (Optional)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.description),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),
                StatefulBuilder(
                  builder: (context, setState) => Column(
                    children: [
                      DropdownButtonFormField<TransactionType>(
                        value: selectedType,
                        decoration: const InputDecoration(
                          labelText: 'Category Type',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.type_specimen),
                        ),
                        items: [
                          const DropdownMenuItem(
                            value: TransactionType.income,
                            child: Text('Income'),
                          ),
                          const DropdownMenuItem(
                            value: TransactionType.expense,
                            child: Text('Expense'),
                          ),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            setState(() => selectedType = value);
                          }
                        },
                      ),
                      const SizedBox(height: 16),
                      // Color Selection
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Category Color',
                              style: TextStyle(fontWeight: FontWeight.w500),
                            ),
                            const SizedBox(height: 8),
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: availableColors.map((color) {
                                return GestureDetector(
                                  onTap: () =>
                                      setState(() => selectedColor = color),
                                  child: Container(
                                    width: 40,
                                    height: 40,
                                    decoration: BoxDecoration(
                                      color: color,
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: selectedColor == color
                                            ? Colors.black
                                            : Colors.transparent,
                                        width: 3,
                                      ),
                                    ),
                                    child: selectedColor == color
                                        ? const Icon(
                                            Icons.check,
                                            color: Colors.white,
                                            size: 20,
                                          )
                                        : null,
                                  ),
                                );
                              }).toList(),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Icon Selection
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Category Icon',
                              style: TextStyle(fontWeight: FontWeight.w500),
                            ),
                            const SizedBox(height: 8),
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: availableIcons.map((icon) {
                                return GestureDetector(
                                  onTap: () =>
                                      setState(() => selectedIcon = icon),
                                  child: Container(
                                    width: 50,
                                    height: 50,
                                    decoration: BoxDecoration(
                                      color: selectedIcon == icon
                                          ? selectedColor.withValues(alpha: 0.2)
                                          : Colors.grey.shade100,
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: selectedIcon == icon
                                            ? selectedColor
                                            : Colors.grey.shade300,
                                        width: 2,
                                      ),
                                    ),
                                    child: Icon(
                                      icon,
                                      color: selectedIcon == icon
                                          ? selectedColor
                                          : Colors.grey.shade600,
                                      size: 24,
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Please enter category name')),
                );
                return;
              }

              // Here you would typically save the custom category to the database
              // For now, we'll show a success message
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Custom category "${nameController.text}" created successfully',
                  ),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  void _handleCategoryAction(
    BuildContext context,
    String action,
    TransactionCategory category,
  ) {
    switch (action) {
      case 'view':
        _showCategoryTransactions(context, category);
        break;
      case 'edit':
        _editCategory(context, category);
        break;
      case 'delete':
        _deleteCategory(context, category);
        break;
    }
  }

  void _showCategoryTransactions(
    BuildContext context,
    TransactionCategory category,
  ) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Showing transactions for ${_getCategoryDisplayName(category)}',
        ),
      ),
    );
  }

  void _editCategory(BuildContext context, TransactionCategory category) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit category functionality coming soon')),
    );
  }

  void _deleteCategory(BuildContext context, TransactionCategory category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Category'),
        content: Text(
          'Are you sure you want to delete ${_getCategoryDisplayName(category)}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Default categories cannot be deleted'),
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  bool _isIncomeCategory(TransactionCategory category) {
    return [
      TransactionCategory.salary,
      TransactionCategory.freelance,
      TransactionCategory.investment,
      TransactionCategory.gift,
      TransactionCategory.otherIncome,
    ].contains(category);
  }
}
