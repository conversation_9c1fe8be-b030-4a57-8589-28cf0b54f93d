import 'package:uuid/uuid.dart';

enum NoteType {
  plainText('Plain Text'),
  checklist('Checklist'),
  canvas('Canvas Drawing');

  const NoteType(this.displayName);
  final String displayName;
}

class ChecklistItem {
  final String id;
  final String text;
  final bool isCompleted;

  ChecklistItem({String? id, required this.text, this.isCompleted = false})
    : id = id ?? const Uuid().v4();

  ChecklistItem copyWith({String? text, bool? isCompleted}) {
    return ChecklistItem(
      id: id,
      text: text ?? this.text,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'text': text, 'isCompleted': isCompleted};
  }

  factory ChecklistItem.fromJson(Map<String, dynamic> json) {
    return ChecklistItem(
      id: json['id'],
      text: json['text'],
      isCompleted: json['isCompleted'] ?? false,
    );
  }
}

class Note {
  final String id;
  final String title;
  final String content;
  final String category;
  final List<String> tags;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isPinned;
  final DateTime? scheduledDate;
  final int? color;
  final NoteType type;
  final List<ChecklistItem> checklistItems;
  final String? canvasData; // JSON string for canvas drawing data

  Note({
    String? id,
    required this.title,
    required this.content,
    required this.category,
    required this.tags,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.isPinned = false,
    this.scheduledDate,
    this.color,
    this.type = NoteType.plainText,
    this.checklistItems = const [],
    this.canvasData,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Note copyWith({
    String? title,
    String? content,
    String? category,
    List<String>? tags,
    DateTime? updatedAt,
    bool? isPinned,
    DateTime? scheduledDate,
    int? color,
    NoteType? type,
    List<ChecklistItem>? checklistItems,
    String? canvasData,
  }) {
    return Note(
      id: id,
      title: title ?? this.title,
      content: content ?? this.content,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      isPinned: isPinned ?? this.isPinned,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      color: color ?? this.color,
      type: type ?? this.type,
      checklistItems: checklistItems ?? this.checklistItems,
      canvasData: canvasData ?? this.canvasData,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'category': category,
      'tags': tags.join(','),
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'isPinned': isPinned ? 1 : 0,
      'scheduledDate': scheduledDate?.millisecondsSinceEpoch,
      'color': color,
      'type': type.name,
      'checklistItems': checklistItems.map((item) => item.toJson()).toList(),
      'canvasData': canvasData,
    };
  }

  factory Note.fromMap(Map<String, dynamic> map) {
    return Note(
      id: map['id'],
      title: map['title'],
      content: map['content'],
      category: map['category'],
      tags: map['tags']
          .toString()
          .split(',')
          .where((tag) => tag.isNotEmpty)
          .toList(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
      isPinned: map['isPinned'] == 1,
      scheduledDate: map['scheduledDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['scheduledDate'])
          : null,
      color: map['color'],
      type: NoteType.values.firstWhere(
        (t) => t.name == (map['type'] ?? 'plainText'),
        orElse: () => NoteType.plainText,
      ),
      checklistItems: map['checklistItems'] != null
          ? (map['checklistItems'] as List)
                .map((item) => ChecklistItem.fromJson(item))
                .toList()
          : [],
      canvasData: map['canvasData'],
    );
  }

  @override
  String toString() {
    return 'Note(id: $id, title: $title, category: $category, tags: $tags, isPinned: $isPinned)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Note && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum NoteCategory {
  personal('Personal'),
  work('Work'),
  study('Study'),
  ideas('Ideas'),
  projects('Projects'),
  other('Other');

  const NoteCategory(this.displayName);
  final String displayName;

  static List<String> get allCategories =>
      NoteCategory.values.map((e) => e.displayName).toList();
}
