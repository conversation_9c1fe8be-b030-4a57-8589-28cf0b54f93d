import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import '../../models/text_note.dart';
import '../../services/memo_providers.dart';
import '../../../../core/theme/app_theme.dart';

class AdvancedTextEditorScreen extends ConsumerStatefulWidget {
  final String? noteId;

  const AdvancedTextEditorScreen({super.key, this.noteId});

  @override
  ConsumerState<AdvancedTextEditorScreen> createState() => _AdvancedTextEditorScreenState();
}

class _AdvancedTextEditorScreenState extends ConsumerState<AdvancedTextEditorScreen>
    with TickerProviderStateMixin {
  late TextEditingController _titleController;
  late TextEditingController _contentController;
  late FocusNode _titleFocusNode;
  late FocusNode _contentFocusNode;
  late TabController _tabController;
  
  Timer? _autoSaveTimer;
  bool _isMarkdownMode = false;
  bool _isPreviewMode = false;
  bool _hasUnsavedChanges = false;
  String _selectedCategory = 'General';
  List<String> _tags = [];
  
  // Editor features
  bool _showWordCount = true;
  // final bool _showLineNumbers = false; // Reserved for future line number display
  bool _enableAutoSave = true;
  double _fontSize = 16.0;
  final String _fontFamily = 'Roboto';
  
  // Markdown toolbar items
  final List<MarkdownAction> _markdownActions = [
    MarkdownAction('Bold', Icons.format_bold, '**', '**'),
    MarkdownAction('Italic', Icons.format_italic, '*', '*'),
    MarkdownAction('Strikethrough', Icons.strikethrough_s, '~~', '~~'),
    MarkdownAction('Code', Icons.code, '`', '`'),
    MarkdownAction('Link', Icons.link, '[', '](url)'),
    MarkdownAction('Image', Icons.image, '![', '](url)'),
    MarkdownAction('Header 1', Icons.title, '# ', ''),
    MarkdownAction('Header 2', Icons.title, '## ', ''),
    MarkdownAction('Header 3', Icons.title, '### ', ''),
    MarkdownAction('Quote', Icons.format_quote, '> ', ''),
    MarkdownAction('List', Icons.format_list_bulleted, '- ', ''),
    MarkdownAction('Numbered List', Icons.format_list_numbered, '1. ', ''),
    MarkdownAction('Code Block', Icons.code_off, '```\n', '\n```'),
    MarkdownAction('Table', Icons.table_chart, '| Header |\n|--------|\n| Cell   |', ''),
  ];

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController();
    _contentController = TextEditingController();
    _titleFocusNode = FocusNode();
    _contentFocusNode = FocusNode();
    _tabController = TabController(length: 2, vsync: this);
    
    _setupAutoSave();
    _loadNote();
    
    // Listen for changes
    _titleController.addListener(_onTextChanged);
    _contentController.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _autoSaveTimer?.cancel();
    _titleController.dispose();
    _contentController.dispose();
    _titleFocusNode.dispose();
    _contentFocusNode.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _setupAutoSave() {
    if (_enableAutoSave) {
      _autoSaveTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
        if (_hasUnsavedChanges) {
          _saveNote(showSnackBar: false);
        }
      });
    }
  }

  void _loadNote() {
    if (widget.noteId != null) {
      final note = ref.read(textNotesProvider).firstWhere(
        (n) => n.id == widget.noteId,
        orElse: () => TextNote(
          id: '',
          title: '',
          content: '',
          category: 'General',
          createdAt: DateTime.now(),
          lastModified: DateTime.now(),
        ),
      );
      
      if (note.id.isNotEmpty) {
        _titleController.text = note.title;
        _contentController.text = note.content;
        _selectedCategory = note.category;
        _tags = note.tags;
      }
    }
  }

  void _onTextChanged() {
    setState(() {
      _hasUnsavedChanges = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(widget.noteId == null ? 'New Note' : 'Edit Note'),
        backgroundColor: AppTheme.memoSuiteColor,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(_isMarkdownMode ? Icons.text_fields : Icons.code),
            onPressed: () {
              setState(() {
                _isMarkdownMode = !_isMarkdownMode;
              });
            },
            tooltip: _isMarkdownMode ? 'Switch to Rich Text' : 'Switch to Markdown',
          ),
          if (_isMarkdownMode)
            IconButton(
              icon: Icon(_isPreviewMode ? Icons.edit : Icons.preview),
              onPressed: () {
                setState(() {
                  _isPreviewMode = !_isPreviewMode;
                });
              },
              tooltip: _isPreviewMode ? 'Edit Mode' : 'Preview Mode',
            ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showEditorSettings,
          ),
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: () => _saveNote(showSnackBar: true),
          ),
        ],
        bottom: _isMarkdownMode && !_isPreviewMode ? _buildMarkdownToolbar() : null,
      ),
      body: Column(
        children: [
          _buildNoteMetadata(),
          Expanded(
            child: _isMarkdownMode
                ? _buildMarkdownEditor()
                : _buildRichTextEditor(),
          ),
          _buildStatusBar(),
        ],
      ),
    );
  }

  Widget _buildNoteMetadata() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        children: [
          TextField(
            controller: _titleController,
            focusNode: _titleFocusNode,
            decoration: const InputDecoration(
              hintText: 'Note title...',
              border: InputBorder.none,
              contentPadding: EdgeInsets.zero,
            ),
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  decoration: const InputDecoration(
                    labelText: 'Category',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: ['General', 'Work', 'Personal', 'Ideas', 'Projects']
                      .map((category) => DropdownMenuItem(
                            value: category,
                            child: Text(category),
                          ))
                      .toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedCategory = value;
                        _hasUnsavedChanges = true;
                      });
                    }
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTagsField(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTagsField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Tags', style: TextStyle(fontSize: 12, color: Colors.grey)),
        const SizedBox(height: 4),
        Wrap(
          spacing: 4,
          children: [
            ..._tags.map((tag) => Chip(
                  label: Text(tag, style: const TextStyle(fontSize: 12)),
                  onDeleted: () {
                    setState(() {
                      _tags.remove(tag);
                      _hasUnsavedChanges = true;
                    });
                  },
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                )),
            ActionChip(
              label: const Icon(Icons.add, size: 16),
              onPressed: _addTag,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ],
        ),
      ],
    );
  }

  PreferredSizeWidget _buildMarkdownToolbar() {
    return PreferredSize(
      preferredSize: const Size(double.infinity, 100),
      child: Container(
        height: 100,
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
        ),
        child: GridView.builder(
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 7,
            childAspectRatio: 1.2,
            crossAxisSpacing: 4,
            mainAxisSpacing: 4,
          ),
          itemCount: _markdownActions.length,
          itemBuilder: (context, index) {
            final action = _markdownActions[index];
            return Tooltip(
              message: action.name,
              child: InkWell(
                onTap: () => _applyMarkdownAction(action),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: Icon(action.icon, size: 16),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildMarkdownEditor() {
    if (_isPreviewMode) {
      return _buildMarkdownPreview();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _contentController,
        focusNode: _contentFocusNode,
        maxLines: null,
        expands: true,
        decoration: const InputDecoration(
          hintText: 'Start writing your note...',
          border: InputBorder.none,
        ),
        style: TextStyle(
          fontSize: _fontSize,
          fontFamily: _fontFamily,
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildMarkdownPreview() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Markdown Preview',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.memoSuiteColor,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Text(
                _contentController.text.isEmpty
                    ? 'Preview will appear here...'
                    : _renderMarkdown(_contentController.text),
                style: TextStyle(
                  fontSize: _fontSize,
                  height: 1.5,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRichTextEditor() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildRichTextToolbar(),
          const SizedBox(height: 8),
          Expanded(
            child: TextField(
              controller: _contentController,
              focusNode: _contentFocusNode,
              maxLines: null,
              expands: true,
              decoration: const InputDecoration(
                hintText: 'Start writing your note...',
                border: InputBorder.none,
              ),
              style: TextStyle(
                fontSize: _fontSize,
                fontFamily: _fontFamily,
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRichTextToolbar() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          _buildToolbarButton(Icons.format_bold, 'Bold', () {}),
          _buildToolbarButton(Icons.format_italic, 'Italic', () {}),
          _buildToolbarButton(Icons.format_underlined, 'Underline', () {}),
          const VerticalDivider(),
          _buildToolbarButton(Icons.format_list_bulleted, 'Bullet List', () {}),
          _buildToolbarButton(Icons.format_list_numbered, 'Numbered List', () {}),
          const VerticalDivider(),
          _buildToolbarButton(Icons.link, 'Insert Link', () {}),
          _buildToolbarButton(Icons.image, 'Insert Image', () {}),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.undo),
            onPressed: () {},
            tooltip: 'Undo',
          ),
          IconButton(
            icon: const Icon(Icons.redo),
            onPressed: () {},
            tooltip: 'Redo',
          ),
        ],
      ),
    );
  }

  Widget _buildToolbarButton(IconData icon, String tooltip, VoidCallback onPressed) {
    return IconButton(
      icon: Icon(icon, size: 20),
      onPressed: onPressed,
      tooltip: tooltip,
    );
  }

  Widget _buildStatusBar() {
    final wordCount = _contentController.text.split(' ').where((word) => word.isNotEmpty).length;
    final charCount = _contentController.text.length;
    final lineCount = _contentController.text.split('\n').length;

    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          if (_showWordCount) ...[
            Text('Words: $wordCount', style: const TextStyle(fontSize: 12)),
            const SizedBox(width: 16),
            Text('Characters: $charCount', style: const TextStyle(fontSize: 12)),
            const SizedBox(width: 16),
            Text('Lines: $lineCount', style: const TextStyle(fontSize: 12)),
          ],
          const Spacer(),
          if (_hasUnsavedChanges)
            const Text(
              'Unsaved changes',
              style: TextStyle(fontSize: 12, color: Colors.orange),
            )
          else
            const Text(
              'All changes saved',
              style: TextStyle(fontSize: 12, color: Colors.green),
            ),
        ],
      ),
    );
  }

  void _applyMarkdownAction(MarkdownAction action) {
    final selection = _contentController.selection;
    final text = _contentController.text;
    
    if (selection.isValid) {
      final selectedText = text.substring(selection.start, selection.end);
      final newText = action.prefix + selectedText + action.suffix;
      
      _contentController.text = text.replaceRange(selection.start, selection.end, newText);
      
      // Update cursor position
      final newCursorPos = selection.start + action.prefix.length + selectedText.length;
      _contentController.selection = TextSelection.collapsed(offset: newCursorPos);
    } else {
      // Insert at cursor position
      final cursorPos = selection.baseOffset;
      final newText = action.prefix + action.suffix;
      
      _contentController.text = text.substring(0, cursorPos) + newText + text.substring(cursorPos);
      
      // Position cursor between prefix and suffix
      _contentController.selection = TextSelection.collapsed(
        offset: cursorPos + action.prefix.length,
      );
    }
    
    _contentFocusNode.requestFocus();
  }

  String _renderMarkdown(String markdown) {
    // Simple markdown rendering for preview
    // In a real implementation, you would use a proper markdown parser
    return markdown
        .replaceAll(RegExp(r'\*\*(.*?)\*\*'), r'$1') // Bold
        .replaceAll(RegExp(r'\*(.*?)\*'), r'$1') // Italic
        .replaceAll(RegExp(r'~~(.*?)~~'), r'$1') // Strikethrough
        .replaceAll(RegExp(r'`(.*?)`'), r'$1') // Code
        .replaceAll(RegExp(r'^# (.*)$', multiLine: true), r'$1') // Headers
        .replaceAll(RegExp(r'^## (.*)$', multiLine: true), r'$1')
        .replaceAll(RegExp(r'^### (.*)$', multiLine: true), r'$1');
  }

  void _addTag() {
    showDialog(
      context: context,
      builder: (context) {
        final controller = TextEditingController();
        return AlertDialog(
          title: const Text('Add Tag'),
          content: TextField(
            controller: controller,
            decoration: const InputDecoration(
              hintText: 'Enter tag name',
              border: OutlineInputBorder(),
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                final tag = controller.text.trim();
                if (tag.isNotEmpty && !_tags.contains(tag)) {
                  setState(() {
                    _tags.add(tag);
                    _hasUnsavedChanges = true;
                  });
                }
                Navigator.pop(context);
              },
              child: const Text('Add'),
            ),
          ],
        );
      },
    );
  }

  void _showEditorSettings() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Editor Settings',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Show Word Count'),
              value: _showWordCount,
              onChanged: (value) {
                setState(() {
                  _showWordCount = value;
                });
              },
            ),
            SwitchListTile(
              title: const Text('Enable Auto-Save'),
              value: _enableAutoSave,
              onChanged: (value) {
                setState(() {
                  _enableAutoSave = value;
                  if (value) {
                    _setupAutoSave();
                  } else {
                    _autoSaveTimer?.cancel();
                  }
                });
              },
            ),
            ListTile(
              title: const Text('Font Size'),
              trailing: DropdownButton<double>(
                value: _fontSize,
                items: [12.0, 14.0, 16.0, 18.0, 20.0, 24.0]
                    .map((size) => DropdownMenuItem(
                          value: size,
                          child: Text('${size.toInt()}pt'),
                        ))
                    .toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _fontSize = value;
                    });
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _saveNote({required bool showSnackBar}) {
    final note = TextNote(
      id: widget.noteId ?? 'note_${DateTime.now().millisecondsSinceEpoch}',
      title: _titleController.text.isEmpty ? 'Untitled' : _titleController.text,
      content: _contentController.text,
      category: _selectedCategory,
      tags: _tags,
      createdAt: widget.noteId == null ? DateTime.now() : DateTime.now(),
      lastModified: DateTime.now(),
    );

    // Save to provider
    if (widget.noteId == null) {
      ref.read(textNotesProvider.notifier).addNote(note);
    } else {
      ref.read(textNotesProvider.notifier).updateNote(note);
    }

    setState(() {
      _hasUnsavedChanges = false;
    });

    if (showSnackBar) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Note saved successfully'),
          backgroundColor: AppTheme.memoSuiteColor,
        ),
      );
    }
  }
}

// Data classes
class MarkdownAction {
  final String name;
  final IconData icon;
  final String prefix;
  final String suffix;

  const MarkdownAction(this.name, this.icon, this.prefix, this.suffix);
}
