import 'dart:async';
import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path/path.dart' as path;
import '../models/smart_gallery_models.dart';
import '../../../core/database/database_service.dart';
import '../../../core/services/error_handler.dart' as error_handler;
import '../../../core/services/cross_platform_storage_service.dart';
import '../../../core/services/storage_scanner_service.dart';
import 'ai_processing_service.dart';

/// Smart Gallery service for managing 200,000+ media files
class SmartGalleryService {
  static bool _isInitialized = false;
  static bool _isScanning = false;
  static final StreamController<SmartGalleryEvent> _eventController =
      StreamController<SmartGalleryEvent>.broadcast();

  static final List<SmartGalleryItem> _cachedItems = [];
  static final Map<String, SmartGalleryAlbum> _cachedAlbums = {};
  static DateTime? _lastScanTime;
  static Timer? _backgroundScanTimer;

  /// Initialize Smart Gallery service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize database tables
      await _initializeDatabaseTables();

      // Initialize AI processing
      await AIProcessingService.initialize();

      // Initialize storage access
      await CrossPlatformStorageService.initialize();

      // Load cached data
      await _loadCachedData();

      // Start background scanning
      _startBackgroundScanning();

      _isInitialized = true;

      _notifyEvent(
        SmartGalleryEvent(
          type: SmartGalleryEventType.initialized,
          message: 'Smart Gallery initialized successfully',
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.operation,
        context: 'Initialize Smart Gallery service',
      );
    }
  }

  /// Initialize database tables
  static Future<void> _initializeDatabaseTables() async {
    await DatabaseService.safeExecute('''
      CREATE TABLE IF NOT EXISTS smart_gallery_items (
        id TEXT PRIMARY KEY,
        path TEXT NOT NULL,
        name TEXT NOT NULL,
        type INTEGER NOT NULL,
        size INTEGER NOT NULL,
        date_created INTEGER NOT NULL,
        date_modified INTEGER NOT NULL,
        width INTEGER,
        height INTEGER,
        duration INTEGER,
        thumbnail_path TEXT,
        is_hidden INTEGER DEFAULT 0,
        is_favorite INTEGER DEFAULT 0,
        is_locked INTEGER DEFAULT 0,
        ai_tags TEXT DEFAULT '',
        custom_tags TEXT DEFAULT '',
        ocr_text TEXT,
        faces TEXT DEFAULT '',
        location TEXT,
        camera_data TEXT
      )
    ''');

    await DatabaseService.safeExecute('''
      CREATE TABLE IF NOT EXISTS smart_gallery_albums (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        type INTEGER NOT NULL,
        media_ids TEXT DEFAULT '',
        cover_image_id TEXT,
        date_created INTEGER NOT NULL,
        date_modified INTEGER NOT NULL,
        is_locked INTEGER DEFAULT 0
      )
    ''');

    // Create indexes for performance
    await DatabaseService.safeExecute('''
      CREATE INDEX IF NOT EXISTS idx_smart_gallery_items_type ON smart_gallery_items(type)
    ''');

    await DatabaseService.safeExecute('''
      CREATE INDEX IF NOT EXISTS idx_smart_gallery_items_date_modified ON smart_gallery_items(date_modified)
    ''');

    await DatabaseService.safeExecute('''
      CREATE INDEX IF NOT EXISTS idx_smart_gallery_items_is_favorite ON smart_gallery_items(is_favorite)
    ''');
  }

  /// Load cached data from database
  static Future<void> _loadCachedData() async {
    try {
      // Load items
      final itemResults = await DatabaseService.safeQuery(
        'SELECT * FROM smart_gallery_items ORDER BY date_modified DESC LIMIT 1000',
      );

      _cachedItems.clear();
      for (final row in itemResults) {
        _cachedItems.add(_itemFromMap(row));
      }

      // Load albums
      final albumResults = await DatabaseService.safeQuery(
        'SELECT * FROM smart_gallery_albums',
      );

      _cachedAlbums.clear();
      for (final row in albumResults) {
        final album = _albumFromMap(row);
        _cachedAlbums[album.id] = album;
      }
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.operation,
        context: 'Load cached data',
      );
    }
  }

  /// Start background scanning for new media
  static void _startBackgroundScanning() {
    _backgroundScanTimer?.cancel();
    _backgroundScanTimer = Timer.periodic(const Duration(minutes: 5), (
      timer,
    ) async {
      if (!_isScanning) {
        await scanForNewMedia();
      }
    });
  }

  /// Process scan results from StorageScannerService
  static Future<void> processScanResults(StorageScanResult scanResult) async {
    if (_isScanning) return;

    _isScanning = true;

    try {
      _notifyEvent(
        SmartGalleryEvent(
          type: SmartGalleryEventType.scanStarted,
          message: 'Processing scan results...',
          timestamp: DateTime.now(),
        ),
      );

      int newItemsFound = 0;

      // Process images
      for (final file in scanResult.images) {
        final item = await _createMediaItem(file);
        if (item != null && await _addMediaItem(item)) {
          newItemsFound++;
          // Process with AI in background
          AIProcessingService.processMediaItem(item);
        }
      }

      // Process videos
      for (final file in scanResult.videos) {
        final item = await _createMediaItem(file);
        if (item != null && await _addMediaItem(item)) {
          newItemsFound++;
          // Process with AI in background
          AIProcessingService.processMediaItem(item);
        }
      }

      _lastScanTime = DateTime.now();

      _notifyEvent(
        SmartGalleryEvent(
          type: SmartGalleryEventType.scanCompleted,
          message: 'Processing completed. Added $newItemsFound new items.',
          timestamp: DateTime.now(),
          data: {'newItemsCount': newItemsFound},
        ),
      );
    } catch (e) {
      _notifyEvent(
        SmartGalleryEvent(
          type: SmartGalleryEventType.scanFailed,
          message: 'Processing failed: $e',
          timestamp: DateTime.now(),
          data: {'error': e.toString()},
        ),
      );
    } finally {
      _isScanning = false;
    }
  }

  /// Scan for new media files
  static Future<void> scanForNewMedia() async {
    if (_isScanning) return;

    _isScanning = true;

    try {
      _notifyEvent(
        SmartGalleryEvent(
          type: SmartGalleryEventType.scanStarted,
          message: 'Scanning for new media files...',
          timestamp: DateTime.now(),
        ),
      );

      final storageLocations =
          await CrossPlatformStorageService.getStorageLocations();
      int newItemsFound = 0;

      for (final location in storageLocations) {
        if (location.type == StorageType.pictures ||
            location.type == StorageType.videos ||
            location.isAccessible) {
          newItemsFound += await _scanDirectory(location.path);
        }
      }

      _lastScanTime = DateTime.now();

      _notifyEvent(
        SmartGalleryEvent(
          type: SmartGalleryEventType.scanCompleted,
          message: 'Scan completed. Found $newItemsFound new items.',
          timestamp: DateTime.now(),
          data: {'newItemsCount': newItemsFound},
        ),
      );
    } catch (e) {
      _notifyEvent(
        SmartGalleryEvent(
          type: SmartGalleryEventType.scanFailed,
          message: 'Scan failed: $e',
          timestamp: DateTime.now(),
          data: {'error': e.toString()},
        ),
      );
    } finally {
      _isScanning = false;
    }
  }

  /// Scan directory for media files
  static Future<int> _scanDirectory(String directoryPath) async {
    int newItemsFound = 0;

    try {
      final directory = Directory(directoryPath);
      if (!await directory.exists()) return 0;

      await for (final entity in directory.list(recursive: true)) {
        if (entity is File && _isMediaFile(entity.path)) {
          final item = await _createMediaItem(entity);
          if (item != null && await _addMediaItem(item)) {
            newItemsFound++;

            // Process with AI in background
            AIProcessingService.processMediaItem(item);
          }
        }
      }
    } catch (e) {
      // Continue scanning other directories
    }

    return newItemsFound;
  }

  /// Check if file is a media file
  static bool _isMediaFile(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    const imageExtensions = [
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.bmp',
      '.webp',
      '.heic',
      '.raw',
    ];
    const videoExtensions = [
      '.mp4',
      '.avi',
      '.mov',
      '.mkv',
      '.wmv',
      '.flv',
      '.webm',
    ];

    return imageExtensions.contains(extension) ||
        videoExtensions.contains(extension);
  }

  /// Create media item from file
  static Future<SmartGalleryItem?> _createMediaItem(File file) async {
    try {
      final stat = await file.stat();
      final fileName = path.basename(file.path);
      final extension = path.extension(file.path).toLowerCase();

      MediaType mediaType;
      if ([
        '.jpg',
        '.jpeg',
        '.png',
        '.gif',
        '.bmp',
        '.webp',
        '.heic',
        '.raw',
      ].contains(extension)) {
        mediaType = MediaType.image;
      } else if ([
        '.mp4',
        '.avi',
        '.mov',
        '.mkv',
        '.wmv',
        '.flv',
        '.webm',
      ].contains(extension)) {
        mediaType = MediaType.video;
      } else {
        return null;
      }

      return SmartGalleryItem(
        id: _generateId(file.path),
        path: file.path,
        name: fileName,
        type: mediaType,
        size: stat.size,
        dateCreated: stat.changed,
        dateModified: stat.modified,
      );
    } catch (e) {
      return null;
    }
  }

  /// Generate unique ID for media item
  static String _generateId(String filePath) {
    return filePath.hashCode.abs().toString();
  }

  /// Add media item to database
  static Future<bool> _addMediaItem(SmartGalleryItem item) async {
    try {
      // Check if item already exists
      final existing = await DatabaseService.safeQuery(
        'SELECT id FROM smart_gallery_items WHERE id = ?',
        [item.id],
      );

      if (existing.isNotEmpty) return false;

      // Insert new item
      await DatabaseService.safeInsert('smart_gallery_items', {
        'id': item.id,
        'path': item.path,
        'name': item.name,
        'type': item.type.index,
        'size': item.size,
        'date_created': item.dateCreated.millisecondsSinceEpoch,
        'date_modified': item.dateModified.millisecondsSinceEpoch,
        'width': item.width,
        'height': item.height,
        'duration': item.duration?.inMilliseconds,
        'thumbnail_path': item.thumbnailPath,
        'is_hidden': item.isHidden ? 1 : 0,
        'is_favorite': item.isFavorite ? 1 : 0,
        'is_locked': item.isLocked ? 1 : 0,
        'ai_tags': item.aiTags.join(','),
        'custom_tags': item.customTags.join(','),
        'ocr_text': item.ocrText,
        'faces': '', // JSON encoded faces
        'location': '', // JSON encoded location
        'camera_data': '', // JSON encoded camera data
      });

      // Add to cache
      _cachedItems.insert(0, item);

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get media items with filtering and pagination
  static Future<List<SmartGalleryItem>> getMediaItems({
    SmartGalleryFilter? filter,
    int offset = 0,
    int limit = 100,
  }) async {
    try {
      String query = 'SELECT * FROM smart_gallery_items';
      final whereConditions = <String>[];
      final args = <dynamic>[];

      // Apply filters
      if (filter != null) {
        if (filter.mediaType != null) {
          whereConditions.add('type = ?');
          args.add(filter.mediaType!.index);
        }

        if (filter.textQuery != null && filter.textQuery!.isNotEmpty) {
          whereConditions.add(
            '(name LIKE ? OR ocr_text LIKE ? OR ai_tags LIKE ?)',
          );
          final searchTerm = '%${filter.textQuery}%';
          args.addAll([searchTerm, searchTerm, searchTerm]);
        }

        if (filter.dateRange != null) {
          whereConditions.add('date_modified BETWEEN ? AND ?');
          args.add(filter.dateRange!.start.millisecondsSinceEpoch);
          args.add(filter.dateRange!.end.millisecondsSinceEpoch);
        }
      }

      if (whereConditions.isNotEmpty) {
        query += ' WHERE ${whereConditions.join(' AND ')}';
      }

      // Apply sorting
      final sortBy = filter?.sortBy ?? SortBy.dateModified;
      final sortOrder = filter?.sortOrder ?? SortOrder.descending;

      String orderBy;
      switch (sortBy) {
        case SortBy.dateCreated:
          orderBy = 'date_created';
          break;
        case SortBy.dateModified:
          orderBy = 'date_modified';
          break;
        case SortBy.name:
          orderBy = 'name';
          break;
        case SortBy.size:
          orderBy = 'size';
          break;
        case SortBy.type:
          orderBy = 'type';
          break;
      }

      query +=
          ' ORDER BY $orderBy ${sortOrder == SortOrder.ascending ? 'ASC' : 'DESC'}';
      query += ' LIMIT $limit OFFSET $offset';

      final results = await DatabaseService.safeQuery(query, args);
      return results.map((row) => _itemFromMap(row)).toList();
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.operation,
        context: 'Get media items',
      );
      return [];
    }
  }

  /// Convert database row to SmartGalleryItem
  static SmartGalleryItem _itemFromMap(Map<String, dynamic> map) {
    return SmartGalleryItem(
      id: map['id'],
      path: map['path'],
      name: map['name'],
      type: MediaType.values[map['type']],
      size: map['size'],
      dateCreated: DateTime.fromMillisecondsSinceEpoch(map['date_created']),
      dateModified: DateTime.fromMillisecondsSinceEpoch(map['date_modified']),
      width: map['width'],
      height: map['height'],
      duration: map['duration'] != null
          ? Duration(milliseconds: map['duration'])
          : null,
      thumbnailPath: map['thumbnail_path'],
      isHidden: map['is_hidden'] == 1,
      isFavorite: map['is_favorite'] == 1,
      isLocked: map['is_locked'] == 1,
      aiTags:
          map['ai_tags']?.split(',').where((tag) => tag.isNotEmpty).toList() ??
          [],
      customTags:
          map['custom_tags']
              ?.split(',')
              .where((tag) => tag.isNotEmpty)
              .toList() ??
          [],
      ocrText: map['ocr_text'],
    );
  }

  /// Convert database row to SmartGalleryAlbum
  static SmartGalleryAlbum _albumFromMap(Map<String, dynamic> map) {
    return SmartGalleryAlbum(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      type: AlbumType.values[map['type']],
      mediaIds:
          map['media_ids']?.split(',').where((id) => id.isNotEmpty).toList() ??
          [],
      coverImageId: map['cover_image_id'],
      dateCreated: DateTime.fromMillisecondsSinceEpoch(map['date_created']),
      dateModified: DateTime.fromMillisecondsSinceEpoch(map['date_modified']),
      isLocked: map['is_locked'] == 1,
    );
  }

  /// Get Smart Gallery statistics
  static Future<SmartGalleryStats> getStatistics() async {
    try {
      final totalResult = await DatabaseService.safeQuery(
        'SELECT COUNT(*) as total FROM smart_gallery_items',
      );
      final total = totalResult.first['total'] as int;

      final imagesResult = await DatabaseService.safeQuery(
        'SELECT COUNT(*) as count FROM smart_gallery_items WHERE type = ?',
        [MediaType.image.index],
      );
      final images = imagesResult.first['count'] as int;

      final videosResult = await DatabaseService.safeQuery(
        'SELECT COUNT(*) as count FROM smart_gallery_items WHERE type = ?',
        [MediaType.video.index],
      );
      final videos = videosResult.first['count'] as int;

      final favoritesResult = await DatabaseService.safeQuery(
        'SELECT COUNT(*) as count FROM smart_gallery_items WHERE is_favorite = 1',
      );
      final favorites = favoritesResult.first['count'] as int;

      return SmartGalleryStats(
        totalItems: total,
        totalImages: images,
        totalVideos: videos,
        totalSize: await _calculateTotalSize(),
        favoritesCount: favorites,
        hiddenCount: await _calculateHiddenCount(),
        lockedCount: await _calculateLockedCount(),
        peopleCount: await _calculatePeopleCount(),
        placesCount: await _calculatePlacesCount(),
        aiProcessedCount: await _calculateAIProcessedCount(),
        lastScanTime: _lastScanTime ?? DateTime.now(),
      );
    } catch (e) {
      return SmartGalleryStats(
        totalItems: 0,
        totalImages: 0,
        totalVideos: 0,
        totalSize: 0,
        favoritesCount: 0,
        hiddenCount: 0,
        lockedCount: 0,
        peopleCount: 0,
        placesCount: 0,
        aiProcessedCount: 0,
        lastScanTime: DateTime.now(),
      );
    }
  }

  /// Notify event
  static void _notifyEvent(SmartGalleryEvent event) {
    if (!_eventController.isClosed) {
      _eventController.add(event);
    }
  }

  /// Toggle favorite status of media item
  static Future<bool> toggleFavorite(String mediaId) async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT is_favorite FROM smart_gallery_items WHERE id = ?',
        [mediaId],
      );

      if (results.isEmpty) return false;

      final currentFavorite = (results.first['is_favorite'] as int) == 1;
      final newFavorite = !currentFavorite;

      await DatabaseService.safeUpdate(
        'smart_gallery_items',
        {'is_favorite': newFavorite ? 1 : 0},
        where: 'id = ?',
        whereArgs: [mediaId],
      );

      _notifyEvent(
        SmartGalleryEvent(
          type: SmartGalleryEventType.itemUpdated,
          message: newFavorite
              ? 'Added to favorites'
              : 'Removed from favorites',
          timestamp: DateTime.now(),
          data: {'mediaId': mediaId, 'isFavorite': newFavorite},
        ),
      );

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Delete media item
  static Future<bool> deleteMediaItem(String mediaId) async {
    try {
      // Get item info first
      final results = await DatabaseService.safeQuery(
        'SELECT path FROM smart_gallery_items WHERE id = ?',
        [mediaId],
      );

      if (results.isEmpty) return false;

      final filePath = results.first['path'] as String;

      // Delete from database
      await DatabaseService.safeDelete(
        'smart_gallery_items',
        where: 'id = ?',
        whereArgs: [mediaId],
      );

      // Try to delete physical file
      try {
        final file = File(filePath);
        if (await file.exists()) {
          await file.delete();
        }
      } catch (e) {
        // File deletion failed, but database record is removed
      }

      _notifyEvent(
        SmartGalleryEvent(
          type: SmartGalleryEventType.itemDeleted,
          message: 'Media item deleted',
          timestamp: DateTime.now(),
          data: {'mediaId': mediaId},
        ),
      );

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get event stream
  static Stream<SmartGalleryEvent> get eventStream => _eventController.stream;

  /// Helper calculation methods
  static Future<int> _calculateTotalSize() async {
    final result = await DatabaseService.safeQuery(
      'SELECT SUM(size) as total_size FROM smart_gallery_items',
    );
    return result.first['total_size'] ?? 0;
  }

  static Future<int> _calculateHiddenCount() async {
    final result = await DatabaseService.safeQuery(
      'SELECT COUNT(*) as count FROM smart_gallery_items WHERE is_hidden = 1',
    );
    return result.first['count'] ?? 0;
  }

  static Future<int> _calculateLockedCount() async {
    final result = await DatabaseService.safeQuery(
      'SELECT COUNT(*) as count FROM smart_gallery_items WHERE is_locked = 1',
    );
    return result.first['count'] ?? 0;
  }

  static Future<int> _calculatePeopleCount() async {
    // Simulate people count from AI analysis
    return 0;
  }

  static Future<int> _calculatePlacesCount() async {
    // Simulate places count from location data
    return 0;
  }

  static Future<int> _calculateAIProcessedCount() async {
    // Simulate AI processed count
    return 0;
  }

  /// Dispose resources
  static void dispose() {
    _backgroundScanTimer?.cancel();
    _eventController.close();
    AIProcessingService.dispose();
  }
}

/// Smart Gallery event
class SmartGalleryEvent {
  final SmartGalleryEventType type;
  final String message;
  final DateTime timestamp;
  final Map<String, dynamic>? data;

  const SmartGalleryEvent({
    required this.type,
    required this.message,
    required this.timestamp,
    this.data,
  });
}

/// Smart Gallery event types
enum SmartGalleryEventType {
  initialized,
  scanStarted,
  scanCompleted,
  scanFailed,
  itemAdded,
  itemUpdated,
  itemDeleted,
}

/// Smart Gallery providers
final smartGalleryServiceProvider = Provider<SmartGalleryService>((ref) {
  return SmartGalleryService();
});

final smartGalleryStatsProvider = FutureProvider<SmartGalleryStats>((ref) {
  return SmartGalleryService.getStatistics();
});

final smartGalleryItemsProvider =
    FutureProvider.family<List<SmartGalleryItem>, SmartGalleryFilter?>((
      ref,
      filter,
    ) {
      return SmartGalleryService.getMediaItems(filter: filter);
    });
