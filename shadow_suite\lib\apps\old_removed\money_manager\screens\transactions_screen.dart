import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/money_manager_models.dart';
import '../services/money_manager_providers.dart';
import '../widgets/add_transaction_dialog.dart';

class TransactionsScreen extends ConsumerStatefulWidget {
  const TransactionsScreen({super.key});

  @override
  ConsumerState<TransactionsScreen> createState() => _TransactionsScreenState();
}

class _TransactionsScreenState extends ConsumerState<TransactionsScreen> {
  final TextEditingController _searchController = TextEditingController();
  TransactionType? _selectedType;
  String? _selectedAccountId;
  String? _selectedCategoryId;

  // Multi-select functionality
  final Set<String> _selectedTransactionIds = {};
  bool _isSelectionMode = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final transactionsAsync = ref.watch(transactionsProvider);
    final accountsAsync = ref.watch(accountsProvider);
    final categoriesAsync = ref.watch(categoriesProvider);

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.blue.shade50, Colors.white],
        ),
      ),
      child: Column(
        children: [
          _buildHeader(context),
          _buildFilters(),
          Expanded(
            child: transactionsAsync.when(
              data: (transactions) => accountsAsync.when(
                data: (accounts) => categoriesAsync.when(
                  data: (categories) => _buildTransactionsList(
                    _filterTransactions(transactions),
                    accounts,
                    categories,
                  ),
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => _buildErrorState(error.toString()),
                ),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => _buildErrorState(error.toString()),
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _buildErrorState(error.toString()),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Color(0xFFE9ECEF))),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF27AE60).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.receipt_long,
              color: Color(0xFF27AE60),
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Transactions',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                Text(
                  'Record and manage your income and expenses',
                  style: TextStyle(fontSize: 14, color: Color(0xFF7F8C8D)),
                ),
              ],
            ),
          ),
          if (_isSelectionMode) ...[
            Text(
              '${_selectedTransactionIds.length} selected',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF2C3E50),
              ),
            ),
            const SizedBox(width: 16),
            IconButton(
              onPressed: _selectedTransactionIds.isNotEmpty
                  ? _showBatchOperations
                  : null,
              icon: const Icon(Icons.edit),
              tooltip: 'Batch Edit',
              style: IconButton.styleFrom(
                backgroundColor: const Color(0xFF3498DB).withValues(alpha: 0.1),
                foregroundColor: const Color(0xFF3498DB),
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: _selectedTransactionIds.isNotEmpty
                  ? _batchDelete
                  : null,
              icon: const Icon(Icons.delete),
              tooltip: 'Delete Selected',
              style: IconButton.styleFrom(
                backgroundColor: const Color(0xFFE74C3C).withValues(alpha: 0.1),
                foregroundColor: const Color(0xFFE74C3C),
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: _exitSelectionMode,
              icon: const Icon(Icons.close),
              tooltip: 'Exit Selection',
            ),
          ] else ...[
            IconButton(
              onPressed: _enterSelectionMode,
              icon: const Icon(Icons.checklist),
              tooltip: 'Select Multiple',
              style: IconButton.styleFrom(
                backgroundColor: const Color(0xFF3498DB).withValues(alpha: 0.1),
                foregroundColor: const Color(0xFF3498DB),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton.icon(
              onPressed: () => _showAddTransactionDialog(context),
              icon: const Icon(Icons.add),
              label: const Text('Add Transaction'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF27AE60),
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Color(0xFFE9ECEF))),
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search transactions...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            onChanged: (value) => setState(() {}),
          ),
          const SizedBox(height: 16),

          // Filter chips
          Row(
            children: [
              _buildFilterChip(
                'All',
                _selectedType == null,
                () => setState(() => _selectedType = null),
              ),
              const SizedBox(width: 8),
              _buildFilterChip(
                'Income',
                _selectedType == TransactionType.income,
                () => setState(() => _selectedType = TransactionType.income),
              ),
              const SizedBox(width: 8),
              _buildFilterChip(
                'Expense',
                _selectedType == TransactionType.expense,
                () => setState(() => _selectedType = TransactionType.expense),
              ),
              const SizedBox(width: 8),
              _buildFilterChip(
                'Transfer',
                _selectedType == TransactionType.transfer,
                () => setState(() => _selectedType = TransactionType.transfer),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF27AE60) : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? const Color(0xFF27AE60) : Colors.grey.shade300,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : const Color(0xFF7F8C8D),
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            fontSize: 12,
          ),
        ),
      ),
    );
  }

  List<Transaction> _filterTransactions(List<Transaction> transactions) {
    var filtered = transactions;

    // Filter by search query
    if (_searchController.text.isNotEmpty) {
      final query = _searchController.text.toLowerCase();
      filtered = filtered
          .where(
            (transaction) =>
                transaction.description.toLowerCase().contains(query) ||
                transaction.notes?.toLowerCase().contains(query) == true ||
                transaction.tags.any(
                  (tag) => tag.toLowerCase().contains(query),
                ),
          )
          .toList();
    }

    // Filter by type
    if (_selectedType != null) {
      filtered = filtered
          .where((transaction) => transaction.type == _selectedType)
          .toList();
    }

    // Filter by account
    if (_selectedAccountId != null) {
      filtered = filtered
          .where(
            (transaction) =>
                transaction.accountId == _selectedAccountId ||
                transaction.toAccountId == _selectedAccountId,
          )
          .toList();
    }

    // Filter by category
    if (_selectedCategoryId != null) {
      filtered = filtered
          .where((transaction) => transaction.categoryId == _selectedCategoryId)
          .toList();
    }

    // Sort by date (newest first)
    filtered.sort((a, b) => b.date.compareTo(a.date));

    return filtered;
  }

  Widget _buildTransactionsList(
    List<Transaction> transactions,
    List<Account> accounts,
    List<Category> categories,
  ) {
    if (transactions.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(24),
      itemCount: transactions.length,
      itemBuilder: (context, index) {
        final transaction = transactions[index];
        final account = accounts.firstWhere(
          (acc) => acc.id == transaction.accountId,
          orElse: () => Account(
            id: '',
            name: 'Unknown Account',
            type: AccountType.checking,
            initialBalance: 0,
            currentBalance: 0,
            createdAt: DateTime.now(),
            lastModified: DateTime.now(),
          ),
        );

        final category = categories.firstWhere(
          (cat) => cat.id == transaction.categoryId,
          orElse: () => Category(
            id: '',
            name: 'Unknown Category',
            type: CategoryType.expense,
            createdAt: DateTime.now(),
          ),
        );

        return _buildTransactionCard(transaction, account, category);
      },
    );
  }

  Widget _buildTransactionCard(
    Transaction transaction,
    Account account,
    Category category,
  ) {
    final isIncome = transaction.type == TransactionType.income;
    final isTransfer = transaction.type == TransactionType.transfer;
    final isSelected = _selectedTransactionIds.contains(transaction.id);

    return GestureDetector(
      onTap: _isSelectionMode
          ? () => _toggleSelection(transaction.id)
          : () => _showTransactionDetails(transaction, category, account),
      onLongPress: _isSelectionMode
          ? null
          : () {
              _enterSelectionMode();
              _toggleSelection(transaction.id);
            },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: _isSelectionMode && isSelected
              ? Border.all(color: const Color(0xFF27AE60), width: 2)
              : null,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          children: [
            if (_isSelectionMode)
              Padding(
                padding: const EdgeInsets.only(right: 16),
                child: Checkbox(
                  value: isSelected,
                  onChanged: (value) => _toggleSelection(transaction.id),
                  activeColor: const Color(0xFF27AE60),
                ),
              ),
            Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color: _getTransactionColor(
                  transaction.type,
                ).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                _getTransactionIcon(transaction.type),
                color: _getTransactionColor(transaction.type),
                size: 28,
              ),
            ),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    transaction.description.isNotEmpty
                        ? transaction.description
                        : category.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF2C3E50),
                    ),
                  ),
                  const SizedBox(height: 6),
                  Row(
                    children: [
                      Icon(
                        Icons.account_balance,
                        size: 14,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        account.name,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const Text(' • ', style: TextStyle(color: Colors.grey)),
                      Icon(
                        Icons.category,
                        size: 14,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        category.name,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatDate(transaction.date),
                    style: TextStyle(fontSize: 11, color: Colors.grey.shade500),
                  ),
                  if (transaction.tags.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 6,
                      children: transaction.tags
                          .take(3)
                          .map(
                            (tag) => Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.blue.shade50,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Text(
                                tag,
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.blue.shade700,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          )
                          .toList(),
                    ),
                  ],
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${isIncome
                      ? '+'
                      : isTransfer
                      ? ''
                      : '-'}\$${transaction.amount.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isIncome
                        ? const Color(0xFF27AE60)
                        : isTransfer
                        ? const Color(0xFF3498DB)
                        : const Color(0xFFE74C3C),
                  ),
                ),
                const SizedBox(height: 8),
                PopupMenuButton<String>(
                  onSelected: (value) =>
                      _handleTransactionAction(value, transaction),
                  itemBuilder: (context) => [
                    const PopupMenuItem(value: 'edit', child: Text('Edit')),
                    const PopupMenuItem(
                      value: 'duplicate',
                      child: Text('Duplicate'),
                    ),
                    const PopupMenuItem(value: 'delete', child: Text('Delete')),
                  ],
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.more_vert, size: 16),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.receipt_long_outlined, size: 64, color: Color(0xFFBDC3C7)),
          SizedBox(height: 16),
          Text(
            'No Transactions Found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF7F8C8D),
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Start recording your income and expenses',
            style: TextStyle(color: Color(0xFF95A5A6)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Color(0xFFE74C3C)),
          const SizedBox(height: 16),
          const Text(
            'Error Loading Transactions',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFFE74C3C),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: const TextStyle(color: Color(0xFF95A5A6)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Selection mode methods
  void _enterSelectionMode() {
    setState(() {
      _isSelectionMode = true;
      _selectedTransactionIds.clear();
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedTransactionIds.clear();
    });
  }

  void _toggleSelection(String transactionId) {
    setState(() {
      if (_selectedTransactionIds.contains(transactionId)) {
        _selectedTransactionIds.remove(transactionId);
      } else {
        _selectedTransactionIds.add(transactionId);
      }
    });
  }

  void _showBatchOperations() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Batch Operations'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.category),
              title: const Text('Change Category'),
              onTap: () {
                Navigator.of(context).pop();
                _batchChangeCategory();
              },
            ),
            ListTile(
              leading: const Icon(Icons.label),
              title: const Text('Add Tags'),
              onTap: () {
                Navigator.of(context).pop();
                _batchAddTags();
              },
            ),
            ListTile(
              leading: const Icon(Icons.date_range),
              title: const Text('Change Date'),
              onTap: () {
                Navigator.of(context).pop();
                _batchChangeDate();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _batchDelete() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Transactions'),
        content: Text(
          'Are you sure you want to delete ${_selectedTransactionIds.length} transactions?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              for (final id in _selectedTransactionIds) {
                ref.read(transactionsProvider.notifier).deleteTransaction(id);
              }
              Navigator.of(context).pop();
              _exitSelectionMode();

              // Use a post-frame callback to avoid BuildContext async issues
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        '${_selectedTransactionIds.length} transactions deleted',
                      ),
                      backgroundColor: const Color(0xFFE74C3C),
                    ),
                  );
                }
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFE74C3C),
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _batchChangeCategory() {
    // Implementation for batch category change
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Changed category for ${_selectedTransactionIds.length} transactions',
        ),
      ),
    );
  }

  void _batchAddTags() {
    // Implementation for batch tag addition
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Added tags to ${_selectedTransactionIds.length} transactions',
        ),
      ),
    );
  }

  void _batchChangeDate() {
    // Implementation for batch date change
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Changed date for ${_selectedTransactionIds.length} transactions',
        ),
      ),
    );
  }

  void _showTransactionDetails(
    Transaction transaction,
    Category category,
    Account account,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Transaction Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Description', transaction.description),
            _buildDetailRow(
              'Amount',
              '\$${transaction.amount.toStringAsFixed(2)}',
            ),
            _buildDetailRow('Type', transaction.type.name.toUpperCase()),
            _buildDetailRow('Account', account.name),
            _buildDetailRow('Category', category.name),
            _buildDetailRow(
              'Date',
              '${transaction.date.month}/${transaction.date.day}/${transaction.date.year}',
            ),
            if (transaction.notes != null && transaction.notes!.isNotEmpty)
              _buildDetailRow('Notes', transaction.notes!),
            if (transaction.tags.isNotEmpty)
              _buildDetailRow('Tags', transaction.tags.join(', ')),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showEditTransactionDialog(transaction);
            },
            child: const Text('Edit'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Color _getTransactionColor(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return const Color(0xFF27AE60);
      case TransactionType.expense:
        return const Color(0xFFE74C3C);
      case TransactionType.transfer:
        return const Color(0xFF3498DB);
    }
  }

  IconData _getTransactionIcon(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return Icons.arrow_downward;
      case TransactionType.expense:
        return Icons.arrow_upward;
      case TransactionType.transfer:
        return Icons.swap_horiz;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.month}/${date.day}/${date.year}';
    }
  }

  void _showAddTransactionDialog(BuildContext context) {
    showDialog(context: context, builder: (context) => AddTransactionDialog());
  }

  void _handleTransactionAction(String action, Transaction transaction) {
    switch (action) {
      case 'edit':
        _showEditTransactionDialog(transaction);
        break;
      case 'duplicate':
        _duplicateTransaction(transaction);
        break;
      case 'delete':
        _deleteTransaction(transaction);
        break;
    }
  }

  void _showEditTransactionDialog(Transaction transaction) {
    showDialog(
      context: context,
      builder: (context) => AddTransactionDialog(transaction: transaction),
    );
  }

  void _duplicateTransaction(Transaction transaction) {
    final duplicated = transaction.copyWith(
      id: 'txn_${DateTime.now().millisecondsSinceEpoch}',
      date: DateTime.now(),
      createdAt: DateTime.now(),
    );

    ref.read(transactionsProvider.notifier).addTransaction(duplicated);

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Transaction duplicated successfully'),
        backgroundColor: Color(0xFF27AE60),
      ),
    );
  }

  void _deleteTransaction(Transaction transaction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Transaction'),
        content: const Text(
          'Are you sure you want to delete this transaction?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref
                  .read(transactionsProvider.notifier)
                  .deleteTransaction(transaction.id);
              Navigator.of(context).pop();

              // Use a post-frame callback to avoid BuildContext async issues
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Transaction deleted successfully'),
                      backgroundColor: Color(0xFFE74C3C),
                    ),
                  );
                }
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFE74C3C),
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
