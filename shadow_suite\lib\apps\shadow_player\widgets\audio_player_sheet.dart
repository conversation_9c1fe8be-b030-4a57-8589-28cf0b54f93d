import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/media_models.dart';

/// Bottom sheet audio player widget
class AudioPlayerSheet extends ConsumerStatefulWidget {
  final MediaFile audioFile;

  const AudioPlayerSheet({
    super.key,
    required this.audioFile,
  });

  @override
  ConsumerState<AudioPlayerSheet> createState() => _AudioPlayerSheetState();
}

class _AudioPlayerSheetState extends ConsumerState<AudioPlayerSheet>
    with TickerProviderStateMixin {
  bool _isPlaying = false;
  bool _isLoading = false;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  bool _isShuffleEnabled = false;
  RepeatMode _repeatMode = RepeatMode.none;

  late AnimationController _playPauseController;
  late AnimationController _waveController;

  @override
  void initState() {
    super.initState();
    _playPauseController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _waveController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _initializeAudio();
  }

  @override
  void dispose() {
    _playPauseController.dispose();
    _waveController.dispose();
    super.dispose();
  }

  void _initializeAudio() {
    setState(() {
      _isLoading = true;
      _totalDuration = widget.audioFile.duration ?? const Duration(minutes: 3, seconds: 30);
    });

    // Simulate loading
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isPlaying = true;
        });
        _playPauseController.forward();
        _startPositionTimer();
      }
    });
  }

  void _startPositionTimer() {
    // Simulate playback progress
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted && _isPlaying) {
        setState(() {
          _currentPosition = Duration(
            seconds: (_currentPosition.inSeconds + 1) % _totalDuration.inSeconds,
          );
        });
        _startPositionTimer();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.keyboard_arrow_down),
                  onPressed: () => Navigator.pop(context),
                ),
                const Spacer(),
                const Text(
                  'Now Playing',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.more_vert),
                  onPressed: _showMoreOptions,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 32),
          
          // Album art
          Container(
            width: 280,
            height: 280,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Container(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                child: Icon(
                  Icons.music_note,
                  size: 120,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 40),
          
          // Track info
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Column(
              children: [
                Text(
                  widget.audioFile.metadata.title ?? widget.audioFile.displayName,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Text(
                  widget.audioFile.metadata.artist ?? 'Unknown Artist',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 40),
          
          // Progress bar
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Column(
              children: [
                SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
                    trackHeight: 4,
                  ),
                  child: Slider(
                    value: _totalDuration.inSeconds > 0
                        ? _currentPosition.inSeconds / _totalDuration.inSeconds
                        : 0.0,
                    onChanged: (value) {
                      setState(() {
                        _currentPosition = Duration(
                          seconds: (value * _totalDuration.inSeconds).round(),
                        );
                      });
                    },
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _formatDuration(_currentPosition),
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                    Text(
                      _formatDuration(_totalDuration),
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 32),
          
          // Controls
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    _isShuffleEnabled ? Icons.shuffle : Icons.shuffle,
                    color: _isShuffleEnabled ? Theme.of(context).primaryColor : Colors.grey,
                  ),
                  onPressed: _toggleShuffle,
                ),
                IconButton(
                  icon: const Icon(Icons.skip_previous, size: 36),
                  onPressed: _previousTrack,
                ),
                Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    icon: AnimatedIcon(
                      icon: AnimatedIcons.play_pause,
                      progress: _playPauseController,
                      size: 32,
                      color: Colors.white,
                    ),
                    onPressed: _togglePlayPause,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.skip_next, size: 36),
                  onPressed: _nextTrack,
                ),
                IconButton(
                  icon: Icon(_getRepeatIcon()),
                  color: _repeatMode != RepeatMode.none ? Theme.of(context).primaryColor : Colors.grey,
                  onPressed: _toggleRepeat,
                ),
              ],
            ),
          ),
          
          const Spacer(),
        ],
      ),
    );
  }

  IconData _getRepeatIcon() {
    switch (_repeatMode) {
      case RepeatMode.none:
        return Icons.repeat;
      case RepeatMode.all:
        return Icons.repeat;
      case RepeatMode.one:
        return Icons.repeat_one;
    }
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  void _togglePlayPause() {
    setState(() {
      _isPlaying = !_isPlaying;
    });
    
    if (_isPlaying) {
      _playPauseController.forward();
      _startPositionTimer();
    } else {
      _playPauseController.reverse();
    }
  }

  void _previousTrack() {
    // TODO: Implement previous track
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Previous track')),
    );
  }

  void _nextTrack() {
    // TODO: Implement next track
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Next track')),
    );
  }

  void _toggleShuffle() {
    setState(() {
      _isShuffleEnabled = !_isShuffleEnabled;
    });
  }

  void _toggleRepeat() {
    setState(() {
      switch (_repeatMode) {
        case RepeatMode.none:
          _repeatMode = RepeatMode.all;
          break;
        case RepeatMode.all:
          _repeatMode = RepeatMode.one;
          break;
        case RepeatMode.one:
          _repeatMode = RepeatMode.none;
          break;
      }
    });
  }

  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.playlist_add),
              title: const Text('Add to Playlist'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement add to playlist
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('Share'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement share
              },
            ),
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('Song Info'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Show song info
              },
            ),
          ],
        ),
      ),
    );
  }
}

enum RepeatMode { none, all, one }
