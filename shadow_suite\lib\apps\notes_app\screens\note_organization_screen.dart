import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Advanced Note Organization Screen with tags, categories, and smart folders
class NoteOrganizationScreen extends ConsumerStatefulWidget {
  const NoteOrganizationScreen({super.key});

  @override
  ConsumerState<NoteOrganizationScreen> createState() => _NoteOrganizationScreenState();
}

class _NoteOrganizationScreenState extends ConsumerState<NoteOrganizationScreen> {
  String _selectedView = 'categories';
  List<String> _selectedTags = [];
  String _selectedCategory = '';

  final List<NoteCategory> _categories = [
    NoteCategory('Work', Icons.work, Colors.blue, 15),
    NoteCategory('Personal', Icons.person, Colors.green, 8),
    NoteCategory('Ideas', Icons.lightbulb, Colors.orange, 12),
    NoteCategory('Projects', Icons.folder, Colors.purple, 6),
    NoteCategory('Archive', Icons.archive, Colors.grey, 23),
  ];

  final List<String> _availableTags = [
    'important', 'urgent', 'meeting', 'todo', 'draft', 'review',
    'inspiration', 'research', 'planning', 'brainstorm', 'feedback'
  ];

  final List<SmartFolder> _smartFolders = [
    SmartFolder('Recent', Icons.access_time, 'Last 7 days', 12),
    SmartFolder('Favorites', Icons.star, 'Starred notes', 5),
    SmartFolder('Untagged', Icons.label_off, 'Notes without tags', 8),
    SmartFolder('Long Notes', Icons.article, 'Notes > 1000 words', 3),
    SmartFolder('Images', Icons.image, 'Notes with images', 7),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Note Organization'),
        backgroundColor: Colors.amber,
        foregroundColor: Colors.black,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddDialog,
          ),
          PopupMenuButton<String>(
            onSelected: (value) => setState(() => _selectedView = value),
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'categories', child: Text('Categories')),
              const PopupMenuItem(value: 'tags', child: Text('Tags')),
              const PopupMenuItem(value: 'smart', child: Text('Smart Folders')),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          _buildViewSelector(),
          Expanded(child: _buildCurrentView()),
        ],
      ),
    );
  }

  Widget _buildViewSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: SegmentedButton<String>(
              segments: const [
                ButtonSegment(value: 'categories', label: Text('Categories'), icon: Icon(Icons.folder)),
                ButtonSegment(value: 'tags', label: Text('Tags'), icon: Icon(Icons.label)),
                ButtonSegment(value: 'smart', label: Text('Smart'), icon: Icon(Icons.auto_awesome)),
              ],
              selected: {_selectedView},
              onSelectionChanged: (Set<String> selection) {
                setState(() => _selectedView = selection.first);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentView() {
    switch (_selectedView) {
      case 'categories':
        return _buildCategoriesView();
      case 'tags':
        return _buildTagsView();
      case 'smart':
        return _buildSmartFoldersView();
      default:
        return _buildCategoriesView();
    }
  }

  Widget _buildCategoriesView() {
    return Column(
      children: [
        _buildCategoryStats(),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _categories.length,
            itemBuilder: (context, index) {
              final category = _categories[index];
              return _buildCategoryCard(category);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryStats() {
    final totalNotes = _categories.fold(0, (sum, cat) => sum + cat.noteCount);
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.amber.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('Total Notes', totalNotes.toString(), Icons.note),
          _buildStatItem('Categories', _categories.length.toString(), Icons.folder),
          _buildStatItem('Tags', _availableTags.length.toString(), Icons.label),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.amber.shade700),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildCategoryCard(NoteCategory category) {
    final isSelected = _selectedCategory == category.name;
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        elevation: isSelected ? 4 : 1,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          onTap: () => setState(() {
            _selectedCategory = isSelected ? '' : category.name;
          }),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: isSelected 
                ? Border.all(color: category.color, width: 2)
                : null,
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: category.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(category.icon, color: category.color),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        category.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${category.noteCount} notes',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.edit, size: 20),
                      onPressed: () => _editCategory(category),
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, size: 20, color: Colors.red),
                      onPressed: () => _deleteCategory(category),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTagsView() {
    return Column(
      children: [
        _buildTagFilter(),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _availableTags.map((tag) {
                final isSelected = _selectedTags.contains(tag);
                return FilterChip(
                  label: Text(tag),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        _selectedTags.add(tag);
                      } else {
                        _selectedTags.remove(tag);
                      }
                    });
                  },
                  backgroundColor: Colors.grey.shade200,
                  selectedColor: Colors.amber.withValues(alpha: 0.3),
                  checkmarkColor: Colors.amber.shade700,
                );
              }).toList(),
            ),
          ),
        ),
        if (_selectedTags.isNotEmpty) _buildTaggedNotes(),
      ],
    );
  }

  Widget _buildTagFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Filter by Tags',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          if (_selectedTags.isNotEmpty) ...[
            Text(
              'Selected: ${_selectedTags.join(', ')}',
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            ElevatedButton.icon(
              onPressed: () => setState(() => _selectedTags.clear()),
              icon: const Icon(Icons.clear),
              label: const Text('Clear Selection'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey.shade200,
                foregroundColor: Colors.grey.shade700,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTaggedNotes() {
    return Container(
      height: 200,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Notes with selected tags (${_selectedTags.length} tags)',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: ListView.builder(
              itemCount: 5, // Simulated notes
              itemBuilder: (context, index) {
                return ListTile(
                  leading: const Icon(Icons.note),
                  title: Text('Note ${index + 1}'),
                  subtitle: Text('Contains: ${_selectedTags.take(2).join(', ')}'),
                  dense: true,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSmartFoldersView() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _smartFolders.length,
      itemBuilder: (context, index) {
        final folder = _smartFolders[index];
        return _buildSmartFolderCard(folder);
      },
    );
  }

  Widget _buildSmartFolderCard(SmartFolder folder) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        elevation: 1,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          onTap: () => _openSmartFolder(folder),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.amber.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(folder.icon, color: Colors.amber.shade700),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        folder.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        folder.description,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.amber.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${folder.noteCount}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.amber.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showAddDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Add ${_selectedView == 'categories' ? 'Category' : 'Tag'}'),
        content: TextField(
          decoration: InputDecoration(
            labelText: _selectedView == 'categories' ? 'Category name' : 'Tag name',
            border: const OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('${_selectedView == 'categories' ? 'Category' : 'Tag'} added')),
              );
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _editCategory(NoteCategory category) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Edit ${category.name} category')),
    );
  }

  void _deleteCategory(NoteCategory category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Category'),
        content: Text('Are you sure you want to delete "${category.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('${category.name} deleted')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _openSmartFolder(SmartFolder folder) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Opening ${folder.name} folder')),
    );
  }
}

/// Model classes
class NoteCategory {
  final String name;
  final IconData icon;
  final Color color;
  final int noteCount;

  NoteCategory(this.name, this.icon, this.color, this.noteCount);
}

class SmartFolder {
  final String name;
  final IconData icon;
  final String description;
  final int noteCount;

  SmartFolder(this.name, this.icon, this.description, this.noteCount);
}
