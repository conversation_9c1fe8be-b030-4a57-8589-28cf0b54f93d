import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class HadithCollectionsScreen extends ConsumerWidget {
  const HadithCollectionsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Hadith Collections'),
        backgroundColor: Colors.brown,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildCollectionCard(
            context,
            '<PERSON><PERSON><PERSON>',
            'The most authentic collection of Hadith',
            '7,563 Hadiths',
            Icons.book,
            Colors.green,
          ),
          const SizedBox(height: 12),
          _buildCollectionCard(
            context,
            '<PERSON>hih Muslim',
            'Second most authentic collection',
            '7,190 Hadiths',
            Icons.library_books,
            Colors.blue,
          ),
          const SizedBox(height: 12),
          _buildCollectionCard(
            context,
            '<PERSON><PERSON>',
            'Collection focusing on legal matters',
            '5,274 Hadiths',
            Icons.auto_stories,
            Colors.orange,
          ),
          const Sized<PERSON><PERSON>(height: 12),
          _buildCollectionCard(
            context,
            '<PERSON><PERSON> at-Tir<PERSON>dhi',
            'Comprehensive collection with commentary',
            '3,956 Hadiths',
            Icons.menu_book,
            Colors.purple,
          ),
          const SizedBox(height: 12),
          _buildCollectionCard(
            context,
            'Sunan an-Nasa\'i',
            'Collection emphasizing authenticity',
            '5,761 Hadiths',
            Icons.book_outlined,
            Colors.teal,
          ),
          const SizedBox(height: 12),
          _buildCollectionCard(
            context,
            'Sunan Ibn Majah',
            'Collection with unique narrations',
            '4,341 Hadiths',
            Icons.import_contacts,
            Colors.indigo,
          ),
        ],
      ),
    );
  }

  Widget _buildCollectionCard(
    BuildContext context,
    String title,
    String description,
    String count,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color),
        ),
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(description),
            const SizedBox(height: 4),
            Text(
              count,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: () => _openCollection(context, title),
      ),
    );
  }

  void _openCollection(BuildContext context, String collectionName) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HadithListScreen(collectionName: collectionName),
      ),
    );
  }
}

class HadithListScreen extends StatelessWidget {
  final String collectionName;

  const HadithListScreen({super.key, required this.collectionName});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(collectionName),
        backgroundColor: Colors.brown,
        foregroundColor: Colors.white,
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: 20,
        itemBuilder: (context, index) {
          return _buildHadithItem(context, index + 1);
        },
      ),
    );
  }

  Widget _buildHadithItem(BuildContext context, int number) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.brown.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Hadith $number',
                    style: const TextStyle(
                      color: Colors.brown,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () {},
                  icon: const Icon(Icons.bookmark_border),
                  iconSize: 20,
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Narrated by Abu Huraira: The Prophet (PBUH) said, "Faith (Belief) consists of more than sixty branches (i.e. parts). And Haya (This term "Haya" covers a large number of concepts which are to be taken together; amongst them are self respect, modesty, bashfulness, and scruple, etc.) is a part of faith."',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                height: 1.5,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Text(
                  'Book: Faith',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {},
                  child: const Text('Read Full'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
