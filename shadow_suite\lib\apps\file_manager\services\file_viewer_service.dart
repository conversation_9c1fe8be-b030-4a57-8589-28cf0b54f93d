import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

enum FileViewerType {
  text,
  image,
  audio,
  video,
  pdf,
  archive,
  document,
  unsupported,
}

class FileViewerService {
  static const Map<String, FileViewerType> _extensionMap = {
    // Text files
    'txt': FileViewerType.text,
    'log': FileViewerType.text,
    'csv': FileViewerType.text,
    'json': FileViewerType.text,
    'xml': FileViewerType.text,
    'md': FileViewerType.text,
    'html': FileViewerType.text,
    'css': FileViewerType.text,
    'js': FileViewerType.text,
    'dart': FileViewerType.text,
    'py': FileViewerType.text,
    'java': FileViewerType.text,
    'cpp': FileViewerType.text,
    'c': FileViewerType.text,
    'h': FileViewerType.text,
    
    // Images
    'jpg': FileViewerType.image,
    'jpeg': FileViewerType.image,
    'png': FileViewerType.image,
    'gif': FileViewerType.image,
    'bmp': FileViewerType.image,
    'webp': FileViewerType.image,
    'svg': FileViewerType.image,
    'ico': FileViewerType.image,
    
    // Audio
    'mp3': FileViewerType.audio,
    'wav': FileViewerType.audio,
    'flac': FileViewerType.audio,
    'aac': FileViewerType.audio,
    'ogg': FileViewerType.audio,
    'm4a': FileViewerType.audio,
    'wma': FileViewerType.audio,
    
    // Video
    'mp4': FileViewerType.video,
    'avi': FileViewerType.video,
    'mov': FileViewerType.video,
    'wmv': FileViewerType.video,
    'flv': FileViewerType.video,
    'mkv': FileViewerType.video,
    'webm': FileViewerType.video,
    '3gp': FileViewerType.video,
    
    // PDF
    'pdf': FileViewerType.pdf,
    
    // Archives
    'zip': FileViewerType.archive,
    'rar': FileViewerType.archive,
    '7z': FileViewerType.archive,
    'tar': FileViewerType.archive,
    'gz': FileViewerType.archive,
    'bz2': FileViewerType.archive,
    
    // Documents
    'doc': FileViewerType.document,
    'docx': FileViewerType.document,
    'xls': FileViewerType.document,
    'xlsx': FileViewerType.document,
    'ppt': FileViewerType.document,
    'pptx': FileViewerType.document,
    'odt': FileViewerType.document,
    'ods': FileViewerType.document,
    'odp': FileViewerType.document,
  };

  /// Get the viewer type for a file
  static FileViewerType getViewerType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    return _extensionMap[extension] ?? FileViewerType.unsupported;
  }

  /// Check if a file can be viewed
  static bool canViewFile(String fileName) {
    return getViewerType(fileName) != FileViewerType.unsupported;
  }

  /// Get the appropriate icon for a file type
  static IconData getFileIcon(String fileName) {
    final viewerType = getViewerType(fileName);
    
    switch (viewerType) {
      case FileViewerType.text:
        return Icons.text_snippet;
      case FileViewerType.image:
        return Icons.image;
      case FileViewerType.audio:
        return Icons.audio_file;
      case FileViewerType.video:
        return Icons.video_file;
      case FileViewerType.pdf:
        return Icons.picture_as_pdf;
      case FileViewerType.archive:
        return Icons.archive;
      case FileViewerType.document:
        final extension = fileName.split('.').last.toLowerCase();
        switch (extension) {
          case 'doc':
          case 'docx':
            return Icons.description;
          case 'xls':
          case 'xlsx':
            return Icons.table_chart;
          case 'ppt':
          case 'pptx':
            return Icons.slideshow;
          default:
            return Icons.description;
        }
      case FileViewerType.unsupported:
        return Icons.insert_drive_file;
    }
  }

  /// Get the color for a file type
  static Color getFileColor(String fileName) {
    final viewerType = getViewerType(fileName);
    
    switch (viewerType) {
      case FileViewerType.text:
        return const Color(0xFF6C757D);
      case FileViewerType.image:
        return const Color(0xFF28A745);
      case FileViewerType.audio:
        return const Color(0xFF17A2B8);
      case FileViewerType.video:
        return const Color(0xFFDC3545);
      case FileViewerType.pdf:
        return const Color(0xFFDC3545);
      case FileViewerType.archive:
        return const Color(0xFF6F42C1);
      case FileViewerType.document:
        return const Color(0xFF007BFF);
      case FileViewerType.unsupported:
        return const Color(0xFF6C757D);
    }
  }

  /// Read text file content
  static Future<String> readTextFile(String filePath) async {
    try {
      final file = File(filePath);
      final content = await file.readAsString();
      return content;
    } catch (error) {
      throw Exception('Error reading text file: $error');
    }
  }

  /// Write text file content
  static Future<void> writeTextFile(String filePath, String content) async {
    try {
      final file = File(filePath);
      await file.writeAsString(content);
    } catch (error) {
      throw Exception('Error writing text file: $error');
    }
  }

  /// Read image file as bytes
  static Future<Uint8List> readImageFile(String filePath) async {
    try {
      final file = File(filePath);
      final bytes = await file.readAsBytes();
      return bytes;
    } catch (error) {
      throw Exception('Error reading image file: $error');
    }
  }

  /// Get file metadata
  static Future<FileMetadata> getFileMetadata(String filePath) async {
    try {
      final file = File(filePath);
      final stat = await file.stat();
      
      return FileMetadata(
        path: filePath,
        name: filePath.split('/').last,
        size: stat.size,
        lastModified: stat.modified,
        lastAccessed: stat.accessed,
        isReadOnly: stat.mode & 0x200 == 0, // Check write permission
        viewerType: getViewerType(filePath.split('/').last),
      );
    } catch (error) {
      throw Exception('Error getting file metadata: $error');
    }
  }

  /// Get supported file extensions for a viewer type
  static List<String> getSupportedExtensions(FileViewerType viewerType) {
    return _extensionMap.entries
        .where((entry) => entry.value == viewerType)
        .map((entry) => entry.key)
        .toList();
  }

  /// Check if file is editable
  static bool isEditable(String fileName) {
    final viewerType = getViewerType(fileName);
    return viewerType == FileViewerType.text;
  }

  /// Get file size in human readable format
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Get MIME type for a file
  static String getMimeType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    
    switch (extension) {
      // Text
      case 'txt':
        return 'text/plain';
      case 'html':
        return 'text/html';
      case 'css':
        return 'text/css';
      case 'js':
        return 'application/javascript';
      case 'json':
        return 'application/json';
      case 'xml':
        return 'application/xml';
      
      // Images
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'bmp':
        return 'image/bmp';
      case 'webp':
        return 'image/webp';
      case 'svg':
        return 'image/svg+xml';
      
      // Audio
      case 'mp3':
        return 'audio/mpeg';
      case 'wav':
        return 'audio/wav';
      case 'flac':
        return 'audio/flac';
      case 'aac':
        return 'audio/aac';
      case 'ogg':
        return 'audio/ogg';
      
      // Video
      case 'mp4':
        return 'video/mp4';
      case 'avi':
        return 'video/x-msvideo';
      case 'mov':
        return 'video/quicktime';
      case 'wmv':
        return 'video/x-ms-wmv';
      case 'webm':
        return 'video/webm';
      
      // Documents
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case 'xls':
        return 'application/vnd.ms-excel';
      case 'xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case 'ppt':
        return 'application/vnd.ms-powerpoint';
      case 'pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      
      // Archives
      case 'zip':
        return 'application/zip';
      case 'rar':
        return 'application/x-rar-compressed';
      case '7z':
        return 'application/x-7z-compressed';
      case 'tar':
        return 'application/x-tar';
      case 'gz':
        return 'application/gzip';
      
      default:
        return 'application/octet-stream';
    }
  }
}

/// File metadata class
class FileMetadata {
  final String path;
  final String name;
  final int size;
  final DateTime lastModified;
  final DateTime lastAccessed;
  final bool isReadOnly;
  final FileViewerType viewerType;

  const FileMetadata({
    required this.path,
    required this.name,
    required this.size,
    required this.lastModified,
    required this.lastAccessed,
    required this.isReadOnly,
    required this.viewerType,
  });

  String get formattedSize => FileViewerService.formatFileSize(size);
  String get mimeType => FileViewerService.getMimeType(name);
  IconData get icon => FileViewerService.getFileIcon(name);
  Color get color => FileViewerService.getFileColor(name);
  bool get isEditable => FileViewerService.isEditable(name);
}
