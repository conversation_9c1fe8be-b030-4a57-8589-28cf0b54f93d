import 'package:flutter/material.dart';
import 'dart:async';
import '../services/file_operations_service.dart';

class FileOperationProgressDialog extends StatefulWidget {
  final String operationId;
  final String operationTitle;
  final VoidCallback? onCancel;

  const FileOperationProgressDialog({
    super.key,
    required this.operationId,
    required this.operationTitle,
    this.onCancel,
  });

  @override
  State<FileOperationProgressDialog> createState() =>
      _FileOperationProgressDialogState();
}

class _FileOperationProgressDialogState
    extends State<FileOperationProgressDialog> {
  late StreamSubscription<FileOperationProgress> _progressSubscription;
  FileOperationProgress? _currentProgress;
  bool _isCompleted = false;
  bool _isCancelled = false;

  @override
  void initState() {
    super.initState();
    _listenToProgress();
  }

  void _listenToProgress() {
    _progressSubscription = FileOperationsService.progressStream.listen(
      (progress) {
        if (progress.operationId == widget.operationId) {
          setState(() {
            _currentProgress = progress;

            // Check if operation is completed
            if (progress.itemsCompleted >= progress.totalItems) {
              _isCompleted = true;
              // Auto-close after 2 seconds
              Timer(const Duration(seconds: 2), () {
                if (mounted) {
                  Navigator.of(context).pop(true);
                }
              });
            }
          });
        }
      },
      onError: (error) {
        setState(() {
          _isCompleted = true;
        });
        _showError(error.toString());
      },
    );
  }

  @override
  void dispose() {
    _progressSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: _isCompleted || _isCancelled,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop && !_isCompleted && !_isCancelled) {
          final navigator = Navigator.of(context);
          final shouldCancel = await _showCancelConfirmation();
          if (shouldCancel) {
            _cancelOperation();
            if (mounted) {
              navigator.pop();
            }
          }
        }
      },
      child: AlertDialog(
        title: Row(
          children: [
            Icon(_getOperationIcon(), color: const Color(0xFFE67E22)),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                widget.operationTitle,
                style: const TextStyle(fontSize: 18),
              ),
            ),
          ],
        ),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Current item being processed
              if (_currentProgress != null) ...[
                Text(
                  'Processing: ${_currentProgress!.currentItem}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
              ],

              // Progress bar
              LinearProgressIndicator(
                value: _currentProgress?.itemProgress ?? 0.0,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  _isCompleted
                      ? const Color(0xFF27AE60)
                      : const Color(0xFFE67E22),
                ),
              ),
              const SizedBox(height: 8),

              // Progress text
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _currentProgress != null
                        ? '${_currentProgress!.itemsCompleted} of ${_currentProgress!.totalItems} items'
                        : 'Preparing...',
                    style: const TextStyle(fontSize: 12),
                  ),
                  Text(
                    _currentProgress != null
                        ? '${(_currentProgress!.itemProgress * 100).toStringAsFixed(1)}%'
                        : '0%',
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Status message
              if (_isCompleted) ...[
                Row(
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Color(0xFF27AE60),
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Operation completed successfully',
                      style: TextStyle(
                        color: const Color(0xFF27AE60),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ] else if (_isCancelled) ...[
                Row(
                  children: [
                    const Icon(Icons.cancel, color: Colors.orange, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      'Operation cancelled',
                      style: TextStyle(color: Colors.orange, fontSize: 12),
                    ),
                  ],
                ),
              ] else ...[
                Row(
                  children: [
                    SizedBox(
                      width: 12,
                      height: 12,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          const Color(0xFFE67E22),
                        ),
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Operation in progress...',
                      style: TextStyle(
                        color: const Color(0xFF7F8C8D),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],

              // Estimated time remaining (placeholder)
              if (!_isCompleted &&
                  !_isCancelled &&
                  _currentProgress != null) ...[
                const SizedBox(height: 8),
                Text(
                  'Estimated time remaining: Calculating...',
                  style: TextStyle(
                    color: const Color(0xFF7F8C8D),
                    fontSize: 11,
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          if (!_isCompleted && !_isCancelled) ...[
            TextButton(
              onPressed: _cancelOperation,
              child: const Text('Cancel'),
            ),
          ] else ...[
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Close'),
            ),
          ],
        ],
      ),
    );
  }

  IconData _getOperationIcon() {
    // This would be determined by the operation type
    return Icons.copy; // Default icon
  }

  Future<bool> _showCancelConfirmation() async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Cancel Operation'),
            content: const Text(
              'Are you sure you want to cancel this operation? Any progress will be lost.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Continue'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context, true),
                style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                child: const Text('Cancel Operation'),
              ),
            ],
          ),
        ) ??
        false;
  }

  void _cancelOperation() {
    setState(() {
      _isCancelled = true;
    });

    FileOperationsService.cancelOperation(widget.operationId);
    widget.onCancel?.call();

    // Close dialog after a short delay
    Timer(const Duration(seconds: 1), () {
      if (mounted) {
        Navigator.of(context).pop(false);
      }
    });
  }

  void _showError(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Operation Error'),
        content: Text(error),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.of(context).pop(false);
            },
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

/// Helper function to show progress dialog
Future<bool?> showFileOperationProgress({
  required BuildContext context,
  required String operationId,
  required String operationTitle,
  VoidCallback? onCancel,
}) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) => FileOperationProgressDialog(
      operationId: operationId,
      operationTitle: operationTitle,
      onCancel: onCancel,
    ),
  );
}
