@echo off
echo ========================================
echo Shadow Suite - Production Build Script
echo ========================================
echo.

:: Set build configuration
set BUILD_TYPE=release
set TARGET_PLATFORM=windows
set OUTPUT_DIR=build\windows\x64\runner\Release

:: Check Flutter installation
echo [1/8] Checking Flutter installation...
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Flutter is not installed or not in PATH
    pause
    exit /b 1
)
echo ✓ Flutter installation verified

:: Clean previous builds
echo.
echo [2/8] Cleaning previous builds...
flutter clean
if %errorlevel% neq 0 (
    echo ERROR: Failed to clean previous builds
    pause
    exit /b 1
)
echo ✓ Previous builds cleaned

:: Get dependencies
echo.
echo [3/8] Getting dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ERROR: Failed to get dependencies
    pause
    exit /b 1
)
echo ✓ Dependencies retrieved

:: Run code generation
echo.
echo [4/8] Running code generation...
flutter packages pub run build_runner build --delete-conflicting-outputs
if %errorlevel% neq 0 (
    echo WARNING: Code generation failed, continuing...
)
echo ✓ Code generation completed

:: Run tests
echo.
echo [5/8] Running tests...
flutter test
if %errorlevel% neq 0 (
    echo WARNING: Some tests failed, continuing with build...
)
echo ✓ Tests completed

:: Build for Windows
echo.
echo [6/8] Building Windows application...
flutter build windows --release --verbose
if %errorlevel% neq 0 (
    echo ERROR: Windows build failed
    pause
    exit /b 1
)
echo ✓ Windows build completed

:: Create installer directory structure
echo.
echo [7/8] Creating installer package...
set INSTALLER_DIR=build\installer
set APP_DIR=%INSTALLER_DIR%\ShadowSuite

:: Clean installer directory
if exist "%INSTALLER_DIR%" rmdir /s /q "%INSTALLER_DIR%"
mkdir "%INSTALLER_DIR%"
mkdir "%APP_DIR%"

:: Copy application files
echo Copying application files...
xcopy "%OUTPUT_DIR%\*" "%APP_DIR%\" /E /I /H /Y
if %errorlevel% neq 0 (
    echo ERROR: Failed to copy application files
    pause
    exit /b 1
)

:: Copy additional resources
echo Copying additional resources...
if exist "assets" xcopy "assets\*" "%APP_DIR%\data\flutter_assets\assets\" /E /I /H /Y
if exist "fonts" xcopy "fonts\*" "%APP_DIR%\data\flutter_assets\fonts\" /E /I /H /Y

:: Create version info file
echo Creating version info...
echo Shadow Suite v1.0.0 > "%APP_DIR%\version.txt"
echo Build Date: %date% %time% >> "%APP_DIR%\version.txt"
echo Platform: Windows x64 >> "%APP_DIR%\version.txt"

:: Create batch file for easy launching
echo Creating launcher...
(
echo @echo off
echo cd /d "%%~dp0"
echo start "" "shadow_suite.exe"
) > "%APP_DIR%\Launch Shadow Suite.bat"

:: Create uninstaller
echo Creating uninstaller...
(
echo @echo off
echo echo Uninstalling Shadow Suite...
echo cd /d "%%~dp0"
echo cd ..
echo rmdir /s /q "ShadowSuite"
echo echo Shadow Suite has been uninstalled.
echo pause
) > "%INSTALLER_DIR%\Uninstall.bat"

:: Create README
echo Creating README...
(
echo Shadow Suite - Complete Productivity Suite
echo ==========================================
echo.
echo Installation Instructions:
echo 1. Extract all files to your desired location
echo 2. Run "Launch Shadow Suite.bat" to start the application
echo 3. To uninstall, run "Uninstall.bat"
echo.
echo System Requirements:
echo - Windows 10 or later
echo - 4GB RAM minimum, 8GB recommended
echo - 500MB free disk space
echo - DirectX 11 compatible graphics
echo.
echo Features:
echo - Money Manager: Complete financial tracking and budgeting
echo - Islamic App: Prayer times, Quran reader, and Islamic tools
echo - Memo Suite: Voice memos, text notes, and cloud sync
echo - Excel to App: Convert spreadsheets to interactive applications
echo.
echo Support:
echo For support and updates, visit: https://shadowsuite.app
echo.
echo Version: 1.0.0
echo Build Date: %date%
) > "%INSTALLER_DIR%\README.txt"

:: Create ZIP package
echo.
echo [8/8] Creating distribution package...
set ZIP_NAME=ShadowSuite_v1.0.0_Windows_x64.zip

:: Use PowerShell to create ZIP (available on Windows 10+)
powershell -command "Compress-Archive -Path '%INSTALLER_DIR%\*' -DestinationPath '%ZIP_NAME%' -Force"
if %errorlevel% neq 0 (
    echo WARNING: Failed to create ZIP package, manual packaging required
) else (
    echo ✓ Distribution package created: %ZIP_NAME%
)

:: Calculate file sizes
echo.
echo Build Summary:
echo ==============
for %%F in ("%OUTPUT_DIR%\shadow_suite.exe") do echo Application Size: %%~zF bytes
if exist "%ZIP_NAME%" (
    for %%F in ("%ZIP_NAME%") do echo Package Size: %%~zF bytes
)

:: Performance verification
echo.
echo Running performance verification...
echo Starting application for 10 seconds to verify startup performance...
start "" "%OUTPUT_DIR%\shadow_suite.exe"
timeout /t 10 /nobreak >nul
taskkill /f /im shadow_suite.exe >nul 2>&1

echo.
echo ========================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Output locations:
echo - Executable: %OUTPUT_DIR%\shadow_suite.exe
echo - Installer: %INSTALLER_DIR%\
if exist "%ZIP_NAME%" echo - Package: %ZIP_NAME%
echo.
echo The application has been built and packaged for distribution.
echo.

:: Optional: Open output directory
set /p OPEN_DIR="Open output directory? (y/n): "
if /i "%OPEN_DIR%"=="y" (
    explorer "%CD%\%INSTALLER_DIR%"
)

echo.
echo Build script completed.
pause
