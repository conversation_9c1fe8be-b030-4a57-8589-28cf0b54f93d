// Re-export player state models from media_models.dart for backward compatibility
export 'media_models.dart' show 
  PlayerS<PERSON>,
  PlaybackState,
  PlayMode,
  RepeatMode,
  ViewMode,
  SortBy,
  SortOrder,
  ScreenRatio,
  PlaybackEventType,
  PlaybackQuality,
  PlaybackEvent,
  PlaybackStatistics;

// Additional player state models if needed
import 'media_models.dart';

/// Player configuration state
class PlayerConfiguration {
  final double volume;
  final double playbackSpeed;
  final RepeatMode repeatMode;
  final bool shuffleEnabled;
  final PlaybackQuality quality;
  final bool equalizerEnabled;
  final bool crossfadeEnabled;
  final Duration crossfadeDuration;

  const PlayerConfiguration({
    this.volume = 1.0,
    this.playbackSpeed = 1.0,
    this.repeatMode = RepeatMode.none,
    this.shuffleEnabled = false,
    this.quality = PlaybackQuality.high,
    this.equalizerEnabled = false,
    this.crossfadeEnabled = false,
    this.crossfadeDuration = const Duration(seconds: 3),
  });

  PlayerConfiguration copyWith({
    double? volume,
    double? playbackSpeed,
    RepeatMode? repeatMode,
    bool? shuffleEnabled,
    PlaybackQuality? quality,
    bool? equalizerEnabled,
    bool? crossfadeEnabled,
    Duration? crossfadeDuration,
  }) {
    return PlayerConfiguration(
      volume: volume ?? this.volume,
      playbackSpeed: playbackSpeed ?? this.playbackSpeed,
      repeatMode: repeatMode ?? this.repeatMode,
      shuffleEnabled: shuffleEnabled ?? this.shuffleEnabled,
      quality: quality ?? this.quality,
      equalizerEnabled: equalizerEnabled ?? this.equalizerEnabled,
      crossfadeEnabled: crossfadeEnabled ?? this.crossfadeEnabled,
      crossfadeDuration: crossfadeDuration ?? this.crossfadeDuration,
    );
  }
}

/// Player UI state
class PlayerUIState {
  final ViewMode viewMode;
  final SortBy sortBy;
  final SortOrder sortOrder;
  final String searchQuery;
  final bool isFullscreen;
  final bool showControls;
  final bool showPlaylist;
  final bool showEqualizer;

  const PlayerUIState({
    this.viewMode = ViewMode.grid,
    this.sortBy = SortBy.name,
    this.sortOrder = SortOrder.ascending,
    this.searchQuery = '',
    this.isFullscreen = false,
    this.showControls = true,
    this.showPlaylist = false,
    this.showEqualizer = false,
  });

  PlayerUIState copyWith({
    ViewMode? viewMode,
    SortBy? sortBy,
    SortOrder? sortOrder,
    String? searchQuery,
    bool? isFullscreen,
    bool? showControls,
    bool? showPlaylist,
    bool? showEqualizer,
  }) {
    return PlayerUIState(
      viewMode: viewMode ?? this.viewMode,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
      searchQuery: searchQuery ?? this.searchQuery,
      isFullscreen: isFullscreen ?? this.isFullscreen,
      showControls: showControls ?? this.showControls,
      showPlaylist: showPlaylist ?? this.showPlaylist,
      showEqualizer: showEqualizer ?? this.showEqualizer,
    );
  }
}

/// Combined player state
class PlayerStateModel {
  final PlayerState playerState;
  final PlaybackState playbackState;
  final PlayerConfiguration configuration;
  final PlayerUIState uiState;
  final MediaFile? currentMedia;
  final Playlist? currentPlaylist;
  final int currentIndex;
  final Duration position;
  final Duration duration;
  final List<PlaybackEvent> recentEvents;

  const PlayerStateModel({
    this.playerState = PlayerState.stopped,
    this.playbackState = PlaybackState.stopped,
    this.configuration = const PlayerConfiguration(),
    this.uiState = const PlayerUIState(),
    this.currentMedia,
    this.currentPlaylist,
    this.currentIndex = 0,
    this.position = Duration.zero,
    this.duration = Duration.zero,
    this.recentEvents = const [],
  });

  PlayerStateModel copyWith({
    PlayerState? playerState,
    PlaybackState? playbackState,
    PlayerConfiguration? configuration,
    PlayerUIState? uiState,
    MediaFile? currentMedia,
    Playlist? currentPlaylist,
    int? currentIndex,
    Duration? position,
    Duration? duration,
    List<PlaybackEvent>? recentEvents,
  }) {
    return PlayerStateModel(
      playerState: playerState ?? this.playerState,
      playbackState: playbackState ?? this.playbackState,
      configuration: configuration ?? this.configuration,
      uiState: uiState ?? this.uiState,
      currentMedia: currentMedia ?? this.currentMedia,
      currentPlaylist: currentPlaylist ?? this.currentPlaylist,
      currentIndex: currentIndex ?? this.currentIndex,
      position: position ?? this.position,
      duration: duration ?? this.duration,
      recentEvents: recentEvents ?? this.recentEvents,
    );
  }

  bool get isPlaying => playbackState == PlaybackState.playing;
  bool get isPaused => playbackState == PlaybackState.paused;
  bool get isStopped => playbackState == PlaybackState.stopped;
  bool get isBuffering => playbackState == PlaybackState.buffering;
  bool get hasError => playbackState == PlaybackState.error;
  
  double get progress {
    if (duration.inMilliseconds == 0) return 0.0;
    return position.inMilliseconds / duration.inMilliseconds;
  }
}
