import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/file_manager_models.dart';
import '../services/file_manager_service.dart';
import '../services/file_operations_service.dart';
import '../services/file_viewer_service.dart';
import 'file_context_menu.dart';
import 'file_operation_progress_dialog.dart';
import 'universal_file_viewer.dart';

class FilePane extends ConsumerStatefulWidget {
  final String path;
  final bool isActive;
  final String paneId;
  final Function(String) onPathChanged;
  final VoidCallback onPaneActivated;

  const FilePane({
    super.key,
    required this.path,
    required this.isActive,
    required this.paneId,
    required this.onPathChanged,
    required this.onPaneActivated,
  });

  @override
  ConsumerState<FilePane> createState() => _FilePaneState();
}

class _FilePaneState extends ConsumerState<FilePane> {
  List<FileSystemItem> _items = [];
  bool _isLoading = false;
  String _searchQuery = '';
  bool _isGridView = false;
  final Set<String> _selectedItems = {};
  String? _lastSelectedItem;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _loadDirectory();
  }

  @override
  void didUpdateWidget(FilePane oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.path != widget.path) {
      _loadDirectory();
    }
  }

  Future<void> _loadDirectory() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await FileManagerService.navigateToDirectory(widget.path);
      _items = FileManagerService.getCurrentItems();
    } catch (error) {
      _showError('Error loading directory: $error');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      onKeyEvent: _handleKeyEvent,
      child: GestureDetector(
        onTap: () {
          widget.onPaneActivated();
          _focusNode.requestFocus();
        },
        onSecondaryTapDown: (details) => _showContextMenu(context, details.globalPosition),
        child: Container(
          decoration: BoxDecoration(
            color: widget.isActive ? Colors.white : const Color(0xFFFAFAFA),
          ),
          child: Column(
            children: [
              // Pane header
              _buildPaneHeader(),

              // Search bar
              if (widget.isActive) _buildSearchBar(),

              // File list
              Expanded(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _buildFileList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaneHeader() {
    return Container(
      height: 32,
      decoration: BoxDecoration(
        color: widget.isActive ? const Color(0xFFE67E22) : const Color(0xFFF8F9FA),
        border: const Border(
          bottom: BorderSide(color: Color(0xFFE9ECEF)),
        ),
      ),
      child: Row(
        children: [
          const SizedBox(width: 8),
          Icon(
            Icons.folder,
            size: 16,
            color: widget.isActive ? Colors.white : const Color(0xFF495057),
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              _getDirectoryName(widget.path),
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: widget.isActive ? Colors.white : const Color(0xFF495057),
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (widget.isActive) ...[
            IconButton(
              icon: Icon(
                _isGridView ? Icons.list : Icons.grid_view,
                size: 16,
                color: Colors.white,
              ),
              onPressed: () => setState(() => _isGridView = !_isGridView),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
            ),
          ],
          const SizedBox(width: 4),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(8),
      child: TextField(
        decoration: const InputDecoration(
          hintText: 'Search...',
          prefixIcon: Icon(Icons.search, size: 16),
          border: OutlineInputBorder(),
          contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          isDense: true,
        ),
        style: const TextStyle(fontSize: 12),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildFileList() {
    final filteredItems = _searchQuery.isEmpty
        ? _items
        : _items.where((item) => 
            item.name.toLowerCase().contains(_searchQuery.toLowerCase())).toList();

    if (filteredItems.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.folder_open,
              size: 48,
              color: Color(0xFF7F8C8D),
            ),
            SizedBox(height: 8),
            Text(
              'No files found',
              style: TextStyle(
                color: Color(0xFF7F8C8D),
              ),
            ),
          ],
        ),
      );
    }

    if (_isGridView) {
      return _buildGridView(filteredItems);
    } else {
      return _buildListView(filteredItems);
    }
  }

  Widget _buildListView(List<FileSystemItem> items) {
    return ListView.builder(
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        final isSelected = _selectedItems.contains(item.path);
        
        return GestureDetector(
          onTap: () => _handleItemTap(
            item,
            isCtrlPressed: HardwareKeyboard.instance.isControlPressed,
            isShiftPressed: HardwareKeyboard.instance.isShiftPressed,
          ),
          onLongPress: () => _toggleSelection(item.path),
          onSecondaryTap: () => _showContextMenu(context, Offset.zero),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: isSelected ? const Color(0xFFE67E22).withValues(alpha: 0.1) : null,
              border: isSelected ? Border.all(color: const Color(0xFFE67E22), width: 1) : null,
            ),
            child: Row(
              children: [
                Icon(
                  item.isDirectory ? Icons.folder : FileViewerService.getFileIcon(item.name),
                  size: 16,
                  color: item.isDirectory ? const Color(0xFFE67E22) : FileViewerService.getFileColor(item.name),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    item.name,
                    style: const TextStyle(fontSize: 12),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (!item.isDirectory) ...[
                  Text(
                    _formatFileSize(item.size),
                    style: const TextStyle(
                      fontSize: 10,
                      color: Color(0xFF7F8C8D),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildGridView(List<FileSystemItem> items) {
    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 0.8,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        final isSelected = _selectedItems.contains(item.path);
        
        return GestureDetector(
          onTap: () => _handleItemTap(
            item,
            isCtrlPressed: HardwareKeyboard.instance.isControlPressed,
            isShiftPressed: HardwareKeyboard.instance.isShiftPressed,
          ),
          onLongPress: () => _toggleSelection(item.path),
          onSecondaryTap: () => _showContextMenu(context, Offset.zero),
          child: Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: isSelected ? const Color(0xFFE67E22).withValues(alpha: 0.1) : null,
              border: isSelected ? Border.all(color: const Color(0xFFE67E22), width: 1) : null,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  item.isDirectory ? Icons.folder : FileViewerService.getFileIcon(item.name),
                  size: 32,
                  color: item.isDirectory ? const Color(0xFFE67E22) : FileViewerService.getFileColor(item.name),
                ),
                const SizedBox(height: 4),
                Text(
                  item.name,
                  style: const TextStyle(fontSize: 10),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _handleItemTap(FileSystemItem item, {bool isCtrlPressed = false, bool isShiftPressed = false}) {
    if (isCtrlPressed) {
      // Ctrl+Click: Toggle selection
      _toggleSelection(item.path);
      _lastSelectedItem = item.path;
    } else if (isShiftPressed && _lastSelectedItem != null) {
      // Shift+Click: Range selection
      _selectRange(_lastSelectedItem!, item.path);
    } else {
      // Normal click: Clear selection and navigate/open
      _clearSelection();
      if (item.isDirectory) {
        widget.onPathChanged(item.path);
      } else {
        _openFile(item);
      }
      _lastSelectedItem = item.path;
    }
  }

  void _toggleSelection(String path) {
    setState(() {
      if (_selectedItems.contains(path)) {
        _selectedItems.remove(path);
        FileOperationsService.removeFromSelection(path);
      } else {
        _selectedItems.add(path);
        FileOperationsService.addToSelection(path);
      }
    });
  }

  void _clearSelection() {
    setState(() {
      _selectedItems.clear();
    });
    FileOperationsService.clearSelection();
  }

  void _selectRange(String startPath, String endPath) {
    final startIndex = _items.indexWhere((item) => item.path == startPath);
    final endIndex = _items.indexWhere((item) => item.path == endPath);

    if (startIndex != -1 && endIndex != -1) {
      final minIndex = startIndex < endIndex ? startIndex : endIndex;
      final maxIndex = startIndex > endIndex ? startIndex : endIndex;

      setState(() {
        _selectedItems.clear();
        for (int i = minIndex; i <= maxIndex; i++) {
          _selectedItems.add(_items[i].path);
          FileOperationsService.addToSelection(_items[i].path);
        }
      });
    }
  }

  void _openFile(FileSystemItem item) {
    if (FileViewerService.canViewFile(item.name)) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => UniversalFileViewer(
            filePath: item.path,
            onClose: () {
              // Refresh the file list in case the file was modified
              _loadDirectory();
            },
          ),
        ),
      );
    } else {
      _showInfo('File type not supported for preview: ${item.name}');
    }
  }

  String _getDirectoryName(String path) {
    if (path.isEmpty || path == '/') return 'Root';
    return path.split('/').last;
  }



  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showInfo(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF3498DB),
      ),
    );
  }

  // Keyboard event handling
  KeyEventResult _handleKeyEvent(FocusNode node, KeyEvent event) {
    if (event is KeyDownEvent) {
      // Ctrl+A: Select all
      if (event.logicalKey == LogicalKeyboardKey.keyA &&
          HardwareKeyboard.instance.isControlPressed) {
        _selectAll();
        return KeyEventResult.handled;
      }

      // Ctrl+C: Copy
      if (event.logicalKey == LogicalKeyboardKey.keyC &&
          HardwareKeyboard.instance.isControlPressed) {
        _copySelected();
        return KeyEventResult.handled;
      }

      // Ctrl+X: Cut
      if (event.logicalKey == LogicalKeyboardKey.keyX &&
          HardwareKeyboard.instance.isControlPressed) {
        _cutSelected();
        return KeyEventResult.handled;
      }

      // Ctrl+V: Paste
      if (event.logicalKey == LogicalKeyboardKey.keyV &&
          HardwareKeyboard.instance.isControlPressed) {
        _pasteItems();
        return KeyEventResult.handled;
      }

      // Delete: Delete selected items
      if (event.logicalKey == LogicalKeyboardKey.delete) {
        _deleteSelected();
        return KeyEventResult.handled;
      }

      // F2: Rename
      if (event.logicalKey == LogicalKeyboardKey.f2) {
        _renameSelected();
        return KeyEventResult.handled;
      }

      // F5: Refresh
      if (event.logicalKey == LogicalKeyboardKey.f5) {
        _loadDirectory();
        return KeyEventResult.handled;
      }

      // Escape: Clear selection
      if (event.logicalKey == LogicalKeyboardKey.escape) {
        _clearSelection();
        return KeyEventResult.handled;
      }
    }

    return KeyEventResult.ignored;
  }

  // Context menu
  void _showContextMenu(BuildContext context, Offset position) {
    final selectedFileItems = _selectedItems
        .map((path) => _items.firstWhere((item) => item.path == path))
        .toList();

    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        position.dx,
        position.dy,
        position.dx + 1,
        position.dy + 1,
      ),
      items: [
        PopupMenuItem(
          child: FileContextMenu(
            selectedItems: selectedFileItems,
            onRefresh: _loadDirectory,
            onNavigate: widget.onPathChanged,
          ),
        ),
      ],
    );
  }

  // Keyboard shortcut actions
  void _selectAll() {
    setState(() {
      _selectedItems.clear();
      for (final item in _items) {
        _selectedItems.add(item.path);
        FileOperationsService.addToSelection(item.path);
      }
    });
  }

  void _copySelected() {
    if (_selectedItems.isNotEmpty) {
      FileOperationsService.copyToClipboard(_selectedItems.toList());
      _showInfo('Copied ${_selectedItems.length} item(s)');
    }
  }

  void _cutSelected() {
    if (_selectedItems.isNotEmpty) {
      FileOperationsService.cutToClipboard(_selectedItems.toList());
      _showInfo('Cut ${_selectedItems.length} item(s)');
    }
  }

  void _pasteItems() async {
    if (FileOperationsService.hasClipboardItems) {
      try {
        final operationId = await FileOperationsService.pasteFromClipboard(widget.path);
        if (operationId != null) {
          _showFileOperationProgress(operationId, 'Pasting Items');
        }
      } catch (error) {
        _showError('Error pasting items: $error');
      }
    }
  }

  void _deleteSelected() async {
    if (_selectedItems.isNotEmpty) {
      final confirmed = await _showDeleteConfirmation();
      if (confirmed) {
        try {
          final operationId = await FileOperationsService.deleteItems(_selectedItems.toList());
          _showFileOperationProgress(operationId, 'Deleting Items');
          _clearSelection();
        } catch (error) {
          _showError('Error deleting items: $error');
        }
      }
    }
  }

  void _renameSelected() {
    if (_selectedItems.length == 1) {
      final item = _items.firstWhere((item) => item.path == _selectedItems.first);
      _showRenameDialog(item);
    }
  }

  Future<bool> _showDeleteConfirmation() async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Items'),
        content: Text(
          'Are you sure you want to delete ${_selectedItems.length} item(s)? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    ) ?? false;
  }

  void _showRenameDialog(FileSystemItem item) {
    String newName = item.name;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Rename Item'),
        content: TextField(
          controller: TextEditingController(text: newName),
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            labelText: 'New name',
          ),
          onChanged: (value) => newName = value,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              if (newName.isNotEmpty && newName != item.name) {
                try {
                  await FileOperationsService.renameItem(item.path, newName);
                  _loadDirectory();
                  _showInfo('Item renamed successfully');
                } catch (error) {
                  _showError('Error renaming item: $error');
                }
              }
            },
            child: const Text('Rename'),
          ),
        ],
      ),
    );
  }

  void _showFileOperationProgress(String operationId, String title) {
    showFileOperationProgress(
      context: context,
      operationId: operationId,
      operationTitle: title,
      onCancel: () => FileOperationsService.cancelOperation(operationId),
    ).then((success) {
      if (success == true) {
        _loadDirectory(); // Refresh after successful operation
      }
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }
}
