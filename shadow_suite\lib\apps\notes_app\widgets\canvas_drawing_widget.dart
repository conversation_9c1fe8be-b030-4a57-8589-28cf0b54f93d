import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import '../models/note_models.dart';

enum DrawingTool { pen, pencil, brush, eraser }

/// Canvas drawing widget for drawing notes
class CanvasDrawingWidget extends StatefulWidget {
  final CanvasData? initialData;
  final Function(CanvasData) onCanvasChanged;
  final bool isReadOnly;

  const CanvasDrawingWidget({
    super.key,
    this.initialData,
    required this.onCanvasChanged,
    this.isReadOnly = false,
  });

  @override
  State<CanvasDrawingWidget> createState() => _CanvasDrawingWidgetState();
}

class _CanvasDrawingWidgetState extends State<CanvasDrawingWidget> {
  List<DrawingStroke> _strokes = [];
  DrawingStroke? _currentStroke;
  Color _selectedColor = Colors.black;
  double _strokeWidth = 2.0;
  DrawingTool _selectedTool = DrawingTool.pen;
  bool _isErasing = false;

  @override
  void initState() {
    super.initState();
    if (widget.initialData != null) {
      _strokes = List.from(widget.initialData!.strokes);
      // CanvasData doesn't store these, so we use defaults
      _selectedColor = Colors.black;
      _strokeWidth = 2.0;
      _selectedTool = DrawingTool.pen;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (!widget.isReadOnly) _buildToolbar(),
        Expanded(
          child: Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: GestureDetector(
                onPanStart: widget.isReadOnly ? null : _onPanStart,
                onPanUpdate: widget.isReadOnly ? null : _onPanUpdate,
                onPanEnd: widget.isReadOnly ? null : _onPanEnd,
                child: CustomPaint(
                  painter: CanvasPainter(_strokes, _currentStroke),
                  size: Size.infinite,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Tools Row
          Row(
            children: [
              _buildToolButton(DrawingTool.pen, Icons.edit, 'Pen'),
              _buildToolButton(DrawingTool.pencil, Icons.create, 'Pencil'),
              _buildToolButton(DrawingTool.brush, Icons.brush, 'Brush'),
              _buildToolButton(
                DrawingTool.eraser,
                Icons.cleaning_services,
                'Eraser',
              ),
              const SizedBox(width: 16),
              IconButton(
                icon: const Icon(Icons.undo),
                onPressed: _strokes.isNotEmpty ? _undo : null,
                tooltip: 'Undo',
              ),
              IconButton(
                icon: const Icon(Icons.clear),
                onPressed: _strokes.isNotEmpty ? _clearCanvas : null,
                tooltip: 'Clear All',
              ),
            ],
          ),
          const SizedBox(height: 8),
          // Color and Stroke Width Row
          Row(
            children: [
              const Text('Color: '),
              ..._buildColorPalette(),
              const SizedBox(width: 16),
              const Text('Size: '),
              SizedBox(
                width: 100,
                child: Slider(
                  value: _strokeWidth,
                  min: 1.0,
                  max: 20.0,
                  divisions: 19,
                  label: _strokeWidth.round().toString(),
                  onChanged: (value) {
                    setState(() => _strokeWidth = value);
                    _notifyCanvasChanged();
                  },
                ),
              ),
              Text('${_strokeWidth.round()}px'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildToolButton(DrawingTool tool, IconData icon, String tooltip) {
    final isSelected = _selectedTool == tool;
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: IconButton(
        icon: Icon(icon),
        onPressed: () {
          setState(() {
            _selectedTool = tool;
            _isErasing = tool == DrawingTool.eraser;
          });
          _notifyCanvasChanged();
        },
        style: IconButton.styleFrom(
          backgroundColor: isSelected
              ? Colors.blue.withValues(alpha: 0.2)
              : null,
          foregroundColor: isSelected ? Colors.blue : null,
        ),
        tooltip: tooltip,
      ),
    );
  }

  List<Widget> _buildColorPalette() {
    final colors = [
      Colors.black,
      Colors.red,
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.brown,
      Colors.pink,
      Colors.teal,
      Colors.indigo,
      Colors.amber,
      Colors.grey,
    ];

    return colors.map((color) {
      return GestureDetector(
        onTap: () {
          setState(() => _selectedColor = color);
          _notifyCanvasChanged();
        },
        child: Container(
          width: 24,
          height: 24,
          margin: const EdgeInsets.only(right: 4),
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
            border: Border.all(
              color: _selectedColor == color ? Colors.black : Colors.grey,
              width: _selectedColor == color ? 2 : 1,
            ),
          ),
        ),
      );
    }).toList();
  }

  void _onPanStart(DragStartDetails details) {
    final localPosition = details.localPosition;

    if (_isErasing) {
      _eraseAtPoint(localPosition);
    } else {
      _currentStroke = DrawingStroke(
        points: [localPosition],
        color: _selectedColor,
        strokeWidth: _strokeWidth,
        createdAt: DateTime.now(),
      );
    }
  }

  void _onPanUpdate(DragUpdateDetails details) {
    final localPosition = details.localPosition;

    if (_isErasing) {
      _eraseAtPoint(localPosition);
    } else if (_currentStroke != null) {
      setState(() {
        _currentStroke = _currentStroke!.copyWith(
          points: [..._currentStroke!.points, localPosition],
        );
      });
    }
  }

  void _onPanEnd(DragEndDetails details) {
    if (_currentStroke != null && !_isErasing) {
      setState(() {
        _strokes.add(_currentStroke!);
        _currentStroke = null;
      });
      _notifyCanvasChanged();
    }
  }

  void _eraseAtPoint(Offset point) {
    setState(() {
      _strokes.removeWhere((stroke) {
        return stroke.points.any((strokePoint) {
          final distance = (strokePoint - point).distance;
          return distance < 20.0; // Eraser radius
        });
      });
    });
    _notifyCanvasChanged();
  }

  void _undo() {
    if (_strokes.isNotEmpty) {
      setState(() {
        _strokes.removeLast();
      });
      _notifyCanvasChanged();
    }
  }

  void _clearCanvas() {
    setState(() {
      _strokes.clear();
    });
    _notifyCanvasChanged();
  }

  void _notifyCanvasChanged() {
    final canvasData = CanvasData(
      strokes: _strokes,
      backgroundColor: Colors.white,
      canvasSize: const Size(400, 600),
    );
    widget.onCanvasChanged(canvasData);
  }
}

/// Custom painter for drawing the canvas
class CanvasPainter extends CustomPainter {
  final List<DrawingStroke> strokes;
  final DrawingStroke? currentStroke;

  CanvasPainter(this.strokes, [this.currentStroke]);

  @override
  void paint(Canvas canvas, Size size) {
    // Draw completed strokes
    for (final stroke in strokes) {
      _drawStroke(canvas, stroke);
    }

    // Draw current stroke being drawn
    if (currentStroke != null) {
      _drawStroke(canvas, currentStroke!);
    }
  }

  void _drawStroke(Canvas canvas, DrawingStroke stroke) {
    final paint = Paint()
      ..color = stroke.color
      ..strokeWidth = stroke.strokeWidth
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..style = PaintingStyle.stroke;

    if (stroke.points.length > 1) {
      final uiPath = ui.Path();
      uiPath.moveTo(stroke.points.first.dx, stroke.points.first.dy);

      for (int i = 1; i < stroke.points.length; i++) {
        uiPath.lineTo(stroke.points[i].dx, stroke.points[i].dy);
      }

      canvas.drawPath(uiPath, paint);
    } else if (stroke.points.length == 1) {
      // Draw a single point
      canvas.drawCircle(stroke.points.first, stroke.strokeWidth / 2, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
