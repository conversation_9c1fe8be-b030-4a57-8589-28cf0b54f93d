import 'dart:io';
import 'dart:typed_data';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import '../models/excel_app_tool.dart';

class ExcelFileService {
  static const String _toolsDirectory = 'excel_to_app_tools';
  static const String _templatesDirectory = 'excel_to_app_templates';

  // Import Excel file (.xlsx)
  static Future<ExcelFileImportResult> importExcelFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) {
        return ExcelFileImportResult.cancelled();
      }

      final file = result.files.first;
      if (file.bytes == null) {
        return ExcelFileImportResult.error('Failed to read file data');
      }

      // Parse Excel file (simplified implementation)
      final spreadsheetData = await _parseExcelFile(file.bytes!, file.name);
      
      return ExcelFileImportResult.success(
        fileName: file.name,
        spreadsheet: spreadsheetData,
      );
    } catch (e) {
      return ExcelFileImportResult.error('Failed to import Excel file: $e');
    }
  }

  // Export tool as Excel file
  static Future<ExcelFileExportResult> exportToolAsExcel(ExcelAppTool tool) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName = '${tool.name.replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}.xlsx';
      final filePath = '${directory.path}/$fileName';

      // Generate Excel file data (simplified implementation)
      final excelData = await _generateExcelFile(tool);
      
      final file = File(filePath);
      await file.writeAsBytes(excelData);

      return ExcelFileExportResult.success(
        filePath: filePath,
        fileName: fileName,
      );
    } catch (e) {
      return ExcelFileExportResult.error('Failed to export Excel file: $e');
    }
  }

  // Save tool to local storage
  static Future<ToolSaveResult> saveTool(ExcelAppTool tool) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final toolsDir = Directory('${directory.path}/$_toolsDirectory');
      
      if (!await toolsDir.exists()) {
        await toolsDir.create(recursive: true);
      }

      final fileName = '${tool.id}.json';
      final filePath = '${toolsDir.path}/$fileName';
      final file = File(filePath);

      final toolJson = tool.toJson();
      await file.writeAsString(toolJson);

      return ToolSaveResult.success(filePath: filePath);
    } catch (e) {
      return ToolSaveResult.error('Failed to save tool: $e');
    }
  }

  // Load tool from local storage
  static Future<ToolLoadResult> loadTool(String toolId) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final filePath = '${directory.path}/$_toolsDirectory/$toolId.json';
      final file = File(filePath);

      if (!await file.exists()) {
        return ToolLoadResult.error('Tool file not found');
      }

      final jsonString = await file.readAsString();
      final tool = ExcelAppTool.fromJson(jsonString);

      return ToolLoadResult.success(tool: tool);
    } catch (e) {
      return ToolLoadResult.error('Failed to load tool: $e');
    }
  }

  // Get all saved tools
  static Future<List<ExcelAppTool>> getAllSavedTools() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final toolsDir = Directory('${directory.path}/$_toolsDirectory');
      
      if (!await toolsDir.exists()) {
        return [];
      }

      final files = await toolsDir.list().where((entity) => 
        entity is File && entity.path.endsWith('.json')
      ).toList();

      final tools = <ExcelAppTool>[];
      for (final file in files) {
        try {
          final jsonString = await (file as File).readAsString();
          final tool = ExcelAppTool.fromJson(jsonString);
          tools.add(tool);
        } catch (e) {
          // Skip corrupted files
          continue;
        }
      }

      return tools;
    } catch (e) {
      return [];
    }
  }

  // Delete tool
  static Future<bool> deleteTool(String toolId) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final filePath = '${directory.path}/$_toolsDirectory/$toolId.json';
      final file = File(filePath);

      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  // Export tool as template
  static Future<TemplateExportResult> exportAsTemplate(ExcelAppTool tool, String templateName, String description) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final templatesDir = Directory('${directory.path}/$_templatesDirectory');
      
      if (!await templatesDir.exists()) {
        await templatesDir.create(recursive: true);
      }

      final template = ExcelAppTemplate(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: templateName,
        description: description,
        tool: tool,
        createdAt: DateTime.now(),
        author: 'User', // In a real app, this would be the current user
      );

      final fileName = '${template.id}.json';
      final filePath = '${templatesDir.path}/$fileName';
      final file = File(filePath);

      final templateJson = template.toJson();
      await file.writeAsString(templateJson);

      return TemplateExportResult.success(template: template);
    } catch (e) {
      return TemplateExportResult.error('Failed to export template: $e');
    }
  }

  // Get all templates
  static Future<List<ExcelAppTemplate>> getAllTemplates() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final templatesDir = Directory('${directory.path}/$_templatesDirectory');
      
      if (!await templatesDir.exists()) {
        return [];
      }

      final files = await templatesDir.list().where((entity) => 
        entity is File && entity.path.endsWith('.json')
      ).toList();

      final templates = <ExcelAppTemplate>[];
      for (final file in files) {
        try {
          final jsonString = await (file as File).readAsString();
          final template = ExcelAppTemplate.fromJson(jsonString);
          templates.add(template);
        } catch (e) {
          // Skip corrupted files
          continue;
        }
      }

      return templates;
    } catch (e) {
      return [];
    }
  }

  // Import tool from template
  static Future<ToolLoadResult> importFromTemplate(ExcelAppTemplate template) async {
    try {
      // Create a new tool from the template
      final newTool = template.tool.copyWith(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: '${template.name} (Copy)',
        createdAt: DateTime.now(),
      );

      return ToolLoadResult.success(tool: newTool);
    } catch (e) {
      return ToolLoadResult.error('Failed to import from template: $e');
    }
  }

  // Private helper methods
  static Future<ExcelSpreadsheet> _parseExcelFile(Uint8List bytes, String fileName) async {
    // This is a simplified implementation
    // In a real app, you'd use a library like 'excel' package to parse the file
    
    return ExcelSpreadsheet(
      name: fileName,
      rows: 20,
      columns: 10,
      lastModified: DateTime.now(),
      cells: {
        'A1': ExcelCell(
          address: 'A1',
          value: 'Imported from $fileName',
          formula: null,
          isFormula: false,
        ),
        'B1': ExcelCell(
          address: 'B1',
          value: 'Sample Data',
          formula: null,
          isFormula: false,
        ),
        'A2': ExcelCell(
          address: 'A2',
          value: 100,
          formula: null,
          isFormula: false,
        ),
        'B2': ExcelCell(
          address: 'B2',
          value: 200,
          formula: null,
          isFormula: false,
        ),
        'C2': ExcelCell(
          address: 'C2',
          value: null,
          formula: '=A2+B2',
          isFormula: true,
        ),
      },
    );
  }

  static Future<Uint8List> _generateExcelFile(ExcelAppTool tool) async {
    // This is a simplified implementation
    // In a real app, you'd use a library like 'excel' package to generate the file
    
    // For now, return a placeholder byte array
    var placeholder = 'Excel file for ${tool.name}\n';
    placeholder += 'Created: ${tool.createdAt}\n';
    placeholder += 'Components: ${tool.uiComponents.length}\n';
    placeholder += 'Cells: ${tool.spreadsheet.cells.length}\n';

    return Uint8List.fromList(placeholder.codeUnits);
  }
}

// Result classes
class ExcelFileImportResult {
  final bool isSuccess;
  final String? fileName;
  final ExcelSpreadsheet? spreadsheet;
  final String? error;
  final bool isCancelled;

  const ExcelFileImportResult._({
    required this.isSuccess,
    this.fileName,
    this.spreadsheet,
    this.error,
    this.isCancelled = false,
  });

  factory ExcelFileImportResult.success({
    required String fileName,
    required ExcelSpreadsheet spreadsheet,
  }) {
    return ExcelFileImportResult._(
      isSuccess: true,
      fileName: fileName,
      spreadsheet: spreadsheet,
    );
  }

  factory ExcelFileImportResult.error(String error) {
    return ExcelFileImportResult._(
      isSuccess: false,
      error: error,
    );
  }

  factory ExcelFileImportResult.cancelled() {
    return const ExcelFileImportResult._(
      isSuccess: false,
      isCancelled: true,
    );
  }
}

class ExcelFileExportResult {
  final bool isSuccess;
  final String? filePath;
  final String? fileName;
  final String? error;

  const ExcelFileExportResult._({
    required this.isSuccess,
    this.filePath,
    this.fileName,
    this.error,
  });

  factory ExcelFileExportResult.success({
    required String filePath,
    required String fileName,
  }) {
    return ExcelFileExportResult._(
      isSuccess: true,
      filePath: filePath,
      fileName: fileName,
    );
  }

  factory ExcelFileExportResult.error(String error) {
    return ExcelFileExportResult._(
      isSuccess: false,
      error: error,
    );
  }
}

class ToolSaveResult {
  final bool isSuccess;
  final String? filePath;
  final String? error;

  const ToolSaveResult._({
    required this.isSuccess,
    this.filePath,
    this.error,
  });

  factory ToolSaveResult.success({required String filePath}) {
    return ToolSaveResult._(isSuccess: true, filePath: filePath);
  }

  factory ToolSaveResult.error(String error) {
    return ToolSaveResult._(isSuccess: false, error: error);
  }
}

class ToolLoadResult {
  final bool isSuccess;
  final ExcelAppTool? tool;
  final String? error;

  const ToolLoadResult._({
    required this.isSuccess,
    this.tool,
    this.error,
  });

  factory ToolLoadResult.success({required ExcelAppTool tool}) {
    return ToolLoadResult._(isSuccess: true, tool: tool);
  }

  factory ToolLoadResult.error(String error) {
    return ToolLoadResult._(isSuccess: false, error: error);
  }
}

class TemplateExportResult {
  final bool isSuccess;
  final ExcelAppTemplate? template;
  final String? error;

  const TemplateExportResult._({
    required this.isSuccess,
    this.template,
    this.error,
  });

  factory TemplateExportResult.success({required ExcelAppTemplate template}) {
    return TemplateExportResult._(isSuccess: true, template: template);
  }

  factory TemplateExportResult.error(String error) {
    return TemplateExportResult._(isSuccess: false, error: error);
  }
}
