import 'dart:math' as math;
import '../models/excel_app_tool.dart';

// Formula validation result class
class FormulaValidationResult {
  final bool isValid;
  final String message;
  final List<String>? suggestions;

  FormulaValidationResult({
    required this.isValid,
    required this.message,
    this.suggestions,
  });
}

class ExcelFormulaEngine {
  static final ExcelFormulaEngine _instance = ExcelFormulaEngine._internal();
  factory ExcelFormulaEngine() => _instance;
  ExcelFormulaEngine._internal();

  // Excel function definitions with categories
  static const Map<String, List<String>> excelFunctionsByCategory = {
    'Math & Trig': [
      'SUM', 'AVERAGE', 'COUNT', 'MIN', 'MAX', 'ROUND', 'ROUNDUP', 'ROUNDDOWN',
      'ABS', 'SQRT', 'POWER', 'MOD', 'PI', 'SIN', 'COS', 'TAN', 'ASIN', 'ACOS',
      'ATAN', 'CEILING', 'FLOOR', 'TRUNC', 'INT', 'SIGN', 'RAND', 'RANDBETWEEN',
      'EXP', 'LN', 'LOG', 'LOG10', 'DEGREES', 'RADIANS'
    ],
    'Statistical': [
      'COUNTIF', 'SUMIF', 'AVERAGEIF', 'COUNTIFS', 'SUMIFS', 'AVERAGEIFS',
      'MEDIAN', 'MODE', 'STDEV', 'VAR', 'COUNTA', 'LARGE', 'SMALL',
      'PERCENTILE', 'QUARTILE', 'RANK', 'FREQUENCY'
    ],
    'Logical': [
      'IF', 'AND', 'OR', 'NOT', 'TRUE', 'FALSE', 'IFERROR', 'IFNA',
      'IFS', 'SWITCH'
    ],
    'Text': [
      'CONCATENATE', 'CONCAT', 'TEXTJOIN', 'LEFT', 'RIGHT', 'MID', 'LEN',
      'UPPER', 'LOWER', 'PROPER', 'TRIM', 'SUBSTITUTE', 'REPLACE', 'FIND',
      'SEARCH', 'TEXT', 'VALUE', 'EXACT', 'CLEAN', 'CHAR', 'CODE'
    ],
    'Date & Time': [
      'TODAY', 'NOW', 'YEAR', 'MONTH', 'DAY', 'HOUR', 'MINUTE', 'SECOND',
      'DATE', 'TIME', 'WEEKDAY', 'WORKDAY', 'NETWORKDAYS', 'DATEDIF',
      'EOMONTH', 'EDATE', 'DATEVALUE', 'TIMEVALUE'
    ],
    'Lookup & Reference': [
      'VLOOKUP', 'HLOOKUP', 'INDEX', 'MATCH', 'CHOOSE', 'LOOKUP', 'OFFSET',
      'INDIRECT', 'ROW', 'COLUMN', 'ROWS', 'COLUMNS'
    ],
    'Information': [
      'ISBLANK', 'ISNUMBER', 'ISTEXT', 'ISERROR', 'ISNA', 'ISLOGICAL',
      'TYPE', 'CELL', 'INFO', 'N', 'NA', 'ERROR.TYPE'
    ],
  };

  static List<String> get allExcelFunctions {
    return excelFunctionsByCategory.values.expand((functions) => functions).toList();
  }

  // Calculation cache for performance
  final Map<String, dynamic> _calculationCache = {};
  final Set<String> _calculatingCells = {};

  // Calculate formula result with enhanced error handling
  dynamic calculateFormula(String formula, Map<String, ExcelCell> cells, [String? currentCellAddress]) {
    try {
      if (!formula.startsWith('=')) {
        return formula;
      }

      // Prevent circular references
      if (currentCellAddress != null && _calculatingCells.contains(currentCellAddress)) {
        return '#CIRCULAR!';
      }

      if (currentCellAddress != null) {
        _calculatingCells.add(currentCellAddress);
      }

      // Check cache first
      final cacheKey = '$formula|${cells.hashCode}';
      if (_calculationCache.containsKey(cacheKey)) {
        if (currentCellAddress != null) {
          _calculatingCells.remove(currentCellAddress);
        }
        return _calculationCache[cacheKey];
      }

      // Remove the = sign and normalize
      String expression = formula.substring(1).trim();

      // Evaluate the expression
      final result = _evaluateExpression(expression, cells, currentCellAddress);

      // Cache the result
      _calculationCache[cacheKey] = result;

      if (currentCellAddress != null) {
        _calculatingCells.remove(currentCellAddress);
      }

      return result;
    } catch (e) {
      if (currentCellAddress != null) {
        _calculatingCells.remove(currentCellAddress);
      }
      return '#ERROR!';
    }
  }

  // Clear calculation cache when cells change
  void clearCache() {
    _calculationCache.clear();
    _calculatingCells.clear();
  }

  // Get function suggestions for auto-complete
  List<String> getFunctionSuggestions(String partial) {
    final upperPartial = partial.toUpperCase();
    return allExcelFunctions
        .where((func) => func.startsWith(upperPartial))
        .take(10)
        .toList();
  }

  // Get function syntax help
  String getFunctionSyntax(String functionName) {
    switch (functionName.toUpperCase()) {
      // Mathematical Functions
      case 'SUM':
        return 'SUM(number1, [number2], ...)';
      case 'AVERAGE':
        return 'AVERAGE(number1, [number2], ...)';
      case 'COUNT':
        return 'COUNT(value1, [value2], ...)';
      case 'MIN':
        return 'MIN(number1, [number2], ...)';
      case 'MAX':
        return 'MAX(number1, [number2], ...)';
      case 'ROUND':
        return 'ROUND(number, num_digits)';
      case 'ROUNDUP':
        return 'ROUNDUP(number, num_digits)';
      case 'ROUNDDOWN':
        return 'ROUNDDOWN(number, num_digits)';
      case 'ABS':
        return 'ABS(number)';
      case 'SQRT':
        return 'SQRT(number)';
      case 'POWER':
        return 'POWER(number, power)';
      case 'MOD':
        return 'MOD(number, divisor)';
      case 'PI':
        return 'PI()';
      case 'RAND':
        return 'RAND()';
      case 'RANDBETWEEN':
        return 'RANDBETWEEN(bottom, top)';

      // Logical Functions
      case 'IF':
        return 'IF(logical_test, value_if_true, value_if_false)';
      case 'AND':
        return 'AND(logical1, [logical2], ...)';
      case 'OR':
        return 'OR(logical1, [logical2], ...)';
      case 'NOT':
        return 'NOT(logical)';
      case 'IFERROR':
        return 'IFERROR(value, value_if_error)';
      case 'IFS':
        return 'IFS(logical_test1, value_if_true1, [logical_test2, value_if_true2], ...)';

      // Text Functions
      case 'CONCATENATE':
        return 'CONCATENATE(text1, [text2], ...)';
      case 'LEFT':
        return 'LEFT(text, [num_chars])';
      case 'RIGHT':
        return 'RIGHT(text, [num_chars])';
      case 'MID':
        return 'MID(text, start_num, num_chars)';
      case 'LEN':
        return 'LEN(text)';
      case 'UPPER':
        return 'UPPER(text)';
      case 'LOWER':
        return 'LOWER(text)';
      case 'TRIM':
        return 'TRIM(text)';
      case 'SUBSTITUTE':
        return 'SUBSTITUTE(text, old_text, new_text, [instance_num])';
      case 'FIND':
        return 'FIND(find_text, within_text, [start_num])';

      // Date & Time Functions
      case 'TODAY':
        return 'TODAY()';
      case 'NOW':
        return 'NOW()';
      case 'DATE':
        return 'DATE(year, month, day)';
      case 'TIME':
        return 'TIME(hour, minute, second)';
      case 'YEAR':
        return 'YEAR(serial_number)';
      case 'MONTH':
        return 'MONTH(serial_number)';
      case 'DAY':
        return 'DAY(serial_number)';

      // Lookup Functions
      case 'VLOOKUP':
        return 'VLOOKUP(lookup_value, table_array, col_index_num, [range_lookup])';
      case 'HLOOKUP':
        return 'HLOOKUP(lookup_value, table_array, row_index_num, [range_lookup])';
      case 'INDEX':
        return 'INDEX(array, row_num, [column_num])';
      case 'MATCH':
        return 'MATCH(lookup_value, lookup_array, [match_type])';

      // Statistical Functions
      case 'MEDIAN':
        return 'MEDIAN(number1, [number2], ...)';
      case 'MODE':
        return 'MODE(number1, [number2], ...)';
      case 'STDEV':
        return 'STDEV(number1, [number2], ...)';
      case 'COUNTIF':
        return 'COUNTIF(range, criteria)';
      case 'SUMIF':
        return 'SUMIF(range, criteria, [sum_range])';
      case 'AVERAGEIF':
        return 'AVERAGEIF(range, criteria, [average_range])';

      // Information Functions
      case 'ISBLANK':
        return 'ISBLANK(value)';
      case 'ISNUMBER':
        return 'ISNUMBER(value)';
      case 'ISTEXT':
        return 'ISTEXT(value)';
      case 'ISERROR':
        return 'ISERROR(value)';

      default:
        return '$functionName(...)';
    }
  }

  // Validate formula syntax
  FormulaValidationResult validateFormula(String formula) {
    try {
      if (!formula.startsWith('=')) {
        return FormulaValidationResult(isValid: true, message: 'Valid value');
      }

      final expression = formula.substring(1).trim();

      // Check for balanced parentheses
      int openParens = 0;
      for (int i = 0; i < expression.length; i++) {
        if (expression[i] == '(') openParens++;
        if (expression[i] == ')') openParens--;
        if (openParens < 0) {
          return FormulaValidationResult(
            isValid: false,
            message: 'Unmatched closing parenthesis at position ${i + 1}'
          );
        }
      }

      if (openParens > 0) {
        return FormulaValidationResult(
          isValid: false,
          message: 'Missing $openParens closing parenthesis'
        );
      }

      // Check for valid function names
      final functionPattern = RegExp(r'([A-Z]+)\s*\(');
      final matches = functionPattern.allMatches(expression.toUpperCase());

      for (final match in matches) {
        final functionName = match.group(1)!;
        if (!allExcelFunctions.contains(functionName)) {
          return FormulaValidationResult(
            isValid: false,
            message: 'Unknown function: $functionName'
          );
        }
      }

      return FormulaValidationResult(isValid: true, message: 'Valid formula');
    } catch (e) {
      return FormulaValidationResult(isValid: false, message: 'Syntax error: $e');
    }
  }

  // Replace cell references (A1, B2, etc.) with actual values
  String _replaceCellReferences(String expression, Map<String, ExcelCell> cells, [String? currentCellAddress]) {
    // Pattern to match cell references (A1, B2, etc.)
    final cellPattern = RegExp(r'[A-Z]+\d+');

    return expression.replaceAllMapped(cellPattern, (match) {
      final cellAddress = match.group(0)!;
      final cell = cells[cellAddress];

      if (cell == null) return '0';

      // If cell has a formula, calculate it recursively
      if (cell.isFormula && cell.formula != null) {
        final result = calculateFormula(cell.formula!, cells, cellAddress);
        return _valueToString(result);
      }

      return _valueToString(cell.value);
    });
  }

  // Convert value to string for expression evaluation
  String _valueToString(dynamic value) {
    if (value == null) return '0';
    if (value is num) return value.toString();
    if (value is String) {
      // Try to parse as number
      final numValue = double.tryParse(value);
      return numValue?.toString() ?? '"$value"';
    }
    return value.toString();
  }

  // Evaluate mathematical and function expressions
  dynamic _evaluateExpression(String expression, Map<String, ExcelCell> cells, [String? currentCellAddress]) {
    // Replace cell references with values first
    expression = _replaceCellReferences(expression, cells, currentCellAddress);

    // Handle Excel functions
    if (expression.contains('(')) {
      return _evaluateFunctions(expression, cells, currentCellAddress);
    }

    // Handle simple mathematical expressions
    return _evaluateMathExpression(expression);
  }

  // Evaluate Excel functions
  dynamic _evaluateFunctions(String expression, Map<String, ExcelCell> cells, [String? currentCellAddress]) {
    // SUM function
    if (expression.startsWith('SUM(')) {
      final args = _extractFunctionArgs(expression, 'SUM');
      return _sumFunction(args, cells);
    }
    
    // AVERAGE function
    if (expression.startsWith('AVERAGE(')) {
      final args = _extractFunctionArgs(expression, 'AVERAGE');
      return _averageFunction(args, cells);
    }
    
    // COUNT function
    if (expression.startsWith('COUNT(')) {
      final args = _extractFunctionArgs(expression, 'COUNT');
      return _countFunction(args, cells);
    }
    
    // MIN function
    if (expression.startsWith('MIN(')) {
      final args = _extractFunctionArgs(expression, 'MIN');
      return _minFunction(args, cells);
    }
    
    // MAX function
    if (expression.startsWith('MAX(')) {
      final args = _extractFunctionArgs(expression, 'MAX');
      return _maxFunction(args, cells);
    }
    
    // IF function
    if (expression.startsWith('IF(')) {
      final args = _extractFunctionArgs(expression, 'IF');
      return _ifFunction(args, cells);
    }
    
    // ROUND function
    if (expression.startsWith('ROUND(')) {
      final args = _extractFunctionArgs(expression, 'ROUND');
      return _roundFunction(args, cells);
    }
    
    // CONCATENATE function
    if (expression.startsWith('CONCATENATE(')) {
      final args = _extractFunctionArgs(expression, 'CONCATENATE');
      return _concatenateFunction(args, cells);
    }
    
    // TODAY function
    if (expression.startsWith('TODAY(')) {
      return DateTime.now().toString().split(' ')[0];
    }
    
    // NOW function
    if (expression.startsWith('NOW(')) {
      return DateTime.now().toString();
    }
    
    return '#FUNCTION_ERROR';
  }

  // Extract function arguments
  List<String> _extractFunctionArgs(String expression, String functionName) {
    final startIndex = expression.indexOf('(') + 1;
    final endIndex = expression.lastIndexOf(')');
    
    if (startIndex >= endIndex) return [];
    
    final argsString = expression.substring(startIndex, endIndex);
    return argsString.split(',').map((arg) => arg.trim()).toList();
  }

  // Function implementations
  double _sumFunction(List<String> args, Map<String, ExcelCell> cells) {
    double sum = 0;
    for (final arg in args) {
      if (arg.contains(':')) {
        // Range (A1:A5)
        final values = _getRangeValues(arg, cells);
        sum += values.fold(0.0, (prev, val) => prev + (double.tryParse(val.toString()) ?? 0));
      } else {
        // Single value
        sum += double.tryParse(arg) ?? 0;
      }
    }
    return sum;
  }

  double _averageFunction(List<String> args, Map<String, ExcelCell> cells) {
    final values = <double>[];
    for (final arg in args) {
      if (arg.contains(':')) {
        final rangeValues = _getRangeValues(arg, cells);
        values.addAll(rangeValues.map((v) => double.tryParse(v.toString()) ?? 0));
      } else {
        values.add(double.tryParse(arg) ?? 0);
      }
    }
    return values.isEmpty ? 0 : values.reduce((a, b) => a + b) / values.length;
  }

  int _countFunction(List<String> args, Map<String, ExcelCell> cells) {
    int count = 0;
    for (final arg in args) {
      if (arg.contains(':')) {
        final values = _getRangeValues(arg, cells);
        count += values.where((v) => v != null && v.toString().isNotEmpty).length;
      } else {
        if (arg.isNotEmpty) count++;
      }
    }
    return count;
  }

  double _minFunction(List<String> args, Map<String, ExcelCell> cells) {
    final values = <double>[];
    for (final arg in args) {
      if (arg.contains(':')) {
        final rangeValues = _getRangeValues(arg, cells);
        values.addAll(rangeValues.map((v) => double.tryParse(v.toString()) ?? double.infinity));
      } else {
        values.add(double.tryParse(arg) ?? double.infinity);
      }
    }
    return values.isEmpty ? 0 : values.reduce(math.min);
  }

  double _maxFunction(List<String> args, Map<String, ExcelCell> cells) {
    final values = <double>[];
    for (final arg in args) {
      if (arg.contains(':')) {
        final rangeValues = _getRangeValues(arg, cells);
        values.addAll(rangeValues.map((v) => double.tryParse(v.toString()) ?? double.negativeInfinity));
      } else {
        values.add(double.tryParse(arg) ?? double.negativeInfinity);
      }
    }
    return values.isEmpty ? 0 : values.reduce(math.max);
  }

  dynamic _ifFunction(List<String> args, Map<String, ExcelCell> cells) {
    if (args.length < 3) return '#ERROR';
    
    final condition = args[0];
    final trueValue = args[1];
    final falseValue = args[2];
    
    // Simple condition evaluation (can be enhanced)
    final conditionResult = _evaluateCondition(condition);
    return conditionResult ? trueValue : falseValue;
  }

  double _roundFunction(List<String> args, Map<String, ExcelCell> cells) {
    if (args.isEmpty) return 0;
    
    final value = double.tryParse(args[0]) ?? 0;
    final digits = args.length > 1 ? int.tryParse(args[1]) ?? 0 : 0;
    
    final multiplier = math.pow(10, digits);
    return (value * multiplier).round() / multiplier;
  }

  String _concatenateFunction(List<String> args, Map<String, ExcelCell> cells) {
    return args.map((arg) => arg.replaceAll('"', '')).join('');
  }

  // Get values from a range (A1:A5)
  List<dynamic> _getRangeValues(String range, Map<String, ExcelCell> cells) {
    final parts = range.split(':');
    if (parts.length != 2) return [];
    
    final startCell = parts[0];
    final endCell = parts[1];
    
    // Parse cell addresses
    final startCol = _getColumnIndex(startCell);
    final startRow = _getRowIndex(startCell);
    final endCol = _getColumnIndex(endCell);
    final endRow = _getRowIndex(endCell);
    
    final values = <dynamic>[];
    for (int col = startCol; col <= endCol; col++) {
      for (int row = startRow; row <= endRow; row++) {
        final cellAddress = _getCellAddress(col, row);
        final cell = cells[cellAddress];
        values.add(cell?.value);
      }
    }
    
    return values;
  }

  // Utility methods for cell address parsing
  int _getColumnIndex(String cellAddress) {
    final match = RegExp(r'[A-Z]+').firstMatch(cellAddress);
    if (match == null) return 0;
    
    final letters = match.group(0)!;
    int index = 0;
    for (int i = 0; i < letters.length; i++) {
      index = index * 26 + (letters.codeUnitAt(i) - 'A'.codeUnitAt(0) + 1);
    }
    return index - 1;
  }

  int _getRowIndex(String cellAddress) {
    final match = RegExp(r'\d+').firstMatch(cellAddress);
    return match != null ? int.parse(match.group(0)!) - 1 : 0;
  }

  String _getCellAddress(int col, int row) {
    String colName = '';
    int temp = col;
    while (temp >= 0) {
      colName = String.fromCharCode('A'.codeUnitAt(0) + (temp % 26)) + colName;
      temp = (temp ~/ 26) - 1;
      if (temp < 0) break;
    }
    return '$colName${row + 1}';
  }

  // Simple mathematical expression evaluation
  double _evaluateMathExpression(String expression) {
    try {
      // Remove spaces
      expression = expression.replaceAll(' ', '');
      
      // Simple evaluation for basic operations
      if (expression.contains('+')) {
        final parts = expression.split('+');
        return parts.fold(0.0, (sum, part) => sum + (double.tryParse(part) ?? 0));
      }
      
      if (expression.contains('-')) {
        final parts = expression.split('-');
        if (parts.length == 2) {
          return (double.tryParse(parts[0]) ?? 0) - (double.tryParse(parts[1]) ?? 0);
        }
      }
      
      if (expression.contains('*')) {
        final parts = expression.split('*');
        return parts.fold(1.0, (product, part) => product * (double.tryParse(part) ?? 1));
      }
      
      if (expression.contains('/')) {
        final parts = expression.split('/');
        if (parts.length == 2) {
          final divisor = double.tryParse(parts[1]) ?? 1;
          return divisor != 0 ? (double.tryParse(parts[0]) ?? 0) / divisor : double.infinity;
        }
      }
      
      // Single number
      return double.tryParse(expression) ?? 0;
    } catch (e) {
      return 0;
    }
  }

  // Simple condition evaluation
  bool _evaluateCondition(String condition) {
    // Basic comparison operators
    if (condition.contains('>=')) {
      final parts = condition.split('>=');
      if (parts.length == 2) {
        final left = double.tryParse(parts[0].trim()) ?? 0;
        final right = double.tryParse(parts[1].trim()) ?? 0;
        return left >= right;
      }
    }
    
    if (condition.contains('<=')) {
      final parts = condition.split('<=');
      if (parts.length == 2) {
        final left = double.tryParse(parts[0].trim()) ?? 0;
        final right = double.tryParse(parts[1].trim()) ?? 0;
        return left <= right;
      }
    }
    
    if (condition.contains('>')) {
      final parts = condition.split('>');
      if (parts.length == 2) {
        final left = double.tryParse(parts[0].trim()) ?? 0;
        final right = double.tryParse(parts[1].trim()) ?? 0;
        return left > right;
      }
    }
    
    if (condition.contains('<')) {
      final parts = condition.split('<');
      if (parts.length == 2) {
        final left = double.tryParse(parts[0].trim()) ?? 0;
        final right = double.tryParse(parts[1].trim()) ?? 0;
        return left < right;
      }
    }
    
    if (condition.contains('=')) {
      final parts = condition.split('=');
      if (parts.length == 2) {
        final left = parts[0].trim();
        final right = parts[1].trim();
        return left == right;
      }
    }
    
    // Default: try to parse as boolean
    return condition.toLowerCase() == 'true';
  }

  // Validate formula syntax (simple version)
  bool isValidFormula(String formula) {
    if (!formula.startsWith('=')) return false;

    try {
      // Basic validation - check for balanced parentheses
      int openParens = 0;
      for (int i = 0; i < formula.length; i++) {
        if (formula[i] == '(') openParens++;
        if (formula[i] == ')') openParens--;
        if (openParens < 0) return false;
      }
      return openParens == 0;
    } catch (e) {
      return false;
    }
  }
}
