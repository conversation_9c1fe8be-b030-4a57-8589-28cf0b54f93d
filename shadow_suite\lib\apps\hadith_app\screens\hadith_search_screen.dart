import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class HadithSearchScreen extends ConsumerStatefulWidget {
  const HadithSearchScreen({super.key});

  @override
  ConsumerState<HadithSearchScreen> createState() => _HadithSearchScreenState();
}

class _HadithSearchScreenState extends ConsumerState<HadithSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedCollection = 'All Collections';
  bool _isSearching = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Search Hadiths'),
        backgroundColor: Colors.brown,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          _buildSearchHeader(),
          Expanded(
            child: _isSearching ? _buildSearchResults() : _buildSearchSuggestions(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        children: [
          TextF<PERSON>(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search hadiths by keyword, narrator, or topic...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        setState(() => _isSearching = false);
                      },
                      icon: const Icon(Icons.clear),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: (value) {
              setState(() {
                _isSearching = value.isNotEmpty;
              });
            },
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedCollection,
                  decoration: InputDecoration(
                    labelText: 'Collection',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'All Collections', child: Text('All Collections')),
                    DropdownMenuItem(value: 'Sahih Bukhari', child: Text('Sahih Bukhari')),
                    DropdownMenuItem(value: 'Sahih Muslim', child: Text('Sahih Muslim')),
                    DropdownMenuItem(value: 'Sunan Abu Dawood', child: Text('Sunan Abu Dawood')),
                    DropdownMenuItem(value: 'Jami at-Tirmidhi', child: Text('Jami at-Tirmidhi')),
                  ],
                  onChanged: (value) {
                    setState(() => _selectedCollection = value!);
                  },
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() => _isSearching = true);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.brown,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                ),
                child: const Text('Search'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSuggestions() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        Text(
          'Popular Topics',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildTopicChip('Prayer'),
            _buildTopicChip('Charity'),
            _buildTopicChip('Fasting'),
            _buildTopicChip('Pilgrimage'),
            _buildTopicChip('Faith'),
            _buildTopicChip('Character'),
            _buildTopicChip('Knowledge'),
            _buildTopicChip('Family'),
          ],
        ),
        const SizedBox(height: 24),
        Text(
          'Recent Searches',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        _buildRecentSearchItem('prayer times'),
        _buildRecentSearchItem('charity obligations'),
        _buildRecentSearchItem('good character'),
      ],
    );
  }

  Widget _buildTopicChip(String topic) {
    return ActionChip(
      label: Text(topic),
      onPressed: () {
        _searchController.text = topic;
        setState(() => _isSearching = true);
      },
      backgroundColor: Colors.brown.withValues(alpha: 0.1),
      labelStyle: const TextStyle(color: Colors.brown),
    );
  }

  Widget _buildRecentSearchItem(String search) {
    return ListTile(
      leading: const Icon(Icons.history, color: Colors.grey),
      title: Text(search),
      trailing: const Icon(Icons.arrow_outward),
      onTap: () {
        _searchController.text = search;
        setState(() => _isSearching = true);
      },
    );
  }

  Widget _buildSearchResults() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 10,
      itemBuilder: (context, index) {
        return _buildSearchResultItem(index);
      },
    );
  }

  Widget _buildSearchResultItem(int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.brown.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Sahih Bukhari ${index + 1}',
                    style: const TextStyle(
                      color: Colors.brown,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  '95% match',
                  style: TextStyle(
                    color: Colors.green[600],
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            RichText(
              text: TextSpan(
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(height: 1.5),
                children: [
                  const TextSpan(text: 'Narrated by Abu Huraira: The Prophet (PBUH) said, "'),
                  TextSpan(
                    text: _searchController.text,
                    style: const TextStyle(
                      backgroundColor: Colors.yellow,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const TextSpan(text: ' is one of the pillars of Islam..."'),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Text(
                  'Book: Faith • Chapter: Pillars',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () {},
                  icon: const Icon(Icons.bookmark_border),
                  iconSize: 20,
                ),
                TextButton(
                  onPressed: () {},
                  child: const Text('Read Full'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
