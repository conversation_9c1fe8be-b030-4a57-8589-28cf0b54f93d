import 'dart:async';
import '../models/hadith_models.dart';
import '../../../core/database/database_service.dart';
import '../../../core/services/error_handler.dart' as error_handler;

class OfflineHadithService {
  static final List<HadithCollection> _collections = [];
  static final List<HadithEntry> _hadiths = [];
  static final List<HadithChapter> _chapters = [];
  static final Map<String, List<HadithEntry>> _collectionHadiths = {};
  static final Map<String, List<HadithChapter>> _collectionChapters = {};

  static final StreamController<HadithChangeEvent> _changeController =
      StreamController<HadithChangeEvent>.broadcast();

  // Initialize offline Hadith service
  static Future<void> initialize() async {
    await _loadHadithCollections();
    await _loadHadithChapters();
    await _loadHadithEntries();
    await _indexHadiths();
  }

  // Load Hadith collections
  static Future<void> _loadHadithCollections() async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM hadith_collections ORDER BY priority',
      );
      _collections.clear();
      for (final row in results) {
        _collections.add(HadithCollection.fromJson(row));
      }

      // If no collections exist, initialize with major collections
      if (_collections.isEmpty) {
        await _initializeHadithCollections();
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.database,
        context: 'Load Hadith collections',
      );
    }
  }

  // Initialize major Hadith collections
  static Future<void> _initializeHadithCollections() async {
    try {
      final collections = [
        HadithCollection(
          id: 'sahih_bukhari',
          name: 'Sahih al-Bukhari',
          nameArabic: 'صحيح البخاري',
          compiler: 'Imam Muhammad al-Bukhari',
          totalHadiths: 7563,
          authenticityLevel: AuthenticityLevel.sahih,
          language: 'Arabic',
          description: 'The most authentic collection of Hadith',
          priority: 1,
          isOffline: true,
          downloadSize: 15000000, // 15MB
          createdAt: DateTime.now(),
        ),
        HadithCollection(
          id: 'sahih_muslim',
          name: 'Sahih Muslim',
          nameArabic: 'صحيح مسلم',
          compiler: 'Imam Muslim ibn al-Hajjaj',
          totalHadiths: 7190,
          authenticityLevel: AuthenticityLevel.sahih,
          language: 'Arabic',
          description: 'Second most authentic collection after Bukhari',
          priority: 2,
          isOffline: true,
          downloadSize: 14000000,
          createdAt: DateTime.now(),
        ),
        HadithCollection(
          id: 'sunan_abu_dawud',
          name: 'Sunan Abu Dawud',
          nameArabic: 'سنن أبي داود',
          compiler: 'Imam Abu Dawud',
          totalHadiths: 5274,
          authenticityLevel: AuthenticityLevel.hasan,
          language: 'Arabic',
          description: 'Collection focusing on legal matters',
          priority: 3,
          isOffline: true,
          downloadSize: 12000000,
          createdAt: DateTime.now(),
        ),
        HadithCollection(
          id: 'jami_tirmidhi',
          name: 'Jami\' at-Tirmidhi',
          nameArabic: 'جامع الترمذي',
          compiler: 'Imam at-Tirmidhi',
          totalHadiths: 3956,
          authenticityLevel: AuthenticityLevel.hasan,
          language: 'Arabic',
          description: 'Collection with detailed grading of Hadiths',
          priority: 4,
          isOffline: true,
          downloadSize: 10000000,
          createdAt: DateTime.now(),
        ),
        HadithCollection(
          id: 'sunan_nasai',
          name: 'Sunan an-Nasa\'i',
          nameArabic: 'سنن النسائي',
          compiler: 'Imam an-Nasa\'i',
          totalHadiths: 5761,
          authenticityLevel: AuthenticityLevel.hasan,
          language: 'Arabic',
          description: 'Collection with strict criteria for authenticity',
          priority: 5,
          isOffline: true,
          downloadSize: 11000000,
          createdAt: DateTime.now(),
        ),
        HadithCollection(
          id: 'sunan_ibn_majah',
          name: 'Sunan Ibn Majah',
          nameArabic: 'سنن ابن ماجه',
          compiler: 'Imam Ibn Majah',
          totalHadiths: 4341,
          authenticityLevel: AuthenticityLevel.daif,
          language: 'Arabic',
          description: 'Sixth book of the Kutub al-Sittah',
          priority: 6,
          isOffline: true,
          downloadSize: 9000000,
          createdAt: DateTime.now(),
        ),
        HadithCollection(
          id: 'muwatta_malik',
          name: 'Muwatta Malik',
          nameArabic: 'موطأ مالك',
          compiler: 'Imam Malik ibn Anas',
          totalHadiths: 1720,
          authenticityLevel: AuthenticityLevel.sahih,
          language: 'Arabic',
          description: 'Earliest surviving collection of Hadith',
          priority: 7,
          isOffline: true,
          downloadSize: 5000000,
          createdAt: DateTime.now(),
        ),
        HadithCollection(
          id: 'musnad_ahmad',
          name: 'Musnad Ahmad',
          nameArabic: 'مسند أحمد',
          compiler: 'Imam Ahmad ibn Hanbal',
          totalHadiths: 26363,
          authenticityLevel: AuthenticityLevel.hasan,
          language: 'Arabic',
          description: 'Largest collection of Hadith',
          priority: 8,
          isOffline: true,
          downloadSize: 25000000,
          createdAt: DateTime.now(),
        ),
      ];

      for (final collection in collections) {
        await DatabaseService.safeInsert(
          'hadith_collections',
          collection.toJson(),
        );
        _collections.add(collection);
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.database,
        context: 'Initialize Hadith collections',
      );
    }
  }

  // Load Hadith chapters
  static Future<void> _loadHadithChapters() async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM hadith_chapters ORDER BY collection_id, chapter_number',
      );
      _chapters.clear();
      for (final row in results) {
        _chapters.add(HadithChapter.fromJson(row));
      }

      // If no chapters exist, initialize with sample chapters
      if (_chapters.isEmpty) {
        await _initializeHadithChapters();
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.database,
        context: 'Load Hadith chapters',
      );
    }
  }

  // Initialize Hadith chapters
  static Future<void> _initializeHadithChapters() async {
    try {
      // Sample chapters for Sahih Bukhari
      final bukhariChapters = [
        HadithChapter(
          id: 'bukhari_ch1',
          collectionId: 'sahih_bukhari',
          chapterNumber: 1,
          title: 'How the Divine Inspiration started',
          titleArabic: 'بدء الوحي',
          description: 'The beginning of revelation to Prophet Muhammad',
          hadithCount: 7,
        ),
        HadithChapter(
          id: 'bukhari_ch2',
          collectionId: 'sahih_bukhari',
          chapterNumber: 2,
          title: 'Belief (Faith)',
          titleArabic: 'الإيمان',
          description: 'Matters related to faith and belief',
          hadithCount: 50,
        ),
        HadithChapter(
          id: 'bukhari_ch3',
          collectionId: 'sahih_bukhari',
          chapterNumber: 3,
          title: 'Knowledge',
          titleArabic: 'العلم',
          description: 'The importance and pursuit of knowledge',
          hadithCount: 76,
        ),
        // Continue with more chapters...
      ];

      for (final chapter in bukhariChapters) {
        await DatabaseService.safeInsert('hadith_chapters', chapter.toJson());
        _chapters.add(chapter);
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.database,
        context: 'Initialize Hadith chapters',
      );
    }
  }

  // Load Hadith entries
  static Future<void> _loadHadithEntries() async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM hadith_entries ORDER BY collection_id, hadith_number',
      );
      _hadiths.clear();
      for (final row in results) {
        _hadiths.add(HadithEntry.fromJson(row));
      }

      // If no hadiths exist, initialize with sample hadiths
      if (_hadiths.isEmpty) {
        await _initializeHadithEntries();
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.database,
        context: 'Load Hadith entries',
      );
    }
  }

  // Initialize sample Hadith entries
  static Future<void> _initializeHadithEntries() async {
    try {
      final sampleHadiths = [
        HadithEntry(
          id: 'bukhari_1',
          collectionId: 'sahih_bukhari',
          chapterId: 'bukhari_ch1',
          hadithNumber: 1,
          arabicText:
              'إِنَّمَا الْأَعْمَالُ بِالنِّيَّاتِ، وَإِنَّمَا لِكُلِّ امْرِئٍ مَا نَوَى',
          englishTranslation:
              'Actions are but by intention and every man shall have only that which he intended.',
          narrator: 'Umar ibn al-Khattab',
          chain: 'Complete chain of narrators',
          grade: HadithGrade.sahih,
          keywords: ['intention', 'actions', 'niyyah'],
          theme: 'Intention in worship',
          reference: 'Sahih al-Bukhari 1',
          createdAt: DateTime.now(),
        ),
        HadithEntry(
          id: 'bukhari_2',
          collectionId: 'sahih_bukhari',
          chapterId: 'bukhari_ch1',
          hadithNumber: 2,
          arabicText:
              'بَيْنَمَا نَحْنُ عِنْدَ رَسُولِ اللَّهِ صلى الله عليه وسلم ذَاتَ يَوْمٍ إِذْ طَلَعَ عَلَيْنَا رَجُلٌ شَدِيدُ بَيَاضِ الثِّيَابِ',
          englishTranslation:
              'One day while we were sitting with the Messenger of Allah, there appeared before us a man whose clothes were exceedingly white.',
          narrator: 'Umar ibn al-Khattab',
          chain: 'Complete chain of narrators',
          grade: HadithGrade.sahih,
          keywords: ['Gabriel', 'Islam', 'Iman', 'Ihsan'],
          theme: 'Pillars of Islam',
          reference: 'Sahih al-Bukhari 2',
          createdAt: DateTime.now(),
        ),
        // Continue with more hadiths...
      ];

      for (final hadith in sampleHadiths) {
        await DatabaseService.safeInsert('hadith_entries', hadith.toJson());
        _hadiths.add(hadith);
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.database,
        context: 'Initialize Hadith entries',
      );
    }
  }

  // Index hadiths for fast lookup
  static Future<void> _indexHadiths() async {
    try {
      _collectionHadiths.clear();
      _collectionChapters.clear();

      // Index hadiths by collection
      for (final hadith in _hadiths) {
        if (!_collectionHadiths.containsKey(hadith.collectionId)) {
          _collectionHadiths[hadith.collectionId] = [];
        }
        _collectionHadiths[hadith.collectionId]!.add(hadith);
      }

      // Index chapters by collection
      for (final chapter in _chapters) {
        if (!_collectionChapters.containsKey(chapter.collectionId)) {
          _collectionChapters[chapter.collectionId] = [];
        }
        _collectionChapters[chapter.collectionId]!.add(chapter);
      }
    } catch (error, stackTrace) {
      error_handler.ErrorHandler.handleError(
        error,
        stackTrace,
        error_handler.ErrorType.operation,
        context: 'Index hadiths',
      );
    }
  }

  // Get collection by ID
  static HadithCollection? getCollection(String collectionId) {
    try {
      return _collections.firstWhere((c) => c.id == collectionId);
    } catch (error) {
      return null;
    }
  }

  // Get chapters for a collection
  static List<HadithChapter> getCollectionChapters(String collectionId) {
    return _collectionChapters[collectionId] ?? [];
  }

  // Get hadiths for a collection
  static List<HadithEntry> getCollectionHadiths(String collectionId) {
    return _collectionHadiths[collectionId] ?? [];
  }

  // Get hadiths for a chapter
  static List<HadithEntry> getChapterHadiths(String chapterId) {
    return _hadiths.where((h) => h.chapterId == chapterId).toList();
  }

  // Search in Hadith
  static List<HadithSearchResult> searchHadith({
    required String query,
    String? collectionId,
    bool searchInArabic = true,
    bool searchInTranslation = true,
    List<HadithGrade>? grades,
  }) {
    final results = <HadithSearchResult>[];
    final queryLower = query.toLowerCase();

    var searchHadiths = _hadiths;
    if (collectionId != null) {
      searchHadiths = getCollectionHadiths(collectionId);
    }

    for (final hadith in searchHadiths) {
      bool matches = false;
      String matchedText = '';

      // Filter by grade
      if (grades != null && !grades.contains(hadith.grade)) {
        continue;
      }

      // Search in Arabic text
      if (searchInArabic &&
          hadith.arabicText.toLowerCase().contains(queryLower)) {
        matches = true;
        matchedText = hadith.arabicText;
      }

      // Search in English translation
      if (searchInTranslation &&
          hadith.englishTranslation.toLowerCase().contains(queryLower)) {
        matches = true;
        matchedText = hadith.englishTranslation;
      }

      // Search in keywords
      if (hadith.keywords.any(
        (keyword) => keyword.toLowerCase().contains(queryLower),
      )) {
        matches = true;
        matchedText = hadith.keywords.join(', ');
      }

      if (matches) {
        results.add(
          HadithSearchResult(
            hadithId: hadith.id,
            collectionId: hadith.collectionId,
            hadithNumber: hadith.hadithNumber,
            arabicText: hadith.arabicText,
            englishTranslation: hadith.englishTranslation,
            narrator: hadith.narrator,
            grade: hadith.grade,
            matchedText: matchedText,
            relevanceScore: _calculateRelevanceScore(query, matchedText),
          ),
        );
      }
    }

    // Sort by relevance
    results.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));
    return results;
  }

  // Get random Hadith
  static HadithEntry? getRandomHadith({String? collectionId}) {
    try {
      var hadiths = _hadiths;
      if (collectionId != null) {
        hadiths = getCollectionHadiths(collectionId);
      }

      if (hadiths.isEmpty) return null;

      final random = DateTime.now().millisecondsSinceEpoch % hadiths.length;
      return hadiths[random];
    } catch (error) {
      return null;
    }
  }

  // Get Hadith by theme with pagination
  static List<HadithEntry> getHadithsByTheme(
    String theme, {
    int page = 0,
    int pageSize = 20,
  }) {
    final filtered = _hadiths
        .where((h) => h.theme.toLowerCase().contains(theme.toLowerCase()))
        .toList();
    final startIndex = page * pageSize;
    final endIndex = (startIndex + pageSize).clamp(0, filtered.length);
    return filtered.sublist(startIndex.clamp(0, filtered.length), endIndex);
  }

  // Get Hadith by narrator with pagination
  static List<HadithEntry> getHadithsByNarrator(
    String narrator, {
    int page = 0,
    int pageSize = 20,
  }) {
    final filtered = _hadiths
        .where((h) => h.narrator.toLowerCase().contains(narrator.toLowerCase()))
        .toList();
    final startIndex = page * pageSize;
    final endIndex = (startIndex + pageSize).clamp(0, filtered.length);
    return filtered.sublist(startIndex.clamp(0, filtered.length), endIndex);
  }

  // Get Hadith by grade with pagination
  static List<HadithEntry> getHadithsByGrade(
    HadithGrade grade, {
    int page = 0,
    int pageSize = 20,
  }) {
    final filtered = _hadiths.where((h) => h.grade == grade).toList();
    final startIndex = page * pageSize;
    final endIndex = (startIndex + pageSize).clamp(0, filtered.length);
    return filtered.sublist(startIndex.clamp(0, filtered.length), endIndex);
  }

  // Get paginated Hadiths from collection
  static List<HadithEntry> getCollectionHadithsPaginated(
    String collectionId, {
    int page = 0,
    int pageSize = 20,
  }) {
    final hadiths = getCollectionHadiths(collectionId);
    final startIndex = page * pageSize;
    final endIndex = (startIndex + pageSize).clamp(0, hadiths.length);
    return hadiths.sublist(startIndex.clamp(0, hadiths.length), endIndex);
  }

  // Get total count for pagination
  static int getHadithCountByTheme(String theme) {
    return _hadiths
        .where((h) => h.theme.toLowerCase().contains(theme.toLowerCase()))
        .length;
  }

  static int getHadithCountByNarrator(String narrator) {
    return _hadiths
        .where((h) => h.narrator.toLowerCase().contains(narrator.toLowerCase()))
        .length;
  }

  static int getHadithCountByGrade(HadithGrade grade) {
    return _hadiths.where((h) => h.grade == grade).length;
  }

  static int getCollectionHadithCount(String collectionId) {
    return getCollectionHadiths(collectionId).length;
  }

  // Helper method for relevance scoring
  static double _calculateRelevanceScore(String query, String text) {
    final queryLower = query.toLowerCase();
    final textLower = text.toLowerCase();

    double score = 0.0;

    // Exact match gets highest score
    if (textLower.contains(queryLower)) {
      score += 100.0;
    }

    // Word matches
    final queryWords = queryLower.split(' ');
    final textWords = textLower.split(' ');

    for (final queryWord in queryWords) {
      for (final textWord in textWords) {
        if (textWord.contains(queryWord)) {
          score += 10.0;
        }
      }
    }

    return score;
  }

  // Getters
  static List<HadithCollection> get collections =>
      List.unmodifiable(_collections);
  static List<HadithEntry> get hadiths => List.unmodifiable(_hadiths);
  static List<HadithChapter> get chapters => List.unmodifiable(_chapters);
  static Stream<HadithChangeEvent> get changeStream => _changeController.stream;

  // Dispose
  static void dispose() {
    _collections.clear();
    _hadiths.clear();
    _chapters.clear();
    _collectionHadiths.clear();
    _collectionChapters.clear();
    _changeController.close();
  }
}
