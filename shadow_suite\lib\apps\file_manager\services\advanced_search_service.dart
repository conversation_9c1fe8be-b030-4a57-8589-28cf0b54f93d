import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/file_manager_models.dart';

class AdvancedSearchService {
  static final StreamController<SearchProgress> _progressController =
      StreamController<SearchProgress>.broadcast();

  static Stream<SearchProgress> get progressStream =>
      _progressController.stream;

  static bool _isSearching = false;
  static String? _currentSearchId;

  /// Advanced file search with filters and regex support
  static Future<List<FileSystemItem>> searchFiles({
    required String searchPath,
    required SearchCriteria criteria,
    Function(SearchProgress)? onProgress,
  }) async {
    final startTime = DateTime.now();
    final searchId = DateTime.now().millisecondsSinceEpoch.toString();
    _currentSearchId = searchId;
    _isSearching = true;

    final results = <FileSystemItem>[];
    int totalScanned = 0;
    int matchesFound = 0;

    try {
      final directory = Directory(searchPath);

      await for (final entity in directory.list(
        recursive: criteria.includeSubdirectories,
      )) {
        if (!_isSearching || _currentSearchId != searchId) break;

        totalScanned++;

        // Update progress every 100 items for performance
        if (totalScanned % 100 == 0) {
          final progress = SearchProgress(
            searchId: searchId,
            totalScanned: totalScanned,
            matchesFound: matchesFound,
            currentPath: entity.path,
            isCompleted: false,
          );

          _progressController.add(progress);
          onProgress?.call(progress);
        }

        if (await _matchesCriteria(entity, criteria)) {
          final item = await _createFileSystemItem(entity);
          if (item != null) {
            results.add(item);
            matchesFound++;
          }
        }

        // Ensure <100ms response time for UI updates
        if (totalScanned % 50 == 0) {
          await Future.delayed(const Duration(microseconds: 1));
        }
      }

      // Final progress update
      final finalProgress = SearchProgress(
        searchId: searchId,
        totalScanned: totalScanned,
        matchesFound: matchesFound,
        currentPath: searchPath,
        isCompleted: true,
        duration: DateTime.now().difference(startTime),
      );

      _progressController.add(finalProgress);
      onProgress?.call(finalProgress);

      return results;
    } catch (error) {
      debugPrint('Search error: $error');
      rethrow;
    } finally {
      _isSearching = false;
      _currentSearchId = null;
    }
  }

  /// Cancel current search operation
  static void cancelSearch() {
    _isSearching = false;
    _currentSearchId = null;
  }

  /// Check if currently searching
  static bool get isSearching => _isSearching;

  /// Search for duplicate files
  static Future<Map<String, List<FileSystemItem>>> findDuplicateFiles(
    String searchPath, {
    bool compareBySize = true,
    bool compareByContent = false,
  }) async {
    final duplicates = <String, List<FileSystemItem>>{};
    final filesByHash = <String, List<FileSystemItem>>{};

    final directory = Directory(searchPath);

    await for (final entity in directory.list(recursive: true)) {
      if (entity is File) {
        try {
          final stat = await entity.stat();
          String key;

          if (compareByContent) {
            // Use file content hash (simplified - in production use proper hashing)
            final bytes = await entity.readAsBytes();
            key = '${bytes.length}_${bytes.hashCode}';
          } else if (compareBySize) {
            key = stat.size.toString();
          } else {
            key = entity.path.split('/').last; // filename only
          }

          final item = FileSystemItem(
            name: entity.path.split('/').last,
            path: entity.path,
            type: FileSystemItemType.file,
            size: stat.size,
            lastModified: stat.modified,
            lastAccessed: stat.accessed,
            isHidden: entity.path.split('/').last.startsWith('.'),
            isReadOnly: stat.mode & 0x80 == 0,
          );

          filesByHash.putIfAbsent(key, () => []).add(item);
        } catch (e) {
          debugPrint('Error processing file ${entity.path}: $e');
        }
      }
    }

    // Filter to only include actual duplicates
    filesByHash.forEach((key, files) {
      if (files.length > 1) {
        duplicates[key] = files;
      }
    });

    return duplicates;
  }

  /// Search with regex pattern
  static Future<List<FileSystemItem>> regexSearch({
    required String searchPath,
    required String pattern,
    bool caseSensitive = false,
    bool searchContent = false,
  }) async {
    final regex = RegExp(pattern, caseSensitive: caseSensitive);
    final results = <FileSystemItem>[];

    final directory = Directory(searchPath);

    await for (final entity in directory.list(recursive: true)) {
      try {
        final fileName = entity.path.split('/').last;
        bool matches = regex.hasMatch(fileName);

        if (!matches && searchContent && entity is File) {
          try {
            final content = await entity.readAsString();
            matches = regex.hasMatch(content);
          } catch (e) {
            // Skip binary files or files that can't be read as text
          }
        }

        if (matches) {
          final item = await _createFileSystemItem(entity);
          if (item != null) {
            results.add(item);
          }
        }
      } catch (e) {
        debugPrint('Error processing ${entity.path}: $e');
      }
    }

    return results;
  }

  /// Search by file attributes
  static Future<List<FileSystemItem>> searchByAttributes({
    required String searchPath,
    int? minSize,
    int? maxSize,
    DateTime? modifiedAfter,
    DateTime? modifiedBefore,
    List<String>? extensions,
  }) async {
    final results = <FileSystemItem>[];
    final directory = Directory(searchPath);

    await for (final entity in directory.list(recursive: true)) {
      try {
        final stat = await entity.stat();
        bool matches = true;

        // Size filters
        if (minSize != null && stat.size < minSize) matches = false;
        if (maxSize != null && stat.size > maxSize) matches = false;

        // Date filters
        if (modifiedAfter != null && stat.modified.isBefore(modifiedAfter)) {
          matches = false;
        }
        if (modifiedBefore != null && stat.modified.isAfter(modifiedBefore)) {
          matches = false;
        }

        // Extension filter
        if (extensions != null && extensions.isNotEmpty) {
          final fileName = entity.path.split('/').last;
          final extension = fileName.contains('.')
              ? fileName.split('.').last.toLowerCase()
              : '';
          matches = matches && extensions.contains(extension);
        }

        if (matches) {
          final item = await _createFileSystemItem(entity);
          if (item != null) {
            results.add(item);
          }
        }
      } catch (e) {
        debugPrint('Error processing ${entity.path}: $e');
      }
    }

    return results;
  }

  static Future<bool> _matchesCriteria(
    FileSystemEntity entity,
    SearchCriteria criteria,
  ) async {
    try {
      final fileName = entity.path.split('/').last;

      // Name pattern matching
      if (criteria.namePattern.isNotEmpty) {
        if (criteria.useRegex) {
          final regex = RegExp(
            criteria.namePattern,
            caseSensitive: criteria.caseSensitive,
          );
          if (!regex.hasMatch(fileName)) return false;
        } else {
          final pattern = criteria.caseSensitive
              ? criteria.namePattern
              : criteria.namePattern.toLowerCase();
          final name = criteria.caseSensitive
              ? fileName
              : fileName.toLowerCase();
          if (!name.contains(pattern)) return false;
        }
      }

      // File type filter
      if (criteria.fileTypes.isNotEmpty) {
        final isDirectory = await FileSystemEntity.isDirectory(entity.path);
        if (criteria.fileTypes.contains(FileType.directories) && !isDirectory) {
          return false;
        }
        if (criteria.fileTypes.contains(FileType.files) && isDirectory) {
          return false;
        }
      }

      // Extension filter
      if (criteria.extensions.isNotEmpty) {
        final extension = fileName.contains('.')
            ? fileName.split('.').last.toLowerCase()
            : '';
        if (!criteria.extensions.contains(extension)) return false;
      }

      // Size and date filters (for files only)
      if (entity is File) {
        final stat = await entity.stat();

        if (criteria.minSize != null && stat.size < criteria.minSize!) {
          return false;
        }
        if (criteria.maxSize != null && stat.size > criteria.maxSize!) {
          return false;
        }
        if (criteria.modifiedAfter != null &&
            stat.modified.isBefore(criteria.modifiedAfter!)) {
          return false;
        }
        if (criteria.modifiedBefore != null &&
            stat.modified.isAfter(criteria.modifiedBefore!)) {
          return false;
        }
      }

      // Content search (for text files only)
      if (criteria.contentPattern.isNotEmpty && entity is File) {
        try {
          final content = await entity.readAsString();
          if (criteria.useRegex) {
            final regex = RegExp(
              criteria.contentPattern,
              caseSensitive: criteria.caseSensitive,
            );
            if (!regex.hasMatch(content)) return false;
          } else {
            final pattern = criteria.caseSensitive
                ? criteria.contentPattern
                : criteria.contentPattern.toLowerCase();
            final text = criteria.caseSensitive
                ? content
                : content.toLowerCase();
            if (!text.contains(pattern)) return false;
          }
        } catch (e) {
          // Skip binary files or files that can't be read as text
          return false;
        }
      }

      return true;
    } catch (e) {
      debugPrint('Error matching criteria for ${entity.path}: $e');
      return false;
    }
  }

  static Future<FileSystemItem?> _createFileSystemItem(
    FileSystemEntity entity,
  ) async {
    try {
      final stat = await entity.stat();
      final isDirectory = await FileSystemEntity.isDirectory(entity.path);
      return FileSystemItem(
        name: entity.path.split('/').last,
        path: entity.path,
        type: isDirectory
            ? FileSystemItemType.directory
            : FileSystemItemType.file,
        size: stat.size,
        lastModified: stat.modified,
        lastAccessed: stat.accessed,
        isHidden: entity.path.split('/').last.startsWith('.'),
        isReadOnly: stat.mode & 0x80 == 0,
      );
    } catch (e) {
      debugPrint('Error creating FileSystemItem for ${entity.path}: $e');
      return null;
    }
  }

  static void dispose() {
    _progressController.close();
  }
}

/// Search criteria configuration
class SearchCriteria {
  final String namePattern;
  final String contentPattern;
  final bool caseSensitive;
  final bool useRegex;
  final bool includeSubdirectories;
  final List<FileType> fileTypes;
  final List<String> extensions;
  final int? minSize;
  final int? maxSize;
  final DateTime? modifiedAfter;
  final DateTime? modifiedBefore;

  const SearchCriteria({
    this.namePattern = '',
    this.contentPattern = '',
    this.caseSensitive = false,
    this.useRegex = false,
    this.includeSubdirectories = true,
    this.fileTypes = const [],
    this.extensions = const [],
    this.minSize,
    this.maxSize,
    this.modifiedAfter,
    this.modifiedBefore,
  });
}

/// Search progress information
class SearchProgress {
  final String searchId;
  final int totalScanned;
  final int matchesFound;
  final String currentPath;
  final bool isCompleted;
  final Duration? duration;

  const SearchProgress({
    required this.searchId,
    required this.totalScanned,
    required this.matchesFound,
    required this.currentPath,
    required this.isCompleted,
    this.duration,
  });
}

enum FileType { files, directories }
