import 'package:flutter/material.dart';
import '../services/advanced_search_service.dart';
import '../models/file_manager_models.dart';

class AdvancedSearchDialog extends StatefulWidget {
  final String initialPath;
  final Function(List<FileSystemItem>) onResults;

  const AdvancedSearchDialog({
    super.key,
    required this.initialPath,
    required this.onResults,
  });

  @override
  State<AdvancedSearchDialog> createState() => _AdvancedSearchDialogState();
}

class _AdvancedSearchDialogState extends State<AdvancedSearchDialog> {
  final _nameController = TextEditingController();
  final _contentController = TextEditingController();
  final _extensionsController = TextEditingController();
  final _minSizeController = TextEditingController();
  final _maxSizeController = TextEditingController();
  
  bool _caseSensitive = false;
  bool _useRegex = false;
  bool _includeSubdirectories = true;
  bool _searchFiles = true;
  bool _searchDirectories = true;
  bool _isSearching = false;
  
  DateTime? _modifiedAfter;
  DateTime? _modifiedBefore;
  
  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 600,
        height: 700,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                const Icon(Icons.search, color: Color(0xFFE67E22)),
                const SizedBox(width: 8),
                const Text(
                  'Advanced Search',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Search form
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Name pattern
                    _buildSectionTitle('File Name Pattern'),
                    TextField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        hintText: 'Enter file name pattern...',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Content search
                    _buildSectionTitle('Content Search'),
                    TextField(
                      controller: _contentController,
                      decoration: const InputDecoration(
                        hintText: 'Search inside files...',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Extensions
                    _buildSectionTitle('File Extensions'),
                    TextField(
                      controller: _extensionsController,
                      decoration: const InputDecoration(
                        hintText: 'txt,pdf,jpg (comma separated)',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Size filters
                    _buildSectionTitle('File Size'),
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _minSizeController,
                            decoration: const InputDecoration(
                              hintText: 'Min size (bytes)',
                              border: OutlineInputBorder(),
                            ),
                            keyboardType: TextInputType.number,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextField(
                            controller: _maxSizeController,
                            decoration: const InputDecoration(
                              hintText: 'Max size (bytes)',
                              border: OutlineInputBorder(),
                            ),
                            keyboardType: TextInputType.number,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    // Date filters
                    _buildSectionTitle('Modified Date'),
                    Row(
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () => _selectDate(true),
                            child: Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                _modifiedAfter != null
                                    ? 'After: ${_formatDate(_modifiedAfter!)}'
                                    : 'Modified after...',
                                style: TextStyle(
                                  color: _modifiedAfter != null ? Colors.black : Colors.grey[600],
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: InkWell(
                            onTap: () => _selectDate(false),
                            child: Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                _modifiedBefore != null
                                    ? 'Before: ${_formatDate(_modifiedBefore!)}'
                                    : 'Modified before...',
                                style: TextStyle(
                                  color: _modifiedBefore != null ? Colors.black : Colors.grey[600],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    // Options
                    _buildSectionTitle('Search Options'),
                    CheckboxListTile(
                      title: const Text('Case sensitive'),
                      value: _caseSensitive,
                      onChanged: (value) => setState(() => _caseSensitive = value ?? false),
                    ),
                    CheckboxListTile(
                      title: const Text('Use regular expressions'),
                      value: _useRegex,
                      onChanged: (value) => setState(() => _useRegex = value ?? false),
                    ),
                    CheckboxListTile(
                      title: const Text('Include subdirectories'),
                      value: _includeSubdirectories,
                      onChanged: (value) => setState(() => _includeSubdirectories = value ?? true),
                    ),
                    CheckboxListTile(
                      title: const Text('Search files'),
                      value: _searchFiles,
                      onChanged: (value) => setState(() => _searchFiles = value ?? true),
                    ),
                    CheckboxListTile(
                      title: const Text('Search directories'),
                      value: _searchDirectories,
                      onChanged: (value) => setState(() => _searchDirectories = value ?? true),
                    ),
                  ],
                ),
              ),
            ),
            
            // Action buttons
            const SizedBox(height: 24),
            Row(
              children: [
                if (_isSearching) ...[
                  ElevatedButton(
                    onPressed: _cancelSearch,
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  const Expanded(
                    child: LinearProgressIndicator(),
                  ),
                ] else ...[
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Close'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: _search,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFE67E22),
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Search'),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Color(0xFF2C3E50),
        ),
      ),
    );
  }

  Future<void> _selectDate(bool isAfter) async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    
    if (date != null) {
      setState(() {
        if (isAfter) {
          _modifiedAfter = date;
        } else {
          _modifiedBefore = date;
        }
      });
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _search() async {
    if (_nameController.text.isEmpty && _contentController.text.isEmpty) {
      _showError('Please enter a search pattern');
      return;
    }

    setState(() => _isSearching = true);

    try {
      final fileTypes = <FileType>[];
      if (_searchFiles) fileTypes.add(FileType.files);
      if (_searchDirectories) fileTypes.add(FileType.directories);

      final extensions = _extensionsController.text
          .split(',')
          .map((e) => e.trim().toLowerCase())
          .where((e) => e.isNotEmpty)
          .toList();

      final criteria = SearchCriteria(
        namePattern: _nameController.text,
        contentPattern: _contentController.text,
        caseSensitive: _caseSensitive,
        useRegex: _useRegex,
        includeSubdirectories: _includeSubdirectories,
        fileTypes: fileTypes,
        extensions: extensions,
        minSize: int.tryParse(_minSizeController.text),
        maxSize: int.tryParse(_maxSizeController.text),
        modifiedAfter: _modifiedAfter,
        modifiedBefore: _modifiedBefore,
      );

      final results = await AdvancedSearchService.searchFiles(
        searchPath: widget.initialPath,
        criteria: criteria,
      );

      widget.onResults(results);
      if (mounted) {
        Navigator.pop(context);
      }
    } catch (error) {
      _showError('Search error: $error');
    } finally {
      if (mounted) {
        setState(() => _isSearching = false);
      }
    }
  }

  void _cancelSearch() {
    AdvancedSearchService.cancelSearch();
    setState(() => _isSearching = false);
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _contentController.dispose();
    _extensionsController.dispose();
    _minSizeController.dispose();
    _maxSizeController.dispose();
    super.dispose();
  }
}
