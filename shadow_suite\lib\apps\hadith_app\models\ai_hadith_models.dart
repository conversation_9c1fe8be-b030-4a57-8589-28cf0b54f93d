/// Hadith recommendation from AI
class HadithRecommendation {
  final String id;
  final RecommendationType type;
  final String title;
  final String description;
  final List<String> suggestedHadiths;
  final Priority priority;
  final double confidence;
  final String estimatedBenefit;
  final DateTime createdAt;

  const HadithRecommendation({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.suggestedHadiths,
    required this.priority,
    required this.confidence,
    required this.estimatedBenefit,
    required this.createdAt,
  });

  HadithRecommendation copyWith({
    String? id,
    RecommendationType? type,
    String? title,
    String? description,
    List<String>? suggestedHadiths,
    Priority? priority,
    double? confidence,
    String? estimatedBenefit,
    DateTime? createdAt,
  }) {
    return HadithRecommendation(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      suggestedHadiths: suggestedHadiths ?? this.suggestedHadiths,
      priority: priority ?? this.priority,
      confidence: confidence ?? this.confidence,
      estimatedBenefit: estimatedBenefit ?? this.estimatedBenefit,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'suggestedHadiths': suggestedHadiths,
      'priority': priority.name,
      'confidence': confidence,
      'estimatedBenefit': estimatedBenefit,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory HadithRecommendation.fromJson(Map<String, dynamic> json) {
    return HadithRecommendation(
      id: json['id'],
      type: RecommendationType.values.firstWhere((e) => e.name == json['type']),
      title: json['title'],
      description: json['description'],
      suggestedHadiths: List<String>.from(json['suggestedHadiths']),
      priority: Priority.values.firstWhere((e) => e.name == json['priority']),
      confidence: json['confidence'].toDouble(),
      estimatedBenefit: json['estimatedBenefit'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Hadith insight from AI analysis
class HadithInsight {
  final String id;
  final InsightType type;
  final String title;
  final String description;
  final double confidence;
  final List<String> recommendations;
  final DateTime createdAt;

  const HadithInsight({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.confidence,
    required this.recommendations,
    required this.createdAt,
  });

  HadithInsight copyWith({
    String? id,
    InsightType? type,
    String? title,
    String? description,
    double? confidence,
    List<String>? recommendations,
    DateTime? createdAt,
  }) {
    return HadithInsight(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      confidence: confidence ?? this.confidence,
      recommendations: recommendations ?? this.recommendations,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'confidence': confidence,
      'recommendations': recommendations,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory HadithInsight.fromJson(Map<String, dynamic> json) {
    return HadithInsight(
      id: json['id'],
      type: InsightType.values.firstWhere((e) => e.name == json['type']),
      title: json['title'],
      description: json['description'],
      confidence: json['confidence'].toDouble(),
      recommendations: List<String>.from(json['recommendations']),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Authentication analysis for hadith verification
class AuthenticationAnalysis {
  final String id;
  final String hadithText;
  final String narrator;
  final AuthenticityLevel authenticity;
  final NarratorReliability narratorReliability;
  final ChainAnalysis chainAnalysis;
  final TextualAnalysis textualAnalysis;
  final List<ScholarlyOpinion> scholarlyOpinions;
  final List<String> similarHadiths;
  final double confidence;
  final List<String> sources;
  final DateTime generatedAt;

  const AuthenticationAnalysis({
    required this.id,
    required this.hadithText,
    required this.narrator,
    required this.authenticity,
    required this.narratorReliability,
    required this.chainAnalysis,
    required this.textualAnalysis,
    required this.scholarlyOpinions,
    required this.similarHadiths,
    required this.confidence,
    required this.sources,
    required this.generatedAt,
  });

  AuthenticationAnalysis copyWith({
    String? id,
    String? hadithText,
    String? narrator,
    AuthenticityLevel? authenticity,
    NarratorReliability? narratorReliability,
    ChainAnalysis? chainAnalysis,
    TextualAnalysis? textualAnalysis,
    List<ScholarlyOpinion>? scholarlyOpinions,
    List<String>? similarHadiths,
    double? confidence,
    List<String>? sources,
    DateTime? generatedAt,
  }) {
    return AuthenticationAnalysis(
      id: id ?? this.id,
      hadithText: hadithText ?? this.hadithText,
      narrator: narrator ?? this.narrator,
      authenticity: authenticity ?? this.authenticity,
      narratorReliability: narratorReliability ?? this.narratorReliability,
      chainAnalysis: chainAnalysis ?? this.chainAnalysis,
      textualAnalysis: textualAnalysis ?? this.textualAnalysis,
      scholarlyOpinions: scholarlyOpinions ?? this.scholarlyOpinions,
      similarHadiths: similarHadiths ?? this.similarHadiths,
      confidence: confidence ?? this.confidence,
      sources: sources ?? this.sources,
      generatedAt: generatedAt ?? this.generatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'hadithText': hadithText,
      'narrator': narrator,
      'authenticity': authenticity.name,
      'narratorReliability': narratorReliability.toJson(),
      'chainAnalysis': chainAnalysis.toJson(),
      'textualAnalysis': textualAnalysis.toJson(),
      'scholarlyOpinions': scholarlyOpinions.map((o) => o.toJson()).toList(),
      'similarHadiths': similarHadiths,
      'confidence': confidence,
      'sources': sources,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }

  factory AuthenticationAnalysis.fromJson(Map<String, dynamic> json) {
    return AuthenticationAnalysis(
      id: json['id'],
      hadithText: json['hadithText'],
      narrator: json['narrator'],
      authenticity: AuthenticityLevel.values.firstWhere(
        (e) => e.name == json['authenticity'],
      ),
      narratorReliability: NarratorReliability.fromJson(
        json['narratorReliability'],
      ),
      chainAnalysis: ChainAnalysis.fromJson(json['chainAnalysis']),
      textualAnalysis: TextualAnalysis.fromJson(json['textualAnalysis']),
      scholarlyOpinions: (json['scholarlyOpinions'] as List)
          .map((o) => ScholarlyOpinion.fromJson(o))
          .toList(),
      similarHadiths: List<String>.from(json['similarHadiths']),
      confidence: json['confidence'].toDouble(),
      sources: List<String>.from(json['sources']),
      generatedAt: DateTime.parse(json['generatedAt']),
    );
  }
}

/// Narrator reliability assessment
class NarratorReliability {
  final ReliabilityLevel level;
  final String description;
  final List<String> supportingEvidence;

  const NarratorReliability({
    required this.level,
    required this.description,
    required this.supportingEvidence,
  });

  NarratorReliability copyWith({
    ReliabilityLevel? level,
    String? description,
    List<String>? supportingEvidence,
  }) {
    return NarratorReliability(
      level: level ?? this.level,
      description: description ?? this.description,
      supportingEvidence: supportingEvidence ?? this.supportingEvidence,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'level': level.name,
      'description': description,
      'supportingEvidence': supportingEvidence,
    };
  }

  factory NarratorReliability.fromJson(Map<String, dynamic> json) {
    return NarratorReliability(
      level: ReliabilityLevel.values.firstWhere((e) => e.name == json['level']),
      description: json['description'],
      supportingEvidence: List<String>.from(json['supportingEvidence']),
    );
  }
}

/// Chain of narration analysis
class ChainAnalysis {
  final bool isComplete;
  final List<String> gaps;
  final List<String> weakLinks;
  final List<String> strengths;
  final String overallAssessment;

  const ChainAnalysis({
    required this.isComplete,
    required this.gaps,
    required this.weakLinks,
    required this.strengths,
    required this.overallAssessment,
  });

  ChainAnalysis copyWith({
    bool? isComplete,
    List<String>? gaps,
    List<String>? weakLinks,
    List<String>? strengths,
    String? overallAssessment,
  }) {
    return ChainAnalysis(
      isComplete: isComplete ?? this.isComplete,
      gaps: gaps ?? this.gaps,
      weakLinks: weakLinks ?? this.weakLinks,
      strengths: strengths ?? this.strengths,
      overallAssessment: overallAssessment ?? this.overallAssessment,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isComplete': isComplete,
      'gaps': gaps,
      'weakLinks': weakLinks,
      'strengths': strengths,
      'overallAssessment': overallAssessment,
    };
  }

  factory ChainAnalysis.fromJson(Map<String, dynamic> json) {
    return ChainAnalysis(
      isComplete: json['isComplete'],
      gaps: List<String>.from(json['gaps']),
      weakLinks: List<String>.from(json['weakLinks']),
      strengths: List<String>.from(json['strengths']),
      overallAssessment: json['overallAssessment'],
    );
  }
}

/// Textual analysis of hadith content
class TextualAnalysis {
  final bool languageConsistency;
  final bool contentConsistency;
  final bool historicalContext;
  final bool quranAlignment;
  final double overallScore;

  const TextualAnalysis({
    required this.languageConsistency,
    required this.contentConsistency,
    required this.historicalContext,
    required this.quranAlignment,
    required this.overallScore,
  });

  TextualAnalysis copyWith({
    bool? languageConsistency,
    bool? contentConsistency,
    bool? historicalContext,
    bool? quranAlignment,
    double? overallScore,
  }) {
    return TextualAnalysis(
      languageConsistency: languageConsistency ?? this.languageConsistency,
      contentConsistency: contentConsistency ?? this.contentConsistency,
      historicalContext: historicalContext ?? this.historicalContext,
      quranAlignment: quranAlignment ?? this.quranAlignment,
      overallScore: overallScore ?? this.overallScore,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'languageConsistency': languageConsistency,
      'contentConsistency': contentConsistency,
      'historicalContext': historicalContext,
      'quranAlignment': quranAlignment,
      'overallScore': overallScore,
    };
  }

  factory TextualAnalysis.fromJson(Map<String, dynamic> json) {
    return TextualAnalysis(
      languageConsistency: json['languageConsistency'],
      contentConsistency: json['contentConsistency'],
      historicalContext: json['historicalContext'],
      quranAlignment: json['quranAlignment'],
      overallScore: json['overallScore'].toDouble(),
    );
  }
}

/// Scholarly opinion on hadith
class ScholarlyOpinion {
  final String scholar;
  final String opinion;
  final String reasoning;
  final double confidence;

  const ScholarlyOpinion({
    required this.scholar,
    required this.opinion,
    required this.reasoning,
    required this.confidence,
  });

  ScholarlyOpinion copyWith({
    String? scholar,
    String? opinion,
    String? reasoning,
    double? confidence,
  }) {
    return ScholarlyOpinion(
      scholar: scholar ?? this.scholar,
      opinion: opinion ?? this.opinion,
      reasoning: reasoning ?? this.reasoning,
      confidence: confidence ?? this.confidence,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'scholar': scholar,
      'opinion': opinion,
      'reasoning': reasoning,
      'confidence': confidence,
    };
  }

  factory ScholarlyOpinion.fromJson(Map<String, dynamic> json) {
    return ScholarlyOpinion(
      scholar: json['scholar'],
      opinion: json['opinion'],
      reasoning: json['reasoning'],
      confidence: json['confidence'].toDouble(),
    );
  }
}

/// Study analytics for hadith learning
class StudyAnalytics {
  final String id;
  final int totalStudySessions;
  final int totalHadithsStudied;
  final int totalMemorized;
  final Duration averageSessionDuration;
  final int studyStreak;
  final int memorizationStreak;
  final List<String> favoriteThemes;
  final List<String> preferredNarrators;
  final List<WeeklyStudyProgress> weeklyProgress;
  final List<MonthlyStudyProgress> monthlyProgress;
  final List<String> achievements;
  final double learningVelocity;
  final double retentionRate;
  final DateTime generatedAt;

  const StudyAnalytics({
    required this.id,
    required this.totalStudySessions,
    required this.totalHadithsStudied,
    required this.totalMemorized,
    required this.averageSessionDuration,
    required this.studyStreak,
    required this.memorizationStreak,
    required this.favoriteThemes,
    required this.preferredNarrators,
    required this.weeklyProgress,
    required this.monthlyProgress,
    required this.achievements,
    required this.learningVelocity,
    required this.retentionRate,
    required this.generatedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'totalStudySessions': totalStudySessions,
      'totalHadithsStudied': totalHadithsStudied,
      'totalMemorized': totalMemorized,
      'averageSessionDuration': averageSessionDuration.inMinutes,
      'studyStreak': studyStreak,
      'memorizationStreak': memorizationStreak,
      'favoriteThemes': favoriteThemes,
      'preferredNarrators': preferredNarrators,
      'weeklyProgress': weeklyProgress.map((w) => w.toJson()).toList(),
      'monthlyProgress': monthlyProgress.map((m) => m.toJson()).toList(),
      'achievements': achievements,
      'learningVelocity': learningVelocity,
      'retentionRate': retentionRate,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }
}

/// Weekly study progress
class WeeklyStudyProgress {
  final DateTime weekStart;
  final int sessionsCount;
  final int totalHadithsStudied;
  final Duration totalDuration;

  const WeeklyStudyProgress({
    required this.weekStart,
    required this.sessionsCount,
    required this.totalHadithsStudied,
    required this.totalDuration,
  });

  Map<String, dynamic> toJson() {
    return {
      'weekStart': weekStart.toIso8601String(),
      'sessionsCount': sessionsCount,
      'totalHadithsStudied': totalHadithsStudied,
      'totalDuration': totalDuration.inMinutes,
    };
  }
}

/// Monthly study progress
class MonthlyStudyProgress {
  final DateTime monthStart;
  final int sessionsCount;
  final int totalHadithsStudied;
  final Duration totalDuration;

  const MonthlyStudyProgress({
    required this.monthStart,
    required this.sessionsCount,
    required this.totalHadithsStudied,
    required this.totalDuration,
  });

  Map<String, dynamic> toJson() {
    return {
      'monthStart': monthStart.toIso8601String(),
      'sessionsCount': sessionsCount,
      'totalHadithsStudied': totalHadithsStudied,
      'totalDuration': totalDuration.inMinutes,
    };
  }
}

/// Enums
enum RecommendationType {
  schedule,
  improvement,
  thematic,
  narrator,
  progression,
  complementary,
}

enum Priority { low, medium, high }

enum InsightType { pattern, comprehension, suggestion, resource }

enum AuthenticityLevel { sahih, hasan, daif, mawdu }

enum ReliabilityLevel { high, medium, low }
