import 'dart:async';
import 'dart:collection';
import '../models/spreadsheet_cell.dart';
import '../models/ui_component.dart';
import 'performance_optimizer.dart';

// Real-time Synchronization Service for collaborative editing
class RealtimeSyncService {
  static final RealtimeSyncService _instance = RealtimeSyncService._internal();
  factory RealtimeSyncService() => _instance;
  RealtimeSyncService._internal();
  
  // Stream controllers for real-time updates
  final StreamController<SyncEvent> _syncEventController = StreamController<SyncEvent>.broadcast();
  final StreamController<CollaboratorUpdate> _collaboratorController = StreamController<CollaboratorUpdate>.broadcast();
  
  // Connection state
  bool _isConnected = false;
  String? _sessionId;
  String? _userId;
  
  // Pending changes queue
  final Queue<ChangeEvent> _pendingChanges = Queue<ChangeEvent>();
  
  // Collaborators tracking
  final Map<String, Collaborator> _collaborators = {};
  
  // Conflict resolution
  final Map<String, ConflictResolution> _conflictResolutions = {};
  
  // Streams
  Stream<SyncEvent> get syncEvents => _syncEventController.stream;
  Stream<CollaboratorUpdate> get collaboratorUpdates => _collaboratorController.stream;
  
  // Getters
  bool get isConnected => _isConnected;
  String? get sessionId => _sessionId;
  List<Collaborator> get collaborators => _collaborators.values.toList();
  
  /// Initialize real-time sync
  Future<void> initialize(String userId, String sessionId) async {
    _userId = userId;
    _sessionId = sessionId;
    
    try {
      // Simulate connection setup
      await _connectToSession();
      _isConnected = true;
      
      // Start periodic sync
      _startPeriodicSync();
      
      _syncEventController.add(SyncEvent(
        type: SyncEventType.connected,
        data: {'sessionId': sessionId, 'userId': userId},
        timestamp: DateTime.now(),
      ));
    } catch (e) {
      _syncEventController.add(SyncEvent(
        type: SyncEventType.error,
        data: {'error': e.toString()},
        timestamp: DateTime.now(),
      ));
    }
  }
  
  /// Connect to sync session
  Future<void> _connectToSession() async {
    // Simulate network connection
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Add current user as collaborator
    _collaborators[_userId!] = Collaborator(
      id: _userId!,
      name: 'Current User',
      color: '#2196F3',
      isActive: true,
      lastSeen: DateTime.now(),
      cursor: null,
    );
  }
  
  /// Start periodic synchronization
  void _startPeriodicSync() {
    Timer.periodic(const Duration(seconds: 2), (timer) {
      if (!_isConnected) {
        timer.cancel();
        return;
      }
      
      _processPendingChanges();
      _updateCollaboratorStatus();
    });
  }
  
  /// Send cell change to other collaborators
  void sendCellChange(String cellAddress, SpreadsheetCell cell) {
    if (!_isConnected) {
      _queueChange(ChangeEvent(
        type: ChangeType.cellUpdate,
        cellAddress: cellAddress,
        data: cell.toMap(),
        userId: _userId!,
        timestamp: DateTime.now(),
      ));
      return;
    }
    
    final changeEvent = ChangeEvent(
      type: ChangeType.cellUpdate,
      cellAddress: cellAddress,
      data: cell.toMap(),
      userId: _userId!,
      timestamp: DateTime.now(),
    );
    
    _broadcastChange(changeEvent);
  }
  
  /// Send component change to other collaborators
  void sendComponentChange(UIComponent component, ComponentChangeType changeType) {
    if (!_isConnected) {
      _queueChange(ChangeEvent(
        type: ChangeType.componentUpdate,
        componentId: component.id,
        data: {
          'component': component.toMap(),
          'changeType': changeType.name,
        },
        userId: _userId!,
        timestamp: DateTime.now(),
      ));
      return;
    }
    
    final changeEvent = ChangeEvent(
      type: ChangeType.componentUpdate,
      componentId: component.id,
      data: {
        'component': component.toMap(),
        'changeType': changeType.name,
      },
      userId: _userId!,
      timestamp: DateTime.now(),
    );
    
    _broadcastChange(changeEvent);
  }
  
  /// Update cursor position for real-time collaboration
  void updateCursor(String? cellAddress, CursorPosition? position) {
    if (!_isConnected || _userId == null) return;
    
    final collaborator = _collaborators[_userId!];
    if (collaborator != null) {
      _collaborators[_userId!] = collaborator.copyWith(
        cursor: position,
        lastSeen: DateTime.now(),
      );
      
      _collaboratorController.add(CollaboratorUpdate(
        collaborator: _collaborators[_userId!]!,
        type: CollaboratorUpdateType.cursorMoved,
      ));
    }
  }
  
  /// Queue change for later sync
  void _queueChange(ChangeEvent change) {
    _pendingChanges.add(change);
    
    // Limit queue size
    while (_pendingChanges.length > 100) {
      _pendingChanges.removeFirst();
    }
  }
  
  /// Broadcast change to other collaborators
  void _broadcastChange(ChangeEvent change) {
    // Simulate network broadcast
    PerformanceOptimizer.debounce('broadcast_${change.id}', () {
      _syncEventController.add(SyncEvent(
        type: SyncEventType.changeReceived,
        data: change.toMap(),
        timestamp: DateTime.now(),
      ));
    });
  }
  
  /// Process pending changes
  void _processPendingChanges() {
    if (_pendingChanges.isEmpty) return;
    
    final changes = <ChangeEvent>[];
    while (_pendingChanges.isNotEmpty && changes.length < 10) {
      changes.add(_pendingChanges.removeFirst());
    }
    
    for (final change in changes) {
      _broadcastChange(change);
    }
  }
  
  /// Update collaborator status
  void _updateCollaboratorStatus() {
    final now = DateTime.now();
    final inactiveThreshold = const Duration(minutes: 5);
    
    for (final collaborator in _collaborators.values) {
      if (collaborator.id != _userId) {
        final timeSinceLastSeen = now.difference(collaborator.lastSeen);
        if (timeSinceLastSeen > inactiveThreshold && collaborator.isActive) {
          _collaborators[collaborator.id] = collaborator.copyWith(isActive: false);
          
          _collaboratorController.add(CollaboratorUpdate(
            collaborator: _collaborators[collaborator.id]!,
            type: CollaboratorUpdateType.statusChanged,
          ));
        }
      }
    }
  }
  
  /// Handle incoming changes from other collaborators
  void handleIncomingChange(Map<String, dynamic> changeData) {
    try {
      final change = ChangeEvent.fromMap(changeData);
      
      // Check for conflicts
      if (_hasConflict(change)) {
        _resolveConflict(change);
        return;
      }
      
      // Apply change
      _applyChange(change);
      
      _syncEventController.add(SyncEvent(
        type: SyncEventType.changeApplied,
        data: change.toMap(),
        timestamp: DateTime.now(),
      ));
    } catch (e) {
      _syncEventController.add(SyncEvent(
        type: SyncEventType.error,
        data: {'error': 'Failed to handle incoming change: $e'},
        timestamp: DateTime.now(),
      ));
    }
  }
  
  /// Check if change conflicts with local changes
  bool _hasConflict(ChangeEvent change) {
    // Simple conflict detection based on timestamp and target
    final key = change.cellAddress ?? change.componentId ?? '';
    return _conflictResolutions.containsKey(key);
  }
  
  /// Resolve conflict between local and remote changes
  void _resolveConflict(ChangeEvent remoteChange) {
    final key = remoteChange.cellAddress ?? remoteChange.componentId ?? '';
    
    // Use last-write-wins strategy for now
    final resolution = ConflictResolution(
      localChange: null, // Would be stored from local changes
      remoteChange: remoteChange,
      resolution: ConflictResolutionType.acceptRemote,
      timestamp: DateTime.now(),
    );
    
    _conflictResolutions[key] = resolution;
    _applyChange(remoteChange);
    
    _syncEventController.add(SyncEvent(
      type: SyncEventType.conflictResolved,
      data: {
        'key': key,
        'resolution': resolution.resolution.name,
        'change': remoteChange.toMap(),
      },
      timestamp: DateTime.now(),
    ));
  }
  
  /// Apply change to local state
  void _applyChange(ChangeEvent change) {
    // This would integrate with the actual spreadsheet/component state
    // For now, just emit the event for the UI to handle
    _syncEventController.add(SyncEvent(
      type: SyncEventType.changeApplied,
      data: change.toMap(),
      timestamp: DateTime.now(),
    ));
  }
  
  /// Add collaborator to session
  void addCollaborator(Collaborator collaborator) {
    _collaborators[collaborator.id] = collaborator;
    
    _collaboratorController.add(CollaboratorUpdate(
      collaborator: collaborator,
      type: CollaboratorUpdateType.joined,
    ));
  }
  
  /// Remove collaborator from session
  void removeCollaborator(String collaboratorId) {
    final collaborator = _collaborators.remove(collaboratorId);
    if (collaborator != null) {
      _collaboratorController.add(CollaboratorUpdate(
        collaborator: collaborator,
        type: CollaboratorUpdateType.left,
      ));
    }
  }
  
  /// Disconnect from sync session
  Future<void> disconnect() async {
    _isConnected = false;
    _sessionId = null;
    _userId = null;
    
    _collaborators.clear();
    _pendingChanges.clear();
    _conflictResolutions.clear();
    
    _syncEventController.add(SyncEvent(
      type: SyncEventType.disconnected,
      data: {},
      timestamp: DateTime.now(),
    ));
  }
  
  /// Dispose resources
  void dispose() {
    _syncEventController.close();
    _collaboratorController.close();
  }
}

// Enums and Models
enum SyncEventType {
  connected,
  disconnected,
  changeReceived,
  changeApplied,
  conflictResolved,
  error,
}

enum ChangeType {
  cellUpdate,
  componentUpdate,
  sheetUpdate,
}

enum ComponentChangeType {
  created,
  updated,
  deleted,
  moved,
}

enum CollaboratorUpdateType {
  joined,
  left,
  cursorMoved,
  statusChanged,
}

enum ConflictResolutionType {
  acceptLocal,
  acceptRemote,
  merge,
}

class SyncEvent {
  final SyncEventType type;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  
  SyncEvent({
    required this.type,
    required this.data,
    required this.timestamp,
  });
}

class ChangeEvent {
  final String id;
  final ChangeType type;
  final String? cellAddress;
  final String? componentId;
  final Map<String, dynamic> data;
  final String userId;
  final DateTime timestamp;
  
  ChangeEvent({
    String? id,
    required this.type,
    this.cellAddress,
    this.componentId,
    required this.data,
    required this.userId,
    required this.timestamp,
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString();
  
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.name,
      'cellAddress': cellAddress,
      'componentId': componentId,
      'data': data,
      'userId': userId,
      'timestamp': timestamp.millisecondsSinceEpoch,
    };
  }
  
  factory ChangeEvent.fromMap(Map<String, dynamic> map) {
    return ChangeEvent(
      id: map['id'],
      type: ChangeType.values.firstWhere((e) => e.name == map['type']),
      cellAddress: map['cellAddress'],
      componentId: map['componentId'],
      data: Map<String, dynamic>.from(map['data']),
      userId: map['userId'],
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp']),
    );
  }
}

class Collaborator {
  final String id;
  final String name;
  final String color;
  final bool isActive;
  final DateTime lastSeen;
  final CursorPosition? cursor;
  
  Collaborator({
    required this.id,
    required this.name,
    required this.color,
    required this.isActive,
    required this.lastSeen,
    this.cursor,
  });
  
  Collaborator copyWith({
    String? name,
    String? color,
    bool? isActive,
    DateTime? lastSeen,
    CursorPosition? cursor,
  }) {
    return Collaborator(
      id: id,
      name: name ?? this.name,
      color: color ?? this.color,
      isActive: isActive ?? this.isActive,
      lastSeen: lastSeen ?? this.lastSeen,
      cursor: cursor ?? this.cursor,
    );
  }
}

class CursorPosition {
  final String? cellAddress;
  final double? x;
  final double? y;
  
  CursorPosition({
    this.cellAddress,
    this.x,
    this.y,
  });
}

class CollaboratorUpdate {
  final Collaborator collaborator;
  final CollaboratorUpdateType type;
  
  CollaboratorUpdate({
    required this.collaborator,
    required this.type,
  });
}

class ConflictResolution {
  final ChangeEvent? localChange;
  final ChangeEvent remoteChange;
  final ConflictResolutionType resolution;
  final DateTime timestamp;
  
  ConflictResolution({
    this.localChange,
    required this.remoteChange,
    required this.resolution,
    required this.timestamp,
  });
}
