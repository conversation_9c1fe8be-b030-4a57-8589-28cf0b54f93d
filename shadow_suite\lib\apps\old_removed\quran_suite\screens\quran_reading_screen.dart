import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/quran_models.dart';
import '../services/quran_service.dart';
import '../widgets/verse_widget.dart';
import '../widgets/tafseer_dialog.dart';
import '../widgets/bookmark_dialog.dart';

/// Comprehensive Quran reading screen with RTL support and Tafseer integration
class QuranReadingScreen extends StatefulWidget {
  final int initialSurah;
  final int initialVerse;

  const QuranReadingScreen({
    super.key,
    this.initialSurah = 1,
    this.initialVerse = 1,
  });

  @override
  State<QuranReadingScreen> createState() => _QuranReadingScreenState();
}

class _QuranReadingScreenState extends State<QuranReadingScreen> {
  final QuranService _quranService = QuranService();
  final ScrollController _scrollController = ScrollController();
  final PageController _pageController = PageController();

  int _currentSurah = 1;
  int _currentVerse = 1; // Used for navigation and state tracking
  List<Surah> _surahs = [];
  Surah? _currentSurahData;
  ReadingSettings _settings = ReadingSettings.defaultSettings();
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _currentSurah = widget.initialSurah;
    _currentVerse = widget.initialVerse;
    _initializeQuranData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _initializeQuranData() async {
    try {
      setState(() => _isLoading = true);

      await _quranService.initialize();
      _surahs = _quranService.getAllSurahs();
      _currentSurahData = await _quranService.getSurah(_currentSurah);
      _settings = _quranService.getReadingSettings();

      setState(() => _isLoading = false);
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('Failed to load Quran data: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Future<void> _navigateToSurah(int surahNumber) async {
    if (surahNumber < 1 || surahNumber > 114) return;

    setState(() {
      _currentSurah = surahNumber;
      _currentVerse = 1;
    });

    // Load surah data asynchronously
    _quranService.getSurah(surahNumber).then((surah) {
      setState(() {
        _currentSurahData = surah;
      });
    });

    // Scroll to top when changing surah
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  void _showVerseOptions(Verse verse) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Verse $_currentSurah:${verse.number}${verse.number == _currentVerse ? ' (Current)' : ''}',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.book),
              title: const Text('View Tafseer'),
              onTap: () {
                Navigator.pop(context);
                _showTafseerDialog(verse);
              },
            ),
            ListTile(
              leading: const Icon(Icons.bookmark_add),
              title: const Text('Add Bookmark'),
              onTap: () {
                Navigator.pop(context);
                _showBookmarkDialog(verse);
              },
            ),
            ListTile(
              leading: const Icon(Icons.copy),
              title: const Text('Copy Verse'),
              onTap: () {
                Navigator.pop(context);
                _copyVerse(verse);
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('Share Verse'),
              onTap: () {
                Navigator.pop(context);
                _shareVerse(verse);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showTafseerDialog(Verse verse) {
    showDialog(
      context: context,
      builder: (context) => TafseerDialog(
        surahNumber: _currentSurah,
        verseNumber: verse.number,
        verse: verse,
      ),
    );
  }

  void _showBookmarkDialog(Verse verse) {
    showDialog(
      context: context,
      builder: (context) => BookmarkDialog(
        surahNumber: _currentSurah,
        verseNumber: verse.number,
        verse: verse,
      ),
    );
  }

  void _copyVerse(Verse verse) {
    final text =
        '${verse.arabicText}\n\n${verse.transliteration ?? ''}\n\nQuran $_currentSurah:${verse.number}';
    Clipboard.setData(ClipboardData(text: text));
    _showSuccessSnackBar('Verse copied to clipboard');
  }

  void _shareVerse(Verse verse) {
    // Implementation would use share_plus package
    // In production, would use:
    // final text = '${verse.arabicText}\n\n${verse.transliteration ?? ''}\n\nQuran $_currentSurah:${verse.number}';
    // Share.share(text);
    _showSuccessSnackBar('Sharing verse...');
  }

  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reading Settings'),
        content: StatefulBuilder(
          builder: (context, setDialogState) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: const Text('Font Size'),
                subtitle: Slider(
                  value: _settings.fontSize,
                  min: 12.0,
                  max: 32.0,
                  divisions: 20,
                  label: _settings.fontSize.round().toString(),
                  onChanged: (value) {
                    setDialogState(() {
                      _settings = _settings.copyWith(fontSize: value);
                    });
                  },
                ),
              ),
              SwitchListTile(
                title: const Text('Show Transliteration'),
                value: _settings.showTransliteration,
                onChanged: (value) {
                  setDialogState(() {
                    _settings = _settings.copyWith(showTransliteration: value);
                  });
                },
              ),
              SwitchListTile(
                title: const Text('Show Translation'),
                value: _settings.showTranslation,
                onChanged: (value) {
                  setDialogState(() {
                    _settings = _settings.copyWith(showTranslation: value);
                  });
                },
              ),
              SwitchListTile(
                title: const Text('Night Mode'),
                value: _settings.nightMode,
                onChanged: (value) {
                  setDialogState(() {
                    _settings = _settings.copyWith(nightMode: value);
                  });
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {});
              _quranService.updateReadingSettings(_settings);
              Navigator.pop(context);
              _showSuccessSnackBar('Settings updated');
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showSurahSelector() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Surah'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            itemCount: _surahs.length,
            itemBuilder: (context, index) {
              final surah = _surahs[index];
              return ListTile(
                leading: CircleAvatar(child: Text(surah.number.toString())),
                title: Text(surah.englishName),
                subtitle: Text(
                  '${surah.arabicName} • ${surah.versesCount} verses',
                ),
                trailing: surah.number == _currentSurah
                    ? const Icon(Icons.check, color: Colors.green)
                    : null,
                onTap: () {
                  Navigator.pop(context);
                  _navigateToSurah(surah.number);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Quran'),
          backgroundColor: Colors.green.shade700,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading Quran...'),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(_currentSurahData?.englishName ?? 'Quran'),
        backgroundColor: Colors.green.shade700,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.list),
            onPressed: _showSurahSelector,
            tooltip: 'Select Surah',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettingsDialog,
            tooltip: 'Settings',
          ),
        ],
      ),
      body: _currentSurahData == null
          ? const Center(child: Text('No surah data available'))
          : Column(
              children: [
                // Surah header
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.green.shade50,
                    border: Border(
                      bottom: BorderSide(color: Colors.green.shade200),
                    ),
                  ),
                  child: Column(
                    children: [
                      Text(
                        _currentSurahData!.arabicName,
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade800,
                        ),
                        textDirection: TextDirection.rtl,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _currentSurahData!.englishName,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.green.shade600,
                        ),
                      ),
                      Text(
                        '${_currentSurahData!.versesCount} verses • ${_currentSurahData!.revelationType.name.toUpperCase()}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.green.shade500,
                        ),
                      ),
                    ],
                  ),
                ),
                // Verses list
                Expanded(
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    itemCount: _currentSurahData!.verses.length,
                    itemBuilder: (context, index) {
                      final verse = _currentSurahData!.verses[index];
                      return VerseWidget(
                        verse: verse,
                        surahNumber: _currentSurah,
                        settings: _settings,
                        onTap: () => _showVerseOptions(verse),
                        onLongPress: () => _showTafseerDialog(verse),
                      );
                    },
                  ),
                ),
              ],
            ),
      bottomNavigationBar: _currentSurahData == null
          ? null
          : Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                border: Border(top: BorderSide(color: Colors.green.shade200)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: _currentSurah > 1
                        ? () => _navigateToSurah(_currentSurah - 1)
                        : null,
                    tooltip: 'Previous Surah',
                  ),
                  Text(
                    'Surah $_currentSurah of 114',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.green.shade700,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.arrow_forward),
                    onPressed: _currentSurah < 114
                        ? () => _navigateToSurah(_currentSurah + 1)
                        : null,
                    tooltip: 'Next Surah',
                  ),
                ],
              ),
            ),
    );
  }
}
