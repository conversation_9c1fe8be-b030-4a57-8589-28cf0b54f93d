import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/money_manager_models.dart';
import '../services/money_manager_providers.dart';

class AddAccountDialog extends ConsumerStatefulWidget {
  final Account? account;

  const AddAccountDialog({super.key, this.account});

  @override
  ConsumerState<AddAccountDialog> createState() => _AddAccountDialogState();
}

class _AddAccountDialogState extends ConsumerState<AddAccountDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _initialBalanceController = TextEditingController();

  AccountType _selectedType = AccountType.checking;
  String _selectedCurrency = 'USD';
  String _selectedColor = '#3498DB';
  bool _showInTotal = true;
  bool _allowNegative = false;

  final List<String> _currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD'];
  final List<String> _colors = [
    '#3498DB', '#27AE60', '#E74C3C', '#F39C12', 
    '#9B59B6', '#1ABC9C', '#E67E22', '#34495E'
  ];

  @override
  void initState() {
    super.initState();
    if (widget.account != null) {
      _initializeFromAccount();
    }
  }

  void _initializeFromAccount() {
    final account = widget.account!;
    _nameController.text = account.name;
    _initialBalanceController.text = account.initialBalance.toString();
    _selectedType = account.type;
    _selectedCurrency = account.currency;
    _selectedColor = account.color;
    _showInTotal = account.showInTotal;
    _allowNegative = account.allowNegative;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _initialBalanceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 600,
        constraints: const BoxConstraints(maxHeight: 700),
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildNameField(),
                      const SizedBox(height: 20),
                      _buildTypeSelector(),
                      const SizedBox(height: 20),
                      _buildInitialBalanceField(),
                      const SizedBox(height: 20),
                      _buildCurrencySelector(),
                      const SizedBox(height: 20),
                      _buildColorSelector(),
                      const SizedBox(height: 20),
                      _buildOptionsSection(),
                    ],
                  ),
                ),
              ),
            ),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Color(0xFF3498DB),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Row(
        children: [
          const Icon(Icons.account_balance, color: Colors.white, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              widget.account == null ? 'Add Account' : 'Edit Account',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildNameField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Account Name',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _nameController,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            hintText: 'e.g., Main Checking, Emergency Fund',
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter an account name';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Account Type',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: AccountType.values.map((type) {
            final isSelected = _selectedType == type;
            return GestureDetector(
              onTap: () => setState(() => _selectedType = type),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? const Color(0xFF3498DB) : Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected ? const Color(0xFF3498DB) : Colors.grey.shade300,
                  ),
                ),
                child: Text(
                  type.name.toUpperCase(),
                  style: TextStyle(
                    color: isSelected ? Colors.white : const Color(0xFF7F8C8D),
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                    fontSize: 12,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildInitialBalanceField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Initial Balance',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _initialBalanceController,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            prefixText: '\$ ',
            hintText: '0.00',
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter an initial balance';
            }
            if (double.tryParse(value) == null) {
              return 'Please enter a valid number';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildCurrencySelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Currency',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedCurrency,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
          ),
          items: _currencies.map((currency) => DropdownMenuItem(
            value: currency,
            child: Text(currency),
          )).toList(),
          onChanged: (value) => setState(() => _selectedCurrency = value!),
        ),
      ],
    );
  }

  Widget _buildColorSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Color',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _colors.map((color) {
            final isSelected = _selectedColor == color;
            return GestureDetector(
              onTap: () => setState(() => _selectedColor = color),
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Color(int.parse(color.replaceFirst('#', '0xFF'))),
                  borderRadius: BorderRadius.circular(8),
                  border: isSelected ? Border.all(color: Colors.black, width: 2) : null,
                ),
                child: isSelected ? const Icon(Icons.check, color: Colors.white) : null,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildOptionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Options',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 12),
        CheckboxListTile(
          title: const Text('Include in total balance'),
          subtitle: const Text('Show this account in overall balance calculations'),
          value: _showInTotal,
          onChanged: (value) => setState(() => _showInTotal = value!),
          controlAffinity: ListTileControlAffinity.leading,
        ),
        CheckboxListTile(
          title: const Text('Allow negative balance'),
          subtitle: const Text('Allow this account to have negative balances'),
          value: _allowNegative,
          onChanged: (value) => setState(() => _allowNegative = value!),
          controlAffinity: ListTileControlAffinity.leading,
        ),
      ],
    );
  }



  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Color(0xFFF8F9FA),
        border: Border(
          top: BorderSide(color: Color(0xFFE9ECEF)),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _saveAccount,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF27AE60),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(widget.account == null ? 'Add Account' : 'Update Account'),
            ),
          ),
        ],
      ),
    );
  }

  void _saveAccount() {
    if (!_formKey.currentState!.validate()) return;

    final initialBalance = double.parse(_initialBalanceController.text);

    final account = Account(
      id: widget.account?.id ?? 'acc_${DateTime.now().millisecondsSinceEpoch}',
      name: _nameController.text,
      type: _selectedType,
      initialBalance: initialBalance,
      currentBalance: widget.account?.currentBalance ?? initialBalance,
      currency: _selectedCurrency,
      color: _selectedColor,
      icon: _getAccountIcon(_selectedType),
      showInTotal: _showInTotal,
      allowNegative: _allowNegative,
      createdAt: widget.account?.createdAt ?? DateTime.now(),
      lastModified: DateTime.now(),
    );

    if (widget.account == null) {
      ref.read(accountsProvider.notifier).addAccount(account);
    } else {
      ref.read(accountsProvider.notifier).updateAccount(account);
    }

    Navigator.of(context).pop();
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(widget.account == null 
            ? 'Account added successfully' 
            : 'Account updated successfully'),
        backgroundColor: const Color(0xFF27AE60),
      ),
    );
  }

  String _getAccountIcon(AccountType type) {
    switch (type) {
      case AccountType.checking:
        return 'account_balance';
      case AccountType.savings:
        return 'savings';
      case AccountType.credit:
        return 'credit_card';
      case AccountType.cash:
        return 'account_balance_wallet';
      case AccountType.investment:
        return 'trending_up';
      case AccountType.loan:
        return 'money_off';
    }
  }
}
