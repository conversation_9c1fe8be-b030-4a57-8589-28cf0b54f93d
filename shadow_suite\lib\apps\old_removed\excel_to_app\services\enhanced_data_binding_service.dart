import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/excel_app_tool.dart';
import 'excel_formula_engine.dart';

// Enhanced Data Binding Service with Real-time Updates and Dependency Tracking
class EnhancedDataBindingService extends ChangeNotifier {
  static final EnhancedDataBindingService _instance = EnhancedDataBindingService._internal();
  factory EnhancedDataBindingService() => _instance;
  EnhancedDataBindingService._internal();

  // Real-time data cache with <100ms access time
  final Map<String, dynamic> _cellValues = {};
  final Map<String, Set<String>> _dependencyGraph = {};
  final Map<String, Set<String>> _reverseDependencyGraph = {};
  final Map<String, Timer> _updateTimers = {};
  
  // Performance tracking
  final Map<String, DateTime> _lastUpdateTimes = {};
  final Queue<PerformanceMetric> _performanceMetrics = Queue();
  
  // Update streams for real-time notifications
  final StreamController<CellUpdateEvent> _updateController = StreamController.broadcast();
  final StreamController<ValidationEvent> _validationController = StreamController.broadcast();
  
  // Formula engine integration
  late final ExcelFormulaEngine _formulaEngine;
  
  // Configuration
  static const int maxPerformanceMetrics = 1000;
  static const Duration updateDebounceTime = Duration(milliseconds: 50);
  static const Duration maxResponseTime = Duration(milliseconds: 100);

  // Initialize the service
  void initialize() {
    _formulaEngine = ExcelFormulaEngine();
  }

  // Get cell value with performance tracking
  dynamic getCellValue(String cellAddress, ExcelAppTool tool) {
    final startTime = DateTime.now();
    
    try {
      // Check cache first for <100ms response
      if (_cellValues.containsKey(cellAddress)) {
        _recordPerformance('getCellValue', startTime, true);
        return _cellValues[cellAddress];
      }
      
      // Get from spreadsheet
      final cell = tool.spreadsheet.cells[cellAddress];
      dynamic value;
      
      if (cell != null) {
        if (cell.formula != null && cell.formula!.isNotEmpty) {
          // Calculate formula value
          value = _formulaEngine.calculateFormula(cell.formula!, tool.spreadsheet.cells);
        } else {
          value = cell.value;
        }
      }
      
      // Cache the value
      _cellValues[cellAddress] = value;
      _recordPerformance('getCellValue', startTime, false);
      
      return value;
    } catch (error) {
      _recordPerformance('getCellValue', startTime, false, error: error.toString());
      return null;
    }
  }

  // Get formatted display value for UI components
  String getCellDisplayValue(String cellAddress, ExcelAppTool tool) {
    final value = getCellValue(cellAddress, tool);
    return _formatValueForDisplay(value);
  }

  // Update cell value with real-time propagation
  Future<void> updateCell(String cellAddress, dynamic value, ExcelAppTool tool, WidgetRef ref) async {
    final startTime = DateTime.now();
    
    try {
      // Validate the update
      final validationResult = await _validateCellUpdate(cellAddress, value, tool);
      if (!validationResult.isValid) {
        _validationController.add(ValidationEvent(
          cellAddress: cellAddress,
          value: value,
          isValid: false,
          errors: validationResult.errors,
          timestamp: DateTime.now(),
        ));
        return;
      }

      // Store old value for change tracking
      final oldValue = _cellValues[cellAddress];
      
      // Update cache immediately for real-time response
      _cellValues[cellAddress] = value;
      
      // Update the actual spreadsheet cell
      final cell = tool.spreadsheet.cells[cellAddress];
      if (cell != null) {
        // Create updated cell
        final updatedCell = ExcelCell(
          address: cell.address,
          value: value,
          formula: cell.formula,
          isFormula: cell.isFormula,
          formatting: cell.formatting,
        );
        
        // Update in spreadsheet
        tool.spreadsheet.cells[cellAddress] = updatedCell;
      }
      
      // Debounced dependency updates to prevent cascading recalculations
      _scheduleDebounceUpdate(cellAddress, tool, ref);
      
      // Emit update event
      _updateController.add(CellUpdateEvent(
        cellAddress: cellAddress,
        oldValue: oldValue,
        newValue: value,
        timestamp: DateTime.now(),
        propagatedCells: [],
      ));
      
      _recordPerformance('updateCell', startTime, false);
      
    } catch (error) {
      _recordPerformance('updateCell', startTime, false, error: error.toString());
      rethrow;
    }
  }

  // Schedule debounced update to prevent excessive recalculations
  void _scheduleDebounceUpdate(String cellAddress, ExcelAppTool tool, WidgetRef ref) {
    // Cancel existing timer
    _updateTimers[cellAddress]?.cancel();
    
    // Schedule new update
    _updateTimers[cellAddress] = Timer(updateDebounceTime, () {
      _propagateUpdates(cellAddress, tool, ref);
      _updateTimers.remove(cellAddress);
    });
  }

  // Propagate updates to dependent cells with cycle detection
  Future<void> _propagateUpdates(String cellAddress, ExcelAppTool tool, WidgetRef ref) async {
    final startTime = DateTime.now();
    final propagatedCells = <String>[];
    final visitedCells = <String>{};
    final processingStack = <String>[];
    
    try {
      await _propagateUpdatesRecursive(
        cellAddress, 
        tool, 
        ref, 
        propagatedCells, 
        visitedCells, 
        processingStack
      );
      
      // Emit propagation complete event
      _updateController.add(CellUpdateEvent(
        cellAddress: cellAddress,
        oldValue: null,
        newValue: null,
        timestamp: DateTime.now(),
        propagatedCells: propagatedCells,
      ));
      
      _recordPerformance('propagateUpdates', startTime, false);
      
    } catch (error) {
      _recordPerformance('propagateUpdates', startTime, false, error: error.toString());
    }
  }

  // Recursive propagation with cycle detection
  Future<void> _propagateUpdatesRecursive(
    String cellAddress,
    ExcelAppTool tool,
    WidgetRef ref,
    List<String> propagatedCells,
    Set<String> visitedCells,
    List<String> processingStack,
  ) async {
    // Cycle detection
    if (processingStack.contains(cellAddress)) {
      throw Exception('Circular dependency detected: ${processingStack.join(' -> ')} -> $cellAddress');
    }
    
    if (visitedCells.contains(cellAddress)) {
      return;
    }
    
    visitedCells.add(cellAddress);
    processingStack.add(cellAddress);
    
    // Get dependent cells
    final dependentCells = _reverseDependencyGraph[cellAddress] ?? <String>{};
    
    for (final dependentCell in dependentCells) {
      // Recalculate dependent cell
      final cell = tool.spreadsheet.cells[dependentCell];
      if (cell != null && cell.formula != null && cell.formula!.isNotEmpty) {
        final newValue = _formulaEngine.calculateFormula(cell.formula!, tool.spreadsheet.cells);
        
        // Update cache
        final oldValue = _cellValues[dependentCell];
        _cellValues[dependentCell] = newValue;
        
        propagatedCells.add(dependentCell);
        
        // Emit individual cell update
        _updateController.add(CellUpdateEvent(
          cellAddress: dependentCell,
          oldValue: oldValue,
          newValue: newValue,
          timestamp: DateTime.now(),
          propagatedCells: [],
        ));
        
        // Recursively update dependents
        await _propagateUpdatesRecursive(
          dependentCell, 
          tool, 
          ref, 
          propagatedCells, 
          visitedCells, 
          processingStack
        );
      }
    }
    
    processingStack.remove(cellAddress);
  }

  // Build dependency graph for efficient updates
  void buildDependencyGraph(ExcelAppTool tool) {
    _dependencyGraph.clear();
    _reverseDependencyGraph.clear();

    for (final entry in tool.spreadsheet.cells.entries) {
      final cellAddress = entry.key;
      final cell = entry.value;

      // Extract dependencies from formula if it exists
      if (cell.isFormula && cell.formula != null) {
        final dependencies = _extractDependenciesFromFormula(cell.formula!);
        if (dependencies.isNotEmpty) {
          _dependencyGraph[cellAddress] = Set.from(dependencies);

          // Build reverse dependency graph
          for (final dependency in dependencies) {
            _reverseDependencyGraph.putIfAbsent(dependency, () => <String>{}).add(cellAddress);
          }
        }
      }
    }
  }

  // Extract cell dependencies from formula
  Set<String> _extractDependenciesFromFormula(String formula) {
    final dependencies = <String>{};

    // Simple regex to find cell references (A1, B2, etc.)
    final cellRefPattern = RegExp(r'[A-Z]+\d+');
    final matches = cellRefPattern.allMatches(formula);

    for (final match in matches) {
      dependencies.add(match.group(0)!);
    }

    return dependencies;
  }

  // Validate cell update with comprehensive rules
  Future<ValidationResult> _validateCellUpdate(String cellAddress, dynamic value, ExcelAppTool tool) async {
    final errors = <String>[];

    try {
      // Basic type validation
      if (value != null) {
        // Range validation for numbers
        if (value is num) {
          if (value.isInfinite || value.isNaN) {
            errors.add('Invalid numeric value: $value');
          }
        }

        // String length validation
        if (value is String && value.length > 32767) {
          errors.add('Text too long. Maximum 32,767 characters allowed');
        }
      }

      return ValidationResult(
        isValid: errors.isEmpty,
        errors: errors,
      );

    } catch (error) {
      return ValidationResult(
        isValid: false,
        errors: ['Validation error: $error'],
      );
    }
  }

  // Performance tracking and optimization
  void _recordPerformance(String operation, DateTime startTime, bool fromCache, {String? error}) {
    final duration = DateTime.now().difference(startTime);
    
    final metric = PerformanceMetric(
      operation: operation,
      duration: duration,
      fromCache: fromCache,
      timestamp: DateTime.now(),
      error: error,
    );
    
    _performanceMetrics.add(metric);
    
    // Keep only recent metrics
    while (_performanceMetrics.length > maxPerformanceMetrics) {
      _performanceMetrics.removeFirst();
    }
    
    // Log slow operations
    if (duration > maxResponseTime) {
      debugPrint('Slow operation detected: $operation took ${duration.inMilliseconds}ms');
    }
  }

  // Utility methods
  String _formatValueForDisplay(dynamic value) {
    if (value == null) return '';
    if (value is num) {
      if (value == value.toInt()) {
        return value.toInt().toString();
      } else {
        return value.toStringAsFixed(2);
      }
    }
    return value.toString();
  }



  // Getters for monitoring and debugging
  Map<String, dynamic> get cellValues => Map.unmodifiable(_cellValues);
  Map<String, Set<String>> get dependencyGraph => Map.unmodifiable(_dependencyGraph);
  List<PerformanceMetric> get recentMetrics => _performanceMetrics.toList();
  Stream<CellUpdateEvent> get updateStream => _updateController.stream;
  Stream<ValidationEvent> get validationStream => _validationController.stream;

  // Performance statistics
  Map<String, dynamic> getPerformanceStats() {
    if (_performanceMetrics.isEmpty) return {};
    
    final durations = _performanceMetrics.map((m) => m.duration.inMilliseconds).toList();
    final cacheHits = _performanceMetrics.where((m) => m.fromCache).length;
    final errors = _performanceMetrics.where((m) => m.error != null).length;
    
    durations.sort();
    
    return {
      'total_operations': _performanceMetrics.length,
      'cache_hit_rate': (cacheHits / _performanceMetrics.length * 100).toStringAsFixed(1),
      'error_rate': (errors / _performanceMetrics.length * 100).toStringAsFixed(1),
      'avg_response_time': (durations.reduce((a, b) => a + b) / durations.length).toStringAsFixed(1),
      'median_response_time': durations[durations.length ~/ 2],
      'p95_response_time': durations[(durations.length * 0.95).floor()],
      'max_response_time': durations.last,
    };
  }

  // Cleanup
  void clearCache() {
    _cellValues.clear();
    _lastUpdateTimes.clear();
    notifyListeners();
  }

  @override
  void dispose() {
    _updateController.close();
    _validationController.close();
    for (final timer in _updateTimers.values) {
      timer.cancel();
    }
    _updateTimers.clear();
    super.dispose();
  }
}

// Supporting Models
class CellUpdateEvent {
  final String cellAddress;
  final dynamic oldValue;
  final dynamic newValue;
  final DateTime timestamp;
  final List<String> propagatedCells;

  const CellUpdateEvent({
    required this.cellAddress,
    required this.oldValue,
    required this.newValue,
    required this.timestamp,
    required this.propagatedCells,
  });
}

class ValidationEvent {
  final String cellAddress;
  final dynamic value;
  final bool isValid;
  final List<String> errors;
  final DateTime timestamp;

  const ValidationEvent({
    required this.cellAddress,
    required this.value,
    required this.isValid,
    required this.errors,
    required this.timestamp,
  });
}

class ValidationResult {
  final bool isValid;
  final List<String> errors;

  const ValidationResult({
    required this.isValid,
    required this.errors,
  });
}

class PerformanceMetric {
  final String operation;
  final Duration duration;
  final bool fromCache;
  final DateTime timestamp;
  final String? error;

  const PerformanceMetric({
    required this.operation,
    required this.duration,
    required this.fromCache,
    required this.timestamp,
    this.error,
  });
}
