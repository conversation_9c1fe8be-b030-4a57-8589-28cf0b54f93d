import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Advanced Budget Forecasting Screen with AI-powered predictions
class BudgetForecastingScreen extends ConsumerStatefulWidget {
  const BudgetForecastingScreen({super.key});

  @override
  ConsumerState<BudgetForecastingScreen> createState() => _BudgetForecastingScreenState();
}

class _BudgetForecastingScreenState extends ConsumerState<BudgetForecastingScreen> {
  int _selectedMonths = 6;
  String _selectedScenario = 'realistic';
  bool _includeInflation = true;
  bool _includeSeasonality = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Budget Forecasting'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showForecastSettings,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildForecastControls(),
            const SizedBox(height: 24),
            _buildForecastSummary(),
            const SizedBox(height: 24),
            _buildScenarioComparison(),
            const SizedBox(height: 24),
            _buildMonthlyBreakdown(),
            const SizedBox(height: 24),
            _buildRecommendations(),
          ],
        ),
      ),
    );
  }

  Widget _buildForecastControls() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Forecast Parameters',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Time period selector
            Row(
              children: [
                const Text('Forecast Period: '),
                const SizedBox(width: 8),
                DropdownButton<int>(
                  value: _selectedMonths,
                  items: [3, 6, 12, 24].map((months) {
                    return DropdownMenuItem(
                      value: months,
                      child: Text('$months months'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() => _selectedMonths = value);
                    }
                  },
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Scenario selector
            Row(
              children: [
                const Text('Scenario: '),
                const SizedBox(width: 8),
                DropdownButton<String>(
                  value: _selectedScenario,
                  items: [
                    const DropdownMenuItem(value: 'optimistic', child: Text('Optimistic')),
                    const DropdownMenuItem(value: 'realistic', child: Text('Realistic')),
                    const DropdownMenuItem(value: 'pessimistic', child: Text('Pessimistic')),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() => _selectedScenario = value);
                    }
                  },
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Advanced options
            CheckboxListTile(
              title: const Text('Include Inflation'),
              subtitle: const Text('Factor in 3.2% annual inflation rate'),
              value: _includeInflation,
              onChanged: (value) => setState(() => _includeInflation = value ?? false),
              dense: true,
            ),
            CheckboxListTile(
              title: const Text('Include Seasonality'),
              subtitle: const Text('Account for seasonal spending patterns'),
              value: _includeSeasonality,
              onChanged: (value) => setState(() => _includeSeasonality = value ?? false),
              dense: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildForecastSummary() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Forecast Summary ($_selectedMonths months)',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'Projected Income',
                    '\$${_calculateProjectedIncome().toStringAsFixed(0)}',
                    Colors.green,
                    Icons.trending_up,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSummaryCard(
                    'Projected Expenses',
                    '\$${_calculateProjectedExpenses().toStringAsFixed(0)}',
                    Colors.red,
                    Icons.trending_down,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'Net Cash Flow',
                    '\$${_calculateNetCashFlow().toStringAsFixed(0)}',
                    _calculateNetCashFlow() >= 0 ? Colors.green : Colors.red,
                    _calculateNetCashFlow() >= 0 ? Icons.arrow_upward : Icons.arrow_downward,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSummaryCard(
                    'Savings Rate',
                    '${_calculateSavingsRate().toStringAsFixed(1)}%',
                    Colors.blue,
                    Icons.savings,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: color.withValues(alpha: 0.8),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScenarioComparison() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Scenario Comparison',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Table(
              border: TableBorder.all(color: Colors.grey.shade300),
              children: [
                const TableRow(
                  decoration: BoxDecoration(color: Colors.grey),
                  children: [
                    Padding(
                      padding: EdgeInsets.all(8),
                      child: Text('Scenario', style: TextStyle(fontWeight: FontWeight.bold)),
                    ),
                    Padding(
                      padding: EdgeInsets.all(8),
                      child: Text('Net Cash Flow', style: TextStyle(fontWeight: FontWeight.bold)),
                    ),
                    Padding(
                      padding: EdgeInsets.all(8),
                      child: Text('Probability', style: TextStyle(fontWeight: FontWeight.bold)),
                    ),
                  ],
                ),
                _buildScenarioRow('Optimistic', 15420, 25),
                _buildScenarioRow('Realistic', 8750, 50),
                _buildScenarioRow('Pessimistic', 2100, 25),
              ],
            ),
          ],
        ),
      ),
    );
  }

  TableRow _buildScenarioRow(String scenario, double cashFlow, int probability) {
    final isSelected = scenario.toLowerCase() == _selectedScenario;
    return TableRow(
      decoration: BoxDecoration(
        color: isSelected ? Colors.green.withValues(alpha: 0.1) : null,
      ),
      children: [
        Padding(
          padding: const EdgeInsets.all(8),
          child: Text(
            scenario,
            style: TextStyle(
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(8),
          child: Text(
            '\$${cashFlow.toStringAsFixed(0)}',
            style: TextStyle(
              color: cashFlow >= 0 ? Colors.green : Colors.red,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(8),
          child: Text(
            '$probability%',
            style: TextStyle(
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMonthlyBreakdown() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Monthly Breakdown',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            SizedBox(
              height: 200,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedMonths,
                itemBuilder: (context, index) {
                  final month = DateTime.now().add(Duration(days: 30 * (index + 1)));
                  final monthName = _getMonthName(month.month);
                  final income = _calculateMonthlyIncome(index);
                  final expenses = _calculateMonthlyExpenses(index);
                  final netFlow = income - expenses;
                  
                  return Container(
                    width: 120,
                    margin: const EdgeInsets.only(right: 12),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          monthName,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Text('Income: \$${income.toStringAsFixed(0)}', style: const TextStyle(color: Colors.green)),
                        Text('Expenses: \$${expenses.toStringAsFixed(0)}', style: const TextStyle(color: Colors.red)),
                        const SizedBox(height: 4),
                        Text(
                          'Net: \$${netFlow.toStringAsFixed(0)}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: netFlow >= 0 ? Colors.green : Colors.red,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendations() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'AI Recommendations',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            ..._generateRecommendations().map((recommendation) {
              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: recommendation['color'].withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: recommendation['color'].withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(recommendation['icon'], color: recommendation['color']),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            recommendation['title'],
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(recommendation['description']),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  // Calculation methods
  double _calculateProjectedIncome() {
    final baseIncome = 5000.0; // Base monthly income
    final multiplier = _selectedScenario == 'optimistic' ? 1.15 : 
                      _selectedScenario == 'pessimistic' ? 0.85 : 1.0;
    final inflationFactor = _includeInflation ? 1.032 : 1.0;
    return baseIncome * _selectedMonths * multiplier * inflationFactor;
  }

  double _calculateProjectedExpenses() {
    final baseExpenses = 3500.0; // Base monthly expenses
    final multiplier = _selectedScenario == 'optimistic' ? 0.9 : 
                      _selectedScenario == 'pessimistic' ? 1.2 : 1.0;
    final inflationFactor = _includeInflation ? 1.032 : 1.0;
    return baseExpenses * _selectedMonths * multiplier * inflationFactor;
  }

  double _calculateNetCashFlow() {
    return _calculateProjectedIncome() - _calculateProjectedExpenses();
  }

  double _calculateSavingsRate() {
    final income = _calculateProjectedIncome();
    final savings = _calculateNetCashFlow();
    return income > 0 ? (savings / income) * 100 : 0;
  }

  double _calculateMonthlyIncome(int monthIndex) {
    final baseIncome = 5000.0;
    final seasonalFactor = _includeSeasonality ? _getSeasonalFactor(monthIndex) : 1.0;
    final multiplier = _selectedScenario == 'optimistic' ? 1.15 : 
                      _selectedScenario == 'pessimistic' ? 0.85 : 1.0;
    return baseIncome * seasonalFactor * multiplier;
  }

  double _calculateMonthlyExpenses(int monthIndex) {
    final baseExpenses = 3500.0;
    final seasonalFactor = _includeSeasonality ? _getSeasonalExpenseFactor(monthIndex) : 1.0;
    final multiplier = _selectedScenario == 'optimistic' ? 0.9 : 
                      _selectedScenario == 'pessimistic' ? 1.2 : 1.0;
    return baseExpenses * seasonalFactor * multiplier;
  }

  double _getSeasonalFactor(int monthIndex) {
    // Simulate seasonal income variations
    final month = (DateTime.now().month + monthIndex) % 12;
    switch (month) {
      case 11: case 0: return 1.2; // December, January - holiday bonuses
      case 5: case 6: return 1.1; // June, July - summer work
      default: return 1.0;
    }
  }

  double _getSeasonalExpenseFactor(int monthIndex) {
    // Simulate seasonal expense variations
    final month = (DateTime.now().month + monthIndex) % 12;
    switch (month) {
      case 11: case 0: return 1.3; // December, January - holiday spending
      case 7: case 8: return 1.15; // August, September - back to school
      default: return 1.0;
    }
  }

  String _getMonthName(int month) {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[month - 1];
  }

  List<Map<String, dynamic>> _generateRecommendations() {
    final recommendations = <Map<String, dynamic>>[];
    
    if (_calculateSavingsRate() < 20) {
      recommendations.add({
        'title': 'Increase Savings Rate',
        'description': 'Your projected savings rate is below 20%. Consider reducing discretionary spending.',
        'icon': Icons.savings,
        'color': Colors.orange,
      });
    }
    
    if (_selectedScenario == 'pessimistic' && _calculateNetCashFlow() < 1000) {
      recommendations.add({
        'title': 'Build Emergency Fund',
        'description': 'In pessimistic scenarios, your cash flow is low. Build an emergency fund.',
        'icon': Icons.security,
        'color': Colors.red,
      });
    }
    
    if (_includeInflation) {
      recommendations.add({
        'title': 'Inflation Protection',
        'description': 'Consider investments that protect against inflation, such as I-bonds or TIPS.',
        'icon': Icons.trending_up,
        'color': Colors.blue,
      });
    }
    
    return recommendations;
  }

  void _showForecastSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Forecast Settings'),
        content: const Text('Advanced forecasting settings will be available here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
