import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/islamic_providers.dart';

class TafseerListScreen extends ConsumerWidget {
  const TafseerListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text('Tafseer Collections'),
        backgroundColor: const Color(0xFF27AE60),
        foregroundColor: Colors.white,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            onPressed: () {
              ref.read(islamicAppCurrentScreenProvider.notifier).state =
                  IslamicAppScreen.dashboard;
            },
            icon: const Icon(Icons.home),
            tooltip: 'Back to Dashboard',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: 32),
            _buildTafseerList(context, ref),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF27AE60),
            const Color(0xFF27AE60).withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Tafseer Collections',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Comprehensive explanations and interpretations of the Quran',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
          const Icon(Icons.auto_stories, size: 64, color: Colors.white),
        ],
      ),
    );
  }

  Widget _buildTafseerList(BuildContext context, WidgetRef ref) {
    final tafseerCollections = [
      TafseerCollection(
        id: 'ibn_kathir',
        name: 'Tafseer Ibn Kathir',
        arabicName: 'تفسير ابن كثير',
        author: 'Ibn Kathir',
        description: 'One of the most respected and widely used commentaries',
        language: 'Arabic/English',
        type: TafseerType.classical,
        icon: Icons.star,
        color: const Color(0xFF27AE60),
      ),
      TafseerCollection(
        id: 'tabari',
        name: 'Tafseer at-Tabari',
        arabicName: 'تفسير الطبري',
        author: 'At-Tabari',
        description: 'Comprehensive historical and linguistic commentary',
        language: 'Arabic',
        type: TafseerType.classical,
        icon: Icons.history_edu,
        color: const Color(0xFF3498DB),
      ),
      TafseerCollection(
        id: 'qurtubi',
        name: 'Tafseer al-Qurtubi',
        arabicName: 'تفسير القرطبي',
        author: 'Al-Qurtubi',
        description: 'Focus on legal rulings and jurisprudence',
        language: 'Arabic',
        type: TafseerType.classical,
        icon: Icons.gavel,
        color: const Color(0xFF9B59B6),
      ),
      TafseerCollection(
        id: 'jalalayn',
        name: 'Tafseer al-Jalalayn',
        arabicName: 'تفسير الجلالين',
        author: 'Al-Mahalli & As-Suyuti',
        description: 'Concise and accessible commentary',
        language: 'Arabic/English',
        type: TafseerType.classical,
        icon: Icons.lightbulb,
        color: const Color(0xFFE67E22),
      ),
      TafseerCollection(
        id: 'maariful_quran',
        name: 'Ma\'ariful Quran',
        arabicName: 'معارف القرآن',
        author: 'Mufti Muhammad Shafi',
        description: 'Modern comprehensive commentary',
        language: 'Urdu/English',
        type: TafseerType.modern,
        icon: Icons.book,
        color: const Color(0xFF1ABC9C),
      ),
      TafseerCollection(
        id: 'tafheem',
        name: 'Tafheem-ul-Quran',
        arabicName: 'تفهيم القرآن',
        author: 'Sayyid Abul Ala Maududi',
        description: 'Contemporary interpretation with social context',
        language: 'Urdu/English',
        type: TafseerType.modern,
        icon: Icons.insights,
        color: const Color(0xFFE74C3C),
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Available Commentaries',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'Explore different scholarly interpretations of the Quran',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: const Color(0xFF7F8C8D)),
        ),
        const SizedBox(height: 24),
        _buildTypeSection(
          context,
          ref,
          'Classical Commentaries',
          tafseerCollections
              .where((t) => t.type == TafseerType.classical)
              .toList(),
        ),
        const SizedBox(height: 32),
        _buildTypeSection(
          context,
          ref,
          'Modern Commentaries',
          tafseerCollections
              .where((t) => t.type == TafseerType.modern)
              .toList(),
        ),
      ],
    );
  }

  Widget _buildTypeSection(
    BuildContext context,
    WidgetRef ref,
    String title,
    List<TafseerCollection> collections,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 16),
        ...collections.map(
          (collection) => _buildTafseerCard(context, ref, collection),
        ),
      ],
    );
  }

  Widget _buildTafseerCard(
    BuildContext context,
    WidgetRef ref,
    TafseerCollection collection,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          // Navigate to tafseer reading screen
          ref.read(islamicAppCurrentScreenProvider.notifier).state =
              IslamicAppScreen.tafseerReading;
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: collection.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(collection.icon, color: collection.color, size: 32),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      collection.name,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF2C3E50),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      collection.arabicName,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: collection.color,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      collection.description,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: const Color(0xFF7F8C8D),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(Icons.person, size: 16, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                        Text(
                          collection.author,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: Colors.grey[600]),
                        ),
                        const SizedBox(width: 16),
                        Icon(Icons.language, size: 16, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                        Text(
                          collection.language,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Column(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: collection.type == TafseerType.classical
                          ? Colors.amber.withValues(alpha: 0.1)
                          : Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      collection.type == TafseerType.classical
                          ? 'Classical'
                          : 'Modern',
                      style: TextStyle(
                        color: collection.type == TafseerType.classical
                            ? Colors.amber[700]
                            : Colors.blue[700],
                        fontWeight: FontWeight.w600,
                        fontSize: 11,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.grey[400],
                    size: 16,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class TafseerCollection {
  final String id;
  final String name;
  final String arabicName;
  final String author;
  final String description;
  final String language;
  final TafseerType type;
  final IconData icon;
  final Color color;

  const TafseerCollection({
    required this.id,
    required this.name,
    required this.arabicName,
    required this.author,
    required this.description,
    required this.language,
    required this.type,
    required this.icon,
    required this.color,
  });
}

enum TafseerType { classical, modern }
