import 'package:uuid/uuid.dart';

enum TodoPriority {
  low('Low'),
  medium('Medium'),
  high('High');

  const TodoPriority(this.displayName);
  final String displayName;

  static List<String> get allPriorities =>
      TodoPriority.values.map((e) => e.displayName).toList();
}

enum TodoStatus {
  todo('To Do'),
  inProgress('In Progress'),
  done('Done');

  const TodoStatus(this.displayName);
  final String displayName;

  static List<String> get allStatuses =>
      TodoStatus.values.map((e) => e.displayName).toList();
}

enum RecurrenceType {
  none('None'),
  daily('Daily'),
  weekly('Weekly'),
  monthly('Monthly'),
  yearly('Yearly');

  const RecurrenceType(this.displayName);
  final String displayName;

  static List<String> get allTypes =>
      RecurrenceType.values.map((e) => e.displayName).toList();
}

class SubTask {
  final String id;
  final String title;
  final bool isCompleted;

  SubTask({String? id, required this.title, this.isCompleted = false})
    : id = id ?? const Uuid().v4();

  SubTask copyWith({String? title, bool? isCompleted}) {
    return SubTask(
      id: id,
      title: title ?? this.title,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }

  Map<String, dynamic> toMap() {
    return {'id': id, 'title': title, 'isCompleted': isCompleted ? 1 : 0};
  }

  factory SubTask.fromMap(Map<String, dynamic> map) {
    return SubTask(
      id: map['id'],
      title: map['title'],
      isCompleted: map['isCompleted'] == 1,
    );
  }
}

class Todo {
  final String id;
  final String title;
  final String description;
  final TodoPriority priority;
  final TodoStatus status;
  final String category;
  final DateTime? dueDate;
  final DateTime? reminderTime;
  final RecurrenceType recurrenceType;
  final List<SubTask> subTasks;
  final DateTime createdAt;
  final DateTime updatedAt;

  Todo({
    String? id,
    required this.title,
    required this.description,
    required this.priority,
    required this.status,
    required this.category,
    this.dueDate,
    this.reminderTime,
    this.recurrenceType = RecurrenceType.none,
    required this.subTasks,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Todo copyWith({
    String? title,
    String? description,
    TodoPriority? priority,
    TodoStatus? status,
    String? category,
    DateTime? dueDate,
    List<SubTask>? subTasks,
    DateTime? updatedAt,
  }) {
    return Todo(
      id: id,
      title: title ?? this.title,
      description: description ?? this.description,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      category: category ?? this.category,
      dueDate: dueDate ?? this.dueDate,
      subTasks: subTasks ?? this.subTasks,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  double get progressPercentage {
    if (subTasks.isEmpty) return status == TodoStatus.done ? 1.0 : 0.0;
    final completedCount = subTasks.where((task) => task.isCompleted).length;
    return completedCount / subTasks.length;
  }

  bool get isOverdue {
    if (dueDate == null || status == TodoStatus.done) return false;
    return DateTime.now().isAfter(dueDate!);
  }

  bool get isCompleted => status == TodoStatus.done;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'priority': priority.name,
      'status': status.name,
      'category': category,
      'dueDate': dueDate?.millisecondsSinceEpoch,
      'subTasks': subTasks.map((task) => task.toMap()).toList(),
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory Todo.fromMap(Map<String, dynamic> map) {
    return Todo(
      id: map['id'],
      title: map['title'],
      description: map['description'],
      priority: TodoPriority.values.firstWhere(
        (p) => p.name == map['priority'],
      ),
      status: TodoStatus.values.firstWhere((s) => s.name == map['status']),
      category: map['category'],
      dueDate: map['dueDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['dueDate'])
          : null,
      subTasks:
          (map['subTasks'] as List<dynamic>?)
              ?.map((taskMap) => SubTask.fromMap(taskMap))
              .toList() ??
          [],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
    );
  }

  @override
  String toString() {
    return 'Todo(id: $id, title: $title, priority: $priority, status: $status, progress: ${(progressPercentage * 100).toStringAsFixed(1)}%)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Todo && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum TodoCategory {
  personal('Personal'),
  work('Work'),
  study('Study'),
  health('Health'),
  finance('Finance'),
  other('Other');

  const TodoCategory(this.displayName);
  final String displayName;

  static List<String> get allCategories =>
      TodoCategory.values.map((e) => e.displayName).toList();
}
