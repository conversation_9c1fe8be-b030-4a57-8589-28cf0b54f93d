import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_theme.dart';
import '../../services/memo_providers.dart';
import '../../models/note.dart';
import '../../widgets/checklist_editor.dart';
import '../../widgets/canvas_drawing_editor.dart';

class NoteEditorScreen extends ConsumerStatefulWidget {
  const NoteEditorScreen({super.key});

  @override
  ConsumerState<NoteEditorScreen> createState() => _NoteEditorScreenState();
}

class _NoteEditorScreenState extends ConsumerState<NoteEditorScreen> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _contentController = TextEditingController();
  final TextEditingController _tagController = TextEditingController();

  String _selectedCategory = NoteCategory.personal.displayName;
  List<String> _tags = [];
  bool _isPinned = false;
  bool _hasUnsavedChanges = false;
  Timer? _autoSaveTimer;
  Note? _editingNote;
  NoteType _selectedNoteType = NoteType.plainText;
  List<ChecklistItem> _checklistItems = [];
  String? _canvasData;

  @override
  void initState() {
    super.initState();
    _editingNote = ref.read(selectedNoteProvider);

    if (_editingNote != null) {
      _titleController.text = _editingNote!.title;
      _contentController.text = _editingNote!.content;
      _selectedCategory = _editingNote!.category;
      _tags = List.from(_editingNote!.tags);
      _isPinned = _editingNote!.isPinned;
      _selectedNoteType = _editingNote!.type;
      _checklistItems = List.from(_editingNote!.checklistItems);
      _canvasData = _editingNote!.canvasData;
    }

    _titleController.addListener(_onContentChanged);
    _contentController.addListener(_onContentChanged);

    _startAutoSave();
  }

  @override
  void dispose() {
    _autoSaveTimer?.cancel();
    _titleController.dispose();
    _contentController.dispose();
    _tagController.dispose();
    super.dispose();
  }

  void _onContentChanged() {
    setState(() {
      _hasUnsavedChanges = true;
    });
  }

  void _startAutoSave() {
    _autoSaveTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_hasUnsavedChanges && _titleController.text.isNotEmpty) {
        _saveNote(showSnackbar: false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(_editingNote != null ? 'Edit Note' : 'New Note'),
        backgroundColor: AppTheme.memoSuiteColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => _handleBackPress(),
        ),
        actions: [
          IconButton(
            icon: Icon(_isPinned ? Icons.push_pin : Icons.push_pin_outlined),
            onPressed: () {
              setState(() {
                _isPinned = !_isPinned;
                _hasUnsavedChanges = true;
              });
            },
          ),
          IconButton(icon: const Icon(Icons.save), onPressed: _saveNote),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export_pdf',
                child: Text('Export as PDF'),
              ),
              const PopupMenuItem(
                value: 'export_txt',
                child: Text('Export as TXT'),
              ),
              const PopupMenuItem(
                value: 'export_md',
                child: Text('Export as Markdown'),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFormattingToolbar(),
          _buildNoteTypeToggle(),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildTitleField(),
                  const SizedBox(height: 16),
                  _buildCategoryAndTagsSection(),
                  const SizedBox(height: 16),
                  _buildContentBasedOnType(),
                ],
              ),
            ),
          ),
          _buildBottomBar(),
        ],
      ),
    );
  }

  Widget _buildFormattingToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Colors.grey, width: 0.2)),
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.format_bold),
            onPressed: () => _insertFormatting('**', '**'),
          ),
          IconButton(
            icon: const Icon(Icons.format_italic),
            onPressed: () => _insertFormatting('*', '*'),
          ),
          IconButton(
            icon: const Icon(Icons.format_underlined),
            onPressed: () => _insertFormatting('<u>', '</u>'),
          ),
          IconButton(
            icon: const Icon(Icons.format_list_bulleted),
            onPressed: () => _insertFormatting('\n• ', ''),
          ),
          IconButton(
            icon: const Icon(Icons.format_list_numbered),
            onPressed: () => _insertFormatting('\n1. ', ''),
          ),
          IconButton(
            icon: const Icon(Icons.link),
            onPressed: () => _insertFormatting('[', '](url)'),
          ),
        ],
      ),
    );
  }

  Widget _buildTitleField() {
    return TextField(
      controller: _titleController,
      style: Theme.of(
        context,
      ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
      decoration: const InputDecoration(
        hintText: 'Note title...',
        border: InputBorder.none,
        contentPadding: EdgeInsets.zero,
      ),
      maxLines: 2,
    );
  }

  Widget _buildCategoryAndTagsSection() {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: DropdownButtonFormField<String>(
            value: _selectedCategory,
            decoration: const InputDecoration(
              labelText: 'Category',
              border: OutlineInputBorder(),
            ),
            items: NoteCategory.allCategories.map((category) {
              return DropdownMenuItem(value: category, child: Text(category));
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedCategory = value;
                  _hasUnsavedChanges = true;
                });
              }
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 3,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextField(
                controller: _tagController,
                decoration: InputDecoration(
                  labelText: 'Add tags',
                  border: const OutlineInputBorder(),
                  suffixIcon: IconButton(
                    icon: const Icon(Icons.add),
                    onPressed: _addTag,
                  ),
                ),
                onSubmitted: (_) => _addTag(),
              ),
              if (_tags.isNotEmpty) ...[
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  children: _tags
                      .map(
                        (tag) => Chip(
                          label: Text(tag),
                          deleteIcon: const Icon(Icons.close, size: 16),
                          onDeleted: () => _removeTag(tag),
                        ),
                      )
                      .toList(),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildContentField() {
    return Expanded(
      child: TextField(
        controller: _contentController,
        style: Theme.of(context).textTheme.bodyLarge,
        decoration: const InputDecoration(
          hintText: 'Start writing your note...',
          border: InputBorder.none,
          contentPadding: EdgeInsets.zero,
        ),
        maxLines: null,
        expands: true,
        textAlignVertical: TextAlignVertical.top,
      ),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey, width: 0.2)),
      ),
      child: Row(
        children: [
          Text(
            'Characters: ${_contentController.text.length}',
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
          ),
          const Spacer(),
          if (_hasUnsavedChanges)
            Text(
              'Unsaved changes',
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.orange[600]),
            ),
          const SizedBox(width: 16),
          ElevatedButton(
            onPressed: () => _handleBackPress(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey[300],
              foregroundColor: Colors.black87,
            ),
            child: const Text('Cancel'),
          ),
          const SizedBox(width: 8),
          ElevatedButton(
            onPressed: _saveNote,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.memoSuiteColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _insertFormatting(String before, String after) {
    final text = _contentController.text;
    final selection = _contentController.selection;

    if (selection.isValid) {
      final selectedText = text.substring(selection.start, selection.end);
      final newText = text.replaceRange(
        selection.start,
        selection.end,
        '$before$selectedText$after',
      );

      _contentController.value = TextEditingValue(
        text: newText,
        selection: TextSelection.collapsed(
          offset:
              selection.start +
              before.length +
              selectedText.length +
              after.length,
        ),
      );
    } else {
      final newText = text + before + after;
      _contentController.value = TextEditingValue(
        text: newText,
        selection: TextSelection.collapsed(
          offset: newText.length - after.length,
        ),
      );
    }
  }

  void _addTag() {
    final tag = _tagController.text.trim();
    if (tag.isNotEmpty && !_tags.contains(tag)) {
      setState(() {
        _tags.add(tag);
        _tagController.clear();
        _hasUnsavedChanges = true;
      });
    }
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
      _hasUnsavedChanges = true;
    });
  }

  void _saveNote({bool showSnackbar = true}) {
    if (_titleController.text.trim().isEmpty) {
      if (showSnackbar) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Please enter a title')));
      }
      return;
    }

    final note = Note(
      id: _editingNote?.id,
      title: _titleController.text.trim(),
      content: _contentController.text.trim(),
      category: _selectedCategory,
      tags: _tags,
      isPinned: _isPinned,
      createdAt: _editingNote?.createdAt,
      type: _selectedNoteType,
      checklistItems: _checklistItems,
      canvasData: _canvasData,
    );

    if (_editingNote != null) {
      ref.read(notesProvider.notifier).updateNote(note);
    } else {
      ref.read(notesProvider.notifier).addNote(note);
    }

    setState(() {
      _hasUnsavedChanges = false;
    });

    if (showSnackbar) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_editingNote != null ? 'Note updated' : 'Note saved'),
        ),
      );
    }
  }

  void _handleBackPress() {
    if (_hasUnsavedChanges) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Unsaved Changes'),
          content: const Text(
            'You have unsaved changes. Do you want to save before leaving?',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                ref.read(memoSuiteCurrentScreenProvider.notifier).state =
                    MemoSuiteScreen.notesList;
              },
              child: const Text('Discard'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _saveNote();
                ref.read(memoSuiteCurrentScreenProvider.notifier).state =
                    MemoSuiteScreen.notesList;
              },
              child: const Text('Save'),
            ),
          ],
        ),
      );
    } else {
      ref.read(memoSuiteCurrentScreenProvider.notifier).state =
          MemoSuiteScreen.notesList;
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export_pdf':
        _exportNote('PDF');
        break;
      case 'export_txt':
        _exportNote('TXT');
        break;
      case 'export_md':
        _exportNote('Markdown');
        break;
    }
  }

  void _exportNote(String format) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Export as $format - Feature coming soon!')),
    );
  }

  Widget _buildNoteTypeToggle() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: const Border(
          bottom: BorderSide(color: Colors.grey, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          const Text('Type: ', style: TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(width: 8),
          Expanded(
            child: SegmentedButton<NoteType>(
              segments: NoteType.values.map((type) {
                return ButtonSegment<NoteType>(
                  value: type,
                  label: Text(type.displayName),
                  icon: Icon(_getNoteTypeIcon(type)),
                );
              }).toList(),
              selected: {_selectedNoteType},
              onSelectionChanged: (Set<NoteType> selection) {
                setState(() {
                  _selectedNoteType = selection.first;
                  _hasUnsavedChanges = true;
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  IconData _getNoteTypeIcon(NoteType type) {
    switch (type) {
      case NoteType.plainText:
        return Icons.text_fields;
      case NoteType.checklist:
        return Icons.checklist;
      case NoteType.canvas:
        return Icons.brush;
    }
  }

  Widget _buildContentBasedOnType() {
    switch (_selectedNoteType) {
      case NoteType.plainText:
        return _buildContentField();
      case NoteType.checklist:
        return ChecklistEditor(
          items: _checklistItems,
          onItemsChanged: (items) {
            setState(() {
              _checklistItems = items;
              _hasUnsavedChanges = true;
            });
          },
        );
      case NoteType.canvas:
        return CanvasDrawingEditor(
          initialData: _canvasData,
          onDataChanged: (data) {
            setState(() {
              _canvasData = data;
              _hasUnsavedChanges = true;
            });
          },
        );
    }
  }
}
