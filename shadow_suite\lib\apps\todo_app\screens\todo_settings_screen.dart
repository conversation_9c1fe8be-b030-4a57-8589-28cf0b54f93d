import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class TodoSettingsScreen extends ConsumerWidget {
  const TodoSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSettingsSection(
            context,
            'Notifications',
            [
              _buildSwitchTile(
                context,
                'Task Reminders',
                'Get notified about upcoming tasks',
                Icons.notifications,
                true,
                (value) {},
              ),
              _buildSwitchTile(
                context,
                'Daily Summary',
                'Daily overview of your tasks',
                Icons.today,
                true,
                (value) {},
              ),
              _buildSwitchTile(
                context,
                'Overdue Alerts',
                'Alerts for overdue tasks',
                Icons.warning,
                false,
                (value) {},
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSettingsSection(
            context,
            'Preferences',
            [
              _buildSettingsTile(
                context,
                'Default Priority',
                'Medium',
                Icons.priority_high,
                () {},
              ),
              _buildSettingsTile(
                context,
                'Start of Week',
                'Monday',
                Icons.calendar_today,
                () {},
              ),
              _buildSettingsTile(
                context,
                'Theme',
                'System Default',
                Icons.palette,
                () {},
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSettingsSection(
            context,
            'Data',
            [
              _buildSettingsTile(
                context,
                'Export Tasks',
                'Export your tasks to file',
                Icons.download,
                () {},
              ),
              _buildSettingsTile(
                context,
                'Import Tasks',
                'Import tasks from file',
                Icons.upload,
                () {},
              ),
              _buildSettingsTile(
                context,
                'Backup',
                'Backup your data to cloud',
                Icons.cloud_upload,
                () {},
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(
    BuildContext context,
    String title,
    List<Widget> children,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.red,
          ),
        ),
        const SizedBox(height: 8),
        Card(
          elevation: 2,
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildSettingsTile(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: Colors.red),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  Widget _buildSwitchTile(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return SwitchListTile(
      secondary: Icon(icon, color: Colors.red),
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      activeColor: Colors.red,
    );
  }
}
