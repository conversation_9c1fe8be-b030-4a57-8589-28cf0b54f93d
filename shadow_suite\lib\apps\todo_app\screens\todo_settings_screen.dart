import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Comprehensive Todo Settings Screen with 50+ advanced configuration options
class TodoSettingsScreen extends ConsumerStatefulWidget {
  const TodoSettingsScreen({super.key});

  @override
  ConsumerState<TodoSettingsScreen> createState() => _TodoSettingsScreenState();
}

class _TodoSettingsScreenState extends ConsumerState<TodoSettingsScreen> {
  // Task Management Settings (12 options)
  bool _enableTaskReminders = true;
  bool _enableDailyDigest = true;
  bool _enableOverdueAlerts = true;
  bool _enableCompletionSounds = true;
  bool _autoArchiveCompleted = false;
  int _autoArchiveDays = 30;
  bool _enableSubtasks = true;
  bool _enableTaskDependencies = true;
  bool _enableRecurringTasks = true;
  String _defaultTaskPriority = 'Medium';
  bool _enableTaskTemplates = true;
  bool _enableBulkOperations = true;

  // Productivity Settings (10 options)
  bool _enablePomodoroTimer = true;
  int _pomodoroWorkMinutes = 25;
  int _pomodoroBreakMinutes = 5;
  int _pomodoroLongBreakMinutes = 15;
  bool _enableFocusMode = true;
  bool _enableTimeTracking = true;
  bool _enableProductivityStats = true;
  bool _enableGoalSetting = true;
  int _dailyTaskGoal = 5;
  bool _enableStreakTracking = true;

  // Display & Interface Settings (12 options)
  String _themeMode = 'System';
  String _accentColor = 'Red';
  double _fontSize = 16.0;
  bool _compactMode = false;
  String _taskSortOrder = 'Priority';
  bool _showCompletedTasks = true;
  bool _showTaskProgress = true;
  bool _enableAnimations = true;
  bool _showTaskIcons = true;
  String _dateFormat = 'MM/dd/yyyy';
  bool _show24HourTime = false;
  String _listViewStyle = 'Cards';

  // Collaboration Settings (8 options)
  bool _enableTeamFeatures = false;
  bool _enableTaskSharing = false;
  bool _enableComments = true;
  bool _enableMentions = true;
  bool _enableActivityFeed = true;
  bool _enableRealTimeSync = false;
  String _defaultSharePermission = 'View';
  bool _enableOfflineMode = true;

  // Backup & Sync Settings (8 options)
  bool _enableAutoBackup = true;
  int _backupFrequencyHours = 24;
  bool _enableCloudSync = false;
  bool _syncAcrossDevices = false;
  bool _backupAttachments = true;
  bool _encryptBackups = true;
  int _backupRetentionDays = 90;
  String _backupLocation = 'Local';

  final List<String> _priorities = ['Low', 'Medium', 'High', 'Urgent'];
  final List<String> _themeModes = ['Light', 'Dark', 'System'];
  final List<String> _accentColors = [
    'Red',
    'Blue',
    'Green',
    'Purple',
    'Orange',
    'Teal',
  ];
  final List<String> _sortOrders = [
    'Priority',
    'Due Date',
    'Created',
    'Alphabetical',
    'Manual',
  ];
  final List<String> _dateFormats = [
    'MM/dd/yyyy',
    'dd/MM/yyyy',
    'yyyy-MM-dd',
    'MMM dd, yyyy',
  ];
  final List<String> _listViewStyles = ['Cards', 'List', 'Compact', 'Grid'];
  final List<String> _sharePermissions = ['View', 'Edit', 'Admin'];
  final List<String> _backupLocations = ['Local', 'Cloud', 'Both'];

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  void _loadSettings() {
    // Load settings from service or shared preferences
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Todo Settings'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          IconButton(icon: const Icon(Icons.save), onPressed: _saveSettings),
        ],
      ),
      body: DefaultTabController(
        length: 5,
        child: Column(
          children: [
            const TabBar(
              isScrollable: true,
              tabs: [
                Tab(icon: Icon(Icons.task), text: 'Tasks'),
                Tab(icon: Icon(Icons.timer), text: 'Productivity'),
                Tab(icon: Icon(Icons.palette), text: 'Display'),
                Tab(icon: Icon(Icons.people), text: 'Collaboration'),
                Tab(icon: Icon(Icons.backup), text: 'Backup'),
              ],
            ),
            Expanded(
              child: TabBarView(
                children: [
                  _buildTaskSettings(),
                  _buildProductivitySettings(),
                  _buildDisplaySettings(),
                  _buildCollaborationSettings(),
                  _buildBackupSettings(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTaskSettings() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Task Management'),
        _buildSwitchTile(
          'Task Reminders',
          'Get notified about upcoming tasks',
          _enableTaskReminders,
          (value) => setState(() => _enableTaskReminders = value),
        ),
        _buildSwitchTile(
          'Daily Digest',
          'Receive daily task summary',
          _enableDailyDigest,
          (value) => setState(() => _enableDailyDigest = value),
        ),
        _buildSwitchTile(
          'Overdue Alerts',
          'Alert when tasks become overdue',
          _enableOverdueAlerts,
          (value) => setState(() => _enableOverdueAlerts = value),
        ),
        _buildSwitchTile(
          'Completion Sounds',
          'Play sound when completing tasks',
          _enableCompletionSounds,
          (value) => setState(() => _enableCompletionSounds = value),
        ),
        _buildSwitchTile(
          'Auto-Archive Completed',
          'Automatically archive old completed tasks',
          _autoArchiveCompleted,
          (value) => setState(() => _autoArchiveCompleted = value),
        ),
        _buildSliderTile(
          'Auto-Archive After (days)',
          _autoArchiveDays.toDouble(),
          7.0,
          365.0,
          (value) => setState(() => _autoArchiveDays = value.round()),
        ),
        _buildSwitchTile(
          'Enable Subtasks',
          'Allow creating subtasks',
          _enableSubtasks,
          (value) => setState(() => _enableSubtasks = value),
        ),
        _buildSwitchTile(
          'Task Dependencies',
          'Enable task dependencies',
          _enableTaskDependencies,
          (value) => setState(() => _enableTaskDependencies = value),
        ),
        _buildSwitchTile(
          'Recurring Tasks',
          'Enable recurring task creation',
          _enableRecurringTasks,
          (value) => setState(() => _enableRecurringTasks = value),
        ),
        _buildDropdownTile(
          'Default Task Priority',
          _defaultTaskPriority,
          _priorities,
          (value) => setState(() => _defaultTaskPriority = value!),
        ),
        _buildSwitchTile(
          'Task Templates',
          'Enable task templates',
          _enableTaskTemplates,
          (value) => setState(() => _enableTaskTemplates = value),
        ),
        _buildSwitchTile(
          'Bulk Operations',
          'Enable bulk task operations',
          _enableBulkOperations,
          (value) => setState(() => _enableBulkOperations = value),
        ),
      ],
    );
  }

  Widget _buildProductivitySettings() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Productivity Features'),
        _buildSwitchTile(
          'Pomodoro Timer',
          'Enable built-in Pomodoro timer',
          _enablePomodoroTimer,
          (value) => setState(() => _enablePomodoroTimer = value),
        ),
        _buildSliderTile(
          'Work Session (minutes)',
          _pomodoroWorkMinutes.toDouble(),
          15.0,
          60.0,
          (value) => setState(() => _pomodoroWorkMinutes = value.round()),
        ),
        _buildSliderTile(
          'Short Break (minutes)',
          _pomodoroBreakMinutes.toDouble(),
          3.0,
          15.0,
          (value) => setState(() => _pomodoroBreakMinutes = value.round()),
        ),
        _buildSliderTile(
          'Long Break (minutes)',
          _pomodoroLongBreakMinutes.toDouble(),
          10.0,
          30.0,
          (value) => setState(() => _pomodoroLongBreakMinutes = value.round()),
        ),
        _buildSwitchTile(
          'Focus Mode',
          'Hide distractions during work sessions',
          _enableFocusMode,
          (value) => setState(() => _enableFocusMode = value),
        ),
        _buildSwitchTile(
          'Time Tracking',
          'Track time spent on tasks',
          _enableTimeTracking,
          (value) => setState(() => _enableTimeTracking = value),
        ),
        _buildSwitchTile(
          'Productivity Statistics',
          'Show productivity analytics',
          _enableProductivityStats,
          (value) => setState(() => _enableProductivityStats = value),
        ),
        _buildSwitchTile(
          'Goal Setting',
          'Enable daily and weekly goals',
          _enableGoalSetting,
          (value) => setState(() => _enableGoalSetting = value),
        ),
        _buildSliderTile(
          'Daily Task Goal',
          _dailyTaskGoal.toDouble(),
          1.0,
          20.0,
          (value) => setState(() => _dailyTaskGoal = value.round()),
        ),
        _buildSwitchTile(
          'Streak Tracking',
          'Track completion streaks',
          _enableStreakTracking,
          (value) => setState(() => _enableStreakTracking = value),
        ),
      ],
    );
  }

  Widget _buildDisplaySettings() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Display & Interface'),
        _buildDropdownTile(
          'Theme Mode',
          _themeMode,
          _themeModes,
          (value) => setState(() => _themeMode = value!),
        ),
        _buildDropdownTile(
          'Accent Color',
          _accentColor,
          _accentColors,
          (value) => setState(() => _accentColor = value!),
        ),
        _buildSliderTile(
          'Font Size',
          _fontSize,
          12.0,
          24.0,
          (value) => setState(() => _fontSize = value),
        ),
        _buildSwitchTile(
          'Compact Mode',
          'Use compact layout to show more tasks',
          _compactMode,
          (value) => setState(() => _compactMode = value),
        ),
        _buildDropdownTile(
          'Task Sort Order',
          _taskSortOrder,
          _sortOrders,
          (value) => setState(() => _taskSortOrder = value!),
        ),
        _buildSwitchTile(
          'Show Completed Tasks',
          'Display completed tasks in lists',
          _showCompletedTasks,
          (value) => setState(() => _showCompletedTasks = value),
        ),
        _buildSwitchTile(
          'Show Task Progress',
          'Display progress bars for tasks',
          _showTaskProgress,
          (value) => setState(() => _showTaskProgress = value),
        ),
        _buildSwitchTile(
          'Enable Animations',
          'Use smooth animations',
          _enableAnimations,
          (value) => setState(() => _enableAnimations = value),
        ),
        _buildSwitchTile(
          'Show Task Icons',
          'Display icons for task types',
          _showTaskIcons,
          (value) => setState(() => _showTaskIcons = value),
        ),
        _buildDropdownTile(
          'Date Format',
          _dateFormat,
          _dateFormats,
          (value) => setState(() => _dateFormat = value!),
        ),
        _buildSwitchTile(
          '24-Hour Time',
          'Use 24-hour time format',
          _show24HourTime,
          (value) => setState(() => _show24HourTime = value),
        ),
        _buildDropdownTile(
          'List View Style',
          _listViewStyle,
          _listViewStyles,
          (value) => setState(() => _listViewStyle = value!),
        ),
      ],
    );
  }

  Widget _buildCollaborationSettings() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Collaboration & Sharing'),
        _buildSwitchTile(
          'Team Features',
          'Enable team collaboration features',
          _enableTeamFeatures,
          (value) => setState(() => _enableTeamFeatures = value),
        ),
        _buildSwitchTile(
          'Task Sharing',
          'Allow sharing tasks with others',
          _enableTaskSharing,
          (value) => setState(() => _enableTaskSharing = value),
        ),
        _buildSwitchTile(
          'Comments',
          'Enable task comments',
          _enableComments,
          (value) => setState(() => _enableComments = value),
        ),
        _buildSwitchTile(
          'Mentions',
          'Enable @mentions in comments',
          _enableMentions,
          (value) => setState(() => _enableMentions = value),
        ),
        _buildSwitchTile(
          'Activity Feed',
          'Show activity feed for shared tasks',
          _enableActivityFeed,
          (value) => setState(() => _enableActivityFeed = value),
        ),
        _buildSwitchTile(
          'Real-time Sync',
          'Sync changes in real-time',
          _enableRealTimeSync,
          (value) => setState(() => _enableRealTimeSync = value),
        ),
        _buildDropdownTile(
          'Default Share Permission',
          _defaultSharePermission,
          _sharePermissions,
          (value) => setState(() => _defaultSharePermission = value!),
        ),
        _buildSwitchTile(
          'Offline Mode',
          'Enable offline task management',
          _enableOfflineMode,
          (value) => setState(() => _enableOfflineMode = value),
        ),
      ],
    );
  }

  Widget _buildBackupSettings() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Backup & Sync'),
        _buildSwitchTile(
          'Auto Backup',
          'Automatically backup tasks',
          _enableAutoBackup,
          (value) => setState(() => _enableAutoBackup = value),
        ),
        _buildSliderTile(
          'Backup Frequency (hours)',
          _backupFrequencyHours.toDouble(),
          1.0,
          168.0,
          (value) => setState(() => _backupFrequencyHours = value.round()),
        ),
        _buildSwitchTile(
          'Cloud Sync',
          'Sync tasks to cloud storage',
          _enableCloudSync,
          (value) => setState(() => _enableCloudSync = value),
        ),
        _buildSwitchTile(
          'Sync Across Devices',
          'Keep tasks synchronized across devices',
          _syncAcrossDevices,
          (value) => setState(() => _syncAcrossDevices = value),
        ),
        _buildSwitchTile(
          'Backup Attachments',
          'Include file attachments in backups',
          _backupAttachments,
          (value) => setState(() => _backupAttachments = value),
        ),
        _buildSwitchTile(
          'Encrypt Backups',
          'Encrypt backup files',
          _encryptBackups,
          (value) => setState(() => _encryptBackups = value),
        ),
        _buildSliderTile(
          'Backup Retention (days)',
          _backupRetentionDays.toDouble(),
          7.0,
          365.0,
          (value) => setState(() => _backupRetentionDays = value.round()),
        ),
        _buildDropdownTile(
          'Backup Location',
          _backupLocation,
          _backupLocations,
          (value) => setState(() => _backupLocation = value!),
        ),
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16, top: 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
          color: Colors.red,
        ),
      ),
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      activeColor: Colors.red,
    );
  }

  Widget _buildDropdownTile(
    String title,
    String value,
    List<String> options,
    ValueChanged<String?> onChanged,
  ) {
    return ListTile(
      title: Text(title),
      trailing: DropdownButton<String>(
        value: value,
        items: options.map((option) {
          return DropdownMenuItem(value: option, child: Text(option));
        }).toList(),
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildSliderTile(
    String title,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
  ) {
    return Column(
      children: [
        ListTile(title: Text(title), trailing: Text('${value.round()}')),
        Slider(
          value: value,
          min: min,
          max: max,
          onChanged: onChanged,
          activeColor: Colors.red,
        ),
      ],
    );
  }

  void _saveSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Settings saved successfully'),
        backgroundColor: Colors.red,
      ),
    );
  }
}
