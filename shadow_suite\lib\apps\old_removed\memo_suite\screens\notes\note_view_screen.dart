import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';
import '../../services/memo_providers.dart';
import '../../models/note.dart';

class NoteViewScreen extends ConsumerWidget {
  const NoteViewScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedNote = ref.watch(selectedNoteProvider);

    if (selectedNote == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Note'),
          backgroundColor: AppTheme.memoSuiteColor,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              ref.read(memoSuiteCurrentScreenProvider.notifier).state =
                  MemoSuiteScreen.notesList;
            },
          ),
        ),
        body: const Center(child: Text('No note selected')),
      );
    }

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Note'),
        backgroundColor: AppTheme.memoSuiteColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            ref.read(memoSuiteCurrentScreenProvider.notifier).state =
                MemoSuiteScreen.notesList;
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              ref.read(memoSuiteCurrentScreenProvider.notifier).state =
                  MemoSuiteScreen.noteEditor;
            },
          ),
          IconButton(
            icon: Icon(
              selectedNote.isPinned ? Icons.push_pin : Icons.push_pin_outlined,
            ),
            onPressed: () {
              final updatedNote = selectedNote.copyWith(
                isPinned: !selectedNote.isPinned,
              );
              ref.read(notesProvider.notifier).updateNote(updatedNote);
              ref.read(selectedNoteProvider.notifier).state = updatedNote;
            },
          ),
          PopupMenuButton<String>(
            onSelected: (value) =>
                _handleMenuAction(context, ref, selectedNote, value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'copy',
                child: Text('Copy to Clipboard'),
              ),
              const PopupMenuItem(value: 'share', child: Text('Share')),
              const PopupMenuItem(
                value: 'export_pdf',
                child: Text('Export as PDF'),
              ),
              const PopupMenuItem(
                value: 'export_txt',
                child: Text('Export as TXT'),
              ),
              const PopupMenuItem(
                value: 'export_md',
                child: Text('Export as Markdown'),
              ),
              const PopupMenuItem(value: 'delete', child: Text('Delete')),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildNoteHeader(context, selectedNote),
            const SizedBox(height: 24),
            _buildNoteContent(context, selectedNote),
            const SizedBox(height: 32),
            _buildNoteMetadata(context, selectedNote),
          ],
        ),
      ),
    );
  }

  Widget _buildNoteHeader(BuildContext context, Note note) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            if (note.isPinned) ...[
              Icon(Icons.push_pin, color: AppTheme.memoSuiteColor, size: 20),
              const SizedBox(width: 8),
            ],
            Expanded(
              child: Text(
                note.title,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppTheme.memoSuiteColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppTheme.memoSuiteColor.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.folder, size: 16, color: AppTheme.memoSuiteColor),
                  const SizedBox(width: 4),
                  Text(
                    note.category,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.memoSuiteColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            Text(
              _formatDate(note.updatedAt),
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
        if (note.tags.isNotEmpty) ...[
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: note.tags
                .map(
                  (tag) => Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.tag, size: 12, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                        Text(
                          tag,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: Colors.grey[700]),
                        ),
                      ],
                    ),
                  ),
                )
                .toList(),
          ),
        ],
      ],
    );
  }

  Widget _buildNoteContent(BuildContext context, Note note) {
    return Card(
      elevation: 0,
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey[200]!),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.article, color: AppTheme.memoSuiteColor, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Content',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.memoSuiteColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SelectableText(
              note.content.isEmpty ? 'No content' : note.content,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                height: 1.6,
                color: note.content.isEmpty ? Colors.grey[500] : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoteMetadata(BuildContext context, Note note) {
    return Card(
      elevation: 0,
      color: Colors.grey[50],
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey[200]!),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.grey[600], size: 20),
                const SizedBox(width: 8),
                Text(
                  'Note Information',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildMetadataRow(
              context,
              'Created',
              _formatFullDate(note.createdAt),
            ),
            const SizedBox(height: 8),
            _buildMetadataRow(
              context,
              'Last Modified',
              _formatFullDate(note.updatedAt),
            ),
            const SizedBox(height: 8),
            _buildMetadataRow(
              context,
              'Characters',
              note.content.length.toString(),
            ),
            const SizedBox(height: 8),
            _buildMetadataRow(
              context,
              'Words',
              _countWords(note.content).toString(),
            ),
            const SizedBox(height: 8),
            _buildMetadataRow(context, 'Tags', note.tags.length.toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildMetadataRow(BuildContext context, String label, String value) {
    return Row(
      children: [
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: Colors.grey[800]),
        ),
      ],
    );
  }

  void _handleMenuAction(
    BuildContext context,
    WidgetRef ref,
    Note note,
    String action,
  ) {
    switch (action) {
      case 'copy':
        Clipboard.setData(
          ClipboardData(text: '${note.title}\n\n${note.content}'),
        );
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Note copied to clipboard')),
        );
        break;
      case 'share':
        _shareNote(context, note);
        break;
      case 'export_pdf':
      case 'export_txt':
      case 'export_md':
        _exportNote(context, note, action);
        break;
      case 'delete':
        _showDeleteDialog(context, ref, note);
        break;
    }
  }

  void _shareNote(BuildContext context, Note note) {
    final shareText =
        '''
📝 ${note.title}

${note.content}

Created: ${_formatFullDate(note.createdAt)}
${note.updatedAt != note.createdAt ? 'Updated: ${_formatFullDate(note.updatedAt)}' : ''}

Shared from Shadow Suite Memo Suite
''';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Share Note'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Share this note with others:'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                shareText,
                style: const TextStyle(fontSize: 12),
                maxLines: 15,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Clipboard.setData(ClipboardData(text: shareText));
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Note copied to clipboard for sharing'),
                ),
              );
            },
            child: const Text('Copy to Share'),
          ),
        ],
      ),
    );
  }

  void _exportNote(BuildContext context, Note note, String format) {
    final formatName = format.split('_').last.toUpperCase();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Export as $formatName - Feature coming soon!')),
    );
  }

  void _showDeleteDialog(BuildContext context, WidgetRef ref, Note note) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Note'),
        content: Text('Are you sure you want to delete "${note.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              ref.read(notesProvider.notifier).deleteNote(note.id);
              Navigator.of(context).pop();
              ref.read(memoSuiteCurrentScreenProvider.notifier).state =
                  MemoSuiteScreen.notesList;
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String _formatFullDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} at ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  int _countWords(String text) {
    if (text.trim().isEmpty) return 0;
    return text.trim().split(RegExp(r'\s+')).length;
  }
}
