import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import '../models/excel_app_tool.dart';
import '../services/excel_formula_engine.dart';
import '../services/real_time_binding_service.dart';
import '../services/excel_app_providers.dart';

class ToolRuntimeScreen extends ConsumerStatefulWidget {
  final ExcelAppTool tool;
  final bool isPopup;

  const ToolRuntimeScreen({
    super.key,
    required this.tool,
    this.isPopup = false,
  });

  @override
  ConsumerState<ToolRuntimeScreen> createState() => _ToolRuntimeScreenState();
}

class _ToolRuntimeScreenState extends ConsumerState<ToolRuntimeScreen> {
  final ExcelFormulaEngine _formulaEngine = ExcelFormulaEngine();
  late RealTimeBindingService _bindingService;
  final Map<String, TextEditingController> _controllers = {};
  final Map<String, dynamic> _componentValues = {};
  final Map<String, StreamSubscription> _cellSubscriptions = {};
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    // Initialize binding service immediately
    _bindingService = RealTimeBindingService();
    _bindingService.initialize();

    // Initialize component values and set up real-time listeners
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeComponentValues();
      _setupRealTimeListeners();
      setState(() {
        _isInitialized = true;
      });
    });
  }

  @override
  void dispose() {
    // Cancel all cell subscriptions
    for (final subscription in _cellSubscriptions.values) {
      subscription.cancel();
    }
    _cellSubscriptions.clear();

    // Dispose text controllers
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    _controllers.clear();

    super.dispose();
  }

  void _initializeComponentValues() {
    for (final component in widget.tool.uiComponents) {
      if (component.boundCell != null) {
        final cellValue = _bindingService.getCellDisplayValue(
          component.boundCell!,
          widget.tool,
        );
        _componentValues[component.id] = cellValue;

        if (component.type == ComponentType.textInput ||
            component.type == ComponentType.numberInput) {
          _controllers[component.id] = TextEditingController(
            text: cellValue?.toString() ?? '',
          );
        }
      }
    }
  }

  void _setupRealTimeListeners() {
    // Set up real-time listeners for cell changes
    for (final component in widget.tool.uiComponents) {
      if (component.boundCell != null) {
        // Listen to cell value changes and update UI immediately
        final subscription = _bindingService
            .getCellStream(component.boundCell!)
            .listen((newValue) {
              if (mounted) {
                setState(() {
                  _componentValues[component.id] = newValue;

                  // Update text controllers for input components
                  if (component.type == ComponentType.textInput ||
                      component.type == ComponentType.numberInput) {
                    final controller = _controllers[component.id];
                    if (controller != null &&
                        controller.text != newValue?.toString()) {
                      controller.text = newValue?.toString() ?? '';
                    }
                  }
                });
              }
            });

        _cellSubscriptions[component.id] = subscription;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Show loading indicator while initializing
    if (!_isInitialized) {
      if (widget.isPopup) {
        return Dialog(
          child: SizedBox(
            width: 800,
            height: 600,
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Color(0xFF3498DB),
                    ),
                  ),
                  SizedBox(height: 16),
                  Text('Initializing real-time data binding...'),
                ],
              ),
            ),
          ),
        );
      }

      return Scaffold(
        appBar: AppBar(
          title: Text(widget.tool.name),
          backgroundColor: const Color(0xFF3498DB),
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF3498DB)),
              ),
              SizedBox(height: 16),
              Text('Initializing real-time data binding...'),
            ],
          ),
        ),
      );
    }

    if (widget.isPopup) {
      return Dialog(
        child: SizedBox(width: 800, height: 600, child: _buildRuntimeContent()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.tool.name),
        backgroundColor: const Color(0xFF3498DB),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _refreshTool,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh & Recalculate',
          ),
          IconButton(
            onPressed: _saveTool,
            icon: const Icon(Icons.save),
            tooltip: 'Save',
          ),
          IconButton(
            onPressed: _recalculateAllComponents,
            icon: const Icon(Icons.calculate),
            tooltip: 'Manual Recalculate',
          ),
        ],
      ),
      body: _buildRuntimeContent(),
    );
  }

  Widget _buildRuntimeContent() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.blue.shade50, Colors.white],
        ),
      ),
      child: Column(
        children: [
          if (!widget.isPopup) _buildToolHeader(),
          Expanded(child: _buildToolInterface()),
          _buildToolFooter(),
        ],
      ),
    );
  }

  Widget _buildToolHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Color(0xFFE9ECEF))),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF3498DB).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.play_arrow,
              color: Color(0xFF3498DB),
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.tool.name,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                if (widget.tool.description.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    widget.tool.description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF7F8C8D),
                    ),
                  ),
                ],
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: const Color(0xFF27AE60).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.circle, color: Color(0xFF27AE60), size: 8),
                SizedBox(width: 6),
                Text(
                  'Running',
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0xFF27AE60),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToolInterface() {
    if (widget.tool.uiComponents.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.widgets, size: 64, color: Color(0xFFBDC3C7)),
            SizedBox(height: 16),
            Text(
              'No UI Components',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF7F8C8D),
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Add UI components in the tool editor to create an interactive interface',
              style: TextStyle(color: Color(0xFF95A5A6)),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Stack(
        children: widget.tool.uiComponents
            .map((component) => _buildRuntimeComponent(component))
            .toList(),
      ),
    );
  }

  Widget _buildRuntimeComponent(UIComponent component) {
    return Positioned(
      left: component.x,
      top: component.y,
      child: SizedBox(
        width: component.width,
        height: component.height,
        child: _buildComponentWidget(component),
      ),
    );
  }

  Widget _buildComponentWidget(UIComponent component) {
    switch (component.type) {
      case ComponentType.textInput:
        return _buildTextField(component);
      case ComponentType.numberInput:
        return _buildNumberField(component);
      case ComponentType.label:
        return _buildLabel(component);
      case ComponentType.dropdown:
        return _buildDropdown(component);
      case ComponentType.calculatedDisplay:
        return _buildCalculatedDisplay(component);
      case ComponentType.chart:
        return _buildChart(component);
      case ComponentType.container:
        return _buildContainer(component);
      case ComponentType.spacer:
        return _buildSpacer(component);
      default:
        // Handle new component types with basic implementations
        return _buildUnsupportedComponent(component);
    }
  }

  Widget _buildTextField(UIComponent component) {
    final controller = _controllers[component.id];
    if (controller == null) return const SizedBox();

    return TextField(
      controller: controller,
      decoration: InputDecoration(
        labelText: component.properties['label'] ?? 'Text Field',
        border: const OutlineInputBorder(),
        filled: true,
        fillColor: Colors.white,
        suffixIcon: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (component.boundCell != null) ...[
              Tooltip(
                message: 'Bound to ${component.boundCell}',
                child: Icon(Icons.link, size: 16, color: Colors.blue.shade600),
              ),
              const SizedBox(width: 4),
            ],
            IconButton(
              icon: const Icon(Icons.refresh, size: 16),
              onPressed: () => _refreshComponentValue(component),
              tooltip: 'Refresh from cell',
            ),
          ],
        ),
      ),
      // Real-time updates on every change
      onChanged: (value) => _updateComponentValue(component, value),
      // Manual trigger on Enter key press
      onSubmitted: (value) {
        _updateComponentValue(component, value);
        _recalculateAllComponents();
      },
      // Auto-trigger on focus loss
      onEditingComplete: () {
        _updateComponentValue(component, controller.text);
        _recalculateAllComponents();
      },
    );
  }

  Widget _buildNumberField(UIComponent component) {
    final controller = _controllers[component.id];
    if (controller == null) return const SizedBox();

    return TextField(
      controller: controller,
      decoration: InputDecoration(
        labelText: component.properties['label'] ?? 'Number Field',
        border: const OutlineInputBorder(),
        filled: true,
        fillColor: Colors.white,
        suffixIcon: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (component.boundCell != null) ...[
              Tooltip(
                message: 'Bound to ${component.boundCell}',
                child: Icon(Icons.link, size: 16, color: Colors.blue.shade600),
              ),
              const SizedBox(width: 4),
            ],
            IconButton(
              icon: const Icon(Icons.refresh, size: 16),
              onPressed: () => _refreshComponentValue(component),
              tooltip: 'Refresh from cell',
            ),
          ],
        ),
      ),
      keyboardType: TextInputType.number,
      // Real-time updates on every change
      onChanged: (value) {
        final numValue = double.tryParse(value);
        if (numValue != null) {
          _updateComponentValue(component, numValue);
        }
      },
      // Manual trigger on Enter key press
      onSubmitted: (value) {
        final numValue = double.tryParse(value) ?? 0;
        _updateComponentValue(component, numValue);
        _recalculateAllComponents();
      },
      // Auto-trigger on focus loss
      onEditingComplete: () {
        final numValue = double.tryParse(controller.text) ?? 0;
        _updateComponentValue(component, numValue);
        _recalculateAllComponents();
      },
    );
  }

  void _refreshComponentValue(UIComponent component) {
    if (component.boundCell != null) {
      final cellValue = _bindingService.getCellDisplayValue(
        component.boundCell!,
        widget.tool,
      );
      setState(() {
        _componentValues[component.id] = cellValue;

        // Update controller if it's an input component
        if (component.type == ComponentType.textInput ||
            component.type == ComponentType.numberInput) {
          final controller = _controllers[component.id];
          if (controller != null) {
            controller.text = cellValue?.toString() ?? '';
          }
        }
      });
    }
  }

  Widget _buildCalculatedDisplay(UIComponent component) {
    final value = _getBoundCellValue(component);
    final displayValue = _formatDisplayValue(value);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  component.label,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF7F8C8D),
                  ),
                ),
              ),
              if (component.boundCell != null) ...[
                Tooltip(
                  message: 'Live calculation from ${component.boundCell}',
                  child: Icon(
                    Icons.auto_graph,
                    size: 16,
                    color: Colors.blue.shade600,
                  ),
                ),
                const SizedBox(width: 4),
                IconButton(
                  icon: const Icon(Icons.refresh, size: 14),
                  onPressed: () => _refreshComponentValue(component),
                  tooltip: 'Refresh calculation',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 20,
                    minHeight: 20,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
          AnimatedSwitcher(
            duration: const Duration(milliseconds: 200),
            child: Text(
              displayValue,
              key: ValueKey(displayValue),
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2C3E50),
              ),
            ),
          ),
          if (component.boundCell != null) ...[
            const SizedBox(height: 4),
            Text(
              'Cell: ${component.boundCell}',
              style: TextStyle(
                fontSize: 10,
                color: Colors.blue.shade600,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatDisplayValue(dynamic value) {
    if (value == null) return '0';
    if (value is num) {
      if (value == value.toInt()) {
        return value.toInt().toString();
      } else {
        return value.toStringAsFixed(2);
      }
    }
    return value.toString();
  }

  Widget _buildContainer(UIComponent component) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Text(
          component.label,
          style: const TextStyle(
            color: Color(0xFF7F8C8D),
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildSpacer(UIComponent component) {
    return const SizedBox();
  }

  Widget _buildLabel(UIComponent component) {
    final value = _getBoundCellValue(component);
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Text(
        value?.toString() ?? component.properties['text'] ?? 'Label',
        style: const TextStyle(fontSize: 14, color: Color(0xFF2C3E50)),
      ),
    );
  }

  Widget _buildDropdown(UIComponent component) {
    final options =
        (component.properties['options'] as List<dynamic>?)?.cast<String>() ??
        ['Option 1', 'Option 2'];
    final currentValue = _componentValues[component.id] ?? options.first;

    return DropdownButtonFormField<String>(
      value: options.contains(currentValue) ? currentValue : options.first,
      decoration: InputDecoration(
        labelText: component.properties['label'] ?? 'Dropdown',
        border: const OutlineInputBorder(),
        filled: true,
        fillColor: Colors.white,
      ),
      items: options
          .map((option) => DropdownMenuItem(value: option, child: Text(option)))
          .toList(),
      onChanged: (value) => _updateComponentValue(component, value),
    );
  }

  Widget _buildChart(UIComponent component) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.bar_chart, size: 48, color: Color(0xFF3498DB)),
            SizedBox(height: 8),
            Text('Chart Component', style: TextStyle(color: Color(0xFF7F8C8D))),
          ],
        ),
      ),
    );
  }

  Widget _buildToolFooter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Color(0xFFF8F9FA),
        border: Border(top: BorderSide(color: Color(0xFFE9ECEF))),
      ),
      child: Row(
        children: [
          const Icon(Icons.info_outline, size: 16, color: Color(0xFF7F8C8D)),
          const SizedBox(width: 8),
          Text(
            'Tool Runtime • ${widget.tool.uiComponents.length} components • Auto-save enabled',
            style: const TextStyle(fontSize: 12, color: Color(0xFF7F8C8D)),
          ),
          const Spacer(),
          if (widget.isPopup)
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
        ],
      ),
    );
  }

  dynamic _getBoundCellValue(UIComponent component) {
    if (component.boundCell == null) return null;

    // Get the current tool state from provider for real-time updates
    final currentTool = ref.read(currentExcelAppToolProvider);
    if (currentTool == null) {
      return _bindingService.getCellDisplayValue(
        component.boundCell!,
        widget.tool,
      );
    }

    // Use the current tool state for real-time calculation
    return _bindingService.getCellDisplayValue(
      component.boundCell!,
      currentTool,
    );
  }

  void _updateComponentValue(UIComponent component, dynamic value) {
    final startTime = DateTime.now();

    // Update local state immediately for <100ms response
    setState(() {
      _componentValues[component.id] = value;
    });

    // Update bound cell immediately with real-time propagation
    if (component.boundCell != null) {
      // Get current tool state
      final currentTool = ref.read(currentExcelAppToolProvider) ?? widget.tool;

      // Update the cell value in the binding service with current tool
      _bindingService.updateCell(component.boundCell!, value, currentTool, ref);

      // Update the current tool provider immediately with the new cell value
      final updatedTool = _updateToolCellValue(
        currentTool,
        component.boundCell!,
        value,
      );
      ref.read(currentExcelAppToolProvider.notifier).setTool(updatedTool);

      // Trigger immediate recalculation with performance tracking
      _recalculateAllComponents();

      // Force refresh all bound components immediately
      _refreshAllBoundComponents();

      // Log performance for monitoring
      final duration = DateTime.now().difference(startTime);
      if (duration.inMilliseconds > 50) {
        // Reduced threshold for better performance
        debugPrint(
          'Warning: Component update took ${duration.inMilliseconds}ms (target: <50ms)',
        );
      }
    }
  }

  // Create updated tool with new cell value
  ExcelAppTool _updateToolCellValue(
    ExcelAppTool tool,
    String cellAddress,
    dynamic value,
  ) {
    final updatedCells = Map<String, ExcelCell>.from(tool.spreadsheet.cells);

    final existingCell = updatedCells[cellAddress];
    final updatedCell = ExcelCell(
      address: cellAddress,
      value: value,
      formula: existingCell?.formula,
      isFormula: existingCell?.isFormula ?? false,
      formatting: existingCell?.formatting ?? {},
    );

    updatedCells[cellAddress] = updatedCell;

    return tool.copyWith(
      spreadsheet: tool.spreadsheet.copyWith(
        cells: updatedCells,
        lastModified: DateTime.now(),
      ),
      lastModified: DateTime.now(),
    );
  }

  void _recalculateAllComponents() {
    final startTime = DateTime.now();

    // Get the latest tool state
    final currentTool = ref.read(currentExcelAppToolProvider);
    if (currentTool == null) return;

    // Clear formula cache for fresh calculations
    _formulaEngine.clearCache();

    // Update all component values based on current cell values with real-time response
    setState(() {
      for (final component in currentTool.uiComponents) {
        if (component.boundCell != null) {
          // Get fresh calculated value
          final cellValue = _bindingService.getCellDisplayValue(
            component.boundCell!,
            currentTool,
          );
          _componentValues[component.id] = cellValue;

          // Update text controllers for input components without triggering onChange
          if (component.type == ComponentType.textInput ||
              component.type == ComponentType.numberInput) {
            final controller = _controllers[component.id];
            if (controller != null) {
              final newText = cellValue?.toString() ?? '';
              if (controller.text != newText) {
                // Temporarily disable listener to prevent recursive updates
                controller.removeListener(() {});
                controller.text = newText;
                // Re-enable listener
                controller.addListener(() {});
              }
            }
          }
        }
      }
    });

    // Performance monitoring
    final duration = DateTime.now().difference(startTime);
    if (duration.inMilliseconds > 100) {
      debugPrint(
        'Warning: Recalculation took ${duration.inMilliseconds}ms (target: <100ms)',
      );
    }
  }

  // Force refresh all bound components for immediate visual updates
  void _refreshAllBoundComponents() {
    final currentTool = ref.read(currentExcelAppToolProvider) ?? widget.tool;

    setState(() {
      for (final component in currentTool.uiComponents) {
        if (component.boundCell != null) {
          final cellValue = _bindingService.getCellDisplayValue(
            component.boundCell!,
            currentTool,
          );
          _componentValues[component.id] = cellValue;

          // Update text controllers for input components
          if (component.type == ComponentType.textInput ||
              component.type == ComponentType.numberInput) {
            final controller = _controllers[component.id];
            if (controller != null &&
                controller.text != cellValue?.toString()) {
              controller.text = cellValue?.toString() ?? '';
            }
          }
        }
      }
    });
  }

  void _refreshTool() {
    _formulaEngine.clearCache();
    setState(() {
      _initializeComponentValues();
    });
  }

  void _saveTool() async {
    try {
      await ref.read(excelAppToolsProvider.notifier).saveTool(widget.tool);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Tool saved successfully'),
            backgroundColor: Color(0xFF27AE60),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save tool: $e'),
            backgroundColor: const Color(0xFFE74C3C),
          ),
        );
      }
    }
  }

  Widget _buildUnsupportedComponent(UIComponent component) {
    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.orange),
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.warning, color: Colors.orange),
          const SizedBox(height: 4),
          Text(
            'Unsupported Component',
            style: TextStyle(
              fontSize: 12,
              color: Colors.orange,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            component.type.toString(),
            style: TextStyle(fontSize: 10, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }
}
