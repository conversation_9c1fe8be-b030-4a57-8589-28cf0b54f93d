import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../core/theme/app_theme.dart';
import '../services/money_flow_providers.dart';

import '../models/transaction.dart';

class MoneyFlowDashboard extends ConsumerWidget {
  const MoneyFlowDashboard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final statisticsAsync = ref.watch(moneyFlowStatisticsProvider);
    final balancesAsync = ref.watch(accountBalancesProvider);
    final recentTransactionsAsync = ref.watch(recentTransactionsProvider);
    final monthlySpendingAsync = ref.watch(monthlySpendingProvider);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Money Flow'),
        automaticallyImplyLeading: false,
        backgroundColor: AppTheme.moneyFlowColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () {
              ref.invalidate(moneyFlowStatisticsProvider);
              ref.invalidate(accountBalancesProvider);
              ref.invalidate(recentTransactionsProvider);
              ref.invalidate(monthlySpendingProvider);
            },
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWelcomeSection(context),
            const SizedBox(height: 32),
            _buildAccountOverview(context, balancesAsync),
            const SizedBox(height: 32),
            _buildQuickActions(context, ref),
            const SizedBox(height: 32),
            _buildQuickStats(context, statisticsAsync),
            const SizedBox(height: 32),
            _buildSpendingChart(context, monthlySpendingAsync),
            const SizedBox(height: 32),
            _buildRecentTransactions(context, ref, recentTransactionsAsync),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.moneyFlowColor,
            AppTheme.moneyFlowColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome to Money Flow',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Your comprehensive financial management and budgeting companion',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            Icons.account_balance_wallet,
            size: 64,
            color: Colors.white,
          ),
        ],
      ),
    );
  }

  Widget _buildAccountOverview(
    BuildContext context,
    AsyncValue<Map<String, double>> balancesAsync,
  ) {
    return balancesAsync.when(
      data: (balances) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Account Overview',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildBalanceCard(
                  context,
                  'Total Balance',
                  balances['totalBalance'] ?? 0.0,
                  Icons.account_balance,
                  AppTheme.moneyFlowColor,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildBalanceCard(
                  context,
                  'Total Assets',
                  balances['totalAssets'] ?? 0.0,
                  Icons.trending_up,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildBalanceCard(
                  context,
                  'Total Liabilities',
                  balances['totalLiabilities'] ?? 0.0,
                  Icons.trending_down,
                  Colors.red,
                ),
              ),
            ],
          ),
        ],
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Text('Error: $error'),
    );
  }

  Widget _buildBalanceCard(
    BuildContext context,
    String title,
    double amount,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 12),
            Text(
              '\$${amount.toStringAsFixed(2)}',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                context,
                'Add Transaction',
                Icons.add_circle,
                AppTheme.moneyFlowColor,
                () {
                  ref.read(selectedTransactionProvider.notifier).state = null;
                  ref.read(moneyFlowCurrentScreenProvider.notifier).state =
                      MoneyFlowScreen.transactionForm;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildActionButton(
                context,
                'Transfer Money',
                Icons.swap_horiz,
                AppTheme.moneyFlowColor,
                () {
                  // Create a transfer transaction
                  ref
                      .read(selectedTransactionProvider.notifier)
                      .state = MoneyTransaction(
                    accountId: '',
                    type: TransactionType.transfer,
                    amount: 0.0,
                    category: 'Transfer',
                    description: '',
                  );
                  ref.read(moneyFlowCurrentScreenProvider.notifier).state =
                      MoneyFlowScreen.transactionForm;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildActionButton(
                context,
                'View Reports',
                Icons.analytics,
                AppTheme.moneyFlowColor,
                () {
                  ref.read(moneyFlowCurrentScreenProvider.notifier).state =
                      MoneyFlowScreen.reports;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildActionButton(
                context,
                'Manage Budgets',
                Icons.pie_chart,
                AppTheme.moneyFlowColor,
                () {
                  ref.read(moneyFlowCurrentScreenProvider.notifier).state =
                      MoneyFlowScreen.budgets;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.all(20),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      child: Column(
        children: [
          Icon(icon, size: 32),
          const SizedBox(height: 8),
          Text(
            title,
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats(
    BuildContext context,
    AsyncValue<Map<String, int>> statisticsAsync,
  ) {
    return statisticsAsync.when(
      data: (stats) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Statistics',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  context,
                  'Active Accounts',
                  stats['accounts']?.toString() ?? '0',
                  Icons.account_balance,
                  AppTheme.moneyFlowColor,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Transactions',
                  stats['transactions']?.toString() ?? '0',
                  Icons.receipt_long,
                  AppTheme.moneyFlowColor,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Active Budgets',
                  stats['budgets']?.toString() ?? '0',
                  Icons.pie_chart,
                  AppTheme.moneyFlowColor,
                ),
              ),
            ],
          ),
        ],
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Text('Error: $error'),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 12),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpendingChart(
    BuildContext context,
    AsyncValue<Map<String, double>> spendingAsync,
  ) {
    return spendingAsync.when(
      data: (spending) {
        if (spending.isEmpty) {
          return const SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Monthly Spending by Category',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: SizedBox(
                  height: 300,
                  child: PieChart(
                    PieChartData(
                      sections: spending.entries.map((entry) {
                        final total = spending.values.reduce((a, b) => a + b);
                        final percentage = (entry.value / total * 100);
                        return PieChartSectionData(
                          value: entry.value,
                          title: '${percentage.toStringAsFixed(1)}%',
                          color: _getCategoryColor(entry.key),
                          radius: 100,
                          titleStyle: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        );
                      }).toList(),
                      sectionsSpace: 2,
                      centerSpaceRadius: 40,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 16,
              runSpacing: 8,
              children: spending.entries.map((entry) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: _getCategoryColor(entry.key),
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${entry.key}: \$${entry.value.toStringAsFixed(2)}',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                );
              }).toList(),
            ),
          ],
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Text('Error: $error'),
    );
  }

  Widget _buildRecentTransactions(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<List<MoneyTransaction>> transactionsAsync,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Transactions',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            TextButton(
              onPressed: () {
                ref.read(moneyFlowCurrentScreenProvider.notifier).state =
                    MoneyFlowScreen.transactions;
              },
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        transactionsAsync.when(
          data: (transactions) => transactions.isEmpty
              ? Card(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Center(
                      child: Column(
                        children: [
                          Icon(
                            Icons.receipt_long,
                            size: 48,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No transactions yet',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(color: Colors.grey[600]),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Add your first transaction to get started',
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(color: Colors.grey[600]),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              : Card(
                  child: ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: transactions.length,
                    separatorBuilder: (context, index) =>
                        const Divider(height: 1),
                    itemBuilder: (context, index) {
                      final transaction = transactions[index];
                      return _buildTransactionTile(context, ref, transaction);
                    },
                  ),
                ),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Text('Error: $error'),
        ),
      ],
    );
  }

  Widget _buildTransactionTile(
    BuildContext context,
    WidgetRef ref,
    MoneyTransaction transaction,
  ) {
    final color = _getTransactionColor(transaction.type);
    final icon = _getTransactionIcon(transaction.type);

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: color.withValues(alpha: 0.1),
        child: Icon(icon, color: color),
      ),
      title: Text(transaction.description),
      subtitle: Text('${transaction.category} • ${transaction.formattedDate}'),
      trailing: Text(
        transaction.formattedAmount,
        style: TextStyle(color: color, fontWeight: FontWeight.bold),
      ),
      onTap: () {
        ref.read(selectedTransactionProvider.notifier).state = transaction;
        ref.read(moneyFlowCurrentScreenProvider.notifier).state =
            MoneyFlowScreen.transactionDetail;
      },
    );
  }

  Color _getCategoryColor(String category) {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
      Colors.pink,
      Colors.indigo,
    ];
    return colors[category.hashCode % colors.length];
  }

  Color _getTransactionColor(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return Colors.green;
      case TransactionType.expense:
        return Colors.red;
      case TransactionType.transfer:
        return Colors.blue;
    }
  }

  IconData _getTransactionIcon(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return Icons.add_circle;
      case TransactionType.expense:
        return Icons.remove_circle;
      case TransactionType.transfer:
        return Icons.swap_horiz;
    }
  }
}
