import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'spreadsheet_editor_screen.dart';

class SpreadsheetListScreen extends ConsumerWidget {
  const SpreadsheetListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Spreadsheets'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () => _createNewSpreadsheet(context),
            icon: const Icon(Icons.add),
          ),
        ],
      ),
      body: _buildSpreadsheetList(context),
    );
  }

  Widget _buildSpreadsheetList(BuildContext context) {
    // Mock data for demonstration
    final spreadsheets = [
      {
        'name': 'Budget Calculator',
        'lastModified': '2 hours ago',
        'type': 'Financial',
      },
      {
        'name': 'Inventory Tracker',
        'lastModified': '1 day ago',
        'type': 'Business',
      },
      {
        'name': 'Grade Calculator',
        'lastModified': '3 days ago',
        'type': 'Education',
      },
      {
        'name': 'Project Timeline',
        'lastModified': '1 week ago',
        'type': 'Project Management',
      },
    ];

    if (spreadsheets.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: spreadsheets.length,
      itemBuilder: (context, index) {
        final spreadsheet = spreadsheets[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.table_chart, color: Colors.orange),
            ),
            title: Text(
              spreadsheet['name'] as String,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Type: ${spreadsheet['type']}'),
                Text(
                  'Last modified: ${spreadsheet['lastModified']}',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
            trailing: PopupMenuButton<String>(
              onSelected: (value) =>
                  _handleSpreadsheetAction(context, value, index),
              itemBuilder: (context) => [
                const PopupMenuItem(value: 'open', child: Text('Open')),
                const PopupMenuItem(
                  value: 'duplicate',
                  child: Text('Duplicate'),
                ),
                const PopupMenuItem(value: 'export', child: Text('Export')),
                const PopupMenuItem(value: 'delete', child: Text('Delete')),
              ],
            ),
            onTap: () => _openSpreadsheet(context, index),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.table_chart, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No Spreadsheets Yet',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first spreadsheet to get started',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _createNewSpreadsheet(context),
            icon: const Icon(Icons.add),
            label: const Text('Create Spreadsheet'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _createNewSpreadsheet(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            const SpreadsheetEditorScreen(spreadsheetName: 'New Spreadsheet'),
      ),
    );
  }

  void _openSpreadsheet(BuildContext context, int index) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SpreadsheetEditorScreen(
          spreadsheetName: 'Existing Spreadsheet',
        ),
      ),
    );
  }

  void _handleSpreadsheetAction(
    BuildContext context,
    String action,
    int index,
  ) {
    switch (action) {
      case 'open':
        _openSpreadsheet(context, index);
        break;
      case 'duplicate':
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Spreadsheet duplicated')));
        break;
      case 'export':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Export functionality coming soon')),
        );
        break;
      case 'delete':
        _deleteSpreadsheet(context, index);
        break;
    }
  }

  void _deleteSpreadsheet(BuildContext context, int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Spreadsheet'),
        content: const Text(
          'Are you sure you want to delete this spreadsheet? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Spreadsheet deleted')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
