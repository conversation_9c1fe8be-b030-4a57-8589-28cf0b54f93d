import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/app_theme.dart';
import '../services/excel_function_implementation_service.dart';
import '../services/data_validation_service.dart';
import '../services/app_generation_service.dart';
import '../services/integration_testing_service.dart';
import '../widgets/excel_function_browser_widget.dart';
import 'app_generation_screen.dart';
import 'deployment/deployment_dashboard_screen.dart';

/// Main Excel-to-App builder screen with comprehensive functionality
class ExcelToAppMainScreen extends ConsumerStatefulWidget {
  const ExcelToAppMainScreen({super.key});

  @override
  ConsumerState<ExcelToAppMainScreen> createState() =>
      _ExcelToAppMainScreenState();
}

class _ExcelToAppMainScreenState extends ConsumerState<ExcelToAppMainScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Statistics
  int _totalFunctions = 0;
  int _generatedApps = 0;
  bool _servicesInitialized = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _initializeServices();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _initializeServices() async {
    try {
      // Initialize all services
      ExcelFunctionImplementationService.initialize();
      DataValidationService.initialize();
      AppGenerationService.initialize();
      IntegrationTestingService.initialize();

      setState(() {
        _totalFunctions =
            ExcelFunctionImplementationService.availableFunctions.length;
        _generatedApps = AppGenerationService.generatedApps.length;
        _servicesInitialized = true;
      });
    } catch (e) {
      _showSnackBar('Failed to initialize services: $e', Colors.red);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Excel-to-App Builder'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showStatistics,
            icon: const Icon(Icons.analytics),
            tooltip: 'Statistics',
          ),
          IconButton(
            onPressed: _runQuickTest,
            icon: const Icon(Icons.play_arrow),
            tooltip: 'Quick Test',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.home), text: 'Overview'),
            Tab(icon: Icon(Icons.functions), text: 'Functions'),
            Tab(icon: Icon(Icons.build), text: 'App Builder'),
            Tab(icon: Icon(Icons.verified), text: 'Validation'),
            Tab(icon: Icon(Icons.assessment), text: 'Testing'),
            Tab(icon: Icon(Icons.rocket_launch), text: 'Deploy'),
          ],
        ),
      ),
      body: _servicesInitialized
          ? TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildFunctionsTab(),
                _buildAppBuilderTab(),
                _buildValidationTab(),
                _buildTestingTab(),
                _buildDeploymentTab(),
              ],
            )
          : const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Initializing Excel-to-App Builder...'),
                ],
              ),
            ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(),
          const SizedBox(height: 16),
          _buildQuickStatsCard(),
          const SizedBox(height: 16),
          _buildQuickActionsCard(),
          const SizedBox(height: 16),
          _buildRecentActivityCard(),
        ],
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          gradient: LinearGradient(
            colors: [
              AppTheme.primaryColor,
              AppTheme.primaryColor.withValues(alpha: 0.7),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Icon(Icons.apps, size: 48, color: Colors.white),
            const SizedBox(height: 16),
            Text(
              'Excel-to-App Builder',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Transform your Excel data into powerful, interactive applications with 100+ Excel functions, advanced layouts, and real-time collaboration.',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.white.withValues(alpha: 0.9),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _tabController.animateTo(2),
              icon: const Icon(Icons.build),
              label: const Text('Start Building'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: AppTheme.primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStatsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Statistics',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Excel Functions',
                    '$_totalFunctions',
                    Icons.functions,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Generated Apps',
                    '$_generatedApps',
                    Icons.apps,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Templates',
                    '${AppGenerationService.appTemplates.length}',
                    Icons.dashboard_outlined,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Components',
                    '${AppGenerationService.componentLibrary.length}',
                    Icons.widgets,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: [
                _buildQuickActionButton(
                  'Browse Functions',
                  Icons.functions,
                  Colors.blue,
                  () => _tabController.animateTo(1),
                ),
                _buildQuickActionButton(
                  'Create App',
                  Icons.add,
                  Colors.green,
                  () => _tabController.animateTo(2),
                ),
                _buildQuickActionButton(
                  'Validate Data',
                  Icons.verified,
                  Colors.orange,
                  () => _tabController.animateTo(3),
                ),
                _buildQuickActionButton(
                  'Run Tests',
                  Icons.play_arrow,
                  Colors.purple,
                  () => _tabController.animateTo(4),
                ),
                _buildQuickActionButton(
                  'Deploy App',
                  Icons.rocket_launch,
                  Colors.red,
                  () => _tabController.animateTo(5),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }

  Widget _buildRecentActivityCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Activity',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildActivityItem(
              'Excel Function Implementation Service initialized',
              'All 100+ Excel functions are ready to use',
              Icons.functions,
              Colors.blue,
            ),
            _buildActivityItem(
              'Data Validation Service initialized',
              'Comprehensive data validation rules available',
              Icons.verified,
              Colors.green,
            ),
            _buildActivityItem(
              'App Generation Service initialized',
              '5 app templates and 6 components ready',
              Icons.build,
              Colors.orange,
            ),
            _buildActivityItem(
              'Integration Testing Service initialized',
              'Quality assurance and deployment tools ready',
              Icons.assessment,
              Colors.purple,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(
    String title,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(subtitle, style: Theme.of(context).textTheme.bodySmall),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionsTab() {
    return ExcelFunctionBrowserWidget(
      onFunctionSelected: (functionName) {
        _showSnackBar('Function $functionName selected', Colors.blue);
      },
    );
  }

  Widget _buildAppBuilderTab() {
    return const AppGenerationScreen();
  }

  Widget _buildValidationTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.verified, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text('Data Validation'),
          Text('Comprehensive data validation tools'),
        ],
      ),
    );
  }

  Widget _buildTestingTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.assessment, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text('Integration Testing'),
          Text('Quality assurance and testing tools'),
        ],
      ),
    );
  }

  Widget _buildDeploymentTab() {
    return const DeploymentDashboardScreen();
  }

  void _showStatistics() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('System Statistics'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatRow('Excel Functions', '$_totalFunctions'),
            _buildStatRow('Generated Apps', '$_generatedApps'),
            _buildStatRow(
              'App Templates',
              '${AppGenerationService.appTemplates.length}',
            ),
            _buildStatRow(
              'UI Components',
              '${AppGenerationService.componentLibrary.length}',
            ),
            _buildStatRow(
              'Validation Rules',
              '${DataValidationService.availableRules.length}',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  void _runQuickTest() async {
    try {
      // Test a simple Excel function
      final result = ExcelFunctionImplementationService.executeFunction('SUM', [
        1,
        2,
        3,
        4,
        5,
      ]);
      _showSnackBar(
        'Quick test passed: SUM(1,2,3,4,5) = $result',
        Colors.green,
      );
    } catch (e) {
      _showSnackBar('Quick test failed: $e', Colors.red);
    }
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
