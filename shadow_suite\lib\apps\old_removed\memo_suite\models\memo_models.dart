

// Core Memo Model
class Memo {
  final String id;
  final String title;
  final String content;
  final MemoType type;
  final List<String> tags;
  final MemoCategory category;
  final bool isPinned;
  final bool isArchived;
  final bool isEncrypted;
  final DateTime createdAt;
  final DateTime lastModified;
  final String? parentId;
  final List<String> attachments;
  final Map<String, dynamic> metadata;

  const Memo({
    required this.id,
    required this.title,
    required this.content,
    required this.type,
    required this.tags,
    required this.category,
    required this.isPinned,
    required this.isArchived,
    required this.isEncrypted,
    required this.createdAt,
    required this.lastModified,
    this.parentId,
    required this.attachments,
    required this.metadata,
  });

  factory Memo.fromJson(Map<String, dynamic> json) {
    return Memo(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      type: MemoType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => MemoType.text,
      ),
      tags: List<String>.from(json['tags'] as List? ?? []),
      category: MemoCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => MemoCategory.general,
      ),
      isPinned: json['is_pinned'] as bool,
      isArchived: json['is_archived'] as bool,
      isEncrypted: json['is_encrypted'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastModified: DateTime.parse(json['last_modified'] as String),
      parentId: json['parent_id'] as String?,
      attachments: List<String>.from(json['attachments'] as List? ?? []),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'type': type.name,
      'tags': tags,
      'category': category.name,
      'is_pinned': isPinned,
      'is_archived': isArchived,
      'is_encrypted': isEncrypted,
      'created_at': createdAt.toIso8601String(),
      'last_modified': lastModified.toIso8601String(),
      'parent_id': parentId,
      'attachments': attachments,
      'metadata': metadata,
    };
  }

  Memo copyWith({
    String? title,
    String? content,
    MemoType? type,
    List<String>? tags,
    MemoCategory? category,
    bool? isPinned,
    bool? isArchived,
    bool? isEncrypted,
    String? parentId,
    List<String>? attachments,
    Map<String, dynamic>? metadata,
  }) {
    return Memo(
      id: id,
      title: title ?? this.title,
      content: content ?? this.content,
      type: type ?? this.type,
      tags: tags ?? this.tags,
      category: category ?? this.category,
      isPinned: isPinned ?? this.isPinned,
      isArchived: isArchived ?? this.isArchived,
      isEncrypted: isEncrypted ?? this.isEncrypted,
      createdAt: createdAt,
      lastModified: DateTime.now(),
      parentId: parentId ?? this.parentId,
      attachments: attachments ?? this.attachments,
      metadata: metadata ?? this.metadata,
    );
  }
}

// Memo Template Model
class MemoTemplate {
  final String id;
  final String name;
  final String description;
  final String content;
  final MemoType type;
  final List<String> placeholders;
  final MemoCategory category;
  final bool isBuiltIn;
  final int usageCount;
  final DateTime createdAt;

  const MemoTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.content,
    required this.type,
    required this.placeholders,
    required this.category,
    required this.isBuiltIn,
    required this.usageCount,
    required this.createdAt,
  });

  factory MemoTemplate.fromJson(Map<String, dynamic> json) {
    return MemoTemplate(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      content: json['content'] as String,
      type: MemoType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => MemoType.text,
      ),
      placeholders: List<String>.from(json['placeholders'] as List? ?? []),
      category: MemoCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => MemoCategory.general,
      ),
      isBuiltIn: json['is_built_in'] as bool,
      usageCount: json['usage_count'] as int,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'content': content,
      'type': type.name,
      'placeholders': placeholders,
      'category': category.name,
      'is_built_in': isBuiltIn,
      'usage_count': usageCount,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Voice Recording Model
class VoiceRecording {
  final String id;
  final String memoId;
  final String filePath;
  final Duration duration;
  final String? transcription;
  final double? confidence;
  final DateTime recordedAt;
  final int fileSize;
  final AudioQuality quality;

  const VoiceRecording({
    required this.id,
    required this.memoId,
    required this.filePath,
    required this.duration,
    this.transcription,
    this.confidence,
    required this.recordedAt,
    required this.fileSize,
    required this.quality,
  });

  factory VoiceRecording.fromJson(Map<String, dynamic> json) {
    return VoiceRecording(
      id: json['id'] as String,
      memoId: json['memo_id'] as String,
      filePath: json['file_path'] as String,
      duration: Duration(milliseconds: json['duration_ms'] as int),
      transcription: json['transcription'] as String?,
      confidence: (json['confidence'] as num?)?.toDouble(),
      recordedAt: DateTime.parse(json['recorded_at'] as String),
      fileSize: json['file_size'] as int,
      quality: AudioQuality.values.firstWhere(
        (e) => e.name == json['quality'],
        orElse: () => AudioQuality.medium,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'memo_id': memoId,
      'file_path': filePath,
      'duration_ms': duration.inMilliseconds,
      'transcription': transcription,
      'confidence': confidence,
      'recorded_at': recordedAt.toIso8601String(),
      'file_size': fileSize,
      'quality': quality.name,
    };
  }
}

// Collaboration Session Model
class CollaborationSession {
  final String id;
  final String memoId;
  final List<String> participants;
  final String ownerId;
  final SessionStatus status;
  final DateTime startedAt;
  final DateTime? endedAt;
  final List<CollaborationChange> changes;
  final Map<String, dynamic> settings;

  const CollaborationSession({
    required this.id,
    required this.memoId,
    required this.participants,
    required this.ownerId,
    required this.status,
    required this.startedAt,
    this.endedAt,
    required this.changes,
    required this.settings,
  });

  factory CollaborationSession.fromJson(Map<String, dynamic> json) {
    return CollaborationSession(
      id: json['id'] as String,
      memoId: json['memo_id'] as String,
      participants: List<String>.from(json['participants'] as List? ?? []),
      ownerId: json['owner_id'] as String,
      status: SessionStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => SessionStatus.active,
      ),
      startedAt: DateTime.parse(json['started_at'] as String),
      endedAt: json['ended_at'] != null 
          ? DateTime.parse(json['ended_at'] as String) 
          : null,
      changes: (json['changes'] as List<dynamic>?)
          ?.map((e) => CollaborationChange.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      settings: Map<String, dynamic>.from(json['settings'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'memo_id': memoId,
      'participants': participants,
      'owner_id': ownerId,
      'status': status.name,
      'started_at': startedAt.toIso8601String(),
      'ended_at': endedAt?.toIso8601String(),
      'changes': changes.map((e) => e.toJson()).toList(),
      'settings': settings,
    };
  }
}

// Collaboration Change Model
class CollaborationChange {
  final String id;
  final String sessionId;
  final String userId;
  final ChangeType type;
  final String content;
  final int position;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  const CollaborationChange({
    required this.id,
    required this.sessionId,
    required this.userId,
    required this.type,
    required this.content,
    required this.position,
    required this.timestamp,
    required this.metadata,
  });

  factory CollaborationChange.fromJson(Map<String, dynamic> json) {
    return CollaborationChange(
      id: json['id'] as String,
      sessionId: json['session_id'] as String,
      userId: json['user_id'] as String,
      type: ChangeType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ChangeType.insert,
      ),
      content: json['content'] as String,
      position: json['position'] as int,
      timestamp: DateTime.parse(json['timestamp'] as String),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'session_id': sessionId,
      'user_id': userId,
      'type': type.name,
      'content': content,
      'position': position,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }
}

// Version Control Model
class MemoVersion {
  final String id;
  final String memoId;
  final int versionNumber;
  final String content;
  final String? changeDescription;
  final String authorId;
  final DateTime createdAt;
  final List<String> changedSections;
  final Map<String, dynamic> metadata;

  const MemoVersion({
    required this.id,
    required this.memoId,
    required this.versionNumber,
    required this.content,
    this.changeDescription,
    required this.authorId,
    required this.createdAt,
    required this.changedSections,
    required this.metadata,
  });

  factory MemoVersion.fromJson(Map<String, dynamic> json) {
    return MemoVersion(
      id: json['id'] as String,
      memoId: json['memo_id'] as String,
      versionNumber: json['version_number'] as int,
      content: json['content'] as String,
      changeDescription: json['change_description'] as String?,
      authorId: json['author_id'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      changedSections: List<String>.from(json['changed_sections'] as List? ?? []),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'memo_id': memoId,
      'version_number': versionNumber,
      'content': content,
      'change_description': changeDescription,
      'author_id': authorId,
      'created_at': createdAt.toIso8601String(),
      'changed_sections': changedSections,
      'metadata': metadata,
    };
  }
}

// Enums
enum MemoType { text, markdown, richText, checklist, voice, drawing, code }
enum MemoCategory { general, work, personal, study, project, meeting, idea, todo }
enum AudioQuality { low, medium, high, lossless }
enum SessionStatus { active, paused, ended }
enum ChangeType { insert, delete, replace, format }
