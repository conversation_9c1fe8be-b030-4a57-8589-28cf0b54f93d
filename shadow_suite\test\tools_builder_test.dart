import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:shadow_suite/apps/tools_builder/models/tools_models.dart';
import 'package:shadow_suite/apps/tools_builder/services/tools_database_service.dart';

void main() {
  // Initialize sqflite for testing
  setUpAll(() {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  });

  group('Tools Builder Models', () {
    test('Basic model functionality', () {
      // Test that enums are accessible
      expect(ToolType.calculator, isNotNull);
      expect(ToolCategory.finance, isNotNull);
      expect(ToolStatus.draft, isNotNull);
    });

    test('Tool types are accessible', () {
      // Test that tool types are accessible
      expect(ToolType.calculator, isNotNull);
      expect(ToolType.converter, isNotNull);
      expect(ToolType.form, isNotNull);
    });
  });

  group('Tools Database Service', () {
    test('Basic service creation', () {
      final service = ToolsDatabaseService();
      expect(service, isNotNull);
    });
  });

  group('Tool Template', () {
    test('Tool template creation', () {
      final template = ToolTemplate(
        name: 'Test Tool',
        description: 'A test tool',
        type: ToolType.calculator,
        category: ToolCategory.finance,
      );

      expect(template.name, equals('Test Tool'));
      expect(template.description, equals('A test tool'));
      expect(template.type, equals(ToolType.calculator));
      expect(template.category, equals(ToolCategory.finance));
    });
  });
}
