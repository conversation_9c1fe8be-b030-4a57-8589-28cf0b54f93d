import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/excel_function_implementation_service.dart';

/// Excel function browser widget for exploring and testing functions
class ExcelFunctionBrowserWidget extends StatefulWidget {
  final Function(String)? onFunctionSelected;

  const ExcelFunctionBrowserWidget({super.key, this.onFunctionSelected});

  @override
  State<ExcelFunctionBrowserWidget> createState() =>
      _ExcelFunctionBrowserWidgetState();
}

class _ExcelFunctionBrowserWidgetState extends State<ExcelFunctionBrowserWidget>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _searchController = TextEditingController();
  final _testController = TextEditingController();

  List<String> _filteredFunctions = [];
  FunctionCategory? _selectedCategory;
  String? _selectedFunction;
  dynamic _testResult;
  String? _testError;

  @override
  void initState() {
    super.initState();
    ExcelFunctionImplementationService.initialize();
    _tabController = TabController(length: 2, vsync: this);
    _loadFunctions();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _testController.dispose();
    super.dispose();
  }

  void _loadFunctions() {
    setState(() {
      _filteredFunctions =
          ExcelFunctionImplementationService.availableFunctions;
    });
  }

  void _filterFunctions() {
    final query = _searchController.text.toLowerCase();
    final allFunctions = ExcelFunctionImplementationService.availableFunctions;

    setState(() {
      _filteredFunctions = allFunctions.where((function) {
        final matchesSearch =
            query.isEmpty || function.toLowerCase().contains(query);
        final matchesCategory =
            _selectedCategory == null ||
            ExcelFunctionImplementationService.getFunctionsByCategory(
              _selectedCategory!,
            ).contains(function);
        return matchesSearch && matchesCategory;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildHeader(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [_buildFunctionBrowser(), _buildFunctionTester()],
          ),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              const Icon(Icons.functions, color: Colors.blue),
              const SizedBox(width: 8),
              Text(
                'Excel Functions',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Colors.blue.shade700,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                '${_filteredFunctions.length} functions',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TabBar(
            controller: _tabController,
            labelColor: Colors.blue.shade700,
            unselectedLabelColor: Colors.grey.shade600,
            indicatorColor: Colors.blue.shade700,
            tabs: const [
              Tab(icon: Icon(Icons.search), text: 'Browse'),
              Tab(icon: Icon(Icons.play_arrow), text: 'Test'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionBrowser() {
    return Column(
      children: [
        _buildSearchAndFilter(),
        Expanded(child: _buildFunctionList()),
        if (_selectedFunction != null) _buildFunctionDetails(),
      ],
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search functions...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        _filterFunctions();
                      },
                      icon: const Icon(Icons.clear),
                    )
                  : null,
              border: const OutlineInputBorder(),
            ),
            onChanged: (_) => _filterFunctions(),
          ),
          const SizedBox(height: 16),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                FilterChip(
                  label: const Text('All'),
                  selected: _selectedCategory == null,
                  onSelected: (selected) {
                    setState(() {
                      _selectedCategory = null;
                    });
                    _filterFunctions();
                  },
                ),
                const SizedBox(width: 8),
                ...FunctionCategory.values.map((category) {
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(_getCategoryDisplayName(category)),
                      selected: _selectedCategory == category,
                      onSelected: (selected) {
                        setState(() {
                          _selectedCategory = selected ? category : null;
                        });
                        _filterFunctions();
                      },
                    ),
                  );
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionList() {
    return ListView.builder(
      itemCount: _filteredFunctions.length,
      itemBuilder: (context, index) {
        final function = _filteredFunctions[index];
        final metadata = ExcelFunctionImplementationService.getFunctionMetadata(
          function,
        );
        final isSelected = _selectedFunction == function;

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          elevation: isSelected ? 4 : 1,
          color: isSelected ? Colors.blue.shade50 : null,
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getCategoryColor(metadata?.category),
              child: Text(
                function.substring(0, 1),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            title: Text(
              function,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: isSelected ? Colors.blue.shade700 : null,
              ),
            ),
            subtitle: Text(
              metadata?.description ?? 'No description available',
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (metadata?.category != null)
                  Chip(
                    label: Text(
                      _getCategoryDisplayName(metadata!.category),
                      style: const TextStyle(fontSize: 10),
                    ),
                    backgroundColor: _getCategoryColor(
                      metadata.category,
                    ).withValues(alpha: 0.2),
                  ),
                const SizedBox(width: 8),
                Icon(
                  isSelected
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                  color: Colors.grey.shade600,
                ),
              ],
            ),
            onTap: () {
              setState(() {
                _selectedFunction = isSelected ? null : function;
              });
              if (widget.onFunctionSelected != null && !isSelected) {
                widget.onFunctionSelected!(function);
              }
            },
          ),
        );
      },
    );
  }

  Widget _buildFunctionDetails() {
    final metadata = ExcelFunctionImplementationService.getFunctionMetadata(
      _selectedFunction!,
    );

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                _selectedFunction!,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: () {
                  Clipboard.setData(ClipboardData(text: _selectedFunction!));
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Function name copied to clipboard'),
                    ),
                  );
                },
                icon: const Icon(Icons.copy),
                tooltip: 'Copy function name',
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (metadata != null) ...[
            Text(
              metadata.description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Text('Category: '),
                Chip(
                  label: Text(_getCategoryDisplayName(metadata.category)),
                  backgroundColor: _getCategoryColor(
                    metadata.category,
                  ).withValues(alpha: 0.2),
                ),
              ],
            ),
          ],
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    _tabController.animateTo(1);
                    _testController.text = '${_selectedFunction!}()';
                  },
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('Test Function'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    if (widget.onFunctionSelected != null) {
                      widget.onFunctionSelected!(_selectedFunction!);
                    }
                  },
                  icon: const Icon(Icons.add),
                  label: const Text('Use Function'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionTester() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Function Tester',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _testController,
            decoration: const InputDecoration(
              labelText: 'Enter function call',
              hintText: 'e.g., SUM(1,2,3) or AVERAGE(10,20,30)',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.functions),
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _testFunction,
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('Execute'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    _testController.clear();
                    setState(() {
                      _testResult = null;
                      _testError = null;
                    });
                  },
                  icon: const Icon(Icons.clear),
                  label: const Text('Clear'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          if (_testResult != null || _testError != null) _buildTestResult(),
        ],
      ),
    );
  }

  Widget _buildTestResult() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _testError != null ? Colors.red.shade50 : Colors.green.shade50,
        border: Border.all(
          color: _testError != null
              ? Colors.red.shade300
              : Colors.green.shade300,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _testError != null ? Icons.error : Icons.check_circle,
                color: _testError != null
                    ? Colors.red.shade700
                    : Colors.green.shade700,
              ),
              const SizedBox(width: 8),
              Text(
                _testError != null ? 'Error' : 'Result',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _testError != null
                      ? Colors.red.shade700
                      : Colors.green.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            _testError ?? _testResult.toString(),
            style: const TextStyle(fontFamily: 'monospace'),
          ),
        ],
      ),
    );
  }

  void _testFunction() {
    final input = _testController.text.trim();
    if (input.isEmpty) return;

    try {
      // Simple parser for function calls like "SUM(1,2,3)"
      final match = RegExp(r'^(\w+)\((.*)\)$').firstMatch(input);
      if (match == null) {
        throw ArgumentError(
          'Invalid function format. Use: FUNCTION(arg1,arg2,...)',
        );
      }

      final functionName = match.group(1)!;
      final argsString = match.group(2)!;

      final args = <dynamic>[];
      if (argsString.isNotEmpty) {
        for (final arg in argsString.split(',')) {
          final trimmed = arg.trim();
          if (trimmed.startsWith('"') && trimmed.endsWith('"')) {
            // String argument
            args.add(trimmed.substring(1, trimmed.length - 1));
          } else {
            // Try to parse as number
            final number = double.tryParse(trimmed);
            args.add(number ?? trimmed);
          }
        }
      }

      final result = ExcelFunctionImplementationService.executeFunction(
        functionName,
        args,
      );

      setState(() {
        _testResult = result;
        _testError = null;
      });
    } catch (e) {
      setState(() {
        _testResult = null;
        _testError = e.toString();
      });
    }
  }

  String _getCategoryDisplayName(FunctionCategory category) {
    switch (category) {
      case FunctionCategory.mathematical:
        return 'Math';
      case FunctionCategory.statistical:
        return 'Stats';
      case FunctionCategory.text:
        return 'Text';
      case FunctionCategory.dateTime:
        return 'Date/Time';
      case FunctionCategory.logical:
        return 'Logical';
      case FunctionCategory.lookup:
        return 'Lookup';
      case FunctionCategory.financial:
        return 'Financial';
      case FunctionCategory.engineering:
        return 'Engineering';
      case FunctionCategory.database:
        return 'Database';
      case FunctionCategory.array:
        return 'Array';
    }
  }

  Color _getCategoryColor(FunctionCategory? category) {
    if (category == null) return Colors.grey;

    switch (category) {
      case FunctionCategory.mathematical:
        return Colors.blue;
      case FunctionCategory.statistical:
        return Colors.green;
      case FunctionCategory.text:
        return Colors.orange;
      case FunctionCategory.dateTime:
        return Colors.purple;
      case FunctionCategory.logical:
        return Colors.red;
      case FunctionCategory.lookup:
        return Colors.teal;
      case FunctionCategory.financial:
        return Colors.indigo;
      case FunctionCategory.engineering:
        return Colors.brown;
      case FunctionCategory.database:
        return Colors.cyan;
      case FunctionCategory.array:
        return Colors.pink;
    }
  }
}
