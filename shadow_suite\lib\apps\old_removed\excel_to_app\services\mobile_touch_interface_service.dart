import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'performance_optimizer.dart';

/// Mobile-optimized touch interface service
class MobileTouchInterfaceService {
  static bool _isInitialized = false;
  static TouchConfiguration _configuration = const TouchConfiguration();
  static final Map<String, GestureHandler> _gestureHandlers = {};
  static final List<TouchEvent> _touchHistory = [];
  static bool _isMobileDevice = false;

  /// Initialize the mobile touch interface service
  static void initialize() {
    if (_isInitialized) return;

    _detectMobileDevice();
    _setupGestureHandlers();
    _isInitialized = true;
  }

  /// Get current touch configuration
  static TouchConfiguration get configuration => _configuration;

  /// Update touch configuration
  static void updateConfiguration(TouchConfiguration newConfig) {
    _configuration = newConfig;
    _applyConfiguration();
  }

  /// Check if running on mobile device
  static bool get isMobileDevice => _isMobileDevice;

  /// Register gesture handler
  static void registerGestureHandler(String id, GestureHandler handler) {
    _gestureHandlers[id] = handler;
  }

  /// Unregister gesture handler
  static void unregisterGestureHandler(String id) {
    _gestureHandlers.remove(id);
  }

  /// Handle touch event
  static Future<void> handleTouchEvent(TouchEvent event) async {
    return PerformanceOptimizer.measureAsync('handle_touch_event', () async {
      _touchHistory.add(event);

      // Keep only last 100 events
      if (_touchHistory.length > 100) {
        _touchHistory.removeAt(0);
      }

      // Process gesture handlers
      for (final handler in _gestureHandlers.values) {
        if (handler.canHandle(event)) {
          await handler.handle(event);
        }
      }
    });
  }

  /// Get optimized widget for mobile
  static Widget optimizeForMobile(
    Widget child, {
    bool enableSwipeGestures = true,
    bool enablePinchZoom = false,
    bool enableDoubleTap = true,
    bool enableLongPress = true,
  }) {
    if (!_isMobileDevice) return child;

    return MobileOptimizedWrapper(
      enableSwipeGestures: enableSwipeGestures,
      enablePinchZoom: enablePinchZoom,
      enableDoubleTap: enableDoubleTap,
      enableLongPress: enableLongPress,
      child: child,
    );
  }

  /// Get mobile-optimized button
  static Widget getMobileButton({
    required String text,
    required VoidCallback? onPressed,
    IconData? icon,
    ButtonStyle? style,
    bool isLarge = false,
  }) {
    final buttonStyle =
        style ??
        ElevatedButton.styleFrom(
          minimumSize: Size(
            isLarge ? 120 : 80,
            _configuration.minimumTouchTargetSize,
          ),
          padding: EdgeInsets.symmetric(
            horizontal: _configuration.buttonPadding,
            vertical: _configuration.buttonPadding / 2,
          ),
        );

    if (icon != null) {
      return ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon),
        label: Text(text),
        style: buttonStyle,
      );
    } else {
      return ElevatedButton(
        onPressed: onPressed,
        style: buttonStyle,
        child: Text(text),
      );
    }
  }

  /// Get mobile-optimized text field
  static Widget getMobileTextField({
    required String label,
    TextEditingController? controller,
    Function(String)? onChanged,
    TextInputType? keyboardType,
    bool obscureText = false,
    String? hintText,
    Widget? suffixIcon,
    int maxLines = 1,
  }) {
    return TextField(
      controller: controller,
      onChanged: onChanged,
      keyboardType: keyboardType,
      obscureText: obscureText,
      maxLines: maxLines,
      style: TextStyle(fontSize: _configuration.fontSize),
      decoration: InputDecoration(
        labelText: label,
        hintText: hintText,
        suffixIcon: suffixIcon,
        border: const OutlineInputBorder(),
        contentPadding: EdgeInsets.all(_configuration.inputPadding),
      ),
    );
  }

  /// Get mobile-optimized list tile
  static Widget getMobileListTile({
    required String title,
    String? subtitle,
    Widget? leading,
    Widget? trailing,
    VoidCallback? onTap,
    bool isThreeLine = false,
  }) {
    return ListTile(
      title: Text(title, style: TextStyle(fontSize: _configuration.fontSize)),
      subtitle: subtitle != null
          ? Text(
              subtitle,
              style: TextStyle(fontSize: _configuration.fontSize - 2),
            )
          : null,
      leading: leading,
      trailing: trailing,
      onTap: onTap,
      isThreeLine: isThreeLine,
      contentPadding: EdgeInsets.symmetric(
        horizontal: _configuration.listItemPadding,
        vertical: _configuration.listItemPadding / 2,
      ),
      minVerticalPadding: _configuration.listItemPadding / 2,
    );
  }

  /// Get mobile-optimized card
  static Widget getMobileCard({
    required Widget child,
    VoidCallback? onTap,
    EdgeInsets? margin,
    EdgeInsets? padding,
  }) {
    return Card(
      margin: margin ?? EdgeInsets.all(_configuration.cardMargin),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: padding ?? EdgeInsets.all(_configuration.cardPadding),
          child: child,
        ),
      ),
    );
  }

  /// Get mobile-optimized app bar
  static PreferredSizeWidget getMobileAppBar({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool automaticallyImplyLeading = true,
    Color? backgroundColor,
    Color? foregroundColor,
  }) {
    return AppBar(
      title: Text(
        title,
        style: TextStyle(fontSize: _configuration.appBarTitleSize),
      ),
      actions: actions?.map((action) {
        return Padding(
          padding: EdgeInsets.all(_configuration.appBarActionPadding),
          child: action,
        );
      }).toList(),
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      toolbarHeight: _configuration.appBarHeight,
    );
  }

  /// Get mobile-optimized bottom navigation
  static Widget getMobileBottomNavigation({
    required List<BottomNavigationBarItem> items,
    required int currentIndex,
    required Function(int) onTap,
    BottomNavigationBarType? type,
  }) {
    return BottomNavigationBar(
      items: items,
      currentIndex: currentIndex,
      onTap: onTap,
      type: type ?? BottomNavigationBarType.fixed,
      selectedFontSize: _configuration.bottomNavFontSize,
      unselectedFontSize: _configuration.bottomNavFontSize - 2,
      iconSize: _configuration.bottomNavIconSize,
    );
  }

  /// Get mobile-optimized floating action button
  static Widget getMobileFAB({
    required VoidCallback onPressed,
    required Widget child,
    String? tooltip,
    Color? backgroundColor,
    bool isExtended = false,
    String? label,
  }) {
    if (isExtended && label != null) {
      return FloatingActionButton.extended(
        onPressed: onPressed,
        icon: child,
        label: Text(
          label,
          style: TextStyle(fontSize: _configuration.fabTextSize),
        ),
        tooltip: tooltip,
        backgroundColor: backgroundColor,
      );
    } else {
      return FloatingActionButton(
        onPressed: onPressed,
        tooltip: tooltip,
        backgroundColor: backgroundColor,
        child: child,
      );
    }
  }

  /// Get mobile-optimized dialog
  static Widget getMobileDialog({
    required String title,
    required Widget content,
    List<Widget>? actions,
    bool scrollable = false,
  }) {
    return AlertDialog(
      title: Text(
        title,
        style: TextStyle(fontSize: _configuration.dialogTitleSize),
      ),
      content: scrollable ? SingleChildScrollView(child: content) : content,
      actions: actions,
      contentPadding: EdgeInsets.all(_configuration.dialogPadding),
      actionsPadding: EdgeInsets.all(_configuration.dialogPadding / 2),
    );
  }

  /// Get mobile-optimized snack bar
  static SnackBar getMobileSnackBar({
    required String message,
    SnackBarAction? action,
    Duration? duration,
    Color? backgroundColor,
  }) {
    return SnackBar(
      content: Text(
        message,
        style: TextStyle(fontSize: _configuration.snackBarFontSize),
      ),
      action: action,
      duration: duration ?? const Duration(seconds: 4),
      backgroundColor: backgroundColor,
      behavior: SnackBarBehavior.floating,
      margin: EdgeInsets.all(_configuration.snackBarMargin),
    );
  }

  /// Provide haptic feedback
  static void hapticFeedback(HapticFeedbackType type) {
    if (!_configuration.enableHapticFeedback) return;

    switch (type) {
      case HapticFeedbackType.light:
        HapticFeedback.lightImpact();
        break;
      case HapticFeedbackType.medium:
        HapticFeedback.mediumImpact();
        break;
      case HapticFeedbackType.heavy:
        HapticFeedback.heavyImpact();
        break;
      case HapticFeedbackType.selection:
        HapticFeedback.selectionClick();
        break;
    }
  }

  /// Get touch statistics
  static TouchStatistics getTouchStatistics() {
    final totalEvents = _touchHistory.length;
    final eventsByType = <TouchEventType, int>{};

    for (final event in _touchHistory) {
      eventsByType[event.type] = (eventsByType[event.type] ?? 0) + 1;
    }

    final averageResponseTime = _touchHistory.isNotEmpty
        ? _touchHistory
                  .map((e) => e.responseTime.inMicroseconds)
                  .reduce((a, b) => a + b) /
              _touchHistory.length
        : 0.0;

    return TouchStatistics(
      totalEvents: totalEvents,
      eventsByType: eventsByType,
      averageResponseTime: Duration(microseconds: averageResponseTime.round()),
      isMobileOptimized: _isMobileDevice,
    );
  }

  /// Clear touch history
  static void clearTouchHistory() {
    _touchHistory.clear();
  }

  // Private methods
  static void _detectMobileDevice() {
    // Simple mobile detection - in production would use proper device detection
    _isMobileDevice = true; // Assume mobile for this implementation
  }

  static void _setupGestureHandlers() {
    // Setup default gesture handlers
    _gestureHandlers['swipe'] = SwipeGestureHandler();
    _gestureHandlers['pinch'] = PinchGestureHandler();
    _gestureHandlers['tap'] = TapGestureHandler();
    _gestureHandlers['long_press'] = LongPressGestureHandler();
  }

  static void _applyConfiguration() {
    // Apply configuration changes
    if (_configuration.enableHapticFeedback) {
      HapticFeedback.lightImpact();
    }
  }
}

/// Touch configuration class
class TouchConfiguration {
  final double minimumTouchTargetSize;
  final double fontSize;
  final double buttonPadding;
  final double inputPadding;
  final double listItemPadding;
  final double cardMargin;
  final double cardPadding;
  final double appBarHeight;
  final double appBarTitleSize;
  final double appBarActionPadding;
  final double bottomNavFontSize;
  final double bottomNavIconSize;
  final double fabTextSize;
  final double dialogTitleSize;
  final double dialogPadding;
  final double snackBarFontSize;
  final double snackBarMargin;
  final bool enableHapticFeedback;
  final bool enableSwipeGestures;
  final bool enablePinchZoom;

  const TouchConfiguration({
    this.minimumTouchTargetSize = 48.0,
    this.fontSize = 16.0,
    this.buttonPadding = 16.0,
    this.inputPadding = 16.0,
    this.listItemPadding = 16.0,
    this.cardMargin = 8.0,
    this.cardPadding = 16.0,
    this.appBarHeight = 56.0,
    this.appBarTitleSize = 20.0,
    this.appBarActionPadding = 8.0,
    this.bottomNavFontSize = 14.0,
    this.bottomNavIconSize = 24.0,
    this.fabTextSize = 16.0,
    this.dialogTitleSize = 20.0,
    this.dialogPadding = 24.0,
    this.snackBarFontSize = 14.0,
    this.snackBarMargin = 16.0,
    this.enableHapticFeedback = true,
    this.enableSwipeGestures = true,
    this.enablePinchZoom = false,
  });
}

/// Touch event class
class TouchEvent {
  final TouchEventType type;
  final Offset position;
  final DateTime timestamp;
  final Duration responseTime;
  final Map<String, dynamic> data;

  const TouchEvent({
    required this.type,
    required this.position,
    required this.timestamp,
    required this.responseTime,
    this.data = const {},
  });
}

/// Gesture handler interface
abstract class GestureHandler {
  bool canHandle(TouchEvent event);
  Future<void> handle(TouchEvent event);
}

/// Swipe gesture handler
class SwipeGestureHandler extends GestureHandler {
  @override
  bool canHandle(TouchEvent event) {
    return event.type == TouchEventType.swipe;
  }

  @override
  Future<void> handle(TouchEvent event) async {
    MobileTouchInterfaceService.hapticFeedback(HapticFeedbackType.light);
  }
}

/// Pinch gesture handler
class PinchGestureHandler extends GestureHandler {
  @override
  bool canHandle(TouchEvent event) {
    return event.type == TouchEventType.pinch;
  }

  @override
  Future<void> handle(TouchEvent event) async {
    MobileTouchInterfaceService.hapticFeedback(HapticFeedbackType.medium);
  }
}

/// Tap gesture handler
class TapGestureHandler extends GestureHandler {
  @override
  bool canHandle(TouchEvent event) {
    return event.type == TouchEventType.tap;
  }

  @override
  Future<void> handle(TouchEvent event) async {
    MobileTouchInterfaceService.hapticFeedback(HapticFeedbackType.selection);
  }
}

/// Long press gesture handler
class LongPressGestureHandler extends GestureHandler {
  @override
  bool canHandle(TouchEvent event) {
    return event.type == TouchEventType.longPress;
  }

  @override
  Future<void> handle(TouchEvent event) async {
    MobileTouchInterfaceService.hapticFeedback(HapticFeedbackType.heavy);
  }
}

/// Mobile optimized wrapper widget
class MobileOptimizedWrapper extends StatelessWidget {
  final Widget child;
  final bool enableSwipeGestures;
  final bool enablePinchZoom;
  final bool enableDoubleTap;
  final bool enableLongPress;

  const MobileOptimizedWrapper({
    super.key,
    required this.child,
    this.enableSwipeGestures = true,
    this.enablePinchZoom = false,
    this.enableDoubleTap = true,
    this.enableLongPress = true,
  });

  @override
  Widget build(BuildContext context) {
    Widget wrappedChild = child;

    if (enablePinchZoom) {
      wrappedChild = InteractiveViewer(child: wrappedChild);
    }

    return GestureDetector(
      onTap: enableDoubleTap ? _handleTap : null,
      onDoubleTap: enableDoubleTap ? _handleDoubleTap : null,
      onLongPress: enableLongPress ? _handleLongPress : null,
      onPanUpdate: enableSwipeGestures ? _handlePanUpdate : null,
      child: wrappedChild,
    );
  }

  void _handleTap() {
    final event = TouchEvent(
      type: TouchEventType.tap,
      position: Offset.zero,
      timestamp: DateTime.now(),
      responseTime: Duration.zero,
    );
    MobileTouchInterfaceService.handleTouchEvent(event);
  }

  void _handleDoubleTap() {
    final event = TouchEvent(
      type: TouchEventType.doubleTap,
      position: Offset.zero,
      timestamp: DateTime.now(),
      responseTime: Duration.zero,
    );
    MobileTouchInterfaceService.handleTouchEvent(event);
  }

  void _handleLongPress() {
    final event = TouchEvent(
      type: TouchEventType.longPress,
      position: Offset.zero,
      timestamp: DateTime.now(),
      responseTime: Duration.zero,
    );
    MobileTouchInterfaceService.handleTouchEvent(event);
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    final event = TouchEvent(
      type: TouchEventType.swipe,
      position: details.localPosition,
      timestamp: DateTime.now(),
      responseTime: Duration.zero,
      data: {'delta': details.delta, 'velocity': details.primaryDelta},
    );
    MobileTouchInterfaceService.handleTouchEvent(event);
  }
}

/// Touch statistics class
class TouchStatistics {
  final int totalEvents;
  final Map<TouchEventType, int> eventsByType;
  final Duration averageResponseTime;
  final bool isMobileOptimized;

  const TouchStatistics({
    required this.totalEvents,
    required this.eventsByType,
    required this.averageResponseTime,
    required this.isMobileOptimized,
  });
}

/// Enums
enum TouchEventType { tap, doubleTap, longPress, swipe, pinch, pan }

enum HapticFeedbackType { light, medium, heavy, selection }
