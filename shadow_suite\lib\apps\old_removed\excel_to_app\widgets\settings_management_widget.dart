import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/settings_persistence_service.dart';
import '../services/advanced_layout_service.dart';

/// Settings management widget with auto-save controls and export/import
class SettingsManagementWidget extends StatefulWidget {
  const SettingsManagementWidget({super.key});

  @override
  State<SettingsManagementWidget> createState() =>
      _SettingsManagementWidgetState();
}

class _SettingsManagementWidgetState extends State<SettingsManagementWidget> {
  bool _autoSaveEnabled = true;
  int _autoSaveInterval = 30;
  DateTime? _lastSaveTime;
  Map<String, dynamic>? _storageStats;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
    SettingsPersistenceService.addChangeListener(_onSettingsChanged);
  }

  @override
  void dispose() {
    SettingsPersistenceService.removeChangeListener(_onSettingsChanged);
    super.dispose();
  }

  void _onSettingsChanged() {
    if (mounted) {
      _loadSettings();
    }
  }

  Future<void> _loadSettings() async {
    setState(() => _isLoading = true);

    try {
      _autoSaveEnabled = SettingsPersistenceService.isAutoSaveEnabled;
      _autoSaveInterval = SettingsPersistenceService.autoSaveIntervalSeconds;
      _lastSaveTime = await SettingsPersistenceService.getLastSaveTime();
      _storageStats = await SettingsPersistenceService.getStorageStats();
    } catch (e) {
      _showErrorSnackBar('Failed to load settings: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.settings, size: 24),
                const SizedBox(width: 8),
                Text(
                  'Settings Management',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const Spacer(),
                if (_isLoading)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
              ],
            ),
            const SizedBox(height: 24),

            // Auto-save Settings
            _buildAutoSaveSection(),
            const SizedBox(height: 24),

            // Export/Import Section
            _buildExportImportSection(),
            const SizedBox(height: 24),

            // Storage Information
            _buildStorageInfoSection(),
            const SizedBox(height: 24),

            // Action Buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildAutoSaveSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Auto-save Settings',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 16),

        SwitchListTile(
          title: const Text('Enable Auto-save'),
          subtitle: const Text('Automatically save changes every few seconds'),
          value: _autoSaveEnabled,
          onChanged: _isLoading
              ? null
              : (value) async {
                  await SettingsPersistenceService.setAutoSaveEnabled(value);
                },
        ),

        if (_autoSaveEnabled) ...[
          const SizedBox(height: 8),
          ListTile(
            title: const Text('Auto-save Interval'),
            subtitle: Text('Save every $_autoSaveInterval seconds'),
            trailing: SizedBox(
              width: 100,
              child: DropdownButton<int>(
                value: _autoSaveInterval,
                isExpanded: true,
                items: [10, 15, 30, 60, 120, 300].map((seconds) {
                  return DropdownMenuItem(
                    value: seconds,
                    child: Text('${seconds}s'),
                  );
                }).toList(),
                onChanged: _isLoading
                    ? null
                    : (value) async {
                        if (value != null) {
                          await SettingsPersistenceService.setAutoSaveInterval(
                            value,
                          );
                        }
                      },
              ),
            ),
          ),
        ],

        if (_lastSaveTime != null) ...[
          const SizedBox(height: 8),
          ListTile(
            leading: const Icon(Icons.access_time),
            title: const Text('Last Saved'),
            subtitle: Text(_formatDateTime(_lastSaveTime!)),
          ),
        ],
      ],
    );
  }

  Widget _buildExportImportSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Configuration Management',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 16),

        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _exportConfiguration,
                icon: const Icon(Icons.download),
                label: const Text('Export Config'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _importConfiguration,
                icon: const Icon(Icons.upload),
                label: const Text('Import Config'),
              ),
            ),
          ],
        ),

        const SizedBox(height: 8),

        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _isLoading ? null : _copyToClipboard,
                icon: const Icon(Icons.copy),
                label: const Text('Copy JSON'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _isLoading ? null : _pasteFromClipboard,
                icon: const Icon(Icons.paste),
                label: const Text('Paste JSON'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStorageInfoSection() {
    if (_storageStats == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Storage Information',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 16),

        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              _buildStorageRow(
                'Configuration Size',
                '${_storageStats!['configurationSize']} bytes',
              ),
              _buildStorageRow(
                'Presets Size',
                '${_storageStats!['presetsSize']} bytes',
              ),
              _buildStorageRow(
                'Total Storage',
                '${_storageStats!['totalSize']} bytes',
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStorageRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isLoading ? null : _saveNow,
            icon: const Icon(Icons.save),
            label: const Text('Save Now'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _isLoading ? null : _clearAllData,
            icon: const Icon(Icons.delete_forever),
            label: const Text('Clear All'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _exportConfiguration() async {
    try {
      setState(() => _isLoading = true);

      final configJson = await SettingsPersistenceService.exportConfiguration();

      // For web, copy to clipboard. For mobile/desktop, save to file
      if (!mounted) return;
      if (Theme.of(context).platform == TargetPlatform.android ||
          Theme.of(context).platform == TargetPlatform.iOS) {
        await Clipboard.setData(ClipboardData(text: configJson));
        _showSuccessSnackBar('Configuration copied to clipboard');
      } else {
        // Save to file (simplified - in real implementation would use file_picker)
        await Clipboard.setData(ClipboardData(text: configJson));
        _showSuccessSnackBar('Configuration copied to clipboard');
      }
    } catch (e) {
      _showErrorSnackBar('Export failed: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _importConfiguration() async {
    try {
      setState(() => _isLoading = true);

      // For simplicity, get from clipboard. In real implementation, would use file picker
      final clipboardData = await Clipboard.getData('text/plain');
      if (clipboardData?.text != null) {
        final success = await SettingsPersistenceService.importConfiguration(
          clipboardData!.text!,
        );

        if (success) {
          _showSuccessSnackBar('Configuration imported successfully');
          await _loadSettings();
        } else {
          _showErrorSnackBar('Failed to import configuration');
        }
      } else {
        _showErrorSnackBar('No configuration data found in clipboard');
      }
    } catch (e) {
      _showErrorSnackBar('Import failed: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _copyToClipboard() async {
    try {
      final configJson = await SettingsPersistenceService.exportConfiguration();
      await Clipboard.setData(ClipboardData(text: configJson));
      _showSuccessSnackBar('Configuration copied to clipboard');
    } catch (e) {
      _showErrorSnackBar('Copy failed: $e');
    }
  }

  Future<void> _pasteFromClipboard() async {
    try {
      final clipboardData = await Clipboard.getData('text/plain');
      if (clipboardData?.text != null) {
        final success = await SettingsPersistenceService.importConfiguration(
          clipboardData!.text!,
        );

        if (success) {
          _showSuccessSnackBar('Configuration imported from clipboard');
          await _loadSettings();
        } else {
          _showErrorSnackBar('Invalid configuration format');
        }
      } else {
        _showErrorSnackBar('No data found in clipboard');
      }
    } catch (e) {
      _showErrorSnackBar('Paste failed: $e');
    }
  }

  Future<void> _saveNow() async {
    try {
      setState(() => _isLoading = true);

      final currentConfig = AdvancedLayoutService.currentConfig;
      final success = await SettingsPersistenceService.saveLayoutConfiguration(
        currentConfig,
      );

      if (success) {
        _showSuccessSnackBar('Configuration saved successfully');
        await _loadSettings();
      } else {
        _showErrorSnackBar('Failed to save configuration');
      }
    } catch (e) {
      _showErrorSnackBar('Save failed: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _clearAllData() async {
    final confirmed = await _showConfirmDialog(
      'Clear All Data',
      'This will permanently delete all saved configurations and presets. This action cannot be undone.',
    );

    if (confirmed) {
      try {
        setState(() => _isLoading = true);
        await SettingsPersistenceService.clearAllData();
        _showSuccessSnackBar('All data cleared successfully');
        await _loadSettings();
      } catch (e) {
        _showErrorSnackBar('Clear failed: $e');
      } finally {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<bool> _showConfirmDialog(String title, String content) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} '
        '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
