import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/excel_app_tool.dart';
import '../services/excel_app_providers.dart';
import 'excel_to_app_main.dart';

class ExcelAppCreateToolDialog extends ConsumerStatefulWidget {
  const ExcelAppCreateToolDialog({super.key});

  @override
  ConsumerState<ExcelAppCreateToolDialog> createState() => _ExcelAppCreateToolDialogState();
}

class _ExcelAppCreateToolDialogState extends ConsumerState<ExcelAppCreateToolDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _pinController = TextEditingController();

  SecurityType _securityType = SecurityType.none;
  int _columns = 10;
  int _rows = 20;
  bool _isLoading = false;

  // Expandable section states
  bool _basicInfoExpanded = true;
  bool _templatesExpanded = false;
  // final bool _securityExpanded = false; // Reserved for future security features
  // final bool _dimensionsExpanded = false; // Reserved for future dimension settings
  bool _uiComponentsExpanded = false;
  bool _dataBindingExpanded = false;
  bool _toolPropertiesExpanded = false;

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _pinController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final dialogWidth = (screenSize.width * 0.8).clamp(600.0, 1000.0);
    final dialogHeight = (screenSize.height * 0.9).clamp(600.0, 800.0);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: SizedBox(
        width: dialogWidth,
        height: dialogHeight,
        child: Column(
          children: [
            _buildHeader(context),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildBasicInfoSection(),
                      const SizedBox(height: 24),
                      _buildQuickTemplatesSection(),
                      const SizedBox(height: 24),
                      _buildSecuritySection(),
                      const SizedBox(height: 24),
                      _buildDimensionsSection(),
                      const SizedBox(height: 24),
                      _buildUIComponentsSection(),
                      const SizedBox(height: 24),
                      _buildDataBindingSection(),
                      const SizedBox(height: 24),
                      _buildToolPropertiesSection(),
                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),
            ),
            _buildActions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: const Color(0xFF3498DB).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.table_view,
            color: Color(0xFF3498DB),
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Create New Tool',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2C3E50),
                ),
              ),
              Text(
                'Transform your spreadsheet into an interactive app',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: const Color(0xFF7F8C8D),
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  Widget _buildNameField() {
    return TextFormField(
      controller: _nameController,
      decoration: const InputDecoration(
        labelText: 'Tool Name *',
        hintText: 'Enter a name for your tool',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.label),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Tool name is required';
        }
        if (value.trim().length < 3) {
          return 'Tool name must be at least 3 characters';
        }
        if (value.trim().length > 50) {
          return 'Tool name must be less than 50 characters';
        }
        return null;
      },
      autofocus: true,
    );
  }

  Widget _buildDescriptionField() {
    return TextFormField(
      controller: _descriptionController,
      decoration: const InputDecoration(
        labelText: 'Description (Optional)',
        hintText: 'Describe what your tool does',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.description),
      ),
      maxLines: 3,
      maxLength: 200,
    );
  }

  Widget _buildSecuritySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.security,
              color: const Color(0xFF2C3E50),
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Security Options',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: const Color(0xFF2C3E50),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFFF8F9FA),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: const Color(0xFFE9ECEF)),
          ),
          child: Column(
            children: [
              _buildSecurityOption(
                SecurityType.none,
                'No Security',
                'Anyone can access this tool instantly',
                Icons.lock_open,
                const Color(0xFF95A5A6),
              ),
              const SizedBox(height: 12),
              _buildSecurityOption(
                SecurityType.pin,
                'PIN Protection',
                'Require a numeric PIN to access this tool',
                Icons.lock,
                const Color(0xFFF39C12),
              ),
              const SizedBox(height: 12),
              _buildSecurityOption(
                SecurityType.biometric,
                'Biometric Protection',
                'Use fingerprint or face recognition (if available)',
                Icons.fingerprint,
                const Color(0xFF27AE60),
              ),
            ],
          ),
        ),
        if (_securityType == SecurityType.pin) ...[
          const SizedBox(height: 16),
          _buildPinInputSection(),
        ],
        if (_securityType == SecurityType.biometric) ...[
          const SizedBox(height: 16),
          _buildBiometricInfoSection(),
        ],
      ],
    );
  }

  Widget _buildPinInputSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFFFF8E1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFF39C12).withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.pin,
                color: const Color(0xFFF39C12),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'PIN Configuration',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFFF39C12),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          TextFormField(
            controller: _pinController,
            decoration: InputDecoration(
              labelText: 'PIN Code',
              hintText: 'Enter 4-6 digit PIN',
              border: const OutlineInputBorder(),
              prefixIcon: const Icon(Icons.pin),
              suffixIcon: _pinController.text.isNotEmpty
                  ? Icon(
                      _pinController.text.length >= 4 ? Icons.check_circle : Icons.error,
                      color: _pinController.text.length >= 4 ? const Color(0xFF27AE60) : const Color(0xFFE74C3C),
                    )
                  : null,
            ),
            keyboardType: TextInputType.number,
            maxLength: 6,
            onChanged: (value) {
              setState(() {}); // Trigger rebuild for suffix icon
            },
            validator: (value) {
              if (_securityType == SecurityType.pin) {
                if (value == null || value.isEmpty) {
                  return 'PIN is required';
                }
                if (value.length < 4) {
                  return 'PIN must be at least 4 digits';
                }
                if (!RegExp(r'^\d+$').hasMatch(value)) {
                  return 'PIN must contain only numbers';
                }
              }
              return null;
            },
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.info_outline,
                size: 16,
                color: const Color(0xFF7F8C8D),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Choose a PIN that\'s easy to remember but hard to guess. Avoid simple patterns like 1234.',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: const Color(0xFF7F8C8D),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBiometricInfoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFE8F5E8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF27AE60).withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.fingerprint,
                color: const Color(0xFF27AE60),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Biometric Authentication',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF27AE60),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(
                Icons.check_circle,
                size: 16,
                color: const Color(0xFF27AE60),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Most secure option available',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: const Color(0xFF27AE60),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Icon(
                Icons.info_outline,
                size: 16,
                color: const Color(0xFF7F8C8D),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Requires device support for fingerprint or face recognition. Falls back to device PIN if biometric fails.',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: const Color(0xFF7F8C8D),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityOption(SecurityType type, String title, String subtitle, IconData icon, Color color) {
    final isSelected = _securityType == type;

    return GestureDetector(
      onTap: () {
        setState(() {
          _securityType = type;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? color.withValues(alpha: 0.1) : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? color : const Color(0xFFE9ECEF),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isSelected ? color : color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                size: 20,
                color: isSelected ? Colors.white : color,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isSelected ? color : const Color(0xFF2C3E50),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: const Color(0xFF7F8C8D),
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: color,
                size: 24,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDimensionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Spreadsheet Dimensions',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: const Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Columns: $_columns',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        'A-${_getColumnName(_columns)}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: const Color(0xFF3498DB),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  Slider(
                    value: _columns.toDouble(),
                    min: 5,
                    max: 50,
                    divisions: 45,
                    activeColor: const Color(0xFF3498DB),
                    onChanged: (value) {
                      setState(() {
                        _columns = value.toInt();
                      });
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Rows: $_rows',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        '1-$_rows',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: const Color(0xFF3498DB),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  Slider(
                    value: _rows.toDouble(),
                    min: 10,
                    max: 1000,
                    divisions: 99,
                    activeColor: const Color(0xFF3498DB),
                    onChanged: (value) {
                      setState(() {
                        _rows = value.toInt();
                      });
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
        _buildDimensionsPreview(),
        const SizedBox(height: 16),
        _buildTemplateSelection(),
      ],
    );
  }

  Widget _buildDimensionsPreview() {
    final totalCells = _columns * _rows;
    final memoryEstimate = (totalCells * 0.1).toStringAsFixed(1); // Rough estimate in KB

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF3498DB).withValues(alpha: 0.1),
            const Color(0xFF2980B9).withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF3498DB).withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF3498DB),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.grid_view,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Spreadsheet Preview',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF2C3E50),
                      ),
                    ),
                    Text(
                      '$totalCells total cells • ~${memoryEstimate}KB memory',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: const Color(0xFF7F8C8D),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildDimensionCard(
                  'Columns',
                  '$_columns',
                  'A to ${_getColumnName(_columns)}',
                  Icons.view_column,
                  const Color(0xFF27AE60),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildDimensionCard(
                  'Rows',
                  '$_rows',
                  '1 to $_rows',
                  Icons.view_agenda,
                  const Color(0xFFE74C3C),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDimensionCard(String title, String value, String range, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: const Color(0xFF7F8C8D),
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            range,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: const Color(0xFF95A5A6),
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Templates',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: const Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildTemplateButton(
                'Small\n(10×20)',
                'Perfect for simple forms',
                Icons.smartphone,
                () => _setDimensions(10, 20),
                _columns == 10 && _rows == 20,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildTemplateButton(
                'Medium\n(20×50)',
                'Good for data tables',
                Icons.tablet,
                () => _setDimensions(20, 50),
                _columns == 20 && _rows == 50,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildTemplateButton(
                'Large\n(30×100)',
                'Complex applications',
                Icons.computer,
                () => _setDimensions(30, 100),
                _columns == 30 && _rows == 100,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTemplateButton(String title, String subtitle, IconData icon, VoidCallback onTap, bool isSelected) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF3498DB).withValues(alpha: 0.1) : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? const Color(0xFF3498DB) : const Color(0xFFE9ECEF),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? const Color(0xFF3498DB) : const Color(0xFF7F8C8D),
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: isSelected ? const Color(0xFF3498DB) : const Color(0xFF2C3E50),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: const Color(0xFF7F8C8D),
                fontSize: 10,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _setDimensions(int columns, int rows) {
    setState(() {
      _columns = columns;
      _rows = rows;
    });
  }

  Widget _buildActions(BuildContext context) {
    final isFormValid = _nameController.text.trim().isNotEmpty &&
        (_securityType != SecurityType.pin || _pinController.text.length >= 4);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE9ECEF)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.summarize,
                color: const Color(0xFF2C3E50),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Summary',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2C3E50),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildSummaryItem('Tool Name', _nameController.text.trim().isEmpty ? 'Not set' : _nameController.text.trim()),
          _buildSummaryItem('Security', _getSecurityDisplayName()),
          _buildSummaryItem('Dimensions', '$_columns columns × $_rows rows'),
          _buildSummaryItem('Total Cells', '${_columns * _rows} cells'),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
                child: const Text('Cancel'),
              ),
              const SizedBox(width: 12),
              ElevatedButton(
                onPressed: _isLoading || !isFormValid ? null : _createTool,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF3498DB),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.add, size: 20),
                          const SizedBox(width: 8),
                          const Text('Create Tool'),
                        ],
                      ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: const Color(0xFF7F8C8D),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const Text(': '),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: const Color(0xFF2C3E50),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getSecurityDisplayName() {
    switch (_securityType) {
      case SecurityType.none:
        return 'No Security';
      case SecurityType.pin:
        return 'PIN Protection';
      case SecurityType.biometric:
        return 'Biometric Protection';
    }
  }

  String _getColumnName(int columnIndex) {
    String result = '';
    while (columnIndex > 0) {
      columnIndex--;
      result = String.fromCharCode(65 + (columnIndex % 26)) + result;
      columnIndex ~/= 26;
    }
    return result;
  }

  void _createTool() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final now = DateTime.now();
      final tool = ExcelAppTool(
        id: now.millisecondsSinceEpoch.toString(),
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        securityType: _securityType,
        securityPin: _securityType == SecurityType.pin ? _pinController.text : null,
        spreadsheet: ExcelSpreadsheet(
          name: 'Sheet1',
          columns: _columns,
          rows: _rows,
          cells: {},
          lastModified: now,
        ),
        uiComponents: [],
        createdAt: now,
        lastModified: now,
      );

      // Save the tool
      await ref.read(excelAppToolsProvider.notifier).saveTool(tool);

      // Set as current tool and navigate to editor
      ref.read(currentExcelAppToolProvider.notifier).setTool(tool);
      ref.read(excelToAppCurrentScreenProvider.notifier).state = ExcelToAppScreen.createTool;

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${tool.name} created successfully!'),
            backgroundColor: const Color(0xFF27AE60),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create tool: $e'),
            backgroundColor: const Color(0xFFE74C3C),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Widget _buildBasicInfoSection() {
    return _buildExpandableSection(
      'Basic Information',
      Icons.info_outline,
      _basicInfoExpanded,
      (expanded) => setState(() => _basicInfoExpanded = expanded),
      [
        _buildNameField(),
        const SizedBox(height: 16),
        _buildDescriptionField(),
      ],
    );
  }

  Widget _buildQuickTemplatesSection() {
    return _buildExpandableSection(
      'Quick Templates',
      Icons.dashboard,
      _templatesExpanded,
      (expanded) => setState(() => _templatesExpanded = expanded),
      [
        const Text(
          'Choose a template to get started quickly:',
          style: TextStyle(fontSize: 14, color: Color(0xFF7F8C8D)),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildTemplatePreview(
                'Calculator',
                'Basic calculator with operations',
                Icons.calculate,
                () => _applyTemplate('calculator'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildTemplatePreview(
                'Form',
                'Data entry form with validation',
                Icons.assignment,
                () => _applyTemplate('form'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildTemplatePreview(
                'Dashboard',
                'Analytics dashboard with charts',
                Icons.dashboard,
                () => _applyTemplate('dashboard'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildUIComponentsSection() {
    return _buildExpandableSection(
      'UI Components',
      Icons.widgets,
      _uiComponentsExpanded,
      (expanded) => setState(() => _uiComponentsExpanded = expanded),
      [
        const Text(
          'Available components for your tool:',
          style: TextStyle(fontSize: 14, color: Color(0xFF7F8C8D)),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildComponentChip('Text Input', Icons.text_fields),
            _buildComponentChip('Button', Icons.smart_button),
            _buildComponentChip('Label', Icons.label),
            _buildComponentChip('Dropdown', Icons.arrow_drop_down_circle),
            _buildComponentChip('Checkbox', Icons.check_box),
            _buildComponentChip('Slider', Icons.tune),
            _buildComponentChip('Image', Icons.image),
            _buildComponentChip('Chart', Icons.bar_chart),
          ],
        ),
      ],
    );
  }

  Widget _buildDataBindingSection() {
    return _buildExpandableSection(
      'Data Binding',
      Icons.link,
      _dataBindingExpanded,
      (expanded) => setState(() => _dataBindingExpanded = expanded),
      [
        const Text(
          'Configure how UI components connect to spreadsheet cells:',
          style: TextStyle(fontSize: 14, color: Color(0xFF7F8C8D)),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFFF8F9FA),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: const Color(0xFFE9ECEF)),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.info, size: 16, color: Color(0xFF3498DB)),
                  SizedBox(width: 8),
                  Text(
                    'Real-time Data Binding',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                ],
              ),
              SizedBox(height: 8),
              Text(
                '• Components automatically sync with spreadsheet cells\n'
                '• Changes update in real-time (<100ms response)\n'
                '• Formulas recalculate automatically\n'
                '• Support for complex data relationships',
                style: TextStyle(fontSize: 12, color: Color(0xFF7F8C8D)),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildToolPropertiesSection() {
    return _buildExpandableSection(
      'Tool Properties',
      Icons.settings,
      _toolPropertiesExpanded,
      (expanded) => setState(() => _toolPropertiesExpanded = expanded),
      [
        const Text(
          'Additional tool configuration:',
          style: TextStyle(fontSize: 14, color: Color(0xFF7F8C8D)),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Auto-save',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 4),
                  const Text(
                    'Automatically save changes',
                    style: TextStyle(fontSize: 12, color: Color(0xFF7F8C8D)),
                  ),
                  Switch(
                    value: true,
                    onChanged: (value) {},
                  ),
                ],
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Offline Mode',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 4),
                  const Text(
                    'Work without internet',
                    style: TextStyle(fontSize: 12, color: Color(0xFF7F8C8D)),
                  ),
                  Switch(
                    value: false,
                    onChanged: (value) {},
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildExpandableSection(
    String title,
    IconData icon,
    bool isExpanded,
    Function(bool) onToggle,
    List<Widget> children,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE9ECEF)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () => onToggle(!isExpanded),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(12),
              topRight: Radius.circular(12),
            ),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isExpanded ? const Color(0xFFF8F9FA) : Colors.transparent,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Icon(icon, size: 20, color: const Color(0xFF3498DB)),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF2C3E50),
                      ),
                    ),
                  ),
                  Icon(
                    isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: const Color(0xFF7F8C8D),
                  ),
                ],
              ),
            ),
          ),
          if (isExpanded)
            Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: children,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTemplatePreview(String name, String description, IconData icon, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: const Color(0xFFF8F9FA),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: const Color(0xFFE9ECEF)),
        ),
        child: Column(
          children: [
            Icon(icon, size: 32, color: const Color(0xFF3498DB)),
            const SizedBox(height: 8),
            Text(
              name,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              description,
              style: const TextStyle(
                fontSize: 10,
                color: Color(0xFF7F8C8D),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComponentChip(String label, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: const Color(0xFFE9ECEF)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: const Color(0xFF3498DB)),
          const SizedBox(width: 6),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF2C3E50),
            ),
          ),
        ],
      ),
    );
  }

  void _applyTemplate(String templateType) {
    switch (templateType) {
      case 'calculator':
        _nameController.text = 'Calculator Tool';
        _descriptionController.text = 'A basic calculator with arithmetic operations';
        _rows = 15;
        _columns = 10;
        break;
      case 'form':
        _nameController.text = 'Data Entry Form';
        _descriptionController.text = 'A form for collecting and validating user input';
        _rows = 20;
        _columns = 8;
        break;
      case 'dashboard':
        _nameController.text = 'Analytics Dashboard';
        _descriptionController.text = 'A dashboard with charts and key metrics';
        _rows = 25;
        _columns = 15;
        break;
    }
    setState(() {});

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Applied $templateType template'),
        backgroundColor: const Color(0xFF27AE60),
      ),
    );
  }
}
