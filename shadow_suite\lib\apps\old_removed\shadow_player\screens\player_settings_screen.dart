import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/shadow_player_providers.dart';

class PlayerSettingsScreen extends ConsumerWidget {
  const PlayerSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Scan Settings
          _buildSettingsSection(
            title: 'Scan Settings',
            children: [
              _buildScanLocationsCard(ref),
              const SizedBox(height: 16),
              _buildFileFormatsCard(ref),
              const SizedBox(height: 16),
              _buildScanOptionsCard(ref),
            ],
          ),

          const SizedBox(height: 32),

          // Playback Settings
          _buildSettingsSection(
            title: 'Playback Settings',
            children: [
              _buildPlaybackOptionsCard(ref),
              const SizedBox(height: 16),
              _buildAudioSettingsCard(context, ref),
            ],
          ),

          const SizedBox(height: 32),

          // Storage Settings
          _buildSettingsSection(
            title: 'Storage & Cache',
            children: [_buildStorageCard(ref)],
          ),

          const SizedBox(height: 32),

          // About Section
          _buildSettingsSection(title: 'About', children: [_buildAboutCard()]),
        ],
      ),
    );
  }

  Widget _buildSettingsSection({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            color: Color(0xFF2C3E50),
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildScanLocationsCard(WidgetRef ref) {
    final scanLocations = ref.watch(scanLocationsProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.folder_open,
                  color: Color(0xFF3498DB),
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Text(
                  'Scan Locations',
                  style: TextStyle(
                    color: Color(0xFF2C3E50),
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => _addScanLocation(ref),
                  child: const Text('Add Location'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...scanLocations.map(
              (location) => _buildLocationItem(location, ref),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationItem(String location, WidgetRef ref) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE9ECEF)),
      ),
      child: Row(
        children: [
          const Icon(Icons.folder, color: Color(0xFF7F8C8D), size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              location,
              style: const TextStyle(color: Color(0xFF2C3E50), fontSize: 14),
            ),
          ),
          IconButton(
            onPressed: () => _removeScanLocation(location, ref),
            icon: const Icon(
              Icons.remove_circle,
              color: Color(0xFFE74C3C),
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFileFormatsCard(WidgetRef ref) {
    final videoFormats = ref.watch(supportedVideoFormatsProvider);
    final audioFormats = ref.watch(supportedAudioFormatsProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.file_present, color: Color(0xFF27AE60), size: 24),
                SizedBox(width: 12),
                Text(
                  'Supported File Formats',
                  style: TextStyle(
                    color: Color(0xFF2C3E50),
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Video Formats
            const Text(
              'Video Formats',
              style: TextStyle(
                color: Color(0xFF7F8C8D),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: videoFormats
                  .map(
                    (format) => Chip(
                      label: Text(format.toUpperCase()),
                      backgroundColor: const Color(
                        0xFFE74C3C,
                      ).withValues(alpha: 0.1),
                      labelStyle: const TextStyle(
                        color: Color(0xFFE74C3C),
                        fontSize: 12,
                      ),
                    ),
                  )
                  .toList(),
            ),
            const SizedBox(height: 16),

            // Audio Formats
            const Text(
              'Audio Formats',
              style: TextStyle(
                color: Color(0xFF7F8C8D),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: audioFormats
                  .map(
                    (format) => Chip(
                      label: Text(format.toUpperCase()),
                      backgroundColor: const Color(
                        0xFF3498DB,
                      ).withValues(alpha: 0.1),
                      labelStyle: const TextStyle(
                        color: Color(0xFF3498DB),
                        fontSize: 12,
                      ),
                    ),
                  )
                  .toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScanOptionsCard(WidgetRef ref) {
    final autoScanEnabled = ref.watch(autoScanEnabledProvider);
    final generateThumbnails = ref.watch(generateThumbnailsProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.settings, color: Color(0xFF9B59B6), size: 24),
                SizedBox(width: 12),
                Text(
                  'Scan Options',
                  style: TextStyle(
                    color: Color(0xFF2C3E50),
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            SwitchListTile(
              title: const Text('Auto-scan on startup'),
              subtitle: const Text(
                'Automatically scan for new media files when app starts',
              ),
              value: autoScanEnabled,
              onChanged: (value) {
                ref.read(autoScanEnabledProvider.notifier).state = value;
              },
              activeColor: const Color(0xFF3498DB),
            ),

            SwitchListTile(
              title: const Text('Generate thumbnails'),
              subtitle: const Text('Create preview thumbnails for video files'),
              value: generateThumbnails,
              onChanged: (value) {
                ref.read(generateThumbnailsProvider.notifier).state = value;
              },
              activeColor: const Color(0xFF3498DB),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaybackOptionsCard(WidgetRef ref) {
    final backgroundPlayback = ref.watch(backgroundPlaybackProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.play_circle, color: Color(0xFFE67E22), size: 24),
                SizedBox(width: 12),
                Text(
                  'Playback Options',
                  style: TextStyle(
                    color: Color(0xFF2C3E50),
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            SwitchListTile(
              title: const Text('Background playback'),
              subtitle: const Text(
                'Continue playing audio when app is minimized',
              ),
              value: backgroundPlayback,
              onChanged: (value) {
                ref.read(backgroundPlaybackProvider.notifier).state = value;
              },
              activeColor: const Color(0xFF3498DB),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAudioSettingsCard(BuildContext context, WidgetRef ref) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.volume_up, color: Color(0xFF1ABC9C), size: 24),
                SizedBox(width: 12),
                Text(
                  'Audio Settings',
                  style: TextStyle(
                    color: Color(0xFF2C3E50),
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            ListTile(
              title: const Text('Equalizer'),
              subtitle: const Text('Adjust audio frequencies'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Opening Equalizer Settings...'),
                  ),
                );
              },
            ),

            ListTile(
              title: const Text('Audio effects'),
              subtitle: const Text('Configure audio enhancement'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Opening Audio Effects Settings...'),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStorageCard(WidgetRef ref) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.storage, color: Color(0xFFF39C12), size: 24),
                SizedBox(width: 12),
                Text(
                  'Storage & Cache',
                  style: TextStyle(
                    color: Color(0xFF2C3E50),
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            ListTile(
              title: const Text('Clear cache'),
              subtitle: const Text('Remove temporary files and thumbnails'),
              trailing: const Icon(Icons.delete_outline),
              onTap: () => _clearCache(),
            ),

            ListTile(
              title: const Text('Export settings'),
              subtitle: const Text('Backup your ShadowPlayer configuration'),
              trailing: const Icon(Icons.file_download),
              onTap: () => _exportSettings(),
            ),

            ListTile(
              title: const Text('Import settings'),
              subtitle: const Text('Restore configuration from backup'),
              trailing: const Icon(Icons.file_upload),
              onTap: () => _importSettings(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAboutCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.info, color: Color(0xFF34495E), size: 24),
                SizedBox(width: 12),
                Text(
                  'About ShadowPlayer',
                  style: TextStyle(
                    color: Color(0xFF2C3E50),
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            const Text(
              'ShadowPlayer is a comprehensive media player built for Shadow Suite. It provides advanced video and audio playback capabilities with a focus on performance and user experience.',
              style: TextStyle(
                color: Color(0xFF7F8C8D),
                fontSize: 14,
                height: 1.5,
              ),
            ),
            const SizedBox(height: 16),

            const Row(
              children: [
                Text(
                  'Version: ',
                  style: TextStyle(
                    color: Color(0xFF2C3E50),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '1.0.0',
                  style: TextStyle(color: Color(0xFF7F8C8D), fontSize: 14),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _addScanLocation(WidgetRef ref) {
    // Implement folder picker functionality
    final locations = ref.read(scanLocationsProvider);
    ref.read(scanLocationsProvider.notifier).state = [
      ...locations,
      '/storage/emulated/0/Downloads',
    ];
  }

  void _removeScanLocation(String location, WidgetRef ref) {
    final locations = ref.read(scanLocationsProvider);
    ref.read(scanLocationsProvider.notifier).state = locations
        .where((l) => l != location)
        .toList();
  }

  void _clearCache() {
    // Implement cache clearing functionality
    // In a real implementation, this would clear thumbnail cache,
    // temporary files, and other cached data
  }

  void _exportSettings() {
    // Implement settings export functionality
    // In a real implementation, this would export user preferences
    // to a JSON file that can be imported later
  }

  void _importSettings() {
    // Implement settings import functionality
    // In a real implementation, this would allow users to import
    // previously exported settings from a JSON file
  }
}
