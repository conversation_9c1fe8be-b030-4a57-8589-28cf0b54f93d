import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/note.dart';
import '../models/todo.dart';
import 'database_service.dart';

// Notification Service for Memo Suite reminders
class MemoNotificationService {
  static Timer? _reminderTimer;
  static final List<PendingNotification> _pendingNotifications = [];

  // Initialize the notification service
  static void initialize() {
    _startReminderTimer();
  }

  // Start the reminder timer that checks every minute
  static void _startReminderTimer() {
    _reminderTimer?.cancel();
    _reminderTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _checkForDueReminders();
    });
  }

  // Check for due reminders and show notifications
  static Future<void> _checkForDueReminders() async {
    final now = DateTime.now();
    
    try {
      // Check for scheduled notes
      final notes = await MemoSuiteDatabaseService.getAllNotes();
      for (final note in notes) {
        if (note.scheduledDate != null && 
            note.scheduledDate!.isBefore(now) &&
            !_isNotificationShown(note.id, 'note')) {
          _showNoteReminder(note);
          _markNotificationShown(note.id, 'note');
        }
      }

      // Check for due todos
      final todos = await MemoSuiteDatabaseService.getAllTodos();
      for (final todo in todos) {
        if (todo.dueDate != null && 
            todo.dueDate!.isBefore(now) &&
            todo.status != TodoStatus.done &&
            !_isNotificationShown(todo.id, 'todo')) {
          _showTodoReminder(todo);
          _markNotificationShown(todo.id, 'todo');
        }
      }
    } catch (e) {
      debugPrint('Error checking reminders: $e');
    }
  }

  // Show note reminder notification
  static void _showNoteReminder(Note note) {
    final notification = PendingNotification(
      id: note.id,
      type: 'note',
      title: 'Note Reminder',
      message: note.title,
      timestamp: DateTime.now(),
      data: note,
    );
    
    _pendingNotifications.add(notification);
    _notifyListeners();
  }

  // Show todo reminder notification
  static void _showTodoReminder(Todo todo) {
    final notification = PendingNotification(
      id: todo.id,
      type: 'todo',
      title: 'Todo Due',
      message: '${todo.title} is due now!',
      timestamp: DateTime.now(),
      data: todo,
    );
    
    _pendingNotifications.add(notification);
    _notifyListeners();
  }

  // Check if notification was already shown
  static bool _isNotificationShown(String id, String type) {
    return _shownNotifications.contains('${type}_$id');
  }

  // Mark notification as shown
  static void _markNotificationShown(String id, String type) {
    _shownNotifications.add('${type}_$id');
  }

  // Get pending notifications
  static List<PendingNotification> getPendingNotifications() {
    return List.from(_pendingNotifications);
  }

  // Dismiss notification
  static void dismissNotification(String id) {
    _pendingNotifications.removeWhere((notification) => notification.id == id);
    _notifyListeners();
  }

  // Clear all notifications
  static void clearAllNotifications() {
    _pendingNotifications.clear();
    _notifyListeners();
  }

  // Schedule a note reminder
  static Future<void> scheduleNoteReminder(Note note) async {
    if (note.scheduledDate != null) {
      // Remove from shown notifications so it can be shown again
      _shownNotifications.remove('note_${note.id}');
    }
  }

  // Schedule a todo reminder
  static Future<void> scheduleTodoReminder(Todo todo) async {
    if (todo.dueDate != null) {
      // Remove from shown notifications so it can be shown again
      _shownNotifications.remove('todo_${todo.id}');
    }
  }

  // Dispose the service
  static void dispose() {
    _reminderTimer?.cancel();
    _reminderTimer = null;
    _pendingNotifications.clear();
    _shownNotifications.clear();
  }

  // Private members
  static final Set<String> _shownNotifications = {};
  static final List<VoidCallback> _listeners = [];

  // Add listener for notification changes
  static void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }

  // Remove listener
  static void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }

  // Notify all listeners
  static void _notifyListeners() {
    for (final listener in _listeners) {
      listener();
    }
  }

  // Get upcoming reminders (next 24 hours)
  static Future<List<UpcomingReminder>> getUpcomingReminders() async {
    final now = DateTime.now();
    final tomorrow = now.add(const Duration(days: 1));
    final upcomingReminders = <UpcomingReminder>[];

    try {
      // Get scheduled notes
      final notes = await MemoSuiteDatabaseService.getAllNotes();
      for (final note in notes) {
        if (note.scheduledDate != null && 
            note.scheduledDate!.isAfter(now) &&
            note.scheduledDate!.isBefore(tomorrow)) {
          upcomingReminders.add(UpcomingReminder(
            id: note.id,
            type: 'note',
            title: note.title,
            scheduledTime: note.scheduledDate!,
            data: note,
          ));
        }
      }

      // Get due todos
      final todos = await MemoSuiteDatabaseService.getAllTodos();
      for (final todo in todos) {
        if (todo.dueDate != null && 
            todo.dueDate!.isAfter(now) &&
            todo.dueDate!.isBefore(tomorrow) &&
            todo.status != TodoStatus.done) {
          upcomingReminders.add(UpcomingReminder(
            id: todo.id,
            type: 'todo',
            title: todo.title,
            scheduledTime: todo.dueDate!,
            data: todo,
          ));
        }
      }

      // Sort by scheduled time
      upcomingReminders.sort((a, b) => a.scheduledTime.compareTo(b.scheduledTime));
    } catch (e) {
      debugPrint('Error getting upcoming reminders: $e');
    }

    return upcomingReminders;
  }
}

// Notification data models
class PendingNotification {
  final String id;
  final String type;
  final String title;
  final String message;
  final DateTime timestamp;
  final dynamic data;

  PendingNotification({
    required this.id,
    required this.type,
    required this.title,
    required this.message,
    required this.timestamp,
    required this.data,
  });
}

class UpcomingReminder {
  final String id;
  final String type;
  final String title;
  final DateTime scheduledTime;
  final dynamic data;

  UpcomingReminder({
    required this.id,
    required this.type,
    required this.title,
    required this.scheduledTime,
    required this.data,
  });
}

// Providers for notification state management
final pendingNotificationsProvider = StateNotifierProvider<PendingNotificationsNotifier, List<PendingNotification>>((ref) {
  return PendingNotificationsNotifier();
});

final upcomingRemindersProvider = FutureProvider<List<UpcomingReminder>>((ref) {
  return MemoNotificationService.getUpcomingReminders();
});

class PendingNotificationsNotifier extends StateNotifier<List<PendingNotification>> {
  PendingNotificationsNotifier() : super([]) {
    MemoNotificationService.addListener(_updateState);
    _updateState();
  }

  void _updateState() {
    state = MemoNotificationService.getPendingNotifications();
  }

  void dismissNotification(String id) {
    MemoNotificationService.dismissNotification(id);
  }

  void clearAll() {
    MemoNotificationService.clearAllNotifications();
  }

  @override
  void dispose() {
    MemoNotificationService.removeListener(_updateState);
    super.dispose();
  }
}

// Notification widget for displaying reminders
class NotificationBanner extends ConsumerWidget {
  const NotificationBanner({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notifications = ref.watch(pendingNotificationsProvider);

    if (notifications.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.all(8),
      child: Column(
        children: notifications.map((notification) {
          return Card(
            color: Colors.orange.shade50,
            child: ListTile(
              leading: Icon(
                notification.type == 'note' ? Icons.note : Icons.task_alt,
                color: Colors.orange.shade700,
              ),
              title: Text(notification.title),
              subtitle: Text(notification.message),
              trailing: IconButton(
                icon: const Icon(Icons.close),
                onPressed: () {
                  ref.read(pendingNotificationsProvider.notifier).dismissNotification(notification.id);
                },
              ),
              onTap: () {
                // Navigate to the item
                _navigateToItem(context, ref, notification);
                ref.read(pendingNotificationsProvider.notifier).dismissNotification(notification.id);
              },
            ),
          );
        }).toList(),
      ),
    );
  }

  void _navigateToItem(BuildContext context, WidgetRef ref, PendingNotification notification) {
    if (notification.type == 'note' && notification.data is Note) {
      // Navigate to note
      // Implementation depends on your navigation setup
    } else if (notification.type == 'todo' && notification.data is Todo) {
      // Navigate to todo
      // Implementation depends on your navigation setup
    }
  }
}
