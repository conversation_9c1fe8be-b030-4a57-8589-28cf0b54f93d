import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_component.dart';
import '../services/cell_binding_service.dart';

// Cell Binding Manager for isolated component connections
class CellBindingManager extends ConsumerStatefulWidget {
  final UIComponent component;
  final Function(UIComponent) onComponentUpdated;

  const CellBindingManager({
    super.key,
    required this.component,
    required this.onComponentUpdated,
  });

  @override
  ConsumerState<CellBindingManager> createState() => _CellBindingManagerState();
}

class _CellBindingManagerState extends ConsumerState<CellBindingManager> {
  final _cellAddressController = TextEditingController();
  final _propertyController = TextEditingController();
  final _transformerController = TextEditingController();
  BindingDirection _selectedDirection = BindingDirection.bidirectional;
  String? _selectedProperty;

  final List<String> _availableProperties = [
    'value',
    'text',
    'enabled',
    'visible',
    'color',
    'backgroundColor',
    'fontSize',
    'width',
    'height',
    'x',
    'y',
  ];

  @override
  void dispose() {
    _cellAddressController.dispose();
    _propertyController.dispose();
    _transformerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bindingService = ref.watch(cellBindingServiceProvider);
    final bindings = bindingService.getComponentBindings(widget.component.id);
    final isolation = bindingService.getComponentIsolation(widget.component.id);

    return Dialog(
      child: Container(
        width: 600,
        height: 700,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(Icons.link, color: Colors.blue[700]),
                const SizedBox(width: 12),
                Text(
                  'Cell Binding Manager',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Component: ${widget.component.label} (${widget.component.type.name})',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),

            // Component Isolation Settings
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Component Isolation',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    SwitchListTile(
                      title: const Text('Enable Component Isolation'),
                      subtitle: const Text('Prevent this component from affecting others'),
                      value: isolation != null,
                      onChanged: (value) {
                        if (value) {
                          bindingService.createIsolation(widget.component.id);
                        } else {
                          bindingService.removeIsolation(widget.component.id);
                        }
                        setState(() {});
                      },
                    ),
                    if (isolation != null) ...[
                      const SizedBox(height: 8),
                      SwitchListTile(
                        title: const Text('Prevent Cascading Updates'),
                        subtitle: const Text('Block updates from propagating to other components'),
                        value: isolation.preventCascading,
                        onChanged: (value) {
                          bindingService.updateIsolation(
                            widget.component.id,
                            preventCascading: value,
                          );
                          setState(() {});
                        },
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Existing Bindings
            Text(
              'Current Bindings',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              flex: 2,
              child: Card(
                child: bindings.isEmpty
                    ? const Center(
                        child: Text(
                          'No bindings configured',
                          style: TextStyle(color: Colors.grey),
                        ),
                      )
                    : ListView.builder(
                        itemCount: bindings.length,
                        itemBuilder: (context, index) {
                          final binding = bindings[index];
                          return ListTile(
                            leading: Icon(
                              _getDirectionIcon(binding.direction),
                              color: binding.isActive ? Colors.green : Colors.grey,
                            ),
                            title: Text('${binding.property} ↔ ${binding.cellAddress}'),
                            subtitle: Text(
                              'Direction: ${binding.direction.name}${binding.transformer != null ? ' | Transform: ${binding.transformer}' : ''}',
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Switch(
                                  value: binding.isActive,
                                  onChanged: (value) {
                                    bindingService.updateBinding(
                                      binding.id,
                                      isActive: value,
                                    );
                                    setState(() {});
                                  },
                                ),
                                IconButton(
                                  onPressed: () => _editBinding(binding),
                                  icon: const Icon(Icons.edit, size: 20),
                                ),
                                IconButton(
                                  onPressed: () {
                                    bindingService.removeBinding(binding.id);
                                    setState(() {});
                                  },
                                  icon: const Icon(Icons.delete, size: 20, color: Colors.red),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
              ),
            ),
            const SizedBox(height: 16),

            // Add New Binding
            Text(
              'Add New Binding',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              flex: 1,
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              value: _selectedProperty,
                              decoration: const InputDecoration(
                                labelText: 'Property',
                                border: OutlineInputBorder(),
                              ),
                              items: _availableProperties.map((property) {
                                return DropdownMenuItem(
                                  value: property,
                                  child: Text(property),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedProperty = value;
                                });
                              },
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: TextField(
                              controller: _cellAddressController,
                              decoration: const InputDecoration(
                                labelText: 'Cell Address (e.g., A1)',
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: DropdownButtonFormField<BindingDirection>(
                              value: _selectedDirection,
                              decoration: const InputDecoration(
                                labelText: 'Direction',
                                border: OutlineInputBorder(),
                              ),
                              items: BindingDirection.values.map((direction) {
                                return DropdownMenuItem(
                                  value: direction,
                                  child: Row(
                                    children: [
                                      Icon(_getDirectionIcon(direction), size: 16),
                                      const SizedBox(width: 8),
                                      Text(direction.name),
                                    ],
                                  ),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedDirection = value!;
                                });
                              },
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: TextField(
                              controller: _transformerController,
                              decoration: const InputDecoration(
                                labelText: 'Transformer (optional)',
                                hintText: 'string, number, bool',
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: _canAddBinding() ? _addBinding : null,
                          icon: const Icon(Icons.add),
                          label: const Text('Add Binding'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue[700],
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Action Buttons
            Row(
              children: [
                TextButton(
                  onPressed: () {
                    final bindingService = ref.read(cellBindingServiceProvider);
                    bindingService.clearIsolatedState(widget.component.id);
                    setState(() {});
                  },
                  child: const Text('Clear Isolated State'),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[700],
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Done'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  IconData _getDirectionIcon(BindingDirection direction) {
    switch (direction) {
      case BindingDirection.input:
        return Icons.arrow_downward;
      case BindingDirection.output:
        return Icons.arrow_upward;
      case BindingDirection.bidirectional:
        return Icons.sync_alt;
    }
  }

  bool _canAddBinding() {
    return _selectedProperty != null && 
           _cellAddressController.text.isNotEmpty &&
           _isValidCellAddress(_cellAddressController.text);
  }

  bool _isValidCellAddress(String address) {
    // Simple validation for cell address format (e.g., A1, B2, AA10)
    final regex = RegExp(r'^[A-Z]+[0-9]+$');
    return regex.hasMatch(address.toUpperCase());
  }

  void _addBinding() {
    if (!_canAddBinding()) return;

    final bindingService = ref.read(cellBindingServiceProvider);
    bindingService.createBinding(
      componentId: widget.component.id,
      cellAddress: _cellAddressController.text.toUpperCase(),
      property: _selectedProperty!,
      direction: _selectedDirection,
      transformer: _transformerController.text.isEmpty ? null : _transformerController.text,
    );

    // Clear form
    _cellAddressController.clear();
    _transformerController.clear();
    _selectedProperty = null;
    _selectedDirection = BindingDirection.bidirectional;

    setState(() {});
  }

  void _editBinding(CellBinding binding) {
    showDialog(
      context: context,
      builder: (context) => _EditBindingDialog(
        binding: binding,
        onSave: (updatedBinding) {
          final bindingService = ref.read(cellBindingServiceProvider);
          bindingService.updateBinding(
            binding.id,
            cellAddress: updatedBinding.cellAddress,
            property: updatedBinding.property,
            direction: updatedBinding.direction,
            transformer: updatedBinding.transformer,
          );
          setState(() {});
        },
      ),
    );
  }
}

class _EditBindingDialog extends StatefulWidget {
  final CellBinding binding;
  final Function(CellBinding) onSave;

  const _EditBindingDialog({
    required this.binding,
    required this.onSave,
  });

  @override
  State<_EditBindingDialog> createState() => _EditBindingDialogState();
}

class _EditBindingDialogState extends State<_EditBindingDialog> {
  late TextEditingController _cellController;
  late TextEditingController _propertyController;
  late TextEditingController _transformerController;
  late BindingDirection _direction;

  @override
  void initState() {
    super.initState();
    _cellController = TextEditingController(text: widget.binding.cellAddress);
    _propertyController = TextEditingController(text: widget.binding.property);
    _transformerController = TextEditingController(text: widget.binding.transformer ?? '');
    _direction = widget.binding.direction;
  }

  @override
  void dispose() {
    _cellController.dispose();
    _propertyController.dispose();
    _transformerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit Binding'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _cellController,
            decoration: const InputDecoration(
              labelText: 'Cell Address',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _propertyController,
            decoration: const InputDecoration(
              labelText: 'Property',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<BindingDirection>(
            value: _direction,
            decoration: const InputDecoration(
              labelText: 'Direction',
              border: OutlineInputBorder(),
            ),
            items: BindingDirection.values.map((direction) {
              return DropdownMenuItem(
                value: direction,
                child: Text(direction.name),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _direction = value!;
              });
            },
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _transformerController,
            decoration: const InputDecoration(
              labelText: 'Transformer (optional)',
              border: OutlineInputBorder(),
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final updatedBinding = widget.binding.copyWith(
              cellAddress: _cellController.text,
              property: _propertyController.text,
              direction: _direction,
              transformer: _transformerController.text.isEmpty ? null : _transformerController.text,
            );
            widget.onSave(updatedBinding);
            Navigator.of(context).pop();
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
}
