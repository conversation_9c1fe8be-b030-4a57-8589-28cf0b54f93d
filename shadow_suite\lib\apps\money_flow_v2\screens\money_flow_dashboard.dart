import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/money_flow_models.dart';
import '../services/money_flow_providers.dart';

class MoneyFlowDashboard extends ConsumerWidget {
  const MoneyFlowDashboard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildOverviewCards(ref),
          const SizedBox(height: 24),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 2,
                child: Column(
                  children: [
                    _buildAccountsOverview(ref),
                    const SizedBox(height: 24),
                    _buildBudgetsOverview(ref),
                  ],
                ),
              ),
              const SizedBox(width: 24),
              Expanded(
                child: Column(
                  children: [
                    _buildRecentTransactions(ref),
                    const SizedBox(height: 24),
                    _buildSpendingChart(ref),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewCards(WidgetRef ref) {
    final statisticsAsync = ref.watch(moneyFlowStatisticsProvider);

    return statisticsAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(child: Text('Error: $error')),
      data: (stats) => Row(
        children: [
          Expanded(
            child: _buildOverviewCard(
              'Total Balance',
              '\$${stats['totalBalance']?.toStringAsFixed(2) ?? '0.00'}',
              Icons.account_balance_wallet,
              const Color(0xFF27AE60),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildOverviewCard(
              'Total Assets',
              '\$${stats['totalAssets']?.toStringAsFixed(2) ?? '0.00'}',
              Icons.trending_up,
              const Color(0xFF3498DB),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildOverviewCard(
              'Total Liabilities',
              '\$${stats['totalLiabilities']?.toStringAsFixed(2) ?? '0.00'}',
              Icons.trending_down,
              const Color(0xFFE74C3C),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildOverviewCard(
              'Accounts',
              '${stats['totalAccounts'] ?? 0}',
              Icons.account_balance,
              const Color(0xFF9B59B6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
              Icon(Icons.more_vert, color: Colors.grey[400], size: 16),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            value,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 4),
          Text(title, style: TextStyle(fontSize: 14, color: Colors.grey[600])),
        ],
      ),
    );
  }

  Widget _buildAccountsOverview(WidgetRef ref) {
    final accountsAsync = ref.watch(moneyFlowAccountsProvider);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                const Text(
                  'Accounts Overview',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () =>
                      ref.read(moneyFlowCurrentScreenProvider.notifier).state =
                          MoneyFlowScreen.accounts,
                  child: const Text('View All'),
                ),
              ],
            ),
          ),
          accountsAsync.when(
            loading: () => const Padding(
              padding: EdgeInsets.all(20),
              child: Center(child: CircularProgressIndicator()),
            ),
            error: (error, stack) => Padding(
              padding: const EdgeInsets.all(20),
              child: Center(child: Text('Error: $error')),
            ),
            data: (accounts) => Column(
              children: accounts
                  .take(3)
                  .map((account) => _buildAccountItem(account))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountItem(MoneyAccount account) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: const BoxDecoration(
        border: Border(top: BorderSide(color: Color(0xFFF1F2F6))),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Color(
                int.parse(account.color.replaceFirst('#', '0xFF')),
              ).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getAccountIcon(account.type),
              color: Color(int.parse(account.color.replaceFirst('#', '0xFF'))),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  account.name,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                Text(
                  '${account.bankName} • ${account.accountNumber}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Text(
            '\$${account.balance.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: account.balance >= 0
                  ? const Color(0xFF27AE60)
                  : const Color(0xFFE74C3C),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetsOverview(WidgetRef ref) {
    final budgetsAsync = ref.watch(moneyFlowBudgetsProvider);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                const Text(
                  'Budget Progress',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () =>
                      ref.read(moneyFlowCurrentScreenProvider.notifier).state =
                          MoneyFlowScreen.budgets,
                  child: const Text('View All'),
                ),
              ],
            ),
          ),
          budgetsAsync.when(
            loading: () => const Padding(
              padding: EdgeInsets.all(20),
              child: Center(child: CircularProgressIndicator()),
            ),
            error: (error, stack) => Padding(
              padding: const EdgeInsets.all(20),
              child: Center(child: Text('Error: $error')),
            ),
            data: (budgets) => Column(
              children: budgets
                  .take(3)
                  .map((budget) => _buildBudgetItem(budget))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetItem(MoneyBudget budget) {
    final percentage = budget.percentageUsed.clamp(0.0, 100.0);
    final isOverBudget = budget.isOverBudget;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: const BoxDecoration(
        border: Border(top: BorderSide(color: Color(0xFFF1F2F6))),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  budget.name,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF2C3E50),
                  ),
                ),
              ),
              Text(
                '\$${budget.spent.toStringAsFixed(2)} / \$${budget.amount.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 12,
                  color: isOverBudget
                      ? const Color(0xFFE74C3C)
                      : Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(
              isOverBudget
                  ? const Color(0xFFE74C3C)
                  : Color(int.parse(budget.color.replaceFirst('#', '0xFF'))),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '${percentage.toStringAsFixed(1)}% used',
            style: TextStyle(
              fontSize: 11,
              color: isOverBudget ? const Color(0xFFE74C3C) : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentTransactions(WidgetRef ref) {
    final transactionsAsync = ref.watch(moneyFlowRecentTransactionsProvider);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                const Text(
                  'Recent Transactions',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () =>
                      ref.read(moneyFlowCurrentScreenProvider.notifier).state =
                          MoneyFlowScreen.transactions,
                  child: const Text('View All'),
                ),
              ],
            ),
          ),
          transactionsAsync.when(
            loading: () => const Padding(
              padding: EdgeInsets.all(20),
              child: Center(child: CircularProgressIndicator()),
            ),
            error: (error, stack) => Padding(
              padding: const EdgeInsets.all(20),
              child: Center(child: Text('Error: $error')),
            ),
            data: (transactions) => Column(
              children: transactions
                  .take(5)
                  .map((transaction) => _buildTransactionItem(transaction))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionItem(MoneyTransactionV2 transaction) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: const BoxDecoration(
        border: Border(top: BorderSide(color: Color(0xFFF1F2F6))),
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: _getTransactionColor(
                transaction.type,
              ).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              _getTransactionIcon(transaction.type),
              color: _getTransactionColor(transaction.type),
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.description,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                Text(
                  transaction.category,
                  style: TextStyle(fontSize: 11, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Text(
            '${transaction.type == TransactionType.expense ? '-' : '+'}\$${transaction.amount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.bold,
              color: _getTransactionColor(transaction.type),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpendingChart(WidgetRef ref) {
    final spendingAsync = ref.watch(moneyFlowMonthlySpendingProvider);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.all(20),
            child: Text(
              'Monthly Spending',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2C3E50),
              ),
            ),
          ),
          spendingAsync.when(
            loading: () => const Padding(
              padding: EdgeInsets.all(20),
              child: Center(child: CircularProgressIndicator()),
            ),
            error: (error, stack) => Padding(
              padding: const EdgeInsets.all(20),
              child: Center(child: Text('Error: $error')),
            ),
            data: (spending) => spending.isEmpty
                ? const Padding(
                    padding: EdgeInsets.all(20),
                    child: Center(child: Text('No spending data available')),
                  )
                : Column(
                    children: spending.entries
                        .map(
                          (entry) => _buildSpendingItem(entry.key, entry.value),
                        )
                        .toList(),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpendingItem(String category, double amount) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Text(
              category,
              style: const TextStyle(fontSize: 13, color: Color(0xFF2C3E50)),
            ),
          ),
          Text(
            '\$${amount.toStringAsFixed(2)}',
            style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: Color(0xFF2C3E50),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getAccountIcon(AccountType type) {
    switch (type) {
      case AccountType.checking:
        return Icons.account_balance;
      case AccountType.savings:
        return Icons.savings;
      case AccountType.credit:
        return Icons.credit_card;
      case AccountType.investment:
        return Icons.trending_up;
      case AccountType.cash:
        return Icons.money;
    }
  }

  IconData _getTransactionIcon(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return Icons.add;
      case TransactionType.expense:
        return Icons.remove;
      case TransactionType.transfer:
        return Icons.swap_horiz;
    }
  }

  Color _getTransactionColor(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return const Color(0xFF27AE60);
      case TransactionType.expense:
        return const Color(0xFFE74C3C);
      case TransactionType.transfer:
        return const Color(0xFF3498DB);
    }
  }
}
