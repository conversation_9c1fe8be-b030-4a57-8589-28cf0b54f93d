import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import '../services/enhanced_settings_service.dart';

// Enhanced Settings Screen with Live Preview and Component-Specific Settings
class EnhancedSettingsScreen extends ConsumerStatefulWidget {
  const EnhancedSettingsScreen({super.key});

  @override
  ConsumerState<EnhancedSettingsScreen> createState() => _EnhancedSettingsScreenState();
}

class _EnhancedSettingsScreenState extends ConsumerState<EnhancedSettingsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _showPreview = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 8, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final settings = ref.watch(enhancedSettingsServiceProvider);
    final theme = ref.watch(enhancedThemeProvider);

    return Theme(
      data: theme,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Settings'),
          backgroundColor: settings.primaryColor,
          foregroundColor: Colors.white,
          actions: [
            IconButton(
              icon: Icon(_showPreview ? Icons.visibility_off : Icons.visibility),
              onPressed: () {
                setState(() {
                  _showPreview = !_showPreview;
                });
              },
              tooltip: _showPreview ? 'Hide Preview' : 'Show Preview',
            ),
            if (settings.hasUnsavedChanges) ...[
              TextButton(
                onPressed: () async {
                  await settings.resetToDefaults();
                  if (mounted && context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Settings reset to defaults')),
                    );
                  }
                },
                child: const Text('Reset', style: TextStyle(color: Colors.white)),
              ),
              ElevatedButton(
                onPressed: () async {
                  await settings.saveSettings();
                  if (mounted && context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Settings saved successfully')),
                    );
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: settings.primaryColor,
                ),
                child: const Text('Save'),
              ),
            ],
          ],
          bottom: TabBar(
            controller: _tabController,
            isScrollable: true,
            indicatorColor: Colors.white,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            tabs: const [
              Tab(icon: Icon(Icons.palette), text: 'Appearance'),
              Tab(icon: Icon(Icons.save), text: 'Auto-Save'),
              Tab(icon: Icon(Icons.speed), text: 'Performance'),
              Tab(icon: Icon(Icons.security), text: 'Privacy'),
              Tab(icon: Icon(Icons.accessibility), text: 'Accessibility'),
              Tab(icon: Icon(Icons.notifications), text: 'Notifications'),
              Tab(icon: Icon(Icons.language), text: 'Language'),
              Tab(icon: Icon(Icons.storage), text: 'Data'),
            ],
          ),
        ),
        body: Row(
          children: [
            Expanded(
              flex: _showPreview ? 2 : 1,
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildAppearanceTab(settings),
                  _buildAutoSaveTab(settings),
                  _buildPerformanceTab(settings),
                  _buildPrivacyTab(settings),
                  _buildAccessibilityTab(settings),
                  _buildNotificationsTab(settings),
                  _buildLanguageTab(settings),
                  _buildDataTab(settings),
                ],
              ),
            ),
            if (_showPreview) ...[
              const VerticalDivider(width: 1),
              Expanded(
                flex: 1,
                child: _buildPreviewPanel(settings, theme),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAppearanceTab(EnhancedSettingsService settings) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Theme'),
        _buildThemeModeSelector(settings),
        const SizedBox(height: 16),
        
        _buildSectionHeader('Colors'),
        _buildColorPicker('Primary Color', settings.primaryColor, (color) {
          settings.updateSetting('primaryColor', color, temporary: true);
        }),
        _buildColorPicker('Accent Color', settings.accentColor, (color) {
          settings.updateSetting('accentColor', color, temporary: true);
        }),
        const SizedBox(height: 16),
        
        _buildSectionHeader('Typography'),
        _buildSlider(
          'Font Size',
          settings.fontSize,
          8.0,
          24.0,
          (value) => settings.updateSetting('fontSize', value, temporary: true),
        ),
        _buildDropdown(
          'Font Family',
          settings.fontFamily,
          ['Roboto', 'Arial', 'Times New Roman', 'Courier New', 'Helvetica'],
          (value) => settings.updateSetting('fontFamily', value, temporary: true),
        ),
        const SizedBox(height: 16),
        
        _buildSectionHeader('Visual Effects'),
        _buildSlider(
          'Border Radius',
          settings.borderRadius,
          0.0,
          20.0,
          (value) => settings.updateSetting('borderRadius', value, temporary: true),
        ),
        _buildSlider(
          'Elevation',
          settings.elevation,
          0.0,
          10.0,
          (value) => settings.updateSetting('elevation', value, temporary: true),
        ),
        _buildSwitch(
          'Animations',
          settings.animations,
          (value) => settings.updateSetting('animations', value, temporary: true),
        ),
        if (settings.animations)
          _buildSlider(
            'Animation Speed',
            settings.animationSpeed,
            0.5,
            2.0,
            (value) => settings.updateSetting('animationSpeed', value, temporary: true),
          ),
        _buildSlider(
          'Opacity',
          settings.opacity,
          0.5,
          1.0,
          (value) => settings.updateSetting('opacity', value, temporary: true),
        ),
      ],
    );
  }

  Widget _buildAutoSaveTab(EnhancedSettingsService settings) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Auto-Save Settings'),
        _buildSwitch(
          'Enable Auto-Save',
          settings.autoSave,
          (value) => settings.updateSetting('autoSave', value, temporary: true),
        ),
        if (settings.autoSave) ...[
          _buildSlider(
            'Auto-Save Interval (seconds)',
            settings.autoSaveInterval.toDouble(),
            10.0,
            300.0,
            (value) => settings.updateSetting('autoSaveInterval', value.round(), temporary: true),
            divisions: 29,
          ),
          _buildSwitch(
            'Save Drafts',
            settings.saveDrafts,
            (value) => settings.updateSetting('saveDrafts', value, temporary: true),
          ),
        ],
        const SizedBox(height: 16),
        
        _buildSectionHeader('Backup & Sync'),
        _buildSwitch(
          'Cloud Sync',
          settings.cloudSync,
          (value) => settings.updateSetting('cloudSync', value, temporary: true),
        ),
        _buildDropdown(
          'Backup Frequency',
          settings.backupFrequency,
          ['never', 'daily', 'weekly', 'monthly'],
          (value) => settings.updateSetting('backupFrequency', value, temporary: true),
        ),
        const SizedBox(height: 16),
        
        _buildSectionHeader('Save Location'),
        ListTile(
          title: const Text('Data Save Location'),
          subtitle: Text(settings.saveLocation),
          trailing: ElevatedButton(
            onPressed: () async {
              final result = await FilePicker.platform.getDirectoryPath();
              if (result != null) {
                settings.updateSetting('saveLocation', result, temporary: true);
              }
            },
            child: const Text('Change'),
          ),
        ),
      ],
    );
  }

  Widget _buildPerformanceTab(EnhancedSettingsService settings) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Memory Optimization'),
        _buildSwitch(
          'Lazy Loading',
          settings.lazyLoading,
          (value) => settings.updateSetting('lazyLoading', value, temporary: true),
        ),
        _buildSwitch(
          'Optimize Memory',
          settings.optimizeMemory,
          (value) => settings.updateSetting('optimizeMemory', value, temporary: true),
        ),
        _buildSlider(
          'Max Cache Size (MB)',
          settings.maxCacheSize.toDouble(),
          50.0,
          500.0,
          (value) => settings.updateSetting('maxCacheSize', value.round(), temporary: true),
          divisions: 45,
        ),
        const SizedBox(height: 16),
        
        _buildSectionHeader('Processing'),
        _buildSwitch(
          'Hardware Acceleration',
          settings.hardwareAcceleration,
          (value) => settings.updateSetting('hardwareAcceleration', value, temporary: true),
        ),
        _buildSlider(
          'Max Threads',
          settings.maxThreads.toDouble(),
          1.0,
          8.0,
          (value) => settings.updateSetting('maxThreads', value.round(), temporary: true),
          divisions: 7,
        ),
        _buildSwitch(
          'Preload Data',
          settings.preloadData,
          (value) => settings.updateSetting('preloadData', value, temporary: true),
        ),
      ],
    );
  }

  Widget _buildPrivacyTab(EnhancedSettingsService settings) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Analytics & Reporting'),
        _buildSwitch(
          'Analytics Enabled',
          settings.analyticsEnabled,
          (value) => settings.updateSetting('analyticsEnabled', value, temporary: true),
        ),
        _buildSwitch(
          'Crash Reporting',
          settings.crashReporting,
          (value) => settings.updateSetting('crashReporting', value, temporary: true),
        ),
        const SizedBox(height: 16),
        
        _buildSectionHeader('Security'),
        _buildSwitch(
          'Biometric Authentication',
          settings.biometricAuth,
          (value) => settings.updateSetting('biometricAuth', value, temporary: true),
        ),
        _buildSwitch(
          'Encrypt Data',
          settings.encryptData,
          (value) => settings.updateSetting('encryptData', value, temporary: true),
        ),
        _buildSwitch(
          'Auto Lock',
          settings.autoLock,
          (value) => settings.updateSetting('autoLock', value, temporary: true),
        ),
        _buildSlider(
          'Session Timeout (minutes)',
          settings.sessionTimeout.toDouble(),
          5.0,
          120.0,
          (value) => settings.updateSetting('sessionTimeout', value.round(), temporary: true),
          divisions: 23,
        ),
      ],
    );
  }

  Widget _buildAccessibilityTab(EnhancedSettingsService settings) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Visual Accessibility'),
        _buildSwitch(
          'High Contrast',
          settings.highContrast,
          (value) => settings.updateSetting('highContrast', value, temporary: true),
        ),
        _buildSwitch(
          'High Contrast Text',
          settings.highContrastText,
          (value) => settings.updateSetting('highContrastText', value, temporary: true),
        ),
        _buildSlider(
          'Text Scale',
          settings.textScale,
          0.8,
          2.0,
          (value) => settings.updateSetting('textScale', value, temporary: true),
        ),
        _buildSwitch(
          'Large Buttons',
          settings.largeButtons,
          (value) => settings.updateSetting('largeButtons', value, temporary: true),
        ),
        const SizedBox(height: 16),
        
        _buildSectionHeader('Motion & Interaction'),
        _buildSwitch(
          'Reduce Motion',
          settings.reduceMotion,
          (value) => settings.updateSetting('reduceMotion', value, temporary: true),
        ),
        const SizedBox(height: 16),
        
        _buildSectionHeader('Assistive Technology'),
        _buildSwitch(
          'Screen Reader Support',
          settings.screenReader,
          (value) => settings.updateSetting('screenReader', value, temporary: true),
        ),
        _buildSwitch(
          'Voice Commands',
          settings.voiceCommands,
          (value) => settings.updateSetting('voiceCommands', value, temporary: true),
        ),
      ],
    );
  }

  Widget _buildNotificationsTab(EnhancedSettingsService settings) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Notification Types'),
        _buildSwitch(
          'Push Notifications',
          settings.pushNotifications,
          (value) => settings.updateSetting('pushNotifications', value, temporary: true),
        ),
        _buildSwitch(
          'Email Notifications',
          settings.emailNotifications,
          (value) => settings.updateSetting('emailNotifications', value, temporary: true),
        ),
        const SizedBox(height: 16),

        _buildSectionHeader('Sound & Vibration'),
        _buildSwitch(
          'Sound Enabled',
          settings.soundEnabled,
          (value) => settings.updateSetting('soundEnabled', value, temporary: true),
        ),
        _buildSwitch(
          'Vibration Enabled',
          settings.vibrationEnabled,
          (value) => settings.updateSetting('vibrationEnabled', value, temporary: true),
        ),
        _buildDropdown(
          'Notification Sound',
          settings.notificationSound,
          ['default', 'chime', 'bell', 'alert', 'none'],
          (value) => settings.updateSetting('notificationSound', value, temporary: true),
        ),
      ],
    );
  }

  Widget _buildLanguageTab(EnhancedSettingsService settings) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Language & Region'),
        _buildDropdown(
          'Language',
          settings.language,
          ['en', 'ar', 'es', 'fr', 'de', 'zh', 'ja', 'ko'],
          (value) => settings.updateSetting('language', value, temporary: true),
        ),
        _buildDropdown(
          'Region',
          settings.region,
          ['US', 'UK', 'CA', 'AU', 'DE', 'FR', 'JP', 'CN'],
          (value) => settings.updateSetting('region', value, temporary: true),
        ),
        const SizedBox(height: 16),

        _buildSectionHeader('Formats'),
        _buildDropdown(
          'Date Format',
          settings.dateFormat,
          ['MM/dd/yyyy', 'dd/MM/yyyy', 'yyyy-MM-dd', 'dd-MM-yyyy'],
          (value) => settings.updateSetting('dateFormat', value, temporary: true),
        ),
        _buildDropdown(
          'Time Format',
          settings.timeFormat,
          ['12h', '24h'],
          (value) => settings.updateSetting('timeFormat', value, temporary: true),
        ),
        _buildDropdown(
          'Currency',
          settings.currency,
          ['USD', 'EUR', 'GBP', 'JPY', 'CNY', 'CAD', 'AUD'],
          (value) => settings.updateSetting('currency', value, temporary: true),
        ),
      ],
    );
  }

  Widget _buildDataTab(EnhancedSettingsService settings) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Data Management'),
        ListTile(
          title: const Text('Export Settings'),
          subtitle: const Text('Save your settings to a file'),
          trailing: ElevatedButton(
            onPressed: () async {
              await settings.exportSettings();
              // In a real app, you would save this to a file
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Settings exported successfully')),
                );
              }
            },
            child: const Text('Export'),
          ),
        ),
        ListTile(
          title: const Text('Import Settings'),
          subtitle: const Text('Load settings from a file'),
          trailing: ElevatedButton(
            onPressed: () async {
              final result = await FilePicker.platform.pickFiles(
                type: FileType.custom,
                allowedExtensions: ['json'],
              );
              if (result != null && result.files.single.path != null) {
                // In a real app, you would read the file content
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Settings imported successfully')),
                  );
                }
              }
            },
            child: const Text('Import'),
          ),
        ),
        const Divider(),
        ListTile(
          title: const Text('Clear All Data'),
          subtitle: const Text('Remove all app data and reset to defaults'),
          trailing: ElevatedButton(
            onPressed: () async {
              final confirmed = await showDialog<bool>(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Clear All Data'),
                  content: const Text(
                    'This will permanently delete all your data and reset the app to defaults. This action cannot be undone.',
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      child: const Text('Cancel'),
                    ),
                    ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                      child: const Text('Clear All Data'),
                    ),
                  ],
                ),
              );

              if (confirmed == true) {
                await settings.clearAllData();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('All data cleared successfully')),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Clear All Data'),
          ),
        ),
      ],
    );
  }

  Widget _buildPreviewPanel(EnhancedSettingsService settings, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Live Preview',
            style: theme.textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Sample card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Sample Card',
                            style: theme.textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'This is how your content will look with the current settings.',
                            style: theme.textTheme.bodyMedium,
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Sample buttons
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {},
                          child: const Text('Primary Button'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {},
                          child: const Text('Secondary'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Sample text styles
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Headline Large', style: theme.textTheme.headlineLarge),
                      Text('Headline Medium', style: theme.textTheme.headlineMedium),
                      Text('Headline Small', style: theme.textTheme.headlineSmall),
                      Text('Body Large', style: theme.textTheme.bodyLarge),
                      Text('Body Medium', style: theme.textTheme.bodyMedium),
                      Text('Body Small', style: theme.textTheme.bodySmall),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods for building UI components
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.blue,
        ),
      ),
    );
  }

  Widget _buildSwitch(String title, bool value, ValueChanged<bool> onChanged) {
    return SwitchListTile(
      title: Text(title),
      value: value,
      onChanged: onChanged,
    );
  }

  Widget _buildSlider(
    String title,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged, {
    int? divisions,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: divisions,
          label: value.toStringAsFixed(1),
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildDropdown<T>(
    String title,
    T value,
    List<T> items,
    ValueChanged<T?> onChanged,
  ) {
    return ListTile(
      title: Text(title),
      trailing: DropdownButton<T>(
        value: value,
        items: items.map((item) {
          return DropdownMenuItem<T>(
            value: item,
            child: Text(item.toString()),
          );
        }).toList(),
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildColorPicker(String title, Color color, ValueChanged<Color> onChanged) {
    return ListTile(
      title: Text(title),
      trailing: GestureDetector(
        onTap: () {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Text('Select $title'),
              content: SingleChildScrollView(
                child: _buildSimpleColorPicker(color, onChanged),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Done'),
                ),
              ],
            ),
          );
        },
        child: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey),
          ),
        ),
      ),
    );
  }

  Widget _buildThemeModeSelector(EnhancedSettingsService settings) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Theme Mode'),
        RadioListTile<ThemeMode>(
          title: const Text('System'),
          value: ThemeMode.system,
          groupValue: settings.themeMode,
          onChanged: (value) {
            if (value != null) {
              settings.updateSetting('themeMode', value, temporary: true);
            }
          },
        ),
        RadioListTile<ThemeMode>(
          title: const Text('Light'),
          value: ThemeMode.light,
          groupValue: settings.themeMode,
          onChanged: (value) {
            if (value != null) {
              settings.updateSetting('themeMode', value, temporary: true);
            }
          },
        ),
        RadioListTile<ThemeMode>(
          title: const Text('Dark'),
          value: ThemeMode.dark,
          groupValue: settings.themeMode,
          onChanged: (value) {
            if (value != null) {
              settings.updateSetting('themeMode', value, temporary: true);
            }
          },
        ),
      ],
    );
  }

  Widget _buildSimpleColorPicker(Color currentColor, ValueChanged<Color> onChanged) {
    final colors = [
      Colors.red,
      Colors.pink,
      Colors.purple,
      Colors.deepPurple,
      Colors.indigo,
      Colors.blue,
      Colors.lightBlue,
      Colors.cyan,
      Colors.teal,
      Colors.green,
      Colors.lightGreen,
      Colors.lime,
      Colors.yellow,
      Colors.amber,
      Colors.orange,
      Colors.deepOrange,
      Colors.brown,
      Colors.grey,
      Colors.blueGrey,
      Colors.black,
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: colors.map((color) {
        return GestureDetector(
          onTap: () => onChanged(color),
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: currentColor == color ? Colors.white : Colors.transparent,
                width: 3,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}
