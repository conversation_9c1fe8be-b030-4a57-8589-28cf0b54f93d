import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../database/database_service.dart';
import 'network_service.dart';
import 'error_handler.dart' as error_handler;

/// Simple connectivity result enum
enum ConnectivityResult { none, wifi, mobile, ethernet }

/// Comprehensive offline-first service for Shadow Suite
class OfflineFirstService {
  static bool _isInitialized = false;
  static ConnectivityResult _connectionStatus = ConnectivityResult.none;
  static final StreamController<OfflineStatus> _statusController =
      StreamController<OfflineStatus>.broadcast();

  static Timer? _syncTimer;
  static Timer? _connectivityTimer;
  static final Map<String, OfflineQueue> _offlineQueues = {};
  static final Map<String, CacheManager> _cacheManagers = {};

  /// Initialize offline-first service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize database
      await DatabaseService.database;

      // Initialize connectivity monitoring
      await _initializeConnectivityMonitoring();

      // Initialize offline queues for each app
      await _initializeOfflineQueues();

      // Initialize cache managers
      await _initializeCacheManagers();

      // Start periodic sync
      _startPeriodicSync();

      _isInitialized = true;

      _notifyStatusChange(
        OfflineStatus(
          isOnline: _connectionStatus != ConnectivityResult.none,
          connectionType: _connectionStatus,
          lastSyncTime: DateTime.now(),
          pendingOperations: 0,
          cacheSize: await _calculateTotalCacheSize(),
          isInitialized: true,
        ),
      );
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.operation,
        context: 'Initialize offline-first service',
      );
    }
  }

  /// Initialize connectivity monitoring
  static Future<void> _initializeConnectivityMonitoring() async {
    // Check initial connectivity status
    _connectionStatus = await _checkConnectivity();

    // Start periodic connectivity checks
    Timer.periodic(const Duration(seconds: 30), (timer) async {
      final newStatus = await _checkConnectivity();
      if (newStatus != _connectionStatus) {
        _connectionStatus = newStatus;
        _onConnectivityChanged(newStatus);
      }
    });
  }

  /// Check connectivity status
  static Future<ConnectivityResult> _checkConnectivity() async {
    try {
      final result = await NetworkService.hasInternetConnection();
      return result ? ConnectivityResult.wifi : ConnectivityResult.none;
    } catch (e) {
      return ConnectivityResult.none;
    }
  }

  /// Handle connectivity changes
  static void _onConnectivityChanged(ConnectivityResult result) async {
    final wasOnline = _connectionStatus != ConnectivityResult.none;
    final isOnline = result != ConnectivityResult.none;

    if (!wasOnline && isOnline) {
      // Just came online - trigger sync
      await _syncAllPendingOperations();
    }

    _notifyStatusChange(
      OfflineStatus(
        isOnline: isOnline,
        connectionType: result,
        lastSyncTime: DateTime.now(),
        pendingOperations: await _getTotalPendingOperations(),
        cacheSize: await _calculateTotalCacheSize(),
        isInitialized: _isInitialized,
      ),
    );
  }

  /// Initialize offline queues for each app
  static Future<void> _initializeOfflineQueues() async {
    final apps = [
      'memo_suite',
      'islamic_app',
      'money_manager',
      'excel_to_app',
      'file_manager',
      'shadow_player',
    ];

    for (final app in apps) {
      _offlineQueues[app] = OfflineQueue(appId: app);
      await _offlineQueues[app]!.initialize();
    }
  }

  /// Initialize cache managers
  static Future<void> _initializeCacheManagers() async {
    final cacheTypes = [
      'images',
      'audio',
      'video',
      'documents',
      'quran_data',
      'hadith_data',
      'prayer_times',
    ];

    for (final type in cacheTypes) {
      _cacheManagers[type] = CacheManager(cacheType: type);
      await _cacheManagers[type]!.initialize();
    }
  }

  /// Start periodic sync
  static void _startPeriodicSync() {
    _syncTimer?.cancel();
    _syncTimer = Timer.periodic(const Duration(minutes: 5), (timer) async {
      if (_connectionStatus != ConnectivityResult.none) {
        await _syncAllPendingOperations();
      }
    });
  }

  /// Queue operation for offline execution
  static Future<void> queueOperation(
    String appId,
    OfflineOperation operation,
  ) async {
    final queue = _offlineQueues[appId];
    if (queue != null) {
      await queue.addOperation(operation);

      // Try immediate sync if online
      if (_connectionStatus != ConnectivityResult.none) {
        await queue.processPendingOperations();
      }
    }
  }

  /// Get cached data
  static Future<T?> getCachedData<T>(
    String cacheType,
    String key, {
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    final cacheManager = _cacheManagers[cacheType];
    if (cacheManager != null) {
      return await cacheManager.get<T>(key, fromJson: fromJson);
    }
    return null;
  }

  /// Cache data
  static Future<void> cacheData(
    String cacheType,
    String key,
    dynamic data, {
    Duration? ttl,
  }) async {
    final cacheManager = _cacheManagers[cacheType];
    if (cacheManager != null) {
      await cacheManager.set(key, data, ttl: ttl);
    }
  }

  /// Sync all pending operations
  static Future<void> _syncAllPendingOperations() async {
    try {
      for (final queue in _offlineQueues.values) {
        await queue.processPendingOperations();
      }

      _notifyStatusChange(
        OfflineStatus(
          isOnline: _connectionStatus != ConnectivityResult.none,
          connectionType: _connectionStatus,
          lastSyncTime: DateTime.now(),
          pendingOperations: await _getTotalPendingOperations(),
          cacheSize: await _calculateTotalCacheSize(),
          isInitialized: _isInitialized,
        ),
      );
    } catch (e) {
      error_handler.ErrorHandler.handleError(
        e,
        StackTrace.current,
        error_handler.ErrorType.network,
        context: 'Sync pending operations',
      );
    }
  }

  /// Get total pending operations
  static Future<int> _getTotalPendingOperations() async {
    int total = 0;
    for (final queue in _offlineQueues.values) {
      total += await queue.getPendingCount();
    }
    return total;
  }

  /// Calculate total cache size
  static Future<int> _calculateTotalCacheSize() async {
    int total = 0;
    for (final cacheManager in _cacheManagers.values) {
      total += await cacheManager.getCacheSize();
    }
    return total;
  }

  /// Clear all caches
  static Future<void> clearAllCaches() async {
    for (final cacheManager in _cacheManagers.values) {
      await cacheManager.clear();
    }
  }

  /// Clear cache for specific type
  static Future<void> clearCache(String cacheType) async {
    final cacheManager = _cacheManagers[cacheType];
    if (cacheManager != null) {
      await cacheManager.clear();
    }
  }

  /// Get offline status stream
  static Stream<OfflineStatus> get statusStream => _statusController.stream;

  /// Get current offline status
  static OfflineStatus get currentStatus => OfflineStatus(
    isOnline: _connectionStatus != ConnectivityResult.none,
    connectionType: _connectionStatus,
    lastSyncTime: DateTime.now(),
    pendingOperations: 0,
    cacheSize: 0,
    isInitialized: _isInitialized,
  );

  /// Notify status change
  static void _notifyStatusChange(OfflineStatus status) {
    if (!_statusController.isClosed) {
      _statusController.add(status);
    }
  }

  /// Dispose resources
  static void dispose() {
    _syncTimer?.cancel();
    _connectivityTimer?.cancel();
    _statusController.close();

    for (final queue in _offlineQueues.values) {
      queue.dispose();
    }

    for (final cacheManager in _cacheManagers.values) {
      cacheManager.dispose();
    }
  }
}

/// Offline operation model
class OfflineOperation {
  final String id;
  final String type;
  final Map<String, dynamic> data;
  final DateTime createdAt;
  final int retryCount;
  final int maxRetries;

  const OfflineOperation({
    required this.id,
    required this.type,
    required this.data,
    required this.createdAt,
    this.retryCount = 0,
    this.maxRetries = 3,
  });

  OfflineOperation copyWith({int? retryCount}) {
    return OfflineOperation(
      id: id,
      type: type,
      data: data,
      createdAt: createdAt,
      retryCount: retryCount ?? this.retryCount,
      maxRetries: maxRetries,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'data': data,
      'created_at': createdAt.toIso8601String(),
      'retry_count': retryCount,
      'max_retries': maxRetries,
    };
  }

  factory OfflineOperation.fromJson(Map<String, dynamic> json) {
    return OfflineOperation(
      id: json['id'],
      type: json['type'],
      data: Map<String, dynamic>.from(json['data']),
      createdAt: DateTime.parse(json['created_at']),
      retryCount: json['retry_count'] ?? 0,
      maxRetries: json['max_retries'] ?? 3,
    );
  }
}

/// Offline status model
class OfflineStatus {
  final bool isOnline;
  final ConnectivityResult connectionType;
  final DateTime lastSyncTime;
  final int pendingOperations;
  final int cacheSize;
  final bool isInitialized;

  const OfflineStatus({
    required this.isOnline,
    required this.connectionType,
    required this.lastSyncTime,
    required this.pendingOperations,
    required this.cacheSize,
    required this.isInitialized,
  });
}

/// Offline queue for managing pending operations
class OfflineQueue {
  final String appId;
  final List<OfflineOperation> _operations = [];

  OfflineQueue({required this.appId});

  Future<void> initialize() async {
    await _loadPendingOperations();
  }

  Future<void> addOperation(OfflineOperation operation) async {
    _operations.add(operation);
    await _savePendingOperations();
  }

  Future<void> processPendingOperations() async {
    final operationsToProcess = List<OfflineOperation>.from(_operations);

    for (final operation in operationsToProcess) {
      try {
        await _executeOperation(operation);
        _operations.remove(operation);
      } catch (e) {
        if (operation.retryCount < operation.maxRetries) {
          final updatedOperation = operation.copyWith(
            retryCount: operation.retryCount + 1,
          );
          final index = _operations.indexOf(operation);
          if (index != -1) {
            _operations[index] = updatedOperation;
          }
        } else {
          _operations.remove(operation);
        }
      }
    }

    await _savePendingOperations();
  }

  Future<int> getPendingCount() async {
    return _operations.length;
  }

  Future<void> _executeOperation(OfflineOperation operation) async {
    // Execute the operation based on type
    switch (operation.type) {
      case 'create':
      case 'update':
      case 'delete':
        // Handle CRUD operations
        break;
      case 'upload':
        // Handle file uploads
        break;
      case 'sync':
        // Handle data synchronization
        break;
    }
  }

  Future<void> _loadPendingOperations() async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM offline_operations WHERE app_id = ?',
        [appId],
      );

      _operations.clear();
      for (final row in results) {
        _operations.add(OfflineOperation.fromJson(row));
      }
    } catch (e) {
      // Handle error
    }
  }

  Future<void> _savePendingOperations() async {
    try {
      await DatabaseService.safeDelete(
        'offline_operations',
        where: 'app_id = ?',
        whereArgs: [appId],
      );

      for (final operation in _operations) {
        final data = operation.toJson();
        data['app_id'] = appId;
        await DatabaseService.safeInsert('offline_operations', data);
      }
    } catch (e) {
      // Handle error
    }
  }

  void dispose() {
    _operations.clear();
  }
}

/// Cache manager for different data types
class CacheManager {
  final String cacheType;
  final Map<String, CacheEntry> _cache = {};

  CacheManager({required this.cacheType});

  Future<void> initialize() async {
    await _loadCacheFromDatabase();
  }

  Future<T?> get<T>(
    String key, {
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    final entry = _cache[key];
    if (entry != null && !entry.isExpired) {
      if (fromJson != null && entry.data is Map<String, dynamic>) {
        return fromJson(entry.data as Map<String, dynamic>);
      }
      return entry.data as T?;
    }
    return null;
  }

  Future<void> set(String key, dynamic data, {Duration? ttl}) async {
    final expiresAt = ttl != null ? DateTime.now().add(ttl) : null;
    final entry = CacheEntry(
      key: key,
      data: data,
      createdAt: DateTime.now(),
      expiresAt: expiresAt,
    );

    _cache[key] = entry;
    await _saveCacheEntry(entry);
  }

  Future<int> getCacheSize() async {
    return _cache.length;
  }

  Future<void> clear() async {
    _cache.clear();
    await DatabaseService.safeDelete(
      'cache_entries',
      where: 'cache_type = ?',
      whereArgs: [cacheType],
    );
  }

  Future<void> _loadCacheFromDatabase() async {
    try {
      final results = await DatabaseService.safeQuery(
        'SELECT * FROM cache_entries WHERE cache_type = ?',
        [cacheType],
      );

      _cache.clear();
      for (final row in results) {
        final entry = CacheEntry.fromJson(row);
        if (!entry.isExpired) {
          _cache[entry.key] = entry;
        }
      }
    } catch (e) {
      // Handle error
    }
  }

  Future<void> _saveCacheEntry(CacheEntry entry) async {
    try {
      final data = entry.toJson();
      data['cache_type'] = cacheType;
      await DatabaseService.safeInsert('cache_entries', data);
    } catch (e) {
      // Handle error
    }
  }

  void dispose() {
    _cache.clear();
  }
}

/// Cache entry model
class CacheEntry {
  final String key;
  final dynamic data;
  final DateTime createdAt;
  final DateTime? expiresAt;

  const CacheEntry({
    required this.key,
    required this.data,
    required this.createdAt,
    this.expiresAt,
  });

  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'data': data,
      'created_at': createdAt.toIso8601String(),
      'expires_at': expiresAt?.toIso8601String(),
    };
  }

  factory CacheEntry.fromJson(Map<String, dynamic> json) {
    return CacheEntry(
      key: json['key'],
      data: json['data'],
      createdAt: DateTime.parse(json['created_at']),
      expiresAt: json['expires_at'] != null
          ? DateTime.parse(json['expires_at'])
          : null,
    );
  }
}

/// Offline-first provider
final offlineFirstProvider =
    StateNotifierProvider<OfflineFirstNotifier, OfflineStatus>((ref) {
      return OfflineFirstNotifier();
    });

class OfflineFirstNotifier extends StateNotifier<OfflineStatus> {
  late StreamSubscription<OfflineStatus> _subscription;

  OfflineFirstNotifier() : super(OfflineFirstService.currentStatus) {
    _initialize();
  }

  void _initialize() async {
    await OfflineFirstService.initialize();
    _subscription = OfflineFirstService.statusStream.listen((status) {
      state = status;
    });
  }

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}
