import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/responsive_layout.dart';
import '../launcher/shadow_suite_launcher.dart';
import '../../features/dashboard/dashboard_screen.dart';
import '../screens/settings_screen.dart';
import '../../features/profile/profile_screen.dart';
import '../../apps/islamic_app/screens/islamic_dashboard.dart';
import '../../apps/unified_finance/unified_finance_main.dart';
import '../../apps/tools_builder/standalone_main.dart';
import '../../apps/file_manager/file_manager_main.dart';
import '../screens/layout_settings_screen.dart';
import '../screens/advanced_settings_screen.dart';

// Router Provider
final routerProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/launcher',
    routes: [
      // New Launcher Route (Default)
      GoRoute(
        path: '/launcher',
        name: 'launcher',
        builder: (context, state) => const ShadowSuiteLauncher(),
      ),

      ShellRoute(
        builder: (context, state, child) {
          return ResponsiveLayout(child: child);
        },
        routes: [
          // Dashboard (Legacy)
          GoRoute(
            path: '/dashboard',
            name: 'dashboard',
            builder: (context, state) => const DashboardScreen(),
          ),

          // Unified Finance Routes
          GoRoute(
            path: '/unified-finance',
            name: 'unified-finance',
            builder: (context, state) => const UnifiedFinanceMain(),
            routes: [
              GoRoute(
                path: '/dashboard',
                name: 'unified-finance-dashboard',
                builder: (context, state) => const UnifiedFinanceMain(),
              ),
              GoRoute(
                path: '/accounts',
                name: 'unified-finance-accounts',
                builder: (context, state) => const UnifiedFinanceMain(),
              ),
              GoRoute(
                path: '/transactions',
                name: 'unified-finance-transactions',
                builder: (context, state) => const UnifiedFinanceMain(),
              ),
              GoRoute(
                path: '/budgets',
                name: 'unified-finance-budgets',
                builder: (context, state) => const UnifiedFinanceMain(),
              ),
              GoRoute(
                path: '/goals',
                name: 'unified-finance-goals',
                builder: (context, state) => const UnifiedFinanceMain(),
              ),
            ],
          ),

          // File Manager Routes
          GoRoute(
            path: '/file-manager',
            name: 'file-manager',
            builder: (context, state) => const FileManagerMain(),
            routes: [
              GoRoute(
                path: '/dashboard',
                name: 'file-manager-dashboard',
                builder: (context, state) => const FileManagerMain(),
              ),
              GoRoute(
                path: '/browse',
                name: 'file-browser',
                builder: (context, state) => const FileManagerMain(),
              ),
              GoRoute(
                path: '/cloud',
                name: 'file-cloud',
                builder: (context, state) => const FileManagerMain(),
              ),
              GoRoute(
                path: '/network',
                name: 'file-network',
                builder: (context, state) => const FileManagerMain(),
              ),
              GoRoute(
                path: '/media',
                name: 'file-media',
                builder: (context, state) => const FileManagerMain(),
              ),
              GoRoute(
                path: '/operations',
                name: 'file-operations',
                builder: (context, state) => const FileManagerMain(),
              ),
            ],
          ),

          // Tools Builder Routes
          GoRoute(
            path: '/tools-builder',
            name: 'tools-builder',
            builder: (context, state) => const ToolsBuilderStandaloneHome(),
            routes: [
              GoRoute(
                path: '/dashboard',
                name: 'tools-dashboard',
                builder: (context, state) => const ToolsBuilderStandaloneHome(),
              ),
              GoRoute(
                path: '/spreadsheet',
                name: 'tools-spreadsheet',
                builder: (context, state) => const ToolsBuilderStandaloneHome(),
              ),
              GoRoute(
                path: '/functions',
                name: 'tools-functions',
                builder: (context, state) => const ToolsBuilderStandaloneHome(),
              ),
              GoRoute(
                path: '/ui-builder',
                name: 'tools-ui-builder',
                builder: (context, state) => const ToolsBuilderStandaloneHome(),
              ),
              GoRoute(
                path: '/templates',
                name: 'tools-templates',
                builder: (context, state) => const ToolsBuilderStandaloneHome(),
              ),
              GoRoute(
                path: '/export',
                name: 'tools-export',
                builder: (context, state) => const ToolsBuilderStandaloneHome(),
              ),
            ],
          ),

          // Islamic App Routes
          GoRoute(
            path: '/islamic-app',
            name: 'islamic-app',
            builder: (context, state) => const IslamicDashboard(),
            routes: [
              GoRoute(
                path: '/dashboard',
                name: 'islamic-dashboard',
                builder: (context, state) => const IslamicDashboard(),
              ),
              GoRoute(
                path: '/quran',
                name: 'quran',
                builder: (context, state) => const IslamicDashboard(),
              ),
              GoRoute(
                path: '/hadith',
                name: 'hadith',
                builder: (context, state) => const IslamicDashboard(),
              ),
              GoRoute(
                path: '/tafseer',
                name: 'tafseer',
                builder: (context, state) => const IslamicDashboard(),
              ),
              GoRoute(
                path: '/athkar',
                name: 'athkar',
                builder: (context, state) => const IslamicDashboard(),
              ),
              GoRoute(
                path: '/prayer-times',
                name: 'prayer-times',
                builder: (context, state) => const IslamicDashboard(),
              ),
              GoRoute(
                path: '/qibla',
                name: 'qibla-compass',
                builder: (context, state) => const IslamicDashboard(),
              ),
              GoRoute(
                path: '/bookmarks',
                name: 'islamic-bookmarks',
                builder: (context, state) => const IslamicDashboard(),
              ),
            ],
          ),

          // Settings Routes
          GoRoute(
            path: '/settings',
            name: 'settings',
            builder: (context, state) => const SettingsScreen(),
            routes: [
              GoRoute(
                path: '/layout',
                name: 'layout-settings',
                builder: (context, state) => const LayoutSettingsScreen(),
              ),
              GoRoute(
                path: '/advanced',
                name: 'advanced-settings',
                builder: (context, state) => const AdvancedSettingsScreen(),
              ),
            ],
          ),

          // Profile
          GoRoute(
            path: '/profile',
            name: 'profile',
            builder: (context, state) => const ProfileScreen(),
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('Page Not Found'),
        backgroundColor: const Color(0xFFE74C3C),
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Color(0xFFE74C3C)),
            const SizedBox(height: 16),
            Text(
              'Page Not Found',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'The page "${state.uri}" could not be found.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/dashboard'),
              child: const Text('Go to Dashboard'),
            ),
          ],
        ),
      ),
    ),
  );
});

// Navigation Helper Extensions
extension GoRouterExtension on GoRouter {
  void goToMoneyManager({String? subRoute}) {
    final route = subRoute != null
        ? '/money-manager/$subRoute'
        : '/money-manager';
    go(route);
  }

  void goToFileManager({String? subRoute}) {
    final route = subRoute != null
        ? '/file-manager/$subRoute'
        : '/file-manager';
    go(route);
  }

  void goToExcelToApp({String? subRoute, String? toolId}) {
    String route = '/excel-to-app';
    if (subRoute != null) {
      if (toolId != null &&
          (subRoute == 'tool' ||
              subRoute == 'run-tool' ||
              subRoute == 'edit-tool')) {
        route = '/excel-to-app/$subRoute/$toolId';
      } else {
        route = '/excel-to-app/$subRoute';
      }
    }
    go(route);
  }

  void goToIslamicApp({String? subRoute}) {
    final route = subRoute != null ? '/islamic-app/$subRoute' : '/islamic-app';
    go(route);
  }

  void goToMemoSuite({String? subRoute}) {
    final route = subRoute != null ? '/memo-suite/$subRoute' : '/memo-suite';
    go(route);
  }
}

// Current Route Provider
final currentRouteProvider = StateProvider<String>((ref) => '/dashboard');

// Navigation State Provider
final navigationStateProvider =
    StateNotifierProvider<NavigationStateNotifier, NavigationState>((ref) {
      return NavigationStateNotifier();
    });

class NavigationState {
  final String currentRoute;
  final Map<String, dynamic> routeData;
  final List<String> navigationHistory;

  const NavigationState({
    required this.currentRoute,
    required this.routeData,
    required this.navigationHistory,
  });

  NavigationState copyWith({
    String? currentRoute,
    Map<String, dynamic>? routeData,
    List<String>? navigationHistory,
  }) {
    return NavigationState(
      currentRoute: currentRoute ?? this.currentRoute,
      routeData: routeData ?? this.routeData,
      navigationHistory: navigationHistory ?? this.navigationHistory,
    );
  }
}

class NavigationStateNotifier extends StateNotifier<NavigationState> {
  NavigationStateNotifier()
    : super(
        const NavigationState(
          currentRoute: '/dashboard',
          routeData: {},
          navigationHistory: ['/dashboard'],
        ),
      );

  void updateRoute(String route, {Map<String, dynamic>? data}) {
    final newHistory = [...state.navigationHistory];
    if (newHistory.isEmpty || newHistory.last != route) {
      newHistory.add(route);
      // Keep only last 50 routes
      if (newHistory.length > 50) {
        newHistory.removeAt(0);
      }
    }

    state = state.copyWith(
      currentRoute: route,
      routeData: data ?? {},
      navigationHistory: newHistory,
    );
  }

  void goBack() {
    if (state.navigationHistory.length > 1) {
      final newHistory = [...state.navigationHistory];
      newHistory.removeLast();

      state = state.copyWith(
        currentRoute: newHistory.last,
        navigationHistory: newHistory,
      );
    }
  }

  void clearHistory() {
    state = state.copyWith(navigationHistory: [state.currentRoute]);
  }
}
