import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../file_manager_main.dart';

class FileOperationsScreen extends ConsumerWidget {
  const FileOperationsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: Column(
        children: [
          const FileManagerHeader(
            title: 'File Operations',
            subtitle: 'Advanced file operations and batch processing',
            actions: [
              Icon(Icons.history, color: Color(0xFFE67E22)),
              SizedBox(width: 16),
              Icon(Icons.clear_all, color: Color(0xFFE67E22)),
            ],
          ),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.build,
                    size: 64,
                    color: Color(0xFFE67E22),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'File Operations',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2C3E50),
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Perform advanced file operations and batch processing',
                    style: TextStyle(
                      color: Color(0xFF7F8C8D),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),
                  Wrap(
                    spacing: 16,
                    runSpacing: 16,
                    children: [
                      ElevatedButton.icon(
                        onPressed: () {
                          // Implement batch copy
                        },
                        icon: const Icon(Icons.copy),
                        label: const Text('Batch Copy'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFE67E22),
                          foregroundColor: Colors.white,
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed: () {
                          // Implement batch move
                        },
                        icon: const Icon(Icons.drive_file_move),
                        label: const Text('Batch Move'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFE67E22),
                          foregroundColor: Colors.white,
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed: () {
                          // Implement batch rename
                        },
                        icon: const Icon(Icons.edit),
                        label: const Text('Batch Rename'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFE67E22),
                          foregroundColor: Colors.white,
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed: () {
                          // Implement compression
                        },
                        icon: const Icon(Icons.compress),
                        label: const Text('Compress'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFE67E22),
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
