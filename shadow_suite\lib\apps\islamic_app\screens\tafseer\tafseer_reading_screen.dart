import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/islamic_providers.dart';
import '../../models/tafseer.dart';

class TafseerReadingScreen extends ConsumerStatefulWidget {
  const TafseerReadingScreen({super.key});

  @override
  ConsumerState<TafseerReadingScreen> createState() =>
      _TafseerReadingScreenState();
}

class _TafseerReadingScreenState extends ConsumerState<TafseerReadingScreen> {
  final PageController _pageController = PageController();
  int _currentVerseIndex = 0;

  final List<Tafseer> _sampleTafseer = [
    Tafseer(
      surahNumber: 1,
      verseNumber: 1,
      text:
          'This verse is known as the Basmala. It begins every chapter of the Quran except one. The name <PERSON> is the proper name of God in Arabic, while <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> are two of His beautiful names that emphasize His mercy and compassion. <PERSON> explains that this verse teaches us to begin all our actions with the name of <PERSON>, seeking His blessing and guidance.',
      source: '<PERSON><PERSON><PERSON>',
      language: 'en',
    ),
    <PERSON><PERSON><PERSON>(
      surahNumber: 1,
      verseNumber: 2,
      text:
          'This verse establishes that all praise belongs to <PERSON> alone. The word "Hamd" encompasses gratitude, praise, and acknowledgment of excellence. "Rabb" means Lord, Master, and Sustainer, while "Alameen" refers to all of creation. The scholars explain that this verse teaches us the proper attitude of gratitude and recognition of Allah\'s sovereignty over all creation.',
      source: 'Tafseer Ibn Kathir',
      language: 'en',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return <PERSON>affold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text('Tafseer Reading'),
        backgroundColor: const Color(0xFF27AE60),
        foregroundColor: Colors.white,
        leading: IconButton(
          onPressed: () {
            ref.read(islamicAppCurrentScreenProvider.notifier).state =
                IslamicAppScreen.tafseerList;
          },
          icon: const Icon(Icons.arrow_back),
        ),
        actions: [
          IconButton(
            onPressed: _showVerseSelector,
            icon: const Icon(Icons.list),
            tooltip: 'Select Verse',
          ),
          IconButton(
            onPressed: _showBookmarkDialog,
            icon: const Icon(Icons.bookmark_add),
            tooltip: 'Bookmark',
          ),
          PopupMenuButton(
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings),
                    SizedBox(width: 8),
                    Text('Reading Settings'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'share',
                child: Row(
                  children: [
                    Icon(Icons.share),
                    SizedBox(width: 8),
                    Text('Share Tafseer'),
                  ],
                ),
              ),
            ],
            onSelected: _handleMenuAction,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildNavigationHeader(),
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              itemCount: _sampleTafseer.length,
              onPageChanged: (index) {
                setState(() {
                  _currentVerseIndex = index;
                });
              },
              itemBuilder: (context, index) {
                return _buildTafseerCard(_sampleTafseer[index]);
              },
            ),
          ),
          _buildBottomNavigation(),
        ],
      ),
    );
  }

  Widget _buildNavigationHeader() {
    final currentEntry = _sampleTafseer[_currentVerseIndex];
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Color(0xFFE9ECEF))),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Al-Fatiha (${currentEntry.surahNumber}:${currentEntry.verseNumber})',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF27AE60),
                  ),
                ),
                Text(
                  currentEntry.source,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: const Color(0xFF7F8C8D),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: const Color(0xFF27AE60).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              '${_currentVerseIndex + 1} of ${_sampleTafseer.length}',
              style: const TextStyle(
                color: Color(0xFF27AE60),
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTafseerCard(Tafseer entry) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildVerseHeader(entry),
              const SizedBox(height: 24),
              _buildArabicVerse(),
              const SizedBox(height: 16),
              _buildTranslation(),
              const SizedBox(height: 24),
              _buildTafseerSection(entry),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVerseHeader(Tafseer entry) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF27AE60).withValues(alpha: 0.1),
            const Color(0xFF27AE60).withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF27AE60),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '${entry.verseNumber}',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Surah Al-Fatiha',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                Text(
                  'Verse ${entry.verseNumber}',
                  style: const TextStyle(
                    color: Color(0xFF7F8C8D),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildArabicVerse() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE9ECEF)),
      ),
      child: Text(
        _currentVerseIndex == 0
            ? 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ'
            : 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
        style: const TextStyle(
          fontSize: 24,
          height: 2.0,
          fontWeight: FontWeight.w500,
          color: Color(0xFF2C3E50),
        ),
        textAlign: TextAlign.center,
        textDirection: TextDirection.rtl,
      ),
    );
  }

  Widget _buildTranslation() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade100),
      ),
      child: Text(
        _currentVerseIndex == 0
            ? 'In the name of Allah, the Most Gracious, the Most Merciful'
            : 'All praise is due to Allah, Lord of all the worlds',
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Color(0xFF2C3E50),
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildTafseerSection(Tafseer entry) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.auto_stories, color: Color(0xFF27AE60)),
            const SizedBox(width: 8),
            Text(
              'Tafseer',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: const Color(0xFF27AE60),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.green.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.green.shade100),
          ),
          child: Text(
            entry.text,
            style: const TextStyle(
              fontSize: 16,
              height: 1.6,
              color: Color(0xFF2C3E50),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Color(0xFFE9ECEF))),
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: _currentVerseIndex > 0 ? _previousVerse : null,
            icon: const Icon(Icons.arrow_back_ios),
            tooltip: 'Previous Verse',
          ),
          Expanded(
            child: LinearProgressIndicator(
              value: (_currentVerseIndex + 1) / _sampleTafseer.length,
              backgroundColor: Colors.grey[300],
              valueColor: const AlwaysStoppedAnimation<Color>(
                Color(0xFF27AE60),
              ),
            ),
          ),
          IconButton(
            onPressed: _currentVerseIndex < _sampleTafseer.length - 1
                ? _nextVerse
                : null,
            icon: const Icon(Icons.arrow_forward_ios),
            tooltip: 'Next Verse',
          ),
        ],
      ),
    );
  }

  void _previousVerse() {
    if (_currentVerseIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _nextVerse() {
    if (_currentVerseIndex < _sampleTafseer.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _showVerseSelector() {
    // Implement verse selector
  }

  void _showBookmarkDialog() {
    // Implement bookmark functionality
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'settings':
        // Show reading settings
        break;
      case 'share':
        // Share current tafseer
        break;
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
}
