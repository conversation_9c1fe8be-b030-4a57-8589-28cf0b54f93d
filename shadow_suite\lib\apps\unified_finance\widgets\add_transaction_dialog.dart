import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/finance_models.dart';
import '../services/finance_service.dart';

class AddTransactionDialog extends ConsumerStatefulWidget {
  final FinanceAccount account;
  final TransactionType? initialType;

  const AddTransactionDialog({
    super.key,
    required this.account,
    this.initialType,
  });

  @override
  ConsumerState<AddTransactionDialog> createState() =>
      _AddTransactionDialogState();
}

class _AddTransactionDialogState extends ConsumerState<AddTransactionDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _notesController = TextEditingController();

  late TransactionType _selectedType;
  TransactionCategory _selectedCategory = TransactionCategory.food;
  DateTime _selectedDate = DateTime.now();
  bool _isRecurring = false;
  FinanceAccount? _selectedToAccount;

  @override
  void initState() {
    super.initState();
    _selectedType = widget.initialType ?? TransactionType.expense;
    _updateCategoryForType();
  }

  void _updateCategoryForType() {
    if (_selectedType == TransactionType.income) {
      _selectedCategory = TransactionCategory.salary;
    } else if (_selectedType == TransactionType.transfer) {
      _selectedCategory = TransactionCategory.transfer;
    } else {
      _selectedCategory = TransactionCategory.food;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: 24),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildTypeSelector(),
                      const SizedBox(height: 16),
                      _buildAmountField(),
                      const SizedBox(height: 16),
                      _buildDescriptionField(),
                      const SizedBox(height: 16),
                      _buildCategorySelector(),
                      const SizedBox(height: 16),
                      if (_selectedType == TransactionType.transfer) ...[
                        _buildAccountSelector(),
                        const SizedBox(height: 16),
                      ],
                      _buildDateSelector(),
                      const SizedBox(height: 16),
                      _buildNotesField(),
                      const SizedBox(height: 16),
                      _buildRecurringOption(),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              _buildActions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            _selectedType == TransactionType.income
                ? Icons.add_circle
                : _selectedType == TransactionType.transfer
                ? Icons.swap_horiz
                : Icons.remove_circle,
            color: _selectedType == TransactionType.income
                ? Colors.green
                : _selectedType == TransactionType.transfer
                ? Colors.blue
                : Colors.red,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Add Transaction',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              Text(
                'Account: ${widget.account.name}',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  Widget _buildTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Transaction Type',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildTypeOption(
                TransactionType.income,
                'Income',
                Icons.add_circle,
                Colors.green,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildTypeOption(
                TransactionType.expense,
                'Expense',
                Icons.remove_circle,
                Colors.red,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildTypeOption(
                TransactionType.transfer,
                'Transfer',
                Icons.swap_horiz,
                Colors.blue,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTypeOption(
    TransactionType type,
    String label,
    IconData icon,
    Color color,
  ) {
    final isSelected = _selectedType == type;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedType = type;
          _updateCategoryForType();
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? color.withValues(alpha: 0.1) : Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: 2,
          ),
        ),
        child: Column(
          children: [
            Icon(icon, color: isSelected ? color : Colors.grey[400], size: 32),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? color : Colors.grey[600],
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountField() {
    return TextFormField(
      controller: _amountController,
      decoration: InputDecoration(
        labelText: 'Amount',
        prefixText: '\$',
        border: const OutlineInputBorder(),
        prefixIcon: Icon(
          _selectedType == TransactionType.income
              ? Icons.add
              : _selectedType == TransactionType.transfer
              ? Icons.swap_horiz
              : Icons.remove,
          color: _selectedType == TransactionType.income
              ? Colors.green
              : _selectedType == TransactionType.transfer
              ? Colors.blue
              : Colors.red,
        ),
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
      ],
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter an amount';
        }
        final amount = double.tryParse(value);
        if (amount == null || amount <= 0) {
          return 'Please enter a valid amount';
        }
        return null;
      },
    );
  }

  Widget _buildDescriptionField() {
    return TextFormField(
      controller: _descriptionController,
      decoration: const InputDecoration(
        labelText: 'Description',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.description),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter a description';
        }
        return null;
      },
    );
  }

  Widget _buildCategorySelector() {
    final categories = _selectedType == TransactionType.income
        ? [
            TransactionCategory.salary,
            TransactionCategory.freelance,
            TransactionCategory.investment,
            TransactionCategory.gift,
            TransactionCategory.otherIncome,
          ]
        : _selectedType == TransactionType.transfer
        ? [TransactionCategory.transfer]
        : [
            TransactionCategory.food,
            TransactionCategory.transportation,
            TransactionCategory.entertainment,
            TransactionCategory.shopping,
            TransactionCategory.utilities,
            TransactionCategory.healthcare,
            TransactionCategory.education,
            TransactionCategory.travel,
            TransactionCategory.insurance,
            TransactionCategory.taxes,
            TransactionCategory.otherExpense,
          ];

    return DropdownButtonFormField<TransactionCategory>(
      value: _selectedCategory,
      decoration: const InputDecoration(
        labelText: 'Category',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.category),
      ),
      items: categories.map((category) {
        return DropdownMenuItem(
          value: category,
          child: Text(_getCategoryDisplayName(category)),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() => _selectedCategory = value);
        }
      },
    );
  }

  Widget _buildAccountSelector() {
    final financeService = ref.watch(financeServiceProvider);
    final accounts = financeService
        .getAccounts()
        .where((account) => account.id != widget.account.id)
        .toList();

    return DropdownButtonFormField<FinanceAccount>(
      value: _selectedToAccount,
      decoration: const InputDecoration(
        labelText: 'Transfer To Account',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.account_balance),
      ),
      items: accounts.map((account) {
        return DropdownMenuItem(
          value: account,
          child: Row(
            children: [
              Icon(account.icon, size: 20, color: account.color),
              const SizedBox(width: 8),
              Expanded(child: Text(account.name)),
            ],
          ),
        );
      }).toList(),
      onChanged: (value) {
        setState(() => _selectedToAccount = value);
      },
      validator: (value) {
        if (_selectedType == TransactionType.transfer && value == null) {
          return 'Please select a destination account';
        }
        return null;
      },
    );
  }

  Widget _buildDateSelector() {
    return InkWell(
      onTap: () => _selectDate(),
      child: InputDecorator(
        decoration: const InputDecoration(
          labelText: 'Date',
          border: OutlineInputBorder(),
          prefixIcon: Icon(Icons.calendar_today),
        ),
        child: Text(
          '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
        ),
      ),
    );
  }

  Widget _buildNotesField() {
    return TextFormField(
      controller: _notesController,
      decoration: const InputDecoration(
        labelText: 'Notes (Optional)',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.note),
      ),
      maxLines: 3,
    );
  }

  Widget _buildRecurringOption() {
    return CheckboxListTile(
      title: const Text('Recurring Transaction'),
      subtitle: const Text('This transaction repeats monthly'),
      value: _isRecurring,
      onChanged: (value) => setState(() => _isRecurring = value ?? false),
      controlAffinity: ListTileControlAffinity.leading,
    );
  }

  Widget _buildActions() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _saveTransaction,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Add Transaction'),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );
    if (date != null) {
      setState(() => _selectedDate = date);
    }
  }

  String _getCategoryDisplayName(TransactionCategory category) {
    switch (category) {
      case TransactionCategory.otherIncome:
        return 'Other Income';
      case TransactionCategory.otherExpense:
        return 'Other Expense';
      case TransactionCategory.transfer:
        return 'Transfer';
      default:
        return category.name[0].toUpperCase() + category.name.substring(1);
    }
  }

  void _saveTransaction() {
    if (_formKey.currentState!.validate()) {
      final amount = double.parse(_amountController.text);
      final financeService = ref.read(financeServiceProvider);

      final now = DateTime.now();
      final transaction = FinanceTransaction(
        id: now.millisecondsSinceEpoch.toString(),
        accountId: widget.account.id,
        toAccountId: _selectedType == TransactionType.transfer
            ? _selectedToAccount?.id
            : null,
        amount: amount,
        type: _selectedType,
        category: _selectedCategory,
        description: _descriptionController.text,
        date: _selectedDate,
        notes: _notesController.text.isEmpty ? null : _notesController.text,
        createdAt: now,
        updatedAt: now,
      );

      financeService.addTransaction(transaction);
      Navigator.pop(context, transaction);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Transaction added successfully'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
