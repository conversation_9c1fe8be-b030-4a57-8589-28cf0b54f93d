import 'package:flutter/material.dart';

class ShadowPlayerHeader extends StatelessWidget {
  final String title;
  final String subtitle;
  final bool isScanning;
  final VoidCallback? onRefresh;
  final List<Widget>? actions;

  const ShadowPlayerHeader({
    super.key,
    required this.title,
    required this.subtitle,
    this.isScanning = false,
    this.onRefresh,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF2C3E50), Color(0xFF34495E)],
        ),
        border: Border(bottom: BorderSide(color: Color(0xFF1A252F), width: 1)),
      ),
      child: SafeArea(
        bottom: false,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Row
            Row(
              children: [
                // App Icon
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: const LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [Color(0xFFE74C3C), Color(0xFFC0392B)],
                    ),
                  ),
                  child: const Icon(
                    Icons.play_circle_filled,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),

                // Title and Subtitle
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          if (isScanning) ...[
                            const SizedBox(
                              width: 12,
                              height: 12,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Color(0xFFE67E22),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                          ],
                          Expanded(
                            child: Text(
                              subtitle,
                              style: TextStyle(
                                color: isScanning
                                    ? const Color(0xFFE67E22)
                                    : const Color(0xFFBDC3C7),
                                fontSize: 14,
                                fontWeight: isScanning
                                    ? FontWeight.w500
                                    : FontWeight.normal,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Action Buttons
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Refresh Button
                    if (onRefresh != null)
                      IconButton(
                        onPressed: isScanning ? null : onRefresh,
                        icon: Icon(
                          Icons.refresh,
                          color: isScanning
                              ? const Color(0xFF7F8C8D)
                              : const Color(0xFF3498DB),
                          size: 24,
                        ),
                        tooltip: 'Refresh Media Library',
                      ),

                    // Custom Actions
                    if (actions != null) ...actions!,

                    // Settings Button
                    IconButton(
                      onPressed: () => _showQuickSettings(context),
                      icon: const Icon(
                        Icons.more_vert,
                        color: Color(0xFFBDC3C7),
                        size: 24,
                      ),
                      tooltip: 'Quick Settings',
                    ),
                  ],
                ),
              ],
            ),

            // Progress Bar (when scanning)
            if (isScanning) ...[
              const SizedBox(height: 12),
              Container(
                height: 3,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(1.5),
                  color: const Color(0xFF34495E),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(1.5),
                  child: const LinearProgressIndicator(
                    backgroundColor: Colors.transparent,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Color(0xFFE67E22),
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showQuickSettings(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFF2C3E50),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            const Text(
              'Quick Settings',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Quick Actions
            _buildQuickAction(
              icon: Icons.folder_open,
              title: 'Scan Locations',
              subtitle: 'Configure scan directories',
              onTap: () {
                Navigator.pop(context);
                // Navigate to scan settings
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Opening Scan Settings...')),
                );
              },
            ),
            _buildQuickAction(
              icon: Icons.video_settings,
              title: 'Video Settings',
              subtitle: 'Configure video playback',
              onTap: () {
                Navigator.pop(context);
                // Navigate to video settings
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Opening Video Settings...')),
                );
              },
            ),
            _buildQuickAction(
              icon: Icons.music_note,
              title: 'Audio Settings',
              subtitle: 'Configure audio playback',
              onTap: () {
                Navigator.pop(context);
                // Navigate to audio settings
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Opening Audio Settings...')),
                );
              },
            ),
            _buildQuickAction(
              icon: Icons.storage,
              title: 'Storage & Cache',
              subtitle: 'Manage storage usage',
              onTap: () {
                Navigator.pop(context);
                // Navigate to storage settings
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Opening Storage Settings...')),
                );
              },
            ),

            const SizedBox(height: 16),

            // Close Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF34495E),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Close'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickAction({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: const Color(0xFF34495E),
              ),
              child: Icon(icon, color: const Color(0xFF3498DB), size: 20),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      color: Color(0xFFBDC3C7),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.chevron_right, color: Color(0xFF7F8C8D), size: 20),
          ],
        ),
      ),
    );
  }
}
