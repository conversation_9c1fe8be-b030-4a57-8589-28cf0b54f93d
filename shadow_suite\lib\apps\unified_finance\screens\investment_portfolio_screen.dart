import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Investment Portfolio Tracking Screen with real-time performance monitoring
class InvestmentPortfolioScreen extends ConsumerStatefulWidget {
  const InvestmentPortfolioScreen({super.key});

  @override
  ConsumerState<InvestmentPortfolioScreen> createState() =>
      _InvestmentPortfolioScreenState();
}

class _InvestmentPortfolioScreenState
    extends ConsumerState<InvestmentPortfolioScreen> {
  String _selectedTimeframe = '1M';
  bool _showDividends = true;
  bool _showRealTimeUpdates = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Investment Portfolio'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddInvestmentDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshPortfolio,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPortfolioSummary(),
            const SizedBox(height: 24),
            _buildPerformanceChart(),
            const SizedBox(height: 24),
            _buildAssetAllocation(),
            const SizedBox(height: 24),
            _buildHoldingsList(),
            const SizedBox(height: 24),
            _buildDividendTracker(),
          ],
        ),
      ),
    );
  }

  Widget _buildPortfolioSummary() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Portfolio Overview',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _showRealTimeUpdates ? Colors.green : Colors.grey,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.circle,
                        size: 8,
                        color: _showRealTimeUpdates
                            ? Colors.white
                            : Colors.grey.shade600,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _showRealTimeUpdates ? 'Live' : 'Delayed',
                        style: TextStyle(
                          color: _showRealTimeUpdates
                              ? Colors.white
                              : Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'Total Value',
                    '\$127,450.32',
                    '+\$8,234.12',
                    '+6.9%',
                    Colors.green,
                    Icons.trending_up,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSummaryCard(
                    'Today\'s Change',
                    '+\$1,234.56',
                    'Since yesterday',
                    '+0.98%',
                    Colors.green,
                    Icons.arrow_upward,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'Total Invested',
                    '\$119,216.20',
                    'Cost basis',
                    '',
                    Colors.blue,
                    Icons.account_balance,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSummaryCard(
                    'Annual Dividend',
                    '\$3,456.78',
                    'Projected',
                    '2.71% yield',
                    Colors.purple,
                    Icons.payments,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    String subtitle,
    String change,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: color.withValues(alpha: 0.8),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          if (subtitle.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(fontSize: 10, color: Colors.grey[600]),
            ),
          ],
          if (change.isNotEmpty) ...[
            const SizedBox(height: 2),
            Text(
              change,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: change.startsWith('+') ? Colors.green : Colors.red,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPerformanceChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Performance Chart',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                Row(
                  children: ['1D', '1W', '1M', '3M', '1Y', 'ALL'].map((
                    timeframe,
                  ) {
                    final isSelected = timeframe == _selectedTimeframe;
                    return GestureDetector(
                      onTap: () =>
                          setState(() => _selectedTimeframe = timeframe),
                      child: Container(
                        margin: const EdgeInsets.only(left: 4),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: isSelected ? Colors.green : Colors.transparent,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          timeframe,
                          style: TextStyle(
                            fontSize: 12,
                            color: isSelected ? Colors.white : Colors.grey[600],
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Simulated chart area
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: CustomPaint(
                painter: _PortfolioChartPainter(),
                child: const Center(
                  child: Text(
                    'Portfolio Performance Chart\n($_selectedTimeframe timeframe)',
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAssetAllocation() {
    final allocations = [
      {
        'name': 'US Stocks',
        'percentage': 45.0,
        'value': 57352.64,
        'color': Colors.blue,
      },
      {
        'name': 'International Stocks',
        'percentage': 25.0,
        'value': 31862.58,
        'color': Colors.green,
      },
      {
        'name': 'Bonds',
        'percentage': 20.0,
        'value': 25490.06,
        'color': Colors.orange,
      },
      {
        'name': 'REITs',
        'percentage': 7.0,
        'value': 8921.52,
        'color': Colors.purple,
      },
      {
        'name': 'Cash',
        'percentage': 3.0,
        'value': 3823.52,
        'color': Colors.grey,
      },
    ];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Asset Allocation',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Allocation pie chart representation
            SizedBox(
              height: 120,
              child: Row(
                children: [
                  // Simulated pie chart
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: const Center(
                      child: Text(
                        'Asset\nAllocation\nChart',
                        textAlign: TextAlign.center,
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),

                  // Legend
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: allocations.map((allocation) {
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: Row(
                            children: [
                              Container(
                                width: 12,
                                height: 12,
                                decoration: BoxDecoration(
                                  color: allocation['color'] as Color,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  allocation['name'] as String,
                                  style: const TextStyle(fontSize: 12),
                                ),
                              ),
                              Text(
                                '${(allocation['percentage'] as double).toStringAsFixed(1)}%',
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHoldingsList() {
    final holdings = [
      {
        'symbol': 'AAPL',
        'name': 'Apple Inc.',
        'shares': 50,
        'price': 175.43,
        'change': 2.34,
        'value': 8771.50,
      },
      {
        'symbol': 'MSFT',
        'name': 'Microsoft Corp.',
        'shares': 30,
        'price': 378.85,
        'change': -1.23,
        'value': 11365.50,
      },
      {
        'symbol': 'GOOGL',
        'name': 'Alphabet Inc.',
        'shares': 25,
        'price': 142.56,
        'change': 0.89,
        'value': 3564.00,
      },
      {
        'symbol': 'TSLA',
        'name': 'Tesla Inc.',
        'shares': 15,
        'price': 248.42,
        'change': 5.67,
        'value': 3726.30,
      },
      {
        'symbol': 'NVDA',
        'name': 'NVIDIA Corp.',
        'shares': 20,
        'price': 875.28,
        'change': 12.45,
        'value': 17505.60,
      },
    ];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Holdings',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            ...holdings.map((holding) {
              final changeColor = (holding['change'] as double) >= 0
                  ? Colors.green
                  : Colors.red;
              final changePrefix = (holding['change'] as double) >= 0
                  ? '+'
                  : '';

              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    // Stock symbol and name
                    Expanded(
                      flex: 2,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            holding['symbol'] as String,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            holding['name'] as String,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Shares and price
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            '${holding['shares']} shares',
                            style: const TextStyle(fontSize: 12),
                          ),
                          Text(
                            '\$${(holding['price'] as double).toStringAsFixed(2)}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ),

                    // Change and total value
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            '$changePrefix${(holding['change'] as double).toStringAsFixed(2)}',
                            style: TextStyle(
                              color: changeColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '\$${(holding['value'] as double).toStringAsFixed(2)}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildDividendTracker() {
    if (!_showDividends) return const SizedBox.shrink();

    final dividends = [
      {
        'symbol': 'AAPL',
        'amount': 46.25,
        'date': 'Nov 15, 2024',
        'frequency': 'Quarterly',
      },
      {
        'symbol': 'MSFT',
        'amount': 22.50,
        'date': 'Dec 12, 2024',
        'frequency': 'Quarterly',
      },
      {'symbol': 'GOOGL', 'amount': 0.00, 'date': 'N/A', 'frequency': 'None'},
    ];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Dividend Tracker',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                Switch(
                  value: _showDividends,
                  onChanged: (value) => setState(() => _showDividends = value),
                ),
              ],
            ),
            const SizedBox(height: 16),

            ...dividends.map((dividend) {
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: Colors.purple.withValues(alpha: 0.1),
                  child: Text(
                    dividend['symbol'] as String,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.purple,
                    ),
                  ),
                ),
                title: Text(
                  '\$${(dividend['amount'] as double).toStringAsFixed(2)}',
                ),
                subtitle: Text(
                  '${dividend['frequency']} • ${dividend['date']}',
                ),
                trailing: dividend['amount'] as double > 0
                    ? const Icon(Icons.payments, color: Colors.green)
                    : const Icon(
                        Icons.remove_circle_outline,
                        color: Colors.grey,
                      ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  void _showAddInvestmentDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Investment'),
        content: const Text(
          'Investment addition dialog will be implemented here.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _refreshPortfolio() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Portfolio data refreshed'),
        backgroundColor: Colors.green,
      ),
    );
  }
}

/// Custom painter for portfolio performance chart
class _PortfolioChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.green
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    // Draw a simple upward trending line
    final path = Path();
    path.moveTo(20, size.height - 40);
    path.lineTo(size.width * 0.3, size.height - 60);
    path.lineTo(size.width * 0.6, size.height - 80);
    path.lineTo(size.width - 20, size.height - 100);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
