import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class VoiceSettingsScreen extends ConsumerWidget {
  const VoiceSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: Colors.blueGrey,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSettingsSection(context, 'Recording Quality', [
            _buildSettingsTile(
              context,
              'Audio Quality',
              'High (44.1 kHz)',
              Icons.high_quality,
              () => _showQualityDialog(context),
            ),
            _buildSettingsTile(
              context,
              'Audio Format',
              'MP3',
              Icons.audiotrack,
              () => _showFormatDialog(context),
            ),
            _buildSettingsTile(
              context,
              'Bitrate',
              '128 kbps',
              Icons.speed,
              () => _showBitrateDialog(context),
            ),
          ]),
          const SizedBox(height: 24),
          _buildSettingsSection(context, 'Recording Behavior', [
            _buildSwitchTile(
              context,
              'Auto-save Recordings',
              'Automatically save recordings when stopped',
              Icons.save,
              true,
              (value) {},
            ),
            _buildSwitchTile(
              context,
              'Background Recording',
              'Continue recording when app is minimized',
              Icons.play_circle_fill,
              false,
              (value) {},
            ),
            _buildSwitchTile(
              context,
              'Noise Reduction',
              'Reduce background noise during recording',
              Icons.noise_control_off,
              true,
              (value) {},
            ),
          ]),
          const SizedBox(height: 24),
          _buildSettingsSection(context, 'Storage', [
            _buildSettingsTile(
              context,
              'Storage Location',
              'Internal Storage',
              Icons.folder,
              () => _showStorageDialog(context),
            ),
            _buildSettingsTile(
              context,
              'Auto-delete Old Recordings',
              'After 30 days',
              Icons.delete_sweep,
              () => _showAutoDeleteDialog(context),
            ),
            _buildSettingsTile(
              context,
              'Storage Usage',
              '156 MB used',
              Icons.storage,
              () => _showStorageUsage(context),
            ),
          ]),
          const SizedBox(height: 24),
          _buildSettingsSection(context, 'Playback', [
            _buildSwitchTile(
              context,
              'Skip Silence',
              'Skip silent parts during playback',
              Icons.fast_forward,
              false,
              (value) {},
            ),
            _buildSettingsTile(
              context,
              'Playback Speed',
              '1.0x',
              Icons.speed,
              () => _showSpeedDialog(context),
            ),
          ]),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(
    BuildContext context,
    String title,
    List<Widget> children,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.blueGrey,
          ),
        ),
        const SizedBox(height: 8),
        Card(elevation: 2, child: Column(children: children)),
      ],
    );
  }

  Widget _buildSettingsTile(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: Colors.blueGrey),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  Widget _buildSwitchTile(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return SwitchListTile(
      secondary: Icon(icon, color: Colors.blueGrey),
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      activeColor: Colors.blueGrey,
    );
  }

  void _showQualityDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Audio Quality'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('Low (22.05 kHz)'),
              value: 'low',
              groupValue: 'high',
              onChanged: (value) {},
            ),
            RadioListTile<String>(
              title: const Text('Medium (44.1 kHz)'),
              value: 'medium',
              groupValue: 'high',
              onChanged: (value) {},
            ),
            RadioListTile<String>(
              title: const Text('High (48 kHz)'),
              value: 'high',
              groupValue: 'high',
              onChanged: (value) {},
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showFormatDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Audio Format'),
        content: const Text('Format selection will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showBitrateDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Bitrate'),
        content: const Text('Bitrate selection will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showStorageDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Storage Location'),
        content: const Text(
          'Storage location selection will be implemented here.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showAutoDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Auto-delete Settings'),
        content: const Text(
          'Auto-delete configuration will be implemented here.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showStorageUsage(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Storage Usage'),
        content: const Text('Storage usage details will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSpeedDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Playback Speed'),
        content: const Text('Speed selection will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }
}
