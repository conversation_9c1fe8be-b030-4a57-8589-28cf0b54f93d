import 'dart:convert';
import 'package:uuid/uuid.dart';

// Re-export from other model files for backward compatibility
export 'tool.dart';
export 'spreadsheet.dart';

/// Tools settings model
class ToolsSettings {
  final bool showFormulaBar;
  final bool highlightDependencies;
  final bool showFormulaErrors;
  final bool errorNotifications;
  final bool preserveFormatting;
  final bool preserveFormulas;
  final bool autoDetectDataTypes;
  final String defaultExportFormat;
  final int compressionLevel;
  final bool enableBackups;
  final bool encryptData;
  final bool debugMode;
  final bool performanceMetrics;

  const ToolsSettings({
    this.showFormulaBar = true,
    this.highlightDependencies = true,
    this.showFormulaErrors = true,
    this.errorNotifications = true,
    this.preserveFormatting = true,
    this.preserveFormulas = true,
    this.autoDetectDataTypes = true,
    this.defaultExportFormat = 'xlsx',
    this.compressionLevel = 6,
    this.enableBackups = true,
    this.encryptData = false,
    this.debugMode = false,
    this.performanceMetrics = false,
  });

  factory ToolsSettings.defaultSettings() {
    return const ToolsSettings();
  }

  ToolsSettings copyWith({
    bool? showFormulaBar,
    bool? highlightDependencies,
    bool? showFormulaErrors,
    bool? errorNotifications,
    bool? preserveFormatting,
    bool? preserveFormulas,
    bool? autoDetectDataTypes,
    String? defaultExportFormat,
    int? compressionLevel,
    bool? enableBackups,
    bool? encryptData,
    bool? debugMode,
    bool? performanceMetrics,
  }) {
    return ToolsSettings(
      showFormulaBar: showFormulaBar ?? this.showFormulaBar,
      highlightDependencies: highlightDependencies ?? this.highlightDependencies,
      showFormulaErrors: showFormulaErrors ?? this.showFormulaErrors,
      errorNotifications: errorNotifications ?? this.errorNotifications,
      preserveFormatting: preserveFormatting ?? this.preserveFormatting,
      preserveFormulas: preserveFormulas ?? this.preserveFormulas,
      autoDetectDataTypes: autoDetectDataTypes ?? this.autoDetectDataTypes,
      defaultExportFormat: defaultExportFormat ?? this.defaultExportFormat,
      compressionLevel: compressionLevel ?? this.compressionLevel,
      enableBackups: enableBackups ?? this.enableBackups,
      encryptData: encryptData ?? this.encryptData,
      debugMode: debugMode ?? this.debugMode,
      performanceMetrics: performanceMetrics ?? this.performanceMetrics,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'showFormulaBar': showFormulaBar,
      'highlightDependencies': highlightDependencies,
      'showFormulaErrors': showFormulaErrors,
      'errorNotifications': errorNotifications,
      'preserveFormatting': preserveFormatting,
      'preserveFormulas': preserveFormulas,
      'autoDetectDataTypes': autoDetectDataTypes,
      'defaultExportFormat': defaultExportFormat,
      'compressionLevel': compressionLevel,
      'enableBackups': enableBackups,
      'encryptData': encryptData,
      'debugMode': debugMode,
      'performanceMetrics': performanceMetrics,
    };
  }

  factory ToolsSettings.fromMap(Map<String, dynamic> map) {
    return ToolsSettings(
      showFormulaBar: map['showFormulaBar'] ?? true,
      highlightDependencies: map['highlightDependencies'] ?? true,
      showFormulaErrors: map['showFormulaErrors'] ?? true,
      errorNotifications: map['errorNotifications'] ?? true,
      preserveFormatting: map['preserveFormatting'] ?? true,
      preserveFormulas: map['preserveFormulas'] ?? true,
      autoDetectDataTypes: map['autoDetectDataTypes'] ?? true,
      defaultExportFormat: map['defaultExportFormat'] ?? 'xlsx',
      compressionLevel: map['compressionLevel']?.toInt() ?? 6,
      enableBackups: map['enableBackups'] ?? true,
      encryptData: map['encryptData'] ?? false,
      debugMode: map['debugMode'] ?? false,
      performanceMetrics: map['performanceMetrics'] ?? false,
    );
  }

  String toJson() => json.encode(toMap());
  factory ToolsSettings.fromJson(String source) => ToolsSettings.fromMap(json.decode(source));
}

/// Template model for tools builder
class Template {
  final String id;
  final String name;
  final String description;
  final String category;
  final String previewImage;
  final DateTime createdAt;
  final int downloads;
  final double rating;
  final Map<String, dynamic> metadata;

  Template({
    String? id,
    required this.name,
    required this.description,
    required this.category,
    this.previewImage = '',
    DateTime? createdAt,
    this.downloads = 0,
    this.rating = 0.0,
    this.metadata = const {},
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now();

  Template copyWith({
    String? id,
    String? name,
    String? description,
    String? category,
    String? previewImage,
    DateTime? createdAt,
    int? downloads,
    double? rating,
    Map<String, dynamic>? metadata,
  }) {
    return Template(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      previewImage: previewImage ?? this.previewImage,
      createdAt: createdAt ?? this.createdAt,
      downloads: downloads ?? this.downloads,
      rating: rating ?? this.rating,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'previewImage': previewImage,
      'createdAt': createdAt.toIso8601String(),
      'downloads': downloads,
      'rating': rating,
      'metadata': metadata,
    };
  }

  factory Template.fromMap(Map<String, dynamic> map) {
    return Template(
      id: map['id'],
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      category: map['category'] ?? '',
      previewImage: map['previewImage'] ?? '',
      createdAt: DateTime.parse(map['createdAt']),
      downloads: map['downloads']?.toInt() ?? 0,
      rating: map['rating']?.toDouble() ?? 0.0,
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
    );
  }

  String toJson() => json.encode(toMap());
  factory Template.fromJson(String source) => Template.fromMap(json.decode(source));
}

/// Cell format model for spreadsheet formatting
class CellFormat {
  final String? fontFamily;
  final double? fontSize;
  final bool bold;
  final bool italic;
  final bool underline;
  final String? textColor;
  final String? backgroundColor;
  final String? borderStyle;
  final String? borderColor;
  final String alignment;
  final String numberFormat;

  const CellFormat({
    this.fontFamily,
    this.fontSize,
    this.bold = false,
    this.italic = false,
    this.underline = false,
    this.textColor,
    this.backgroundColor,
    this.borderStyle,
    this.borderColor,
    this.alignment = 'left',
    this.numberFormat = 'general',
  });

  CellFormat copyWith({
    String? fontFamily,
    double? fontSize,
    bool? bold,
    bool? italic,
    bool? underline,
    String? textColor,
    String? backgroundColor,
    String? borderStyle,
    String? borderColor,
    String? alignment,
    String? numberFormat,
  }) {
    return CellFormat(
      fontFamily: fontFamily ?? this.fontFamily,
      fontSize: fontSize ?? this.fontSize,
      bold: bold ?? this.bold,
      italic: italic ?? this.italic,
      underline: underline ?? this.underline,
      textColor: textColor ?? this.textColor,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      borderStyle: borderStyle ?? this.borderStyle,
      borderColor: borderColor ?? this.borderColor,
      alignment: alignment ?? this.alignment,
      numberFormat: numberFormat ?? this.numberFormat,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'fontFamily': fontFamily,
      'fontSize': fontSize,
      'bold': bold,
      'italic': italic,
      'underline': underline,
      'textColor': textColor,
      'backgroundColor': backgroundColor,
      'borderStyle': borderStyle,
      'borderColor': borderColor,
      'alignment': alignment,
      'numberFormat': numberFormat,
    };
  }

  factory CellFormat.fromMap(Map<String, dynamic> map) {
    return CellFormat(
      fontFamily: map['fontFamily'],
      fontSize: map['fontSize']?.toDouble(),
      bold: map['bold'] ?? false,
      italic: map['italic'] ?? false,
      underline: map['underline'] ?? false,
      textColor: map['textColor'],
      backgroundColor: map['backgroundColor'],
      borderStyle: map['borderStyle'],
      borderColor: map['borderColor'],
      alignment: map['alignment'] ?? 'left',
      numberFormat: map['numberFormat'] ?? 'general',
    );
  }

  String toJson() => json.encode(toMap());
  factory CellFormat.fromJson(String source) => CellFormat.fromMap(json.decode(source));
}
