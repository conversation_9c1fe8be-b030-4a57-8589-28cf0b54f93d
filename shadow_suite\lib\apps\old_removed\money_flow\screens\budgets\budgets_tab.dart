import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_theme.dart';
import '../../services/money_flow_providers.dart';
import '../../models/budget.dart';
import '../../models/goal.dart';

class BudgetsTab extends ConsumerStatefulWidget {
  const BudgetsTab({super.key});

  @override
  ConsumerState<BudgetsTab> createState() => _BudgetsTabState();
}

class _BudgetsTabState extends ConsumerState<BudgetsTab>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Column(
        children: [
          Container(
            color: Colors.white,
            child: TabBar(
              controller: _tabController,
              labelColor: AppTheme.moneyFlowColor,
              unselectedLabelColor: Colors.grey,
              indicatorColor: AppTheme.moneyFlowColor,
              tabs: const [
                Tab(icon: Icon(Icons.pie_chart), text: 'Budgets'),
                Tab(icon: Icon(Icons.flag), text: 'Goals'),
              ],
            ),
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [_buildBudgetsView(), _buildGoalsView()],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetsView() {
    final budgetsAsync = ref.watch(budgetsProvider);

    return budgetsAsync.when(
      data: (budgets) => budgets.isEmpty
          ? _buildBudgetsEmptyState(context)
          : _buildBudgetsList(context, ref, budgets),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildBudgetsErrorState(context, ref, error),
    );
  }

  Widget _buildGoalsView() {
    final goalsAsync = ref.watch(goalsProvider);

    return goalsAsync.when(
      data: (goals) => goals.isEmpty
          ? _buildGoalsEmptyState(context)
          : _buildGoalsList(context, ref, goals),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildGoalsErrorState(context, ref, error),
    );
  }

  Widget _buildBudgetsEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.pie_chart,
            size: 120,
            color: AppTheme.moneyFlowColor.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 24),
          Text(
            'No Budgets Yet',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: AppTheme.moneyFlowColor,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Create your first budget to track your spending goals',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () => _showAddBudgetDialog(),
            icon: const Icon(Icons.add),
            label: const Text('Create Budget'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.moneyFlowColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddBudgetDialog() {
    showDialog(context: context, builder: (context) => AddBudgetDialog());
  }

  Widget _buildBudgetsList(
    BuildContext context,
    WidgetRef ref,
    List<Budget> budgets,
  ) {
    final activeBudgets = budgets
        .where((b) => b.status == BudgetStatus.active)
        .toList();
    final completedBudgets = budgets
        .where((b) => b.status == BudgetStatus.completed)
        .toList();
    final exceededBudgets = budgets
        .where((b) => b.status == BudgetStatus.exceeded)
        .toList();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBudgetsSummary(context, budgets),
          const SizedBox(height: 24),
          _buildBudgetStatusTabs(
            context,
            activeBudgets,
            completedBudgets,
            exceededBudgets,
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetsSummary(BuildContext context, List<Budget> budgets) {
    final totalBudgeted = budgets.fold(
      0.0,
      (sum, budget) => sum + budget.amount,
    );
    final totalSpent = budgets.fold(0.0, (sum, budget) => sum + budget.spent);
    final remaining = totalBudgeted - totalSpent;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Budget Overview',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.moneyFlowColor,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    'Total Budgeted',
                    totalBudgeted,
                    Icons.account_balance_wallet,
                    AppTheme.moneyFlowColor,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    'Total Spent',
                    totalSpent,
                    Icons.shopping_cart,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    'Remaining',
                    remaining,
                    Icons.savings,
                    remaining >= 0 ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(
    BuildContext context,
    String title,
    double amount,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(height: 8),
        Text(
          '\$${amount.toStringAsFixed(2)}',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildBudgetStatusTabs(
    BuildContext context,
    List<Budget> activeBudgets,
    List<Budget> completedBudgets,
    List<Budget> exceededBudgets,
  ) {
    return Expanded(
      child: DefaultTabController(
        length: 3,
        child: Column(
          children: [
            TabBar(
              labelColor: AppTheme.moneyFlowColor,
              unselectedLabelColor: Colors.grey,
              indicatorColor: AppTheme.moneyFlowColor,
              tabs: [
                Tab(text: 'Active (${activeBudgets.length})'),
                Tab(text: 'Completed (${completedBudgets.length})'),
                Tab(text: 'Exceeded (${exceededBudgets.length})'),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: TabBarView(
                children: [
                  _buildBudgetList(context, activeBudgets),
                  _buildBudgetList(context, completedBudgets),
                  _buildBudgetList(context, exceededBudgets),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBudgetList(BuildContext context, List<Budget> budgets) {
    if (budgets.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.pie_chart_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No budgets in this category',
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: budgets.length,
      itemBuilder: (context, index) {
        final budget = budgets[index];
        return _buildBudgetCard(context, budget);
      },
    );
  }

  Widget _buildBudgetCard(BuildContext context, Budget budget) {
    final color = Color(int.parse(budget.color.replaceFirst('#', '0xFF')));
    final progress = budget.amount > 0 ? budget.spent / budget.amount : 0.0;
    final progressColor = progress > 1.0
        ? Colors.red
        : (progress > 0.8 ? Colors.orange : Colors.green);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: color.withValues(alpha: 0.1),
                  child: Icon(Icons.category, color: color),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        budget.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        '${budget.category} • ${budget.period.name.toUpperCase()}',
                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '\$${budget.spent.toStringAsFixed(2)} / \$${budget.amount.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: progressColor,
                      ),
                    ),
                    Text(
                      '${(progress * 100).toStringAsFixed(1)}%',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: progress.clamp(0.0, 1.0),
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(progressColor),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Remaining: \$${(budget.amount - budget.spent).toStringAsFixed(2)}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
                Text(
                  budget.formattedDateRange,
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Goals methods
  Widget _buildGoalsEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.flag,
            size: 120,
            color: AppTheme.moneyFlowColor.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 24),
          Text(
            'No Goals Yet',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: AppTheme.moneyFlowColor,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Create your first savings goal to track your progress',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () => _showAddGoalDialog(),
            icon: const Icon(Icons.add),
            label: const Text('Create Goal'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.moneyFlowColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGoalsErrorState(
    BuildContext context,
    WidgetRef ref,
    Object error,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
          const SizedBox(height: 16),
          Text(
            'Error loading goals',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => ref.refresh(goalsProvider),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetsErrorState(
    BuildContext context,
    WidgetRef ref,
    Object error,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
          const SizedBox(height: 16),
          Text(
            'Error loading budgets',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => ref.refresh(budgetsProvider),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildGoalsList(
    BuildContext context,
    WidgetRef ref,
    List<Goal> goals,
  ) {
    final activeGoals = goals
        .where((g) => g.status == GoalStatus.active)
        .toList();
    final completedGoals = goals
        .where((g) => g.status == GoalStatus.completed)
        .toList();
    final pausedGoals = goals
        .where((g) => g.status == GoalStatus.paused)
        .toList();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildGoalsSummary(context, goals),
          const SizedBox(height: 24),
          _buildGoalStatusTabs(
            context,
            activeGoals,
            completedGoals,
            pausedGoals,
          ),
        ],
      ),
    );
  }

  Widget _buildGoalsSummary(BuildContext context, List<Goal> goals) {
    final totalTarget = goals.fold(0.0, (sum, goal) => sum + goal.targetAmount);
    final totalCurrent = goals.fold(
      0.0,
      (sum, goal) => sum + goal.currentAmount,
    );
    final remaining = totalTarget - totalCurrent;
    final overallProgress = totalTarget > 0
        ? (totalCurrent / totalTarget * 100)
        : 0.0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Goals Overview',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.moneyFlowColor,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildGoalSummaryItem(
                    context,
                    'Total Target',
                    totalTarget,
                    Icons.flag,
                    AppTheme.moneyFlowColor,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildGoalSummaryItem(
                    context,
                    'Total Saved',
                    totalCurrent,
                    Icons.savings,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildGoalSummaryItem(
                    context,
                    'Remaining',
                    remaining,
                    Icons.trending_up,
                    Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Overall Progress',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${overallProgress.toStringAsFixed(1)}%',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.moneyFlowColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalSummaryItem(
    BuildContext context,
    String title,
    double amount,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(height: 8),
        Text(
          '\$${amount.toStringAsFixed(2)}',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildGoalStatusTabs(
    BuildContext context,
    List<Goal> activeGoals,
    List<Goal> completedGoals,
    List<Goal> pausedGoals,
  ) {
    return Expanded(
      child: DefaultTabController(
        length: 3,
        child: Column(
          children: [
            TabBar(
              labelColor: AppTheme.moneyFlowColor,
              unselectedLabelColor: Colors.grey,
              indicatorColor: AppTheme.moneyFlowColor,
              tabs: [
                Tab(text: 'Active (${activeGoals.length})'),
                Tab(text: 'Completed (${completedGoals.length})'),
                Tab(text: 'Paused (${pausedGoals.length})'),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: TabBarView(
                children: [
                  _buildGoalList(context, activeGoals),
                  _buildGoalList(context, completedGoals),
                  _buildGoalList(context, pausedGoals),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalList(BuildContext context, List<Goal> goals) {
    if (goals.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.flag_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No goals in this category',
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: goals.length,
      itemBuilder: (context, index) {
        final goal = goals[index];
        return _buildGoalCard(context, goal);
      },
    );
  }

  Widget _buildGoalCard(BuildContext context, Goal goal) {
    final color = Color(int.parse(goal.color.replaceFirst('#', '0xFF')));
    final progress = goal.progressPercentage / 100;
    final progressColor = goal.isCompleted
        ? Colors.green
        : (goal.isOverdue ? Colors.red : AppTheme.moneyFlowColor);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: Text(
                      goal.category.icon,
                      style: const TextStyle(fontSize: 24),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        goal.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        '${goal.category.displayName} • ${goal.formattedTimeRemaining}',
                        style: TextStyle(
                          color: goal.isOverdue ? Colors.red : Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '\$${goal.currentAmount.toStringAsFixed(2)} / \$${goal.targetAmount.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: progressColor,
                      ),
                    ),
                    Text(
                      '${goal.progressPercentage.toStringAsFixed(1)}%',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: progress.clamp(0.0, 1.0),
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(progressColor),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Remaining: \$${goal.amountRemaining.toStringAsFixed(2)}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
                Text(
                  'Target: ${goal.targetDate.day}/${goal.targetDate.month}/${goal.targetDate.year}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showAddGoalDialog() {
    // Implementation for add goal dialog will be added
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add Goal dialog will be implemented')),
    );
  }
}

/// Dialog for adding a new budget
class AddBudgetDialog extends StatefulWidget {
  const AddBudgetDialog({super.key});

  @override
  State<AddBudgetDialog> createState() => _AddBudgetDialogState();
}

class _AddBudgetDialogState extends State<AddBudgetDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _amountController = TextEditingController();
  String _selectedCategory = 'Food';
  String _selectedPeriod = 'Monthly';

  final List<String> _categories = [
    'Food',
    'Transportation',
    'Entertainment',
    'Shopping',
    'Bills',
    'Healthcare',
    'Education',
    'Other',
  ];

  final List<String> _periods = ['Weekly', 'Monthly', 'Quarterly', 'Yearly'];

  @override
  void dispose() {
    _nameController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create Budget'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Budget Name',
                hintText: 'Enter budget name',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a budget name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: const InputDecoration(labelText: 'Category'),
              items: _categories.map((category) {
                return DropdownMenuItem(value: category, child: Text(category));
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _amountController,
              decoration: const InputDecoration(
                labelText: 'Amount',
                hintText: 'Enter budget amount',
                prefixText: '\$',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter an amount';
                }
                if (double.tryParse(value) == null) {
                  return 'Please enter a valid number';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedPeriod,
              decoration: const InputDecoration(labelText: 'Period'),
              items: _periods.map((period) {
                return DropdownMenuItem(value: period, child: Text(period));
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedPeriod = value!;
                });
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(onPressed: _createBudget, child: const Text('Create')),
      ],
    );
  }

  void _createBudget() {
    if (_formKey.currentState!.validate()) {
      // TODO: Create budget with the provided data
      // For now, just show a success message
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Budget "${_nameController.text}" created successfully',
          ),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}
