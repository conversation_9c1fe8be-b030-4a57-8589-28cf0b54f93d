/// Study session model
class StudySession {
  final String id;
  final DateTime startTime;
  final Duration duration;
  final List<String> studiedHadiths;
  final double comprehensionScore;
  final List<String> bookmarkedHadiths;
  final String notes;
  final DateTime createdAt;

  const StudySession({
    required this.id,
    required this.startTime,
    required this.duration,
    required this.studiedHadiths,
    required this.comprehensionScore,
    required this.bookmarkedHadiths,
    this.notes = '',
    required this.createdAt,
  });

  StudySession copyWith({
    String? id,
    DateTime? startTime,
    Duration? duration,
    List<String>? studiedHadiths,
    double? comprehensionScore,
    List<String>? bookmarkedHadiths,
    String? notes,
    DateTime? createdAt,
  }) {
    return StudySession(
      id: id ?? this.id,
      startTime: startTime ?? this.startTime,
      duration: duration ?? this.duration,
      studiedHadiths: studiedHadiths ?? this.studiedHadiths,
      comprehensionScore: comprehensionScore ?? this.comprehensionScore,
      bookmarkedHadiths: bookmarkedHadiths ?? this.bookmarkedHadiths,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'startTime': startTime.toIso8601String(),
      'duration': duration.inMinutes,
      'studiedHadiths': studiedHadiths,
      'comprehensionScore': comprehensionScore,
      'bookmarkedHadiths': bookmarkedHadiths,
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory StudySession.fromJson(Map<String, dynamic> json) {
    return StudySession(
      id: json['id'],
      startTime: DateTime.parse(json['startTime']),
      duration: Duration(minutes: json['duration']),
      studiedHadiths: List<String>.from(json['studiedHadiths']),
      comprehensionScore: json['comprehensionScore'].toDouble(),
      bookmarkedHadiths: List<String>.from(json['bookmarkedHadiths']),
      notes: json['notes'] ?? '',
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Hadith bookmark model
class HadithBookmark {
  final String id;
  final String hadithText;
  final String narrator;
  final String source;
  final String category;
  final String personalNotes;
  final List<String> tags;
  final DateTime createdAt;

  const HadithBookmark({
    required this.id,
    required this.hadithText,
    required this.narrator,
    required this.source,
    required this.category,
    this.personalNotes = '',
    this.tags = const [],
    required this.createdAt,
  });

  HadithBookmark copyWith({
    String? id,
    String? hadithText,
    String? narrator,
    String? source,
    String? category,
    String? personalNotes,
    List<String>? tags,
    DateTime? createdAt,
  }) {
    return HadithBookmark(
      id: id ?? this.id,
      hadithText: hadithText ?? this.hadithText,
      narrator: narrator ?? this.narrator,
      source: source ?? this.source,
      category: category ?? this.category,
      personalNotes: personalNotes ?? this.personalNotes,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'hadithText': hadithText,
      'narrator': narrator,
      'source': source,
      'category': category,
      'personalNotes': personalNotes,
      'tags': tags,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory HadithBookmark.fromJson(Map<String, dynamic> json) {
    return HadithBookmark(
      id: json['id'],
      hadithText: json['hadithText'],
      narrator: json['narrator'],
      source: json['source'],
      category: json['category'],
      personalNotes: json['personalNotes'] ?? '',
      tags: List<String>.from(json['tags'] ?? []),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Memorized hadith model
class MemorizedHadith {
  final String id;
  final String hadithText;
  final String narrator;
  final DateTime memorizedAt;
  final double confidenceLevel;
  final int reviewCount;
  final DateTime lastReviewed;
  final String memorizationNotes;

  const MemorizedHadith({
    required this.id,
    required this.hadithText,
    required this.narrator,
    required this.memorizedAt,
    required this.confidenceLevel,
    required this.reviewCount,
    required this.lastReviewed,
    this.memorizationNotes = '',
  });

  MemorizedHadith copyWith({
    String? id,
    String? hadithText,
    String? narrator,
    DateTime? memorizedAt,
    double? confidenceLevel,
    int? reviewCount,
    DateTime? lastReviewed,
    String? memorizationNotes,
  }) {
    return MemorizedHadith(
      id: id ?? this.id,
      hadithText: hadithText ?? this.hadithText,
      narrator: narrator ?? this.narrator,
      memorizedAt: memorizedAt ?? this.memorizedAt,
      confidenceLevel: confidenceLevel ?? this.confidenceLevel,
      reviewCount: reviewCount ?? this.reviewCount,
      lastReviewed: lastReviewed ?? this.lastReviewed,
      memorizationNotes: memorizationNotes ?? this.memorizationNotes,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'hadithText': hadithText,
      'narrator': narrator,
      'memorizedAt': memorizedAt.toIso8601String(),
      'confidenceLevel': confidenceLevel,
      'reviewCount': reviewCount,
      'lastReviewed': lastReviewed.toIso8601String(),
      'memorizationNotes': memorizationNotes,
    };
  }

  factory MemorizedHadith.fromJson(Map<String, dynamic> json) {
    return MemorizedHadith(
      id: json['id'],
      hadithText: json['hadithText'],
      narrator: json['narrator'],
      memorizedAt: DateTime.parse(json['memorizedAt']),
      confidenceLevel: json['confidenceLevel'].toDouble(),
      reviewCount: json['reviewCount'],
      lastReviewed: DateTime.parse(json['lastReviewed']),
      memorizationNotes: json['memorizationNotes'] ?? '',
    );
  }
}

/// User learning profile
class UserLearningProfile {
  final String id;
  final LearningLevel currentLevel;
  final List<String> preferredNarrators;
  final List<String> favoriteCategories;
  final int dailyStudyGoal;
  final bool enableArabicText;
  final bool enableTransliteration;
  final String preferredLanguage;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UserLearningProfile({
    required this.id,
    required this.currentLevel,
    required this.preferredNarrators,
    required this.favoriteCategories,
    required this.dailyStudyGoal,
    required this.enableArabicText,
    required this.enableTransliteration,
    required this.preferredLanguage,
    required this.createdAt,
    required this.updatedAt,
  });

  UserLearningProfile copyWith({
    String? id,
    LearningLevel? currentLevel,
    List<String>? preferredNarrators,
    List<String>? favoriteCategories,
    int? dailyStudyGoal,
    bool? enableArabicText,
    bool? enableTransliteration,
    String? preferredLanguage,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserLearningProfile(
      id: id ?? this.id,
      currentLevel: currentLevel ?? this.currentLevel,
      preferredNarrators: preferredNarrators ?? this.preferredNarrators,
      favoriteCategories: favoriteCategories ?? this.favoriteCategories,
      dailyStudyGoal: dailyStudyGoal ?? this.dailyStudyGoal,
      enableArabicText: enableArabicText ?? this.enableArabicText,
      enableTransliteration:
          enableTransliteration ?? this.enableTransliteration,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'currentLevel': currentLevel.name,
      'preferredNarrators': preferredNarrators,
      'favoriteCategories': favoriteCategories,
      'dailyStudyGoal': dailyStudyGoal,
      'enableArabicText': enableArabicText,
      'enableTransliteration': enableTransliteration,
      'preferredLanguage': preferredLanguage,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory UserLearningProfile.fromJson(Map<String, dynamic> json) {
    return UserLearningProfile(
      id: json['id'],
      currentLevel: LearningLevel.values.firstWhere(
        (e) => e.name == json['currentLevel'],
      ),
      preferredNarrators: List<String>.from(json['preferredNarrators']),
      favoriteCategories: List<String>.from(json['favoriteCategories']),
      dailyStudyGoal: json['dailyStudyGoal'],
      enableArabicText: json['enableArabicText'],
      enableTransliteration: json['enableTransliteration'],
      preferredLanguage: json['preferredLanguage'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }
}

/// Enums
enum LearningLevel { beginner, intermediate, advanced }

enum HadithGrade { sahih, hasan, daif, mawdu }

/// Hadith recommendation model
class HadithRecommendation {
  final String id;
  final RecommendationType type;
  final String title;
  final String description;
  final Priority priority;
  final double confidence;
  final DateTime createdAt;

  const HadithRecommendation({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.priority,
    required this.confidence,
    required this.createdAt,
  });

  HadithRecommendation copyWith({
    String? id,
    RecommendationType? type,
    String? title,
    String? description,
    Priority? priority,
    double? confidence,
    DateTime? createdAt,
  }) {
    return HadithRecommendation(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      priority: priority ?? this.priority,
      confidence: confidence ?? this.confidence,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'priority': priority.name,
      'confidence': confidence,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory HadithRecommendation.fromJson(Map<String, dynamic> json) {
    return HadithRecommendation(
      id: json['id'],
      type: RecommendationType.values.firstWhere((e) => e.name == json['type']),
      title: json['title'],
      description: json['description'],
      priority: Priority.values.firstWhere((e) => e.name == json['priority']),
      confidence: json['confidence'].toDouble(),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Recommendation type enum
enum RecommendationType {
  learning,
  thematic,
  narrator,
  difficulty,
  complementary,
}

/// Priority enum
enum Priority { low, medium, high, urgent }

/// Hadith insight model
class HadithInsight {
  final String id;
  final String title;
  final String description;
  final double confidence;
  final DateTime createdAt;

  const HadithInsight({
    required this.id,
    required this.title,
    required this.description,
    required this.confidence,
    required this.createdAt,
  });

  HadithInsight copyWith({
    String? id,
    String? title,
    String? description,
    double? confidence,
    DateTime? createdAt,
  }) {
    return HadithInsight(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      confidence: confidence ?? this.confidence,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'confidence': confidence,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory HadithInsight.fromJson(Map<String, dynamic> json) {
    return HadithInsight(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      confidence: json['confidence'].toDouble(),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Authentication analysis model
class AuthenticationAnalysis {
  final String id;
  final String hadithId;
  final AuthenticityLevel level;
  final double confidence;
  final List<String> reasons;
  final DateTime createdAt;

  const AuthenticationAnalysis({
    required this.id,
    required this.hadithId,
    required this.level,
    required this.confidence,
    required this.reasons,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'hadithId': hadithId,
      'level': level.name,
      'confidence': confidence,
      'reasons': reasons,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory AuthenticationAnalysis.fromJson(Map<String, dynamic> json) {
    return AuthenticationAnalysis(
      id: json['id'],
      hadithId: json['hadithId'],
      level: AuthenticityLevel.values.firstWhere(
        (e) => e.name == json['level'],
      ),
      confidence: json['confidence'].toDouble(),
      reasons: List<String>.from(json['reasons']),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Authenticity level enum
enum AuthenticityLevel { authentic, good, weak, fabricated }

/// Narrator reliability model
class NarratorReliability {
  final String id;
  final String narratorName;
  final ReliabilityLevel level;
  final double confidence;
  final List<String> reasons;
  final DateTime createdAt;

  const NarratorReliability({
    required this.id,
    required this.narratorName,
    required this.level,
    required this.confidence,
    required this.reasons,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'narratorName': narratorName,
      'level': level.name,
      'confidence': confidence,
      'reasons': reasons,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory NarratorReliability.fromJson(Map<String, dynamic> json) {
    return NarratorReliability(
      id: json['id'],
      narratorName: json['narratorName'],
      level: ReliabilityLevel.values.firstWhere((e) => e.name == json['level']),
      confidence: json['confidence'].toDouble(),
      reasons: List<String>.from(json['reasons']),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Reliability level enum
enum ReliabilityLevel { high, medium, low, unknown }

/// Chain analysis model
class ChainAnalysis {
  final String id;
  final String hadithId;
  final List<String> narratorChain;
  final double reliability;
  final List<String> issues;
  final DateTime createdAt;

  const ChainAnalysis({
    required this.id,
    required this.hadithId,
    required this.narratorChain,
    required this.reliability,
    required this.issues,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'hadithId': hadithId,
      'narratorChain': narratorChain,
      'reliability': reliability,
      'issues': issues,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory ChainAnalysis.fromJson(Map<String, dynamic> json) {
    return ChainAnalysis(
      id: json['id'],
      hadithId: json['hadithId'],
      narratorChain: List<String>.from(json['narratorChain']),
      reliability: json['reliability'].toDouble(),
      issues: List<String>.from(json['issues']),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Textual analysis model
class TextualAnalysis {
  final String id;
  final String hadithId;
  final String analysis;
  final double confidence;
  final List<String> keywords;
  final DateTime createdAt;

  const TextualAnalysis({
    required this.id,
    required this.hadithId,
    required this.analysis,
    required this.confidence,
    required this.keywords,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'hadithId': hadithId,
      'analysis': analysis,
      'confidence': confidence,
      'keywords': keywords,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory TextualAnalysis.fromJson(Map<String, dynamic> json) {
    return TextualAnalysis(
      id: json['id'],
      hadithId: json['hadithId'],
      analysis: json['analysis'],
      confidence: json['confidence'].toDouble(),
      keywords: List<String>.from(json['keywords']),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Scholarly opinion model
class ScholarlyOpinion {
  final String id;
  final String scholarName;
  final String opinion;
  final String hadithId;
  final DateTime createdAt;

  const ScholarlyOpinion({
    required this.id,
    required this.scholarName,
    required this.opinion,
    required this.hadithId,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'scholarName': scholarName,
      'opinion': opinion,
      'hadithId': hadithId,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory ScholarlyOpinion.fromJson(Map<String, dynamic> json) {
    return ScholarlyOpinion(
      id: json['id'],
      scholarName: json['scholarName'],
      opinion: json['opinion'],
      hadithId: json['hadithId'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Study analytics model
class StudyAnalytics {
  final String id;
  final int totalSessions;
  final Duration totalTime;
  final int hadithsStudied;
  final double averageScore;
  final List<String> topTopics;
  final DateTime createdAt;

  const StudyAnalytics({
    required this.id,
    required this.totalSessions,
    required this.totalTime,
    required this.hadithsStudied,
    required this.averageScore,
    required this.topTopics,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'totalSessions': totalSessions,
      'totalTime': totalTime.inMilliseconds,
      'hadithsStudied': hadithsStudied,
      'averageScore': averageScore,
      'topTopics': topTopics,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory StudyAnalytics.fromJson(Map<String, dynamic> json) {
    return StudyAnalytics(
      id: json['id'],
      totalSessions: json['totalSessions'],
      totalTime: Duration(milliseconds: json['totalTime']),
      hadithsStudied: json['hadithsStudied'],
      averageScore: json['averageScore'].toDouble(),
      topTopics: List<String>.from(json['topTopics']),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Weekly study progress model
class WeeklyStudyProgress {
  final String id;
  final DateTime weekStart;
  final int sessionsCompleted;
  final Duration totalTime;
  final int hadithsStudied;
  final double averageScore;
  final DateTime createdAt;

  const WeeklyStudyProgress({
    required this.id,
    required this.weekStart,
    required this.sessionsCompleted,
    required this.totalTime,
    required this.hadithsStudied,
    required this.averageScore,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'weekStart': weekStart.toIso8601String(),
      'sessionsCompleted': sessionsCompleted,
      'totalTime': totalTime.inMilliseconds,
      'hadithsStudied': hadithsStudied,
      'averageScore': averageScore,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory WeeklyStudyProgress.fromJson(Map<String, dynamic> json) {
    return WeeklyStudyProgress(
      id: json['id'],
      weekStart: DateTime.parse(json['weekStart']),
      sessionsCompleted: json['sessionsCompleted'],
      totalTime: Duration(milliseconds: json['totalTime']),
      hadithsStudied: json['hadithsStudied'],
      averageScore: json['averageScore'].toDouble(),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Monthly study progress model
class MonthlyStudyProgress {
  final String id;
  final DateTime monthStart;
  final int sessionsCompleted;
  final Duration totalTime;
  final int hadithsStudied;
  final double averageScore;
  final DateTime createdAt;

  const MonthlyStudyProgress({
    required this.id,
    required this.monthStart,
    required this.sessionsCompleted,
    required this.totalTime,
    required this.hadithsStudied,
    required this.averageScore,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'monthStart': monthStart.toIso8601String(),
      'sessionsCompleted': sessionsCompleted,
      'totalTime': totalTime.inMilliseconds,
      'hadithsStudied': hadithsStudied,
      'averageScore': averageScore,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory MonthlyStudyProgress.fromJson(Map<String, dynamic> json) {
    return MonthlyStudyProgress(
      id: json['id'],
      monthStart: DateTime.parse(json['monthStart']),
      sessionsCompleted: json['sessionsCompleted'],
      totalTime: Duration(milliseconds: json['totalTime']),
      hadithsStudied: json['hadithsStudied'],
      averageScore: json['averageScore'].toDouble(),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}
