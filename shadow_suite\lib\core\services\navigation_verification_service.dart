import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../models/app_navigation.dart';

/// Service to verify all navigation routes are properly connected
class NavigationVerificationService {
  static final Map<String, bool> _routeStatus = {};
  static final List<String> _brokenRoutes = [];
  static final List<String> _workingRoutes = [];

  /// Verify all navigation routes
  static Future<NavigationVerificationResult> verifyAllRoutes(
    BuildContext context,
  ) async {
    _routeStatus.clear();
    _brokenRoutes.clear();
    _workingRoutes.clear();

    // Get all routes from navigation items
    final allRoutes = _getAllRoutes();

    // Test each route
    for (final route in allRoutes) {
      final isWorking = await _testRoute(context, route);
      _routeStatus[route] = isWorking;

      if (isWorking) {
        _workingRoutes.add(route);
      } else {
        _brokenRoutes.add(route);
      }
    }

    return NavigationVerificationResult(
      totalRoutes: allRoutes.length,
      workingRoutes: _workingRoutes.length,
      brokenRoutes: _brokenRoutes.length,
      routeStatus: Map.from(_routeStatus),
      brokenRoutesList: List.from(_brokenRoutes),
      workingRoutesList: List.from(_workingRoutes),
    );
  }

  /// Get all routes from navigation items
  static List<String> _getAllRoutes() {
    final routes = <String>[];

    // Main navigation routes
    for (final item in AppNavigation.mainItems) {
      final route = _getSectionRoute(item.section);
      if (route.isNotEmpty) {
        routes.add(route);
      }

      // Add sub-routes
      if (item.subItems != null) {
        for (final subItem in item.subItems!) {
          routes.add(subItem.route);
        }
      }
    }

    // App navigation routes
    for (final item in AppNavigation.appItems) {
      final route = _getSectionRoute(item.section);
      if (route.isNotEmpty) {
        routes.add(route);
      }

      // Add sub-routes
      if (item.subItems != null) {
        for (final subItem in item.subItems!) {
          routes.add(subItem.route);
        }
      }
    }

    return routes.toSet().toList(); // Remove duplicates
  }

  /// Test if a route is working
  static Future<bool> _testRoute(BuildContext context, String route) async {
    try {
      // Try to navigate to the route
      final router = GoRouter.of(context);
      final currentLocation = router.routerDelegate.currentConfiguration.uri
          .toString();

      // Check if route exists in router configuration
      try {
        router.go(route);
        await Future.delayed(const Duration(milliseconds: 100));

        // Navigate back to original location
        router.go(currentLocation);
        return true;
      } catch (e) {
        debugPrint('Route test failed for $route: $e');
        return false;
      }
    } catch (e) {
      debugPrint('Error testing route $route: $e');
      return false;
    }
  }

  /// Get route for a section
  static String _getSectionRoute(AppSection section) {
    switch (section) {
      case AppSection.dashboard:
        return '/dashboard';
      case AppSection.memoSuite:
        return '/memo-suite';
      case AppSection.islamicApp:
        return '/islamic-app';
      case AppSection.moneyManager:
        return '/money-manager';
      case AppSection.excelToApp:
        return '/excel-to-app';
      case AppSection.fileManager:
        return '/file-manager';
      case AppSection.shadowPlayer:
        return '/shadow-player';
      case AppSection.smartGallery:
        return '/smart-gallery';
      case AppSection.settings:
        return '/settings';
      case AppSection.profile:
        return '/profile';
    }
  }

  /// Generate navigation report
  static String generateNavigationReport(NavigationVerificationResult result) {
    final buffer = StringBuffer();

    buffer.writeln('=== SHADOW SUITE NAVIGATION VERIFICATION REPORT ===');
    buffer.writeln('Generated: ${DateTime.now()}');
    buffer.writeln();

    buffer.writeln('SUMMARY:');
    buffer.writeln('- Total Routes: ${result.totalRoutes}');
    buffer.writeln('- Working Routes: ${result.workingRoutes}');
    buffer.writeln('- Broken Routes: ${result.brokenRoutes}');
    buffer.writeln(
      '- Success Rate: ${((result.workingRoutes / result.totalRoutes) * 100).toStringAsFixed(1)}%',
    );
    buffer.writeln();

    if (result.workingRoutesList.isNotEmpty) {
      buffer.writeln('WORKING ROUTES (${result.workingRoutes}):');
      for (final route in result.workingRoutesList) {
        buffer.writeln('  ✓ $route');
      }
      buffer.writeln();
    }

    if (result.brokenRoutesList.isNotEmpty) {
      buffer.writeln('BROKEN ROUTES (${result.brokenRoutes}):');
      for (final route in result.brokenRoutesList) {
        buffer.writeln('  ✗ $route');
      }
      buffer.writeln();
    }

    buffer.writeln('RECOMMENDATIONS:');
    if (result.brokenRoutes > 0) {
      buffer.writeln('- Fix broken routes by implementing missing screens');
      buffer.writeln('- Verify route definitions in unified_router.dart');
      buffer.writeln(
        '- Check navigation item route paths in app_navigation.dart',
      );
    } else {
      buffer.writeln('- All routes are working correctly!');
      buffer.writeln('- Navigation system is fully functional');
    }

    return buffer.toString();
  }

  /// Fix common navigation issues
  static List<String> getNavigationFixSuggestions(
    NavigationVerificationResult result,
  ) {
    final suggestions = <String>[];

    for (final brokenRoute in result.brokenRoutesList) {
      if (brokenRoute.contains('/dashboard')) {
        suggestions.add(
          'Implement dashboard screen for ${brokenRoute.split('/')[1]}',
        );
      } else if (brokenRoute.contains('/settings')) {
        suggestions.add('Add settings screen for ${brokenRoute.split('/')[1]}');
      } else {
        suggestions.add('Implement screen for route: $brokenRoute');
      }
    }

    if (suggestions.isEmpty) {
      suggestions.add('All navigation routes are working correctly!');
    }

    return suggestions;
  }
}

/// Navigation verification result model
class NavigationVerificationResult {
  final int totalRoutes;
  final int workingRoutes;
  final int brokenRoutes;
  final Map<String, bool> routeStatus;
  final List<String> brokenRoutesList;
  final List<String> workingRoutesList;

  const NavigationVerificationResult({
    required this.totalRoutes,
    required this.workingRoutes,
    required this.brokenRoutes,
    required this.routeStatus,
    required this.brokenRoutesList,
    required this.workingRoutesList,
  });

  bool get hasAllRoutesWorking => brokenRoutes == 0;
  double get successRate =>
      totalRoutes > 0 ? (workingRoutes / totalRoutes) * 100 : 0;
}

/// Navigation health status
enum NavigationHealthStatus {
  excellent, // 95-100% routes working
  good, // 80-94% routes working
  fair, // 60-79% routes working
  poor, // <60% routes working
}

extension NavigationHealthExtension on NavigationVerificationResult {
  NavigationHealthStatus get healthStatus {
    if (successRate >= 95) return NavigationHealthStatus.excellent;
    if (successRate >= 80) return NavigationHealthStatus.good;
    if (successRate >= 60) return NavigationHealthStatus.fair;
    return NavigationHealthStatus.poor;
  }

  Color get healthColor {
    switch (healthStatus) {
      case NavigationHealthStatus.excellent:
        return const Color(0xFF27AE60);
      case NavigationHealthStatus.good:
        return const Color(0xFF2ECC71);
      case NavigationHealthStatus.fair:
        return const Color(0xFFF39C12);
      case NavigationHealthStatus.poor:
        return const Color(0xFFE74C3C);
    }
  }

  String get healthDescription {
    switch (healthStatus) {
      case NavigationHealthStatus.excellent:
        return 'Excellent - All routes working perfectly';
      case NavigationHealthStatus.good:
        return 'Good - Most routes working correctly';
      case NavigationHealthStatus.fair:
        return 'Fair - Some routes need attention';
      case NavigationHealthStatus.poor:
        return 'Poor - Many routes are broken';
    }
  }
}
