import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/data_import_export_service.dart';

/// Data import/export widget with multiple format support
class DataImportExportWidget extends StatefulWidget {
  final Function(List<List<dynamic>>)? onDataImported;
  final List<List<dynamic>>? dataToExport;

  const DataImportExportWidget({
    super.key,
    this.onDataImported,
    this.dataToExport,
  });

  @override
  State<DataImportExportWidget> createState() => _DataImportExportWidgetState();
}

class _DataImportExportWidgetState extends State<DataImportExportWidget>
    with TickerProviderStateMixin {
  late TabController _tabController;
  DataFormat _selectedImportFormat = DataFormat.csv;
  DataFormat _selectedExportFormat = DataFormat.csv;
  ImportOptions _importOptions = const ImportOptions();
  ExportOptions _exportOptions = const ExportOptions();
  DataPreview? _dataPreview;
  bool _isProcessing = false;
  String? _fileName;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    DataImportExportService.initialize();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildImportTab(),
                _buildExportTab(),
                _buildHistoryTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              const Icon(Icons.import_export, size: 24),
              const SizedBox(width: 8),
              Text(
                'Data Import/Export',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const Spacer(),
              if (_isProcessing)
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
            ],
          ),
          const SizedBox(height: 16),
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(icon: Icon(Icons.file_download), text: 'Import'),
              Tab(icon: Icon(Icons.file_upload), text: 'Export'),
              Tab(icon: Icon(Icons.history), text: 'History'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildImportTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFormatSelector(true),
          const SizedBox(height: 16),
          _buildImportOptions(),
          const SizedBox(height: 16),
          _buildFileSelector(),
          const SizedBox(height: 16),
          if (_dataPreview != null) ...[
            _buildDataPreview(),
            const SizedBox(height: 16),
          ],
          _buildImportActions(),
        ],
      ),
    );
  }

  Widget _buildExportTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFormatSelector(false),
          const SizedBox(height: 16),
          _buildExportOptions(),
          const SizedBox(height: 16),
          _buildDataSummary(),
          const SizedBox(height: 16),
          _buildExportActions(),
        ],
      ),
    );
  }

  Widget _buildHistoryTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _showStatistics,
                  icon: const Icon(Icons.analytics),
                  label: const Text('Statistics'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _clearHistory,
                  icon: const Icon(Icons.clear),
                  label: const Text('Clear History'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(child: _buildHistoryList()),
        ],
      ),
    );
  }

  Widget _buildFormatSelector(bool isImport) {
    final formats = isImport
        ? DataImportExportService.supportedImportFormats
        : DataImportExportService.supportedExportFormats;
    final selectedFormat = isImport
        ? _selectedImportFormat
        : _selectedExportFormat;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${isImport ? 'Import' : 'Export'} Format:',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: formats.map((format) {
            final isSelected = format == selectedFormat;
            return FilterChip(
              label: Text(format.displayName),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (isImport) {
                    _selectedImportFormat = format;
                  } else {
                    _selectedExportFormat = format;
                  }
                });
              },
              avatar: Icon(
                _getFormatIcon(format),
                size: 16,
                color: isSelected ? Colors.white : null,
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 8),
        Text(
          selectedFormat.description,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildImportOptions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Import Options',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            CheckboxListTile(
              title: const Text('Include Headers'),
              subtitle: const Text('First row contains column headers'),
              value: _importOptions.includeHeaders,
              onChanged: (value) {
                setState(() {
                  _importOptions = ImportOptions(
                    includeHeaders: value ?? true,
                    previewMode: _importOptions.previewMode,
                    maxRows: _importOptions.maxRows,
                    encoding: _importOptions.encoding,
                    delimiter: _importOptions.delimiter,
                  );
                });
              },
            ),
            if (_selectedImportFormat == DataFormat.csv ||
                _selectedImportFormat == DataFormat.tsv)
              ListTile(
                title: const Text('Delimiter'),
                subtitle: TextField(
                  decoration: const InputDecoration(
                    hintText: 'e.g., , or ; or |',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _importOptions = ImportOptions(
                        includeHeaders: _importOptions.includeHeaders,
                        previewMode: _importOptions.previewMode,
                        maxRows: _importOptions.maxRows,
                        encoding: _importOptions.encoding,
                        delimiter: value.isEmpty ? null : value,
                      );
                    });
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildExportOptions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Export Options',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            CheckboxListTile(
              title: const Text('Include Headers'),
              subtitle: const Text('Export column headers'),
              value: _exportOptions.includeHeaders,
              onChanged: (value) {
                setState(() {
                  _exportOptions = ExportOptions(
                    includeHeaders: value ?? true,
                    encoding: _exportOptions.encoding,
                    delimiter: _exportOptions.delimiter,
                    prettyPrint: _exportOptions.prettyPrint,
                  );
                });
              },
            ),
            if (_selectedExportFormat == DataFormat.json)
              CheckboxListTile(
                title: const Text('Pretty Print'),
                subtitle: const Text('Format JSON with indentation'),
                value: _exportOptions.prettyPrint,
                onChanged: (value) {
                  setState(() {
                    _exportOptions = ExportOptions(
                      includeHeaders: _exportOptions.includeHeaders,
                      encoding: _exportOptions.encoding,
                      delimiter: _exportOptions.delimiter,
                      prettyPrint: value ?? false,
                    );
                  });
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFileSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Select File', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: 'No file selected',
                      border: const OutlineInputBorder(),
                      suffixIcon: IconButton(
                        onPressed: _selectFile,
                        icon: const Icon(Icons.folder_open),
                      ),
                    ),
                    readOnly: true,
                    controller: TextEditingController(text: _fileName),
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _pasteFromClipboard,
                  icon: const Icon(Icons.content_paste),
                  label: const Text('Paste'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataPreview() {
    final preview = _dataPreview!;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Data Preview',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Spacer(),
                Text('${preview.totalRows} rows'),
              ],
            ),
            const SizedBox(height: 16),
            if (preview.error != null)
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  border: Border.all(color: Colors.red),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'Error: ${preview.error}',
                  style: TextStyle(color: Colors.red[700]),
                ),
              )
            else
              Container(
                constraints: const BoxConstraints(maxHeight: 200),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: DataTable(
                    columns: preview.headers.map((header) {
                      return DataColumn(label: Text(header));
                    }).toList(),
                    rows: preview.rows.map((row) {
                      return DataRow(
                        cells: row.map((cell) {
                          return DataCell(Text(cell.toString()));
                        }).toList(),
                      );
                    }).toList(),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataSummary() {
    final data = widget.dataToExport;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Data Summary',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            if (data == null || data.isEmpty)
              const Text('No data available for export')
            else ...[
              Row(
                children: [
                  _buildSummaryItem('Rows', '${data.length}'),
                  const SizedBox(width: 24),
                  _buildSummaryItem('Columns', '${data.first.length}'),
                ],
              ),
              const SizedBox(height: 16),
              Text('Preview:', style: Theme.of(context).textTheme.titleSmall),
              const SizedBox(height: 8),
              Container(
                constraints: const BoxConstraints(maxHeight: 150),
                child: SingleChildScrollView(
                  child: Text(
                    data.take(5).map((row) => row.join(', ')).join('\n'),
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        Text(label),
      ],
    );
  }

  Widget _buildImportActions() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _fileName != null && !_isProcessing
                ? _previewData
                : null,
            icon: const Icon(Icons.preview),
            label: const Text('Preview'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _dataPreview != null && !_isProcessing
                ? _importData
                : null,
            icon: const Icon(Icons.file_download),
            label: const Text('Import'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildExportActions() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: widget.dataToExport != null && !_isProcessing
                ? _exportData
                : null,
            icon: const Icon(Icons.file_upload),
            label: const Text('Export'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _showFormatConverter,
            icon: const Icon(Icons.transform),
            label: const Text('Convert'),
          ),
        ),
      ],
    );
  }

  Widget _buildHistoryList() {
    final importHistory = DataImportExportService.importHistory;
    final exportHistory = DataImportExportService.exportHistory;
    final allHistory = [...importHistory, ...exportHistory]
      ..sort((a, b) {
        final aTime = a is ImportResult
            ? a.timestamp
            : (a as ExportResult).timestamp;
        final bTime = b is ImportResult
            ? b.timestamp
            : (b as ExportResult).timestamp;
        return bTime.compareTo(aTime);
      });

    if (allHistory.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.history, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No import/export history'),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: allHistory.length,
      itemBuilder: (context, index) {
        final item = allHistory[index];
        final isImport = item is ImportResult;

        // Cast to proper type for accessing properties
        ImportResult? importItem;
        ExportResult? exportItem;

        if (isImport) {
          importItem = item as ImportResult?;
        } else {
          exportItem = item as ExportResult?;
        }

        final success = isImport ? importItem!.success : exportItem!.success;
        final fileName = isImport ? importItem!.fileName : exportItem!.fileName;
        final format = isImport ? importItem!.format : exportItem!.format;
        final timestamp = isImport
            ? importItem!.timestamp
            : exportItem!.timestamp;
        final error = isImport ? importItem!.error : exportItem!.error;

        return Card(
          child: ListTile(
            leading: Icon(
              isImport ? Icons.file_download : Icons.file_upload,
              color: success ? Colors.green : Colors.red,
            ),
            title: Text(fileName),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${format.displayName} • ${timestamp.toString().substring(0, 19)}',
                ),
                if (!success && error != null)
                  Text(
                    'Error: $error',
                    style: TextStyle(color: Colors.red[700]),
                  ),
              ],
            ),
            trailing: Text(
              isImport
                  ? '${importItem!.rowCount} rows'
                  : '${exportItem!.fileSize} bytes',
            ),
            isThreeLine: !success,
          ),
        );
      },
    );
  }

  IconData _getFormatIcon(DataFormat format) {
    switch (format) {
      case DataFormat.csv:
      case DataFormat.tsv:
        return Icons.table_chart;
      case DataFormat.json:
        return Icons.code;
      case DataFormat.xml:
        return Icons.code;
      case DataFormat.excel:
        return Icons.grid_on;
      case DataFormat.txt:
        return Icons.text_snippet;
      case DataFormat.pdf:
        return Icons.picture_as_pdf;
      case DataFormat.html:
        return Icons.web;
    }
  }

  void _selectFile() {
    // Simulate file selection
    setState(() {
      _fileName =
          'sample_data.${_selectedImportFormat.extensions.first.substring(1)}';
    });
  }

  void _pasteFromClipboard() async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      if (clipboardData?.text != null) {
        final data = Uint8List.fromList(clipboardData!.text!.codeUnits);
        _processImportData(data, 'clipboard_data.txt');
      }
    } catch (e) {
      _showError('Failed to paste from clipboard: $e');
    }
  }

  void _previewData() async {
    if (_fileName == null) return;

    setState(() => _isProcessing = true);

    try {
      // Simulate file reading
      final sampleData = _generateSampleData();
      final preview = await DataImportExportService.getDataPreview(
        data: sampleData,
        format: _selectedImportFormat,
        maxRows: 10,
      );

      setState(() {
        _dataPreview = preview;
      });
    } catch (e) {
      _showError('Preview failed: $e');
    } finally {
      setState(() => _isProcessing = false);
    }
  }

  void _importData() async {
    if (_fileName == null) return;

    setState(() => _isProcessing = true);

    try {
      final sampleData = _generateSampleData();
      final result = await DataImportExportService.importData(
        data: sampleData,
        format: _selectedImportFormat,
        fileName: _fileName!,
        options: _importOptions,
      );

      if (result.success) {
        widget.onDataImported?.call(result.data);
        _showSuccess('Data imported successfully: ${result.rowCount} rows');
      } else {
        _showError('Import failed: ${result.error}');
      }
    } catch (e) {
      _showError('Import failed: $e');
    } finally {
      setState(() => _isProcessing = false);
    }
  }

  void _exportData() async {
    final data = widget.dataToExport;
    if (data == null) return;

    setState(() => _isProcessing = true);

    try {
      final fileName =
          'export_${DateTime.now().millisecondsSinceEpoch}.${_selectedExportFormat.extensions.first.substring(1)}';
      final result = await DataImportExportService.exportData(
        data: data,
        format: _selectedExportFormat,
        fileName: fileName,
        options: _exportOptions,
      );

      if (result.success) {
        _showSuccess('Data exported successfully: ${result.fileSize} bytes');
      } else {
        _showError('Export failed: ${result.error}');
      }
    } catch (e) {
      _showError('Export failed: $e');
    } finally {
      setState(() => _isProcessing = false);
    }
  }

  void _processImportData(Uint8List data, String fileName) async {
    setState(() => _isProcessing = true);

    try {
      final preview = await DataImportExportService.getDataPreview(
        data: data,
        format: _selectedImportFormat,
        maxRows: 10,
      );

      setState(() {
        _dataPreview = preview;
        _fileName = fileName;
      });
    } catch (e) {
      _showError('Failed to process data: $e');
    } finally {
      setState(() => _isProcessing = false);
    }
  }

  Uint8List _generateSampleData() {
    // Generate sample data based on format
    switch (_selectedImportFormat) {
      case DataFormat.csv:
        return Uint8List.fromList(
          'Name,Age,City\nJohn,25,New York\nJane,30,London\nBob,35,Paris'
              .codeUnits,
        );
      case DataFormat.json:
        return Uint8List.fromList(
          '[{"name":"John","age":25,"city":"New York"},{"name":"Jane","age":30,"city":"London"}]'
              .codeUnits,
        );
      case DataFormat.tsv:
        return Uint8List.fromList(
          'Name\tAge\tCity\nJohn\t25\tNew York\nJane\t30\tLondon'.codeUnits,
        );
      default:
        return Uint8List.fromList(
          'Sample data for ${_selectedImportFormat.displayName}'.codeUnits,
        );
    }
  }

  void _showFormatConverter() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Format Converter'),
        content: const Text(
          'Format conversion features will be implemented here',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showStatistics() {
    final stats = DataImportExportService.getStatistics();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Processing Statistics'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatRow('Total Imports', '${stats.totalImports}'),
            _buildStatRow('Successful Imports', '${stats.successfulImports}'),
            _buildStatRow('Total Exports', '${stats.totalExports}'),
            _buildStatRow('Successful Exports', '${stats.successfulExports}'),
            _buildStatRow(
              'Import Success Rate',
              '${(stats.importSuccessRate * 100).toStringAsFixed(1)}%',
            ),
            _buildStatRow(
              'Export Success Rate',
              '${(stats.exportSuccessRate * 100).toStringAsFixed(1)}%',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  void _clearHistory() {
    DataImportExportService.clearHistory();
    setState(() {});
    _showSuccess('History cleared');
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}
