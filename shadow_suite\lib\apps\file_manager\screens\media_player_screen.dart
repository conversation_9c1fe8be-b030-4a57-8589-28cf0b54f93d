import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../file_manager_main.dart';

class MediaPlayerScreen extends ConsumerWidget {
  const MediaPlayerScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: Column(
        children: [
          const FileManagerHeader(
            title: 'Media Player',
            subtitle: 'Play audio and video files',
            actions: [
              Icon(Icons.playlist_add, color: Color(0xFFE67E22)),
              SizedBox(width: 16),
              Icon(Icons.settings, color: Color(0xFFE67E22)),
            ],
          ),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.play_circle,
                    size: 64,
                    color: Color(0xFFE67E22),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Media Player',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2C3E50),
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Play your audio and video files',
                    style: TextStyle(
                      color: Color(0xFF7F8C8D),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),
                  ElevatedButton.icon(
                    onPressed: () {
                      // Implement media file selection
                    },
                    icon: const Icon(Icons.file_open),
                    label: const Text('Open Media File'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFE67E22),
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
