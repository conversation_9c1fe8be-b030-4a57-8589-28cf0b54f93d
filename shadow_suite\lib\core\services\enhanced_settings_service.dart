import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';

// Enhanced Settings Service with Live Updates and Comprehensive Features
class EnhancedSettingsService extends ChangeNotifier {
  static EnhancedSettingsService? _instance;
  static EnhancedSettingsService get instance => _instance ??= EnhancedSettingsService._();
  EnhancedSettingsService._();

  SharedPreferences? _prefs;
  final Map<String, dynamic> _tempSettings = {};
  bool _hasUnsavedChanges = false;

  // Theme & Appearance Settings
  ThemeMode _themeMode = ThemeMode.system;
  Color _primaryColor = Colors.blue;
  Color _accentColor = Colors.blueAccent;
  double _fontSize = 14.0;
  String _fontFamily = 'Roboto';
  bool _darkMode = false;
  bool _highContrast = false;
  double _borderRadius = 8.0;
  double _elevation = 2.0;
  bool _animations = true;
  double _animationSpeed = 1.0;
  String _backgroundImage = '';
  double _opacity = 1.0;

  // Auto-save Settings
  bool _autoSave = true;
  int _autoSaveInterval = 30; // seconds
  bool _saveDrafts = true;
  String _saveLocation = '';
  bool _cloudSync = false;
  String _backupFrequency = 'daily';

  // Performance Settings
  bool _lazyLoading = true;
  int _maxCacheSize = 100;
  bool _preloadData = false;
  bool _optimizeMemory = true;
  bool _hardwareAcceleration = true;
  int _maxThreads = 4;

  // Privacy & Security
  bool _analyticsEnabled = false;
  bool _crashReporting = true;
  bool _biometricAuth = false;
  int _sessionTimeout = 30; // minutes
  bool _encryptData = true;
  bool _autoLock = false;

  // Accessibility
  double _textScale = 1.0;
  bool _screenReader = false;
  bool _reduceMotion = false;
  bool _highContrastText = false;
  bool _largeButtons = false;
  bool _voiceCommands = false;

  // Notification Settings
  bool _pushNotifications = true;
  bool _emailNotifications = false;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  String _notificationSound = 'default';

  // Language & Localization
  String _language = 'en';
  String _region = 'US';
  String _dateFormat = 'MM/dd/yyyy';
  String _timeFormat = '12h';
  String _currency = 'USD';

  // Component-specific settings
  Map<String, Map<String, dynamic>> _componentSettings = {};

  // Getters
  ThemeMode get themeMode => _themeMode;
  Color get primaryColor => _primaryColor;
  Color get accentColor => _accentColor;
  double get fontSize => _fontSize;
  String get fontFamily => _fontFamily;
  bool get darkMode => _darkMode;
  bool get highContrast => _highContrast;
  double get borderRadius => _borderRadius;
  double get elevation => _elevation;
  bool get animations => _animations;
  double get animationSpeed => _animationSpeed;
  String get backgroundImage => _backgroundImage;
  double get opacity => _opacity;
  bool get autoSave => _autoSave;
  int get autoSaveInterval => _autoSaveInterval;
  bool get saveDrafts => _saveDrafts;
  String get saveLocation => _saveLocation;
  bool get cloudSync => _cloudSync;
  String get backupFrequency => _backupFrequency;
  bool get lazyLoading => _lazyLoading;
  int get maxCacheSize => _maxCacheSize;
  bool get preloadData => _preloadData;
  bool get optimizeMemory => _optimizeMemory;
  bool get hardwareAcceleration => _hardwareAcceleration;
  int get maxThreads => _maxThreads;
  bool get analyticsEnabled => _analyticsEnabled;
  bool get crashReporting => _crashReporting;
  bool get biometricAuth => _biometricAuth;
  int get sessionTimeout => _sessionTimeout;
  bool get encryptData => _encryptData;
  bool get autoLock => _autoLock;
  double get textScale => _textScale;
  bool get screenReader => _screenReader;
  bool get reduceMotion => _reduceMotion;
  bool get highContrastText => _highContrastText;
  bool get largeButtons => _largeButtons;
  bool get voiceCommands => _voiceCommands;
  bool get pushNotifications => _pushNotifications;
  bool get emailNotifications => _emailNotifications;
  bool get soundEnabled => _soundEnabled;
  bool get vibrationEnabled => _vibrationEnabled;
  String get notificationSound => _notificationSound;
  String get language => _language;
  String get region => _region;
  String get dateFormat => _dateFormat;
  String get timeFormat => _timeFormat;
  String get currency => _currency;
  bool get hasUnsavedChanges => _hasUnsavedChanges;

  // Initialize settings
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadSettings();
    await _initializeSaveLocation();
    await _requestPermissions();
  }

  // Load settings from storage
  Future<void> _loadSettings() async {
    if (_prefs == null) return;

    _themeMode = ThemeMode.values[_prefs!.getInt('themeMode') ?? 0];
    _primaryColor = Color(_prefs!.getInt('primaryColor') ?? 0xFF2196F3);
    _accentColor = Color(_prefs!.getInt('accentColor') ?? 0xFF448AFF);
    _fontSize = _prefs!.getDouble('fontSize') ?? 14.0;
    _fontFamily = _prefs!.getString('fontFamily') ?? 'Roboto';
    _darkMode = _prefs!.getBool('darkMode') ?? false;
    _highContrast = _prefs!.getBool('highContrast') ?? false;
    _borderRadius = _prefs!.getDouble('borderRadius') ?? 8.0;
    _elevation = _prefs!.getDouble('elevation') ?? 2.0;
    _animations = _prefs!.getBool('animations') ?? true;
    _animationSpeed = _prefs!.getDouble('animationSpeed') ?? 1.0;
    _backgroundImage = _prefs!.getString('backgroundImage') ?? '';
    _opacity = _prefs!.getDouble('opacity') ?? 1.0;
    _autoSave = _prefs!.getBool('autoSave') ?? true;
    _autoSaveInterval = _prefs!.getInt('autoSaveInterval') ?? 30;
    _saveDrafts = _prefs!.getBool('saveDrafts') ?? true;
    _saveLocation = _prefs!.getString('saveLocation') ?? '';
    _cloudSync = _prefs!.getBool('cloudSync') ?? false;
    _backupFrequency = _prefs!.getString('backupFrequency') ?? 'daily';
    _lazyLoading = _prefs!.getBool('lazyLoading') ?? true;
    _maxCacheSize = _prefs!.getInt('maxCacheSize') ?? 100;
    _preloadData = _prefs!.getBool('preloadData') ?? false;
    _optimizeMemory = _prefs!.getBool('optimizeMemory') ?? true;
    _hardwareAcceleration = _prefs!.getBool('hardwareAcceleration') ?? true;
    _maxThreads = _prefs!.getInt('maxThreads') ?? 4;
    _analyticsEnabled = _prefs!.getBool('analyticsEnabled') ?? false;
    _crashReporting = _prefs!.getBool('crashReporting') ?? true;
    _biometricAuth = _prefs!.getBool('biometricAuth') ?? false;
    _sessionTimeout = _prefs!.getInt('sessionTimeout') ?? 30;
    _encryptData = _prefs!.getBool('encryptData') ?? true;
    _autoLock = _prefs!.getBool('autoLock') ?? false;
    _textScale = _prefs!.getDouble('textScale') ?? 1.0;
    _screenReader = _prefs!.getBool('screenReader') ?? false;
    _reduceMotion = _prefs!.getBool('reduceMotion') ?? false;
    _highContrastText = _prefs!.getBool('highContrastText') ?? false;
    _largeButtons = _prefs!.getBool('largeButtons') ?? false;
    _voiceCommands = _prefs!.getBool('voiceCommands') ?? false;
    _pushNotifications = _prefs!.getBool('pushNotifications') ?? true;
    _emailNotifications = _prefs!.getBool('emailNotifications') ?? false;
    _soundEnabled = _prefs!.getBool('soundEnabled') ?? true;
    _vibrationEnabled = _prefs!.getBool('vibrationEnabled') ?? true;
    _notificationSound = _prefs!.getString('notificationSound') ?? 'default';
    _language = _prefs!.getString('language') ?? 'en';
    _region = _prefs!.getString('region') ?? 'US';
    _dateFormat = _prefs!.getString('dateFormat') ?? 'MM/dd/yyyy';
    _timeFormat = _prefs!.getString('timeFormat') ?? '12h';
    _currency = _prefs!.getString('currency') ?? 'USD';

    // Load component settings
    final componentSettingsJson = _prefs!.getString('componentSettings') ?? '{}';
    _componentSettings = Map<String, Map<String, dynamic>>.from(
      jsonDecode(componentSettingsJson).map((key, value) => 
        MapEntry(key, Map<String, dynamic>.from(value))
      )
    );

    notifyListeners();
  }

  // Initialize save location
  Future<void> _initializeSaveLocation() async {
    if (_saveLocation.isEmpty) {
      final directory = await getApplicationDocumentsDirectory();
      _saveLocation = '${directory.path}/ShadowSuite';
      await Directory(_saveLocation).create(recursive: true);
    }
  }

  // Request necessary permissions (simplified for cross-platform compatibility)
  Future<void> _requestPermissions() async {
    // Permissions will be handled by the platform-specific implementations
    // This is a placeholder for future permission handling
  }

  // Update setting with live reflection
  void updateSetting(String key, dynamic value, {bool temporary = false}) {
    if (temporary) {
      _tempSettings[key] = value;
    }

    // Apply setting immediately for live preview
    _applySetting(key, value);
    _hasUnsavedChanges = temporary;
    notifyListeners();
  }

  // Apply setting immediately
  void _applySetting(String key, dynamic value) {
    switch (key) {
      case 'themeMode':
        _themeMode = value as ThemeMode;
        break;
      case 'primaryColor':
        _primaryColor = value as Color;
        break;
      case 'accentColor':
        _accentColor = value as Color;
        break;
      case 'fontSize':
        _fontSize = value as double;
        break;
      case 'fontFamily':
        _fontFamily = value as String;
        break;
      case 'darkMode':
        _darkMode = value as bool;
        break;
      case 'highContrast':
        _highContrast = value as bool;
        break;
      case 'borderRadius':
        _borderRadius = value as double;
        break;
      case 'elevation':
        _elevation = value as double;
        break;
      case 'animations':
        _animations = value as bool;
        break;
      case 'animationSpeed':
        _animationSpeed = value as double;
        break;
      case 'backgroundImage':
        _backgroundImage = value as String;
        break;
      case 'opacity':
        _opacity = value as double;
        break;
      case 'autoSave':
        _autoSave = value as bool;
        break;
      case 'autoSaveInterval':
        _autoSaveInterval = value as int;
        break;
      case 'saveDrafts':
        _saveDrafts = value as bool;
        break;
      case 'saveLocation':
        _saveLocation = value as String;
        break;
      case 'cloudSync':
        _cloudSync = value as bool;
        break;
      case 'backupFrequency':
        _backupFrequency = value as String;
        break;
      case 'lazyLoading':
        _lazyLoading = value as bool;
        break;
      case 'maxCacheSize':
        _maxCacheSize = value as int;
        break;
      case 'preloadData':
        _preloadData = value as bool;
        break;
      case 'optimizeMemory':
        _optimizeMemory = value as bool;
        break;
      case 'hardwareAcceleration':
        _hardwareAcceleration = value as bool;
        break;
      case 'maxThreads':
        _maxThreads = value as int;
        break;
      case 'analyticsEnabled':
        _analyticsEnabled = value as bool;
        break;
      case 'crashReporting':
        _crashReporting = value as bool;
        break;
      case 'biometricAuth':
        _biometricAuth = value as bool;
        break;
      case 'sessionTimeout':
        _sessionTimeout = value as int;
        break;
      case 'encryptData':
        _encryptData = value as bool;
        break;
      case 'autoLock':
        _autoLock = value as bool;
        break;
      case 'textScale':
        _textScale = value as double;
        break;
      case 'screenReader':
        _screenReader = value as bool;
        break;
      case 'reduceMotion':
        _reduceMotion = value as bool;
        break;
      case 'highContrastText':
        _highContrastText = value as bool;
        break;
      case 'largeButtons':
        _largeButtons = value as bool;
        break;
      case 'voiceCommands':
        _voiceCommands = value as bool;
        break;
      case 'pushNotifications':
        _pushNotifications = value as bool;
        break;
      case 'emailNotifications':
        _emailNotifications = value as bool;
        break;
      case 'soundEnabled':
        _soundEnabled = value as bool;
        break;
      case 'vibrationEnabled':
        _vibrationEnabled = value as bool;
        break;
      case 'notificationSound':
        _notificationSound = value as String;
        break;
      case 'language':
        _language = value as String;
        break;
      case 'region':
        _region = value as String;
        break;
      case 'dateFormat':
        _dateFormat = value as String;
        break;
      case 'timeFormat':
        _timeFormat = value as String;
        break;
      case 'currency':
        _currency = value as String;
        break;
    }
  }

  // Component-specific settings
  Map<String, dynamic> getComponentSettings(String componentId) {
    return _componentSettings[componentId] ?? {};
  }

  void updateComponentSetting(String componentId, String key, dynamic value, {bool temporary = false}) {
    if (!_componentSettings.containsKey(componentId)) {
      _componentSettings[componentId] = {};
    }

    if (temporary) {
      // Store in temp settings with component prefix
      _tempSettings['$componentId.$key'] = value;
    } else {
      _componentSettings[componentId]![key] = value;
    }

    _hasUnsavedChanges = temporary;
    notifyListeners();
  }

  void saveComponentSettings(String componentId) {
    // Move temp settings to permanent settings for this component
    final componentTempKeys = _tempSettings.keys.where((k) => k.startsWith('$componentId.')).toList();

    if (!_componentSettings.containsKey(componentId)) {
      _componentSettings[componentId] = {};
    }

    for (final tempKey in componentTempKeys) {
      final key = tempKey.substring(componentId.length + 1);
      _componentSettings[componentId]![key] = _tempSettings[tempKey];
      _tempSettings.remove(tempKey);
    }

    _hasUnsavedChanges = _tempSettings.isNotEmpty;
    notifyListeners();
  }

  // Save all settings
  Future<void> saveSettings() async {
    if (_prefs == null) return;

    // Merge temporary settings with permanent settings
    for (final entry in _tempSettings.entries) {
      if (entry.key.contains('.')) {
        // Component setting
        final parts = entry.key.split('.');
        final componentId = parts[0];
        final key = parts.sublist(1).join('.');

        if (!_componentSettings.containsKey(componentId)) {
          _componentSettings[componentId] = {};
        }
        _componentSettings[componentId]![key] = entry.value;
      } else {
        // Global setting
        _applySetting(entry.key, entry.value);
      }
    }
    _tempSettings.clear();

    await _prefs!.setInt('themeMode', _themeMode.index);
    await _prefs!.setInt('primaryColor', _primaryColor.toARGB32());
    await _prefs!.setInt('accentColor', _accentColor.toARGB32());
    await _prefs!.setDouble('fontSize', _fontSize);
    await _prefs!.setString('fontFamily', _fontFamily);
    await _prefs!.setBool('darkMode', _darkMode);
    await _prefs!.setBool('highContrast', _highContrast);
    await _prefs!.setDouble('borderRadius', _borderRadius);
    await _prefs!.setDouble('elevation', _elevation);
    await _prefs!.setBool('animations', _animations);
    await _prefs!.setDouble('animationSpeed', _animationSpeed);
    await _prefs!.setString('backgroundImage', _backgroundImage);
    await _prefs!.setDouble('opacity', _opacity);
    await _prefs!.setBool('autoSave', _autoSave);
    await _prefs!.setInt('autoSaveInterval', _autoSaveInterval);
    await _prefs!.setBool('saveDrafts', _saveDrafts);
    await _prefs!.setString('saveLocation', _saveLocation);
    await _prefs!.setBool('cloudSync', _cloudSync);
    await _prefs!.setString('backupFrequency', _backupFrequency);
    await _prefs!.setBool('lazyLoading', _lazyLoading);
    await _prefs!.setInt('maxCacheSize', _maxCacheSize);
    await _prefs!.setBool('preloadData', _preloadData);
    await _prefs!.setBool('optimizeMemory', _optimizeMemory);
    await _prefs!.setBool('hardwareAcceleration', _hardwareAcceleration);
    await _prefs!.setInt('maxThreads', _maxThreads);
    await _prefs!.setBool('analyticsEnabled', _analyticsEnabled);
    await _prefs!.setBool('crashReporting', _crashReporting);
    await _prefs!.setBool('biometricAuth', _biometricAuth);
    await _prefs!.setInt('sessionTimeout', _sessionTimeout);
    await _prefs!.setBool('encryptData', _encryptData);
    await _prefs!.setBool('autoLock', _autoLock);
    await _prefs!.setDouble('textScale', _textScale);
    await _prefs!.setBool('screenReader', _screenReader);
    await _prefs!.setBool('reduceMotion', _reduceMotion);
    await _prefs!.setBool('highContrastText', _highContrastText);
    await _prefs!.setBool('largeButtons', _largeButtons);
    await _prefs!.setBool('voiceCommands', _voiceCommands);
    await _prefs!.setBool('pushNotifications', _pushNotifications);
    await _prefs!.setBool('emailNotifications', _emailNotifications);
    await _prefs!.setBool('soundEnabled', _soundEnabled);
    await _prefs!.setBool('vibrationEnabled', _vibrationEnabled);
    await _prefs!.setString('notificationSound', _notificationSound);
    await _prefs!.setString('language', _language);
    await _prefs!.setString('region', _region);
    await _prefs!.setString('dateFormat', _dateFormat);
    await _prefs!.setString('timeFormat', _timeFormat);
    await _prefs!.setString('currency', _currency);

    // Save component settings
    await _prefs!.setString('componentSettings', jsonEncode(_componentSettings));

    _hasUnsavedChanges = false;
    notifyListeners();
  }

  // Reset to defaults
  Future<void> resetToDefaults() async {
    _themeMode = ThemeMode.system;
    _primaryColor = Colors.blue;
    _accentColor = Colors.blueAccent;
    _fontSize = 14.0;
    _fontFamily = 'Roboto';
    _darkMode = false;
    _highContrast = false;
    _borderRadius = 8.0;
    _elevation = 2.0;
    _animations = true;
    _animationSpeed = 1.0;
    _backgroundImage = '';
    _opacity = 1.0;
    _autoSave = true;
    _autoSaveInterval = 30;
    _saveDrafts = true;
    _cloudSync = false;
    _backupFrequency = 'daily';
    _lazyLoading = true;
    _maxCacheSize = 100;
    _preloadData = false;
    _optimizeMemory = true;
    _hardwareAcceleration = true;
    _maxThreads = 4;
    _analyticsEnabled = false;
    _crashReporting = true;
    _biometricAuth = false;
    _sessionTimeout = 30;
    _encryptData = true;
    _autoLock = false;
    _textScale = 1.0;
    _screenReader = false;
    _reduceMotion = false;
    _highContrastText = false;
    _largeButtons = false;
    _voiceCommands = false;
    _pushNotifications = true;
    _emailNotifications = false;
    _soundEnabled = true;
    _vibrationEnabled = true;
    _notificationSound = 'default';
    _language = 'en';
    _region = 'US';
    _dateFormat = 'MM/dd/yyyy';
    _timeFormat = '12h';
    _currency = 'USD';

    _tempSettings.clear();
    _componentSettings.clear();
    _hasUnsavedChanges = false;
    notifyListeners();
  }

  // Clear all user data
  Future<void> clearAllData() async {
    if (_prefs != null) {
      await _prefs!.clear();
    }

    // Clear app data directory
    try {
      final directory = Directory(_saveLocation);
      if (await directory.exists()) {
        await directory.delete(recursive: true);
      }
    } catch (e) {
      // Handle error silently in production
    }

    await _initializeSaveLocation();
    await resetToDefaults();
  }

  // Export settings
  Future<String> exportSettings() async {
    final settingsMap = {
      'themeMode': _themeMode.index,
      'primaryColor': _primaryColor.toARGB32(),
      'accentColor': _accentColor.toARGB32(),
      'fontSize': _fontSize,
      'fontFamily': _fontFamily,
      'darkMode': _darkMode,
      'highContrast': _highContrast,
      'borderRadius': _borderRadius,
      'elevation': _elevation,
      'animations': _animations,
      'animationSpeed': _animationSpeed,
      'backgroundImage': _backgroundImage,
      'opacity': _opacity,
      'autoSave': _autoSave,
      'autoSaveInterval': _autoSaveInterval,
      'saveDrafts': _saveDrafts,
      'saveLocation': _saveLocation,
      'cloudSync': _cloudSync,
      'backupFrequency': _backupFrequency,
      'lazyLoading': _lazyLoading,
      'maxCacheSize': _maxCacheSize,
      'preloadData': _preloadData,
      'optimizeMemory': _optimizeMemory,
      'hardwareAcceleration': _hardwareAcceleration,
      'maxThreads': _maxThreads,
      'analyticsEnabled': _analyticsEnabled,
      'crashReporting': _crashReporting,
      'biometricAuth': _biometricAuth,
      'sessionTimeout': _sessionTimeout,
      'encryptData': _encryptData,
      'autoLock': _autoLock,
      'textScale': _textScale,
      'screenReader': _screenReader,
      'reduceMotion': _reduceMotion,
      'highContrastText': _highContrastText,
      'largeButtons': _largeButtons,
      'voiceCommands': _voiceCommands,
      'pushNotifications': _pushNotifications,
      'emailNotifications': _emailNotifications,
      'soundEnabled': _soundEnabled,
      'vibrationEnabled': _vibrationEnabled,
      'notificationSound': _notificationSound,
      'language': _language,
      'region': _region,
      'dateFormat': _dateFormat,
      'timeFormat': _timeFormat,
      'currency': _currency,
      'componentSettings': _componentSettings,
    };

    return jsonEncode(settingsMap);
  }

  // Import settings
  Future<void> importSettings(String settingsJson) async {
    try {
      final settingsMap = jsonDecode(settingsJson) as Map<String, dynamic>;

      for (final entry in settingsMap.entries) {
        if (entry.key == 'componentSettings') {
          _componentSettings = Map<String, Map<String, dynamic>>.from(
            entry.value.map((key, value) =>
              MapEntry(key, Map<String, dynamic>.from(value))
            )
          );
        } else {
          _applySetting(entry.key, entry.value);
        }
      }

      await saveSettings();
    } catch (e) {
      // Handle import error silently
    }
  }
}

// Providers
final enhancedSettingsServiceProvider = ChangeNotifierProvider<EnhancedSettingsService>((ref) {
  return EnhancedSettingsService.instance;
});

final enhancedThemeProvider = Provider<ThemeData>((ref) {
  final settings = ref.watch(enhancedSettingsServiceProvider);

  return ThemeData(
    useMaterial3: true,
    brightness: settings.darkMode ? Brightness.dark : Brightness.light,
    colorScheme: ColorScheme.fromSeed(
      seedColor: settings.primaryColor,
      brightness: settings.darkMode ? Brightness.dark : Brightness.light,
    ),
    textTheme: TextTheme(
      bodyLarge: TextStyle(
        fontSize: settings.fontSize * settings.textScale,
        fontFamily: settings.fontFamily,
      ),
      bodyMedium: TextStyle(
        fontSize: (settings.fontSize - 2) * settings.textScale,
        fontFamily: settings.fontFamily,
      ),
      bodySmall: TextStyle(
        fontSize: (settings.fontSize - 4) * settings.textScale,
        fontFamily: settings.fontFamily,
      ),
    ),
    cardTheme: CardThemeData(
      elevation: settings.elevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(settings.borderRadius),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: settings.elevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(settings.borderRadius),
        ),
        minimumSize: settings.largeButtons ? const Size(120, 48) : const Size(88, 36),
      ),
    ),
  );
});
