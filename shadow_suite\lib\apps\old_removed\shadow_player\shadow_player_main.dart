import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'models/player_state_models.dart' as state_models;
import 'models/media_models.dart';
import 'services/shadow_player_providers.dart';
import 'widgets/responsive_shadow_player_layout.dart';

class ShadowPlayerMain extends ConsumerStatefulWidget {
  const ShadowPlayerMain({super.key});

  @override
  ConsumerState<ShadowPlayerMain> createState() => _ShadowPlayerMainState();
}

class _ShadowPlayerMainState extends ConsumerState<ShadowPlayerMain> {
  @override
  void initState() {
    super.initState();

    // Initialize media scanning on app start
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeMediaLibrary();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Responsive Layout System
        Expanded(child: const ResponsiveShadowPlayerLayout()),

        // Mini Player Bar (when media is playing)
        Consumer(
          builder: (context, ref, child) {
            final playerState = ref.watch(mediaPlayerStateProvider);
            final currentMedia = ref.watch(currentPlayingMediaProvider);

            if (playerState != state_models.PlayerState.stopped &&
                currentMedia != null) {
              return _buildMiniPlayer(currentMedia, playerState);
            }
            return const SizedBox.shrink();
          },
        ),
      ],
    );
  }

  Widget _buildMiniPlayer(MediaFile media, state_models.PlayerState state) {
    return Container(
      height: 80,
      decoration: const BoxDecoration(
        color: Color(0xFF34495E),
        border: Border(top: BorderSide(color: Color(0xFF2C3E50), width: 1)),
      ),
      child: Row(
        children: [
          // Media Thumbnail
          Container(
            width: 60,
            height: 60,
            margin: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: const Color(0xFF2C3E50),
            ),
            child: media.thumbnailPath != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.asset(
                      media.thumbnailPath!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                          _buildDefaultThumbnail(media.type),
                    ),
                  )
                : _buildDefaultThumbnail(media.type),
          ),

          // Media Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  media.metadata.title?.isNotEmpty == true
                      ? media.metadata.title!
                      : media.displayName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  media.metadata.artist?.isNotEmpty == true
                      ? media.metadata.artist!
                      : 'Unknown Artist',
                  style: const TextStyle(
                    color: Color(0xFFBDC3C7),
                    fontSize: 12,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          // Playback Controls
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: () => _previousTrack(),
                icon: const Icon(
                  Icons.skip_previous,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              IconButton(
                onPressed: () => _togglePlayPause(),
                icon: Icon(
                  state == state_models.PlayerState.playing
                      ? Icons.pause
                      : Icons.play_arrow,
                  color: const Color(0xFFE67E22),
                  size: 32,
                ),
              ),
              IconButton(
                onPressed: () => _nextTrack(),
                icon: const Icon(
                  Icons.skip_next,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ],
          ),

          // Expand Player Button
          IconButton(
            onPressed: () => _openFullPlayer(),
            icon: const Icon(
              Icons.keyboard_arrow_up,
              color: Color(0xFFBDC3C7),
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultThumbnail(MediaType type) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: const Color(0xFF2C3E50),
      ),
      child: Icon(
        type == MediaType.video ? Icons.video_file : Icons.music_note,
        color: type == MediaType.video
            ? const Color(0xFFE74C3C)
            : const Color(0xFF3498DB),
        size: 24,
      ),
    );
  }

  Future<void> _initializeMediaLibrary() async {
    try {
      await ref.read(mediaLibraryServiceProvider).initializeLibrary();
      await _refreshMediaLibrary();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to initialize media library: $e'),
            backgroundColor: const Color(0xFFE74C3C),
          ),
        );
      }
    }
  }

  Future<void> _refreshMediaLibrary() async {
    try {
      ref.read(mediaScanningProvider.notifier).state = true;
      await ref.read(mediaLibraryServiceProvider).scanMediaFiles();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to scan media files: $e'),
            backgroundColor: const Color(0xFFE74C3C),
          ),
        );
      }
    } finally {
      ref.read(mediaScanningProvider.notifier).state = false;
    }
  }

  void _togglePlayPause() {
    ref.read(mediaPlayerServiceProvider).togglePlayPause();
  }

  void _previousTrack() {
    ref.read(mediaPlayerServiceProvider).previousTrack();
  }

  void _nextTrack() {
    ref.read(mediaPlayerServiceProvider).nextTrack();
  }

  void _openFullPlayer() {
    // Navigate to full player screen
    Navigator.of(context).pushNamed('/shadow-player/full-player');
  }
}

// ShadowPlayer Tab Enum
enum ShadowPlayerTab { video, music, settings }
