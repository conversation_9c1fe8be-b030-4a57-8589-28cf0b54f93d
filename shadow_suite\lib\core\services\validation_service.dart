// import 'dart:math' as math; // Reserved for future mathematical validations
import 'error_handler.dart';

class ValidationService {
  // Email validation
  static ValidationResult validateEmail(String? email) {
    if (email == null || email.trim().isEmpty) {
      return ValidationResult.error('Email is required');
    }
    
    final trimmedEmail = email.trim();
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    
    if (!emailRegex.hasMatch(trimmedEmail)) {
      return ValidationResult.error('Please enter a valid email address');
    }
    
    if (trimmedEmail.length > 254) {
      return ValidationResult.error('Email address is too long');
    }
    
    return ValidationResult.success(trimmedEmail);
  }
  
  // Password validation
  static ValidationResult validatePassword(String? password, {
    int minLength = 8,
    bool requireUppercase = true,
    bool requireLowercase = true,
    bool requireNumbers = true,
    bool requireSpecialChars = false,
  }) {
    if (password == null || password.isEmpty) {
      return ValidationResult.error('Password is required');
    }
    
    if (password.length < minLength) {
      return ValidationResult.error('Password must be at least $minLength characters long');
    }
    
    if (requireUppercase && !password.contains(RegExp(r'[A-Z]'))) {
      return ValidationResult.error('Password must contain at least one uppercase letter');
    }
    
    if (requireLowercase && !password.contains(RegExp(r'[a-z]'))) {
      return ValidationResult.error('Password must contain at least one lowercase letter');
    }
    
    if (requireNumbers && !password.contains(RegExp(r'[0-9]'))) {
      return ValidationResult.error('Password must contain at least one number');
    }
    
    if (requireSpecialChars && !password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      return ValidationResult.error('Password must contain at least one special character');
    }
    
    return ValidationResult.success(password);
  }
  
  // Name validation
  static ValidationResult validateName(String? name, {
    int minLength = 1,
    int maxLength = 50,
    bool allowNumbers = false,
    bool allowSpecialChars = false,
  }) {
    if (name == null || name.trim().isEmpty) {
      return ValidationResult.error('Name is required');
    }
    
    final trimmedName = name.trim();
    
    if (trimmedName.length < minLength) {
      return ValidationResult.error('Name must be at least $minLength character${minLength > 1 ? 's' : ''} long');
    }
    
    if (trimmedName.length > maxLength) {
      return ValidationResult.error('Name must be no more than $maxLength characters long');
    }
    
    if (!allowNumbers && trimmedName.contains(RegExp(r'[0-9]'))) {
      return ValidationResult.error('Name cannot contain numbers');
    }
    
    if (!allowSpecialChars && trimmedName.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      return ValidationResult.error('Name cannot contain special characters');
    }
    
    return ValidationResult.success(trimmedName);
  }
  
  // Phone number validation
  static ValidationResult validatePhoneNumber(String? phone) {
    if (phone == null || phone.trim().isEmpty) {
      return ValidationResult.error('Phone number is required');
    }
    
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d+]'), '');
    
    if (cleanPhone.isEmpty) {
      return ValidationResult.error('Please enter a valid phone number');
    }
    
    if (cleanPhone.length < 10 || cleanPhone.length > 15) {
      return ValidationResult.error('Phone number must be between 10 and 15 digits');
    }
    
    return ValidationResult.success(cleanPhone);
  }
  
  // Amount validation (for financial inputs)
  static ValidationResult validateAmount(String? amount, {
    double? minValue,
    double? maxValue,
    bool allowNegative = false,
    int maxDecimalPlaces = 2,
  }) {
    if (amount == null || amount.trim().isEmpty) {
      return ValidationResult.error('Amount is required');
    }
    
    final trimmedAmount = amount.trim();
    final parsedAmount = double.tryParse(trimmedAmount);
    
    if (parsedAmount == null) {
      return ValidationResult.error('Please enter a valid amount');
    }
    
    if (!allowNegative && parsedAmount < 0) {
      return ValidationResult.error('Amount cannot be negative');
    }
    
    if (minValue != null && parsedAmount < minValue) {
      return ValidationResult.error('Amount must be at least ${minValue.toStringAsFixed(2)}');
    }
    
    if (maxValue != null && parsedAmount > maxValue) {
      return ValidationResult.error('Amount cannot exceed ${maxValue.toStringAsFixed(2)}');
    }
    
    // Check decimal places
    final decimalPart = trimmedAmount.split('.').length > 1 ? trimmedAmount.split('.')[1] : '';
    if (decimalPart.length > maxDecimalPlaces) {
      return ValidationResult.error('Amount can have at most $maxDecimalPlaces decimal places');
    }
    
    return ValidationResult.success(parsedAmount);
  }
  
  // Date validation
  static ValidationResult validateDate(String? date, {
    DateTime? minDate,
    DateTime? maxDate,
    String format = 'yyyy-MM-dd',
  }) {
    if (date == null || date.trim().isEmpty) {
      return ValidationResult.error('Date is required');
    }
    
    DateTime? parsedDate;
    try {
      parsedDate = DateTime.parse(date.trim());
    } catch (e) {
      return ValidationResult.error('Please enter a valid date');
    }
    
    if (minDate != null && parsedDate.isBefore(minDate)) {
      return ValidationResult.error('Date cannot be before ${_formatDate(minDate)}');
    }
    
    if (maxDate != null && parsedDate.isAfter(maxDate)) {
      return ValidationResult.error('Date cannot be after ${_formatDate(maxDate)}');
    }
    
    return ValidationResult.success(parsedDate);
  }
  
  // URL validation
  static ValidationResult validateUrl(String? url) {
    if (url == null || url.trim().isEmpty) {
      return ValidationResult.error('URL is required');
    }
    
    final trimmedUrl = url.trim();
    final urlRegex = RegExp(r'^https?:\/\/[^\s/$.?#].[^\s]*$', caseSensitive: false);
    
    if (!urlRegex.hasMatch(trimmedUrl)) {
      return ValidationResult.error('Please enter a valid URL');
    }
    
    return ValidationResult.success(trimmedUrl);
  }
  
  // Required field validation
  static ValidationResult validateRequired(dynamic value, String fieldName) {
    if (value == null) {
      return ValidationResult.error('$fieldName is required');
    }
    
    if (value is String && value.trim().isEmpty) {
      return ValidationResult.error('$fieldName is required');
    }
    
    if (value is List && value.isEmpty) {
      return ValidationResult.error('$fieldName is required');
    }
    
    return ValidationResult.success(value);
  }
  
  // Length validation
  static ValidationResult validateLength(String? value, {
    required String fieldName,
    int? minLength,
    int? maxLength,
  }) {
    if (value == null) {
      return ValidationResult.error('$fieldName is required');
    }
    
    if (minLength != null && value.length < minLength) {
      return ValidationResult.error('$fieldName must be at least $minLength character${minLength > 1 ? 's' : ''} long');
    }
    
    if (maxLength != null && value.length > maxLength) {
      return ValidationResult.error('$fieldName must be no more than $maxLength characters long');
    }
    
    return ValidationResult.success(value);
  }
  
  // Range validation for numbers
  static ValidationResult validateRange(num? value, {
    required String fieldName,
    num? min,
    num? max,
  }) {
    if (value == null) {
      return ValidationResult.error('$fieldName is required');
    }
    
    if (min != null && value < min) {
      return ValidationResult.error('$fieldName must be at least $min');
    }
    
    if (max != null && value > max) {
      return ValidationResult.error('$fieldName cannot exceed $max');
    }
    
    return ValidationResult.success(value);
  }
  
  // Custom validation with regex
  static ValidationResult validatePattern(String? value, {
    required String fieldName,
    required String pattern,
    required String errorMessage,
  }) {
    if (value == null || value.trim().isEmpty) {
      return ValidationResult.error('$fieldName is required');
    }
    
    final regex = RegExp(pattern);
    if (!regex.hasMatch(value.trim())) {
      return ValidationResult.error(errorMessage);
    }
    
    return ValidationResult.success(value.trim());
  }
  
  // Sanitize input to prevent XSS and injection attacks
  static String sanitizeInput(String? input) {
    if (input == null) return '';

    return input
        .trim()
        .replaceAll(RegExp(r'<script[^>]*>.*?</script>', caseSensitive: false), '')
        .replaceAll(RegExp(r'<[^>]*>'), '') // Remove HTML tags
        .replaceAll(RegExp(r'[<>"\x27`]'), '') // Remove potentially dangerous characters
        .replaceAll(RegExp(r'\s+'), ' '); // Normalize whitespace
  }
  
  // Validate multiple fields at once
  static Map<String, ValidationResult> validateFields(Map<String, ValidationRule> rules) {
    final results = <String, ValidationResult>{};
    
    for (final entry in rules.entries) {
      final fieldName = entry.key;
      final rule = entry.value;
      
      try {
        results[fieldName] = rule.validate();
      } catch (error, stackTrace) {
        ErrorHandler.handleError(error, stackTrace, ErrorType.validation,
          context: 'Field validation: $fieldName'
        );
        results[fieldName] = ValidationResult.error('Validation error occurred');
      }
    }
    
    return results;
  }
  
  // Check if all validations passed
  static bool allValid(Map<String, ValidationResult> results) {
    return results.values.every((result) => result.isValid);
  }
  
  // Get all error messages
  static List<String> getErrorMessages(Map<String, ValidationResult> results) {
    return results.values
        .where((result) => !result.isValid)
        .map((result) => result.errorMessage!)
        .toList();
  }
  
  // Private helper methods
  static String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}

// Validation result class
class ValidationResult {
  final bool isValid;
  final String? errorMessage;
  final dynamic value;
  
  const ValidationResult._(this.isValid, this.errorMessage, this.value);
  
  factory ValidationResult.success(dynamic value) {
    return ValidationResult._(true, null, value);
  }
  
  factory ValidationResult.error(String message) {
    return ValidationResult._(false, message, null);
  }
  
  @override
  String toString() {
    return isValid ? 'Valid: $value' : 'Invalid: $errorMessage';
  }
}

// Validation rule class for batch validation
abstract class ValidationRule {
  ValidationResult validate();
}

class RequiredRule extends ValidationRule {
  final dynamic value;
  final String fieldName;
  
  RequiredRule(this.value, this.fieldName);
  
  @override
  ValidationResult validate() {
    return ValidationService.validateRequired(value, fieldName);
  }
}

class EmailRule extends ValidationRule {
  final String? email;
  
  EmailRule(this.email);
  
  @override
  ValidationResult validate() {
    return ValidationService.validateEmail(email);
  }
}

class AmountRule extends ValidationRule {
  final String? amount;
  final double? minValue;
  final double? maxValue;
  final bool allowNegative;
  
  AmountRule(this.amount, {this.minValue, this.maxValue, this.allowNegative = false});
  
  @override
  ValidationResult validate() {
    return ValidationService.validateAmount(amount,
      minValue: minValue,
      maxValue: maxValue,
      allowNegative: allowNegative,
    );
  }
}
