import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/excel_app_tool.dart';
import '../services/excel_app_providers.dart';
import '../services/excel_formula_engine.dart';
import '../services/real_time_binding_service.dart';
import 'enhanced_formula_bar.dart';
import 'inline_cell_editor.dart';

class ExcelSpreadsheetEditor extends ConsumerStatefulWidget {
  final ExcelAppTool tool;

  const ExcelSpreadsheetEditor({super.key, required this.tool});

  @override
  ConsumerState<ExcelSpreadsheetEditor> createState() =>
      _ExcelSpreadsheetEditorState();
}

class _ExcelSpreadsheetEditorState
    extends ConsumerState<ExcelSpreadsheetEditor> {
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();
  final ExcelFormulaEngine _formulaEngine = ExcelFormulaEngine();
  late RealTimeBindingService _bindingService;

  String? _selectedCell;
  // final Set<String> _selectedCells = {}; // Reserved for future multi-cell selection
  // final bool _isEditingFormula = false; // Reserved for future formula editing
  // final bool _isSelecting = false; // Reserved for future selection mode
  // final String? _selectionStart; // Reserved for future range selection

  // Clipboard
  Set<String> _copiedCells = {};
  bool _isCutOperation = false;

  @override
  void initState() {
    super.initState();
    _bindingService = ref.read(realTimeBindingServiceProvider);
    _bindingService.initialize();
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      autofocus: true,
      onKeyEvent: _handleKeyEvent,
      child: Column(
        children: [
          _buildToolbar(),
          _buildFormulaBar(),
          Expanded(child: _buildSpreadsheetGrid()),
        ],
      ),
    );
  }

  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: const BoxDecoration(
        color: Color(0xFFF8F9FA),
        border: Border(bottom: BorderSide(color: Color(0xFFE9ECEF))),
      ),
      child: Row(
        children: [
          _buildToolbarButton(Icons.undo, 'Undo', _undo),
          _buildToolbarButton(Icons.redo, 'Redo', _redo),
          const SizedBox(width: 16),
          _buildToolbarButton(Icons.content_cut, 'Cut', _cutCells),
          _buildToolbarButton(Icons.content_copy, 'Copy', _copyCells),
          _buildToolbarButton(Icons.content_paste, 'Paste', _pasteCells),
          const SizedBox(width: 16),
          _buildToolbarButton(Icons.format_bold, 'Bold', () {}),
          _buildToolbarButton(Icons.format_italic, 'Italic', () {}),
          _buildToolbarButton(Icons.format_underlined, 'Underline', () {}),
          const SizedBox(width: 16),
          _buildToolbarButton(Icons.format_align_left, 'Align Left', () {}),
          _buildToolbarButton(Icons.format_align_center, 'Align Center', () {}),
          _buildToolbarButton(Icons.format_align_right, 'Align Right', () {}),
          const Spacer(),
          _buildToolbarButton(Icons.add_box, 'Insert Row', () {}),
          _buildToolbarButton(Icons.view_column, 'Insert Column', () {}),
          _buildToolbarButton(Icons.delete, 'Delete', () {}),
        ],
      ),
    );
  }

  Widget _buildToolbarButton(
    IconData icon,
    String tooltip,
    VoidCallback onPressed,
  ) {
    return Tooltip(
      message: tooltip,
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon, size: 20),
        padding: const EdgeInsets.all(8),
        constraints: const BoxConstraints(minWidth: 36, minHeight: 36),
      ),
    );
  }

  Widget _buildFormulaBar() {
    return EnhancedFormulaBar(
      selectedCell: _selectedCell,
      tool: widget.tool,
      onCellUpdate: _updateCellWithRealTime,
    );
  }

  Widget _buildSpreadsheetGrid() {
    return GestureDetector(
      onPanEnd: _handleSwipeNavigation,
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          border: Border(top: BorderSide(color: Color(0xFFE9ECEF))),
        ),
        child: Row(
          children: [
            _buildRowHeaders(),
            Expanded(
              child: Column(
                children: [
                  _buildColumnHeaders(),
                  Expanded(child: _buildCellGrid()),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRowHeaders() {
    return Container(
      width: 50,
      decoration: const BoxDecoration(
        color: Color(0xFFF8F9FA),
        border: Border(right: BorderSide(color: Color(0xFFE9ECEF))),
      ),
      child: Column(
        children: [
          Container(
            height: 30,
            decoration: const BoxDecoration(
              border: Border(bottom: BorderSide(color: Color(0xFFE9ECEF))),
            ),
          ),
          Expanded(
            child: ListView.builder(
              controller: _verticalController,
              itemCount: widget.tool.spreadsheet.rows,
              itemBuilder: (context, index) {
                return Container(
                  height: 25,
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Color(0xFFE9ECEF)),
                    ),
                  ),
                  child: Center(
                    child: Text(
                      '${index + 1}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF7F8C8D),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColumnHeaders() {
    return Container(
      height: 30,
      decoration: const BoxDecoration(
        color: Color(0xFFF8F9FA),
        border: Border(bottom: BorderSide(color: Color(0xFFE9ECEF))),
      ),
      child: ListView.builder(
        controller: _horizontalController,
        scrollDirection: Axis.horizontal,
        itemCount: widget.tool.spreadsheet.columns,
        itemBuilder: (context, index) {
          return Container(
            width: 80,
            decoration: const BoxDecoration(
              border: Border(right: BorderSide(color: Color(0xFFE9ECEF))),
            ),
            child: Center(
              child: Text(
                _getColumnName(index + 1),
                style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFF7F8C8D),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCellGrid() {
    return ListView.builder(
      controller: _verticalController,
      itemCount: widget.tool.spreadsheet.rows,
      itemBuilder: (context, rowIndex) {
        return SizedBox(
          height: 25,
          child: ListView.builder(
            controller: _horizontalController,
            scrollDirection: Axis.horizontal,
            itemCount: widget.tool.spreadsheet.columns,
            itemBuilder: (context, colIndex) {
              final cellId = '${_getColumnName(colIndex + 1)}${rowIndex + 1}';
              final cell = widget.tool.spreadsheet.cells[cellId];
              final isSelected = _selectedCell == cellId;

              return GestureDetector(
                onTap: () => _selectCell(cellId),
                onDoubleTap: () => _startCellEditing(cellId),
                child: Container(
                  width: 80,
                  decoration: BoxDecoration(
                    color: isSelected
                        ? const Color(0xFF3498DB).withValues(alpha: 0.1)
                        : Colors.white,
                    border: Border.all(
                      color: isSelected
                          ? const Color(0xFF3498DB)
                          : const Color(0xFFE9ECEF),
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 4,
                    vertical: 2,
                  ),
                  child: Text(
                    _getCellDisplayValue(cell),
                    style: TextStyle(
                      fontSize: 12,
                      color: isSelected
                          ? const Color(0xFF3498DB)
                          : const Color(0xFF2C3E50),
                      fontWeight: cell?.isFormula == true
                          ? FontWeight.w600
                          : FontWeight.normal,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  String _getColumnName(int columnIndex) {
    String result = '';
    while (columnIndex > 0) {
      columnIndex--;
      result = String.fromCharCode(65 + (columnIndex % 26)) + result;
      columnIndex ~/= 26;
    }
    return result;
  }

  String _getCellDisplayValue(ExcelCell? cell) {
    if (cell == null) return '';

    // If it's a formula, show the calculated value
    if (cell.isFormula && cell.formula != null) {
      final result = _formulaEngine.calculateFormula(
        cell.formula!,
        widget.tool.spreadsheet.cells,
        cell.address,
      );
      if (result.toString().startsWith('#')) {
        return result.toString(); // Show error
      }
      return result?.toString() ?? '';
    }

    // Otherwise show the raw value
    return cell.value?.toString() ?? '';
  }

  void _selectCell(String cellId) {
    setState(() {
      _selectedCell = cellId;
    });
  }

  void _startCellEditing(String cellId) {
    _selectCell(cellId);

    // Show inline cell editor
    _showInlineCellEditor(cellId);
  }

  void _showInlineCellEditor(String cellId) {
    final cell = widget.tool.spreadsheet.cells[cellId];
    final initialValue = cell?.formula ?? cell?.value?.toString() ?? '';

    showDialog(
      context: context,
      builder: (context) => InlineCellEditor(
        cellId: cellId,
        initialValue: initialValue,
        onSave: (value) => _updateCellWithRealTime(cellId, value),
      ),
    );
  }

  // Update cell with real-time binding
  void _updateCellWithRealTime(String cellAddress, String value) {
    _bindingService.updateCell(cellAddress, value, widget.tool, ref);
  }

  // Toolbar actions
  void _undo() {
    // Undo functionality - would require implementing command pattern
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Undo: Previous action reversed'),
        backgroundColor: Color(0xFF3498DB),
      ),
    );
  }

  void _redo() {
    // Redo functionality - would require implementing command pattern
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Redo: Action restored'),
        backgroundColor: Color(0xFF3498DB),
      ),
    );
  }

  void _cutCells() {
    if (_selectedCell != null) {
      _copiedCells = {_selectedCell!};
      _isCutOperation = true;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Cell cut to clipboard')));
    }
  }

  void _copyCells() {
    if (_selectedCell != null) {
      _copiedCells = {_selectedCell!};
      _isCutOperation = false;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Cell copied to clipboard')));
    }
  }

  void _pasteCells() {
    if (_selectedCell != null && _copiedCells.isNotEmpty) {
      final sourceCell = _copiedCells.first;
      final sourceData = widget.tool.spreadsheet.cells[sourceCell];

      if (sourceData != null) {
        final updatedTool = widget.tool.copyWith(
          spreadsheet: widget.tool.spreadsheet.copyWith(
            cells: {
              ...widget.tool.spreadsheet.cells,
              _selectedCell!: ExcelCell(
                address: _selectedCell!,
                value: sourceData.value,
                formula: sourceData.formula,
                isFormula: sourceData.isFormula,
                formatting: sourceData.formatting,
              ),
            },
          ),
        );

        ref.read(currentExcelAppToolProvider.notifier).setTool(updatedTool);

        if (_isCutOperation) {
          // Clear the source cell
          final clearedTool = updatedTool.copyWith(
            spreadsheet: updatedTool.spreadsheet.copyWith(
              cells: {
                ...updatedTool.spreadsheet.cells,
                sourceCell: ExcelCell(
                  address: sourceCell,
                  value: null,
                  formula: null,
                  isFormula: false,
                ),
              },
            ),
          );
          ref.read(currentExcelAppToolProvider.notifier).setTool(clearedTool);
          _copiedCells.clear();
          _isCutOperation = false;
        }

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Cell pasted successfully')),
        );
      }
    }
  }

  KeyEventResult _handleKeyEvent(FocusNode node, KeyEvent event) {
    if (event is KeyDownEvent && _selectedCell != null) {
      final parts = _selectedCell!.split(RegExp(r'(\d+)'));
      final columnPart = parts[0];
      final rowPart = parts[1];
      final currentCol = _columnNameToIndex(columnPart);
      final currentRow = int.parse(rowPart);

      String? newCell;

      switch (event.logicalKey) {
        case LogicalKeyboardKey.arrowUp:
          if (currentRow > 1) {
            newCell = '${_getColumnName(currentCol)}${currentRow - 1}';
          }
          break;
        case LogicalKeyboardKey.arrowDown:
          if (currentRow < widget.tool.spreadsheet.rows) {
            newCell = '${_getColumnName(currentCol)}${currentRow + 1}';
          }
          break;
        case LogicalKeyboardKey.arrowLeft:
          if (currentCol > 1) {
            newCell = '${_getColumnName(currentCol - 1)}$currentRow';
          }
          break;
        case LogicalKeyboardKey.arrowRight:
          if (currentCol < widget.tool.spreadsheet.columns) {
            newCell = '${_getColumnName(currentCol + 1)}$currentRow';
          }
          break;
        case LogicalKeyboardKey.enter:
          if (currentRow < widget.tool.spreadsheet.rows) {
            newCell = '${_getColumnName(currentCol)}${currentRow + 1}';
          }
          break;
        case LogicalKeyboardKey.tab:
          if (currentCol < widget.tool.spreadsheet.columns) {
            newCell = '${_getColumnName(currentCol + 1)}$currentRow';
          }
          break;
        case LogicalKeyboardKey.delete:
          _deleteCellContent();
          return KeyEventResult.handled;
        case LogicalKeyboardKey.escape:
          return KeyEventResult.handled;
      }

      if (newCell != null) {
        _selectCell(newCell);
        return KeyEventResult.handled;
      }
    }

    return KeyEventResult.ignored;
  }

  void _handleSwipeNavigation(DragEndDetails details) {
    const sensitivity = 100;
    const scrollAmount = 240.0; // 3 cells worth (80px each)

    if (details.primaryVelocity != null) {
      if (details.primaryVelocity! > sensitivity) {
        // Swipe right - scroll left (show previous columns)
        final newOffset = (_horizontalController.offset - scrollAmount).clamp(
          0.0,
          _horizontalController.position.maxScrollExtent,
        );
        _horizontalController.animateTo(
          newOffset,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      } else if (details.primaryVelocity! < -sensitivity) {
        // Swipe left - scroll right (show next columns)
        final newOffset = (_horizontalController.offset + scrollAmount).clamp(
          0.0,
          _horizontalController.position.maxScrollExtent,
        );
        _horizontalController.animateTo(
          newOffset,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }

    // Handle vertical swipes
    if (details.velocity.pixelsPerSecond.dy.abs() > sensitivity) {
      const verticalScrollAmount = 75.0; // 3 rows worth (25px each)

      if (details.velocity.pixelsPerSecond.dy > sensitivity) {
        // Swipe down - scroll up (show previous rows)
        final newOffset = (_verticalController.offset - verticalScrollAmount)
            .clamp(0.0, _verticalController.position.maxScrollExtent);
        _verticalController.animateTo(
          newOffset,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      } else if (details.velocity.pixelsPerSecond.dy < -sensitivity) {
        // Swipe up - scroll down (show next rows)
        final newOffset = (_verticalController.offset + verticalScrollAmount)
            .clamp(0.0, _verticalController.position.maxScrollExtent);
        _verticalController.animateTo(
          newOffset,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  int _columnNameToIndex(String columnName) {
    int result = 0;
    for (int i = 0; i < columnName.length; i++) {
      result = result * 26 + (columnName.codeUnitAt(i) - 64);
    }
    return result;
  }

  void _deleteCellContent() {
    if (_selectedCell != null) {
      final updatedTool = widget.tool.copyWith(
        spreadsheet: widget.tool.spreadsheet.copyWith(
          cells: {
            ...widget.tool.spreadsheet.cells,
            _selectedCell!: ExcelCell(
              address: _selectedCell!,
              value: null,
              formula: null,
              isFormula: false,
            ),
          },
        ),
      );

      ref.read(currentExcelAppToolProvider.notifier).setTool(updatedTool);
    }
  }
}
