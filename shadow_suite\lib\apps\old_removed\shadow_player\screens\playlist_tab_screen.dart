import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/media_providers.dart';
import '../models/media_models.dart';
import 'playlist_detail_screen.dart';

/// Playlist tab screen for managing playlists
class PlaylistTabScreen extends ConsumerStatefulWidget {
  const PlaylistTabScreen({super.key});

  @override
  ConsumerState<PlaylistTabScreen> createState() => _PlaylistTabScreenState();
}

class _PlaylistTabScreenState extends ConsumerState<PlaylistTabScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildPlaylistList(),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewPlaylist,
        tooltip: 'Create Playlist',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildPlaylistList() {
    final playlists = ref.watch(playlistsProvider);

    if (playlists.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      itemCount: playlists.length,
      itemBuilder: (context, index) {
        return _buildPlaylistItem(playlists[index]);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.playlist_play, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          const Text(
            'No playlists yet',
            style: TextStyle(fontSize: 18, color: Colors.grey),
          ),
          const SizedBox(height: 8),
          const Text(
            'Create your first playlist to organize your music',
            style: TextStyle(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _createNewPlaylist,
            icon: const Icon(Icons.add),
            label: const Text('Create Playlist'),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaylistItem(Playlist playlist) {
    return ListTile(
      leading: const CircleAvatar(child: Icon(Icons.playlist_play)),
      title: Text(playlist.name),
      subtitle: Text('${playlist.mediaIds.length} songs'),
      trailing: PopupMenuButton<String>(
        onSelected: (action) => _handlePlaylistAction(action, playlist),
        itemBuilder: (context) => [
          const PopupMenuItem(value: 'play', child: Text('Play')),
          const PopupMenuItem(value: 'edit', child: Text('Edit')),
          const PopupMenuItem(value: 'delete', child: Text('Delete')),
        ],
      ),
      onTap: () => _openPlaylist(playlist),
    );
  }

  void _createNewPlaylist() {
    showDialog(context: context, builder: (context) => _CreatePlaylistDialog());
  }

  void _openPlaylist(Playlist playlist) {
    // Navigate to playlist detail screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlaylistDetailScreen(playlist: playlist),
      ),
    );
  }

  void _handlePlaylistAction(String action, Playlist playlist) {
    switch (action) {
      case 'play':
        // Play playlist
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Playing playlist: ${playlist.name}')),
        );
        break;
      case 'edit':
        _editPlaylist(playlist);
        break;
      case 'delete':
        _deletePlaylist(playlist);
        break;
    }
  }

  void _editPlaylist(Playlist playlist) {
    showDialog(
      context: context,
      builder: (context) => _EditPlaylistDialog(playlist: playlist),
    );
  }

  void _deletePlaylist(Playlist playlist) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Playlist'),
        content: Text('Are you sure you want to delete "${playlist.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ref.read(playlistsProvider.notifier).deletePlaylist(playlist.id);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Deleted playlist: ${playlist.name}')),
              );
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

/// Dialog for creating a new playlist
class _CreatePlaylistDialog extends ConsumerStatefulWidget {
  @override
  ConsumerState<_CreatePlaylistDialog> createState() =>
      _CreatePlaylistDialogState();
}

class _CreatePlaylistDialogState extends ConsumerState<_CreatePlaylistDialog> {
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create Playlist'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'Playlist Name',
              hintText: 'Enter playlist name',
            ),
            autofocus: true,
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'Description (optional)',
              hintText: 'Enter description',
            ),
            maxLines: 3,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(onPressed: _createPlaylist, child: const Text('Create')),
      ],
    );
  }

  void _createPlaylist() {
    final name = _nameController.text.trim();
    if (name.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a playlist name')),
      );
      return;
    }

    Navigator.pop(context);
    ref
        .read(playlistsProvider.notifier)
        .createPlaylist(name, _descriptionController.text);
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Created playlist: $name')));
  }
}

/// Dialog for editing a playlist
class _EditPlaylistDialog extends ConsumerStatefulWidget {
  final Playlist playlist;

  const _EditPlaylistDialog({required this.playlist});

  @override
  ConsumerState<_EditPlaylistDialog> createState() =>
      _EditPlaylistDialogState();
}

class _EditPlaylistDialogState extends ConsumerState<_EditPlaylistDialog> {
  late final TextEditingController _nameController;
  late final TextEditingController _descriptionController;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.playlist.name);
    _descriptionController = TextEditingController(
      text: widget.playlist.description,
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit Playlist'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _nameController,
            decoration: const InputDecoration(labelText: 'Playlist Name'),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'Description (optional)',
            ),
            maxLines: 3,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(onPressed: _saveChanges, child: const Text('Save')),
      ],
    );
  }

  void _saveChanges() {
    final name = _nameController.text.trim();
    if (name.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a playlist name')),
      );
      return;
    }

    Navigator.pop(context);
    ref
        .read(playlistsProvider.notifier)
        .updatePlaylist(widget.playlist.id, name, _descriptionController.text);
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Updated playlist: $name')));
  }
}
