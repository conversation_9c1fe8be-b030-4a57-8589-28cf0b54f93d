import 'package:flutter/material.dart';

/// Account types for financial accounts
enum AccountType { checking, savings, credit, investment, loan, cash }

/// Transaction types
enum TransactionType { income, expense, transfer }

/// Transaction categories
enum TransactionCategory {
  // Income categories
  salary,
  freelance,
  investment,
  gift,
  otherIncome,

  // Expense categories
  food,
  transportation,
  housing,
  utilities,
  entertainment,
  healthcare,
  shopping,
  education,
  travel,
  insurance,
  taxes,
  otherExpense,

  // Transfer category
  transfer,
}

/// Financial Account model
class FinanceAccount {
  final String id;
  final String name;
  final String institution;
  final AccountType type;
  final double balance;
  final String currency;
  final Color color;
  final IconData icon;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;
  final bool showInTotal;
  final bool allowNegative;

  const FinanceAccount({
    required this.id,
    required this.name,
    required this.institution,
    required this.type,
    required this.balance,
    this.currency = 'USD',
    this.color = Colors.blue,
    this.icon = Icons.account_balance,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
    this.showInTotal = true,
    this.allowNegative = false,
  });

  FinanceAccount copyWith({
    String? id,
    String? name,
    String? institution,
    AccountType? type,
    double? balance,
    String? currency,
    Color? color,
    IconData? icon,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    bool? showInTotal,
    bool? allowNegative,
  }) {
    return FinanceAccount(
      id: id ?? this.id,
      name: name ?? this.name,
      institution: institution ?? this.institution,
      type: type ?? this.type,
      balance: balance ?? this.balance,
      currency: currency ?? this.currency,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      showInTotal: showInTotal ?? this.showInTotal,
      allowNegative: allowNegative ?? this.allowNegative,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'institution': institution,
      'type': type.name,
      'balance': balance,
      'currency': currency,
      'color': color.value,
      'icon': icon.codePoint,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isActive': isActive,
      'showInTotal': showInTotal,
      'allowNegative': allowNegative,
    };
  }

  factory FinanceAccount.fromJson(Map<String, dynamic> json) {
    return FinanceAccount(
      id: json['id'],
      name: json['name'],
      institution: json['institution'],
      type: AccountType.values.firstWhere((e) => e.name == json['type']),
      balance: json['balance'].toDouble(),
      currency: json['currency'] ?? 'USD',
      color: Colors.blue,
      icon: Icons.account_balance,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      isActive: json['isActive'] ?? true,
      showInTotal: json['showInTotal'] ?? true,
      allowNegative: json['allowNegative'] ?? false,
    );
  }
}

/// Financial Transaction model
class FinanceTransaction {
  final String id;
  final String accountId;
  final String? toAccountId; // For transfers
  final TransactionType type;
  final TransactionCategory category;
  final double amount;
  final String description;
  final String? notes;
  final DateTime date;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<String> tags;
  final String? receiptPath;

  const FinanceTransaction({
    required this.id,
    required this.accountId,
    this.toAccountId,
    required this.type,
    required this.category,
    required this.amount,
    required this.description,
    this.notes,
    required this.date,
    required this.createdAt,
    required this.updatedAt,
    this.tags = const [],
    this.receiptPath,
  });

  FinanceTransaction copyWith({
    String? id,
    String? accountId,
    String? toAccountId,
    TransactionType? type,
    TransactionCategory? category,
    double? amount,
    String? description,
    String? notes,
    DateTime? date,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? tags,
    String? receiptPath,
  }) {
    return FinanceTransaction(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      toAccountId: toAccountId ?? this.toAccountId,
      type: type ?? this.type,
      category: category ?? this.category,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      notes: notes ?? this.notes,
      date: date ?? this.date,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      tags: tags ?? this.tags,
      receiptPath: receiptPath ?? this.receiptPath,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'accountId': accountId,
      'toAccountId': toAccountId,
      'type': type.name,
      'category': category.name,
      'amount': amount,
      'description': description,
      'notes': notes,
      'date': date.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'tags': tags,
      'receiptPath': receiptPath,
    };
  }

  factory FinanceTransaction.fromJson(Map<String, dynamic> json) {
    return FinanceTransaction(
      id: json['id'],
      accountId: json['accountId'],
      toAccountId: json['toAccountId'],
      type: TransactionType.values.firstWhere((e) => e.name == json['type']),
      category: TransactionCategory.values.firstWhere(
        (e) => e.name == json['category'],
      ),
      amount: json['amount'].toDouble(),
      description: json['description'],
      notes: json['notes'],
      date: DateTime.parse(json['date']),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      tags: List<String>.from(json['tags'] ?? []),
      receiptPath: json['receiptPath'],
    );
  }
}

/// Budget model
class Budget {
  final String id;
  final String name;
  final TransactionCategory category;
  final double budgetAmount;
  final double spentAmount;
  final DateTime startDate;
  final DateTime endDate;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;

  const Budget({
    required this.id,
    required this.name,
    required this.category,
    required this.budgetAmount,
    this.spentAmount = 0.0,
    required this.startDate,
    required this.endDate,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
  });

  double get remainingAmount => budgetAmount - spentAmount;
  double get percentageUsed => spentAmount / budgetAmount;
  bool get isOverBudget => spentAmount > budgetAmount;

  Budget copyWith({
    String? id,
    String? name,
    TransactionCategory? category,
    double? budgetAmount,
    double? spentAmount,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
  }) {
    return Budget(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      budgetAmount: budgetAmount ?? this.budgetAmount,
      spentAmount: spentAmount ?? this.spentAmount,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
    );
  }
}

/// Financial Goal model
class FinancialGoal {
  final String id;
  final String name;
  final String description;
  final double targetAmount;
  final double currentAmount;
  final DateTime targetDate;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isCompleted;

  const FinancialGoal({
    required this.id,
    required this.name,
    required this.description,
    required this.targetAmount,
    this.currentAmount = 0.0,
    required this.targetDate,
    required this.createdAt,
    required this.updatedAt,
    this.isCompleted = false,
  });

  double get progressPercentage => currentAmount / targetAmount;
  double get remainingAmount => targetAmount - currentAmount;

  FinancialGoal copyWith({
    String? id,
    String? name,
    String? description,
    double? targetAmount,
    double? currentAmount,
    DateTime? targetDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isCompleted,
  }) {
    return FinancialGoal(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      targetAmount: targetAmount ?? this.targetAmount,
      currentAmount: currentAmount ?? this.currentAmount,
      targetDate: targetDate ?? this.targetDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }
}
