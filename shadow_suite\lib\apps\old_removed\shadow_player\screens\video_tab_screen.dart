import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/media_models.dart';

import '../services/shadow_player_providers.dart';
import '../widgets/video_grid_view.dart';
import '../widgets/video_list_view.dart';
import '../widgets/video_folder_view.dart';
import '../widgets/media_search_bar.dart';
import '../widgets/video_filter_panel.dart';

class VideoTabScreen extends ConsumerStatefulWidget {
  const VideoTabScreen({super.key});

  @override
  ConsumerState<VideoTabScreen> createState() => _VideoTabScreenState();
}

class _VideoTabScreenState extends ConsumerState<VideoTabScreen>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);

    final videos = ref.watch(filteredVideoFilesProvider);
    final viewMode = ref.watch(videoViewModeProvider);
    final isScanning = ref.watch(mediaScanningProvider);
    final searchQuery = ref.watch(videoSearchQueryProvider);

    return Column(
      children: [
        // Search and Filter Bar
        Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: Color(0xFFF8F9FA),
            border: Border(
              bottom: BorderSide(color: Color(0xFFE9ECEF), width: 1),
            ),
          ),
          child: Column(
            children: [
              // Search Bar
              MediaSearchBar(
                hintText: 'Search videos...',
                searchQuery: searchQuery,
                onSearchChanged: (query) {
                  ref.read(videoSearchQueryProvider.notifier).state = query;
                },
                onClearSearch: () {
                  ref.read(videoSearchQueryProvider.notifier).state = '';
                },
              ),
              const SizedBox(height: 12),

              // View Mode and Filter Controls
              Row(
                children: [
                  // View Mode Selector
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: const Color(0xFFE9ECEF)),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildViewModeButton(
                          icon: Icons.grid_view,
                          mode: ViewMode.grid,
                          currentMode: viewMode,
                          onTap: () =>
                              ref.read(videoViewModeProvider.notifier).state =
                                  ViewMode.grid,
                        ),
                        _buildViewModeButton(
                          icon: Icons.list,
                          mode: ViewMode.list,
                          currentMode: viewMode,
                          onTap: () =>
                              ref.read(videoViewModeProvider.notifier).state =
                                  ViewMode.list,
                        ),
                        _buildViewModeButton(
                          icon: Icons.folder,
                          mode: ViewMode.folder,
                          currentMode: viewMode,
                          onTap: () =>
                              ref.read(videoViewModeProvider.notifier).state =
                                  ViewMode.folder,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Sort Selector
                  Expanded(child: _buildSortSelector()),
                  const SizedBox(width: 12),

                  // Filter Button
                  IconButton(
                    onPressed: () => _showFilterPanel(context),
                    icon: const Icon(
                      Icons.filter_list,
                      color: Color(0xFF3498DB),
                    ),
                    tooltip: 'Filter Videos',
                  ),
                ],
              ),
            ],
          ),
        ),

        // Video Count and Status
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: const BoxDecoration(
            color: Color(0xFFF1F2F6),
            border: Border(
              bottom: BorderSide(color: Color(0xFFE9ECEF), width: 1),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.video_library,
                color: const Color(0xFFE74C3C),
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                '${videos.length} videos',
                style: const TextStyle(
                  color: Color(0xFF2C3E50),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (searchQuery.isNotEmpty) ...[
                const SizedBox(width: 8),
                Text(
                  '(filtered)',
                  style: const TextStyle(
                    color: Color(0xFF7F8C8D),
                    fontSize: 12,
                  ),
                ),
              ],
              const Spacer(),
              if (isScanning)
                const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: 12,
                      height: 12,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Color(0xFFE67E22),
                        ),
                      ),
                    ),
                    SizedBox(width: 8),
                    Text(
                      'Scanning...',
                      style: TextStyle(
                        color: Color(0xFFE67E22),
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),

        // Video Content
        Expanded(child: _buildVideoContent(videos, viewMode, isScanning)),
      ],
    );
  }

  Widget _buildVideoContent(
    List<MediaFile> videos,
    ViewMode viewMode,
    bool isScanning,
  ) {
    if (isScanning && videos.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFE74C3C)),
            ),
            SizedBox(height: 16),
            Text(
              'Scanning for videos...',
              style: TextStyle(color: Color(0xFF7F8C8D), fontSize: 16),
            ),
          ],
        ),
      );
    }

    if (videos.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.video_library_outlined,
              size: 64,
              color: const Color(0xFFBDC3C7),
            ),
            const SizedBox(height: 16),
            const Text(
              'No videos found',
              style: TextStyle(
                color: Color(0xFF7F8C8D),
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Try refreshing or check your scan locations',
              style: TextStyle(color: Color(0xFF95A5A6), fontSize: 14),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _refreshVideos(),
              icon: const Icon(Icons.refresh),
              label: const Text('Refresh Library'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFE74C3C),
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    switch (viewMode) {
      case ViewMode.grid:
        return VideoGridView(videos: videos);
      case ViewMode.list:
        return VideoListView(videos: videos);
      case ViewMode.folder:
        return VideoFolderView(videos: videos);
    }
  }

  Widget _buildViewModeButton({
    required IconData icon,
    required ViewMode mode,
    required ViewMode currentMode,
    required VoidCallback onTap,
  }) {
    final isSelected = mode == currentMode;

    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF3498DB) : Colors.transparent,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Icon(
          icon,
          color: isSelected ? Colors.white : const Color(0xFF7F8C8D),
          size: 20,
        ),
      ),
    );
  }

  Widget _buildSortSelector() {
    final sortBy = ref.watch(videoSortByProvider);
    final sortOrder = ref.watch(videoSortOrderProvider);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE9ECEF)),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<SortBy>(
          value: sortBy,
          isDense: true,
          icon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(width: 4),
              Icon(
                sortOrder == SortOrder.ascending
                    ? Icons.arrow_upward
                    : Icons.arrow_downward,
                size: 16,
                color: const Color(0xFF7F8C8D),
              ),
            ],
          ),
          style: const TextStyle(color: Color(0xFF2C3E50), fontSize: 14),
          onChanged: (SortBy? newValue) {
            if (newValue != null) {
              if (newValue == sortBy) {
                // Toggle sort order
                final newOrder = sortOrder == SortOrder.ascending
                    ? SortOrder.descending
                    : SortOrder.ascending;
                ref.read(videoSortOrderProvider.notifier).state = newOrder;
              } else {
                ref.read(videoSortByProvider.notifier).state = newValue;
              }
            }
          },
          items: const [
            DropdownMenuItem(value: SortBy.name, child: Text('Name')),
            DropdownMenuItem(
              value: SortBy.dateAdded,
              child: Text('Date Added'),
            ),
            DropdownMenuItem(
              value: SortBy.dateModified,
              child: Text('Date Modified'),
            ),
            DropdownMenuItem(value: SortBy.size, child: Text('Size')),
            DropdownMenuItem(value: SortBy.duration, child: Text('Duration')),
          ],
        ),
      ),
    );
  }

  void _showFilterPanel(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        builder: (context, scrollController) =>
            VideoFilterPanel(scrollController: scrollController),
      ),
    );
  }

  void _refreshVideos() {
    ref.read(mediaLibraryServiceProvider).scanMediaFiles();
  }
}
