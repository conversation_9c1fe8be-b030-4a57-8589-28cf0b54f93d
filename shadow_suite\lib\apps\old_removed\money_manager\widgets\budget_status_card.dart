import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/money_manager_models.dart';
import '../services/money_manager_providers.dart';
import '../money_manager_main.dart';

class BudgetStatusCard extends ConsumerWidget {
  const BudgetStatusCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final budgetsAsync = ref.watch(budgetsProvider);
    final categoriesAsync = ref.watch(categoriesProvider);

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF9B59B6).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.pie_chart,
                  color: Color(0xFF9B59B6),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Budget Status',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
              ),
              TextButton(
                onPressed: () {
                  ref.read(currentMoneyManagerScreenProvider.notifier).state = 
                      MoneyManagerScreen.budgets;
                },
                child: const Text('View All'),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          budgetsAsync.when(
            data: (budgets) {
              if (budgets.isEmpty) {
                return _buildEmptyState();
              }
              
              return categoriesAsync.when(
                data: (categories) => _buildBudgetsList(budgets, categories),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => _buildErrorState(error.toString()),
              );
            },
            loading: () => const Center(
              child: Padding(
                padding: EdgeInsets.all(32),
                child: CircularProgressIndicator(),
              ),
            ),
            error: (error, stack) => _buildErrorState(error.toString()),
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetsList(List<Budget> budgets, List<Category> categories) {
    // Filter current month budgets
    final now = DateTime.now();
    final currentMonthBudgets = budgets.where((budget) {
      return budget.startDate.month == now.month && 
             budget.startDate.year == now.year;
    }).toList();

    if (currentMonthBudgets.isEmpty) {
      return _buildNoBudgetsThisMonth();
    }

    return Column(
      children: currentMonthBudgets.take(3).map((budget) {
        final category = categories.firstWhere(
          (cat) => cat.id == budget.categoryId,
          orElse: () => Category(
            id: '',
            name: 'Unknown Category',
            type: CategoryType.expense,
            createdAt: DateTime.now(),
          ),
        );
        
        return _buildBudgetItem(budget, category);
      }).toList(),
    );
  }

  Widget _buildBudgetItem(Budget budget, Category category) {
    final progress = budget.progress;
    final isOverBudget = budget.isOverBudget;
    final shouldAlert = budget.shouldAlert;
    
    Color progressColor;
    if (isOverBudget) {
      progressColor = const Color(0xFFE74C3C);
    } else if (shouldAlert) {
      progressColor = const Color(0xFFF39C12);
    } else {
      progressColor = const Color(0xFF27AE60);
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isOverBudget 
              ? const Color(0xFFE74C3C).withValues(alpha: 0.3)
              : Colors.grey.shade200,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: Color(int.parse(category.color.replaceFirst('#', '0xFF'))).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getCategoryIcon(category.icon),
                  color: Color(int.parse(category.color.replaceFirst('#', '0xFF'))),
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      budget.name,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF2C3E50),
                      ),
                    ),
                    Text(
                      category.name,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF7F8C8D),
                      ),
                    ),
                  ],
                ),
              ),
              if (shouldAlert || isOverBudget)
                Icon(
                  isOverBudget ? Icons.warning : Icons.info,
                  color: progressColor,
                  size: 16,
                ),
            ],
          ),
          const SizedBox(height: 12),
          
          // Progress Bar
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '\$${budget.spent.toStringAsFixed(2)} spent',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFF7F8C8D),
                    ),
                  ),
                  Text(
                    '\$${budget.amount.toStringAsFixed(2)} budget',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFF7F8C8D),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 6),
              ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: LinearProgressIndicator(
                  value: progress.clamp(0.0, 1.0),
                  backgroundColor: Colors.grey.shade300,
                  valueColor: AlwaysStoppedAnimation<Color>(progressColor),
                  minHeight: 6,
                ),
              ),
              const SizedBox(height: 6),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${(progress * 100).toStringAsFixed(0)}% used',
                    style: TextStyle(
                      fontSize: 11,
                      color: progressColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (budget.remaining > 0)
                    Text(
                      '\$${budget.remaining.toStringAsFixed(2)} left',
                      style: const TextStyle(
                        fontSize: 11,
                        color: Color(0xFF27AE60),
                        fontWeight: FontWeight.w500,
                      ),
                    )
                  else
                    Text(
                      '\$${budget.spent - budget.amount} over',
                      style: const TextStyle(
                        fontSize: 11,
                        color: Color(0xFFE74C3C),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: const Column(
        children: [
          Icon(
            Icons.pie_chart_outline,
            size: 48,
            color: Color(0xFFBDC3C7),
          ),
          SizedBox(height: 16),
          Text(
            'No Budgets Set',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF7F8C8D),
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Create budgets to track your spending',
            style: TextStyle(
              color: Color(0xFF95A5A6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNoBudgetsThisMonth() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: const Column(
        children: [
          Icon(
            Icons.calendar_today,
            size: 40,
            color: Color(0xFFBDC3C7),
          ),
          SizedBox(height: 12),
          Text(
            'No Budgets This Month',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Color(0xFF7F8C8D),
            ),
          ),
          SizedBox(height: 6),
          Text(
            'Set up monthly budgets to track spending',
            style: TextStyle(
              fontSize: 12,
              color: Color(0xFF95A5A6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          const Icon(
            Icons.error_outline,
            size: 48,
            color: Color(0xFFE74C3C),
          ),
          const SizedBox(height: 16),
          const Text(
            'Error Loading Budgets',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFFE74C3C),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: const TextStyle(
              color: Color(0xFF95A5A6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  IconData _getCategoryIcon(String iconName) {
    switch (iconName) {
      case 'restaurant':
        return Icons.restaurant;
      case 'directions_car':
        return Icons.directions_car;
      case 'shopping_bag':
        return Icons.shopping_bag;
      case 'electrical_services':
        return Icons.electrical_services;
      case 'movie':
        return Icons.movie;
      case 'local_hospital':
        return Icons.local_hospital;
      case 'work':
        return Icons.work;
      case 'laptop':
        return Icons.laptop;
      case 'trending_up':
        return Icons.trending_up;
      default:
        return Icons.category;
    }
  }
}
