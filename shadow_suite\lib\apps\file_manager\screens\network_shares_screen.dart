import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../file_manager_main.dart';
import '../services/network_drive_service.dart';

class NetworkSharesScreen extends ConsumerStatefulWidget {
  const NetworkSharesScreen({super.key});

  @override
  ConsumerState<NetworkSharesScreen> createState() => _NetworkSharesScreenState();
}

class _NetworkSharesScreenState extends ConsumerState<NetworkSharesScreen> {
  List<NetworkDriveInfo> _discoveredDrives = [];
  List<NetworkDrive> _mountedDrives = [];
  bool _isDiscovering = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadMountedDrives();
    _startDiscovery();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          FileManagerHeader(
            title: 'Network Shares',
            subtitle: 'Access shared folders on your network',
            actions: [
              IconButton(
                onPressed: _isDiscovering ? null : _startDiscovery,
                icon: _isDiscovering
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.refresh, color: Color(0xFFE67E22)),
                tooltip: 'Refresh Network',
              ),
              IconButton(
                onPressed: _showAddNetworkDriveDialog,
                icon: const Icon(Icons.add, color: Color(0xFFE67E22)),
                tooltip: 'Add Network Drive',
              ),
            ],
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          const TabBar(
            labelColor: Color(0xFFE67E22),
            unselectedLabelColor: Color(0xFF7F8C8D),
            indicatorColor: Color(0xFFE67E22),
            tabs: [
              Tab(text: 'Mounted Drives'),
              Tab(text: 'Discovered'),
            ],
          ),
          Expanded(
            child: TabBarView(
              children: [
                _buildMountedDrives(),
                _buildDiscoveredDrives(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMountedDrives() {
    if (_mountedDrives.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.folder_off,
              size: 64,
              color: Color(0xFF7F8C8D),
            ),
            const SizedBox(height: 16),
            const Text(
              'No Network Drives Mounted',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2C3E50),
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Mount network drives to access shared folders',
              style: TextStyle(color: Color(0xFF7F8C8D)),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _showAddNetworkDriveDialog,
              icon: const Icon(Icons.add),
              label: const Text('Add Network Drive'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFE67E22),
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _mountedDrives.length,
      itemBuilder: (context, index) {
        final drive = _mountedDrives[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: Icon(
              _getDriveIcon(drive.type),
              color: drive.isConnected ? const Color(0xFF27AE60) : Colors.red,
              size: 32,
            ),
            title: Text(
              drive.displayName,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(drive.connectionUrl),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: drive.isConnected ? const Color(0xFF27AE60) : Colors.red,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      drive.isConnected ? 'Connected' : 'Disconnected',
                      style: TextStyle(
                        color: drive.isConnected ? const Color(0xFF27AE60) : Colors.red,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            trailing: PopupMenuButton(
              itemBuilder: (context) => [
                if (drive.isConnected) ...[
                  const PopupMenuItem(
                    value: 'browse',
                    child: Row(
                      children: [
                        Icon(Icons.folder_open),
                        SizedBox(width: 8),
                        Text('Browse'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'refresh',
                    child: Row(
                      children: [
                        Icon(Icons.refresh),
                        SizedBox(width: 8),
                        Text('Refresh'),
                      ],
                    ),
                  ),
                ],
                const PopupMenuItem(
                  value: 'unmount',
                  child: Row(
                    children: [
                      Icon(Icons.eject, color: Colors.red),
                      SizedBox(width: 8),
                      Text('Unmount', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
              onSelected: (value) => _handleDriveAction(drive, value.toString()),
            ),
            onTap: drive.isConnected ? () => _browseDrive(drive) : null,
          ),
        );
      },
    );
  }

  Widget _buildDiscoveredDrives() {
    if (_discoveredDrives.isEmpty && !_isDiscovering) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.search_off,
              size: 64,
              color: Color(0xFF7F8C8D),
            ),
            const SizedBox(height: 16),
            const Text(
              'No Network Drives Found',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2C3E50),
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Start discovery to find available network drives',
              style: TextStyle(color: Color(0xFF7F8C8D)),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _startDiscovery,
              icon: const Icon(Icons.search),
              label: const Text('Start Discovery'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFE67E22),
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _discoveredDrives.length,
      itemBuilder: (context, index) {
        final driveInfo = _discoveredDrives[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ExpansionTile(
            leading: Icon(
              _getDriveIcon(driveInfo.type),
              color: const Color(0xFFE67E22),
              size: 32,
            ),
            title: Text(
              driveInfo.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text('${driveInfo.host} (${driveInfo.shares.length} shares)'),
            children: driveInfo.shares.map((share) {
              return ListTile(
                leading: const Icon(Icons.folder_shared, size: 20),
                title: Text(share),
                trailing: ElevatedButton(
                  onPressed: () => _mountShare(driveInfo, share),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFE67E22),
                    foregroundColor: Colors.white,
                    minimumSize: const Size(80, 32),
                  ),
                  child: const Text('Mount'),
                ),
              );
            }).toList(),
          ),
        );
      },
    );
  }

  void _loadMountedDrives() {
    _mountedDrives = NetworkDriveService.mountedDrives;
    setState(() => _isLoading = false);
  }

  void _startDiscovery() async {
    setState(() => _isDiscovering = true);

    try {
      final discovered = await NetworkDriveService.discoverNetworkDrives();
      setState(() {
        _discoveredDrives = discovered;
        _isDiscovering = false;
      });
    } catch (error) {
      setState(() => _isDiscovering = false);
      _showError('Discovery failed: $error');
    }
  }

  IconData _getDriveIcon(NetworkDriveType type) {
    switch (type) {
      case NetworkDriveType.smb:
        return Icons.folder_shared;
      case NetworkDriveType.ftp:
        return Icons.cloud_upload;
      case NetworkDriveType.sftp:
        return Icons.security;
      case NetworkDriveType.webdav:
        return Icons.web;
    }
  }

  void _showAddNetworkDriveDialog() {
    showDialog(
      context: context,
      builder: (context) => AddNetworkDriveDialog(
        onMount: (drive) {
          setState(() {
            _mountedDrives.add(drive);
          });
          _showSuccess('Network drive mounted successfully');
        },
      ),
    );
  }

  void _handleDriveAction(NetworkDrive drive, String action) {
    switch (action) {
      case 'browse':
        _browseDrive(drive);
        break;
      case 'refresh':
        _refreshDrive(drive);
        break;
      case 'unmount':
        _unmountDrive(drive);
        break;
    }
  }

  void _browseDrive(NetworkDrive drive) {
    _showInfo('Opening ${drive.displayName}...');
  }

  void _refreshDrive(NetworkDrive drive) async {
    await NetworkDriveService.refreshDriveStatus(drive.id);
    _loadMountedDrives();
  }

  void _unmountDrive(NetworkDrive drive) async {
    final success = await NetworkDriveService.unmountDrive(drive.id);
    if (success) {
      _loadMountedDrives();
      _showSuccess('Drive unmounted successfully');
    } else {
      _showError('Failed to unmount drive');
    }
  }

  void _mountShare(NetworkDriveInfo driveInfo, String share) async {
    final success = await NetworkDriveService.mountDrive(
      name: '${driveInfo.name} - $share',
      host: driveInfo.host,
      path: '/$share',
      type: driveInfo.type,
    );

    if (success) {
      _loadMountedDrives();
      _showSuccess('Share mounted successfully');
    } else {
      _showError('Failed to mount share');
    }
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF27AE60),
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showInfo(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF3498DB),
      ),
    );
  }
}

class AddNetworkDriveDialog extends StatefulWidget {
  final Function(NetworkDrive) onMount;

  const AddNetworkDriveDialog({super.key, required this.onMount});

  @override
  State<AddNetworkDriveDialog> createState() => _AddNetworkDriveDialogState();
}

class _AddNetworkDriveDialogState extends State<AddNetworkDriveDialog> {
  final _nameController = TextEditingController();
  final _hostController = TextEditingController();
  final _pathController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _portController = TextEditingController();

  NetworkDriveType _selectedType = NetworkDriveType.smb;
  bool _isConnecting = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Network Drive'),
      content: SizedBox(
        width: 400,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Drive Name',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<NetworkDriveType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'Connection Type',
                  border: OutlineInputBorder(),
                ),
                items: NetworkDriveType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(type.name.toUpperCase()),
                  );
                }).toList(),
                onChanged: (value) => setState(() => _selectedType = value!),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _hostController,
                decoration: const InputDecoration(
                  labelText: 'Host/IP Address',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _pathController,
                decoration: const InputDecoration(
                  labelText: 'Path',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _usernameController,
                      decoration: const InputDecoration(
                        labelText: 'Username',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextField(
                      controller: _passwordController,
                      obscureText: true,
                      decoration: const InputDecoration(
                        labelText: 'Password',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _portController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Port (optional)',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isConnecting ? null : () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isConnecting ? null : _mountDrive,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFE67E22),
            foregroundColor: Colors.white,
          ),
          child: _isConnecting
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Mount'),
        ),
      ],
    );
  }

  void _mountDrive() async {
    if (_nameController.text.isEmpty || _hostController.text.isEmpty) {
      return;
    }

    setState(() => _isConnecting = true);

    try {
      final success = await NetworkDriveService.mountDrive(
        name: _nameController.text,
        host: _hostController.text,
        path: _pathController.text.isEmpty ? '/' : _pathController.text,
        type: _selectedType,
        username: _usernameController.text.isEmpty ? null : _usernameController.text,
        password: _passwordController.text.isEmpty ? null : _passwordController.text,
        port: int.tryParse(_portController.text),
      );

      if (success && mounted) {
        final drive = NetworkDriveService.mountedDrives.last;
        widget.onMount(drive);
        Navigator.pop(context);
      }
    } catch (error) {
      // Error handling is done in the service
    } finally {
      if (mounted) {
        setState(() => _isConnecting = false);
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _hostController.dispose();
    _pathController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _portController.dispose();
    super.dispose();
  }
}
