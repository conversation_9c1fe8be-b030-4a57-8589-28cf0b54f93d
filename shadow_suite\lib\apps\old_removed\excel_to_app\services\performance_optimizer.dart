import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// Performance optimization service for Excel-to-App builder
class PerformanceOptimizer {
  static final Map<String, dynamic> _cache = {};
  static final Queue<String> _cacheKeys = Queue<String>();
  static const int _maxCacheSize = 1000;
  static final Map<String, Timer> _debounceTimers = {};
  static final Map<String, PerformanceMetric> _metrics = {};
  static bool _isInitialized = false;

  /// Initialize the performance optimizer
  static void initialize() {
    if (_isInitialized) return;
    
    _startPerformanceMonitoring();
    _isInitialized = true;
  }

  /// Cache a value with automatic cleanup
  static void cacheValue(String key, dynamic value) {
    if (_cache.length >= _maxCacheSize) {
      final oldestKey = _cacheKeys.removeFirst();
      _cache.remove(oldestKey);
    }
    
    _cache[key] = value;
    _cacheKeys.add(key);
  }

  /// Get cached value
  static T? getCachedValue<T>(String key) {
    return _cache[key] as T?;
  }

  /// Clear cache
  static void clearCache() {
    _cache.clear();
    _cacheKeys.clear();
  }

  /// Debounce function calls
  static void debounce(String key, VoidCallback callback, Duration delay) {
    _debounceTimers[key]?.cancel();
    _debounceTimers[key] = Timer(delay, callback);
  }

  /// Throttle function calls
  static bool throttle(String key, Duration interval) {
    final now = DateTime.now();
    final lastCall = _cache['throttle_$key'] as DateTime?;
    
    if (lastCall == null || now.difference(lastCall) >= interval) {
      _cache['throttle_$key'] = now;
      return true;
    }
    
    return false;
  }

  /// Measure function execution time
  static Future<T> measureAsync<T>(String operationName, Future<T> Function() operation) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      final result = await operation();
      stopwatch.stop();
      
      _recordMetric(operationName, stopwatch.elapsedMicroseconds);
      return result;
    } catch (e) {
      stopwatch.stop();
      _recordMetric(operationName, stopwatch.elapsedMicroseconds, hasError: true);
      rethrow;
    }
  }

  /// Measure synchronous function execution time
  static T measureSync<T>(String operationName, T Function() operation) {
    final stopwatch = Stopwatch()..start();
    
    try {
      final result = operation();
      stopwatch.stop();
      
      _recordMetric(operationName, stopwatch.elapsedMicroseconds);
      return result;
    } catch (e) {
      stopwatch.stop();
      _recordMetric(operationName, stopwatch.elapsedMicroseconds, hasError: true);
      rethrow;
    }
  }

  /// Optimize widget rebuilds with memoization
  static T memoize<T>(String key, T Function() computation) {
    if (_cache.containsKey(key)) {
      return _cache[key] as T;
    }
    
    final result = computation();
    cacheValue(key, result);
    return result;
  }

  /// Batch operations for better performance
  static Future<List<T>> batchOperations<T>(
    List<Future<T> Function()> operations,
    {int batchSize = 10}
  ) async {
    final results = <T>[];
    
    for (int i = 0; i < operations.length; i += batchSize) {
      final batch = operations.skip(i).take(batchSize);
      final batchResults = await Future.wait(batch.map((op) => op()));
      results.addAll(batchResults);
      
      // Allow UI to update between batches
      if (i + batchSize < operations.length) {
        await Future.delayed(const Duration(milliseconds: 1));
      }
    }
    
    return results;
  }

  /// Optimize image loading and caching
  static void preloadImages(List<String> imagePaths) {
    for (final path in imagePaths) {
      if (!_cache.containsKey('image_$path')) {
        // In a real implementation, this would preload the image
        cacheValue('image_$path', true);
      }
    }
  }

  /// Memory usage optimization
  static void optimizeMemoryUsage() {
    // Clear old cache entries
    if (_cache.length > _maxCacheSize * 0.8) {
      final keysToRemove = _cacheKeys.take(_maxCacheSize ~/ 4).toList();
      for (final key in keysToRemove) {
        _cache.remove(key);
        _cacheKeys.remove(key);
      }
    }
    
    // Cancel old debounce timers
    _debounceTimers.removeWhere((key, timer) {
      if (!timer.isActive) {
        timer.cancel();
        return true;
      }
      return false;
    });
    
    // Force garbage collection in debug mode
    if (kDebugMode) {
      SystemChannels.platform.invokeMethod('System.gc');
    }
  }

  /// Get performance metrics
  static Map<String, PerformanceMetric> getMetrics() {
    return Map.unmodifiable(_metrics);
  }

  /// Get performance report
  static PerformanceReport getPerformanceReport() {
    final totalOperations = _metrics.values.fold<int>(0, (sum, metric) => sum + metric.callCount);
    final averageExecutionTime = _metrics.values.isEmpty 
        ? 0.0 
        : _metrics.values.fold<double>(0, (sum, metric) => sum + metric.averageTime) / _metrics.length;
    
    final slowOperations = _metrics.entries
        .where((entry) => entry.value.averageTime > 100000) // >100ms
        .map((entry) => entry.key)
        .toList();
    
    final errorOperations = _metrics.entries
        .where((entry) => entry.value.errorCount > 0)
        .map((entry) => entry.key)
        .toList();
    
    return PerformanceReport(
      totalOperations: totalOperations,
      averageExecutionTime: averageExecutionTime,
      cacheHitRate: _calculateCacheHitRate(),
      memoryUsage: _cache.length,
      slowOperations: slowOperations,
      errorOperations: errorOperations,
      metrics: Map.from(_metrics),
    );
  }

  /// Reset performance metrics
  static void resetMetrics() {
    _metrics.clear();
  }

  /// Optimize for mobile devices
  static void optimizeForMobile() {
    // Reduce cache size for mobile
    const mobileCacheSize = 500;
    while (_cache.length > mobileCacheSize) {
      final oldestKey = _cacheKeys.removeFirst();
      _cache.remove(oldestKey);
    }
    
    // More aggressive memory cleanup
    optimizeMemoryUsage();
  }

  /// Optimize for desktop devices
  static void optimizeForDesktop() {
    // Desktop can handle larger cache
    // Current settings are already optimized for desktop
  }

  /// Check if operation meets performance target
  static bool meetsPerformanceTarget(String operationName, {double targetMs = 100}) {
    final metric = _metrics[operationName];
    if (metric == null) return true;
    
    return metric.averageTime < targetMs * 1000; // Convert to microseconds
  }

  /// Get optimization suggestions
  static List<String> getOptimizationSuggestions() {
    final suggestions = <String>[];
    
    // Check for slow operations
    final slowOps = _metrics.entries
        .where((entry) => entry.value.averageTime > 100000)
        .toList();
    
    if (slowOps.isNotEmpty) {
      suggestions.add('Consider optimizing these slow operations: ${slowOps.map((e) => e.key).join(', ')}');
    }
    
    // Check cache efficiency
    final hitRate = _calculateCacheHitRate();
    if (hitRate < 0.7) {
      suggestions.add('Cache hit rate is low ($hitRate). Consider caching more frequently used data.');
    }
    
    // Check memory usage
    if (_cache.length > _maxCacheSize * 0.9) {
      suggestions.add('Cache is nearly full. Consider clearing old entries or increasing cache size.');
    }
    
    // Check for operations with errors
    final errorOps = _metrics.entries
        .where((entry) => entry.value.errorCount > 0)
        .toList();
    
    if (errorOps.isNotEmpty) {
      suggestions.add('These operations have errors: ${errorOps.map((e) => e.key).join(', ')}');
    }
    
    if (suggestions.isEmpty) {
      suggestions.add('Performance is optimal! No suggestions at this time.');
    }
    
    return suggestions;
  }

  // Private methods
  static void _recordMetric(String operationName, int microseconds, {bool hasError = false}) {
    final metric = _metrics[operationName] ?? PerformanceMetric(operationName);
    
    metric.callCount++;
    metric.totalTime += microseconds;
    metric.averageTime = metric.totalTime / metric.callCount;
    
    if (microseconds < metric.minTime || metric.minTime == 0) {
      metric.minTime = microseconds;
    }
    
    if (microseconds > metric.maxTime) {
      metric.maxTime = microseconds;
    }
    
    if (hasError) {
      metric.errorCount++;
    }
    
    _metrics[operationName] = metric;
  }

  static double _calculateCacheHitRate() {
    final hits = _cache['cache_hits'] as int? ?? 0;
    final misses = _cache['cache_misses'] as int? ?? 0;
    final total = hits + misses;
    
    return total > 0 ? hits / total : 0.0;
  }

  static void _startPerformanceMonitoring() {
    Timer.periodic(const Duration(minutes: 5), (_) {
      optimizeMemoryUsage();
    });
  }

  /// Dispose resources
  static void dispose() {
    for (final timer in _debounceTimers.values) {
      timer.cancel();
    }
    _debounceTimers.clear();
    clearCache();
    _metrics.clear();
    _isInitialized = false;
  }
}

/// Performance metric data class
class PerformanceMetric {
  final String operationName;
  int callCount = 0;
  int totalTime = 0;
  double averageTime = 0;
  int minTime = 0;
  int maxTime = 0;
  int errorCount = 0;

  PerformanceMetric(this.operationName);
}

/// Performance report data class
class PerformanceReport {
  final int totalOperations;
  final double averageExecutionTime;
  final double cacheHitRate;
  final int memoryUsage;
  final List<String> slowOperations;
  final List<String> errorOperations;
  final Map<String, PerformanceMetric> metrics;

  const PerformanceReport({
    required this.totalOperations,
    required this.averageExecutionTime,
    required this.cacheHitRate,
    required this.memoryUsage,
    required this.slowOperations,
    required this.errorOperations,
    required this.metrics,
  });

  bool get isOptimal => slowOperations.isEmpty && errorOperations.isEmpty && cacheHitRate > 0.8;
}
