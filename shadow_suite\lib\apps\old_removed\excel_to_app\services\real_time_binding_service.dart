import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/excel_app_tool.dart';
import '../services/excel_formula_engine.dart';
import 'excel_app_providers.dart';

// Real-time data binding service for Excel to App
class RealTimeBindingService {
  static final RealTimeBindingService _instance = RealTimeBindingService._internal();
  factory RealTimeBindingService() => _instance;
  RealTimeBindingService._internal();

  final ExcelFormulaEngine _formulaEngine = ExcelFormulaEngine();
  final Map<String, StreamController<dynamic>> _cellStreams = {};
  final Map<String, Set<String>> _cellDependencies = {};
  final Map<String, Timer> _autoSaveTimers = {};
  
  Timer? _globalAutoSaveTimer;
  bool _autoSaveEnabled = true;
  int _autoSaveInterval = 2; // seconds

  // Initialize the service
  void initialize({bool autoSave = true, int autoSaveInterval = 2}) {
    _autoSaveEnabled = autoSave;
    _autoSaveInterval = autoSaveInterval;
  }

  // Get stream for a specific cell
  Stream<dynamic> getCellStream(String cellAddress) {
    if (!_cellStreams.containsKey(cellAddress)) {
      _cellStreams[cellAddress] = StreamController<dynamic>.broadcast();
    }
    return _cellStreams[cellAddress]!.stream;
  }

  // Update cell value and trigger real-time updates
  void updateCell(String cellAddress, dynamic value, ExcelAppTool tool, WidgetRef ref) {
    // Update the cell in the tool
    final updatedTool = _updateToolCell(tool, cellAddress, value);

    // Save to provider immediately
    ref.read(currentExcelAppToolProvider.notifier).setTool(updatedTool);

    // Clear formula cache
    _formulaEngine.clearCache();

    // Recalculate dependent cells immediately
    _recalculateDependentCells(cellAddress, updatedTool);

    // Get the updated tool from provider
    final currentTool = ref.read(currentExcelAppToolProvider);
    if (currentTool == null) return;

    // Emit the new value to streams immediately
    _emitCellValue(cellAddress, value);

    // Update bound UI components immediately
    _updateBoundComponents(cellAddress, currentTool, ref);

    // Schedule auto-save
    if (_autoSaveEnabled) {
      _scheduleAutoSave(currentTool, ref);
    }
  }

  // Update tool cell and return new tool
  ExcelAppTool _updateToolCell(ExcelAppTool tool, String cellAddress, dynamic value) {
    final isFormula = value.toString().startsWith('=');
    
    final updatedCell = ExcelCell(
      address: cellAddress,
      value: isFormula ? null : value,
      formula: isFormula ? value.toString() : null,
      isFormula: isFormula,
    );

    final updatedCells = Map<String, ExcelCell>.from(tool.spreadsheet.cells);
    updatedCells[cellAddress] = updatedCell;

    return tool.copyWith(
      spreadsheet: tool.spreadsheet.copyWith(
        cells: updatedCells,
        lastModified: DateTime.now(),
      ),
      lastModified: DateTime.now(),
    );
  }

  // Recalculate cells that depend on the changed cell
  void _recalculateDependentCells(String changedCell, ExcelAppTool tool) {
    final dependentCells = _findDependentCells(changedCell, tool);
    
    for (final cellAddress in dependentCells) {
      final cell = tool.spreadsheet.cells[cellAddress];
      if (cell != null && cell.isFormula && cell.formula != null) {
        final calculatedValue = _formulaEngine.calculateFormula(
          cell.formula!,
          tool.spreadsheet.cells,
          cellAddress,
        );
        
        // Emit the calculated value
        _emitCellValue(cellAddress, calculatedValue);
      }
    }
  }

  // Find cells that depend on the given cell
  Set<String> _findDependentCells(String cellAddress, ExcelAppTool tool) {
    final dependents = <String>{};
    
    for (final entry in tool.spreadsheet.cells.entries) {
      final cell = entry.value;
      if (cell.isFormula && cell.formula != null) {
        if (_formulaReferencesCell(cell.formula!, cellAddress)) {
          dependents.add(entry.key);
        }
      }
    }
    
    return dependents;
  }

  // Check if a formula references a specific cell
  bool _formulaReferencesCell(String formula, String cellAddress) {
    final cellPattern = RegExp(r'\b$cellAddress\b', caseSensitive: false);
    return cellPattern.hasMatch(formula);
  }

  // Emit cell value to stream
  void _emitCellValue(String cellAddress, dynamic value) {
    if (_cellStreams.containsKey(cellAddress)) {
      _cellStreams[cellAddress]!.add(value);
    }
  }

  // Update bound UI components immediately
  void _updateBoundComponents(String cellAddress, ExcelAppTool tool, WidgetRef ref) {
    final boundComponents = getBoundComponents(cellAddress, tool);

    for (final component in boundComponents) {
      // Emit update event for each bound component
      final componentValue = getCellDisplayValue(cellAddress, tool);
      _emitComponentUpdate(component.id, componentValue);
    }
  }

  // Emit component update event
  void _emitComponentUpdate(String componentId, dynamic value) {
    // This would be used by UI components to listen for updates
    // Implementation depends on how components are structured
  }

  // Schedule auto-save
  void _scheduleAutoSave(ExcelAppTool tool, WidgetRef ref) {
    // Cancel existing timer
    _globalAutoSaveTimer?.cancel();
    
    // Schedule new auto-save
    _globalAutoSaveTimer = Timer(Duration(seconds: _autoSaveInterval), () {
      _performAutoSave(tool, ref);
    });
  }

  // Perform auto-save
  void _performAutoSave(ExcelAppTool tool, WidgetRef ref) async {
    try {
      await ref.read(excelAppToolsProvider.notifier).saveTool(tool);
      // Auto-save successful
    } catch (e) {
      // Auto-save failed - could emit error event here
    }
  }

  // Get cell display value (calculated for formulas)
  dynamic getCellDisplayValue(String cellAddress, ExcelAppTool tool) {
    final cell = tool.spreadsheet.cells[cellAddress];
    if (cell == null) return null;
    
    if (cell.isFormula && cell.formula != null) {
      return _formulaEngine.calculateFormula(
        cell.formula!,
        tool.spreadsheet.cells,
        cellAddress,
      );
    }
    
    return cell.value;
  }

  // Get cell raw value (formula text for formulas)
  dynamic getCellRawValue(String cellAddress, ExcelAppTool tool) {
    final cell = tool.spreadsheet.cells[cellAddress];
    if (cell == null) return null;
    
    return cell.isFormula ? cell.formula : cell.value;
  }

  // Check if cell is bound to UI component
  bool isCellBound(String cellAddress, ExcelAppTool tool) {
    return tool.uiComponents.any((component) => component.boundCell == cellAddress);
  }

  // Get UI components bound to cell
  List<UIComponent> getBoundComponents(String cellAddress, ExcelAppTool tool) {
    return tool.uiComponents
        .where((component) => component.boundCell == cellAddress)
        .toList();
  }

  // Update UI component value and propagate to cell
  void updateComponentValue(String componentId, dynamic value, ExcelAppTool tool, WidgetRef ref) {
    final component = tool.uiComponents.firstWhere(
      (c) => c.id == componentId,
      orElse: () => throw Exception('Component not found: $componentId'),
    );
    
    if (component.boundCell != null) {
      updateCell(component.boundCell!, value, tool, ref);
    }
  }

  // Validate cell binding
  bool isValidCellBinding(String cellAddress) {
    final cellPattern = RegExp(r'^[A-Z]+\d+$');
    return cellPattern.hasMatch(cellAddress);
  }

  // Get cell validation errors
  List<String> getCellValidationErrors(String cellAddress, ExcelAppTool tool) {
    final errors = <String>[];
    final cell = tool.spreadsheet.cells[cellAddress];
    
    if (cell != null && cell.isFormula && cell.formula != null) {
      final validation = _formulaEngine.validateFormula(cell.formula!);
      if (!validation.isValid) {
        errors.add(validation.message);
      }
    }
    
    return errors;
  }

  // Clean up resources
  void dispose() {
    for (final controller in _cellStreams.values) {
      controller.close();
    }
    _cellStreams.clear();
    _cellDependencies.clear();
    
    for (final timer in _autoSaveTimers.values) {
      timer.cancel();
    }
    _autoSaveTimers.clear();
    
    _globalAutoSaveTimer?.cancel();
  }

  // Enable/disable auto-save
  void setAutoSave(bool enabled, [int? interval]) {
    _autoSaveEnabled = enabled;
    if (interval != null) {
      _autoSaveInterval = interval;
    }
    
    if (!enabled) {
      _globalAutoSaveTimer?.cancel();
    }
  }

  // Force save
  void forceSave(ExcelAppTool tool, WidgetRef ref) {
    _globalAutoSaveTimer?.cancel();
    _performAutoSave(tool, ref);
  }
}

// Provider for the real-time binding service
final realTimeBindingServiceProvider = Provider<RealTimeBindingService>((ref) {
  return RealTimeBindingService();
});

// Provider for auto-save settings
final autoSaveSettingsProvider = StateNotifierProvider<AutoSaveSettingsNotifier, AutoSaveSettings>((ref) {
  return AutoSaveSettingsNotifier();
});

class AutoSaveSettings {
  final bool enabled;
  final int interval; // seconds
  final bool showNotifications;

  const AutoSaveSettings({
    this.enabled = true,
    this.interval = 2,
    this.showNotifications = false,
  });

  AutoSaveSettings copyWith({
    bool? enabled,
    int? interval,
    bool? showNotifications,
  }) {
    return AutoSaveSettings(
      enabled: enabled ?? this.enabled,
      interval: interval ?? this.interval,
      showNotifications: showNotifications ?? this.showNotifications,
    );
  }
}

class AutoSaveSettingsNotifier extends StateNotifier<AutoSaveSettings> {
  AutoSaveSettingsNotifier() : super(const AutoSaveSettings());

  void updateSettings(AutoSaveSettings settings) {
    state = settings;
    
    // Update the binding service
    RealTimeBindingService().setAutoSave(settings.enabled, settings.interval);
  }

  void setEnabled(bool enabled) {
    state = state.copyWith(enabled: enabled);
    RealTimeBindingService().setAutoSave(enabled);
  }

  void setInterval(int interval) {
    state = state.copyWith(interval: interval);
    RealTimeBindingService().setAutoSave(state.enabled, interval);
  }

  void setShowNotifications(bool show) {
    state = state.copyWith(showNotifications: show);
  }
}
