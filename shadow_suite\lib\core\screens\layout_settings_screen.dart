import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/layout_service.dart';
import '../themes/theme_models_base.dart';

class LayoutSettingsScreen extends ConsumerStatefulWidget {
  const LayoutSettingsScreen({super.key});

  @override
  ConsumerState<LayoutSettingsScreen> createState() =>
      _LayoutSettingsScreenState();
}

class _LayoutSettingsScreenState extends ConsumerState<LayoutSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    final currentLayout = ref.watch(currentLayoutProvider);
    final screenSize = ref.watch(screenSizeProvider);
    final layoutService = ref.read(layoutServiceProvider);
    final deviceType = layoutService.getDeviceType(screenSize);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Layout Settings'),
        backgroundColor: const Color(0xFF3498DB),
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current Device Info
            _buildDeviceInfoCard(deviceType, screenSize),
            const SizedBox(height: 24),

            // Layout System Selection
            _buildLayoutSystemSection(currentLayout, layoutService, deviceType),
            const SizedBox(height: 24),

            // Layout Configuration
            _buildLayoutConfigSection(currentLayout),
            const SizedBox(height: 24),

            // Touch Target Configuration
            _buildTouchTargetSection(currentLayout),
            const SizedBox(height: 24),

            // Responsive Breakpoints
            _buildBreakpointsSection(currentLayout),
            const SizedBox(height: 24),

            // Preview and Apply
            _buildPreviewSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceInfoCard(DeviceType deviceType, Size screenSize) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.devices, color: Color(0xFF3498DB)),
                SizedBox(width: 8),
                Text(
                  'Current Device',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Device Type: ${deviceType.name.toUpperCase()}'),
                      Text(
                        'Screen Size: ${screenSize.width.toInt()} × ${screenSize.height.toInt()}',
                      ),
                      Text(
                        'Aspect Ratio: ${(screenSize.width / screenSize.height).toStringAsFixed(2)}',
                      ),
                    ],
                  ),
                ),
                Icon(
                  _getDeviceIcon(deviceType),
                  size: 48,
                  color: const Color(0xFF3498DB),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLayoutSystemSection(
    LayoutConfiguration currentLayout,
    LayoutService layoutService,
    DeviceType deviceType,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.view_quilt, color: Color(0xFF3498DB)),
                SizedBox(width: 8),
                Text(
                  'Layout System',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Layout 1: Desktop Optimized
            _buildLayoutOption(
              LayoutSystem.desktopOptimized,
              'Desktop Optimized',
              'Enhanced desktop layout with sidebar navigation and optimized for mouse/keyboard interaction',
              Icons.desktop_windows,
              currentLayout.layoutSystem == LayoutSystem.desktopOptimized,
              layoutService.getRecommendedLayout(deviceType) ==
                  LayoutSystem.desktopOptimized,
            ),

            const SizedBox(height: 12),

            // Layout 2: Material Design Mobile
            _buildLayoutOption(
              LayoutSystem.materialDesignMobile,
              'Material Design Mobile',
              'Google Material Design 3 with bottom navigation, FABs, and responsive breakpoints',
              Icons.phone_android,
              currentLayout.layoutSystem == LayoutSystem.materialDesignMobile,
              layoutService.getRecommendedLayout(deviceType) ==
                  LayoutSystem.materialDesignMobile,
            ),

            const SizedBox(height: 12),

            // Layout 3: Android Native Small
            _buildLayoutOption(
              LayoutSystem.androidNativeSmall,
              'Android Native Small Screen',
              'Mobile-first design with collapsible sidebar, bottom tabs, and optimized touch targets',
              Icons.smartphone,
              currentLayout.layoutSystem == LayoutSystem.androidNativeSmall,
              layoutService.getRecommendedLayout(deviceType) ==
                  LayoutSystem.androidNativeSmall,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLayoutOption(
    LayoutSystem layoutSystem,
    String title,
    String description,
    IconData icon,
    bool isSelected,
    bool isRecommended,
  ) {
    return InkWell(
      onTap: () {
        ref
            .read(currentLayoutProvider.notifier)
            .updateLayoutSystem(layoutSystem);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Switched to $title'),
            backgroundColor: const Color(0xFF27AE60),
          ),
        );
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? const Color(0xFF3498DB) : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
          color: isSelected
              ? const Color(0xFF3498DB).withValues(alpha: 0.1)
              : null,
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isSelected
                  ? const Color(0xFF3498DB)
                  : Colors.grey.shade600,
              size: 32,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: isSelected ? const Color(0xFF3498DB) : null,
                        ),
                      ),
                      if (isRecommended) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(0xFF27AE60),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Text(
                            'RECOMMENDED',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  ),
                ],
              ),
            ),
            if (isSelected)
              const Icon(Icons.check_circle, color: Color(0xFF3498DB)),
          ],
        ),
      ),
    );
  }

  Widget _buildLayoutConfigSection(LayoutConfiguration currentLayout) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.tune, color: Color(0xFF3498DB)),
                SizedBox(width: 8),
                Text(
                  'Layout Configuration',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            SwitchListTile(
              title: const Text('Enable Sidebar Collapse'),
              subtitle: const Text(
                'Allow sidebar to be collapsed on smaller screens',
              ),
              value: currentLayout.enableSidebarCollapse,
              onChanged: (value) {
                ref
                    .read(currentLayoutProvider.notifier)
                    .toggleSidebarCollapse();
              },
              activeColor: const Color(0xFF3498DB),
            ),

            SwitchListTile(
              title: const Text('Show Bottom Navigation'),
              subtitle: const Text(
                'Display bottom navigation bar on mobile devices',
              ),
              value: currentLayout.showBottomNavigation,
              onChanged: (value) {
                ref
                    .read(currentLayoutProvider.notifier)
                    .toggleBottomNavigation();
              },
              activeColor: const Color(0xFF3498DB),
            ),

            ListTile(
              title: const Text('Sidebar Width'),
              subtitle: Text('${currentLayout.sidebarWidth.toInt()}px'),
              trailing: SizedBox(
                width: 150,
                child: Slider(
                  value: currentLayout.sidebarWidth,
                  min: 200,
                  max: 400,
                  divisions: 20,
                  onChanged: (value) {
                    // Update sidebar width with immediate effect
                    ref
                        .read(currentLayoutProvider.notifier)
                        .updateSidebarWidth(value);
                  },
                  activeColor: const Color(0xFF3498DB),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTouchTargetSection(LayoutConfiguration currentLayout) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.touch_app, color: Color(0xFF3498DB)),
                SizedBox(width: 8),
                Text(
                  'Touch Targets',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            ListTile(
              title: const Text('Minimum Touch Size'),
              subtitle: Text(
                '${currentLayout.touchTargets.minimumSize.toInt()}dp (Material Design: 48dp minimum)',
              ),
              trailing: Text(
                '${currentLayout.touchTargets.minimumSize.toInt()}dp',
              ),
            ),

            ListTile(
              title: const Text('Preferred Touch Size'),
              subtitle: Text(
                '${currentLayout.touchTargets.preferredSize.toInt()}dp',
              ),
              trailing: Text(
                '${currentLayout.touchTargets.preferredSize.toInt()}dp',
              ),
            ),

            SwitchListTile(
              title: const Text('Haptic Feedback'),
              subtitle: const Text('Vibrate on touch interactions'),
              value: currentLayout.touchTargets.enableHapticFeedback,
              onChanged: (value) {
                // Update haptic feedback with immediate effect
                ref.read(currentLayoutProvider.notifier).toggleHapticFeedback();
              },
              activeColor: const Color(0xFF3498DB),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBreakpointsSection(LayoutConfiguration currentLayout) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.view_quilt, color: Color(0xFF3498DB)),
                SizedBox(width: 8),
                Text(
                  'Responsive Breakpoints',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildBreakpointItem(
              'Mobile',
              currentLayout.breakpoints.mobile,
              Icons.smartphone,
            ),
            _buildBreakpointItem(
              'Tablet',
              currentLayout.breakpoints.tablet,
              Icons.tablet,
            ),
            _buildBreakpointItem(
              'Desktop',
              currentLayout.breakpoints.desktop,
              Icons.desktop_windows,
            ),
            _buildBreakpointItem(
              'Large Desktop',
              currentLayout.breakpoints.largeDesktop,
              Icons.tv,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBreakpointItem(String label, double value, IconData icon) {
    return ListTile(
      leading: Icon(icon, color: const Color(0xFF3498DB)),
      title: Text(label),
      trailing: Text('${value.toInt()}px'),
    );
  }

  Widget _buildPreviewSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.preview, color: Color(0xFF3498DB)),
                SizedBox(width: 8),
                Text(
                  'Preview & Apply',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Layout settings saved successfully'),
                          backgroundColor: Color(0xFF27AE60),
                        ),
                      );
                    },
                    icon: const Icon(Icons.save),
                    label: const Text('Save Settings'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF27AE60),
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      // Reset to defaults
                      ref
                          .read(currentLayoutProvider.notifier)
                          .updateLayout(const LayoutConfiguration());
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Layout settings reset to defaults'),
                          backgroundColor: Color(0xFF95A5A6),
                        ),
                      );
                    },
                    icon: const Icon(Icons.restore),
                    label: const Text('Reset'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  IconData _getDeviceIcon(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return Icons.smartphone;
      case DeviceType.tablet:
        return Icons.tablet;
      case DeviceType.desktop:
        return Icons.desktop_windows;
      case DeviceType.largeDesktop:
        return Icons.tv;
    }
  }
}
